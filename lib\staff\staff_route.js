const express = require('express');
const route = express.Router();
const staff = require('./staff_controller');
const validater = require('./staff_validator');
const file_upload = require('../utility/file_upload');

const multer = require('multer');

const staff_upload = file_upload.uploadfile.fields([
    { name: '_employee_id_doc', maxCount: 1 },
    { name: '_nationality_id_doc', maxCount: 1 },
    { name: '_address_doc', maxCount: 1 },
    { name: '_degree_doc', maxCount: 1 },
    { name: '_appointment_order_doc', maxCount: 1 },
]);


// route.post('/list', staff.list_values);
route.get('/list_filter/:program/:time/:type', staff.list_filter);
route.get('/staff_dashboard', staff.staff_dashboard);
route.get('/:id', validater.staff_id, staff.list_id);
route.get('/', staff.list);
route.post('/',
    (req, res, next) => {
        staff_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                // A Multer error occurred when uploading.
                return res.status(500).send('Multer Error in uploading');
            } if (err) {
                // An unknown error occurred when uploading.
                console.log('error', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
            // Everything went fine.
        });
    },
    validater.staff, staff.insert);


route.get('/staff_list/:filter', staff.staff_list);

route.put('/:id', validater.staff_id, validater.staff, staff.update);
route.delete('/:id', validater.staff_id, staff.delete);
route.get('/:match/:value', staff.get_mobile_mail);

route.post('/import', validater.staff_import, staff.staff_import);
route.post('/login', validater.signup, staff.login);
route.post('/login_otp', validater.login, staff.staff_login);
route.post('/set_password', validater.set_password, staff.set_password);
route.post('/register_mobile', validater.mobile_register, staff.register_mobile);
route.post('/otp_verify', validater.otp_verify, staff.otp_verify);
route.post('/profile_update', validater.profile_update, staff.profile_update);
route.post('/profile_document_update', (req, res, next) => {
    staff_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            console.log('error', err);
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    });
}, validater.profile_document_update, staff.profile_document_update);

route.post('/staff_mail_push', staff.staff_mail_push);
route.post('/staff_add_edit', validater.staff_add_edit, staff.staff_add_edit);
route.post('/staff_edit', validater.staff_edit, staff.staff_edit);
route.post('/staff_face_bio_register', validater.staff_face_bio_register, staff.staff_face_bio_register);
route.post('/staff_academic_allocation', validater.staff_academic_allocation, staff.staff_academic_allocation);
route.post('/staff_employment', staff.staff_employment);

module.exports = route;