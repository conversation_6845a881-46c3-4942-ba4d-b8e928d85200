const multer = require('multer');
const { uploadDocumentFile, assignmentFilesUpload } = require('../../../utility/file-upload');
const keys = require('../../../utility/util_keys');
const { getS3SignedUrl, getSizeOfS3Url } = require('../../../../service/aws.service');
const { response_function } = require('../../../../lib/utility/common');

const fileUpload = assignmentFilesUpload.array('file', 20);
const singleFileUpload = uploadDocumentFile.fields([{ name: 'file', maxCount: 1 }]);

const uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};
const singleDocumentUpload = (req, res, next) => {
    singleFileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getSignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getS3SignedUrl({
        bucket: keys.AWS_BUCKET_NAME_ASSIGNMENT,
        key: fileName,
    });
    return signedUrl;
};

const getSizeOfUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getSizeOfS3Url({
        bucket: keys.AWS_BUCKET_NAME_ASSIGNMENT,
        key: fileName,
    });
    return signedUrl;
};

module.exports = {
    uploadDocument,
    singleDocumentUpload,
    getSignedUrl,
    getSizeOfUrl,
};
