const express = require('express');
const route = express.Router();
const department_subject = require('./digi_department_subject_controller');
const validator = require('./digi_department_subject_validator');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');

//Department
route.get(
    '/:program_id',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'user_management:staff_management:registered:profile_edit',
        ]),
    ],
    department_subject.list,
);
route.get(
    '/withOutShare/:program_id',
    [userPolicyAuthentication(['program_input:programs:view'])],
    department_subject.listWithoutShareAdding,
);
route.get(
    '/course_department/:program_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    department_subject.course_department_list,
);
route.post(
    '/import_department_subject',
    /* validator.department_subject_import, */
    [
        userPolicyAuthentication([
            ...defaultProgramInputPolicy.VIEW,
            ...defaultProgramInputPolicy.EDIT,
        ]),
    ],
    department_subject.import_department_subject,
);
route.post(
    '/data_check_department_subject',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    /* validator.department_subject_import, */
    department_subject.data_check_department_subject,
);
route.post(
    '/',
    [
        userPolicyAuthentication([
            ...defaultProgramInputPolicy.VIEW,
            ...defaultProgramInputPolicy.EDIT,
        ]),
    ],
    validator.insert_department,
    department_subject.insert,
);
route.delete('/:id', validator.id, department_subject.delete_department);
route.put('/:id', validator.id, validator.update_department, department_subject.update_department);
route.put(
    '/share_department/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.id,
    department_subject.share_department,
);

//Subject
route.post(
    '/subject',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    department_subject.add_subject,
);
route.put(
    '/subject/:department_id/:subject_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.department_id,
    validator.subject_id,
    validator.subject_id,
    department_subject.update_subject,
);
route.put(
    '/share_subject/:department_id/:subject_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.department_id,
    validator.subject_id,
    department_subject.share_subject,
);
route.delete(
    '/subject/:department_id/:subject_id',
    validator.department_id,
    validator.subject_id,
    department_subject.delete_subject,
);

module.exports = route;
