const { convertToMongoObjectId } = require('../../utility/common');
const {
    GROUPS,
    FAMILIES,
    COMPLETED,
    SURVEY_QUESTION_TYPE: { RATING },
    SURVEY_TAG_REPORT_EXPIRE_TIME,
    TAG_REPORT_REFRESH_STATE: { REGULAR },
    TAG_REPORTS,
    TAG_REPORT_DEFAULT_RATING_VALUE,
} = require('../../utility/constants');
const { calculateMean } = require('../digiSurvey/digiSurvey.service');
const digiSurveyTagReportSchema = require('./digiSurveyTagReports.model');
const digiSurveyResponseSchema = require('../digiSurveyResponse/digiSurveyResponse.model');
const surveySchema = require('../../models/digiSurvey');
const { redisClient } = require('../../../config/redis-connection');

exports.getExcludedSurveyList = async ({
    reportId,
    tagId,
    questionId,
    prefixSentence = '',
    isOutcomeQuestion = false,
}) => {
    const excludeSurveyList = await digiSurveyTagReportSchema
        .findOne(
            {
                _id: convertToMongoObjectId(reportId),
                ...(tagId && { 'excludedSurveyIds.tagId': convertToMongoObjectId(tagId) }),
                ...(questionId && { 'excludedSurveyIds.questionId': questionId }),
                ...(!!isOutcomeQuestion && {
                    'excludedSurveyIds.prefixSentence': prefixSentence,
                }),
            },
            { excludedSurveyIds: 1 },
        )
        .lean();
    return excludeSurveyList;
};

const createTagAnalysis = ({ currentTagDetails, constructedTagBasedReports }) => {
    const tagBasedMeanValues = constructedTagBasedReports.get(String(currentTagDetails._id));
    const studentTotalResponses = tagBasedMeanValues ? tagBasedMeanValues.studentAnswers : [];
    return {
        ...currentTagDetails,
        meanValue: calculateMean(studentTotalResponses),
        totalQuestions: tagBasedMeanValues ? tagBasedMeanValues.totalQuestions : 0,
    };
};

const createGroupAnalysis = ({
    currentGroupDetails,
    mappedTagDetails,
    constructedTagBasedReports,
    isMappedTagNeeded,
}) => {
    if (isMappedTagNeeded) {
        const studentTotalResponsesWithTagDetails = [];
        currentGroupDetails.tags.forEach((tagIdElement) => {
            const currentMappedTagDetails = mappedTagDetails.find(
                ({ _id }) => String(_id) === String(tagIdElement),
            );
            if (currentMappedTagDetails) {
                studentTotalResponsesWithTagDetails.push(
                    createTagAnalysis({
                        currentTagDetails: currentMappedTagDetails,
                        constructedTagBasedReports,
                    }),
                );
            }
        });
        return { tagDetails: studentTotalResponsesWithTagDetails };
    }
    const studentTotalResponses = [];
    let groupedBasedTotalQuestions = 0;
    currentGroupDetails.tags.forEach((tagIdElement) => {
        const tagBasedMeanValues = constructedTagBasedReports.get(String(tagIdElement));
        if (tagBasedMeanValues) {
            studentTotalResponses.push(...tagBasedMeanValues.studentAnswers);
            groupedBasedTotalQuestions += tagBasedMeanValues.totalQuestions;
        }
    });
    delete currentGroupDetails.tags;
    return {
        ...currentGroupDetails,
        meanValue: calculateMean(studentTotalResponses),
        totalQuestions: groupedBasedTotalQuestions,
    };
};

const createFamilyAnalysis = ({
    currentFamilyDetails,
    mappedGroupedDetails,
    mappedTagDetails,
    constructedTagBasedReports,
    groupId,
    isMappedGroupNeeded,
}) => {
    if (groupId) {
        const studentTotalResponsesWithTagDetails = [];
        currentFamilyDetails.groups.forEach((groupIdElement) => {
            if (String(groupId) === String(groupIdElement)) {
                const currentMappedGroupDetail = mappedGroupedDetails.find(
                    ({ _id }) => String(_id) === String(groupIdElement),
                );
                const tagDetailsWithMeanValue = createGroupAnalysis({
                    currentGroupDetails: currentMappedGroupDetail,
                    mappedTagDetails,
                    constructedTagBasedReports,
                    isMappedTagNeeded: true,
                });
                studentTotalResponsesWithTagDetails.push(...tagDetailsWithMeanValue.tagDetails);
            }
        });
        return { tagDetails: studentTotalResponsesWithTagDetails };
    }
    if (!groupId && isMappedGroupNeeded) {
        const studentTotalResponsesWithGroupDetails = [];
        currentFamilyDetails.groups.forEach((groupIdElement) => {
            const currentMappedGroupDetail = mappedGroupedDetails.find(
                ({ _id }) => String(_id) === String(groupIdElement),
            );
            if (currentMappedGroupDetail) {
                studentTotalResponsesWithGroupDetails.push(
                    createGroupAnalysis({
                        currentGroupDetails: currentMappedGroupDetail,
                        mappedTagDetails,
                        constructedTagBasedReports,
                        isMappedTagNeeded: false,
                    }),
                );
            }
        });
        return { groupDetails: studentTotalResponsesWithGroupDetails };
    }
    const studentTotalResponses = [];
    let familyBasedTotalQuestions = 0;
    currentFamilyDetails.groups.forEach((groupIdElement) => {
        const currentMappedGroupDetail = mappedGroupedDetails.find(
            ({ _id }) => String(_id) === String(groupIdElement),
        );
        if (currentMappedGroupDetail) {
            currentMappedGroupDetail.tags.forEach((mappedGroupTagId) => {
                const tagBasedMeanValues = constructedTagBasedReports.get(String(mappedGroupTagId));
                if (tagBasedMeanValues) {
                    studentTotalResponses.push(...tagBasedMeanValues.studentAnswers);
                    familyBasedTotalQuestions += tagBasedMeanValues.totalQuestions;
                }
            });
        }
    });
    delete currentFamilyDetails.groups;
    return {
        ...currentFamilyDetails,
        meanValue: calculateMean(studentTotalResponses),
        totalQuestions: familyBasedTotalQuestions,
    };
};

exports.analyzeTagReportByView = ({
    viewBy,
    tagReportDetail,
    constructedTagBasedReports,
    familyId,
    groupId,
    selectedTagIds,
    selectedGroupsIds,
    selectedFamilyIds,
}) => {
    const surveyReportAnalysis = [];
    switch (viewBy) {
        case GROUPS:
            tagReportDetail.selectedGroups.groups.forEach((selectedGroupElement) => {
                if (
                    !selectedGroupsIds.length ||
                    selectedGroupsIds.includes(String(selectedGroupElement._id))
                ) {
                    if (!groupId || String(groupId) === String(selectedGroupElement._id)) {
                        surveyReportAnalysis.push(
                            createGroupAnalysis({
                                currentGroupDetails: selectedGroupElement,
                                mappedTagDetails: tagReportDetail.selectedTags.tags,
                                constructedTagBasedReports,
                                isMappedTagNeeded: !!groupId,
                            }),
                        );
                    }
                }
            });
            break;
        case FAMILIES:
            tagReportDetail.selectedFamilies.families.forEach((selectedFamilyElement) => {
                if (
                    !selectedFamilyIds.length ||
                    selectedFamilyIds.includes(String(selectedFamilyElement._id))
                ) {
                    if (!familyId || String(familyId) === String(selectedFamilyElement._id)) {
                        surveyReportAnalysis.push(
                            createFamilyAnalysis({
                                currentFamilyDetails: selectedFamilyElement,
                                mappedGroupedDetails: tagReportDetail.selectedGroups.groups,
                                mappedTagDetails: tagReportDetail.selectedTags.tags,
                                constructedTagBasedReports,
                                groupId,
                                isMappedGroupNeeded: !!familyId,
                            }),
                        );
                    }
                }
            });
            break;
        default:
            tagReportDetail.selectedTags.tags.forEach((selectedTagElement) => {
                if (
                    !selectedTagIds.length ||
                    selectedTagIds.includes(String(selectedTagElement._id))
                ) {
                    surveyReportAnalysis.push(
                        createTagAnalysis({
                            currentTagDetails: selectedTagElement,
                            constructedTagBasedReports,
                        }),
                    );
                }
            });
    }
    return surveyReportAnalysis;
};

const getConstructTagReportCacheData = async ({ reportId, tagReportRedisKey }) => {
    const tagReportDetail = await digiSurveyTagReportSchema
        .findOne(
            {
                _id: convertToMongoObjectId(reportId),
            },
            {
                'selectedTags.tags._id': 1,
                'selectedTags.tags.code': 1,
                'selectedTags.tags.description': 1,
                'selectedTags.tags.name': 1,
                'selectedTags.tags.targetRange': 1,
                'selectedTags.tags.targetRangeColorCode': 1,
                'selectedGroups.groups._id': 1,
                'selectedGroups.groups.code': 1,
                'selectedGroups.groups.description': 1,
                'selectedGroups.groups.name': 1,
                'selectedGroups.groups.tags': 1,
                'selectedGroups.groups.targetRange': 1,
                'selectedGroups.groups.targetRangeColorCode': 1,
                'selectedFamilies.families._id': 1,
                'selectedFamilies.families.code': 1,
                'selectedFamilies.families.description': 1,
                'selectedFamilies.families.name': 1,
                'selectedFamilies.families.groups': 1,
                'selectedFamilies.families.targetRange': 1,
                'selectedFamilies.families.targetRangeColorCode': 1,
                excludedSurveyIds: 1,
            },
        )
        .lean();
    const reportTagIds = tagReportDetail.selectedTags.tags.map(({ _id }) => String(_id));
    const surveyDetails = await surveySchema
        .find(
            {
                status: COMPLETED,
                isTemplate: true,
                isDeleted: false,
                ...(reportTagIds.length && {
                    'mappedTagDetails.tagId': { $in: tagReportDetail.selectedTags.tags },
                }),
            },
            {
                'questions.pages.elements': 1,
                institutionCalendarId: 1,
                mappedTagDetails: 1,
                surveyLevel: 1,
                term: 1,
                programId: 1,
                courseId: 1,
                yearNo: 1,
                levelNo: 1,
            },
        )
        .lean();
    const surveyDetailsWithStudentResponse = {
        questionDetails: [],
        studentResponses: [],
    };
    const surveyStudentResponseFindQuery = [];
    surveyDetails.forEach((surveyElement) => {
        const {
            _id: surveyId,
            surveyLevel,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            mappedTagDetails,
            institutionCalendarId,
            questions: { pages = [] },
        } = surveyElement;
        const constructedTaggedQuestion = new Map();
        mappedTagDetails.forEach((mappedTagDetailElement) => {
            if (reportTagIds.includes(String(mappedTagDetailElement.tagId))) {
                constructedTaggedQuestion.set(
                    String(mappedTagDetailElement.questionId),
                    String(mappedTagDetailElement.tagId),
                );
            }
        });
        pages.forEach(({ elements = [] }) => {
            const matchedRatingQuestions = elements.filter(
                ({ uniqueId, type }) =>
                    constructedTaggedQuestion.has(String(uniqueId)) && type === RATING,
            );
            matchedRatingQuestions.forEach((matchedQuestionElement) => {
                const { uniqueId = '', rateValues = [] } = matchedQuestionElement;
                const mappedTagId = constructedTaggedQuestion.get(String(uniqueId));
                const maxRatingPoints = rateValues?.[rateValues.length - 1]?.value ?? 0;
                const isSurveyExcluded =
                    tagReportDetail.excludedSurveyIds &&
                    tagReportDetail.excludedSurveyIds.some(
                        (excludedSurveyElement) =>
                            String(excludedSurveyElement.tagId) === String(mappedTagId) &&
                            String(excludedSurveyElement.questionId) === String(uniqueId) &&
                            String(excludedSurveyElement.surveyIds).includes(String(surveyId)),
                    );
                if (!isSurveyExcluded) {
                    surveyDetailsWithStudentResponse.questionDetails.push({
                        institutionCalendarId,
                        surveyId,
                        maxRatingPoints,
                        questionId: uniqueId,
                        mappedTagId,
                        surveyLevel,
                        programId,
                        courseId,
                        yearNo,
                        levelNo,
                        term,
                    });
                    surveyStudentResponseFindQuery.push({
                        surveyId: convertToMongoObjectId(surveyId),
                        questionType: RATING,
                        questionId: uniqueId,
                    });
                }
            });
        });
    });
    const userSurveyResponses = surveyStudentResponseFindQuery.length
        ? await digiSurveyResponseSchema
              .find(
                  { $or: surveyStudentResponseFindQuery },
                  { questionId: 1, answer: 1, surveyId: 1 },
              )
              .lean()
        : [];
    surveyDetailsWithStudentResponse.studentResponses = userSurveyResponses;
    surveyDetailsWithStudentResponse.tagReportDetail = tagReportDetail;
    surveyDetailsWithStudentResponse.refreshedTime = new Date();
    await redisClient.Client.set(
        tagReportRedisKey,
        JSON.stringify(surveyDetailsWithStudentResponse),
        'EX',
        SURVEY_TAG_REPORT_EXPIRE_TIME,
    );
    return surveyDetailsWithStudentResponse;
};

exports.getTagReportResponseRateAnalysisFromCache = async ({ reportId, state }) => {
    const tagReportRedisKey = `${TAG_REPORTS}:${reportId}`;
    if (state === REGULAR) {
        const tagReportCacheData = await redisClient.Client.get(tagReportRedisKey);
        if (tagReportCacheData) {
            return JSON.parse(tagReportCacheData);
        }
    }
    const constructTagReportCacheData = await getConstructTagReportCacheData({
        reportId,
        tagReportRedisKey,
    });
    return constructTagReportCacheData;
};

exports.getTotalDocument = async ({ model, findQuery }) => {
    return await model.countDocuments(findQuery);
};

exports.convertRatingValuesToFive = ({ values, maxRatingPoints }) => {
    return values.map(
        (valueElement) => (valueElement / maxRatingPoints) * TAG_REPORT_DEFAULT_RATING_VALUE,
    );
};

exports.getTotalAnsweredUserCount = async ({ questions = [], surveyId }) => {
    const totalQuestionId = questions.reduce((acc, curr) => {
        curr.elements.forEach(({ uniqueId }) => {
            acc.push(uniqueId);
        });
        return acc;
    }, []);
    const totalAnsweredUserCount = await digiSurveyResponseSchema.distinct('userId', {
        surveyId,
        questionId: { $in: totalQuestionId },
        questionType: RATING,
    });
    return totalAnsweredUserCount.length;
};
