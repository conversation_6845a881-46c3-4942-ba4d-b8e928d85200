const multer = require('multer');
const { uploadDocumentFile } = require('../utility/file_upload');
const keys = require('../utility/util_keys');
const { getS3SignedUrl, getSizeOfS3Url } = require('../../service/aws.service');
const { response_function } = require('../utility/common');

const fileUpload = uploadDocumentFile.fields([{ name: 'file', maxCount: 1 }]);

const uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getSignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getS3SignedUrl({
        bucket: keys.BUCKET_NAME_DOC,
        key: `digiclass/` + fileName,
    });
    return signedUrl;
};

const getSizeOfUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getSizeOfS3Url({
        bucket: keys.BUCKET_NAME_DOC,
        key: `digiclass/` + fileName,
    });
    return signedUrl;
};

module.exports = {
    uploadDocument,
    getSignedUrl,
    getSizeOfUrl,
};
