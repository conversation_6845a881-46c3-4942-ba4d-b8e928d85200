const EvaluationService = require('./evaluation.service');

const { checkIfTrue, getPaginationValues } = require('../../common/utils/common.util');

const updateStudentMarks = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentIds,
        marks,
        rubrics,
        globalRubrics,
        email,
    },
    headers: { user_id: userId } = {},
}) => {
    const { message, students, isError } = await EvaluationService.updateStudentMarks({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentIds,
        userId,
        marks,
        rubrics,
        globalRubrics,
        email,
    });

    return { message, data: { isError, students } };
};

const rejectStudentSubmission = async ({
    query: { componentId, childrenId },
    body: { studentIds = [], scheduleIds = [], isApproved = false, reason },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.rejectStudentSubmission({
        componentId,
        childrenId,
        scheduleIds,
        studentIds,
        isApproved,
        userId,
        reason,
    });

    return { statusCode: 200, data: 'STUDENT_SUBMISSION_REJECTED_SUCCESSFULLY' };
};

const completeEvaluation = async ({
    body: { componentId, childrenId, scheduleId, studentIds },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.completeEvaluation({
        componentId,
        childrenId,
        scheduleId,
        studentIds,
        userId,
    });

    return { statusCode: 200, data: 'EVALUATION_COMPLETED_SUCCESSFULLY' };
};

const getPortfolioInsights = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId },
}) => {
    const insights = await EvaluationService.getPortfolioInsights({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
    });

    return { statusCode: 200, data: insights };
};

const getEvaluationCount = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId },
}) => {
    const count = await EvaluationService.getEvaluationCount({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
    });

    return { statusCode: 200, data: count };
};

const assignEvaluator = async ({
    body: {
        portfolioId,
        componentId,
        childrenId,
        infrastructureId,
        deliveryTypes = [],
        deliveryType = {},
        roles = [],
        typeOfEvaluation,
        students = [],
        groups = [],
        session = {},
        prepareAndPublish = [],
    },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.assignEvaluator({
        portfolioId,
        componentId,
        childrenId,
        infrastructureId,
        deliveryTypes,
        deliveryType,
        roles,
        typeOfEvaluation,
        students,
        userId,
        groups,
        session,
        prepareAndPublish,
    });

    return { statusCode: 200, data: result };
};

const getInfrastructuresForAssignEvaluator = async ({
    query: { portfolioId, componentId, childrenId },
}) => {
    const result = await EvaluationService.getInfrastructuresForAssignEvaluator({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const switchEvaluationType = async ({ query: { portfolioId, componentId, childrenId } }) => {
    await EvaluationService.switchEvaluationType({ portfolioId, componentId, childrenId });

    return { statusCode: 200, data: 'EVALUATION_TYPE_SWITCHED_SUCCESSFULLY' };
};

const getEvaluators = async ({ query = {} }) => {
    const { portfolioId, componentId, childrenId, isGeneralUser } = query;

    const { limit, pageNo, skip, search } = getPaginationValues(query);

    const result = await EvaluationService.getEvaluators({
        portfolioId,
        componentId,
        childrenId,
        isGeneralUser: checkIfTrue(isGeneralUser),
        limit,
        pageNo,
        skip,
        search,
    });
    return { statusCode: 200, data: result };
};

const getStudentGroupsForAssignEvaluator = async ({
    query: {
        portfolioId,
        componentId,
        childrenId,
        isStudentGroup,
        deliveryTypeSymbol,
        deliveryTypeId,
    },
}) => {
    const result = await EvaluationService.getStudentGroupsForAssignEvaluator({
        portfolioId,
        componentId,
        childrenId,
        isStudentGroup: checkIfTrue(isStudentGroup),
        deliveryTypeSymbol,
        deliveryTypeId,
    });

    return { statusCode: 200, data: result };
};

const getDeliveryTypeForAssignEvaluator = async ({ query: { portfolioId, componentId } }) => {
    const result = await EvaluationService.getDeliveryTypeForAssignEvaluator({
        portfolioId,
        componentId,
    });

    return { statusCode: 200, data: result };
};

const addExternalEvaluator = async ({
    body: { email, name, roleId, mobile, gender, componentId, childrenId, deliveryTypeId },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.addExternalEvaluator({
        email,
        name,
        roleId,
        mobile,
        gender,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
    });

    return { statusCode: 200, message: 'VERIFICATION_CODE_SENT_SUCCESSFULLY' };
};

const verifyVerificationCode = async ({ body: { email, code } }) => {
    const verification = await EvaluationService.verifyVerificationCode({ email, code });

    return { statusCode: 200, data: verification };
};

const updateStudentsForPrepareAndPublish = async ({
    body: { portfolioId, componentId, childrenId, studentIds, formId, roleId, pages, session },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.updateStudentsForPrepareAndPublish({
        portfolioId,
        componentId,
        childrenId,
        studentIds,
        formId,
        roleId,
        pages,
        session,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getChildrenForPrepareAndPublish = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        rotation,
        rotationCount,
    },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getChildrenForPrepareAndPublish({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        curriculumId,
        rotation,
        rotationCount,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getStudentsForPrepareAndPublish = async ({
    query: { portfolioId, componentId, childrenId },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getStudentsForPrepareAndPublish({
        portfolioId,
        componentId,
        childrenId,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getRolesFromChild = async ({ query }) => {
    const { portfolioId, componentId, childrenId } = query;

    const result = await EvaluationService.getRolesFromChild({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const getEvaluationType = async ({ query: { portfolioId, componentId, childrenId } }) => {
    const result = await EvaluationService.getEvaluationType({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: result };
};

const deleteEvaluation = async ({
    query: {
        portfolioId,
        componentId,
        childrenId,
        typeOfEvaluation,
        deliveryTypeId,
        infrastructureId,
    },
}) => {
    await EvaluationService.deleteEvaluation({
        portfolioId,
        componentId,
        childrenId,
        typeOfEvaluation,
        deliveryTypeId,
        infrastructureId,
        groupId,
    });

    return { statusCode: 200, data: 'EVALUATION_DELETED_SUCCESSFULLY' };
};

const reSendVerificationCode = async ({
    body: { email, roleId, componentId, childrenId, deliveryTypeId },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.reSendVerificationCode({
        email,
        roleId,
        componentId,
        childrenId,
        deliveryTypeId,
        studentId: userId,
    });

    return { statusCode: 200, data: 'VERIFICATION_CODE_RESENT_SUCCESSFULLY' };
};

const getAccessibleFormSectionsByChild = async ({
    query: { portfolioId, componentId, childrenId, studentId, formId },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getAccessibleFormSectionsByChild({
        portfolioId,
        componentId,
        childrenId,
        formId,
        studentId,
        userId,
    });

    return { statusCode: 200, data: result };
};

module.exports = {
    updateStudentMarks,
    rejectStudentSubmission,
    completeEvaluation,
    getPortfolioInsights,
    getEvaluationCount,
    assignEvaluator,
    getInfrastructuresForAssignEvaluator,
    switchEvaluationType,
    getEvaluators,
    getStudentGroupsForAssignEvaluator,
    getDeliveryTypeForAssignEvaluator,
    addExternalEvaluator,
    verifyVerificationCode,
    updateStudentsForPrepareAndPublish,
    getChildrenForPrepareAndPublish,
    getStudentsForPrepareAndPublish,
    getRolesFromChild,
    getEvaluationType,
    deleteEvaluation,
    reSendVerificationCode,
    getAccessibleFormSectionsByChild,
};
