const Joi = require('joi');
const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
    REMAINDER_SECTION,
    INVALID_PROFILE_SECTION,
    DAILY,
    WEEKLY,
    CUSTOM,
} = require('../../utility/enums');
const constant = require('../../utility/enums');
const constants = require('../../utility/constants');
const session = Joi.object().keys({
    start: Joi.object().keys({
        hour: Joi.number()
            .required()
            .error((error) => error),
        minute: Joi.number()
            .required()
            .error((error) => error),
        format: Joi.string()
            .required()
            .error((error) => error),
    }),
    end: Joi.object().keys({
        hour: Joi.number()
            .required()
            .error((error) => error),
        minute: Joi.number()
            .required()
            .error((error) => error),
        format: Joi.string()
            .required()
            .error((error) => error),
    }),
});

const updateGlobalConfiguration = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                isIndependentHours: Joi.boolean()
                    .required()
                    .error((error) => error),
                isGenderSegregation: Joi.boolean()
                    .required()
                    .error((error) => error),
                timeZone: Joi.string().error((error) => error),
                session,
            })
            .unknown(true),
    })
    .unknown(true);

const updateWorkingDays = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                isIndependentHours: Joi.boolean()
                    .required()
                    .error((error) => error),
                isAddWorkingDay: Joi.boolean()
                    .required()
                    .error((error) => error),
                session,
                isActive: Joi.boolean().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const breakValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                session,
                breaks: Joi.object().keys({
                    name: Joi.string()
                        .min(1)
                        .max(60)
                        .regex(/^[a-zA-Z0-9\s]+$/i)
                        .required()
                        .error((error) => error),
                    days: Joi.array()
                        .required()
                        .items(
                            Joi.string()
                                .required()
                                .valid(
                                    MONDAY,
                                    TUESDAY,
                                    WEDNESDAY,
                                    THURSDAY,
                                    FRIDAY,
                                    SATURDAY,
                                    SUNDAY,
                                ),
                        )
                        .error((error) => error),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const breakRemoveValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const emailConfigValidation = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                emailIdConfig: Joi.object().keys({
                    displayName: Joi.string().error((error) => error),
                    fromEmail: Joi.string()
                        .email({ minDomainSegments: 2, tlds: { allow: ['com', 'net'] } })
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    toEmail: Joi.string()
                        .email({ minDomainSegments: 2, tlds: { allow: ['com', 'net'] } })
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    userName: Joi.string().error((error) => {
                        return error;
                    }),
                    password: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    reEnterPassword: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    smtpClient: Joi.string()
                        .required()
                        .error((error) => error),
                    portNumber: Joi.number()
                        .required()
                        .error((error) => error),
                    ttl_ssl: Joi.boolean()
                        .required()
                        .error((error) => error),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const updateEmailConfigValidation = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                emailIdConfig: Joi.object().keys({
                    displayName: Joi.string().error((error) => error),
                    email: Joi.string()
                        .email({ minDomainSegments: 2, tlds: { allow: ['com', 'net'] } })
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    password: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    reEnterPassword: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    smtpClient: Joi.string().error((error) => error),
                    portNumber: Joi.number().error((error) => error),
                    ttl_ssl: Joi.boolean().error((error) => error),
                    server: Joi.string().error((error) => error),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const eventTypeValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                eventType: Joi.object()
                    .required()
                    .keys({
                        name: Joi.string()
                            .required()
                            .error((error) => error),
                        isLeave: Joi.boolean()
                            .required()
                            .error((error) => error),
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const eventTypeUpdateValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                eventType: Joi.object()
                    .required()
                    .keys({
                        name: Joi.string()
                            .required()
                            .error((error) => error),
                        isLeave: Joi.boolean()
                            .required()
                            .error((error) => error),
                    }),
                isRemove: Joi.boolean()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const languageValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                language: Joi.object()
                    .required()
                    .keys({
                        name: Joi.string()
                            .required()
                            .error((error) => error),
                        code: Joi.string()
                            .required()
                            .error((error) => error),
                        isDefault: Joi.boolean().error((error) => error),
                    }),
                isRemove: Joi.boolean().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const labelConfigurationResetValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .required()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
    })
    .unknown(true);

const labelConfigurationValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .required()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                labelName: Joi.string()
                    .min(1)
                    .max(50)
                    .required()
                    .error(() => 'LABEL_NAME_SHOULD_BE_ALPHABET'),
                translatedLabels: Joi.array()
                    .required()
                    .items(
                        Joi.object()
                            .required()
                            .keys({
                                label: Joi.string()
                                    .required()
                                    .error(() => 'LABEL_NAME_SHOULD_BE_ALPHABET'),
                                language: Joi.string()
                                    .required()
                                    .error((error) => error),
                            }),
                    ),
            })
            .unknown(true),
    })
    .unknown(true);

const ProgramTypeValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                name: Joi.string()
                    .min(1)
                    .max(50)
                    .regex(/^[a-zA-Z0-9\s]+$/i)
                    .required()
                    .error((error) => error),
                code: Joi.string()
                    .min(1)
                    .max(50)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const ProgramTypeRemoveValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const addDesignationValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),

                designationName: Joi.string()
                    .min(1)
                    .max(60)
                    .regex(/^[a-zA-Z0-9\s]+$/i)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const designationCheckValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            designationId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const designationUpdateValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            designationId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                designationName: Joi.string()
                    .min(1)
                    .max(60)
                    .regex(/^[a-zA-Z0-9\s]+$/i)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const designationDeleteValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                designationId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const designationGetValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const labelFieldUpdateValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            labelId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                labelType: Joi.string()
                    .required()
                    .error((error) => error),
                labelName: Joi.string()
                    .min(1)
                    .max(60)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const labelFieldCheckValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            labelId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                labelType: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const biometricCheckValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            labelId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const updateMailContentValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            labelId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                labelBody: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const updateDocumentFormatValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                documentFormat: Joi.array()
                    .items(
                        Joi.string().valid(
                            constant.JPG,
                            constant.PNG,
                            constant.PDF,
                            constant.SVG,
                            constant.DOC,
                        ),
                    )
                    .error((error) => error),
                documentSize: Joi.string()
                    .valid(constant.COMMON, constant.INDEPENDENT)
                    .error((error) => error),
                maximumSize: Joi.number().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const updateMaximumSizeValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                maximumSize: Joi.number()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const addDocumentCategoryValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                documentCategory: Joi.string().error((error) => error),
                documentCategoryDescription: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const deleteDocumentCategoryValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                documentCategoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const editDocumentCategoryValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            documentCategoryId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                documentCategory: Joi.string().error((error) => error),
                documentCategoryDescription: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const addDocumentValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                documentCategoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                document: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            size: Joi.number()
                                .integer()
                                .error((error) => {
                                    return error;
                                }),
                            isMandatory: Joi.boolean().error((error) => {
                                return error;
                            }),
                            labelName: Joi.string().error((error) => {
                                return error;
                            }),
                        }),
                    )
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
const editDocumentValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            documentCategoryId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            documentId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                document: Joi.object()
                    .keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        size: Joi.number()
                            .integer()
                            .error((error) => {
                                return error;
                            }),
                        isMandatory: Joi.boolean().error((error) => {
                            return error;
                        }),
                        isActive: Joi.boolean().error((error) => {
                            return error;
                        }),
                        labelName: Joi.string().error((error) => {
                            return error;
                        }),
                    })
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const deleteDocumentValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                documentCategoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                documentId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const remainderMailValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                recurringInterval: Joi.number()
                    .optional()
                    .error((error) => error),
                time: Joi.string()
                    .optional()
                    .error((error) => error),
                isMandatoryDocuments: Joi.boolean().error((error) => {
                    return error;
                }),
                recurring: Joi.string()
                    .optional()
                    .error((error) => error),
                notificationMailTime: Joi.string()
                    .optional()
                    .error((error) => error),
                weeks: Joi.array()
                    .items(
                        Joi.string().valid(
                            MONDAY,
                            TUESDAY,
                            WEDNESDAY,
                            THURSDAY,
                            FRIDAY,
                            SATURDAY,
                            SUNDAY,
                        ),
                    )
                    .optional()
                    .error((error) => error),
                notificationMailLabel: Joi.string()
                    .optional()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const mailSettingsValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            userType: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                isActive: Joi.boolean()
                    .required()
                    .error((error) => error),
                type: Joi.string()
                    .valid(REMAINDER_SECTION, INVALID_PROFILE_SECTION)
                    .optional()
                    .error((error) => error),
                recurring: Joi.string()
                    .valid(DAILY, WEEKLY, CUSTOM)
                    .optional()
                    .error((error) => error),
                recurringInterval: Joi.number()
                    .optional()
                    .error((error) => error),
                time: Joi.string()
                    .optional()
                    .error((error) => error),
                weeks: Joi.array()
                    .items(
                        Joi.string().valid(
                            MONDAY,
                            TUESDAY,
                            WEDNESDAY,
                            THURSDAY,
                            FRIDAY,
                            SATURDAY,
                            SUNDAY,
                        ),
                    )
                    .optional()
                    .error((error) => error),
                mailExpiration: Joi.number()
                    .optional()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const vaccineCategoryValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                categoryName: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const vaccineCategoryEditValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                categoryName: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const vaccineDetailsAddValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                vaccineDetails: Joi.object().keys({
                    vaccineNumber: Joi.string()
                        .required()
                        .error((error) => error),
                    vaccineName: Joi.string()
                        .required()
                        .error((error) => error),
                    vaccineType: Joi.string()
                        .required()
                        .error((error) => error),
                    antigenName: Joi.string()
                        .required()
                        .error((error) => error),
                    companyName: Joi.string()
                        .required()
                        .error((error) => error),
                    brandName: Joi.string()
                        .required()
                        .error((error) => error),
                    noOfDosage: Joi.number()
                        .required()
                        .error((error) => error),
                    noOfBooster: Joi.number()
                        .required()
                        .error((error) => error),
                    dosageDetails: Joi.array()
                        .required()
                        .items(
                            Joi.object()
                                .required()
                                .keys({
                                    labelName: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    days: Joi.number()
                                        .required()
                                        .error((error) => error),
                                }),
                        ),
                    boosterDetails: Joi.array().items(
                        Joi.object()
                            .required()
                            .keys({
                                labelName: Joi.string()
                                    .required()
                                    .error((error) => error),
                                days: Joi.number()
                                    .required()
                                    .error((error) => error),
                            }),
                    ),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const vaccineDetailsEditValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineDetailsId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                vaccineDetails: Joi.object().keys({
                    vaccineNumber: Joi.string()
                        .required()
                        .error((error) => error),
                    vaccineName: Joi.string()
                        .required()
                        .error((error) => error),
                    vaccineType: Joi.string()
                        .required()
                        .error((error) => error),
                    antigenName: Joi.string()
                        .required()
                        .error((error) => error),
                    companyName: Joi.string()
                        .required()
                        .error((error) => error),
                    brandName: Joi.string()
                        .required()
                        .error((error) => error),
                    noOfDosage: Joi.number()
                        .required()
                        .error((error) => error),
                    noOfBooster: Joi.number()
                        .required()
                        .error((error) => error),
                    dosageDetails: Joi.array()
                        .required()
                        .items(
                            Joi.object()
                                .required()
                                .keys({
                                    labelName: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    days: Joi.number()
                                        .required()
                                        .error((error) => error),
                                }),
                        ),
                    boosterDetails: Joi.array()
                        .required()
                        .items(
                            Joi.object()
                                .required()
                                .keys({
                                    labelName: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    days: Joi.number()
                                        .required()
                                        .error((error) => error),
                                }),
                        ),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const vaccineDetailsDeleteValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                vaccineId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                vaccineDetailsId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const vaccineDetailsUpdateValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                isActive: Joi.boolean()
                    .required()
                    .error((error) => error),
                isMandatory: Joi.boolean()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const IndependentCourseCreditHoursUpdateValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
    })
    .unknown(true);

const getIndependentCourseInputValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
    })
    .unknown(true);

const vaccineCategoryToggleMixedVaccineValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            vaccineId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            allow: Joi.boolean()
                .required()
                .error(() => 'ALLOW_MIXED_VACCINE'),
        }),
    })
    .unknown(true);
const phoneFieldValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            labelId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                defaultValue: Joi.string()
                    .required()
                    .error((error) => error),
                allowToChange: Joi.boolean()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const privacyValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                labelId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const resetLabelConfiguration = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
        body: Joi.object()
            .keys({
                labelName: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);

const allowCheckBoxValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                labelId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                type: Joi.string().error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
const notificationMailValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
            type: Joi.string().error((error) => error),
        }),
        body: Joi.object()
            .keys({
                labelBody: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);

const DepartmentSubjectValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                labelId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);

const departmentSubjectGetValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);

const DepartmentTypeValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            settingId: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => error),
        }),
        body: Joi.object()
            .keys({
                mode: Joi.string()
                    .required()
                    .valid(constants.DS_CREATE, constants.DS_UPDATE)
                    .error((error) => error),
                _id: Joi.string().alphanum().length(24).when('mode', {
                    is: constants.DS_UPDATE,
                    then: Joi.required(),
                    otherwise: Joi.optional(),
                }),
                labelName: Joi.string()
                    .required()
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);
const DepartmentTypeDeleteValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                settingId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
                labelId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
            })
            .unknown(false),
    })
    .unknown(true);
module.exports = {
    updateGlobalConfiguration,
    updateWorkingDays,
    breakValidation,
    eventTypeValidation,
    languageValidation,
    labelConfigurationValidation,
    ProgramTypeValidation,
    breakRemoveValidation,
    eventTypeUpdateValidation,
    labelConfigurationResetValidation,
    ProgramTypeRemoveValidation,
    IndependentCourseCreditHoursUpdateValidation,
    emailConfigValidation,
    updateEmailConfigValidation,
    addDesignationValidation,
    designationCheckValidation,
    designationUpdateValidation,
    designationDeleteValidation,
    designationGetValidation,
    labelFieldUpdateValidation,
    labelFieldCheckValidation,
    biometricCheckValidation,
    updateMailContentValidation,
    updateDocumentFormatValidation,
    updateMaximumSizeValidation,
    addDocumentCategoryValidation,
    editDocumentCategoryValidation,
    deleteDocumentCategoryValidation,
    addDocumentValidation,
    editDocumentValidation,
    deleteDocumentValidation,
    remainderMailValidation,
    mailSettingsValidation,
    vaccineCategoryValidation,
    vaccineCategoryEditValidation,
    vaccineDetailsAddValidation,
    vaccineDetailsEditValidation,
    vaccineDetailsDeleteValidation,
    vaccineDetailsUpdateValidation,
    getIndependentCourseInputValidator,
    vaccineCategoryToggleMixedVaccineValidation,
    phoneFieldValidation,
    privacyValidation,
    allowCheckBoxValidation,
    notificationMailValidation,
    DepartmentSubjectValidation,
    departmentSubjectGetValidation,
    resetLabelConfiguration,
    DepartmentTypeValidation,
    DepartmentTypeDeleteValidation,
};
