const Joi = require('joi');
const courseWarningValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        query: Joi.object().keys({
            institutionCalendarId: Joi.ObjectId(),
            studentId: Joi.ObjectId(),
        }),
    });
};
const studentAcknowledgeValidator = async () => {
    Joi.object().key({
        query: Joi.object().keys({
            _id: Joi.ObjectId().required(),
            user_Id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            acknowledge: Joi.boolean(),
        }),
    });
};

const staffAutoWarningValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().key({
            institutionCalendarId: Joi.ObjectId().required(),
            userId: Joi.ObjectId(),
            programId: Joi.ObjectId(),
            courseIds: Joi.array().items(Joi.ObjectId().required()),
            warningStatus: Joi.string(),
            acknowledgeStatus: Joi.boolean(),
            searchKey: Joi.string(),
        }),
    });
};
const getLmsWarningConfigValidator = async () => {
    Joi.object().key({
        query: Joi.object().keys({
            programId: Joi.ObjectId().required(),
            courseId: Joi.ObjectId().required(),
            warningConfigBasedOn: Joi.string().required(),
        }),
    });
};
const createLmsWarningConfigValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().key({
            courseId: Joi.ObjectId().required(),
            programId: Joi.ObjectId().required(),
            warningConfigBasedOn: Joi.string().required(),
            warningConfigLevels: Joi.array().items(Joi.ObjectId().required()),
        }),
    });
};
module.exports = {
    courseWarningValidator,
    studentAcknowledgeValidator,
    staffAutoWarningValidator,
    getLmsWarningConfigValidator,
    createLmsWarningConfigValidator,
};
