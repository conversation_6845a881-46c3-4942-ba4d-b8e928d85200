const { MulterError } = require('multer');
const { uploadfile3 } = require('../../utility/file_upload');
const faceUpload = uploadfile3.fields([
    { name: 'center', maxCount: 1 },
    { name: 'left', maxCount: 1 },
    { name: 'right', maxCount: 1 },
    { name: 'up', maxCount: 1 },
]);
const uploadFaceBiometric = (req, res, next) => {
    faceUpload(req, res, (err) => {
        if (err instanceof MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};
module.exports = { uploadFaceBiometric };
