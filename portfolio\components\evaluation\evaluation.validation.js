const { Joi } = require('../../common/middlewares/validation');

const assignFacultySchema = Joi.object({
    body: Joi.object({
        programId: Joi.string().hex().length(24).required(),
        courseId: Joi.string().hex().length(24).required(),
        institutionCalendarId: Joi.string().hex().length(24).required(),
        portfolioId: Joi.string().hex().length(24).required(),
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        role: Joi.object({
            _id: Joi.string().hex().length(24).optional(),
        }).optional(),
        evaluators: Joi.array().required(),
        deliveryTypes: Joi.array().required(),
    }),
}).unknown(true);

const getStudentSchema = Joi.object({
    query: Joi.object({
        programId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'PROGRAM_ID_REQUIRED' }),
        courseId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'COURSE_ID_REQUIRED' }),
        institutionCalendarId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'INSTITUTION_CALENDAR_ID_REQUIRED' }),
        portfolioId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'PORTFOLIO_ID_REQUIRED' }),
        componentId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'COMPONENT_ID_REQUIRED' }),
        childrenId: Joi.string()
            .hex()
            .length(24)
            .required()
            .messages({ 'any.required': 'CHILDREN_ID_REQUIRED' }),
        year: Joi.string().required(),
        level: Joi.string().required(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
        term: Joi.string().required(),
    }),
}).unknown(true);

const getEvaluatorListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: Joi.string().hex().length(24).required(),
        programId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getResponseSchema = Joi.object({
    query: Joi.object({
        responseId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const updateMarksSchema = Joi.object({
    body: Joi.object({
        programId: Joi.string().hex().length(24).required(),
        courseId: Joi.string().hex().length(24).required(),
        institutionCalendarId: Joi.string().hex().length(24).required(),
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        studentIds: Joi.any().optional(),
        marks: Joi.number().integer().min(0).optional(),
        rubrics: Joi.array().optional(),
    }),
}).unknown(true);

const getComponentsSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: Joi.string().hex().length(24).required(),
        programId: Joi.string().hex().length(24).required(),
        courseId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const rejectSubmissionSchema = Joi.object({
    query: Joi.object({
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const completeEvaluationSchema = Joi.object({
    body: Joi.object({
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        scheduleId: Joi.string().hex().length(24).optional(),
        studentIds: Joi.array().optional(),
    }),
}).unknown(true);

const getRubricsSchema = Joi.object({
    query: Joi.object({
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        formId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getEvaluatorListForAdminSchema = Joi.object({
    query: Joi.object({
        portfolioId: Joi.string().hex().length(24).required(),
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getInsightsSchema = Joi.object({
    query: Joi.object({
        portfolioId: Joi.string().hex().length(24).required(),
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        scheduleId: Joi.string().hex().length(24).optional(),
        studentId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getEvaluationCountSchema = Joi.object({
    query: Joi.object({
        portfolioId: Joi.string().hex().length(24).required(),
        componentId: Joi.string().hex().length(24).required(),
        childrenId: Joi.string().hex().length(24).required(),
        scheduleId: Joi.string().hex().length(24).optional(),
    }),
}).unknown(true);

module.exports = {
    assignFacultySchema,
    getStudentSchema,
    getEvaluatorListSchema,
    getResponseSchema,
    updateMarksSchema,
    getComponentsSchema,
    rejectSubmissionSchema,
    completeEvaluationSchema,
    getRubricsSchema,
    getEvaluatorListForAdminSchema,
    getInsightsSchema,
    getEvaluationCountSchema,
};
