const { STUDENT_GROUP, COURSE_SCHEDULE, USER, DIGI_COURSE } = require('../utility/constants');
const { send_email } = require('../utility/common_functions');
const { responseFunctionWithRequest, convertToMongoObjectId } = require('../utility/common');
const StudentGroup = require('mongoose').model(STUDENT_GROUP);
const CourseSchedule = require('mongoose').model(COURSE_SCHEDULE);
const User = require('mongoose').model(USER);
const course = require('mongoose').model(DIGI_COURSE);
const { dsGetAllWithSortAsJSON, get } = require('../base/base_controller');
const { INSTITUTION_NAME } = require('../utility/util_keys');
const getJSON = dsGetAllWithSortAsJSON;
const common_query = { isActive: true, isDeleted: false };

const getStaffIds = async (body) => {
    const { inst_cal_id, program_id, term, level, _course_id } = body;
    const cs_query = {
        ...common_query,
        _institution_calendar_id: inst_cal_id,
        _program_id: program_id,
        _course_id: convertToMongoObjectId(_course_id),
        term,
        level_no: level,
    };
    const cs_project = {
        'staffs._staff_id': 1,
    };
    const course_schedules = (await getJSON(CourseSchedule, cs_query, cs_project)).data;
    const staffIds = [];
    course_schedules.forEach((course_schedule) => {
        if (course_schedule.staffs) {
            course_schedule.staffs.forEach((staff) => {
                if (staff._staff_id) staffIds.push(convertToMongoObjectId(staff._staff_id));
            });
        }
    });
    return staffIds;
};

const getStudentIds = async (body) => {
    const { inst_cal_id, program_id, term, level, _course_id } = body;
    const sg_query = {
        ...common_query,
        _institution_calendar_id: inst_cal_id,
        'master._program_id': program_id,
        'groups.term': term,
        'groups.level': level,
        'groups.courses._course_id': convertToMongoObjectId(_course_id),
    };
    const sg_project = {
        'groups.courses': 1,
    };
    const StudentGroups = (await getJSON(StudentGroup, sg_query, sg_project)).data;
    let StudentIds = [];
    StudentGroups.forEach((StudentGroups) => {
        StudentGroups.groups.forEach((group) => {
            group.courses.forEach((courseElement) => {
                if (_course_id.toString() === courseElement._course_id.toString()) {
                    courseElement.setting.forEach((settingEle) => {
                        settingEle.session_setting.forEach((sessionElement) => {
                            sessionElement.groups.forEach((groupElement) => {
                                StudentIds = StudentIds.concat(groupElement._student_ids);
                            });
                        });
                    });
                }
            });
        });
    });
    StudentIds = [...new Set(StudentIds)];
    return StudentIds;
};

const getEmailOfStudentAndStaff = async (studentIds, staffIds) => {
    const ids = [...studentIds, ...staffIds];
    const u_query = {
        ...common_query,
        _id: { $in: ids },
    };
    const u_project = { email: 1 };
    const Users = (await getJSON(User, u_query, u_project)).data;
    return Users.map((user) => user.email);
};

exports.publish = async (req, res) => {
    try {
        const { _course_id } = req.body;
        const { status: c_status, data: c_data } = await get(
            course,
            {
                _id: convertToMongoObjectId(_course_id),
                isActive: true,
                isDeleted: false,
            },
            {
                course_name: 1,
                course_code: 1,
            },
        );
        if (!c_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSES_NOT_FOUND'),
                        req.t('COURSES_NOT_FOUND'),
                    ),
                );

        const StudentIds = await getStudentIds(req.body);
        const StaffIds = await getStaffIds(req.body);
        const EmailIds = await getEmailOfStudentAndStaff(StudentIds, StaffIds);
        const mailContent =
            '<p>' +
            req.t('DEAR') +
            ',' +
            ',<br>' +
            c_data.course_name +
            ' (' +
            c_data.course_code +
            '), ' +
            req.t('COURSE_TIME_TABLE_IS_PUBLISHED') +
            '<br>' +
            req.t('THANK_YOU') +
            '<br>' +
            INSTITUTION_NAME +
            '<br></p>';
        await send_email(EmailIds, req.t('DIGISCHEDULER_ALERT'), mailContent);
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('EMAIL_SENT_SUCCESSFULLY'), []),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(501)
            .send(
                responseFunctionWithRequest(req, 501, false, req.t('INTERNAL_SERVER_ISSUE'), error),
            );
    }
};
