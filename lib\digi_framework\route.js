const express = require('express');
const route = express.Router();
const controller = require('./controller');
const {
    userPolicyAuthentication,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
const validator = require('./validator');

route.get(
    '/filter_curriculum_year_program_standard_range_settings',
    [userPolicyAuthentication(['program_input:programs:view'])],
    controller.filter_curriculum_year_program_standard_range_settings,
);
route.get(
    '/curriculum_standard_range_settings/:program_id',
    controller.curriculum_standard_range_settings,
);
route.get('/:id', validator.framework_id, controller.get);
route.get('/', [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])], controller.getAll);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'program_input:programs:add_pre-requisite',
            'program_input:programs:add_program',
        ]),
    ],
    validator.framework,
    controller.insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'program_input:programs:add_pre-requisite',
            'program_input:programs:add_program',
        ]),
    ],
    validator.framework_id,
    validator.framework,
    controller.update,
);
route.delete('/:id', validator.framework_id, controller.delete);
route.post('/upsert_standard_range_settings', controller.upsert_standard_range_settings);

module.exports = route;
