const constants = require('../utility/constants');
const institution = require('mongoose').model(constants.INSTITUTION);
const Program = require('mongoose').model(constants.DIGI_PROGRAM);
const Curriculum = require('mongoose').model(constants.DIGI_CURRICULUM);
const Course = require('mongoose').model(constants.DIGI_COURSE);
const Framework = require('mongoose').model(constants.FRAMEWORK);
const SESSION_ORDER = require('mongoose').model(constants.DIGI_SESSION_ORDER);
const SESSION_DELIVERY_TYPE = require('mongoose').model(constants.DIGI_SESSION_DELIVERY_TYPES);
const common_files = require('../utility/common');
const base_control = require('../base/base_controller');
const ObjectId = common_files.convertToMongoObjectId;
const {
    response_function,
    clone,
    sendResult,
    sendErrorResponse,
    mergeStandardWithCurriculum,
    convertToMongoObjectId,
    sendResultWithRequest,
} = require('../utility/common');
const {
    get_list,
    dsGetAllWithSort,
    dsGetAllWithSortAsJSON,
    dsUpdate,
    update_condition_array_filter,
} = require('../base/base_controller');

const serverError = constants.DS_INTERNAL_SERVER_ERROR;
const cs = (str) => str.toString(); // convert string as (cs)
const {
    allCourseList,
    clearItem,
    allCurriculumDatas,
    allSessionOrderDatas,
} = require('../../service/cache.service');

// Updating Course Flat Caching Data
const updateCourseFlatCacheData = async () => {
    clearItem('allCourses');
    await allCourseList();
};

// Updating Curriculum Flat Caching Data
const updateCurriculumFlatCacheData = async () => {
    clearItem('allCurriculum');
    await allCurriculumDatas();
};

// Updating Session Flow Flat Caching Data
const updateSessionFlowFlatCacheData = async () => {
    clearItem('allSessionOrders');
    await allSessionOrderDatas();
};

const getMappedCurriculumOfCourses = async (courses) => {
    let AllCurriculums = (
        await dsGetAllWithSortAsJSON(Curriculum, { isDeleted: false }, {}, { order: 1 })
    ).data;
    const allCloIds = [];
    courses = courses.filter((course) => course.isDeleted === false);
    courses.forEach((course) => {
        course.framework.domains.forEach((domain) => {
            domain.clo.forEach((clo) => {
                allCloIds.push(cs(clo._id));
            });
        });
    });
    selectedCurriculums = [];
    const isSelectableCurriculum = (curriculum, allCloIds) => {
        for (const domain of curriculum.framework.domains) {
            for (const plo of domain.plo) {
                for (const clo of plo.clos) {
                    if (allCloIds.includes(cs(clo.clo_id))) return curriculum;
                }
            }
        }
    };
    AllCurriculums = AllCurriculums.filter((AllCurriculum) => AllCurriculum.framework._id);
    AllCurriculums.forEach((curriculum) => {
        selectedCurriculum = isSelectableCurriculum(curriculum, allCloIds);
        if (selectedCurriculum) {
            selectedCurriculums.push(selectedCurriculum);
        }
    });

    return selectedCurriculums;
};

const getCurriculumWithCourse = async (program_id) => {
    try {
        const aggregate = [
            {
                $lookup: {
                    from: 'digi_curriculums',
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'curriculums',
                },
            },
            { $match: { isDeleted: false, _id: ObjectId(program_id) } },
            { $unwind: '$curriculums' },
            {
                $lookup: {
                    from: 'digi_courses',
                    localField: 'curriculums._id',
                    foreignField: '_curriculum_id',
                    as: 'curriculums.courses',
                },
            },
            {
                $project: {
                    curriculums: {
                        $filter: {
                            input: ['$curriculums'],
                            as: 'curriculum',
                            cond: {
                                $and: [{ $eq: ['$$curriculum.isDeleted', false] }],
                            },
                        },
                    },
                },
            },
            {
                $project: {
                    'curriculums._id': 1,
                    'curriculums.curriculum_name': 1,
                    'curriculums.framework': 1,
                    'curriculums.standard_range_settings': 1,
                    'curriculums.year_level': 1,
                    'curriculums.start_at': 1,
                    'curriculums.end_at': 1,
                    'curriculums._program_id': 1,
                    'curriculums.courses.framework': 1,
                    'curriculums.courses._id': 1,
                    'curriculums.courses.isDeleted': 1,
                    'curriculums.courses.course_name': 1,
                    'curriculums.courses.course_assigned_details': 1,
                    // 'curriculums.year_level.levels.courses.framework.code': 1,
                },
            },
            {
                $project: {
                    'curriculums.framework.name': 0,
                    'curriculums.framework.domains.plo.isActive': 0,
                    // 'curriculums.framework.domains.plo.isDeleted': 0,
                    'curriculums.framework.domains.plo._id': 0,
                    'curriculums.framework.domains.plo.name': 0,
                    'curriculums.framework.domains.plo.clos.name': 0,
                    'curriculums.framework.domains.plo.clos.mapped_value': 0,
                    'curriculums.framework.domains.plo.clos.content_mapped_value': 0,
                    'curriculums.framework.domains.plo.clos.level_id': 0,
                    'curriculums.framework.domains.plo.clos.year_id': 0,
                    'curriculums.year_level.no_of_level': 0,
                    'curriculums.year_level.pre_requisite_name': 0,
                    'curriculums.year_level.levels.start_week': 0,
                    'curriculums.year_level.levels.end_week': 0,
                    'curriculums.courses.framework.name': 0,
                    'curriculums.courses.framework.domains.name': 0,
                    'curriculums.courses.framework.domains.clo.isActive': 0,
                    // 'curriculums.courses.framework.domains.clo.isDeleted': 0,
                    'curriculums.courses.framework.domains.clo.name': 0,
                    'curriculums.courses.framework.domains.clo.year_id': 0,
                    'curriculums.courses.course_assigned_details.course_duration': 0,
                    'curriculums.courses.course_assigned_details.curriculum_name': 0,
                    'curriculums.courses.course_assigned_details.isActive': 0,
                    // 'curriculums.courses.course_assigned_details.isDeleted': 0,
                    'curriculums.courses.course_assigned_details.mapping_type': 0,
                    'curriculums.courses.course_assigned_details.content_mapping_type': 0,
                },
            },
        ];
        const doc = await base_control.get_aggregate(Program, aggregate);
        if (!doc.status) return doc;
        // get All Shared course of this program start
        const c_query = {
            'course_assigned_details.course_shared_with._program_id': program_id,
            isDeleted: false,
            isActive: true,
        };
        const c_Options = {
            _id: 1,
            framework: 1,
            course_name: 1,
            course_assigned_details: 1,
        };
        const AllSharedCourses = (await dsGetAllWithSortAsJSON(Course, c_query, c_Options)).data;
        const findSharedCourse = (p_id, c_id, y_id, l_id) => {
            const sharedCourse = [];
            AllSharedCourses.forEach((course) => {
                course.isShareCourse = true;
                course.course_assigned_details.forEach((cAD) => {
                    cAD.course_shared_with.forEach((cSW) => {
                        if (
                            cs(cSW._program_id) === cs(p_id) &&
                            cs(cSW._curriculum_id) === cs(c_id) &&
                            cs(cSW._year_id) === cs(y_id) &&
                            cs(cSW._level_id) === cs(l_id)
                        ) {
                            sharedCourse.push(course);
                        }
                    });
                });
            });
            return sharedCourse;
        };
        // get All shared course of this program end
        for (const program of doc.data) {
            for (const curriculum of program.curriculums) {
                for (const year of curriculum.year_level) {
                    // for Shared course start
                    for (const level of year.levels) {
                        const sharedCourses = findSharedCourse(
                            program._id,
                            curriculum._id,
                            year._id,
                            level._id,
                        );
                        if (sharedCourses.length) {
                            sharedCourses.forEach((sharedCourse) => {
                                curriculum.courses.push(sharedCourse);
                            });
                        }
                    }
                    // for shared course end
                    if (year._pre_requisite_id) {
                        const course_query = {
                            isDeleted: false,
                            isActive: true,
                            _curriculum_id: ObjectId(year._pre_requisite_id),
                        };
                        const curriculum_query = {
                            isDeleted: false,
                            isActive: true,
                            _id: ObjectId(year._pre_requisite_id),
                        };
                        const get = dsGetAllWithSortAsJSON;
                        const curriculumResult = (await get(Curriculum, curriculum_query)).data;
                        if (curriculumResult[0]) {
                            year._id = curriculumResult[0].year_level[0]._id;
                            year.levels = curriculumResult[0].year_level[0].levels;
                        }
                        const preCourses = (await get(Course, course_query)).data;
                        preCourses.forEach((pCourse) => curriculum.courses.push(clone(pCourse)));
                    }
                }
            }
        }
        for (const program of doc.data) {
            for (const curriculum of program.curriculums) {
                if (!curriculum.framework._id) {
                    curriculum.frameworks = await getMappedCurriculumOfCourses(curriculum.courses);
                }
                curriculum.framework.domains.forEach((domain) => {
                    domain.mappedObject = {};
                    domain.plo.forEach((plo) => {
                        plo.clos.forEach((clo) => {
                            const key = clo.year_name;
                            if (!domain.mappedObject[key]) domain.mappedObject[key] = [];
                            // domain.mappedObject[key].push(cs(clo.clo_id));
                        });
                    });
                });
                curriculum.year_level.forEach((year_level) => {
                    year_level.levels.forEach((level) => {
                        level.courses = [];
                        level.domains = clone(curriculum.framework.domains);
                        curriculum.courses.forEach((course) => {
                            course.course_assigned_details.forEach((cad) => {
                                const getCondition1 = () => {
                                    return (
                                        cs(cad._program_id) === cs(curriculum._program_id) &&
                                        cs(cad._curriculum_id) === cs(curriculum._id) &&
                                        cs(cad._year_id) === cs(year_level._id) &&
                                        cs(cad._level_id) === cs(level._id) &&
                                        course.isDeleted === false
                                    );
                                };
                                const getCondition2 = () => {
                                    return cad.course_shared_with.filter(
                                        (cSW) =>
                                            cs(cSW._program_id) === cs(curriculum._program_id) &&
                                            cs(cSW._curriculum_id) === cs(curriculum._id) &&
                                            cs(cSW.year) === cs(year_level.y_type) &&
                                            cs(cSW.level_no) === cs(level.level_name) &&
                                            course.isShareCourse === true,
                                    ).length;
                                };
                                const getCondition3 = () => {
                                    return (
                                        year_level._pre_requisite_id &&
                                        course._curriculum_id &&
                                        cs(course._curriculum_id) ===
                                            cs(year_level._pre_requisite_id) &&
                                        cs(cad._level_id) === cs(level._id) &&
                                        course.isDeleted === false
                                    );
                                };
                                if (
                                    getCondition1() ||
                                    getCondition2() ||
                                    getCondition3() /*  ||
                                    year_level._pre_requisite_id */
                                ) {
                                    // course level mapped count start
                                    const cloIds = [];
                                    course.framework.domains.forEach((domain) => {
                                        domain.clo.forEach((clo) => {
                                            cloIds.push(cs(clo._id));
                                        });
                                    });
                                    const domains = clone(curriculum.framework.domains);
                                    domains.forEach((domain, domainIndex) => {
                                        domain.plo.forEach((plo, ploIndex) => {
                                            plo.mappedCount = 0;
                                            plo.clos.forEach((clo) => {
                                                if (cloIds.includes(cs(clo.clo_id))) {
                                                    if (
                                                        !level.domains[domainIndex].plo[ploIndex]
                                                            .mappedCount
                                                    ) {
                                                        level.domains[domainIndex].plo[
                                                            ploIndex
                                                        ].mappedCount = 0;
                                                    }
                                                    level.domains[domainIndex].plo[ploIndex]
                                                        .mappedCount++;
                                                    plo.mappedCount++;
                                                    if (
                                                        !curriculum.framework.domains[domainIndex]
                                                            .mappedObject[year_level.y_type]
                                                    ) {
                                                        curriculum.framework.domains[
                                                            domainIndex
                                                        ].mappedObject[year_level.y_type] = [];
                                                    }
                                                    curriculum.framework.domains[
                                                        domainIndex
                                                    ].mappedObject[year_level.y_type].push(
                                                        clo.clo_id,
                                                    );
                                                }
                                            });
                                            plo.clos = undefined;
                                        });
                                    });
                                    course = clone(course);
                                    course.course_assigned_details = undefined;
                                    course.framework = {
                                        code:
                                            course.framework && course.framework.code
                                                ? course.framework.code
                                                : '',
                                    };
                                    course.domains = domains;
                                    // course level mapped count
                                    level.courses.push(course);
                                }
                            });
                        });
                    });
                });
            }
        }
        const newCurriculums = [];
        doc.data.forEach((program) => {
            program.curriculums.forEach((curriculum) => {
                newCurriculums.push(curriculum);
            });
        });
        doc.data[0].curriculums = newCurriculums;
        return doc;
    } catch (error) {
        console.log(error);
        return error;
    }
};

exports.getCurriculum = async (req, res) => {
    const { program_id } = req.params;
    const get = dsGetAllWithSortAsJSON;
    const order = { order: 1 };
    const project = {};
    try {
        const result = await getCurriculumWithCourse(program_id);
        if (!result.status) return sendResult(res, 200, req.t('SUCCESS'), { curriculums: [] });
        const program = result.data[0];
        const frameworks = (
            await get(Framework, { isDeleted: false, isActive: true }, project, order)
        ).data;
        program.curriculums.forEach((curriculum) => {
            // apply standard range settings
            let standard_range_settings = [];
            if (curriculum.framework._id) {
                const selectedFramework = frameworks.filter(
                    (framework) => cs(framework._id) === cs(curriculum.framework._id),
                );
                if (selectedFramework[0])
                    standard_range_settings = selectedFramework[0].standard_range_settings;
            }
            // apply domain wise clos count
            curriculum.framework.domains.forEach((domain) => {
                domain.maxClos = 0;
                domain.plo.forEach((plo) => {
                    domain.maxClos += plo.clos.length;
                });
                domain.maxYear = curriculum.end_at;
            });
            // prepare graph data
            curriculum.hasFramework = !!curriculum.framework._id;
            curriculum.default_standard_range_settings = mergeStandardWithCurriculum(
                clone(standard_range_settings),
                'standard',
                clone(curriculum),
            ).filter((srSetting) => !srSetting.year_program.includes('_PRE'));
            curriculum.standard_range_settings = mergeStandardWithCurriculum(
                clone(standard_range_settings),
                'curriculum',
                clone(curriculum),
            ).filter((srSetting) => !srSetting.year_program.includes('_PRE'));
            // removable
            curriculum.courses = undefined;
        });
        sendResult(res, 200, req.t('CLO_PLO_GRAPH_DATA'), program);
    } catch (error) {
        console.log(error);
        sendErrorResponse(res, 404, serverError, error);
    }
};

const getCurriculumWithCourseWithoutFramework = async (
    program_id,
    exit_curriculum_id,
    exit_curriculum,
) => {
    try {
        const aggregate = [
            {
                $lookup: {
                    from: 'digi_curriculums',
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'curriculums',
                },
            },
            { $match: { isDeleted: false, _id: ObjectId(program_id) } },
            { $unwind: '$curriculums' },
            {
                $lookup: {
                    from: 'digi_courses',
                    localField: 'curriculums._id',
                    foreignField: '_curriculum_id',
                    as: 'curriculums.courses',
                },
            },
            {
                $project: {
                    curriculums: {
                        $filter: {
                            input: ['$curriculums'],
                            as: 'curriculum',
                            cond: {
                                $and: [
                                    { $eq: ['$$curriculum.isDeleted', false] },
                                    { $eq: ['$$curriculum._id', ObjectId(exit_curriculum_id)] },
                                ],
                            },
                        },
                    },
                },
            },
            {
                $project: {
                    'curriculums._id': 1,
                    'curriculums.curriculum_name': 1,
                    'curriculums.framework': 1,
                    'curriculums.standard_range_settings': 1,
                    'curriculums.year_level': 1,
                    'curriculums.start_at': 1,
                    'curriculums.end_at': 1,
                    'curriculums._program_id': 1,
                    'curriculums.courses.framework': 1,
                    'curriculums.courses._id': 1,
                    'curriculums.courses.isDeleted': 1,
                    'curriculums.courses.course_name': 1,
                    'curriculums.courses.course_assigned_details': 1,
                },
            },
        ];
        const doc = await base_control.get_aggregate(Program, aggregate);
        if (!doc.status) return doc;
        const c_Options = {
            _id: 1,
            framework: 1,
            course_name: 1,
            course_assigned_details: 1,
        };
        for (const program of doc.data) {
            for (const curriculum of program.curriculums) {
                for (const year of curriculum.year_level) {
                    if (year._pre_requisite_id) {
                        const course_query = {
                            isDeleted: false,
                            _curriculum_id: ObjectId(year._pre_requisite_id),
                        };
                        const curriculum_query = {
                            isDeleted: false,
                            _id: ObjectId(year._pre_requisite_id),
                        };
                        const get = dsGetAllWithSortAsJSON;
                        const curriculumResult = (await get(Curriculum, curriculum_query)).data;
                        if (curriculumResult[0]) {
                            year._id = curriculumResult[0].year_level[0]._id;
                            year.levels = curriculumResult[0].year_level[0].levels;
                        }
                        const preCourses = (await get(Course, course_query, c_Options)).data;
                        preCourses.forEach((pCourse) => curriculum.courses.push(clone(pCourse)));
                    }
                }
            }
        }
        for (const program of doc.data) {
            for (const curriculum of program.curriculums) {
                if (!curriculum.framework._id) {
                    curriculum.framework = exit_curriculum.framework;
                }
                curriculum.framework.domains.forEach((domain) => {
                    domain.mappedObject = {};
                    domain.plo.forEach((plo) => {
                        plo.clos.forEach((clo) => {
                            if (!domain.mappedObject[clo.year_name])
                                domain.mappedObject[clo.year_name] = [];
                            // domain.mappedObject[clo.year_name].push(cs(clo.clo_id));
                        });
                    });
                });
                curriculum.year_level.forEach((year_level) => {
                    year_level.levels.forEach((level) => {
                        level.courses = [];
                        level.domains = clone(curriculum.framework.domains);
                        curriculum.courses.forEach((course) => {
                            course.course_assigned_details.forEach((cad) => {
                                if (
                                    cs(cad._year_id) === cs(year_level._id) &&
                                    cs(cad._level_id) === cs(level._id) &&
                                    course.isDeleted === false
                                ) {
                                    // course level mapped count start
                                    const cloIds = [];
                                    course.framework.domains.forEach((domain) => {
                                        domain.clo.forEach((clo) => {
                                            cloIds.push(cs(clo._id));
                                        });
                                    });
                                    const domains = clone(curriculum.framework.domains);
                                    domains.forEach((domain, domainIndex) => {
                                        domain.plo.forEach((plo, ploIndex) => {
                                            plo.mappedCount = 0;
                                            plo.clos.forEach((clo) => {
                                                if (cloIds.includes(cs(clo.clo_id))) {
                                                    if (
                                                        !level.domains[domainIndex].plo[ploIndex]
                                                            .mappedCount
                                                    ) {
                                                        level.domains[domainIndex].plo[
                                                            ploIndex
                                                        ].mappedCount = 0;
                                                    }
                                                    level.domains[domainIndex].plo[ploIndex]
                                                        .mappedCount++;
                                                    plo.mappedCount++;
                                                    curriculum.framework.domains[
                                                        domainIndex
                                                    ].mappedObject[year_level.y_type].push(
                                                        clo.clo_id,
                                                    );
                                                }
                                            });
                                        });
                                    });
                                    course = clone(course);
                                    course.domains = domains;
                                    // course level mapped count
                                    level.courses.push(course);
                                }
                            });
                        });
                    });
                });
            }
        }
        const newCurriculums = [];
        doc.data.forEach((program) => {
            program.curriculums.forEach((curriculum) => {
                newCurriculums.push(curriculum);
            });
        });
        doc.data[0].curriculums = newCurriculums;
        return doc;
    } catch (error) {
        console.log(error);
        return error;
    }
};

exports.getCurriculumWithoutFramework = async (req, res) => {
    const { program_id, curriculum_id, curriculum } = req.body;
    const existCurriculum = curriculum;
    const get = dsGetAllWithSortAsJSON;
    const order = { order: 1 };
    const project = {};
    try {
        const result = await getCurriculumWithCourseWithoutFramework(
            program_id,
            curriculum_id,
            curriculum,
        );
        if (!result.status) return sendErrorResponse(res, 404, serverError, 'error');
        const program = result.data[0];
        const frameworks = (await get(Framework, { isDeleted: false }, project, order)).data;
        program.curriculums.forEach((curriculum) => {
            if (cs(curriculum._id) === cs(curriculum_id)) {
                curriculum.framework = existCurriculum.framework;
                // apply standard range settings
                let standard_range_settings = [];
                if (curriculum.framework._id) {
                    const selectedFramework = frameworks.filter(
                        (framework) => cs(framework._id) === cs(curriculum.framework._id),
                    );
                    if (selectedFramework[0])
                        standard_range_settings = selectedFramework[0].standard_range_settings;
                }
                // apply domain wise clos count
                curriculum.framework.domains.forEach((domain) => {
                    domain.maxClos = 0;
                    domain.plo.forEach((plo) => {
                        domain.maxClos += plo.clos.length;
                    });
                    domain.maxYear = curriculum.end_at;
                });
                // prepare graph data
                curriculum.hasFramework = !!curriculum.framework._id;
                curriculum.default_standard_range_settings = mergeStandardWithCurriculum(
                    clone(standard_range_settings),
                    'standard',
                    clone(curriculum),
                );
                curriculum.standard_range_settings = mergeStandardWithCurriculum(
                    clone(standard_range_settings),
                    'curriculum',
                    clone(curriculum),
                );
            }
            delete curriculum.courses;
        });
        sendResult(res, 200, req.t('CLO_PLO_GRAPH_DATA'), program.curriculums[0]);
    } catch (error) {
        console.log(error);
        sendErrorResponse(res, 404, serverError, error);
    }
};

exports.getDashboard = async (req, res) => {
    const query = { isDeleted: false };
    const order = { order: 1 };
    const project = {};
    try {
        const get = dsGetAllWithSort;
        const programs = await get(Program, query, project, order);
        const curriculums = await get(Curriculum, query, project, order);
        const courseResult = await get(Course, query, project, order);
        const courses = courseResult.data.filter((course) => course.course_assigned_details.length);
        const sessionOrderResult = await get(SESSION_ORDER, query, project, order);
        const sessionOrders = sessionOrderResult.data;
        const results = [];
        programs.data.forEach((program) => {
            const tempObj = { ...program._doc, curriculums: [] };
            curriculums.data.forEach((curriculum) => {
                if (curriculum._program_id.toString() === program._id.toString()) {
                    //PLO Count
                    let plo_count = 0;
                    if ('framework' in curriculum) {
                        if ('domains' in curriculum.framework) {
                            curriculum.framework.domains.forEach((cur) => {
                                if ('plo' in cur) plo_count += cur.plo.length;
                            });
                        }
                    }

                    const tempCurriculum = {
                        ...curriculum._doc,
                        year_level: [],
                        total_course: 0,
                        total_clo_done: 0,
                        total_plo_clo: 0,
                    };
                    tempCurriculum.plo_count = plo_count;
                    curriculum.year_level.forEach((year) => {
                        const tempYearObj = { ...year._doc, levels: [] };
                        year.levels.forEach((level) => {
                            const tempLevelObj = { ...level._doc, courses: [] };
                            courses.forEach((course) => {
                                //CLO count
                                let clo_count = 0;
                                if ('framework' in course) {
                                    if (course.framework.domains) {
                                        course.framework.domains.forEach((domain) => {
                                            if ('clo' in domain) clo_count += domain.clo.length;
                                        });
                                    }
                                }

                                course.course_assigned_details.forEach((course_assigned_detail) => {
                                    const cs = (val) => val.toString();
                                    const cad = course_assigned_detail;
                                    const yearId = cs(cad._year_id);
                                    const levelId = cs(cad._level_id);
                                    if (yearId === cs(year._id) && levelId === cs(level._id)) {
                                        const sessionOrder_temp = sessionOrders.find(
                                            (sessionOrder) =>
                                                sessionOrder._course_id.toString() ===
                                                course._id.toString(),
                                        );

                                        //Session order slo count
                                        let slo_count = 0;
                                        if (sessionOrder_temp) {
                                            if ('session_flow_data' in sessionOrder_temp) {
                                                sessionOrder_temp.session_flow_data.forEach(
                                                    (ele) => {
                                                        if ('slo' in ele)
                                                            slo_count += ele.slo.length;
                                                    },
                                                );
                                            }
                                        }

                                        const sessionOrder = sessionOrder_temp
                                            ? {
                                                  isDeleted: sessionOrder_temp.isDeleted,
                                                  isActive: sessionOrder_temp.isActive,
                                                  _id: sessionOrder_temp._id,
                                                  session_flow_data:
                                                      sessionOrder_temp.session_flow_data,
                                                  _course_id: sessionOrder_temp._course_id,
                                                  _program_id: sessionOrder_temp._program_id,
                                                  createdAt: sessionOrder_temp.createdAt,
                                                  updatedAt: sessionOrder_temp.updatedAt,
                                                  slo_count,
                                                  clo_slo_count: 0,
                                              }
                                            : undefined;
                                        tempLevelObj.courses.push({
                                            ...course._doc,
                                            content_mapping_state: 'Inprogress',
                                            plo_clo_count: 0,
                                            sessionOrder,
                                            clo_count,
                                        });
                                    }
                                });
                            });
                            tempCurriculum.total_course += tempLevelObj.courses.length;
                            tempYearObj.levels.push(tempLevelObj);
                        });
                        tempCurriculum.year_level.push(tempYearObj);
                    });
                    tempObj.curriculums.push(tempCurriculum);
                }
            });
            results.push(tempObj);
        });
        sendResult(res, 200, programs.message, results);
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.addFramework = async (req, res) => {
    const get = dsGetAllWithSort;
    const { framework_id, framework_for, course_id, curriculum_id } = req.body;
    if (framework_for === 'course') {
        const where = { isDeleted: false, _id: course_id };
        const order = { order: 1 };
        const select = {};
        const courseResult = await get(Course, where, select, order);
        const course = courseResult.data[0];
        if (course.framework._id) {
            let mappedCount = 0;
            const existingFramework = course.framework;
            existingFramework.domains.forEach((domain) => {
                domain.clo.forEach((clo) => {
                    mappedCount += clo.slos.length;
                });
            });
            if (mappedCount)
                return sendErrorResponseWithRequest(
                    req,
                    res,
                    406,
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                    req.t('VALIDATION_ERROR'),
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                );
        }
        const frameworkResult = await Framework.find({ _id: framework_id });
        const framework = frameworkResult ? frameworkResult[0] : { domains: [] };
        const data = { ...course._doc, framework };
        const updateResult = await dsUpdate(Course, course_id, data);
        // remove all mapping type of all courses of curriculum
        const query_1 = { _id: course_id, course_assigned_details: { $exists: true } };
        const obj_1 = { $set: { 'course_assigned_details.$.mapping_type': '' } };
        const array_filter = { multi: true };
        // send results
        const doc_1 = await base_control.update_many_with_array_filter(
            Course,
            query_1,
            obj_1,
            array_filter,
        );
        updateCourseFlatCacheData();
        if (doc_1.status && updateResult.success)
            return sendResult(res, 200, req.t('UPDATE_FRAMEWORK'), updateResult);
        return sendErrorResponseWithRequest(
            req,
            res,
            406,
            req.t('ERROR'),
            req.t('ERROR'),
            req.t('ERROR_ON_UPDATE_FRAMEWORK'),
        );
    }
    // Update in all courses
    const course_ids = await base_control.get_list(
        Course,
        { isDeleted: false, _curriculum_id: ObjectId(curriculum_id) },
        { _id: 1, framework: 1 },
    );
    if (!course_ids.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('COURSE_NOT_FOUND'),
                    req.t('COURSE_NOT_FOUND'),
                ),
            );
    for (courseElement of course_ids.data) {
        if (courseElement.framework._id) {
            let mappedCount = 0;
            const existingFramework = courseElement.framework;
            existingFramework.domains.forEach((domain) => {
                domain.clo.forEach((clo) => {
                    mappedCount += clo.slos.length;
                });
            });
            if (mappedCount)
                return res
                    .status(406)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                            req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                        ),
                    );
        }
    }
    const where = { isDeleted: false, _id: curriculum_id };
    const order = { order: 1 };
    const select = {};
    const curriculumResult = await get(Curriculum, where, select, order);
    const curriculum = curriculumResult.data[0];
    if (curriculum.framework._id) {
        let mappedCount = 0;
        const existingFramework = curriculum.framework;
        existingFramework.domains.forEach((domain) => {
            domain.plo.forEach((plo) => {
                if (!plo.isDeleted) mappedCount += plo.clos.length;
            });
        });
        if (mappedCount)
            return sendErrorResponseWithRequest(
                req,
                res,
                406,
                req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_CURRICULUM_HAS_MAPPING'),
                req.t('VALIDATION_ERROR'),
                req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_CURRICULUM_HAS_MAPPING'),
            );
    }
    const frameworkResult = await Framework.find({ _id: framework_id });
    const framework = frameworkResult ? frameworkResult[0] : { domains: [] };
    const data = { ...curriculum._doc, framework, mapping_type: '', standard_range_settings: [] };
    const updateResult = await dsUpdate(Curriculum, curriculum_id, data);
    updateCurriculumFlatCacheData();
    if (updateResult.success) {
        const ids = [];
        for (let i = 0; i < course_ids.data.length; i++) ids.push(ObjectId(course_ids.data[i]._id));
        const query = { _id: { $in: ids } };
        const obj = { $set: { framework } };
        const doc = await base_control.dsUpdateMany(Course, query, obj);
        // remove all mapping type of all courses of curriculum
        const query_1 = { _id: { $in: ids }, course_assigned_details: { $exists: true } };
        const obj_1 = { $set: { 'course_assigned_details.$[cur].mapping_type': '' } };
        const array_filter = {
            multi: true,
            arrayFilters: [{ 'cur._curriculum_id': curriculum_id }],
        };
        const doc_1 = await base_control.update_many_with_array_filter(
            Course,
            query_1,
            obj_1,
            array_filter,
        );
        updateCourseFlatCacheData();
        //  send results
        if (doc.success && doc_1.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('FRAMEWORK_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_ADD_FRAMEWORK'),
                    [],
                ),
            );
    }
    const response = prepare(res, 200, false, req.t('UNABLE_TO_ADD_FRAMEWORK'), []);
    return res.status(410).send(response);
};

exports.removeFramework = async (req, res) => {
    const get = dsGetAllWithSort;
    const { framework_for, course_id, curriculum_id } = req.body;
    if (framework_for === 'course') {
        const where = { isDeleted: false, _id: course_id };
        const order = { order: 1 };
        const select = {};
        const courseResult = await get(Course, where, select, order);
        const course = courseResult.data[0];
        if (course.framework._id) {
            let mappedCount = 0;
            const existingFramework = course.framework;
            existingFramework.domains.forEach((domain) => {
                domain.clo.forEach((clo) => {
                    mappedCount += clo.slos.length;
                });
            });
            if (mappedCount)
                return sendErrorResponse(
                    res,
                    406,
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                    req.t('VALIDATION_ERROR'),
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                );
        }
        const data = { ...course._doc, framework: { domains: [] } };
        const updateResult = await dsUpdate(Course, course_id, data);
        updateCourseFlatCacheData();
        sendResult(res, 200, req.t('REMOVE_FRAMEWORK'), updateResult);
    } else {
        // Update in all courses
        const course_ids = await base_control.get_list(
            Course,
            { isDeleted: false, _curriculum_id: ObjectId(curriculum_id) },
            { _id: 1, framework: 1 },
        );
        if (!course_ids.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        for (courseElement of course_ids.data) {
            if (courseElement.framework._id) {
                let mappedCount = 0;
                const existingFramework = courseElement.framework;
                existingFramework.domains.forEach((domain) => {
                    domain.clo.forEach((clo) => {
                        mappedCount += clo.slos.length;
                    });
                });
                if (mappedCount)
                    return res
                        .status(406)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                                req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING'),
                            ),
                        );
            }
        }
        const where = { isDeleted: false, _id: curriculum_id };
        const order = { order: 1 };
        const select = {};
        const curriculumResult = await get(Curriculum, where, select, order);
        const curriculum = curriculumResult.data[0];
        if (curriculum.framework._id) {
            let mappedCount = 0;
            const existingFramework = curriculum.framework;
            existingFramework.domains.forEach((domain) => {
                domain.plo.forEach((plo) => {
                    mappedCount += plo.clos.length;
                });
            });
            if (mappedCount)
                return sendErrorResponse(
                    res,
                    406,
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_CURRICULUM_HAS_MAPPING'),
                    req.t('VALIDATION_ERROR'),
                    req.t('YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_CURRICULUM_HAS_MAPPING'),
                );
        }
        const data = {
            ...curriculum._doc,
            framework: { domains: [] },
            mapping_type: '',
            standard_range_settings: [],
        };
        const updateResult = await dsUpdate(Curriculum, curriculum_id, data);
        updateCurriculumFlatCacheData();
        if (updateResult.success) {
            const ids = [];
            for (let i = 0; i < course_ids.data.length; i++)
                ids.push(ObjectId(course_ids.data[i]._id));
            //return res.send(ids)
            const query = { _id: { $in: ids } };
            const obj = { $set: { framework: { domains: [] } } };
            const doc = await base_control.dsUpdateMany(Course, query, obj);
            // remove all mapping type of all courses of curriculum
            const query_1 = { _id: { $in: ids }, course_assigned_details: { $exists: true } };
            const obj_1 = { $set: { 'course_assigned_details.$[cur].mapping_type': '' } };
            const array_filter = {
                multi: true,
                arrayFilters: [{ 'cur._curriculum_id': curriculum_id }],
            };
            const doc_1 = await base_control.update_many_with_array_filter(
                Course,
                query_1,
                obj_1,
                array_filter,
            );
            updateCourseFlatCacheData();
            // send results
            if (doc.success && doc_1.status)
                return res
                    .status(200)
                    .send(
                        common_files.response_function(
                            res,
                            200,
                            true,
                            req.t('FRAMEWORK_ADDED_SUCCESSFULLY'),
                            doc.data,
                        ),
                    );
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_ADD_FRAMEWORK'),
                        [],
                    ),
                );
        }
        const response = prepare(res, 200, false, req.t('UNABLE_TO_REMOVE_FRAMEWORK'), []);
        return res.status(410).send(response);
    }
};

exports.addPLO = async (req, res) => {
    const query = { _id: ObjectId(req.body.curriculum_id) };
    const obj = { $push: { 'framework.domains.$[i].plo': req.body.plo } };
    const filter = { arrayFilters: [{ 'i._id': req.body.domain_id }] };
    const doc = await update_condition_array_filter(Curriculum, query, obj, filter);
    updateCurriculumFlatCacheData();
    const prepare = response_function;
    if (doc.status) {
        const response = prepare(res, 201, true, req.t('PLO_ADDED_SUCCESSFULLY'), doc);
        return res.status(201).send(response);
    }
    const response = prepare(res, 200, false, req.t('UNABLE_TO_ADD_PLO'), []);
    return res.status(410).send(response);
};

exports.updatePLO = async (req, res) => {
    const query = { _id: ObjectId(req.body.curriculum_id) };
    const obj = {
        $set: {
            'framework.domains.$[i].plo.$[j].no': req.body.plo.no,
            'framework.domains.$[i].plo.$[j].name': req.body.plo.name,
        },
    };
    const filter = {
        arrayFilters: [{ 'i._id': req.body.domain_id }, { 'j._id': req.params.id }],
    };
    const doc = await update_condition_array_filter(Curriculum, query, obj, filter);
    updateCurriculumFlatCacheData();
    const prepare = response_function;
    if (doc.status) {
        const response = prepare(res, 201, true, req.t('PLO_UPDATED_SUCCESSFULLY'), doc);
        return res.status(201).send(response);
    }
    const response = prepare(res, 200, false, req.t('UNABLE_TO_UPDATE_PLO'), []);
    return res.status(410).send(response);
};

exports.deletePLO = async (req, res) => {
    const query = { _id: ObjectId(req.params.curriculum_id) };
    const curriculums = (await dsGetAllWithSort(Curriculum, query, {}, { order: 1 })).data;
    if (curriculums[0]) {
        const isPloRemovable = () => {
            let isRemovable = 0;
            curriculums[0].framework.domains.forEach((domain) => {
                if (domain.plo) {
                    domain.plo.forEach((plo) => {
                        if (plo._id.toString() === req.params.id.toString())
                            isRemovable = plo.clos.length;
                    });
                }
            });
            return isRemovable;
        };
        if (isPloRemovable()) {
            return sendErrorResponse(
                res,
                406,
                req.t('ERROR_ON_DELETE_PLO'),
                req.t('ERROR'),
                req.t('PLO_IS_MAPPED_WITH_CLO_CAN_NOT_BE_DELETED'),
            );
        }
    }
    const obj = { $set: { 'framework.domains.$[i].plo.$[j].isDeleted': true } };
    const filter = {
        arrayFilters: [{ 'i._id': req.params.domain_id }, { 'j._id': req.params.id }],
    };
    const doc = await update_condition_array_filter(Curriculum, query, obj, filter);
    updateCurriculumFlatCacheData();
    const prepare = response_function;
    if (doc.status) {
        const data = doc.data;
        const response = prepare(res, 201, true, req.t('PLO_DELETED_SUCCESSFULLY'), data);
        return res.status(201).send(response);
    }
    const response = response_function(res, 200, false, req.t('UNABLE_TO_DELETE'), []);
    return res.status(410).send(response);
};

exports.getPLO = async (req, res) => {
    const prepare = response_function;
    try {
        const query = { isDeleted: false, _id: req.params.curriculum_id };
        const doc = await base_control.get(Curriculum, query, { framework: 1 });
        if (!doc.status) {
            const response = prepare(res, 200, false, req.t('ERROR'), []);
            return res.status(200).send(response);
        }
        const ind = doc.data.framework.domains.findIndex((ele) =>
            ele._id.equals(req.params.domain_id),
        );
        let data = [];
        if (ind != -1) data = doc.data.framework.domains[ind].plo;
        plo_data = data.filter((ele) => ele.isDeleted == false);

        const response = prepare(res, 200, true, req.t('PLO_LIST'), plo_data);
        return res.status(200).send(response);
    } catch (error) {
        const response = prepare(res, 500, false, req.t('ERROR'), error.toString());
        return res.status(500).send(response);
    }
};

exports.addCLO = async (req, res) => {
    const query = { _id: ObjectId(req.body.course_id) };
    const obj = {
        $push: {
            'framework.domains.$[i].clo': req.body.clo,
        },
    };
    const filter = {
        arrayFilters: [{ 'i._id': req.body.domain_id }],
    };

    const doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
    updateCourseFlatCacheData();
    if (doc.status)
        return res
            .status(201)
            .send(
                common_files.response_function(
                    res,
                    201,
                    true,
                    req.t('CLO_ADDED_SUCCESSFULLY'),
                    doc,
                ),
            );
    return res
        .status(410)
        .send(common_files.response_function(res, 200, false, req.t('UNABLE_TO_ADD_CLO'), []));
};

exports.update_clo = async (req, res) => {
    const query = { _id: ObjectId(req.body.course_id) };
    const obj = {
        $set: {
            'framework.domains.$[i].clo.$[j].no': req.body.clo.no,
            'framework.domains.$[i].clo.$[j].name': req.body.clo.name,
        },
    };
    const filter = {
        arrayFilters: [{ 'i._id': req.body.domain_id }, { 'j._id': req.params.id }],
    };

    const doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
    updateCourseFlatCacheData();
    if (doc.status)
        return res
            .status(201)
            .send(
                common_files.response_function(
                    res,
                    201,
                    true,
                    req.t('CLO_UPDATED_SUCCESSFULLY'),
                    doc,
                ),
            );
    return res
        .status(410)
        .send(common_files.response_function(res, 200, false, req.t('UNABLE_TO_UPDATE_CLO'), []));
};

exports.delete_clo = async (req, res) => {
    const curriculums = await Curriculum.find({
        isDeleted: false,
        'framework.domains.plo.clos': { $elemMatch: { clo_id: cs(req.params.id) } },
    }).sort({ order: 1 });
    const courses = await Course.find({
        isDeleted: false,
        'framework.domains.clo': { $elemMatch: { _id: ObjectId(req.params.id) } },
    }).sort({ order: 1 });
    let totalSlos = 0;
    courses.forEach((course) => {
        course.framework.domains.forEach((domain) => {
            domain.clo.forEach((clo) => {
                if (clo.isDeleted === false && cs(clo._id) === cs(req.params.id)) {
                    totalSlos += clo.slos.length;
                }
            });
        });
    });
    if (totalSlos > 0)
        return sendErrorResponse(
            res,
            406,
            req.t('VALIDATION_ERROR'),
            req.t('ERROR'),
            req.t('CLO_IS_MAPPED_WITH_SLO_CAN_NOT_BE_DELETED'),
        );
    if (curriculums.length)
        return sendErrorResponse(
            res,
            406,
            req.t('VALIDATION_ERROR'),
            req.t('ERROR'),
            req.t('CLO_IS_MAPPED_WITH_PLO_CAN_NOT_BE_DELETED'),
        );
    const query = { _id: ObjectId(req.params.course_id) };
    const obj = {
        $set: {
            'framework.domains.$[i].clo.$[j].isDeleted': true,
        },
    };
    const filter = {
        arrayFilters: [{ 'i._id': req.params.domain_id }, { 'j._id': req.params.id }],
    };

    const doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
    updateCourseFlatCacheData();
    if (doc.status)
        return res
            .status(201)
            .send(
                common_files.response_function(
                    res,
                    201,
                    true,
                    req.t('CLO_DELETED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    return res
        .status(410)
        .send(common_files.response_function(res, 200, false, req.t('UNABLE_TO_ADD_CLO'), []));
};

exports.list_clo = async (req, res) => {
    try {
        const query = { isDeleted: false, _id: req.params.course_id };
        const doc = await base_control.get(Course, query, { framework: 1 });
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, req.t('ERROR'), []));

        const ind = doc.data.framework.domains.findIndex((ele) =>
            ele._id.equals(req.params.domain_id),
        );
        let data = [];
        if (ind != -1) data = doc.data.framework.domains[ind];

        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('CLO_LIST'), data));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.getMappingTree = async (req, res) => {
    const { program_id, curriculum_id, course_id } = req.body;
    const prepare = response_function;
    const get = dsGetAllWithSortAsJSON;
    const query = { isDeleted: false };
    const order = { order: 1 };
    const programs = (await get(Program, query, { name: 1, _id: 1 }, order)).data;
    const curriculums = (
        await get(
            Curriculum,
            query,
            {
                _id: 1,
                framework: 1,
                'year_level.levels.level_name': 1,
                'year_level.levels._id': 1,
                'year_level._id': 1,
                'year_level.y_type': 1,
                curriculum_name: 1,
                mapping_type: 1,
                _program_id: 1,
                program_name: 1,
            },
            order,
        )
    ).data;
    const selectedCourse = (
        await get(
            Course,
            { isDeleted: false, _id: course_id },
            {
                _id: 1,
                framework: 1,
                course_name: 1,
                content_mapping_type: 1,
                mapping_type: 1,
                _curriculum_id: 1,
                'course_assigned_details._year_id': 1,
                'course_assigned_details._level_id': 1,
                'course_assigned_details.mapping_type': 1,
                'course_assigned_details._curriculum_id': 1,
            },
            order,
        )
    ).data[0];
    const cloIds = [];
    selectedCourse.framework.domains.forEach((domain) => {
        domain.clo.forEach((clo) => {
            cloIds.push(cs(clo._id));
        });
    });
    const results = [];
    try {
        programs.forEach((program) => {
            program.curriculums = [];
            curriculums.forEach((curriculum) => {
                const frameworkHasPlo = () => {
                    let length = 0;
                    const domains = curriculum.framework.domains;
                    if (domains) {
                        curriculum.framework.domains.forEach((domain) => {
                            length += domain.plo.filter((plo) => !plo.isDeleted).length;
                        });
                    }
                    return length;
                };
                if (frameworkHasPlo() > 0) {
                    if (cs(curriculum._program_id) === cs(program._id)) {
                        const hasMapping = () => {
                            let mappedLength = 0;
                            curriculum.framework.domains.forEach((domain) => {
                                domain.plo.forEach((plo) => {
                                    mappedLength += plo.clos.filter((clo) =>
                                        cloIds.includes(cs(clo.clo_id)),
                                    ).length;
                                });
                            });
                            return mappedLength > 0;
                        };
                        if (
                            (program_id === cs(program._id) &&
                                curriculum_id === cs(curriculum._id)) ||
                            hasMapping()
                        ) {
                            curriculum.isSelected = true;
                            curriculum.isDefault = true;
                        } else {
                            curriculum.isSelected = false;
                            curriculum.isDefault = false;
                        }
                        program.curriculums.push(curriculum);
                    }
                }
            });
            if (program.curriculums.length) results.push(program);
        });
        const response = prepare(res, 200, true, req.t('GET_MAPPING_TREE'), results);
        return res.status(200).send(response);
    } catch (error) {
        return res.sendErrorResponse(
            res,
            500,
            req.t('ERROR_ON_GETTING_MAPPING_TREE'),
            error,
            req.t('ERROR_ON_GETTING_MAPPING_TREE'),
        );
    }
};

exports.getMappingMatrix = async (req, res) => {
    const { programs, course_id } = req.body;
    const query = { isDeleted: false };
    const order = { order: 1 };
    const get = dsGetAllWithSortAsJSON;
    const courseResult = await get(
        Course,
        query,
        {
            _id: 1,
            framework: 1,
            course_name: 1,
            _curriculum_id: 1,
            'course_assigned_details._year_id': 1,
            'course_assigned_details._level_id': 1,
        },
        order,
    );
    const selectedCourse = (
        await get(
            Course,
            { ...query, _id: course_id },
            {
                _id: 1,
                framework: 1,
                course_name: 1,
                _curriculum_id: 1,
                'course_assigned_details._year_id': 1,
                'course_assigned_details._level_id': 1,
            },
            order,
        )
    ).data[0];
    const courses = courseResult.data.filter((course) => course.course_assigned_details);
    const sessionOrderResult = await get(
        SESSION_ORDER,
        query,
        {
            _id: 1,
            'session_flow_data.slo': 1,
            _course_id: 1,
            _program_id: 1,
        },
        order,
    );
    const sessionOrders = sessionOrderResult.data;
    const tabs = [];
    let selectedClos = selectedCourse.framework.domains;
    programs.forEach((program) => {
        program.curriculums.forEach((curriculum) => {
            if (curriculum.isSelected) {
                plos = ((curriculum || []).framework || []).domains;
                const matrix = { plos, clos: [], slos: [], curriculum: { _id: curriculum._id } };
                curriculum.year_level.forEach((year) => {
                    year.levels.forEach((level) => {
                        level.courses = [];
                        courses.forEach((course) => {
                            course.course_assigned_details.forEach((cad) => {
                                const yearId = cs(cad._year_id);
                                const levelId = cs(cad._level_id);
                                if (yearId === cs(year._id) && levelId === cs(level._id)) {
                                    const sessionOrder = sessionOrders.find(
                                        (so) => cs(so._course_id) === cs(course._id),
                                    );
                                    if (sessionOrder) {
                                        course.sessionOrder = sessionOrder;
                                        matrix.slos = sessionOrder.session_flow_data;
                                    }
                                    if (course.framework && cs(course._id) === cs(course_id)) {
                                        matrix.clos = course.framework.domains;
                                        selectedClos = course.framework.domains;
                                    }
                                    // matrix.course = course;
                                    level.courses.push(course);
                                }
                            });
                        });
                    });
                });
                if (matrix.plos) {
                    tabs.push({
                        tabName: `${curriculum.program_name}  ${curriculum.curriculum_name}`,
                        matrix,
                    });
                    program.curriculums.push(curriculum);
                }
            }
        });
    });
    tabs.forEach((tab) => {
        tab.matrix.clos = selectedClos;
    });
    sendResult(res, 200, req.t('GET_MAPPING_MATRIX_OLD'), tabs);
};

exports.getMappingMatrixNewGET = async (req, res) => {
    const { course_id } = req.params;
    const query = { isDeleted: false, isActive: true };
    const order = { order: 1 };
    const get = dsGetAllWithSortAsJSON;
    const courseResult = await get(
        Course,
        query,
        {
            _id: 1,
            framework: 1,
            course_name: 1,
            _curriculum_id: 1,
            'course_assigned_details._year_id': 1,
            'course_assigned_details._level_id': 1,
        },
        order,
    );
    const courses = courseResult.data.filter((course) => course.course_assigned_details);
    // const sessionOrderResult = await get(
    //     SESSION_ORDER,
    //     query,
    //     {
    //         _id: 1,
    //         'session_flow_data.slo': 1,
    //         _course_id: 1,
    //         _program_id: 1,
    //     },
    //     order,
    // );
    // const sessionOrders = sessionOrderResult.data;
    const selectedCourse = (
        await get(
            Course,
            { ...query, _id: course_id },
            {
                _id: 1,
                framework: 1,
                course_name: 1,
                _curriculum_id: 1,
                'course_assigned_details._year_id': 1,
                'course_assigned_details._level_id': 1,
            },
            order,
        )
    ).data[0];
    const cloIds = [];
    selectedCourse.framework.domains.forEach((domain) => {
        domain.clo.forEach((clo) => {
            cloIds.push(cs(clo._id));
        });
    });
    //list program and its curriculum
    const aggre = [
        {
            $lookup: {
                from: 'digi_curriculums',
                localField: '_id',
                foreignField: '_program_id',
                as: 'curriculums',
            },
        },
        { $match: { isDeleted: false, isActive: true } },
        {
            $project: {
                curriculums: {
                    $filter: {
                        input: '$curriculums',
                        as: 'curriculum',
                        cond: {
                            $and: [{ $eq: ['$$curriculum.isDeleted', false] }],
                        },
                    },
                },
            },
        },
        {
            $project: {
                'curriculums._id': 1,
                'curriculums.framework': 1,
                'curriculums.content_mapping_type': 1,
                'curriculums.mapping_type': 1,
                'curriculums.year_level._id': 1,
                'curriculums.year_level.levels._id': 1,
                'curriculums.program_name': 1,
                'curriculums.curriculum_name': 1,
            },
        },
    ];
    const doc = await base_control.get_aggregate(Program, aggre);
    if (!doc.status)
        return res
            .status(410)
            .send(common_files.response_function(res, 410, false, req.t('NO_DATA_FOUND'), []));
    const programs = doc.data;
    const tabs = [];
    let selectedClos = [];
    try {
        programs.forEach((program) => {
            program.curriculums.forEach((curriculum) => {
                plos = ((curriculum || []).framework || []).domains;
                const hasMapping = () => {
                    let mappedLength = 0;
                    curriculum.framework.domains.forEach((domain) => {
                        domain.plo.forEach((plo) => {
                            mappedLength += plo.clos.filter((clo) =>
                                cloIds.includes(cs(clo.clo_id)),
                            ).length;
                        });
                    });
                    return mappedLength > 0;
                };
                const matrix = { plos, clos: [], slos: [], curriculum };
                curriculum.year_level.forEach((year) => {
                    year.levels.forEach((level) => {
                        level.courses = [];
                        courses.forEach((course) => {
                            course.course_assigned_details.forEach((cad) => {
                                const yearId = cs(cad._year_id);
                                const levelId = cs(cad._level_id);
                                if (yearId === cs(year._id) && levelId === cs(level._id)) {
                                    // const sessionOrder = sessionOrders.find(
                                    //     (so) => cs(so._course_id) === cs(course._id),
                                    // );
                                    // if (sessionOrder) {
                                    //     course.sessionOrder = sessionOrder;
                                    //     matrix.slos = sessionOrder.session_flow_data;
                                    // }
                                    if (course.framework && cs(course._id) === cs(course_id)) {
                                        matrix.clos = course.framework.domains;
                                        selectedClos = course.framework.domains;
                                    }
                                    // matrix.course = course;
                                    level.courses.push(course);
                                }
                            });
                        });
                    });
                });
                if (matrix.plos && matrix.plos.length && hasMapping()) {
                    tabs.push({
                        tabName: `${curriculum.program_name}  ${curriculum.curriculum_name}`,
                        matrix,
                    });
                    program.curriculums.push(curriculum);
                }
            });
        });
        tabs.forEach((tab) => {
            tab.matrix.clos = selectedClos;
        });
        sendResult(res, 200, req.t('GET_MAPPING_MATRIX'), tabs);
    } catch (error) {
        console.log(error);
        sendErrorResponse(res, 406, serverError, error, serverError);
    }
};

exports.updateMappingMatrix = async (req, res) => {
    try {
        for (tab of req.body.data) {
            for (domain of tab.matrix.clos) {
                for (data of domain.clo) {
                    for (domain_plo of tab.matrix.plos) {
                        for (plo_data of domain_plo.plo) {
                            const ind = plo_data.clos.findIndex(
                                (ele) => ele.clo_id.toString() == data._id.toString(),
                            );
                            if (ind != -1) {
                                //Validation success and update data
                                return res
                                    .status(200)
                                    .send(
                                        common_files.response_function(
                                            res,
                                            200,
                                            true,
                                            req.t('VALIDATION_SUCCESS_AND_UPDATED_MAPPING_MATRIX'),
                                            req.t('VALIDATION_SUCCESS_AND_UPDATED_MAPPING_MATRIX'),
                                        ),
                                    );
                            }
                        }
                    }
                }
            }
        }
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('VALIDATION_ERROR'),
                    req.t('VALIDATION_ERROR'),
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.update_mapping_type = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = { mapping_type: req.body.mapping_type };
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(Curriculum, query, obj);
        if (!doc.status)
            return res
                .status(404)
                .send(common_files.response_function(res, 404, true, req.t('ERROR'), doc.data));

        const course_ids = await base_control.get_list(
            Course,
            { isDeleted: false, _curriculum_id: ObjectId(req.params.id) },
            {},
        );
        if (!course_ids.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const ids = [];
        const cloIds = [];
        for (let i = 0; i < course_ids.data.length; i++) {
            ids.push(ObjectId(course_ids.data[i]._id));
            if (course_ids.data[i].framework && course_ids.data[i].framework.domains) {
                course_ids.data[i].framework.domains.forEach((domain) => {
                    domain.clo.forEach((clo) => {
                        if (!clo.isDeleted) cloIds.push(cs(clo._id));
                    });
                });
            }
        }
        // remove existing mapped value from curriculum on map type change
        const query_0 = { 'framework.domains.plo.clos': { $exists: true } };
        const obj_0 = {
            $pull: { 'framework.domains.$[].plo.$[].clos': { clo_id: { $in: cloIds } } },
        };
        const array_filter_0 = { multi: true, arrayFilters: [{ 'cur.isDeleted': false }] };
        const doc_0 = await base_control.update_many_with_array_filter(
            Curriculum,
            query_0,
            obj_0,
            array_filter_0,
        );

        const query_1 = { _id: { $in: ids }, course_assigned_details: { $exists: true } };
        const obj_1 = {
            $set: { 'course_assigned_details.$[cur].mapping_type': req.body.mapping_type },
        };
        const array_filter = {
            multi: true,
            arrayFilters: [{ 'cur._curriculum_id': req.params.id }],
        };
        const doc_1 = await base_control.update_many_with_array_filter(
            Course,
            query_1,
            obj_1,
            array_filter,
        );
        updateCourseFlatCacheData();
        updateCurriculumFlatCacheData();
        if (doc_1.status && doc_0.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('CURRICULUM_MAPPED_SUCCESSFULLY'),
                        doc_1.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_MAP_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.update_content_mapping_type = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = { content_mapping_type: req.body.content_mapping_type };
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(Curriculum, query, obj);
        updateCurriculumFlatCacheData();
        if (!doc.status)
            return res
                .status(404)
                .send(common_files.response_function(res, 404, true, req.t('ERROR'), doc.data));

        const course_ids = await base_control.get_list(Course, { isDeleted: false }, { _id: 1 });
        if (!course_ids.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );

        const ids = [];
        for (let i = 0; i < course_ids.data.length; i++) ids.push(ObjectId(course_ids.data[i]._id));

        const query_1 = {
            _id: { $in: ids },
            course_assigned_details: { $exists: true },
        };
        const obj_1 = {
            $set: {
                'course_assigned_details.$[cur].content_mapping_type':
                    req.body.content_mapping_type,
            },
        };
        const array_filter = {
            multi: true,
            arrayFilters: [{ 'cur._curriculum_id': req.params.id }],
        };
        const doc_1 = await base_control.update_many_with_array_filter(
            Course,
            query_1,
            obj_1,
            array_filter,
        );
        updateCourseFlatCacheData();
        if (doc_1.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('CURRICULUM_MAPPED_SUCCESSFULLY'),
                        doc_1.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_MAP_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.update_mapping_type_course = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // let curriculum_data = await base_control.get(Curriculum, { _id: ObjectId(req.params.curriculum_id), $or: [{ mapping_type: constants.MAPPING_TYPE_LIST.IMPACT }, { mapping_type: constants.MAPPING_TYPE_LIST.ALIGNMENT }] }, { _id: 1 });
        // if (curriculum_data.status) return res.status(404).send(common_files.response_function(res, 404, false, "Curriculum already mapped, You cant map course", 'Curriculum already mapped, You cant map course'));

        const curriculum_data = await base_control.get(
            Curriculum,
            { _id: ObjectId(req.params.curriculum_id) },
            { _id: 1, mapping_type: 1 },
        );

        if (
            curriculum_data.status &&
            curriculum_data.data.mapping_type &&
            curriculum_data.data.mapping_type !== ''
        )
            return sendErrorResponse(
                res,
                406,
                req.t('CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE'),
                req.t('CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE'),
                req.t('CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE'),
            );

        const query = { _id: ObjectId(req.params.course_id) };
        const obj = {
            $set: {
                'course_assigned_details.$[i].mapping_type': req.body.mapping_type,
            },
        };
        const filter = {
            arrayFilters: [{ 'i._id': req.params.course_assigned_id }],
        };
        doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('COURSE_MAPPED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(res, 200, false, req.t('UNABLE_TO_MAP_COURSE'), []),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.update_content_mapping_type_course = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const curriculum_data = await base_control.get(
            Curriculum,
            {
                _id: ObjectId(req.params.curriculum_id),
                $or: [
                    { content_mapping_type: constants.CONTENT_MAPPING_TYPE.REQUIRED },
                    { content_mapping_type: constants.CONTENT_MAPPING_TYPE.OPTIONAL },
                ],
            },
            { _id: 1 },
        );
        if (curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE'),
                        req.t('CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE'),
                    ),
                );

        const query = { _id: ObjectId(req.params.course_id) };
        const obj = {
            $set: {
                'course_assigned_details.$[i].content_mapping_type': req.body.content_mapping_type,
            },
        };
        const filter = {
            arrayFilters: [{ 'i._id': req.params.course_assigned_id }],
        };
        doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('COURSE_CONTENT_MAPPED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(res, 200, false, req.t('UNABLE_TO_MAP_COURSE'), []),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.get_program_Dashboard = async (req, res) => {
    const query = { isDeleted: false, isActive: true };
    const order = { order: 1 };
    try {
        const getJSON = dsGetAllWithSortAsJSON;
        const programs = await getJSON(
            Program,
            { _id: ObjectId(req.params.id), isDeleted: false, isActive: true },
            { name: 1, _id: 1 },
            order,
        );
        const curriculumQuery = { isDeleted: false, isActive: true };
        // if (req.params.curriculumId) curriculumQuery._id = ObjectId(req.params.curriculumId);
        const curriculums = await getJSON(
            Curriculum,
            curriculumQuery,
            {
                _id: 1,
                framework: 1,
                'year_level.levels.level_name': 1,
                'year_level.levels._id': 1,
                'year_level._id': 1,
                'year_level.y_type': 1,
                curriculum_name: 1,
                mapping_type: 1,
                _program_id: 1,
            },
            order,
        );
        const courseResult = (
            await getJSON(
                Course,
                query,
                {
                    _id: 1,
                    framework: 1,
                    course_name: 1,
                    content_mapping_type: 1,
                    mapping_type: 1,
                    _curriculum_id: 1,
                    'course_assigned_details._year_id': 1,
                    'course_assigned_details._level_id': 1,
                    'course_assigned_details.mapping_type': 1,
                    'course_assigned_details._curriculum_id': 1,
                    'course_assigned_details._id': 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
                order,
            )
        ).data;
        const courses = courseResult.filter((course) => course.course_assigned_details.length);

        const sessionOrderResult = await getJSON(
            SESSION_ORDER,
            query,
            {
                _id: 1,
                'session_flow_data.slo': 1,
                _course_id: 1,
                _program_id: 1,
            },
            order,
        );
        const sessionOrders = sessionOrderResult.data;
        const results = [];
        const totalClos = [];
        curriculums.data.forEach((curriculum) => {
            if (curriculum.framework && curriculum.framework.domains) {
                curriculum.framework.domains.forEach((domain) => {
                    if (domain.plo) {
                        domain.plo.forEach((plo) => {
                            plo.clos.forEach((clo) => {
                                totalClos.push(cs(clo.clo_id));
                            });
                        });
                    }
                });
            }
        });
        programs.data.forEach((program) => {
            const tempObj = { ...program, curriculums: [] };
            curriculums.data.forEach((curriculum) => {
                if (
                    curriculum._program_id.toString() === program._id.toString() &&
                    req.params.curriculumId === curriculum._id.toString()
                ) {
                    //PLO Count
                    let plo_count = 0;
                    const total_plo_clo = 0;
                    if (curriculum.framework && curriculum.framework.domains) {
                        curriculum.framework.domains.forEach((cur) => {
                            if (cur.plo) {
                                plo_count += cur.plo.filter((plo) => !plo.isDeleted).length;
                            }
                        });
                    }

                    const tempCurriculum = {
                        ...curriculum,
                        year_level: [],
                        total_course: 0,
                        total_clo_done: 0,
                        total_plo_clo,
                        plo_count,
                    };
                    curriculum.year_level.forEach((year) => {
                        const tempYearObj = { ...year, levels: [] };
                        year.levels.forEach((level) => {
                            const tempLevelObj = { ...level, courses: [] };
                            courses.forEach((course) => {
                                //CLO count
                                let clo_count = 0;
                                if (course.framework && course.framework.domains) {
                                    course.framework.domains.forEach((domain) => {
                                        if (domain.clo)
                                            clo_count += domain.clo.filter(
                                                (clo) => !clo.isDeleted,
                                            ).length;
                                    });
                                }

                                course.course_assigned_details.forEach((course_assigned_detail) => {
                                    const cad = course_assigned_detail;
                                    const yearId = cs(cad._year_id);
                                    const levelId = cs(cad._level_id);
                                    if (yearId === cs(year._id) && levelId === cs(level._id)) {
                                        tempCurriculum.total_course += 1;
                                        let plo_clo_count = 0;
                                        let clo_slo_count = 0;
                                        const getCloCount = (domains) => {
                                            let cloCount = 0;
                                            domains.forEach((domain) => {
                                                cloCount += domain.clo.filter(
                                                    (c) => !c.isDeleted,
                                                ).length;
                                                domain.clo.forEach((c) => {
                                                    if (!c.isDeleted) {
                                                        plo_clo_count += totalClos.filter(
                                                            (cloId) => cloId === cs(c._id),
                                                        ).length;
                                                        clo_slo_count += c.slos.length;
                                                    }
                                                });
                                            });
                                            return cloCount;
                                        };
                                        tempCurriculum.total_clo_done += getCloCount(
                                            course.framework.domains,
                                        )
                                            ? 1
                                            : 0;
                                        if (plo_clo_count) tempCurriculum.total_plo_clo++;
                                        const course_assigned_details = course_assigned_detail;
                                        course_assigned_details._course_id = course._id;
                                        const f = {
                                            _id: course.framework._id,
                                            name: course.framework.name,
                                            code: course.framework.code,
                                        };
                                        course_assigned_details.framework = f;
                                        course_assigned_details.course_name = course.course_name;
                                        const sessionOrder_temp = sessionOrders.find(
                                            (sessionOrder) =>
                                                sessionOrder._course_id.toString() ===
                                                course._id.toString(),
                                        );

                                        //Session order slo count
                                        let slo_count = 0;
                                        if (sessionOrder_temp) {
                                            if (sessionOrder_temp.session_flow_data) {
                                                sessionOrder_temp.session_flow_data.forEach(
                                                    (ele) => {
                                                        if (ele.slo) slo_count += ele.slo.length;
                                                    },
                                                );
                                            }
                                        }

                                        const sessionOrder = sessionOrder_temp
                                            ? {
                                                  _id: sessionOrder_temp._id,
                                                  slo_count,
                                                  clo_slo_count,
                                              }
                                            : undefined;
                                        tempLevelObj.courses.push({
                                            ...course_assigned_details,
                                            sessionOrder,
                                            clo_count,
                                            plo_clo_count,
                                            versionNo: course.versionNo || '',
                                            versioned: course.versioned || false,
                                            versionName: course.versionName || 'default',
                                            versionedFrom: course.versionedFrom || '',
                                            versionedCourseIds: course.versionedCourseIds || [],
                                        });
                                    }
                                });
                            });
                            if (tempLevelObj.courses.length) tempYearObj.levels.push(tempLevelObj);
                        });
                        if (tempYearObj.levels.length) tempCurriculum.year_level.push(tempYearObj);
                    });
                    delete tempCurriculum.framework.domains;
                    if (tempCurriculum.year_level.length) tempObj.curriculums.push(tempCurriculum);
                }
            });
            results.push(tempObj);
        });
        sendResultWithRequest(req, res, 200, programs.message, results[0]);
    } catch (e) {
        console.log(e);
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.update_matrix_clo_plo = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body.curriculum_id) };
        let obj;
        let filter;
        const curriculum = await base_control.get(
            Curriculum,
            { _id: req.body.curriculum_id },
            { framework: 1 },
        );
        let clo_id;
        curriculum.data.framework.domains.forEach((domain) => {
            if (cs(domain._id) === cs(req.body.domain_id)) {
                domain.plo.forEach((p) => {
                    if (cs(p._id) === cs(req.body.plo_id)) {
                        p.clos.forEach((clo) => {
                            if (cs(clo.clo_id) === cs(req.body.clo.clo_id)) clo_id = cs(clo._id);
                        });
                    }
                });
            }
        });
        if (clo_id) {
            if (req.body.clo.mapped_value == 'false' || req.body.clo.mapped_value == 'NONE') {
                obj = {
                    $pull: {
                        'framework.domains.$[d].plo.$[p].clos': { clo_id: req.body.clo.clo_id },
                    },
                };
            } else {
                obj = {
                    $set: {
                        'framework.domains.$[d].plo.$[p].clos.$[c].clo_id': req.body.clo.clo_id,
                        'framework.domains.$[d].plo.$[p].clos.$[c].no': req.body.clo.no,
                        'framework.domains.$[d].plo.$[p].clos.$[c].name': req.body.clo.name,
                        'framework.domains.$[d].plo.$[p].clos.$[c].year_id': req.body.clo.year_id,
                        'framework.domains.$[d].plo.$[p].clos.$[c].year_name':
                            req.body.clo.year_name,
                        'framework.domains.$[d].plo.$[p].clos.$[c].level_id': req.body.clo.level_id,
                        'framework.domains.$[d].plo.$[p].clos.$[c].level_name':
                            req.body.clo.level_name,
                        'framework.domains.$[d].plo.$[p].clos.$[c].mapped_value':
                            req.body.clo.mapped_value,
                        'framework.domains.$[d].plo.$[p].clos.$[c].content_mapped_value':
                            req.body.clo.content_mapped_value,
                    },
                };
            }

            filter = {
                arrayFilters: [
                    { 'd._id': req.body.domain_id },
                    { 'p._id': req.body.plo_id },
                    { 'c._id': clo_id },
                ],
            };
        } else {
            obj = {
                $push: {
                    'framework.domains.$[d].plo.$[p].clos': req.body.clo,
                },
            };
            filter = {
                arrayFilters: [{ 'd._id': req.body.domain_id }, { 'p._id': req.body.plo_id }],
            };
        }
        doc = await base_control.update_condition_array_filter(Curriculum, query, obj, filter);
        updateCurriculumFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('PLO_CLO_MAPPED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(res, 200, false, req.t('UNABLE_TO_MAP_PLO_CLO'), []),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.update_matrix_slo_clo = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body.course_id) };
        const selectedCourse = (await dsGetAllWithSort(Course, query, {}, { order: 1 })).data[0];
        let slo_id;
        let obj;
        selectedCourse.framework.domains.forEach((domain) => {
            if (cs(domain._id) !== cs(req.body.domain_id)) return;
            domain.clo.forEach((clo) => {
                clo.slos.forEach((slo) => {
                    if (
                        cs(slo.slo_id) === cs(req.body.slo.slo_id) &&
                        cs(clo._id) === cs(req.body.clo_id)
                    ) {
                        slo_id = slo._id;
                    }
                });
            });
        });
        if (slo_id) {
            obj = {
                $pull: {
                    'framework.domains.$[d].clo.$[c].slos': { _id: ObjectId(slo_id) },
                },
            };
        } else {
            obj = { $push: { 'framework.domains.$[d].clo.$[c].slos': req.body.slo } };
        }
        const filter = {
            arrayFilters: [
                { 'd._id': ObjectId(req.body.domain_id) },
                { 'c._id': ObjectId(req.body.clo_id) },
            ],
        };
        doc = await base_control.update_condition_array_filter(Course, query, obj, filter);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('CLO_SLO_MAPPED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(res, 200, false, req.t('UNABLE_TO_MAP_CLO_SLO'), []),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};

exports.addSlo = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const { course_id, session_order_id, session_flow_data_id, slo } = req.body;
        const where = {
            _course_id: ObjectId(course_id),
            _id: ObjectId(session_order_id),
            isDeleted: false,
        };
        const select = {};
        const short = { order: 1 };
        const sessionOrders = (await dsGetAllWithSort(SESSION_ORDER, where, select, short)).data;
        if (sessionOrders[0]) {
            const object = {
                $push: {
                    'session_flow_data.$[sfd].slo': slo,
                },
            };
            const filter = {
                arrayFilters: [{ 'sfd._id': session_flow_data_id }],
            };
            doc = await base_control.update_condition_array_filter(
                SESSION_ORDER,
                where,
                object,
                filter,
            );
            updateSessionFlowFlatCacheData();
            if (doc.status)
                return res
                    .status(201)
                    .send(
                        common_files.response_function(
                            res,
                            201,
                            true,
                            req.t('SLO_ADDED_SUCCESSFULLY'),
                            doc.data,
                        ),
                    );
            return res
                .status(410)
                .send(
                    common_files.response_function(res, 200, false, req.t('UNABLE_TO_ADD_SLO'), []),
                );
        }
    } catch (error) {
        sendErrorResponse(res, 500, 'Error', error);
    }
};

exports.updateSlo = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const { course_id, session_order_id, session_flow_data_id, slo_id, slo } = req.body;
        const where = {
            _course_id: ObjectId(course_id),
            _id: ObjectId(session_order_id),
            isDeleted: false,
        };
        const select = {};
        const short = { order: 1 };
        const sessionOrders = (await dsGetAllWithSort(SESSION_ORDER, where, select, short)).data;
        if (sessionOrders[0]) {
            if (slo_id) {
                object = {
                    $set: {
                        'session_flow_data.$[sfd].slo.$[s].no': slo.no,
                        'session_flow_data.$[sfd].slo.$[s].name': slo.name,
                    },
                };
                filter = {
                    arrayFilters: [{ 'sfd._id': session_flow_data_id }, { 's._id': slo_id }],
                };
            } else {
                object = {
                    $push: {
                        'session_flow_data.$[sfd].slo': slo,
                    },
                };
                filter = {
                    arrayFilters: [{ 'sfd._id': session_flow_data_id }],
                };
            }
            doc = await base_control.update_condition_array_filter(
                SESSION_ORDER,
                where,
                object,
                filter,
            );
            updateSessionFlowFlatCacheData();
            if (doc.status)
                return res
                    .status(201)
                    .send(
                        common_files.response_function(
                            res,
                            201,
                            true,
                            req.t('SLO_UPDATED_SUCCESSFULLY'),
                            doc.data,
                        ),
                    );
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('UNABLE_TO_UPDATE_SLO'),
                        [],
                    ),
                );
        }
    } catch (error) {
        sendErrorResponse(res, 500, req.t('ERROR'), error);
    }
};

exports.deleteSlo = async (req, res) => {
    const { course_id, session_order_id, session_flow_data_id, slo_id } = req.body;
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const courses = (
            await dsGetAllWithSort(Course, { _id: ObjectId(course_id) }, {}, { order: 1 })
        ).data;
        if (courses[0]) {
            const domains = courses[0].framework.domains.filter((d) => d.clo.length);
            const isSloExist = (domains, slo_id) => {
                isExist = false;
                domains.forEach((domain) => {
                    if (domain.clo) {
                        domain.clo.forEach((c) => {
                            if (c.slos) {
                                c.slos.forEach((slo) => {
                                    if (cs(slo.slo_id) === cs(slo_id)) isExist = true;
                                });
                            }
                        });
                    }
                });
                return isExist;
            };
            if (isSloExist(domains, slo_id)) {
                return sendErrorResponse(
                    res,
                    501,
                    '',
                    req.t('VALIDATION_ERROR'),
                    req.t('SLO_IS_MAPPED_WITH_CLO_CAN_NOT_BE_DELETED'),
                );
            }
            const where = {
                _course_id: ObjectId(course_id),
                _id: ObjectId(session_order_id),
                isDeleted: false,
            };
            const select = {};
            const short = { order: 1 };
            const sessionOrders = (await dsGetAllWithSort(SESSION_ORDER, where, select, short))
                .data;
            if (sessionOrders[0]) {
                const object = {
                    $pull: {
                        'session_flow_data.$[sfd].slo': { _id: slo_id },
                    },
                };
                const filter = {
                    arrayFilters: [{ 'sfd._id': session_flow_data_id }],
                };
                doc = await base_control.update_condition_array_filter(
                    SESSION_ORDER,
                    where,
                    object,
                    filter,
                );
                updateSessionFlowFlatCacheData();
                if (doc.status)
                    return res
                        .status(201)
                        .send(
                            common_files.response_function(
                                res,
                                201,
                                true,
                                req.t('SLO_DELETED_SUCCESSFULLY'),
                                doc.data,
                            ),
                        );
                return res
                    .status(410)
                    .send(
                        common_files.response_function(
                            res,
                            200,
                            false,
                            req.t('UNABLE_TO_DELETE_SLO'),
                            [],
                        ),
                    );
            }
        } else {
            return sendErrorResponse(
                res,
                501,
                req.t('COURSE_NOT_FOUND'),
                req.t('VALIDATION_ERROR'),
            );
        }
    } catch (error) {
        sendErrorResponse(res, 500, req.t('ERROR'), error);
    }
};

exports.getSLOGraphData = async (req, res) => {
    const { course_id } = req.params;
    const get = dsGetAllWithSortAsJSON;
    try {
        const courses = (await get(Course, { _id: ObjectId(course_id), isDeleted: false })).data;
        const course = courses[0] || undefined;
        const allSloIds = [];
        if (course) {
            course.framework.domains.forEach((domain) => {
                domain.mappedObject = {};
                domain.clo.forEach((clo) => {
                    clo.mappedCount = 0;
                    clo.slos.forEach((slo) => {
                        const key = `${slo.delivery_symbol}${slo.delivery_no}`;
                        if (!domain.mappedObject[key]) domain.mappedObject[key] = [];
                        domain.mappedObject[key].push(cs(slo.slo_id));
                        allSloIds.push(cs(slo.slo_id));
                    });
                });
            });
        }
        const sessionQuery = { isDeleted: false, _course_id: ObjectId(course_id) };
        const sessionOrders = (await get(SESSION_ORDER, sessionQuery)).data;
        const sessionOrder = sessionOrders[0] || undefined;
        if (sessionOrder && course) {
            const allSessionIds = sessionOrder.session_flow_data.map((sfd) => sfd._session_id);
            const sessionDeliveryQuery = { isDeleted: false, _id: { $in: allSessionIds } };
            const sessionDeliveryTypes = (await get(SESSION_DELIVERY_TYPE, sessionDeliveryQuery))
                .data;
            sessionDeliveryTypes.forEach((sDT) => {
                sDT.session_flow_data = sessionOrder.session_flow_data.filter(
                    (sfd) => cs(sfd._session_id) === cs(sDT._id),
                );
            });
            sessionDeliveryTypes.forEach((sDT) => {
                sDT.domains = clone(course.framework.domains);
                sDT.session_flow_data.forEach((sFD) => {
                    sFD.domains = clone(course.framework.domains);
                    sFD.slo.forEach((slo) => {
                        slo.domains = clone(course.framework.domains);
                        slo.domains.forEach((domain, d_index) => {
                            domain.clo.forEach((clo, c_index) => {
                                clo.slos.forEach((c_slo) => {
                                    clo.mappedCount = 0;
                                    if (cs(c_slo.slo_id) === cs(slo._id)) {
                                        clo.mappedCount++;
                                        sFD.domains[d_index].clo[c_index].mappedCount++;
                                        sDT.domains[d_index].clo[c_index].mappedCount++;
                                    }
                                });
                            });
                        });
                    });
                });
            });
            return sendResult(res, 200, req.t('SLO_CLO_GRAPH_DATA'), sessionDeliveryTypes);
        }
        return sendErrorResponse(res, 406, serverError, req.t('ERROR'), req.t('ERROR'));
    } catch (error) {
        return sendErrorResponse(res, 406, serverError, error, req.t('ERROR'));
    }
};

exports.getCourseSLOGraphData = async (req, res) => {
    const {
        headers: { _institution_id },
        params: { course_id },
    } = req;
    try {
        const get = dsGetAllWithSortAsJSON;
        const courseProject = {
            domains: 1,
            'framework.domains._id': 1,
            'framework.domains.no': 1,
            'framework.domains.name': 1,
            'framework.domains.clo.no': 1,
            'framework.domains.clo._id': 1,
            'framework.domains.clo.mappedCount': 1,
            'framework.domains.clo.isDeleted': 1,
            'framework.domains.clo.slos.delivery_symbol': 1,
            'framework.domains.clo.slos.delivery_no': 1,
            'framework.domains.clo.slos.slo_id': 1,
            'framework.domains.clo.slos.no': 1,
            'framework.domains.clo.slos.mapped_value': 1,
        };
        const courses = (
            await get(Course, { _id: ObjectId(course_id), isDeleted: false }, courseProject)
        ).data;
        const course = courses[0] || undefined;
        // const allSloIds = [];
        if (course) {
            course.framework.domains.forEach((domain) => {
                domain.mappedObject = {};
                domain.clo.forEach((clo) => {
                    clo.mappedCount = 0;
                    clo.slos.forEach((slo) => {
                        const key = `${slo.delivery_symbol}${slo.delivery_no}`;
                        if (!domain.mappedObject[key]) domain.mappedObject[key] = [];
                        domain.mappedObject[key].push(cs(slo.slo_id));
                        // allSloIds.push(cs(slo.slo_id));
                    });
                });
            });
        }
        const sessionQuery = { isDeleted: false, _course_id: ObjectId(course_id) };
        const sessionProject = {
            'session_flow_data._id': 1,
            'session_flow_data._session_id': 1,
            'session_flow_data.slo': 1,
            'session_flow_data.domains': 1,
            'session_flow_data.delivery_type': 1,
            'session_flow_data._delivery_id': 1,
            'session_flow_data.delivery_symbol': 1,
            'session_flow_data.delivery_no': 1,
            'session_flow_data.delivery_topic': 1,
        };
        const sessionOrders = (await get(SESSION_ORDER, sessionQuery, sessionProject)).data;
        // return res.send(sessionOrders);
        const sessionOrder = sessionOrders[0] || undefined;
        if (sessionOrder && course) {
            const allSessionIds = sessionOrder.session_flow_data.map((sfd) => sfd._session_id);
            const sessionDeliveryQuery = { isDeleted: false, _id: { $in: allSessionIds } };
            const sessionDeliveryProject = {
                session_name: 1,
                session_symbol: 1,
                'delivery_types.delivery_name': 1,
                'delivery_types.delivery_symbol': 1,
                'delivery_types.delivery_no': 1,
            };
            const sessionDeliveryTypes = (
                await get(SESSION_DELIVERY_TYPE, sessionDeliveryQuery, sessionDeliveryProject)
            ).data;
            sessionDeliveryTypes.forEach((sDT) => {
                sDT.session_flow_data = sessionOrder.session_flow_data.filter(
                    (sfd) => cs(sfd._session_id) === cs(sDT._id),
                );
            });
            sessionDeliveryTypes.forEach((sDT) => {
                sDT.session_flow_data.forEach((sFD) => {
                    sFD.domains = clone(course.framework.domains);
                    sFD.slo.forEach((slo) => {
                        sFD.domains.forEach((domain, d_index) => {
                            domain.clo.forEach((clo, c_index) => {
                                clo.slos.forEach((c_slo) => {
                                    if (cs(c_slo.slo_id) === cs(slo._id)) {
                                        sFD.domains[d_index].clo[c_index].mappedCount++;
                                    }
                                });
                            });
                        });
                    });
                });
            });
            return sendResult(res, 200, req.t('SLO_CLO_GRAPH_DATA'), sessionDeliveryTypes);
        }

        return res
            .status(200)
            .send(common_files.response_function(res, 200, false, req.t('NOT_FOUND'), []));
    } catch (error) {
        console.log(error);
        return sendErrorResponse(res, 406, serverError, error, error);
    }
};
