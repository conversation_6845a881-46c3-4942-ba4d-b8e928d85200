const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.committee = (req, res, next) => {
    const schema = Joi.object().keys({
       body: Joi.object().keys({
          committee_name: Joi.string().min(5).max(25).required().error(error =>{
              return error;
          }),
          committee_description: Joi.string().required().error(error =>{
              return error;
          }),
          committee_admin: Joi.array().items(Joi.string().alphanum().length(24).required()).error(error =>{
              return error;
          })
       }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.members = (req,res,next) =>{
    const schema = Joi.object().keys({
       body: Joi.object().keys({
        _member_id: Joi.array().items(Joi.string().alphanum().length(24).required()).error(error =>{
            return error;
        })
       }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    })
}


exports.committee_id = (req,res,next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}


exports.member_id = (req,res,next) =>{
    const schema = Joi.object().keys({
       params: Joi.object().keys({
        id: Joi.string().alphanum().length(24).required().error(error => {
            return error;
        }),
        memberid: Joi.string().alphanum().length(24).required().error(error => {
            return error;
        })
       }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}


exports.admmin_id = (req,res,next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}

