const ReportService = require('./report.service');

const generatePortfolioReport = async ({
    body: { portfolioId, studentIds = [] } = {},
    headers: { user_id: userId } = {},
}) => {
    await ReportService.generatePortfolioReport({ portfolioId, studentIds, userId });

    return { statusCode: 200, message: 'REPORT_WILL_GENERATE_IN_SHORT_TIME' };
};

const getPortfolioReport = async ({ query: { portfolioId } }) => {
    const report = await ReportService.getPortfolioReport({ portfolioId });

    return { statusCode: 200, message: 'REPORT_GENERATED_SUCCESSFULLY', data: report };
};

const addExtraMark = async ({ body: { portfolioId, studentId, marks } }) => {
    const report = await ReportService.addExtraMark({ portfolioId, studentId, marks });

    return { statusCode: 200, message: 'EXTRA_MARKS_ADDED_SUCCESSFULLY', data: report };
};

const updateMarkSettings = async ({ body: { portfolioId, marks, type } }) => {
    await ReportService.updateMarkSettings({ portfolioId, marks, type });

    return { statusCode: 200, message: 'MARK_SETTINGS_UPDATED_SUCCESSFULLY' };
};

const updateReportSettings = async ({
    body: { portfolioId, overAllPassEnabled, isComponentWisePassEnabled },
}) => {
    await ReportService.updateReportSettings({
        portfolioId,
        overAllPassEnabled,
        isComponentWisePassEnabled,
    });

    return { statusCode: 200, message: 'REPORT_SETTINGS_UPDATED_SUCCESSFULLY' };
};

const updateComponentWisePassSettings = async ({
    body: { portfolioId, componentId, childId, marks, mustPass, completionRules },
}) => {
    await ReportService.updateComponentWisePassSettings({
        portfolioId,
        componentId,
        childId,
        marks,
        mustPass,
        completionRules,
    });

    return { statusCode: 200, message: 'COMPONENT_WISE_PASS_SETTINGS_UPDATED_SUCCESSFULLY' };
};

const getIndividualStudentReport = async ({ query: { portfolioId, studentId } }) => {
    const report = await ReportService.getIndividualStudentReport({ portfolioId, studentId });

    return { statusCode: 200, message: 'REPORT_GENERATED_SUCCESSFULLY', data: report };
};

const getPortfolioComponentReport = async ({ query: { portfolioId, componentId, childrenId } }) => {
    const report = await ReportService.getPortfolioComponentReport({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, message: 'REPORT_GENERATED_SUCCESSFULLY', data: report };
};

module.exports = {
    generatePortfolioReport,
    getPortfolioReport,
    addExtraMark,
    updateMarkSettings,
    updateReportSettings,
    updateComponentWisePassSettings,
    getIndividualStudentReport,
    getPortfolioComponentReport,
};
