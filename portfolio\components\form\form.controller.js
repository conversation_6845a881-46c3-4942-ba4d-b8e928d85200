const FormService = require('./form.service');

const { FORM_TYPES } = require('../../common/utils/constants');
const { checkIfTrue } = require('../../common/utils/common.util');

const createForm = async ({
    body: { title, type = {}, description, pages = [], rubrics = [] } = {},
    headers: { user_id: userId } = {},
}) => {
    const form = await FormService.createForm({ title, type, description, pages, rubrics, userId });

    return { message: 'FORM_CREATED_SUCCESSFULLY', data: form };
};

const getForms = async ({
    query: { formId, isPages, type, status },
    headers: { user_id: userId } = {},
}) => {
    const forms = await FormService.getForms({
        formId,
        isPages: checkIfTrue(isPages),
        type,
        userId,
        status,
    });

    return { message: 'FORM_RETRIEVED_SUCCESSFULLY', data: forms };
};

const updateForm = async ({
    query: { formId },
    body: { title, type = {}, description, pages = [], evaluations = [] } = {},
}) => {
    await FormService.updateForm({ formId, title, type, description, pages, evaluations });

    return { message: 'FORM_UPDATED_SUCCESSFULLY' };
};

const deleteForm = async ({ query: { formId } }) => {
    await FormService.deleteForm({ formId });

    return { message: 'FORM_DELETE_SUCCESSFULLY' };
};

const getFormsByUser = async ({
    query: { email = '', type = '', formId = '', isPages = true },
}) => {
    const results = await FormService.getFormsByUser({
        email,
        type,
        formId,
        isPages: isPages !== 'false',
    });

    return { message: 'FORM_RETRIEVED_SUCCESSFULLY', data: results };
};

const publishForm = async ({ query: { formId } }) => {
    await FormService.publishForm({ formId });

    return { message: 'FORM_RETRIEVED_SUCCESSFULLY' };
};

const getFormType = async () => {
    return { message: 'FORM_TYPE_RETRIEVED_SUCCESSFULLY', data: FORM_TYPES };
};

const getAssignedRolesInForm = async ({ query: { formId } }) => {
    const roles = await FormService.getAssignedRolesInForm({ formId });

    return { message: 'FORM_ROLES_RETRIEVED_SUCCESSFULLY', data: roles };
};

module.exports = {
    createForm,
    getForms,
    updateForm,
    deleteForm,
    getFormsByUser,
    publishForm,
    getFormType,
    getAssignedRolesInForm,
};
