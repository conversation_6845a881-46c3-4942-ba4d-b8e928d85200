const calculateStudentTotalMarks = ({ evaluators, totalMarks }) => {
    const totalEvaluatorMarks = evaluators.reduce((sum, ev) => sum + ev.marks, 0);

    const totalAchievedEvaluatorMarks = evaluators.reduce((sum, ev) => sum + ev.awardedMarks, 0);

    const scaledStudentMarks = (totalAchievedEvaluatorMarks / totalEvaluatorMarks) * totalMarks;

    return Number(scaledStudentMarks.toFixed(2));
};

module.exports = {
    calculateStudentTotalMarks,
};
