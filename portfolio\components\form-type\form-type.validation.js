const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const createFormTypeSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const updateFormTypeSchema = Joi.object({
    query: Joi.object({
        formTypeId: objectIdRQSchema,
    }),
    body: Joi.object({
        name: Joi.string().optional(),
        code: Joi.string().optional(),
    }),
}).unknown(true);

const deleteFormTypeSchema = Joi.object({
    query: Joi.object({
        formTypeId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    createFormTypeSchema,
    updateFormTypeSchema,
    deleteFormTypeSchema,
};
