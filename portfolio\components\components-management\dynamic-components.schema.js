const mongoose = require('mongoose');

const { Schema } = mongoose;

const nameCode = {
    name: {
        type: String,
        trim: true,
    },
    code: {
        type: String,
    },
};

const curriculumSchema = new Schema(
    {
        ...nameCode,
        componentLevel: { type: String },
        isCourseInRow: { type: Boolean, default: true },
    },
    { _id: false, timestamps: false },
);

const hierarchySchema = {
    program: nameCode,
    term: nameCode,
    year: nameCode,
    curriculums: [curriculumSchema],
    level: nameCode,
    rotationGroup: nameCode,
    course: nameCode,
};

const children = {
    no: String,
    label: String,
    desc: String,
};

const childrenSchema = {
    ...children,
    threshold: Number,
    children: [
        {
            ...children,
            children: [
                {
                    ...children,
                    children: [children],
                },
            ],
        },
    ],
};

module.exports = {
    hierarchySchema,
    children,
    childrenSchema,
};
