const constant = require('../utility/constants');
const base_control = require('../base/base_controller');
const getJSON = base_control.dsGetAllWithSortAsJSON;
const PCalender = require('mongoose').model(constant.PROGRAM_CALENDAR);
const User = require('mongoose').model(constant.USER);
const DeptSub = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const CourseSchedule = require('mongoose').model(constant.COURSE_SCHEDULE);

exports.getTotalYears = async (_program_id) => {
    const pc_query = { isActive: true, isDeleted: false, _program_id };
    const pc_project = {
        'level.curriculum': 1,
        'level.year': 1,
        'level.level_no': 1,
        'level.course': 1,
        'level.rotation': 1,
        'level.rotation_course': 1,
    };
    const PCalenders = (await getJSON(PCalender, pc_query, pc_project)).data;
    let curriculums = [];
    let years = [];
    let levels = [];
    let totalCourse = 0;
    let courses = [];
    PCalenders.forEach((pcCalender) => {
        pcCalender.level.forEach((level) => {
            curriculums.push(level.curriculum);
            years.push(level.year);
            levels.push(level.level_no);
            // if (level.rotation === 'no') totalCourse += level.course.length;
            // else totalCourse += level.rotation_course[0].course.length;
            if (level.rotation === 'no') courses = [...courses, ...level.course];
            else courses = [...courses, ...level.rotation_course[0].course];
        });
    });
    curriculums = [...new Set(curriculums)];
    years = [...new Set(years)];
    levels = [...new Set(levels)];
    courses = courses.filter(
        (tag, index) =>
            courses.findIndex((t) => t._course_id.toString() === tag._course_id.toString()) ===
            index,
    );
    totalCourse = courses.length;
    const yearNumbers = years.map((year) => parseInt(year.split('year')[1]));
    const levelNumbers = levels.map((level) => parseInt(level.split('Level ')[1]));
    const totalYears = Math.max(...yearNumbers);
    const totalLevels = Math.max(...levelNumbers);
    return { curriculums, years, levels, totalCourse, totalYears, totalLevels };
};
exports.getTotalStaffDeptSubDetails = async (program, users, course_schedule_data, deptData) => {
    const staffSubjectDetails = [];
    let subjectCount = 0;
    for (const dept of deptData) {
        subjectCount += parseInt(dept.subject.length);
    }
    const departmentCount = deptData.length;
    let staffCount = 0;
    for (const user of users) {
        if (!user.academic_allocation) continue;
        //Staff Count
        const programAcadInd = user.academic_allocation.findIndex(
            (ele) => ele._program_id.toString() == program._id.toString(),
        );
        if (programAcadInd != -1) staffCount++;

        //Staff Details
        for (const dept of deptData) {
            for (const subject of dept.subject) {
                const acad_ind = user.academic_allocation.findIndex(
                    (ele) => ele._department_id.toString() == dept._id.toString(),
                );
                if (acad_ind != -1) {
                    const deptSubInd = user.academic_allocation[
                        acad_ind
                    ]._department_subject_id.findIndex(
                        (ele) => ele.toString() == subject._id.toString(),
                    );
                    if (deptSubInd != -1) {
                        //Check subject already present array. if not present create one
                        const staffSubInd = staffSubjectDetails.findIndex(
                            (ele) => ele.subject_id.toString() == subject._id.toString(),
                        );

                        //Check in Course schedule staff and subject present
                        let flag = 0;
                        //console.log(course_schedule_data);
                        for (courseSchedule of course_schedule_data) {
                            if (!courseSchedule.staffs && !courseSchedule.subjects) continue;
                            const csStaffInd = courseSchedule.staffs.findIndex(
                                (ele) => ele._staff_id.toString() == user._id.toString(),
                            );
                            if (csStaffInd != -1) {
                                const csSubjInd = courseSchedule.subjects.findIndex(
                                    (ele) => ele._subject_id.toString() == subject._id.toString(),
                                );
                                if (csSubjInd != -1) {
                                    flag = 1; // staff and subject present
                                    break;
                                }
                            }
                        }

                        if (flag == 1) {
                            if (staffSubInd == -1) {
                                staffSubjectDetails.push({
                                    subject_id: subject._id,
                                    subject_name: subject.subject_name,
                                    department_id: dept._id,
                                    department_name: dept.department_name,
                                    staff_details: [
                                        {
                                            staff_id: user._id,
                                            staff_name: user.name,
                                        },
                                    ],
                                });
                            } else {
                                staffSubjectDetails[staffSubInd].staff_details.push({
                                    staff_id: user._id,
                                    staff_name: user.name,
                                });
                            }
                        }
                    }
                }
            }
        }
    }

    return { staffCount, departmentCount, subjectCount, staffSubjectDetails };
};
