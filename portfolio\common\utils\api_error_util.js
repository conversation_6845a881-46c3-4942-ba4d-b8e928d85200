const HttpStatusCode = {
    OK: 200,
    UPDATE_FAILED: 400,
    BAD_REQUEST: 400,
    NOT_FOUND: 404,
    INTERNAL_SERVER: 500,
};

class HTTPError extends Error {
    constructor(message, data, statusCode = 400, isOperational = true) {
        super();
        this.message = message;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.data = data;
    }
}

class BadRequestError extends Error {
    constructor(message, data, isOperational = true) {
        super();
        this.message = message;
        this.statusCode = HttpStatusCode.BAD_REQUEST;
        this.isOperational = isOperational;
        this.data = data;
    }
}

class NotFoundError extends Error {
    constructor(message, data, isOperational = true) {
        super();
        this.message = message;
        this.statusCode = HttpStatusCode.NOT_FOUND;
        this.isOperational = isOperational;
        this.data = data;
    }
}

class InternalServerError extends Error {
    constructor(message, data, isOperational = true) {
        super();
        this.message = message;
        this.statusCode = HttpStatusCode.INTERNAL_SERVER;
        this.isOperational = isOperational;
        this.data = data;
    }
}

class UpdateFailedError extends Error {
    constructor(message, data, isOperational = true) {
        super();
        this.message = message;
        this.statusCode = HttpStatusCode.UPDATE_FAILED;
        this.isOperational = isOperational;
        this.data = data;
    }
}

module.exports = {
    HTTPError,
    BadRequestError,
    NotFoundError,
    InternalServerError,
    UpdateFailedError,
};
