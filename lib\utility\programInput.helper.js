const programSchema = require('../models/digi_programs');
const curriculumSchema = require('../models/digi_curriculum');
const courseSchema = require('../models/digi_course');
const { PUBLISHED } = require('./constants');
const { convertToMongoObjectId } = require('./common');
exports.allProgramList = async ({ institutionId, searchKey }) => {
    const programList = await programSchema
        .find(
            {
                _institution_id: convertToMongoObjectId(institutionId),
                isActive: true,
                isDeleted: false,
                ...(searchKey && {
                    $or: [
                        { name: { $regex: searchKey, $options: 'i' } },
                        { code: { $regex: searchKey, $options: 'i' } },
                    ],
                }),
            },
            { _id: 1, name: 1, code: 1 },
        )
        .sort({ _id: -1 })
        .lean();
    return programList;
};

exports.singleProgramDetail = async ({ institutionId, programId }) => {
    const curriculamData = await curriculumSchema
        .find(
            {
                _institution_id: convertToMongoObjectId(institutionId),
                _program_id: convertToMongoObjectId(programId),
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                program_name: 1,
                curriculum_name: 1,
                'year_level.y_type': 1,
                'year_level.levels': 1,
            },
        )
        .lean();
    const courseList = await courseSchema
        .find(
            {
                isActive: true,
                isDeleted: false,
                $or: [
                    {
                        _program_id: convertToMongoObjectId(programId),
                        _curriculum_id: {
                            $in: curriculamData.map((courseElement) =>
                                convertToMongoObjectId(courseElement._id),
                            ),
                        },
                    },
                    {
                        'course_assigned_details.course_shared_with._program_id':
                            convertToMongoObjectId(programId),
                        'course_assigned_details.course_shared_with._curriculum_id': {
                            $in: curriculamData.map((courseElement) =>
                                convertToMongoObjectId(courseElement._id),
                            ),
                        },
                    },
                ],
            },
            {
                course_code: 1,
                course_name: 1,
                _program_id: 1,
                _curriculum_id: 1,
                course_type: 1,
                'course_assigned_details._program_id': 1,
                'course_assigned_details._curriculum_id': 1,
                'course_assigned_details.year': 1,
                'course_assigned_details.level_no': 1,
                'course_assigned_details.course_shared_with._program_id': 1,
                'course_assigned_details.course_shared_with.year': 1,
                'course_assigned_details.course_shared_with.level_no': 1,
                'course_assigned_details.course_shared_with._curriculum_id': 1,
            },
        )
        .lean();
    const programList = { [programId]: {} };
    if (curriculamData.length) {
        for (const curriculamElement of curriculamData) {
            programList[programId] = {
                program_name: curriculamElement.program_name,
                curriculum: {},
            };
            const years = {};
            for (const yearElement of curriculamElement.year_level) {
                for (const levelElement of yearElement.levels) {
                    years[`${yearElement.y_type}-${levelElement.level_name}`] = {
                        courseIds: [],
                    };
                }
            }
            const curriculumEntry = {
                curriculum_name: curriculamElement.curriculum_name,
                years,
            };
            programList[programId].curriculum[curriculamElement._id] = curriculumEntry;
        }
    }
    // course type wise split
    for (const [programIndex, programElement] of Object.entries(programList)) {
        for (const courseElement of courseList) {
            for (const [curriculumIndex, curriculumElement] of Object.entries(
                programElement.curriculum,
            )) {
                for (const [yearIndex, yearElement] of Object.entries(curriculumElement.years)) {
                    const isSharedCourse =
                        programIndex.toString() !== courseElement._program_id.toString() &&
                        curriculumIndex.toString() !== courseElement._curriculum_id.toString();

                    const shared_with_others = !!isSharedCourse;
                    if (
                        courseElement.course_assigned_details &&
                        courseElement.course_assigned_details.length
                    ) {
                        for (const courseSharedElement of courseElement.course_assigned_details) {
                            if (!shared_with_others) {
                                if (
                                    `${courseSharedElement.year}-${courseSharedElement.level_no}` ===
                                    yearIndex
                                ) {
                                    yearElement.courseIds.push({
                                        courseId: courseElement._id,
                                        course_code: courseElement.course_code,
                                        course_name: courseElement.course_name,
                                        course_type: courseElement.course_type,
                                        shared_with_others,
                                    });
                                }
                            } else {
                                const sharedCourseDetails =
                                    courseSharedElement.course_shared_with.find(
                                        ({
                                            _program_id = '',
                                            _curriculum_id = '',
                                            year = '',
                                            level_no = '',
                                        }) =>
                                            String(_program_id) === programIndex &&
                                            String(_curriculum_id) === _curriculum_id &&
                                            `${year}-${level_no}` === yearIndex,
                                    );
                                if (sharedCourseDetails) {
                                    yearElement.courseIds.push({
                                        courseId: courseElement._id,
                                        course_code: courseElement.course_code,
                                        course_name: courseElement.course_name,
                                        course_type: courseElement.course_type,
                                        shared_with_others,
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return programList;
};

exports.ploDetails = async ({ programId, curriculumName }) => {
    const programFrameworkDetails = await curriculumSchema
        .find(
            {
                _program_id: convertToMongoObjectId(programId),
                ...(curriculumName && { curriculum_name: curriculumName }),
            },
            {
                curriculum_name: 1,
                'framework._id': 1,
                'framework.name': 1,
                'framework.code': 1,
                'framework.domains._id': 1,
                'framework.domains.name': 1,
                'framework.domains.no': 1,
                'framework.domains.plo._id': 1,
                'framework.domains.plo.name': 1,
                'framework.domains.plo.no': 1,
                'framework.domains.plo.isDeleted': 1,
            },
        )
        .lean();
    return programFrameworkDetails;
};

exports.cloDetails = async ({ courseId }) => {
    const courseFrameworkDetails = await courseSchema
        .findOne(
            {
                _id: convertToMongoObjectId(courseId),
            },
            {
                'framework._id': 1,
                'framework.name': 1,
                'framework.code': 1,
                'framework.domains._id': 1,
                'framework.domains.name': 1,
                'framework.domains.no': 1,
                'framework.domains.clo._id': 1,
                'framework.domains.clo.name': 1,
                'framework.domains.clo.no': 1,
                'framework.domains.clo.isDeleted': 1,
            },
        )
        .lean();
    return courseFrameworkDetails;
};
