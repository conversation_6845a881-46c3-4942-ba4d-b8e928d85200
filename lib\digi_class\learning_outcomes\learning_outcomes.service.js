const Course = require('../../models/digi_course');
const Curriculum = require('../../models/digi_curriculum');
const { dsGetAllWithSortAsJSON } = require('../../base/base_controller');

/**
 * get all plos by clo id
 * @param {*} cloIds
 * @returns
 */
const getPlosByCloIds = async (cloIds) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': { $in: cloIds } };
        const project = { _id: 1, framework: 1 };
        const curriculums = (await dsGetAllWithSortAsJSON(Curriculum, query, project)).data;
        const resultPlos = [];
        curriculums.forEach((curriculum) => {
            curriculum.framework.domains.forEach((domain) => {
                domain.plo.forEach((plo) => {
                    plo.clos.forEach((clo) => {
                        if (
                            cloIds.find((cloId) => cloId.toString() === clo.clo_id.toString()) &&
                            !resultPlos.find(
                                (resPlo) => resPlo._id.toString() === plo._id.toString(),
                            )
                        ) {
                            resultPlos.push(plo);
                        }
                    });
                });
            });
        });
        return resultPlos;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get all clos by slo id
 * @param {*} sloIds
 * @returns
 */
const getClosBySloIds = async (sloIds) => {
    try {
        const query = { 'framework.domains.clo.slos.slo_id': { $in: sloIds } };
        const project = { _id: 1, framework: 1 };
        const courses = (await dsGetAllWithSortAsJSON(Course, query, project)).data;
        if (!courses.length) return [];
        const resultClos = [];
        for (const course of courses) {
            const { domains } = course.framework;
            for (const domain of domains) {
                for (const clo of domain.clo) {
                    for (const slo of clo.slos) {
                        if (
                            sloIds.find((sloId) => sloId.toString() === slo.slo_id.toString()) &&
                            !resultClos.find(
                                (resClo) => resClo._id.toString() === clo._id.toString(),
                            )
                        ) {
                            clo.mappedPlo = (await getPlosByCloIds([clo._id])).length;
                            resultClos.push(clo);
                        }
                    }
                }
            }
        }
        return resultClos;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get all clos by slo id
 * @param {*} sloIds
 * @returns
 */
const getClos = async (sloIds) => {
    try {
        const query = { 'framework.domains.clo._id': { $in: sloIds } };
        const project = { _id: 1, framework: 1 };
        const courses = (await dsGetAllWithSortAsJSON(Course, query, project)).data;
        if (!courses.length) return [];
        const resultClos = [];
        for (const course of courses) {
            const { domains } = course.framework;
            for (const domain of domains) {
                for (const clo of domain.clo) {
                    if (
                        sloIds.find((sloId) => sloId.toString() === clo._id.toString()) &&
                        !resultClos.find((resClo) => resClo._id.toString() === clo._id.toString())
                    ) {
                        resultClos.push(clo);
                    }
                }
            }
        }
        return resultClos;
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = {
    getClosBySloIds,
    getPlosByCloIds,
    getClos,
};
