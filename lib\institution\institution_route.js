const express = require('express');
const route = express.Router();
const institution = require('./institution_controller');
const validater = require('./institution_validator');
route.post('/list', institution.list_values);
route.get('/:id', validater.institution_id, institution.list_id);
route.get('/', institution.list);
route.post('/', validater.institution, institution.insert);
route.put('/:id', validater.institution_id, validater.institution, institution.update);
route.delete('/:id', validater.institution_id, institution.delete);
module.exports = route;
