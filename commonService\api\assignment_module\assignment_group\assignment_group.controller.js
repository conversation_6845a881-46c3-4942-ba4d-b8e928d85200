const assignmentGroupSchema = require('./assignment_group.model');
const { convertToMongoObjectId } = require('../../../utility/common');

const addAssignmentGroup = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _assignment_id,
            groupSetName,
            grouping,
            courseGroup,
            courseGroupId,
            noOfGroup,
            enrollmentDate,
            groups,
            rotation_count,
            term,
        } = body;
        const assignmentGroup = await assignmentGroupSchema.create({
            _institution_id,
            _assignment_id,
            groupSetName,
            grouping,
            courseGroup,
            courseGroupId,
            noOfGroup,
            enrollmentDate,
            groups,
            rotation_count,
            term,
        });
        if (!assignmentGroup) return { statusCode: 410, message: 'ASSESSMENT GROUP NOT ADDED' };
        return {
            statusCode: 200,
            message: 'ASSESSMENT GROUP ADDED',
            data: { assignmentGroupId: assignmentGroup._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAssignmentGroup = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const {
            _assignment_id,
            groupSetName,
            grouping,
            courseGroup,
            courseGroupId,
            noOfGroup,
            enrollmentDate,
            groups,
        } = body;
        const assignment = await assignmentGroupSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            {
                _assignment_id,
                groupSetName,
                grouping,
                courseGroup,
                courseGroupId,
                noOfGroup,
                enrollmentDate,
                groups,
                _institution_id,
            },
        );
        if (!assignment) return { statusCode: 410, message: 'ASSIGNMENT GROUP NOT UPDATED' };
        return { statusCode: 200, message: 'ASSIGNMENT GROUP UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssignmentGroup = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const assignment = await assignmentGroupSchema
            .findById({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!assignment) return { statusCode: 410, message: 'ASSIGNMENT GROUP NOT FOUND' };
        return { statusCode: 200, message: 'ASSIGNMENT GROUP DATA', data: assignment };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAssignmentGroup = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _assignment_id } = params;
        const { term, rotation_count } = query;
        const assignment = await assignmentGroupSchema
            .find({
                _institution_id: convertToMongoObjectId(_institution_id),
                _assignment_id: convertToMongoObjectId(_assignment_id),
                term,
                rotation_count,
                isDeleted: false,
            })
            .lean();
        if (!assignment.length) return { statusCode: 410, message: 'ASSIGNMENT GROUP NOT FOUND' };
        return { statusCode: 200, message: 'ASSIGNMENT GROUP DATA', data: assignment };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteAssignmentGroup = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const assignment = await assignmentGroupSchema
            .findByIdAndDelete({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!assignment) return { statusCode: 410, message: 'ASSIGNMENT GROUP NOT DELETE' };
        return { statusCode: 200, message: 'ASSIGNMENT GROUP DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    addAssignmentGroup,
    updateAssignmentGroup,
    deleteAssignmentGroup,
    listAssignmentGroup,
    getAssignmentGroup,
};
