const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    numberSchema,
    stringSchema,
} = require('../../../utility/validationSchemas');

exports.paramIDValidator = Joi.object({
    id: objectIdRQSchema,
});

exports.addAssignmentGroupValidator = Joi.object({
    _assignment_id: objectIdSchema,
    groupSetName: stringSchema,
    grouping: stringSchema,
    courseGroup: stringSchema,
    courseGroupId: Joi.array().items(
        Joi.object({
            groupId: objectIdSchema,
            subGroups: Joi.array().items(
                Joi.object({
                    subGroup: stringSchema,
                    sessionGroups: Joi.array().items(
                        Joi.object({
                            sessionGroupId: objectIdSchema,
                        }).unknown(true),
                    ),
                }).unknown(true),
            ),
        }).unknown(true),
    ),
    noOfGroup: numberSchema,
    enrollmentDate: stringSchema,
    groups: Joi.array().items(
        Joi.object({
            groupName: stringSchema,
            description: stringSchema,
            memberLimit: numberSchema,
            students: Joi.array().items(Joi.object({ studentId: objectIdSchema }).unknown(true)),
            prompts: Joi.array().items(Joi.object({ _prompt_id: objectIdSchema }).unknown(true)),
            instructor: Joi.array().items(
                Joi.object({
                    _staff_id: objectIdSchema,
                    staffName: Joi.object({
                        first: stringSchema,
                        last: stringSchema,
                    }),
                }).unknown(true),
            ),
        }).unknown(true),
    ),
    rotation_count: numberSchema,
    term: stringSchema,
}).unknown(true);
