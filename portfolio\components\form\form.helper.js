const { PUBLISHED } = require('../../common/utils/enums');
const { isBoolean, areStringsEqualIgnoringCase } = require('../../common/utils/common.util');

const getAssignedUserSections = ({
    form,
    roleName,
    canGetRolesAndUsers = false,
    isEvaluator = false,
}) => {
    const filteredPages = form.pages
        .map(({ elements, ...page }) => {
            const filteredElements = elements.filter((element) => {
                const role = element?.roles?.find((roleEntry) =>
                    areStringsEqualIgnoringCase(roleName, roleEntry.role),
                );
                const user = element?.users?.find((userEntry) =>
                    areStringsEqualIgnoringCase(userEntry.role, roleName),
                );

                if (role?.view || user?.view) {
                    Object.assign(element, {
                        edit: isBoolean(role?.edit) ? role.edit : user?.view,
                        fill: isBoolean(role?.fill) ? role.fill : user?.fill,
                        modify: isBoolean(role?.modify) ? role.modify : user?.modify,
                        evaluate: isBoolean(role?.evaluate) ? role.evaluate : user?.evaluate,
                        view: isBoolean(role?.view) ? role.view : user?.view,
                        ...(canGetRolesAndUsers && {
                            roles: element?.roles ?? [],
                            users: element?.users ?? [],
                        }),
                    });
                    if (!canGetRolesAndUsers) delete element.roles;
                    return true;
                }

                return false;
            });

            if (!canGetRolesAndUsers && page?.evaluators?.length) delete page.evaluators;
            return filteredElements.length ? { ...page, elements: filteredElements } : null;
        })
        .filter(Boolean); // Remove empty pages

    return { ...form, pages: filteredPages };
};

const buildFormQuery = ({ email, formId, isEvaluator }) => {
    const orConditions = isEvaluator
        ? [
              {
                  'pages.elements.users': {
                      $elemMatch: {
                          email,
                          evaluate: true,
                      },
                  },
              },
              {
                  'pages.elements.roles': {
                      $elemMatch: {
                          evaluate: true,
                          users: {
                              $elemMatch: {
                                  email,
                              },
                          },
                      },
                  },
              },
          ]
        : [{ 'pages.elements.users.email': email }, { 'pages.elements.roles.users.email': email }];

    return {
        $or: orConditions,
        status: PUBLISHED,
        isDeleted: false,
        ...(formId && { _id: formId }),
    };
};

module.exports = {
    getAssignedUserSections,
    buildFormQuery,
};
