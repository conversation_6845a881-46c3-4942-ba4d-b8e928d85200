const express = require('express');
const route = express.Router();
const notification_manager = require('./notification_manager_controller');
const validater = require('./notification_manager_validator');
route.post('/list', notification_manager.list_values);
route.get('/:id', validater.notification_manager_id, notification_manager.list_id);
route.get('/', notification_manager.list);
route.post('/', validater.notification_manager, notification_manager.insert);
route.put('/:id', validater.notification_manager_id, validater.notification_manager, notification_manager.update);
route.delete('/:id', validater.notification_manager_id, notification_manager.delete);

module.exports = route;