// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.institution = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    type: Joi.string()
                        .min(3)
                        .max(20)
                        .error((error) => {
                            return error;
                        }),
                    name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return error;
                        }),
                    code: Joi.string()
                        .alphanum()
                        .min(2)
                        .max(10)
                        .error((error) => {
                            return error;
                        }),
                    location: Joi.string()
                        .allow('-')
                        .min(1)
                        .max(250)
                        .error((error) => {
                            return error;
                        }),
                    _country_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.institution_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
