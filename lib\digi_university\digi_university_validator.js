// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');


exports.id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.user_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            user_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.insert = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            institute_name: Joi.string().min(2).max(100).required().error(error => {
                return error;
            }),
            institute_type: Joi.string().valid(constant.INSTITUTION_TYPE.INDIVIDUAL, constant.INSTITUTION_TYPE.GROUP).required().error(error => {
                return error;
            }),
            no_of_college: Joi.number().error(error => {
                return error;
            }),
            logo: Joi.string().required().error(error => {
                return error;
            }),
            address_details: Joi.object().keys({
                address: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                country: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                state: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                district: Joi.string().error(error => {
                    return error;
                }),
                city: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                zipcode: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
            }).unknown(true)

        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            institute_name: Joi.string().min(2).max(100).required().error(error => {
                return error;
            }),
            no_of_college: Joi.number().error(error => {
                return error;
            }),
            // logo: Joi.string().min(2).max(100).required().error(error => {
            //     return error;
            // }),
            address_details: Joi.object().keys({
                address: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                country: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                state: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                district: Joi.string().allow('').error(error => {
                    return error;
                }),
                city: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                zipcode: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
            }).unknown(true)

        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}