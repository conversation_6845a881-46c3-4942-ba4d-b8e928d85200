const express = require('express');
const route = express.Router();
const {
    getStudentGroups,
    createDiscussionTopic,
    getDiscussionTopics,
    deleteDiscussionTopic,
    likeDiscussionTopic,
    createDiscussionReply,
    likeReplyComment,
    deleteReplyComment,
    getRepliesLists,
    viewDiscussionAttachment,
    getStudentDiscussionLists,
    updateUnReadDiscussionCount,
} = require('./discussions_controller');
const { createDiscussion, createReply } = require('./discussions_validate_schema');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get('/list', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], getStudentGroups);
route.post(
    '/create',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    createDiscussion,
    createDiscussionTopic,
);
route.get('/getLists', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], getDiscussionTopics);
route.delete('/', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], deleteDiscussionTopic);
route.put('/like', [userPolicyAuthentication([defaultPolicy.DC_STUDENT])], likeDiscussionTopic);
route.get(
    '/viewAttachment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    viewDiscussionAttachment,
);
route.get(
    '/getStudentDiscussionLists',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    getStudentDiscussionLists,
);

route.post(
    '/reply',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    createReply,
    createDiscussionReply,
);
route.put(
    '/reply/like',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    likeReplyComment,
);
route.delete(
    '/reply',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    deleteReplyComment,
);
route.get(
    '/getReplyLists',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getRepliesLists,
);
route.get(
    '/unReadCounts',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateUnReadDiscussionCount,
);

module.exports = route;
