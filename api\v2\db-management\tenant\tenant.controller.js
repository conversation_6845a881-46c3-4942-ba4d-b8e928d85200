const { MONGO_DB_V2 } = require('../../utility/util_keys');
const { connectNewTenant, getAdminConnection } = require('../db-connection.service');
const { startMigration } = require('./tenant-db.service');
const { ROLE, ROLE_ASSIGN, CITY, COUNTRY, STATES, USER } = require('../../utility/constants');

const createTenant = async (adminDbConnection, body) => {
    try {
        const Tenant = await adminDbConnection.model('Tenant');
        const name = body.name;
        const subdomain = body.subdomain;
        const tenantPresent = await Tenant.findOne({
            name,
            subdomain,
        });
        if (tenantPresent) {
            throw new Error('Tenant Already Present');
        }
        const defaultDbName = 'digischeduler';
        const dbNameIndex = MONGO_DB_V2.lastIndexOf(defaultDbName);
        const newTenantDbURI =
            MONGO_DB_V2.substring(0, dbNameIndex) +
            subdomain +
            MONGO_DB_V2.substring(dbNameIndex + defaultDbName.length);
        const newTenant = await new Tenant({
            name,
            dbURI: newTenantDbURI,
            subdomain,
        }).save();
        return newTenant;
    } catch (error) {
        console.log('createTenant error', error);
        throw error;
    }
};

const createTenantHandler = async (req, res) => {
    try {
        const adminConnection = getAdminConnection();
        const tenant = await createTenant(adminConnection, req.body);
        connectNewTenant(tenant.subdomain, tenant.dbURI);
        res.status(200).json({ success: true, tenant });
    } catch (err) {
        console.log('tenant create handler error', err);
        res.status(err.statusCode || 500).json({ error: err.message });
    }
};

const getTenants = async () => {
    try {
        const adminConnection = getAdminConnection();
        const Tenant = await adminConnection.model('Tenant');
        const tenants = await Tenant.find({}, { name: 1, subdomain: 1, createdAt: 1 });
        return { statusCode: 200, data: tenants };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const migrateAdminCollections = async ({ body = {} }) => {
    try {
        const { tenantDomain } = body;
        const adminConnection = getAdminConnection();
        const Tenant = await adminConnection.model('Tenant');
        const tenant = await Tenant.findOne({ subdomain: tenantDomain }, { dbURI: 1 }).lean();
        const collections = [ROLE_ASSIGN, CITY, STATES, COUNTRY, ROLE, USER];
        collections.forEach(
            async (collection) =>
                await startMigration(
                    MONGO_DB_V2,
                    'digischeduler',
                    collection,
                    tenant.dbURI,
                    tenantDomain,
                    collection,
                ),
        );
        return { statusCode: 200 };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = { createTenant, createTenantHandler, getTenants, migrateAdminCollections };
