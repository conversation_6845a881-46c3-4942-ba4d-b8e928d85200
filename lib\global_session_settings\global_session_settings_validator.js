const Joi = require('joi');

exports.createDaySessionValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string().alphanum().length(24).required(),
        }).unknown(true),
        body: Joi.object({
            sessions: Joi.array()
                .items(
                    Joi.object({
                        _id: Joi.string().alphanum().length(24),
                        sessionName: Joi.string().allow(''),
                        sessionStart: Joi.date(),
                        sessionEnd: Joi.date(),
                    }),
                )
                .empty(null),
            isSameTimeForEveryDay: Joi.boolean().required(),
            scheduleForNonWorkingDays: Joi.boolean().required(),
            workingDays: Joi.array().items(
                Joi.object({
                    day: Joi.string()
                        .valid(
                            'monday',
                            'tuesday',
                            'wednesday',
                            'thursday',
                            'friday',
                            'saturday',
                            'sunday',
                        )
                        .insensitive(),
                }).unknown(true),
            ),
        }).unknown(true),
    });

    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });

    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }

    next();
};

exports.getSessionValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string().alphanum().length(24).required(),
        }).unknown(true),
    });

    const { error } = schema.validate({
        headers: req.headers,
    });

    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }

    next();
};

const INSTITUTION_ID_VALIDATOR = Joi.string().alphanum().length(24).required();
exports.getTardisValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: INSTITUTION_ID_VALIDATOR,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.createTardisValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: INSTITUTION_ID_VALIDATOR,
        }).unknown(true),
        body: Joi.object({
            _id: Joi.string().allow(null).alphanum().length(24).required(),
            name: Joi.string().min(1).required(),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.deleteTardisValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: INSTITUTION_ID_VALIDATOR,
        }).unknown(true),
        body: Joi.object({
            _id: Joi.string().alphanum().length(24).required(),
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
exports.updateGlobalSessionStatusValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: INSTITUTION_ID_VALIDATOR,
        }).unknown(true),
        body: Joi.object({
            sessionStatusDetails: Joi.object({
                isEnabled: Joi.boolean().required(),
                activeToInactive: Joi.string().valid('present', 'absent', 'exclude').required(),
                inactiveToActive: Joi.string().valid('present', 'absent', 'exclude').required(),
            }).required(),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
exports.schedulePermissionValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({ _institution_id: INSTITUTION_ID_VALIDATOR }).unknown(true),
        query: Joi.object({
            create: Joi.boolean().required(),
            edit: Joi.boolean().required(),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.getHandoutValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({ _institution_id: INSTITUTION_ID_VALIDATOR }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.updateHandoutValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({ _institution_id: INSTITUTION_ID_VALIDATOR }).unknown(true),
        body: Joi.object({
            handout: Joi.boolean().optional(),
            sessionView: Joi.boolean().optional(),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
