const sessionOrderSchema = require('./session-order.model');
const programInputSchema = require('../program-input/program-input.model');
const courseSchema = require('../course/course.model');
const { getPaginationValues } = require('../../utility/pagination');
const { convertToMongoObjectId, getModel } = require('../../utility/common');
const {
    DS_UPDATED,
    DS_ADDED,
    DS_DELETED,
    DS_UPDATE_FAILED,
    DS_ADD_FAILED,
    DS_GET_FAILED,
    DS_DELETE_FAILED,
    SESSION_ORDER,
    PROGRAM,
    COURSE,
} = require('../../utility/constants');

const listSessions = async ({ query = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _program_id } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        const findQuery = { _course_id, _program_id, isDeleted: false };
        let sessions = await sessionOrderModel
            .find(findQuery)
            .sort({ sNo: 1 })
            .skip(skip)
            .limit(limit)
            .lean();
        if (!sessions) return { statusCode: 500, message: DS_GET_FAILED };
        const courseDoc = await courseModel
            .findById({ _id: _course_id })
            .lean()
            .select('linkSessions');
        const linkedSessionIds = [];
        if (courseDoc.linkSessions)
            for (const linkSession of courseDoc.linkSessions) {
                for (const sessionOrder of linkSession.sessionOrder) {
                    linkedSessionIds.push(sessionOrder._session_id.toString());
                }
            }
        sessions.forEach((value, index) => {
            if (linkedSessionIds.includes(value._id.toString())) {
                sessions[index].isLinked = true;
            } else {
                sessions[index].isLinked = false;
            }
        });
        const totalDocs = await sessionOrderModel.find(findQuery).countDocuments().lean();
        const totalPages = Math.ceil(totalDocs / limit);
        const courseThemeList = await courseModel.findOne(
            { _id: convertToMongoObjectId(_course_id) },
            { _id: 1, themes: 1, courseAssignedDetails: 1, linkSessionsOrder: 1 },
        );
        if (courseThemeList.themes.length) {
            const themesLists = courseThemeList.themes.flat();

            sessions = sessions.map((element) => {
                const sessionLists = element;
                if (element.theme) {
                    sessionLists.themeDetails = themesLists.find(
                        (elementEntry) => elementEntry._id.toString() === element.theme.toString(),
                    );
                }
                return sessionLists;
            });
        }
        const courseAssign = [];
        if (courseThemeList.courseAssignedDetails && courseThemeList.courseAssignedDetails.length) {
            courseThemeList.courseAssignedDetails.forEach((assign) => {
                courseAssign.push({
                    levelId: assign._level_id,
                    isSessionOrderWeekActive: assign.isSessionOrderWeekActive,
                });
            });
        }
        return {
            statusCode: 200,
            data: {
                totalPages,
                currentPage: pageNo,
                sessions,
                courseAssign,
                linkSessionsOrder: courseThemeList.linkSessionsOrder,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listIndependentCourseSessions = async ({ query = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { _course_id } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        const findQuery = { _course_id, isDeleted: false };
        let sessions = await sessionOrderModel
            .find(findQuery)
            .sort({ sNo: 1 })
            .skip(skip)
            .limit(limit)
            .lean();
        if (!sessions) return { statusCode: 500, message: DS_GET_FAILED };
        const courseDoc = await courseModel
            .findById({ _id: _course_id })
            .lean()
            .select('linkSessions isWeekActiveForIndependentCourse');
        const linkedSessionIds = [];
        if (courseDoc.linkSessions)
            for (const linkSession of courseDoc.linkSessions) {
                for (const sessionOrder of linkSession.sessionOrder) {
                    linkedSessionIds.push(sessionOrder._session_id.toString());
                }
            }
        sessions.forEach((value, index) => {
            if (linkedSessionIds.includes(value._id.toString())) {
                sessions[index].isLinked = true;
            } else {
                sessions[index].isLinked = false;
            }
        });
        const totalDocs = await sessionOrderModel.find(findQuery).countDocuments().lean();
        const totalPages = Math.ceil(totalDocs / limit);
        const courseThemeList = await courseModel.findOne(
            { _id: convertToMongoObjectId(_course_id) },
            { _id: 1, themes: 1, courseAssignedDetails: 1 },
        );
        if (courseThemeList.themes.length) {
            const themesLists = courseThemeList.themes.flat();

            sessions = sessions.map((element) => {
                const sessionLists = element;
                if (element.theme) {
                    sessionLists.themeDetails = themesLists.find(
                        (elementEntry) => elementEntry._id.toString() === element.theme.toString(),
                    );
                }
                return sessionLists;
            });
        }
        const courseAssign = [];
        if (courseThemeList.courseAssignedDetails && courseThemeList.courseAssignedDetails.length) {
            courseThemeList.courseAssignedDetails.forEach((assign) => {
                courseAssign.push({
                    levelId: assign._level_id,
                    isSessionOrderWeekActive: assign.isSessionOrderWeekActive,
                });
            });
        }
        return {
            statusCode: 200,
            data: {
                totalPages,
                currentPage: pageNo,
                isWeekActiveForIndependentCourse: courseDoc.isWeekActiveForIndependentCourse,
                sessions,
                courseAssign,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addSession = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const programInputModel = getModel(tenantURL, PROGRAM, programInputSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _course_id,
            _program_id,
            _institution_id,
            sessions,
            areActive,
            isCourseLevelDraft,
            isSessionOrderWeekDraft,
            _level_id,
            linkSessionsOrder,
        } = body;
        const program = await programInputModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_program_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 400, message: 'PROGRAM_NOT_EXISTS' };
        }
        const oldSessions = await sessionOrderModel.find(
            {
                _institution_id,
                _course_id,
                _program_id,
            },
            { _id: 1 },
        );
        const newSessionIds = [];
        await sessionOrderModel.deleteMany({ _institution_id, _program_id, _course_id });
        const sessionsOrder = [];
        sessions.forEach((session, index) => {
            sessionsOrder.push({
                _id: session._id,
                _institution_id,
                _course_id,
                _program_id,
                deliveryType: session.deliveryType,
                _deliveryType_id: session.deliveryTypeId,
                deliveryNumber: session.deliveryNumber,
                deliverySymbol: session.deliverySymbol,
                deliveryTopic: session.deliveryTopic,
                _session_id: session.sessionId,
                subjects: session.subjects,
                mode: session.mode,
                duration: session.duration,
                sNo: session.sNo,
                theme: session.theme,
                week: session.week,
                isActive: areActive,
            });
            newSessionIds.push(session._id);
        });
        const deletedSessions = [];
        const bulkUpdate = [];
        const courseDocs = await courseModel
            .findById({ _id: convertToMongoObjectId(_course_id) })
            .select('linkSessions');
        oldSessions.forEach((session) => {
            if (!newSessionIds.includes(session._id.toString())) {
                deletedSessions.push(session._id);
                courseDocs.linkSessions.forEach((links) => {
                    links.sessionOrder.forEach((linkedSessionOrder) => {
                        if (linkedSessionOrder._session_id.toString() === session._id.toString()) {
                            bulkUpdate.push({
                                updateOne: {
                                    filter: {
                                        isDeleted: false,
                                        _id: convertToMongoObjectId(_course_id),
                                    },
                                    update: {
                                        $pull: {
                                            'linkSessions.$[linksId].sessionOrder': {
                                                _session_id: convertToMongoObjectId(session._id),
                                            },
                                        },
                                    },
                                    arrayFilters: [{ 'linksId._id': links._id }],
                                },
                            });
                        }
                    });
                });
            }
        });
        if (bulkUpdate.length) await courseModel.bulkWrite(bulkUpdate);
        const courseActive = await courseModel.findByIdAndUpdate(
            { _id: convertToMongoObjectId(_course_id) },
            { $set: { isActive: areActive, linkSessionsOrder } },
            { new: true },
        );
        const bulkUpdateForSingleLink = [];
        courseActive.linkSessions.forEach((links) => {
            links.sessionOrder.forEach((linkedSessionOrder) => {
                if (links.sessionOrder.length === 1) {
                    bulkUpdateForSingleLink.push({
                        updateOne: {
                            filter: {
                                isDeleted: false,
                                _id: convertToMongoObjectId(_course_id),
                            },
                            update: {
                                $pull: {
                                    linkSessions: {
                                        _id: convertToMongoObjectId(links._id),
                                    },
                                },
                            },
                        },
                    });
                }
            });
        });
        if (bulkUpdateForSingleLink.length) await courseModel.bulkWrite(bulkUpdateForSingleLink);
        const sessionsInserted = await sessionOrderModel.create(sessionsOrder);
        if (isCourseLevelDraft) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                { 'courseAssignedDetails.$[assignId].isActive': false },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (isSessionOrderWeekDraft && _level_id) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                {
                    'courseAssignedDetails.$[assignId].isSessionOrderWeekActive': false,
                },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (!isSessionOrderWeekDraft && _level_id) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                {
                    'courseAssignedDetails.$[assignId].isSessionOrderWeekActive': true,
                },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (sessionsInserted) return { statusCode: 201, message: DS_ADDED };
        return { statusCode: 201, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addIndependentCourseSession = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _course_id,
            _institution_id,
            sessions,
            areActive,
            isWeekActiveForIndependentCourse,
            isCourseLevelDraft,
            isSessionOrderWeekDraft,
            _level_id,
        } = body;
        const oldSessions = await sessionOrderModel.find(
            {
                _institution_id,
                _course_id,
            },
            { _id: 1 },
        );
        const newSessionIds = [];
        await sessionOrderModel.deleteMany({ _institution_id, _course_id });
        const sessionsOrder = [];
        sessions.forEach((session, index) => {
            sessionsOrder.push({
                _id: session._id,
                _institution_id,
                _course_id,
                deliveryType: session.deliveryType,
                _deliveryType_id: session.deliveryTypeId,
                deliveryNumber: session.deliveryNumber,
                deliverySymbol: session.deliverySymbol,
                deliveryTopic: session.deliveryTopic,
                _session_id: session.sessionId,
                subjects: session.subjects,
                mode: session.mode,
                duration: session.duration,
                sNo: session.sNo,
                theme: session.theme,
                week: session.week,
                isActive: areActive,
            });
            newSessionIds.push(session._id);
        });
        const deletedSessions = [];
        const bulkUpdate = [];
        const courseDocs = await courseModel
            .findById({ _id: convertToMongoObjectId(_course_id) })
            .select('linkSessions');
        oldSessions.forEach((session) => {
            if (!newSessionIds.includes(session._id.toString())) {
                deletedSessions.push(session._id);
                courseDocs.linkSessions.forEach((links) => {
                    links.sessionOrder.forEach((linkedSessionOrder) => {
                        if (linkedSessionOrder._session_id.toString() === session._id.toString()) {
                            bulkUpdate.push({
                                updateOne: {
                                    filter: {
                                        isDeleted: false,
                                        _id: convertToMongoObjectId(_course_id),
                                    },
                                    update: {
                                        $pull: {
                                            'linkSessions.$[linksId].sessionOrder': {
                                                _session_id: convertToMongoObjectId(session._id),
                                            },
                                        },
                                    },
                                    arrayFilters: [{ 'linksId._id': links._id }],
                                },
                            });
                        }
                    });
                });
            }
        });
        if (bulkUpdate.length) await courseModel.bulkWrite(bulkUpdate);
        const courseActive = await courseModel.findByIdAndUpdate(
            { _id: convertToMongoObjectId(_course_id) },
            { $set: { isActive: areActive, isWeekActiveForIndependentCourse } },
            { new: true },
        );
        const bulkUpdateForSingleLink = [];
        courseActive.linkSessions.forEach((links) => {
            links.sessionOrder.forEach((linkedSessionOrder) => {
                if (links.sessionOrder.length === 1) {
                    bulkUpdateForSingleLink.push({
                        updateOne: {
                            filter: {
                                isDeleted: false,
                                _id: convertToMongoObjectId(_course_id),
                            },
                            update: {
                                $pull: {
                                    linkSessions: {
                                        _id: convertToMongoObjectId(links._id),
                                    },
                                },
                            },
                        },
                    });
                }
            });
        });
        if (bulkUpdateForSingleLink.length) await courseModel.bulkWrite(bulkUpdateForSingleLink);
        const sessionsInserted = await sessionOrderModel.create(sessionsOrder);
        if (isCourseLevelDraft) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                { 'courseAssignedDetails.$[assignId].isActive': false },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (isSessionOrderWeekDraft && _level_id) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                {
                    'courseAssignedDetails.$[assignId].isSessionOrderWeekActive': false,
                },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (!isSessionOrderWeekDraft && _level_id) {
            const updateLevel = await courseModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_course_id) },
                {
                    'courseAssignedDetails.$[assignId].isSessionOrderWeekActive': true,
                },
                { arrayFilters: [{ 'assignId._level_id': convertToMongoObjectId(_level_id) }] },
            );
        }
        if (sessionsInserted) return { statusCode: 201, message: DS_ADDED };
        return { statusCode: 201, message: DS_ADD_FAILED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editSession = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const programModel = getModel(tenantURL, PROGRAM, programInputSchema);
        const { _course_id, _program_id, _institution_id, sessions, areActive } = body;
        const program = await programModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_program_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 400, message: 'PROGRAM_NOT_EXISTS' };
        }

        const sessionsOrder = [];
        sessions.forEach((session, index) => {
            sessionsOrder.push({
                _institution_id,
                _course_id,
                _program_id,
                deliveryType: session.deliveryType,
                _deliveryType_id: session._deliveryType_id,
                deliveryNumber: session.deliveryNumber,
                deliverySymbol: session.deliverySymbol,
                deliveryTopic: session.deliveryTopic,
                subjects: session.subjects,
                mode: session.mode,
                duration: session.duration,
                sNo: session.sNo,
                theme: session.theme,
                week: session.week,
                isActive: areActive,
            });
        });

        await sessionOrderModel.deleteMany({ _institution_id, _program_id, _course_id });

        const sessionsInserted = await sessionOrderModel.create(sessionsOrder);
        if (!sessionsInserted) return { statusCode: 500, message: DS_ADD_FAILED };
        return { statusCode: 201, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editIndependentCourseSession = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programInputModel = getModel(tenantURL, PROGRAM, programInputSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { _course_id, _institution_id, sessions, areActive } = body;
        const program = await programInputModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_program_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 400, message: 'PROGRAM_NOT_EXISTS' };
        }

        const sessionsOrder = [];
        sessions.forEach((session, index) => {
            sessionsOrder.push({
                _institution_id,
                _course_id,
                deliveryType: session.deliveryType,
                _deliveryType_id: session._deliveryType_id,
                deliveryNumber: session.deliveryNumber,
                deliveryTopic: session.deliveryTopic,
                subjects: session.subjects,
                mode: session.mode,
                duration: session.duration,
                sNo: session.sNo,
                theme: session.theme,
                week: session.week,
                isActive: areActive,
            });
        });

        await sessionOrderModel.deleteMany({ _institution_id, _course_id });

        const sessionsInserted = await sessionOrderModel.create(sessionsOrder);
        if (!sessionsInserted) return { statusCode: 500, message: DS_ADD_FAILED };
        return { statusCode: 201, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteSession = async ({ params = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { id } = params;
        const session = await sessionOrderModel.findByIdAndUpdate(
            id,
            {
                isDeleted: true,
                isActive: false,
            },
            { new: true },
        );
        if (!session) {
            return { statusCode: 500, message: DS_DELETED };
        }
        return { statusCode: 200, message: DS_DELETE_FAILED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    addSession,
    editSession,
    deleteSession,
    listSessions,
    addIndependentCourseSession,
    editIndependentCourseSession,
    listIndependentCourseSessions,
};
