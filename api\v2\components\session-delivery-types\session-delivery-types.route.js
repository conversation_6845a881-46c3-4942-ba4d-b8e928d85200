const route = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    sessionTypesAdd,
    deliveryTypesAdd,
    sessionTypesUpdate,
    deliveryTypesUpdate,
    sessionTypesDelete,
    deliveryTypesDelete,
    sessionTypesGet,
    deliveryTypesGet,
    addIndependentCourseDeliveryType,
    updateIndependentCourseSessionType,
    addSessionTypesForIndependentCourse,
    updateIndependentCourseDeliveryType,
    getIndependentCourseSessionTypes,
    deleteIndependentSessionTypes,
    sessionCreateIndianSystem,
    editSessionIndianSystem,
    deleteSessionTypeIndianSystem,
    addDeliveryTypesIndianSystem,
    deliveryTypesUpdateIndianSystem,
    deliveryTypesDeleteIndianSystem,
    configureIndianSystemWithCredits,
    toggleDeliveryTypeIndianCreditSystem,
} = require('./session-delivery-types.controller');
const {
    sessionTypesAddSchema,
    deliveryTypesAddSchema,
    sessionTypesUpdateSchema,
    deliveryTypesUpdateSchema,
    deliveryTypesDeleteSchema,
    sessionTypesGetSchema,
    sessionTypesDeleteSchema,
    addIndependentCourseSessionSchema,
    updateIndependentCourseSessionSchema,
    independentDeliveryTypesUpdateSchema,
    independentCourseDeliveryTypesAddSchema,
    independentSessionTypesGetSchema,
    deleteIndependentSessionTypeValidator,
} = require('./session-delivery-types.validator');
const { validate } = require('../../utility/input-validation');

route.post('/', validate(sessionTypesAddSchema), catchAsync(sessionTypesAdd));
route.post(
    '/delivery-type/:sessionTypeId',
    validate(deliveryTypesAddSchema),
    catchAsync(deliveryTypesAdd),
);
route.put('/:sessionTypeId', validate(sessionTypesUpdateSchema), catchAsync(sessionTypesUpdate));
route.put(
    '/:sessionTypeId/:deliveryTypeId',
    validate(deliveryTypesUpdateSchema),
    catchAsync(deliveryTypesUpdate),
);
route.delete('/:sessionTypeId', validate(sessionTypesDeleteSchema), catchAsync(sessionTypesDelete));
route.delete(
    '/:sessionTypeId/:deliveryTypeId',
    validate(deliveryTypesDeleteSchema),
    catchAsync(deliveryTypesDelete),
);
route.get('/delivery-list', catchAsync(deliveryTypesGet));
route.get('/:_program_id', validate(sessionTypesGetSchema), catchAsync(sessionTypesGet));
route.post(
    '/add-independent-course-session-type',
    validate(addIndependentCourseSessionSchema),
    catchAsync(addSessionTypesForIndependentCourse),
);
route.put(
    '/update-independent-course/session-type/:sessionTypeId',
    validate(updateIndependentCourseSessionSchema),
    catchAsync(updateIndependentCourseSessionType),
);
route.post(
    '/add-independent-delivery-type/:id',
    validate(independentCourseDeliveryTypesAddSchema),
    catchAsync(addIndependentCourseDeliveryType),
);
route.put(
    '/update-independent-course/delivery-type/:sessionTypeId/:deliveryTypeId',
    validate(independentDeliveryTypesUpdateSchema),
    catchAsync(updateIndependentCourseDeliveryType),
);
route.get(
    '/independent-course-session-types/:_institution_id',
    validate(independentSessionTypesGetSchema),
    catchAsync(getIndependentCourseSessionTypes),
);
route.delete(
    '/independent-course/delete-session-type/:sessionTypeId',
    validate(deleteIndependentSessionTypeValidator),
    catchAsync(deleteIndependentSessionTypes),
);
route.post('/session-create-indian-system', catchAsync(sessionCreateIndianSystem));
route.post('/edit-session-indian-system/:sessionTypeId', catchAsync(editSessionIndianSystem));
route.delete(
    '/delete-session-indian-system/:sessionTypeId',
    catchAsync(deleteSessionTypeIndianSystem),
);
route.post(
    '/delivery-type-create-indian-system/:sessionTypeId',
    catchAsync(addDeliveryTypesIndianSystem),
);
route.post(
    '/delivery-types-update-indian-system/:sessionTypeId/:deliveryTypeId',
    catchAsync(deliveryTypesUpdateIndianSystem),
);
route.delete(
    '/delete-delivery-type-indian-system/:sessionTypeId/:deliveryTypeId',
    catchAsync(deliveryTypesDeleteIndianSystem),
);
route.post('/config-indian-system-credits', catchAsync(configureIndianSystemWithCredits));
route.post(
    '/toggle-delivery-type-indian-credit-system',
    catchAsync(toggleDeliveryTypeIndianCreditSystem),
);

module.exports = route;
