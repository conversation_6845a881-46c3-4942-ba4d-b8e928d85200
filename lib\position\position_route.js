const express = require('express');
const route = express.Router();
const position = require('./position_controller');
const validater = require('./position_validator');
route.post('/list', position.list_values);
route.get('/:id', validater.position_id, position.list_id);
route.get('/', position.list);
route.post('/', validater.position, position.insert);
route.put('/:id', validater.position_id, validater.position, position.update);
route.delete('/:id', validater.position_id, position.delete);

module.exports = route;