const courseScheduleSchema = require('../models/course_schedule');
const userSchema = require('../models/user');
const { convertToMongoObjectId, clone } = require('../utility/common');
const { getStudentCourseDetails, getStaffCourseDetails } = require('./userGlobalSearch.service');
const {
    EVENT_WHOM: { STUDENT, STAFF },
    COMPLETED,
} = require('../utility/constants');

// User drop down list
const getUserDropDownList = async ({ query = {} }) => {
    try {
        const { userType, searchKey } = query;
        const userSearchKey = searchKey.trim().split(' ');
        const searchQuery = {
            isDeleted: false,
            isActive: true,
            user_type: userType,
            status: COMPLETED,
        };
        if (searchKey && searchKey.trim() !== '') {
            if (userSearchKey.length === 1) {
                searchQuery.$or = [
                    { 'name.first': { $regex: searchKey, $options: 'i' } },
                    { 'name.last': { $regex: searchKey, $options: 'i' } },
                    { 'name.family': { $regex: searchKey, $options: 'i' } },
                    { 'name.middle': { $regex: searchKey, $options: 'i' } },
                    { user_id: { $regex: searchKey, $options: 'i' } },
                ];
            } else {
                const searchRegex = userSearchKey.map((term) => new RegExp(term, 'i'));
                searchQuery.$or = [
                    { 'name.first': { $in: searchRegex } },
                    { 'name.last': { $in: searchRegex } },
                    { 'name.family': { $in: searchRegex } },
                    { 'name.middle': { $in: searchRegex } },
                    { user_id: { $in: searchRegex } },
                ];
            }
        }
        const userList = await userSchema
            .find(searchQuery, { user_type: 1, user_id: 1, name: 1, email: 1, gender: 1 })
            .sort({ 'name.first': 1 })
            .limit(10)
            .lean();
        if (!userList || !userList.length) {
            return { statusCode: 404, data: [], message: 'No Data' };
        }
        return {
            statusCode: 200,
            data: userList,
            message: 'User List',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getTotalCourseSchedules = async ({ courseScheduleData }) => {
    const missingScheduleStudentDetail = new Map();
    const missingScheduleId = [];
    let totalSchedules = courseScheduleData;
    for (const scheduleElement of courseScheduleData) {
        if (scheduleElement.merge_status === true) {
            for (const mergeWithElement of scheduleElement.merge_with) {
                const scheduleId = courseScheduleData.find(
                    (mergeScheduleElement) =>
                        mergeScheduleElement._id.toString() ===
                        mergeWithElement.schedule_id.toString(),
                );
                if (!scheduleId) {
                    const missingScheduleStudentDetailKey = mergeWithElement.schedule_id.toString();
                    missingScheduleStudentDetail.set(missingScheduleStudentDetailKey, {
                        studentAttendanceDetail: scheduleElement.students[0],
                    });
                    missingScheduleId.push(mergeWithElement.schedule_id);
                }
            }
        }
    }
    if (missingScheduleId.length) {
        const missingCourseScheduleData = await courseScheduleSchema
            .find(
                {
                    _id: { $in: missingScheduleId },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _program_id: 1,
                    program_name: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    _course_id: 1,
                    course_name: 1,
                    course_code: 1,
                    status: 1,
                    rotation: 1,
                    rotation_count: 1,
                    session: 1,
                    merge_status: 1,
                    merge_with: 1,
                },
            )
            .populate({ path: '_course_id', select: { credit_hours: 1 } })
            .lean();
        for (const missingCourseScheduleElement of missingCourseScheduleData) {
            const studentAttendanceDetailForMissingSchedule = missingScheduleStudentDetail.get(
                missingCourseScheduleElement._id.toString(),
            );
            missingCourseScheduleElement.students = studentAttendanceDetailForMissingSchedule
                ? [studentAttendanceDetailForMissingSchedule]
                : [
                      {
                          status: 'absent',
                          primaryStatus: 'absent',
                          _id: userId,
                      },
                  ];
        }
        totalSchedules = [...courseScheduleData, ...missingCourseScheduleData];
    }
    return totalSchedules;
};

//user course details
const getUserCourseDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { institutionCalendarId, userType, userId } = query;
        const { _institution_id } = headers;
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            type: 'regular',
            ...(userType === STUDENT
                ? { 'students._id': convertToMongoObjectId(userId) }
                : { 'staffs._staff_id': convertToMongoObjectId(userId) }),
        };
        const scheduleProjection = {
            _program_id: 1,
            program_name: 1,
            term: 1,
            year_no: 1,
            level_no: 1,
            _course_id: 1,
            course_name: 1,
            course_code: 1,
            status: 1,
            rotation: 1,
            rotation_count: 1,
            session: 1,
            merge_status: 1,
            merge_with: 1,
            scheduleStartDateAndTime: 1,
            'sessionDetail.start_time': 1,
            ...(userType === STUDENT ? { 'students.$': 1 } : { 'staffs.$': 1 }),
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .find(scheduleQuery, scheduleProjection)
            .lean();
        console.timeEnd('courseScheduleData');
        console.log(courseScheduleData.length);
        if (!courseScheduleData || !courseScheduleData.length) {
            return {
                statusCode: 404,
                message: 'No Data Found',
                data: [],
            };
        }
        let userCourseDetails = [];
        if (userType === STUDENT) {
            // const totalCourseSchedules = await getTotalCourseSchedules({ courseScheduleData });
            console.time('StudentCourseDetails');
            userCourseDetails = await getStudentCourseDetails({
                studentScheduleData: courseScheduleData,
                studentId: userId,
                institutionCalendarId,
                _institution_id,
            });
            console.timeEnd('StudentCourseDetails');
        }
        if (userType === STAFF) {
            console.time('StaffCourseDetails');
            userCourseDetails = await getStaffCourseDetails({
                staffScheduleData: courseScheduleData,
                staffId: userId,
                institutionCalendarId,
            });
            console.timeEnd('StaffCourseDetails');
        }
        return {
            statusCode: 200,
            message: 'Course Details',
            data: userCourseDetails,
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getUserCourseDetails,
    getUserDropDownList,
};
