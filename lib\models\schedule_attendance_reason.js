const mongoose = require('mongoose');
const { Schema } = mongoose;

const { SCHEDULE_ATTENDANCE_REASONS, PRESENT, ABSENT, USER } = require('../utility/constants');

const attendanceStatusReason = new Schema(
    {
        reason: {
            type: String,
        },
        status: {
            type: String,
            enum: [PRESENT, ABSENT],
        },
        staffs: {
            _staff_id: {
                type: Schema.Types.ObjectId,
                ref: USER,
            },
            staff_name: {
                first: {
                    type: String,
                    // required: true,
                    trim: true,
                },
                middle: {
                    type: String,
                    trim: true,
                },
                last: {
                    type: String,
                    trim: true,
                },
                family: {
                    type: String,
                    trim: true,
                },
            },
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(SCHEDULE_ATTENDANCE_REASONS, attendanceStatusReason);
