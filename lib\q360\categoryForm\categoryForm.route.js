const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    getProgramList,
    programDetails,
    getInstitution,
    roleUserList,
    createDuplicateForm,
    getCategoryForm,
    updateCategoryForm,
    categoryTypeForm,
    getFormAttachment,
    getApprovalHierarchy,
    singleFormOccurrence,
    getCurriculum,
    createGroups,
    formConfigureList,
    editConfigure,
    concludingPhase,
    termList,
    resetFormCourseSetting,
    qapcGuideResources,
    qapcSignedUrl,
    unPublishedForm,
    getAllInstitutionCalender,
} = require('./categoryForm.controller');
const { qapcGuideResourcesValidator } = require('./categoryForm.validator');
const validator = require('./categoryForm.validator');
const { validate } = require('../../../middleware/validation');

//program List
router.get('/getProgramList', validate(validator.getProgramValidator), catchAsync(getProgramList));
router.get(
    '/programDetails',
    validate(validator.programDetailsValidator),
    catchAsync(programDetails),
);
router.get('/getCurriculum', validate(validator.getCurriculumValidator), catchAsync(getCurriculum));
router.get('/getInstitution', catchAsync(getInstitution));
router.get('/roleUserList', validate(validator.getProgramValidator), catchAsync(roleUserList));
router.get('/termList', validate(validator.getCurriculumValidator), catchAsync(termList));
//form creation
router.post(
    '/createDuplicateForm',
    validate(validator.createDuplicateFormValidator),
    catchAsync(createDuplicateForm),
);
router.get(
    '/getCategoryForm',
    validate(validator.getCategoryFormValidator),
    catchAsync(getCategoryForm),
);
router.get(
    '/categoryTypeForm',
    validate(validator.categoryTypeFormValidator),
    catchAsync(categoryTypeForm),
);
//upload multiple file
router.post('/qapcGuideResources', qapcGuideResourcesValidator, catchAsync(qapcGuideResources));
router.get('/qapcSignedUrl', validate(validator.qapcSignedUrlValidator), catchAsync(qapcSignedUrl));
router.get(
    '/getFormAttachment',
    validate(validator.getFormAttachmentValidator),
    catchAsync(getFormAttachment),
);
router.get(
    '/getApprovalHierarchy',
    validate(validator.getFormAttachmentValidator),
    catchAsync(getApprovalHierarchy),
);
router.get(
    '/singleFormOccurrence',
    validate(validator.getFormAttachmentValidator),
    catchAsync(singleFormOccurrence),
);
router.put(
    '/updateCategoryForm',
    validate(validator.updateCategoryFormValidator),
    catchAsync(updateCategoryForm),
);
//update the configure
router.post('/createGroups', validate(validator.createGroupsValidator), catchAsync(createGroups));
router.get(
    '/formConfigureList',
    validate(validator.formConfigureListValidator),
    catchAsync(formConfigureList),
);
router.put('/editConfigure', validate(validator.editConfigureValidator), catchAsync(editConfigure));
router.get(
    '/concludingPhase',
    validate(validator.getFormAttachmentValidator),
    catchAsync(concludingPhase),
);
//configure action edit
router.put(
    '/resetFormCourseSetting',
    validate(validator.resetFormCourseSettingValidator),
    catchAsync(resetFormCourseSetting),
);
//isUnpublished form
router.put('/unPublishedForm', catchAsync(unPublishedForm));
//HEBA
router.get('/getAllInstitutionCalender', catchAsync(getAllInstitutionCalender));

module.exports = router;
