const {
    response_function,
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
    query: commonQuery,
} = require('../utility/common');
const { get_list, get, get_list_populate } = require('../base/base_controller');
const { redisClient } = require('../../config/redis-connection');
const {
    INSTITUTION,
    // PROGRAM_CALENDAR,
    // DIGI_COURSE,
    // USER,
    // ROLE,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    USER,
    PART_TIME,
    FULL_TIME,
    PROGRAM_<PERSON>LENDAR,
    STUDENT_GROUP,
    DIGI_SESSION_ORDER,
    D<PERSON>I_SESSION_DELIVERY_TYPES,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_COURSE,
    ACADEMIC,
    ADMINISTRATION,
    BOTH,
    COURSE_SCHEDULE,
    COMPLETED,
    PRIMARY,
    PRESENT,
    LMS,
    ABSENT,
    LEAVE,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    REPORT_ANALYTICS: { PROGRAM_LIST, PLO, CREDIT_HOURS, NO_STUDENT, NO_STAFF },
    SUMMARY,
    STUDENT_CRITERIA_MANIPULATION,
    SCHEDULE_TYPES,
    V1_PROGRAM_CREDIT_HOURS,
    V1_PROGRAM_COURSE_CREDIT_HOURS,
    V1_PROGRAM_COURSE_SCHEDULE_STAFF_SUBJECT,
    V1_PROGRAM_LEVEL_STUDENT_COUNT,
    V1_PROGRAM_LEVEL_STUDENTS,
    V1_PROGRAM_LEVEL_STAFFS,
    COURSE_WISE,
    LEAVE_TYPE,
    GENDER,
    EXCLUDE,
} = require('../utility/constants');
const {
    staffSplit,
    scheduleDateFormateChange,
    dateTimeBasedConverter,
    courseSessionOrderFilterBasedSchedule,
} = require('../utility/common_functions');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const {
    allProgramDatas,
    allCurriculumDatas,
    allProgramCalendarDatas,
    allSessionOrderDatas,
    allSessionDeliveryTypesDatas,
    allRoleAssignDatas,
    allCourseScheduleTillYesterday,
    allStudentGroupYesterday,
    allCourseList,
    allDepartmentSubjectList,
    clearAllItem,
    clearItem,
} = require('../../service/cache.service');
const {
    dashboardProgramList,
    dashboardPLO,
    dashboardCreditHoursCalculation,
    dashboardNoStudent,
    dashboardNoStaff,
    programWiseSummary,
    programWiseYearLevelCourseData,
} = require('./reportsAnalytics.services');
const { logger, SCHEDULE_SESSION_BASED_REPORT } = require('../utility/util_keys');
const {
    userRoleData,
    courseCoordinatorBasedIds,
    userCourseScheduleList,
} = require('../digi_dashboard/dashboard.service');
const {
    lmsNewSetting,
    getLateAutoAndManualRange,
    getLateConfigAndStudentLateAbsent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
} = require('../utility/utility.service');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
// const course = require('mongoose').model(DIGI_COURSE);
const user = require('mongoose').model(USER);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
const digi_curriculum = require('mongoose').model(DIGI_CURRICULUM);
const program = require('mongoose').model(DIGI_PROGRAM);
const student_group = require('mongoose').model(STUDENT_GROUP);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const session_delivery_type = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const department_subject = require('mongoose').model(DIGI_DEPARTMENT_SUBJECT);
const course = require('mongoose').model(DIGI_COURSE);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const lms = require('mongoose').model(LMS);
const lmsStudentSettingData = async (_institution_id) => {
    // Leave Setting Data
    let warningAbsenceData = [
        { warning: 'Denial', absence_percentage: 101, warning_message: 'Denial' },
    ];
    const leave_category = await get(
        lms,
        {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
        },
        { _id: 1, category: 1, student_warning_absence_calculation: 1 },
    );
    if (
        leave_category.status &&
        leave_category.data &&
        leave_category.data.student_warning_absence_calculation &&
        leave_category.data.student_warning_absence_calculation.length !== 0 &&
        leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        ).length !== 0
    )
        warningAbsenceData = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
    warningAbsenceData = clone(
        warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        }),
    );

    const finaleWarning =
        warningAbsenceData.length !== 1
            ? warningAbsenceData[1] && warningAbsenceData[1].warning
                ? warningAbsenceData[1].warning
                : undefined
            : undefined;
    const denialWarning = warningAbsenceData[0].warning;
    return {
        warningAbsenceData,
        finaleWarning,
        denialWarning,
    };
};

const studentAttendanceReport = async (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const studentSchedule = courseScheduled.filter(
            (ele) =>
                ele &&
                ele.students.find((ele2) => ele2._id.toString() === studentElement.toString()),
        );
        // const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
        const absentSchedules = studentSchedule.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === PERMISSION ||
                            ele2.status === LEAVE),
                ),
        );
        const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
        studentElement.absence = denialPercentage.toFixed(2);
        const studentWarningAbsence = clone(warningAbsenceData);
        if (
            studentCriteriaData &&
            studentCriteriaData.data &&
            studentCriteriaData.status != false
        ) {
            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement.toString(),
            );
            studentElement.manipulationStatus = false;
            studentElement.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].absence_percentage &&
                studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
                studentElement.manipulationStatus = true;
                studentElement.manipulationPercentage = studentManipulation.absencePercentage;
            }
        }
        const warningData = studentWarningAbsence.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
        );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.warning : '',
        });
    }
    return studentData;
};
const studentAttendanceReportV2 = async (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
    lateDurationRange,
    manualLateRange,
    manualLateData,
    lateExcludeManagement,
) => {
    try {
        const studentData = [];
        let lateExclude = false;
        if (courseScheduled?.length) {
            const { lateExclude: updatedLateExclude } =
                checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: courseScheduled[0]._institution_calendar_id,
                    courseId: courseScheduled[0]._course_id,
                    programId: courseScheduled[0]._program_id,
                    levelNo: courseScheduled[0].level_no,
                    term: courseScheduled[0].term,
                    rotationCount: courseScheduled[0].rotation_count,
                    lateExcludeManagement,
                });
            lateExclude = updatedLateExclude;
        }
        for (studentElement of courseStudentIds) {
            const studentSchedule = courseScheduled.filter(
                (courseScheduleElement) =>
                    courseScheduleElement &&
                    courseScheduleElement.students.find(
                        (scheduledStudentElement) =>
                            scheduledStudentElement._id.toString() === studentElement.toString() &&
                            scheduledStudentElement.status !== EXCLUDE,
                    ),
            );
            const completedSchedule = studentSchedule.filter(
                (scheduleElem) =>
                    scheduleElem.status === COMPLETED &&
                    scheduleElem.students.find(
                        (studentScheduleElem) =>
                            studentScheduleElem._id.toString() === studentElement.toString(),
                    ),
            ).length;
            const absentSchedules = studentSchedule.filter(
                (scheduleElem) =>
                    scheduleElem.status === COMPLETED &&
                    scheduleElem.students.find(
                        (studentScheduleElem) =>
                            studentScheduleElem._id.toString() === studentElement.toString() &&
                            (studentScheduleElem.status === ABSENT ||
                                studentScheduleElem.status === LEAVE),
                    ),
            );
            let lateExcludeForStudent = false;
            if (courseScheduled?.length) {
                lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: courseScheduled[0]._institution_calendar_id,
                    courseId: courseScheduled[0]._course_id,
                    programId: courseScheduled[0]._program_id,
                    levelNo: courseScheduled[0].level_no,
                    term: courseScheduled[0].term,
                    rotationCount: courseScheduled[0].rotation_count,
                    studentId: studentElement,
                    lateExcludeManagement,
                }).lateExclude;
            }
            let studentLateAbsent = 0;
            if (!lateExcludeForStudent && !lateExclude) {
                const { studentLateAbsent: updateStudentLateAbsent } =
                    getLateConfigAndStudentLateAbsent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        scheduleData: courseScheduled,
                        studentElement: { _student_id: studentElement },
                        lateExcludeManagement,
                    });
                studentLateAbsent = updateStudentLateAbsent;
            }
            const denialPercentage =
                ((absentSchedules.length + studentLateAbsent) / studentSchedule.length) * 100;
            studentElement.absence = denialPercentage.toFixed(2);
            const studentWarningAbsence = clone(warningAbsenceData);
            if (studentCriteriaData.length && studentSchedule.length) {
                const studentManipulation =
                    studentCriteriaData.length &&
                    studentCriteriaData.find(
                        (denialStudentElement) =>
                            ((studentSchedule[0].rotation_count &&
                            studentSchedule[0].rotation_count > 0
                                ? parseInt(studentSchedule[0].rotation_count) ===
                                  denialStudentElement.rotationCount
                                : true) &&
                                denialStudentElement.term === studentSchedule[0].term &&
                                denialStudentElement.courseId.toString() ===
                                    studentSchedule[0]._course_id.toString() &&
                                denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                            (denialStudentElement.term === studentSchedule[0].term &&
                                denialStudentElement.studentId &&
                                denialStudentElement.courseId.toString() ===
                                    studentSchedule[0]._course_id.toString() &&
                                denialStudentElement.studentId.toString() ===
                                    studentElement.toString()),
                    );
                studentElement.manipulationStatus = false;
                studentElement.manipulationPercentage = 0;
                if (
                    studentManipulation &&
                    studentManipulation.absencePercentage &&
                    studentWarningAbsence[0] &&
                    studentWarningAbsence[0].percentage &&
                    studentWarningAbsence[0].percentage < studentManipulation.absencePercentage
                ) {
                    studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                    studentElement.manipulationStatus = true;
                    studentElement.manipulationPercentage = studentManipulation.absencePercentage;
                }
            }
            const warningData = studentWarningAbsence.find(
                (ele) =>
                    ele.percentage && parseFloat(denialPercentage) > parseFloat(ele.percentage),
            );
            studentData.push({
                student_id: studentElement.toString(),
                warning: warningData ? warningData.labelName : '',
                studentLateAbsent,
            });
        }
        return studentData;
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

const courseCreditContactHours = (
    sessionDeliveryTypes,
    _program_id,
    courseScheduled,
    sessionOrderData,
    credit_hours,
    studentId,
) => {
    const credit_hoursData = [];
    const sessionDeliveryTypesData = sessionDeliveryTypes.filter(
        (ele) => ele._program_id.toString() === _program_id.toString(),
    );
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (courseScheduleIdElement) =>
                            courseScheduleIdElement &&
                            courseScheduleIdElement.session &&
                            courseScheduleIdElement.session.session_type &&
                            courseScheduleIdElement.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase() &&
                            (studentId
                                ? courseScheduleIdElement.students &&
                                  courseScheduleIdElement.students.some(
                                      (studentElement) =>
                                          studentElement._id &&
                                          studentElement.status &&
                                          studentElement._id.toString() === studentId.toString() &&
                                          studentElement.status !== EXCLUDE,
                                  )
                                : true),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        let sessionCompletedHours = 0;
        let endSchedule = 0;
        let scheduleSessionCount = 0;
        let attendanceCount = 0;
        let leaveCount = 0;
        let permissionCount = 0;
        let ondutyCount = 0;
        let absentCount = 0;
        // let endSchedule = [];
        if (deliveryTypeData.length) {
            // Altering Based on Scheduled Session SG wise
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    if (studentId) {
                        const studentAttendance = sessionSchedule.filter(
                            (scheduleElement) =>
                                scheduleElement.status === COMPLETED &&
                                scheduleElement.students &&
                                scheduleElement.students.find(
                                    (studentElement) =>
                                        studentElement._id.toString() === studentId.toString(),
                                ),
                        );
                        for (studentAttendanceElement of studentAttendance) {
                            const studentScheduleElement = studentAttendanceElement.students.find(
                                (studentElement) =>
                                    studentElement._id.toString() === studentId.toString(),
                            );
                            if (studentScheduleElement) {
                                switch (studentScheduleElement.status) {
                                    case PRESENT:
                                        attendanceCount++;
                                        break;
                                    case LEAVE_TYPE.LEAVE:
                                        leaveCount++;
                                        break;
                                    case LEAVE_TYPE.ONDUTY:
                                        ondutyCount++;
                                        break;
                                    case LEAVE_TYPE.PERMISSION:
                                        permissionCount++;
                                        break;
                                    case ABSENT:
                                        absentCount++;
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        // attendanceCount += sessionSchedule.filter(
                        //     (ele2) =>
                        //         ele2 &&
                        //         ele2.students &&
                        //         ele2.students.find(
                        //             (ele) =>
                        //                 ele &&
                        //                 ele._id.toString() === studentId.toString() &&
                        //                 (ele.status === PRESENT || ele.status === ONDUTY),
                        //         ),
                        // ).length;
                    }
                    endSchedule++;
                    sessionCompletedHours += sessionOrderElement.duration;
                }
            }

            // endSchedule = deliveryTypeData.filter((ele) => ele.status === COMPLETED);
            // for (sessionOrderElement of sessionOrderData) {
            //     if (
            //         endSchedule.find(
            //             (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
            //         )
            //     )
            //         sessionCompletedHours += sessionOrderElement.duration;
            // }
        }
        const courseDeliveryWiseDurationPerContactHours =
            deliveryCredit &&
            deliveryCredit.duration_per_contact_hour &&
            parseInt(deliveryCredit.duration_per_contact_hour)
                ? parseInt(deliveryCredit.duration_per_contact_hour)
                : 60;
        const sessionCompletedCredit =
            sessionCompletedHours !== 0
                ? sessionCompletedHours / courseDeliveryWiseDurationPerContactHours
                : 0;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours:
                deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
            completed_credit_hours:
                sessionCompletedCredit /
                (studentId
                    ? parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0
                    : (deliveryCredit && deliveryCredit.contact_hours
                          ? parseInt(deliveryCredit.contact_hours)
                          : parseInt(sessionTypeElement.contact_hour_per_credit_hour)) || 0),
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours:
                parseInt(
                    deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
                ) *
                (deliveryCredit && deliveryCredit.contact_hours
                    ? parseInt(deliveryCredit.contact_hours)
                    : parseInt(sessionTypeElement.contact_hour_per_credit_hour)),
            // no_of_sessions: deliveryTypeData.length,
            no_of_sessions: scheduleSessionCount,
            completed_sessions: endSchedule,
            present_count: attendanceCount,
            leave_count: leaveCount,
            onduty_count: ondutyCount,
            permission_count: permissionCount,
            absent_count: absentCount,
            // completed_sessions: endSchedule.length,
        });
    }
    return credit_hoursData;
};

const courseScheduleData = async ({ institutionCalendarId }) => {
    console.time('scheduleList');
    const scheduleList = await course_schedule
        .find(
            {
                isActive: true,
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                type: 'regular',
            },
            {
                'session._session_id': 1,
                'session.session_type': 1,
                'students._id': 1,
                'students.status': 1,
                'students.tardisId': 1,
                'students.primaryTime': 1,
                'students.lateExclude': 1,
                'staffs._staff_id': 1,
                'subjects._subject_id': 1,
                _course_id: 1,
                _institution_calendar_id: 1,
                _program_id: 1,
                year_no: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                status: 1,
                scheduleStartDateAndTime: 1,
            },
        )
        .lean();
    console.timeEnd('scheduleList');
    return scheduleList;
};

// Get Program -> Level wise Course Course Coordinators
exports.dashboard = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, userId, roleId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const program_list = await get_list(
            program,
            { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
            {},
        );
        if (!program_list.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('PROGRAM_LIST'), []));
        const curriculum_data = await get_list(
            digi_curriculum,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                isActive: true,
            },
            {
                curriculum_name: 1,
                _program_id: 1,
                framework: 1,
            },
        );
        const role_assign_list = await get(
            role_assign,
            {
                _user_id: convertToMongoObjectId(userId),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            {},
        );
        if (!role_assign_list.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('USERS_ROLE_NOT_FOUND'),
                        req.t('USERS_ROLE_NOT_FOUND'),
                    ),
                );
        const roleData = role_assign_list.data.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        // let programIds = roleData.isAdmin
        //     ? roleData.program.map((ele) => ele._program_id.toString())
        //     : [];
        let programIds = roleData.program.map((ele) => ele._program_id.toString());
        programIds = [...new Set(programIds)];

        //Staff Gets
        const staffData = await get_list(
            user,
            {
                status: 'completed',
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
                'academic_allocation._program_id': {
                    $in: programIds,
                },
                isActive: true,
                isDeleted: false,
            },
            { gender: 1, employment: 1, academic_allocation: 1 },
        );
        staffData.data = staffData.status ? staffData.data : [];

        //Credit Hours Get in Program Calendar
        const programCalendar = await get_list(
            program_calendar,
            {
                _program_id: { $in: programIds },
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                'level.term': 1,
                'level.level_no': 1,
                'level.course._course_id': 1,
                'level.course.credit_hours': 1,
                'level.year': 1,
                'level.rotation': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.credit_hours': 1,
            },
        );
        //Student group data
        const studentGroupData = await get_list(
            student_group,
            {
                isDeleted: false,
                'master._program_id': { $in: programIds },
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            },
            {},
        );

        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: { $in: programIds },
                // _course_id: { $in: courseIds },
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                _session_id: '$session._session_id',
                'session._session_id': 1,
                'session.session_type': 1,
                status: 1,
                _course_id: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];

        const sessionDeliveryTypes = await get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: { $in: programIds },
            },
            {},
        );
        const sessionOrder = await get_list(
            session_order,
            {
                ...commonQuery,
                _institution_id: convertToMongoObjectId(_institution_id),
                // _course_id: { $in: courseIds },
            },
            {
                _program_id: 1,
                _course_id: 1,
                'session_flow_data._id': 1,
                'session_flow_data.duration': 1,
            },
        );

        const programs = [];
        const programPlo = [];
        const staffPrograms = [];
        const creditHoursPrograms = [];
        const studentPrograms = [];
        // program_list.data.forEach((element) => {
        for (element of program_list.data) {
            if (programIds.findIndex((ele) => ele.toString() === element._id.toString()) !== -1) {
                // Staff Calculation
                // const programStaffs = [];
                // for (staffElement of staffData.data) {
                //     if (
                //         staffElement.academic_allocation.find(
                //             (allocationElement) =>
                //                 // allocationElement.allocation_type.toString() === PRIMARY &&
                //                 allocationElement._program_id.toString() === element._id.toString(),
                //         )
                //     ) {
                //         programStaffs.push(staffElement);
                //     }
                // }
                const programStaffs = staffData.data.filter((staffElement) =>
                    staffElement.academic_allocation.find(
                        (allocationElement) =>
                            allocationElement.allocation_type.toString() === PRIMARY &&
                            allocationElement._program_id.toString() === element._id.toString(),
                    ),
                );
                staffPrograms.push({
                    program_id: element._id,
                    program_name: element.name,
                    program_code: element.code,
                    male_count: programStaffs.filter((ele) => ele.gender === 'male').length,
                    female_count: programStaffs.filter((ele) => ele.gender === 'female').length,
                    part_time_count: programStaffs.filter(
                        (ele) => ele.employment.user_employment_type === PART_TIME,
                    ).length,
                    full_time_count: programStaffs.filter(
                        (ele) => ele.employment.user_employment_type === FULL_TIME,
                    ).length,
                });

                //Credit Hours Calculation
                let theory = 0;
                let practical = 0;
                let clinical = 0;
                let theoryCompleted = 0;
                let practicalCompleted = 0;
                let clinicalCompleted = 0;
                let years = [];
                const pcData = programCalendar.status
                    ? programCalendar.data.find(
                          (idElement) =>
                              idElement._program_id.toString() === element._id.toString(),
                      )
                    : undefined;
                if (pcData) {
                    const programSessionDeliveryTypes = sessionDeliveryTypes.data.filter(
                        (ele) => ele._program_id.toString() === element._id.toString(),
                    );
                    for (levelElement of pcData.level) {
                        years.push(levelElement.year);
                        if (levelElement.rotation === 'no') {
                            for (courseElement of levelElement.course) {
                                const theoryCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Theory',
                                );
                                const practicalCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Practical',
                                );
                                const clinicalCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Clinical',
                                );
                                theory += theoryCredit ? theoryCredit.credit_hours : 0;
                                practical += practicalCredit ? practicalCredit.credit_hours : 0;
                                clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;

                                // Credit Hour Calculation
                                const courseSessionOrder = sessionOrder.data.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        courseElement._course_id.toString(),
                                );
                                const sessionOrderData = courseSessionOrder
                                    ? courseSessionOrder.session_flow_data
                                    : [];
                                const courseScheduled = clone(
                                    scheduleData.data.filter(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                                courseElement._course_id.toString() &&
                                            ele.term === levelElement.term &&
                                            ele.level_no === levelElement.level_no,
                                    ),
                                );
                                const credit_hours = await courseCreditContactHours(
                                    programSessionDeliveryTypes,
                                    element._id.toString(),
                                    courseScheduled,
                                    sessionOrderData,
                                    courseElement.credit_hours,
                                );
                                // credit_hours
                                const completedTheoryCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Theory',
                                );
                                const completedPracticalCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Practical',
                                );
                                const completedClinicalCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Clinical',
                                );
                                theoryCompleted += completedTheoryCredit
                                    ? completedTheoryCredit.completed_credit_hours
                                    : 0;
                                practicalCompleted += completedPracticalCredit
                                    ? completedPracticalCredit.completed_credit_hours
                                    : 0;
                                clinicalCompleted += completedClinicalCredit
                                    ? completedClinicalCredit.completed_credit_hours
                                    : 0;
                            }
                        } else if (
                            levelElement.rotation_course[0] &&
                            levelElement.rotation_course[0].course
                        ) {
                            for (courseElement of levelElement.rotation_course[0].course) {
                                const theoryCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Theory',
                                );
                                const practicalCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Practical',
                                );
                                const clinicalCredit = courseElement.credit_hours.find(
                                    (ele) => ele.type_name === 'Clinical',
                                );
                                theory += theoryCredit ? theoryCredit.credit_hours : 0;
                                practical += practicalCredit ? practicalCredit.credit_hours : 0;
                                clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;

                                // Credit Hour Calculation
                                const courseSessionOrder = sessionOrder.data.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        courseElement._course_id.toString(),
                                );
                                const sessionOrderData = courseSessionOrder
                                    ? courseSessionOrder.session_flow_data
                                    : [];
                                const courseScheduled = clone(
                                    scheduleData.data.filter(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                                courseElement._course_id.toString() &&
                                            ele.term === levelElement.term &&
                                            ele.level_no === levelElement.level_no,
                                    ),
                                );
                                const credit_hours = await courseCreditContactHours(
                                    programSessionDeliveryTypes,
                                    element._id.toString(),
                                    courseScheduled,
                                    sessionOrderData,
                                    courseElement.credit_hours,
                                );
                                // credit_hours
                                const completedTheoryCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Theory',
                                );
                                const completedPracticalCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Practical',
                                );
                                const completedClinicalCredit = credit_hours.find(
                                    (ele) => ele.type_name === 'Clinical',
                                );
                                theoryCompleted += completedTheoryCredit
                                    ? completedTheoryCredit.completed_credit_hours
                                    : 0;
                                practicalCompleted += completedPracticalCredit
                                    ? completedPracticalCredit.completed_credit_hours
                                    : 0;
                                clinicalCompleted += completedClinicalCredit
                                    ? completedClinicalCredit.completed_credit_hours
                                    : 0;
                            }
                        }
                    }
                }
                creditHoursPrograms.push({
                    program_id: element._id,
                    program_name: element.name,
                    program_code: element.code,
                    theory,
                    practical,
                    clinical,
                    // Todo Need to check All schedule Based on SG
                    theory_complete: theoryCompleted,
                    practical_complete: practicalCompleted,
                    clinical_complete: clinicalCompleted,
                });
                years = [...new Set(years)];

                // Program List
                let curriculum_list = [];
                const curriculumDatas = [];
                if (curriculum_data.status) {
                    curriculum_list = curriculum_data.data.filter(
                        (i) => i._program_id.toString() == element._id.toString(),
                    );
                }

                //PLO Count
                const curriculumData = [];
                for (curriculumElement of curriculum_list) {
                    let plo_count = 0;
                    for (const domain of curriculumElement.framework.domains) {
                        for (const plo of domain.plo) {
                            if (plo && plo.isDeleted === false) plo_count++;
                        }
                    }
                    curriculumDatas.push({
                        _id: curriculumElement._id,
                        curriculum_name: curriculumElement.curriculum_name,
                        _program_id: curriculumElement._program_id,
                    });
                    curriculumData.push({
                        curriculum_name: curriculumElement.curriculum_name,
                        plo_count,
                    });
                }
                programPlo.push({
                    program_id: element._id,
                    program_name: element.name,
                    program_code: element.code,
                    curriculums: curriculumData,
                });

                programs.push({
                    ...element.toObject(),
                    curriculum: curriculumDatas,
                    no_year: Math.max(...years.map((year) => parseInt(year.split('year')[1]))),
                });

                // Student Calculation
                const sgStudents = [];
                const sgProgramData = studentGroupData.status
                    ? studentGroupData.data.filter(
                          (ele) => ele.master._program_id.toString() === element._id.toString(),
                      )
                    : [];
                for (sgElement of sgProgramData) {
                    for (sgLevel of sgElement.groups) {
                        let studentIds = [];
                        for (sgCourse of sgLevel.courses) {
                            if (sgLevel.rotation === 'yes') {
                                const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                                for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                                    for (sgSetting of sgCourse.setting) {
                                        if (
                                            sgSetting &&
                                            sgSetting.session_setting &&
                                            sgSetting.session_setting.length &&
                                            sgSetting.session_setting[0].groups
                                        )
                                            for (sgSession of sgSetting.session_setting[0].groups) {
                                                studentIds = [
                                                    ...studentIds,
                                                    ...sgSession._student_ids,
                                                ];
                                            }
                                        studentIds = studentIds.filter(
                                            (ele, index) =>
                                                studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                    }
                                }
                            } else {
                                for (sgSetting of sgCourse.setting) {
                                    if (
                                        sgSetting &&
                                        sgSetting.session_setting &&
                                        sgSetting.session_setting.length &&
                                        sgSetting.session_setting[0].groups
                                    )
                                        for (sgSession of sgSetting.session_setting[0].groups) {
                                            studentIds = [...studentIds, ...sgSession._student_ids];
                                        }
                                    studentIds = studentIds.filter(
                                        (ele, index) =>
                                            studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                }
                            }
                        }
                        const studentDatas = sgLevel.students.filter((ele) =>
                            studentIds.find(
                                (ele2) => ele2.toString() === ele._student_id.toString(),
                            ),
                        );
                        const loc = sgStudents.findIndex((ele) => ele && ele.term === sgLevel.term);
                        if (loc === -1) {
                            sgStudents.push({
                                term: sgLevel.term,
                                students: studentDatas,
                                male_count: studentDatas.filter((ele) => ele.gender === 'male')
                                    .length,
                                female_count: studentDatas.filter((ele) => ele.gender === 'female')
                                    .length,
                                count: studentDatas.length,
                            });
                        } else {
                            let sgStudentDatas = [
                                ...sgStudents[loc].students,
                                ...sgLevel.students.filter((ele) =>
                                    studentIds.find(
                                        (ele2) => ele2.toString() === ele._student_id.toString(),
                                    ),
                                ),
                            ];
                            sgStudentDatas = sgStudentDatas.filter(
                                (ele, index) =>
                                    sgStudentDatas.findIndex(
                                        (ele2) =>
                                            ele2._student_id.toString() ===
                                            ele._student_id.toString(),
                                    ) === index,
                            );
                            sgStudents[loc].students = sgStudentDatas;
                            sgStudents[loc].male_count = sgStudentDatas.filter(
                                (ele) => ele.gender === 'male',
                            ).length;
                            sgStudents[loc].female_count = sgStudentDatas.filter(
                                (ele) => ele.gender === 'female',
                            ).length;
                            sgStudents[loc].count = sgStudentDatas.length;
                        }
                    }
                }
                sgStudents.forEach((ele) => {
                    delete ele.students;
                });
                studentPrograms.push({
                    program_id: element._id,
                    program_name: element.name,
                    program_code: element.code,
                    student_term: sgStudents,
                });
            }
            // });
        }
        const responses = {
            programs,
            program_learning_outcomes: programPlo,
            credit_hours: creditHoursPrograms,
            students: studentPrograms,
            staffs: staffPrograms,
        };
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('PROGRAM_LIST'), responses));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program Wise Details
exports.programView = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program Calendar to Get Courses
        const programCalendar = await get(
            program_calendar,
            {
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                'level.year': 1,
                'level.term': 1,
                'level.level_no': 1,
                'level.curriculum': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.course._course_id': 1,
                // 'level.course.courses_name': 1,
                // 'level.course.courses_number': 1,
                'level.course.model': 1,
                'level.course.credit_hours': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                // 'level.rotation_course.course.courses_name': 1,
                // 'level.rotation_course.course.courses_number': 1,
                'level.rotation_course.course.model': 1,
                'level.rotation_course.course.credit_hours': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );
        if (!programCalendar.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );

        const courseIds = programCalendar.data.level
            .map((ele) =>
                ele.rotation === 'no'
                    ? ele.course.map((ele2) => ele2._course_id.toString()).flat()
                    : ele.rotation_course[0].course
                          .map((ele2) => ele2._course_id.toString())
                          .flat(),
            )
            .flat();

        // get courses
        const cQuery = {
            isDeleted: false,
            isActive: true,
            _id: { $in: courseIds },
        };
        const cProject = {
            _id: 1,
            course_code: 1,
            course_name: 1,
            duration: 1,
            course_type: 1,
            _program_id: 1,
            administration: 1,
            participating: 1,
            'course_assigned_details._program_id': 1,
            'course_assigned_details.program_name': 1,
        };
        const { data: courseList } = await get_list(course, cQuery, cProject);
        //Student group data
        const studentGroupData = await get_list(
            student_group,
            {
                isDeleted: false,
                'master._program_id': convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            },
            {
                'master.program_name': 1,
                'master.program_no': 1,
                'groups.term': 1,
                'groups.year': 1,
                'groups.level': 1,
                'groups.curriculum': 1,
                'groups.rotation': 1,
                'groups.students': 1,
                'groups.courses._course_id': 1,
                'groups.courses.course_name': 1,
                'groups.courses.course_no': 1,
                'groups.courses.course_type': 1,
                'groups.courses.student_absence_percentage': 1,
                'groups.courses.setting': 1,
            },
        );
        if (!studentGroupData.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: { $in: courseIds },
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _session_id: '$session._session_id',
                'session._session_id': 1,
                'session.session_type': 1,
                status: 1,
                _course_id: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
                students: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];

        const sessionDeliveryTypes = await get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
            },
            {},
        );
        const sessionOrder = await get_list(
            session_order,
            {
                ...commonQuery,
                _institution_id: convertToMongoObjectId(_institution_id),
                _course_id: { $in: courseIds },
            },
            {
                _course_id: 1,
                'session_flow_data._id': 1,
                'session_flow_data.duration': 1,
            },
        );

        // LMS Setting Data
        const lmsData = await lmsStudentSettingData(convertToMongoObjectId(_institution_id));
        const studentCriteriaData = await get_list(studentCriteriaCollection, { programId });
        const lmsClonedData = clone(lmsData);
        const LevelCourses = [];
        for (sgElement of studentGroupData.data) {
            for (sgLevel of sgElement.groups) {
                for (sgCourse of sgLevel.courses) {
                    if (sgLevel.rotation === 'yes') {
                        const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                        for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                            const sgGenderStudents = [];
                            for (sgSetting of sgCourse.setting) {
                                const loc = sgGenderStudents.findIndex(
                                    (ele) => ele && ele.gender === sgSetting.gender,
                                );
                                let studentIds = [];
                                if (
                                    loc === -1 &&
                                    sgSetting &&
                                    sgSetting._group_no.toString() === i.toString() &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups
                                ) {
                                    for (sgSession of sgSetting.session_setting[0].groups) {
                                        studentIds = [...studentIds, ...sgSession._student_ids];
                                    }
                                    // studentIds = [...new Set(studentIds)];
                                    studentIds = studentIds.filter(
                                        (ele, index) =>
                                            studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents.push({
                                        gender: sgSetting.gender,
                                        studentIds,
                                        count: studentIds.length,
                                    });
                                } else if (
                                    loc !== -1 &&
                                    sgSetting &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups &&
                                    sgSetting._group_no.toString() === i.toString()
                                ) {
                                    for (sgSession of sgSetting.session_setting[0].groups) {
                                        studentIds = [...studentIds, ...sgSession._student_ids];
                                    }
                                    // studentIds = [...new Set(studentIds)];
                                    studentIds = studentIds.filter(
                                        (ele, index) =>
                                            studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[loc].studentIds = [
                                        ...studentIds,
                                        ...sgGenderStudents[loc].studentIds,
                                    ];
                                    sgGenderStudents[loc].count =
                                        sgGenderStudents[loc].studentIds.length;
                                }
                            }
                            // To do Need to calculate based on input with DC
                            const sessionData = { no_session: 0, completed_session: 0 };
                            if (sessionOrder.status) {
                                const sessionOrderData = sessionOrder.data.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        sgCourse._course_id.toString(),
                                );
                                if (sessionOrderData) {
                                    sessionData.no_session =
                                        sessionOrderData.session_flow_data.length;
                                    sessionData.completed_session = 0;
                                }
                            }
                            LevelCourses.push({
                                level: sgLevel.level,
                                term: sgLevel.term,
                                curriculum: sgLevel.curriculum,
                                rotation: sgLevel.rotation,
                                rotation_count: i,
                                _course_id: sgCourse._course_id,
                                course_name: sgCourse.course_name,
                                course_no: sgCourse.course_no,
                                course_type: sgCourse.course_type,
                                students: sgGenderStudents,
                                // To do Need to calculate based on input in LMS
                                final_warning: 0,
                                denial: 0,
                                ...sessionData,
                                student_absence_percentage: sgCourse.student_absence_percentage,
                            });
                        }
                    } else {
                        const sgGenderStudents = [];
                        for (sgSetting of sgCourse.setting) {
                            const loc = sgGenderStudents.findIndex(
                                (ele) => ele && ele.gender === sgSetting.gender,
                            );
                            let studentIds = [];
                            if (
                                loc === -1 &&
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                                // studentIds = [...new Set(studentIds)];
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents.push({
                                    gender: sgSetting.gender,
                                    studentIds,
                                    count: studentIds.length,
                                });
                            } else if (
                                loc !== -1 &&
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                                // studentIds = [...new Set(studentIds)];
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents[loc].studentIds = [
                                    ...studentIds,
                                    ...sgGenderStudents[loc].studentIds,
                                ];
                                sgGenderStudents[loc].count =
                                    sgGenderStudents[loc].studentIds.length;
                            }
                        }
                        // To do Need to calculate based on input with DC
                        const sessionData = { no_session: 0, completed_session: 0 };
                        if (sessionOrder.status) {
                            const sessionOrderData = sessionOrder.data.find(
                                (ele) =>
                                    ele._course_id.toString() === sgCourse._course_id.toString(),
                            );
                            if (sessionOrderData) {
                                sessionData.no_session = sessionOrderData.session_flow_data.length;
                                sessionData.completed_session = 0;
                            }
                        }
                        LevelCourses.push({
                            level: sgLevel.level,
                            term: sgLevel.term,
                            curriculum: sgLevel.curriculum,
                            rotation: sgLevel.rotation,
                            _course_id: sgCourse._course_id,
                            course_name: sgCourse.course_name,
                            course_no: sgCourse.course_no,
                            course_type: sgCourse.course_type,
                            students: sgGenderStudents,
                            // To do Need to calculate based on input in LMS
                            final_warning: 0,
                            denial: 0,
                            ...sessionData,
                            student_absence_percentage: sgCourse.student_absence_percentage,
                        });
                    }
                }
            }
        }

        // Program Calendar Data Adding
        const pcData = clone(programCalendar.data.level);
        const programStudents = [];
        for (pcLevel of pcData) {
            const students = [];
            let denial_count = 0;
            let final_warning_count = 0;
            let lmsTemp;
            // Rotation Flow
            if (pcLevel.rotation === 'yes') {
                const rotationCourses = [];
                for (rotationCourse of pcLevel.rotation_course) {
                    const courseLists = [];
                    for (pcCourse of rotationCourse.course) {
                        const courseData = LevelCourses.find(
                            (ele) =>
                                ele.level === pcLevel.level_no &&
                                ele.term === pcLevel.term &&
                                ele.rotation_count === rotationCourse.rotation_count &&
                                ele._course_id.toString() === pcCourse._course_id.toString(),
                        );
                        if (courseData) {
                            for (stdElement of courseData.students) {
                                const loc = students.findIndex(
                                    (ele) =>
                                        ele &&
                                        ele.gender === stdElement.gender &&
                                        ele.term === pcLevel.term,
                                );
                                if (loc === -1) {
                                    students.push({
                                        term: pcLevel.term,
                                        gender: stdElement.gender,
                                        studentIds: stdElement.studentIds,
                                        count: stdElement.count,
                                    });
                                } else {
                                    let std = [
                                        ...students[loc].studentIds,
                                        ...stdElement.studentIds,
                                    ];
                                    std = clone(
                                        std.filter(
                                            (ele4, index) =>
                                                std.findIndex(
                                                    (ele3) => ele3.toString() === ele4.toString(),
                                                ) === index,
                                        ),
                                    );
                                    students[loc].studentIds = std;
                                    students[loc].count = std.length;
                                }
                            }
                            const sessionOrderData = sessionOrder.data.find(
                                (ele) =>
                                    ele._course_id.toString() === pcCourse._course_id.toString(),
                            )
                                ? sessionOrder.data.find(
                                      (ele) =>
                                          ele._course_id.toString() ===
                                          pcCourse._course_id.toString(),
                                  ).session_flow_data
                                : [];
                            const courseScheduled = clone(
                                scheduleData.data.filter(
                                    (ele) =>
                                        ele.rotation &&
                                        ele.rotation === 'yes' &&
                                        ele.rotation_count === rotationCourse.rotation_count &&
                                        ele._course_id.toString() ===
                                            pcCourse._course_id.toString() &&
                                        ele.term === pcLevel.term &&
                                        ele.level_no === pcLevel.level_no,
                                ),
                            );

                            //Warning Calculations
                            lmsTemp = lmsData;
                            const courseStudentIds = courseData.students
                                .map((ele) => ele.studentIds)
                                .flat();
                            if (
                                lmsTemp.warningAbsenceData[0] &&
                                courseData.student_absence_percentage &&
                                courseData.student_absence_percentage !== 0
                            ) {
                                lmsTemp.warningAbsenceData[0].absence_percentage =
                                    courseData.student_absence_percentage;
                            } else {
                                lmsTemp = lmsClonedData;
                            }
                            const studentReport = await studentAttendanceReport(
                                courseStudentIds,
                                courseScheduled,
                                lmsTemp.warningAbsenceData,
                                studentCriteriaData,
                            );
                            courseData.denial = studentReport.filter(
                                (ele) => ele.warning === lmsTemp.denialWarning,
                            ).length;
                            courseData.final_warning = lmsTemp.finaleWarning
                                ? studentReport.filter(
                                      (ele) => ele.warning === lmsTemp.finaleWarning,
                                  ).length
                                : 0;
                            // Credit Hour Calculation
                            const credit_hours = await courseCreditContactHours(
                                sessionDeliveryTypes.data,
                                programCalendar.data._program_id,
                                courseScheduled,
                                sessionOrderData,
                                pcCourse.credit_hours,
                            );
                            pcCourse.credit_hours = credit_hours;
                            pcCourse = { ...courseData, ...pcCourse };
                            pcCourse.students.forEach((courseElement) => {
                                delete courseElement.studentIds;
                            });
                            final_warning_count += courseData.final_warning;
                            denial_count += courseData.denial;

                            // pcData[levelIndex].rotation_course[rotationCourseIndex].course[
                            //     courseIndex
                            // ].program_name = departmentSubject.data.find(
                            //     (ele) =>
                            //         ele.program_id.toString() === pcLevel._program_id.toString(),
                            // ).program_name;
                            pcCourse._program_id = programCalendar.data._program_id[0];
                            // Shared Details Adding
                            const sCourse = clone(
                                courseList.find(
                                    (uCourse) =>
                                        uCourse._id.toString() === courseData._course_id.toString(),
                                ),
                            );
                            pcCourse.course_shared =
                                sCourse._program_id.toString() !== programId.toString();

                            pcCourse.course_shared_program = pcCourse.course_shared
                                ? sCourse.course_assigned_details.find(
                                      (cad) =>
                                          cad._program_id.toString() ===
                                          sCourse._program_id.toString(),
                                  ).program_name
                                : studentGroupData.data[0].master.program_name;
                            pcCourse.completed_session = credit_hours
                                .map((ele) => ele.completed_sessions)
                                .reduce((accumulator, current) => accumulator + current);
                            // pcCourse.completed_session = courseScheduled.filter(
                            //     (ele) => ele && ele.status && ele.status === COMPLETED,
                            // ).length;
                            courseLists.push(pcCourse);
                        }
                    }
                    rotationCourse.course = courseLists;
                    rotationCourses.push(rotationCourse);
                }
                pcLevel.rotation_course = rotationCourses;
            } else {
                const courseLists = [];
                for (pcCourse of pcLevel.course) {
                    const courseData = LevelCourses.find(
                        (ele) =>
                            ele.level === pcLevel.level_no &&
                            ele.term === pcLevel.term &&
                            ele._course_id.toString() === pcCourse._course_id.toString(),
                    );
                    if (courseData) {
                        for (stdElement of courseData.students) {
                            const loc = students.findIndex(
                                (ele) =>
                                    ele &&
                                    ele.gender === stdElement.gender &&
                                    ele.term === pcLevel.term,
                            );
                            if (loc === -1) {
                                students.push({
                                    term: pcLevel.term,
                                    gender: stdElement.gender,
                                    studentIds: stdElement.studentIds,
                                    count: stdElement.count,
                                });
                            } else {
                                let std = [...students[loc].studentIds, ...stdElement.studentIds];
                                std = clone(
                                    std.filter(
                                        (ele4, index) =>
                                            std.findIndex(
                                                (ele3) => ele3.toString() === ele4.toString(),
                                            ) === index,
                                    ),
                                );
                                students[loc].studentIds = std;
                                students[loc].count = std.length;
                            }
                        }
                        // Credit Hour Calculation
                        const sessionOrderData = sessionOrder.data.find(
                            (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                        )
                            ? sessionOrder.data.find(
                                  (ele) =>
                                      ele._course_id.toString() === pcCourse._course_id.toString(),
                              ).session_flow_data
                            : [];
                        const courseScheduled = clone(
                            scheduleData.data.filter(
                                (ele) =>
                                    ele._course_id.toString() === pcCourse._course_id.toString() &&
                                    ele.term === pcLevel.term &&
                                    ele.level_no === pcLevel.level_no,
                            ),
                        );

                        //Warning Calculations
                        lmsTemp = lmsData;
                        const courseStudentIds = courseData.students
                            .map((ele) => ele.studentIds)
                            .flat();
                        if (
                            lmsTemp.warningAbsenceData[0] &&
                            courseData.student_absence_percentage &&
                            courseData.student_absence_percentage !== 0
                        ) {
                            lmsTemp.warningAbsenceData[0].absence_percentage =
                                courseData.student_absence_percentage;
                        } else {
                            lmsTemp = lmsClonedData;
                        }
                        const studentReport = await studentAttendanceReport(
                            courseStudentIds,
                            courseScheduled,
                            lmsTemp.warningAbsenceData,
                            studentCriteriaData,
                        );
                        courseData.denial = studentReport.filter(
                            (ele) => ele.warning === lmsTemp.denialWarning,
                        ).length;
                        courseData.final_warning = lmsTemp.finaleWarning
                            ? studentReport.filter((ele) => ele.warning === lmsTemp.finaleWarning)
                                  .length
                            : 0;

                        const credit_hours = await courseCreditContactHours(
                            sessionDeliveryTypes.data,
                            programCalendar.data._program_id,
                            courseScheduled,
                            sessionOrderData,
                            pcCourse.credit_hours,
                        );

                        pcCourse.credit_hours = credit_hours;
                        pcCourse = { ...courseData, ...pcCourse };
                        pcCourse.students.forEach((courseElement) => {
                            delete courseElement.studentIds;
                        });
                        final_warning_count += courseData.final_warning;
                        denial_count += courseData.denial;
                        pcCourse._program_id = programCalendar.data._program_id[0];
                        // Shared Details Adding
                        const sCourse = clone(
                            courseList.find(
                                (uCourse) =>
                                    uCourse._id.toString() === courseData._course_id.toString(),
                            ),
                        );
                        pcCourse.course_shared = sCourse
                            ? sCourse._program_id.toString() !== programId.toString()
                            : false;
                        pcCourse.course_shared_program = sCourse
                            ? pcCourse.course_shared
                                ? sCourse.course_assigned_details.find(
                                      (cad) =>
                                          cad._program_id.toString() ===
                                          sCourse._program_id.toString(),
                                  ).program_name
                                : studentGroupData.data[0].master.program_name
                            : '';
                        pcCourse.completed_session = credit_hours
                            .map((ele) => ele.completed_sessions)
                            .reduce((accumulator, current) => accumulator + current);
                        // pcCourse.completed_session = courseScheduled.filter(
                        //     (ele) => ele && ele.status && ele.status === COMPLETED,
                        // ).length;
                        courseLists.push(pcCourse);
                    }
                    // });
                }
                pcLevel.course = courseLists;
            }
            // students.forEach((ele) => {
            for (ele of students) {
                const proLoc = programStudents.findIndex(
                    (ele2) => ele2.term === pcLevel.term && ele2.gender === ele.gender,
                );
                if (proLoc === -1) {
                    programStudents.push({
                        term: pcLevel.term,
                        gender: ele.gender,
                        studentIds: ele.studentIds,
                        count: ele.count,
                    });
                } else {
                    let std = [...programStudents[proLoc].studentIds, ...ele.studentIds];
                    std = clone(
                        std.filter(
                            (ele4, index) =>
                                std.findIndex((ele3) => ele3.toString() === ele4.toString()) ===
                                index,
                        ),
                    );
                    programStudents[proLoc].studentIds = std;
                    programStudents[proLoc].count = std.length;
                }
                // Removing Due to Data Load
                delete ele.studentIds;
            }
            pcLevel.students = students;
            pcLevel.final_warning = final_warning_count;
            pcLevel.denial = denial_count;
        }
        let activeStd = 0;
        let programStudentIds = [];
        programStudents.forEach((ele) => {
            activeStd += ele.count;
            programStudentIds = [...programStudentIds, ...ele.studentIds];
            delete ele.studentIds;
        });
        const response = {
            // To do Need to Active & inActive Student Details from User DB
            program_name: studentGroupData.data[0].master.program_name || '',
            program_no: studentGroupData.data[0].master.program_no || '',
            active_student: activeStd,
            inactive: 0,
            students: programStudents,
            level: pcData,
        };
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('COURSE_LIST'), response));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program Wise Details
exports.programStudentList = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        console.time('v1ProgramLevelStudents');
        const v1ProgramLevelStudents = await redisClient.Client.get(
            `${V1_PROGRAM_LEVEL_STUDENTS}-${institutionCalendarId}`,
        );
        console.timeEnd('v1ProgramLevelStudents');
        if (v1ProgramLevelStudents) {
            // response.programLevelStudents = JSON.parse(v1ProgramLevelStudents);
            return res.status(200).send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    'Student List',
                    JSON.parse(v1ProgramLevelStudents).find(
                        (levelElement) =>
                            levelElement.program_id.toString() === programId.toString(),
                    ),
                ),
            );
        }
        //Leave Setting Data
        const lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
        });
        const studentCriteriaData = await lmsDenialSchema
            .find({
                programId,
                _institution_calendar_id: institutionCalendarId,
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 });
        const lmsClonedData = clone(lmsData);
        let studentGroupData = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        studentGroupData = studentGroupData.filter(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() === institutionCalendarId.toString(),
        );
        const courseIds = studentGroupData
            .map((ele1) =>
                ele1.groups
                    .map((ele) => ele.courses.map((ele2) => ele2._course_id.toString()).flat())
                    .flat(),
            )
            .flat();
        console.time('courseSchedule');
        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: { $in: courseIds },
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _session_id: '$session._session_id',
                'session._session_id': 1,
                'session.session_type': 1,
                status: 1,
                _course_id: 1,
                _institution_calendar_id: 1,
                _program_id: 1,
                year_no: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
                students: 1,
                scheduleStartDateAndTime: 1,
            },
        );
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId: courseIds,
        });
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        console.timeEnd('courseSchedule');
        let studentList = [];
        for (sgYear of studentGroupData) {
            for (sgLevel of sgYear.groups) {
                studentList = [...studentList, ...sgLevel.students];
            }
        }
        const studentDatas = [];
        let termList = [];
        const levelStudents = [];
        const programCourseBasedStudentCount = {
            male: 0,
            female: 0,
        };
        for (sgElement of studentGroupData) {
            for (levelElement of sgElement.groups) {
                const courseBasedStudentCount = {
                    male: 0,
                    female: 0,
                };
                const students = [];
                termList.push(levelElement.term);
                let lmsTemp;
                for (courseElement of levelElement.courses) {
                    let studentIds = [];
                    for (settingElement of courseElement.setting) {
                        for (sessionSettingElement of settingElement.session_setting) {
                            for (deliveryElement of sessionSettingElement.groups) {
                                studentIds = [...studentIds, ...deliveryElement._student_ids];
                            }
                        }
                    }
                    // studentIds = [...new Set(studentIds)];
                    studentIds = studentIds.filter(
                        (ele, index) =>
                            studentIds.findIndex((ele2) => ele2.toString() === ele.toString()) ===
                            index,
                    );
                    const courseScheduled = clone(
                        scheduleData.data.filter(
                            (ele) =>
                                ele._course_id.toString() === courseElement._course_id.toString() &&
                                ele.term === levelElement.term &&
                                ele.level_no === levelElement.level,
                        ),
                    );
                    //Warning Calculations
                    lmsTemp = lmsData;
                    if (
                        lmsTemp.warningAbsenceData[0] &&
                        courseElement.student_absence_percentage &&
                        courseElement.student_absence_percentage !== 0
                    ) {
                        lmsTemp.warningAbsenceData[0].absence_percentage =
                            courseElement.student_absence_percentage;
                    } else {
                        lmsTemp = lmsClonedData;
                    }
                    const studentReport = await studentAttendanceReportV2(
                        studentIds,
                        courseScheduled,
                        lmsTemp.warningAbsenceData,
                        studentCriteriaData,
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        lateExcludeManagement,
                    );
                    const denialStudentList = studentReport.filter(
                        (ele) => ele.warning === lmsTemp.denialWarning,
                    );
                    for (stdElement of studentIds) {
                        const loc = studentDatas.findIndex(
                            (ele) =>
                                ele._student_id.toString() === stdElement.toString() &&
                                ele.term === levelElement.term,
                        );
                        const stdLoc = studentList.find(
                            (stdEle) => stdEle._student_id.toString() === stdElement.toString(),
                        );
                        const studentCourseStatus = denialStudentList
                            ? denialStudentList.find(
                                  (ele) => ele.student_id.toString() === stdElement.toString(),
                              )
                            : undefined;
                        if (loc === -1 && stdLoc) {
                            studentDatas.push({
                                _student_id: stdLoc._student_id,
                                name: stdLoc.name,
                                academic_no: stdLoc.academic_no,
                                gender: stdLoc.gender,
                                term: levelElement.term,
                                courses: 1,
                                denial: studentCourseStatus ? 1 : 0,
                                years: [sgElement.master.year],
                                levels: [levelElement.level],
                            });
                        } else if (stdLoc) {
                            studentDatas[loc].courses += 1;
                            studentDatas[loc].denial +=
                                studentCourseStatus && studentCourseStatus.warning ? 1 : 0;
                            const levelData = studentDatas[loc].levels;
                            levelData.push(levelElement.level);
                            studentDatas[loc].levels = [...new Set(levelData)];
                            const yearData = studentDatas[loc].years;
                            yearData.push(sgElement.master.year);
                            studentDatas[loc].years = [...new Set(yearData)];
                        }
                        if (stdLoc) {
                            if (stdLoc.gender === GENDER.MALE) {
                                courseBasedStudentCount.male++;
                                programCourseBasedStudentCount.male++;
                            }
                            if (stdLoc.gender === GENDER.FEMALE) {
                                courseBasedStudentCount.female++;
                                programCourseBasedStudentCount.female++;
                            }
                            const locs = students.findIndex(
                                (ele) =>
                                    ele &&
                                    ele.gender === stdLoc.gender &&
                                    ele.term === levelElement.term,
                            );
                            if (locs === -1) {
                                students.push({
                                    term: levelElement.term,
                                    gender: stdLoc.gender,
                                    studentIds: [stdElement.toString()],
                                    count: 1,
                                });
                            } else {
                                let std = students[locs].studentIds;
                                std.push(stdElement.toString());
                                std = [...new Set(std)];
                                students[locs].studentIds = std;
                                students[locs].count = std.length;
                            }
                        }
                    }
                }
                students.forEach((ele) => {
                    delete ele.studentIds;
                    ele.courseStudents =
                        ele.gender === GENDER.MALE
                            ? courseBasedStudentCount.male
                            : courseBasedStudentCount.female;
                });
                levelStudents.push({
                    year: sgElement.master.year,
                    level_no: levelElement.level,
                    term: levelElement.term,
                    students,
                });
            }
        }
        termList = [...new Set(termList)];
        const termStudents = [];
        for (termElement of termList) {
            const termData = studentDatas.filter((ele) => ele.term === termElement);
            termStudents.push({
                term: termElement,
                total: termData.length,
                male: termData.filter((ele) => ele.gender === 'male').length,
                female: termData.filter((ele) => ele.gender === 'female').length,
                maleCourseStudents: programCourseBasedStudentCount.male,
                femaleCourseStudents: programCourseBasedStudentCount.female,
                active: termData.length,
                inactive: 0,
                students: termData,
            });
        }
        const response = {
            program_name:
                studentGroupData[0] && studentGroupData[0].master.program_name
                    ? studentGroupData[0].master.program_name || ''
                    : '',
            program_no:
                studentGroupData[0] && studentGroupData[0].master.program_no
                    ? studentGroupData[0].master.program_no || ''
                    : '',
            term: termList,
            students: termStudents,
            level: levelStudents,
        };
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('STUDENT_LIST'), response));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program Wise Details
exports.programStudentDetails = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, studentId },
        } = req;
        // Student Details
        const populate = { path: 'program._program_id', select: { name: 1 } };
        const { status: studentStatus, data: studentData } = await get_list_populate(
            user,
            { _id: convertToMongoObjectId(studentId) },
            {
                name: 1,
                user_id: 1,
                email: 1,
                batch: 1,
                gender: 1,
                mobile: 1,
                'address.nationality_id': 1,
                'program.program_no': 1,
                'program._program_id': 1,
                createdAt: 1,
            },
            populate,
        );

        if (!studentStatus)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_NOT_FOUND'),
                        req.t('STUDENT_NOT_FOUND'),
                    ),
                );
        //TODO:need to reverify the _institution_calendar_id filter
        const pcData =
            (await allProgramCalendarDatas()).find(
                (programElement) =>
                    programElement._program_id.toString() === programId.toString() &&
                    programElement._institution_calendar_id.toString() ===
                        institutionCalendarId.toString(),
            ).level || [];
        let studentGroupData = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        studentGroupData = studentGroupData.filter(
            (sgElement) =>
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement.groups.find((groupElement) =>
                    groupElement.students.find(
                        (studentElement) =>
                            studentElement._student_id.toString() === studentId.toString(),
                    ),
                ),
        );
        const courseIds = pcData
            .map((ele) =>
                ele.rotation === 'no'
                    ? ele.course.map((ele2) => ele2._course_id.toString()).flat()
                    : ele.rotation_course[0].course
                          .map((ele2) => ele2._course_id.toString())
                          .flat(),
            )
            .flat();
        const courseList = await allCourseList();

        // LMS Setting Data
        const lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
        });
        const sessionDeliveryTypes = (await allSessionDeliveryTypesDatas()).filter(
            (sessionDeliveryElement) =>
                sessionDeliveryElement._program_id.toString() === programId.toString(),
        );
        const sessionOrder = (await allSessionOrderDatas()).filter(
            (sessionOrderElement) =>
                sessionOrderElement._program_id.toString() === programId.toString(),
        );

        const LevelCourses = [];
        for (sgElement of studentGroupData) {
            for (sgLevel of sgElement.groups) {
                for (sgCourse of sgLevel.courses) {
                    if (sgLevel.rotation === 'yes') {
                        // const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                        // for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                        for (sgSetting of sgCourse.setting) {
                            let studentIds = [];
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            )
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                            // studentIds = [...new Set(studentIds)];
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            if (
                                studentIds.findIndex(
                                    (ele) => ele.toString() === studentId.toString(),
                                ) !== -1
                            ) {
                                // To do Need to calculate based on input with DC
                                const sessionData = { no_session: 0, completed_session: 0 };
                                if (sessionOrder) {
                                    const sessionOrderData = sessionOrder.find(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                            sgCourse._course_id.toString(),
                                    );
                                    if (sessionOrderData) {
                                        sessionData.no_session =
                                            sessionOrderData.session_flow_data.length;
                                        sessionData.completed_session = 0;
                                    }
                                }
                                const courseVersionDetails = courseList.find(
                                    (courseListElement) =>
                                        courseListElement._id.toString() ===
                                        sgCourse._course_id.toString(),
                                );
                                LevelCourses.push({
                                    level: sgLevel.level,
                                    term: sgLevel.term,
                                    curriculum: sgLevel.curriculum,
                                    rotation: sgLevel.rotation,
                                    rotation_count: sgSetting._group_no,
                                    _course_id: sgCourse._course_id,
                                    course_name: sgCourse.course_name,
                                    versionNo: courseVersionDetails.versionNo
                                        ? courseVersionDetails.versionNo
                                        : 1,
                                    versioned: courseVersionDetails.versioned
                                        ? courseVersionDetails.versioned
                                        : false,
                                    versionName: courseVersionDetails.versionName
                                        ? courseVersionDetails.versionName
                                        : '',
                                    versionedFrom: courseVersionDetails.versionedFrom
                                        ? courseVersionDetails.versionedFrom
                                        : null,
                                    versionedCourseIds: courseVersionDetails.versionedCourseIds
                                        ? courseVersionDetails.versionedCourseIds
                                        : [],
                                    course_no: sgCourse.course_no,
                                    course_type: sgCourse.course_type,
                                    // To do Need to calculate based on input in LMS
                                    final_warning: 0,
                                    denial: 0,
                                    ...sessionData,
                                    student_absence_percentage: sgCourse.student_absence_percentage,
                                });
                            }
                        }
                        // }
                    } else {
                        let studentIds = [];
                        // const deliveryGroupIds = [];
                        for (sgSetting of sgCourse.setting) {
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            )
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                            // for (sessionDeliveryElement of sgSetting.session_setting) {
                            //     for (sessionDeliveryGroupElement of sessionDeliveryElement.groups) {
                            //         if (
                            //             sessionDeliveryGroupElement._student_ids.find(
                            //                 (ele) => ele.toString() === studentId.toString(),
                            //             )
                            //         )
                            //             deliveryGroupIds.push(
                            //                 sessionDeliveryGroupElement._id.toString(),
                            //             );
                            //     }
                            // }
                        }
                        if (
                            studentIds.findIndex(
                                (ele) => ele.toString() === studentId.toString(),
                            ) !== -1
                        ) {
                            // To do Need to calculate based on input with DC
                            const sessionData = { no_session: 0, completed_session: 0 };
                            if (sessionOrder) {
                                const sessionOrderData = sessionOrder.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        sgCourse._course_id.toString(),
                                );
                                if (sessionOrderData) {
                                    sessionData.no_session =
                                        sessionOrderData.session_flow_data.length;
                                    sessionData.completed_session = 0;
                                }
                            }
                            const courseVersionDetails = courseList.find(
                                (courseListElement) =>
                                    courseListElement._id.toString() ===
                                    sgCourse._course_id.toString(),
                            );
                            LevelCourses.push({
                                level: sgLevel.level,
                                term: sgLevel.term,
                                curriculum: sgLevel.curriculum,
                                rotation: sgLevel.rotation,
                                _course_id: sgCourse._course_id,
                                course_name: sgCourse.course_name,
                                versionNo: courseVersionDetails.versionNo
                                    ? courseVersionDetails.versionNo
                                    : 1,
                                versioned: courseVersionDetails.versioned
                                    ? courseVersionDetails.versioned
                                    : false,
                                versionName: courseVersionDetails.versionName
                                    ? courseVersionDetails.versionName
                                    : '',
                                versionedFrom: courseVersionDetails.versionedFrom
                                    ? courseVersionDetails.versionedFrom
                                    : null,
                                versionedCourseIds: courseVersionDetails.versionedCourseIds
                                    ? courseVersionDetails.versionedCourseIds
                                    : [],
                                course_no: sgCourse.course_no,
                                course_type: sgCourse.course_type,
                                // deliveryGroupIds,
                                // To do Need to calculate based on input in LMS
                                final_warning: 0,
                                denial: 0,
                                ...sessionData,
                                student_absence_percentage: sgCourse.student_absence_percentage,
                            });
                        }
                    }
                }
            }
        }
        console.time('courseSchedule');
        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: { $in: courseIds },
                'students._id': convertToMongoObjectId(studentId),
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _session_id: '$session._session_id',
                'session._session_id': 1,
                'session.session_type': 1,
                status: 1,
                _course_id: 1,
                level_no: 1,
                term: 1,
                _institution_calendar_id: 1,
                _program_id: 1,
                year_no: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
                students: 1,
                scheduleStartDateAndTime: 1,
                'sessionDetail.start_time': 1,
            },
        );
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId: courseIds,
        });
        scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
        console.timeEnd('courseSchedule');
        // const studentSchedule = [];
        // for (scheduleElement of scheduleData.data) {
        //     const studentData = scheduleElement.students.find(
        //         (ele) =>
        //             ele && ele._id.toString() === studentId.toString() && ele.status === PRESENT,
        //     );
        //     if (studentData) {
        //         scheduleElement.students = studentData;
        //         studentSchedule.push(scheduleElement);
        //     }
        // }
        // scheduleData.data = clone(studentSchedule);

        // Program Calendar Data Adding
        const yearLevel = [];

        const studentCriteriaData = await lmsDenialSchema
            .find({
                programId,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 });
        const lmsClonedData = clone(lmsData);
        for (pcLevel of pcData) {
            // Rotation Flow
            let lmsTemp;
            if (pcLevel.rotation === 'yes') {
                const rotationData = [];
                for (rotationCourse of pcLevel.rotation_course) {
                    const coursesData = [];
                    for (pcCourse of rotationCourse.course) {
                        const courseData = LevelCourses.find(
                            (ele) =>
                                ele.level === pcLevel.level_no &&
                                ele.term === pcLevel.term &&
                                ele.rotation_count === rotationCourse.rotation_count &&
                                ele._course_id.toString() === pcCourse._course_id.toString(),
                        );
                        if (courseData) {
                            // Credit Hour Calculation
                            const sessionOrderData = sessionOrder.find(
                                (ele) =>
                                    ele._course_id.toString() === pcCourse._course_id.toString(),
                            )
                                ? sessionOrder.find(
                                      (ele) =>
                                          ele._course_id.toString() ===
                                          pcCourse._course_id.toString(),
                                  ).session_flow_data
                                : [];
                            const courseScheduled = clone(
                                scheduleData.data.filter(
                                    (ele) =>
                                        ele.rotation &&
                                        ele.rotation === 'yes' &&
                                        ele.rotation_count === rotationCourse.rotation_count &&
                                        ele._course_id.toString() ===
                                            pcCourse._course_id.toString() &&
                                        ele.term === pcLevel.term &&
                                        ele.level_no === pcLevel.level_no,
                                ),
                            );
                            //Warning Calculations
                            lmsTemp = lmsData;
                            const courseStudentIds = [studentId];
                            if (
                                lmsTemp.warningAbsenceData[0] &&
                                courseData.student_absence_percentage &&
                                courseData.student_absence_percentage !== 0
                            ) {
                                lmsTemp.warningAbsenceData[0].absence_percentage =
                                    courseData.student_absence_percentage;
                            } else {
                                lmsTemp = lmsClonedData;
                            }
                            const studentReport = await studentAttendanceReportV2(
                                courseStudentIds,
                                courseScheduled,
                                lmsTemp.warningAbsenceData,
                                studentCriteriaData,
                                lateDurationRange,
                                manualLateRange,
                                manualLateData,
                                lateExcludeManagement,
                            );
                            courseData.warning =
                                studentReport[0] &&
                                lmsData.warningAbsenceData.find(
                                    (warningElement) =>
                                        warningElement.labelName.toLowerCase() ===
                                        studentReport[0].warning.toLowerCase(),
                                );
                            courseData.denial = studentReport.filter(
                                (ele) => ele.warning === lmsTemp.denialWarning,
                            ).length;
                            courseData.final_warning = lmsTemp.finaleWarning
                                ? studentReport.filter(
                                      (ele) => ele.warning === lmsTemp.finaleWarning,
                                  ).length
                                : 0;
                            const courseCredit = courseList.find(
                                (courseElement) =>
                                    courseElement._id.toString() === pcCourse._course_id.toString(),
                            );
                            const credit_hours = await courseCreditContactHours(
                                sessionDeliveryTypes,
                                programId,
                                courseScheduled,
                                sessionOrderData,
                                courseCredit ? courseCredit.credit_hours : pcCourse.credit_hours,
                                studentId,
                            );
                            pcCourse._program_id = programId;
                            pcCourse.credit_hours = credit_hours;
                            pcCourse.completed_session = credit_hours
                                .map((ele) => ele.completed_sessions)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.present_count = credit_hours
                                .map((ele) => ele.present_count)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.leave_count = credit_hours
                                .map((ele) => ele.leave_count)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.onduty_count = credit_hours
                                .map((ele) => ele.onduty_count)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.permission_count = credit_hours
                                .map((ele) => ele.permission_count)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.absent_count = credit_hours
                                .map((ele) => ele.absent_count)
                                .reduce((accumulator, current) => accumulator + current);
                            pcCourse.studentLateAbsent = studentReport
                                ? studentReport.length
                                    ? studentReport[0].studentLateAbsent
                                    : 0
                                : 0;
                            // pcCourse.completed_session = courseScheduled.filter(
                            //     (ele) => ele && ele.status && ele.status === COMPLETED,
                            // ).length;
                            delete pcCourse.versionNo;
                            delete pcCourse.versioned;
                            delete pcCourse.versionName;
                            delete pcCourse.versionedFrom;
                            pcCourse = { ...courseData, ...pcCourse };
                            coursesData.push(pcCourse);
                        }
                    }
                    rotationCourse.course = coursesData;
                    if (rotationCourse.course.length) rotationData.push(rotationCourse);
                }
                pcLevel.rotation_course = rotationData;
                if (pcLevel.rotation_course.length) yearLevel.push(pcLevel);
            } else {
                const coursesData = [];
                for (pcCourse of pcLevel.course) {
                    const courseData = LevelCourses.find(
                        (ele) =>
                            ele.level === pcLevel.level_no &&
                            ele.term === pcLevel.term &&
                            ele._course_id.toString() === pcCourse._course_id.toString(),
                    );
                    if (courseData) {
                        // Credit Hour Calculation
                        const sessionOrderData = sessionOrder.find(
                            (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                        )
                            ? sessionOrder.find(
                                  (ele) =>
                                      ele._course_id.toString() === pcCourse._course_id.toString(),
                              ).session_flow_data
                            : [];
                        const courseScheduled = clone(
                            scheduleData.data.filter(
                                (ele) =>
                                    ele._course_id.toString() === pcCourse._course_id.toString() &&
                                    ele.term === pcLevel.term &&
                                    ele.level_no === pcLevel.level_no,
                            ),
                        );
                        //Warning Calculations
                        lmsTemp = lmsData;
                        const courseStudentIds = [studentId];
                        if (
                            lmsTemp.warningAbsenceData[0] &&
                            courseData.student_absence_percentage &&
                            courseData.student_absence_percentage !== 0
                        ) {
                            lmsTemp.warningAbsenceData[0].absence_percentage =
                                courseData.student_absence_percentage;
                        } else {
                            lmsTemp = lmsClonedData;
                        }
                        const studentReport = await studentAttendanceReportV2(
                            courseStudentIds,
                            courseScheduled,
                            lmsTemp.warningAbsenceData,
                            studentCriteriaData,
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            lateExcludeManagement,
                        );
                        courseData.warning =
                            studentReport[0] &&
                            lmsData.warningAbsenceData.find(
                                (warningElement) =>
                                    warningElement.labelName.toLowerCase() ===
                                    studentReport[0].warning.toLowerCase(),
                            );
                        courseData.denial = studentReport.filter(
                            (ele) => ele.warning === lmsTemp.denialWarning,
                        ).length;
                        courseData.final_warning = lmsTemp.finaleWarning
                            ? studentReport.filter((ele) => ele.warning === lmsTemp.finaleWarning)
                                  .length
                            : 0;
                        const courseCredit = courseList.find(
                            (courseElement) =>
                                courseElement._id.toString() === pcCourse._course_id.toString(),
                        );
                        const credit_hours = await courseCreditContactHours(
                            sessionDeliveryTypes,
                            programId,
                            courseScheduled,
                            sessionOrderData,
                            courseCredit ? courseCredit.credit_hours : pcCourse.credit_hours,
                            studentId,
                        );
                        pcCourse._program_id = programId;
                        pcCourse.credit_hours = credit_hours;
                        pcCourse.completed_session = credit_hours
                            .map((ele) => ele.completed_sessions)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.present_count = credit_hours
                            .map((ele) => ele.present_count)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.leave_count = credit_hours
                            .map((ele) => ele.leave_count)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.onduty_count = credit_hours
                            .map((ele) => ele.onduty_count)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.permission_count = credit_hours
                            .map((ele) => ele.permission_count)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.absent_count = credit_hours
                            .map((ele) => ele.absent_count)
                            .reduce((accumulator, current) => accumulator + current);
                        pcCourse.studentLateAbsent = studentReport
                            ? studentReport.length
                                ? studentReport[0].studentLateAbsent
                                : 0
                            : 0;
                        // pcCourse.completed_session = courseScheduled.filter(
                        //     (ele) => ele && ele.status && ele.status === COMPLETED,
                        // ).length;
                        delete pcCourse.versionNo;
                        delete pcCourse.versioned;
                        delete pcCourse.versionName;
                        delete pcCourse.versionedFrom;
                        pcCourse = { ...courseData, ...pcCourse };
                        coursesData.push(pcCourse);
                    }
                }
                pcLevel.course = coursesData;
                if (pcLevel.course.length) yearLevel.push(pcLevel);
            }
        }

        const response = {
            // To do Need to Active & inActive Student Details from User DB
            student_details: studentData[0],
            program_name: studentGroupData[0].master.program_name || '',
            program_no: studentGroupData[0].master.program_no || '',
            level: yearLevel,
        };
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('COURSE_LIST'), response));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

const courseCreditHours = async (programId, session_delivery, courseCredit_hours) => {
    const credit_hours = [];
    for (creditElement of courseCredit_hours) {
        const credits = session_delivery.find(
            (ele) =>
                ele._program_id.toString() === programId.toString() &&
                ele.session_name === creditElement.type_name &&
                ele.type_symbol === creditElement.session_symbol,
        );
        if (credits) {
            credit_hours.push({
                type_name: creditElement.type_name,
                type_symbol: creditElement.type_symbol,
                credit_hours: creditElement.credit_hours,
                contact_hours:
                    parseInt(creditElement.credit_hours) *
                    parseInt(credits.contact_hour_per_credit_hour),
                // To do Need to calculate based on input with DC
                completed_credit_hours: 0,
                completed_contact_hours: 0,
            });
        }
    }
    return credit_hours;
};

exports.department_subject_courses = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, departmentId, subjectId },
        } = req;
        const departmentSubject = await allDepartmentSubjectList();

        //Staff Gets
        console.time('staffData');
        const staffData = await get_list(
            user,
            {
                status: 'completed',
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
                'academic_allocation._program_id': convertToMongoObjectId(programId),
                'academic_allocation._department_id': convertToMongoObjectId(departmentId),
                'academic_allocation._department_subject_id': {
                    $in: [convertToMongoObjectId(subjectId)],
                },
                isActive: true,
                isDeleted: false,
            },
            { gender: 1, employment: 1, academic_allocation: 1, staff_employment_type: 1 },
        );
        console.timeEnd('staffData');
        staffData.data = staffData.status ? clone(staffData.data) : [];
        const programDatas = await allProgramDatas();
        let courseData = await allCourseList();
        courseData = courseData
            .filter(
                (courseElement) =>
                    (courseElement.administration &&
                        courseElement.administration._department_id &&
                        courseElement.participating &&
                        courseElement.administration._department_id.toString() ===
                            departmentId.toString() &&
                        courseElement.administration._subject_id.toString() ===
                            subjectId.toString()) ||
                    courseElement.participating.find(
                        (participationElement) =>
                            participationElement._department_id &&
                            participationElement._department_id.toString() ===
                                departmentId.toString() &&
                            participationElement._subject_id.toString() === subjectId.toString(),
                    ),
            )
            .map((mappingElement) => {
                return {
                    _id: mappingElement._id,
                    _program_id: mappingElement._program_id,
                    administration: mappingElement.administration,
                    participating: mappingElement.participating,
                    credit_hours: mappingElement.credit_hours,
                    versionNo: mappingElement.versionNo || 1,
                    versioned: mappingElement.versioned || false,
                    versionName: mappingElement.versionName || '',
                    versionedFrom: mappingElement.versionedFrom || null,
                    versionedCourseIds: mappingElement.versionedCourseIds || [],
                };
            });
        const courseIds = courseData.map((ele) => ele._id);
        const sessionDeliveryTypes = await allSessionDeliveryTypesDatas();
        let sessionFlowData = await allSessionOrderDatas();
        sessionFlowData = sessionFlowData.filter((sessionFlowElement) =>
            courseIds.find(
                (courseElement) =>
                    courseElement.toString() === sessionFlowElement._course_id.toString(),
            ),
        );
        const coursesList = [];
        // Redis Cache Loading Area
        console.time('v1ProgramCourseCreditHoursData');
        const v1ProgramCourseCreditHoursData = await redisClient.Client.get(
            `${V1_PROGRAM_COURSE_CREDIT_HOURS}-${institutionCalendarId}`,
        );
        console.timeEnd('v1ProgramCourseCreditHoursData');
        console.time('v1ProgramCourseScheduleStaffSubject');
        const v1ProgramCourseScheduleStaffSubject = await redisClient.Client.get(
            `${V1_PROGRAM_COURSE_SCHEDULE_STAFF_SUBJECT}-${institutionCalendarId}`,
        );
        console.timeEnd('v1ProgramCourseScheduleStaffSubject');
        if (v1ProgramCourseCreditHoursData && v1ProgramCourseScheduleStaffSubject) {
            console.time('Redis Load');
            const filteredCourseList = JSON.parse(v1ProgramCourseCreditHoursData).filter(
                (courseElement) =>
                    courseIds.find(
                        (courseIdElement) =>
                            courseIdElement.toString() === courseElement._course_id.toString(),
                    ),
            );
            const courseScheduleDatas = JSON.parse(v1ProgramCourseScheduleStaffSubject).filter(
                (courseScheduleElement) =>
                    courseIds.find(
                        (courseIdElement) =>
                            courseIdElement.toString() ===
                            courseScheduleElement._course_id.toString(),
                    ),
            );
            for (courseElement of filteredCourseList) {
                const courseSchedule = courseScheduleDatas.find(
                    (courseScheduleElement) =>
                        courseElement._program_id.toString() ===
                            courseScheduleElement._program_id.toString() &&
                        courseElement._course_id.toString() ===
                            courseScheduleElement._course_id.toString() &&
                        courseElement.term === courseScheduleElement.term &&
                        courseElement.year === courseScheduleElement.year &&
                        courseElement.level === courseScheduleElement.level,
                );
                delete courseElement.students;
                coursesList.push({ ...courseElement, ...courseSchedule });
            }
            console.timeEnd('Redis Load');
        } else {
            console.time('courseSchedule');
            // Course Schedule Datas
            const scheduleData = await get_list(
                course_schedule,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _course_id: { $in: courseIds },
                    type: 'regular',
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _session_id: '$session._session_id',
                    'session._session_id': 1,
                    'session.session_type': 1,
                    status: 1,
                    _course_id: 1,
                    level_no: 1,
                    term: 1,
                    rotation: 1,
                    rotation_count: 1,
                    subjects: 1,
                    staffs: 1,
                },
            );
            scheduleData.data = scheduleData.status ? scheduleData.data : [];
            console.timeEnd('courseSchedule');
            let pcData = await allProgramCalendarDatas();
            pcData = pcData.filter(
                (programElement) =>
                    programElement._institution_calendar_id.toString() === institutionCalendarId &&
                    programElement.level.some(
                        (levelElement) =>
                            levelElement.course.some((courseElement) =>
                                courseIds.find(
                                    (courseIdElement) =>
                                        courseIdElement.toString() ===
                                        courseElement._course_id.toString(),
                                ),
                            ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.some((courseElement) =>
                                    courseIds.find(
                                        (courseIdElement) =>
                                            courseIdElement.toString() ===
                                            courseElement._course_id.toString(),
                                    ),
                                ),
                            ),
                    ),
            );
            // const coursesList = [];
            for (const pcList of pcData) {
                for (const pcLevel of pcList.level) {
                    if (pcLevel.rotation === 'no')
                        for (levelCourse of pcLevel.course) {
                            if (
                                courseIds.find(
                                    (ele) => ele.toString() === levelCourse._course_id.toString(),
                                )
                            ) {
                                const courseSessionOrderData = sessionFlowData.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        levelCourse._course_id.toString(),
                                )
                                    ? sessionFlowData.find(
                                          (ele) =>
                                              ele._course_id.toString() ===
                                              levelCourse._course_id.toString(),
                                      ).session_flow_data
                                    : [];

                                const courseScheduled = clone(
                                    scheduleData.data.filter(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                                levelCourse._course_id.toString() &&
                                            ele.term === pcLevel.term &&
                                            ele.level_no === pcLevel.level_no,
                                    ),
                                );

                                // * Session Schedule Based Report Course Session Order filter based on Schedule
                                let sessionOrderData = courseSessionOrderData
                                    ? clone(courseSessionOrderData)
                                    : { session_flow_data: [] };
                                if (
                                    SCHEDULE_SESSION_BASED_REPORT &&
                                    SCHEDULE_SESSION_BASED_REPORT === 'true'
                                ) {
                                    sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                        courseSessionFlow: courseSessionOrderData,
                                        courseSchedule: courseScheduled,
                                    }).session_flow_data;
                                }

                                let sessionIds = courseScheduled.map((scheduleElement) =>
                                    scheduleElement.session._session_id.toString(),
                                );
                                sessionIds = [...new Set(sessionIds)];
                                const crsData = clone(levelCourse);
                                crsData.course_name = crsData.courses_name;
                                crsData.course_no = crsData.courses_number;
                                crsData.course_type = crsData.model;
                                crsData.schedule = courseScheduled;
                                crsData.session_count = sessionIds.length;
                                crsData.no_session = sessionOrderData.length;
                                // crsData.completed_session = courseScheduled.filter(
                                //     (ele) => ele.status && ele.status === COMPLETED,
                                // ).length;
                                // Need to get Finale Warning & Denial count from LMS
                                crsData.final_warning = 5;
                                crsData.denial = 2;
                                crsData.term = pcLevel.term;
                                crsData.program_name = programDatas.find(
                                    (ele) => ele._id.toString() === pcLevel._program_id.toString(),
                                ).name;
                                // crsData.program_name = pcLevel.curriculum.substring(
                                //     0,
                                //     pcLevel.curriculum.length - 4,
                                // );
                                crsData._program_id = pcLevel._program_id;
                                crsData.year = pcLevel.year;
                                crsData.level = pcLevel.level_no;
                                crsData.curriculum = pcLevel.curriculum;
                                crsData.rotation = pcLevel.rotation;
                                const courseCredit = courseData.find(
                                    (courseElement) =>
                                        courseElement._id.toString() ===
                                        crsData._course_id.toString(),
                                );
                                const credit_hours = await courseCreditContactHours(
                                    sessionDeliveryTypes,
                                    pcLevel._program_id,
                                    courseScheduled,
                                    sessionOrderData,
                                    courseCredit ? courseCredit.credit_hours : crsData.credit_hours,
                                );
                                crsData.credit_hours = credit_hours;
                                // crsData.no_session = credit_hours
                                //     .map((ele) => ele.no_of_sessions)
                                //     .reduce((accumulator, current) => accumulator + current);
                                crsData.completed_session = credit_hours
                                    .map((ele) => ele.completed_sessions)
                                    .reduce((accumulator, current) => accumulator + current);
                                coursesList.push(crsData);
                            }
                        }
                    else
                        for (rotations of pcLevel.rotation_course) {
                            for (rotationCourse of rotations.course) {
                                if (
                                    courseIds.find(
                                        (ele) =>
                                            ele.toString() === rotationCourse._course_id.toString(),
                                    )
                                ) {
                                    const courseSessionOrderData = sessionFlowData.find(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                            rotationCourse._course_id.toString(),
                                    )
                                        ? sessionFlowData.find(
                                              (ele) =>
                                                  ele._course_id.toString() ===
                                                  rotationCourse._course_id.toString(),
                                          ).session_flow_data
                                        : [];
                                    const courseScheduled = clone(
                                        scheduleData.data.filter(
                                            (ele) =>
                                                ele.rotation &&
                                                ele.rotation === 'yes' &&
                                                ele.rotation_count === rotations.rotation_count &&
                                                ele._course_id.toString() ===
                                                    rotationCourse._course_id.toString() &&
                                                ele.term === pcLevel.term &&
                                                ele.level_no === pcLevel.level_no,
                                        ),
                                    );

                                    // * Session Schedule Based Report Course Session Order filter based on Schedule
                                    let sessionOrderData = courseSessionOrderData
                                        ? clone(courseSessionOrderData)
                                        : { session_flow_data: [] };
                                    if (
                                        SCHEDULE_SESSION_BASED_REPORT &&
                                        SCHEDULE_SESSION_BASED_REPORT === 'true'
                                    ) {
                                        sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                            courseSessionFlow: courseSessionOrderData,
                                            courseSchedule: courseScheduled,
                                        }).session_flow_data;
                                    }

                                    let sessionIds = courseScheduled.map((scheduleElement) =>
                                        scheduleElement.session._session_id.toString(),
                                    );
                                    sessionIds = [...new Set(sessionIds)];
                                    const crsData = clone(rotationCourse);
                                    crsData.course_name = crsData.courses_name;
                                    crsData.course_no = crsData.courses_number;
                                    crsData.course_type = crsData.model;
                                    crsData.schedule = courseScheduled;
                                    crsData.session_count = sessionIds.length;
                                    crsData.no_session = sessionOrderData.length;
                                    crsData.completed_session = courseScheduled.filter(
                                        (ele) => ele.status && ele.status === COMPLETED,
                                    ).length;
                                    // Need to get Finale Warning & Denial count from LMS
                                    crsData.final_warning = 5;
                                    crsData.denial = 2;
                                    crsData.term = pcLevel.term;
                                    crsData.program_name = programDatas.find(
                                        (ele) =>
                                            ele._id.toString() === pcLevel._program_id.toString(),
                                    ).name;
                                    crsData._program_id = pcLevel._program_id;
                                    crsData.year = pcLevel.year;
                                    crsData.level = pcLevel.level_no;
                                    crsData.curriculum = pcLevel.curriculum;
                                    crsData.rotation = pcLevel.rotation;
                                    crsData.rotation_count = rotations.rotation_count;
                                    const courseCredit = courseData.find(
                                        (courseElement) =>
                                            courseElement._id.toString() ===
                                            crsData._course_id.toString(),
                                    );
                                    const credit_hours = await courseCreditContactHours(
                                        sessionDeliveryTypes,
                                        pcLevel._program_id,
                                        courseScheduled,
                                        sessionOrderData,
                                        courseCredit
                                            ? courseCredit.credit_hours
                                            : crsData.credit_hours,
                                    );
                                    crsData.credit_hours = credit_hours;
                                    // crsData.no_session = credit_hours
                                    //     .map((ele) => ele.no_of_sessions)
                                    //     .reduce((accumulator, current) => accumulator + current);
                                    crsData.completed_session = credit_hours
                                        .map((ele) => ele.completed_sessions)
                                        .reduce((accumulator, current) => accumulator + current);

                                    coursesList.push(crsData);
                                }
                            }
                        }
                }
            }
        }
        const subjectListData = departmentSubject.map((ele) => ele.subject).flat();
        const departmentData = departmentSubject.find(
            (ele) => ele._id.toString() === departmentId.toString(),
        );
        const subData = subjectListData.find((ele) => ele._id.toString() === subjectId.toString());
        const depart = {
            _id: departmentData._id,
            department_name: departmentData.department_name,
            _subject_id: subData._id,
            subject_name: subData.subject_name,
        };
        const adminCourse = [];
        const adminCourseStaff = {
            male_count: 0,
            female_count: 0,
            part_time_count: 0,
            full_time_count: 0,
        };
        const participationCourse = [];
        const participationCourseStaff = {
            male_count: 0,
            female_count: 0,
            part_time_count: 0,
            full_time_count: 0,
        };
        for (courseElementData of coursesList) {
            const courseElement = courseData.find(
                (ele) => courseElementData._course_id.toString() === ele._id.toString(),
            );
            courseElementData.versionNo = courseElement.versionNo || 1;
            courseElementData.versioned = courseElement.versioned || false;
            courseElementData.versionName = courseElement.versionName || '';
            courseElementData.versionedFrom = courseElement.versionedFrom || null;
            courseElementData.versionedCourseIds = courseElement.versionedCourseIds || [];
            if (
                departmentData.program_id.toString() !== programId
                    ? courseElement._program_id.toString() === programId
                    : true &&
                      // courseElement.administration._department_id.toString() ===
                      //     departmentId.toString() ||
                      courseElement.administration._subject_id.toString() === subjectId.toString()
            ) {
                let scheduleStaffIds = [];
                if (
                    courseElementData.countAvailable !== undefined &&
                    courseElementData.countAvailable
                ) {
                    scheduleStaffIds = courseElementData.schedule.map((ele) => ele.staffIds).flat();
                    scheduleStaffIds = [...new Set(scheduleStaffIds)];
                } else {
                    scheduleStaffIds = courseElementData.schedule
                        .map((ele) => ele.staffs.map((ele2) => ele2._staff_id.toString()).flat())
                        .flat();
                    if (courseElementData.scheduleStaffIds)
                        scheduleStaffIds = [
                            ...scheduleStaffIds,
                            ...courseElementData.scheduleStaffIds,
                        ];
                    scheduleStaffIds = [...new Set(scheduleStaffIds)];
                }
                const staffIdDatas = staffData.data.filter((ele) =>
                    scheduleStaffIds.includes(ele._id.toString()),
                );
                courseElementData.scheduleStaffIds = scheduleStaffIds;
                const staffSplitData = await staffSplit(staffIdDatas);
                courseElementData.staffs = staffSplitData;
                courseElementData.course_shared =
                    courseElementData._program_id.toString() !==
                    courseElement._program_id.toString();
                courseElementData.course_shared_program = courseElementData.course_shared
                    ? programDatas.find(
                          (ele) => ele._id.toString() === courseElement._program_id.toString(),
                      ).name
                    : '';
                delete courseElementData.schedule;
                adminCourse.push(courseElementData);
                if (staffIdDatas.length) {
                    adminCourseStaff.male_count = staffSplitData.male_count;
                    adminCourseStaff.female_count = staffSplitData.female_count;
                    adminCourseStaff.part_time_count = staffSplitData.part_time_count;
                    adminCourseStaff.full_time_count = staffSplitData.full_time_count;
                }
            } else if (
                departmentData.program_id.toString() !== programId
                    ? courseElement._program_id.toString() === programId
                    : true
            ) {
                let scheduleStaffIds = [];
                for (participationSubjectElement of courseElement.participating) {
                    if (
                        // participationSubjectElement._department_id.toString() ===
                        //     departmentId.toString() ||
                        participationSubjectElement._subject_id.toString() === subjectId.toString()
                    ) {
                        if (
                            courseElementData.countAvailable !== undefined &&
                            courseElementData.countAvailable
                        ) {
                            scheduleStaffIds = courseElementData.schedule
                                .map((ele) => ele.staffIds)
                                .flat();
                            scheduleStaffIds = [...new Set(scheduleStaffIds)];
                            courseElementData.session_count = courseElementData.sessionCount;
                        } else {
                            courseElementData.schedule = clone(
                                courseElementData.schedule.filter((ele) =>
                                    ele.subjects.some(
                                        (ele2) =>
                                            ele2._subject_id.toString() === subjectId.toString(),
                                    ),
                                ),
                            );
                            let scheduleStaffIds = courseElementData.schedule
                                .map((ele) =>
                                    ele.staffs.map((ele2) => ele2._staff_id.toString()).flat(),
                                )
                                .flat();
                            scheduleStaffIds = [...new Set(scheduleStaffIds)];
                            let sessionIds = courseElementData.schedule.map((csElement) =>
                                csElement.session._session_id.toString(),
                            );
                            if (courseElementData.scheduleStaffIds)
                                scheduleStaffIds = [
                                    ...scheduleStaffIds,
                                    ...courseElementData.scheduleStaffIds,
                                ];
                            sessionIds = [...new Set(sessionIds)];
                            courseElementData.session_count = sessionIds.length;
                        }
                        const staffIdDatas = staffData.data.filter((ele) =>
                            scheduleStaffIds.includes(ele._id.toString()),
                        );
                        const staffSplitData = await staffSplit(staffIdDatas);
                        courseElementData.scheduleStaffIds = scheduleStaffIds;
                        courseElementData.staffs = staffSplitData;
                        courseElementData.course_shared =
                            courseElementData._program_id.toString() !==
                            courseElement._program_id.toString();
                        courseElementData.course_shared_program = courseElementData.course_shared
                            ? programDatas.find(
                                  (ele) =>
                                      ele._id.toString() === courseElement._program_id.toString(),
                              ).name
                            : '';
                        delete courseElementData.schedule;
                        participationCourse.push(courseElementData);
                        if (staffIdDatas.length) {
                            participationCourseStaff.male_count += staffSplitData.male_count;
                            participationCourseStaff.female_count += staffSplitData.female_count;
                            participationCourseStaff.part_time_count +=
                                staffSplitData.part_time_count;
                            participationCourseStaff.full_time_count +=
                                staffSplitData.full_time_count;
                        }
                    }
                }
            }
        }
        depart.admin_course_staff = adminCourseStaff;
        depart.participation_course_staff = participationCourseStaff;
        depart.admin_course = adminCourse;
        depart.participation_course = participationCourse;
        const response = {
            staffs: await staffSplit(staffData.data),
            courses: depart,
        };
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_COURSE_LIST'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program -> Level wise Course Course Coordinators
exports.dashboardSplittedModules = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, userId, roleId, tab },
            query: { module },
        } = req;
        const programList = await allProgramDatas();
        if (!programList)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));
        const curriculumList = await allCurriculumDatas();
        if (!curriculumList)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));
        const userRoleAssign = await allRoleAssignDatas(userId);
        const roleData = userRoleAssign.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        let programIdResult = await userRoleData(userId, roleId);
        let programIds = roleData.program.map((ele) => ele._program_id.toString());
        const programCourseData = (await allCourseList()).map((courseElement) => {
            return {
                _id: courseElement._id,
                course_name: courseElement.course_name,
                course_code: courseElement.course_code,
                course_type: courseElement.course_type,
                _program_id: courseElement._program_id,
                administration: courseElement.administration,
                participating: courseElement.participating,
                course_assigned_details: courseElement.course_assigned_details,
                coordinators: courseElement.coordinators,
            };
        });
        let courseIds = [];
        let courseWithTerm = [];
        if (roleData.role_name === 'Course Coordinator') {
            const ccDatas = await courseCoordinatorBasedIds(
                programCourseData,
                userId,
                institutionCalendarId,
            );
            courseIds = ccDatas.courseIds;
            programIdResult = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
            programIds = [...programIds, ...programIdResult];
        }
        programIds =
            programIdResult && programIdResult.data && programIdResult.data.length !== 0
                ? [...programIds, ...programIdResult.data]
                : programIds;
        // Schedule Gets
        // const { scheduleCourseTerm, courseScheduleFilteredData, courseStaff } =
        //     await userCourseScheduleList(
        //         userId,
        //         institutionCalendarId,
        //         programIdResult.isAdmin,
        //         programIds,
        //         courseIds,
        //         programIdResult,
        //     );
        // console.log(programIds);
        // programIds = [
        //     ...programIds,
        //     ...courseScheduleFilteredData.map((ele) => ele._program_id.toString()),
        // ];

        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            type: SCHEDULE_TYPES.REGULAR,
        };
        if (programIdResult.isAdmin === false) {
            if (courseIds.length !== 0) {
                Object.assign(scheduleQuery, {
                    $or: [
                        { _course_id: { $in: courseIds } },
                        { 'staffs._staff_id': convertToMongoObjectId(userId) },
                    ],
                });
            } else
                Object.assign(scheduleQuery, {
                    'staffs._staff_id': convertToMongoObjectId(userId),
                });
        } else Object.assign(scheduleQuery, { 'staffs._staff_id': convertToMongoObjectId(userId) });
        console.time('userScheduleData');
        // Program Id List in Schedule by staffID
        const userScheduleData = await course_schedule.distinct('_program_id', scheduleQuery);
        console.timeEnd('userScheduleData');
        if (userScheduleData && userScheduleData.length)
            programIds = [
                ...programIds,
                ...userScheduleData.map((userProgramIdElement) => userProgramIdElement.toString()),
            ];
        programIds = [...new Set(programIds)];
        const activeProgramCalendarList = (await allProgramCalendarDatas())
            .filter(
                (programElement) =>
                    programElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString(),
            )
            .map((programElement) => programElement._program_id.toString());
        if (activeProgramCalendarList && activeProgramCalendarList.length)
            programIds = programIds.filter((programIdElement) =>
                activeProgramCalendarList.find(
                    (programElement) => programElement.toString() === programIdElement.toString(),
                ),
            );
        const userProgram = programList.filter((programElement) =>
            programIds.find(
                (programIdElement) => programIdElement.toString() === programElement._id.toString(),
            ),
        );
        const responses = {};
        switch (tab) {
            case PROGRAM_LIST:
                responses.programs = await dashboardProgramList({
                    userProgram,
                    curriculumList,
                });
                break;
            case PLO:
                responses.program_learning_outcomes = await dashboardPLO({
                    userProgram,
                    curriculumList,
                });
                break;
            case CREDIT_HOURS:
                {
                    // Redis Cache Loading Area
                    console.time('v1ProgramCreditHoursData');
                    const v1ProgramCreditHoursData = await redisClient.Client.get(
                        `${V1_PROGRAM_CREDIT_HOURS}-${institutionCalendarId}`,
                    );
                    console.timeEnd('v1ProgramCreditHoursData');
                    if (v1ProgramCreditHoursData) {
                        responses.credit_hours = JSON.parse(v1ProgramCreditHoursData).filter(
                            (creditHoursElement) =>
                                userProgram.find(
                                    (programElement) =>
                                        programElement._id.toString() ===
                                        creditHoursElement.program_id.toString(),
                                ),
                        );
                    } else {
                        const programCalendarList = (await allProgramCalendarDatas()).filter(
                            (programElement) =>
                                programElement._institution_calendar_id.toString() ===
                                institutionCalendarId,
                        );
                        const sessionDeliveryTypeList = await allSessionDeliveryTypesDatas();
                        const sessionOrderList = await allSessionOrderDatas();
                        // const scheduleData = await allCourseScheduleTillYesterday(
                        //     institutionCalendarId,
                        //     scheduleDateFormateChange(new Date()),
                        // );
                        const scheduleData = await courseScheduleData({ institutionCalendarId });
                        responses.credit_hours = await dashboardCreditHoursCalculation({
                            userProgram,
                            programCalendarList,
                            sessionDeliveryTypeList,
                            sessionOrderList,
                            scheduleData,
                        });
                    }
                    // Course Schedule Datas
                    // const scheduleData = await get_list(
                    //     course_schedule,
                    //     {
                    //         // _institution_id: convertToMongoObjectId(_institution_id),
                    //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    //         // _program_id: { $in: programIds },
                    //         // _course_id: { $in: courseIds },
                    //         type: 'regular',
                    //         isActive: true,
                    //         isDeleted: false,
                    //     },
                    //     {
                    //         // _program_id: 1,
                    //         _session_id: '$session._session_id',
                    //         'session._session_id': 1,
                    //         'session.session_type': 1,
                    //         status: 1,
                    //         _course_id: 1,
                    //         level_no: 1,
                    //         term: 1,
                    //         rotation: 1,
                    //         rotation_count: 1,
                    //         // subjects: 1,
                    //         // staffs: 1,
                    //     },
                    // );
                    // scheduleData.data = scheduleData.status ? scheduleData.data : [];
                }
                break;
            case NO_STUDENT:
                {
                    const studentGroupData = (
                        await allStudentGroupYesterday(
                            institutionCalendarId,
                            scheduleDateFormateChange(new Date()),
                        )
                    ).filter(
                        (studentGroupElement) =>
                            studentGroupElement._institution_calendar_id.toString() ===
                            institutionCalendarId.toString(),
                    );
                    //Student group data
                    // const studentGroupData = await get_list(
                    //     student_group,
                    //     {
                    //         isDeleted: false,
                    //         // 'master._program_id': { $in: programIds },
                    //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    //     },
                    //     {},
                    // );
                    responses.students = await dashboardNoStudent({
                        institutionCalendarId,
                        userProgram,
                        studentGroupData,
                    });
                }
                break;
            case NO_STAFF:
                {
                    console.time('staffData');
                    const staffData = await get_list(
                        user,
                        {
                            status: 'completed',
                            user_type: 'staff',
                            // 'academic_allocation.allocation_type': 'primary',
                            'academic_allocation._program_id': {
                                $in: programIds,
                            },
                            isActive: true,
                            isDeleted: false,
                        },
                        { gender: 1, employment: 1, 'academic_allocation._program_id': 1 },
                    );
                    console.timeEnd('staffData');
                    staffData.data = staffData.status ? clone(staffData.data) : [];
                    responses.staffs = await dashboardNoStaff({
                        userProgram,
                        staffData: staffData.data,
                    });
                }
                break;
            default:
                break;
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), responses));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program Wise Splitted Modules Details
exports.programViewSplittedModules = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, tab },
            query: { term, year, level },
        } = req;
        const programList = await allProgramDatas();
        if (!programList)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));
        const userProgram = programList.find(
            (programElement) => programElement._id.toString() === programId.toString(),
        );

        const studentGroupList = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        const programStudentGroupData = studentGroupList.filter(
            (studentGroupElement) =>
                studentGroupElement.master._program_id.toString() === programId.toString() &&
                studentGroupElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString(),
        );

        // LMS Setting Data
        const lmsData = await lmsStudentSettingData(convertToMongoObjectId(_institution_id));

        let responses = {};
        switch (tab) {
            case SUMMARY:
                responses = await programWiseSummary({ userProgram, programStudentGroupData });
                break;
            case 'academic_report':
                {
                    const levelStudentGroupData = programStudentGroupData.find(
                        (programStudentGroupElement) =>
                            programStudentGroupElement.master.year.toString() === year.toString(),
                    )
                        ? clone(
                              programStudentGroupData.find(
                                  (programStudentGroupElement) =>
                                      programStudentGroupElement.master.year.toString() ===
                                      year.toString(),
                              ),
                          )
                        : { groups: [] };
                    levelStudentGroupData.groups = levelStudentGroupData.groups.filter(
                        (groupElement) =>
                            groupElement.level === level && groupElement.term === term,
                    );
                    const programCalendarList = await allProgramCalendarDatas();
                    //TODO:need to reverify the _institution_calendar_id filter
                    const programCalendarDataList = programCalendarList.find(
                        (programCalendarElement) =>
                            programCalendarElement._program_id.toString() ===
                                programId.toString() &&
                            programCalendarElement._institution_calendar_id.toString() ===
                                institutionCalendarId.toString(),
                    );
                    if (!programCalendarDataList)
                        return res
                            .status(200)
                            .send(
                                responseFunctionWithRequest(
                                    req,
                                    200,
                                    true,
                                    req.t('PROGRAM_LIST'),
                                    [],
                                ),
                            );
                    programCalendarDataList.level = programCalendarDataList.level.find(
                        (levelElement) =>
                            levelElement.term === term &&
                            levelElement.year === year &&
                            levelElement.level_no === level,
                    );
                    const courseIds =
                        programCalendarDataList.level.rotation === 'no'
                            ? programCalendarDataList.level.course
                                  .map((ele2) => ele2._course_id.toString())
                                  .flat()
                            : programCalendarDataList.level.rotation_course[0].course
                                  .map((ele2) => ele2._course_id.toString())
                                  .flat();
                    // course List
                    const programCourseData = await allCourseList();
                    const programSessionDeliveryTypeData = await allSessionDeliveryTypesDatas();
                    const sessionOrderList = await allSessionOrderDatas();

                    // Redis Cache Loading Area
                    console.time('v1ProgramCourseCreditHoursData');
                    const v1ProgramCourseCreditHoursData = await redisClient.Client.get(
                        `${V1_PROGRAM_COURSE_CREDIT_HOURS}-${institutionCalendarId}`,
                    );
                    console.timeEnd('v1ProgramCourseCreditHoursData');
                    console.time('v1ProgramLevelStudentCount');
                    const v1ProgramLevelStudentCount = await redisClient.Client.get(
                        `${V1_PROGRAM_LEVEL_STUDENT_COUNT}-${institutionCalendarId}`,
                    );
                    console.timeEnd('v1ProgramLevelStudentCount');
                    if (v1ProgramCourseCreditHoursData && v1ProgramLevelStudentCount) {
                        const coursesData = JSON.parse(v1ProgramCourseCreditHoursData).filter(
                            (courseElement) =>
                                courseElement._program_id.toString() === programId.toString() &&
                                courseElement.term === term &&
                                courseElement.year === year &&
                                courseElement.level === level,
                        );
                        // const students = [];
                        // for (courseElement of coursesData) {
                        //     for (stdElement of courseElement.students) {
                        //         const loc = students.findIndex(
                        //             (ele) =>
                        //                 ele &&
                        //                 ele.gender === stdElement.gender &&
                        //                 ele.term === term,
                        //         );
                        //         if (loc === -1) {
                        //             students.push({
                        //                 term,
                        //                 gender: stdElement.gender,
                        //                 studentIds: stdElement.studentIds,
                        //                 count: stdElement.count,
                        //             });
                        //         } else {
                        //             let std = [
                        //                 ...students[loc].studentIds,
                        //                 ...stdElement.studentIds,
                        //             ];
                        //             std = clone(
                        //                 std.filter(
                        //                     (ele4, index) =>
                        //                         std.findIndex(
                        //                             (ele3) => ele3.toString() === ele4.toString(),
                        //                         ) === index,
                        //                 ),
                        //             );
                        //             students[loc].studentIds = std;
                        //             students[loc].count = std.length;
                        //         }
                        //         delete stdElement.studentIds;
                        //     }
                        // }
                        // for (studentElement of students) {
                        //     delete studentElement.studentIds;
                        // }
                        const students = JSON.parse(v1ProgramLevelStudentCount).find(
                            (studentGenderElement) =>
                                studentGenderElement._program_id.toString() ===
                                    programId.toString() &&
                                studentGenderElement.term === term &&
                                studentGenderElement.year === year &&
                                studentGenderElement.level === level,
                        );
                        const rotationCourse = [];
                        if (programCalendarDataList.level.rotation === 'yes') {
                            for (
                                let i = 1;
                                i <= programCalendarDataList.level.rotation_count;
                                i++
                            ) {
                                rotationCourse.push({
                                    rotation_count: i,
                                    course: coursesData.filter(
                                        (courseElement) =>
                                            courseElement.rotation_count.toString() ===
                                            i.toString(),
                                    ),
                                });
                            }
                        }

                        responses.levelData = {
                            rotation_count: programCalendarDataList.level.rotation_count,
                            _id: programCalendarDataList.level._id,
                            term: programCalendarDataList.level.term,
                            year: programCalendarDataList.level.year,
                            level_no: programCalendarDataList.level.level_no,
                            curriculum: programCalendarDataList.level.curriculum,
                            rotation: programCalendarDataList.level.rotation,
                            _program_id: programCalendarDataList.level._program_id,
                            course:
                                programCalendarDataList.level.rotation === 'no' ? coursesData : [],
                            rotation_course: rotationCourse,
                            start_date: programCalendarDataList.level.start_date,
                            end_date: programCalendarDataList.level.end_date,
                            students: students
                                ? students.students.map((studentElement) => {
                                      return {
                                          count: studentElement.count,
                                          gender: studentElement.gender,
                                          term,
                                      };
                                  })
                                : [],
                            final_warning: 0,
                            denial: 0,
                        };
                    } else {
                        console.time('courseSchedule');
                        // Course Schedule Datas
                        const programCourseScheduleData = await get_list(
                            course_schedule,
                            {
                                _institution_id: convertToMongoObjectId(_institution_id),
                                _institution_calendar_id:
                                    convertToMongoObjectId(institutionCalendarId),
                                _course_id: { $in: courseIds },
                                type: 'regular',
                                isActive: true,
                                isDeleted: false,
                            },
                            {
                                _session_id: '$session._session_id',
                                'session._session_id': 1,
                                'session.session_type': 1,
                                status: 1,
                                _course_id: 1,
                                level_no: 1,
                                term: 1,
                                rotation: 1,
                                rotation_count: 1,
                                subjects: 1,
                                staffs: 1,
                                students: 1,
                            },
                        );
                        console.timeEnd('courseSchedule');

                        responses.levelData = await programWiseYearLevelCourseData({
                            userProgram,
                            levelStudentGroupData,
                            programCalendarDataList,
                            programCourseData,
                            programSessionDeliveryTypeData,
                            sessionOrderList,
                            programCourseScheduleData: programCourseScheduleData.status
                                ? programCourseScheduleData.data
                                : [],
                            lmsData,
                            programId,
                        });
                    }
                }
                break;
            default:
                break;
        }
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_WISE_REPORT'),
                    responses,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.updateCourseScheduleCache = async (req, res) => {
    try {
        clearItem('allCourseSchedule');
        await allCourseScheduleTillYesterday('', scheduleDateFormateChange(new Date()));
        return res
            .status(200)
            .send(
                response_function(
                    res,
                    200,
                    true,
                    req.t('COURSE_SCHEDULE_CACHE_UPDATED'),
                    req.t('CACHE_UPDATED'),
                ),
            );
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.clearCacheData = async (req, res) => {
    try {
        const response = clearAllItem();
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('CACHE_CLEARED_AND_UPDATED'), response));
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Program -> Level wise Course Course Coordinators
exports.reportCreditHours = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId },
            query: { operationBy },
        } = req;
        if (operationBy && operationBy.length && operationBy === 'manual') clearAllItem();
        // const response = {};
        // console.time('v1ProgramCreditHoursData');
        // const v1ProgramCreditHoursData = await redisClient.Client.get(`${V1_PROGRAM_CREDIT_HOURS}-${institutionCalendarId}`);
        // console.timeEnd('v1ProgramCreditHoursData');
        // if (v1ProgramCreditHoursData) response.creditHours = JSON.parse(v1ProgramCreditHoursData);
        // console.time('v1ProgramCourseCreditHoursData');
        // const v1ProgramCourseCreditHoursData = await redisClient.Client.get(
        //     `${V1_PROGRAM_COURSE_CREDIT_HOURS}-${institutionCalendarId}`,
        // );
        // console.timeEnd('v1ProgramCourseCreditHoursData');
        // if (v1ProgramCourseCreditHoursData)
        //     response.courses = JSON.parse(v1ProgramCourseCreditHoursData);
        // console.time('v1ProgramCourseScheduleStaffSubject');
        // const v1ProgramCourseScheduleStaffSubject = await redisClient.Client.get(
        //     `${V1_PROGRAM_COURSE_SCHEDULE_STAFF_SUBJECT}-${institutionCalendarId}`,
        // );
        // console.timeEnd('v1ProgramCourseScheduleStaffSubject');
        // if (v1ProgramCourseScheduleStaffSubject)
        //     response.courseStaffSubjects = JSON.parse(v1ProgramCourseScheduleStaffSubject);
        // console.time('v1ProgramLevelStudentCount');
        // const v1ProgramLevelStudentCount = await redisClient.Client.get(
        //     `${V1_PROGRAM_LEVEL_STUDENT_COUNT}-${institutionCalendarId},
        // );
        // console.timeEnd('v1ProgramLevelStudentCount');
        // if (v1ProgramLevelStudentCount)
        //     response.programLevelStudentCount = JSON.parse(v1ProgramLevelStudentCount);
        // console.time('v1ProgramLevelStudents');
        // const v1ProgramLevelStudents = await redisClient.Client.get(`${V1_PROGRAM_LEVEL_STUDENTS}-${institutionCalendarId}`);
        // console.timeEnd('v1ProgramLevelStudents');
        // if (v1ProgramLevelStudents)
        //     response.programLevelStudents = JSON.parse(v1ProgramLevelStudents);
        // console.time('v1ProgramLevelStaffs');
        // const v1ProgramLevelStaffs = await redisClient.Client.get(`${V1_PROGRAM_LEVEL_STAFFS}-${institutionCalendarId}`);
        // console.timeEnd('v1ProgramLevelStaffs');
        // if (v1ProgramLevelStaffs) response.programLevelStaffs = JSON.parse(v1ProgramLevelStaffs);
        // if (response !== {}) return res.send(response);

        const programList = await allProgramDatas();
        if (!programList)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('PROGRAM_LIST'), []));
        const programCalendarList = await allProgramCalendarDatas();
        const sessionDeliveryTypeList = await allSessionDeliveryTypesDatas();
        const sessionOrderList = await allSessionOrderDatas();
        // const scheduleData = (
        //     await allCourseScheduleTillYesterday(
        //         institutionCalendarId,
        //         scheduleDateFormateChange(new Date()),
        //     )
        // ).filter(
        //     (scheduleElement) =>
        //         scheduleElement._institution_calendar_id === institutionCalendarId.toString(),
        // );
        const scheduleData = await courseScheduleData({ institutionCalendarId });
        const studentGroupList = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        //Leave Setting Data
        const lmsData = await lmsNewSetting({
            _institution_id:
                _institution_id && _institution_id.length
                    ? convertToMongoObjectId(_institution_id)
                    : convertToMongoObjectId('5e5d0f1a15b4d600173d5692'),
            _institution_calendar_id: institutionCalendarId,
        });
        console.time('studentCriteriaData');
        const studentCriteriaData = await lmsDenialSchema({
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            isActive: true,
            isDeleted: false,
        });
        console.timeEnd('studentCriteriaData');
        const lmsClonedData = clone(lmsData);

        // Staff List
        const departmentSubject = await allDepartmentSubjectList();
        const programCourseData = await allCourseList();
        //Staff Gets
        console.time('staffData');
        const staffData = await get_list(
            user,
            {
                status: 'completed',
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
                isActive: true,
                isDeleted: false,
            },
            {
                name: 1,
                user_id: 1,
                gender: 1,
                employment: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
            },
        );
        staffData.data = staffData.status ? clone(staffData.data) : [];
        console.timeEnd('staffData');

        const programs = [];
        const coursesData = [];
        const coursesScheduleStaff = [];
        const programLevelWiseStudentCount = [];
        const programStudentList = [];
        const programStaffList = [];
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId: programCourseData.map((courseElement) => courseElement._id),
        });
        for (element of programList) {
            const programStudentGroupData = studentGroupList.filter(
                (studentGroupElement) =>
                    studentGroupElement.master._program_id.toString() === element._id.toString() &&
                    studentGroupElement._institution_calendar_id.toString() ===
                        institutionCalendarId.toString(),
            );
            //Credit Hours Calculation
            let theory = 0;
            let practical = 0;
            let clinical = 0;
            let theoryCompleted = 0;
            let practicalCompleted = 0;
            let clinicalCompleted = 0;
            const pcData = programCalendarList.find(
                (idElement) =>
                    idElement._program_id.toString() === element._id.toString() &&
                    idElement._institution_calendar_id.toString() ===
                        institutionCalendarId.toString(),
            );
            if (pcData) {
                const programSessionDeliveryTypes = sessionDeliveryTypeList.filter(
                    (ele) => ele._program_id.toString() === element._id.toString(),
                );
                for (levelElement of pcData.level) {
                    // programStudentGroupData
                    const levelStudentGroupData = programStudentGroupData.find(
                        (programStudentGroupElement) =>
                            programStudentGroupElement.master.year.toString() ===
                            levelElement.year.toString(),
                    )
                        ? clone(
                              programStudentGroupData.find(
                                  (programStudentGroupElement) =>
                                      programStudentGroupElement.master.year.toString() ===
                                      levelElement.year.toString(),
                              ),
                          )
                        : { groups: [] };
                    levelStudentGroupData.groups = levelStudentGroupData.groups.find(
                        (groupElement) =>
                            groupElement.level.toString() === levelElement.level_no.toString() &&
                            groupElement.term.toString() === levelElement.term.toString(),
                    );
                    if (levelElement.rotation === 'no') {
                        for (courseElement of levelElement.course) {
                            const courseBasedStudentCount = {
                                male: 0,
                                female: 0,
                            };
                            // Credit Hour Calculation
                            const theoryCredit = courseElement.credit_hours.find(
                                (ele) => ele.type_name === 'Theory',
                            );
                            const practicalCredit = courseElement.credit_hours.find(
                                (ele) => ele.type_name === 'Practical',
                            );
                            const clinicalCredit = courseElement.credit_hours.find(
                                (ele) => ele.type_name === 'Clinical',
                            );
                            theory += theoryCredit ? theoryCredit.credit_hours : 0;
                            practical += practicalCredit ? practicalCredit.credit_hours : 0;
                            clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;
                            const courseSessionOrder = sessionOrderList.find(
                                (ele) =>
                                    ele._course_id.toString() ===
                                    courseElement._course_id.toString(),
                            );
                            const courseSessionOrderData = courseSessionOrder
                                ? courseSessionOrder.session_flow_data
                                : [];
                            const courseScheduled = clone(
                                scheduleData.filter(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                            courseElement._course_id.toString() &&
                                        ele.term === levelElement.term &&
                                        ele.level_no === levelElement.level_no,
                                ),
                            );

                            // * Session Schedule Based Report Course Session Order filter based on Schedule
                            let sessionOrderData = courseSessionOrderData
                                ? clone(courseSessionOrderData)
                                : { session_flow_data: [] };
                            if (
                                SCHEDULE_SESSION_BASED_REPORT &&
                                SCHEDULE_SESSION_BASED_REPORT === 'true'
                            ) {
                                sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                    courseSessionFlow: courseSessionOrderData,
                                    courseSchedule: courseScheduled,
                                }).session_flow_data;
                            }

                            const sCourse = clone(
                                programCourseData.find(
                                    (uCourse) =>
                                        uCourse._id.toString() ===
                                        courseElement._course_id.toString(),
                                ),
                            );
                            const credit_hours = courseCreditContactHours(
                                programSessionDeliveryTypes,
                                element._id.toString(),
                                courseScheduled,
                                sessionOrderData,
                                sCourse && sCourse.credit_hours
                                    ? sCourse.credit_hours
                                    : courseElement.credit_hours,
                            );
                            // credit_hours
                            const completedTheoryCredit = credit_hours.find(
                                (ele) => ele.type_name === 'Theory',
                            );
                            const completedPracticalCredit = credit_hours.find(
                                (ele) => ele.type_name === 'Practical',
                            );
                            const completedClinicalCredit = credit_hours.find(
                                (ele) => ele.type_name === 'Clinical',
                            );
                            theoryCompleted += completedTheoryCredit
                                ? completedTheoryCredit.completed_credit_hours
                                : 0;
                            practicalCompleted += completedPracticalCredit
                                ? completedPracticalCredit.completed_credit_hours
                                : 0;
                            clinicalCompleted += completedClinicalCredit
                                ? completedClinicalCredit.completed_credit_hours
                                : 0;

                            // SG Course Students
                            const sgCourse =
                                levelStudentGroupData.groups && levelStudentGroupData.groups.courses
                                    ? levelStudentGroupData.groups.courses.find(
                                          (sgLevelCourse) =>
                                              sgLevelCourse._course_id.toString() ===
                                              courseElement._course_id.toString(),
                                      )
                                    : undefined;

                            const sgGenderStudents = [];
                            const sessionData = { no_session: 0, completed_session: 0 };
                            if (sgCourse) {
                                for (sgSetting of sgCourse.setting) {
                                    if (sgSetting.gender === BOTH) {
                                        let studentIds = [];
                                        if (
                                            sgSetting &&
                                            sgSetting.session_setting &&
                                            sgSetting.session_setting.length &&
                                            sgSetting.session_setting[0].groups
                                        ) {
                                            studentIds = [
                                                ...studentIds,
                                                ...sgSetting.session_setting[0].groups
                                                    .map(
                                                        (groupElement) => groupElement._student_ids,
                                                    )
                                                    .flat(),
                                            ];
                                        }
                                        studentIds = studentIds.filter(
                                            (ele, index) =>
                                                studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                        const maleStudentIds = studentIds.filter(
                                            (studentIdElement) =>
                                                levelStudentGroupData.groups.students.find(
                                                    (studentElement) =>
                                                        studentElement.gender === GENDER.MALE &&
                                                        studentElement._student_id.toString() ===
                                                            studentIdElement.toString(),
                                                ),
                                        );
                                        const femaleStudentIds = studentIds.filter(
                                            (studentIdElement) =>
                                                levelStudentGroupData.groups.students.find(
                                                    (studentElement) =>
                                                        studentElement.gender === GENDER.FEMALE &&
                                                        studentElement._student_id.toString() ===
                                                            studentIdElement.toString(),
                                                ),
                                        );
                                        courseBasedStudentCount.male += maleStudentIds.length;
                                        courseBasedStudentCount.female += femaleStudentIds.length;
                                        if (sgGenderStudents.length) {
                                            const maleLoc = sgGenderStudents.findIndex(
                                                (ele) => ele && ele.gender === GENDER.MALE,
                                            );
                                            const femaleLoc = sgGenderStudents.findIndex(
                                                (ele) => ele && ele.gender === GENDER.FEMALE,
                                            );
                                            if (maleLoc === -1) {
                                                sgGenderStudents.push({
                                                    gender: GENDER.MALE,
                                                    studentIds: maleStudentIds,
                                                    count: maleStudentIds.length,
                                                });
                                            } else {
                                                sgGenderStudents[maleLoc].studentIds = [
                                                    ...maleStudentIds,
                                                    ...sgGenderStudents[maleLoc].studentIds,
                                                ];
                                                sgGenderStudents[maleLoc].studentIds =
                                                    sgGenderStudents[maleLoc].studentIds.filter(
                                                        (ele, index) =>
                                                            sgGenderStudents[
                                                                maleLoc
                                                            ].studentIds.findIndex(
                                                                (ele2) =>
                                                                    ele2.toString() ===
                                                                    ele.toString(),
                                                            ) === index,
                                                    );
                                                sgGenderStudents[maleLoc].count =
                                                    sgGenderStudents[maleLoc].studentIds.length;
                                            }
                                            if (femaleLoc === -1) {
                                                sgGenderStudents.push({
                                                    gender: GENDER.FEMALE,
                                                    studentIds: femaleStudentIds,
                                                    count: femaleStudentIds.length,
                                                });
                                            } else {
                                                // Female
                                                sgGenderStudents[femaleLoc].studentIds = [
                                                    ...femaleStudentIds,
                                                    ...sgGenderStudents[femaleLoc].studentIds,
                                                ];
                                                sgGenderStudents[femaleLoc].studentIds =
                                                    sgGenderStudents[femaleLoc].studentIds.filter(
                                                        (ele, index) =>
                                                            sgGenderStudents[
                                                                femaleLoc
                                                            ].studentIds.findIndex(
                                                                (ele2) =>
                                                                    ele2.toString() ===
                                                                    ele.toString(),
                                                            ) === index,
                                                    );
                                                sgGenderStudents[femaleLoc].count =
                                                    sgGenderStudents[femaleLoc].studentIds.length;
                                            }
                                        } else {
                                            sgGenderStudents.push(
                                                {
                                                    gender: GENDER.MALE,
                                                    studentIds: maleStudentIds,
                                                    count: maleStudentIds.length,
                                                },
                                                {
                                                    gender: GENDER.FEMALE,
                                                    studentIds: femaleStudentIds,
                                                    count: femaleStudentIds.length,
                                                },
                                            );
                                        }
                                    } else {
                                        let studentIds = [];
                                        if (
                                            sgSetting &&
                                            sgSetting.session_setting &&
                                            sgSetting.session_setting.length &&
                                            sgSetting.session_setting[0].groups
                                        )
                                            for (sgSession of sgSetting.session_setting[0].groups) {
                                                studentIds = [
                                                    ...studentIds,
                                                    ...sgSession._student_ids,
                                                ];
                                            }
                                        studentIds = studentIds.filter(
                                            (ele, index) =>
                                                studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                        if (sgSetting.gender === GENDER.MALE)
                                            courseBasedStudentCount.male += studentIds.length;
                                        if (sgSetting.gender === GENDER.FEMALE)
                                            courseBasedStudentCount.female += studentIds.length;
                                        const loc = sgGenderStudents.findIndex(
                                            (ele) => ele && ele.gender === sgSetting.gender,
                                        );
                                        if (loc === -1)
                                            sgGenderStudents.push({
                                                gender: sgSetting.gender,
                                                studentIds,
                                                count: studentIds.length,
                                            });
                                        else {
                                            sgGenderStudents[loc].studentIds = [
                                                ...studentIds,
                                                ...sgGenderStudents[loc].studentIds,
                                            ];
                                            sgGenderStudents[loc].studentIds = sgGenderStudents[
                                                loc
                                            ].studentIds.filter(
                                                (ele, index) =>
                                                    sgGenderStudents[loc].studentIds.findIndex(
                                                        (ele2) =>
                                                            ele2.toString() === ele.toString(),
                                                    ) === index,
                                            );
                                            sgGenderStudents[loc].count =
                                                sgGenderStudents[loc].studentIds.length;
                                        }
                                    }
                                }
                                // const sessionOrderData = sessionOrderList.find(
                                //     (ele) =>
                                //         ele._course_id.toString() ===
                                //         sgCourse._course_id.toString(),
                                // );
                                // if (sessionOrderData)
                                //     sessionData.no_session =
                                //         sessionOrderData.session_flow_data.length;

                                sessionData.no_session = sessionOrderData.length;
                            }
                            sessionData.completed_session = credit_hours
                                .map((ele) => ele.completed_sessions)
                                .reduce((accumulator, current) => accumulator + current);
                            if (sessionData.no_session !== 0) {
                                const scheduleSubjects = [];
                                for (scheduleElement of courseScheduled) {
                                    for (scheduleSubjectElement of scheduleElement.subjects) {
                                        const subjectIndex = scheduleSubjects.findIndex(
                                            (subjectElement) =>
                                                subjectElement._subject_id.toString() ===
                                                scheduleSubjectElement._subject_id.toString(),
                                        );
                                        if (subjectIndex === -1) {
                                            scheduleSubjects.push({
                                                _subject_id:
                                                    scheduleSubjectElement._subject_id.toString(),
                                                staffIds: scheduleElement.staffs.map(
                                                    (scheduleStaffElement) =>
                                                        scheduleStaffElement._staff_id.toString(),
                                                ),
                                                sessionIds: [
                                                    scheduleElement.session._session_id.toString(),
                                                ],
                                            });
                                        } else {
                                            const scheduleStaffIds = [
                                                ...scheduleSubjects[subjectIndex].staffIds,
                                                ...scheduleElement.staffs.map(
                                                    (scheduleStaffElement) =>
                                                        scheduleStaffElement._staff_id.toString(),
                                                ),
                                            ];
                                            const scheduleSessionIds = [
                                                ...scheduleSubjects[subjectIndex].sessionIds,
                                                scheduleElement.session._session_id.toString(),
                                            ];
                                            scheduleSubjects[subjectIndex].staffIds = [
                                                ...new Set(scheduleStaffIds),
                                            ];
                                            scheduleSubjects[subjectIndex].sessionIds = [
                                                ...new Set(scheduleSessionIds),
                                            ];
                                        }
                                    }
                                }
                                for (scheduleSubjectElement of scheduleSubjects) {
                                    scheduleSubjectElement.sessionCount =
                                        scheduleSubjectElement.sessionIds.length;
                                    delete scheduleSubjectElement.sessionIds;
                                }
                                // const scheduleSubjects = courseScheduled.map((scheduleElement) => {
                                //     return {
                                //         session: {
                                //             _session_id: scheduleElement.session._session_id,
                                //         },
                                //         staffs: scheduleElement.staffs.map((staffElement) => {
                                //             return {
                                //                 _staff_id: staffElement._staff_id.toString(),
                                //             };
                                //         }),
                                //         subjects: scheduleElement.subjects.map((subjectElement) => {
                                //             return {
                                //                 _subject_id: subjectElement._subject_id.toString(),
                                //             };
                                //         }),
                                //     };
                                // });
                                coursesScheduleStaff.push({
                                    _program_id: element._id,
                                    year: levelElement.year,
                                    level: levelElement.level_no,
                                    term: levelElement.term,
                                    _course_id: courseElement._course_id,
                                    countAvailable: true,
                                    schedule: scheduleSubjects,
                                    program_name: programList.find(
                                        (ele) => ele._id.toString() === element._id.toString(),
                                    ).name,
                                });

                                // Level Wise Student Adding
                                const levelCourseStudentIndex =
                                    programLevelWiseStudentCount.findIndex(
                                        (programLevelElement) =>
                                            programLevelElement._program_id.toString() ===
                                                element._id &&
                                            programLevelElement.level.toString() ===
                                                levelElement.level_no &&
                                            programLevelElement.year.toString() ===
                                                levelElement.year &&
                                            programLevelElement.term.toString() ===
                                                levelElement.term,
                                    );
                                for (sgStudentElement of sgGenderStudents) {
                                    sgStudentElement.courseStudents =
                                        sgStudentElement.gender === GENDER.MALE
                                            ? courseBasedStudentCount.male
                                            : courseBasedStudentCount.female;
                                }
                                if (levelCourseStudentIndex === -1) {
                                    programLevelWiseStudentCount.push({
                                        _program_id: element._id,
                                        year: levelElement.year,
                                        level: levelElement.level_no,
                                        term: levelElement.term,
                                        students: sgGenderStudents,
                                    });
                                } else {
                                    for (sgSetting of sgGenderStudents) {
                                        const studentGenderIndex = programLevelWiseStudentCount[
                                            levelCourseStudentIndex
                                        ].students.findIndex(
                                            (genderElement) =>
                                                genderElement.gender === sgSetting.gender,
                                        );
                                        if (studentGenderIndex === -1)
                                            programLevelWiseStudentCount[
                                                levelCourseStudentIndex
                                            ].students.push(sgSetting);
                                        else {
                                            let levelGenderStudentIds = [
                                                ...programLevelWiseStudentCount[
                                                    levelCourseStudentIndex
                                                ].students[studentGenderIndex].studentIds,
                                                ...sgSetting.studentIds,
                                            ];
                                            levelGenderStudentIds = [
                                                ...new Set(levelGenderStudentIds),
                                            ];
                                            programLevelWiseStudentCount[
                                                levelCourseStudentIndex
                                            ].students[studentGenderIndex].studentIds =
                                                levelGenderStudentIds;
                                            programLevelWiseStudentCount[
                                                levelCourseStudentIndex
                                            ].students[studentGenderIndex].count =
                                                levelGenderStudentIds.length;
                                            programLevelWiseStudentCount[
                                                levelCourseStudentIndex
                                            ].students[studentGenderIndex].courseStudents +=
                                                sgSetting.gender === GENDER.MALE
                                                    ? courseBasedStudentCount.male
                                                    : courseBasedStudentCount.female;
                                        }
                                    }
                                }
                                // for (sgStudentElement of sgGenderStudents)
                                //     delete sgStudentElement.studentIds;
                                const courseVersionedDetails = programCourseData.find(
                                    (programCourseElement) =>
                                        programCourseElement._id.toString() ===
                                        courseElement._course_id.toString(),
                                );
                                coursesData.push({
                                    _program_id: element._id,
                                    year: levelElement.year,
                                    level: levelElement.level_no,
                                    term: levelElement.term,
                                    curriculum: levelElement.curriculum,
                                    _course_id: courseElement._course_id,
                                    course_name: courseElement.courses_name,
                                    course_no: courseElement.courses_number,
                                    course_type: courseElement.model,
                                    versionNo:
                                        (courseVersionedDetails &&
                                            courseVersionedDetails.versionNo) ||
                                        1,
                                    versioned:
                                        (courseVersionedDetails &&
                                            courseVersionedDetails.versioned) ||
                                        false,
                                    versionName:
                                        (courseVersionedDetails &&
                                            courseVersionedDetails.versionName) ||
                                        '',
                                    versionedFrom:
                                        (courseVersionedDetails &&
                                            courseVersionedDetails.versionedFrom) ||
                                        null,
                                    versionedCourseIds:
                                        (courseVersionedDetails &&
                                            courseVersionedDetails.versionedCourseIds) ||
                                        [],
                                    credit_hours,
                                    start_date: courseElement.start_date,
                                    end_date: courseElement.end_date,
                                    rotation: 'no',
                                    students: sgGenderStudents.map((sgGenderStudentElement) => {
                                        return {
                                            gender: sgGenderStudentElement.gender,
                                            count: sgGenderStudentElement.count,
                                        };
                                    }),
                                    ...sessionData,
                                });
                            }
                        }
                    } else if (
                        levelElement.rotation_course[0] &&
                        levelElement.rotation_course[0].course
                    ) {
                        for (rotationCourseElement of levelElement.rotation_course)
                            for (courseElement of rotationCourseElement.course) {
                                const courseBasedStudentCount = {
                                    male: 0,
                                    female: 0,
                                };
                                // Credit Hour Calculation
                                const courseSessionOrder = sessionOrderList.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        courseElement._course_id.toString(),
                                );
                                const courseSessionOrderData = courseSessionOrder
                                    ? courseSessionOrder.session_flow_data
                                    : [];
                                const courseScheduled = clone(
                                    scheduleData.filter(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                                courseElement._course_id.toString() &&
                                            ele.term === levelElement.term &&
                                            ele.level_no === levelElement.level_no &&
                                            ele.rotation_count.toString() ===
                                                rotationCourseElement.rotation_count.toString(),
                                    ),
                                );

                                // * Session Schedule Based Report Course Session Order filter based on Schedule
                                let sessionOrderData = courseSessionOrderData
                                    ? clone(courseSessionOrderData)
                                    : { session_flow_data: [] };
                                if (
                                    SCHEDULE_SESSION_BASED_REPORT &&
                                    SCHEDULE_SESSION_BASED_REPORT === 'true'
                                ) {
                                    sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                        courseSessionFlow: courseSessionOrderData,
                                        courseSchedule: courseScheduled,
                                    }).session_flow_data;
                                }

                                const sCourse = clone(
                                    programCourseData.find(
                                        (uCourse) =>
                                            uCourse._id.toString() ===
                                            courseElement._course_id.toString(),
                                    ),
                                );
                                const credit_hours = courseCreditContactHours(
                                    programSessionDeliveryTypes,
                                    element._id.toString(),
                                    courseScheduled,
                                    sessionOrderData,
                                    sCourse && sCourse.credit_hours
                                        ? sCourse.credit_hours
                                        : courseElement.credit_hours,
                                );

                                if (parseInt(rotationCourseElement.rotation_count) === 1) {
                                    const theoryCredit = courseElement.credit_hours.find(
                                        (ele) => ele.type_name === 'Theory',
                                    );
                                    const practicalCredit = courseElement.credit_hours.find(
                                        (ele) => ele.type_name === 'Practical',
                                    );
                                    const clinicalCredit = courseElement.credit_hours.find(
                                        (ele) => ele.type_name === 'Clinical',
                                    );
                                    theory += theoryCredit ? theoryCredit.credit_hours : 0;
                                    practical += practicalCredit ? practicalCredit.credit_hours : 0;
                                    clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;

                                    const completedTheoryCredit = credit_hours.find(
                                        (ele) => ele.type_name === 'Theory',
                                    );
                                    const completedPracticalCredit = credit_hours.find(
                                        (ele) => ele.type_name === 'Practical',
                                    );
                                    const completedClinicalCredit = credit_hours.find(
                                        (ele) => ele.type_name === 'Clinical',
                                    );
                                    theoryCompleted += completedTheoryCredit
                                        ? completedTheoryCredit.completed_credit_hours
                                        : 0;
                                    practicalCompleted += completedPracticalCredit
                                        ? completedPracticalCredit.completed_credit_hours
                                        : 0;
                                    clinicalCompleted += completedClinicalCredit
                                        ? completedClinicalCredit.completed_credit_hours
                                        : 0;
                                }

                                // SG Course Students
                                const sgCourse =
                                    levelStudentGroupData.groups &&
                                    levelStudentGroupData.groups.courses
                                        ? levelStudentGroupData.groups.courses.find(
                                              (sgLevelCourse) =>
                                                  sgLevelCourse._course_id.toString() ===
                                                  courseElement._course_id.toString(),
                                          )
                                        : undefined;
                                if (sgCourse) {
                                    const sgGenderStudents = [];
                                    const sessionData = { no_session: 0, completed_session: 0 };
                                    // const sessionOrderData = sessionOrderList.find(
                                    //     (ele) =>
                                    //         ele._course_id.toString() ===
                                    //         sgCourse._course_id.toString(),
                                    // );
                                    // if (sessionOrderData)
                                    //     sessionData.no_session =
                                    //         sessionOrderData.session_flow_data.length;
                                    sessionData.no_session = sessionOrderData.length;

                                    const sgRotationGroups = sgCourse.setting.filter(
                                        (sgCourseSettingElement) =>
                                            sgCourseSettingElement._group_no.toString() ===
                                            rotationCourseElement.rotation_count.toString(),
                                    );
                                    for (sgSetting of sgRotationGroups) {
                                        let studentIds = [];
                                        if (
                                            sgSetting &&
                                            sgSetting.session_setting &&
                                            sgSetting.session_setting.length &&
                                            sgSetting.session_setting[0].groups &&
                                            sgSetting._group_no.toString() ===
                                                rotationCourseElement.rotation_count.toString()
                                        )
                                            for (sgSession of sgSetting.session_setting[0].groups) {
                                                studentIds = [
                                                    ...studentIds,
                                                    ...sgSession._student_ids,
                                                ];
                                            }
                                        studentIds = studentIds.filter(
                                            (ele, index) =>
                                                studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                        if (sgSetting.gender === GENDER.MALE)
                                            courseBasedStudentCount.male += studentIds.length;
                                        if (sgSetting.gender === GENDER.FEMALE)
                                            courseBasedStudentCount.female += studentIds.length;
                                        const loc = sgGenderStudents.findIndex(
                                            (ele) => ele && ele.gender === sgSetting.gender,
                                        );
                                        if (loc === -1)
                                            sgGenderStudents.push({
                                                gender: sgSetting.gender,
                                                studentIds,
                                                count: studentIds.length,
                                            });
                                        else {
                                            sgGenderStudents[loc].studentIds = [
                                                ...studentIds,
                                                ...sgGenderStudents[loc].studentIds,
                                            ];
                                            sgGenderStudents[loc].studentIds = sgGenderStudents[
                                                loc
                                            ].studentIds.filter(
                                                (ele, index) =>
                                                    sgGenderStudents[loc].studentIds.findIndex(
                                                        (ele2) =>
                                                            ele2.toString() === ele.toString(),
                                                    ) === index,
                                            );
                                            sgGenderStudents[loc].count =
                                                sgGenderStudents[loc].studentIds.length;
                                        }
                                    }
                                    sessionData.completed_session = credit_hours
                                        .map((ele) => ele.completed_sessions)
                                        .reduce((accumulator, current) => accumulator + current);
                                    if (sessionData.no_session !== 0) {
                                        const scheduleSubjects = [];
                                        for (scheduleElement of courseScheduled) {
                                            for (scheduleSubjectElement of scheduleElement.subjects) {
                                                const subjectIndex = scheduleSubjects.findIndex(
                                                    (subjectElement) =>
                                                        subjectElement._subject_id.toString() ===
                                                        scheduleSubjectElement._subject_id.toString(),
                                                );
                                                if (subjectIndex === -1) {
                                                    scheduleSubjects.push({
                                                        _subject_id:
                                                            scheduleSubjectElement._subject_id.toString(),
                                                        staffIds: scheduleElement.staffs.map(
                                                            (scheduleStaffElement) =>
                                                                scheduleStaffElement._staff_id.toString(),
                                                        ),
                                                        sessionIds: [
                                                            scheduleElement.session._session_id.toString(),
                                                        ],
                                                    });
                                                } else {
                                                    const scheduleStaffIds = [
                                                        ...scheduleSubjects[subjectIndex].staffIds,
                                                        ...scheduleElement.staffs.map(
                                                            (scheduleStaffElement) =>
                                                                scheduleStaffElement._staff_id.toString(),
                                                        ),
                                                    ];
                                                    const scheduleSessionIds = [
                                                        ...scheduleSubjects[subjectIndex]
                                                            .sessionIds,
                                                        scheduleElement.session._session_id.toString(),
                                                    ];
                                                    scheduleSubjects[subjectIndex].staffIds = [
                                                        ...new Set(scheduleStaffIds),
                                                    ];
                                                    scheduleSubjects[subjectIndex].sessionIds = [
                                                        ...new Set(scheduleSessionIds),
                                                    ];
                                                }
                                            }
                                        }
                                        for (scheduleSubjectElement of scheduleSubjects) {
                                            scheduleSubjectElement.sessionCount =
                                                scheduleSubjectElement.sessionIds.length;
                                            delete scheduleSubjectElement.sessionIds;
                                        }
                                        // const scheduleSubjects = courseScheduled.map(
                                        //     (scheduleElement) => {
                                        //         return {
                                        //             session: {
                                        //                 _session_id:
                                        //                     scheduleElement.session._session_id,
                                        //             },
                                        //             staffs: scheduleElement.staffs.map(
                                        //                 (staffElement) => {
                                        //                     return {
                                        //                         _staff_id:
                                        //                             staffElement._staff_id.toString(),
                                        //                     };
                                        //                 },
                                        //             ),
                                        //             subjects: scheduleElement.subjects.map(
                                        //                 (subjectElement) => {
                                        //                     return {
                                        //                         _subject_id:
                                        //                             subjectElement._subject_id.toString(),
                                        //                     };
                                        //                 },
                                        //             ),
                                        //         };
                                        //     },
                                        // );
                                        coursesScheduleStaff.push({
                                            _program_id: element._id,
                                            year: levelElement.year,
                                            level: levelElement.level_no,
                                            term: levelElement.term,
                                            _course_id: courseElement._course_id,
                                            countAvailable: true,
                                            schedule: scheduleSubjects,
                                            program_name: programList.find(
                                                (ele) =>
                                                    ele._id.toString() === element._id.toString(),
                                            ).name,
                                        });

                                        // Level Wise Student Adding
                                        const levelCourseStudentIndex =
                                            programLevelWiseStudentCount.findIndex(
                                                (programLevelElement) =>
                                                    programLevelElement._program_id.toString() ===
                                                        element._id.toString() &&
                                                    programLevelElement.level.toString() ===
                                                        levelElement.level_no.toString() &&
                                                    programLevelElement.year.toString() ===
                                                        levelElement.year.toString() &&
                                                    programLevelElement.term.toString() ===
                                                        levelElement.term.toString(),
                                            );
                                        for (sgStudentElement of sgGenderStudents) {
                                            sgStudentElement.courseStudents =
                                                sgStudentElement.gender === GENDER.MALE
                                                    ? courseBasedStudentCount.male
                                                    : courseBasedStudentCount.female;
                                        }
                                        if (levelCourseStudentIndex === -1) {
                                            programLevelWiseStudentCount.push({
                                                _program_id: element._id,
                                                year: levelElement.year,
                                                level: levelElement.level_no,
                                                term: levelElement.term,
                                                students: sgGenderStudents,
                                            });
                                        } else {
                                            for (sgSetting of sgGenderStudents) {
                                                const studentGenderIndex =
                                                    programLevelWiseStudentCount[
                                                        levelCourseStudentIndex
                                                    ].students.findIndex(
                                                        (genderElement) =>
                                                            genderElement.gender ===
                                                            sgSetting.gender,
                                                    );
                                                if (studentGenderIndex === -1)
                                                    programLevelWiseStudentCount[
                                                        levelCourseStudentIndex
                                                    ].students.push(sgSetting);
                                                else {
                                                    let levelGenderStudentIds = [
                                                        ...programLevelWiseStudentCount[
                                                            levelCourseStudentIndex
                                                        ].students[studentGenderIndex].studentIds,
                                                        ...sgSetting.studentIds,
                                                    ];
                                                    levelGenderStudentIds = [
                                                        ...new Set(levelGenderStudentIds),
                                                    ];
                                                    programLevelWiseStudentCount[
                                                        levelCourseStudentIndex
                                                    ].students[studentGenderIndex].studentIds =
                                                        levelGenderStudentIds;
                                                    programLevelWiseStudentCount[
                                                        levelCourseStudentIndex
                                                    ].students[studentGenderIndex].count =
                                                        levelGenderStudentIds.length;
                                                    programLevelWiseStudentCount[
                                                        levelCourseStudentIndex
                                                    ].students[studentGenderIndex].courseStudents +=
                                                        sgSetting.gender === GENDER.MALE
                                                            ? courseBasedStudentCount.male
                                                            : courseBasedStudentCount.female;
                                                }
                                            }
                                        }
                                        const courseVersionedDetails = programCourseData.find(
                                            (programCourseElement) =>
                                                programCourseElement._id.toString() ===
                                                courseElement._course_id.toString(),
                                        );
                                        coursesData.push({
                                            _program_id: element._id,
                                            _course_id: courseElement._course_id,
                                            year: levelElement.year,
                                            level: levelElement.level_no,
                                            term: levelElement.term,
                                            curriculum: levelElement.curriculum,
                                            course_name: courseElement.courses_name,
                                            versionNo:
                                                (courseVersionedDetails &&
                                                    courseVersionedDetails.versionNo) ||
                                                1,
                                            versioned:
                                                (courseVersionedDetails &&
                                                    courseVersionedDetails.versioned) ||
                                                false,
                                            versionName:
                                                (courseVersionedDetails &&
                                                    courseVersionedDetails.versionName) ||
                                                '',
                                            versionedFrom:
                                                (courseVersionedDetails &&
                                                    courseVersionedDetails.versionedFrom) ||
                                                null,
                                            versionedCourseIds:
                                                (courseVersionedDetails &&
                                                    courseVersionedDetails.versionedCourseIds) ||
                                                [],
                                            course_no: courseElement.courses_number,
                                            course_type: courseElement.model,
                                            credit_hours,
                                            start_date: courseElement.start_date,
                                            end_date: courseElement.end_date,
                                            rotation: 'yes',
                                            rotation_count: rotationCourseElement.rotation_count,
                                            students: sgGenderStudents.map(
                                                (sgGenderStudentElement) => {
                                                    return {
                                                        gender: sgGenderStudentElement.gender,
                                                        count: sgGenderStudentElement.count,
                                                    };
                                                },
                                            ),
                                            ...sessionData,
                                        });
                                    }
                                }
                            }
                    }
                }
            }
            programs.push({
                program_id: element._id,
                program_name: element.name,
                program_code: element.code,
                theory,
                practical,
                clinical,
                theory_complete: theoryCompleted,
                practical_complete: practicalCompleted,
                clinical_complete: clinicalCompleted,
            });

            // Student List
            let studentList = [];
            for (sgYear of programStudentGroupData) {
                for (sgLevel of sgYear.groups) {
                    studentList = [...studentList, ...sgLevel.students];
                }
            }
            const studentDatas = [];
            let termList = [];
            const levelStudents = [];
            const programCourseBasedStudentCount = {
                male: 0,
                female: 0,
            };
            for (sgElement of programStudentGroupData) {
                for (levelElement of sgElement.groups) {
                    const courseBasedStudentCount = {
                        male: 0,
                        female: 0,
                    };
                    const students = [];
                    termList.push(levelElement.term);
                    let lmsTemp;
                    for (courseElement of levelElement.courses) {
                        let studentIds = [];
                        for (settingElement of courseElement.setting) {
                            for (sessionSettingElement of settingElement.session_setting) {
                                for (deliveryElement of sessionSettingElement.groups) {
                                    studentIds = [...studentIds, ...deliveryElement._student_ids];
                                }
                            }
                        }
                        // studentIds = [...new Set(studentIds)];
                        studentIds = studentIds.filter(
                            (ele, index) =>
                                studentIds.findIndex(
                                    (ele2) => ele2.toString() === ele.toString(),
                                ) === index,
                        );
                        const courseScheduled = clone(
                            scheduleData.filter(
                                (ele) =>
                                    ele._course_id.toString() ===
                                        courseElement._course_id.toString() &&
                                    ele.term === levelElement.term &&
                                    ele.level_no === levelElement.level,
                            ),
                        );
                        //Warning Calculations
                        lmsTemp = lmsData;
                        if (
                            lmsTemp.warningAbsenceData[0] &&
                            courseElement.student_absence_percentage &&
                            courseElement.student_absence_percentage !== 0
                        ) {
                            lmsTemp.warningAbsenceData[0].absence_percentage =
                                courseElement.student_absence_percentage;
                        } else {
                            lmsTemp = lmsClonedData;
                        }
                        const studentReport = await studentAttendanceReportV2(
                            studentIds,
                            courseScheduled,
                            lmsTemp.warningAbsenceData,
                            studentCriteriaData,
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            lateExcludeManagement,
                        );
                        const denialStudentList = studentReport.filter(
                            (ele) => ele.warning === lmsTemp.denialWarning,
                        );
                        for (stdElement of studentIds) {
                            const loc = studentDatas.findIndex(
                                (ele) =>
                                    ele._student_id.toString() === stdElement.toString() &&
                                    ele.term === levelElement.term,
                            );
                            const stdLoc = studentList.find(
                                (stdEle) => stdEle._student_id.toString() === stdElement.toString(),
                            );
                            const studentCourseStatus = denialStudentList
                                ? denialStudentList.find(
                                      (ele) => ele.student_id.toString() === stdElement.toString(),
                                  )
                                : undefined;
                            if (loc === -1 && stdLoc) {
                                studentDatas.push({
                                    _student_id: stdLoc._student_id,
                                    name: stdLoc.name,
                                    academic_no: stdLoc.academic_no,
                                    gender: stdLoc.gender,
                                    term: levelElement.term,
                                    courses: 1,
                                    denial: studentCourseStatus ? 1 : 0,
                                    years: [sgElement.master.year],
                                    levels: [levelElement.level],
                                });
                            } else if (stdLoc) {
                                studentDatas[loc].courses += 1;
                                studentDatas[loc].denial +=
                                    studentCourseStatus && studentCourseStatus.warning ? 1 : 0;
                                const levelData = studentDatas[loc].levels;
                                levelData.push(levelElement.level);
                                studentDatas[loc].levels = [...new Set(levelData)];
                                const yearData = studentDatas[loc].years;
                                yearData.push(sgElement.master.year);
                                studentDatas[loc].years = [...new Set(yearData)];
                            }
                            if (stdLoc) {
                                if (stdLoc.gender === GENDER.MALE) {
                                    courseBasedStudentCount.male++;
                                    programCourseBasedStudentCount.male++;
                                }
                                if (stdLoc.gender === GENDER.FEMALE) {
                                    courseBasedStudentCount.female++;
                                    programCourseBasedStudentCount.female++;
                                }
                                const locs = students.findIndex(
                                    (ele) =>
                                        ele &&
                                        ele.gender === stdLoc.gender &&
                                        ele.term === levelElement.term,
                                );
                                if (locs === -1) {
                                    students.push({
                                        term: levelElement.term,
                                        gender: stdLoc.gender,
                                        studentIds: [stdElement.toString()],
                                        count: 1,
                                    });
                                } else {
                                    let std = students[locs].studentIds;
                                    std.push(stdElement.toString());
                                    std = [...new Set(std)];
                                    students[locs].studentIds = std;
                                    students[locs].count = std.length;
                                }
                            }
                        }
                    }
                    students.forEach((ele) => {
                        delete ele.studentIds;
                        ele.courseStudents =
                            ele.gender === GENDER.MALE
                                ? courseBasedStudentCount.male
                                : courseBasedStudentCount.female;
                    });
                    levelStudents.push({
                        year: sgElement.master.year,
                        level_no: levelElement.level,
                        term: levelElement.term,
                        students,
                    });
                }
            }
            termList = [...new Set(termList)];
            const termStudents = [];
            for (termElement of termList) {
                const termData = studentDatas.filter((ele) => ele.term === termElement);
                termStudents.push({
                    term: termElement,
                    total: termData.length,
                    male: termData.filter((ele) => ele.gender === 'male').length,
                    female: termData.filter((ele) => ele.gender === 'female').length,
                    maleCourseStudents: programCourseBasedStudentCount.male,
                    femaleCourseStudents: programCourseBasedStudentCount.female,
                    active: termData.length,
                    inactive: 0,
                    students: termData,
                });
            }
            programStudentList.push({
                program_id: element._id,
                program_name: element.name,
                program_no: element.code,
                term: termList,
                students: termStudents,
                level: levelStudents,
            });

            // Staff List
            const programStaffs = clone(
                staffData.data.filter((staffElement) =>
                    staffElement.academic_allocation.find(
                        (academicAllocationElement) =>
                            academicAllocationElement._program_id.toString() ===
                            element._id.toString(),
                    ),
                ),
            );
            // Staff Academic Calculation
            const academicStaffs = programStaffs.filter(
                (ele) => ele.staff_employment_type === ACADEMIC,
            );
            const adminStaffs = programStaffs.filter(
                (ele) => ele.staff_employment_type === ADMINISTRATION,
            );
            const bothStaffs = programStaffs.filter((ele) => ele.staff_employment_type === BOTH);
            const programStaffObject = {
                total: programStaffs.length,
                academic: await staffSplit(academicStaffs),
                admin: await staffSplit(adminStaffs),
                both: await staffSplit(bothStaffs),
            };
            // Staff Department Calculation
            const programDepartmentStaff = [];
            const programDepartmentSubject = departmentSubject.filter(
                (ele) => ele.program_id.toString() === element._id.toString(),
            );
            for (departmentElement of programDepartmentSubject) {
                const subjectDetails = [];
                for (subjectElement of departmentElement.subject) {
                    const subjectStaffs = [];
                    for (staffElement of programStaffs) {
                        if (
                            !subjectStaffs.find(
                                (ele) =>
                                    ele &&
                                    ele._id &&
                                    ele._id.toString() === staffElement._id.toString(),
                            ) &&
                            staffElement.academic_allocation.find(
                                (allocationElement) =>
                                    allocationElement._department_id.toString() ===
                                        departmentElement._id.toString() &&
                                    allocationElement._department_subject_id.find(
                                        (ele) => ele.toString() === subjectElement._id.toString(),
                                    ),
                            )
                        ) {
                            subjectStaffs.push(staffElement._id);
                        }
                    }
                    subjectDetails.push({
                        _id: subjectElement._id,
                        subject_name: subjectElement.subject_name,
                        staffs: subjectStaffs,
                    });
                }
                programDepartmentStaff.push({
                    _id: departmentElement._id,
                    department_name: departmentElement.department_name,
                    subjects: subjectDetails,
                });
            }
            const subjectList = departmentSubject
                .map((ele) =>
                    ele.subject
                        .map((ele2) => {
                            return {
                                subject_id: ele2._id.toString(),
                                subject_name: ele2.subject_name,
                            };
                        })
                        .flat(),
                )
                .flat();
            const staffList = [];
            for (const staffElement of programStaffs) {
                const staffScheduleList = scheduleData.filter((ele) =>
                    ele.staffs.find(
                        (ele2) => ele2._staff_id.toString() === staffElement._id.toString(),
                    ),
                );
                const courseDatas = [];
                for (staffScheduleElement of staffScheduleList) {
                    if (
                        courseDatas.findIndex(
                            (ele) =>
                                ele &&
                                ((staffScheduleElement.rotation === 'yes' &&
                                    ele.rotation_count &&
                                    ele.rotation_count.toString() ===
                                        staffScheduleElement.rotation_count.toString() &&
                                    ele._course_id.toString() ===
                                        staffScheduleElement._course_id.toString() &&
                                    ele.term === staffScheduleElement.term &&
                                    ele.level_no === staffScheduleElement.level_no) ||
                                    (staffScheduleElement.rotation === 'no' &&
                                        ele._course_id.toString() ===
                                            staffScheduleElement._course_id.toString() &&
                                        ele.term === staffScheduleElement.term &&
                                        ele.level_no === staffScheduleElement.level_no)),
                        ) === -1
                    ) {
                        courseDatas.push({
                            rotation: staffScheduleElement.rotation,
                            rotation_count: staffScheduleElement.rotation_count,
                            _course_id: staffScheduleElement._course_id,
                            term: staffScheduleElement.term,
                            level_no: staffScheduleElement.level_no,
                        });
                    }
                }

                let dSubject = departmentSubject.find(
                    (ele) =>
                        ele.program_id.toString() ===
                        staffElement.academic_allocation
                            .find((ele2) => ele2.allocation_type === PRIMARY)
                            ._program_id.toString(),
                );
                const primary_program = dSubject ? dSubject.program_name : null;
                dSubject = departmentSubject.find(
                    (ele) =>
                        ele._id.toString() ===
                        staffElement.academic_allocation
                            .find((ele2) => ele2.allocation_type === PRIMARY)
                            ._department_id.toString(),
                );
                const primary_department = dSubject ? dSubject.department_name : null;
                const primary_subjects = subjectList
                    .filter((ele) =>
                        staffElement.academic_allocation
                            .find((ele2) => ele2.allocation_type === PRIMARY)
                            ._department_subject_id.find(
                                (ele3) => ele3.toString() === ele.subject_id.toString(),
                            ),
                    )
                    .map((ele4) => ele4.subject_name);
                staffList.push({
                    _id: staffElement._id,
                    name: staffElement.name,
                    user_id: staffElement.user_id,
                    gender: staffElement.gender,
                    no_courses: courseDatas.length,
                    staff_employment_type: staffElement.staff_employment_type,
                    employment_type: staffElement.employment.user_employment_type,
                    primary_program,
                    primary_department,
                    primary_subjects,
                    department_ids: staffElement.academic_allocation.map(
                        (ele) => ele._department_id,
                    ),
                    subject_ids: staffElement.academic_allocation
                        .map((ele) => ele._department_subject_id)
                        .flat(),
                });
            }
            programStaffList.push({
                program_id: element._id,
                program_name: element.name,
                program_staffs: programStaffObject,
                department_subject_staff: programDepartmentStaff,
                staff_list: staffList,
            });
        }
        for (levelStudentCountElement of programLevelWiseStudentCount)
            for (studentGenderElement of levelStudentCountElement.students)
                delete studentGenderElement.studentIds;
        await redisClient.Client.set(
            `${V1_PROGRAM_CREDIT_HOURS}-${institutionCalendarId}`,
            JSON.stringify(programs),
        );
        await redisClient.Client.set(
            `${V1_PROGRAM_COURSE_CREDIT_HOURS}-${institutionCalendarId}`,
            JSON.stringify(coursesData),
        );
        await redisClient.Client.set(
            `${V1_PROGRAM_COURSE_SCHEDULE_STAFF_SUBJECT}-${institutionCalendarId}`,
            JSON.stringify(coursesScheduleStaff),
        );
        await redisClient.Client.set(
            `${V1_PROGRAM_LEVEL_STUDENT_COUNT}-${institutionCalendarId}`,
            JSON.stringify(programLevelWiseStudentCount),
        );
        await redisClient.Client.set(
            `${V1_PROGRAM_LEVEL_STUDENTS}-${institutionCalendarId}`,
            JSON.stringify(programStudentList),
        );
        await redisClient.Client.set(
            `${V1_PROGRAM_LEVEL_STAFFS}-${institutionCalendarId}`,
            JSON.stringify(programStaffList),
        );
        return res.status(200).send(
            response_function(res, 200, true, req.t('PROGRAM_LIST'), {
                creditHours: programs,
                courses: coursesData,
                coursesScheduleStaff,
                programLevelWiseStudentCount,
                programStudentList,
                programStaffList,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
