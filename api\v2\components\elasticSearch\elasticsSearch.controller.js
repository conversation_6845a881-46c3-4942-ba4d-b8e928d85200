const {
    getRecords,
    addRecords,
    updateRecords,
    removeRecords,
} = require('../../services/elasticSearch.service');
const constants = require('../../utility/constants');

const elasticGetAll = async ({ query = {} }) => {
    try {
        const { indexName } = query;
        const userRecords = await getRecords({
            indexName,
            query: {
                match_all: {},
            },
        });
        return { statusCode: 200, message: constants.DS_DATA_RETRIEVED, data: userRecords };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const elasticSearch = async ({ body = {} }) => {
    try {
        const { indexName, query } = body;
        const userRecords = await getRecords({
            indexName,
            query,
        });
        return { statusCode: 200, message: constants.DS_DATA_RETRIEVED, data: userRecords };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const elasticDataAdd = async ({ body = {} }) => {
    try {
        const { document, indexName } = body;
        const userRecords = await addRecords({
            indexName,
            document,
        });
        return { statusCode: 200, message: constants.DS_ADDED, data: userRecords };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const elasticDataUpdate = async ({ body = {} }) => {
    try {
        const { script, indexName, query } = body;
        const userRecords = await updateRecords({
            indexName,
            query,
            script,
        });
        return { statusCode: 200, message: constants.DS_ADDED, data: userRecords };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const elasticDataRemove = async ({ body = {} }) => {
    try {
        const { script, indexName, query } = body;
        const userRecords = await removeRecords({
            indexName,
            query,
            script,
        });
        return { statusCode: 200, message: constants.DS_ADDED, data: userRecords };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    elasticGetAll,
    elasticSearch,
    elasticDataAdd,
    elasticDataUpdate,
    elasticDataRemove,
};
