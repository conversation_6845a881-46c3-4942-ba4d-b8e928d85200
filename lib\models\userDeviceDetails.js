const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { USER, USER_DEVICE_DETAIL, ANDROID, IOS, WEB } = require('../utility/constants');

const userDeviceSchema = new Schema(
    {
        userId: {
            type: ObjectId,
            ref: USER,
        },
        userType: { type: String },
        deviceType: { type: String },
        deviceBrandName: { type: String },
        deviceID: { type: String },
        deviceNumber: { type: String },
        deviceOSVersion: { type: String },
    },
    { timestamps: true },
);
module.exports = model(USER_DEVICE_DETAIL, userDeviceSchema);
