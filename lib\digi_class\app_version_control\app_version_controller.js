const appVersion = require('../../models/appVersions');
const { get_sort_limt: get_sort_limit, insert } = require('../../base/base_controller');
const { logger } = require('../../utility/util_keys');
const { response_function } = require('../../utility/common');
const { DS_DATA_RETRIEVED, DS_ADDED } = require('../../utility/constants');

exports.appVersionGet = async (req, res) => {
    try {
        const {
            query: { osType, appType, limit },
        } = req;
        const appQuery = {
            osType,
            isDeleted: false,
            isActive: true,
        };
        if (appType) appQuery.appType = appType;
        const limits = parseInt(limit) || 5;
        const listData = await get_sort_limit(
            appVersion,
            appQuery,
            {
                osType: 1,
                appType: 1,
                version: 1,
                deploymentType: 1,
            },
            { _id: -1 },
            limits,
        );
        listData.data = listData.status ? listData.data : [];
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), listData.data));
    } catch (error) {
        logger.error('appVersionControl -> getList -> error ', error);
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.appVersionAdd = async (req, res) => {
    try {
        const {
            body: { osType, appType, version, deploymentType, comments },
        } = req;

        logger.info('appVersionControl -> addVersion -> %s version %s - start', osType, version);
        await insert(appVersion, { osType, appType, version, deploymentType, comments });
        logger.warn('appVersionControl -> addVersion -> %s version %s - end', osType, version);
        return res.status(200).send(response_function(res, 200, true, req.t(DS_ADDED), null));
    } catch (error) {
        logger.error('appVersionControl -> addVersion -> error ', error);
        return response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
