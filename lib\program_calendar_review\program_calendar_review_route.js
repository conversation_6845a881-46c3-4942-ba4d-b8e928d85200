const express = require('express');
const route = express.Router();
const program_calendar = require('./program_calendar_review_controller');
const validator = require('./program_calendar_review_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.post('/add_reviewer', validator.add_reviewer, program_calendar.add_reviewer);
route.delete(
    '/remove_reviewer/:id/:reviewer',
    validator.remove_reviewer,
    program_calendar.remove_reviewer,
);
route.post('/add_reviewer_review', validator.reviewer_review, program_calendar.add_reviewer_review);
route.post(
    '/send_reviewer_notification' /* , validator.program_calendar_add_reviewer_review */,
    program_calendar.send_reviewer_notification,
);
route.post(
    '/send_notification',
    validator.send_notification,
    program_calendar.send_dean_reviewer_notification,
);

route.get('/list_reviewer/:id', validator.list_reviewer, program_calendar.list_reviewer);
route.get('/list_review/:whom/:id', validator.list_review, program_calendar.list_review);
route.get(
    '/list_approver_reviewer_review/:whom/:id',
    validator.list_approver_reviewer_review,
    program_calendar.list_approver_reviewer_review,
);
route.get(
    '/staff_student_calendar_view' /* , validator.list_approver_reviewer_review */,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    program_calendar.staff_student_calendar_view,
);
module.exports = route;
