const mongoose = require('mongoose');

const { convertToMongoObjectId } = require('../../../lib/utility/common');

const checkObjectIsInValid = (obj) => !obj || !Object.keys(obj).length;

const checkObjectIsValid = (obj) => !checkObjectIsInValid(obj);

const generateObjectId = () => new mongoose.Types.ObjectId();

// get page limit
const getPageLimit = (limit) => (parseInt(limit) > 0 ? parseInt(limit) : 10);

// get page no
const getPageNo = (pageNo) => (parseInt(pageNo) > 0 ? parseInt(pageNo) : 1);

// get pagination skip value
const getSkip = (limit, pageNo) => limit * (pageNo - 1);

const getPaginationValues = (query) => {
    let { pageNo, limit } = query;
    limit = getPageLimit(limit);
    pageNo = getPageNo(pageNo);
    const skip = getSkip(limit, pageNo);
    return { limit, pageNo, skip, search: query?.search };
};

const getFileAndFormatFromMimeType = (mimetype = '') => {
    const fileType = mimetype.split('/')[0];
    const format = mimetype.split('/')[1];

    return { fileType, format };
};

const checkIfTrue = (bool = '') => bool.toString() === 'true';

// check is id are equals
const isIDEquals = (firstString = '', secondString = '') => {
    let status = false;

    if (firstString === '' || secondString === '') {
        return status;
    }

    if (firstString?.toString() === secondString?.toString()) {
        status = true;
    }
    return status;
};

// remove duplicates from array
const getUniqueArrayBasedOnMultipleKeys = (array = [], keyProps = []) => {
    const keyValueArray = array.map((entry) => {
        const joinedKey = keyProps.map((key) => entry[key]).join('|');
        return [joinedKey, entry];
    });
    const map = new Map(keyValueArray);
    return Array.from(map.values());
};

const getNestedValue = (obj, keys) => {
    return keys.reduce((nestedObj, key) => {
        if (checkObjectIsValid(nestedObj)) return nestedObj[key];

        return nestedObj;
    }, obj);
};

const sortArrayByNestedObject = ({ array = [], nestedKey = '', descending = false }) => {
    return array.sort((a, b) => {
        const nestedValueA = getNestedValue(a, nestedKey.split('.'));
        const nestedValueB = getNestedValue(b, nestedKey.split('.'));

        const valueA = typeof nestedValueA === 'string' ? nestedValueA.toLowerCase() : nestedValueA;
        const valueB = typeof nestedValueB === 'string' ? nestedValueB.toLowerCase() : nestedValueB;

        let result;
        if (valueA < valueB) result = -1;
        else if (valueA > valueB) result = 1;
        else result = 0;

        return descending ? -result : result;
    });
};

const extractBucketAndKey = ({ signedUrl }) => {
    const [bucket, ...keyParts] = signedUrl.split('/');
    return { bucket, key: keyParts.join('/') };
};

const isNumber = (value) => typeof value === 'number';

const removeDuplicatesFromArrayOfObjects = (data, key) => [
    ...new Map(data.map((item) => [item[key], item])).values(),
];

const convertToNumber = (num) => (Number(num) ? Number(num) : 0);

const calculateAverage = (numbers) => {
    if (!Array.isArray(numbers) || numbers.length === 0) return 0;
    const sum = numbers.reduce((a, b) => a + b, 0);
    return convertToNumber(sum / numbers.length);
};

const roundToTwoDecimalPlaces = (number = 0) => {
    return Math.round(number * 100) / 100 || 0;
};

const isBoolean = (value) => typeof value === 'boolean';

const areStringsEqualIgnoringCase = (a = '', b = '', partial = false) => {
    if (!a || !b) return false;
    [a, b] = [a.trim().toLowerCase(), b.trim().toLowerCase()];
    return partial ? a.includes(b) : a === b;
};

const deepClone = (value = '') => {
    try {
        return JSON.parse(JSON.stringify(value));
    } catch {
        throw new Error('Error in deep copy');
    }
};

const getFileCategory = ({ type = '' }) => {
    const imageTypes = new Set(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']);
    const videoTypes = new Set(['mp4', 'mov', 'avi', 'mkv', 'webm']);
    const documentTypes = new Set([
        'pdf',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'txt',
        'csv',
    ]);

    type = type?.toLowerCase();

    if (imageTypes.has(type)) return 'image';
    if (videoTypes.has(type)) return 'video';
    if (documentTypes.has(type)) return 'document';

    return 'unknown';
};

module.exports = {
    checkObjectIsInValid,
    convertToMongoObjectId,
    getPaginationValues,
    getFileAndFormatFromMimeType,
    generateObjectId,
    checkIfTrue,
    isIDEquals,
    getUniqueArrayBasedOnMultipleKeys,
    sortArrayByNestedObject,
    getNestedValue,
    extractBucketAndKey,
    isNumber,
    removeDuplicatesFromArrayOfObjects,
    convertToNumber,
    calculateAverage,
    roundToTwoDecimalPlaces,
    isBoolean,
    areStringsEqualIgnoringCase,
    deepClone,
    getFileCategory,
};
