// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.digi_institute_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.institute_add = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({

            college_name: Joi.string().min(2).max(255).required().error(error => {
                return error;
            }),
            street: Joi.string().min(2).max(255).required().error(error => {
                return error;
            }),
            city: Joi.string().min(2).max(255).required().error(error => {
                return error;
            }),
            state: Joi.string().min(2).max(255).required().error(error => {
                return error;
            }),
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}