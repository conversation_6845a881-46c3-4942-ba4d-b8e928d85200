// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');
const constants = require('../utility/constants');

exports.program = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_count: Joi.number()
                        .min(0)
                        .max(9)
                        .error((error) => {
                            return req.t('ROTATION_COUNT_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    start_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('START_DATE_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    color_code: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('COLOR_CODE_REQUIRED');
                        }),
                    // row_start: Joi.number().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // row_end: Joi.number().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // column_start: Joi.number().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // column_end: Joi.number().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // total_column: Joi.number().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    _event_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_add = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_count: Joi.number()
                        .min(0)
                        .max(9)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_COUNT_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    by: Joi.string()
                        .valid(constant.BY_DATE_WEEK.DATE, constant.BY_DATE_WEEK.WEEK)
                        .error((error) => {
                            return req.t('BY_REQUIRED');
                        }),
                    start_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_WEEK_REQUIRED');
                        }),
                    end_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_WEEK_REQUIRED');
                        }),
                    start_date: Joi.date()
                        .required()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_DATE_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    // by: Joi.string().valid(constant.BY_DATE_WEEK.DATE, constant.BY_DATE_WEEK.WEEK).error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // start_week: Joi.number().min(1).error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // end_week: Joi.number().min(1).error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // start_date: Joi.date().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // end_date: Joi.date().required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    color_code: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('COLOR_CODE_REQUIRED');
                        }),
                    _event_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                    _batch_course_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_BATCH_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_count: Joi.number()
                        .min(1)
                        .max(20)
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    by: Joi.string()
                        .valid(constant.BY_DATE_WEEK.DATE, constant.BY_DATE_WEEK.WEEK)
                        .error((error) => {
                            return req.t('BY_REQUIRED');
                        }),
                    start_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_WEEK_REQUIRED');
                        }),
                    end_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_WEEK_REQUIRED');
                        }),
                    start_date: Joi.date()
                        .required()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_DATE_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    color_code: Joi.string().error((error) => {
                        return req.t('COLOR_CODE_REQUIRED');
                    }),
                    _event_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                    // row_start: Joi.number().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // row_end: Joi.number().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // column_start: Joi.number().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // column_end: Joi.number().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    // total_column: Joi.number().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.update_rotation_course_manual = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_count: Joi.number()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_COUNT_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    by: Joi.string()
                        .valid(constant.BY_DATE_WEEK.DATE, constant.BY_DATE_WEEK.WEEK)
                        .error((error) => {
                            return req.t('BY_REQUIRED');
                        }),
                    start_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_WEEK_REQUIRED');
                        }),
                    end_week: Joi.number()
                        .min(1)
                        .when('by', {
                            is: constant.BY_DATE_WEEK.WEEK,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_WEEK_REQUIRED');
                        }),
                    start_date: Joi.date()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('START_DATE_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .when('by', {
                            is: constant.BY_DATE_WEEK.DATE,
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    color_code: Joi.string().error((error) => {
                        return req.t('COLOR_CODE_REQUIRED');
                    }),
                    _event_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_no: Joi.number()
                        .min(1)
                        .max(20)
                        .error((error) => {
                            return req.t('ROTATION_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_manual_course_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_no: Joi.number()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_delete_event = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    rotation_no: Joi.number()
                        .min(1)
                        .max(20)
                        .error((error) => {
                            return req.t('ROTATION_NO_REQUIRED');
                        }),
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_CALENDAR_ID_REQUIRED');
                    }),
                rotation_no: Joi.number()
                    .min(1)
                    .max(9)
                    .required()
                    .error((error) => {
                        return req.t('ROTATION_NO_REQUIRED');
                    }),
                course: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_COURSE_ID_REQUIRED');
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_list_year_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_CALENDAR_ID_REQUIRED');
                    }),
                year_no: Joi.string()
                    .min(1)
                    .max(9)
                    .required()
                    .error((error) => {
                        return req.t('YEAR_NO_REQUIRED');
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_list_level_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_CALENDAR_ID_REQUIRED');
                    }),
                level_no: Joi.string()
                    .min(1)
                    .max(25)
                    .required()
                    .error((error) => {
                        return req.t('LEVEL_NO_REQUIRED');
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_CALENDAR_ID_REQUIRED');
                    }),
                term: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('TERM_REQUIRED');
                    }),
                course: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('COURSE_REQUIRED');
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_move = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    rotation_no: Joi.number()
                        .min(0)
                        .max(9)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    direction: Joi.string()
                        .valid(constant.DIRECTION.UP, constant.DIRECTION.DOWN)
                        .required()
                        .error((error) => {
                            return req.t('DIRECTION_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
