module.exports = {

    country: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.categoryName,
               
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    country_ID: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            code: doc.code,
            currency: doc.currency,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    }
}