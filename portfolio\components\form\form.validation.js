const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const createFormSchema = Joi.object({
    body: Joi.object({
        title: Joi.string().required(),
        type: Joi.object().required(),
        description: Joi.string().optional(),
        pages: Joi.array().min(1).optional().messages({ 'array.min': 'PAGES_REQUIRED' }),
    }),
}).unknown(true);

const getFormsSchema = Joi.object({
    query: Joi.object({
        type: Joi.string()
            .optional()
            .error(() => {
                return 'TYPE_REQUIRED';
            }),
        isPages: Joi.string()
            .optional()
            .error(() => {
                return 'IS_PAGES_REQUIRED';
            }),
    }),
}).unknown(true);

const updateFormSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().label('FORM_ID_REQUIRED'),
    }),
    body: Joi.object({
        title: Joi.string().optional(),
        type: Joi.object().optional(),
        description: Joi.string().optional(),
        pages: Joi.array().min(1).optional().messages({ 'array.min': 'PAGES_REQUIRED' }),
    }),
}).unknown(true);

const deleteFormSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().label('FORM_ID_REQUIRED'),
    }),
}).unknown(true);

const getFormsByUserSchema = Joi.object({
    query: Joi.object({
        email: Joi.string().email().required(),
        type: Joi.string().optional(),
    }),
}).unknown(true);

const publishFormSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().label('FORM_ID_REQUIRED'),
    }),
}).unknown(true);

const getAssignedRolesSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().label('FORM_ID_REQUIRED'),
    }),
}).unknown(true);

module.exports = {
    createFormSchema,
    getFormsSchema,
    updateFormSchema,
    deleteFormSchema,
    getFormsByUserSchema,
    publishFormSchema,
    getAssignedRolesSchema,
};
