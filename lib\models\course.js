let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let course = new Schema({
    courses_name: {
        type: String,
        required: true
    },
    study_year: {
        type: Number,
        required: true
    },
    study_level: {
        type: Number,
        required: true
    },
    order: {
        type: Number,
        required: true
    },
    courses_number: {
        type: String,
        required: true
    },
    duration: {
        type: Number,
        required: true
    },
    model: {
        type: String,
        enum: [constant.MODEL.COURSE, constant.MODEL.MODULE, constant.MODEL.ELECTIVE],
        required: true
    },
    _administration_department_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT
    },
    _administration_division_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_DIVISIONS,
    },
    _administration_subject_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
    },
    _participating_department_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT
    }],
    _participating_division_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_DIVISIONS
    }],
    _participating_subject_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
    }],
    elective: [{
        topics: {
            type: String
        },
        _subject_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DEPARTMENT_SUBJECT
        },
        /* _staff_id: {
            type: String
            // type: Schema.Types.ObjectId,
            // ref: constant.STAFF
        }, */
        _elective_student_group_id: {
            type: String
            // type: Schema.Types.ObjectId,
            // ref: constant.STAFF
        }
    }],
    theory_credit: {
        type: Number,
        required: true
    },
    practical_credit: {
        type: Number,
        required: true
    },
    clinical_credit: {
        type: Number,
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.COURSE, course);