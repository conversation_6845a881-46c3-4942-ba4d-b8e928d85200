let mongoose = require('mongoose');
let Schemas = mongoose.Schema;
let constant = require('../utility/constants');

let staffSchemas = new Schemas({
    user_type: {
        type: String,
        required: true,
        enum: ['student', 'staff', 'administration']
    },
    username: {
        type: String,
        unique: true,
        required: true,
        trim: true,
    },
    employee_id: {
        type: String,
        unique: true,
        required: true,
        trim: true,
    },
    email: {
        type: String,
        required: true,
        unique: true,
        sparse: true,
    },
    name: {
        first: {
            type: String,
            trim: true,
        },
        middle: {
            type: String,
            trim: true,
        },
        last: {
            type: String,
            trim: true,
        },
        family: {
            type: String,
            trim: true,
        }
    },
    dob: Date,

    password: String,
    gender: {
        type: String,
        enum: [constant.GENDER.MALE, constant.GENDER.FEMALE]
    },
    mobile: {
        type: Number,
        unique: true,
        sparse: true,
    },
    _employee_id_doc: String,
    address: {
        nationality_id: String,
        _nationality_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.COUNTRY
        },
        _nationality_id_doc: String,
        building: String,
        city: String,
        district: String,
        zip_code: Number,
        unit: String,
        street_no: String,
        passport_no: {
            type: String
        },
        _address_doc: String
    },
    office: {
        office_extension: Number,
        office_room_no: String
    },
    qualifications: {
        degree: [{
            degree_name: String,
            _degree_doc: String,
        }],
        _appointment_order_doc: String,

        // _position_id: {
        //     type: Schemas.Types.ObjectId,
        //     ref: constant.POSITION
        // },

        // designation: String,
    },
    _role_id: [{
        type: Schemas.Types.ObjectId,
        ref: constant.ROLE
    }],
    academic_allocation: [{
        allocation_type: {
            type: String,
            enum: [constant.PRIMARY, constant.AUXILIARY]
        },
        _program_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.PROGRAM
        },
        _department_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.DEPARTMENT
        },
        _department_division_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.DEPARTMENT_DIVISIONS
        },
        _department_subject_id: [{
            type: Schemas.Types.ObjectId,
            ref: constant.DEPARTMENT_SUBJECT
        }],
    }],
    // availability_type: String,
    // availability_days: [{
    //     type: String
    // }],
    employment: {
        staff_type: {
            type: String,
            enum: [constant.ACADEMIC, constant.ADMINISTRATION, constant.BOTH]
        },
        institution_role: String,
        staff_employment_type: {
            type: String,
            enum: [constant.FULL_TIME, constant.PART_TIME]
        },
        staff_schedule_type: {
            tyep: String,
            enum: [constant.PART_TIME_DATE, constant.PART_TIME_DAY]
        },
        academic_year: String,

        schedule_times: {
            full_time: [{
                mode: {
                    type: String,
                    enum: [constant.ONLINE, constant.OFFLINE, constant.BOTH]
                },
                days: [{
                    type: String,
                    enum: [constant.DAYS.SUNDAY, constant.DAYS.MONDAY, constant.DAYS.TUESDAY, constant.DAYS.WEDNESDAY, constant.DAYS.THURSDAY, constant.DAYS.FRIDAY, constant.DAYS.SATURDAY]
                }],
                start_time: Date,
                end_time: Date
            }],
            by_date: [{
                start_date: Date,
                end_date: Date,
                schedule: [{
                    mode: {
                        type: String,
                        enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH]
                    },
                    days: [{
                        type: String,
                        enum: [constant.DAYS.SUNDAY, constant.DAYS.MONDAY, constant.DAYS.TUESDAY, constant.DAYS.WEDNESDAY, constant.DAYS.THURSDAY, constant.DAYS.FRIDAY, constant.DAYS.SATURDAY]
                    }],
                    start_time: Date,
                    end_time: Date
                }]
            }],
            by_day: [{
                mode: {
                    type: String,
                    enum: [constant.ONLINE, constant.OFFLINE, constant.BOTH]
                },
                days: {
                    type: String
                },
                start_time: Date,
                end_time: Date
            }]
        }
    },
    verification: { // verification status
        email: {
            type: Boolean,
            default: false,
        },
        mobile: {
            type: Boolean,
            default: false,
        },
        data: {
            type: String,
            default: constant.PENDING,
            enum: [constant.VALID, constant.INVALID, constant.PENDING, constant.CORRECTION_REQUIRED, constant.DONE],
        },
        face: { // facial
            type: Boolean,
            default: false,
        },
        finger: { // finger_print
            type: Boolean,
            default: false,
        }
    },
    correction: [{ type: String, default: null }],
    status: {
        type: String,
        // default: constant.ACTIVE,
        enum: [constant.IMPORTED, constant.PASSWORD_CONFIRMED, constant.PROFILE_UPDATED, constant.VERIFICATION_DONE, constant.DONE, constant.ACTIVE, constant.INACTIVE, constant.BLOCKED],
    },
    fcm_token: {
        type: String,
    },
    last_login: {
        type: Date,
    },
    otp: {
        no: {
            type: Number,
            default: 0,
        },
        expiry_date: {
            type: Date,
            default: Date.now(),
        },
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.STAFF, staffSchemas);