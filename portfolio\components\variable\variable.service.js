const VariableModel = require('./variable.model');

const { BadRequestError } = require('../../common/utils/api_error_util');

const createVariable = async ({
    programId,
    courseId,
    institutionCalendarId,
    name,
    type,
    variable,
}) => {
    const newVariable = await VariableModel.create({
        programId,
        courseId,
        institutionCalendarId,
        type,
        name,
        variable,
    });

    return {
        _id: newVariable._id,
        name: newVariable.name,
        type: newVariable.type,
        programId,
        courseId,
        institutionCalendarId,
    };
};

const getVariable = async ({ programId, courseId, institutionCalendarId, type }) => {
    const variable = await VariableModel.find(
        {
            ...(programId && { programId }),
            ...(courseId && { courseId }),
            ...(institutionCalendarId && { institutionCalendarId }),
            ...(type && { type }),
        },
        { type: 1, name: 1 },
    ).lean();

    return variable;
};

const updateVariable = async ({ variableId, name, type, variable }) => {
    const update = await VariableModel.updateOne({ _id: variableId }, { name, type, variable });
    if (!update.modifiedCount) {
        throw new BadRequestError('UPDATED_FAILED');
    }
};

const deleteVariable = async ({ variableId }) => {
    const variable = await VariableModel.deleteOne({ _id: variableId });
    if (!variable.deletedCount) {
        throw new BadRequestError('DELETED_FAILED');
    }

    return variable;
};

module.exports = {
    createVariable,
    getVariable,
    updateVariable,
    deleteVariable,
};
