const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const optionalObjectId = Joi.string().alphanum().length(24);
const { DS_PROGRAM_KEY, DS_COURSE_KEY, DS_INSTITUTION_KEY } = require('../../utility/constants');

exports.getPublishedSurveyCalendarListValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            userId: optionalObjectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getSurveyListValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'CALENDAR ID REQUIRED'),
            isTemplate: Joi.boolean(),
            surveyLevel: Joi.string()
                .valid(DS_PROGRAM_KEY, DS_COURSE_KEY, DS_INSTITUTION_KEY)
                .required()
                .error((error) => error),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getSinglePublishedSurveyDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            surveyId: objectId.error(() => 'SURVEY ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.updatedPublishedSurveyValidator = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            updateId: objectId.error(() => 'UPDATE ID REQUIRED'),
            updateType: Joi.string()
                .required()
                .error(() => 'UPDATE TYPE IS REQUIRED'),
            status: Joi.string().error(() => 'STATUS IS REQUIRED'),
            expiryDate: Joi.date().error(() => 'INVALID DATE TYPE'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getTotalPublishedSurveyCountValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'CALENDAR ID REQUIRED'),
            isTemplate: Joi.boolean()
                .required()
                .error(() => 'IS TEMPLATE IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getTemplateRunnerUserValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            templateId: objectId.error(() => 'TEMPLATE ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getTotalPublishedSurveyUserValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            templateId: objectId.error(() => 'TEMPLATE ID REQUIRED'),
            userId: objectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.createUserOutcomeReportResponseSettingValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
            _user_id: objectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            maxResponseSettings: Joi.array(),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserOutcomeReportResponseSettingValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
            _user_id: objectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.extendSurveyDurationValidator = Joi.object({
    query: Joi.object({
        surveyId: objectId.error((error) => error),
        expireDateAndTime: Joi.date()
            .required()
            .error((error) => error),
    }).unknown(true),
});
