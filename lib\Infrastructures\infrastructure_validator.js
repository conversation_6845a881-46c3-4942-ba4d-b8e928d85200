// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.infrastructure = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    // _institution_id: Joi.string().alphanum().length(24).error(error => {
                    //     return error;
                    // }),
                    name: Joi.string().required(),
                    location: Joi.string().required(),
                    type: Joi.string()
                        .valid(constant.BUILDING_TYPE.COLLEGE, constant.BUILDING_TYPE.HOSPITAL)
                        .required(),
                    floors: Joi.array().items(
                        Joi.object().keys({
                            floor_name: Joi.string(),
                            category: Joi.string().valid(
                                constant.FLOOR_CATEGORY.LEVEL,
                                constant.FLOOR_CATEGORY.BASEMENT,
                            ),
                        }),
                    ),
                    zones: Joi.array().items(Joi.string()),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.infrastructure_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
