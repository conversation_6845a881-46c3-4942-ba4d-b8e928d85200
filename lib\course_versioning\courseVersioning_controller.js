//schema
const curriculumSchema = require('../models/digi_curriculum');
const courseSchema = require('../models/digi_course');
const sessionOrderSchema = require('../models/digi_session_order');
const courseScheduleSchema = require('../models/course_schedule');
const programCalendarSchema = require('../models/program_calendar');
const studentGroupSchema = require('../models/student_group');
const { redisClient } = require('../../config/redis-connection');
//common functions
const { convertToMongoObjectId, query: commonQuery } = require('../utility/common');
const {
    DC_STAFF,
    DC_STUDENT,
    REDIS_FOLDERS: { USER_COURSES },
    PUBLISHED,
    COURSE_TYPE: { STANDARD, SELECTIVE },
} = require('../utility/constants');
const { getUserRoleProgramList } = require('../utility/utility.service');

exports.createCourseVersioning = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { defaultCourseId } = params;
        const { _institution_id } = headers;
        const { _curriculum_id } = body;
        //add current versionNumber
        const existingCourseVersionedNumber = await courseSchema
            .findOne(
                {
                    versionedFrom: convertToMongoObjectId(defaultCourseId),
                },
                { versionNo: 1 },
            )
            .sort({ createdAt: -1 })
            .lean()
            .exec();
        // get curriculum for the current course
        const curriculumData = await curriculumSchema
            .findOne(
                { _id: convertToMongoObjectId(_curriculum_id) },
                { framework: 1, curriculum_name: 1, _program_id: 1, program_name: 1 },
            )
            .lean();
        //update course assigned details automatically while creating course
        const courseAssignedLevels =
            body.course_type === 'standard'
                ? body.course_occurring
                : body.course_type === 'selective'
                ? body.course_recurring
                : [];
        const courseAssignedDetails = [];
        if (courseAssignedLevels.length) {
            for (const courseAssignedLevelElement of courseAssignedLevels) {
                courseAssignedDetails.push({
                    _curriculum_id: curriculumData._id,
                    curriculum_name: curriculumData.curriculum_name,
                    _program_id: curriculumData._program_id,
                    program_name: curriculumData.program_name,
                    _year_id: courseAssignedLevelElement._year_id,
                    year: courseAssignedLevelElement.year_no,
                    _level_id: courseAssignedLevelElement._level_id,
                    level_no: courseAssignedLevelElement.level_no,
                });
            }
        }
        // Update request body
        body._institution_id = _institution_id;
        body.framework =
            curriculumData && curriculumData.framework ? curriculumData.framework : { domains: [] };
        body.course_assigned_details = courseAssignedDetails;
        body.versionNo =
            existingCourseVersionedNumber && existingCourseVersionedNumber.versionNo
                ? existingCourseVersionedNumber.versionNo + 1
                : 2;
        const createdCourseDetail = await courseSchema.create(body);
        if (createdCourseDetail) {
            // If version course created successfully then update default course with versionedCourseIds
            const masterCourseDetail = await courseSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(defaultCourseId),
                        isDeleted: false,
                    },
                    { versionedCourseIds: 1 },
                )
                .lean();
            if (masterCourseDetail) {
                const updateQuery =
                    'versionedCourseIds' in masterCourseDetail
                        ? { $push: { versionedCourseIds: createdCourseDetail._id } }
                        : {
                              $set: {
                                  versionedCourseIds: [createdCourseDetail._id],
                                  versionNo: 1,
                                  versioned: false,
                                  versionName: 'default',
                              },
                          };

                await courseSchema.updateOne(
                    {
                        _id: convertToMongoObjectId(defaultCourseId),
                        isDeleted: false,
                    },
                    updateQuery,
                );
            }
            return {
                statusCode: 201,
                message: 'VERSIONED_COURSE_ADDED_SUCCESSFULLY',
                data: createdCourseDetail,
            };
        }
        return {
            statusCode: 410,
            message: 'UNABLE_TO_ADD_VERSIONED_COURSE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.viewCourseDetails = async ({ params = {} }) => {
    try {
        const { courseId } = params;
        const courseDetail = await courseSchema
            .findOne(
                { _id: convertToMongoObjectId(courseId) },
                {
                    isDeleted: 0,
                    isActive: 0,
                    achieve_target: 0,
                    createdAt: 0,
                    updatedAt: 0,
                    __v: 0,
                },
            )
            .populate({ path: 'versionedCourseIds', select: { versionName: 1 } })
            .lean();
        if (!courseDetail) {
            return {
                statusCode: 404,
                message: 'UNABLE_TO_GET_COURSE_DETAILS',
            };
        }
        // get session order details
        const sessionOrderDetails = await sessionOrderSchema
            .findOne(
                {
                    ...commonQuery,
                    _course_id: convertToMongoObjectId(courseDetail._id),
                },
                { session_flow_data: 1, additional_session_flow_data: 1 },
            )
            .lean();
        const responseObj = {
            basicDetails: courseDetail,
            sessionFlowDetails: sessionOrderDetails || [],
            versionCourseDetails: [],
        };
        // If its default course then its has total versioned course details
        if (!courseDetail.versioned) {
            const courseVersionedDetails = courseDetail.versionedCourseIds;
            if (courseVersionedDetails && courseVersionedDetails.length) {
                responseObj.versionCourseDetails.push(...courseVersionedDetails, {
                    _id: courseDetail._id,
                    versionName: courseDetail.versionName,
                });
            }
            return {
                statusCode: 200,
                message: 'COURSE_DETAILS',
                data: responseObj,
            };
        }
        // If its versioned course then get default course for total versioned course details
        if (courseDetail.versioned && courseDetail.versionedFrom) {
            const defaultCourseDetail = await courseSchema
                .findOne(
                    { _id: convertToMongoObjectId(courseDetail.versionedFrom) },
                    { versionedCourseIds: 1, versionName: 1 },
                )
                .populate({ path: 'versionedCourseIds', select: { versionName: 1 } })
                .lean();
            const defaultCourseVersionedDetails = defaultCourseDetail.versionedCourseIds || [];
            if (defaultCourseVersionedDetails && defaultCourseVersionedDetails.length) {
                responseObj.versionCourseDetails.push(...defaultCourseVersionedDetails, {
                    _id: defaultCourseDetail._id,
                    versionName: defaultCourseDetail.versionName,
                });
            }
        }
        return {
            statusCode: 200,
            message: 'COURSE_DETAILS',
            data: responseObj,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.editCourseDetails = async ({ params = {}, body = {} }) => {
    try {
        const { courseId } = params;
        if (body?.course_code) {
            const isDuplicateCourseData = await courseSchema.findOne({
                _id: { $ne: convertToMongoObjectId(courseId) },
                course_code: { $regex: `^${body.course_code}$`, $options: 'i' },
                isDeleted: false,
                isActive: true,
            });
            if (isDuplicateCourseData) {
                return { statusCode: 410, message: 'DUPLICATE_COURSE_CODE' };
            }
        }
        const checkCourseAssignedDetails = await courseSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(courseId),
                },
                { course_assigned_details: 1 },
            )
            .lean();
        let course_assigned_details = checkCourseAssignedDetails?.course_assigned_details || [];
        const curriculumData = await curriculumSchema
            .findOne(
                { _id: convertToMongoObjectId(body?._curriculum_id) },
                { curriculum_name: 1, _program_id: 1, program_name: 1 },
            )
            .lean();
        //update course assigned details automatically while creating course
        const courseAssignedLevels =
            body.course_type === STANDARD
                ? body.course_occurring
                : body.course_type === SELECTIVE
                ? body.course_recurring
                : [];
        if (courseAssignedLevels?.length) {
            course_assigned_details = course_assigned_details.filter((assignedElement) =>
                courseAssignedLevels.some(
                    (levelElement) =>
                        String(levelElement.level_no) === String(assignedElement.level_no),
                ),
            );
            courseAssignedLevels.forEach((courseAssignedLevelElement) => {
                if (
                    !course_assigned_details.some(
                        (assigned) =>
                            String(assigned.level_no) ===
                            String(courseAssignedLevelElement.level_no),
                    )
                ) {
                    course_assigned_details.push({
                        _curriculum_id: curriculumData._id,
                        curriculum_name: curriculumData.curriculum_name,
                        _program_id: curriculumData._program_id,
                        program_name: curriculumData.program_name,
                        _year_id: courseAssignedLevelElement._year_id,
                        year: courseAssignedLevelElement.year_no,
                        _level_id: courseAssignedLevelElement._level_id,
                        level_no: courseAssignedLevelElement.level_no,
                    });
                }
            });
        }
        body.course_assigned_details = course_assigned_details;
        const updatedCourseDetails = await courseSchema
            .findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(courseId),
                },
                body,
                {
                    projection: {
                        course_name: 1,
                        course_code: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedCourseIds: 1,
                    },
                },
            )
            .lean();
        if (!updatedCourseDetails) {
            return {
                statusCode: 410,
                message: 'UNABLE_TO_UPDATE_COURSE',
            };
        }
        const {
            course_name,
            course_code,
            versioned,
            versionName,
            versionedCourseIds = [],
        } = updatedCourseDetails;
        // if default course name and course code edited then change to all (versioned courses,programCalendar,studentGroup,courseSchedule)
        if (
            !versioned &&
            versionName === 'default' &&
            (course_name.trim() !== body.course_name.trim() ||
                course_code.trim() !== body.course_code.trim())
        ) {
            //update versioned course
            if (versionedCourseIds.length) {
                await courseSchema.updateMany(
                    { _id: { $in: versionedCourseIds } },
                    {
                        $set: {
                            course_name: body.course_name,
                            course_code: body.course_code,
                        },
                    },
                );
            }
            //update course schedule
            await courseScheduleSchema.updateMany(
                { _course_id: convertToMongoObjectId(courseId) },
                {
                    $set: {
                        course_name: body.course_name,
                        course_code: body.course_code,
                    },
                },
            );
            // update program Calendar
            const courseDetailsFromProgramCalendar = await programCalendarSchema
                .find(
                    {
                        'level.course._course_id': convertToMongoObjectId(courseId),
                    },
                    {
                        'level._id': 1,
                        'level.course._id': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        'level.course.courses_number': 1,
                    },
                )
                .lean();
            if (courseDetailsFromProgramCalendar && courseDetailsFromProgramCalendar.length) {
                const programCalendarBulkUpdate = [];
                for (const programCalendarElement of courseDetailsFromProgramCalendar) {
                    for (const programCalendarLevelElement of programCalendarElement.level) {
                        for (const programCalendarCourseElement of programCalendarLevelElement.course) {
                            if (
                                programCalendarCourseElement._course_id.toString() ===
                                courseId.toString()
                            ) {
                                programCalendarBulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoObjectId(programCalendarElement._id),
                                        },
                                        update: {
                                            $set: {
                                                'level.$[i].course.$[j].courses_name':
                                                    body.course_name,
                                                'level.$[i].course.$[j].courses_number':
                                                    body.course_code,
                                            },
                                        },
                                        arrayFilters: [
                                            { 'i._id': programCalendarLevelElement._id },
                                            { 'j._id': programCalendarCourseElement._id },
                                        ],
                                    },
                                });
                            }
                        }
                    }
                }
                await programCalendarSchema.bulkWrite(programCalendarBulkUpdate);
            }
            //update Student grouping
            const courseDetailsFomStudentGroup = await studentGroupSchema
                .find(
                    {
                        'groups.courses._course_id': convertToMongoObjectId(courseId),
                    },
                    {
                        'groups._id': 1,
                        'groups.courses._id': 1,
                        'groups.courses._course_id': 1,
                        'groups.courses.course_name': 1,
                        'groups.courses.course_no': 1,
                    },
                )
                .lean();
            if (courseDetailsFomStudentGroup && courseDetailsFomStudentGroup.length) {
                const studentGroupBulkUpdate = [];
                for (const courseDetailsFomStudentGroupELement of courseDetailsFomStudentGroup) {
                    for (const studentGroupElement of courseDetailsFomStudentGroupELement.groups) {
                        for (const studentGroupCourseElement of studentGroupElement.courses) {
                            if (
                                studentGroupCourseElement._course_id.toString() ===
                                courseId.toString()
                            )
                                studentGroupBulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoObjectId(
                                                courseDetailsFomStudentGroupELement._id,
                                            ),
                                        },
                                        update: {
                                            $set: {
                                                'groups.$[i].courses.$[j].course_name':
                                                    body.course_name,
                                                'groups.$[i].courses.$[j].course_no':
                                                    body.course_code,
                                            },
                                        },
                                        arrayFilters: [
                                            { 'i._id': studentGroupElement._id },
                                            { 'j._id': studentGroupCourseElement._id },
                                        ],
                                    },
                                });
                        }
                    }
                }
                await studentGroupSchema.bulkWrite(studentGroupBulkUpdate);
            }
        }
        return {
            statusCode: 201,
            message: 'COURSE_UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteCourseDetails = async ({ query = {} }) => {
    try {
        const {
            courseId,
            defaultCourseId,
            changeDefaultCourse = 'false',
            newDefaultCourseId = null,
        } = query;
        // If default course deleted change some other versioned course as default
        if (changeDefaultCourse === 'true' && newDefaultCourseId) {
            const defaultCourse = await courseSchema
                .findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(courseId),
                    },
                    { isDeleted: true },
                    {
                        new: true,
                        projection: {
                            versioned: 1,
                            versionName: 1,
                            versionedFrom: 1,
                            versionedCourseIds: 1,
                        },
                    },
                )
                .lean();
            if (defaultCourse) {
                const bulkUpdate = [];
                if (defaultCourse.versionedCourseIds && defaultCourse.versionedCourseIds.length) {
                    const { versioned, versionName, versionedFrom, versionedCourseIds } =
                        defaultCourse;
                    for (const versionedCourseIdElement of versionedCourseIds) {
                        const updateOne = {
                            filter: {
                                isDeleted: false,
                                _id: convertToMongoObjectId(versionedCourseIdElement),
                            },
                        };
                        if (versionedCourseIdElement.toString() === newDefaultCourseId.toString()) {
                            const courseIdsExceptNewDefaultCourse = versionedCourseIds.filter(
                                (courseIdElement) =>
                                    courseIdElement.toString() !== newDefaultCourseId.toString(),
                            );
                            updateOne.update = {
                                versioned,
                                versionName,
                                versionedFrom,
                                versionedCourseIds: courseIdsExceptNewDefaultCourse,
                            };
                        } else {
                            updateOne.update = {
                                versionedFrom: newDefaultCourseId,
                            };
                        }
                        bulkUpdate.push({ updateOne });
                    }
                }
                // change default course and other versioned course details
                if (bulkUpdate.length) {
                    await courseSchema.bulkWrite(bulkUpdate);
                }
                return {
                    statusCode: 200,
                    message: 'COURSE_DELETED_SUCCESSFULLY',
                };
            }
            return {
                statusCode: 410,
                message: 'UNABLE_TO_DELETE_COURSE',
            };
        }
        const deletedCourseDetail = await courseSchema.updateOne(
            {
                _id: convertToMongoObjectId(courseId),
            },
            { isDeleted: true },
        );
        if (deletedCourseDetail.nModified) {
            await courseSchema.updateOne(
                { _id: convertToMongoObjectId(defaultCourseId) },
                { $pull: { versionedCourseIds: courseId } },
            );
            return {
                statusCode: 200,
                message: 'COURSE_DELETED_SUCCESSFULLY',
            };
        }
        return {
            statusCode: 410,
            message: 'UNABLE_TO_DELETE_COURSE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteCourse = async ({ query = {} }) => {
    try {
        const { courseId, isVersioned = 'false', defaultCourseId = '' } = query;
        const isVersionedCourse = isVersioned.toLowerCase() === 'true';
        const courseFilter = { _id: convertToMongoObjectId(courseId) };
        const currentCourseDetails = await courseSchema
            .findOne(courseFilter, {
                versionedCourseIds: 1,
                course_assigned_details: 1,
            })
            .lean();
        // check if course have been assigned and configured in levels.
        if (
            currentCourseDetails &&
            currentCourseDetails.course_assigned_details &&
            currentCourseDetails.course_assigned_details.length
        ) {
            const isCourseConfigured = currentCourseDetails.course_assigned_details.some(
                (courseAssignedElement) => {
                    if (courseAssignedElement.hasOwnProperty('isConfigured')) {
                        return courseAssignedElement.isConfigured;
                    }
                    return true;
                },
            );
            if (isCourseConfigured) {
                return {
                    statusCode: 410,
                    message: `Unable to delete, Course have been configured in levels`,
                };
            }
        }
        // If its default course, check if it has versioned courses
        if (!isVersionedCourse) {
            if (
                currentCourseDetails &&
                currentCourseDetails.versionedCourseIds &&
                currentCourseDetails.versionedCourseIds.length
            ) {
                return {
                    statusCode: 410,
                    message: `Unable to delete, Default course have ${currentCourseDetails.versionedCourseIds.length} versioned course`,
                };
            }
            const deleteDefaultCourse = await courseSchema.updateOne(courseFilter, {
                isDeleted: true,
            });
            if (deleteDefaultCourse.modifiedCount) {
                return {
                    statusCode: 200,
                    message: 'COURSE_DELETED_SUCCESSFULLY',
                };
            }
        }
        // If its versioned course, remove versioned course id from default course
        if (isVersionedCourse && defaultCourseId) {
            const deleteVersionedCourse = await courseSchema.updateOne(courseFilter, {
                isDeleted: true,
            });
            if (deleteVersionedCourse.modifiedCount) {
                await courseSchema.updateOne(
                    { _id: convertToMongoObjectId(defaultCourseId) },
                    { $pull: { versionedCourseIds: courseId } },
                );
                return {
                    statusCode: 200,
                    message: 'COURSE_DELETED_SUCCESSFULLY',
                };
            }
        }
        return {
            statusCode: 410,
            message: 'UNABLE_TO_DELETE_COURSE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.programCourseList = async ({ query = {} }) => {
    try {
        const { program_id, curriculum_id } = query;
        const courseList = await courseSchema
            .find(
                {
                    isDeleted: false,
                    $or: [
                        {
                            _program_id: convertToMongoObjectId(program_id),
                            _curriculum_id: convertToMongoObjectId(curriculum_id),
                        },
                        {
                            'course_assigned_details.course_shared_with._program_id':
                                convertToMongoObjectId(program_id),
                            'course_assigned_details.course_shared_with._curriculum_id':
                                convertToMongoObjectId(curriculum_id),
                        },
                    ],
                },
                {
                    course_code: 1,
                    course_name: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    duration: 1,
                    course_type: 1,
                    'course_assigned_details.isConfigured': 1,
                    'course_assigned_details.level_no': 1,
                    'course_assigned_details.course_shared_with._program_id': 1,
                    'course_assigned_details.course_shared_with.level_no': 1,
                    'course_assigned_details.course_shared_with._curriculum_id': 1,
                    course_occurring: 1,
                    course_recurring: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                    isActive: 1,
                    staffIds: 1,
                },
            )
            .populate({ path: 'staffIds', select: { name: 1, user_id: 1 } })
            .lean();
        if (courseList && courseList.length) {
            const programCourseList = courseList.map((courseListElement) => {
                if (courseListElement._program_id && courseListElement._curriculum_id) {
                    if (
                        courseListElement._program_id.toString() === program_id.toString() &&
                        courseListElement._curriculum_id.toString() === curriculum_id.toString()
                    ) {
                        return {
                            ...courseListElement,
                            shared_with_others: false,
                        };
                    }
                    if (
                        courseListElement.course_assigned_details &&
                        courseListElement.course_assigned_details.length
                    ) {
                        for (const courseSharedElement of courseListElement.course_assigned_details) {
                            const assignedCourseDetail =
                                courseSharedElement.course_shared_with.find(
                                    (courseSharedWithElement) =>
                                        courseSharedWithElement._program_id &&
                                        courseSharedWithElement._curriculum_id &&
                                        courseSharedWithElement._program_id.toString() ===
                                            program_id.toString() &&
                                        courseSharedWithElement._curriculum_id.toString() ===
                                            curriculum_id.toString(),
                                );
                            if (assignedCourseDetail) {
                                return {
                                    ...courseListElement,
                                    shared_with_others: true,
                                };
                            }
                        }
                    }
                }
                return courseListElement;
            });

            return {
                statusCode: 200,
                message: 'COURSE_LIST',
                data: programCourseList,
            };
        }
        return {
            statusCode: 404,
            message: 'COURSE_LIST_NOT_FOUND',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.checkDuplicateVersionName = async ({ query = {} }) => {
    try {
        const { versionName, versionedFrom } = query;
        const versionedCourseNames = await courseSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(versionedFrom),
                },
                { versionedCourseIds: 1 },
            )
            .populate({ path: 'versionedCourseIds', select: { versionName: 1 } })
            .lean();
        const isDuplicateVersionName =
            versionedCourseNames.versionedCourseIds &&
            versionedCourseNames.versionedCourseIds.some(
                (versionedNameElement) =>
                    versionedNameElement.versionName.trim().toLowerCase() ===
                    versionName.trim().toLowerCase(),
            );
        return {
            statusCode: 200,
            message: 'DUPLICATE_VERSION_NAME',
            data: {
                isDuplicateVersionName,
                versionName,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getStudentMultiCalendarCourseIds = async ({
    inistitutionCalendarIds,
    _institution_id,
    userId,
}) => {
    const convertedCalendarIds = inistitutionCalendarIds.map((calendaIdElement) =>
        convertToMongoObjectId(calendaIdElement),
    );
    let scheduleDatasAggregate = await courseScheduleSchema.aggregate([
        {
            $match: {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: { $in: convertedCalendarIds },
                'students._id': convertToMongoObjectId(userId),
                isDeleted: false,
            },
        },
        {
            $group: {
                _id: {
                    _program_id: '$_program_id',
                    term: '$term',
                    _course_id: '$_course_id',
                    year_no: '$year_no',
                    level_no: '$level_no',
                    rotation_count: '$rotation_count',
                },
                program_name: { $first: '$program_name' },
            },
        },
    ]);
    const studentStudentGroupAggregate = await studentGroupSchema.aggregate([
        {
            $match: {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: { $in: convertedCalendarIds },
                'groups.courses.setting.session_setting.groups._student_ids':
                    convertToMongoObjectId(userId),
            },
        },
        { $unwind: { path: '$groups' } },
        { $unwind: { path: '$groups.courses' } },
        { $unwind: { path: '$groups.courses.setting' } },
        {
            $match: {
                'groups.courses.setting.session_setting.groups._student_ids':
                    convertToMongoObjectId(userId),
            },
        },
        {
            $group: {
                _id: {
                    _program_id: '$master._program_id',
                    term: '$groups.term',
                    level_no: '$groups.level',
                    rotation: '$groups.rotation',
                    rotation_count: '$groups.courses.setting._group_no',
                    _course_id: '$groups.courses._course_id',
                    year_no: '$master.year',
                },
                program_name: { $first: '$master.program_name' },
            },
        },
    ]);
    if (studentStudentGroupAggregate) {
        scheduleDatasAggregate = [...scheduleDatasAggregate, ...studentStudentGroupAggregate];
    }
    const programCalendarData = await programCalendarSchema
        .find(
            {
                _institution_calendar_id: { $in: convertedCalendarIds },
                isDeleted: false,
                status: PUBLISHED,
                _program_id: {
                    $in: scheduleDatasAggregate.map(
                        (scheduleElement) => scheduleElement._id._program_id,
                    ),
                },
            },
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.rotation': 1,
                'level.course._course_id': 1,
                'level.course.courses_name': 1,
                'level.course.courses_number': 1,
                'level.course.model': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.courses_name': 1,
                'level.rotation_course.course.courses_number': 1,
                'level.rotation_course.course.model': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        )
        .populate({
            path: 'level.course._course_id',
            select: {
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        })
        .populate({
            path: 'level.rotation_course.course._course_id',
            select: {
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        })
        .lean();
    const userCourseList = [];
    for (programCalendarElement of programCalendarData) {
        for (levelElement of programCalendarElement.level) {
            if (levelElement.rotation === 'no') {
                for (courseElement of levelElement.course) {
                    const scheduleCourse = scheduleDatasAggregate.find(
                        (scheduleElement) =>
                            scheduleElement._id._program_id.toString() ===
                                programCalendarElement._program_id.toString() &&
                            scheduleElement._id.term === levelElement.term &&
                            scheduleElement._id.year_no === levelElement.year &&
                            scheduleElement._id.level_no === levelElement.level_no &&
                            scheduleElement._id._course_id.toString() ===
                                courseElement._course_id._id.toString(),
                    );
                    if (scheduleCourse) {
                        userCourseList.push({
                            _institution_calendar_id:
                                programCalendarElement._institution_calendar_id,
                            _program_id: programCalendarElement._program_id.toString(),
                            _id: courseElement._course_id._id,
                            year: levelElement.year,
                            level: levelElement.level_no,
                            term: levelElement.term,
                            course_code: courseElement.courses_number,
                            course_name: courseElement.courses_name,
                            course_type: courseElement.model,
                            versionNo: courseElement._course_id.versionNo || 1,
                            versioned: courseElement._course_id.versioned || false,
                            versionName: courseElement._course_id.versionName || '',
                            versionedFrom: courseElement._course_id.versionedFrom || null,
                            versionedCourseIds: courseElement._course_id.versionedCourseIds || [],
                            start_date: courseElement.start_date,
                            end_date: courseElement.end_date,
                            program_name: scheduleCourse.program_name,
                            rotation: levelElement.rotation,
                        });
                    }
                }
            } else
                for (rotationElement of levelElement.rotation_course) {
                    for (courseElement of rotationElement.course) {
                        const scheduleCourse = scheduleDatasAggregate.find(
                            (scheduleElement) =>
                                scheduleElement._id.rotation_count ===
                                    rotationElement.rotation_count &&
                                scheduleElement._id._program_id.toString() ===
                                    programCalendarElement._program_id.toString() &&
                                scheduleElement._id.term === levelElement.term &&
                                scheduleElement._id.year_no === levelElement.year &&
                                scheduleElement._id.level_no === levelElement.level_no &&
                                scheduleElement._id._course_id.toString() ===
                                    courseElement._course_id._id.toString(),
                        );
                        if (scheduleCourse) {
                            userCourseList.push({
                                _institution_calendar_id:
                                    programCalendarElement._institution_calendar_id,
                                _program_id: programCalendarElement._program_id.toString(),
                                _id: courseElement._course_id._id,
                                year: levelElement.year,
                                level: levelElement.level_no,
                                term: levelElement.term,
                                course_code: courseElement.courses_number,
                                course_name: courseElement.courses_name,
                                course_type: courseElement.model,
                                versionNo: courseElement._course_id.versionNo || 1,
                                versioned: courseElement._course_id.versioned || false,
                                versionName: courseElement._course_id.versionName || '',
                                versionedFrom: courseElement._course_id.versionedFrom || null,
                                versionedCourseIds:
                                    courseElement._course_id.versionedCourseIds || [],
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                program_name: scheduleCourse.program_name,
                                rotation: levelElement.rotation,
                                rotation_count: rotationElement.rotation_count,
                            });
                        }
                    }
                }
        }
    }
    return userCourseList;
};
const getStudentCourseIds = async ({ inistitutionCalendarIds, _institution_id, userId }) => {
    try {
        // construct redis key for student course details
        const studentCourseRediKey = inistitutionCalendarIds.map(
            (inistitutionCalendarElement) =>
                `${USER_COURSES}:${userId}-${inistitutionCalendarElement}`,
        );
        const studentCoursesCache = await redisClient.Client.mget(...studentCourseRediKey);
        let studentCurrentCourseIds = [];
        const filteredStudentCourseCache = [];
        const missingCalendaIds = [];
        //check if cache reterived for all requested calendars
        for (const [index, studentCoursesCacheElement] of studentCoursesCache.entries()) {
            if (studentCoursesCacheElement !== null) {
                const parseData = JSON.parse(studentCoursesCacheElement);
                filteredStudentCourseCache.push(...parseData);
            } else {
                missingCalendaIds.push(inistitutionCalendarIds[index]);
            }
        }
        studentCurrentCourseIds = [...filteredStudentCourseCache];
        // if all requested calendars reterived from cache - return directly
        if (!missingCalendaIds.length) {
            return { studentCurrentCourseIds };
        }
        // if any calendar is missed get data for missed calendar from DB and update in cache
        inistitutionCalendarIds =
            missingCalendaIds.length === inistitutionCalendarIds.length
                ? [...inistitutionCalendarIds]
                : [...missingCalendaIds];
        const studentCourseForCurrentCalendars = await getStudentMultiCalendarCourseIds({
            inistitutionCalendarIds,
            _institution_id,
            userId,
        });
        if (studentCourseForCurrentCalendars && studentCourseForCurrentCalendars.length) {
            const calendaBasedCourseIds = new Map();
            for (const studentCurrentCourseElement of studentCourseForCurrentCalendars) {
                const calendarId = studentCurrentCourseElement._institution_calendar_id.toString();
                if (!calendaBasedCourseIds.get(calendarId)) {
                    calendaBasedCourseIds.set(calendarId, [studentCurrentCourseElement]);
                } else {
                    calendaBasedCourseIds.get(calendarId).push(studentCurrentCourseElement);
                }
            }
            const msetRedisArguments = inistitutionCalendarIds
                .map((inistitutionCalendarElement) => {
                    const currentCalendarCourse = calendaBasedCourseIds.get(
                        inistitutionCalendarElement,
                    );
                    if (currentCalendarCourse) {
                        const key = `${USER_COURSES}:${userId}-${inistitutionCalendarElement}`;
                        const value = JSON.stringify(currentCalendarCourse);
                        return [key, value];
                    }
                })
                .filter((removeNullValues) => removeNullValues);
            await redisClient.Client.mset(...msetRedisArguments.flat());
            studentCurrentCourseIds = [
                ...studentCurrentCourseIds,
                ...studentCourseForCurrentCalendars,
            ];
        }
        return { studentCurrentCourseIds };
    } catch (error) {
        console.log(`Error in getStudentCourseIds - ${error}`);
        return {
            studentCurrentCourseIds: [],
        };
    }
};
const getStaffCourseIds = async ({ inistitutionCalendarIds, userId, roleId, _institution_id }) => {
    try {
        const { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin } =
            await getUserRoleProgramList({
                _institution_id,
                user_id: userId,
                role_id: roleId,
                institutionCalendarId: inistitutionCalendarIds,
            });
        if (isProgramAdmin && userProgramIds.length) {
            return { isProgramAdmin, staffCurrentCourseIds: userProgramIds };
        }
        if (isCourseAdmin && userCourseIds.length) {
            return {
                isCourseAdmin,
                staffCurrentCourseIds: userCourseIds,
            };
        }
        const staffScheduledCourseIds = await courseScheduleSchema
            .distinct('_course_id', {
                isDeleted: false,
                isActive: true,
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: { $in: inistitutionCalendarIds },
                'staffs._staff_id': convertToMongoObjectId(userId),
            })
            .lean();
        return {
            staffCurrentCourseIds: staffScheduledCourseIds,
        };
    } catch (error) {
        console.log(`Error in getStaffCourseIds - ${error}`);
        return {
            isProgramAdmin: false,
            isCourseAdmin: false,
            staffCurrentCourseIds: [],
        };
    }
};
const getCourseNameWithVersionedDetails = ({ courseDetails }) => {
    const construcedCourseWithVersionedDetails = new Map();
    for (const courseVersionedDetailElement of courseDetails) {
        const {
            course_name: courseName = '',
            versionNo = 1,
            versioned = false,
            versionName = '',
            versionedFrom = null,
            versionedCourseIds = [],
        } = courseVersionedDetailElement;
        const courseId =
            courseVersionedDetailElement._id || courseVersionedDetailElement._course_id || '';
        construcedCourseWithVersionedDetails.set(courseId.toString(), {
            versionNo,
            courseName,
            versioned,
            versionName,
            versionedFrom,
            versionedCourseIds,
        });
    }
    return construcedCourseWithVersionedDetails;
};
exports.getVersionedCourseDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { inistitutionCalendarIds, userId, userType, roleId } = query;
        const { _institution_id } = headers;
        let construcedCourseDetails = new Map();
        let searchQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        if (userType === DC_STAFF) {
            const {
                staffCurrentCourseIds = [],
                isProgramAdmin = false,
                isCourseAdmin = false,
            } = await getStaffCourseIds({
                inistitutionCalendarIds,
                userId,
                roleId,
                _institution_id,
            });
            if (staffCurrentCourseIds.length) {
                if (!isCourseAdmin) {
                    searchQuery = isProgramAdmin
                        ? { ...searchQuery, _program_id: { $in: staffCurrentCourseIds } }
                        : { ...searchQuery, _id: { $in: staffCurrentCourseIds } };
                    const staffCourseDetails = await courseSchema
                        .find(searchQuery, {
                            course_name: 1,
                            versionNo: 1,
                            versioned: 1,
                            versionName: 1,
                            versionedFrom: 1,
                            versionedCourseIds: 1,
                        })
                        .lean();
                    if (staffCourseDetails && staffCourseDetails.length) {
                        construcedCourseDetails = await getCourseNameWithVersionedDetails({
                            courseDetails: staffCourseDetails,
                        });
                    }
                } else {
                    // for courseAdmin we already have course details - (no need for course DB Find)
                    construcedCourseDetails = await getCourseNameWithVersionedDetails({
                        courseDetails: staffCurrentCourseIds,
                    });
                }
            }
        }
        if (userType === DC_STUDENT) {
            const { studentCurrentCourseIds = [] } = await getStudentCourseIds({
                inistitutionCalendarIds,
                _institution_id,
                userId,
            });
            // In studentCurrentCourseIds we have all student course details
            if (studentCurrentCourseIds && studentCurrentCourseIds.length) {
                construcedCourseDetails = await getCourseNameWithVersionedDetails({
                    courseDetails: studentCurrentCourseIds,
                });
            }
        }
        return {
            statusCode: 200,
            message: 'Versioned Course Details',
            data: Object.fromEntries(construcedCourseDetails),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
