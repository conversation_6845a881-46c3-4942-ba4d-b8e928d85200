.rules_staging:
  rules:
    - if: $CI_COMMIT_BRANCH == "staging"
      when: manual

build staging:
  stage: build
  environment: staging
  interruptible: true
  extends:
    - .rules_staging
    - .build
  variables:
    ECR_STAGING: $ECR:staging
  script:
    - docker build --tag $ECR_STAGING .
    - docker push $ECR_STAGING

deploy to staging:
  stage: deploy
  environment: staging
  extends:
    - .aws_image
    - .ecs_update_service
