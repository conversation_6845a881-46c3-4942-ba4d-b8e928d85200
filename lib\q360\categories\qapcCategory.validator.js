const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const { TAG_LEVEL, ANNUAL, PERIODIC, FORM, TEMPLATE } = require('../../utility/constants');

exports.createCategoryConfigureValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
            _user_id: Joi.string()
                .alphanum()
                .optional()
                .length(24)
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                categoryName: Joi.string()
                    .optional()
                    .error(() => {
                        return 'CATEGORY_NAME_MUST_BE_STRING';
                    }),
                level: Joi.string()
                    .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                    .optional()
                    .error(() => {
                        return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PROGRAM,COURSE,INSTITUTION';
                    }),
                describe: Joi.string()
                    .optional()
                    .allow('')
                    .error(() => {
                        return 'DESCRIBE_MUST_BE_STRING';
                    }),
                categoryFor: Joi.string()
                    .valid(ANNUAL, PERIODIC)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ANNUAL,PERIODIC';
                    }),
                periodicInterval: Joi.number()
                    .optional()
                    .error(() => {
                        return 'PERIODIC_INTERVAL_MUST_BE_NUMBER';
                    }),
                template: Joi.array()
                    .items(
                        Joi.object().keys({
                            isDefault: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            formName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'FORM_NAME_MUST_BE_STRING';
                                }),
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                        }),
                    )
                    .optional(),
                actions: Joi.object()
                    .keys({
                        studentGroups: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        everyAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        occurrenceConfiguration: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        academicTerms: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        attemptType: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        incorporateMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        displayMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                    })
                    .optional(),
                categoryFormType: Joi.string()
                    .valid(FORM, TEMPLATE)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:FORM, TEMPLATE';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getConfigureTemplateValidator = Joi.object().keys({
    Headers: Joi.object()
        .keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        })
        .unknown(true),
});

exports.updateCategoryConfigureValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .optional()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                categoryName: Joi.string()
                    .optional()
                    .error(() => {
                        return 'CATEGORY_NAME_MUST_BE_STRING';
                    }),
                level: Joi.string()
                    .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                    .optional()
                    .error(() => {
                        return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PROGRAM,COURSE,INSTITUTION';
                    }),
                describe: Joi.string()
                    .optional()
                    .allow('')
                    .error(() => {
                        return 'DESCRIBE_MUST_BE_STRING';
                    }),
                categoryFor: Joi.string()
                    .valid(ANNUAL, PERIODIC)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ANNUAL,PERIODIC';
                    }),
                periodicInterval: Joi.number()
                    .optional()
                    .error(() => {
                        return 'PERIODIC_INTERVAL_MUST_BE_NUMBER';
                    }),
                isConfigure: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                isActive: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                isDefault: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                template: Joi.array()
                    .items(
                        Joi.object().keys({
                            isDefault: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            formName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'FORM_NAME_MUST_BE_STRING';
                                }),
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                        }),
                    )
                    .optional(),
                actions: Joi.object()
                    .keys({
                        studentGroups: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        everyAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        occurrenceConfiguration: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        academicTerms: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        attemptType: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        incorporateMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        displayMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                    })
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.singleCategoryConfigValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
