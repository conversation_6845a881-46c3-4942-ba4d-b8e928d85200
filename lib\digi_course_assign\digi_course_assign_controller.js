const constant = require('../utility/constants');
const digi_course_assign = require('mongoose').model(constant.DIGI_COURSE_ASSIGN);
const institution = require('mongoose').model(constant.INSTITUTION);
const digi_session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;

async function insert(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );

        const doc = await base_control.insert(digi_course_assign, req.body);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        'Course assigned successfully',
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(common_files.response_function(res, 200, false, 'Unable to assign course', []));
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function update(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(digi_course_assign, query, req.body);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        'Course assign updated successfully',
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    'Unable to update course assign',
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function update_session_flow(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );

        const week_data = req.body.week_update;
        let doc = '';
        let query = '';
        for (let i = 0; i < week_data.length; i++) {
            query = { _id: week_data[i].session_flow_id };
            obj = {
                $set: { 'week.$[i].week_no': week_data[i].week },
            };
            filter = {
                arrayFilters: [{ 'i._id': req.body.level_id }],
            };
            doc = await base_control.update_condition_array_filter(
                digi_session_order,
                query,
                obj,
                filter,
            );
        }

        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        'Session flow updated successfully',
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    'Unable to update session flow',
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function list(req, res) {
    try {
        /* let query = { isDeleted: false, _program_id: req.params.program_id, _curriculum_id: req.params.curriculum_id, _year_id: req.params.year_id, _level_id: req.params.level_id };
        let doc = await base_control.get_list(digi_course_assign, query, {}); */

        const aggre = [
            {
                $match: {
                    isDeleted: false,
                    _program_id: ObjectId(req.params.program_id),
                    _curriculum_id: ObjectId(req.params.curriculum_id),
                    _year_id: ObjectId(req.params.year_id),
                    _level_id: ObjectId(req.params.level_id),
                },
            },
            {
                $lookup: {
                    from: 'digi_courses',
                    localField: '_course_id',
                    foreignField: '_id',
                    as: 'course_data',
                },
            },
        ];
        console.log(aggre);
        const doc = await base_control.get_aggregate(digi_course_assign, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Assigned Course List', doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function list_course_assign_group_data(req, res) {
    try {
        /* let query = { isDeleted: false, _program_id: req.params.program_id, _curriculum_id: req.params.curriculum_id, _year_id: req.params.year_id, _level_id: req.params.level_id };
        let doc = await base_control.get_list(digi_course_assign, query, {}); */

        const aggre = [
            {
                $match: {
                    isDeleted: false,
                    _program_id: ObjectId(req.params.program_id),
                    _curriculum_id: ObjectId(req.params.curriculum_id),
                    _year_id: ObjectId(req.params.year_id),
                    _level_id: ObjectId(req.params.level_id),
                },
            },
            {
                $lookup: {
                    from: 'digi_courses',
                    localField: '_course_id',
                    foreignField: '_id',
                    as: 'course_data',
                },
            },
            {
                $lookup: {
                    from: 'digi_course_groups',
                    localField: '_course_id',
                    foreignField: 'course_id',
                    as: 'course_group_data',
                },
            },
        ];
        const doc = await base_control.get_aggregate(digi_course_assign, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    'Assigned Course group data List',
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function delete_course_assign(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const obj = { isDeleted: true };
        const doc = await base_control.update(digi_course_assign, query, obj);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        'Course assigned deleted successfully',
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    'Unable to delete course assigned ',
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function get_session_flow_details_by_course_id(req, res) {
    try {
        const query = { ...common_files.query, _course_id: ObjectId(req.params.course_id) };
        const doc = await base_control.get_list(digi_session_order, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Session flow details', doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

module.exports = {
    insert,
    update,
    update_session_flow,
    list,
    list_course_assign_group_data,
    delete_course_assign,
    get_session_flow_details_by_course_id,
};
