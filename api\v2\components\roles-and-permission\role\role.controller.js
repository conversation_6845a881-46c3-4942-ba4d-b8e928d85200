const roleSchema = require('./role.model');
const roleAssignSchema = require('../role-assign/role-assign.model');

const { convertToMongoObjectId, ROLE } = require('../../../utility/common');
const {
    checkIfInstitutionExists,
    checkIfParentExists,
    checkIfRoleNameExists,
} = require('./role.util');
const { ROLE_ASSIGN } = require('../../../utility/constants');

const getAllRoleList = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleModel = getModel(tenantURL, ROLE, roleSchema);
        const { _institution_id, _parent_id } = headers;
        const roleList = await roleSchema
            .find({}, { _institution_id: 1, name: 1, modules: 1, _college_id: 1 })
            .findAllRoles({ _institution_id, _parent_id })
            .lean();
        if (!roleList.length) {
            return { statusCode: 200, message: 'ROLE_LIST', data: [] };
        }

        return { statusCode: 200, message: 'ROLE_LIST', data: roleList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createRole = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleModel = getModel(tenantURL, ROLE, roleSchema);
        const { _institution_id, _parent_id } = headers;
        const { name } = body;

        await checkIfInstitutionExists({
            _institution_id,
            _parent_id,
            roleName: name,
        });

        if (_institution_id) {
            if (!(await checkIfInstitutionExists({ _institution_id }))) {
                return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
            }
        }

        if (_parent_id) {
            if (!(await checkIfParentExists({ _parent_id }))) {
                return { statusCode: 400, message: 'COLLEGE_NOT_FOUND' };
            }
        }

        if (name) {
            if (!(await checkIfRoleNameExists({ _institution_id, _parent_id, roleName: name }))) {
                return { statusCode: 400, message: 'DUPLICATE_ROLE' };
            }
        }

        const roleInsert = await roleModel.create({
            ...(_institution_id && { _institution_id }),
            ...(_parent_id && { _parent_id }),
            name,
        });

        if (!roleInsert) {
            return { statusCode: 500, message: 'UNABLE_TO_CREATE_ROLE', data: roleInsert.data };
        }

        return { statusCode: 210, message: 'ROLE_CREATED', data: roleInsert.data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateRole = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleModel = getModel(tenantURL, ROLE, roleSchema);
        const { name } = body;
        const { _institution_id, _parent_id } = headers;
        const { id } = params;

        if (name) {
            if (!(await checkIfRoleNameExists({ _institution_id, _parent_id, roleName: name }))) {
                return { statusCode: 400, message: 'DUPLICATE_ROLE' };
            }
        }

        const roleUpdate = await roleModel.updateOne({ _id: convertToMongoObjectId(id) }, body);
        if (!roleUpdate) {
            return { statusCode: 500, message: 'UNABLE_TO_UPDATE_ROLE' };
        }

        return { statusCode: 200, message: 'ROLE_UPDATED', data: roleUpdate.data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteRole = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleModel = getModel(tenantURL, ROLE, roleSchema);
        const roleAssignModel = getModel(tenantURL, ROLE_ASSIGN, roleAssignSchema);
        const { id } = params;

        //Check is Role is Assigned to User / If it not assigned only able to delete
        const roleAssign = await roleAssignModel.find(
            { roles: { $elemMatch: { _role_id: convertToMongoObjectId(id) } } },
            { _id: 1 },
        );
        if (roleAssign.length) {
            return { statusCode: 400, message: 'ROLE_IS_ASSIGNED_TO_USER_UNABLE_TO_REMOVE' };
        }

        const roleUpdate = await roleModel.updateOne(
            { _id: convertToMongoObjectId(id) },
            { $set: { isDeleted: true } },
        );
        if (!roleUpdate) {
            return { statusCode: 202, message: 'UNABLE_TO_ROLE_REMOVED', data: roleUpdate.data };
        }

        return { statusCode: 200, message: 'ROLE_REMOVED', data: roleUpdate.data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAllRoleList,
    createRole,
    updateRole,
    deleteRole,
};
