const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {
    INSTITUTION_CALENDAR,
    DIGI_COURSE,
    USER,
    STUDENT_CRITERIA_MANIPULATION,
    DIGI_PROGRAM,
} = require('../utility/constants');

const studentCriteriaManipulation = new Schema(
    {
        institutionCalendarId: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        programId: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        courseId: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        yearNo: {
            type: String,
            required: true,
        },
        levelNo: {
            type: String,
            required: true,
        },
        rotationCount: {
            type: Number,
        },
        studentId: {
            type: Schema.Types.ObjectId,
            ref: USER,
            required: true,
        },
        absencePercentage: {
            type: Number,
            required: true,
            min: 1,
            max: 100,
        },
        reason: {
            type: String,
        },
        updatedBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(STUDENT_CRITERIA_MANIPULATION, studentCriteriaManipulation);
