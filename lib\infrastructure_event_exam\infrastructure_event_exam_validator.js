const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');


exports.infra_event_exam = (req,res,next) =>{
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            type: Joi.string().min(3).max(50).required().error(error =>{
                return error;
            }),
            event: Joi.boolean().required().error(error =>{
                return error;
            }),
            exam: Joi.boolean().required().error(error =>{
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    })
}

exports.event_exam = (req,res,next) =>{
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            event: Joi.boolean().error(error =>{
                return error;
            }),
            exam: Joi.boolean().error(error =>{
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    })
}

exports.infra_event_exam_id = (req,res,next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}