const RoleModel = require('./role.model');

const createRole = async ({ name, type, isDefault }) => {
    if (await RoleModel.exists({ name })) {
        throw new Error('ROLE_ALREADY_EXISTS');
    }

    const role = await RoleModel.create({ name, type, isDefault });

    return role;
};

const getRoles = async () => {
    const roles = await RoleModel.find({}, { name: 1, type: 1, isDefault: 1 }).lean();

    return roles;
};

const updateRole = async ({ roleId, name, type, isDefault }) => {
    if (await RoleModel.exists({ name, _id: { $ne: roleId } })) {
        throw new Error('ROLE_ALREADY_EXISTS');
    }

    const role = await RoleModel.updateOne({ _id: roleId }, { $set: { name, type, isDefault } });
};

const deleteRole = async ({ roleId }) => {
    const role = await RoleModel.deleteOne({ _id: roleId });

    return role;
};

module.exports = {
    createRole,
    getRoles,
    updateRole,
    deleteRole,
};
