const express = require('express');
const route = express.Router();
const user = require('./user.controller');
const validator = require('./user.validator');
const catchAsync = require('../../utility/catch-async');
const { authentication, authenticationForSignUp } = require('../../middleware/auth.middleware');
const { validate } = require('../../utility/input-validation');
const { uploadDocument } = require('./user.util');
const utilToken = require('../../utility/token.util');
// New Login Service
route.post('/authLogin', validate(validator.authLoginBodySchema), catchAsync(user.loginService));
route.post(
    '/authLoginForWeb',
    validate(validator.authLoginBodySchema),
    catchAsync(user.loginServiceForWeb),
);
route.post(
    '/authUserInstitutions',
    // validate(validator.authLoginBodySchema),
    catchAsync(user.authUserInstitutions),
);
route.post(
    '/authLoggedIn',
    authentication,
    validate(validator.authLoggedInBodySchema),
    catchAsync(user.loggedInService),
);
route.post(
    '/import',
    // validate(validator.userImportSchema),
    catchAsync(user.userStaffStudentImport),
);
route.put('/forgot-password/:email', catchAsync(user.forgotPassword));

route.post('/sendRequest', catchAsync(user.sendRequest));

route.post(
    '/userSignUp',
    // validate(validator.userImportSchema),
    catchAsync(user.userSignUp),
);

route.post(
    '/userSignUpForWeb',
    // validate(validator.userImportSchema),
    catchAsync(user.userSignUpForWeb),
);

route.put(
    '/userRegistration/:id',
    validate(validator.userRegistrationSchema),
    catchAsync(user.userRegistration),
);
route.get('/userDetails/:id', catchAsync(user.userDetails));
// User Reset
route.get('/userReSet/:email', catchAsync(user.userReSet));
route.post('/import-templates', function (req, res, next) {
    user.userStaffStudentImportTemplates(req, res)
        .then(({ data = {} }) => {
            res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
            res.setHeader(
                'Content-Type',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            );
            res.setHeader('Content-Disposition', `attachment; filename=${data.fileName}`);
            data.wb.xlsx
                .write(res)
                .then(() => {
                    res.end();
                })
                .catch((err) => {
                    next(err);
                });
        })
        .catch((err) => {
            next(err);
        });
});
// route.get('/export/:user_type/:tab/:_institution_id', function (req, res, next) {
//     user.exportUsers(req, res)
//         .then(({ data = {} }) => {
//             res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
//             res.setHeader(
//                 'Content-Type',
//                 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//             );
//             res.setHeader('Content-Disposition', `attachment; filename=${data.fileName}`);
//             data.wb.xlsx
//                 .write(res)
//                 .then(() => {
//                     res.end();
//                 })
//                 .catch((err) => {
//                     next(err);
//                 });
//         })
//         .catch((err) => {
//             next(err);
//         });
// });
route.put(
    '/active-inactive-user/:id',
    validate(validator.activeInActiveValidator),
    catchAsync(user.activeInActiveUser),
);
route.put(
    '/default-institution/:email',
    validate(validator.isDefaultValidator),
    catchAsync(user.userDefaultInstitution),
);
route.get('/list-institution/:email', catchAsync(user.listInstitution));
route.put('/update-user/:id', validate(validator.updateUserValidator), catchAsync(user.updateUser));
route.delete('/delete/:id', validate(validator.userIdValidator), catchAsync(user.deleteUser));
route.get('/get-user/:user_type/:tab/:limit/:pageNo', catchAsync(user.getUser));

route.get(
    '/document-configuration/:_institution_id',
    validate(validator.institutionIdGetValidation),
    catchAsync(user.getDocumentConfiguration),
);
route.post(
    '/document-upload',
    authenticationForSignUp,
    uploadDocument,
    catchAsync(user.documentUpload),
);
route.put(
    '/users-document-upload/:_user_id',
    validate(validator.usersDocumentUploadValidator),
    catchAsync(user.usersDocumentUpload),
);

route.put(
    '/user-single-document-upload/:_user_id',
    validate(validator.usersSingleDocumentUploadValidator),
    catchAsync(user.userSingleDocumentUpload),
);
route.get(
    '/users-document-upload/:_user_id',
    validate(validator.getUsersDocumentUploadValidator),
    catchAsync(user.getUsersDocumentUpload),
);
route.post('/verify-user', catchAsync(user.verifyUserData));
route.post('/verify-student', catchAsync(user.verifyStudentData));
route.get('/list-users', validate(validator.listUserDataValidator), catchAsync(user.listUsers));
route.get(
    '/list-users-without-paginate',
    validate(validator.listUserDataValidator),
    catchAsync(user.listUsersWithOutPaginate),
);
route.get('/search-users', validate(validator.listUserDataValidator), catchAsync(user.searchUsers));

route.get(
    '/biometric-configuration/:_institution_id',
    validate(validator.institutionIdGetValidation),
    catchAsync(user.getUsersBiometricConfiguration),
);
route.put(
    '/users-biometric-upload/:_user_id',
    validate(validator.usersBiometricUploadValidator),
    catchAsync(user.usersBiometricUpload),
);
route.put('/users-register-skip/:id', catchAsync(user.userRegistrationSkip));
route.get(
    '/users-biometric-upload/:_user_id',
    validate(validator.getUsersDocumentUploadValidator),
    catchAsync(user.getUsersBiometricUpload),
);
route.get(
    '/staff-designation/:_institution_id',
    validate(validator.institutionIdGetValidation),
    catchAsync(user.getStaffDesignation),
);

route.get(
    '/staff-particular-designation',
    validate(validator.designationIdGetValidation),
    catchAsync(user.getParticularStaffDesignation),
);

route.put(
    '/update-designation',
    validate(validator.updateDesignationValidator),
    catchAsync(user.updateDesignation),
);

route.put(
    '/academic-allocation/:id',
    validate(validator.academicAllocationValidator),
    catchAsync(user.updateUserAcademicAllocation),
);

route.put(
    '/employment-schedule/:id',
    validate(validator.employmentScheduleValidator),
    catchAsync(user.updateEmploymentSchedule),
);

route.get(
    '/employment-schedule/:_user_id',
    validate(validator.getUsersDocumentUploadValidator),
    catchAsync(user.getEmploymentSchedule),
);

route.get(
    '/academic-allocation/:id',
    validate(validator.academicAllocationValidator),
    catchAsync(user.getUserAcademicAllocation),
);
route.get(
    '/profile-details/:_user_id',
    validate(validator.getUsersDocumentUploadValidator),
    catchAsync(user.getProfileDetails),
);
route.post(
    '/send-staff-mail',
    validate(validator.userSendMailValidator),
    catchAsync(user.userMailPush),
);
route.post(
    '/flag-unflag-user-details',
    validate(validator.flagUnflagUserValidator),
    catchAsync(user.flagUnflagUser),
);
route.put(
    '/approval-reject',
    validate(validator.approvalRejectUserValidator),
    catchAsync(user.approvalRejectUser),
);
route.post(
    '/verifying-status/:userId',
    validate(validator.verifyingStatusValidator),
    catchAsync(user.verifyingStatus),
);
route.post(
    '/multiple-status-verifying',
    validate(validator.multipleStatusVerifying),
    catchAsync(user.multipleStatusUpdating),
);
route.get('/verification-pending/:userId', catchAsync(user.verificationPending));
route.post('/refresh-token', utilToken.verifyRefreshToken);
route.post('/refresh-token-signup', utilToken.verifyRefreshTokenForSignUp);
module.exports = route;
