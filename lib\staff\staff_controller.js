const constant = require('../utility/constants');
var staff = require('mongoose').model(constant.STAFF);
var program = require('mongoose').model(constant.PROGRAM);
var department = require('mongoose').model(constant.DEPARTMENT);
var position = require('mongoose').model(constant.POSITION);
var role = require('mongoose').model(constant.ROLE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const staff_formate = require('./staff_formate');
const ObjectId = require('mongodb').ObjectID;
const common_fun = require('../utility/common_functions');
const util_key = require('../utility/util_keys');
const crypto = require('crypto');
const moment = require('moment');


exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $sort: { updatedAt: -1 } },
        { $project: { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 } },
        { $skip: skips }, { $limit: limits }
    ];
    let doc = await base_control.get_aggregate(staff, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ staff_formate.staff_list_array(doc.data));
        // common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* staff_formate.staff(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    //try {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COUNTRY, localField: 'address._nationality_id', foreignField: '_id', as: 'country' } },
        { $unwind: { path: '$country', preserveNullAndEmptyArrays: true } },
        {
            $addFields:
            {
                'correction_first_name': {
                    $in: ['first', '$correction']
                },
                'correction_middle_name': {
                    $in: ['middle', '$correction']
                },
                'correction_last_name': {
                    $in: ['last', '$correction']
                },
                'correction_gender': {
                    $in: ['gender', '$correction']
                },
                'correction_employee_id': {
                    $in: ['employee_id', '$correction']
                },
                'correction_nationality_id': {
                    $in: ['nationality_id', '$correction']
                },
            }
        },
        { $project: { password: 0 } }
    ];
    let doc = await base_control.get_aggregate(staff, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "staff details", /* doc.data[0] */staff_formate.staff_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "staff details", doc.data[0]/* staff_formate.staff_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error Id not found or data missmatch", doc.data);
    }
    // } catch (error) {
    //     console.log('Internal Server Error ', error);
    //     common_files.com_response(res, 500, false, "Internal Server Error : Catch : ", error);
    // }
};

exports.insert = async (req, res) => {
    // let checks1 = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    // let checks2 = await base_control.check_id(department, { _id: { $in: req.body._department_id }, 'isDeleted': false });
    // let checks3 = await base_control.check_id(position, { _id: { $in: req.body._position_id }, 'isDeleted': false });
    // let checks4 = { status: true };
    // if (req.body._role_id != undefined) {
    //     checks4 = await base_control.check_id(role, { _id: { $in: req.body._role_id }, 'isDeleted': false });
    // }
    // if (checks1.status && checks2.status && checks3.status && checks4.status) {
    // console.log(req.body);
    let objs = {
        employee_id: req.body.employee_id,
        name: {
            first: req.body.first_name,
            last: req.body.last_name,
            middle: req.body.middle_name,
            family: req.body.family_name
        },
        dob: req.body.dob,
        email: req.body.email,
        gender: req.body.gender,
        mobile: req.body.mobile,
        _employee_id_doc: req.body._employee_id_doc,
        address: {
            nationality_id: req.body.nationality_id,
            _nationality_id: req.body._nationality_id,
            _nationality_id_doc: req.body._nationality_id_doc,
            building: req.body.building,
            city: req.body.city,
            district: req.body.district,
            zip_code: req.body.zip_code,
            unit: req.body.unit,
            street_no: req.body.street_no,
            passport_no: req.body.passport_no,
            _address_doc: req.body._address_doc
        },
        qualifications: {
            degree: [{
                degree_name: req.body.degree_name,
                _degree_doc: req.body._degree_doc
            }],
            _appointment_order_doc: req.body._appointment_order_doc,
            _program_id: req.body._program_id,
            _department_id: req.body._department_id,
            // _department_division_id: req.body._department_division_id,
            _department_subject_id: req.body._department_subject_id,
            designation: req.body.designation
        }
    };
    if (req.body._department_division_id != undefined && req.body._department_division_id.length == 24) {
        objs._department_division_id = req.body._department_division_id;
    }

    let doc = await base_control.insert(staff, objs);
    if (doc.status) {
        common_files.com_response(res, 201, true, "staff Added successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }


    // } else {
    //     common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    // }
}

exports.update = async (req, res) => {
    let checks1 = { status: true }, checks2 = { status: true }, checks3 = { status: true }, checks4 = { status: true };
    if (req.body._program_id != undefined) {
        checks1 = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (req.body._department_id != undefined) {
        checks2 = await base_control.check_id(department, { _id: { $in: req.body._department_id }, 'isDeleted': false });
    }
    if (req.body._position_id != undefined) {
        checks3 = await base_control.check_id(position, { _id: { $in: req.body._position_id }, 'isDeleted': false });
    }
    if (req.body._role_id != undefined) {
        checks4 = await base_control.check_id(role, { _id: { $in: req.body._role_id }, 'isDeleted': false });
    }
    if (checks1.status && checks2.status && checks3.status && checks4.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(staff, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "staff update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(staff, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "staff deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(staff, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "staff List", staff_formate.staff_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.get_mobile_mail = async (req, res) => {
    let match = req.params.match;
    let aggre = [
        { $match: { 'isDeleted': false } },
    ];
    // console.log(match, req.params.value);
    if (match == 'mobile') {
        aggre.push({ $match: { 'mobile': parseInt(req.params.value) } });
    } else {
        aggre.push({ $match: { 'employee_id': req.params.value } });
    }
    let doc = await base_control.get_aggregate(staff, aggre);
    // console.log(doc.data);
    if (doc.status) {
        common_files.com_response(res, 200, true, "staff details", doc.data/* staff_formate.staff_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.staff_import = async (req, res) => {
    let docs = { status: false };
    let status, datas;
    await req.body.data.forEach(async (doc, index) => {

        let query = { employee_id: doc.employee_id, 'isDeleted': false };
        let project = { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 };
        let staff_geting = await base_control.get(staff, query, project);
        // console.log(staff_geting.data);
        if (!staff_geting.status) {
            let objs = {
                username: doc.username,
                employee_id: doc.employee_id,
                email: doc.email,
                name: {
                    first: doc.first_name,
                    last: doc.last_name,
                    middle: doc.middle_name,
                    // family: doc.family_name
                },
                gender: doc.gender,
                password: common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH), // generate random password
                address: {
                    nationality_id: doc.nationality_id,
                },
                status: 'imported'
            };

            docs = await base_control.insert(staff, objs);
            if (docs.status) {
                status = true;
                datas = docs;
            } else {
                datas = docs;
                status = false;
            }
        }
        else {
            let objs = {
                username: doc.username,
                email: doc.email,
                name: {
                    first: doc.first_name,
                    last: doc.last_name,
                    middle: doc.middle_name,
                    // family: doc.family_name
                },
                gender: doc.gender,
                password: common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH), // generate random password
                address: {
                    nationality_id: doc.nationality_id,
                }
            };
            // console.log(staff_geting.data._id);
            docs = await base_control.update(staff, staff_geting.data._id, objs);
            if (docs.status) {
                status = true;
                datas = docs;
            } else {
                datas = docs;
                status = false;
            }
        }
        if (req.body.data.length == index + 1) {
            if (status) {
                let aggre = [
                    // { $match: { '_course_id': ObjectId(req.body._course_id), '_program_id': ObjectId(req.body._program_id) } },
                    { $match: { 'isDeleted': false } },
                    { $sort: { updatedAt: -1 } },
                    { $project: { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 } }
                ];
                let staff_data = await base_control.get_aggregate(staff, aggre);
                common_files.com_response(res, 201, true, "Staff data's imported successfully", staff_formate.staff_list_array(staff_data.data));
            } else {
                common_files.com_response(res, 500, false, "Error", docs.data);
            }
        }
    });
};

exports.login = async (req, res) => {
    let cond = { 'email': req.body.email, 'isDeleted': false };
    let proj = { _id: 1, email: 1, password: 1 };
    let doc = await base_control.get(staff, cond, proj);
    let respon = { _id: doc.data._id, email: doc.data.email };
    // console.log(doc.data[0].password);
    if (doc.status) {
        if (doc.data.password == req.body.password) {
            await base_control.update(staff, doc.data._id, { 'verification.email': true });
            common_files.com_response(res, 200, true, "Staff login successfully", respon/* staff_formate.staff_ID(doc.data[0]) */);
        } else {
            common_files.com_response(res, 500, false, "Password not match", "Password not match");
        }
    } else {
        common_files.com_response(res, 500, false, "Email id Not found", "Email id Not found");
    }
};

exports.set_password = async (req, res) => {
    // let cond = { 'email': req.body.email, 'isDeleted': false };
    let cond = { '_id': req.body.id, 'isDeleted': false };
    let proj = { _id: 1, password: 1 };
    let doc = await base_control.get(staff, cond, proj);
    // console.log(doc.data.password);
    if (doc.status) {
        if (doc.data.password == req.body.old_password) {
            // if (doc.data.status == 'success' || doc.data.status == 'imported') {
            let update_pass = await base_control.update(staff, doc.data._id, { password: req.body.new_password, status: 'password_confirmed' });
            if (update_pass.status) {
                common_files.com_response(res, 200, true, "Password successfully changed", ' Password successfully changed'/* staff_formate.staff_ID(doc.data) */);
            } else {
                common_files.com_response(res, 500, false, "Unable to change password", "Unable to change password");
            }
            // } else {
            //     common_files.com_response(res, 500, false, "", "");
            // }
        } else {
            common_files.com_response(res, 500, false, "Password not match", "Password not match");
        }
    } else {
        common_files.com_response(res, 500, false, "Email id Not found", "Email id Not found");
    }
};

exports.register_mobile = async (req, res) => {
    // let cond = { 'email': req.body.email, 'isDeleted': false };
    let cond = { '_id': req.body.id, 'isDeleted': false };
    let proj = { _id: 1 };
    let doc = await base_control.get(staff, cond, proj);
    // console.log(doc.data[0].password);
    if (doc.status) {
        // if (doc.data[0].status == 'success' || doc.data[0].status == 'imported') {
        let update_pass = await base_control.update(staff, doc.data._id, { mobile: req.body.mobile, 'otp.no': '1234', 'otp.expiry_date': common_fun.timestampNow() });
        // let update_pass = await base_control.update(staff, doc.data._id, { mobile: req.body.mobile, 'otp.no': common_fun.getFourDigitOTP(), 'otp.expiry_date': common_fun.timestampNow() });
        if (update_pass.status) {
            common_files.com_response(res, 200, true, "Mobile Registered and OTP Send", 'Mobile Registered and OTP Send'/* staff_formate.staff_ID(doc.data[0]) */);
        } else {
            common_files.com_response(res, 500, false, "This mobile number already exist", "This mobile number already exist");
        }
        // } else {
        //     common_files.com_response(res, 500, false, "", "");
        // }
    } else {
        common_files.com_response(res, 500, false, "Email id Not found", "Email id Not found");
    }
};

exports.otp_verify = async (req, res) => {
    // let cond = { 'email': req.body.email, 'isDeleted': false };
    let cond = { '_id': req.body.id, 'isDeleted': false };
    // let proj = { _id: 1, otp: 1, verification: 1 };
    let proj = {};
    let doc = await base_control.get(staff, cond, proj);
    if (doc.status) {
        if (doc.data.otp.no == req.body.otp) {
            const secondsDifference = common_fun.getSecondsDifference(doc.data.otp.expiry_date, common_fun.timestampNow());
            if (secondsDifference <= util_key.OTP_EXPIRY_DURATION_IN_SECS) {
                let update_pass = { status: true };
                if (!doc.data.verification.mobile) {
                    update_pass = await base_control.update(staff, doc.data._id, { 'verification.mobile': true });
                }
                if (update_pass.status) {
                    common_files.com_response(res, 200, true, "OTP verified successfully", doc.data/* staff_formate.staff_ID(doc.data) */);
                } else {
                    common_files.com_response(res, 500, false, "Unable to verify OTP", "Unable to verify OTP");
                }
            } else {
                common_files.com_response(res, 500, false, "OTP Expires", "OTP Expires");
            }
        } else {
            common_files.com_response(res, 500, false, "Invalid OTP", "Invalid OTP");
        }
    } else {
        common_files.com_response(res, 500, false, "Email id Not found", "Email id Not found");
    }
};

exports.staff_login = async (req, res) => {
    let cond = { 'email': req.body.email, 'isDeleted': false };
    let proj = { _id: 1, email: 1, password: 1 };
    let messages = '';
    let doc = await base_control.get(staff, cond, proj);
    if (doc.status) {
        if (doc.data.password == req.body.password) {
            let otp = common_fun.getFourDigitOTP();
            await base_control.update(staff, doc.data._id, { 'otp.no': '1234', 'otp.expiry_date': common_fun.timestampNow() });
            // await base_control.update(staff, doc.data._id, { 'otp.no': otp, 'otp.expiry_date': common_fun.timestampNow() });
            messages = "<p>Dear DigiScheduler User,<br>" + otp + " is SECRET OTP for you account login on " + common_fun.timestampNow_in_current()
                + ". valid for 3min. Pls do not share OTP with anyone</p>";
            if (req.body.otp_mode == 'email') {
                const mailOptions = {
                    from: util_key.EMAIL_SENDER_ADDRESS, // sender address
                    to: doc.data.email, // emails, // receiver
                    subject: 'Digi Scheduler Alert', // Subject line
                    html: messages,
                };
                common_fun.send_mail(mailOptions, (error) => {
                    if (error) {
                        //common_files.com_response(res, 500, false, "Error", error);
                        console.log('Email server Error : ', error);
                    }
                    console.log('mail sent');
                });
            } else {
                //
                /* function sentMessageCallback(error, response, body) {
                    if (!error && common_fun.checkSMSResponse(response, body)) {
                        // doc.otp.no = otp;
                        // doc.save((err3) => {
                        //     if (err3) {
                        //         // console.log('error ' + err);
                        //         common_files.com_response(res, 500, false, 'Error in login');
                        //     }
                        // });
                        common_files.com_response(res, 200, true, "SMS Send");
                    } else {
                        common_files.com_response(res, 503, false, 'Error in sending message');
                    }
                }
                common_fun.send_message('+919080002310', 'Bharath Testing', sentMessageCallback); */
            }
            let respon = { _id: doc.data._id, email: doc.data.email };
            common_files.com_response(res, 200, true, "OTP Send", respon/* staff_formate.staff_ID(doc.data[0]) */);
        } else {
            common_files.com_response(res, 500, false, "Password not match", "Password not match");
        }
    } else {
        common_files.com_response(res, 500, false, "Email id Not found", "Email id Not found");
    }
};

exports.profile_update = async (req, res) => {
    try {
        // console.log(req.body);
        let objs = {
            dob: req.body.dob,
            'address._nationality_id': req.body._nationality_id,
            'address.building': req.body.building,
            'address.city': req.body.city,
            'address.district': req.body.district,
            'address.zip_code': req.body.zip_code,
            'address.unit': req.body.unit,
            'address.passport_no': req.body.passport_no,
            'office.office_extension': req.body.office_extension,
            'office.office_room_no': req.body.office_room_no,
            status: 'submitted'
        };
        let cor = [];
        if (req.body.first_name != undefined && !req.body.first_name) {
            cor.push('first');
        }
        if (req.body.last_name != undefined && !req.body.last_name) {
            cor.push('last');
        }
        if (req.body.middle_name != undefined && !req.body.middle_name) {
            cor.push('middle');
        }
        if (req.body.gender != undefined && !req.body.gender) {
            cor.push('gender');
        }
        if (req.body.employee_id != undefined && !req.body.employee_id) {
            cor.push('employee_id');
        }
        if (req.body.nationality_id != undefined && !req.body.nationality_id) {
            cor.push('nationality_id');
        }
        // console.log(cor.length);
        // if (cor.length != -1) {
        //     objs.verification.data = 'error';
        // }
        // console.log(cor);
        objs.correction = cor;
        // let doc = { status: true };
        // console.log(objs);
        let doc = await base_control.update(staff, req.body.id, objs);
        // console.log(doc);
        if (doc.status) {
            common_files.com_response(res, 201, true, "staff Profile updated successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } catch (error) {
        common_files.com_response(res, 500, false, "Catch Error", error);
    }
};

exports.profile_document_update = async (req, res) => {
    let objs = {
        _employee_id_doc: req.body._employee_id_doc,
        'address._nationality_id_doc': req.body._nationality_id_doc,
        'address._address_doc': req.body._address_doc,
        'qualifications.degree': [{
            degree_name: req.body.degree_name,
            _degree_doc: req.body._degree_doc
        }],
        'qualifications._appointment_order_doc': req.body._appointment_order_doc,
        status: 'submitted'
    };

    let doc = await base_control.update(staff, req.body.id, objs);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Staff documents update successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.staff_list = async (req, res) => {
    let filter = req.params.filter;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
    ];
    let aggre_leng = { 'isDeleted': false };

    if (filter == 'submitted') {
        aggre.push({ $match: { 'status': 'submitted' } });
        aggre_leng.status = 'submitted';
    } else if (filter == constant.VALID) {
        aggre.push({ $match: { 'status': constant.VALID } });
        aggre_leng.status = constant.VALID;
    } else if (filter == constant.INVALID) {
        aggre.push({ $match: { 'status': constant.INVALID } });
        aggre_leng.status = constant.INVALID;
    } else if (filter == 'mismatch') {
        aggre.push({ $match: { 'status': 'submitted' } });
        aggre.push({ $match: { 'verification.data': constant.CORRECTION_REQUIRED } });
        aggre_leng = { 'isDeleted': false, 'status': 'submitted', 'verification.data': constant.CORRECTION_REQUIRED };
    }

    // const pending_query = {
    //     $or: [{
    //         'verification.data': Constants.PENDING,
    //     }, {
    //         'verification.data': Constants.CORRECTION_REQUIRED,
    //     }, {
    //         'verification.face': false,
    //     }, {
    //         'verification.finger': false,
    //     }],
    //     isDeleted: false,
    // };
    // const pending_data_query = {
    //     $or: [{
    //         'verification.data': Constants.PENDING,
    //     }, {
    //         'verification.data': Constants.CORRECTION_REQUIRED,
    //     }],
    //     isDeleted: false,
    // };
    // const pending_bio_query = {
    //     $or: [{
    //         'verification.face': false,
    //     }, {
    //         'verification.finger': false,
    //     }],
    //     isDeleted: false,
    // };
    // const valid_query = {
    //     'verification.data': Constants.DONE,
    //     'verification.face': true,
    //     'verification.finger': true,
    //     isDeleted: false,
    // };


    // const data_all_count = await base_control.get_aggregate_length(staff, all_query);
    // const data_valid_count = await base_control.get_aggregate_length(staff, valid_query);
    // const data_pending_count = await base_control.get_aggregate_length(staff, pending_query);
    // const data_pending_all_count = await base_control.get_aggregate_length(staff, pending_query);
    // const data_pending_data_count = await base_control.get_aggregate_length(staff, pending_data_query);
    // const data_pending_bio_count = await base_control.get_aggregate_length(staff, pending_bio_query);

    aggre.push({ $sort: { updatedAt: -1 } }, { $skip: skips }, { $limit: limits });
    let doc = await base_control.get_aggregate(staff, aggre);
    if (doc.status) {
        // const response = {
        //     count: {
        //         allCount: totalPages,
        //         validCount: data_valid_count,
        //         pendingCount: data_pending_count,
        //         pendingAllCount: data_pending_all_count,
        //         pendingDataCount: data_pending_data_count,
        //         pendingBioCount: data_pending_bio_count,
        //     },
        //     data: doc.data
        // };


        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ staff_formate.staff_list_array(doc.data));
        common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* staff_formate.staff(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.staff_mail_push = async (req, res) => {

    let token;
    let messages = '';
    // console.log('Master : ', req.body);
    // console.log('Ids ', req.body.id);
    let staff_lists = await base_control.get_list(staff, { _id: req.body.id }, {});
    // console.log('Lists : ', staff_lists);
    if (staff_lists.status) {
        await staff_lists.data.forEach(async (staff_datas, index) => {
            // let query = { _id: element, 'isDeleted': false };
            // let staff_datas = await base_control.get(staff, query, {});
            token = crypto.randomBytes(16).toString('hex');
            let date_f = new Date();
            date_f = moment(date_f).add(72, 'hours').format('D MMM YYYY h:mm a');
            // messages = `<p> Name: ${staff_datas.name.first + ' ' + staff_datas.name.last}<br>` +
            //     `Link: https://digischeduler.netlify.com/signup?token=${token}<br>` +
            //     `Password: ${staff_datas.password}<br>` +
            //     `Date to Expire: ${date_f}(72 hrs from sending this email)<br>` +
            //     "By: 'DEAN' </p>";

            if (req.body.type == 'signup') {
                const replace_data = {
                    v_name: staff_datas.name.first + ' ' + staff_datas.name.last,
                    v_link: `https://digischeduler.netlify.com/signup?token=${token}`,
                    v_password: staff_datas.password,
                    v_date_expired: `${date_f}(72 hrs from sending this email)`, // Date.toString('d M Y'),
                    v_admin_sign: 'DEAN',
                };
                const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                messages = req.body.message.replace(re, (matched) => replace_data[matched]);
            } else if (req.body.type == 'valid') {
                messages = req.body.message
            } else {
                messages = req.body.message
            }
            const mailOptions = {
                from: util_key.EMAIL_SENDER_ADDRESS, // sender address
                to: staff_datas.email, // emails, // receiver
                subject: 'Digi Scheduler Account', // Subject line
                html: messages,
            };
            await common_fun.send_mail(mailOptions, (error) => {
                if (error) {
                    console.log('err', error);
                }
                if (!error) {
                    console.log('mail sent : ' + staff_datas.email);
                } else {
                    console.log('Mail Error : ', error);
                }
            });
            // console.log('Pushing Message : ', messages);
            if (req.body.id.length == index + 1) {
                common_files.com_response(res, 201, true, "Mail Send successfully", "Mail Send successfully");
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error in User data is not match Database", staff_lists.data);
    }
}


exports.staff_add_edit = async (req, res) => {
    let docs = { status: false };
    // let project = { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 };

    // let query = { employee_id: req.body.employee_id, 'isDeleted': false };
    // let staff_geting = await base_control.get(staff, query, project);
    // if (!staff_geting.status) {
    // } else {
    //     common_files.com_response(res, 500, false, "Staff data is already present", "Staff data is already present");
    // }


    if (req.body.id.length == 0) {
        let objs = {
            username: req.body.username,
            employee_id: req.body.employee_id,
            email: req.body.email,
            name: {
                first: req.body.first_name,
                last: req.body.last_name,
                middle: req.body.middle_name,
                // family: req.body.family_name
            },
            gender: req.body.gender,
            // password: common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH), // generate random password
            address: {
                nationality_id: req.body.nationality_id,
            },
            status: 'imported'
        };
        objs.password = common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH); // generate random password
        docs = await base_control.insert(staff, objs);
    } else {
        /* let objs = {};
        objs.insert(first = req.body.first_name);
        // if (req.body.first_name != undefined) {
        //     objs.name.first= req.body.first_name
        // }
        // if (req.body.last_name != undefined) {
        //     objs.name.last = req.body.last_name
        // }
        // if (req.body.middle_name != undefined) {
        //     objs.name.middle = req.body.middle_name
        // }
        // if (req.body.nationality_id != undefined) {
        //     objs.address.nationality_id = req.body.nationality_id
        // }
        if (req.body.username != undefined) {
            objs.username = req.body.username;
        }
        if (req.body.employee_id != undefined) {
            objs.employee_id = req.body.employee_id
        }
        if (req.body.email != undefined) {
            objs.email = req.body.email
        }
        if (req.body.gender != undefined) {
            objs.gender = req.body.gender
        }
        console.log(objs); */
        let objs = {
            username: req.body.username,
            employee_id: req.body.employee_id,
            email: req.body.email,
            'name.first': req.body.first_name,
            'name.last': req.body.last_name,
            'name.middle': req.body.middle_name,
            // family: req.body.family_name
            gender: req.body.gender,
            'address.nationality_id': req.body.nationality_id,
            status: 'imported'
        };

        docs = await base_control.update(staff, req.body.id, objs);
    }
    if (docs.status) {
        await common_files.com_response(res, 201, true, "Staff data's Created/Modified successfully", "Staff data's Created/Modified successfully");
    } else {
        common_files.com_response(res, 500, false, "Staff data Error", "Staff data Error");
    }
};

exports.staff_edit = async (req, res) => {
    let docs = { status: false };
    let objs = {};
    if (req.body.first_name != undefined) {
        Object.assign(objs, { 'name.first': req.body.first_name });
    }
    if (req.body.last_name != undefined) {
        Object.assign(objs, { 'name.last': req.body.last_name });
    }
    if (req.body.middle_name != undefined) {
        Object.assign(objs, { 'name.middle': req.body.middle_name });
    }
    if (req.body.nationality_id != undefined) {
        Object.assign(objs, { 'address.nationality_id': req.body.nationality_id });
    }
    if (req.body.employee_id != undefined) {
        objs.employee_id = req.body.employee_id
    }
    if (req.body.gender != undefined) {
        objs.gender = req.body.gender
    }
    objs.correction = [];
    Object.assign(objs, { 'verification.data': constant.DONE });
    docs = await base_control.update(staff, req.body.id, objs);
    if (docs.status) {
        await common_files.com_response(res, 201, true, "Staff data's Modified successfully", "Staff data's Modified successfully");
    } else {
        common_files.com_response(res, 500, false, "Staff data Error", docs.data);
    }
};

exports.profile_status_update = async (req, res) => {
    let objs = {
        'verification.data': req.body.data
    };
    let doc = await base_control.update(staff, req.body.id, objs);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Staff documents update successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.staff_face_bio_register = async (req, res) => {
    let objs = {};
    if (req.body.face) {
        Object.assign(objs, { 'verification.face': true });
    }
    if (req.body.finger) {
        Object.assign(objs, { 'verification.finger': true });
    }
    // objs = {
    //     'verification.face': true,
    //     'verification.finger': true
    // };
    let doc = await base_control.update(staff, req.body.id, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, "Staff Face,Finger successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.staff_academic_allocation = async (req, res) => {
    let doc = { status: false, data: '' }
    // if (req.body.face) {
    //     Object.assign(objs, { 'verification.face': true });
    // }
    // if (req.body.finger) {
    //     Object.assign(objs, { 'verification.finger': true });
    // }
    req.body.data.forEach(async (element, index) => {
        let objs = {
            $push: {
                'academic_allocation': {
                    allocation_type: element.allocation_type,
                    _program_id: element._program_id,
                    _department_id: element._department_id,
                    // _department_division_id: element._department_division_id,
                    _department_subject_id: element._department_subject_id
                }
            }
        }
        if (element._department_division_id != undefined && element._department_division_id.length == 24) {
            objs = {
                $push: {
                    'academic_allocation': {
                        allocation_type: element.allocation_type,
                        _program_id: element._program_id,
                        _department_id: element._department_id,
                        _department_division_id: element._department_division_id,
                        _department_subject_id: element._department_subject_id
                    }
                }
            }
        }
        // console.log(objs);
        doc = await base_control.update_condition(staff, { _id: req.body.id }, objs);
        // console.log(doc);
        if (index == (req.body.data.length - 1)) {
            if (doc.status) {
                common_files.com_response(res, 201, true, "Staff Academic is successfully allocated", doc.data);
            } else {
                common_files.com_response(res, 500, false, "Error in Allocations", doc.data);
            }
        }
    });
};

exports.staff_employment = async (req, res) => {
    let doc = { status: false, data: '' }
    let objs = {};
    if (req.body.staff_employment_type == constant.FULL_TIME) {
        objs = {
            employment: {
                staff_type: req.body.staff_type,
                institution_role: req.body.institution_role,
                staff_employment_type: req.body.staff_employment_type,
                schedule_times: {
                    full_time: req.body.schedule_times
                }
            }
        }
    } else if (req.body.staff_employment_type == constant.PART_TIME) {
        if (req.body.staff_schedule_type == constant.BY_DATE) {
            objs = {
                employment: {
                    staff_type: req.body.staff_type,
                    institution_role: req.body.institution_role,
                    staff_employment_type: req.body.staff_employment_type,
                    schedule_times: {
                        by_date: req.body.schedule_times
                    }
                }
            }
        } else if (req.body.staff_schedule_type == constant.BY_DAY) {

            // for (let i = 0; i < req.body.schedule_times.length; i++) {
            //     let f_start = req.body.schedule_times[i].start_time;
            //     let f_end = req.body.schedule_times[i].end_time;
            //     for (let j = i; j < req.body.schedule_times.length; j++) {
            //         if (req.body.schedule_times[i].days == req.body.schedule_times[j].days) {
            //             let s_start = req.body.schedule_times[j].start_time;
            //             let s_end = req.body.schedule_times[j].end_time;
            //             console.log(i, j, 'FS SS', f_start < s_start);
            //             console.log(i, j, 'FS SE', f_start > s_end);
            //             console.log(i, j, 'FE SE', f_start < s_start);
            //             console.log(i, j, 'FE SS', f_start > s_end);
            //             // console.log(i, j, 'FS FS', f_start > s_start);
            //             if (f_start < s_start && f_start > s_end) {
            //                 console.log('First ');
            //             }
            //         }
            //     }
            // }
            objs = {
                employment: {
                    staff_type: req.body.staff_type,
                    institution_role: req.body.institution_role,
                    staff_employment_type: req.body.staff_employment_type,
                    schedule_times: {
                        by_day: req.body.schedule_times
                    }
                }
            }
        }
    }
    // console.log(objs);
    doc = await base_control.update(staff, req.body.id, objs);
    // console.log(doc);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Staff Academic is successfully allocated", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error in Allocations", doc.data);
    }
};

exports.list_filter = async (req, res) => {
    // let skips = Number(req.query.limit * (req.query.pageNo - 1));
    // let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } }
        // { $skip: skips }, { $limit: limits }
    ];

    console.log('program', req.params.program);
    console.log('time', req.params.time);
    console.log('type', req.params.type);
    aggre.push({ $project: { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 } });
    aggre.push({ $sort: { updatedAt: -1 } });
    // console.log(aggre);
    // let doc = await base_control.get_aggregate(staff, aggre);
    if (doc.status) {
        // let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ staff_formate.staff_list_array(doc.data));
        // common_files.list_all_response(res, 200, true, "staff list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* staff_formate.staff(doc.data) */);
        common_files.list_all_response(res, 200, true, "staff list", doc.data /* staff_formate.staff(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.staff_dashboard = async (req, res) => {
    let cond = { '_id': req.query.id, 'isDeleted': false };
    let proj = { _id: 1, email: 1, password: 1, name: 1 };
    // let messages = '';
    let doc = await base_control.get(staff, cond, proj);
    if (doc.status) {
        if (doc.data.name.first == 'Bharath') {
            let respon = { _id: doc.data._id, email: doc.data.email, role: 'dean' };
            common_files.com_response(res, 200, true, "Dashboard", respon/* staff_formate.staff_ID(doc.data[0]) */);
        } else {
            let respon = { _id: doc.data._id, email: doc.data.email, role: 'vice-dean' };
            common_files.com_response(res, 200, true, "Dashboard", respon/* staff_formate.staff_ID(doc.data[0]) */);
        }
    } else {
        common_files.com_response(res, 500, false, "ID Not found", "ID Not found");
    }
};