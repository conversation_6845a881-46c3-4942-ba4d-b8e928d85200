const express = require('express');
const route = express.Router();
const { getUserRegisteredDevices, deactivateUserDevice } = require('./userDevice.controller');
const { getUserDevicesSchema, deactivateUserDeviceSchema } = require('./userDevice.validation');
const { authMiddleware } = require('../../middleware');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const { validate } = require('../../middleware/validation');
const catchAsync = require('../utility/catch-async');

// User Device Management Routes
route.get(
    '/deviceList',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getUserDevicesSchema, property: 'query' }]),
    catchAsync(getUserRegisteredDevices),
);

route.put(
    '/deactivateDevice',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: deactivateUserDeviceSchema, property: 'body' }]),
    catchAsync(deactivateUserDevice),
);

module.exports = route;
