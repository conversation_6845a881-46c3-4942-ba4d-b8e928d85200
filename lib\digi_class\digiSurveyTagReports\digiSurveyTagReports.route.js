const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const { middlewareValidator } = require('../../utility/utility.service');
const {
    getExistingTagReport,
    getTagBasedQuestions,
    getSingleSurveyDetail,
    createTagReport,
    getTagReportList,
    getQuestionBasedSurveyDetails,
    excludeSurvey,
    getTagReportHeaderFilters,
    getTagReportResponseRateAnalysis,
    deleteTagReport,
    getExternalUsers,
    getTotalStaff,
    updateExternalUserList,
    checkIsEmailDuplicate,
} = require('./digiSurveyTagReports.controller');
const {
    getExistingTagReportValidator,
    getTagBasedQuestionsValidator,
    getSingleSurveyDetailValidator,
    createTagReportValidator,
    getTagReportListValidator,
    getQuestionBasedSurveyDetailValidator,
    excludeSurveyValidator,
    getTagReportHeaderFilterValidator,
    deleteTagReportValidator,
    getTagReportResponseRateAnalysisValidator,
    getExternalUserValidator,
    updateExternalUserListValidator,
    checkIsEmailDuplicateValidator,
} = require('./digiSurveyTagReports.validator');
router.get(
    '/getExistingTagReport',
    middlewareValidator(getExistingTagReportValidator),
    catchAsync(getExistingTagReport),
);
router.put(
    '/getTagBasedQuestions',
    middlewareValidator(getTagBasedQuestionsValidator),
    catchAsync(getTagBasedQuestions),
);
router.get(
    '/getQuestionBasedSurveyDetails',
    middlewareValidator(getQuestionBasedSurveyDetailValidator),
    catchAsync(getQuestionBasedSurveyDetails),
);
router.get(
    '/getSingleSurveyDetail',
    middlewareValidator(getSingleSurveyDetailValidator),
    catchAsync(getSingleSurveyDetail),
);
router.put(
    '/excludeSurvey',
    middlewareValidator(excludeSurveyValidator),
    catchAsync(excludeSurvey),
);
router.put(
    '/createTagReport',
    middlewareValidator(createTagReportValidator),
    catchAsync(createTagReport),
);
router.get(
    '/getTagReportList',
    middlewareValidator(getTagReportListValidator),
    catchAsync(getTagReportList),
);
router.put(
    '/getTagReportHeaderFilters',
    middlewareValidator(getTagReportHeaderFilterValidator),
    catchAsync(getTagReportHeaderFilters),
);
router.put(
    '/getTagReportResponseRateAnalysis',
    middlewareValidator(getTagReportResponseRateAnalysisValidator),
    catchAsync(getTagReportResponseRateAnalysis),
);
router.put(
    '/deleteTagReport',
    middlewareValidator(deleteTagReportValidator),
    catchAsync(deleteTagReport),
);
//tag reports external users
router.get(
    '/getExternalUsers',
    middlewareValidator(getExternalUserValidator),
    catchAsync(getExternalUsers),
);
router.get(
    '/getTotalStaff',
    middlewareValidator(getExternalUserValidator),
    catchAsync(getTotalStaff),
);
router.put(
    '/updateExternalUserList',
    middlewareValidator(updateExternalUserListValidator),
    catchAsync(updateExternalUserList),
);
router.get(
    '/checkIsEmailDuplicate',
    middlewareValidator(checkIsEmailDuplicateValidator),
    catchAsync(checkIsEmailDuplicate),
);

module.exports = router;
