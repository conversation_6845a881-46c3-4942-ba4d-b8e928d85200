const route = require('express').Router();
const { validate } = require('../../../utility/input-validation');

const {
    getAllModuleList,
    createModule,
    updateModule,
    deleteModule,
} = require('./module.controller');
const { roleValidator, roleIdValidator } = require('./module.validator');
const catchAsync = require('../../../utility/catch-async');

route.get('/', catchAsync(getAllModuleList));

route.post('/', validate(roleValidator), catchAsync(createModule));

route.put('/:id', validate(roleIdValidator), validate(roleValidator), catchAsync(updateModule));

route.delete('/:id', validate(roleIdValidator), catchAsync(deleteModule));

module.exports = route;
