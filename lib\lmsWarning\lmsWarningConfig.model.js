const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    LMS_WARNING_CONFIG,
    ROLE,
    USER,
    DIGI_PROGRAM,
    DIGI_COURSE,
    DS_PROGRAM_KEY,
    DS_INSTITUTION_KEY,
    DS_COURSE_KEY,
    AUTOMATIC,
    MANUAL,
    CUMULATIVE,
    INDIVIDUAL,
    TILL_THE_END_OF_LEVEL,
    START_OF_THE_NEXT_LEVEL,
    END_OF_THE_COURSES,
    START_OF_THE_NEXT_ACADEMIC_YEAR,
    END_OF_THE_ACADEMIC_YEAR,
} = require('../utility/constants');
const lmsWarningConfigSchema = new Schema({
    // new warningConfig flow additional keys
    institutionId: { type: ObjectId },
    warningConfigBasedOn: {
        type: String,
        enum: [DS_PROGRAM_KEY, DS_COURSE_KEY, DS_INSTITUTION_KEY],
    },
    programId: {
        type: ObjectId,
        ref: DIGI_PROGRAM,
    },
    courseId: {
        type: ObjectId,
        ref: DIGI_COURSE,
    },
    warningConfigLevels: [
        {
            typeName: { type: String },
            unappliedLeaveConsideredAs: { type: String },
            labelName: { type: String },
            percentage: { type: Number },
            colorCode: { type: String },
            message: { type: String },
            denialManagement: {
                accessType: { type: String },
                roleIds: [{ type: ObjectId, ref: ROLE }],
                userIds: [{ type: ObjectId, ref: USER }],
                turnAroundTime: {
                    type: String,
                    enum: [
                        TILL_THE_END_OF_LEVEL,
                        START_OF_THE_NEXT_LEVEL,
                        END_OF_THE_COURSES,
                        START_OF_THE_NEXT_ACADEMIC_YEAR,
                        END_OF_THE_ACADEMIC_YEAR,
                    ],
                },
            },
            denialCondition: { type: String, enum: [CUMULATIVE, INDIVIDUAL] },
            categoryWisePercentage: [
                {
                    categoryId: ObjectId,
                    categoryName: String,
                    percentage: Number,
                },
            ],
            notificationToParent: {
                isActive: Boolean,
                setType: { type: String, enum: [AUTOMATIC, MANUAL] },
            },
            isAdditionStaffNotify: { type: Boolean },
            notificationRoleIds: [{ type: ObjectId, ref: ROLE }],
            notificationToStudent: {
                isActive: Boolean,
                setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                sendNotificationAuthority: [{ type: ObjectId, ref: ROLE }],
            },
            notificationToStaff: { type: Boolean, default: false },
            restrictCourseAccess: { type: Boolean, default: false },
            isActive: { type: Boolean, default: true },
            acknowledgeToStudent: { type: Boolean, default: false },
            markItMandatory: { type: Boolean, default: false },
            adminRestrictedAttendance: { type: Boolean, default: false },
            scheduleStaffRestrictedAttendance: { type: Boolean, default: false },
        },
    ],
});
module.exports = model(LMS_WARNING_CONFIG, lmsWarningConfigSchema);
