const { Joi } = require('../../common/middlewares/validation');

const createDynamicComponentSchema = Joi.object({
    body: Joi.object({
        component: Joi.object({
            children: Joi.array().required(),
            hierarchies: Joi.array().required(),
        }).required(),
    }),
}).unknown(true);

const getDynamicComponentSchema = Joi.object({
    query: Joi.object({
        componentId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const deleteDynamicComponentSchema = Joi.object({
    query: Joi.object({
        componentId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

module.exports = {
    createDynamicComponentSchema,
    getDynamicComponentSchema,
    deleteDynamicComponentSchema,
};
