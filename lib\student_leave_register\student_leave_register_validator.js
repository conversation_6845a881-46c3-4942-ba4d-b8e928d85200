const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.leave_document_upload = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    student_name: Joi.string()
                        .min(3)
                        .max(20)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    leave_type: Joi.string()
                        .valid('sick', 'general', 'extra')
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    reason: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    leave_document: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    approver_comment: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    leave_start_date: Joi.date()
                        .format('YYYY-MM-DD')
                        .raw()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    leave_end_date: Joi.date()
                        .format('YYYY-MM-DD')
                        .raw()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    approval_date: Joi.date()
                        .format('YYYY-MM-DD')
                        .raw()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.getStudentDataValidater = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.getStudentDataValidater = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.notifyStudentValidater = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    comment: Joi.string()
                        .min(3)
                        .max(250)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
