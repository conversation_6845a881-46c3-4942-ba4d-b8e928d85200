const {
    Types: { ObjectId },
} = require('mongoose');

const { rubric } = require('../rubric/rubric.schema');
const { WITH_IN_WEEK, WITH_IN_DAY, ALL_WAYS_OPEN } = require('../../common/utils/constants');
const { ABSENT, PRESENT } = require('../../common/utils/enums');

const portfolioSchema = {
    institutionCalendarId: {
        type: ObjectId,
        required: true,
    },
    programId: {
        type: ObjectId,
        required: true,
    },
    courseId: {
        type: ObjectId,
        required: true,
    },
    term: {
        type: String,
        required: true,
    },
    year: {
        type: String,
        required: true,
    },
    level: {
        type: String,
        required: true,
    },
    rotation: {
        type: String,
    },
    rotationCount: {
        type: Number,
    },
    components: [
        {
            name: { type: String },
            code: { type: String },
            componentId: { type: ObjectId },
            marks: { type: Number },
            percentage: { type: Number },
            hasExtraEntries: { type: Boolean, default: false },
            hasApproved: { type: Boolean, default: false },
            hasReflection: { type: Boolean, default: false },
            hasReview: { type: Boolean, default: false },
            deliveryTypes: [
                {
                    sessionId: { type: ObjectId },
                    deliveryTypeId: { type: ObjectId },
                    deliveryTypeName: { type: String },
                    deliveryTypeSymbol: { type: String },
                },
            ],
            hasConsolidateEvaluation: { type: Boolean, default: false },
            rubrics: [rubric],
            hasGlobalRubric: { type: Boolean, default: false },
            hasNoGrade: { type: Boolean, default: false },
            timeline: {
                type: String,
                enum: [WITH_IN_WEEK, WITH_IN_DAY, ALL_WAYS_OPEN],
            },
            children: [
                {
                    name: { type: String },
                    weightage: { type: Number },
                    marks: { type: Number },
                    formId: { type: ObjectId },
                    templateId: { type: ObjectId },
                    description: { type: String },
                    caseDate: { type: Date },
                    caseCategory: { type: String },
                    clinicalSetting: { type: String },
                    patientId: { type: String },
                    supervisor: { type: String },
                    learningPoints: { type: String },
                    followUp: { type: String },
                    isNewEntry: { type: Boolean },
                    awardedMarks: { type: Number },
                    startDate: { type: Date },
                    endDate: { type: Date },
                    isAssigned: { type: Boolean, default: false },
                    rubrics: [rubric],
                    attendance: { type: String, enum: [ABSENT, PRESENT] },
                    passMarks: { type: Number },
                    mustPass: { type: Boolean, default: false },
                    globalRubricAwardedPoints: { type: Number },
                    globalRubricTotalPoints: { type: Number },
                },
            ],
            attendance: { type: String, enum: [ABSENT, PRESENT] },
            awardedMarks: { type: Number },
        },
    ],
    totalMarks: {
        type: Number,
        default: 0,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    status: {
        type: String,
    },
    publishedDate: { type: Date },
    isReportGenerated: { type: Boolean, default: false },
    grades: [
        {
            from: { type: Number },
            to: { type: Number },
            name: { type: String },
            isFail: { type: Boolean },
        },
    ],
};

module.exports = { portfolioSchema };
