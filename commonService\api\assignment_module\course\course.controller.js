const {
    DIGI_COURSE,
    COURSE_SCHEDULE,
    PROGRAM_CALENDAR,
} = require('../../../../lib/utility/constants');
const { getUserRoleProgramList } = require('../../../../lib/utility/utility.service');
const { convertToMongoObjectId } = require('../../../utility/common');
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const _ = require('lodash');
const {
    getCourseList,
    getSubjectList,
    getSloPlusCloBySessionIdAndCourseIdForAssessment,
    getCourseListForStaff,
    getTaxonomyList,
} = require('./course.service');
/* const getCourses = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, staffId, isAdmin } = query;
        const getCourseLists = await getCourseList({
            _institution_id,
            programId,
            staffId,
            isAdmin,
        });
        if (!getCourseLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getCourseLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
}; */
const getCourses = async ({ headers = {}, query = {} }) => {
    const { _institution_id, _institution_calendar_id } = headers;
    const { staffId, programId, roleId } = query;
    const { isCourseAdmin, isProgramAdmin, userCourseIds } = await getUserRoleProgramList({
        _institution_id,
        user_id: staffId,
        role_id: roleId,
        institutionCalendarId: [_institution_calendar_id],
    });
    if (!isProgramAdmin && !isCourseAdmin)
        return {
            statusCode: 404,
            message: 'NOT_AN_ADMIN',
        };
    const courseQuery = {};
    if (isProgramAdmin) courseQuery._program_id = convertToMongoObjectId(programId);
    if (isCourseAdmin) {
        courseQuery._id = {
            $in: userCourseIds.map((courseElement) =>
                convertToMongoObjectId(courseElement._course_id),
            ),
        };
        courseQuery._program_id = convertToMongoObjectId(programId);
    }
    const courseLists = await courseSchema.find(
        { ...courseQuery, isDeleted: false, isActive: true },
        {
            courseId: '$_id',
            courseName: '$course_name',
            courseCode: '$course_code',
            _id: 0,
        },
    );
    return {
        statusCode: 200,
        data: courseLists,
    };
};
const getSubjects = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId } = query;
        const getSubjectLists = await getSubjectList({ _institution_id, courseId });
        if (!getSubjectLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getSubjectLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCloSlo = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId, sessionIds } = body;
        const getSubjectLists = await getSloPlusCloBySessionIdAndCourseIdForAssessment({
            _institution_id,
            courseId,
            sessionIds,
        });
        if (!getSubjectLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getSubjectLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

/* const getCoursesForStaff = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { staffId, _institution_calendar_id, roleId } = query;
        const getCourseLists = await getCourseListForStaff({
            _institution_id,
            staffId,
            _institution_calendar_id,
            roleId,
        });
        if (!getCourseLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getCourseLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
}; */
const getCoursesForNormalStaff = async ({ _institution_id, _institution_calendar_id, staffId }) => {
    let courseList = await courseScheduleSchema.aggregate([
        {
            $match: {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'staffs._staff_id': convertToMongoObjectId(staffId),
                isDeleted: false,
            },
        },
        { $unwind: '$subjects' },
        {
            $project: {
                _id: 0,
                courseName: '$course_name',
                programId: '$_program_id',
                programName: '$program_name',
                term: '$term',
                course_code: '$course_code',
                level_no: '$level_no',
                year_no: '$year_no',
                _institution_calendar_id: '$_institution_calendar_id',
                rotation: '$rotation',
                rotation_count: '$rotation_count',
                courseId: '$_course_id',
                subjectIds: '$subjects',
            },
        },
    ]);

    const courseGroupedData = {};
    for (const courseItem of courseList) {
        const key = `${courseItem.courseId}_${courseItem.term}_${courseItem.rotation_count}`;
        if (!courseGroupedData[key]) {
            courseGroupedData[key] = {
                ...courseItem,
                subjects: [
                    {
                        subjectId: courseItem.subjectIds._subject_id,
                        subjectName: courseItem.subjectIds.subject_name,
                    },
                ],
            };
        } else {
            courseGroupedData[key].subjects.push({
                subjectId: courseItem.subjectIds._subject_id,
                subjectName: courseItem.subjectIds.subject_name,
            });
        }
    }
    courseList = Object.values(courseGroupedData);
    return {
        statusCode: 200,
        message: 'DATA_RETRIEVED',
        data: { staffCourses: courseList, todaysDate: new Date() },
    };
};
const unWindLevel = {
    $unwind: '$level',
};
const unWindCourse = [
    {
        $addFields: {
            rotationCourses: {
                $cond: [
                    { $eq: ['$level.rotation', 'no'] },
                    [{ course: '$level.course' }],
                    '$level.rotation_course',
                ],
            },
        },
    },
    {
        $unwind: {
            path: '$rotationCourses',
            preserveNullAndEmptyArrays: true,
        },
    },
    {
        $unwind: {
            path: '$rotationCourses.course',
            preserveNullAndEmptyArrays: true,
        },
    },
];
const projectData = {
    $project: {
        courseName: '$rotationCourses.course.courses_name',
        programId: '$_program_id.0',
        programName: '$level.curriculum',
        term: '$level.term',
        course_code: '$rotationCourses.course.courses_number',
        level_no: '$level.level_no',
        year_no: '$level.year',
        _institution_calendar_id: '$_institution_calendar_id',
        rotation: '$level.rotation',
        rotation_count: '$level.rotation_count',
        courseId: '$rotationCourses.course._course_id',
    },
};
const courseProject = {
    participating: 1,
    administration: 1,
    _program_id: 1,
    _id: 1,
};
const updateCourseDetails = ({ courseLists, individualCourse, programData }) => {
    let courseDetails = {};
    const subjects = [];
    if (courseLists[individualCourse] && courseLists[individualCourse][0]) {
        courseDetails = courseLists[individualCourse][0];
        let adminSubjectId;
        if (courseDetails.administration._subject_id) {
            adminSubjectId = courseDetails.administration._subject_id.toString();
            subjects.push({
                subjectId: adminSubjectId,
                subjectName: courseDetails.administration.subject_name,
                departmentId: courseDetails.administration._department_id,
            });
        }

        if (courseDetails.participating.length) {
            for (const participating of courseDetails.participating) {
                const participatingId = participating._subject_id.toString();
                if (adminSubjectId && participatingId === adminSubjectId) continue;
                subjects.push({
                    subjectId: participating._subject_id.toString(),
                    subjectName: participating.subject_name,
                    departmentId: participating._department_id,
                });
            }
        }
    }
    return {
        ...programData,
        subjects,
        ...(Object.entries(courseDetails).length && {
            programName: courseDetails._program_id.name,
            programId: courseDetails._program_id._id,
        }),
    };
};
const matchCourse = ({ programLists, courseLists }) => {
    const staffCourses = [];
    for (const programCourse in programLists) {
        if (programCourse) {
            let rotationIndex = 1;
            for (const programData of programLists[programCourse]) {
                if (programData.rotation === 'yes') {
                    programData.rotation_count = rotationIndex;
                    rotationIndex++;
                }
                staffCourses.push(
                    updateCourseDetails({
                        courseLists,
                        individualCourse: programCourse,
                        programData,
                    }),
                );
            }
        }
    }
    return {
        statusCode: 200,
        message: 'DATA_RETRIEVED',
        data: { staffCourses, todaysDate: new Date() },
    };
};
const getCoursesFromProgramIds = async ({ userProgramIds, _institution_calendar_id }) => {
    const programIds = userProgramIds.map((userProgramId) => convertToMongoObjectId(userProgramId));
    const institutionCalendarId = convertToMongoObjectId(_institution_calendar_id);
    let programLists = await programCalendarSchema.aggregate([
        {
            $match: {
                _program_id: {
                    $in: programIds,
                },
                _institution_calendar_id: institutionCalendarId,
                isActive: true,
                isDeleted: false,
            },
        },
        unWindLevel,
        ...unWindCourse,
        projectData,
    ]);
    programLists = _.groupBy(programLists, 'courseId');
    let courseLists = await courseSchema
        .find(
            {
                _program_id: { $in: programIds },
                isDeleted: false,
                isActive: true,
            },
            courseProject,
        )
        .populate({ path: '_program_id', select: { name: 1 } })
        .lean();
    courseLists = _.groupBy(courseLists, '_id');
    return matchCourse({ programLists, courseLists });
};

const getCoursesFromCourseIds = async ({
    userCourseIds,
    userProgramIds,
    _institution_calendar_id,
}) => {
    const programIds = userProgramIds.map((userProgramId) => convertToMongoObjectId(userProgramId));
    let courseIds = userCourseIds.map((courseElement) =>
        convertToMongoObjectId(courseElement._course_id),
    );
    const institutionCalendarId = convertToMongoObjectId(_institution_calendar_id);
    let programLists = await programCalendarSchema.aggregate([
        {
            $match: {
                _program_id: {
                    $in: programIds,
                },
                _institution_calendar_id: institutionCalendarId,
                isActive: true,
                isDeleted: false,
            },
        },
        unWindLevel,
        ...unWindCourse,
        projectData,
        {
            $match: {
                courseId: { $in: courseIds },
            },
        },
    ]);
    programLists = _.groupBy(programLists, 'courseId');
    courseIds = Object.keys(programLists).map((courseId) => convertToMongoObjectId(courseId));
    let courseLists = await courseSchema
        .find(
            {
                _id: { $in: courseIds },
                isDeleted: false,
                isActive: true,
            },
            courseProject,
        )
        .populate({ path: '_program_id', select: { name: 1 } })
        .lean();
    courseLists = _.groupBy(courseLists, '_id');

    return matchCourse({ programLists, courseLists });
};
const getCoursesForStaff = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { staffId, roleId } = query;
        const { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin } =
            await getUserRoleProgramList({
                _institution_id,
                user_id: staffId,
                role_id: roleId,
                institutionCalendarId: [_institution_calendar_id],
            });
        if (isProgramAdmin && userProgramIds.length)
            return getCoursesFromProgramIds({ userProgramIds, _institution_calendar_id });
        if (isCourseAdmin && userCourseIds.length)
            return getCoursesFromCourseIds({
                userCourseIds,
                userProgramIds,
                _institution_calendar_id,
            });
        return getCoursesForNormalStaff({ _institution_id, _institution_calendar_id, staffId });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getTaxonomy = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { search } = query;
        const getTaxonomyLists = await getTaxonomyList({
            _institution_id,
            search,
        });
        if (!getTaxonomyLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getTaxonomyLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    getCourses,
    getSubjects,
    getCloSlo,
    getCoursesForStaff,
    getTaxonomy,
};
