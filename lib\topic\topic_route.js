const express = require('express');
const route = express.Router();
const topic = require('./topic_controller');
const validater = require('./topic_validator');
route.post('/list', topic.list_values);
route.get('/:id', validater.topic_id, topic.list_id);
route.get('/', topic.list);
route.post('/', validater.topic, topic.insert);
route.put('/:id', validater.topic_id, validater.topic, topic.update);
route.delete('/:id', validater.topic_id, topic.delete);

module.exports = route;