const FormModel = require('../form/form.model');
const StudentResponseModel = require('./student-response.model');
const UserModel = require('../../../lib/models/user');
const PortfolioModel = require('../portfolio/portfolio.model');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');

const { PUBLISHED, ON_GOING, COMPLETED } = require('../../common/utils/enums');
const {
    NotFoundError,
    BadRequestError,
    UpdateFailedError,
} = require('../../common/utils/api_error_util');
const { getAssignedUserSections } = require('../form/form.helper');
const { convertToMongoObjectId } = require('../../common/utils/common.util');
const { STUDENT } = require('../../common/utils/constants');
const { checkDocumentsExists } = require('../../base/base.helper');

const startForm = async ({ formId, portfolioId, componentId, childrenId, userId, scheduleId }) => {
    const query = {
        formId,
        'student._id': userId,
        portfolioId,
        componentId,
        childrenId,
        ...(scheduleId && { scheduleId }),
        status: ON_GOING,
    };
    const formExists = await checkDocumentsExists({
        Model: StudentResponseModel,
        query,
        canThrowError: false,
    });
    if (formExists) {
        throw new BadRequestError('ALREADY_FORM_STARTED');
    }

    const updateStudentResponse = await StudentResponseModel.findOneAndUpdate(
        query,
        {
            $set: {
                status: ON_GOING,
                'formTimestamps.firstStartedAt': new Date(),
            },
        },
        {
            projection: { _id: 1, student: 1 },
        },
    ).lean();

    return updateStudentResponse;
};

const updateStudentResponse = async ({ studentResponseId, pageId, sectionId, section, userId }) => {
    const studentResponse = await StudentResponseModel.updateOne(
        { _id: studentResponseId, 'student._id': userId },
        {
            $set: {
                'pages.$[pageId].elements.$[sectionId].elements': section.elements,
                'formTimestamps.lastUpdated': new Date(),
            },
        },
        {
            arrayFilters: [
                { 'pageId._id': convertToMongoObjectId(pageId) },
                { 'sectionId._id': convertToMongoObjectId(sectionId) },
            ],
        },
    );
    if (!studentResponse.modifiedCount) {
        throw new UpdateFailedError('FAILED_TO_UPDATE_STUDENT_RESPONSE');
    }
};

const submitForm = async ({ studentResponseId, pages, userId }) => {
    await StudentResponseModel.updateOne(
        { _id: studentResponseId, 'student._id': userId },
        {
            $set: { status: COMPLETED, pages, 'formTimestamps.submittedAt': new Date() },
            $push: {
                statusHistory: {
                    status: COMPLETED,
                    createdAt: new Date(),
                    userId: convertToMongoObjectId(userId),
                },
            },
        },
    );
};

const getStudentResponse = async ({
    formId,
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    userId,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        { _id: portfolioId, isDeleted: false },
        { programId: 1, courseId: 1, institutionCalendarId: 1 },
    ).lean();
    if (!portfolio) {
        throw new NotFoundError('PORTFOLIO_NOT_FOUND');
    }

    const program = await ProgramModel.findOne(
        { _id: portfolio?.programId, isDeleted: false },
        { name: 1, code: 1 },
    ).lean();

    const course = await CourseModel.findOne(
        { _id: portfolio?.courseId, isDeleted: false },
        { name: '$course_name', code: '$course_code' },
    );

    const studentResponse = await StudentResponseModel.findOne(
        {
            formId,
            'student._id': userId,
            portfolioId,
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
        },
        { student: 1, pages: 1, status: 1, totalMarks: 1, title: 1, type: 1 },
    ).lean();
    if (!studentResponse) {
        throw new NotFoundError('STUDENT_RESPONSE_NOT_FOUND');
    }

    return { ...studentResponse, program, course };
};

module.exports = {
    startForm,
    updateStudentResponse,
    submitForm,
    getStudentResponse,
};
