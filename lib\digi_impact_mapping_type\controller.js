const constants = require('../utility/constants');
const { MAPPING_TYPES } = constants;
const controller = require('../base/base_controller');
const { dsGet, dsGetAll, dsGetCount, dsInsert, dsUpdate } = controller;
const { dsDeleteOne, dsGetAllWithSort } = controller;
const ImpactMappingTypeFormat = require('./formate');
const ImpactMappingType = require('mongoose').model(MAPPING_TYPES.IMPACT);
const { sendResult, sendErrorResponse, sendResultWithRequest } = require('../utility/common');
const { DIGI_CURRICULUM } = require('../utility/constants');
const CURRICULUM = require('mongoose').model(DIGI_CURRICULUM);
const { dsGetAllWithSortAsJSON } = require('../base/base_controller');

const project = { name: 1, code: 1, description: 1, order: 1 };
const errorMsg = constants.DS_INTERNAL_SERVER_ERROR;

exports.getAll = async (req, res) => {
    const query = { isDeleted: false };
    try {
        const result = await dsGetAllWithSort(ImpactMappingType, query, project, {
            order: 1,
        });
        const formattedResponse = ImpactMappingTypeFormat.all(result.data);
        sendResultWithRequest(req, res, 200, result.message, formattedResponse);
    } catch (e) {
        sendErrorResponse(res, 500, errorMsg, e);
    }
};

exports.get = async (req, res) => {
    try {
        const { id } = req.params;
        const query = { _id: id, isDeleted: false };
        const result = await dsGet(ImpactMappingType, query, project);
        if (result.success) {
            const response = ImpactMappingTypeFormat.single(result.data);
            sendResult(res, 200, result.message, response);
        } else sendResult(res, 404, result.message);
    } catch (e) {
        sendErrorResponse(res, 500, errorMsg, e);
    }
};

exports.insert = async (req, res) => {
    try {
        const { name, code, description } = req.body;
        const existingDoc = await dsGet(ImpactMappingType, { name, isDeleted: false }, { name: 1 });
        if (existingDoc.success) sendErrorResponse(res, 409, constants.DS_NAME_ALREADY_EXISTS);
        else {
            const doc = await dsGetAll(ImpactMappingType, {}, { name: 1 });
            const order = doc.data.length;
            const data = { name, code, description, order };
            const result = await dsInsert(ImpactMappingType, data);
            if (result.success) sendResultWithRequest(req, res, 200, result.message);
            else sendResultWithRequest(req, res, 500, result.message);
        }
    } catch (e) {
        sendErrorResponse(res, 500, errorMsg, e);
    }
};

exports.update = async (req, res) => {
    try {
        const objectId = req.params.id;
        const { name, code, description } = req.body;
        const query = { _id: objectId };
        const idCheck = await dsGetCount(ImpactMappingType, query);
        if (idCheck > 0) {
            const data = { name, code, description };
            const result = await dsUpdate(ImpactMappingType, objectId, data);
            if (result.success) sendResultWithRequest(req, res, 200, result.message);
            else sendResultWithRequest(req, res, 404, constants.DS_NOT_FOUND);
        } else sendErrorResponse(res, 404, constants.DS_INVALID_ID);
    } catch (e) {
        sendErrorResponse(res, 500, errorMsg, e);
    }
};

const isAssignedType = async (_id) => {
    const get = dsGetAllWithSortAsJSON;
    const query = { isDeleted: false, _id };
    const iMapTypeResult = (await get(ImpactMappingType, query)).data;
    const iMapType = iMapTypeResult[0] || undefined;
    if (iMapType) {
        const query_1 = {
            'framework.domains.plo.clos.mapped_value': iMapType.code,
            isDeleted: false,
        };
        const curriculumResult = await get(CURRICULUM, query_1, {});
        return curriculumResult.data.length > 0;
    }
    return false;
};

exports.delete = async (req, res) => {
    try {
        const objectId = req.params.id;
        const result = await isAssignedType(objectId);
        if (result)
            return sendErrorResponse(
                res,
                406,
                `Impact mapping type is selected in CLO and PLO mapping, can't be deleted`,
                `Impact mapping type is selected in CLO and PLO mapping, can't be deleted`,
                `Impact mapping type is selected in CLO and PLO mapping, can't be deleted`,
            );
        const query = { _id: objectId };
        const idCheck = await dsGetCount(ImpactMappingType, query);
        if (idCheck > 0) {
            const result = await dsDeleteOne(ImpactMappingType, objectId);
            if (result.success) sendResultWithRequest(req, res, 200, result.message);
            else sendResultWithRequest(req, res, 404, constants.DS_NOT_FOUND);
        } else sendErrorResponse(res, 404, constants.DS_INVALID_ID);
    } catch (e) {
        sendErrorResponse(res, 500, errorMsg, e);
    }
};
