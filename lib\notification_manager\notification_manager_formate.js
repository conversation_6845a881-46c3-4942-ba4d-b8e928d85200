let notification_manager_formate = require('./notification_manager_formate');

module.exports = {
    notification_manager: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                batch: element.batch,
                notification_manager: notification_manager_formate.notification_manager_ID_Only(element.notification_manager),
                start_date: element.start_date,
                end_date: element.end_date,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    notification_manager_ID: (doc) => {
        let obj = {
            _id: doc._id,
            batch: doc.batch,
            notification_manager: notification_manager_formate.notification_manager_ID_Only(doc.notification_manager),
            start_date: doc.start_date,
            end_date: doc.end_date,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    notification_manager_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            batch: doc.batch,
            notification_manager: doc._notification_manager_id,
            start_date: doc.start_date,
            end_date: doc.end_date,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    notification_manager_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                batch: element.batch,
                notification_manager: element._notification_manager_id,
                start_date: element.start_date,
                end_date: element.end_date,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}