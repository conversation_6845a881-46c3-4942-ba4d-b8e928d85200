const Joi = require('joi');
const {
    objectIdRQSchema,
    booleanRQSchema,
    numberRQSchema,
} = require('../utility/validationSchemas');

exports.getCourseWiseFacialDataValidator = Joi.object({
    courseId: objectIdRQSchema,
    programId: objectIdRQSchema,
});

exports.updateCourseWiseFacialSettingValidator = Joi.object({
    courseId: objectIdRQSchema,
    programId: objectIdRQSchema,
    studentFacial: booleanRQSchema,
    staffFacial: booleanRQSchema,
    sessionChange: booleanRQSchema,
    autoEndAttendance: numberRQSchema,
});
