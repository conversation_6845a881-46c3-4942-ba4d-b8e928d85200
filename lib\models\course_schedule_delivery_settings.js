const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    INSTITUTION,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_SESSION_ORDER,
    DIGI_COURSE,
    INFRASTRUCTURE_MANAGEMENT,
    USER,
    COURSE_SCHEDULE_DELIVERY_SETTINGS,
    // SCHEDULE_TYPES: { SCHEDULE, REGULAR, EVENT, SUPPORT_SESSION },
    // ONLINE,
    // ON_SITE,
    TIME_GROUP_BOOKING_TYPE: { ONSITE, REMOTE },
    GENDER: { MALE, FEMALE, BOTH },
    AM,
    PM,
    STUDENT_GROUP,
    DAYS: { SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY },
    REMOTE_PLATFORM: { ZO<PERSON>, TEAMS },
} = require('../utility/constants');

const course_schedule_delivery_settings = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        program_name: {
            type: String,
            // required: true,
        },
        _topic_id: {
            type: Schema.Types.ObjectId,
        },
        topic: {
            type: String,
            trim: true,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        _session_type_id: {
            type: Schema.Types.ObjectId,
            required: true,
        },
        _delivery_id: {
            type: Schema.Types.ObjectId,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        year_no: {
            type: String,
            required: true,
        },
        level_no: {
            type: String,
            required: true,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        session: [
            {
                _session_id: {
                    type: Schema.Types.ObjectId,
                    ref: DIGI_SESSION_ORDER,
                    required: true,
                },
                s_no: { type: Number },
                delivery_symbol: { type: String },
                delivery_no: { type: Number },
                session_type: { type: String },
                session_topic: { type: String },
                student_groups: [
                    {
                        group_id: {
                            type: Schema.Types.ObjectId,
                        },
                        gender: {
                            type: String,
                            enum: [MALE, FEMALE, BOTH],
                        },
                        group_no: Number,
                        group_name: String,
                        session_group: [
                            {
                                session_group_id: {
                                    type: Schema.Types.ObjectId,
                                },
                                group_no: Number,
                                group_name: String,
                            },
                        ],
                    },
                ],
            },
        ],
        occurrence: [
            {
                day: {
                    type: String,
                    enum: [SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY],
                },
                start: {
                    hour: {
                        type: Number,
                    },
                    minute: {
                        type: Number,
                    },
                    format: {
                        type: String,
                        enum: [AM, PM],
                    },
                },
                end: {
                    hour: {
                        type: Number,
                    },
                    minute: {
                        type: Number,
                    },
                    format: {
                        type: String,
                        enum: [AM, PM],
                    },
                },
            },
        ],
        mode: { type: String, enum: [ONSITE, REMOTE] },
        remotePlatform: { default: TEAMS, type: String, enum: [null, ZOOM, TEAMS] },
        _student_group_id: {
            type: Schema.Types.ObjectId,
            ref: STUDENT_GROUP,
        },
        subjects: [
            {
                _subject_id: {
                    type: Schema.Types.ObjectId,
                    ref: DIGI_DEPARTMENT_SUBJECT,
                },
                subject_name: { type: String },
            },
        ],
        _infra_id: {
            type: Schema.Types.ObjectId,
            ref: INFRASTRUCTURE_MANAGEMENT,
        },
        infra_name: { type: String },
        staffs: [
            {
                _staff_id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                staff_name: {
                    first: {
                        type: String,
                        required: true,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                },
            },
        ],
        attendanceTakingStaff: [
            {
                staffId: { type: Schema.Types.ObjectId, ref: USER },
                staffName: {
                    first: {
                        type: String,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        outsideCampus: { type: Boolean, default: false },
        campusDetails: {
            mobile: { type: Number },
            email: { type: String },
            byDevice: { type: String },
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(
    COURSE_SCHEDULE_DELIVERY_SETTINGS,
    course_schedule_delivery_settings,
);
