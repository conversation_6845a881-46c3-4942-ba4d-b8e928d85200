const express = require('express');
const route = express.Router();
const course_group = require('./digi_course_group_controller');
const validator = require('./digi_course_group_validator');
const {
    userPolicyAuthentication,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');

route.post(
    '/',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    course_group.insert,
);
route.put(
    '/ungroup',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    course_group.ungroup,
);
route.get('/', [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])], course_group.list);

module.exports = route;
