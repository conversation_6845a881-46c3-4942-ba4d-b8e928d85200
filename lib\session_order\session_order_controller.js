const constant = require('../utility/constants');
var session_order = require('mongoose').model(constant.SESSION_ORDER);
var program = require('mongoose').model(constant.PROGRAM);
var course = require('mongoose').model(constant.COURSE);
var department_subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const session_order_formate = require('./session_order_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        { $unwind: { path: '$course', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        // { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { session_name: 1, s_no: 1 } },
        { $skip: skips }, { $limit: limits }
    ];
    let doc = await base_control.get_aggregate(session_order, aggre);
    // console.log(doc);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_order_formate.session_order(doc.data));
        common_files.list_all_response(res, 200, true, "session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* session_order_formate.session_order(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        { $unwind: { path: '$course', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        // { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { session_name: 1, s_no: 1 } }
    ];
    let doc = await base_control.get_aggregate(session_order, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "session_order details", /* doc.data */session_order_formate.session_order_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "session_order details", doc.data/* session_order_formate.session_order_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let docs = { status: false };
    let status, datas;
    let checks = await base_control.check_id(program, { _id: req.body._program_id, 'isDeleted': false });
    let checks1 = await base_control.check_id(course, { _id: req.body._course_id, 'isDeleted': false });
    console.log(checks, checks1);
    if (checks.status && checks1.status) {
        await req.body.data.forEach(async (doc, index) => {
            let objects = {
                s_no: doc.s_no,
                delivery_symbol: doc.delivery_symbol,
                delivery_no: doc.delivery_no,
                session_name: doc.session_name,
                _subject_id: doc._subject_id,
                contact_hours: doc.contact_hours,
                week: doc.week,
                _course_id: ObjectId(req.body._course_id),
                _program_id: ObjectId(req.body._program_id)
            };
            // console.log(objects);
            if (doc.id == '' && doc.id.length == 0) {
                docs = await base_control.insert(session_order, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            } else {
                docs = await base_control.update(session_order, doc.id, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    let aggre = [
                        { $match: { '_course_id': ObjectId(req.body._course_id), '_program_id': ObjectId(req.body._program_id) } },
                        { $match: { 'isDeleted': false } }
                    ];
                    let session_orders_data = await base_control.get_aggregate(session_order, aggre);
                    common_files.com_response(res, 201, true, "session type Added successfully", session_orders_data);
                } else {
                    common_files.com_response(res, 500, false, "Error", docs.data);
                }
            }
        });
    } else {
        common_files.com_response(res, 404, false, "Error id not match", 'Check Parsing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }, checks1 = { status: true }, checks2 = { status: true };
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (req.body._couser_id != undefined) {
        checks1 = await base_control.check_id(course, { _id: { $in: req.body._couser_id }, 'isDeleted': false });
    }
    if (req.body._subject_id != undefined) {
        checks2 = await base_control.check_id(department_subject, { _id: { $in: req.body._subject_id }, 'isDeleted': false });
    }
    if (checks.status && checks1.status && checks2.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(session_order, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "session_order update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parsing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(session_order, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "session_order deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(session_order, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "session_order List", session_order_formate.session_order_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_session_order_division_subject = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(session_order, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "session_order List", session_order_formate.session_order_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(req.params.id) } },
        { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        { $unwind: { path: '$course', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        // { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { session_name: 1, s_no: 1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(session_order, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_order_formate.session_order(doc.data));
        // common_files.list_all_response(res, 200, true, "session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* session_order_formate.session_order(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_course = async (req, res) => {
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_course_id': ObjectId(req.params.id) } },
        // { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        // { $unwind: { path: '$course', preserveNullAndEmptyArrays: true } },
        // { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        // { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
        // { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        // { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { session_name: 1, s_no: 1 } }
    ];
    let doc = await base_control.get_aggregate(session_order, aggre);
    if (doc.status) {
        // console.log(doc.data);
        common_files.com_response(res, 200, true, 'course wise session order list', /* doc.data */ session_order_formate.session_order_ID_Array_Only(doc.data));
        // common_files.list_all_response(res, 200, true, "course session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_order_formate.session_order(doc.data));
        // common_files.list_all_response(res, 200, true, "session_order list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* session_order_formate.session_order(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};