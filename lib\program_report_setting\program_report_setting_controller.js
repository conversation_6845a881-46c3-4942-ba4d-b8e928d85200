const ProgramReportSettings = require('../models/program_report_setting');
const {
    sendResponse,
    convertToMongoObjectId,
    sendResponseWithRequest,
} = require('../utility/common');
const { ObjectPreparationFromRequest } = require('../utility/common_functions');
const { getProgramReport, getProgramReportBenchMark } = require('./program_report_setting_service');
const { allProgramReportSetting, clearItem } = require('../../service/cache.service');
exports.getProgramReportSetting = async (req, res) => {
    try {
        const { _program_id, _institution_id } = req.query;

        const programReportSettings = await getProgramReport(_program_id, _institution_id);

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Program report settings',
            programReportSettings,
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Server Error', error.toString());
    }
};

exports.updateProgramReportSetting = async (req, res) => {
    try {
        const { _program_id, _institution_id } = req.query;
        const {
            scalePoints,
            questions,
            status,
            step,
            selfEvaluationSurvey,
            sessionExperienceSurvey,
            activity,
            courseAdminUserPermission,
            rattingScaleForSurvey,
            benchMarkForSurveyAndActivities,
            noOfQuestion,
            benchMarkValue,
            benchMarks,
            benchMarkStatus,
            scalePointStatus,
        } = req.body;
        let updateDetails = {};
        if (status) {
            updateDetails.status = status;
        }
        if (benchMarkStatus) {
            updateDetails.benchMarkStatus = benchMarkStatus;
        }
        if (scalePointStatus) {
            updateDetails.scalePointStatus = scalePointStatus;
        }
        if (scalePoints && scalePoints.length) {
            updateDetails.scalePoints = scalePoints;
            updateDetails.scalePointStructure = scalePoints.length;
        }
        if (questions && questions.length) {
            updateDetails.questions = questions;
            updateDetails.noOfQuestion = noOfQuestion;
        }
        if (step) {
            updateDetails.step = step;
        }
        if (benchMarkForSurveyAndActivities) {
            updateDetails.benchMarkForSurveyAndActivities = benchMarkForSurveyAndActivities;
        }
        if (benchMarkValue) {
            updateDetails.benchMarkValue = benchMarkValue;
        }
        if (benchMarks) {
            updateDetails.benchMarks = benchMarks;
        }
        if (activity) {
            updateDetails = ObjectPreparationFromRequest(updateDetails, activity, 'activity');
        }
        if (courseAdminUserPermission || courseAdminUserPermission !== undefined) {
            updateDetails.courseAdminUserPermission = courseAdminUserPermission;
        }
        const programReport = await getProgramReport(_program_id, _institution_id);
        if (selfEvaluationSurvey) {
            if (!selfEvaluationSurvey.isChecked && selfEvaluationSurvey.isChecked !== undefined) {
                updateDetails['selfEvaluationSurvey.scalePoints'] = [];
                updateDetails['selfEvaluationSurvey.benchMarks'] = [];
                updateDetails['selfEvaluationSurvey.questions'] = [];
                if (programReport && programReport.selfEvaluationSurvey.scalePointStatus) {
                    updateDetails['selfEvaluationSurvey.scalePointStatus'] = 'draft';
                }
                if (programReport && programReport.selfEvaluationSurvey.benchMarkStatus) {
                    updateDetails['selfEvaluationSurvey.benchMarkStatus'] = 'draft';
                }
            }
            updateDetails = ObjectPreparationFromRequest(
                updateDetails,
                selfEvaluationSurvey,
                'selfEvaluationSurvey',
            );
        }
        if (sessionExperienceSurvey) {
            if (sessionExperienceSurvey.separateRatingScaleValueForEachQuestion) {
                updateDetails['sessionExperienceSurvey.scalePoints'] = [];
                updateDetails['sessionExperienceSurvey.questions'] = [];
            }
            if (
                !sessionExperienceSurvey.isChecked &&
                sessionExperienceSurvey.isChecked !== undefined
            ) {
                updateDetails['sessionExperienceSurvey.scalePoints'] = [];
                updateDetails['sessionExperienceSurvey.benchMarks'] = [];
                updateDetails['sessionExperienceSurvey.questions'] = [];
                if (programReport && programReport.sessionExperienceSurvey.scalePointStatus) {
                    updateDetails['sessionExperienceSurvey.scalePointStatus'] = 'draft';
                }
                if (programReport && programReport.sessionExperienceSurvey.benchMarkStatus) {
                    updateDetails['sessionExperienceSurvey.benchMarkStatus'] = 'draft';
                }
            }
            updateDetails = ObjectPreparationFromRequest(
                updateDetails,
                sessionExperienceSurvey,
                'sessionExperienceSurvey',
            );

            if (
                !sessionExperienceSurvey.separateRatingScaleValueForEachQuestion &&
                sessionExperienceSurvey.separateRatingScaleValueForEachQuestion !== undefined
            ) {
                updateDetails['sessionExperienceSurvey.questions'] = [];
            }
        }

        if (rattingScaleForSurvey) {
            updateDetails.rattingScaleForSurvey = rattingScaleForSurvey;
            if (rattingScaleForSurvey === 'common') {
                updateDetails['selfEvaluationSurvey.scalePoints'] = [];
                updateDetails['selfEvaluationSurvey.benchMarks'] = [];
                updateDetails['sessionExperienceSurvey.scalePoints'] = [];
                updateDetails['sessionExperienceSurvey.benchMarks'] = [];
                updateDetails['sessionExperienceSurvey.questions'] = [];
                if (programReport && programReport.sessionExperienceSurvey.scalePointStatus) {
                    updateDetails['sessionExperienceSurvey.scalePointStatus'] = 'draft';
                }
                if (programReport && programReport.sessionExperienceSurvey.benchMarkStatus) {
                    updateDetails['sessionExperienceSurvey.benchMarkStatus'] = 'draft';
                }
                if (programReport && programReport.selfEvaluationSurvey.scalePointStatus) {
                    updateDetails['selfEvaluationSurvey.scalePointStatus'] = 'draft';
                }
                if (programReport && programReport.selfEvaluationSurvey.benchMarkStatus) {
                    updateDetails['selfEvaluationSurvey.benchMarkStatus'] = 'draft';
                }
            }
            if (rattingScaleForSurvey === 'independent') {
                updateDetails.scalePoints = [];
                updateDetails.benchMarks = [];
                updateDetails.questions = [];
                if (programReport && programReport.scalePointStatus) {
                    updateDetails.scalePointStatus = 'draft';
                }
                if (programReport && programReport.benchMarkStatus) {
                    updateDetails.benchMarkStatus = 'draft';
                }
            }
        }
        let programReportSettings;
        if (programReport) {
            if (sessionExperienceSurvey) {
                if (
                    sessionExperienceSurvey.separateRatingScaleValueForEachQuestion &&
                    programReport.sessionExperienceSurvey.scalePointStatus
                ) {
                    updateDetails['sessionExperienceSurvey.scalePointStatus'] = 'draft';
                }
                if (
                    !sessionExperienceSurvey.separateRatingScaleValueForEachQuestion &&
                    sessionExperienceSurvey.separateRatingScaleValueForEachQuestion !== undefined &&
                    programReport.sessionExperienceSurvey.scalePointStatus
                ) {
                    updateDetails['sessionExperienceSurvey.scalePointStatus'] = 'draft';
                }
            }
            if (benchMarkValue) {
                if (programReport.benchMarkValue && benchMarkForSurveyAndActivities === 'common') {
                    updateDetails.benchMarks = [];
                }
                if (
                    programReport.benchMarkValue &&
                    benchMarkForSurveyAndActivities === 'independent'
                ) {
                    updateDetails['sessionExperienceSurvey.benchMarks'] = [];
                    updateDetails['selfEvaluationSurvey.benchMarks'] = [];
                    updateDetails['activity.benchMarks'] = [];
                    if (programReport.selfEvaluationSurvey.benchMarkStatus) {
                        updateDetails['selfEvaluationSurvey.benchMarkStatus'] = 'draft';
                    }
                    if (programReport.sessionExperienceSurvey.benchMarkStatus) {
                        updateDetails['sessionExperienceSurvey.benchMarkStatus'] = 'draft';
                    }
                    if (programReport.activity.benchMarkStatus) {
                        updateDetails['activity.benchMarkStatus'] = 'draft';
                    }
                }
            }
            if (
                programReport.rattingScaleForSurvey &&
                sessionExperienceSurvey &&
                selfEvaluationSurvey &&
                sessionExperienceSurvey.limit &&
                selfEvaluationSurvey.limit
            ) {
                if (
                    sessionExperienceSurvey.limit.from !==
                        programReport.sessionExperienceSurvey.limit.from &&
                    programReport.rattingScaleForSurvey === 'common'
                ) {
                    updateDetails.scalePointStatus = 'draft';
                    updateDetails.scalePoints = [];
                    updateDetails.questions = [];
                }
                if (
                    sessionExperienceSurvey.limit.from !==
                        programReport.sessionExperienceSurvey.limit.from &&
                    programReport.rattingScaleForSurvey === 'independent'
                ) {
                    if (programReport.sessionExperienceSurvey.scalePointStatus) {
                        updateDetails['sessionExperienceSurvey.scalePointStatus'] = 'draft';
                    }
                    updateDetails['sessionExperienceSurvey.scalePoints'] = [];
                }
                if (
                    selfEvaluationSurvey.limit.from !==
                        programReport.selfEvaluationSurvey.limit.from &&
                    programReport.rattingScaleForSurvey === 'independent'
                ) {
                    if (programReport.selfEvaluationSurvey.scalePointStatus) {
                        updateDetails['selfEvaluationSurvey.scalePointStatus'] = 'draft';
                    }
                    updateDetails['selfEvaluationSurvey.scalePoints'] = [];
                }
            }
            programReportSettings = await ProgramReportSettings.updateOne(
                {
                    _program_id: convertToMongoObjectId(_program_id),
                    // _institution_id: convertToMongoObjectId(_institution_id),
                },
                { $set: updateDetails },
            );
        } else {
            updateDetails._program_id = convertToMongoObjectId(_program_id);
            updateDetails._institution_id = convertToMongoObjectId(_institution_id);
            programReportSettings = await ProgramReportSettings.create(updateDetails);
        }
        clearItem('programReportSettings');
        allProgramReportSetting();
        programReportSettings = await getProgramReport(_program_id, _institution_id);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Program report settings updated successfully',
            programReportSettings,
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Server Error', error.toString());
    }
};

exports.getProgramReportSettingBenchMark = async (req, res) => {
    try {
        const { _program_id, _institution_id } = req.query;

        const programReportBenchMark = await getProgramReportBenchMark(
            _program_id,
            _institution_id,
        );

        return sendResponse(res, 200, true, 'Program report settings', programReportBenchMark);
    } catch (error) {
        return sendResponse(res, 500, false, 'Server Error', error.toString());
    }
};

exports.updateProgramReportSettingCourse = async (req, res) => {
    try {
        const { _program_id, _institution_id } = req.query;
        const { course } = req.body;
        const programReport = await getProgramReport(_program_id, _institution_id);
        let programReportSettings;
        if (programReport) {
            const { courses } = programReport;
            if (courses && courses.length) {
                const programReportCourse = courses.find(
                    (courseEntry) =>
                        (courseEntry.courseId.toString() === course.courseId.toString() &&
                            courseEntry.year === course.year &&
                            courseEntry.level === course.level &&
                            courseEntry.term === course.term &&
                            courseEntry.rotation === course.rotation &&
                            course.rotationCount &&
                            courseEntry.rotationCount &&
                            courseEntry.rotationCount.toString() === course.rotationCount) ||
                        (courseEntry.courseId.toString() === course.courseId.toString() &&
                            courseEntry.year === course.year &&
                            courseEntry.level === course.level &&
                            courseEntry.term === course.term &&
                            courseEntry.rotation === course.rotation &&
                            !course.rotationCount),
                );
                if (programReportCourse) {
                    const updateFilter = {
                        'i.courseId': convertToMongoObjectId(course.courseId),
                        'i.year': course.year,
                        'i.level': course.level,
                        'i.term': course.term,
                        'i.rotation': course.rotation,
                    };

                    if (course.rotationCount) {
                        updateFilter['i.rotationCount'] = course.rotationCount;
                    }
                    updateDetails = {
                        'courses.$[i].selfEvaluationSurvey': course.selfEvaluationSurvey,
                        'courses.$[i].sessionExperienceSurvey': course.sessionExperienceSurvey,
                    };
                    programReportSettings = await ProgramReportSettings.findOneAndUpdate(
                        {
                            _program_id: convertToMongoObjectId(_program_id),
                            // _institution_id: convertToMongoObjectId(_institution_id),
                        },
                        {
                            $set: updateDetails,
                        },
                        {
                            arrayFilters: [updateFilter],
                        },
                    );
                } else {
                    updateDetails = course;
                    programReportSettings = await ProgramReportSettings.updateOne(
                        {
                            _program_id: convertToMongoObjectId(_program_id),
                            // _institution_id: convertToMongoObjectId(_institution_id),
                        },
                        {
                            $push: { courses: course },
                        },
                    );
                }
            } else {
                updateDetails = course;
                programReportSettings = await ProgramReportSettings.updateOne(
                    {
                        _program_id: convertToMongoObjectId(_program_id),
                        // _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    {
                        $push: { courses: course },
                    },
                );
            }
        }
        programReportSettings = await getProgramReport(_program_id, _institution_id);
        clearItem('programReportSettings');
        allProgramReportSetting();
        return sendResponse(
            res,
            200,
            true,
            'Program report settings updated successfully',
            programReportSettings,
        );
    } catch (error) {
        return sendResponse(res, 500, false, 'Server Error', error.toString());
    }
};
