 
module.exports = {
    infrastructure_delivery_type: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id, 
                name: element.name, 
                primary_delivery_type: element.primary_delivery_type,
                delivery_symbol: element.delivery_symbol, 
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    infrastructure_delivery_type_ID: (doc) => {
        let obj = {
            _id: doc._id, 
            name: doc.name,
            primary_delivery_type: doc.primary_delivery_type,
            delivery_symbol: doc.delivery_symbol, 
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    infrastructure_delivery_type_ID_Only: (doc) => {
        let obj = {
            _id: doc._id, 
            name: doc.name,
            primary_delivery_type: doc.primary_delivery_type,
            delivery_symbol: doc.delivery_symbol, 
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    infrastructure_delivery_type_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id, 
                name: element.name,
                primary_delivery_type: element.primary_delivery_type,
                delivery_symbol: element.delivery_symbol, 
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}