const { Joi } = require('../../common/middlewares/validation');

const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const updateMarksSchema = Joi.object({
    body: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        studentIds: Joi.any().optional(),
        marks: Joi.number().integer().min(0).optional(),
        rubrics: Joi.array().optional(),
    }),
}).unknown(true);

const rejectSubmissionSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const completeEvaluationSchema = Joi.object({
    body: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        scheduleId: objectIdSchema.optional(),
        studentIds: Joi.array().optional(),
    }),
}).unknown(true);

const getInsightsSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        scheduleId: objectIdSchema.optional(),
        studentId: objectIdRQSchema,
    }),
}).unknown(true);

const getEvaluationCountSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        scheduleId: objectIdSchema.optional(),
    }),
}).unknown(true);

const assignEvaluatorSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getInfrastructuresForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const switchEvaluationTypeSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getEvaluatorsSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        isGeneralUser: Joi.boolean().optional(),
    }),
});

const getStudentGroupsForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        isStudentGroup: Joi.boolean(),
        deliveryTypeSymbol: Joi.string().optional(),
        deliveryTypeId: objectIdSchema.optional(),
    }),
});

const getDeliveryTypeForAssignEvaluatorSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
    }),
}).unknown(true);

const addExternalEvaluatorSchema = Joi.object({
    body: Joi.object({
        email: Joi.string().email().required(),
        name: Joi.string().required(),
        roleId: objectIdRQSchema,
        mobile: Joi.number().required(),
        gender: Joi.string().required(),
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
}).unknown(true);

const verifyVerificationCodeSchema = Joi.object({
    body: Joi.object({
        email: Joi.string().email().required(),
        code: Joi.number().required(),
        roleId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
}).unknown(true);

const updateStudentsForPrepareAndPublishSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const getChildrenForPrepareAndPublishSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema,
        courseId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
        term: Joi.string().required(),
        year: Joi.string().required(),
        level: Joi.string().required(),
        curriculumId: objectIdSchema.optional(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
    }),
}).unknown(true);

const validateChildIdSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    updateMarksSchema,
    rejectSubmissionSchema,
    completeEvaluationSchema,
    getInsightsSchema,
    getEvaluationCountSchema,
    assignEvaluatorSchema,
    getInfrastructuresForAssignEvaluatorSchema,
    switchEvaluationTypeSchema,
    getEvaluatorsSchema,
    getStudentGroupsForAssignEvaluatorSchema,
    getDeliveryTypeForAssignEvaluatorSchema,
    addExternalEvaluatorSchema,
    verifyVerificationCodeSchema,
    updateStudentsForPrepareAndPublishSchema,
    getChildrenForPrepareAndPublishSchema,
    validateChildIdSchema,
};
