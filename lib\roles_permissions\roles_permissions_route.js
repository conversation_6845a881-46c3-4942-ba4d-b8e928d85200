const express = require('express');
const route = express.Router();
const roles_permissions = require('./roles_permissions_controller');
const validator = require('./roles_permissions_validator');


route.post('/add_role',validator.roles,roles_permissions.create);
route.put('/update_role/:id',validator.roles,validator.roles_id,roles_permissions.update);
route.get('/get_role',roles_permissions.get);
route.get('/get_role_id/:id',validator.roles_id,roles_permissions.get_id);
route.delete('/delete_role/:id',validator.roles_id,roles_permissions.delete);


module.exports = route;