const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    institutionCalendarList,
    saveConfigure,
    getConfigureSetting,
    createCalendar,
} = require('./qapcSetting.controller');

const validator = require('./qapcSetting.validator');
const { validate } = require('../../../middleware/validation');
//qapc configure setting
router.post(
    '/saveConfigure',
    validate(validator.saveConfigureValidator),
    catchAsync(saveConfigure),
);
router.get(
    '/getConfigureSetting',
    validate(validator.getConfigureValidator),
    catchAsync(getConfigureSetting),
);
//create new calender
router.get(
    '/institutionCalendarList',
    validate(validator.getConfigureValidator),
    catchAsync(institutionCalendarList),
);
router.post(
    '/createCalendar',
    validate(validator.createCalendarValidator),
    catchAsync(createCalendar),
);
module.exports = router;
