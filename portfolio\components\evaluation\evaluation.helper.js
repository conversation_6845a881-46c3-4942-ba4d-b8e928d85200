const { INFRASTRUCTURE } = require('../../common/utils/constants');
const { isIDEquals, convertToMongoObjectId } = require('../../common/utils/common.util');

const calculateStudentTotalMarks = ({ evaluators, totalMarks }) => {
    const totalEvaluatorMarks = evaluators.reduce((sum, ev) => sum + ev.marks, 0);

    const totalAchievedEvaluatorMarks = evaluators.reduce((sum, ev) => sum + ev.awardedMarks, 0);

    const scaledStudentMarks = (totalAchievedEvaluatorMarks / totalEvaluatorMarks) * totalMarks;

    return Number(scaledStudentMarks.toFixed(2));
};

const buildAssignEvaluationQuery = ({
    portfolioId,
    componentId,
    childrenId,
    institutionCalendarId,
    programId,
    courseId,
    infrastructureId,
    typeOfEvaluation,
    deliveryTypes = [],
    roles = [],
    assignEvaluators = [],
    userId,
}) => {
    const bulkUpdates = [];

    if (typeOfEvaluation === INFRASTRUCTURE) {
        deliveryTypes.forEach((deliveryType) => {
            const assignEvaluator = assignEvaluators.find((ae) =>
                isIDEquals(ae.deliveryTypeId, deliveryType.deliveryTypeId),
            );

            const isInfrastructureExists = assignEvaluator?.infrastructures?.some((infra) =>
                isIDEquals(infra.infrastructureId, infrastructureId),
            );

            const baseFilter = {
                portfolioId,
                componentId,
                childrenId,
                ...(deliveryType?.deliveryTypeId && {
                    deliveryTypeId: deliveryType?.deliveryTypeId,
                }),
                typeOfEvaluation,
            };

            let updateDoc = {};
            let arrayFilters = [];

            if (!assignEvaluator) {
                // Evaluator does not exist: create new doc with infra
                updateDoc = {
                    $setOnInsert: {
                        ...baseFilter,
                        deliveryTypeName: deliveryType?.deliveryTypeName,
                        sessionId: deliveryType?.sessionId,
                        infrastructures: [
                            {
                                infrastructureId,
                                roles,
                            },
                        ],
                        createdBy: {
                            id: userId,
                        },
                    },
                };
            } else if (!isInfrastructureExists) {
                // Evaluator exists, infra doesn't: push infra
                updateDoc = {
                    $push: {
                        infrastructures: {
                            infrastructureId,
                            roles,
                        },
                    },
                    $set: {
                        createdBy: {
                            id: userId,
                        },
                    },
                };
            } else {
                // Both exist: update infra roles
                updateDoc = {
                    $set: {
                        'infrastructures.$[i].roles': roles,
                        createdBy: {
                            id: userId,
                        },
                    },
                };
                arrayFilters = [{ 'i.infrastructureId': convertToMongoObjectId(infrastructureId) }];
            }

            bulkUpdates.push({
                updateOne: {
                    filter: baseFilter,
                    update: updateDoc,
                    upsert: !assignEvaluator,
                    ...(arrayFilters.length > 0 && { arrayFilters }),
                },
            });
        });
    }

    return bulkUpdates;
};

const generate6DigitCode = () => {
    return Math.floor(100000 + Math.random() * 900000);
};

const generateVerificationEmailHTML = (code, expiryTime = 300) => `
  <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
    <div style="padding: 30px; background-color: #f9f9f9;">
      <p style="font-size: 16px; color: #333;">Dear Evaluator,</p>
      <p style="font-size: 16px; color: #333;">
        You have been invited to evaluate a student's submission. Please use the verification code below:
      </p>
      <div style="text-align: center; margin: 30px 0;">
        <span style="display: inline-block; background-color: #4a90e2; color: white; padding: 14px 32px; font-size: 24px; letter-spacing: 4px; border-radius: 6px; font-weight: bold;">
          ${code}
        </span>
      </div>
      <p style="font-size: 16px; color: #333;">
        <strong>Note:</strong> This code is valid for <strong>${expiryTime} seconds</strong>. Do not share it with anyone.
      </p>
      <p style="font-size: 16px; color: #333;">Thank you,<br/>University Administration</p>
    </div>

  </div>
`;

module.exports = {
    calculateStudentTotalMarks,
    buildAssignEvaluationQuery,
    generate6DigitCode,
    generateVerificationEmailHTML,
};
