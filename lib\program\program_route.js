const express = require('express');
const route = express.Router();
const program = require('./program_controller');
const validater = require('./program_validator');

route.get('/program_list', program.program_list);
route.get('/program_department_subject', program.program_department_subject);
route.post('/list', program.list_values);
route.get('/:id', validater.program_id, program.list_id);
route.get('/', program.list);
route.post('/', validater.program, program.insert);
route.put('/:id', validater.program_id, validater.program, program.update);
route.delete('/:id', validater.program_id, program.delete);
route.get('/institution/:id', validater.program_id, program.list_institution);
route.post('/abbreviation/:id', validater.program_id, validater.abbreviation, program.update);

module.exports = route;