const express = require('express');
const route = express.Router();
const catchAsync = require('../utility/catch-async');
// controller;
const { getCourseListRefactored, getUserSessionList } = require('./course_schedule_dashboard');
const { publish } = require('./course_schedule_publish_controller');
const {
    scheduledExternalStaff,
    getClassLeaders,
    scheduleList,
    getScheduleList,
    schedule,
    lectureSettingSave,
    lectureSettingUpdate,
    lectureSettingDelete,
    updateAdvancedSettingsManageCourse,
    manageTopic,
    updateManageTopic,
    deleteManageTopic,
    getManageTopic,
    topicCheckInCourseSchedule,
    mergeSession,
    mergeSessionEdit,
    detachSession,
    cancelSession,
    reassignCancelledSession,
    availableList,
    scheduleUpdate,
    scheduleDelete,
    courseTimeTable,
    LevelTimeTable,
    courseScheduleExport,
    manageCourse,
    manageCourseDelivery,
    getAdvancedSettingsManageCourse,
    lectureSettingDayAssign,
    autoAssignDelivery,
    autoAssignSchedule,
    addSupportSession,
    editSupportSession,
    addEvent,
    editEvent,
    mergeScheduleList,
    lectureSettingDayUpdate,
    lectureSettingDayDelete,
    getStudentGroups,
    createScheduleSupportSessionEvent,
    updateManualAttendance,
    listManualAttendanceStaff,
    listManualAttendanceDetails,
    updateAdvancedSettingMissedSchedule,
    getStudentGroupsBasedDeliveryTypes,
    mergeScheduledClassLeader,
    dayScheduleOnSiteToRemote,
} = require('./course_schedule_controller');

// // validate schema
// const {
//     getCourseCourseCoordinatorsValidate: { body: getCourseCourseCoordinatorsBodySchema },
// } = require('./course_coordinator_validate_schema');

const {
    getManageTopicsValidator: { body: getManageTopicsValidatorSchema },
    getAddSupportSessionValidator: { body: getAddSupportSessionValidatorSchema },
    getEditSupportSessionValidator: { body: getEditSupportSessionValidatorSchema },
    getAddEventValidator: { body: getAddEventValidatorSchema },
    getEditEventValidator: { body: getEditEventValidatorSchema },
    getCreateScheduleSupportSession: { body: getCreateScheduleSupportSessionSchema },
    updateAdvancedSettingMissedScheduleValidator: { body: updateAdvancedSettingSchema },
} = require('./course_schedule_validator');

// const {
//     getUpdateManageTopicsValidator: { body: getUpdateManageTopicsValidatorSchema },
// } = require('./course_schedule_validator');

// validate
const { validate } = require('../../middleware/validation');

// validate schema
const {
    getLectureSettingValidator: { body: getLectureSettingValidatorSchema },
} = require('./course_schedule_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get(
    '/course_schedule_export/:exportType/:studentGroupId/:courseId/:institutionCalendarId',
    courseScheduleExport,
);
route.get('/course_list/:institution_calendar/:programId/:userId/:roleId', getCourseListRefactored);
route.get(
    '/user_session_list/:instCalId/:userId/:date',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getUserSessionList,
);
route.get('/schedule_list/:programId/:instCalId/:courseId/:term/:level_no', scheduleList);
route.get('/merge_schedule/:programId/:instCalId/:courseId/:term/:level_no', mergeScheduleList);
route.get('/get_schedule_list', getScheduleList);
route.get('/manage_course/:programId/:instCalId/:courseId/:term/:level_no', manageCourse);
route.get(
    '/manage_course_delivery/:programId/:instCalId/:courseId/:term/:level_no/:deliveryId',
    manageCourseDelivery,
);
route.get(
    '/auto_assign_course_delivery/:programId/:instCalId/:courseId/:term/:level_no/:deliveryId',
    autoAssignDelivery,
);
route.get(
    '/level_time_table/:programId/:instCalId/:term/:level_no/:startDate/:endDate',
    LevelTimeTable,
);
route.get(
    '/course_time_table/:programId/:instCalId/:courseId/:term/:level_no/:startDate/:endDate',
    courseTimeTable,
);
route.get(
    '/available_list/:programId/:instCalId/:courseId/:term/:level_no/:_session_id/:start_time/:end_time',
    availableList,
);
route.post(
    '/manage_topic',
    validate([{ schema: getManageTopicsValidatorSchema, property: 'body' }]),
    manageTopic,
);
route.put(
    '/manage_topic/:id/:course_id/:level_no/:program_id',
    //validate([{ schema: getUpdateManageTopicsValidatorSchema, property: 'body' }]),
    updateManageTopic,
);
route.delete('/manage_topic/:id/:course_id/:level_no/:program_id/', deleteManageTopic);
route.get(
    '/manage_topic/:course_id/:level_no/:program_id/:_institution_calendar_id',
    getManageTopic,
);
route.get('/manage_topic/topic_check_in_course_schedule/:id', topicCheckInCourseSchedule);
route.put('/merge_session', mergeSession);
route.put('/merge_session_edit', mergeSessionEdit);
route.put('/detach_session/:id', detachSession);
route.put('/cancel_session/:id', cancelSession);
route.put('/reassign_cancelled_session/:id', reassignCancelledSession);
route.post(
    '/lecture_setting',
    validate([{ schema: getLectureSettingValidatorSchema, property: 'body' }]),
    lectureSettingSave,
);
route.put(
    '/lecture_setting_update/:id',
    validate([{ schema: getLectureSettingValidatorSchema, property: 'body' }]),
    lectureSettingUpdate,
);
route.delete('/lecture_setting_delete/:id', lectureSettingDelete);
route.post('/', schedule);
route.post('/auto_schedule', autoAssignSchedule);
route.put(
    '/update_advanced_setting_manage_course/:programId/:courseId/:levelNo',
    updateAdvancedSettingsManageCourse,
);
route.get(
    '/get_advanced_setting_manage_course/:programId/:courseId/:levelNo',
    getAdvancedSettingsManageCourse,
);
/* route.put(
    '/update_delivery_type_same_time_for_delivery_group/:programId/:courseId/:levelId',
    updateAdvancedSettingsManageCourse,
); */
route.post('/manage_course/assign_day_time/:id', lectureSettingDayAssign);
route.put('/manage_course/update/:id/:day_id', lectureSettingDayUpdate);
route.delete('/manage_course/remove/:id/:day_id', lectureSettingDayDelete);
route.put('/:id', scheduleUpdate);
route.delete('/:id', scheduleDelete);
route.post('/publish', publish);

//Support Session
route.post(
    '/support_session',
    validate([{ schema: getAddSupportSessionValidatorSchema, property: 'body' }]),
    addSupportSession,
);
route.put(
    '/support_session/:id',
    validate([{ schema: getEditSupportSessionValidatorSchema, property: 'body' }]),
    editSupportSession,
);
route.post(
    '/event',
    validate([{ schema: getAddEventValidatorSchema, property: 'body' }]),
    addEvent,
);
route.put(
    '/event/:id',
    validate([{ schema: getEditEventValidatorSchema, property: 'body' }]),
    editEvent,
);
route.get(
    '/get_student_group/:_institution_calendar_id/:_program_id/:_course_id/:term',
    getStudentGroups,
);
route.get('/getStudentGroupBasedDeliveryTypes', catchAsync(getStudentGroupsBasedDeliveryTypes));
route.put(
    '/create_schedule_support_session_event/:id',
    validate([{ schema: getCreateScheduleSupportSessionSchema, property: 'body' }]),
    createScheduleSupportSessionEvent,
);
///////////////////////////////
// route.post(
//     '/',
//     validate([{ schema: getCourseCourseCoordinatorsBodySchema, property: 'body' }]),
//     assignCourseCoordinator,
// );
route.put('/updateManualAttendance/:programId/:courseId/:levelNo', updateManualAttendance);
route.get('/listManualAttendanceStaff', listManualAttendanceStaff);
route.get(
    '/listManualAttendanceDetails/:programId/:courseId/:levelNo',
    listManualAttendanceDetails,
);
route.put(
    '/update_advanced_setting_missed_schedule/:programId/:courseId/:levelNo',
    validate([{ schema: updateAdvancedSettingSchema, property: 'body' }]),
    updateAdvancedSettingMissedSchedule,
);

//outside campus class leader list
route.post('/classLeader', getClassLeaders);
route.get('/scheduledExternalStaff/:_id', scheduledExternalStaff);
route.get('/mergeScheduledClassLeader', mergeScheduledClassLeader);

// Date Based Onsite Schedule migrate to Remote Session
route.post('/dayScheduleOnSiteToRemote', dayScheduleOnSiteToRemote);
module.exports = route;
