const { USER } = require("../utility/constants");
const format = require("./infrastructure_event_format");
const base_control = require("../base/base_controller");
const { com_response } = require("../utility/common");
const constant = require("../utility/constants");
var InfrastructureEvents = require("mongoose").model(
  constant.INFRASTRUCTURE_EVENTS
);
var moment = require("moment");
const common_fun = require("../utility/common_functions");
var user = require("mongoose").model(USER);

sendNotification = (req) => {
  var participantsList = [req.body.creator_id];
  if (req.body.participants && req.body.comittes) {
    req.body.participants.forEach((participantId) => {
      participantsList.push(common_fun.toObjectId(participantId));
    });
    req.body.comittes.forEach((comitte) => {
      participantsList.push(comitte);
    });
  }
  user
    .find({ _id: { $in: participantsList } })
    .select({ email: 1, _id: 0, mobile: 1 })
    .exec((err, emailList) => {
      if (err) console.log(err);
      var mailList = [];
      emailList.forEach((emailObject) => mailList.push(emailObject.email));
      let messages = "<p>" + req.body.notify_message + "</p>";
      if (req.body.notify_via === "email") {
        const mailOptions = {
          to: mailList,
          subject: "Digi Scheduler Infra Event Creation",
          messages: messages,
        };
        common_fun.sending_mail(mailOptions, (error) => {
          if (!error) console.log("mail sent : " + mailList);
          else console.log("Mail Error : ", error);
        });
      }
      var smsList = [];
      emailList.forEach((emailObject) => {
        if (emailObject.mobile) {
          smsList.push("+91" + emailObject.mobile);
        }
      });
      if (req.body.notify_via === "sms") {
        let sms_messages = "DigiScheduler " + req.body.notify_message;
        common_fun.send_message(
          smsList[0],
          sms_messages,
          (error, response, body) => {
            if (!error && common_fun.checkSMSResponse(response, body))
              console.log("sms sent :" + smsList);
            else console.log("sms error : ", error);
          }
        );
      }
    });
};

exports.create = async (req, res) => {
  let infraEvent = new InfrastructureEvents(req.body);
  infraEvent.save((err) => {
    if (!err) {
      sendNotification(req);
      com_response(res, 200, true, "Infra event created successfully", []);
    } else com_response(res, 500, false, "Error on create infra_event", err);
  });
};

exports.createEventForMaintenance = async (req, res) => {
  req.body.event_mode = "offline";
  req.body.no_of_attendees = 1;
  req.body.gender = "both";
  req.body.event_for = "both";
  req.body.event_name = "maintenance";

  let infraEvent = new InfrastructureEvents(req.body);
  infraEvent.save((err) => {
    if (!err) {
      sendNotification(req);
      com_response(res, 200, true, "Infra event created successfully", []);
    } else com_response(res, 500, false, "Error on create infra event", err);
  });
};

getDateString = (date = new Date()) => {
  return date.toISOString().slice(0, 10);
};

exports.read = async (req, res) => {
  // conditions to get soft undeleted events
  const conditions = { isDeleted: false };
  // conditions to get approved or canceled events
  conditions["infra_approved"] = req.query.infra_approved;
  conditions["infra_canceled"] = req.query.infra_canceled;
  // conditions to get individual event by _id
  if (req.params._id) conditions._id = req.params._id;
  // conditions to get completed, today or upcoming
  // new Date(new Date(getDateString()).setHours(00, 00, 00))
  if (req.query.infra_filter) {
    switch (req.query.infra_filter) {
      case "completed":
        conditions["end_date"] = {
          $lt: getDateString(),
        };
        break;
      case "today":
        conditions["end_date"] = {
          $gte: getDateString(),
          $lte: getDateString(),
        };
        break;
      case "upcoming":
        conditions["end_date"] = {
          $gt: getDateString(),
        };
    }
  }
  // pagination options
  var perPage = parseInt(req.query.perPage);
  var page = parseInt(req.query.page);
  // query eloquent
  InfrastructureEvents.find()
    .where(conditions)
    .limit(perPage)
    .skip(perPage * page - perPage)
    .sort({ event_name: "asc" })
    .exec((err, events) => {
      if (err)
        com_response(res, 500, false, "Error on getting infra event", err);
      InfrastructureEvents.find()
        .where(conditions)
        .countDocuments()
        .exec(function (err, count) {
          if (err)
            com_response(res, 500, false, "Error on getting infra event", err);
          com_response(res, 200, true, "Infra events", {
            total_pages: Math.ceil(count / perPage),
            records_per_page: perPage,
            current_page: !Math.ceil(count / perPage)
              ? Math.ceil(count / perPage)
              : page,
            events: events,
          });
        });
    });
};

exports.readEventsOfPaticipants = async (req, res) => {
  req.body.isDeleted = false;
  var perPage = parseInt(req.query.perPage);
  var page = parseInt(req.query.page);
  var condition = { $or: req.body.query };
  InfrastructureEvents.find(condition)
    .limit(perPage)
    .skip(perPage * page - perPage)
    .sort({ event_name: "asc" })
    .exec((err, events) => {
      if (err)
        com_response(res, 500, false, "Error on getting infra event", err);
      InfrastructureEvents.where(condition)
        .countDocuments()
        .exec(function (err, count) {
          if (err)
            com_response(res, 500, false, "Error on getting infra event", err);
          com_response(res, 200, true, "Infra events", {
            total_pages: Math.ceil(count / perPage),
            records_per_page: perPage,
            current_page: page,
            events: events,
          });
        });
    });
};

exports.readEventsOfInfrastuctures = async (req, res) => {
  req.body.isDeleted = false;
  var perPage = parseInt(req.query.perPage);
  var page = parseInt(req.query.page);
  InfrastructureEvents.find()
    .where(req.body)
    .limit(perPage)
    .skip(perPage * page - perPage)
    .sort({ event_name: "asc" })
    .exec((err, events) => {
      if (err)
        com_response(res, 500, false, "Error on getting infra event", err);
      InfrastructureEvents.where(req.body)
        .countDocuments()
        .exec(function (err, count) {
          if (err)
            com_response(res, 500, false, "Error on getting infra event", err);
          com_response(res, 200, true, "Infra events", {
            total_pages: Math.ceil(count / perPage),
            records_per_page: perPage,
            current_page: page,
            events: events,
          });
        });
    });
};

exports.checkAvailableTimings = async (req, res) => {
  let settingsCollegeTime = ["10:00 AM", "3:30 PM"];
  /* let start_date = "2020-09-11";
    let start_time = "11:00 AM";
    let end_date = "2020-09-11";
    let end_time = "3:00 PM"; */

  var fromDate = req.body.start_date;
  var toDate = req.body.end_date;
  let where = { is_deleted: false };
  let aggre = [
    {
      $match: {
        start_date: { $gte: fromDate, $lte: toDate },
      },
    },
  ];
  let doc = await base_control.get_aggregate(InfrastructureEvents, aggre);
  if (doc.status) {
    if (!doc.data) {
      com_response(res, 500, false, "Error", doc.data);
    } else {
      var rangeDates = [
        {
          start_date: req.body.start_date,
          start_time: req.body.start_time,
          end_date: req.body.end_date,
          end_time: req.body.end_time,
        },
      ];
      var splitedDates = common_fun.splitIndividualFromRangeUserDate(
        rangeDates,
        settingsCollegeTime
      );
      /* var fetchedData = [{start_date:"2020-09-11",start_time:"11:00 AM",end_date:"2020-09-13",end_time:"3:00 PM"},
                      {start_date:"2020-09-14",start_time:"2:00 PM",end_date:"2020-09-14",end_time:"3:00 PM"},
                      {start_date:"2020-09-14",start_time:"11:00 AM",end_date:"2020-09-14",end_time:"2:00 PM"},
                      {start_date:"2020-09-11",start_time:"10:00 AM",end_date:"2020-09-11",end_time:"10:30 AM"}
                      ]; */
      var fetchedData = doc.data;
      //console.log(fetchedData)
      //console.log(splitedDates)
      ////////////////
      var checkFallInRange = common_fun.fallInRange(
        splitedDates,
        fetchedData,
        settingsCollegeTime
      );
      if (checkFallInRange) {
        //Available Timings and 1 week avalable Timings
        //Available Timings
        let avTimings = common_fun.getAvailableTimingsOnRangeDates(
          fetchedData,
          settingsCollegeTime
        );
        //+1 week Available Timings
        var eDt = moment(req.body.end_date);
        var eDt1 = moment(eDt.add(1, "days")).format("YYYY-MM-DD");
        var eDt2 = moment(eDt.add(7, "days")).format("YYYY-MM-DD");
        let whereW = { is_deleted: false };
        let aggreW = [
          {
            $match: {
              start_date: { $gte: eDt1, $lte: eDt2 },
            },
          },
        ];
        //console.log(eDt1,eDt2);
        let docW = await base_control.get_aggregate(
          InfrastructureEvents,
          aggreW
        );
        if (docW.status) {
          if (!docW.data) {
            com_response(res, 500, false, "Error", docW.data);
          } else {
            let fetchedDataW = docW.data;
            let avTimingsW = common_fun.getAvailableTimingsOnRangeDates(
              fetchedDataW,
              settingsCollegeTime
            );
            com_response(
              res,
              200,
              true,
              "infrastructure Event Check Availability fallin range. Choose other timings",
              avTimingsW
            );
          }
        }
      } else com_response(res, 200, true, "Time Available", []);
    }
  } else com_response(res, 500, false, "Error", doc.data);
};

exports.approveEventsOfInfrastuctures = async (req, res) => {
  InfrastructureEvents.updateMany(
    { _id: req.params._id },
    { $set: { infra_approved: true } },
    (err, events) => {
      if (!err)
        com_response(res, 200, true, "Infra event aproved successfuly", []);
      else com_response(res, 500, false, "Error on approve infra event", err);
    }
  );
};

exports.cancelEventsOfInfrastuctures = async (req, res) => {
  InfrastructureEvents.updateMany(
    { _id: req.params._id },
    { $set: { infra_canceled: true } },
    (err, events) => {
      if (!err)
        com_response(res, 200, true, "Infra event canceled successfuly", []);
      else com_response(res, 500, false, "Error on cancel infra event", err);
    }
  );
};

exports.update = async (req, res) => {
  InfrastructureEvents.findByIdAndUpdate(
    req.params._id,
    { $set: req.body },
    (err, events) => {
      if (!err)
        com_response(res, 200, true, "Infra event updated successfuly", []);
      else com_response(res, 500, false, "Infra event update faild", []);
    }
  );
};

exports.softDelete = async (req, res) => {
  InfrastructureEvents.updateMany(
    { _id: req.params._id },
    { $set: { isDeleted: true } },
    (err, role) => {
      if (!err)
        com_response(res, 200, true, "Infra event deleted successfuly", []);
      else com_response(res, 500, true, "Error on infra event delete", []);
    }
  );
};

exports.recovery = async (req, res) => {
  InfrastructureEvents.updateMany(
    { _id: req.params._id },
    { $set: { isDeleted: false } },
    (err, role) => {
      if (!err)
        com_response(res, 200, true, "Infra event recovered successfuly", []);
      else com_response(res, 500, false, "Error on infra event recovery", []);
    }
  );
};

exports.delete = async (req, res) => {
  InfrastructureEvents.findByIdAndRemove(req.params._id, function (err) {
    if (!err)
      com_response(res, 200, true, "Infra event deleted successfuly", []);
    else com_response(res, 500, false, "Error on infra event delete", []);
  });
};
