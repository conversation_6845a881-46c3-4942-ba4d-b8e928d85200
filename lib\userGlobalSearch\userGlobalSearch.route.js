const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const { getUserCourseDetails, getUserDropDownList } = require('./userGlobalSearch.controller');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

router.get(
    '/getUserDropDownList',
    [userPolicyAuthentication(['global_search:dashboard:view'])],
    catchAsync(getUserDropDownList),
);
router.get(
    '/getUserCourseDetails',
    [userPolicyAuthentication(['global_search:dashboard:view'])],
    catchAsync(getUserCourseDetails),
);
module.exports = router;
