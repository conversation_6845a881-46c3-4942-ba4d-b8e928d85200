const { getStudentGroupLists } = require('./discussions_service');
const {
    SERVICES: { DISCUSSION_FORUM },
} = require('../../utility/util_keys');
const { DS_DATA_RETRIEVED, ALL } = require('../../utility/constants');
const { discussionTopicsSchema, discussionUnReadSchema } = require('./discussions_topics_model');
const {
    sendResponse,
    convertToMongoObjectId,
    sendResponseWithRequest,
} = require('../../utility/common');
const discussionRepliesSchema = require('./discussions_replies_model');
const userSchema = require('../../models/user');
const { getSignedURL } = require('../../utility/common_functions');
const { sendNotificationPush } = require('../../../service/pushNotification.service');
const { saveDiscussionNotification } = require('../../utility/notification_push');

const discussionReplyData = async ({ discussionId, userId }) => {
    const discussionTopics = await discussionTopicsSchema
        .find(
            {
                _id: convertToMongoObjectId(discussionId),
            },
            {
                likes: 1,
                _id: 1,
                author: 1,
                channel_id: 1,
                createdAt: 1,
                title: 1,
                topics: 1,
                updatedAt: 1,
                description: 1,
                attachments: 1,
            },
        )
        .populate({ path: 'author', select: { name: 1, user_id: 1 } })
        .sort({ updatedAt: -1 })
        .lean();
    const replyDiscussionComments = await discussionRepliesSchema
        .find(
            {
                discussion_id: convertToMongoObjectId(discussionId),
                isDeleted: false,
            },
            {
                likes: 1,
                _id: 1,
                author: 1,
                createdAt: 1,
                comment: 1,
                updatedAt: 1,
                user_type: 1,
                reply_id: 1,
                readBy: 1,
            },
        )
        .populate({ path: 'author', select: { name: 1, user_id: 1 } })
        .sort({ createdAt: -1 })
        .lean();
    const totalUnreadCount = replyDiscussionComments
        .filter((authorElement) => authorElement.author._id.toString() !== userId.toString())
        .reduce((acc, readByElement) => {
            const readBy = readByElement.readBy !== undefined ? readByElement.readBy : [];
            const existingItem = readBy.some(
                (uniqueUserElement) => uniqueUserElement.toString() === userId.toString(),
            );
            if (!existingItem) {
                acc += 1;
            }
            return acc;
        }, 0);
    const topStudentContributors = replyDiscussionComments
        .filter((userTypeElement) => userTypeElement.user_type === 'student')
        .reduce((acc, readByElement) => {
            const existingItem = acc.find(
                (uniqueItem) =>
                    uniqueItem.author._id.toString() === readByElement.author._id.toString(),
            );
            if (existingItem) {
                existingItem.count += 1;
            } else {
                acc.push({
                    author: readByElement.author,
                    count: 1,
                });
            }
            return acc;
        }, []);

    const topContributors = replyDiscussionComments
        .filter((userTypeElement) => userTypeElement.user_type === 'student')
        .reduce((acc, item) => {
            const existingItem = acc.find(
                (uniqueItem) => uniqueItem.author._id.toString() === item.author._id.toString(),
            );
            if (existingItem) {
                existingItem.count += 1;
            } else {
                acc.push({
                    author: item.author,
                    count: 1,
                });
            }
            return acc;
        }, []);
    const updateReadByBulkWrites = replyDiscussionComments
        .filter((authorElement) => authorElement.author._id.toString() !== userId.toString())
        .filter((readByElement) => {
            const readBy = readByElement.readBy !== undefined ? readByElement.readBy : [];
            const userIndex = readBy.findIndex(
                (userElement) => userElement.toString() === userId.toString(),
            );
            return userIndex === -1;
        })
        .map((replyElement) => {
            const readBy = replyElement.readBy !== undefined ? replyElement.readBy : [];
            const updateReadByUser = [...readBy, userId.toString()];
            return {
                updateMany: {
                    filter: { _id: replyElement._id },
                    update: { $set: { readBy: updateReadByUser } },
                },
            };
        });
    if (updateReadByBulkWrites.length) {
        await discussionRepliesSchema.bulkWrite(updateReadByBulkWrites);
    }
    return {
        discussionData: discussionTopics,
        replies: replyDiscussionComments,
        totalUnreadCount,
        topContributors,
        totalRespondCount: topStudentContributors.length,
    };
};

exports.getStudentGroups = async (req, res) => {
    const {
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotation,
        rotationCount,
        courseId,
    } = req.query;
    const { _institution_id } = req.headers;
    try {
        if (!_institution_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('INVALID_INSTITUTION_ID'));
        }
        const getStudents = await getStudentGroupLists({
            _institution_id,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            _user_id: null,
            group_name: null,
        });
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), getStudents);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

const getStudentListForNotification = async ({
    institution_id,
    institution_calendar_id,
    course_id,
    program_id,
    year,
    level_no,
    term,
    rotation,
    rotation_count,
    channel_id,
    main_group_name,
    author,
}) => {
    const studentGroupLists = await getStudentGroupLists({
        _institution_id: institution_id,
        _institution_calendar_id: convertToMongoObjectId(institution_calendar_id),
        _program_id: convertToMongoObjectId(program_id),
        _course_id: convertToMongoObjectId(course_id),
        yearNo: year,
        levelNo: level_no,
        term,
        rotation,
        rotationCount: rotation_count,
        _user_id: null,
        channel_id,
        group_name: main_group_name,
    });
    if (
        studentGroupLists.studentGroups !== undefined &&
        studentGroupLists.studentGroups.length > 0
    ) {
        const studentsList = studentGroupLists.studentGroups.flatMap((studentGroupElement) => {
            if (studentGroupElement.groups) {
                return studentGroupElement.groupName !== 'DELIVERY TYPE'
                    ? studentGroupElement.groups
                          .filter((group) => group.channelId === channel_id)
                          .flatMap((group) => group.students)
                    : studentGroupElement.groups.flatMap((mainGroup) =>
                          mainGroup.groups
                              .filter((group) => group.channelId === channel_id)
                              .flatMap((group) => group.students),
                      );
            }
            return [];
        });

        if (studentsList.length > 0) {
            const groupedStudentIds = studentsList.map(
                (studentElement) => studentElement._student_id,
            );
            const mergedStaffId = [...groupedStudentIds, ...author];
            const userData = await userSchema
                .find(
                    {
                        _id: { $in: mergedStaffId },
                    },
                    {
                        _id: 1,
                        fcm_token: 1,
                        web_fcm_token: 1,
                        device_type: 1,
                    },
                )
                .lean();
            return userData;
        }
        return [];
    }
    return [];
};

exports.createDiscussionTopic = async (req, res) => {
    try {
        const {
            _id,
            title,
            description,
            topics,
            author,
            channel_id,
            attachments,
            isActive = true,
            isDeleted = false,
            program_id,
            level_no,
            term,
            rotation,
            rotation_count,
            main_group_name,
            group_name,
            admin_course,
            merge_status,
        } = req.body;
        const { _institution_id, _user_id } = req.headers;
        const institution_id = _institution_id;
        let institution_calendar_id;
        let course_id;
        let year;
        if (DISCUSSION_FORUM && DISCUSSION_FORUM === 'true') {
            const extractChannelData = channel_id.split('_');
            institution_calendar_id = extractChannelData[3];
            course_id = extractChannelData[2];
            year = extractChannelData[0];
        }

        //381 ms
        const updatedDiscussion = await discussionTopicsSchema
            .findOneAndUpdate(
                { _id: _id ? convertToMongoObjectId(_id) : convertToMongoObjectId() },
                {
                    $set: {
                        title,
                        description,
                        topics,
                        author,
                        channel_id,
                        isActive,
                        isDeleted,
                        attachments,
                        institution_id,
                        institution_calendar_id,
                        program_id,
                        course_id,
                        year,
                        level_no,
                        term,
                        rotation,
                        rotation_count,
                        main_group_name,
                        group_name,
                        admin_course,
                        merge_status,
                    },
                },
                { upsert: true, new: true },
            )
            .populate({ path: 'author', select: { name: 1, user_id: 1 } })
            .populate({ path: 'course_id', select: { course_name: 1, course_code: 1 } })
            .populate({ path: 'program_id', select: { name: 1 } })
            .lean();
        if (updatedDiscussion) {
            const studentsList = await getStudentListForNotification({
                institution_id,
                institution_calendar_id,
                course_id,
                program_id,
                year,
                level_no,
                term,
                rotation,
                rotation_count,
                channel_id,
                main_group_name,
                author: [],
            });
            if (studentsList.length > 0) {
                const notificationTitle = _id ? 'Discussion Updated' : 'New Discussion Created';
                const notificationDesc =
                    title +
                    `\n | ` +
                    `${updatedDiscussion.course_id.course_name} (${
                        updatedDiscussion.course_id.course_code
                    }) - ${year.replace('year', 'Year ')}  - ${level_no} - ${term}` +
                    `\n | ` +
                    `- ${_id ? 'Updated' : 'Created'} By ${updatedDiscussion.author.name.first} ${
                        updatedDiscussion.author.name.last
                    }`;
                const pushData = {
                    title: notificationTitle,
                    Title: notificationTitle,
                    description: notificationDesc,
                    Body: notificationDesc,
                    notificationType: 'discussion',
                    buttonAction: 'discussion',
                    ClickAction: 'discussion',
                    institutionCalendarId: institution_calendar_id,
                    courseId: course_id,
                    CourseId: course_id,
                    programId: program_id,
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    rotation,
                    rotation_count,
                    mergeStatus: merge_status,
                    channelId: channel_id,
                    discussionId: updatedDiscussion._id,
                    adminCourse: admin_course,
                    groupName: group_name,
                    courseName: updatedDiscussion.course_id.course_name,
                    courseCode: updatedDiscussion.course_id.course_code,
                    programName: updatedDiscussion.program_id.name,
                };
                studentsList
                    .filter((userElement) => userElement._id.toString() !== _user_id.toString())
                    .forEach((userElement) => {
                        if (userElement.fcm_token) {
                            sendNotificationPush(
                                userElement.fcm_token,
                                pushData,
                                userElement.device_type,
                            );
                        }
                        if (userElement.web_fcm_token) {
                            sendNotificationPush(userElement.web_fcm_token, pushData, 'web');
                        }
                    });
                pushData.users = studentsList
                    .filter((userElement) => userElement._id.toString() !== _user_id.toString())
                    .map((studentElement) => {
                        return { _id: studentElement._id, isViewed: false };
                    });
                delete pushData.Title;
                delete pushData.Body;
                delete pushData.CourseId;
                saveDiscussionNotification(pushData);
            }
        }

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            _id ? 'UPDATED_SUCCESSFULLY' : 'CREATED_SUCCESSFULLY',
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

const getFormattedDiscussions = async ({ discussionIds, userId }) => {
    const replyDiscussionComments = await discussionRepliesSchema
        .find(
            {
                discussion_id: { $in: discussionIds },
                isDeleted: false,
            },
            {
                _id: 1,
                discussion_id: 1,
                user_type: 1,
                author: 1,
                readBy: 1,
            },
        )
        .populate({ path: 'author', select: { name: 1, user_id: 1 } })
        .lean();
    const groupedData = replyDiscussionComments.reduce((acc, item) => {
        if (!acc[item.discussion_id]) {
            acc[item.discussion_id] = [];
        }
        acc[item.discussion_id].push({
            _id: item._id,
            user_type: item.user_type,
            author: item.author,
            readBy: item.readBy !== undefined ? item.readBy : [],
        });
        return acc;
    }, {});
    const groupedDiscussions = Object.keys(groupedData).map((discussion_id) => ({
        discussion_id,
        replies: groupedData[discussion_id],
    }));
    return groupedDiscussions.map((groupedElement) => {
        const topStudentContributors = groupedElement.replies
            .filter((userElement) => userElement.user_type === 'student')
            .reduce((acc, item) => {
                const existingItem = acc.find(
                    (uniqueItem) => uniqueItem.author._id.toString() === item.author._id.toString(),
                );
                if (existingItem) {
                    existingItem.count += 1;
                } else {
                    acc.push({
                        author: item.author,
                        count: 1,
                    });
                }
                return acc;
            }, []);
        const totalUnreadCount = groupedElement.replies
            .filter((authorElement) => authorElement.author._id.toString() !== userId.toString())
            .reduce((acc, readByElement) => {
                const readBy = readByElement.readBy !== undefined ? readByElement.readBy : [];

                const existingItem = readBy.some(
                    (uniqueUserElement) => uniqueUserElement.toString() === userId.toString(),
                );
                if (!existingItem) {
                    acc += 1;
                }
                return acc;
            }, 0);

        return {
            discussion_id: groupedElement.discussion_id,
            totalUnreadCount,
            totalRespondCount: topStudentContributors.length,
        };
    });
};

exports.getDiscussionTopics = async (req, res) => {
    const { channelId } = req.query;
    const { _user_id } = req.headers;
    try {
        if (!_user_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('USER_NOT_FOUND'));
        }
        const discussionTopics = await discussionTopicsSchema
            .find(
                {
                    channel_id: channelId,
                    // author: convertToMongoObjectId(_user_id),
                    isDeleted: false,
                },
                {
                    likes: 1,
                    _id: 1,
                    author: 1,
                    channel_id: 1,
                    createdAt: 1,
                    title: 1,
                    topics: 1,
                    updatedAt: 1,
                    description: 1,
                    attachments: 1,
                },
            )
            .populate({ path: 'author', select: { name: 1, user_id: 1 } })
            .sort({ updatedAt: -1 })
            .lean();
        const discussionIds = discussionTopics.map((discussionElement) =>
            discussionElement._id.toString(),
        );
        const formattedDiscussions = await getFormattedDiscussions({
            discussionIds,
            userId: _user_id,
        });
        const discussionData = {
            discussionLists: discussionTopics,
            totalCountData: formattedDiscussions,
        };
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            discussionData,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.deleteDiscussionTopic = async (req, res) => {
    try {
        const { discussionId } = req.query;
        await discussionTopicsSchema.updateOne(
            {
                _id: convertToMongoObjectId(discussionId),
            },
            { $set: { isDeleted: true, isActive: false } },
        );
        return sendResponseWithRequest(req, res, 200, true, req.t('DELETED_SUCCESSFULLY'), []);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.likeDiscussionTopic = async (req, res) => {
    try {
        const { discussionId } = req.query;
        const { _user_id } = req.headers;
        if (!_user_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('USER_NOT_FOUND'));
        }
        const filteredDiscussion = await discussionTopicsSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(discussionId),
                },
                {
                    likes: 1,
                    author: 1,
                },
            )
            .lean();
        if (filteredDiscussion === null) {
            return sendResponseWithRequest(req, res, 404, false, "Discussion doesn't exists", []);
        }

        if (filteredDiscussion.author.toString() === _user_id.toString())
            return sendResponseWithRequest(
                req,
                res,
                400,
                false,
                "You can't like your own discussion",
                [],
            );

        const { likes } = filteredDiscussion;
        const likeArray = likes !== undefined ? likes : [];
        const userIndex = likeArray.findIndex(
            (userElement) => userElement.toString() === _user_id.toString(),
        );
        if (userIndex === -1) {
            likeArray.push(_user_id);
        } else {
            likeArray.splice(userIndex, 1);
        }
        await discussionTopicsSchema.findOneAndUpdate(
            { _id: convertToMongoObjectId(discussionId) },
            { $set: { likes: likeArray } },
        );
        return sendResponseWithRequest(req, res, 200, true, 'UPDATED_SUCCESSFULLY');
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.createDiscussionReply = async (req, res) => {
    try {
        const {
            _id,
            discussion_id,
            reply_id,
            comment,
            author,
            user_type,
            isActive = true,
            isDeleted = false,
        } = req.body;
        const { _user_id } = req.headers;
        const updatedReply = await discussionRepliesSchema
            .findOneAndUpdate(
                { _id: _id ? convertToMongoObjectId(_id) : convertToMongoObjectId() },
                {
                    $set: {
                        discussion_id,
                        reply_id,
                        comment,
                        author,
                        user_type,
                        isActive,
                        isDeleted,
                    },
                },
                { upsert: true, new: true },
            )
            .populate({ path: 'author', select: { name: 1, user_id: 1 } })
            .populate({
                path: 'discussion_id',
                select: {
                    institution_id: 1,
                    institution_calendar_id: 1,
                    course_id: 1,
                    program_id: 1,
                    year: 1,
                    level_no: 1,
                    term: 1,
                    rotation: 1,
                    rotation_count: 1,
                    channel_id: 1,
                    main_group_name: 1,
                    merge_status: 1,
                    group_name: 1,
                    admin_course: 1,
                    title: 1,
                    author: 1,
                },
                populate: [
                    {
                        path: 'course_id',
                        select: { course_name: 1, course_code: 1 },
                    },
                    {
                        path: 'program_id',
                        select: { name: 1 },
                    },
                ],
            })
            .lean();
        if (updatedReply) {
            const {
                institution_id,
                institution_calendar_id,
                course_id,
                program_id,
                year,
                level_no,
                term,
                rotation,
                rotation_count,
                channel_id,
                main_group_name,
                merge_status,
                admin_course,
                group_name,
                title,
                author,
            } = updatedReply.discussion_id;
            const { course_name, course_code } = course_id;
            const studentsList = await getStudentListForNotification({
                institution_id,
                institution_calendar_id,
                course_id: course_id._id,
                program_id: program_id._id,
                year,
                level_no,
                term,
                rotation,
                rotation_count,
                channel_id,
                main_group_name,
                author: [author],
            });
            if (studentsList.length > 0) {
                const notificationTitle = _id
                    ? 'Discussion Comment Updated'
                    : 'New Discussion Comment Received';
                const notificationDesc =
                    title +
                    `\n | ` +
                    `${course_name} (${course_code}) - ${year.replace(
                        'year',
                        'Year ',
                    )}  - ${level_no} - ${term}` +
                    `\n | ` +
                    `- ${_id ? 'Updated' : 'Created'} By ${updatedReply.author.name.first} ${
                        updatedReply.author.name.last
                    }`;
                const pushData = {
                    title: notificationTitle,
                    Title: notificationTitle,
                    description: notificationDesc,
                    Body: notificationDesc,
                    notificationType: 'discussion',
                    buttonAction: 'discussion',
                    ClickAction: 'discussion',
                    institutionCalendarId: institution_calendar_id,
                    courseId: course_id._id,
                    CourseId: course_id._id,
                    programId: program_id._id,
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    rotation,
                    rotation_count,
                    mergeStatus: merge_status,
                    channelId: channel_id,
                    discussionId: updatedReply.discussion_id._id,
                    adminCourse: admin_course,
                    groupName: group_name,
                    courseName: course_name,
                    courseCode: course_code,
                    programName: program_id.name,
                };
                studentsList
                    .filter((userElement) => userElement._id.toString() !== _user_id.toString())
                    .forEach((userElement) => {
                        if (userElement.fcm_token) {
                            sendNotificationPush(
                                userElement.fcm_token,
                                pushData,
                                userElement.device_type,
                            );
                        }
                        if (userElement.web_fcm_token) {
                            sendNotificationPush(userElement.web_fcm_token, pushData, 'web');
                        }
                    });
                pushData.users = studentsList
                    .filter((userElement) => userElement._id.toString() !== _user_id.toString())
                    .map((studentElement) => {
                        return { _id: studentElement._id, isViewed: false };
                    });
                delete pushData.Title;
                delete pushData.Body;
                delete pushData.CourseId;
                saveDiscussionNotification(pushData);
            }
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            _id ? 'UPDATED_SUCCESSFULLY' : 'CREATED_SUCCESSFULLY',
            updatedReply,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.likeReplyComment = async (req, res) => {
    try {
        const { replyId } = req.query;
        const { _user_id } = req.headers;
        if (!_user_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('USER_NOT_FOUND'));
        }
        const filteredDiscussionReply = await discussionRepliesSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(replyId),
                },
                {
                    likes: 1,
                    author: 1,
                },
            )
            .lean();
        if (filteredDiscussionReply === null) {
            return sendResponseWithRequest(req, res, 404, false, "Reply doesn't exists", []);
        }

        if (filteredDiscussionReply.author.toString() === _user_id.toString())
            return sendResponseWithRequest(
                req,
                res,
                400,
                false,
                "You can't like your own reply comment",
                [],
            );

        const { likes } = filteredDiscussionReply;
        const likeArray = likes !== undefined ? likes : [];
        const userIndex = likeArray.findIndex(
            (userElement) => userElement.toString() === _user_id.toString(),
        );
        if (userIndex === -1) {
            likeArray.push(_user_id);
        } else {
            likeArray.splice(userIndex, 1);
        }
        await discussionRepliesSchema.findOneAndUpdate(
            { _id: replyId },
            { $set: { likes: likeArray } },
        );
        return sendResponseWithRequest(req, res, 200, true, 'UPDATED_SUCCESSFULLY');
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.deleteReplyComment = async (req, res) => {
    try {
        const { replyId } = req.query;
        await discussionRepliesSchema.updateOne(
            {
                _id: convertToMongoObjectId(replyId),
            },
            { $set: { isDeleted: true, isActive: false } },
        );
        return sendResponseWithRequest(req, res, 200, true, req.t('DELETED_SUCCESSFULLY'), []);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getRepliesLists = async (req, res) => {
    const { discussion_id } = req.query;
    const { _user_id } = req.headers;
    try {
        if (!_user_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('USER_NOT_FOUND'));
        }
        const discussionRepliedData = await discussionReplyData({
            discussionId: discussion_id,
            userId: _user_id,
        });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            discussionRepliedData,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.viewDiscussionAttachment = async (req, res) => {
    try {
        const { discussionId, attachmentId } = req.query;
        const filteredDiscussion = await discussionTopicsSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(discussionId),
                },
                {
                    attachments: 1,
                },
            )
            .lean();
        const data = {};
        if (filteredDiscussion.attachments.length > 0) {
            const filteredAttachment = filteredDiscussion.attachments.find(
                (attachmentElement) => attachmentElement._id.toString() === attachmentId.toString(),
            );
            data.url = filteredAttachment.url;
            data.signedUrl = await getSignedURL(filteredAttachment.url);
            data.name = filteredAttachment.name;
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), data);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getStudentDiscussionLists = async (req, res) => {
    const {
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotation,
        rotationCount,
        courseId,
    } = req.query;
    const { _institution_id, _user_id } = req.headers;
    try {
        if (!_institution_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('INVALID_INSTITUTION_ID'));
        }
        if (!_user_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('USER_NOT_FOUND'));
        }
        const studentGroupsList = await getStudentGroupLists({
            _institution_id,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            _user_id,
            group_name: null,
        });
        const studentDiscussionList = { discussionLists: [], totalCountData: [] };
        if (studentGroupsList.studentGroups) {
            const channelIds = studentGroupsList.studentGroups
                .map((studentGroupElement) => {
                    if (studentGroupElement.groups) {
                        return studentGroupElement.groupName !== 'DELIVERY TYPE'
                            ? studentGroupElement.groups
                                  .filter((group) => group.channelId)
                                  .map((group) => group.channelId)
                            : studentGroupElement.groups
                                  .map((mainGroup) => {
                                      return mainGroup.groups
                                          .filter((group) => group.channelId)
                                          .map((group) => group.channelId);
                                  })
                                  .flat();
                    }
                    return null;
                })
                .flat();

            if (channelIds.length > 0) {
                const discussionTopics = await discussionTopicsSchema
                    .find(
                        {
                            channel_id: { $in: channelIds },
                            isDeleted: false,
                        },
                        {
                            likes: 1,
                            _id: 1,
                            author: 1,
                            channel_id: 1,
                            createdAt: 1,
                            title: 1,
                            topics: 1,
                            updatedAt: 1,
                            description: 1,
                            attachments: 1,
                        },
                    )
                    .populate({ path: 'author', select: { name: 1, user_id: 1 } })
                    .sort({ updatedAt: -1 })
                    .lean();
                const discussionIds = discussionTopics.map((discussionElement) =>
                    discussionElement._id.toString(),
                );
                const formattedDiscussions = await getFormattedDiscussions({
                    discussionIds,
                    userId: _user_id,
                });
                studentDiscussionList.discussionLists = discussionTopics;
                studentDiscussionList.totalCountData = formattedDiscussions;
            }
        }

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            studentDiscussionList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.updateUnReadDiscussionCount = async (req, res) => {
    try {
        const { author, channelId, type, institutionCalendarId, isView } = req.query;
        if (type === ALL) {
            const unReadMessagesList = await discussionUnReadSchema
                .find(
                    { institutionCalendarId: convertToMongoObjectId(institutionCalendarId) },
                    { channelId: 1, userIds: 1, _id: 0 },
                )
                .lean();
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), unReadMessagesList);
        }
        const unReadMessages = await discussionUnReadSchema
            .findOne({ channelId }, { userIds: 1 })
            .lean();
        if (!unReadMessages) {
            await discussionUnReadSchema.create({
                channelId,
                institutionCalendarId,
                userIds: [{ userId: convertToMongoObjectId(author), unReadCount: 0 }],
            });
        } else {
            const userIdStr = String(author);
            const updatedUserIds = unReadMessages.userIds.map(({ userId, unReadCount }) => ({
                userId: convertToMongoObjectId(userId),
                unReadCount:
                    String(userId) === userIdStr ? 0 : isView ? unReadCount : unReadCount + 1,
            }));
            if (!unReadMessages.userIds.some(({ userId }) => String(userId) === userIdStr)) {
                updatedUserIds.push({ userId: convertToMongoObjectId(author), unReadCount: 0 });
            }
            await discussionUnReadSchema.updateOne(
                { channelId },
                { $set: { userIds: updatedUserIds } },
            );
        }
        return sendResponse(res, 200, true, 'UPDATED_SUCCESSFULLY');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
