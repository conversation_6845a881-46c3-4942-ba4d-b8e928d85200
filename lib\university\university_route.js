const express = require('express');
const route = express.Router();
const university = require('../university/university_controller');
const validater = require('./university_validator');
route.post('/list', university.list_values);
route.get('/:id', validater.university_id, university.list_id);
route.get('/', university.list);
route.post('/', validater.university, university.insert);
route.put('/:id', validater.university_id, validater.university, university.update);
route.delete('/:id', validater.university_id, university.delete);

module.exports = route;