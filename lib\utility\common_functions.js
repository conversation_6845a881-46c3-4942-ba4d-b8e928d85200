const { v4: uuidv4 } = require('uuid');
const mongoose = require('mongoose');
const randomNumber = require('random-number');
const nodemailer = require('nodemailer');
const axios = require('axios');
const util_keys = require('./util_keys');
const AWS = require('aws-sdk');
AWS.config.update({
    accessKeyId: util_keys.AWS_ACCESS_KEY,
    secretAccessKey: util_keys.AWS_SECRET_KEY,
    region: util_keys.AWS_REGION,
});
const moment = require('moment-timezone');
const util_key = require('./util_keys');
const { remove } = require('fs-extra');

const {
    EMAIL_SENDER_ADDRESS,
    SLACK_AUTH_TOKEN,
    TIMEZONE,
    LOCAL_TIMEZONE,
    logger,
    SLACK_CHANNEL,
    AWS_PINPOINT_ENABLE,
    DIGIVAL_CLOUD_PROVIDER,
    INSTITUTION_NAME,
} = require('./util_keys');

const { PART_TIME, FULL_TIME } = require('./constants');
const { getS3UnsignedUrl, getS3SignedUrl } = require('../../service/aws.service');

const { ensureDir } = require('fs-extra');

exports.ensureDirectory = (dir) => {
    ensureDir(dir)
        .then(() => logger.info({ dir }, 'ensureDirectory -> storage -> destination: There'))
        .catch((err) => {
            logger.error({ err }, 'ensureDirectory -> storage -> destination:');
        });
};
// const jwt = require('jsonwebtoken');
exports.toObjectId = (string) => {
    return new mongoose.Types.ObjectId(string);
};

exports.generateRandomNumber = (length) => {
    const pool = 'abcdefghijklmnopqrstuvwxyz0123456789@';
    const uuid = uuidv4().replace(/-/g, ''); // Generate a UUID and remove hyphens
    const randomStr = uuid
        .split('')
        .map((char, index) => pool[Math.floor(Math.random() * pool.length)]) // Map UUID characters to pool
        .slice(0, length)
        .join('');
    return randomStr;
};

exports.getFourDigitOTP = () => {
    if (util_key.APP_STATE == 'production') {
        const options = {
            min: 1000,
            max: 9999,
            integer: true,
        };
        return randomNumber(options);
    }
    return 1234;
};

exports.getSixDigitOTP = () => {
    if (util_key.APP_STATE == 'production') {
        const options = {
            min: 100000,
            max: 999999,
            integer: true,
        };
        return randomNumber(options);
    }
    return 123456;
};

// // JWT Token
// exports.generateJWTToken = (data) => {
//     const token = jwt.sign(data, JWT_SECRET_KEY);
//     return token;
// };

exports.timestampNow = () => new Date().getTime();

exports.timestampNow_in_current = () => {
    const d_t = new Date();
    return (
        d_t.getDate() +
        '-' +
        (d_t.getMonth() + 1) +
        '-' +
        d_t.getFullYear() +
        ' ' +
        d_t.getHours() +
        ':' +
        d_t.getMinutes() +
        ':' +
        d_t.getMilliseconds()
    );
};

exports.getSecondsDifference = (date1, date2) => {
    const seconds = (date2 - date1) / 1000;
    return seconds;
};

exports.checkSMSResponse = (response /* , body */) =>
    response.statusCode === 200 || response.statusCode === 201;

exports.send_message = (mobileNumber, text, callback) => {
    this.send_emails_to_slack({ mobileNumber, text, callback });
};

exports.send_mail = async (options, callback) => {
    this.send_emails_to_slack(options);
    // send mail to admin for reviewing the artist
};

exports.sending_mail = async (options, callback) => {
    const mailOptions = {
        from: EMAIL_SENDER_ADDRESS,
        to: options.to,
        subject: options.subject,
        html: options.messages,
    };
    this.send_emails_to_slack(mailOptions);
};

exports.check_value_or_null = (obj, key) => {
    return key.split('.').reduce(function (o, x) {
        return typeof o == 'undefined' || o === null ? o : o[x];
    }, obj);
};

exports.check_value_null = (obj, objs) => {
    // console.log(objs);
    const dd = objs || '';
    const sp = objs.split('.');
    // let obj = sp[0];
    const key = dd.substring(sp[0].length + 1, dd.length);
    return key.split('.').reduce(function (o, x) {
        // console.log(o[x]);
        const ret = typeof o == 'undefined' || o === null ? o : o[x];
        // console.log(ret);
        return ret;
    }, obj);
};

exports.show_null_empty_Undefined = (obj) => {
    // console.log(obj);
    if (obj === '' || obj === undefined || obj === 'undefined' || obj === 'null') {
        return null;
    }
    return obj;
};

// check objects and show null if needed
exports.check_objects_show_null = (obj) => {
    const newObj = {};
    // console.log(obj);
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            // console.log(key, " -> ", obj[key], typeof obj[key]);
            const objValue = obj[key];
            if (
                objValue === '' ||
                objValue === undefined ||
                objValue === 'undefined' ||
                objValue === 'null'
            ) {
                newObj[key] = null;
            } else {
                newObj[key] = objValue;
            }
        }
    }
    return newObj;
};

// send OTP to slack channel
exports.send_otp_to_slack = async (data) => {
    this.send_emails_to_slack(data);
};

// send EMAIL to slack channel
exports.send_emails_to_slack = async (data) => {
    switch (util_key.IN_HOUSE_NOTIFICATION_SERVICE) {
        case 'google':
            {
                const chatAxios = require('./googleChat.axios');
                await chatAxios({
                    url: util_key.GOOGLE_CHAT_WEBHOOK,
                    method: 'POST',
                    data: {
                        text: JSON.stringify(data),
                    },
                })
                    .then(() => {
                        return true;
                    })
                    .catch(function (error) {
                        console.error(error);
                        return [];
                    });
            }
            break;
        case 'slack': {
            const { WebClient } = require('@slack/web-api');
            // Create a new instance of the WebClient class with the token read from your environment variable
            let slack_channel = 'ds-v2-email';
            slack_channel = util_key.APP_STATE == 'production' ? SLACK_CHANNEL : 'ds-v2-email';
            const web = new WebClient(SLACK_AUTH_TOKEN);
            try {
                await web.chat.postMessage({
                    channel: slack_channel,
                    text: data,
                });
                // console.log('Sent to slack channel', r);
                return true;
            } catch (e) {
                console.log('Error in sending to slack channel', e);
                throw Error(e);
            }
        }
        default:
            break;
    }
};

const pinpoint = new AWS.Pinpoint();
// send EMAIL using Pinpoint
exports.send_email = async (to_address, subject, data) => {
    const mailOptions = {
        to: to_address,
        subject,
        html: data,
    };
    this.send_emails_to_slack({ area: util_key.APP_STATE, data: mailOptions });
    if (util_key.APP_STATE === 'production') {
        const body_html = `<html><head></head><body>${data}</body></html>`;
        if (
            // (process.env.DEPLOY_TO && process.env.DEPLOY_TO === 'india') ||
            AWS_PINPOINT_ENABLE === 'false'
        ) {
            const email = {
                host: process.env.GMAIL_SMTP,
                port: process.env.GMAIL_PORT,
                auth: {
                    user: process.env.GMAIL_USERNAME,
                    pass: process.env.GMAIL_USERPASS,
                },
            };
            const msg = {
                from: util_keys.SENDER_ADDRESS,
                to: to_address,
                subject,
                html: body_html,
            };
            const awsTransport = nodemailer.createTransport(email);
            awsTransport.sendMail(msg).catch((awsErr) => {
                console.log(awsErr);
            });
        } else {
            const email = {
                host: process.env.SMTP_HOST_AWS,
                port: process.env.SMTP_PORT_AWS,
                auth: {
                    user: process.env.SMTP_USERNAME_AWS,
                    pass: process.env.SMTP_PASSWORD_AWS,
                },
            };
            const msg = {
                from: util_keys.SENDER_ADDRESS,
                to: to_address,
                subject,
                html: body_html,
            };
            const awsTransport = nodemailer.createTransport(email);
            awsTransport.sendMail(msg).catch((awsErr) => {
                console.log(awsErr);
            });

            // Pinpoint Mode
            // const charset = util_keys.AWS_CHARSET;
            // const params = {
            //     ApplicationId: util_keys.AWS_APP_ID,
            //     MessageRequest: {
            //         Addresses: {
            //             [to_address]: {
            //                 ChannelType: 'EMAIL',
            //             },
            //         },
            //         MessageConfiguration: {
            //             EmailMessage: {
            //                 FromAddress: util_keys.SENDER_ADDRESS,
            //                 SimpleEmail: {
            //                     Subject: {
            //                         Charset: charset,
            //                         Data: subject,
            //                     },
            //                     HtmlPart: {
            //                         Charset: charset,
            //                         Data: body_html,
            //                     },
            //                 },
            //             },
            //         },
            //     },
            // };
            // pinpoint.sendMessages(params, function (err) {
            //     if (err) {
            //         console.log(err);
            //         console.log(err.message);
            //     } else {
            //         console.log('Email sent!');
            //     }
            // });
        }
    }
};

// send SMS using Pinpoint
exports.send_sms = async (to_mobiles, data) => {
    this.send_emails_to_slack({ area: util_key.APP_STATE, mode: 'SMS', to: to_mobiles, data });
    if (util_key.APP_STATE === 'production') {
        if (util_key.TWILIO_ENABLE === 'true') {
            const client = require('twilio')(
                util_key.TWILIO_ACCOUNT_SID,
                util_key.TWILIO_AUTH_TOKEN,
            );
            client.messages
                .create({
                    body: data,
                    to: to_mobiles.includes('+') ? to_mobiles : `+${to_mobiles}`,
                    from: util_key.TWILIO_FROM_NO,
                })
                .then((message) => console.log(message.sid));
            return;
        }
        if (util_key.OUR_SMS_ENABLE === 'true') {
            // request(
            //     {
            //         method: 'GET',
            //         url: `http://www.OurSms.net/api/sendsms.php?username=${util_keys.SMS_USERNAME}&password=${util_keys.SMS_PASSWORD}&message=${data}&numbers=${to_mobiles}&sender=${util_keys.SMS_SENDER}&return=${util_keys.SMS_RETURN}`,
            //     },
            //     (error, response, body) => {
            //         if (error) {
            //             console.log(`Error in sending message ${to_mobiles} ` + error);
            //         } else {
            //             console.log('Response: ', body);
            //         }
            //     },
            // );
            const config = {
                url: `${util_keys.OURSMS_BASE_URL}/msgs/sms`,
                method: 'POST',
                data: {
                    src: util_keys.OURSMS_SENDER,
                    dests: [to_mobiles.toString()],
                    body: data,
                    priority: 0,
                    delay: 0,
                    validity: 0,
                    maxParts: 0,
                    dlr: 0,
                    prevDups: 0,
                    msgClass: util_keys.OURSMS_MSG_CLASS,
                },
                headers: { Authorization: `Bearer ${util_keys.OURSMS_TOKEN}` },
            };
            axios(config)
                .then((resp) => {
                    console.log(`Response: ${to_mobiles} `, resp.status);
                })
                .catch(function (error) {
                    console.error(`Error in sending message ${to_mobiles} ` + error);
                });
        } else {
            const params = {
                ApplicationId: util_keys.AWS_APP_ID,
                MessageRequest: {
                    Addresses: {
                        [to_mobiles]: {
                            ChannelType: 'SMS',
                        },
                    },
                    MessageConfiguration: {
                        SMSMessage: {
                            Body: data,
                            MessageType: 'TRANSACTIONAL',
                            SenderId: util_keys.AWS_SENDER_ID,
                        },
                    },
                },
            };
            pinpoint.sendMessages(params, function (err, data) {
                if (err) {
                    console.log(err.message);
                } else {
                    console.log('Message sent!');
                }
            });
        }
    }
};

/*
// send SMS using Pinpoint
exports.send_sms = async (to_mobiles, data) => {

  this.send_emails_to_slack({ mode: 'SMS', to: to_mobiles, data });
  var params = {
    ApplicationId: util_keys.AWS_APP_ID,
    MessageRequest: {
      Addresses: {
        [to_mobiles]: {
          ChannelType: 'SMS'
        }
      },
      MessageConfiguration: {
        SMSMessage: {
          Body: data,
          MessageType: "TRANSACTIONAL",
          SenderId: util_keys.AWS_SENDER_ID,
        }
      }
    }
  };
  pinpoint.sendMessages(params, function (err, data) {
    if (err) {
      console.log(err.message);
    } else {
      console.log("Message sent!");
    }
  });
}; */
// };
//

// const getDateTimeToTime = function (dateTime) {
//     let dt = new Date(dateTime);
//     dt = moment(dt).format('hh:mm A');
//     return dt;
// };

const getTimeStampNew = function (date, time) {
    const dat = date.split('-');
    const day = dat[2];
    const month = dat[1];
    const year = dat[0];
    const t = time.split(':');
    let hour = t[0];
    const minAm = t[1].split(' ');
    const mins = minAm[0];
    const am = minAm[1];
    if (am != 'AM' && parseInt(hour) != 12) {
        hour = parseInt(hour) + 12;
    }
    return new Date(year, month, day, hour, mins).getTime();
};

const getLocalTime = function (timestamp) {
    const date = new Date(timestamp);
    return moment(date).format('YYYY-MM-DD hh:mm:ss A');
    //return new Date(timestamp).toLocaleString();
};

/////////////////Split data individual dated data from range dates and normal date/////////////

const splitIndividualFromRange = function (fetchedData, settingsCollegeTime) {
    //var sDate = moment(end_date);
    //var eDate = moment(end_date);
    const bookedDateandTimingsArr = [];
    const dateArr = [];
    for (let i = 0; i < fetchedData.length; i++) {
        const sDate = moment(fetchedData[i].start_date);
        const eDate = moment(fetchedData[i].end_date);
        let j = 0;
        for (let m = moment(sDate); m.diff(eDate, 'days') <= 0; m.add(1, 'days')) {
            let sTime;
            let eTime = 0;
            if (j != 0) sTime = settingsCollegeTime[0];
            else sTime = fetchedData[i].start_time;

            if (m.diff(eDate, 'days') != 0) eTime = settingsCollegeTime[1];
            else eTime = fetchedData[i].end_time;

            bookedDateandTimingsArr.push([
                { date: m.format('YYYY-MM-DD'), start_time: sTime, end_time: eTime },
            ]);
            dateArr.push(m.format('YYYY-MM-DD'));
            j++;
        }
    }
    return { dateArr, bookedDateandTimingsArr };
};
/////////////////Split data individual dated data from range dates and normal date end/////////////
/////////////////////////////////////////////////////////////////////
const searchInArray = function (newDateArr, date) {
    let flag = 0;
    const arr = [];
    let index = null;
    for (let i = 0; i < newDateArr.length; i++) {
        if (newDateArr[i] == date) {
            flag = 1;
            index = i;
            break;
        }
    }
    if (flag == 1) {
        arr.push({ index, flag });
        return arr;
    }

    arr.push({ index, flag });
    return arr;
};
///////////////////////////////////////////////////////////////////////////////
///////////////////////Grouping duplicate date/////////////////////////

exports.groupDuplicateDate = function (dateArr, bookedDateandTimingsArr) {
    const newDateArr = [];
    const newBookedDateandTimingsArr = [];
    for (let i = 0; i < dateArr.length; i++) {
        const arr = searchInArray(newDateArr, dateArr[i]);
        if (arr[0].flag == 1) {
            for (let k = 0; k < bookedDateandTimingsArr[i].length; k++) {
                newBookedDateandTimingsArr[arr[0].index].push(bookedDateandTimingsArr[i][k]);
            }
        } else {
            newDateArr.push(dateArr[i]);
            newBookedDateandTimingsArr.push(bookedDateandTimingsArr[i]);
        }
    }
    return { newDateArr, newBookedDateandTimingsArr };
};
const groupDuplicateDate = function (dateArr, bookedDateandTimingsArr) {
    const newDateArr = [];
    const newBookedDateandTimingsArr = [];
    for (let i = 0; i < dateArr.length; i++) {
        const arr = searchInArray(newDateArr, dateArr[i]);
        if (arr[0].flag == 1) {
            for (let k = 0; k < bookedDateandTimingsArr[i].length; k++) {
                newBookedDateandTimingsArr[arr[0].index].push(bookedDateandTimingsArr[i][k]);
            }
        } else {
            newDateArr.push(dateArr[i]);
            newBookedDateandTimingsArr.push(bookedDateandTimingsArr[i]);
        }
    }
    return { newDateArr, newBookedDateandTimingsArr };
};
///////////////////////Grouping duplicate date ENd/////////////////////////
///////////////////////Sort Booked Timing//////////////////////////////////////////////
exports.sortBookedTime = function (date, timings) {
    let temp = '';
    for (let i = 0; i < timings.length; i++) {
        for (let j = 0; j < timings.length - i - 1; j++) {
            const timestamp1 = getTimeStampNew(date, timings[j].start_time);
            const timestamp2 = getTimeStampNew(date, timings[j + 1].start_time);
            if (timestamp1 > timestamp2) {
                temp = timings[j];
                timings[j] = timings[j + 1];
                timings[j + 1] = temp;
            }
        }
    }
    return timings;
};

const sortBookedTime = function (date, timings) {
    let temp = '';
    for (let i = 0; i < timings.length; i++) {
        for (let j = 0; j < timings.length - i - 1; j++) {
            const timestamp1 = getTimeStampNew(date, timings[j].start_time);
            const timestamp2 = getTimeStampNew(date, timings[j + 1].start_time);
            if (timestamp1 > timestamp2) {
                temp = timings[j];
                timings[j] = timings[j + 1];
                timings[j + 1] = temp;
            }
        }
    }
    return timings;
};
/////////////////////////Sort Booked Timing End/////////////////////
////////////////////////////Get Available Timings///////////////////////////////////////////////////
exports.getAvailableTimings = function (
    date,
    bookedTimings,
    settingsCollegeTimings = ['10:00 AM', '3:30 PM'],
) {
    const collegeStartTime = getTimeStampNew(date, settingsCollegeTimings[0]);
    const collegeEndTime = getTimeStampNew(date, settingsCollegeTimings[1]);
    const availableTimingsArr = [];
    let prevTime = 0;
    for (let i = 0; i < bookedTimings.length; i++) {
        if (i == 0) prevTime = collegeStartTime;
        else prevTime = getTimeStampNew(date, bookedTimings[i - 1].end_time);

        if (prevTime < getTimeStampNew(date, bookedTimings[i].start_time))
            availableTimingsArr.push(
                getLocalTime(prevTime) +
                    ' - ' +
                    getLocalTime(getTimeStampNew(date, bookedTimings[i].start_time)),
            );

        if (i == bookedTimings.length - 1) {
            const bETime = getTimeStampNew(date, bookedTimings[i].end_time);
            if (bETime < collegeEndTime)
                availableTimingsArr.push(
                    getLocalTime(bETime) + ' - ' + getLocalTime(collegeEndTime),
                );
        }
    }
    return availableTimingsArr;
};

const getAvailableTimings = function (
    date,
    bookedTimings,
    settingsCollegeTimings = ['10:00 AM', '3:30 PM'],
) {
    const collegeStartTime = getTimeStampNew(date, settingsCollegeTimings[0]);
    const collegeEndTime = getTimeStampNew(date, settingsCollegeTimings[1]);
    const availableTimingsArr = [];
    let prevTime = 0;
    for (let i = 0; i < bookedTimings.length; i++) {
        if (i == 0) prevTime = collegeStartTime;
        else prevTime = getTimeStampNew(date, bookedTimings[i - 1].end_time);

        if (prevTime < getTimeStampNew(date, bookedTimings[i].start_time))
            availableTimingsArr.push(
                getLocalTime(prevTime) +
                    ' - ' +
                    getLocalTime(getTimeStampNew(date, bookedTimings[i].start_time)),
            );

        if (i == bookedTimings.length - 1) {
            const bETime = getTimeStampNew(date, bookedTimings[i].end_time);
            if (bETime < collegeEndTime)
                availableTimingsArr.push(
                    getLocalTime(bETime) + ' - ' + getLocalTime(collegeEndTime),
                );
        }
    }
    return availableTimingsArr;
};
////////////////////////////Get Available Timings End/////////////////////////////////////////
//////////////////////////////////Split User Given Range DAte//////////////////////////

exports.splitIndividualFromRangeUserDate = function (userGivenDates, settingsCollegeTime) {
    //var sDate = moment(end_date);
    //var eDate = moment(end_date);
    const splitedDate = [];
    // const dateArr = [];
    for (let i = 0; i < userGivenDates.length; i++) {
        const sDate = moment(userGivenDates[i].start_date);
        const eDate = moment(userGivenDates[i].end_date);
        let j = 0;
        for (let m = moment(sDate); m.diff(eDate, 'days') <= 0; m.add(1, 'days')) {
            let sTime;
            let eTime = 0;
            if (j != 0) sTime = settingsCollegeTime[0];
            else sTime = userGivenDates[i].start_time;

            if (m.diff(eDate, 'days') != 0) eTime = settingsCollegeTime[1];
            else eTime = userGivenDates[i].end_time;

            splitedDate.push({ date: m.format('YYYY-MM-DD'), start_time: sTime, end_time: eTime });
            //dateArr.push(m.format('YYYY-MM-DD'));
            j++;
        }
    }
    return splitedDate;
};
/////////////////Split User Given Range DAte End/////////////
////////////////////////////Fall In Range/////////////////////////////
exports.fallInRange = (splitedDates, fetchedData, settingsCollegeTime) => {
    const splitedIndividualFromRange = splitIndividualFromRange(fetchedData, settingsCollegeTime);
    const { newBookedDateandTimingsArr } = groupDuplicateDate(
        splitedIndividualFromRange.dateArr,
        splitedIndividualFromRange.bookedDateandTimingsArr,
    );
    //console.log("-----------------------");
    //console.log(splitedIndividualFromRange);
    //console.log(newBookedDateandTimingsArr);

    const sortedBookedTime = [];
    for (let i = 0; i < newBookedDateandTimingsArr.length; i++) {
        const sortedBTimings = sortBookedTime(
            newBookedDateandTimingsArr[i][0].date,
            newBookedDateandTimingsArr[i],
        );
        sortedBookedTime.push(sortedBTimings);
    }
    //console.log(sortedBookedTime);

    let flag = 0;
    for (let i = 0; i < splitedDates.length; i++) {
        //splittedDates
        for (let sbt = 0; sbt < sortedBookedTime.length; sbt++) {
            //sortedBookedTime outer
            for (sbt2 = 0; sbt2 < sortedBookedTime[sbt].length; sbt2++) {
                //sortedBookedTime inner
                //console.log(splitedDates[i]["date"] +"=="+ sortedBookedTime[sbt][sbt2]["date"]);
                if (splitedDates[i].date == sortedBookedTime[sbt][sbt2].date) {
                    //fall in range check
                    const userGivenStartTime = getTimeStampNew(
                        splitedDates[i].date,
                        splitedDates[i].start_time,
                    );
                    const userGivenEndTime = getTimeStampNew(
                        splitedDates[i].date,
                        splitedDates[i].end_time,
                    );
                    const fetchedBookedStartTime = getTimeStampNew(
                        sortedBookedTime[sbt][sbt2].date,
                        sortedBookedTime[sbt][sbt2].start_time,
                    );
                    const fetchedBookedEndTime = getTimeStampNew(
                        sortedBookedTime[sbt][sbt2].date,
                        sortedBookedTime[sbt][sbt2].end_time,
                    );
                    /* if((userGivenTime >= fetchedBookedStartTime) && (userGivenTime < fetchedBookedEndTime)){
                //fall in range
                flag = 1;
          } */
                    if (
                        flag == 0 &&
                        userGivenStartTime <= fetchedBookedStartTime &&
                        userGivenEndTime <= fetchedBookedEndTime
                    )
                        flag = 1;
                    else if (
                        flag == 0 &&
                        userGivenStartTime >= fetchedBookedStartTime &&
                        userGivenEndTime <= fetchedBookedEndTime
                    )
                        flag = 1;
                    else if (
                        flag == 0 &&
                        userGivenStartTime <= fetchedBookedStartTime &&
                        userGivenEndTime >= fetchedBookedEndTime
                    )
                        flag = 1;
                    // else if (
                    //     flag == 0 &&
                    //     userGivenStartTime <= fetchedBookedStartTime &&
                    //     userGivenEndTime >= fetchedBookedEndTime
                    // )
                    //     flag = 1;
                }
                if (flag == 1) break;
            }
            if (flag == 1) break;
        }
        if (flag == 1) break;
    }
    if (flag == 1) console.log('Fall in Range');
    else console.log('Not Fall in Range');

    return flag;
};

////////////////////////////Fall In Range End/////////////////////////////
///////////////////////////Get Available Timings///////////////////////////
exports.getAvailableTimingsOnRangeDates = (fetchedData, settingsCollegeTime) => {
    const { dateArr, bookedDateandTimingsArr } = splitIndividualFromRange(
        fetchedData,
        settingsCollegeTime,
    );
    const { newBookedDateandTimingsArr } = groupDuplicateDate(dateArr, bookedDateandTimingsArr);
    //Sorting
    const sortedBookedTime = [];
    for (let i = 0; i < newBookedDateandTimingsArr.length; i++) {
        const sortedBTimings = sortBookedTime(
            newBookedDateandTimingsArr[i][0].date,
            newBookedDateandTimingsArr[i],
        );
        sortedBookedTime.push(sortedBTimings);
    }
    //////////////////////
    //Get Available Timings
    const aTimingsArr = [];
    for (let i = 0; i < sortedBookedTime.length; i++) {
        const timings = getAvailableTimings(sortedBookedTime[i][0].date, sortedBookedTime[i]);
        if (timings != '') aTimingsArr.push(timings);
    }
    return aTimingsArr;
};

////////////////////////
//////////////////////////Get Available Timings End////////////////////////

exports.nameFormatter = (name) => {
    return (
        name.middle && name.middle.length !== 0
            ? name.first +
              ' ' +
              (name.middle && name.middle.length !== 0 ? name.middle : '') +
              ' ' +
              (name.last && name.last.length !== 0 ? name.last : '') +
              ' ' +
              (name.family && name.family.length !== 0 ? name.family : '')
            : name.first +
              ' ' +
              (name.last && name.last.length !== 0 ? name.last : '') +
              ' ' +
              (name.family && name.family.length !== 0 ? name.family : '')
    ).trim();
};

exports.scheduleTimeFormateChange = (time) => {
    return `${time.hour}:${time.minute} ${time.format}`;
};

exports.scheduleDateFormateChange = (dateTime) => {
    const timeZone = util_key.APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
    const eveDate = new Date(dateTime).toLocaleString('en-US', {
        timeZone,
    });
    const splitData = eveDate.split(',');
    const dateSplit = splitData[0].split('/');
    return `${dateSplit[1] + '-' + dateSplit[0] + '-' + dateSplit[2]}`;
};

exports.dateTimeBasedConverter = (dateTime, timeZone) => {
    const timeZoneData = timeZone || LOCAL_TIMEZONE;
    return moment.tz(dateTime, timeZoneData);
};

exports.dateTimeLocalFormatter = (dateTime) => {
    const timeZone = util_key.APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
    const eveDate = new Date(dateTime).toLocaleString('en-US', {
        timeZone,
    });
    const splitData = eveDate.split(',');
    const dateSplit = splitData[0].split('/');
    const timeDataSplit = splitData[1].split(' ');
    const timeSplit = timeDataSplit[1].split(':');
    return new Date(
        dateSplit[2] +
            '-' +
            dateSplit[0] +
            '-' +
            dateSplit[1] +
            ' ' +
            timeSplit[0] +
            ':' +
            timeSplit[1] +
            ' ' +
            timeDataSplit[2],
    );
};

exports.dateTimeLocalFormatInString = (dateTime) => {
    const timeZone = util_key.APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
    const eveDate = new Date(dateTime).toLocaleString('en-US', {
        timeZone,
    });
    const splitData = eveDate.split(',');
    const dateSplit = splitData[0].split('/');
    const timeDataSplit = splitData[1].split(' ');
    const timeSplit = timeDataSplit[1].split(':');
    const response =
        (timeSplit[0].toString() === '12' && timeDataSplit[2] === 'AM') ||
        (timeSplit[0].toString() === '11' && timeDataSplit[2] === 'PM')
            ? `${dateSplit[1]}-${dateSplit[0]}-${dateSplit[2]}`
            : `${dateSplit[1]}-${dateSplit[0]}-${dateSplit[2]} ${timeSplit[0]}:${timeSplit[1]} ${timeDataSplit[2]}`;
    return response;
};

exports.timeAgo = (time) => {
    return moment(time, 'YYYY-MM-DD HH:mm:ss').fromNow();
};

exports.getAppCodeByDomain = ({ host = '' } = {}) => {
    if (!host) return 'DS';
    const [, domain = ''] = host.split('://');
    return domain.startsWith('ibnsina.digischeduler') ? 'DSST' : 'DS';
};

exports.scheduleDateTimeFormatter = (schedule_date, time) => {
    return new Date(
        schedule_date.getFullYear() +
            '-' +
            (schedule_date.getMonth() + 1) +
            '-' +
            schedule_date.getDate() +
            ' ' +
            time.hour +
            ':' +
            time.minute +
            ' ' +
            time.format,
    );
};
//convert Riyadh to UTC
exports.convertingRiyadhToUTC = (schedule_date, Hours, Minutes) => {
    const timeZone = util_key.APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
    const formattingDate =
        schedule_date.getFullYear() +
        '-' +
        (schedule_date.getMonth() + 1) +
        '-' +
        schedule_date.getDate() +
        ' ' +
        Hours +
        ':' +
        Minutes;

    return (
        moment
            .tz(formattingDate, 'YYYY-M-D H:m', timeZone)
            .tz('UTC')
            .format('YYYY-MM-DDTHH:mm:ss.SSS') + 'Z'
    );
};
//Staff Gender & Employment Splitter
exports.staffSplit = async (staffs) => {
    return {
        male_count: staffs.filter((ele) => ele.gender === 'male').length,
        female_count: staffs.filter((ele) => ele.gender === 'female').length,
        part_time_count: staffs.filter((ele) => ele.employment.user_employment_type === PART_TIME)
            .length,
        full_time_count: staffs.filter((ele) => ele.employment.user_employment_type === FULL_TIME)
            .length,
    };
};

exports.capitalize = (s) => {
    if (typeof s !== 'string') return '';
    return s.charAt(0).toUpperCase() + s.slice(1);
};

exports.deleteFileOrFolderUtil = function ({ path }) {
    remove(path)
        .then(() => logger.info(`${path} was deleted`))
        .catch((err) => logger.error(`${path} was not deleted due to ${err.message}`));
};

exports.getSignedURL = (url, from) => {
    // If Oracle Enabled mean this will work
    if (DIGIVAL_CLOUD_PROVIDER && DIGIVAL_CLOUD_PROVIDER === 'OCI') {
        const { getOciSignedUrl } = require('../../service/oracleBucket.service');
        return getOciSignedUrl(url);
    }

    const fileName = url.split('/').pop();
    const urlSplit = url.split('/');
    let bucketUrls = `${urlSplit[3]}`;
    for (let i = 4; i < urlSplit.length - 1; i++) {
        bucketUrls += `/${urlSplit[i]}`;
    }
    return getS3SignedUrl({
        // bucket: from && from === 'userData' ? BUCKET_NAME_USER_DATA : BUCKET_DOCUMENT,
        bucket: bucketUrls,
        key: fileName,
    });
};
exports.differenceInMinutes = (startDateAndTime, endDateAndTime) => {
    const end = moment(endDateAndTime);
    const start = moment(startDateAndTime);
    return end.diff(start, 'minutes');
};

exports.addMinutes = (DateAndTime, add) => {
    const startdate = moment(DateAndTime);
    return new Date(moment(startdate).add(add, 'minutes'));
};
exports.getDateString = () => {
    const today = new Date();
    const logfileByDate =
        today.getUTCDate() + '-' + parseInt(today.getUTCMonth() + 1) + '-' + today.getUTCFullYear();
    return logfileByDate;
};

// make object update data
exports.ObjectPreparationFromRequest = (data, object, param) => {
    for (prop in object) {
        if (object.hasOwnProperty(prop)) {
            data[param + '.' + prop] = object[prop];
        }
    }
    return data;
};

exports.emailGreetingContent = () => {
    return `<br><br>Greetings from ${INSTITUTION_NAME.replace(/'/g, '').replace(/"/g, '')}<br><br>`;
};

exports.emailRegardsContent = () => {
    return `<br><br>Best Regards<br>${INSTITUTION_NAME.replace(/'/g, '').replace(/"/g, '')}`;
};

// * Course Session Order Filter Based on Scheduled Session Due to Multi Group Diff Session Flow Schedules
exports.courseSessionOrderFilterBasedSchedule = ({ courseSessionFlow, courseSchedule }) => {
    const sessionIds = courseSchedule.map((scheduleElement) =>
        scheduleElement.session._session_id.toString(),
    );
    const filteredSessionFlowData = {
        session_flow_data: courseSessionFlow.session_flow_data
            ? courseSessionFlow.session_flow_data.filter((sessionElement) =>
                  sessionIds.find(
                      (scheduleSessionElement) =>
                          scheduleSessionElement.toString() === sessionElement._id.toString(),
                  ),
              )
            : courseSessionFlow.filter((sessionElement) =>
                  sessionIds.find(
                      (scheduleSessionElement) =>
                          scheduleSessionElement.toString() === sessionElement._id.toString(),
                  ),
              ),
    };
    return filteredSessionFlowData;
};

//search Data
exports.searchData = async (searchKey) => {
    try {
        let dbQuery = {};
        const word = searchKey.trim().split(/\s+/);
        if (word.length < 2) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    { 'name.first': { $regex: searchKey, $options: 'i' } },
                    { 'name.middle': { $regex: searchKey, $options: 'i' } },
                    { 'name.last': { $regex: searchKey, $options: 'i' } },
                    { 'name.family': { $regex: searchKey, $options: 'i' } },
                    { email: { $regex: searchKey, $options: 'i' } },
                    { user_id: { $regex: searchKey, $options: 'i' } },
                    { gender: { $regex: searchKey, $options: 'i' } },
                ],
            };
        } else if (word.length === 2) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    {
                        $and: [
                            { 'name.first': { $regex: word[0], $options: 'i' } },
                            { 'name.middle': { $regex: word[1], $options: 'i' } },
                        ],
                    },
                    {
                        $and: [
                            { 'name.first': { $regex: word[0], $options: 'i' } },
                            { 'name.last': { $regex: word[1], $options: 'i' } },
                        ],
                    },
                ],
            };
        } else if (word.length === 3) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    {
                        $and: [
                            { 'name.first': { $regex: word[0], $options: 'i' } },
                            { 'name.middle': { $regex: word[1], $options: 'i' } },
                            { 'name.last': { $regex: word[2], $options: 'i' } },
                        ],
                    },
                ],
            };
        } else if (word.length === 4) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    {
                        $and: [
                            { 'name.first': { $regex: word[0], $options: 'i' } },
                            { 'name.middle': { $regex: word[1], $options: 'i' } },
                            { 'name.last': { $regex: word[2], $options: 'i' } },
                            { 'name.family': { $regex: word[3], $options: 'i' } },
                        ],
                    },
                ],
            };
        }
        return dbQuery;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//search function
exports.searchKeyFunction = ({ searchKey }) => {
    return { $regex: searchKey, $options: 'i' };
};

exports.createPolicyKey = ({ policyKey }) => {
    return policyKey
        .split('+')
        .map((part) => part.trim().toLowerCase().replace(/\s+/g, '_'))
        .join(':');
};
