let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let course_management_setting = new Schema({
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION
    },
    program: {
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM
        },
        program_name: String
    },
    session_type: {
        type: String,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    gender: {
        type: String,
        enum: [constant.GENDER.MALE, constant.GENDER.FEMALE, constant.GENDER.BOTH]
    },
    time: {
        start_time: {
            type: String,
            required: true
        },
        end_time: {
            type: String,
            required: true
        }
    },
    days: {
        mode: {
            type: String,
            required: true
        },
        weekly: [{
            type: String,
            required: true
        }]
    },
    start_date: {
        type: Date,
    },
    end_date: {
        type: Date,
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.COURSE_MANAGEMENT_SETTING, course_management_setting);