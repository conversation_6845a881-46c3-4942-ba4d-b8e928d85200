const multer = require('multer');
const { uploadDocumentFile } = require('../../utility/file-upload');
const keys = require('../../utility/util_keys');
const { getS3SignedUrl } = require('../../services/aws.service');
const { sendStaffEmail } = require('../../services/email.service');
const { GENDER, PENDING } = require('../../utility/constants');
const { response_function } = require('../../utility/common');
const moment = require('moment');
const { base64encode } = require('nodejs-base64');
const { sendMessage } = require('../../services/slack.service');
// const { addRecords } = require('../../services/elasticSearch.service');
const { USER_INSTITUTIONS } = require('../../utility/util_keys');
const { makeDeepLinkUrl } = require('../../services/deep-link.service');

const validUser = (user, mandatoryKey, user_type, programData) => {
    const nameRegex = /[a-zA-Z]*$/;
    const codeRegex = /[+0-9]*$/;
    const emailRegex = /\S+@\S+\.\S+/;
    const genderValidator = [GENDER.MALE, GENDER.FEMALE, GENDER.BOTH];
    const status = [];
    const {
        first_name,
        middle_name,
        last_name,
        family_name,
        employeeId,
        academicNo,
        enrollmentYear,
        programName,
        batch,
        email,
        code,
        mobile,
        gender,
    } = user;
    if (mandatoryKey.includes('first_name') && (!first_name || !nameRegex.test(first_name))) {
        status.push('first_name');
    } else if (first_name && !nameRegex.test(first_name)) {
        status.push('first_name');
    }
    if (mandatoryKey.includes('last_name') && (!last_name || !nameRegex.test(last_name))) {
        status.push('last_name');
    } else if (last_name && !nameRegex.test(last_name)) {
        status.push('last_name');
    }
    if (mandatoryKey.includes('middle_name') && (!middle_name || !nameRegex.test(middle_name))) {
        status.push('middle_name');
    } else if (middle_name && !nameRegex.test(middle_name)) {
        status.push('middle_name');
    }
    if (mandatoryKey.includes('family_name') && (!family_name || !nameRegex.test(family_name))) {
        status.push('family_name');
    } else if (family_name && !nameRegex.test(family_name)) {
        status.push('family_name');
    }
    if (
        mandatoryKey.includes('gender') &&
        (!gender || !genderValidator.includes(gender.toLowerCase()))
    ) {
        status.push('gender');
    } else if (gender && !genderValidator.includes(gender.toLowerCase())) {
        status.push('gender');
    }
    if (user_type === 'staff') {
        if (
            mandatoryKey.includes('user_id') &&
            (!employeeId || typeof employeeId !== 'string' || employeeId.length < 0)
        ) {
            status.push('employeeId');
        } else if (employeeId && (typeof employeeId !== 'string' || employeeId.length < 0)) {
            status.push('employeeId');
        }
    } else if (user_type === 'student') {
        if (
            mandatoryKey.includes('user_id') &&
            (!academicNo || typeof academicNo !== 'string' || academicNo.length < 0)
        ) {
            status.push('academicNo');
        } else if (academicNo && (typeof academicNo !== 'string' || academicNo.length < 0)) {
            status.push('academicNo');
        }
        if (
            mandatoryKey.includes('enrollment_year') &&
            (!enrollmentYear ||
                typeof enrollmentYear !== 'string' ||
                enrollmentYear.length !== 4 ||
                !Number(enrollmentYear))
        ) {
            status.push('enrollmentYear');
        } else if (
            enrollmentYear &&
            (typeof enrollmentYear !== 'string' ||
                enrollmentYear.length !== 4 ||
                !Number(enrollmentYear))
        ) {
            status.push('enrollmentYear');
        }
        if (
            mandatoryKey.includes('batch') &&
            (!batch || typeof batch !== 'string' || batch.length < 0)
        ) {
            status.push('batch');
        } else if (batch && (typeof batch !== 'string' || batch.length < 0)) {
            status.push('batch');
        } else if (batch) {
            const checkProgram = programData.find((element) => element.name == programName);
            if (!checkProgram) {
                status.push('batch');
            } else if (checkProgram) {
                const checkBatch = checkProgram.terms.find((element) => element.termName == batch);
                if (!checkBatch) {
                    status.push('batch');
                }
            }
        }
        if (mandatoryKey.includes('program_no') && !programName) {
            status.push('programName');
        } else if (programName) {
            const checkProgram = programData.find((element) => element.name == programName);
            if (!checkProgram) {
                status.push('programName');
            }
        }
    }
    if (code) if (!codeRegex.test(code)) status.push('code');
    if (!email || !emailRegex.test(email)) status.push('email');
    if (!mobile || typeof mobile != 'number') status.push('mobile');
    return status;
};

const fileUpload = uploadDocumentFile.fields([
    {
        name: 'file',
        maxCount: 1,
    },
]);

const uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getsignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getS3SignedUrl({
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        key: `users/documentation/` + fileName,
    });
    return signedUrl;
};

const requestMailReplace = (body, data) => {
    let replacedBody = body.replace(/User_Name/g, data.name);
    replacedBody = replacedBody.replace(/Clicking_Here/g, data.link);
    replacedBody = replacedBody.replace(/Temp_Password/g, data.password);
    replacedBody = replacedBody.replace(/Date/g, data.dateExpired);
    replacedBody = replacedBody.replace(/College_Name/g, data.collegeName);
    replacedBody = replacedBody.replace(/Flagged_Documents/g, data.flaggedDocuments);
    return replacedBody;
};

const sendStaffMail = async (userImportsCopy, settingDoc, collegeName, origin, body, subject) => {
    if (
        body === undefined &&
        settingDoc &&
        settingDoc.globalConfiguration &&
        settingDoc.globalConfiguration.staffUserManagement
    )
        settingDoc.globalConfiguration.staffUserManagement.mailConfiguration.forEach((val) => {
            if (val.isActive === true && val.labelName === 'Request for verification') {
                body = val.labelBody;
            }
        });
    for (let i = 0; i < userImportsCopy.length; i++) {
        // // User Elastic Push
        // const userDocument = {
        //     email: userImportsCopy[i].email,
        //     _institution_id: userImportsCopy[i]._institution_id,
        //     status: PENDING,
        //     subDomain: origin,
        //     isDefault: false,
        // };
        // addRecords({ indexName: USER_INSTITUTIONS, document: userDocument });

        let expiryDate = userImportsCopy[i].invitedAt;
        expiryDate = expiryDate.setDate(
            expiryDate.getDate() +
                (settingDoc &&
                settingDoc.globalConfiguration &&
                settingDoc.globalConfiguration.staffUserManagement
                    ? settingDoc.globalConfiguration.staffUserManagement.mailSettingsConfiguration
                          .mailExpiration
                    : 0),
        );
        const uniqueUser = `email=${userImportsCopy[i].email}&expiry=${expiryDate}&type=signup`;
        const encoded = base64encode(uniqueUser);
        const deepLinkUrl = await makeDeepLinkUrl(`${origin}/signup?${encoded}`, encoded);
        const data = {
            name:
                userImportsCopy[i].firstName != undefined
                    ? userImportsCopy[i].firstName
                    : '' + userImportsCopy[i].middleName != undefined
                    ? userImportsCopy[i].middleName
                    : '' + userImportsCopy[i].lastName != undefined
                    ? userImportsCopy[i].lastName
                    : '' + userImportsCopy[i].familyName != undefined
                    ? userImportsCopy[i].familyName
                    : '',
            link: deepLinkUrl,
            password: userImportsCopy[i].tempPassword,
            dateExpired: new Date(expiryDate),
            collegeName: collegeName.name,
            flaggedDocuments: userImportsCopy[i].flaggedDocuments,
        };
        //const subject = `Staff Registration`;
        let sendMailResponse;
        if (body && body !== null) {
            mailBody = requestMailReplace(body, data);
            let error;
            if (settingDoc.globalConfiguration.basicDetails.emailIdConfiguration) {
                const msg = {
                    from: settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                        .fromEmail,
                    to: userImportsCopy[i].email,
                    subject,
                    mailBody,
                };
                if (
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.displayName &&
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.fromEmail &&
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.password &&
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient &&
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.portNumber
                ) {
                    sendMailResponse = sendStaffEmail({
                        displayName:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .displayName,
                        emailId:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .fromEmail,
                        userName:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .userName,

                        password:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .password,
                        smtpClient:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .smtpClient,
                        portNumber:
                            settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                                .portNumber,
                        toEmail: userImportsCopy[i].email,
                        subject,
                        text: mailBody,
                    });
                }
            }
        }
    }
};

const sendStaffMailForPasswordReset = async (user, settingDoc, collegeName, origin) => {
    let linkExpiry = new Date();
    //15 minutes expiry
    linkExpiry = new Date(linkExpiry.getTime() + 15 * 60000);
    const uniqueUser = `email=${user.email}&password=${user.tempPassword}&expiry=${linkExpiry}&id=${user._id}&userType=${user.user_type}&type=reset`;
    const encoded = base64encode(uniqueUser);
    let body;
    if (settingDoc && settingDoc.globalConfiguration)
        if (user.user_type === 'staff') {
            settingDoc.globalConfiguration.staffUserManagement.mailConfiguration.forEach((val) => {
                if (val.isActive === true && val.labelName === 'Forgot password') {
                    body = val.labelBody;
                }
            });
        } else {
            settingDoc.globalConfiguration.studentUserManagement.mailConfiguration.forEach(
                (val) => {
                    if (val.isActive === true && val.labelName === 'Forgot password') {
                        body = val.labelBody;
                    }
                },
            );
        }
    const deepLinkUrl = await makeDeepLinkUrl(`${origin}/forgot-password?${encoded}`, encoded);
    const data = {
        name:
            user.firstName != undefined
                ? user.firstName
                : '' + user.middleName != undefined
                ? user.middleName
                : '' + user.lastName != undefined
                ? user.lastName
                : '' + user.familyName != undefined
                ? user.familyName
                : '',
        link: deepLinkUrl,
        password: user.tempPassword,
        collegeName: collegeName.name,
    };
    const subject = `Password reset`;
    if (body && body !== null) {
        mailBody = requestMailReplace(body, data);
        if (settingDoc.globalConfiguration.basicDetails.emailIdConfiguration) {
            const msg = {
                from: settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.email,
                to: user.email,
                subject,
                mailBody,
            };
            sendMessage(msg);
            if (
                settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.displayName &&
                settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.fromEmail &&
                settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.password &&
                settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient &&
                settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.portNumber
            ) {
                sendMailResponse = sendStaffEmail({
                    displayName:
                        settingDoc.globalConfiguration.basicDetails.emailIdConfiguration
                            .displayName,
                    emailId:
                        settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.fromEmail,
                    password:
                        settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.password,
                    smtpClient:
                        settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient,
                    portNumber:
                        settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.portNumber,
                    toEmail: user.email,
                    subject,
                    text: mailBody,
                });
            }
        }
    }
};

const getSignedLogoUrl = async (institutes) => {
    let fileName;
    let url;
    const formattedInstitutes = [];
    for (const doc of institutes) {
        fileName = doc.logo.split('/').pop();
        url = await getS3SignedUrl({
            bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
            key: `logos/` + fileName,
        });
        doc.logo = url;
        formattedInstitutes.push(doc);
    }
    return formattedInstitutes;
};

module.exports = {
    uploadDocument,
    getsignedUrl,
    validUser,
    requestMailReplace,
    sendStaffMail,
    sendStaffMailForPasswordReset,
    getSignedLogoUrl,
};
