const Joi = require('joi');
const { com_response, comResponseWithRequest } = require('../../utility/common');

// update feedback schema
function updateCourseSessionSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                rating: Joi.string().optional(),
                comments: Joi.string().optional(),
            }),
            params: {
                scheduleId: Joi.string().length(24),
                studentId: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// get courses by staff id
function getCoursesParamSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                staffId: Joi.string().length(24),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// pushStudent
function pushStudentSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                courseId: Joi.string().optional(),
                staffId: Joi.string().optional(),
                sessionId: Joi.string().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

module.exports = {
    updateCourseSessionSchema,
    pushStudentSchema,
    getCoursesParamSchema,
};
