const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const StudentController = require('./student.controller');
const {
    getStudentCoursesSchema,
    getStudentPortfolioSchema,
    updateStudentPortfolioSchema,
    getGlobalRubricSchema,
} = require('./student.validation');

const {
    userCalendars,
} = require('../../../lib/digi_class/course_session/course_session_controller');

router.get('/institute-calender', catchAsync(userCalendars));

router.get(
    '/course',
    validate(getStudentCoursesSchema),
    catchAsync(StudentController.getStudentCourses),
);

router.get(
    '/portfolio',
    validate(getStudentPortfolioSchema),
    catchAsync(StudentController.getStudentPortfolio),
);

router.put(
    '/portfolio',
    validate(updateStudentPortfolioSchema),
    catchAsync(StudentController.updateStudentPortfolio),
);

router.get(
    '/global-rubric',
    validate(getGlobalRubricSchema),
    catchAsync(StudentController.getGlobalRubric),
);

module.exports = router;
