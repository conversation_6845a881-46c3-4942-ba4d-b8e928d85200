const { APP_STATE, TIMEZONE, LOCAL_TIMEZONE } = require('../../utility/util_keys');

class DigiDate extends Date {
    constructor(date = new Date()) {
        super();
        this.date = date;
        return this;
    }

    static getLocalISO(date = new Date()) {
        const timeZone = APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
        const eveDate = new Date(date).toLocaleString('en-US', { timeZone });
        const splitData = eveDate.split(',');
        const dateSplit = splitData[0].split('/');
        const timeDataSplit = splitData[1].split(' ');
        const timeSplit = timeDataSplit[1].split(':');
        return new Date(
            dateSplit[2] +
                '-' +
                dateSplit[0] +
                '-' +
                dateSplit[1] +
                ' ' +
                timeSplit[0] +
                ':' +
                timeSplit[1] +
                ' ' +
                timeDataSplit[2],
        );
    }

    seconds(seconds = 0) {
        this.date = new Date(this.date.setSeconds(seconds));
        return this;
    }

    milliSeconds(milliSeconds = 0) {
        this.date = new Date(this.date.setMilliseconds(milliSeconds));
        return this;
    }

    getISO() {
        return this.date.toISOString();
    }

    get() {
        return this.date;
    }
}

exports.DigiDate = DigiDate;
