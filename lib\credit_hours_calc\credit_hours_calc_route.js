const express = require('express');
const route = express.Router();
const credit_hours_calc = require('./credit_hours_calc_controller');
const validater = require('./credit_hours_calc_validator');
route.post('/list', credit_hours_calc.list_values);
route.get('/:id', validater.credit_hours_calc_id, credit_hours_calc.list_id);
route.get('/', credit_hours_calc.list);
route.post('/', validater.credit_hours_calc, credit_hours_calc.insert);
route.put('/:id', validater.credit_hours_calc_id, validater.credit_hours_calc_update, credit_hours_calc.update);
route.delete('/:id', validater.credit_hours_calc_id, credit_hours_calc.delete);
route.get('/program/:id',validater.credit_hours_calc_id, credit_hours_calc.list_program);

module.exports = route;