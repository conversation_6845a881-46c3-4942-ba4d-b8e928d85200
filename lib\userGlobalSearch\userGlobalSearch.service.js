const { clone, convertToMongoObjectId, query: commonQuery } = require('../utility/common');
const {
    COMPLETED,
    PRESENT,
    ABSENT,
    LEAVE_TYPE: { LEAVE, PERMISSION },
    ON_DUTY,
    COURSE_WISE,
    EXCLUDE,
} = require('../utility/constants');
const {
    SERVICES: { LMS_VERSION, REACT_APP_INSTITUTION_ID },
} = require('../utility/util_keys');
const {
    lmsNewSetting,
    getLateAutoAndManualRange,
    getConfigAndStudentLateAbsentForSingleStudent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
} = require('../utility/utility.service');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const sessionDeliveryTypeSchema = require('../models/digi_session_delivery_types');
const sessionOrderDataSchema = require('../models/digi_session_order');
const programCalendarSchema = require('../models/program_calendar');
const courseSchema = require('../models/digi_course');
const updateStaffAttendance = async ({ existingScheduleDetail, status, staffAttendanceStatus }) => {
    if (status === COMPLETED) {
        existingScheduleDetail.totalCompletedSchedule += 1;
        if (staffAttendanceStatus === PRESENT) {
            existingScheduleDetail.totalPresent += 1;
        } else {
            existingScheduleDetail.totalAbsent += 1;
        }
    }
};
const getCourseStartDateAndEndDate = async ({
    institutionCalendarId,
    programIds,
    terms,
    years,
    levels,
    courseIds,
    rotationCourseIds,
    rotationCourseCount,
}) => {
    try {
        let queryConditions = {
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: { $in: programIds },
            'level.term': { $in: terms },
            'level.year': { $in: years },
            'level.level_no': { $in: levels },
        };
        if (rotationCourseIds.length && rotationCourseCount.length) {
            queryConditions.$or = [
                {
                    'level.rotation_course.rotation_count': { $in: rotationCourseCount },
                    'level.rotation_course.course._course_id': { $in: rotationCourseIds },
                },
                { 'level.course._course_id': { $in: courseIds } },
            ];
        } else {
            queryConditions = { ...queryConditions, 'level.course._course_id': { $in: courseIds } };
        }
        const programCalendarData = await programCalendarSchema
            .find(queryConditions, {
                _program_id: 1,
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.course': 1,
                'level.rotation_course': 1,
                'level.rotation_count': 1,
            })
            .lean();
        const programCalendarGroupedObj = new Map();
        for (const programCalendarELement of programCalendarData) {
            for (const programCalendarLevelElement of programCalendarELement.level) {
                for (const programCalendarCourseElement of programCalendarLevelElement.course) {
                    const programCalendarGroupedObjKey = `${programCalendarELement._program_id}-${programCalendarLevelElement.term}-${programCalendarLevelElement.year}-${programCalendarLevelElement.level_no}-${programCalendarCourseElement._course_id}-${programCalendarLevelElement.rotation_count}`;
                    programCalendarGroupedObj.set(programCalendarGroupedObjKey, {
                        startDate: programCalendarCourseElement.start_date,
                        endDate: programCalendarCourseElement.end_date,
                    });
                }
                if (
                    rotationCourseIds.length &&
                    programCalendarLevelElement.rotation_course.length
                ) {
                    for (const programCalendarRotationCourseElementCourseElement of programCalendarLevelElement.rotation_course) {
                        if (
                            rotationCourseCount.includes(
                                programCalendarRotationCourseElementCourseElement.rotation_count,
                            )
                        ) {
                            for (const rotationCourseDetailElement of programCalendarRotationCourseElementCourseElement.course) {
                                if (
                                    rotationCourseIds.includes(
                                        rotationCourseDetailElement._course_id,
                                    )
                                ) {
                                    const programCalendarGroupedObjKey = `${programCalendarELement._program_id}-${programCalendarLevelElement.term}-${programCalendarLevelElement.year}-${programCalendarLevelElement.level_no}-${rotationCourseDetailElement._course_id}-${programCalendarRotationCourseElementCourseElement.rotation_count}`;
                                    programCalendarGroupedObj.set(programCalendarGroupedObjKey, {
                                        startDate: rotationCourseDetailElement.start_date,
                                        endDate: rotationCourseDetailElement.end_date,
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }
        return programCalendarGroupedObj;
    } catch (error) {
        console.log(
            `getCourseStartDateAndEndDate -> programCalendarGroupedObj -> error - ${error}`,
        );
        return new Map();
    }
};
exports.getStaffCourseDetails = async ({ staffScheduleData, staffId, institutionCalendarId }) => {
    try {
        const groupedStaffScheduleObj = new Map();
        const courseIds = [];
        const rotationCourseIds = [];
        const rotationCourseCount = [];
        const programIds = [];
        const terms = [];
        const years = [];
        const levels = [];
        for (const staffScheduleElement of staffScheduleData) {
            const {
                _program_id = '',
                _course_id = '',
                program_name = '',
                course_name = '',
                course_code = '',
                term = '',
                year_no = '',
                level_no = '',
                status = '',
                rotation = 'no',
                rotation_count = 0,
                session = {},
                staffs = [],
            } = staffScheduleElement;
            if (rotation === 'yes' && rotation_count) {
                rotationCourseIds.push(_course_id);
                rotationCourseCount.push(rotation_count);
            }
            courseIds.push(_course_id);
            programIds.push(_program_id);
            terms.push(term);
            years.push(year_no);
            levels.push(level_no);
            const groupedStaffScheduleObjKey = `${_program_id}-${_course_id}-${term}-${year_no}-${level_no}`;
            if (!groupedStaffScheduleObj.has(groupedStaffScheduleObjKey)) {
                groupedStaffScheduleObj.set(groupedStaffScheduleObjKey, {
                    programId: _program_id,
                    programName: program_name,
                    courseId: _course_id,
                    courseName: course_name,
                    courseCode: course_code,
                    sessions: [],
                    term,
                    rotation,
                    rotation_count,
                    year: year_no,
                    level: level_no,
                    totalSchedules: 0,
                    totalCompletedSchedule: 0,
                    totalPresent: 0,
                    totalAbsent: 0,
                });
            }
            const existingScheduleDetail = groupedStaffScheduleObj.get(groupedStaffScheduleObjKey);
            const staffAttendanceStatus =
                staffs.length &&
                staffs[0]._staff_id &&
                staffs[0].status &&
                staffs[0]._staff_id.toString() === staffId.toString()
                    ? staffs[0].status
                    : ABSENT;
            await updateStaffAttendance({
                existingScheduleDetail,
                status,
                staffAttendanceStatus,
            });
            existingScheduleDetail.totalSchedules += 1;
            existingScheduleDetail.sessions.push({
                ...session,
                scheduleStatus: status,
                staffAttendanceStatus,
            });
        }
        //get course credit hour, course type, program code details
        const courseDetailWithCreditHour = await courseSchema
            .find(
                {
                    _id: { $in: [...new Set(courseIds)] },
                },
                {
                    _program_id: 1,
                    'credit_hours.type_name': 1,
                    'credit_hours.type_symbol': 1,
                    'credit_hours.duration_per_contact_hour': 1,
                    course_type: 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            )
            .lean()
            .populate({ path: '_program_id', select: { code: 1 } });
        //constructing creditHour,courseCode and programCode details
        const groupedCourseDetailsWithCreditHours = new Map();
        for (const creditHourDetailELement of courseDetailWithCreditHour) {
            const key = `${creditHourDetailELement._id}-${creditHourDetailELement._program_id._id}`;
            groupedCourseDetailsWithCreditHours.set(key, {
                courseType: creditHourDetailELement.course_type
                    ? creditHourDetailELement.course_type
                    : '',
                creditHourDetails: creditHourDetailELement.credit_hours
                    ? creditHourDetailELement.credit_hours
                    : [],
                programCode: creditHourDetailELement._program_id.code
                    ? creditHourDetailELement._program_id.code
                    : '',
                versionNo: creditHourDetailELement.versionNo
                    ? creditHourDetailELement.versionNo
                    : 1,
                versioned: creditHourDetailELement.versioned
                    ? creditHourDetailELement.versioned
                    : false,
                versionName: creditHourDetailELement.versionName
                    ? creditHourDetailELement.versionName
                    : '',
                versionedFrom: creditHourDetailELement.versionedFrom
                    ? creditHourDetailELement.versionedFrom
                    : null,
                versionedCourseIds: creditHourDetailELement.versionedCourseIds
                    ? creditHourDetailELement.versionedCourseIds
                    : [],
            });
        }
        const courseStartDateAndEndDetails = await getCourseStartDateAndEndDate({
            institutionCalendarId,
            programIds,
            terms,
            years,
            levels,
            courseIds,
            rotationCourseIds,
            rotationCourseCount,
        });
        const sessionDeliveryType = await sessionDeliveryTypeSchema
            .find(
                { isDeleted: false, _program_id: { $in: programIds } },
                {
                    _program_id: 1,
                    session_name: 1,
                    session_symbol: 1,
                    contact_hour_per_credit_hour: 1,
                    'delivery_types.delivery_name': 1,
                    'delivery_types.delivery_symbol': 1,
                    'delivery_types.delivery_duration': 1,
                },
            )
            .lean();
        const sessionOrderData = await sessionOrderDataSchema
            .find(
                {
                    ...commonQuery,
                    _course_id: { $in: courseIds },
                },
                {
                    _program_id: 1,
                    _course_id: 1,
                    'session_flow_data._id': 1,
                    'session_flow_data.duration': 1,
                },
            )
            .lean();
        const staffCourseDetailsWithCreditHours = [];
        for (const [
            groupedScheduleIndex,
            groupedScheduleElement,
        ] of groupedStaffScheduleObj.entries()) {
            try {
                //currentCourse Credit Hour, courseType, and Program Code details
                const key = `${groupedScheduleElement.courseId}-${groupedScheduleElement.programId}`;
                const currentCourseDetailsWithCreditHours =
                    groupedCourseDetailsWithCreditHours.get(key);
                const creditHourDetail = currentCourseDetailsWithCreditHours.creditHourDetails;
                const courseType = currentCourseDetailsWithCreditHours.courseType;
                const programCode = currentCourseDetailsWithCreditHours.programCode;
                const versionNo = currentCourseDetailsWithCreditHours.versionNo;
                const versioned = currentCourseDetailsWithCreditHours.versioned;
                const versionName = currentCourseDetailsWithCreditHours.versionName;
                const versionedFrom = currentCourseDetailsWithCreditHours.versionedFrom;
                const versionedCourseIds = currentCourseDetailsWithCreditHours.versionedCourseIds;
                // get all session types for current program(ex:theory,practical and so on...)
                const sessionDeliveryForCurrentProgram = sessionDeliveryType.filter(
                    (sessionDeliveryELement) =>
                        sessionDeliveryELement._program_id &&
                        sessionDeliveryELement._program_id.toString() ===
                            groupedScheduleElement.programId.toString(),
                );
                // get total sessions they have created for current course(ex:theory(interactive lecture - 3sessions) and so on...)
                const sessionOrderIndex = sessionOrderData.findIndex(
                    (sessionOrderDataElement) =>
                        sessionOrderDataElement._program_id &&
                        sessionOrderDataElement._course_id &&
                        sessionOrderDataElement._program_id.toString() ===
                            groupedScheduleElement.programId.toString() &&
                        sessionOrderDataElement._course_id.toString() ===
                            groupedScheduleElement.courseId.toString(),
                );
                // we have all scheduledSession and total sessionOrder created for the current course.
                const totalSessionConducted = groupedScheduleElement.sessions;
                const sessionFlowData = sessionOrderData[sessionOrderIndex].session_flow_data || [];
                //calculate credit hours for all session delivery types for the current program
                const creditHourCalculations = sessionDeliveryForCurrentProgram.map(
                    (sessionDeliveryForCurrentProgramElement) => {
                        // get contact hour and credit hour created for the current session type
                        const creditHourDetails = creditHourDetail.find(
                            (creditHourELement) =>
                                creditHourELement.type_name ===
                                    sessionDeliveryForCurrentProgramElement.session_name &&
                                creditHourELement.type_symbol ===
                                    sessionDeliveryForCurrentProgramElement.session_symbol,
                        );
                        let totalScheduledSessionForCurrentSessionType = [];
                        let totalScheduledSessionContactHours = 0;

                        let completedScheduledSessionForCurrentSessionType = [];
                        let completedScheduledSessionContactHours = 0;

                        for (const sessionDeliveryTypeElement of sessionDeliveryForCurrentProgramElement.delivery_types) {
                            const totalSessionForCurrentDeliveryType = [];
                            const totalCompletedSessionForCurrentDeliveryTypes = [];
                            // for current delivery types get(how many session are scheduled and how many completed)
                            for (const totalSessionConductedElement of totalSessionConducted) {
                                if (
                                    sessionDeliveryTypeElement.delivery_name &&
                                    totalSessionConductedElement.session_type &&
                                    sessionDeliveryTypeElement.delivery_name.toLowerCase() ===
                                        totalSessionConductedElement.session_type.toLowerCase()
                                ) {
                                    totalSessionForCurrentDeliveryType.push({
                                        _session_id: totalSessionConductedElement._session_id,
                                    });
                                    // MultiStaff creditHour and contact hour calculation flow handled code
                                    // if (
                                    //     totalSessionConductedElement.scheduleStatus === COMPLETED &&
                                    //     totalSessionConductedElement.staffAttendanceStatus ===
                                    //         PRESENT
                                    // ) {
                                    //     totalCompletedSessionForCurrentDeliveryTypes.push({
                                    //         _session_id: totalSessionConductedElement._session_id,
                                    //     });
                                    // }
                                    if (totalSessionConductedElement.scheduleStatus === COMPLETED) {
                                        totalCompletedSessionForCurrentDeliveryTypes.push({
                                            _session_id: totalSessionConductedElement._session_id,
                                        });
                                    }
                                }
                                // get current ScheduledSession actual duration from session flow data
                                const actualDurationForCurrentSession = sessionFlowData.find(
                                    (sessionFlowDataElement) =>
                                        totalSessionConductedElement.session_type &&
                                        sessionDeliveryTypeElement.delivery_name &&
                                        totalSessionConductedElement._session_id &&
                                        totalSessionConductedElement.session_type.toLowerCase() ===
                                            sessionDeliveryTypeElement.delivery_name.toLowerCase() &&
                                        totalSessionConductedElement._session_id.toString() ===
                                            sessionFlowDataElement._id.toString(),
                                );
                                //calculate total duration and completed duration
                                if (actualDurationForCurrentSession) {
                                    totalScheduledSessionContactHours += parseInt(
                                        actualDurationForCurrentSession.duration,
                                    );
                                    // MultiStaff creditHour and contact hour calculation flow handled code
                                    // if (
                                    //     totalSessionConductedElement.scheduleStatus === COMPLETED &&
                                    //     totalSessionConductedElement.staffAttendanceStatus ===
                                    //         PRESENT
                                    // ) {
                                    //     completedScheduledSessionContactHours += parseInt(
                                    //         actualDurationForCurrentSession.duration,
                                    //     );
                                    // }
                                    if (totalSessionConductedElement.scheduleStatus === COMPLETED) {
                                        completedScheduledSessionContactHours += parseInt(
                                            actualDurationForCurrentSession.duration,
                                        );
                                    }
                                }
                            }
                            totalScheduledSessionForCurrentSessionType = [
                                ...totalScheduledSessionForCurrentSessionType,
                                ...totalSessionForCurrentDeliveryType,
                            ];
                            completedScheduledSessionForCurrentSessionType = [
                                ...completedScheduledSessionForCurrentSessionType,
                                ...totalCompletedSessionForCurrentDeliveryTypes,
                            ];
                        }
                        const courseDeliveryWiseDurationPerContactHours =
                            creditHourDetails &&
                            creditHourDetails.duration_per_contact_hour &&
                            parseInt(creditHourDetails.duration_per_contact_hour)
                                ? parseInt(creditHourDetails.duration_per_contact_hour)
                                : 60;
                        const totalContactHours =
                            totalScheduledSessionContactHours !== 0
                                ? totalScheduledSessionContactHours /
                                  courseDeliveryWiseDurationPerContactHours
                                : 0;
                        const totalCompletedContactHours =
                            completedScheduledSessionContactHours !== 0
                                ? completedScheduledSessionContactHours /
                                  courseDeliveryWiseDurationPerContactHours
                                : 0;
                        return {
                            totalSession: totalScheduledSessionForCurrentSessionType.length,
                            totalCompletedSession:
                                completedScheduledSessionForCurrentSessionType.length,
                            totalCreditHours:
                                totalContactHours /
                                    parseInt(
                                        sessionDeliveryForCurrentProgramElement.contact_hour_per_credit_hour,
                                    ) || 0,
                            totalCompletedCreditHours:
                                totalCompletedContactHours /
                                    parseInt(
                                        sessionDeliveryForCurrentProgramElement.contact_hour_per_credit_hour,
                                    ) || 0,
                            totalContactHours,
                            totalCompletedContactHours,
                            typeName: creditHourDetails.type_name,
                            typeSymbol: creditHourDetails.type_symbol,
                        };
                    },
                );
                //absentPercentage calculation
                const absentPercentage =
                    groupedScheduleElement.totalAbsent !== 0
                        ? (groupedScheduleElement.totalAbsent /
                              groupedScheduleElement.totalCompletedSchedule) *
                          100
                        : 0;
                //presentPercentage calculation
                const presentPercentage =
                    groupedScheduleElement.totalPresent !== 0
                        ? (groupedScheduleElement.totalPresent /
                              groupedScheduleElement.totalCompletedSchedule) *
                          100
                        : 0;
                //course start date and end date
                const constructedCourseObjKey = `${groupedScheduleElement.programId}-${groupedScheduleElement.term}-${groupedScheduleElement.year}-${groupedScheduleElement.level}-${groupedScheduleElement.courseId}-${groupedScheduleElement.rotation_count}`;
                const currentCourseStartAndEndDate =
                    courseStartDateAndEndDetails.get(constructedCourseObjKey);
                delete groupedScheduleElement.creditHours;
                delete groupedScheduleElement.sessions;
                staffCourseDetailsWithCreditHours.push({
                    ...groupedScheduleElement,
                    creditHourCalculations,
                    courseType,
                    programCode,
                    absentPercentage,
                    presentPercentage,
                    courseStartAndEndDate: currentCourseStartAndEndDate || {
                        startDate: '',
                        endDate: '',
                    },
                    versionNo,
                    versioned,
                    versionName,
                    versionedFrom,
                    versionedCourseIds,
                });
            } catch (error) {
                console.log(
                    `Error in iterating groupedStaffScheduleObj at index-${groupedScheduleIndex} -> Error-${error}`,
                );
                continue;
            }
        }
        return staffCourseDetailsWithCreditHours;
    } catch (error) {
        console.error(error);
        return [];
    }
};
const updateStudentAttendance = async ({ existingScheduleDetail, status, students, studentId }) => {
    if (status === COMPLETED && students.length && students[0].status !== EXCLUDE) {
        existingScheduleDetail.totalCompletedSchedule += 1;
        if (
            students.length &&
            students[0]._id &&
            students[0]._id.toString() === studentId.toString()
        ) {
            switch (students[0].status) {
                case PRESENT:
                    existingScheduleDetail.totalPresent += 1;
                    break;
                case PERMISSION:
                    existingScheduleDetail.totalPermission += 1;
                    break;
                case ON_DUTY:
                    existingScheduleDetail.totalOnDuty += 1;
                    break;
                case LEAVE:
                    existingScheduleDetail.totalLeave += 1;
                    break;
                default:
                    existingScheduleDetail.totalAbsent += 1;
            }
        }
    }
};
exports.getStudentCourseDetails = async ({
    studentScheduleData,
    studentId,
    institutionCalendarId,
    _institution_id,
}) => {
    try {
        const groupedStudentScheduleObj = new Map();
        let studentCourseIds = [];
        for (const studentScheduleElement of studentScheduleData) {
            const {
                _program_id = '',
                _course_id = '',
                program_name = '',
                course_name = '',
                course_code = '',
                term = '',
                year_no = '',
                level_no = '',
                status = '',
                rotation = 'no',
                rotation_count = 0,
                students = [],
            } = studentScheduleElement;
            studentCourseIds.push(_course_id);
            const groupedScheduleObjKey = `${_program_id}-${_course_id}-${term}-${year_no}-${level_no}`;
            if (!groupedStudentScheduleObj.has(groupedScheduleObjKey)) {
                groupedStudentScheduleObj.set(groupedScheduleObjKey, {
                    programId: _program_id,
                    programName: program_name,
                    courseId: _course_id,
                    courseName: course_name,
                    courseCode: course_code,
                    term,
                    rotation,
                    rotation_count,
                    year: year_no,
                    level: level_no,
                    totalSchedules: 0,
                    totalCompletedSchedule: 0,
                    totalPresent: 0,
                    totalAbsent: 0,
                    totalPermission: 0,
                    totalOnDuty: 0,
                    totalLeave: 0,
                });
            }
            const existingScheduleDetail = groupedStudentScheduleObj.get(groupedScheduleObjKey);
            await updateStudentAttendance({ existingScheduleDetail, status, students, studentId });
            if (students.length && students[0].status !== EXCLUDE) {
                existingScheduleDetail.totalSchedules += 1;
            }
        }
        // get course type and program code details
        const courseDetails = await courseSchema
            .find(
                {
                    _id: { $in: [...new Set(studentCourseIds)] },
                },
                {
                    _program_id: 1,
                    course_type: 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            )
            .lean()
            .populate({ path: '_program_id', select: { code: 1 } });
        //constructing courseCode and programCode details
        const groupedCourseDetails = new Map();
        for (const courseDetailsElement of courseDetails) {
            const key = `${courseDetailsElement._id}-${courseDetailsElement._program_id._id}`;
            groupedCourseDetails.set(key, {
                courseType: courseDetailsElement.course_type
                    ? courseDetailsElement.course_type
                    : '',
                versionNo: courseDetailsElement.versionNo ? courseDetailsElement.versionNo : 1,
                versioned: courseDetailsElement.versioned ? courseDetailsElement.versioned : false,
                versionName: courseDetailsElement.versionName
                    ? courseDetailsElement.versionName
                    : '',
                versionedFrom: courseDetailsElement.versionedFrom
                    ? courseDetailsElement.versionedFrom
                    : null,
                versionedCourseIds: courseDetailsElement.versionedCourseIds
                    ? courseDetailsElement.versionedCourseIds
                    : [],
                programCode: courseDetailsElement._program_id.code
                    ? courseDetailsElement._program_id.code
                    : '',
            });
        }
        let lmsSettingData = {};
        let studentCriteriaData = [];
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId: studentCourseIds,
        });
        if (LMS_VERSION === 'V2') {
            studentCourseIds = [...new Set(studentCourseIds)];
            const institutionId =
                _institution_id && _institution_id.length
                    ? convertToMongoObjectId(_institution_id)
                    : convertToMongoObjectId(REACT_APP_INSTITUTION_ID);
            //lms setting data
            lmsSettingData = await lmsNewSetting({ _institution_id: institutionId });
            //Denial manipulated data
            studentCriteriaData = await lmsDenialSchema
                .find(
                    {
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        $or: [
                            { studentId: convertToMongoObjectId(studentId) },
                            { courseId: { $in: studentCourseIds } },
                        ],
                    },
                    {
                        rotationCount: 1,
                        term: 1,
                        courseId: 1,
                        typeWiseUpdate: 1,
                        studentId: 1,
                        absencePercentage: 1,
                    },
                )
                .sort({ updatedAt: -1 })
                .lean();
        }
        const studentCourseDetailsWithPercentage = [];
        for (const [
            groupedScheduleIndex,
            groupedScheduleElement,
        ] of groupedStudentScheduleObj.entries()) {
            try {
                const {
                    totalSchedules,
                    totalCompletedSchedule,
                    totalPermission,
                    totalOnDuty,
                    totalLeave,
                    rotation,
                    rotation_count,
                    term,
                    year,
                    level,
                    courseId,
                    programId,
                    totalPresent,
                    totalAbsent,
                } = groupedScheduleElement;
                const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo: level,
                    term,
                    rotationCount: rotation_count,
                    lateExcludeManagement,
                });
                const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo: level,
                    term,
                    rotationCount: rotation_count,
                    lateExcludeManagement,
                }).lateExclude;
                let studentLateAbsent = 0;
                if (!lateExclude && !lateExcludeForStudent) {
                    const { studentLateAbsent: updateStudentLateAbsent } =
                        getConfigAndStudentLateAbsentForSingleStudent({
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            sortedStudentSessions: studentScheduleData.filter((scheduleElement) => {
                                return (
                                    ((rotation === 'yes' &&
                                        rotation_count === scheduleElement.rotationCount) ||
                                        rotation === 'no') &&
                                    scheduleElement._course_id.toString() === courseId.toString()
                                );
                            }),
                            _institution_calendar_id: institutionCalendarId,
                            courseId,
                            programId,
                            levelNo: level,
                            term,
                            rotationCount: rotation_count,
                            lateExcludeManagement,
                        });
                    studentLateAbsent = updateStudentLateAbsent;
                }
                const key = `${courseId}-${programId}`;
                const currentCourseTypeAndProgramCode = groupedCourseDetails.get(key);
                const courseType = currentCourseTypeAndProgramCode.courseType;
                const programCode = currentCourseTypeAndProgramCode.programCode;
                const versionNo = currentCourseTypeAndProgramCode.versionNo;
                const versioned = currentCourseTypeAndProgramCode.versioned;
                const versionName = currentCourseTypeAndProgramCode.versionName;
                const versionedFrom = currentCourseTypeAndProgramCode.versionedFrom;
                const versionedCourseIds = currentCourseTypeAndProgramCode.versionedCourseIds;
                const presentPercentage =
                    ((totalPresent - studentLateAbsent + totalPermission + totalOnDuty) /
                        totalCompletedSchedule) *
                    100;
                const absentPercentage =
                    ((totalAbsent + studentLateAbsent + totalLeave) / totalCompletedSchedule) * 100;
                const warningPercentage =
                    ((totalAbsent + totalLeave + studentLateAbsent) / totalSchedules) * 100;

                const studentWarningAbsence = lmsSettingData.warningAbsenceData
                    ? clone(lmsSettingData.warningAbsenceData)
                    : [];

                if (studentCriteriaData && studentCriteriaData.length) {
                    const studentManipulation = studentCriteriaData.find(
                        (denialStudentElement) =>
                            ((rotation === 'yes' && rotation_count > 0
                                ? parseInt(rotation_count) === denialStudentElement.rotationCount
                                : true) &&
                                denialStudentElement.term === term &&
                                denialStudentElement.courseId.toString() === courseId.toString() &&
                                denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                            (denialStudentElement.term === term &&
                                denialStudentElement.studentId &&
                                denialStudentElement.courseId.toString() === courseId.toString() &&
                                denialStudentElement.studentId.toString() === studentId.toString()),
                    );
                    if (
                        studentManipulation &&
                        studentManipulation.absencePercentage &&
                        studentWarningAbsence.length &&
                        studentWarningAbsence[0].percentage &&
                        studentWarningAbsence[0].percentage < studentManipulation.absencePercentage
                    ) {
                        studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                    }
                }
                let warningLabel = '';
                let waringColorCode = '';
                if (studentWarningAbsence.length) {
                    const warningData = studentWarningAbsence.find(
                        (studentWarningElement) =>
                            studentWarningElement.percentage &&
                            parseFloat(warningPercentage) >
                                parseFloat(studentWarningElement.percentage),
                    );
                    if (warningData && warningData.labelName && warningData.colorCode) {
                        warningLabel = warningData.labelName;
                        waringColorCode = warningData.colorCode;
                    }
                }
                studentCourseDetailsWithPercentage.push({
                    ...groupedScheduleElement,
                    courseType,
                    programCode,
                    presentPercentage,
                    absentPercentage,
                    warningAbsencePercentage: warningPercentage,
                    warning: warningLabel,
                    totalPresent,
                    totalAbsent,
                    colorCode: waringColorCode,
                    versionNo,
                    versioned,
                    versionName,
                    versionedFrom,
                    versionedCourseIds,
                    studentLateAbsent,
                });
            } catch (error) {
                console.log(
                    `Error in iterating groupedStudentScheduleObj at index-${groupedScheduleIndex} -> Error-${error}`,
                );
                continue;
            }
        }
        return studentCourseDetailsWithPercentage;
    } catch (error) {
        console.error(error);
        return [];
    }
};
