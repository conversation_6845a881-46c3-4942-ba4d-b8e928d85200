const constant = require('../utility/constants');
const country = require('mongoose').model(constant.COUNTRY);
const institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const country_formate = require('./country_formate');
const ObjectId = common_files.convertToMongoObjectId;

exports.list = async (req, res) => {
    const query = { isDeleted: false };
    const project = { _id: 1, name: 1, code: 1, currency: 1, isDeleted: 1, isActive: 1 };
    const doc = await base_control.list(country, req.query.limit, req.query.pageNo, query, project);
    if (doc.status) {
        common_files.listAllResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('COUNTRY_LIST'),
            doc.totalDoc,
            doc.totalPages,
            doc.currentPage,
            country_formate.country(doc.data),
        );
    } else {
        common_files.sendResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const query = { _id: id, isDeleted: false };
    const project = { _id: 1, name: 1, code: 1, currency: 1, isDeleted: 1, isActive: 1 };
    const doc = await base_control.get(country, query, project);
    if (doc.status) {
        const formated = country_formate.country_ID(doc.data);
        common_files.com_response(res, 200, true, 'Country List', formated);
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.insert = async (req, res) => {
    const query = { name: req.body.name, isDeleted: false };
    const project = { _id: 1, name: 1, code: 1, currency: 1, isDeleted: 1, isActive: 1 };
    const docs = await base_control.get(country, query, project);
    if (!docs.status) {
        const doc = await base_control.insert(country, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, 'Country Added successfully', doc.data);
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error duplicate values found ',
            'This content already present in DB',
        );
    }
};

exports.update = async (req, res) => {
    const object_id = req.params.id;
    const doc = await base_control.update(country, object_id, req.body);
    if (doc.status) {
        common_files.com_response(res, 201, true, 'Update successfully', doc.data);
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.delete = async (req, res) => {
    const id = req.params.id;

    //Before delete it has to check linked collections
    const aggre = { _country_id: ObjectId(id) };
    const doc_check = await base_control.get(institution, aggre, {});
    console.log(doc_check);
    if (!doc_check.status) {
        const doc = await base_control.delete(country, id);
        if (doc.status) {
            common_files.com_response(res, 201, true, 'Deleted successfully', doc.data);
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            409,
            false,
            'Error',
            'Unable to Delete its linked to institution so please delete linked institution first',
        );
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }

        const doc = await base_control.get_list(country, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                'Country List',
                country_formate.country(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error parse field in body',
            'Error parse field in body',
        );
    }
};

exports.bulk_import = async (req, res) => {
    const country_imports = [];
    req.body.data.forEach((doc) => {
        const objs = {
            insertOne: {
                document: {
                    name: doc.name,
                    code: doc.code,
                    currency: doc.currency,
                },
            },
        };
        country_imports.push(objs);
    });
    const doc_ins = await base_control.bulk_write(country, country_imports);
    if (doc_ins.status) {
        common_files.com_response(res, 201, true, 'Country Added successfully', doc_ins.data);
    } else {
        common_files.com_response(res, 500, false, 'Error', doc_ins.data);
    }
};
