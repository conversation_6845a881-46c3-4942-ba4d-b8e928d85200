const { Joi } = require('../../common/middlewares/validation');
const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const generatePortfolioReportSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        studentIds: Joi.array().optional(),
    }),
}).unknown(true);

const getPortfolioReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
    }),
}).unknown(true);

const addExtraMarkSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        studentId: objectIdRQSchema,
        marks: Joi.number().integer().required(),
    }),
}).unknown(true);

const updateMarkSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        marks: Joi.number().integer().required(),
        type: Joi.string()
            .valid('equivalenceMark', 'overAllPassMark', 'minComponentsForInferencePass')
            .required(),
    }),
}).unknown(true);

const updateReportSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        overAllPassEnabled: Joi.boolean().required(),
        isComponentWisePassEnabled: Joi.boolean().required(),
    }),
}).unknown(true);

const updateComponentWisePassSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdSchema.optional(),
        childId: objectIdSchema.optional(),
        marks: Joi.number().integer().optional(),
        mustPass: Joi.boolean().optional(),
        completionRules: Joi.array().optional(),
    }),
}).unknown(true);

const getIndividualStudentReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        studentId: objectIdRQSchema,
    }),
}).unknown(true);

const getPortfolioComponentReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdSchema.optional(),
    }),
}).unknown(true);
module.exports = {
    generatePortfolioReportSchema,
    getPortfolioReportSchema,
    addExtraMarkSchema,
    updateMarkSettingsSchema,
    updateReportSettingsSchema,
    updateComponentWisePassSettingsSchema,
    getIndividualStudentReportSchema,
    getPortfolioComponentReportSchema,
};
