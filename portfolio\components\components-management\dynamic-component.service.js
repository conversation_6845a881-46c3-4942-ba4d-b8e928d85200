const DynamicOutcomeComponentModel = require('./dynamic-components.model');

const { NotFoundError } = require('../../common/utils/api_error_util');

const createDynamicOutcomeComponent = async ({ component = [], userId }) => {
    const dynamicOutcomeComponent = await DynamicOutcomeComponentModel.create({
        ...component,
        createdBy: userId,
    });

    return dynamicOutcomeComponent;
};

const getDynamicOutcomeComponents = async ({ componentId }) => {
    if (componentId) {
        const dynamicOutcomeComponent = await DynamicOutcomeComponentModel.findOne(
            { _id: componentId },
            { children: 1, hierarchies: 1, name: 1, createdBy: 1 },
        ).lean();
        if (!dynamicOutcomeComponent) {
            throw new NotFoundError('DYNAMIC_OUTCOME_COMPONENT_NOT_FOUND');
        }

        return dynamicOutcomeComponent;
    }

    const dynamicOutcomeComponents = await DynamicOutcomeComponentModel.find(
        { isDeleted: false },
        { children: 1, hierarchies: 1, name: 1, createdBy: 1 },
    ).lean();

    if (!dynamicOutcomeComponents?.length) {
        throw new NotFoundError('DYNAMIC_OUTCOME_COMPONENT_NOT_FOUND');
    }

    return dynamicOutcomeComponents;
};

const deleteDynamicOutcomeComponent = async ({ componentId }) => {
    const dynamicOutcomeComponent = await DynamicOutcomeComponentModel.deleteOne({
        _id: componentId,
    });

    return dynamicOutcomeComponent;
};

module.exports = {
    createDynamicOutcomeComponent,
    getDynamicOutcomeComponents,
    deleteDynamicOutcomeComponent,
};
