const assignmentRubricsSchema = require('./assignment-rubrics.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { COURSE_BASED, GLOBAL } = require('../../../utility/constants');
const createAssignmentRubrics = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            courseId,
            programId,
            rubricScope,
            code,
            name,
            abbreviation,
            description,
            evaluateInSequence,
            isPublic,
            scoreType,
            rubrics,
            duplicateFromTheRubrics,
        } = body;
        const rubricsDocs = await assignmentRubricsSchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            {
                name: 1,
                code: 1,
            },
        );
        if (rubricsDocs.length) {
            for (const rubrics of rubricsDocs) {
                if (name && rubrics.name && rubrics.name.toLowerCase() === name.toLowerCase()) {
                    return { statusCode: 410, message: 'RUBRICS_NAME_ALREADY_EXISTS' };
                }
                if (code && rubrics.code && rubrics.code.toLowerCase() === code.toLowerCase()) {
                    return { statusCode: 410, message: 'RUBRICS_CODE_ALREADY_EXISTS' };
                }
            }
        }
        const createRubrics = await assignmentRubricsSchema.create({
            _institution_id,
            ...(rubricScope == COURSE_BASED && { rubricScope, courseId }),
            ...(rubricScope == GLOBAL && { rubricScope }),
            code,
            name,
            abbreviation,
            description,
            programId,
            evaluateInSequence,
            isPublic,
            scoreType,
            rubrics,
            duplicateFromTheRubrics,
        });
        if (!createRubrics)
            return { statusCode: 410, message: 'ERROR IN CREATING ASSIGNMENT RUBRICS' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT RUBRICS CREATED',
            data: createRubrics._id,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteAssignmentRubrics = async ({ params = {} }) => {
    try {
        const { id } = params;
        const deleteRubrics = await assignmentRubricsSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            { $set: { isDeleted: true } },
        );
        if (!deleteRubrics)
            return { statusCode: 410, message: 'ERROR IN DELETING ASSIGNMENT RUBRICS' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT RUBRICS DELETED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateAssignmentRubrics = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const {
            courseId,
            programId,
            rubricScope,
            code,
            name,
            abbreviation,
            description,
            evaluateInSequence,
            isPublic,
            scoreType,
            rubrics,
            duplicateFromTheRubrics,
        } = body;
        const updateRubrics = await assignmentRubricsSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            {
                _institution_id,
                courseId,
                programId,
                rubricScope,
                code,
                name,
                abbreviation,
                description,
                evaluateInSequence,
                isPublic,
                scoreType,
                rubrics,
                duplicateFromTheRubrics,
            },
        );
        if (!updateRubrics)
            return { statusCode: 410, message: 'ERROR IN UPDATING ASSIGNMENT RUBRICS' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT RUBRICS UPDATED',
            data: { assignmentRubrics_Id: updateRubrics._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAssignmentRubrics = async ({ params = {} }) => {
    try {
        const { id } = params;
        const assignmentRubrics = await assignmentRubricsSchema
            .findById(
                { _id: convertToMongoObjectId(id) },
                {
                    _id: 1,
                    _institution_id: 1,
                    courseId: 1,
                    programId: 1,
                    rubricScope: 1,
                    code: 1,
                    name: 1,
                    abbreviation: 1,
                    description: 1,
                    evaluateInSequence: 1,
                    isPublic: 1,
                    scoreType: 1,
                    rubrics: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: { course_name: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: 'duplicateFromTheRubrics',
                select: { name: 1 },
            })
            .lean();
        if (!assignmentRubrics)
            return { statusCode: 410, message: 'ERROR IN GETTING ASSIGNMENT RUBRICS ' };
        return { statusCode: 200, message: 'ASSIGNMENT RUBRICS', data: assignmentRubrics };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listAllAssignmentRubrics = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId } = query;
        let listAssignmentRubrics = await assignmentRubricsSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _id: 1,
                    courseId: 1,
                    rubricScope: 1,
                    code: 1,
                    name: 1,
                    isPublic: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: { course_name: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: 'duplicateFromTheRubrics',
                select: { name: 1 },
            })
            .lean();
        listAssignmentRubrics = listAssignmentRubrics.reduce((hash, obj) => {
            if (obj.isPublic) {
                // eslint-disable-next-line no-unused-expressions, no-sequences
                hash.public = hash.public || [];
                hash.public.push(obj);
            } else {
                // eslint-disable-next-line no-unused-expressions, no-sequences
                (hash[obj.rubricScope] = hash[obj.rubricScope] || []),
                    hash[obj.rubricScope].push(obj);
            }
            return hash;
        }, Object.create(null));
        const assignmentRubricsCount = await assignmentRubricsSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        });
        if (!listAssignmentRubrics)
            return { statusCode: 410, message: 'ERROR IN LISTING ASSIGNMENT RUBRICS ' };
        if (courseId && listAssignmentRubrics.course_based) {
            listAssignmentRubrics.course_based = listAssignmentRubrics.course_based.filter(
                (courseElem) =>
                    courseElem.courseId &&
                    courseElem.courseId._id.toString() === courseId.toString(),
            );
        }
        return {
            statusCode: 200,
            message: 'ALL ASSIGNMENT RUBRICS',
            data: { listAssignmentRubrics, assignmentRubricsCount },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listAssignmentRubrics = async ({ query = {} }) => {
    try {
        const { rubricsIds } = query;
        const assignmentRubrics = await assignmentRubricsSchema
            .find(
                {
                    _id: rubricsIds.map((rubricsIdElement) =>
                        convertToMongoObjectId(rubricsIdElement),
                    ),
                },
                {
                    _id: 1,
                    _institution_id: 1,
                    courseId: 1,
                    programId: 1,
                    rubricScope: 1,
                    code: 1,
                    name: 1,
                    abbreviation: 1,
                    description: 1,
                    evaluateInSequence: 1,
                    isPublic: 1,
                    scoreType: 1,
                    rubrics: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: { course_name: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: 'duplicateFromTheRubrics',
                select: { name: 1 },
            })
            .lean();
        if (!assignmentRubrics)
            return { statusCode: 410, message: 'ERROR IN GETTING ASSIGNMENT RUBRICS ' };
        return { statusCode: 200, message: 'ASSIGNMENT RUBRICS', data: assignmentRubrics };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    createAssignmentRubrics,
    deleteAssignmentRubrics,
    updateAssignmentRubrics,
    getAssignmentRubrics,
    listAssignmentRubrics,
    listAllAssignmentRubrics,
};
