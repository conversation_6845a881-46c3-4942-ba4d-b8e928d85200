const { convertToMongoObjectId } = require('../../utility/common');
const formSettingCourseSchema = require('../categoryForm/formSettingCourses.model');
const qapcPermissionSchema = require('./qapcPermission.model');
const { QAPC_GROUPED_BY_CATEGORY, QAPC_GROUPED_BY_PROGRAM } = require('../../utility/constants');
const qapcRoleSchema = require('./qapcRole.model');
const formSettingSchema = require('../categoryForm/formSetting.model');
const { searchKeyFunction } = require('../../utility/common_functions');

const getFormCourseSetting = async ({ formSettingId, _institution_id }) => {
    try {
        const formCourseSettingData = await formSettingCourseSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryFormId: { $in: formSettingId },
                    isEnable: true,
                    isConfigure: true,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    categoryId: 1,
                    categoryFormId: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                },
            )
            .lean();
        return { formCourseSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateUserRolePermission = async ({ hierarchy, userPermission, _institution_id }) => {
    try {
        const qapcRoleBulkWrites = [];
        Object.keys(userPermission).forEach((userIdElement) => {
            const userPermissions = userPermission[userIdElement];
            const qapcRoleIds = Array.from(userPermissions.qapcRoleIds);
            qapcRoleBulkWrites.push({
                updateOne: {
                    filter: {
                        userId: convertToMongoObjectId(userPermissions.userId),
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    update: {
                        $set: {
                            userId: userPermissions.userId,
                        },
                        $push: {
                            [`permissions.${hierarchy}`]: userPermissions.permissions[hierarchy],
                        },
                        $addToSet: {
                            qapcRoleIds: { $each: qapcRoleIds },
                        },
                    },
                    upsert: true,
                },
            });
        });
        if (qapcRoleBulkWrites.length) {
            await qapcPermissionSchema.bulkWrite(qapcRoleBulkWrites);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCFPCPermission = async ({ _institution_id, cfpcPermissions }) => {
    try {
        const userPermission = {};
        const roleLevelBulkUpdate = [];
        cfpcPermissions.forEach((permissionElement) => {
            const userId = permissionElement.userId;

            if (!userPermission[userId]) {
                userPermission[userId] = {
                    userId,
                    qapcRoleIds: new Set(),
                    permissions: { cfpc: [] },
                };
            }
            const cfpcEntry = {
                categoryFormCourseId: permissionElement.categoryFormCourseId,
                formCourseGroupId: permissionElement.formCourseGroupId,
                selectType: permissionElement.selectType,
                selectAll: permissionElement.selectAll,
                selectedAcademic: permissionElement.selectedAcademic,
                subModuleId: permissionElement.subModuleId,
                subModuleName: permissionElement.subModuleName,
                approverLevelIndex: permissionElement.approverLevelIndex,
                actionId: permissionElement.actionId,
                qapcRoleId: permissionElement.qapcRoleId,
                roleId: permissionElement.roleId,
                assignType: permissionElement.assignType,
                levelName: permissionElement.levelName,
                academicYear: permissionElement.academicYear,
                institutionCalendarIds: permissionElement.institutionCalendarIds,
                multiUser: permissionElement.multiUser,
                uuid: permissionElement.uuid,
            };
            if (!permissionElement._id) {
                userPermission[userId].permissions.cfpc = [
                    cfpcEntry,
                    ...userPermission[userId].permissions.cfpc,
                ];
            }
            if (!userPermission[userId].qapcRoleIds.has(permissionElement.qapcRoleId)) {
                userPermission[userId].qapcRoleIds.add(permissionElement.qapcRoleId);
            }
            if (permissionElement?.qapcRoleId) {
                roleLevelBulkUpdate.push({
                    updateOne: {
                        filter: { _id: convertToMongoObjectId(permissionElement.qapcRoleId) },
                        update: {
                            $addToSet: {
                                levels: permissionElement.levelName,
                            },
                        },
                        upsert: true,
                    },
                });
            }
        });

        if (roleLevelBulkUpdate.length) {
            await qapcRoleSchema.bulkWrite(roleLevelBulkUpdate);
        }
        return updateUserRolePermission({
            hierarchy: QAPC_GROUPED_BY_CATEGORY,
            _institution_id,
            userPermission,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatePCCFPermission = async ({ _institution_id, pccfPermissions }) => {
    try {
        const qapcRoleBulkWrites = [];
        const userPermission = {};
        pccfPermissions.forEach((permissionElement) => {
            const userId = permissionElement.userId;
            if (!userPermission[userId]) {
                userPermission[userId] = {
                    userId,
                    qapcRoleIds: new Set(),
                    permissions: { pccf: [] },
                };
            }
            const pccfEntry = {
                programId: permissionElement.programId,
                curriculamId: permissionElement.curriculamId,
                year: permissionElement.year,
                courseId: permissionElement.courseId,
                categoryFormCourseId: permissionElement.categoryFormCourseId,
                formCourseGroupId: permissionElement.formCourseGroupId,
                assignedInstitutionId: permissionElement.assignedInstitutionId,
                selectType: permissionElement.selectType,
                selectAll: permissionElement.selectAll,
                selectedAcademic: permissionElement.selectedAcademic,
                subModuleId: permissionElement.subModuleId,
                subModuleName: permissionElement.subModuleName,
                approverLevelIndex: permissionElement.approverLevelIndex,
                actionId: permissionElement.actionId,
                qapcRoleId: permissionElement.qapcRoleId,
                roleId: permissionElement.roleId,
                assignType: permissionElement.assignType,
                levelName: permissionElement.levelName,
                academicYear: permissionElement.academicYear,
                institutionCalendarIds: permissionElement.institutionCalendarIds,
                multiUser: permissionElement.multiUser,
            };
            if (!permissionElement._id) {
                userPermission[userId].permissions.pccf = [
                    pccfEntry,
                    ...userPermission[userId].permissions.pccf,
                ];
            }
            if (!userPermission[userId].qapcRoleIds.has(permissionElement.qapcRoleId)) {
                userPermission[userId].qapcRoleIds.add(permissionElement.qapcRoleId);
            }
            // userPermission[userId].qapcRoleIds.add(permissionElement.qapcRoleId);
            if (permissionElement._id) {
                qapcRoleBulkWrites.push({
                    updateOne: {
                        filter: { userId },
                        update: {
                            $set: {
                                'permissions.pccf.$[pccfElement]': {
                                    categoryFormCourseId: pccfEntry.categoryFormCourseId,
                                    ...(typeof permissionElement.isDeleted === 'boolean' && {
                                        isDeleted: permissionElement.isDeleted,
                                    }),
                                },
                            },
                        },
                        arrayFilters: [
                            { 'pccfElement._id': convertToMongoObjectId(permissionElement._id) },
                        ],
                        upsert: false,
                    },
                });
            }
        });
        return updateUserRolePermission({
            hierarchy: QAPC_GROUPED_BY_PROGRAM,
            _institution_id,
            qapcRoleBulkWrites,
            userPermission,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPermissionData = async ({ qapcRoleId, levelName, hierarchy, _institution_id }) => {
    try {
        const permissionQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            qapcRoleIds: { $in: [convertToMongoObjectId(qapcRoleId)] },
            [`permissions.${hierarchy}.qapcRoleId`]: convertToMongoObjectId(qapcRoleId),
            [`permissions.${hierarchy}.isDeleted`]: false,
        };
        const permissionProjection = {
            [`permissions.${hierarchy}`]: 1,
            userId: 1,
        };
        const rolePermissionData = await qapcPermissionSchema
            .find(permissionQuery, permissionProjection)
            .populate({ path: 'userId', select: { name: 1 } })
            .lean();
        if (!rolePermissionData.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        }
        const permissionUserList = [];
        rolePermissionData.forEach((permissionElement) => {
            const userId = permissionElement.userId._id;
            const userName = permissionElement.userId.name;
            for (const hierarchyElement of permissionElement.permissions[`${hierarchy}`]) {
                if (
                    !hierarchyElement.isDeleted &&
                    hierarchyElement.qapcRoleId &&
                    hierarchyElement.qapcRoleId.toString() === qapcRoleId.toString() &&
                    levelName.toString() === hierarchyElement.levelName.toString()
                ) {
                    permissionUserList.push({ userId, userName, ...hierarchyElement });
                }
            }
        });
        return { statusCode: 200, message: 'List Data', data: permissionUserList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const pullCFPCPermission = async ({ uniqueId }) => {
    try {
        const qapcRoleBulkWrites = uniqueId.map((uuidElement) => {
            const qapcRoleId = uuidElement.split('-')[0];
            return {
                updateMany: {
                    filter: {
                        ...(qapcRoleId && {
                            qapcRoleIds: convertToMongoObjectId(qapcRoleId),
                            'permissions.cfpc.qapcRoleId': convertToMongoObjectId(qapcRoleId),
                        }),
                    },
                    update: {
                        $pull: {
                            'permissions.cfpc': {
                                uuid: uuidElement,
                            },
                        },
                    },
                },
            };
        });
        if (qapcRoleBulkWrites.length) {
            await qapcPermissionSchema.bulkWrite(qapcRoleBulkWrites);
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//level wise category list
const getGroupedCategoryForms = async ({ _institution_id, level }) => {
    const categoryLevelWiseData = await formSettingSchema
        .find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                'categorySettings.level': searchKeyFunction({ searchKey: level }),
                isActive: true,
                isDeleted: false,
            },
            {
                _id: 1,
                formName: 1,
                categoryId: 1,
                selectedProgram: 1,
                selectedInstitution: 1,
            },
        )
        .populate({ path: 'categoryId', select: { categoryName: 1 } })
        .lean();

    return {
        statusCode: 200,
        message: 'DATA_RETRIEVED',
        data: categoryLevelWiseData,
    };
};

const getGroupedProgramForms = async ({ _institution_id, level }) => {
    const categoryLevelWiseData = await formSettingSchema
        .find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                'categorySettings.level': searchKeyFunction({ searchKey: level }),
                isActive: true,
                isDeleted: false,
            },
            {
                _id: 1,
                formName: 1,
                categoryId: 1,
                selectedProgram: 1,
                selectedInstitution: 1,
            },
        )
        .populate({ path: 'categoryId', select: { categoryName: 1 } })
        .lean();

    return {
        statusCode: 200,
        message: 'DATA_RETRIEVED',
        data: categoryLevelWiseData,
    };
};

module.exports = {
    getFormCourseSetting,
    updateCFPCPermission,
    updatePCCFPermission,
    updateUserRolePermission,
    getPermissionData,
    pullCFPCPermission,
    getGroupedCategoryForms,
    getGroupedProgramForms,
};
