const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const createVariableSchema = Joi.object({
    query: Joi.object({
        programId: objectId.optional(),
        courseId: objectId.optional(),
        institutionCalendarId: objectId.optional(),
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().required(),
    }),
}).unknown(true);

const getVariableSchema = Joi.object({
    query: Joi.object({
        programId: objectId.optional(),
        courseId: objectId.optional(),
        institutionCalendarId: objectId.optional(),
        type: Joi.string().optional(),
    }),
}).unknown(true);

const updateVariableSchema = Joi.object({
    query: Joi.object({
        variableId: objectId.required(),
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().required(),
    }),
}).unknown(true);

const deleteVariableSchema = Joi.object({
    query: Joi.object({
        variableId: objectId.required(),
    }),
}).unknown(true);

module.exports = {
    createVariableSchema,
    getVariableSchema,
    updateVariableSchema,
    deleteVariableSchema,
};
