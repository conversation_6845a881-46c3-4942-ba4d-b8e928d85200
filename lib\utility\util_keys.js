require('dotenv').config();
const logger = require('./pino.config');
module.exports = {
    APP_STATE: process.env.NODE_ENV || 'development',
    DEPLOY_TO: process.env.DEPLOY_TO || 'others',
    INSTITUTION_NAME: process.env.INSTITUTION_NAME || '',
    SAAS_ENABLE: process.env.SAAS_ENABLE || false,
    V2_ENABLE: process.env.V2_ENABLE || false,
    ELASTIC_ENABLE: process.env.ELASTIC_ENABLE || false,
    AWS_PINPOINT_ENABLE: process.env.AWS_PINPOINT_ENABLE || false,
    OUR_SMS_ENABLE: process.env.OUR_SMS_ENABLE || false,

    SERVICES: {
        OUTSIDE_CAMPUS_V2: process.env.OUTSIDE_CAMPUS_V2 || 'false',
        SCHEDULED_V2: process.env.SCHEDULED_V2 || 'false',
        OUTSIDE_CAMPUS: process.env.OUTSIDE_CAMPUS || 'false',
        LEADER_BOARD: process.env.LEADER_BOARD || 'false',
        Q360: process.env.Q360 || 'false',
        LATE_CONFIGURATION: process.env.LATE_CONFIGURATION || 'false',
        DISCUSSION_FORUM: process.env.DISCUSSION_FORUM || 'false',
        BLE_RANGE: process.env.BLE_RANGE || 'false',
        MANUAL_ATTENDANCE: process.env.MANUAL_ATTENDANCE || 'false',
        PARENT_APP: process.env.PARENT_APP || 'false',
        LMS_VERSION: process.env.LMS_VERSION || 'V1',
        FACE_AUTH_TYPE: process.env.FACE_AUTH_TYPE || 'mobile',
        FACE_VERIFY_MODE: process.env.FACE_VERIFY_MODE || 'online',
        LABEL_CHANGE: process.env.LABEL_CHANGE || 'true',
        CHAT_ENABLED: process.env.CHAT_ENABLED || 'false',
        ACTIVITY_ENABLED: process.env.ACTIVITY_ENABLED || 'true',
        GENDER_MERGE: process.env.GENDER_MERGE || 'true',
        MOBILE: process.env.MOBILE || 'false',
        DOCUMENT: process.env.DOCUMENT || 'false',
        ANNOUNCEMENT: process.env.ANNOUNCEMENT || 'false',
        ANNOUNCEMENT_V2: process.env.ANNOUNCEMENT_V2 || 'false',
        ATTAINMENT_MODULE: process.env.ATTAINMENT_MODULE || 'false',
        ASSIGNMENT_MODULE: process.env.ASSIGNMENT_MODULE || 'false',
        ASSESSMENT_MODULE: process.env.ASSESSMENT_MODULE || 'false',
        TRANSLATION: process.env.TRANSLATION || 'false',
        TIME_STAMP: process.env.TIME_STAMP || 'false',
        V2_ENABLE: process.env.V2_ENABLE || 'false',
        USER_SENSITIVE: process.env.USER_SENSITIVE || 'false',
        DOCUMENT_SEC_NEED: process.env.DOCUMENT_SEC_NEED || 'false',
        NATIONALITY_ID: process.env.NATIONALITY_ID || 'false',
        USER_DOB: process.env.USER_DOB || 'false',
        MULTI_CALENDAR: process.env.MULTI_CALENDAR || 'false',
        STAFF_STUDENT_MANUAL_ATTENDANCE: process.env.STAFF_STUDENT_MANUAL_ATTENDANCE || 'false',
        FACE_ANOMALY: process.env.FACE_ANOMALY || 'false',
        SURVEY_OPEN_ENDED: process.env.SURVEY_OPEN_ENDED || 'false',
        // TIME_STAMP: process.env.TIME_STAMP || 'false',
        LOCAL_FACE_TEST: process.env.LOCAL_FACE_TEST || 'false',
        REACT_APP_ENVIRONMENT: process.env.REACT_APP_ENVIRONMENT || 'ecs-indian',
        REACT_APP_COLLEGE_NAME: process.env.REACT_APP_COLLEGE_NAME || 'Digival Institute',
        REACT_APP_COUNTRY_CODE: process.env.REACT_APP_COUNTRY_CODE || '91',
        REACT_APP_COUNTRY_CODE_LENGTH: process.env.REACT_APP_COUNTRY_CODE_LENGTH || '10',
        REACT_APP_ROTATION_LEVEL: process.env.REACT_APP_ROTATION_LEVEL || '[9,10]',
        REACT_APP_ACTIVE_VERSION: process.env.REACT_APP_ACTIVE_VERSION || 'v1',
        REACT_APP_INSTITUTION_ID:
            process.env.REACT_APP_INSTITUTION_ID || '5e5d0f1a15b4d600173d5692',
        FACE_OFF: process.env.FACE_OFF || 'false',
        REACT_APP_PARENT_DETAILS_MANDATORY:
            process.env.REACT_APP_PARENT_DETAILS_MANDATORY || 'false',
        DC_ADMIN_URL: process.env.DC_ADMIN_URL || 'http://localhost:3000',
        DC_URL: process.env.DC_URL || 'http://localhost:3001',
        DA_URL: process.env.DA_URL || 'http://localhost:3002',
        DA_URL_1: process.env.DA_URL_1 || '',
        // SURVEY_OPEN_ENDED: process.env.SURVEY_OPEN_ENDED || 'false',
        MISSED_TO_COMPLETE: process.env.MISSED_TO_COMPLETE || 'false',
        ANOMALY_RESTRICT: process.env.ANOMALY_RESTRICT || 'false',
        ANOMALY_RESTRICT_MESSAGE:
            process.env.ANOMALY_RESTRICT_MESSAGE ||
            'تم اكتشاف محاولة التجاوز على بصمة الوجه، تم تعليق حسابك مؤقتا، (من غش فليس منا) يرجى الاتصال بالإدارة للمساعدة. Breach detected in face recognition; P.U.R.E. activated. Your account is temporarily suspended. Contact the Administration for assistance.',
        MERGE_COURSE_SUBJECT: process.env.MERGE_COURSE_SUBJECT || 'false',
        ASSIGNMENT_MOBILE: process.env.ASSIGNMENT_MOBILE || 'false',
        SIS_SYNC: process.env.SIS_SYNC || 'false',
        TARDIS_MODULE: process.env.TARDIS_ENABLE || 'false',
        GENDER_MIXED: process.env.GENDER_MIXED || 'false',
        MULTI_DOCUMENT: process.env.MULTI_DOCUMENT || 'false',
        NATIVE_FACIAL: process.env.NATIVE_FACIAL || 'false',
        // DISCUSSION_FORUM: process.env.DISCUSSION_FORUM || 'false',
        ATTENDANCE_OR_CONDITION: process.env.ATTENDANCE_OR_CONDITION || 'false',
        DEFAULT_ATTENDANCE_CONDITION: process.env.DEFAULT_ATTENDANCE_CONDITION || 'and',
        LATE_ABSENT_CONFIGURATION: process.env.LATE_ABSENT_CONFIGURATION || 'false',
        LATE_CONFIGURATION_MANUAL_ENABLED: process.env.LATE_CONFIGURATION_MANUAL_ENABLED || 'false',
        DC_MISSED_TO_COMPLETE: process.env.DC_MISSED_TO_COMPLETE || 'false',
        COURSE_RESTRICTION: process.env.COURSE_RESTRICTION || 'false',
        HEBA_ENGAGER: process.env.HEBA_ENGAGER || 'false',
        DIGI_SURVEY: process.env.DIGI_SURVEY || 'false',
        DIGI_CHAT: process.env.DIGI_CHAT || 'false',
        FACE_RE_REGISTER: process.env.FACE_RE_REGISTER || 'false',
        DC_STAFF_OFFLINE: process.env.DC_STAFF_OFFLINE || 'false',
        ONSITE_WEB_START: process.env.ONSITE_WEB_START || 'false',
        SAAM_MOBILE_APPLY: process.env.SAAM_MOBILE_APPLY || 'false',
        STUDENT_FACE_REGISTER: process.env.STUDENT_FACE_REGISTER || 'false',
        MULTI_SCHEDULE_START: process.env.MULTI_SCHEDULE_START || 'false',
        SELF_REGISTRATION_PROGRAM: process.env.SELF_REGISTRATION_PROGRAM || 'false',
        DOCUMENT_MANAGEMENT: process.env.DOCUMENT_MANAGEMENT || 'false',
        ACTIVITY_MANAGEMENT: process.env.ACTIVITY_MANAGEMENT || 'false',
        ENCRYPT_PAYLOAD: process.env.ENCRYPT_PAYLOAD || '',
        QUIZ_OUTCOME_MAPPING: process.env.QUIZ_OUTCOME_MAPPING || 'false',
        GROUP_BASED_DELIVERY_REPORT: process.env.GROUP_BASED_DELIVERY_REPORT || 'false',
    },

    SLACK_CHANNEL: process.env.SLACK_CHANNEL || 'ds-v2-email',

    AWS_BUCKET_NAME: process.env.AWS_BUCKET_NAME,
    AWS_ACCESS_KEY: process.env.AWS_ACCESS_KEY,
    AWS_SECRET_KEY: process.env.AWS_SECRET_KEY,
    AWS_REGION: process.env.AWS_REGION,
    AWS_APP_ID: process.env.AWS_APP_ID,
    AWS_SENDER_ID: process.env.AWS_SENDER_ID,
    AWS_CHARSET: process.env.AWS_CHARSET,
    SENDER_ADDRESS: process.env.SENDER_ADDRESS,
    BUCKET_NAME_DOC: process.env.AWS_BUCKET_NAME_DOC,
    BUCKET_NAME_USER_DATA: process.env.AWS_BUCKET_NAME_USER_DATA,
    BUCKET_DOCUMENT: process.env.AWS_BUCKET_DOCUMENT,
    //outside campus
    BUCKET_OUTSIDE_CAMPUS: process.env.AWS_BUCKET_OUTSIDE_CAMPUS,
    BUCKET_SESSION_ORDER_DOCUMENTS: process.env.AWS_BUCKET_SESSION_ORDER_DOCUMENTS,
    //Q360
    BUCKET_QAPC_DOCUMENT: process.env.AWS_BUCKET_QAPC_DOCUMENT,
    //OUR-SMS Saudi SMS service
    SMS_USERNAME: process.env.SMS_USERNAME,
    SMS_PASSWORD: process.env.SMS_PASSWORD,
    SMS_SENDER: process.env.SMS_SENDER,
    SMS_RETURN: process.env.SMS_RETURN,

    //OUR-SMS Saudi SMS service
    OURSMS_BASE_URL: process.env.OURSMS_BASE_URL,
    OURSMS_TOKEN: process.env.OURSMS_TOKEN,
    OURSMS_SENDER: process.env.OURSMS_SENDER,
    OURSMS_MSG_CLASS: process.env.OURSMS_MSG_CLASS,

    TWILIO_ENABLE: process.env.TWILIO_ENABLE || 'false',
    TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
    TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
    TWILIO_FROM_NO: process.env.TWILIO_FROM_NO,

    JWT_SECRET_KEY: process.env.JWT_SECRET_KEY || 'digischedulerfrZmDcOIT8', // JWT secret key
    apiKey: process.env.API_KEY || 'fHUuioTyELUantbGA7O4qP7NkY6JMdE8', // api key

    RANDOM_PASSWORD_LENGTH: process.env.RANDOM_PASSWORD_LENGTH || 8,

    OTP_EXPIRY_DURATION_IN_SECS: process.env.OTP_EXPIRY_DURATION_IN_SECS || 300,

    EMAIL_SENDER_ADDRESS:
        process.env.EMAIL_SENDER_ADDRESS || '"DigiScheduler "<<EMAIL>>',

    // Slack Chat Service Key - OTP Service.
    SLACK_AUTH_TOKEN:
        '*******************************************************************************',

    MONGO_DB: process.env.dbUrl,
    MONGODB_CLOUD_SERVICE: process.env.MONGODB_CLOUD_SERVICE || 'atlas',
    MONGODB_SSL_CA: process.env.MONGODB_SSL_CA,
    TLS_CA_FILE: process.env.TLS_CA_FILE,
    TLS_CERTIFICATE_KEY_FILE: process.env.TLS_CERTIFICATE_KEY_FILE,

    FE_URL: process.env.FE_URL || 'https://staging.digischeduler.digivalsolutions.com',
    SIGN_UP_URL: process.env.SIGN_UP_URL,
    STAFF_SIGN_UP_URL: process.env.STAFF_SIGN_UP_URL,
    logger,
    LOCAL_TIMEZONE: 'Asia/Calcutta',
    TIMEZONE: process.env.TIMEZONE || 'Asia/Riyadh',
    FCM_SECRET_KEY: process.env.FCM_SECRET_KEY,
    CRYPTO_KEY: process.env.CRYPTO_KEY || '',
    FE_CRYPTO_KEY: process.env.FE_CRYPTO_KEY || '',
    DSCRONKEY: process.env.DSCRONKEY || '',
    DIGIVAL_CLOUD_PROVIDER: process.env.DIGIVAL_CLOUD_PROVIDER || 'AWS',
    OCI_REGION: process.env.OCI_REGION || '',
    OCI_AWS_S3_API: process.env.OCI_AWS_S3_API || '',
    OCI_ACCESS_KEY_ID: process.env.OCI_ACCESS_KEY_ID || '',
    OCI_SECRET_ACCESS_KEY: process.env.OCI_SECRET_ACCESS_KEY || '',
    OCI_NAMESPACE: process.env.OCI_NAMESPACE || '',
    OCI_USER: process.env.OCI_USER || '',
    OCI_FINGERPRINT: process.env.OCI_FINGERPRINT || '',
    OCI_TENANCY: process.env.OCI_TENANCY || '',
    OCI_KEY: process.env.OCI_KEY || '',
    OCI_PAR_EXPIRE: process.env.OCI_PAR_EXPIRE || 60000,
    LMS_VERSION: process.env.LMS_VERSION || 'V1',
    SAAM_APPROVED_PRESENT: process.env.SAAM_APPROVED_PRESENT || 'false',
    SCHEDULE_SESSION_BASED_REPORT: process.env.SCHEDULE_SESSION_BASED_REPORT || 'false',
    AUTH_PASSWORD_SYNC: process.env.AUTH_PASSWORD_SYNC || 'false',
    IN_HOUSE_NOTIFICATION_SERVICE: process.env.IN_HOUSE_NOTIFICATION_SERVICE || 'google',
    GOOGLE_CHAT_WEBHOOK: process.env.GOOGLE_CHAT_WEBHOOK || 'http://',
    USER_PERMISSION_MODULE_NAMES: {
        SURVEY: 'Survey Management',
        ANNOUNCEMENT_MANAGEMENT: 'Announcement Management',
    },
    SOCKET_SERVER_URL: process.env.SOCKET_SERVER_URL,
    ATTENDANCE_END_BASED_SCHEDULE: process.env.ATTENDANCE_END_BASED_SCHEDULE || 'false',

    // Firebase Push Service
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID || '',
    FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY || '',
    FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL || '',
    DIGI_ASSESS_BASE_URL: process.env.DIGI_ASSESS_BASE_URL || '',
    DIGI_ASSESS_API_KEY: process.env.DIGI_ASSESS_API_KEY || '',
    AUTH_SESSION: process.env.AUTH_SESSION || 'false',
    CORS_WHITELIST_ENABLED: process.env.CORS_WHITELIST_ENABLED || false,
    WHITELIST_IPS:
        process.env?.WHITELIST_IPS?.trim() !== '' ? process.env?.WHITELIST_IPS?.split(',') : [],
    WHITELIST_DOMAINS:
        process.env?.WHITELIST_DOMAINS?.trim() !== ''
            ? process.env?.WHITELIST_DOMAINS?.split(',')
            : [],
    RATE_LIMIT_ENABLED: process.env.RATE_LIMIT_ENABLED || false,
    RATE_LIMIT_CONFIG: {
        RATE_LIMIT_LOGIN_POINTS: process.env.RATE_LIMIT_LOGIN_POINTS || 5,
        RATE_LIMIT_LOGIN_DURATION: process.env.RATE_LIMIT_LOGIN_DURATION || 15 * 60,
        RATE_LIMIT_LOGIN_BLOCK_DURATION: process.env.RATE_LIMIT_LOGIN_BLOCK_DURATION || 15 * 60,

        RATE_LIMIT_FORGOT_PASSWORD_POINTS: process.env.RATE_LIMIT_FORGOT_PASSWORD_POINTS || 5,
        RATE_LIMIT_FORGOT_PASSWORD_DURATION:
            process.env.RATE_LIMIT_FORGOT_PASSWORD_DURATION || 15 * 60,
        RATE_LIMIT_FORGOT_PASSWORD_BLOCK_DURATION:
            process.env.RATE_LIMIT_FORGOT_PASSWORD_BLOCK_DURATION || 15 * 60,

        RATE_LIMIT_SIGNUP_POINTS: process.env.RATE_LIMIT_SIGNUP_POINTS || 5,
        RATE_LIMIT_SIGNUP_DURATION: process.env.RATE_LIMIT_SIGNUP_DURATION || 15 * 60,
        RATE_LIMIT_SIGNUP_BLOCK_DURATION: process.env.RATE_LIMIT_SIGNUP_BLOCK_DURATION || 15 * 60,

        RATE_LIMIT_DEFAULT_ROUTES_POINTS: process.env.RATE_LIMIT_DEFAULT_ROUTES_POINTS || 10,
        RATE_LIMIT_DEFAULT_ROUTES_DURATION: process.env.RATE_LIMIT_DEFAULT_ROUTES_DURATION || 1,
        RATE_LIMIT_DEFAULT_ROUTES_BLOCK_DURATION:
            process.env.RATE_LIMIT_DEFAULT_ROUTES_BLOCK_DURATION || 60 * 60,

        RATE_LIMIT_IP_ADDRESS_POINTS: process.env.RATE_LIMIT_IP_ADDRESS_POINTS || 10,
        RATE_LIMIT_IP_ADDRESS_DURATION: process.env.RATE_LIMIT_IP_ADDRESS_DURATION || 1,
        RATE_LIMIT_IP_ADDRESS_BLOCK_DURATION:
            process.env.RATE_LIMIT_IP_ADDRESS_BLOCK_DURATION || 60 * 60,
    },
    //Course Handout
    BUCKET_HANDOUT_DOCUMENT: process.env.AWS_BUCKET_HANDOUT_DOCUMENT || '',
    HANDOUT_COLOR_ONE: process.env.HANDOUT_COLOR_ONE || '',
    HANDOUT_COLOR_TWO: process.env.HANDOUT_COLOR_TWO || '',
    HANDOUT_COLOR_THREE: process.env.HANDOUT_COLOR_THREE || '',
    HANDOUT_COLOR_FOUR: process.env.HANDOUT_COLOR_FOUR || '',
    MAX_FILE_SIZE: process.env.MAX_FILE_SIZE || 50 * 1024 * 1024,
    USER_ACTIVITY_LOG: process.env.USER_ACTIVITY_LOG === 'true' || false,
};
