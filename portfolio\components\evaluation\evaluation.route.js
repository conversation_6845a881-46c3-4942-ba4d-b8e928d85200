const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const EvaluationController = require('./evaluation.controller');
const {
    assignFacultySchema,
    getStudentSchema,
    getEvaluatorListSchema,
    getResponseSchema,
    updateMarksSchema,
    getComponentsSchema,
    rejectSubmissionSchema,
    completeEvaluationSchema,
    getRubricsSchema,
    getEvaluatorListForAdminSchema,
    getInsightsSchema,
    getEvaluationCountSchema,
} = require('./evaluation.validation');

// PUT /assign
router.put(
    '/assign',
    validate(assignFacultySchema),
    catchAsync(EvaluationController.assignFacultyForEvaluation),
);

// GET /student
router.get(
    '/student',
    validate(getStudentSchema),
    catchAsync(EvaluationController.getStudentForEvaluator),
);

// GET /evaluator
router.get(
    '/evaluator',
    validate(getEvaluatorListSchema),
    catchAsync(EvaluationController.getEvaluatorListByCourse),
);

// GET /response
router.get(
    '/response',
    validate(getResponseSchema),
    catchAsync(EvaluationController.getStudentResponseForEvaluator),
);

// PUT /mark
router.put(
    '/mark',
    validate(updateMarksSchema),
    catchAsync(EvaluationController.updateStudentMarks),
);

router.get(
    '/component',
    validate(getComponentsSchema),
    catchAsync(EvaluationController.getComponentsByEvaluator),
);

router.put(
    '/reject',
    validate(rejectSubmissionSchema),
    catchAsync(EvaluationController.rejectStudentSubmission),
);

router.put(
    '/complete',
    validate(completeEvaluationSchema),
    catchAsync(EvaluationController.completeEvaluation),
);

router.get(
    '/rubric',
    validate(getRubricsSchema),
    catchAsync(EvaluationController.getRubricsOrMarksForEvaluation),
);

router.get(
    '/evaluator-list',
    validate(getEvaluatorListForAdminSchema),
    catchAsync(EvaluationController.getEvaluatorsForCourseAdmin),
);

router.get(
    '/insights',
    validate(getInsightsSchema),
    catchAsync(EvaluationController.getPortfolioInsights),
);

router.get(
    '/count',
    validate(getEvaluationCountSchema),
    catchAsync(EvaluationController.getEvaluationCount),
);

module.exports = router;
