const constant = require('../utility/constants');
const institution = require('mongoose').model(constant.INSTITUTION);
const country = require('mongoose').model(constant.COUNTRY);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const institution_formate = require('./institution_formate');
const ObjectId = common_files.convertToMongoObjectId;

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.COUNTRY,
                localField: '_country_id',
                foreignField: '_id',
                as: 'country',
            },
        },
        { $unwind: '$country' },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(institution, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(
            res,
            200,
            true,
            'institution list',
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            /* doc.data */ institution_formate.institution(doc.data),
        );
        // common_files.list_all_response(res, 200, true, "institution list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* institution_formate.institution(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.COUNTRY,
                localField: '_country_id',
                foreignField: '_id',
                as: 'country',
            },
        },
        { $unwind: '$country' },
    ];
    const doc = await base_control.get_aggregate(institution, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'institution details',
            /* doc.data */ institution_formate.institution_ID(doc.data[0]),
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.insert = async (req, res) => {
    const checks = await base_control.check_id(country, {
        _id: { $in: req.body._country_id },
        isDeleted: false,
    });
    if (checks.status) {
        const query = { name: req.body.name, code: req.body.code, isDeleted: false };
        const docs = await base_control.get(institution, query, {});
        if (!docs.status) {
            const doc = await base_control.insert(institution, req.body);
            if (doc.status) {
                common_files.com_response(
                    res,
                    201,
                    true,
                    'institution Added successfully',
                    doc.data,
                );
            } else {
                common_files.com_response(res, 500, false, 'Error', doc.data);
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                'Error duplicate values found ',
                'This content already present in DB',
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error id not match',
            'Check Parshing reference ID',
        );
    }
};

exports.update = async (req, res) => {
    let checks = { status: true };
    if (req.body._country_id != undefined) {
        checks = await base_control.check_id(country, {
            _id: { $in: req.body._country_id },
            isDeleted: false,
        });
    }
    if (checks.status) {
        const object_id = req.params.id;
        const doc = await base_control.update(institution, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, 'institution update successfully', doc.data);
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error id not match',
            'Check Parshing reference ID',
        );
    }
};

exports.delete = async (req, res) => {
    const object_id = req.params.id;

    //Before delete it has to check linked collections
    // let aggre=[];

    const doc = await base_control.delete(institution, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, 'institution deleted successfully', doc.data);
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }

        const doc = await base_control.get_list(institution, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                'institution List',
                institution_formate.institution_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error parse field in body',
            'Error parse field in body',
        );
    }
};
