const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;
const {
    EXTERNAL_STAFF,
    DIGI_PROGRAM,
    DIGI_COURSE,
    INSTITUTION_CALENDAR,
    INFRASTRUCTURE_MANAGEMENT,
} = require('../utility/constants');
const externalStaffSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        name: { type: String },
        email: { type: String },
        mobileNo: { type: Number },
        _infra_id: {
            type: Schema.Types.ObjectId,
            ref: INFRASTRUCTURE_MANAGEMENT,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(EXTERNAL_STAFF, externalStaffSchema);
