const route = require('express').Router();
const {
    listInstitutes,
    getInstitutionById,
    createInstitute,
    updateInstitute,
    updateInstituteLogo,
    getInstituteLogo,
    addOrEditInstituteDescription,
    addInstitutePortfolio,
    editInstitutePortfolio,
    deleteInstitutePortfolioMedia,
    deleteInstitutePortfolio,
    deleteInstitute,
    archieveInstitute,
    unArchieveInstitute,
    removeInstituteDescription,
    getTimezones,
    addOrEditAccreditation,
} = require('./institution.controller');
const {
    addInstitutionValidator,
    updateInstitutionValidator,
    institutionIdvalidator,
    addOrEditInstituteDescriptionValidator,
    addInstitutePortfolioValidator,
    editInstitutePorfolioValidator,
    deleteInstitutePortfolioValidator,
    listInstitutesValidator,
    updateInstituteLogoValidator,
    addOrEditInstituteAccreditationValidator,
} = require('./institution.validator');
const catchAsync = require('../../utility/catch-async');
const { validate } = require('../../utility/input-validation');
const { uploadInstituteLogo, uploadInstituteMediaFile } = require('./institution.util');

route.get('/institute-logo/:id', validate(institutionIdvalidator), catchAsync(getInstituteLogo));
route.get('/institute/:id', validate(institutionIdvalidator), catchAsync(getInstitutionById));
route.get('/institutes/:id', validate(listInstitutesValidator), catchAsync(listInstitutes));
route.post(
    '/create-institute',
    uploadInstituteLogo,
    validate(addInstitutionValidator),
    catchAsync(createInstitute),
);
route.patch(
    '/institute-description/:id',
    uploadInstituteMediaFile,
    validate(addOrEditInstituteDescriptionValidator),
    catchAsync(addOrEditInstituteDescription),
);
route.patch(
    '/institute-accreditation/:id',
    validate(addOrEditInstituteAccreditationValidator),
    catchAsync(addOrEditAccreditation),
);

route.patch(
    '/institute-add-portfolio/:id',
    uploadInstituteMediaFile,
    validate(addInstitutePortfolioValidator),
    catchAsync(addInstitutePortfolio),
);

route.patch(
    '/institute-edit-portfolio/:id',
    uploadInstituteMediaFile,
    validate(editInstitutePorfolioValidator),
    catchAsync(editInstitutePortfolio),
);

route.patch('/remove-portfolio-media/:id', catchAsync(deleteInstitutePortfolioMedia));

route.delete(
    '/remove-description/:id',
    validate(institutionIdvalidator),
    catchAsync(removeInstituteDescription),
);

route.delete(
    '/remove-portfolio/:id',
    validate(deleteInstitutePortfolioValidator),
    catchAsync(deleteInstitutePortfolio),
);

route.patch(
    '/institute-logo/:id',
    uploadInstituteLogo,
    validate(updateInstituteLogoValidator),
    catchAsync(updateInstituteLogo),
);

route.put(
    '/update-institute/:id',
    uploadInstituteLogo,
    validate(updateInstitutionValidator),
    catchAsync(updateInstitute),
);
route.patch('/delete-institute/:id', validate(institutionIdvalidator), catchAsync(deleteInstitute));
route.patch(
    '/archieve-institute/:id',
    validate(institutionIdvalidator),
    catchAsync(archieveInstitute),
);
route.patch(
    '/restore-institute/:id',
    validate(institutionIdvalidator),
    catchAsync(unArchieveInstitute),
);
route.get('/timezones', catchAsync(getTimezones));

module.exports = route;
