const Joi = require('joi');
const { com_response } = require('../../utility/common');

validateGetCourseAct = (req, res, next) => {
    const schema = Joi.object().keys({
        query: {
            _course_id: Joi.string()
                .length(24)
                .required()
                .error(() => {
                    return req.t('COURSEID_REQUIRED');
                }),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateCreateCourseChannels = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: {
                institutionCalendarId: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('institutionCalendarId');
                    }),
                userId: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
                type: Joi.string()
                    .valid('staff', 'student')
                    .required()
                    .error(() => {
                        return req.t('STUDENT_STAFF_TYPE_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateIoUserRegistor = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: {
                userId: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
                role: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('ROLE_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateIoChannelCreate = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: {
                id: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('ID_REQUIRED');
                    }),
                created_by_id: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('CREATED_BY_ID_REQUIRED');
                    }),
                type: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('TYPE_REQUIRED');
                    }),
                channelName: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('CHANNEL_NAME_REQUIRED');
                    }),
                imageUrl: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('IMAGE_URL_REQUIRED');
                    }),
                membersId: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('MEMBERS_ID_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateGetIoChannels = (req, res, next) => {
    const schema = Joi.object().keys({
        query: {
            userId: Joi.string()
                .length(24)
                .required()
                .error(() => {
                    return req.t('USER_ID_REQUIRED');
                }),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateDeleteChannels = (req, res, next) => {
    const schema = Joi.object().keys({
        query: {
            cid: Joi.string()
                .required()
                .error(() => {
                    return req.t('CHANNEL_ID_REQUIRED');
                }),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateGetCourseDoc = (req, res, next) => {
    const schema = Joi.object().keys({
        query: {
            _course_id: Joi.string()
                .length(24)
                .required()
                .error(() => {
                    return req.t('COURSEID_REQUIRED');
                }),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
validateGetSubjects = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: {
                _institution_calendar_id: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('INSTITUTION_CALENDAR_ID');
                    }),
                _course_id: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('COURSEID_REQUIRED');
                    }),
                year_no: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('YEAR_NO');
                    }),
                level_no: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('LEVEL_NO');
                    }),
                term: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('TERM_NO');
                    }),
                _program_id: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('PROGRAM_ID_REQUIRED');
                    }),
                group_id: Joi.string().length(24).required(),
                rotation: Joi.string()
                    .required()
                    .error(() => {
                        return req.t('ROTATION_REQUIRED');
                    }),
                rotation_count: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_COUNT_REQUIRED');
                    }),
                staffs: Joi.array()
                    .required()
                    .error(() => {
                        return req.t('STAFFS_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateRemoveUserIoToken = (req, res, next) => {
    const schema = Joi.object().keys({
        body: {
            userId: Joi.string()
                .length(24)
                .required()
                .error(() => {
                    return req.t('USERID_REQUIRED');
                }),
            isAll: Joi.string().required(),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

validateBlockUser = (req, res, next) => {
    const schema = Joi.object().keys({
        body: {
            memberId: Joi.string().length(24).required(),
            channelId: Joi.string()
                .required()
                .error(() => {
                    return req.t('CHANNEL_ID_REQUIRED');
                }),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

module.exports = {
    validateGetCourseDoc,
    validateDeleteChannels,
    validateGetIoChannels,
    validateIoChannelCreate,
    validateIoUserRegistor,
    validateGetCourseAct,
    validateCreateCourseChannels,
    validateGetSubjects,
    validateRemoveUserIoToken,
    validateBlockUser,
};
