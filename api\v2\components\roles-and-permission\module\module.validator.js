const Joi = require('joi');

const roleValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                staff_type: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return error;
                    }),
                position_type: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return error;
                    }),
                role: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const roleIdValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

module.exports = {
    roleIdValidator,
    roleValidator,
};
