let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let infrastructure_delivery_medium = new Schema ({
    name: {
        type: String,
        required: true
    },
    remote: {
        type: Boolean,
        default: false
    },
    onsite: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true});


module.exports = mongoose.model(constant.INFRASTRUCTURE_DELIVERY_MEDIUM,infrastructure_delivery_medium);