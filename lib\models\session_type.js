let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let session_type = new Schema({
    session_type: {
        type: String,
        required: true
    },
    delivery_type: {
        type: String,
        required: true
    },
    delivery_mode: {
        type: String,
        required: true
    },
    delivery_symbol: {
        type: String,
        required: true
    },
    _credit_calc_id: {
        type: Schema.Types.ObjectId,
        ref: constant.CREDIT_HOURS_CALC,
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.SESSION_TYPE, session_type);