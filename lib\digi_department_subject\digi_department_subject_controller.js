const constant = require('../utility/constants');

// var institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const department_subject = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const digi_course = require('mongoose').model(constant.DIGI_COURSE);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const {
    clearItem,
    allDepartmentSubjectList,
    allCourseList,
} = require('../../service/cache.service');

// Updating Department Subject Flat Caching Data
const updateDepartmentSubjectFlatCacheData = async () => {
    clearItem('allDepartmentSubjects');
    await allDepartmentSubjectList();
};

// Updating Course Flat Caching Data
const updateCourseFlatCacheData = async () => {
    clearItem('allCourses');
    await allCourseList();
};

/**
 * Adding Department
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Add Department>}
 */
async function insert(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        if (req.body.department_name && req.body.department_name.length != 0) {
            const department_name = [
                req.body.department_name,
                req.body.department_name.charAt(0).toUpperCase() +
                    req.body.department_name.slice(1),
                req.body.department_name.toUpperCase(),
                req.body.department_name.toLowerCase(),
            ];
            const course_data = await base_control.get(
                department_subject,
                {
                    department_name: { $in: department_name },
                    _institution_id: ObjectId(req.headers._institution_id),
                    program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            if (course_data.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            true,
                            req.t('DUPLICATE_DEPARTMENT_NAME'),
                            req.t('DUPLICATE_DEPARTMENT_NAME'),
                        ),
                    );
        }
        const obj = {
            _institution_id: req.headers._institution_id,
            program_id: req.body.program_id,
            program_name: req.body.program_name,
            department_name:
                req.body.department_name.charAt(0).toUpperCase() +
                req.body.department_name.slice(1),
        };
        const doc = await base_control.insert(department_subject, obj);
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DEPARTMENT_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_ADD_DEPARTMENT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update_department(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //check duplicate
        if (req.body.department_name && req.body.department_name.length != 0) {
            const department_name = [
                req.body.department_name,
                req.body.department_name.charAt(0).toUpperCase() +
                    req.body.department_name.slice(1),
                req.body.department_name.toUpperCase(),
                req.body.department_name.toLowerCase(),
            ];
            const course_data = await base_control.get(
                department_subject,
                {
                    _id: { $ne: ObjectId(req.params.id) },
                    department_name: { $in: department_name },
                    _institution_id: ObjectId(req.headers._institution_id),
                    program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            if (course_data.status)
                return res
                    .status(410)
                    .send(
                        common_files.response_function(
                            res,
                            410,
                            true,
                            req.t('DUPLICATE_DEPARTMENT_NAME'),
                            req.t('DUPLICATE_DEPARTMENT_NAME'),
                        ),
                    );
        }
        const obj = {
            department_name:
                req.body.department_name.charAt(0).toUpperCase() +
                req.body.department_name.slice(1),
        };
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(department_subject, query, obj);
        if (doc.status) {
            //Get Depts and subjects
            const dept_subj_list = await base_control.get_list(
                department_subject,
                {
                    _id: { $ne: ObjectId(req.params.id) },
                },
                { _id: 1, subject: 1 },
            );
            //return res.send(dept_subj_list.data);
            if (dept_subj_list.status) {
                const bulkSubjDept = [];
                for (const dept_subj of dept_subj_list.data) {
                    //subj shared with
                    for (const subj of dept_subj.subject) {
                        if (subj == [] || subj.shared_with == []) continue;

                        const filteredSubj = subj.shared_with.filter(
                            (ele) =>
                                ele.department_id.toString() == ObjectId(req.params.id).toString(),
                        );
                        if (filteredSubj == []) continue;
                        for (const subjF of filteredSubj) {
                            bulkSubjDept.push({
                                updateOne: {
                                    filter: {
                                        //program_id: { $ne: ObjectId(req.params.id) },
                                        //'subject._id': ObjectId(subj._id),
                                        'subject.shared_with._id': ObjectId(subjF._id),
                                    },
                                    update: {
                                        $set: {
                                            'subject.$[subj].shared_with.$[sharedID].department_name':
                                                req.body.department_name,
                                        },
                                    },
                                    arrayFilters: [
                                        { 'subj._id': ObjectId(subj._id) },
                                        { 'sharedID._id': ObjectId(subjF._id) },
                                    ],
                                },
                            });
                        }
                    }
                }
                if (bulkSubjDept.length > 0) await department_subject.bulkWrite(bulkSubjDept);
            }

            ////
            //Update Dept name in course area
            const courses_list = await base_control.get_list(
                digi_course,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1, administration: 1, participating: 1 },
            );
            //return res.send(courses_list);
            if (courses_list.status) {
                const bulk_data = [];
                courses_list.data.forEach((eleCL) => {
                    const parArr = eleCL.participating.filter(
                        (eleCL) => eleCL._department_id.toString() === req.params.id.toString(),
                    );
                    if (
                        eleCL.administration._department_id.toString() ===
                            req.params.id.toString() &&
                        parArr.length > 0
                    ) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'administration.department_name':
                                            req.body.department_name.charAt(0).toUpperCase() +
                                            req.body.department_name.slice(1),
                                        'participating.$[i].department_name':
                                            req.body.department_name.charAt(0).toUpperCase() +
                                            req.body.department_name.slice(1),
                                    },
                                },
                                arrayFilters: [{ 'i._department_id': ObjectId(req.params.id) }],
                            },
                        });
                    } else if (
                        eleCL.administration._department_id.toString() === req.params.id.toString()
                    ) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'administration.department_name':
                                            req.body.department_name.charAt(0).toUpperCase() +
                                            req.body.department_name.slice(1),
                                    },
                                },
                            },
                        });
                    } else if (parArr.length > 0) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'participating.$[i].department_name':
                                            req.body.department_name.charAt(0).toUpperCase() +
                                            req.body.department_name.slice(1),
                                    },
                                },
                                arrayFilters: [{ 'i._department_id': ObjectId(req.params.id) }],
                            },
                        });
                    }
                });
                const changes = await base_control.bulk_write(digi_course, bulk_data);
                if (!changes.status) console.log('Update successfully');
            }
            ////
            updateDepartmentSubjectFlatCacheData();
            updateCourseFlatCacheData();
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('DEPARTMENT_UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }

        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_UPDATE_DEPARTMENT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { /* program_id: ObjectId(req.params.program_id), */ isDeleted: false };
        const { status, data: department_data } = await base_control.get_list(
            department_subject,
            query,
            {},
        );
        if (!status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );
        let new_dept_data = [];
        const shared_subject = [];
        const shared_depart = [];
        for (let j = 0; j < department_data.length; j++) {
            const subject_data = department_data[j].subject.filter((ele) => ele.isDeleted == false);
            for (element of subject_data) {
                if (element.shared_with && element.shared_with.length !== 0) {
                    shared_subject.push({
                        program_id: department_data[j].program_id,
                        program_name: department_data[j].program_name,
                        department_id: department_data[j]._id,
                        department_name: department_data[j].department_name,
                        subject_id: element._id,
                        subject_name: element.subject_name,
                        shared_with: element.shared_with,
                        isActive: element.isActive,
                        isDeleted: element.isDeleted,
                    });
                }
            }
            department_data[j].shared_with.forEach((ele3) => {
                shared_depart.push({
                    _id: department_data[j]._id,
                    isActive: department_data[j].isActive,
                    isDeleted: department_data[j].isDeleted,
                    program_id: ele3.program_id,
                    program_name: ele3.program_name,
                    shared_from_program_id: department_data[j].program_id,
                    shared_from_program_name: department_data[j].program_name,
                    department_name: department_data[j].department_name,
                    subject: subject_data,
                    shared_with: department_data[j].shared_with,
                    shared: true,
                });
            });
            new_dept_data.push({
                _id: department_data[j]._id,
                isActive: department_data[j].isActive,
                isDeleted: department_data[j].isDeleted,
                program_id: department_data[j].program_id,
                program_name: department_data[j].program_name,
                department_name: department_data[j].department_name,
                subject: subject_data,
                shared: department_data[j].shared_with,
            });
        }
        for (let j = 0; j < new_dept_data.length; j++) {
            // const shared = [];
            for (element of shared_subject) {
                const datas = new_dept_data[j];
                if (
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                )
                    new_dept_data[j].subject.push({
                        _id: element.subject_id,
                        subject_name: element.subject_name,
                        isActive: element.isActive,
                        isDeleted: element.isDeleted,
                        shared: [],
                    });
            }
            // const subject = new_dept_data[j].subject.concat(shared);
            // Object.assign(new_dept_data[j], { subject });
        }
        new_dept_data = [...new_dept_data, ...shared_depart];
        new_dept_data = new_dept_data.filter(
            (i) => i.program_id.toString() === req.params.program_id.toString(),
        );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_LIST'),
                    new_dept_data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function listWithoutShareAdding(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { /* program_id: ObjectId(req.params.program_id), */ isDeleted: false };
        const { status, data: department_data } = await base_control.get_list(
            department_subject,
            query,
            {},
        );
        if (!status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );
        let new_dept_data = [];
        const shared_subject = [];
        for (let j = 0; j < department_data.length; j++) {
            const subject_data = department_data[j].subject.filter((ele) => ele.isDeleted == false);
            for (element of subject_data) {
                if (element.shared_with && element.shared_with.length != 0) {
                    shared_subject.push({
                        program_id: department_data[j].program_id,
                        program_name: department_data[j].program_name,
                        department_id: department_data[j]._id,
                        department_name: department_data[j].department_name,
                        subject_id: element._id,
                        subject_name: element.subject_name,
                        shared_with: element.shared_with,
                        isActive: element.isActive,
                        isDeleted: element.isDeleted,
                    });
                }
            }
            new_dept_data.push({
                _id: department_data[j]._id,
                isActive: department_data[j].isActive,
                isDeleted: department_data[j].isDeleted,
                program_id: department_data[j].program_id,
                program_name: department_data[j].program_name,
                department_name: department_data[j].department_name,
                subject: subject_data,
                shared: department_data[j].shared_with,
            });
        }
        for (let j = 0; j < new_dept_data.length; j++) {
            // const shared = [];
            for (element of shared_subject) {
                const datas = new_dept_data[j];
                if (
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                )
                    new_dept_data[j].subject.push({
                        _id: element.subject_id,
                        subject_name: element.subject_name,
                        isActive: element.isActive,
                        isDeleted: element.isDeleted,
                        shared: [],
                    });
            }
            // const subject = new_dept_data[j].subject.concat(shared);
            // Object.assign(new_dept_data[j], { subject });
        }
        new_dept_data = new_dept_data.filter(
            (i) => i.program_id.toString() === req.params.program_id.toString(),
        );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_LIST'),
                    new_dept_data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function course_department_list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            /* program_id: ObjectId(req.params.program_id), */ _institution_id: ObjectId(
                req.headers._institution_id,
            ),
            isDeleted: false,
        };
        const doc = await base_control.get_list(department_subject, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );
        let department_subject_list = [];
        const shared_subject = [];
        let obj = {};
        for (let i = 0; i < doc.data.length; i++) {
            if (doc.data[i].program_id.toString() == req.params.program_id.toString()) {
                const subject_list = doc.data[i].subject.filter((ele) => ele.isDeleted == false);
                obj = {
                    _id: doc.data[i]._id,
                    isActive: doc.data[i].isActive,
                    isDeleted: doc.data[i].isDeleted,
                    program_id: doc.data[i].program_id,
                    program_name: doc.data[i].program_name,
                    department_name: doc.data[i].department_name,
                    subject: subject_list,
                    share_with: doc.data[i].shared_with,
                    createdAt: doc.data[i].createdAt,
                    updatedAt: doc.data[i].updatedAt,
                };
                department_subject_list.push(obj);
                shared_subject.push(obj);
            } else {
                if (
                    doc.data[i].shared_with.findIndex(
                        (i) => i.program_id.toString() === req.params.program_id.toString(),
                    ) != -1
                ) {
                    const subject_list = doc.data[i].subject.filter(
                        (ele) => ele.isDeleted == false,
                    );
                    obj = {
                        _id: doc.data[i]._id,
                        isActive: doc.data[i].isActive,
                        isDeleted: doc.data[i].isDeleted,
                        program_id: doc.data[i].program_id,
                        program_name: doc.data[i].program_name,
                        department_name: doc.data[i].department_name,
                        subject: subject_list,
                        share_with: doc.data[i].shared_with,
                        createdAt: doc.data[i].createdAt,
                        updatedAt: doc.data[i].updatedAt,
                    };
                    // if (department_subject_list.findIndex(i => (i._id).toString() == (obj._id).toString()) == -1) {
                    department_subject_list.push(obj);
                    shared_subject.push(obj);
                    // }
                } else {
                    const subject_list = doc.data[i].subject.filter(
                        (ele) => ele.isDeleted == false,
                    );
                    const shared_subject_list = [];
                    for (subject_element of subject_list) {
                        if (
                            subject_element.shared_with.findIndex(
                                (i) => i.program_id.toString() === req.params.program_id.toString(),
                            ) != -1
                        ) {
                            shared_subject_list.push(subject_element);
                        }
                    }
                    if (shared_subject_list.length != 0) {
                        for (subject_element of shared_subject_list) {
                            for (share_subject_element of subject_element.shared_with) {
                                //Department check has to be change subject
                                if (
                                    share_subject_element.program_id.toString() ==
                                    req.params.program_id.toString()
                                ) {
                                    obj = {
                                        _id: share_subject_element.department_id,
                                        isActive: subject_element.isActive,
                                        isDeleted: subject_element.isDeleted,
                                        program_id: share_subject_element.program_id,
                                        program_name: share_subject_element.program_name,
                                        department_name: share_subject_element.department_name,
                                        subject: [subject_element],
                                        share_with: subject_element.shared_with,
                                        createdAt: subject_element.createdAt,
                                        updatedAt: subject_element.updatedAt,
                                    };
                                    // if (department_subject_list.findIndex(i => (i._id).toString() == (share_subject_element.department_id).toString()) == -1)
                                    department_subject_list.push(obj);
                                }
                            }
                        }
                        obj = {
                            _id: doc.data[i]._id,
                            isActive: doc.data[i].isActive,
                            isDeleted: doc.data[i].isDeleted,
                            program_id: doc.data[i].program_id,
                            program_name: doc.data[i].program_name,
                            department_name: doc.data[i].department_name,
                            subject: shared_subject_list,
                            share_with: doc.data[i].shared_with,
                            createdAt: doc.data[i].createdAt,
                            updatedAt: doc.data[i].updatedAt,
                        };
                        // department_subject_list.push({
                        //     _id: doc.data[i]._id,
                        //     isActive: doc.data[i].isActive,
                        //     isDeleted: doc.data[i].isDeleted,
                        //     program_id: doc.data[i].program_id,
                        //     program_name: doc.data[i].program_name,
                        //     department_name: doc.data[i].department_name,
                        //     subject: shared_subject_list,
                        //     share_with: doc.data[i].shared_with,
                        //     createdAt: doc.data[i].createdAt,
                        //     updatedAt: doc.data[i].updatedAt
                        // });
                        shared_subject.push(obj);
                    }
                }
            }
        }
        department_subject_list = [...new Set(department_subject_list)];
        return res.status(200).send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('DEPARTMENT_SUBJECT_LIST'),
                {
                    participation_subject: shared_subject,
                    admin_department: department_subject_list,
                },
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function delete_department(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Check is Department is Associated with Any course
        const check_query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            $or: [
                { 'administration._department_id': ObjectId(req.params.id) },
                { participating: { $elemMatch: { _department_id: ObjectId(req.params.id) } } },
            ],
        };
        const check_data = await base_control.get_list(digi_course, check_query, { _id: 1 });
        if (check_data.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('THIS_DEPARTMENT_ASSOCIATED_WITH_COURSES'),
                        req.t('THIS_DEPARTMENT_ASSOCIATED_WITH_COURSES'),
                    ),
                );

        const query = { _id: ObjectId(req.params.id) };
        const obj = { isDeleted: true };
        const doc = await base_control.update_condition(department_subject, query, obj);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        updateDepartmentSubjectFlatCacheData();
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('DEPARTMENT_DELETED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function add_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        if (req.body.subject && req.body.subject.length != 0) {
            const subject = [];
            for (element of req.body.subject) {
                if (element.subject_name.length != 0) {
                    subject.push(
                        element.subject_name,
                        element.subject_name.charAt(0).toUpperCase() +
                            element.subject_name.slice(1),
                        element.subject_name.toUpperCase(),
                        element.subject_name.toLowerCase(),
                    );
                }
            }
            const duplicate_data = await base_control.get(
                department_subject,
                {
                    subject: { $elemMatch: { subject_name: { $in: subject } } },
                    _institution_id: ObjectId(req.headers._institution_id),
                    program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            if (duplicate_data.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            true,
                            req.t('DUPLICATE_SUBJECT_NAME'),
                            req.t('DUPLICATE_SUBJECT_NAME'),
                        ),
                    );
        }

        const obj = {
            $push: { subject: req.body.subject },
        };
        const query = { _id: ObjectId(req.body.department_id) };
        const doc = await base_control.update_condition(department_subject, query, obj);
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SUBJECT_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_ADD_SUBJECT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        if (req.body.subject_name && req.body.subject_name.length != 0) {
            const subject = [
                req.body.subject_name,
                req.body.subject_name.charAt(0).toUpperCase() + req.body.subject_name.slice(1),
                req.body.subject_name.toUpperCase(),
                req.body.subject_name.toLowerCase(),
            ];
            const course_data = await base_control.get(
                department_subject,
                {
                    subject: {
                        $elemMatch: {
                            _id: {
                                $ne: ObjectId(req.params.department_id),
                                subject_name: { $in: subject },
                            },
                        },
                    },
                    // subject: { $elemMatch: { subject_name: { $in: subject } } },
                    _institution_id: ObjectId(req.headers._institution_id),
                    program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            if (course_data.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            true,
                            req.t('DUPLICATE_SUBJECT_NAME'),
                            req.t('DUPLICATE_SUBJECT_NAME'),
                        ),
                    );
        }

        const query = { _id: ObjectId(req.params.department_id), isDeleted: false };
        const obj = {
            $set: {
                'subject.$[sub].subject_name': req.body.subject_name,
            },
        };
        const filter = {
            arrayFilters: [{ 'sub._id': req.params.subject_id }],
        };
        const doc = await base_control.update_condition_array_filter(
            department_subject,
            query,
            obj,
            filter,
        );
        ///////////////////////
        if (doc.status) {
            ////
            //Update Dept name in course area
            const courses_list = await base_control.get_list(
                digi_course,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _program_id: ObjectId(req.body.program_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1, administration: 1, participating: 1 },
            );
            //return res.send(courses_list);
            if (courses_list.status) {
                const bulk_data = [];
                courses_list.data.forEach((eleCL) => {
                    const parArr = eleCL.participating.filter(
                        (eleCL) =>
                            eleCL._department_id.toString() ===
                                req.params.department_id.toString() &&
                            eleCL._subject_id.toString() === req.params.subject_id.toString(),
                    );
                    if (
                        eleCL.administration._department_id.toString() ===
                            req.params.department_id.toString() &&
                        eleCL.administration._subject_id.toString() ===
                            req.params.subject_id.toString() &&
                        parArr.length > 0
                    ) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'administration.subject_name':
                                            req.body.subject_name.charAt(0).toUpperCase() +
                                            req.body.subject_name.slice(1),
                                        'participating.$[i].subject_name':
                                            req.body.subject_name.charAt(0).toUpperCase() +
                                            req.body.subject_name.slice(1),
                                    },
                                },
                                arrayFilters: [
                                    { 'i._subject_id': ObjectId(req.params.subject_id) },
                                ],
                            },
                        });
                    } else if (
                        eleCL.administration._department_id.toString() ===
                            req.params.department_id.toString() &&
                        eleCL.administration._subject_id.toString() ===
                            req.params.subject_id.toString()
                    ) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'administration.subject_name':
                                            req.body.subject_name.charAt(0).toUpperCase() +
                                            req.body.subject_name.slice(1),
                                    },
                                },
                            },
                        });
                    } else if (parArr.length > 0) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCL._id),
                                },
                                update: {
                                    $set: {
                                        'participating.$[i].subject_name':
                                            req.body.subject_name.charAt(0).toUpperCase() +
                                            req.body.subject_name.slice(1),
                                    },
                                },
                                arrayFilters: [
                                    { 'i._subject_id': ObjectId(req.params.subject_id) },
                                ],
                            },
                        });
                    }
                });
                const changes = await base_control.bulk_write(digi_course, bulk_data);
                if (changes.status) console.log('Update successfully');
            }
            ////
        }
        //////////////////////
        updateDepartmentSubjectFlatCacheData();
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SUBJECT_UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );

        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_UPDATE_SUBJECT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function delete_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Check is Department Subject is Associated with Any course
        const check_query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            $or: [
                // { 'administration._department_id': ObjectId(req.params.department_id) },
                { 'administration._subject_id': ObjectId(req.params.subject_id) },
                // { 'participating': { $elemMatch: { _department_id: ObjectId(req.params.department_id) } } },
                { participating: { $elemMatch: { _subject_id: ObjectId(req.params.subject_id) } } },
            ],
        };
        const check_data = await base_control.get_list(digi_course, check_query, { _id: 1 });
        if (check_data.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('THIS_SUBJECT_IS_ASSOCIATED_WITH_COURSES'),
                        req.t('THIS_SUBJECT_IS_ASSOCIATED_WITH_COURSES'),
                    ),
                );

        const query = { _id: ObjectId(req.params.department_id), isDeleted: false };
        const obj = {
            $pull: {
                subject: { _id: req.params.subject_id },
            },
        };
        const doc = await base_control.update_condition(department_subject, query, obj);
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SUBJECT_REMOVED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_UPDATE_SUBJECT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function share_department(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { status: d_status, data: d_data } = await base_control.get(
            department_subject,
            {
                _id: ObjectId(req.params.id),
                isDeleted: false,
            },
            { program_id: 1, shared_with: 1 },
        );
        if (!d_status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        req.t('DEPARTMENT_NOT_FOUND'),
                    ),
                );
        const programs = [];
        d_data.shared_with.forEach((element) => {
            if (
                req.body.shared_with.findIndex(
                    (i) => i.program_id.toString() === element.program_id.toString(),
                ) === -1
            ) {
                programs.push(ObjectId(element.program_id));
            }
        });
        // Check is Department Sharing is Present in Course Area
        const { status: c_status } = await base_control.get(
            digi_course,
            {
                isDeleted: false,
                _program_id: { $in: programs },
                $or: [
                    {
                        'administration._program_id': ObjectId(d_data.program_id),
                        'administration._department_id': ObjectId(d_data._id),
                    },
                    {
                        'participating._program_id': ObjectId(d_data.program_id),
                        'participating._department_id': ObjectId(d_data._id),
                    },
                ],
            },
            { _id: 1 },
        );
        if (c_status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('DEPARTMENT_IS_ASSOCIATED_WITH_COURSE'),
                        req.t('DEPARTMENT_IS_ASSOCIATED_WITH_COURSE'),
                    ),
                );

        // Update Sharing data
        const obj = { shared_with: req.body.shared_with };
        const doc = await base_control.update(department_subject, Object(req.params.id), obj);
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DEPARTMENT_SHARED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_SHARE_DEPARTMENT'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function share_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const { status: d_status, data: d_data } = await base_control.get(
            department_subject,
            {
                _id: ObjectId(req.params.department_id),
                isDeleted: false,
            },
            { program_id: 1, subject: 1 },
        );
        if (!d_status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        req.t('DEPARTMENT_NOT_FOUND'),
                    ),
                );
        const programs = [];
        d_data.subject.forEach((sub_element) => {
            sub_element.shared_with.forEach((share_element) => {
                if (req.body.shared_with.length === 0)
                    programs.push(ObjectId(share_element.program_id));
                else if (
                    req.body.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() === share_element.program_id.toString() &&
                            i.department_id.toString() === share_element.department_id.toString(),
                    ) === -1
                ) {
                    programs.push(ObjectId(share_element.program_id));
                }
            });
        });
        // Check is Department Sharing is Present in Course Area
        const { status: c_status } = await base_control.get(
            digi_course,
            {
                isDeleted: false,
                _program_id: { $in: programs },
                $or: [
                    {
                        'administration._program_id': ObjectId(d_data.program_id),
                        'administration._department_id': ObjectId(d_data._id),
                        'administration._subject_id': ObjectId(req.params.subject_id),
                    },
                    {
                        'participating._program_id': ObjectId(d_data.program_id),
                        'participating._department_id': ObjectId(d_data._id),
                        'participating._subject_id': ObjectId(req.params.subject_id),
                    },
                ],
            },
            { _id: 1 },
        );
        if (c_status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('DEPARTMENT_SUBJECT_IS_ASSOCIATED_WITH_COURSE'),
                        req.t('DEPARTMENT_SUBJECT_IS_ASSOCIATED_WITH_COURSE'),
                    ),
                );
        const query = { _id: ObjectId(req.params.department_id), isDeleted: false };
        const obj = {
            $set: {
                'subject.$[sub].shared_with': req.body.shared_with,
            },
        };
        const filter = {
            arrayFilters: [{ 'sub._id': req.params.subject_id }],
        };
        const doc = await base_control.update_condition_array_filter(
            department_subject,
            query,
            obj,
            filter,
        );
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DEPARTMENT_SHARED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_SHARE_DEPARTMENT'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const checkDuplicateDepartmentNameInExcel = function (program_excel_data, department_name) {
    const course_name_arr = program_excel_data.map((ele) => ele.Department_Name.toLowerCase());
    let flag = 0;
    const first_ind = course_name_arr.indexOf(department_name.trim().toLowerCase());
    const last_ind = course_name_arr.lastIndexOf(department_name.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const findDuplicates = (arr) => arr.filter((item, index) => arr.indexOf(item) != index);

const validate_data_check = async function (req, data, res, data_obj, duplicate_in_excel) {
    const { program_data, dept_subj_data } = data_obj;

    const message = [];
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) =>
            ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase() &&
            ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase(),
    );
    if (program_name_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME_AND_CODE'));

    //Department Duplicate Check in DB
    //Subject Duplicate Check
    //console.log(program_data.data[program_name_ind], data.Department_Name.toLowerCase())
    if (program_name_ind != -1) {
        if (dept_subj_data.status) {
            const dept_ind = dept_subj_data.data.findIndex(
                (ele) =>
                    ele.department_name.toLowerCase() ==
                        data.Department_Name.trim().toLowerCase() &&
                    ele.program_id.toString() ==
                        program_data.data[program_name_ind]._id.toString() &&
                    ele.isDeleted == false,
            );
            if (dept_ind != -1) message.push(req.t('DEPARTMENT_ALREADY_EXIST'));
        }

        ///
        //Subject Duplicate Check
        const subject_arr = data.Subjects.split(',');
        const new_subject_arr = [];
        for (let i = 0; i < subject_arr.length; i++) {
            new_subject_arr.push(subject_arr[i].trim().toLowerCase());
        }
        const duplicate_subject = findDuplicates(new_subject_arr);
        if (duplicate_subject.length > 0) message.push(req.t('CHECK_SUBJECTS_ARE_DUPLICATE'));
        else {
            if (dept_subj_data.status) {
                //Subject Duplicate check in Program Level
                const d_s_data = dept_subj_data.data.filter(
                    (ele) =>
                        ele.program_id.toString() ==
                        program_data.data[program_name_ind]._id.toString(),
                );
                for (let i = 0; i < new_subject_arr.length; i++) {
                    for (d_data of d_s_data) {
                        const subj_ind = d_data.subject.findIndex(
                            (ele) =>
                                ele.subject_name.toLowerCase() ==
                                    new_subject_arr[i].trim().toLowerCase() &&
                                ele.isDeleted == false,
                        );
                        if (subj_ind != -1) {
                            message.push(
                                req.t('CHECK_SUBJECTS_IS_DUPLICATE_IN_BETWEEN_DEPARTMENTS') +
                                    ':' +
                                    new_subject_arr[i],
                            );
                            break;
                        }
                    }
                }
            }
        }
    }

    return message;
};
const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + ' is required');
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
};
async function data_check_department_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Department and subject list
        //let query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const query = { isDeleted: false };
        const dept_subj_data = await base_control.get_list(department_subject, query, {});
        // if (!dept_subj_data.status)
        //     return res
        //         .status(200)
        //         .send(common_files.response_function(res, 200, false, 'Department not found', []));

        const data_obj = { program_data, dept_subj_data }; //return res.send(data_obj)

        //Empty Validation
        const optional_field = [];
        const empty_validation_check = field_empty_validation(req.body.department, optional_field);
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        res,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.department) {
            let message = [];
            const department_name_dup_status = checkDuplicateDepartmentNameInExcel(
                req.body.department,
                data.Department_Name,
            );
            if (department_name_dup_status) {
                //Duplicate in Excel
                message = await validate_data_check(
                    req,
                    data,
                    res,
                    data_obj,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                message = await validate_data_check(
                    req,
                    data,
                    res,
                    data_obj,
                    (duplicate_in_excel = false),
                );
            }
            if (message.length > 0) invalid_data.push({ data, message });
            else valid_data.push({ data, message });
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function localDataCheckDepartmentSubject(req, res) {
    try {
        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.response_function(res, 409, false, req.t('PROGRAM_NOT_FOUND'), []),
                );

        // Department and subject list
        //let query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const query = { isDeleted: false };
        const dept_subj_data = await base_control.get_list(department_subject, query, {});
        // if (!dept_subj_data.status)
        //     return res
        //         .status(200)
        //         .send(common_files.response_function(res, 200, false, 'Department not found', []));

        const data_obj = { program_data, dept_subj_data }; //return res.send(data_obj)

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.department) {
            let message = [];
            const department_name_dup_status = checkDuplicateDepartmentNameInExcel(
                req.body.department,
                data.Department_Name,
            );
            if (department_name_dup_status) {
                //Duplicate in Excel
                message = await validate_data_check(
                    req,
                    data,
                    res,
                    data_obj,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                message = await validate_data_check(
                    req,
                    data,
                    res,
                    data_obj,
                    (duplicate_in_excel = false),
                );
            }
            if (message.length > 0) invalid_data.push({ data, message });
            else valid_data.push({ data, message });
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0) return { status: false, data: d };
        return { status: true, data: d };
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function import_department_subject(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Data Check
        const data_check_status = await localDataCheckDepartmentSubject(req, res);
        if (!data_check_status.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        data_check_status.data,
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        //res.send("Success")
        let obj = {};
        const department_subject_DS = [];
        for (data of req.body.department) {
            const program_ind = program_data.data.findIndex(
                (ele) => ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase(),
            );

            //Subjects
            const subject_arr = data.Subjects.split(',');
            const new_subject_arr = [];
            for (let i = 0; i < subject_arr.length; i++) {
                new_subject_arr.push({ subject_name: subject_arr[i].trim() });
            }

            obj = {
                insertOne: {
                    document: {
                        _institution_id: ObjectId(req.headers._institution_id),
                        program_id: ObjectId(program_data.data[program_ind]._id),
                        program_name: program_data.data[program_ind].name,
                        department_name: data.Department_Name.trim(),
                        subject: new_subject_arr,
                    },
                },
            };
            department_subject_DS.push(obj);
        }
        //return res.send(department_subject_DS)
        const doc = await base_control.bulk_write(department_subject, department_subject_DS);
        updateDepartmentSubjectFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DEPARTMENTS_AND_SUBJECTS_IMPORTED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_DEPARTMENTS_AND_SUBJECTS'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

module.exports = {
    insert,
    list,
    listWithoutShareAdding,
    course_department_list,
    delete_department,
    update_department,
    add_subject,
    update_subject,
    delete_subject,
    share_department,
    share_subject,
    import_department_subject,
    data_check_department_subject,
};
