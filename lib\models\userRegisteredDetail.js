const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { USER, USER_REGISTERED_DETAIL, ACTIVE, INACTIVE } = require('../utility/constants');

const userRegisteredDetailSchema = new Schema(
    {
        userId: {
            type: ObjectId,
            ref: USER,
        },
        registeredOn: { type: Date },
        userType: { type: String },
        deviceType: { type: String },
        deviceBrandName: { type: String },
        deviceID: { type: String },
        deviceNumber: { type: String },
        deviceOSVersion: { type: String },
        status: { type: String, enum: [ACTIVE, INACTIVE], default: ACTIVE },
        unregisteredBy: { type: ObjectId, ref: USER },
        unregisteredOn: { type: Date },
        unregisteredReason: { type: String },
    },
    { timestamps: true },
);
module.exports = model(USER_REGISTERED_DETAIL, userRegisteredDetailSchema);
