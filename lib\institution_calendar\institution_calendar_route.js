const express = require('express');
const route = express.Router();
const institution = require('./institution_calendar_controller');
const validater = require('./institution_calendar_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get(
    '/list_calendar',
    [userPolicyAuthentication(['calendar:institution_calendar:calendar_list_view'])],
    institution.listAll,
);
route.post('/list', [userPolicyAuthentication([])], institution.list_values);
route.get(
    '/list_mon_two_date',
    [userPolicyAuthentication([])],
    /* validater.institution_calendar_id, */ institution.list_mon_two_date,
);
route.get(
    '/calendars',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, defaultPolicy.DC_STAFF])],
    institution.institutionCalenders,
);
route.get(
    '/:id',
    [userPolicyAuthentication([])],
    validater.institution_calendar_id,
    institution.list_id,
);
route.get(
    '/',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'calendar:institution_calendar:calendar_list_view',
        ]),
    ],
    institution.list,
);
route.post(
    '/',
    [userPolicyAuthentication(['calendar:institution_calendar:create'])],
    validater.institution_calendar,
    institution.insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:calendar_edit',
            'calendar:institution_calendar:calendar_active_and_inactive',
        ]),
    ],
    validater.institution_calendar_update,
    institution.update,
);
route.delete(
    '/:id',
    [userPolicyAuthentication(['calendar:institution_calendar:calendar_delete'])],
    validater.institution_calendar_id,
    institution.delete,
);

module.exports = route;
