const { Schema } = require('mongoose');
const { MASTER_TYPE, INSTITUTION, SETTINGS } = require('../../utility/constants');
const { session } = require('../setting/setting.schema');
const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
} = require('../../utility/enums');

const basicDetailsSchema = new Schema({
    timeZone: {
        type: String,
        default: '',
    },
    isGenderSegregation: {
        type: Boolean,
        default: false,
    },
    breaks: [
        {
            name: {
                type: String,
            },
            session,
            workingDays: [
                {
                    type: String,
                    enum: [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY],
                },
            ],
            isParentConfig: {
                type: Boolean,
                default: true,
            },
        },
    ],
    eventType: [
        {
            name: {
                type: String,
            },
            isLeave: {
                type: Boolean,
                default: false,
            },
            isParentConfig: {
                type: Boolean,
                default: true,
            },
        },
    ],
});

const programInputSchema = new Schema({
    curriculumNaming: [
        {
            mode: {
                type: Number,
            },
            isDefault: {
                type: Boolean,
                default: false,
            },
        },
    ],
    creditHours: [
        {
            mode: {
                type: String,
            },
            isDefault: {
                type: Boolean,
                default: false,
            },
        },
    ],
    programDurationFormat: [
        {
            format: {
                type: String,
            },
            defaultInput: {
                type: String,
            },
            isDefault: {
                type: Boolean,
                default: false,
            },
            withoutLevel: {
                type: Boolean,
                default: false,
            },
        },
    ],
});

const program = new Schema(
    {
        programType: {
            type: String,
        },
        _program_type_id: {
            type: Schema.Types.ObjectId,
            ref: SETTINGS,
        },
        name: {
            type: String,
            trim: true,
            required: true,
        },
        code: {
            type: String,
            trim: true,
            required: true,
        },
        level: {
            type: String,
            trim: true,
        },
        type: {
            type: String,
            required: true,
            enum: [MASTER_TYPE.PROGRAM, MASTER_TYPE.PREREQUISITE],
        },
        degree: {
            type: String,
            trim: true,
        },
        noOfTerms: {
            type: Number,
        },
        terms: [
            {
                termNo: Number,
                termName: String,
            },
        ],
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        _parent_id: {
            type: Schema.Types.ObjectId,
            default: null,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isConfigured: {
            type: Boolean,
            default: false,
        },
        goals: {
            content: { type: String },
            mediaURL: { type: String },
        },
        objectives: {
            content: { type: String },
            mediaURL: { type: String },
        },
        portfolio: [
            {
                title: String,
                description: String,
                mediaURL: String,
            },
        ],
        settings: {
            basicDetails: { type: basicDetailsSchema },
            programInput: { type: programInputSchema },
        },
    },
    { timestamps: true },
);
module.exports = program;
