const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
// const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const institution = require('mongoose').model(constant.INSTITUTION);
const {
    addingStudentSchedule,
    updateStudentGroupFlatCacheData,
} = require('./student_group_services');
const {
    updateStudentGroupRedisKey,
    updateUserCourseRedisKey,
} = require('../utility/utility.service');

exports.grouping = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        let doc = { status: true, data: [] };
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );

        if (req.body.mode === 'auto') {
            const setting_loc = student_group_data.data.groups[group_loc].group_setting.findIndex(
                (i) =>
                    req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
            );
            if (setting_loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('NO_GROUPS_FOUND'),
                            req.t('NO_GROUPS_FOUND'),
                        ),
                    );
            const settings = student_group_data.data.groups[group_loc].group_setting[setting_loc];
            const student = [];
            let sorted = [];
            const calc = [];
            let filter;
            let status = true;
            let data = '';
            req.body._student_ids.forEach((element) => {
                const std_data =
                    student_group_data.data.groups[group_loc].students[
                        student_group_data.data.groups[group_loc].students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ];
                student.push(std_data);
            });
            switch (req.body.method) {
                case 'cluster':
                    console.log('cluster');
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'academic_id':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.academic_no) > parseInt(b.academic_no)) {
                            comparison = -1;
                        } else if (parseInt(a.academic_no) < parseInt(b.academic_no)) {
                            comparison = 1;
                        }
                        return comparison * -1;
                    });
                    break;
                case 'mark':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'name':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
                default:
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
            }
            if (req.body.method !== 'cluster') {
                console.log('Non cluster');
                const grouping_student_count = req.body._student_ids.length;
                const per_group = grouping_student_count / settings.no_of_group;
                let pos = 0;
                let last = 0;
                let last_know = grouping_student_count;
                let index = 0;
                for (element of settings.groups) {
                    // settings.groups.forEach((element, index) => {
                    let count = 0;
                    if (parseInt(per_group) * (settings.groups.length - index) < last_know) {
                        last_know -= Math.ceil(per_group);
                        count = Math.ceil(per_group);
                    } else {
                        last_know -= parseInt(per_group);
                        count = parseInt(per_group);
                    }
                    pos += count;
                    cut_std = sorted.slice(last, pos);
                    last = pos;
                    const old_std = settings.groups[index]._student_ids;
                    const std_ids = cut_std.map((dat) => {
                        return ObjectId(dat._student_id);
                    });
                    const new_std = old_std.concat(std_ids);
                    if (settings.no_of_student < new_std.length) {
                        status = false;
                        data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                        break;
                        // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                    } else {
                        settings.groups[index]._student_ids = new_std;
                        status = true;
                    }
                    // });
                    index++;
                }
            } else {
                const group_len = settings.groups.length;
                settings.groups.forEach((element) => {
                    let old_mark = 0;
                    element._student_ids.forEach((sub_element) => {
                        old_mark += parseFloat(
                            student_group_data.data.groups[group_loc].students[
                                student_group_data.data.groups[group_loc].students.findIndex(
                                    (i) => i._student_id.toString() === sub_element.toString(),
                                )
                            ].mark,
                        );
                    });
                    calc.push({
                        group: element.group_no,
                        mark: old_mark,
                        students: Array.from(element._student_ids),
                    });
                });
                for (element of sorted) {
                    // sorted.forEach((element, index) => {
                    const val = calc.map((i) => i.mark);
                    const low_pos = calc.findIndex((i) => i.mark === Math.min(...val));
                    if (low_pos !== -1 && group_len > low_pos) {
                        calc[low_pos].students.push(element._student_id);
                        calc[low_pos].mark += parseFloat(element.mark);
                        settings.groups[low_pos]._student_ids.push(element._student_id);
                        status = true;
                    }
                    if (settings.no_of_student < settings.groups[low_pos]._student_ids.length) {
                        status = false;
                        data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                        break;
                        // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                    }
                    // });
                }
            }
            if (status) {
                objs = {
                    $pull: { 'groups.$[k].ungrouped': { $in: req.body._student_ids } },
                    $set: { 'groups.$[k].group_setting.$[i]': settings },
                };
                filter = {
                    arrayFilters: [
                        { 'k.term': req.body.batch, 'k.level': req.body.level },
                        { 'i.gender': req.query.mode ? req.query.mode : req.body.gender },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data };
            }
        } else if (req.body.mode === 'manual') {
            const setting_loc = student_group_data.data.groups[group_loc].group_setting.findIndex(
                (i) =>
                    req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
            );
            if (setting_loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                            req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                        ),
                    );
            const setting_group_loc = student_group_data.data.groups[group_loc].group_setting[
                setting_loc
            ].groups.findIndex((i) => i.group_no.toString() === req.body.group_no.toString());
            if (setting_group_loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('STUDENT_GROUP_SETTING_NOT_FOUND'),
                            req.t('STUDENT_GROUP_SETTING_NOT_FOUND'),
                        ),
                    );
            let excess = 0;
            let capacity = 0;
            let strength = 0;
            excess = student_group_data.data.groups[group_loc].group_excess_count
                ? student_group_data.data.groups[group_loc].group_excess_count
                : 0;
            capacity =
                student_group_data.data.groups[group_loc].group_setting[setting_loc].no_of_student;
            strength =
                student_group_data.data.groups[group_loc].group_setting[setting_loc].groups[
                    setting_group_loc
                ]._student_ids.length + req.body._student_ids.length;
            if (!(strength <= excess + capacity))
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                        ),
                    );
            objs = {
                $pull: { 'groups.$[k].ungrouped': { $in: req.body._student_ids } },
                $push: {
                    'groups.$[k].group_setting.$[i].groups.$[j]._student_ids':
                        req.body._student_ids,
                },
            };
            filter = {
                arrayFilters: [
                    { 'k.term': req.body.batch, 'k.level': req.body.level },
                    { 'i.gender': req.query.mode ? req.query.mode : req.body.gender },
                    { 'j.group_no': req.body.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(req, 410, false, doc.data, doc.data),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.sub_course_grouping = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_pos = student_group_data.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_pos === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        const group_no = student_group_data.data.groups[student_group_row].courses[
            course_pos
        ].setting.findIndex(
            (i) =>
                (req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender) &&
                i._group_no.toString() === req.body.master_group.toString(),
        );
        if (group_no === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_GROUP'),
                        req.t('UNABLE_TO_FIND_GROUP'),
                    ),
                );

        let doc = { status: true, data: [] };
        if (req.body.mode === 'auto') {
            const settings =
                student_group_data.data.groups[student_group_row].courses[course_pos].setting[
                    group_no
                ];
            const student = [];
            let sorted = [];
            let calc = [];
            let filter;
            let status = true;
            let data = '';
            req.body._student_ids.forEach((element) => {
                const std_data =
                    student_group_data.data.groups[student_group_row].students[
                        student_group_data.data.groups[student_group_row].students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ];
                student.push(std_data);
            });
            switch (req.body.method) {
                case 'cluster':
                    console.log('cluster');
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'academic_id':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.academic_no) > parseInt(b.academic_no)) {
                            comparison = -1;
                        } else if (parseInt(a.academic_no) < parseInt(b.academic_no)) {
                            comparison = 1;
                        }
                        return comparison * -1;
                    });
                    break;
                case 'mark':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'name':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
                default:
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
            }
            if (req.body.method !== 'cluster') {
                console.log('Non cluster');
                const grouping_student_count = req.body._student_ids.length;
                let master_index = 0;
                for (element of settings.session_setting) {
                    // settings.session_setting.forEach((element, master_index) => {
                    const per_group = grouping_student_count / element.no_of_group;
                    let pos = 0;
                    let last = 0;
                    let index = 0;
                    let last_know = grouping_student_count;
                    for (sub_element of element.groups) {
                        // element.groups.forEach((sub_element, index) => {
                        let count = 0;
                        if (parseInt(per_group) * (element.groups.length - index) < last_know) {
                            last_know -= Math.ceil(per_group);
                            count = Math.ceil(per_group);
                        } else {
                            last_know -= parseInt(per_group);
                            count = parseInt(per_group);
                        }
                        pos += count;
                        cut_std = sorted.slice(last, pos);
                        last = pos;
                        const old_std = element.groups[index]._student_ids;
                        const std_ids = cut_std.map((dat) => {
                            return ObjectId(dat._student_id);
                        });
                        const new_std = old_std.concat(std_ids);
                        if (element.no_of_student < new_std.length) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        } else {
                            settings.session_setting[master_index].groups[index]._student_ids =
                                new_std;
                            status = true;
                        }
                        //     });
                        // });
                        index++;
                    }
                    if (!status) break;
                    master_index++;
                }
            } else {
                let master_index = 0;
                for (master_element of settings.session_setting) {
                    // settings.session_setting.forEach((master_element, master_index) => {
                    const group_len = master_element.groups.length;
                    calc = [];
                    master_element.groups.forEach((element) => {
                        let old_mark = 0;
                        element._student_ids.forEach((sub_element) => {
                            old_mark += parseFloat(
                                student_group_data.data.groups[student_group_row].students[
                                    student_group_data.data.groups[
                                        student_group_row
                                    ].students.findIndex(
                                        (i) => i._student_id.toString() === sub_element.toString(),
                                    )
                                ].mark,
                            );
                        });
                        calc.push({
                            group: element.group_no,
                            mark: old_mark,
                            students: Array.from(element._student_ids),
                        });
                    });
                    for (element of sorted) {
                        // sorted.forEach((element, index) => {
                        const val = calc.map((i) => i.mark);
                        const low_pos = calc.findIndex((i) => i.mark === Math.min(...val));
                        if (low_pos !== -1 && group_len > low_pos) {
                            calc[low_pos].students.push(element._student_id);
                            calc[low_pos].mark += parseFloat(element.mark);
                            settings.session_setting[master_index].groups[
                                low_pos
                            ]._student_ids.push(element._student_id);
                            status = true;
                        }
                        if (
                            master_element.no_of_student <
                            settings.session_setting[master_index].groups[low_pos]._student_ids
                                .length
                        ) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        }
                        // });
                    }
                    if (!status) break;
                    master_index++;
                } // });
            }
            if (status) {
                settings.ungrouped = settings.ungrouped.filter(
                    (item) => !req.body._student_ids.includes(item.toString()),
                );
                objs = {
                    $set: { 'groups.$[s].courses.$[i].setting.$[j]': settings },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        {
                            'j._group_no': req.body.master_group,
                            'j.gender': req.query.mode ? req.query.mode : req.body.gender,
                        },
                        {
                            's.level': req.body.level,
                            's.term': req.body.batch,
                        },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data };
            }
        } else if (req.body.mode === 'manual') {
            //Excess count validation has to be hear
            const course_session_setting =
                student_group_data.data.groups[student_group_row].courses[course_pos].setting[
                    group_no
                ].session_setting;
            if (course_session_setting.length === 0) {
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('NO_SESSION_GROUPS_CREATED'),
                            req.t('NO_SESSION_GROUPS_CREATED'),
                        ),
                    );
            }
            let excess = 0;
            let capacity = 0;
            let strength = 0;
            const validation = [];
            for (let index = 0; index < req.body.delivery_group.length; index++) {
                const element = req.body.delivery_group[index].session_type;
                const grp_nos = req.body.delivery_group[index].group_no;
                const session_type_ind =
                    course_session_setting[
                        course_session_setting.findIndex((i) => i.session_type === element)
                    ];
                if (session_type_ind === undefined)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('SESSION_TYPE_NOT_MATCHING'),
                                req.t('SESSION_TYPE_NOT_MATCHING'),
                            ),
                        );
                capacity = session_type_ind.no_of_student;
                excess = student_group_data.data.groups[student_group_row].course_excess_count
                    ? student_group_data.data.groups[student_group_row].course_excess_count
                    : 0;
                const new_grp_ind = session_type_ind.groups.findIndex(
                    (i) => i.group_no.toString() === grp_nos.toString(),
                );
                if (new_grp_ind === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('GROUP_NO_NOT_FOUND'),
                                req.t('GROUP_NO_NOT_FOUND'),
                            ),
                        );
                strength =
                    session_type_ind.groups[new_grp_ind]._student_ids.length +
                    req.body._student_ids.length;
                if (!(strength <= excess + capacity))
                    validation.push({
                        session_type: element,
                        message: 'capacity exceeded increase extra allowed',
                    });
            }
            if (validation.length !== 0)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                            validation,
                        ),
                    );

            req.body.delivery_group.forEach(async (element, index) => {
                objs = {
                    $push: {
                        'groups.$[s].courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                            req.body._student_ids,
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        {
                            'j._group_no': req.body.master_group,
                            'j.gender': req.query.mode ? req.query.mode : req.body.gender,
                        },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': element.group_no },
                        {
                            's.level': req.body.level,
                            's.term': req.body.batch,
                        },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
                if (req.body.delivery_group.length === index + 1) {
                    objs = {
                        $pull: {
                            'groups.$[s].courses.$[i].setting.$[j].ungrouped': {
                                $in: req.body._student_ids,
                            },
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i._course_id': ObjectId(req.body._course_id) },
                            { 'j._group_no': req.body.master_group },
                            {
                                's.level': req.body.level,
                                's.term': req.body.batch,
                            },
                        ],
                    };
                    doc = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                    // Adding Student to Course Schedule
                    await addingStudentSchedule({
                        _institution_id: student_group_data.data._institution_id,
                        institutionCalendarId: student_group_data.data._institution_calendar_id,
                        studentGroup:
                            student_group_data.data.groups[student_group_row].courses[course_pos]
                                .setting[group_no],
                        studentData: student_group_data.data.groups[student_group_row].students,
                        studentIds: req.body._student_ids,
                        deliveryGroups: req.body.delivery_group,
                        courseId: req.body._course_id,
                        status: req.body.status,
                    });
                }
            });
        }
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(req, 410, false, doc.data, doc.data),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.individual_course_grouping = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        let doc = { status: true };
        // let doc;
        if (req.body.mode === 'auto') {
            // let course_pos = student_group_check.data.groups[student_group_row].courses.findIndex(i => i._course_id === req.body._course_id);
            const settings =
                student_group_check.data.groups[student_group_row].courses[course_loc].setting[
                    student_group_check.data.groups[student_group_row].courses[
                        course_loc
                    ].setting.findIndex((i) =>
                        req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
                    )
                ];
            const student = [];
            let sorted = [];
            let calc = [];
            let filter;
            let status = true;
            let data = '';
            req.body._student_ids.forEach((element) => {
                const std_data =
                    student_group_check.data.groups[student_group_row].students[
                        student_group_check.data.groups[student_group_row].students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ];
                student.push(std_data);
            });
            switch (req.body.method) {
                case 'cluster':
                    console.log('cluster');
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'academic_id':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.academic_no) > parseInt(b.academic_no)) {
                            comparison = -1;
                        } else if (parseInt(a.academic_no) < parseInt(b.academic_no)) {
                            comparison = 1;
                        }
                        return comparison * -1;
                    });
                    break;
                case 'mark':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'name':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
                default:
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
            }
            if (req.body.method !== 'cluster') {
                console.log('Non cluster');
                const grouping_student_count = req.body._student_ids.length;
                let master_index = 0;
                for (element of settings.session_setting) {
                    // settings.session_setting.forEach((element, master_index) => {
                    const per_group = grouping_student_count / element.no_of_group;
                    let pos = 0;
                    let last = 0;
                    let index = 0;
                    let last_know = grouping_student_count;
                    for (sub_element of element.groups) {
                        // element.groups.forEach((sub_element, index) => {
                        let count = 0;
                        if (parseInt(per_group) * (element.groups.length - index) < last_know) {
                            last_know -= Math.ceil(per_group);
                            count = Math.ceil(per_group);
                        } else {
                            last_know -= parseInt(per_group);
                            count = parseInt(per_group);
                        }
                        pos += count;
                        cut_std = sorted.slice(last, pos);
                        last = pos;
                        const old_std = element.groups[index]._student_ids;
                        const std_ids = cut_std.map((dat) => {
                            return ObjectId(dat._student_id);
                        });
                        const new_std = old_std.concat(std_ids);
                        if (element.no_of_student < new_std.length) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        } else {
                            settings.session_setting[master_index].groups[index]._student_ids =
                                new_std;
                            status = true;
                        }
                        // });
                        index++;
                    }
                    if (!status) break;
                    master_index++;
                }
                // });
                // data = settings;
            } else {
                let master_index = 0;
                for (master_element of settings.session_setting) {
                    // settings.session_setting.forEach((master_element, master_index) => {
                    const group_len = master_element.groups.length;
                    calc = [];
                    master_element.groups.forEach((element) => {
                        let old_mark = 0;
                        element._student_ids.forEach((sub_element) => {
                            old_mark += parseFloat(
                                student_group_check.data.groups[student_group_row].students[
                                    student_group_check.data.groups[
                                        student_group_row
                                    ].students.findIndex(
                                        (i) => i._student_id.toString() === sub_element.toString(),
                                    )
                                ].mark,
                            );
                        });
                        calc.push({ mark: old_mark, students: Array.from(element._student_ids) });
                    });
                    for (element of sorted) {
                        // sorted.forEach((element, index) => {
                        const val = calc.map((i) => i.mark);
                        const low_pos = calc.findIndex((i) => i.mark === Math.min(...val));
                        if (low_pos !== -1 && group_len > low_pos) {
                            calc[low_pos].students.push(element._student_id);
                            calc[low_pos].mark += parseFloat(element.mark);
                            settings.session_setting[master_index].groups[
                                low_pos
                            ]._student_ids.push(element._student_id);
                            status = true;
                        }
                        if (
                            master_element.no_of_student <
                            settings.session_setting[master_index].groups[low_pos]._student_ids
                                .length
                        ) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        }
                    }
                    // });
                    if (!status) break;
                    master_index++;
                }
                // });
            }
            if (status) {
                settings.ungrouped = settings.ungrouped.filter(
                    (item) => !req.body._student_ids.includes(item.toString()),
                );
                objs = {
                    $set: { 'groups.$[i].courses.$[m].setting.$[j]': settings },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'j.gender': req.query.mode ? req.query.mode : req.body.gender },
                        { 'm._course_id': req.body._course_id },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data };
            }
        } else if (req.body.mode === 'manual') {
            //Validate Excess count
            //student_group_check.data.groups[student_group_row].courses[course_loc].setting
            const group_no = student_group_check.data.groups[student_group_row].courses[
                course_loc
            ].setting.findIndex((i) =>
                req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
            );
            const course_session_setting =
                student_group_check.data.groups[student_group_row].courses[course_loc].setting[
                    group_no
                ].session_setting;
            if (course_session_setting.length === 0) {
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('NO_SESSION_GROUPS_CREATED'),
                            req.t('NO_SESSION_GROUPS_CREATED'),
                        ),
                    );
            }
            let excess = 0;
            let capacity = 0;
            let strength = 0;
            const validation = [];
            for (let index = 0; index < req.body.delivery_group.length; index++) {
                const element = req.body.delivery_group[index].session_type;
                const grp_nos = req.body.delivery_group[index].group_no;
                const session_type_ind =
                    course_session_setting[
                        course_session_setting.findIndex((i) => i.session_type === element)
                    ];
                if (session_type_ind === undefined)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('SESSION_TYPE_NOT_MATCHING'),
                                req.t('SESSION_TYPE_NOT_MATCHING'),
                            ),
                        );
                capacity = session_type_ind.no_of_student;
                excess = student_group_check.data.groups[student_group_row].course_excess_count
                    ? student_group_check.data.groups[student_group_row].course_excess_count
                    : 0;
                const new_grp_ind = session_type_ind.groups.findIndex(
                    (i) => i.group_no.toString() === grp_nos.toString(),
                );
                if (new_grp_ind === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('GROUP_NO_NOT_FOUND'),
                                req.t('GROUP_NO_NOT_FOUND'),
                            ),
                        );
                strength =
                    session_type_ind.groups[new_grp_ind]._student_ids.length +
                    req.body._student_ids.length;
                if (!(strength <= excess + capacity))
                    validation.push({
                        session_type: element,
                        message: 'capacity exceeded increase extra allowed',
                    });
            }
            if (validation.length !== 0)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                            validation,
                        ),
                    );

            for (element of req.body.delivery_group) {
                objs = {
                    $push: {
                        'groups.$[i].courses.$[m].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                            req.body._student_ids,
                    },
                    $pull: {
                        'groups.$[i].courses.$[m].setting.$[j].ungrouped': {
                            $in: req.body._student_ids,
                        },
                    },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'm._course_id': req.body._course_id },
                        { 'j.gender': req.query.mode ? req.query.mode : req.body.gender },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': element.group_no },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }

            await addingStudentSchedule({
                _institution_id: student_group_check.data._institution_id,
                institutionCalendarId: student_group_check.data._institution_calendar_id,
                studentGroup:
                    student_group_check.data.groups[student_group_row].courses[course_loc].setting[
                        group_no
                    ],
                studentData: student_group_check.data.groups[student_group_row].students,
                studentIds: req.body._student_ids,
                deliveryGroups: req.body.delivery_group,
                courseId: req.body._course_id,
                status: req.body.status,
            });
        }
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(req, 410, false, doc.data, doc.data),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (req.body._student_ids && req.body._student_ids.length) {
            await updateUserCourseRedisKey({
                studentIds: req.body._student_ids,
            });
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_grouping = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );

        let doc = { status: true, data: [] };
        let male_groups = 0;
        let female_groups = 0;
        let len = 0;
        const rotation_group_setting =
            student_group_data.data.groups[group_loc].rotation_group_setting;
        if (req.body.mode === 'auto') {
            rotation_group_setting.forEach((element) => {
                if (element.gender === constant.GENDER.MALE) {
                    male_groups++;
                } else {
                    female_groups++;
                }
            });
            if (req.body.gender === constant.GENDER.MALE) len = male_groups;
            else len = female_groups;
            const student = [];
            let sorted = [];
            const calc = [];
            let index = 0;
            let status;
            req.body._student_ids.forEach((element) => {
                const std_data =
                    student_group_data.data.groups[group_loc].students[
                        student_group_data.data.groups[group_loc].students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ];
                if (std_data) student.push(std_data);
            });
            switch (req.body.method) {
                case 'cluster':
                    console.log('cluster');
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'academic_id':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.academic_no) > parseInt(b.academic_no)) {
                            comparison = -1;
                        } else if (parseInt(a.academic_no) < parseInt(b.academic_no)) {
                            comparison = 1;
                        }
                        return comparison * -1;
                    });
                    break;
                case 'mark':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'name':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
                default:
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
            }
            if (req.body.method !== 'cluster') {
                console.log('Non cluster');
                male_groups = 0;
                female_groups = 0;
                student_group_data.data.groups[group_loc].rotation_group_setting.forEach(
                    (element) => {
                        if (element.gender === constant.GENDER.MALE) {
                            male_groups++;
                        } else {
                            female_groups++;
                        }
                    },
                );
                const grouping_student_count = req.body._student_ids.length;
                let per_group;
                if (req.body.gender === constant.GENDER.MALE) {
                    per_group = grouping_student_count / male_groups;
                    len = male_groups;
                } else {
                    per_group = grouping_student_count / female_groups;
                    len = female_groups;
                }
                if (req.query.mode) {
                    per_group =
                        grouping_student_count /
                        student_group_data.data.groups[group_loc].rotation_count;
                    len = student_group_data.data.groups[group_loc].rotation_count;
                }
                let pos = 0;
                let last = 0;
                let last_know = grouping_student_count;
                for (element of rotation_group_setting) {
                    // rotation_group_setting.forEach((element, index) => {
                    if (
                        req.query.mode
                            ? req.query.mode === element.gender
                            : element.gender === req.body.gender
                    ) {
                        let count = 0;
                        if (parseInt(per_group) * len < last_know) {
                            last_know -= Math.ceil(per_group);
                            count = Math.ceil(per_group);
                        } else {
                            last_know -= parseInt(per_group);
                            count = parseInt(per_group);
                        }
                        pos += count;
                        cut_std = sorted.slice(last, pos);
                        last = pos;
                        const old_std = rotation_group_setting[index]._student_ids;
                        const std_ids = cut_std.map((dat) => {
                            return ObjectId(dat._student_id);
                        });
                        const new_std = old_std.concat(std_ids);
                        if (element.no_of_student < new_std.length) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        } else {
                            rotation_group_setting[index]._student_ids = new_std;
                            status = true;
                        }
                    }
                    // });
                    index++;
                }
            } else {
                rotation_group_setting.forEach((element) => {
                    if (
                        req.query.mode
                            ? req.query.mode === element.gender
                            : element.gender === req.body.gender
                    ) {
                        let old_mark = 0;
                        element._student_ids.forEach((sub_element) => {
                            old_mark += parseFloat(
                                student_group_data.data.groups[group_loc].students[
                                    student_group_data.data.groups[group_loc].students.findIndex(
                                        (i) => i._student_id.toString() === sub_element.toString(),
                                    )
                                ].mark,
                            );
                        });
                        calc.push({
                            group: element.group_no,
                            gender: element.gender,
                            mark: old_mark,
                            students: Array.from(element._student_ids),
                        });
                    }
                });
                for (element of sorted) {
                    // sorted.forEach((element, index) => {
                    const val = calc.map((i) => i.mark);
                    const low_pos = calc[calc.findIndex((i) => i.mark === Math.min(...val))].group;
                    if (low_pos !== -1) {
                        calc[
                            calc.findIndex(
                                (i) =>
                                    i.group === low_pos &&
                                    (req.query.mode
                                        ? req.query.mode === i.gender
                                        : i.gender === req.body.gender),
                            )
                        ].students.push(element._student_id);
                        calc[
                            calc.findIndex(
                                (i) =>
                                    i.group === low_pos &&
                                    (req.query.mode
                                        ? req.query.mode === i.gender
                                        : i.gender === req.body.gender),
                            )
                        ].mark += parseFloat(element.mark);
                        rotation_group_setting[
                            rotation_group_setting.findIndex(
                                (i) =>
                                    i.group_no.toString() === low_pos.toString() &&
                                    (req.query.mode
                                        ? req.query.mode === i.gender
                                        : i.gender === req.body.gender),
                            )
                        ]._student_ids.push(element._student_id);
                        status = true;
                    }
                    if (
                        rotation_group_setting[
                            rotation_group_setting.findIndex(
                                (i) =>
                                    i.group_no.toString() === low_pos.toString() &&
                                    (req.query.mode
                                        ? req.query.mode === i.gender
                                        : i.gender === req.body.gender),
                            )
                        ].no_of_student <
                        rotation_group_setting[
                            rotation_group_setting.findIndex(
                                (i) =>
                                    i.group_no.toString() === low_pos.toString() &&
                                    (req.query.mode
                                        ? req.query.mode === i.gender
                                        : i.gender === req.body.gender),
                            )
                        ]._student_ids.length
                    ) {
                        status = false;
                        data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                        break;
                        // return res.status(410).send(common_files.response_function(res, 410, false, "Groups Capacity is Exceeded", 'Groups Capacity is Exceeded'));
                    }
                    // });
                    index++;
                }
            }
            if (status) {
                objs = {
                    $pull: { 'groups.$[k].ungrouped': { $in: req.body._student_ids } },
                    $set: { 'groups.$[k].rotation_group_setting': rotation_group_setting },
                };
                filter = {
                    arrayFilters: [{ 'k.term': req.body.batch, 'k.level': req.body.level }],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data };
            }
        } else if (req.body.mode === 'manual') {
            const setting_loc = student_group_data.data.groups[
                group_loc
            ].rotation_group_setting.findIndex(
                (i) =>
                    (req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender) &&
                    i.group_no.toString() === req.body.group_no.toString(),
            );
            if (setting_loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                            req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                        ),
                    );
            let excess = 0;
            let capacity = 0;
            let strength = 0;
            excess = student_group_data.data.groups[group_loc].group_excess_count
                ? student_group_data.data.groups[group_loc].group_excess_count
                : 0;
            capacity =
                student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                    .no_of_student;
            strength =
                student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                    ._student_ids.length + req.body._student_ids.length;
            if (!(strength <= excess + capacity))
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                            req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                        ),
                    );
            objs = {
                $pull: { 'groups.$[k].ungrouped': { $in: req.body._student_ids } },
                $push: {
                    'groups.$[k].rotation_group_setting.$[i]._student_ids': req.body._student_ids,
                },
            };
            filter = {
                arrayFilters: [
                    { 'k.term': req.body.batch, 'k.level': req.body.level },
                    {
                        'i.gender': req.query.mode ? req.query.mode : req.body.gender,
                        'i.group_no': req.body.group_no,
                    },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(req, 410, false, doc.data, doc.data),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_grouping = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );

        let doc = { status: true, data: [] };
        if (req.body.mode === 'auto') {
            const course_pos = student_group_data.data.courses.findIndex(
                (i) => i._course_id.toString() === req.body._course_id.toString(),
            );
            const settings =
                student_group_data.data.courses[course_pos].setting[
                    student_group_data.data.courses[course_pos].setting.findIndex(
                        (i) => i.gender === req.body.gender && i._group_no === req.body.group_no,
                    )
                ];
            const student = [];
            let sorted = [];
            let calc = [];
            let filter;
            let status = true;
            let data = '';
            req.body._student_ids.forEach((element) => {
                const std_data =
                    student_group_data.data.students[
                        student_group_data.data.students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ];
                student.push(std_data);
            });
            switch (req.body.method) {
                case 'cluster':
                    console.log('cluster');
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'academic_id':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.academic_no) > parseInt(b.academic_no)) {
                            comparison = -1;
                        } else if (parseInt(a.academic_no) < parseInt(b.academic_no)) {
                            comparison = 1;
                        }
                        return comparison * -1;
                    });
                    break;
                case 'mark':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (parseFloat(a.mark) > parseFloat(b.mark)) {
                            comparison = -1;
                        } else if (parseFloat(a.mark) < parseFloat(b.mark)) {
                            comparison = 1;
                        }
                        return comparison;
                    });
                    break;
                case 'name':
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
                    break;
                default:
                    sorted = student.sort((a, b) => {
                        let comparison = 0;
                        if (a.name.first.toUpperCase() > b.name.first.toUpperCase()) {
                            comparison = 1;
                        } else if (a.name.first.toUpperCase() < b.name.first.toUpperCase()) {
                            comparison = -1;
                        }
                        return comparison;
                    });
            }
            if (req.body.method !== 'cluster') {
                console.log('Non cluster');
                const grouping_student_count = req.body._student_ids.length;
                settings.session_setting.forEach((element, master_index) => {
                    const per_group = grouping_student_count / element.no_of_group;
                    let pos = 0;
                    let last = 0;
                    let last_know = grouping_student_count;
                    element.groups.forEach((sub_element, index) => {
                        let count = 0;
                        if (parseInt(per_group) * (element.groups.length - index) < last_know) {
                            last_know -= Math.ceil(per_group);
                            count = Math.ceil(per_group);
                        } else {
                            last_know -= parseInt(per_group);
                            count = parseInt(per_group);
                        }
                        pos += count;
                        cut_std = sorted.slice(last, pos);
                        last = pos;
                        const old_std = element.groups[index]._student_ids;
                        const std_ids = cut_std.map((dat) => {
                            return ObjectId(dat._student_id);
                        });
                        const new_std = old_std.concat(std_ids);
                        if (element.no_of_student < new_std.length) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        } else {
                            settings.session_setting[master_index].groups[index]._student_ids =
                                new_std;
                            status = true;
                        }
                    });
                });
                // status = true;
                data = settings;
            } else {
                settings.session_setting.forEach((master_element, master_index) => {
                    const group_len = master_element.groups.length;
                    calc = [];
                    master_element.groups.forEach((element) => {
                        let old_mark = 0;
                        element._student_ids.forEach((sub_element) => {
                            old_mark += parseFloat(
                                student_group_data.data.students[
                                    student_group_data.data.students.findIndex(
                                        (i) => i._student_id.toString() === sub_element.toString(),
                                    )
                                ].mark,
                            );
                        });
                        calc.push({
                            group: element.group_no,
                            mark: old_mark,
                            students: Array.from(element._student_ids),
                        });
                    });
                    for (element of sorted) {
                        // sorted.forEach((element, index) => {
                        const val = calc.map((i) => i.mark);
                        const low_pos = calc.findIndex((i) => i.mark === Math.min(...val));
                        if (low_pos !== -1 && group_len > low_pos) {
                            calc[low_pos].students.push(element._student_id);
                            calc[low_pos].mark += parseFloat(element.mark);
                            settings.session_setting[master_index].groups[
                                low_pos
                            ]._student_ids.push(element._student_id);
                            status = true;
                        }
                        if (
                            master_element.no_of_student <
                            settings.session_setting[master_index].groups[low_pos]._student_ids
                                .length
                        ) {
                            status = false;
                            data = 'Student Capacity Exceeded cannot do automatic, go with Manual';
                            break;
                            // return res.status(410).send(common_files.response_function(res, 410, false, "Student Capacity Exceeded cannot do automatic, go with Manual", 'Student Capacity Exceeded cannot do automatic, go with Manual'));
                        }
                        // });
                    }
                });
                data = settings;
            }
            if (status) {
                settings.ungrouped = settings.ungrouped.filter(
                    (item) => !req.body._student_ids.includes(item.toString()),
                );
                objs = {
                    $set: { 'courses.$[i].setting.$[j]': settings },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        { 'j._group_no': req.body.group_no, 'j.gender': req.body.gender },
                    ],
                };
                // doc = { status: true, data: settings }
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data };
            }
        } else if (req.body.mode === 'manual') {
            req.body.delivery_group.forEach(async (element, index) => {
                objs = {
                    $push: {
                        'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                            req.body._student_ids,
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': element.group_no },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
                if (req.body.delivery_group.length === index + 1) {
                    objs = {
                        $pull: {
                            'courses.$[i].setting.$[j].ungrouped': { $in: req.body._student_ids },
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i._course_id': req.body._course_id },
                            { 'j._group_no': req.body.master_group },
                        ],
                    };
                    doc = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }
            });
        }
        if (!doc.status)
            return res
                .status(410)
                .send(common_files.response_function(res, 410, false, doc.data, doc.data));
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
