const userSchemas = require('../../models/user');
const institutionSchemas = require('../../models/institution');
const faceRegisterSchemas = require('../../models/faceRegister');
const courseScheduleSchema = require('../../models/course_schedule');
const {
    convertToMongoObjectId,
    sendResponse,
    list_all_response,
    listAllResponseWithRequest,
} = require('../../utility/common');
const { getSignedURL, nameFormatter, send_email } = require('../../utility/common_functions');
const {
    SERVICES: { REACT_APP_INSTITUTION_ID },
} = require('../../utility/util_keys');
const {
    LEAVE_FLOW_TYPE_STATUS: { PENDING, APPROVED, REJECTED },
} = require('../../utility/constants');
const { digiAuthService } = require('../../../digi-auth');
const { sendNotificationPush } = require('../../../service/pushNotification.service');

const reRegisterFace = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { scheduleId, userId, userType, center, left, right, up, faceDescriptors } = req.body;
        const faceRegister = await faceRegisterSchemas.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            scheduleId: convertToMongoObjectId(scheduleId),
            userId: convertToMongoObjectId(userId),
            userType,
            faceURL: center,
            faceURLs: [center, left, right, up],
            descriptors: JSON.parse(faceDescriptors),
        });
        if (faceRegister) {
            await courseScheduleSchema.updateMany(
                {
                    $or: [
                        { _id: convertToMongoObjectId(scheduleId) },
                        { 'merge_with.schedule_id': convertToMongoObjectId(scheduleId) },
                    ],
                },
                {
                    $set: {
                        startWithOutFace: true,
                        startWithOutFaceStaffId: convertToMongoObjectId(userId),
                    },
                },
            );
            return sendResponse(res, 200, true, 'Face Uploaded');
        }
        return sendResponse(res, 410, false, 'Face Not Uploaded');
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getBucketData = async ({ body = {} }) => {
    try {
        const { userId, passCode, faceId } = body;
        const passCodeData = await institutionSchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(REACT_APP_INSTITUTION_ID),
                    facePassKey: passCode,
                },
                { _id: 1 },
            )
            .lean();
        if (!passCode || !passCodeData) return { statusCode: 410, message: 'PassKey Not Matching' };
        let userOldPhoto;
        if (userId) {
            const userDetail = await userSchemas
                .findOne(
                    { _id: convertToMongoObjectId(userId) },
                    { 'biometric_data.face': { $arrayElemAt: ['$biometric_data.face', 0] } },
                )
                .lean();
            userOldPhoto = await getSignedURL(userDetail.biometric_data.face);
        }
        let signedURLs = [];
        if (faceId) {
            const registerFaceData = await faceRegisterSchemas
                .findOne(
                    {
                        _id: convertToMongoObjectId(faceId),
                    },
                    { faceURLs: 1 },
                )
                .lean();
            signedURLs = await Promise.all(
                registerFaceData?.faceURLs.map((faceUrlElement) => getSignedURL(faceUrlElement)),
            );
        }
        return {
            statusCode: 200,
            message: 'SingedURL',
            data: { userOldPhoto, signedURLs },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const faceRegisterUserList = async (req, res) => {
    try {
        const { filter, limit, pageNo, statusFilter, userType } = req.query;
        const skips = Number(limit * (pageNo - 1));
        const limits = Number(limit) || 0;
        const filterAggregate = [
            { $match: { isDeleted: false, ...(userType && { userType }) } },
            { $sort: { _id: -1 } },
            {
                $group: {
                    _id: '$userId',
                    status: { $first: '$status' },
                    scheduleIds: { $push: '$scheduleId' },
                    faceRegisterId: { $first: '$_id' },
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'userData',
                    pipeline: [{ $project: { name: 1, user_id: 1, email: 1, gender: 1 } }],
                },
            },
        ];
        if (statusFilter && statusFilter.length)
            filterAggregate.push({ $match: { status: { $in: statusFilter } } });
        if (filter) {
            const userRegex = new RegExp(filter, 'i');
            filterAggregate.push({
                $match: {
                    $or: [
                        { 'userData.name.first': userRegex },
                        { 'userData.name.middle': userRegex },
                        { 'userData.name.last': userRegex },
                        { 'userData.email': userRegex },
                        { 'userData.user_id': userRegex },
                    ],
                },
            });
        }
        filterAggregate.push(
            { $sort: { faceRegisterId: -1 } },
            {
                $facet: {
                    totalCount: [{ $count: 'value' }],
                    pipelineResults: [
                        {
                            $project: {
                                _id: '$_id',
                                status: '$status',
                                userData: '$userData',
                                scheduleCount: { $size: '$scheduleIds' },
                            },
                        },
                    ],
                },
            },
            { $unwind: { path: '$totalCount' } },
            { $unwind: { path: '$pipelineResults' } },
            { $unwind: { path: '$pipelineResults.userData' } },
        );
        if (skips || limits) {
            filterAggregate.push({ $skip: skips });
            filterAggregate.push({ $limit: limits });
        }
        const userFaceList = await faceRegisterSchemas.aggregate(filterAggregate);
        const responseData = userFaceList.map((userFaceElement) => ({
            ...userFaceElement.pipelineResults,
        }));
        const totalPages = userFaceList.length
            ? Math.ceil(userFaceList[0].totalCount.value / Number(limit) || 0)
            : 0;
        return listAllResponseWithRequest(
            req,
            res,
            200,
            true,
            'userList',
            userFaceList.length ? userFaceList[0].totalCount.value : 0,
            totalPages,
            Number(pageNo),
            responseData,
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userBasedScheduleList = async ({ query = {} }) => {
    try {
        const { userId } = query;
        const userBasedDatas = await faceRegisterSchemas
            .find(
                { isDeleted: false, userId: convertToMongoObjectId(userId) },
                {
                    scheduleId: 1,
                    status: 1,
                    faceURL: 1,
                    reasonToReject: 1,
                },
            )
            .sort({ _id: -1 })
            .populate({
                path: 'scheduleId',
                select: {
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_topic': 1,
                    type: 1,
                    title: 1,
                    program_name: 1,
                    _program_id: 1,
                    course_name: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    mode: 1,
                    infra_name: 1,
                    rotation_count: 1,
                    topic: 1,
                    merge_status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                },
                populate: {
                    path: 'merge_with.schedule_id',
                    select: {
                        'session.delivery_symbol': 1,
                        'session.delivery_no': 1,
                    },
                },
            })
            .lean();
        const userDetail = await userSchemas.findOne(
            { _id: convertToMongoObjectId(userId) },
            {
                name: 1,
                user_id: 1,
                email: 1,
                gender: 1,
                'biometric_data.face': { $arrayElemAt: ['$biometric_data.face', 0] },
            },
        );
        return {
            statusCode: 200,
            message: 'User Based Schedule & Details List',
            data: { userDetail, userBasedDatas },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const faceRegisterApproval = async ({ body = {} }) => {
    try {
        const { userId, status, reasonToReject, faceId } = body;
        const userDetail = await userSchemas
            .findOne(
                { _id: convertToMongoObjectId(userId) },
                {
                    email: 1,
                    name: 1,
                    'biometric_data.face': 1,
                    user_id: 1,
                    fcm_token: 1,
                    device_type: 1,
                },
            )
            .lean();
        const userReRegisterFaceData = await faceRegisterSchemas
            .findOne(
                {
                    isDeleted: false,
                    userId: convertToMongoObjectId(userId),
                    _id: convertToMongoObjectId(faceId),
                },
                {
                    faceURLs: 1,
                    descriptors: 1,
                },
            )
            .lean();
        if (!userReRegisterFaceData)
            return {
                statusCode: 410,
                message: 'Unable to Set Status',
            };
        let faceStatusContent = `Dear ${nameFormatter(userDetail.name)},<br>`;
        switch (status) {
            case APPROVED:
                {
                    faceStatusContent += ` Your Face Update Application has been Approved Successfully`;
                    await userSchemas.updateOne(
                        { _id: convertToMongoObjectId(userId) },
                        {
                            $set: {
                                'biometric_data.face': userReRegisterFaceData.faceURLs,
                                originalRegisteredFace: userReRegisterFaceData.faceURLs[0],
                            },
                        },
                    );
                    const facialTrained = userReRegisterFaceData.faceURLs.map((faceUrl, index) => ({
                        bucketUrl: faceUrl,
                        labeledFaceDescriptors: {
                            label: faceUrl.split('/').pop(),
                            descriptors: [userReRegisterFaceData.descriptors[index]?.descriptor],
                        },
                        trainedAt: new Date(),
                    }));
                    // Sync User Face to DigiAuth verification
                    digiAuthService
                        .syncUser({
                            employeeOrAcademicId: userDetail.user_id,
                            facial: userReRegisterFaceData.faceURLs,
                            userId: userDetail._id,
                            facialTrained,
                        })
                        .then((updatedUser) => {
                            console.info('Auth User Facial Updated');
                        })
                        .catch((err) => {
                            console.error(err, ' Error User Facial Update');
                        });
                    // Send Notification to User Device for Update Face data in Local
                    const userPushNotificationData = {
                        Title: 'Your Face Registration has been Approved',
                        ClickAction: 're_register',
                    };
                    sendNotificationPush(
                        userDetail.fcm_token,
                        userPushNotificationData,
                        userDetail.device_type,
                    );
                }
                break;
            case REJECTED:
                faceStatusContent += ` Your Face Update Application has been Rejected,<br> ${reasonToReject}`;
                break;
            default:
                break;
        }
        await send_email(userDetail.email, 'Digi Class Facial Update', faceStatusContent);
        const statusUpdate = await faceRegisterSchemas.updateMany(
            {
                isDeleted: false,
                userId: convertToMongoObjectId(userId),
                status: PENDING,
            },
            {
                $set: { status, reasonToReject },
            },
        );
        if (!statusUpdate || (statusUpdate && statusUpdate.nModified))
            return {
                statusCode: 410,
                message: 'Unable to Set Status',
            };
        return {
            statusCode: 200,
            message: 'Face Re-Register Status Updated',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    reRegisterFace,
    getBucketData,
    faceRegisterUserList,
    userBasedScheduleList,
    faceRegisterApproval,
};
