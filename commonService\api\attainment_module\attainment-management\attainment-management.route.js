const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    getSettingAssessmentNode,
    getAttainmentList,
    getAttainmentPrerequisite,
    addAttainment,
    editAttainment,
    removeAttainment,
    getAttainment,
    addAttainmentNode,
    editAttainmentNode,
    deleteAttainmentNode,
    attainmentLevel,
    getCourseAttainment,
} = require('./attainment-management.controller');

router.get('/assessment-node', catchAsync(getSettingAssessmentNode));
router.get('/attainment-list', catchAsync(getAttainmentList));
router.get('/attainment-prerequisite', catchAsync(getAttainmentPrerequisite));
router.post('/add-attainment', catchAsync(addAttainment));
router.put('/edit-attainment', catchAsync(editAttainment));
router.delete('/delete-attainment', catchAsync(removeAttainment));
router.get('/get-attainment', catchAsync(getAttainment));
router.post('/add-node', catchAsync(addAttainmentNode));
router.put('/edit-node', catchAsync(editAttainmentNode));
router.delete('/delete-node', catchAsync(deleteAttainmentNode));

// Course Attainments
router.get('/get-course-attainment', catchAsync(getCourseAttainment));

// Attainment Level Feeding
router.post('/attainment-level', catchAsync(attainmentLevel));

module.exports = router;
