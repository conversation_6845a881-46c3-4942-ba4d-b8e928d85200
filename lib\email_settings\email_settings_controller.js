const emailContentSchema = require('../models/email_content');

exports.createNewEmailContent = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, content } = query;
        const originalDocContent = {
            _institution_id,
            type,
            content,
            isOriginal: true,
        };
        const isEmailDocAlreadyPresent = await emailContentSchema.findOne(originalDocContent);
        if (!isEmailDocAlreadyPresent) {
            const currentDocContent = { ...originalDocContent, isOriginal: false };
            const createdDocs = await emailContentSchema.insertMany([
                originalDocContent,
                currentDocContent,
            ]);
            return { statusCode: 200, message: 'success' };
        }
        return { statusCode: 200, message: 'Data already exists' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getEmailContent = async ({ headers = {}, query = {} }) => {
    try {
        const { type } = query;
        const { _institution_id } = headers;

        const emailContent = await emailContentSchema
            .findOne({ type, _institution_id, isOriginal: false }, { content: 1 })
            .lean();
        return { statusCode: 200, message: 'success', data: emailContent };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateEmailContent = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, content } = query;
        const updatedEmailContent = await emailContentSchema.findOneAndUpdate(
            { _institution_id, type, isOriginal: false },
            { content },
        );
        if (updatedEmailContent) {
            return { statusCode: 200, message: 'updated successfully' };
        }
        return { statusCode: 200, message: 'update failed' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.resetEmailContent = async ({ headers = {}, query = {} }) => {
    const { _institution_id } = headers;
    const { type } = query;
    try {
        const originalEmailContent = await emailContentSchema
            .findOne({ _institution_id, type, isOriginal: true }, { content: 1 })
            .lean();

        if (!originalEmailContent) {
            return { statusCode: 200, message: 'No data found to reset email content' };
        }

        const updatedCurrentContent = await emailContentSchema.updateOne(
            { _institution_id, type, isOriginal: false },
            { content: originalEmailContent.content },
        );
        return { statusCode: 200, message: 'Reset Successful' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
