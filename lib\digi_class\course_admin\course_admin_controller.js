const course = require('../../models/digi_course');
const Document = require('../../models/document_manager');
const Activity = require('../../models/activities');
const session_order = require('../../models/digi_session_order');
const base_control = require('../../base/base_controller');
const common_files = require('../../utility/common');
const CourseSchedule = require('../../models/course_schedule');
const getJSON = base_control.dsGetAllWithSortAsJSON;
const {
    getAllCourses,
    getCourse,
    getCourseDocuments,
    getScheduleById,
    getScheduleByDate,
    getCoursesByStaffId,
    getSloBySessionIdAndCourseId,
    getStudentCourseIds,
    getRatingBySessions,
    getInfraById,
    getCourses,
} = require('./course_admin_service');
const {
    sendResponse,
    convertToMongoObjectId,
    cs,
    query,
    sendResponseWithRequest,
} = require('../../utility/common');
const {
    timestampNow,
    toObjectId,
    convertingRiyadhToUTC,
} = require('../../utility/common_functions');
const {
    INVALID_ID,
    DS_UPDATE_FAILED,
    DS_UPDATED,
    DS_DATA_RETRIEVED,
    SCHEDULE_TYPES: { REGULAR, EVENT, SUPPORT_SESSION },
    PUBLISHED,
} = require('../../utility/constants');
const { get, dsGetAllWithSortAsJSON, bulk_write } = require('../../base/base_controller');
const {
    getCoursesSessionList,
    getScheduleBySessionOrScheduleList,
} = require('./courseAdmin_service');
const { logger } = require('../../utility/util_keys');
const { PM } = require('../../utility/enums');

exports.getDropdownSchedule = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const query = {
            _course_id: convertToMongoObjectId(courseId),
            $or: [
                { 'session._session_id': convertToMongoObjectId(sessionId) },
                { _id: convertToMongoObjectId(sessionId) },
            ],
            isActive: true,
            isDeleted: false,
        };
        const project = {};

        const courseSchedules = (await getJSON(CourseSchedule, query, project)).data;
        if (!courseSchedules.length)
            sendResponseWithRequest(req, res, 200, true, req.t('SCHEDULE_NOT_FOUND'), null);
        const sessions = [];
        for (const courseSchedule of courseSchedules) {
            const { student_groups, _id, merge_status, merge_with } = courseSchedule;
            if (student_groups && student_groups.length && !merge_status) {
                let studentGroups = student_groups.map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                });
                studentGroups = studentGroups.toString();
                sessions.push({ _id, studentGroupNames: studentGroups });
            }
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.student_groups);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                    if (scheduleStudents && scheduleStudents.length) {
                        scheduleStudents = scheduleStudents.concat(student_groups);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    }
                    let studentGroups = scheduleStudents.map((student_group) => {
                        const { group_name, session_group } = student_group;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((groupNameEntry) => {
                                let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    });

                    studentGroups = studentGroups.toString();
                    sessions.push({ _id, studentGroupNames: studentGroups });
                }
            }
        }
        sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), sessions);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.course_session_flow_list = async (req, res) => {
    try {
        const cond = {
            _course_id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        const proj = {};
        const doc = await base_control.get(session_order, cond, proj);
        const delivery_types = [
            ...new Set(doc.data.session_flow_data.map((item) => item.delivery_type)),
        ];
        // delivery_types = [...new Set(delivery_types)]
        const data_doc = [];
        delivery_types.forEach((element) => {
            const data = doc.data.session_flow_data.filter(
                (item) => item.delivery_type === element,
            );
            const session = [];
            data.forEach((flow) => {
                session.push({
                    _id: flow._id,
                    delivery_symbol: flow.delivery_symbol,
                    _session_id: flow._session_id,
                    delivery_topic: flow.delivery_topic,
                    delivery_type: flow.delivery_type,
                    delivery_no: flow.delivery_no,
                    s_no: flow.s_no,
                    duration: flow.duration,
                    document_count: 3,
                    activity_count: 6,
                    session_date: '2021-01-23T00:00:00.000Z',
                    start_time: '2021-01-23T03:30:00.000Z',
                    end_time: '2021-01-23T05:30:00.000Z',
                    status: 'pending',
                });
            });
            data_doc.push({
                delivery_type: element,
                count: data.length,
                rating: 4.3,
                session_status: {
                    ongoing: 23,
                    total: 52,
                },
                assignment: 5,
                session_flow: session,
            });
        });

        if (!doc.status) return sendResponse(res, 200, false, req.t('NO_COURSE_FOUND'), []);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), data_doc);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.document_course_session_flow_list = async (req, res) => {
    try {
        const aggre = [
            {
                $match: {
                    course_assigned_details: {
                        $exists: true,
                        $ne: [],
                    },
                    isDeleted: false,
                    isActive: true,
                },
            },
            { $unwind: { path: '$course_assigned_details', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: '$_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    year: { $first: '$course_assigned_details.year' },
                    level: { $first: '$course_assigned_details.level_no' },
                    program: { $first: '$course_assigned_details.program_name' },
                },
            },

            {
                $lookup: {
                    from: 'digi_session_orders',
                    localField: '_id',
                    foreignField: '_course_id',
                    as: 'session_flow',
                },
            },
            { $unwind: { path: '$session_flow', preserveNullAndEmptyArrays: true } },
            {
                $unwind: {
                    path: '$session_flow.session_flow_data',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    session_flows: {
                        _id: '$session_flow.session_flow_data._id',
                        delivery_topic: '$session_flow.session_flow_data.delivery_topic',
                    },
                },
            },
            {
                $group: {
                    _id: '$_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    year: { $first: '$year' },
                    level: { $first: '$level' },
                    program: { $first: '$program' },
                    session_flows: { $push: '$session_flows' },
                },
            },
        ];
        const doc = await base_control.get_aggregate(course, aggre);
        if (!doc.status) return sendResponse(res, 200, false, req.t('NO_COURSE_FOUND'), []);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), doc.data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.course = async (req, res) => {
    try {
        const { userId, courseId } = req.params;
        const {
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            rotation,
            term,
            rotationCount,
            type,
        } = req.query;
        const courses = courseId
            ? await getCourse(
                  userId,
                  courseId,
                  institutionCalendarId,
                  programId,
                  yearNo,
                  levelNo,
                  term,
                  rotation,
                  rotationCount,
                  type,
              )
            : await getAllCourses(userId, institutionCalendarId);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), courses);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.courseSessionFlow = async (req, res) => {
    try {
        const cond = {
            _course_id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        const proj = {};
        const doc = await base_control.get(session_order, cond, proj);
        const deliveryTypes = [
            ...new Set(doc.data.session_flow_data.map((item) => item.delivery_type)),
        ];
        const sessionOrders = [];
        deliveryTypes.forEach((element) => {
            const data = doc.data.session_flow_data.filter(
                (item) => item.delivery_type === element,
            );
            const session = [];
            data.forEach((flow) => {
                session.push({
                    _id: flow._id,
                    delivery_symbol: flow.delivery_symbol,
                    _session_id: flow._session_id,
                    delivery_topic: flow.delivery_topic,
                    delivery_type: flow.delivery_type,
                    delivery_no: flow.delivery_no,
                    s_no: flow.s_no,
                    duration: flow.duration,
                    document_count: 3,
                    activity_count: 6,
                    session_date: '2021-01-23T00:00:00.000Z',
                    start_time: '2021-01-23T03:30:00.000Z',
                    end_time: '2021-01-23T05:30:00.000Z',
                    status: 'pending',
                });
            });
            sessionOrders.push({
                delivery_type: element,
                count: data.length,
                rating: 4.3,
                session_status: {
                    ongoing: 23,
                    total: 52,
                },
                assignment: 5,
                session_flow: session,
            });
        });
        const sessionFlows = sessionOrders.map((dd) => dd.session_flow);
        // eslint-disable-next-line no-sequences
        const result = sessionFlows.reduce((r, e) => (r.push(...e), r), []);
        if (!doc.status)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_COURSE_FOUND'), []);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), result);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.courseSessionFlowSlo = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const slos = await getSloBySessionIdAndCourseId(courseId, sessionId);
        if (slos.length === 0)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_SLOS_FOUND'), []);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), slos);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getStudentDocuments = async (req, res) => {
    try {
        const { studentId } = req.params;
        const courseIds = await getStudentCourseIds(convertToMongoObjectId(studentId));
        const dQuery = { isDeleted: false, isActive: true, _course_id: { $in: courseIds } };
        const dProject = {
            type: 1,
            name: 1,
            url: 1,
            _course_id: 1,
            _session_flow_id: 1,
            shared: 1,
        };
        const response = (await getJSON(Document, dQuery, dProject)).data;
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseDocuments = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const response = await getCourseDocuments(courseId, sessionId);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSessions = async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            courseId,
            sessionId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            type,
            rotation,
            rotationCount,
            mergedStatus,
            scheduleId,
        } = req.query;
        const courses = await getCourse(
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            rotation,
            rotationCount,
            '',
            true,
        );
        let courseType;
        const cResult = await get(course, { _id: convertToMongoObjectId(courseId) }, {});
        if (cResult.status) courseType = cResult.data.course_type;
        const response = [];
        let allSchedules = courses.map((course) =>
            course.sessions.map((session) => {
                session.schedules.forEach((schedule) => {
                    schedule.courseType = courseType;
                });
                return session.schedules;
            }),
        );
        // eslint-disable-next-line prefer-spread
        allSchedules = [].concat.apply([], [].concat.apply([], allSchedules));
        if (courses.length) {
            courses.forEach((c) => {
                c.sessions.forEach((session) => {
                    let sessionSchedules = session.schedules;
                    sessionSchedules = sessionSchedules.filter(
                        (sessionSchedule) =>
                            sessionSchedule.merge_status.toString() === mergedStatus,
                    );
                    if (
                        session._session_id &&
                        sessionId &&
                        session._session_id.toString() === sessionId.toString()
                    ) {
                        if (mergedStatus === 'true') {
                            sessionSchedules = sessionSchedules.filter(
                                (sessionSchedule) =>
                                    sessionSchedule._id.toString() === scheduleId.toString(),
                            );
                        }
                        sessionSchedules = sessionSchedules.map((sessionSchedule) => {
                            sessionSchedule.program_name = c.program_name;
                            if (sessionSchedule.merge_status) {
                                const mergedSessions = sessionSchedule.merge_with.map(
                                    (mergeWith) => {
                                        const sessionDetails = allSchedules.find(
                                            (courseScheduleEntry) =>
                                                courseScheduleEntry._id.toString() ===
                                                mergeWith.schedule_id.toString(),
                                        );
                                        if (sessionDetails) {
                                            mergeWith.session = {
                                                _session_id: sessionDetails.session._session_id,
                                                s_no: sessionDetails.session.s_no,
                                                delivery_symbol:
                                                    sessionDetails.session.delivery_symbol,
                                                delivery_no: sessionDetails.session.delivery_no,
                                            };
                                        }
                                        return mergeWith;
                                    },
                                );
                                sessionSchedule.merge_with = mergedSessions;
                            }
                            return sessionSchedule;
                        });
                        if (sessionSchedules.length) {
                            response.push({
                                _session_id: session._session_id,
                                delivery_symbol: session.delivery_symbol,
                                delivery_no: session.delivery_no,
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                documentCount: session.documentCount,
                                activityCount: session.activityCount,
                                isActive: c.isActive,
                                isDeleted: c.isDeleted,
                                schedules: sessionSchedules,
                                program_name: c.program_name,
                                level: c.level,
                                year: c.year,
                            });
                        }
                    } else if (session.session_type && type && session.session_type === type) {
                        let sessionSchedules = session.schedules;
                        sessionSchedules = sessionSchedules.map((sessionSchedule) => {
                            sessionSchedule.program_name = c.program_name;
                            return sessionSchedule;
                        });
                        sessionSchedules = sessionSchedules.filter(
                            (sessionSchedule) => sessionSchedule._id.toString() === scheduleId,
                        );
                        if (sessionSchedules.length) {
                            response.push({
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                documentCount: session.documentCount,
                                activityCount: session.activityCount,
                                schedules: sessionSchedules,
                                program_name: c.program_name,
                                level: c.level,
                                year: c.year,
                            });
                        }
                    }
                });
            });
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        }
        return sendResponse(res, 200, true, req.t('SCHEDULED_LISTS_NOT_FOUND'), []);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSchedule = async (req, res) => {
    try {
        const { scheduleId } = req.params;
        const response = await getScheduleById(scheduleId);
        const feedBack = await getRatingBySessions([convertToMongoObjectId(scheduleId)]);

        const infraIds = response.map((infraIds) => infraIds._infra_id);

        const infraDetails = await getInfraById(infraIds);

        response.map((resp) => {
            const feedBacks = feedBack.find(
                (feedBackDetail) => feedBackDetail._session_id.toString() === resp._id.toString(),
            );

            const infraDatas = infraDetails
                .map((a) => a.programs)
                .flat()
                .map((b) =>
                    b.remoteScheduling.filter(
                        (i) => i._id.toString() === resp._infra_id.toString(),
                    ),
                )
                .flat()
                .shift();

            if (infraDatas) resp.infraDatas = infraDatas;

            if (feedBacks) resp.feedBacks = feedBacks;
            return resp;
        });
        for (resp of response) {
            const courseResult = await course.findOne({
                _id: convertToMongoObjectId(resp._course_id),
            });
            if (courseResult) resp.course_type = courseResult.course_type;
            if (resp.merge_status) {
                const scheduleIds = resp.merge_with.map((mergeWith) => mergeWith.schedule_id);
                const mergedSchedules = (
                    await dsGetAllWithSortAsJSON(CourseSchedule, { _id: { $in: scheduleIds } }, {})
                ).data;
                resp.merge_with.forEach((mergeWith) => {
                    const schedule = mergedSchedules.find((mergedSchedule) => {
                        return mergedSchedule._id.toString() === mergeWith.schedule_id.toString();
                    });
                    if (schedule) mergeWith.session = schedule.session;
                });
            }
        }
        if (response.length)
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponse(res, 200, true, req.t('SCHEDULED_LISTS_NOT_FOUND'), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

const getCount = async (sessionIds, mode, userId) => {
    const activitiesOrDocuments = [];
    if (mode === 'activity') {
        const activities = await Activity.find(
            {
                $or: [
                    { 'sessionFlowIds._id': { $in: sessionIds } },
                    { createdBy: convertToMongoObjectId(userId) },
                    { createdBy: { $ne: convertToMongoObjectId(userId) }, status: PUBLISHED },
                    { scheduleIds: { $in: sessionIds } },
                ],
            },
            { status: 1, createdBy: 1, scheduleIds: 1, sessionFlowIds: 1 },
        ).lean();
        sessionIds.forEach((sessionId) => {
            const counts = activities.filter((activity) => {
                const { createdBy, sessionFlowIds, scheduleIds, status } = activity;
                let sessionFlowSessionIds;
                if (sessionFlowIds && sessionFlowIds.length) {
                    sessionFlowSessionIds = sessionFlowIds.map((sessionFlowId) =>
                        sessionFlowId._id.toString(),
                    );
                }

                const schedules =
                    scheduleIds && scheduleIds.length
                        ? scheduleIds.map((scheduleId) => scheduleId.toString())
                        : [];
                if (
                    createdBy.toString() === userId.toString() ||
                    (createdBy.toString() !== userId.toString() && status === PUBLISHED) ||
                    schedules.includes(sessionId.toString()) ||
                    sessionFlowSessionIds.includes(sessionId.toString())
                ) {
                    return true;
                }
                return false;
            }).length;
            activitiesOrDocuments.push({ sessionId, counts });
        });
    }
    if (mode === 'document') {
        const documents = await Document.find(
            { 'sessionOrScheduleIds._id': { $in: sessionIds }, isDeleted: false },
            { _id: 1, sessionOrScheduleIds: 1 },
        ).lean();
        sessionIds.forEach((sessionId) => {
            const counts = documents.filter((document) =>
                document.sessionOrScheduleIds.find(
                    (sessionOrScheduleId) =>
                        sessionOrScheduleId._id.toString() === sessionId.toString(),
                ),
            ).length;
            activitiesOrDocuments.push({ sessionId, counts });
        });
    }
    return activitiesOrDocuments;
};

exports.getScheduleByDate = async (req, res) => {
    try {
        const { userId, date } = req.params;
        const { timeZone } = req.query;
        const schedules = await getScheduleByDate(userId, date, timeZone);
        const sessionIds = schedules
            .filter(
                (schedule) =>
                    schedule.type === REGULAR ||
                    schedule.type === SUPPORT_SESSION ||
                    schedule.type === EVENT,
            )
            .map(
                (schedule) =>
                    (schedule.session &&
                        schedule.session._session_id &&
                        convertToMongoObjectId(schedule.session._session_id)) ||
                    convertToMongoObjectId(schedule._id),
            );
        const feedBack = await getRatingBySessions(sessionIds, userId);
        const documentCounts = await getCount(sessionIds, 'document', '');
        const activityCounts = await getCount(sessionIds, 'activity', userId);
        schedules.map((resp) => {
            const feedBacks = feedBack.find((fB) => cs(fB._session_id) === cs(resp._id));
            if (feedBacks) resp.feedBacks = feedBacks;
            if (documentCounts.length) {
                const documentCount = documentCounts.find(
                    (documentCountEntry) =>
                        (resp.type === REGULAR ||
                            resp.type === SUPPORT_SESSION ||
                            resp.type === EVENT) &&
                        ((resp.session &&
                            resp.session._session_id.toString() ===
                                documentCountEntry.sessionId.toString()) ||
                            resp._id.toString() === documentCountEntry.sessionId.toString()),
                );
                if (documentCount) {
                    resp.documentCount = documentCount.counts;
                }
            }
            if (activityCounts.length) {
                const activityCount = activityCounts.find(
                    (activityCountEntry) =>
                        (resp.type === REGULAR ||
                            resp.type === SUPPORT_SESSION ||
                            resp.type === EVENT) &&
                        ((resp.session &&
                            resp.session._session_id.toString() ===
                                activityCountEntry.sessionId.toString()) ||
                            resp._id.toString() === activityCountEntry.sessionId.toString()),
                );
                if (activityCount) {
                    resp.activityCount = activityCount.counts;
                }
            }
            return resp;
        });
        if (schedules.length)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t(DS_DATA_RETRIEVED),
                schedules,
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SCHEDULED_LISTS_NOT_FOUND'),
            schedules,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getTodaySchedule = async (req, res) => {
    try {
        const { userId } = req.params;
        const response = await getScheduleByDate(userId, timestampNow());
        if (response.length)
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponse(res, 200, true, req.t('TODAYS_SCHEDULED_LIST_NOT_FOUND'), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseOnly = async (req, res) => {
    try {
        const { staffId } = req.params;
        const response = await getCoursesByStaffId(staffId);
        if (response.length)
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponse(res, 200, true, req.t('COURSE_LIST_NOT_FOUND'), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// update Feedback
exports.updateFeedback = async (req, res) => {
    try {
        const { rating, comments } = req.body;
        const { scheduleId, studentId } = req.params;

        //check if ratings are empty or zero
        if (!rating || rating === '0')
            return sendResponseWithRequest(req, res, 200, false, req.t('SELECT_RATING'));
        // check if document exists
        const docCount = await base_control.dsGetCount(CourseSchedule, {
            'students._id': convertToMongoObjectId(studentId),
            _id: convertToMongoObjectId(scheduleId),
        });
        if (!docCount) return sendResponseWithRequest(req, res, 200, false, INVALID_ID);
        // update the document
        const updateQuery = { _id: convertToMongoObjectId(scheduleId) };
        const data = { $set: { 'students.$[i].feedBack': { rating, comments } } };
        arrayFilter = [{ 'i._id': convertToMongoObjectId(studentId) }];
        const { success } = await base_control.dsCustomUpdate(
            CourseSchedule,
            updateQuery,
            data,
            arrayFilter,
        );
        if (!success) return sendResponseWithRequest(req, res, 200, false, req.t(DS_UPDATE_FAILED));
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_UPDATED));
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getCourseSession = async (req, res) => {
    try {
        const { userId, courseId } = req.params;
        const { institutionCalendarId, programId, yearNo, levelNo, rotation, rotationCount } =
            req.query;
        // const { type, userId, institutionCalendarId } = req.query;
        logger.info(
            'courseSessionController -> getCourseSession -> %s Course Module %s start',
            userId,
        );
        const courseDatas = await getCoursesSessionList(
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            rotation,
            rotationCount,
            userId,
            courseId,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { courses: courseDatas });
    } catch (error) {
        logger.error(
            'courseSessionController -> getCourses -> %s Course Module error : %o',
            req.query.userId,
            { error },
        );
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// update riyadhToUTC
exports.riyadhToUTC = async (req, res) => {
    try {
        const courseSchedule = await CourseSchedule.find(
            { scheduleStartDateAndTime: { $exists: false } },
            {
                _id: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
            },
        );

        if (!courseSchedule) {
            return sendResponse(res, 200, true, req.t('NO_DATA_FOUND'));
        }

        const scheduleDateRecords = [];

        for (const schedules of courseSchedule) {
            const {
                start: { hour: startHour, minute: startMinute, format: startFormat },
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
            } = schedules;
            console.log(schedule_date);
            if (
                !schedules.scheduleStartDateAndTime &&
                !schedules.scheduleEndDateAndTime &&
                schedules.schedule_date
            ) {
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertingRiyadhToUTC(
                    schedule_date,
                    startHours,
                    startMinute,
                );
                const endDateAndTime = convertingRiyadhToUTC(schedule_date, endHours, endMinute);

                scheduleDateRecords.push({
                    updateOne: {
                        filter: {
                            _id: schedules._id,
                        },
                        update: {
                            $set: {
                                scheduleStartDateAndTime: startDateAndTime,
                                scheduleEndDateAndTime: endDateAndTime,
                            },
                        },
                    },
                });
            }
        }

        if (scheduleDateRecords.length > 0) {
            const doc = await bulk_write(CourseSchedule, scheduleDateRecords);
            if (doc.status) {
                return sendResponse(res, 200, false, req.t(DS_UPDATED));
            }
            return sendResponse(res, 200, false, req.t(DS_UPDATE_FAILED));
        }
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getScheduleBySessionOrScheduleId = async (req, res) => {
    try {
        const {
            query: { userId, type, institutionCalendarId },
            params: { sessionOrScheduleId },
        } = req;
        logger.info(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s start ',
            userId,
            sessionOrScheduleId,
        );
        const courseDatas = await getScheduleBySessionOrScheduleList(
            sessionOrScheduleId,
            institutionCalendarId,
            userId,
            type,
        );
        logger.info(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s end ',
            userId,
            sessionOrScheduleId,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), courseDatas);
    } catch (error) {
        logger.error(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s error : %o',
            req.query.userId,
            { error },
        );
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
exports.getCourses = async (req, res) => {
    const { staffId } = req.params;
    try {
        const courses = await getCourses(staffId);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), courses);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
