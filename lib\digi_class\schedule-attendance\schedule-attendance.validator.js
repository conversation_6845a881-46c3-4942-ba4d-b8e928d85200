const Joi = require('joi');
const constant = require('../../utility/constants');
const { com_response } = require('../../utility/common');
// create activities schema
function sessionRetake(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                modeBy: Joi.string()
                    .required()
                    .valid(
                        constant.RETAKE_ALL,
                        constant.RETAKE_ABSENT,
                        constant.BUZZER,
                        constant.SURPRISE_QUIZ,
                    ),
                _staff_id: Joi.string().length(24).required(),
                _id: Joi.string().length(24).required(),
                scheduleAttendanceId: Joi.string().length(24).optional(),
                isLive: Joi.boolean().optional(),
                faceAuthentication: Joi.boolean().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

function endSessionRetake(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                modeBy: Joi.string()
                    .required()
                    .valid(
                        constant.RETAKE_ALL,
                        constant.RETAKE_ABSENT,
                        constant.BUZZER,
                        constant.SURPRISE_QUIZ,
                    ),
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function acceptAttendance(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                modeBy: Joi.string()
                    .required()
                    .valid(
                        constant.RETAKE_ALL,
                        constant.RETAKE_ABSENT,
                        constant.BUZZER,
                        constant.SURPRISE_QUIZ,
                    ),
                _student_id: Joi.string().length(24).required(),
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
function getStudentStatus(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                modeBy: Joi.string()
                    .required()
                    .valid(
                        constant.RETAKE_ALL,
                        constant.RETAKE_ABSENT,
                        constant.BUZZER,
                        constant.SURPRISE_QUIZ,
                    ),
                _student_id: Joi.string().length(24).required(),
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function attendanceReport(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
                status: Joi.string().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
function submitQuiz(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
                _staff_id: Joi.string().length(24).required(),
                quizTitle: Joi.string().optional(),
                questions: Joi.array().required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
function getQuestions(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function studentSubmitQuiz(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
                _student_id: Joi.string().length(24).required(),
                questions: Joi.array().required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function submittedQuizResult(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function updateStudentAttendance(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().length(24).required(),
                userIds: Joi.array().required(),
                scheduleAttendanceIds: Joi.array().required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function getStudentAnsweredQuestions(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                _student_id: Joi.string().length(24).required(),
                _id: Joi.string().length(24).required(),
                scheduleAttendanceId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function StudentAttendanceReport(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                _student_id: Joi.string().length(24).required(),
                _id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function updateQuiz(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                quizTitle: Joi.string().required(),
                questions: Joi.array().required(),
            }),
            params: Joi.object().keys({
                scheduleAttendanceId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
module.exports = {
    sessionRetake,
    endSessionRetake,
    acceptAttendance,
    getStudentStatus,
    attendanceReport,
    submitQuiz,
    getQuestions,
    studentSubmitQuiz,
    submittedQuizResult,
    updateStudentAttendance,
    getStudentAnsweredQuestions,
    StudentAttendanceReport,
    updateQuiz,
};
