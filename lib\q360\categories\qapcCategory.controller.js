const qapcFormCategorySchema = require('./qapcCategory.model');
const { convertToMongoObjectId } = require('../../utility/common');
const { checkDuplicateCategoryName } = require('./qapcCategory.service');
const formSettingCourseSchema = require('../categoryForm/formSettingCourses.model');
const { DRAFT } = require('../../utility/constants');

exports.createCategoryConfigure = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            categoryName,
            level,
            describe,
            categoryFor,
            periodicInterval,
            template,
            actions,
            categoryFormType,
        } = body;
        //check the duplicate category name
        const isDuplicate = await checkDuplicateCategoryName({ categoryName });
        if (isDuplicate.checkCategoryNameCount) {
            return { statusCode: 409, message: 'CATEGORY_NAME_ALREADY_EXISTS' };
        }
        const qapcCategoryData = await qapcFormCategorySchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryName,
            level,
            describe,
            categoryFor,
            periodicInterval,
            template,
            categoryFormType,
            actions,
            createdBy: convertToMongoObjectId(_user_id),
        });
        if (!qapcCategoryData) return { statusCode: 400, message: 'CATEGORY_NAME_NOT_CREATED' };
        return { statusCode: 201, message: 'CATEGORY_NAME_CREATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getConfigureTemplate = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const qapcCategoryData = await qapcFormCategorySchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    categoryName: 1,
                    level: 1,
                    describe: 1,
                    categoryFor: 1,
                    periodicInterval: 1,
                    template: 1,
                    createdBy: 1,
                    updatedAt: 1,
                    isConfigure: 1,
                    isDefault: 1,
                    actions: 1,
                    categoryFormType: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1 } })
            .lean();
        if (!qapcCategoryData) return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: qapcCategoryData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateCategoryConfigure = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            categoryId,
            categoryName,
            level,
            describe,
            categoryFor,
            periodicInterval,
            template,
            isConfigure,
            actions,
        } = body;
        if (categoryName) {
            const isDuplicate = await checkDuplicateCategoryName({ categoryName, categoryId });
            if (isDuplicate.checkCategoryNameCount) {
                return { statusCode: 409, message: 'CATEGORY_NAME_ALREADY_EXISTS' };
            }
        }
        const qapcCategoryData = await qapcFormCategorySchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(categoryId),
            },
            {
                $set: {
                    ...(categoryName && { categoryName }),
                    ...(level && { level }),
                    ...(describe && { describe }),
                    ...(categoryFor && { categoryFor }),
                    ...(periodicInterval && { periodicInterval }),
                    ...(typeof isConfigure === 'boolean' && { isConfigure }),
                    ...(typeof isDefault === 'boolean' && { isDefault }),
                    ...(actions && { actions }),
                    ...(template && { template }),
                    ...(_user_id && { createdBy: convertToMongoObjectId(_user_id) }),
                },
            },
        );
        //update the action in form setting course
        if (actions) {
            await formSettingCourseSchema.updateMany(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(categoryId),
                    status: DRAFT,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    $set: {
                        actions,
                    },
                },
            );
        }
        if (!qapcCategoryData.modifiedCount) return { statusCode: 400, message: 'UPDATE_ERROR' };
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.singleCategoryConfig = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId } = query;
        const qapcCategoryData = await qapcFormCategorySchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(categoryId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    categoryName: 1,
                    level: 1,
                    describe: 1,
                    categoryFor: 1,
                    periodicInterval: 1,
                    template: 1,
                    createdBy: 1,
                    updatedAt: 1,
                    isDefault: 1,
                    isConfigure: 1,
                    actions: 1,
                    categoryFormType: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1 } })
            .lean();
        if (!qapcCategoryData) return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: qapcCategoryData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
