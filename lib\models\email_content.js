const mongoose = require('mongoose');
const constants = require('../utility/constants');
const objectId = mongoose.Types.ObjectId;

const schema = new mongoose.Schema({
    _institution_id: {
        type: objectId,
        ref: constants.INSTITUTION_CALENDAR,
        required: true,
    },
    type: {
        type: String,
        required: true,
    },
    content: {
        type: String,
        required: true,
    },
    isOriginal: {
        type: Boolean,
    },
});

module.exports = mongoose.model(constants.EMAIL_CONTENT, schema);
