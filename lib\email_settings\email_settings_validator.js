const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.getEmailContentValidator = (req, res, next) => {
    const schema = Joi.object().keys({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            type: Joi.string().required(),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((err) => err.message).join(`,`);
        return res.status(400).json({ errors });
    }
    next();
};

exports.updateEmailContentValidator = (req, res, next) => {
    const schema = Joi.object().keys({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            type: Joi.string().required(),
            content: Joi.string().required(),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((err) => err.message).join(`,`);
        return res.status(400).json({ errors });
    }
    next();
};
