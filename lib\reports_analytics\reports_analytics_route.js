const express = require('express');
const route = express.Router();

// controller
const {
    dashboard,
    programView,
    programStudentList,
    programStudentDetails,
    // department_subject_overview,
    department_subject_courses,
    dashboardSplittedModules,
    programViewSplittedModules,
    updateCourseScheduleCache,
    clearCacheData,
    reportCreditHours,
} = require('./reports_analytics_controller');
const {
    department_subject_list,
    program_staff_list,
    programStaffDetails,
    course_sessions,
    attendance_log,
    student_details,
    staff_details,
    student_attendance_report,
    courseAttendanceLog,
    courseAttendanceLogExport,
} = require('./reports_analytics_department_subject_controller');
const {
    course_overview,
    courseLearningOutCome,
    courseStaffAttendanceReport,
} = require('./reports_analytics_course');
const {
    courseActivitySummary,
    getAllClo,
    getAllCloNew,
    getAllPlo,
    getAllPloNew,
    getSessionSlos,
} = require('./reports_analytics_activity_controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

// Cache Data Update
route.get('/updateCourseScheduleCache', updateCourseScheduleCache);
route.get('/clearCacheData', clearCacheData);

// Redis Manual Caching
route.get('/reportCreditHours/:institutionCalendarId', reportCreditHours);

// Report Analytics v2
route.get(
    '/dashboard/:institutionCalendarId/:userId/:roleId/:tab',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'reports_and_analytics:dashboard:view',
            'reports_and_analytics:dashboard:plo_bar_graph',
            'reports_and_analytics:dashboard:credit_hours',
            'reports_and_analytics:dashboard:students_bar_graph',
            'reports_and_analytics:dashboard:staffs_bar_graph',
        ]),
    ],
    dashboardSplittedModules,
);
route.get(
    '/program/:institutionCalendarId/:programId/:tab',
    [userPolicyAuthentication(['reports_and_analytics:dashboard:academic_overview:view'])],
    programViewSplittedModules,
);

route.get(
    '/dashboard/:institutionCalendarId/:userId/:roleId',
    [userPolicyAuthentication([])],
    dashboard,
);
route.get('/program/:institutionCalendarId/:programId', programView);
route.get(
    '/student_list/:institutionCalendarId/:programId',
    [userPolicyAuthentication(['reports_and_analytics:dashboard:academic_student_details:view'])],
    programStudentList,
);
route.get('/student_details/:institutionCalendarId/:programId/:studentId', programStudentDetails);

// Department Subjects
// route.get('/department_program/:institutionCalendarId/:programId', department_subject_overview);
route.get(
    '/department_subject/:institutionCalendarId/:programId/:departmentId/:subjectId',
    [userPolicyAuthentication(['reports_and_analytics:dashboard:department_overview:view'])],
    department_subject_courses,
);
route.get(
    '/department_subject_list/:institutionCalendarId/:programId',
    [userPolicyAuthentication(['reports_and_analytics:dashboard:department_overview:view'])],
    department_subject_list,
);
route.get(
    '/program_staff_list/:institutionCalendarId/:programId',
    [userPolicyAuthentication(['reports_and_analytics:dashboard:department_staff_details:view'])],
    program_staff_list,
);
route.get(
    '/staff_details/:institutionCalendarId/:programId/:staffId',
    [
        userPolicyAuthentication([
            'reports_and_analytics:dashboard:department_staff_details:staff_details:view',
        ]),
    ],
    programStaffDetails,
);
route.get(
    '/course_sessions/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
    [userPolicyAuthentication(['reports_and_analytics:course_details:session_status:view'])],
    course_sessions,
);
route.get(
    '/attendance_log/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
    attendance_log,
);

route.get(
    '/course-attendance-log/:institutionCalendarId/:programId/:courseId/:term/:scheduleType/:level',
    [userPolicyAuthentication(['reports_and_analytics:course_details:attendance_log:view'])],
    courseAttendanceLog,
);

route.get(
    '/course-attendance-log-export/:institutionCalendarId/:programId/:courseId/:term/:scheduleType/:level',
    [userPolicyAuthentication(['reports_and_analytics:course_details:attendance_log:export'])],
    courseAttendanceLogExport,
);

route.get(
    '/course_overview/:institutionCalendarId/:programId/:courseId/:levelNo/:term',
    [userPolicyAuthentication(['reports_and_analytics:course_details:overview:view'])],
    course_overview,
);
route.get(
    '/course_learning_outcome/:institutionCalendarId/:programId/:courseId/:levelNo/:term',
    courseLearningOutCome,
);
route.get(
    '/course_attendance_report/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:userId/:userType',
    [userPolicyAuthentication(['reports_and_analytics:course_details:student_details:view'])],
    courseStaffAttendanceReport,
);

route.get(
    '/student_details/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
    [userPolicyAuthentication(['reports_and_analytics:course_details:student_details:view'])],
    student_details,
);
route.get(
    '/staff_details/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
    [userPolicyAuthentication(['reports_and_analytics:course_details:staff_details:view'])],
    staff_details,
);
route.get(
    '/student_attendance_report/:institutionCalendarId/:programId/:courseId/:term/:type/:level/:studentId',
    student_attendance_report,
);

// Activity Reports
route.get(
    '/activity_report/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:userType/:tab',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    courseActivitySummary,
);

//Learning out come
route.get('/learning-outcomes/clo/:courseId/:sloId', getAllCloNew);
route.post('/learning-outcomes/clo', getAllClo);
route.get('/learning-outcomes/plo/:cloId', getAllPloNew);
route.post('/learning-outcomes/plo', getAllPlo);
route.get('/learning-outcomes/session/slos/:courseId/:sessionId', getSessionSlos);

module.exports = route;
