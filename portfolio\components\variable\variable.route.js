const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const VariableController = require('./variable.controller');
const {
    createVariableSchema,
    getVariableSchema,
    updateVariableSchema,
    deleteVariableSchema,
} = require('./variable.validation');

router.post('/', validate(createVariableSchema), catchAsync(VariableController.createVariable));

router.get('/', validate(getVariableSchema), catchAsync(VariableController.getVariable));

router.put('/', validate(updateVariableSchema), catchAsync(VariableController.updateVariable));

router.delete('/', validate(deleteVariableSchema), catchAsync(VariableController.deleteVariable));

module.exports = router;
