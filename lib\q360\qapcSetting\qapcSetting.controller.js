const institutionCalenderSchema = require('../../models/institution_calendar');
const { convertToMongoObjectId } = require('../../utility/common');
const { PRIMARY, GREGORIAN, PUBLISHED } = require('../../utility/constants');
const qapcSettingAttemptTypeSchema = require('./qapcSettingAttemptType.model');
const qapcSettingTagSchema = require('./qapcSettingTag.model');

exports.institutionCalendarList = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const institutionCalenderData = await institutionCalenderSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    calendar_name: 1,
                    start_date: 1,
                    end_date: 1,
                    status: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        const upcomingAcademicYear = [];
        const runningAcademicYear = [];
        institutionCalenderData.forEach((calenderElement) => {
            const { _id, calendar_name, start_date, end_date, status } = calenderElement;
            const isPublished = status ? status === PUBLISHED : false;
            if (isPublished) {
                runningAcademicYear.push({
                    _id,
                    calendar_name,
                    start_date,
                    end_date,
                });
            } else {
                upcomingAcademicYear.push({
                    _id,
                    calendar_name,
                    start_date,
                    end_date,
                });
            }
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { upcomingAcademicYear, runningAcademicYear },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//qapc configure setting
exports.saveConfigure = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { tags, attemptType } = body;
        const tagBulkUpdate = [];
        const attemptTypeBulkUpdate = [];
        const tagBulkWrites = [];
        const attemptBulkWrites = [];
        let filter;
        let update;
        if (tags && tags.length) {
            tags.forEach((tagElement) => {
                if (tagElement._id) {
                    filter = {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(tagElement._id),
                    };
                    update = {
                        $set: {
                            name: tagElement.name,
                            isDefault: tagElement.isDefault,
                            level: tagElement.level,
                            isActive: tagElement.isActive,
                            subTag: tagElement.subTag,
                        },
                    };
                    tagBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    tagBulkWrites.push({
                        _institution_id: convertToMongoObjectId(_institution_id),
                        name: tagElement.name,
                        isDefault: tagElement.isDefault,
                        level: tagElement.level,
                        isActive: tagElement.isActive,
                        subTag: tagElement.subTag,
                    });
                }
            });
        }
        if (attemptType && attemptType.length) {
            attemptType.forEach((attemptElement) => {
                if (attemptElement._id) {
                    filter = {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(attemptElement._id),
                    };
                    update = {
                        $set: {
                            name: attemptElement.name,
                            isActive: attemptElement.isActive,
                        },
                    };
                    attemptTypeBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    attemptBulkWrites.push({
                        _institution_id: convertToMongoObjectId(_institution_id),
                        name: attemptElement.name,
                    });
                }
            });
        }
        if (attemptBulkWrites.length) {
            await qapcSettingAttemptTypeSchema.insertMany(attemptBulkWrites);
        }
        if (tagBulkWrites.length) {
            await qapcSettingTagSchema.insertMany(tagBulkWrites);
        }
        if (tagBulkUpdate.length) {
            await qapcSettingTagSchema.bulkWrite(tagBulkUpdate);
        }
        if (attemptTypeBulkUpdate.length) {
            await qapcSettingAttemptTypeSchema.bulkWrite(attemptTypeBulkUpdate);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getConfigureSetting = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const settingQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
        };
        const tagData = await qapcSettingTagSchema
            .find(settingQuery, {
                name: 1,
                isDefault: 1,
                level: 1,
                subTag: 1,
            })
            .sort({ _id: -1 })
            .lean();
        const attemptData = await qapcSettingAttemptTypeSchema
            .find(settingQuery, { name: 1 })
            .sort({ _id: -1 })
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: { tagData, attemptData } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createCalendar = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { upcomingInstitution } = body;
        const calenderBulkUpdate = [];
        const calenderBulkWrites = [];
        let filter;
        let update;
        if (upcomingInstitution.length) {
            upcomingInstitution.forEach((institutionElement) => {
                if (institutionElement._id) {
                    filter = {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(institutionElement._id),
                    };
                    update = {
                        $set: {
                            ...(_user_id && {
                                _creater_id: convertToMongoObjectId(_user_id),
                            }),
                            ...(institutionElement.calendar_name && {
                                calendar_name: institutionElement.calendar_name,
                            }),
                            ...(institutionElement.start_date && {
                                start_date: institutionElement.start_date,
                            }),
                            ...(institutionElement.end_date && {
                                end_date: institutionElement.end_date,
                            }),
                            ...(institutionElement.batch && { batch: institutionElement.batch }),
                            ...(typeof institutionElement.isDeleted === 'boolean' && {
                                isDeleted: institutionElement.isDeleted,
                            }),
                        },
                    };
                    calenderBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    calenderBulkWrites.push({
                        _institution_id: convertToMongoObjectId(_institution_id),
                        calendar_name: institutionElement.calendar_name,
                        calendar_type: PRIMARY,
                        primary_calendar: GREGORIAN,
                        _creater_id: convertToMongoObjectId(_user_id),
                        start_date: institutionElement.start_date,
                        end_date: institutionElement.end_date,
                        batch: institutionElement.batch,
                    });
                }
            });
        }
        if (calenderBulkWrites.length) {
            await institutionCalenderSchema.insertMany(calenderBulkWrites);
        }
        if (calenderBulkUpdate.length) {
            await institutionCalenderSchema.bulkWrite(calenderBulkUpdate);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
