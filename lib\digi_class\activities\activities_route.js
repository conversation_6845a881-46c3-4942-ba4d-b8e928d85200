const multer = require('multer');
const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const {
    createActivitiesSchema,
    updateActivitiesSchema,
    getActivitySchema,
    getResultSchema,
    getStudentParamSchema,
    getAdminActivitiesSchema,
    getActivitiesSchema,
    getQuestionBanksSchema,
    getQuestionSchema,
    getSessionAndStudentGroupSchema,
    deleteActivitiesSchema,
    quizStartByStaffSchema,
    acceptQuestionResponseSchema,
    quizStopByStaffSchema,
    questionAnsweredByStudentSchema,
    selectSessionsSchema,
} = require('./activities_validate_schema');
const { uploadAttachment } = require('../../utility/question_file_upload');
const uploadFiles = uploadAttachment.fields([
    { name: 'optionAttachment', maxCount: 15 },
    { name: 'attachment', maxCount: 1 },
]);
const {
    getActivities,
    getActivity,
    getResults,
    getStudents,
    getQuestionBank,
    getQuestionsBySession,
    getQuestions,
    getSessionAndStudentGroup,
    uploadFile,
    createActivities,
    updateActivities,
    deleteActivities,
    questionAnsweredByStudent,
    quizStartByStaff,
    quizStopByStaff,
    acceptQuestionResponse,
    getStudentGroupBySessionId,
    selectSessions,
    getAdminActivities,
    exportActivity,
    surveyExportActivity,
    activitySurveyExport,
} = require('./activities_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

// get, create, update,delete activities
route.get('/exportActivity/:id', exportActivity);
route.get('/surveyExportActivity/:id', surveyExportActivity);
route.get('/activitySurveyExport', activitySurveyExport);
route.get('/courseAdmin/', getAdminActivitiesSchema, getAdminActivities);
route.get('/:id', getActivitySchema, getActivity);

route.get('/:id/view-result', getResultSchema, getResults);

route.get(
    '/:id/get-students',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getStudentParamSchema,
    getStudents,
);
route.get(
    '/select-sessions/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    selectSessionsSchema,
    selectSessions,
);
route.get(
    '/session-groupName/:activityId/:sessionId',
    getSessionAndStudentGroupSchema,
    getSessionAndStudentGroup,
);
route.get('/courseAdmin', getAdminActivitiesSchema, getAdminActivities);
route.get(
    '/session-groupName/:activityId',
    getSessionAndStudentGroupSchema,
    getSessionAndStudentGroup,
);
route.post('/get-questions', getQuestions);
route.post('/question-bank/questions', /* getQuestionBanksSchema, */ getQuestionsBySession);
route.post('/question-bank', /* getQuestionBanksSchema, */ getQuestionBank);

route.get('/', getActivitiesSchema, getActivities);

route.post(
    '/:id/upload-file',
    (req, res, next) => {
        uploadFiles(req, res, (err) => {
            if (err instanceof multer.MulterError)
                return res.status(500).send('Multer Error in uploading'); // A Multer error occurred when uploading.
            if (err) next({ message: err.message, status: 404 });
            next();
        });
    },
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    createActivitiesSchema,
    uploadFile,
);
route.post(
    '/',
    (req, res, next) => {
        uploadFiles(req, res, (err) => {
            if (err instanceof multer.MulterError)
                return res.status(500).send('Multer Error in uploading'); // A Multer error occurred when uploading.
            if (err) next({ message: err.message, status: 404 });
            next();
        });
    },
    createActivitiesSchema,
    createActivities,
);

//student answer the question
route.put('/:id/answer', questionAnsweredByStudentSchema, questionAnsweredByStudent);
route.put('/:id/started', quizStartByStaffSchema, quizStartByStaff);
route.put('/:id/stop', quizStopByStaffSchema, quizStopByStaff);
route.put('/:id/accept-question-response', acceptQuestionResponseSchema, acceptQuestionResponse);
route.put(
    '/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateActivitiesSchema,
    updateActivities,
);
route.get('/getStudentGroupBySessionId/:sessionId', getStudentGroupBySessionId);
route.delete('/:id', deleteActivitiesSchema, deleteActivities);
module.exports = route;
