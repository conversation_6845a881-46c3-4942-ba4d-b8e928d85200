const router = require('express').Router();
const catchAsync = require('../utility/catch-async');

const {
    addNewDisciplinaryRemarks,
    updateDisciplinaryRemarks,
    deleteDisciplinaryRemarks,
    getStudentsDisciplinaryRemarks,
    getScheduleDetails,
    uploadDisciplinaryRemarkFiles,
    sendRemarksMail,
    generateSignedURLs,
    getMailSentDetails,
} = require('./disciplinaryRemarks.controller');
const {
    addNewDisciplinaryRemarksValidator,
    updateDisciplinaryRemarksValidator,
    deleteDisciplinaryRemarksValidator,
    getStudentsDisciplinaryRemarksValidator,
    getScheduleDetailsValidator,
    fileUploadValidator,
    sendRemarkMailValidator,
    getSignedUrlsValidator,
    getMailDetailsValidator,
} = require('./disciplinaryRemarks.validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

router.post(
    '/',
    userPolicyAuthentication(['global_search:dashboard:view']),
    addNewDisciplinaryRemarksValidator,
    catchAsync(addNewDisciplinaryRemarks),
);
router.put(
    '/',
    userPolicyAuthentication(['global_search:dashboard:view']),
    updateDisciplinaryRemarksValidator,
    catchAsync(updateDisciplinaryRemarks),
);
router.delete(
    '/',
    userPolicyAuthentication(['global_search:dashboard:view']),
    deleteDisciplinaryRemarksValidator,
    catchAsync(deleteDisciplinaryRemarks),
);
router.get(
    '/students_all_remarks',
    userPolicyAuthentication(['global_search:dashboard:view']),
    getStudentsDisciplinaryRemarksValidator,
    catchAsync(getStudentsDisciplinaryRemarks),
);
router.post('/userRemarkFiles', fileUploadValidator, catchAsync(uploadDisciplinaryRemarkFiles));
router.get(
    '/schedule_details',
    userPolicyAuthentication(['global_search:dashboard:view']),
    getScheduleDetailsValidator,
    catchAsync(getScheduleDetails),
);
router.post(
    '/sendMail',
    userPolicyAuthentication(['global_search:dashboard:view']),
    sendRemarkMailValidator,
    catchAsync(sendRemarksMail),
);
router.post('/generateSignedURLs', getSignedUrlsValidator, catchAsync(generateSignedURLs));
router.get(
    '/mailDetails',
    userPolicyAuthentication(['global_search:dashboard:view']),
    getMailDetailsValidator,
    catchAsync(getMailSentDetails),
);

module.exports = router;
