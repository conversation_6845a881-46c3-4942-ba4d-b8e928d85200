const { convertToMongoObjectId } = require('../utility/common');
const programCurriculumSchema = require('../models/digi_curriculum');
const yearLevelAuthorSchema = require('../models/yearLevelAuthor');
const programSchema = require('../models/digi_programs');
const programCalendarSchema = require('../models/program_calendar');
const { YEAR_LEVEL_AUTHOR_TYPE_MODULE } = require('../utility/constants');

const getYearLevelList = async ({ programId }) => {
    try {
        const programCurriculumList = await programCurriculumSchema
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    curriculum_name: 1,
                    'year_level.y_type': 1,
                    'year_level.levels.level_name': 1,
                },
            )
            .sort({ _id: 1 })
            .lean();

        if (!programCurriculumList) return [];

        const yearLevelAuthors = await yearLevelAuthorSchema
            .find({ programId: convertToMongoObjectId(programId) })
            .populate({
                path: 'users.userId',
                select: { name: 1 },
            })
            .lean();

        return {
            curriculumId: programCurriculumList._id,
            curriculumName: programCurriculumList.curriculum_name,
            year: programCurriculumList.year_level.map((yearElement) => {
                const yearAuthor = yearLevelAuthors.find(
                    (author) =>
                        author.yearName === yearElement.y_type &&
                        author.authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.YEAR,
                );

                return {
                    yearName: yearElement.y_type,
                    // termName: yearAuthor?.termName || null,
                    users:
                        yearAuthor?.users.map((user) => ({
                            userId: user.userId._id,
                            name: user.userId.name,
                            modules: user.modules,
                        })) || [],
                    level: yearElement.levels.map((levelElement) => {
                        const levelAuthor = yearLevelAuthors.find(
                            (author) =>
                                author.yearName === yearElement.y_type &&
                                author.levelName === levelElement.level_name &&
                                author.authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.LEVEL,
                        );

                        return {
                            levelName: levelElement.level_name,
                            // termName: levelAuthor?.termName || null,
                            users:
                                levelAuthor?.users.map((user) => ({
                                    userId: user.userId._id,
                                    name: user.userId.name,
                                    modules: user.modules,
                                })) || [],
                        };
                    }),
                };
            }),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programList = async ({ programIds }) => {
    try {
        return await programSchema
            .find(
                {
                    _id: { $in: programIds },
                    isDeleted: false,
                },
                {
                    name: 1,
                    code: 1,
                    term: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programYearLevelList = async ({ institutionCalendarId, programId, term }) => {
    try {
        const programCalendarData = await programCalendarSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            )
            .lean();

        if (!programCalendarData || !programCalendarData.level) {
            return [];
        }

        // Filter levels by term if term is provided
        const filteredLevels = term
            ? programCalendarData.level.filter((levelElement) => levelElement.term === term)
            : programCalendarData.level;

        // Group levels by year
        const yearGroups = filteredLevels.reduce((acc, levelElement) => {
            if (!acc[levelElement.year]) {
                acc[levelElement.year] = [];
            }
            acc[levelElement.year].push(levelElement);
            return acc;
        }, {});

        // Transform into the desired nested structure
        const yearLevelDetails = Object.entries(yearGroups).map(([year, levels]) => {
            const levelDetails = levels.map((levelElement) => ({
                level_no: levelElement.level_no,
                rotation: levelElement.rotation,
                rotation_count: levelElement.rotation_count,
                course: levelElement.course || [],
                rotation_course: levelElement.rotation_course || [],
            }));

            return {
                year,
                term: levels[0].term,
                level: levelDetails,
            };
        });

        return yearLevelDetails;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getYearLevelList,
    programList,
    programYearLevelList,
};
