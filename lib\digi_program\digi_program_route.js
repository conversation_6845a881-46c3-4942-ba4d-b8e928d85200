const express = require('express');
const route = express.Router();
const program = require('./digi_program_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
// const validator = require('./digi_program_validator');

route.get(
    '/programListWithType',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            defaultPolicy.DC_STUDENT,
            'program_input:programs:view',
        ]),
    ],
    program.programListWithType,
);
route.get(
    '/program_department_list',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            defaultPolicy.DC_STUDENT,
            'program_input:programs:view',
        ]),
    ],
    program.program_department_list,
);
route.get(
    '/program_department_list_along_share',
    [userPolicyAuthentication(['infrastructure_management:onsite:view'])],
    program.program_department_list_along_share,
);
route.get(
    '/sidebar/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    program.program_input_sidebar,
);
route.get(
    '/:id',
    [userPolicyAuthentication(['program_input:programs:view', 'program_input:programs:search'])],
    program.list_id,
);
route.get('/type_list/:type', program.type_list);
route.get(
    '/',
    [
        userPolicyAuthentication([
            defaultPolicy.SIGNUP,
            defaultPolicy.DC_STAFF,
            'user_management:student_management:registered:profile_view',
            'user_management:student_management:registration_pending:view',
            'user_management:student_management:registration_pending:expired:edit',
            'program_input:programs:view',
        ]),
    ],
    program.list,
);
route.post(
    '/import_program',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    /* validator.program_import, */ program.import_program,
);
route.post('/data_check_program', /* validator.program_import, */ program.data_check_program);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'program_input:programs:view',
            'program_input:programs:add_program',
            'program_input:programs:add_pre-requisite',
        ]),
    ],
    program.insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'program_input:programs:active_programs:edit',
            'program_input:programs:active_programs:edit',
        ]),
    ],
    program.update,
);
route.delete('/:id', program.remove);
route.post('/labels', program.addLabels);
route.put('/labels/:id', program.updateLabel);

module.exports = route;
