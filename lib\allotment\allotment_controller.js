const constant = require('../utility/constants');
// const allotment= require('mongoose').model(constant.ALLOTME)
var department_subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var department = require('mongoose').model(constant.DEPARTMENT);
var allotment = require('mongoose').model(constant.COURSE);
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
var credit_master = require('mongoose').model(constant.CREDIT_HOURS_MASTER);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const allotment_formate = require('./allotment_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        {
            $lookup: {
                from: 'department_divisions',
                localField: '_division_id',
                foreignField: '_id',
                as: 'division'
            }
        }, {
            $lookup: {
                from: 'department_subjects',
                localField: 'division._subject_id',
                foreignField: '_id',
                as: 'division_subject'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: '_id',
                foreignField: '_administration_department_id',
                as: 'admin_courses'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: 'division._subject_id',
                foreignField: '_participating_subject_id',
                as: 'part_courses'
            }
        }, {
            $unwind: {
                path: '$division_subject',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division_subject.courses': {
                    $filter: {
                        input: '$part_courses',
                        as: 'part_id',
                        cond: {
                            $in: ['$division_subject._id', '$$part_id._participating_subject_id']
                        }
                    }
                },
                'division.course': {
                    $filter: {
                        input: '$admin_courses',
                        as: 'course',
                        cond: {
                            $in: ['$$course._administration_division_id', '$division._id']
                        }
                    }
                }
            }
        }, {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $first: '$division'
                },
                _subject_id: {
                    $first: '$_subject_id'
                },
                division_subject: {
                    $push: '$division_subject'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        }, {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'subject_id',
                        cond: {
                            $eq: ['$division._id', '$$subject_id._master_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $push: '$division'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        },
        {
            $project: {
                'department_title': 1,
                'division._id': 1,
                'division.title': 1,
                'division.course._id': 1,
                'division.course.courses_name': 1,
                'division.subjects._id': 1,
                'division.subjects.title': 1,
                'division.subjects.courses._id': 1,
                'division.subjects.courses.courses_name': 1
            }
        },
        { $sort: { updatedAt: -1 } },
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "allotment list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ allotment_formate.allotment(doc.data));
        common_files.list_all_response(res, 200, true, "allotment list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* allotment_formate.allotment(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        {
            $lookup: {
                from: 'department_divisions',
                localField: '_division_id',
                foreignField: '_id',
                as: 'division'
            }
        }, {
            $lookup: {
                from: 'department_subjects',
                localField: 'division._subject_id',
                foreignField: '_id',
                as: 'division_subject'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: '_id',
                foreignField: '_administration_department_id',
                as: 'admin_courses'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: 'division._subject_id',
                foreignField: '_participating_subject_id',
                as: 'part_courses'
            }
        }, {
            $unwind: {
                path: '$division_subject',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division_subject.courses': {
                    $filter: {
                        input: '$part_courses',
                        as: 'part_id',
                        cond: {
                            $in: ['$division_subject._id', '$$part_id._participating_subject_id']
                        }
                    }
                },
                'division.course': {
                    $filter: {
                        input: '$admin_courses',
                        as: 'course',
                        cond: {
                            $in: ['$$course._administration_division_id', '$division._id']
                        }
                    }
                }
            }
        }, {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $first: '$division'
                },
                _subject_id: {
                    $first: '$_subject_id'
                },
                division_subject: {
                    $push: '$division_subject'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        }, {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'subject_id',
                        cond: {
                            $eq: ['$division._id', '$$subject_id._master_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $push: '$division'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        },
        {
            $project: {
                'department_title': 1,
                'division._id': 1,
                'division.title': 1,
                'division.course._id': 1,
                'division.course.courses_name': 1,
                'division.subjects._id': 1,
                'division.subjects.title': 1,
                'division.subjects.courses._id': 1,
                'division.subjects.courses.courses_name': 1
            }
        }
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "allotment details", /* doc.data */ allotment_formate.allotment_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "allotment details", doc.data[0]/*  allotment_formate.allotment_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let department_ids = [], division_ids = [], subject_ids = [], admin_course_ids = [], part_course_ids = [];
    let status, datas;
    await req.body.data.forEach(async element => {
        if (element._department_id != undefined && department_ids.indexOf(element._department_id) == -1) {
            department_ids.push(element._department_id);
        }
        if (element._division_id != undefined && division_ids.indexOf(element._division_id) == -1) {
            division_ids.push(element._division_id);
        }
        await element._admin_course_id.forEach(async admin_course => {
            if (admin_course != undefined && admin_course_ids.indexOf(admin_course) == -1) {
                admin_course_ids.push(admin_course);
            }
        });
        await element.participation.forEach(async sub_element => {
            if (sub_element._subject_id != undefined && subject_ids.indexOf(sub_element._subject_id) == -1) {
                subject_ids.push(sub_element._subject_id);
            }
            await sub_element._participation_course_id.forEach(async part_course => {
                if (part_course != undefined && part_course_ids.indexOf(part_course) == -1) {
                    part_course_ids.push(part_course);
                }
            });
        });
    });
    console.log('part', part_course_ids, part_course_ids.length);
    let depart_checks = await base_control.check_id(department, { _id: { $in: department_ids }, 'isDeleted': false });
    let division_checks = await base_control.check_id(department_division, { _id: { $in: division_ids }, 'isDeleted': false });
    let subject_checks = await base_control.check_id(department_subject, { _id: { $in: subject_ids }, 'isDeleted': false });
    let course_checks = { status: true };
    if (admin_course_ids.length > 0) {
        course_checks = await base_control.check_id(allotment, { _id: { $in: admin_course_ids }, 'isDeleted': false });
    }
    let course_checks2 = { status: true };
    if (part_course_ids.length > 0) {
        course_checks2 = await base_control.check_id(allotment, { _id: { $in: part_course_ids }, 'isDeleted': false });
    }
    if (depart_checks.status) {
        if (division_checks.status) {
            if (subject_checks.status) {
                if (course_checks.status) {
                    if (course_checks2.status) {
                        let update_obj = { _administration_department_id: department_ids };
                        if (division_ids.length != 0) {
                            update_obj = { _administration_department_id: department_ids, _administration_division_id: division_ids }
                        }
                        let admin_updates = await base_control.update_many(allotment, admin_course_ids, { $set: update_obj });
                        console.log('admin', admin_updates);

                        await req.body.data.forEach(async master => {
                            await master.participation.forEach(async (child, index) => {
                                let sub_id = ObjectId(child._subject_id);
                                let part_update_obj;
                                if (master._division_id != undefined) {
                                    part_update_obj = { _participating_department_id: master._department_id, _participating_division_id: master._division_id, _participating_subject_id: sub_id };
                                } else {
                                    part_update_obj = { _participating_department_id: master._department_id, _participating_subject_id: sub_id };
                                }
                                let part_updates = await base_control.update_many(allotment, child._participation_course_id, { $set: part_update_obj });
                                console.log('participation : ', index, ' :-> ', part_updates);
                            });
                        });
                        common_files.com_response(res, 200, true, "Courses successfully Alloted to Seprate departments", "Courses successfully Alloted to Seprate departments");
                        // req.body.data.forEach(async (element, index) => {
                        //     if (element._department_id != undefined) {
                        //         let updates = { _administration_department_id: element._department_id };
                        //         if (element._division_id != undefined || element._division_id.length != 0) {
                        //             updates = { _administration_department_id: element._department_id, _administration_division_id: element._division_id }
                        //         }
                        //         await base_control.update(allotment, element.);
                        //     }
                        // });
                    } else {
                        common_files.com_response(res, 500, false, "Error id Participation Course id not match", 'Check Parshing reference ID');
                    }
                } else {
                    common_files.com_response(res, 500, false, "Error id Admin Course id not match", 'Check Parshing reference ID');
                }
            } else {
                common_files.com_response(res, 500, false, "Error id Department subject id not match", 'Check Parshing reference ID');
            }
        } else {
            common_files.com_response(res, 500, false, "Error id Department division id not match", 'Check Parshing reference ID');
        }
    } else {
        common_files.com_response(res, 500, false, "Error id Department id not match", 'Check Parshing reference ID');
    }
}

/*
exports.update = async (req, res) => {
    let department_check = { status: true }, division_check = { status: true }, subject_check = { status: true };
    let part_department_check = { status: true }, part_division_check = { status: true }, part_subject_check = { status: true };

    if (req.body._administration_department_id != undefined) {
        department_check = await base_control.check_id(department, { _id: { $in: req.body._administration_department_id }, 'isDeleted': false });
    }
    if (req.body._administration_division_id != undefined) {
        division_check = await base_control.check_id(department_division, { _id: { $in: req.body._administration_division_id }, 'isDeleted': false });
    }
    if (req.body._administration_subject_id != undefined) {
        subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._administration_subject_id }, 'isDeleted': false });
    }
    if (req.body._participating_department_id != undefined) {
        part_department_check = await base_control.check_id(department, { _id: { $in: req.body._participating_department_id }, 'isDeleted': false });
    }
    if (req.body._participating_division_id != undefined) {
        part_division_check = await base_control.check_id(department_division, { _id: { $in: req.body._participating_division_id }, 'isDeleted': false });
    }
    if (req.body._participating_subject_id != undefined) {
        part_subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._participating_subject_id }, 'isDeleted': false });
    }
    // console.log(department_check.status, division_check.status, subject_check.status, part_department_check.status, part_division_check.status, part_subject_check.status);
    if (department_check.status && division_check.status && subject_check.status && part_department_check.status && part_division_check.status && part_subject_check.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(allotment, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "allotment update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(allotment, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "allotment deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
} */

exports.list_values = async (req, res) => {
    let proj, query = {
        'isDeleted': false
    };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else {
            proj = {};
        }

        let doc = await base_control.get_list(allotment, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "allotment List", allotment_formate.allotment_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.allotment_complete = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(id) } },
        { $lookup: { from: constant.CREDIT_HOURS_INDIVIDUAL, localField: 'year', foreignField: 'year', as: 'individuals' } },
        { $unwind: { path: '$individuals', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                'study_year': {
                    $first: '$year'
                },
                'no_allotment': {
                    $first: '$allotment_no'
                },
                'theory_credit_hours': {
                    $sum: '$individuals.theory_credit_hours'
                },
                'pratical_credit_hours': {
                    $sum: '$individuals.pratical_credit_hours'
                },
                'clinical_credit_hours': {
                    $sum: '$individuals.clinical_credit_hours'
                }
            }
        },
        { $addFields: { 'total_credit_hours': { $sum: ['$theory_credit_hours', '$pratical_credit_hours', '$clinical_credit_hours'] } } },
        { $lookup: { from: constant.allotment, localField: 'study_year', foreignField: 'study_year', as: 'allotment' } },
        { $unwind: { path: '$allotment', preserveNullAndEmptyArrays: true } },
        { $addFields: { 'allotment_total_credit_hours': { $sum: ['$allotment.theory_credit', '$allotment.practical_credit', '$allotment.clinical_credit'] } } },
        {
            $group: {
                _id: '$study_year',
                'study_year': {
                    $first: '$study_year'
                },
                'no_allotment': {
                    $first: '$no_allotment'
                },
                'theory_credit_hours': {
                    $first: '$theory_credit_hours'
                },
                'pratical_credit_hours': {
                    $first: '$pratical_credit_hours'
                },
                'clinical_credit_hours': {
                    $first: '$clinical_credit_hours'
                },
                'total_credit_hours': {
                    $first: '$total_credit_hours'
                },
                'allotment_total_credit_hours': {
                    $first: '$allotment_total_credit_hours'
                },
                'allotment': {
                    $push: '$allotment'
                },
                'id': {
                    $first: '$_id'
                }
            }
        },
        { $sort: { study_year: 1 } },
        { $addFields: { status: { $eq: ['$total_credit_hours', '$allotment_total_credit_hours'] } } }
    ];
    let doc = await base_control.get_aggregate(credit_master, aggre);
    // console.log(doc.data.length);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "allotment complete details", /* doc.data */allotment_formate.allotment_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "allotment complete details", doc.data /* allotment_formate.allotment_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        {
            $lookup: {
                from: 'department_divisions',
                localField: '_division_id',
                foreignField: '_id',
                as: 'division'
            }
        }, {
            $lookup: {
                from: 'department_subjects',
                localField: 'division._subject_id',
                foreignField: '_id',
                as: 'division_subject'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: '_id',
                foreignField: '_administration_department_id',
                as: 'admin_courses'
            }
        }, {
            $lookup: {
                from: 'courses',
                localField: 'division._subject_id',
                foreignField: '_participating_subject_id',
                as: 'part_courses'
            }
        }, {
            $unwind: {
                path: '$division_subject',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division_subject.courses': {
                    $filter: {
                        input: '$part_courses',
                        as: 'part_id',
                        cond: {
                            $in: ['$division_subject._id', '$$part_id._participating_subject_id']
                        }
                    }
                },
                'division.course': {
                    $filter: {
                        input: '$admin_courses',
                        as: 'course',
                        cond: {
                            $in: ['$$course._administration_division_id', '$division._id']
                        }
                    }
                }
            }
        }, {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $first: '$division'
                },
                _subject_id: {
                    $first: '$_subject_id'
                },
                division_subject: {
                    $push: '$division_subject'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        }, {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        }, {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'subject_id',
                        cond: {
                            $eq: ['$division._id', '$$subject_id._master_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: {
                    $first: '$department_title'
                },
                division: {
                    $push: '$division'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        },
        {
            $project: {
                'department_title': 1,
                'division._id': 1,
                'division.title': 1,
                'division.course._id': 1,
                'division.course.courses_name': 1,
                'division.subjects._id': 1,
                'division.subjects.title': 1,
                'division.subjects.courses._id': 1,
                'division.subjects.courses.courses_name': 1
            }
        },
        { $sort: { updatedAt: 1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(department, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "allotment list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ allotment_formate.allotment(doc.data));
        common_files.list_all_response(res, 200, true, "allotment list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* allotment_formate.allotment(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_allotment_program_level = async (req, res) => {
    let id = req.params.id;
    // console.log(req.params.level);
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'study_level': parseInt(req.params.level) } },
        { $match: { 'isDeleted': false } },
        { $project: { _id: 1, allotments_name: 1 } }
    ];
    let doc = await base_control.get_aggregate(allotment, aggre);
    // console.log(doc);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "allotment list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ allotment_formate.allotment(doc.data));
        common_files.com_response(res, 200, true, "level wise allotment list", doc.data /* allotment_formate.allotment(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

// exports.list_subjects = async (req, res) => {
//     let id = req.params.id;
//     let aggre = [
//         { $match: { '_id': ObjectId(id) } },
//         { $match: { 'isDeleted': false } },
//         { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
//         { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
//         { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } }
//     ];
//     let doc = await base_control.get_aggregate(allotment, aggre);
//     // console.log(doc.data[0].participating_subject);
//     let subject_array = []
//     subject_array = doc.data[0].participating_subject;
//     // console.log(subject_array.length);
//     subject_array.push(doc.data[0].administration_subject);
//     // console.log(subject_array);
//     // console.log(subject_array.length);
//     if (doc.status) {
//         // common_files.com_response(res, 200, true, "allotment details", /* doc.data */ allotment_formate.allotment_ID(doc.data[0]));
//         common_files.com_response(res, 200, true, "allotment details", subject_array /* allotment_formate.allotment_ID(doc.data[0]) */);
//     } else {
//         common_files.com_response(res, 500, false, "Error", doc.data);
//     }
// };


exports.list_course_year_level = async (req, res) => {
    // console.log(req.params.year_level, req.params.input);
    let aggre = [{ $match: { 'isDeleted': false } }, { $match: { '_program_id': ObjectId(req.params.id) } }, { $sort: { updatedAt: -1 } }];
    if (req.params.year_level == 'year') {
        aggre.push({ $match: { 'study_year': parseInt(req.params.input) } });
    } else {
        aggre.push({ $match: { 'study_level': parseInt(req.params.input) } });
    }
    aggre.push({ $project: { _id: 1, courses_name: 1 } });
    // console.log(aggre);
    let doc = await base_control.get_aggregate(allotment, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "allotment details", /* doc.data */ allotment_formate.allotment_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "allotment details", doc.data /* allotment_formate.allotment_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_program_department = async (req, res) => {
    let id = ObjectId(req.params.id);
    // let skips = Number(req.query.limit * (req.query.pageNo - 1));
    // let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(id) } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: 'division._subject_id', foreignField: '_id', as: 'division_subject' } },
        { $unwind: { path: '$division', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'sub_id',
                        cond: {
                            $in: ['$$sub_id._id', '$division._subject_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: { $first: '$department_title' },
                division: { $push: '$division' },
                _subject_id: { $first: '$_subject_id' },
                program: { $first: '$program' },
                isActive: { $first: '$isActive' },
                isDeleted: { $first: '$isDeleted' }
            }
        },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        { $sort: { updatedAt: -1 } },
    ];
    // let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "department list", /* doc.data */ department_formate.department(doc.data));
        common_files.com_response(res, 200, true, "department list", doc.data /* department_formate.department(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.department_list_added = async (req, res) => {
    let id = ObjectId(req.params.id);
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(id) } },
        { $lookup: { from: constant.COURSE, localField: '_id', foreignField: '_administration_department_id', as: 'courses' } },
        { $addFields: { sizes: { $size: '$courses' } } },
        // { $unwind: { path: '$courses', preserveNullAndEmptyArrays: true } },
        { $addFields: { added: { $ne: ['$sizes', 0] } } },
        { $project: { department_title: 1, added: 1, 'courses._administration_department_id': 1 } },
        { $sort: { updatedAt: -1 } },
    ];
    // let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "department list", /* doc.data */ department_formate.department(doc.data));
        common_files.com_response(res, 200, true, "department list along with Added status", doc.data /* department_formate.department(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}