const {
    response_function,
    responseFunctionWithRequest,
    convertToMongoObjectId,
} = require('../utility/common');
const {
    get_list,
    get,
    insert,
    update,
    get_list_populate,
    update_condition_array_filter,
    bulk_write,
    update_condition,
    delete: remove,
} = require('../base/base_controller');
const { INSTITUTION, VACCINATION } = require('../utility/constants');

const institution = require('mongoose').model(INSTITUTION);
const vaccination = require('mongoose').model(VACCINATION);

exports.insert = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: { vaccination_name, no_of_dosses },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        //Duplicate check
        const vaccinationNameCheck = await get(vaccination, { vaccination_name }, { _id: 1 });
        if (vaccinationNameCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Vaccination name duplicate found',
                        'Vaccination name duplicate found',
                    ),
                );
        const obj = {
            _institution_id,
            vaccination_name,
            no_of_dosses,
        };
        const doc = await insert(vaccination, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to add', []));
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Added successfully', 'Added Successfully'));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
//(Model, object_id, objects)
exports.update = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: { vaccination_name, no_of_dosses },
            params: { id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const obj = {
            vaccination_name,
            no_of_dosses,
        };
        const query = { _id: convertToMongoObjectId(id) };
        const doc = await update(vaccination, query, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to update', []));
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Updated successfully', 'Updated Successfully'),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.deleteVaccination = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const obj = {
            isDeleted: true,
        };
        const query = { _id: convertToMongoObjectId(id) };
        const doc = await update(vaccination, query, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to delete', []));
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Deleted successfully', 'Deleted Successfully'),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.list = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const doc = await get_list(
            vaccination,
            { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
            {},
        );

        if (!doc.status)
            return res.status(410).send(responseFunctionWithRequest(req, 410, false, 'Error', []));
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, 'Vaccination List', doc));
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};
