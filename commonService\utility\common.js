const mongoose = require('mongoose');
module.exports = {
    clone: (object) => {
        return JSON.parse(JSON.stringify(object));
    },
    query: { isActive: true, isDeleted: false },
    convertToMongoObjectId: (id) => new mongoose.Types.ObjectId(id),

    nameFormatter: (name) => {
        return (
            name.middle && name.middle.length !== 0
                ? name.first +
                  ' ' +
                  (name.middle && name.middle.length !== 0 ? name.middle : '') +
                  ' ' +
                  (name.last && name.last.length !== 0 ? name.last : '') +
                  ' ' +
                  (name.family && name.family.length !== 0 ? name.family : '')
                : name.first +
                  ' ' +
                  (name.last && name.last.length !== 0 ? name.last : '') +
                  ' ' +
                  (name.family && name.family.length !== 0 ? name.family : '')
        ).trim();
    },

    fileExist: (filePath) => {
        const fs = require('fs');
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                return false;
            }
            return true;
        });
    },
};
