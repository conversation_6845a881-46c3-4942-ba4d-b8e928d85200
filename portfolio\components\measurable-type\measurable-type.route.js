const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const MeasurableController = require('./measurable-type.controller');
const {
    createMeasurableTypeSchema,
    updateMeasurableTypeSchema,
    deleteMeasurableTypeSchema,
} = require('./measurable-type.validation');

router.post(
    '/',
    validate(createMeasurableTypeSchema),
    catchAsync(MeasurableController.createMeasurableType),
);

router.get('/', catchAsync(MeasurableController.getMeasurableTypes));

router.put(
    '/',
    validate(updateMeasurableTypeSchema),
    catchAsync(MeasurableController.updateMeasurableType),
);

router.delete(
    '/',
    validate(deleteMeasurableTypeSchema),
    catchAsync(MeasurableController.deleteMeasurableType),
);

router.get('/type', catchAsync(MeasurableController.getApplicationMeasurableTypes));

module.exports = router;
