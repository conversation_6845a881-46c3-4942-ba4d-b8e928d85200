const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    stringSchema,
    stringRQSchema,
    numberSchema,
    booleanSchema,
    dateSchema,
} = require('../../../utility/validationSchemas');

exports.paramIDValidator = Joi.object({
    id: objectIdRQSchema,
});

exports.assignmentAnswerValidator = Joi.object({
    assignmentId: objectIdRQSchema,
    studentId: objectIdRQSchema,
    programId: objectIdSchema,
    courseId: objectIdSchema,
    promptsAnswer: Joi.array().items(objectIdSchema),
    // assignmentAnswer: Joi.object({
    //     value: stringSchema,
    //     attachments: Joi.array().items(
    //         Joi.object({
    //             url: stringSchema,
    //             signedUrl: stringSchema,
    //             name: stringSchema,
    //             sizeInKb: numberSchema,
    //         }),
    //     ),
    // }).unknown(true),
    isDraft: booleanSchema,
    noOfAttempt: numberSchema,
    submittedAt: dateSchema,
    status: stringRQSchema,
}).unknown(true);
