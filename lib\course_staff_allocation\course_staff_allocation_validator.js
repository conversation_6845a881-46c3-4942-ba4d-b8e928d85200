const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.course_staff_allocation_validator = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _program_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _department_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _subject_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _course_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            year: Joi.number().min(1).max(10).required().error(error => {
                return error;
            }),
            level: Joi.string().required().error(error => {
                return error;
            }),
            _course_administrator_regular_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _course_administrator_interim_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _user_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.course_staff_allocation_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
/* exports.course_staff_allocation_validator = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _course_administrator_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _user_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
} */
