const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { DIGI_USER_SURVEY_TYPE_LIST, USER } = require('../../utility/constants');

const digiUserSurveyTypeListSchema = new Schema({
    _institution_id: { type: ObjectId },
    userId: { type: ObjectId, ref: USER },
    userSurveyTypeList: [{ type: String }],
});

module.exports = model(DIGI_USER_SURVEY_TYPE_LIST, digiUserSurveyTypeListSchema);
