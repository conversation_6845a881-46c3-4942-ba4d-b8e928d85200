const { sendResponse, sortArrayByObjectAscending } = require('../../utility/common');
const StreamChat = require('stream-chat').StreamChat;
const User = require('../../models/user');
const DigiChat = require('../../models/digi_chat');
const Course = require('../../models/digi_course');
const Document = require('../../models/document_manager');
const Activity = require('../../models/activities');
const CourseSchedules = require('../../models/course_schedule');
const StudentGroups = require('../../models/student_group');
const { sendNotificationPush } = require('../../../service/pushNotification.service');
const {
    createChannel,
    addMembersToChannel,
    getStudentSubjects,
    ioRegister,
} = require('./chat_service');
const api_key = process.env.STREAM_API_KEY;
const api_secret = process.env.STREAM_API_SECRET;
const { localLogger } = require('../../utility/locallogger');
const { getDashboardCoursesList } = require('../dashboard/dashboard.service');
// get stream io client instance
const ioServerClient = StreamChat.getInstance(api_key, api_secret);
const fs = require('fs');
const { DS_DATA_RETRIEVED, DS_UPDATED, DS_DELETED } = require('../../utility/constants');

const STAFF = 'staff';
const STUDENT = 'student';
const MALE = 'male';
const FEMALE = 'female';
const SUB_CHANNEL = 'sub_channel';
const MAIN_CHANNEL = 'main_channel';

exports.getCourseAct = async (req, res) => {
    try {
        const { _course_id, statusIn } = req.query;
        if (!_course_id) return sendResponse(res, 200, false, req.t('COURSEID_REQUIRED'));
        const query = {
            courseId: _course_id,
            isActive: true,
            isDeleted: false,
            status: { $in: ['started', 'not_started', 'published'] },
            schedule: { $exists: true },
        };
        const activities = await Activity.find(query)
            .populate('scheduleIds')
            .populate('courseId', 'course_name course_code')
            .populate('createdBy', 'name');
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), activities);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getCourseDoc = async (req, res) => {
    try {
        const { _course_id } = req.query;
        if (!_course_id) return sendResponse(res, 200, false, req.t('COURSEID_REQUIRED'));
        const query = { _course_id };
        const documents = await Document.find(query).populate('_user_id', 'name');
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), documents);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.ioUserRegistor = async (req, res) => {
    try {
        // Todo Deactivating this for User Overloading
        /* const { userId, role, image } = req.body;
        let user = await User.findOne({ _id: userId });
        if (!user) return sendResponse(res, 200, false, req.t('USER_NOT_FOUND'));
        const { name, email, user_type, user_id, gender } = user;
        const token = await ioServerClient.createToken(userId);
        await ioServerClient.upsertUsers([
            {
                id: userId,
                name: name.first,
                fullName: `${name.first || ''} ${name.middle || ''} ${name.last || ''}`,
                role,
                email,
                user_type,
                user_id,
                gender,
                image,
            },
        ]);
        await User.updateMany({ _id: userId }, { $set: { ioToken: token } });
        user = await User.findOne({ _id: userId });
        return sendResponse(res, 200, true, req.t('YOUR_CHAT_REGISTRATION_COMPLETED'), user); */
        return sendResponse(res, 200, true, req.t('YOUR_CHAT_REGISTRATION_COMPLETED'));
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.deleteAllChannels = async (req, res) => {
    try {
        const filter = { type: { $in: ['messaging', 'team'] } };
        const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
        channels = channelsRes.map((c) => {
            c.delete();
            return { channel: c.data.cid };
        });
        return sendResponse(res, 200, true, `${channelsRes.length} channels deleted`, channels);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.deleteChannel = async (req, res) => {
    try {
        const { cid } = req.query;
        const filter = { cid: { $in: [cid] } };
        const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
        channelsRes.forEach(async (channel) => {
            const destroy = await channel.delete();
        });
        return sendResponse(res, 200, true, req.t(DS_UPDATED), []);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.addMembersToChannel = async (req, res) => {
    try {
        const { id, memberIds } = req.body;
        await addMembersToChannel(id, memberIds);
        return sendResponse(res, 200, true, req.t(DS_UPDATED), []);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.removeMembersFromChannel = async (req, res) => {
    try {
        const { cid, memberIds } = req.body;
        const filter = { cid: { $in: [cid] } };
        const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
        channelsRes.forEach(async (channel) => {
            await channel.removeMembers(memberIds);
        });
        return sendResponse(res, 200, true, req.t(DS_UPDATED), []);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getIoChannels = async (req, res) => {
    try {
        const { userId, type, id } = req.query;
        const filter = { type: { $in: ['messaging', 'team'] } };
        if (userId) filter.members = { $in: [userId] };
        if (type) filter.type = { $in: [type] };
        if (id) filter.id = { $in: [id] };
        const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
        channels = channelsRes.map((c) => {
            return { channel: c.data };
        });
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), channels);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.ioChannelCreate = async (req, res) => {
    try {
        createChannel(req.body);
        return sendResponse(res, 200, true, 'test', update);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.sendNotification = async (req, res) => {
    try {
        const { token, deviceType, title, body } = req.body;
        const data = { Tile: title, Body: body, title, body };
        sendNotificationPush(token, data, deviceType);
        return sendResponse(res, 200, true, req.t('NOTIFICATION_SENT'));
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const createIoChannel = async (courseInput, userId, user_type, _institution_calendar_id) => {
    // eslint-disable-next-line no-useless-catch
    try {
        localLogger.info(`api_key ${api_key} api_secret ${api_secret}`);
        const { _course_id, year: year_no, level: level_no, term, _program_id } = courseInput;
        const { rotation, rotation_count, admin } = courseInput;
        courseInput._institution_calendar_id = _institution_calendar_id;
        const others = { ...courseInput, admin: admin ? admin._user_id.toString() : userId };
        delete others._id;
        const uniqueChannels = [];
        // push main channel on unique channels
        const rotation_str = rotation === 'yes' ? `_R${rotation_count}` : ``;
        let mainChannelId = `${year_no}_${level_no.replace(' ', '__')}_${_course_id}_${term}`;
        mainChannelId += rotation_str;
        const mainChannel = { id: mainChannelId, subChannels: [], ...others };
        mainChannel.channelName = '';
        mainChannel.channelType = MAIN_CHANNEL;
        uniqueChannels.push(mainChannel);
        // push sub channels inuniqueChannels
        if (user_type === STAFF) {
            const query = {
                _institution_calendar_id,
                _program_id,
                _course_id,
                year_no,
                level_no,
                term,
                rotation,
                type: 'regular',
                isDeleted: false,
                isActive: true,
            };
            if (rotation_count) query.rotation_count = rotation_count;
            query['staffs._staff_id'] = { $in: [userId] };
            const project = { student_groups: 1 };
            const schedules = await CourseSchedules.find(query, project).lean();
            const groups = schedules.map((schedules) => schedules.student_groups).flat(1);
            for (group of groups) {
                const subChannelName = group.group_name.split('-').splice(-2).join('-');
                const subChannelId = `${mainChannelId}_${subChannelName}`;
                const exist = uniqueChannels.find((channel) => channel.id == subChannelId);
                if (!exist) {
                    const subChannel = { id: subChannelId, subChannels: [], ...others };
                    subChannel.channelName = subChannelName;
                    subChannel.channelType = SUB_CHANNEL;
                    uniqueChannels.push(subChannel);
                }
            }
        } else if (user_type.toString().toLowerCase() == STUDENT) {
            const query = {
                _institution_calendar_id,
                'master.year': year_no,
                'master._program_id': _program_id,
                'groups.level': level_no,
                'groups.term': term,
                'groups.rotation': rotation,
                'groups.courses._course_id': _course_id,
                $or: [
                    { 'groups.courses.setting.session_setting.groups._student_ids': userId },
                    { 'groups.group_setting.groups._student_ids': userId },
                    { 'groups.rotation_group_setting._student_ids': userId },
                ],
            };
            if (rotation === 'yes' && rotation_count) {
                query['groups.rotation_group_setting.group_no'] = rotation_count;
            }
            const project = {
                'groups.courses._course_id': 1,
                'groups.courses.course_name': 1,
                'groups.courses.course_no': 1,
                'master.year': 1,
                'master._program_id': 1,
                'groups.level': 1,
                'groups.term': 1,
                'groups.rotation': 1,
                'groups.rotation_count': 1,
                'groups.courses.setting.session_setting.groups.group_no': 1,
                'groups.courses.setting.session_setting.groups.group_name': 1,
                'groups.courses.setting.session_setting.groups._student_ids': 1,
                'groups.courses.setting.gender': 1,
                'groups.group_setting.groups.group_name': 1,
                'groups.group_setting.groups._student_ids': 1,
                'groups.rotation_group_setting._student_ids': 1,
                'groups.rotation_group_setting.group_name': 1,
                'groups.rotation_group_setting.group_no': 1,
            };
            const studentGroups = await StudentGroups.find(query, project);

            const settings = studentGroups
                .map((sg) => sg.groups)
                .flat(1)
                .filter((sg) => sg.term === term && sg.level === level_no);
            for (setting of settings) {
                if (setting.group_setting && setting.group_setting.length > 0) {
                    for (groupSettings of setting.group_setting) {
                        for (group of groupSettings.groups) {
                            const { _student_ids: studentIds, group_no, group_name } = group;
                            if (!studentIds) continue;
                            const isExist = studentIds.find(
                                (sId) => sId.toString() === userId.toString(),
                            );
                            if (isExist) {
                                const subChannelName = group_name.split('-').splice(-2).join('-');
                                const subChannelId = `${mainChannelId}_${subChannelName}`;
                                const exist = uniqueChannels.find(
                                    (channel) => channel.id == subChannelId,
                                );
                                if (!exist) {
                                    const subChannel = {
                                        id: subChannelId,
                                        subChannels: [],
                                        ...others,
                                    };
                                    subChannel.channelName = subChannelName;
                                    subChannel.channelType = SUB_CHANNEL;
                                    uniqueChannels.push(subChannel);
                                }
                            }
                        }
                    }
                } else if (
                    setting.rotation_group_setting &&
                    setting.rotation_group_setting.length > 0 &&
                    rotation === 'yes'
                ) {
                    for (rotation_group of setting.rotation_group_setting) {
                        const { _student_ids: studentIds, group_no, group_name } = rotation_group;
                        if (!studentIds) continue;
                        const isExist = studentIds.find(
                            (sId) => sId.toString() === userId.toString(),
                        );
                        if (isExist) {
                            const subChannelName = group_name.split('-').splice(-2).join('-');
                            const subChannelId = `${mainChannelId}_${subChannelName}`;
                            const exist = uniqueChannels.find(
                                (channel) => channel.id == subChannelId,
                            );
                            if (!exist) {
                                const subChannel = {
                                    id: subChannelId,
                                    subChannels: [],
                                    ...others,
                                };
                                subChannel.channelName = subChannelName;
                                subChannel.channelType = SUB_CHANNEL;
                                uniqueChannels.push(subChannel);
                            }
                        }
                    }
                } else {
                    const courseSettings = setting.courses
                        .filter((course) => course._course_id.toString() === _course_id.toString())
                        .map((course) => course.setting)
                        .flat(1);
                    for (setting of courseSettings) {
                        const groups = setting.session_setting
                            .map((sSetting) => sSetting.groups)
                            .flat(1);
                        const channelName = setting.gender == MALE ? 'MG-1' : 'FG-1';
                        for (group of groups) {
                            const { _student_ids: studentIds, group_no } = group;
                            if (!studentIds) continue;
                            const isExist = studentIds.find(
                                (sId) => sId.toString() === userId.toString(),
                            );
                            if (isExist) {
                                const subChannelName = `${channelName}`;
                                const subChannelId = `${mainChannelId}_${channelName}`;
                                const exist = uniqueChannels.find(
                                    (channel) => channel.id == subChannelId,
                                );
                                if (!exist) {
                                    const subChannel = {
                                        id: subChannelId,
                                        channelType: SUB_CHANNEL,
                                        ...others,
                                    };
                                    subChannel.subChannels = [];
                                    subChannel.channelName = subChannelName;
                                    uniqueChannels.push(subChannel);
                                }
                            }
                        }
                    }
                }
            }
        }
        localLogger.info(`uniqueChannels ${JSON.stringify(uniqueChannels)}`);
        let removeChannels = [];
        if (uniqueChannels.length) {
            const sortedChannels = sortArrayByObjectAscending(uniqueChannels, 'channelType');
            const filters = { id: { $in: sortedChannels.map((c) => c.id) } };
            let allIoChannels = await ioServerClient.queryChannels(filters, {}, { limit: 100 });
            removeChannels = allIoChannels.map((allIoChannel) => allIoChannel.id);

            /* TODO production old data update changes once it done old institution calendar id update and remove here codes
            start line code
           */
            const allIoChannelsCheck = [];
            for (const allIoChannel of allIoChannels) {
                const { id, type } = allIoChannel;
                if (allIoChannel.data.channelType === undefined) {
                    const channelDetails = sortedChannels.find(
                        (channelElement) => channelElement.id.toString() === id,
                    );
                    if (channelDetails) {
                        const channelUpdateObject = {
                            _course_id: channelDetails._course_id,
                            name: channelDetails.channelName,
                            subChannels: null,
                            program_name: channelDetails.program_name,
                            course_type: channelDetails.course_type,
                            end_date: channelDetails.end_date,
                            admin: channelDetails.admin,
                            course_name: channelDetails.course_name,
                            level: channelDetails.level,
                            _program_id: channelDetails._program_id,
                            course_number: channelDetails.course_number,
                            course_name_labels: channelDetails.course_name_labels,
                            start_date: channelDetails.start_date,
                            course_code_labels: channelDetails.course_code_labels,
                            term: channelDetails.term,
                            rotation: channelDetails.rotation,
                            channelName: channelDetails.channelName,
                            _institution_calendar_id: channelDetails._institution_calendar_id,
                            year: channelDetails.year,
                            channelType: SUB_CHANNEL,
                        };
                        const updateChannel = await ioServerClient.channel(type, id, {});
                        await updateChannel.update(channelUpdateObject);
                    }
                }
                if (!allIoChannel.data._institution_calendar_id) {
                    const updateChannel = await ioServerClient.channel(type, id, {});
                    await updateChannel.update({ _institution_calendar_id });
                }
                allIoChannelsCheck.push({
                    ...allIoChannel,
                    'data._institution_calendar_id': _institution_calendar_id,
                });
            }
            allIoChannels = allIoChannelsCheck;
            /* TODO
            end line code
           */
            for (const channel of sortedChannels) {
                const { id, channelType, channelName } = channel;
                const ioChannel = allIoChannels.find(
                    (c) =>
                        c.id === id &&
                        c.data._institution_calendar_id &&
                        c.data._institution_calendar_id.toString() ===
                            _institution_calendar_id.toString(),
                );
                if (!ioChannel) {
                    const params = { id, created_by_id: others.admin, type: 'team', channelName };
                    if (channelType === SUB_CHANNEL) params.membersId = [userId];
                    params.user_type = user_type;
                    await createChannel(params, channel);
                } else {
                    const members = ioChannel.state.members;
                    const mArr = Object.entries(members).map(([id, member]) => member);
                    const moderator = mArr.find((m) => m.channel_role === 'channel_moderator');
                    const channelIo = await ioServerClient.channel(
                        ioChannel.type,
                        ioChannel.id,
                        {},
                    );
                    if (!members[userId]) await addMembersToChannel(id, [userId]);
                    if (
                        moderator &&
                        moderator.user_id.toString() !== others.admin.toString() &&
                        user_type === STAFF
                    ) {
                        await channelIo.addModerators([others.admin]);
                        await channelIo.removeMembers([moderator.user_id]);
                        await channelIo.addMembers([moderator.user_id]);
                    } else if (!moderator && user_type === STAFF) {
                        await channelIo.addModerators([others.admin]);
                    }
                }
            }
            const subChannels = uniqueChannels.filter((g) => g.channelType == SUB_CHANNEL);
            const subChannelsList = [];
            let mainChannelName = '';
            for (subChannel of subChannels) {
                const { id, channelName } = subChannel;
                mainChannelName += mainChannelName.length ? `, ${channelName}` : channelName;
                subChannelsList.push({ id, channelName });
            }
            const mainChannel = uniqueChannels.find((g) => g.channelType === MAIN_CHANNEL);
            let channel = allIoChannels.find((c) => c.id === mainChannel.id);
            channel = await ioServerClient.channel(channel.type, channel.id, {});

            if (channel) {
                if (!channel.data.subChannels) channel.data.subChannels = [];
                for (subChannel of channel.data.subChannels) {
                    const { id, channelName } = subChannel;
                    if (!subChannelsList.find((o) => o.id === id)) {
                        subChannelsList.push({ id, channelName });
                        mainChannelName += mainChannelName.length
                            ? `, ${channelName}`
                            : channelName;
                    }
                }
            }
            mainChannel.channelName = mainChannelName;
            mainChannel.subChannels = subChannelsList;
            await addMembersToChannel(mainChannel.id, [userId], mainChannelName, mainChannel);
            if (channel) {
                const members = channel.state.members;
                const mArr = Object.entries(members).map(([id, member]) => member);
                const moderator = mArr.find((m) => m.channel_role === 'channel_moderator');
                if (
                    moderator &&
                    moderator.user_id.toString() !== others.admin.toString() &&
                    user_type === STAFF
                ) {
                    await channel.addModerators([others.admin]);
                    await channel.removeMembers([moderator.user_id]);
                    await channel.addMembers([moderator.user_id]);
                } else if (!moderator && user_type === STAFF) {
                    await channel.addModerators([others.admin]);
                }
            }
            // const members = channels.length ? channels[0].state.members : {};
            // if (!members[userId]) {
            //     await addMembersToChannel(mainChannel.id, [userId], mainChannelName, mainChannel);
            // }
        }
        return removeChannels;
    } catch (error) {
        console.log('error', error);
        throw error;
    }
};

exports.getCourseWithCoordinators = async (courses) => {
    const courseIds = courses.map((course) => course._course_id);
    const tempCourses = await Course.find({ _id: { $in: courseIds } });
    for (const course of courses) {
        const tempCourse = tempCourses.find(
            (tc) => tc._id.toString() === course._course_id.toString(),
        );
        const admins = tempCourse.coordinators || [];
        let admin = null;
        if (tempCourse && admins.length) {
            const { year, level, term } = course;
            admin = admins.find((a) => a.year === year && a.level_no === level && a.term === term);
            if (admin) await ioRegister(admin._user_id);
        }
        course.admin = admin;
    }
    return courses;
};

exports.createCourseChannels = async (req, res) => {
    try {
        const { userId, type, institutionCalendarId } = req.body;
        // Todo Deactivating this for User Overloading
        /* if (SERVICES.CHAT_ENABLED && SERVICES.CHAT_ENABLED === 'true') {
            let courses = await getDashboardCoursesList(userId, type, institutionCalendarId);
            courses = await this.getCourseWithCoordinators(courses);
            const allIoChannelIds = [];
            if (courses.length) {
                for (course of courses) {
                    const courseRemoveChennels = await createIoChannel(
                        course,
                        userId,
                        type,
                        institutionCalendarId,
                    );
                    if (courseRemoveChennels.length) allIoChannelIds.push(...courseRemoveChennels);
                }
            }
            const userGetAllChennels = await ioServerClient.queryChannels(
                {
                    members: { $in: [userId] },
                    _institution_calendar_id: institutionCalendarId,
                },
                {},
                { limit: 100 },
            );
            const removeChannels = userGetAllChennels.filter(
                (userGetAllChennel) => !allIoChannelIds.includes(userGetAllChennel.id),
            );
            if (removeChannels && removeChannels.length) {
                for (const removeChannel of removeChannels) {
                    const { id } = removeChannel;
                    const ioChannel = removeChannels.find(
                        (c) =>
                            c.id === id &&
                            c.data._institution_calendar_id &&
                            c.data._institution_calendar_id.toString() ===
                                institutionCalendarId.toString(),
                    );
                    const channelIo = await ioServerClient.channel(
                        ioChannel.type,
                        ioChannel.id,
                        {},
                    );
                    if (channelIo) {
                        const members = ioChannel.state.members;
                        const channelMembers = Object.entries(members).map(
                            ([id, member]) => member,
                        );
                        const moderator = channelMembers.find(
                            (channelMember) => channelMember.channel_role === 'channel_moderator',
                        );
                        if (moderator && type === STAFF) {
                            await channelIo.removeMembers([moderator.user_id]);
                        } else if (!moderator && type === STAFF) {
                            await channelIo.removeMembers([userId]);
                        } else {
                            if (members[userId]) await channelIo.removeMembers([userId]);
                        }
                    }
                }
            }
        } */
        localLogger.info('success on createCourseChannels');
        return sendResponse(res, 200, true, req.t('YOUR_CHANNELS_CREATED'));
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getLocalLogger = (req, res) => {
    fs.readFile('./public/logs/' + this.getDateString(), 'utf8', function (err, data) {
        res.send(data);
    });
};

exports.getDateString = () => {
    const today = new Date();
    const logfileByDate =
        today.getUTCDate() + '-' + parseInt(today.getUTCMonth() + 1) + '-' + today.getUTCFullYear();
    return logfileByDate + '.log';
};

exports.getSubjects = async (req, res) => {
    try {
        const {
            _institution_calendar_id,
            _course_id,
            year_no,
            level_no,
            term,
            _program_id,
            rotation,
            rotation_count,
            staffs,
            group_id,
        } = req.body;

        const getSubjects = await getStudentSubjects(
            _institution_calendar_id,
            _course_id,
            year_no,
            level_no,
            term,
            _program_id,
            rotation,
            rotation_count,
            staffs,
            group_id,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), getSubjects);
    } catch (error) {
        localLogger.info('exception on createCourseChannels' + JSON.stringify(error));
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.removeUserIoToken = async (req, res) => {
    try {
        const { userId, isAll } = req.body;
        const query = {};
        if (isAll === 'FALSE') query._id = userId;
        const data = { ioToken: null };
        await User.updateMany(query, { $set: data });
        return sendResponse(res, 200, true, 'data', 'removed');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.deleteMessage = async (req, res) => {
    try {
        const { channelId, messageID } = req.body;
        if (!channelId || !messageId)
            return sendResponse(res, 200, true, 'error', 'channelId, messageId required');
        const filter = { type: { $in: ['messaging', 'team'] } };
        if (id) filter.id = { $in: [channelId] };
        const channels = await ioServerClient.queryChannels(filter, {}, { state: true });
        let result;
        for (const channel of channels) result = await channel.deleteMessage(messageID, true);
        return sendResponse(res, 200, true, req.t(DS_DELETED), 'removed');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.blockUser = async (req, res) => {
    try {
        const { channelId, memberId } = req.body;
        const query = { isDeleted: false, channelId };
        const channel = await DigiChat.findOne(query);
        let result;
        if (!channel) {
            const data = { channelId, members: [{ id: memberId, isBlocked: true }] };
            result = await DigiChat.create(data);
        } else {
            const conditions = { channelId, 'members.id': { $ne: memberId } };
            const update = { $addToSet: { members: { id: memberId, isBlocked: true } } };
            result = await DigiChat.findOneAndUpdate(conditions, update);
        }
        const data = await DigiChat.find(query);
        return sendResponse(res, 200, true, req.t('USER_BLOCKED'), data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.unBlockUser = async (req, res) => {
    try {
        const { channelId, memberId } = req.body;
        const query = { isDeleted: false, channelId };
        const channel = await DigiChat.findOne(query);
        if (channel) {
            await DigiChat.update({ channelId }, { $pull: { members: { id: memberId } } });
            const data = await DigiChat.find(query);
            return sendResponse(res, 200, true, req.t('USER_UNBLOCKED'), data);
        }
        return sendResponse(res, 200, true, req.t('NO_CHANNEL_FOUND'), data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getBlockedUsers = async (req, res) => {
    try {
        const { channelId } = req.body;
        const query = { isDeleted: false, channelId };
        const data = await DigiChat.findOne(query)
            .populate({
                path: 'members.id',
                select: { name: 1, user_id: 1, user_type: 1 },
            })
            .lean();
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};
