const Joi = require('joi');
const { objectIdRQSchema, stringRQSchema } = require('../utility/validationSchemas');

const programYearLevelListValidation = {
    schema: Joi.object({
        programId: objectIdRQSchema,
    }),
    property: 'query',
};

const upsertYearLevelAuthorValidation = {
    schema: Joi.object({
        programId: objectIdRQSchema,
        curriculumId: objectIdRQSchema,
        yearName: stringRQSchema,
        levelName: Joi.string().when('authorType', {
            is: 'level',
            then: stringRQSchema,
            otherwise: Joi.allow(null),
        }),
        users: Joi.array()
            .items(
                Joi.object({
                    userId: objectIdRQSchema,
                    modules: Joi.array()
                        .items(Joi.string().valid('studentGroup', 'schedule'))
                        .required(),
                }),
            )
            .required(),
        authorType: Joi.string().valid('year', 'level', 'studentGroup', 'schedule').required(),
        termName: Joi.string().optional(),
    }),
    property: 'body',
};

const userProgramListValidation = {
    schema: Joi.object({
        institutionCalendarId: Joi.string().optional(),
    }),
    property: 'query',
};

const userAuthorProgramListValidation = {
    schema: Joi.object({
        institutionCalendarId: Joi.string().optional(),
        userId: objectIdRQSchema.optional(),
        module: Joi.string().valid('schedule', 'studentGroup').optional(),
        withDetail: Joi.boolean().optional(),
    }),
    property: 'query',
};

const staffListValidation = {
    schema: Joi.object({
        programId: objectIdRQSchema,
        institutionCalendarId: objectIdRQSchema,
    }),
    property: 'query',
};

module.exports = {
    programYearLevelListValidation,
    upsertYearLevelAuthorValidation,
    userProgramListValidation,
    userAuthorProgramListValidation,
    staffListValidation,
};
