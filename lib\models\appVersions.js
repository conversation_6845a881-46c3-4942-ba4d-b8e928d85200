const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {
    APP_VERSION_CONTROL,
    IOS,
    ANDROID,
    WEB,
    DC_STUDENT,
    DC_STAFF,
} = require('../utility/constants');

const appVersion = new Schema(
    {
        osType: {
            type: String,
            required: true,
            enum: [IOS, ANDROID, WEB],
        },
        appType: {
            type: String,
            required: true,
            enum: [DC_STUDENT, DC_STAFF],
        },
        version: {
            type: String,
            required: true,
        },
        deploymentType: {
            type: String,
            required: true,
        },
        comments: {
            type: String,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(APP_VERSION_CONTROL, appVersion);
