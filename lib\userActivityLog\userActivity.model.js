const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { USER_ACTIVITY_LOG, USER, INSTITUTION } = require('../utility/constants');

const userActivitySchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        userId: { type: ObjectId, ref: USER },
        role: { type: String },
        method: { type: String },
        action: { type: String },
        host: { type: String },
        url: { type: String },
        data: { type: Object },
        message: { type: String },
    },
    {
        timestamps: true,
    },
);
module.exports = model(USER_ACTIVITY_LOG, userActivitySchema);
