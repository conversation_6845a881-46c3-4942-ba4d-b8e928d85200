const curriculamSchema = require('../../models/digi_curriculum');
const { convertToMongoObjectId } = require('../../utility/common');
const formSettingCourseSchema = require('./formSettingCourses.model');
const { PUBLISHED, DRAFT } = require('../../utility/constants');
const formSettingSchema = require('./formSetting.model');
const courseSchema = require('../../models/digi_course');
const { checkDuplicateValue } = require('../categories/qapcCategory.service');
const formCourseGroupSchema = require('./formCourseGroups.module');
const qapcFormInitiatorSchema = require('../formInitiator/qapcFormInitiator.model');
//add the published status in form course setting
const updateFormCourseSetting = async ({ categoryFormId, _institution_id }) => {
    try {
        await formSettingCourseSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                categoryFormId: convertToMongoObjectId(categoryFormId),
                isActive: true,
                isDeleted: false,
                status: DRAFT,
            },
            {
                $set: {
                    status: PUBLISHED,
                },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCourseGroupStatus = async ({ categoryFormId, _institution_id }) => {
    try {
        await formCourseGroupSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                categoryFormId: convertToMongoObjectId(categoryFormId),
                isActive: true,
                isDeleted: false,
            },
            {
                $set: {
                    status: PUBLISHED,
                },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDraftFormSetting = async ({ _institution_id, categoryId }) => {
    try {
        const formSettingIds = await formSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(categoryId),
                    status: DRAFT,
                    isActive: true,
                    isDeleted: false,
                    archive: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        return { formSettingIds };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const checkDuplicateFormName = async ({ formName, categoryId, categoryFormId }) => {
    try {
        const checkFormNameCount = await formSettingSchema
            .findOne(
                {
                    ...(categoryFormId && { _id: { $ne: convertToMongoObjectId(categoryFormId) } }),
                    categoryId: convertToMongoObjectId(categoryId),
                    formName: checkDuplicateValue({ searchKey: formName }),
                },
                { _id: 1 },
            )
            .lean();
        return { checkFormNameCount };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCurriculumData = async ({ _institution_id, programId }) => {
    try {
        return await curriculamSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: {
                        $in: programId.map((programElement) =>
                            convertToMongoObjectId(programElement),
                        ),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _program_id: 1,
                    program_name: 1,
                    curriculum_name: 1,
                    'year_level.y_type': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCourseList = async ({ programId, curriculumIds }) => {
    try {
        return await courseSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    $or: [
                        {
                            _program_id: {
                                $in: programId.map((programElement) =>
                                    convertToMongoObjectId(programElement),
                                ),
                            },
                            _curriculum_id: {
                                $in: curriculumIds,
                            },
                        },
                        {
                            'course_assigned_details.course_shared_with._program_id': {
                                $in: programId.map((programElement) =>
                                    convertToMongoObjectId(programElement),
                                ),
                            },
                            'course_assigned_details.course_shared_with._curriculum_id': {
                                $in: curriculumIds,
                            },
                        },
                    ],
                },
                {
                    course_code: 1,
                    course_name: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    course_type: 1,
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details._curriculum_id': 1,
                    'course_assigned_details.year': 1,
                    'course_assigned_details.course_shared_with._program_id': 1,
                    'course_assigned_details.course_shared_with.year': 1,
                    'course_assigned_details.course_shared_with._curriculum_id': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const programCurriculumList = async ({ programId, programList, curriculamData }) => {
    try {
        programId.forEach((programElement) => {
            if (!programList[programElement]) {
                programList[programElement] = {
                    program_name: '',
                    curriculum: {},
                };
            }
            curriculamData.forEach((curriculamElement) => {
                if (!programList[programElement].program_name) {
                    programList[programElement].program_name = curriculamElement.program_name;
                }
                const years = {};
                curriculamElement.year_level.forEach((yearElement) => {
                    years[yearElement.y_type] = { courseIds: [] };
                });
                const curriculumEntry = {
                    curriculum_name: curriculamElement.curriculum_name,
                    years,
                };
                programList[programElement].curriculum =
                    programList[programElement].curriculum || {};
                programList[programElement].curriculum[curriculamElement._id] = curriculumEntry;
            });
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const assignCoursesToPrograms = async ({ programList, courseList }) => {
    try {
        Object.entries(programList).forEach(([programIndex, programElement]) => {
            courseList.forEach((courseElement) => {
                Object.entries(programElement.curriculum).forEach(
                    ([curriculumIndex, curriculumElement]) => {
                        Object.entries(curriculumElement.years).forEach(
                            ([yearIndex, yearElement]) => {
                                const isMatchingCurriculum =
                                    programIndex.toString() ===
                                        courseElement._program_id.toString() &&
                                    curriculumIndex.toString() ===
                                        courseElement._curriculum_id.toString();

                                let isSharedFrom = false;
                                let shared_with_others = false;

                                if (isMatchingCurriculum) {
                                    shared_with_others = false;
                                }

                                if (
                                    courseElement.course_assigned_details &&
                                    courseElement.course_assigned_details.length
                                ) {
                                    courseElement.course_assigned_details.forEach(
                                        (courseSharedElement) => {
                                            const isMatchingShared =
                                                courseSharedElement.course_shared_with.find(
                                                    (courseSharedWithElement) =>
                                                        courseSharedWithElement._program_id &&
                                                        courseSharedWithElement._curriculum_id &&
                                                        courseSharedWithElement._program_id.toString() ===
                                                            programIndex.toString() &&
                                                        courseSharedWithElement._curriculum_id.toString() ===
                                                            curriculumIndex.toString(),
                                                );

                                            isSharedFrom =
                                                courseSharedElement._program_id.toString() !==
                                                    programIndex.toString() &&
                                                courseSharedElement._curriculum_id.toString() !==
                                                    curriculumIndex.toString();

                                            if (
                                                isMatchingShared &&
                                                isMatchingShared.year.toLowerCase() ===
                                                    yearIndex.toLowerCase()
                                            ) {
                                                yearElement.courseIds.push({
                                                    courseId: courseElement._id,
                                                    course_code: courseElement.course_code,
                                                    course_name: courseElement.course_name,
                                                    course_type: courseElement.course_type,
                                                    shared_with_others: true,
                                                    shared_from_others: isSharedFrom,
                                                });
                                            }
                                        },
                                    );
                                }

                                if (!shared_with_others) {
                                    const yearData = courseElement.course_assigned_details.find(
                                        (courseYear) =>
                                            courseYear._program_id.toString() ===
                                                programIndex.toString() &&
                                            courseYear._curriculum_id.toString() ===
                                                curriculumIndex.toString() &&
                                            courseYear.year.toLowerCase() ===
                                                yearIndex.toLowerCase(),
                                    );
                                    if (
                                        yearData &&
                                        yearData.year.toLowerCase() === yearIndex.toLowerCase()
                                    ) {
                                        yearElement.courseIds.push({
                                            courseId: courseElement._id,
                                            course_code: courseElement.course_code,
                                            course_name: courseElement.course_name,
                                            course_type: courseElement.course_type,
                                            shared_with_others: false,
                                            shared_from_others: isSharedFrom,
                                        });
                                    }
                                }
                            },
                        );
                    },
                );
            });
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const isCreatedFromInitiator = async ({ categoryFormId }) => {
    try {
        return await qapcFormInitiatorSchema
            .findOne(
                {
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                },
                { _id: 1 },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

module.exports = {
    updateFormCourseSetting,
    getDraftFormSetting,
    checkDuplicateFormName,
    getCurriculumData,
    getCourseList,
    programCurriculumList,
    assignCoursesToPrograms,
    updateCourseGroupStatus,
    isCreatedFromInitiator,
};
