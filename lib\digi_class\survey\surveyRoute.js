const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const {
    getSessionSurvey,
    updateSessionSurvey,
    //courseList,
    getCourses,
    createCourseMockSurvey,
    selfEvaluation,
    yetToRate,
    comparisonReport,
    getScheduleSessionSurvey,
    updateScheduleSessionSurvey,
} = require('./surveyController');
// const {
//     pushStudentSchema: { body: pushStudentBodySchema },

//     updateCourseSessionSchema: {
//         body: updateCourseSessionBodySchema,
//         params: updateCourseSessionParamSchema,
//     },
//     getCoursesParamSchema: { params: getCoursesParamSchema },
// } = require('./surveyValidatorSchema');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
route.get(
    '/sessionSurvey/:userId/:institutionCalendarId/:programId/:courseId/:sessionId/:yearNo/:levelNo/:term',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getSessionSurvey,
);

route.get(
    '/scheduleSessionSurvey/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getScheduleSessionSurvey,
);

//route.get('/courseList/:userId/:_institution_calendar_id', courseList);
route.get(
    '/getCourses',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourses,
);

route.get(
    '/selfEvaluation/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    selfEvaluation,
);

route.get(
    '/comparisonReport/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    comparisonReport,
);

route.get(
    '/yetToRate/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    yetToRate,
);

route.put(
    '/sessionSurvey',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateSessionSurvey,
);
route.put(
    '/scheduleSessionSurvey',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateScheduleSessionSurvey,
);
route.post(
    '/courseSessionMock',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    createCourseMockSurvey,
);
module.exports = route;
