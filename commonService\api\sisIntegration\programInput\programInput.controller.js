// const termCalendarSchemas = require('./termCalendar.model');
// const institutionCalendarSchemas = require('../../../../lib/models/institution');
const studentGroupSchemas = require('../../../../lib/models/student_group');
const programCalendarSchemas = require('../../../../lib/models/program_calendar');
const courseScheduleSchemas = require('../../../../lib/models/course_schedule');
const userSchemas = require('../../../../lib/models/user');
const institutionCalendarSchemas = require('../../../../lib/models/institution_calendar');
const courseScheduleDeliverySettingSchemas = require('../../../../lib/models/course_schedule_delivery_settings');
const infrastructureSchemas = require('../../../../lib/models/infrastructure_management');
const remoteInfraSchema = require('../../../../lib/models/course_schedule_setting');
const { convertToMongoObjectId, clone } = require('../../../utility/common');
const {
    getCalendarTermList,
    getEnrollStudentList,
    getCourseList,
    getCourseScheduleSettingList,
} = require('./programInput.service');
const {
    GENDER,
    MIXED,
    PENDING,
    MISSED,
    PUBLISHED,
    REMOTE_PLATFORM,
} = require('../../../utility/constants');
const moment = require('moment');
const { updateStudentGroupRedisKey } = require('../../../../lib/utility/utility.service');
const { getCourseSessionOrderData } = require('./programInput.helper');

const getTermDetails = async ({ query = {} }) => {
    try {
        const { termCode } = query;
        const calendarTermList = await getCalendarTermList({ termCode });
        const calendarListing = calendarTermList.map((calendarTermElement) => {
            return {
                collegeCode: calendarTermElement.collegeCode,
                collegeType: calendarTermElement.collegeType,
                academicYear: calendarTermElement.academicYear,
                termCode: calendarTermElement.termCode,
            };
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { calendarListing, calendarTermList },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getEnrollStudentDetails = async ({ query = {} }) => {
    try {
        const { termCode, courseId, classNo } = query;
        const enrollStudentList = await getEnrollStudentList({ termCode, courseId, classNo });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: enrollStudentList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseDetails = async ({ query = {} }) => {
    try {
        const { termCode, courseId, courseDescription } = query;
        const courseList = await getCourseList({
            filterKey: 'termCode',
            termCode,
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: courseList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentGroupStudentPull = async ({ body = {}, headers = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            studentGroupId,
            courseId,
            yearNo,
            levelNo,
            term,
        } = body;
        const { user_id, _user_id } = headers;

        // Program Calendar List
        console.time('programCalendarData');
        const programCalendarData = await programCalendarSchemas.findOne(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                'level.year': yearNo,
                'level.level_no': levelNo,
                'level.term': term,
                'level.course._course_id': convertToMongoObjectId(courseId),
            },
            {
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.course._course_id': 1,
                'level.course.courses_number': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
            },
        );
        console.timeEnd('programCalendarData');
        const levelData = programCalendarData.level.find(
            (levelElement) =>
                levelElement.year === yearNo &&
                levelElement.level_no === levelNo &&
                levelElement.term === term,
        );
        if (!levelData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        const courseData = levelData.course.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        const courseCode = courseData.courses_number.split(' ');
        const dateFormatter = (inwardDate) => {
            const dateFormate = new Date(inwardDate);
            return dateFormate.toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'numeric',
                year: 'numeric',
            });
        };
        // const sqlQueryWhere = `WHERE A.START_DT='${dateFormatter(
        //     courseData.start_date,
        // )}' AND A.END_DT='${dateFormatter(courseData.end_date)}' AND A.CATALOG_NBR='${
        //     courseCode[1]
        // }' AND A.SUBJECT='${courseCode[0]}'`;
        const sqlQueryWhere = `START_DT = TO_DATE(:startDt, 'DD/MM/YYYY') AND END_DT = TO_DATE(:endDt, 'DD/MM/YYYY') AND CATALOG_NBR = :catalogNbr AND SUBJECT = :subject`;
        const sqlQueryWhereParams = {
            params: {
                startDt: dateFormatter(courseData.start_date),
                endDt: dateFormatter(courseData.end_date),
                catalogNbr: courseCode[1],
                subject: courseCode[0],
            },
        };
        const courseObjects = {
            levelStartDate: levelData.start_date,
            levelEndDate: levelData.end_date,
            subject: courseCode[0],
            catalogNo: courseCode[1],
            courseNumber: courseData.courses_number,
            courseStartDate: courseData.start_date,
            courseEndDate: courseData.end_date,
            courseSqlQuery: `SELECT * FROM PS_U_CLASS_CRSE_VW A WHERE ${sqlQueryWhere};`,
            sqlQueryWhereParams,
        };
        // Course Listing From SIS
        console.time('courseList');
        const courseList = await getCourseList({
            filterKey: 'query',
            startDate: courseData.start_date,
            endDate: courseData.end_date,
            subject: courseCode[0],
            catalogNo: courseCode[1],
        });
        console.timeEnd('courseList');
        if (!courseList || !courseList.length)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        courseObjects.courseStudentSqlQuery = `SELECT * FROM PS_U_ENROL_STUD_VW A WHERE A.STRM='${courseList[0].courseTerm}' AND A.CRSE_ID='${courseList[0].courseId}' AND A.CLASS_NBR=${courseList[0].classNo};`;
        console.time('enrollStudentList');
        const enrollStudentList = await getEnrollStudentList({
            termCode: courseList[0].courseTerm,
            courseId: courseList[0].courseId,
            // classNo: courseList[0].classNo,
        });
        console.timeEnd('enrollStudentList');
        const studentUserIds = enrollStudentList.map((studentElement) => studentElement.studentId);
        console.time('studentDetails');
        const studentDetails = await userSchemas.find(
            { user_id: { $in: studentUserIds } },
            { name: 1, gender: 1, email: 1, user_id: 1 },
        );
        console.timeEnd('studentDetails');

        console.time('studentGroup');
        const studentGroupData = await studentGroupSchemas.findOne(
            {
                _id: convertToMongoObjectId(studentGroupId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses._course_id': 1,
                'groups.courses.setting.ungrouped': 1,
                'groups.courses.setting.gender': 1,
                'groups.courses.setting.session_setting.groups._student_ids': 1,
                'groups.students._student_id': 1,
            },
        );
        console.timeEnd('studentGroup');
        if (!studentGroupData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Student Group Not Found',
            };
        const groupLevelData = studentGroupData.groups.find(
            (groupElement) => groupElement.level === levelNo && groupElement.term === term,
        );
        if (!groupLevelData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Student Group Not Found',
            };
        const courseGroupData = groupLevelData.courses.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseGroupData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Course Not Found',
            };
        const studentCourseIds = groupLevelData.courses.map((courseElement) => {
            return { _course_id: courseElement._course_id, status: 'pending' };
        });
        const newStudentImport = [];
        const studentUngroupIds =
            courseList[0].campus === MIXED
                ? [
                      {
                          gender: GENDER.BOTH,
                          ungrouped: [],
                      },
                  ]
                : [
                      {
                          gender: GENDER.MALE,
                          ungrouped: [],
                      },
                      {
                          gender: GENDER.FEMALE,
                          ungrouped: [],
                      },
                  ];
        for (studentElement of studentDetails) {
            const groupGenderIndex =
                courseList[0].campus === MIXED
                    ? 0
                    : studentUngroupIds.findIndex(
                          (genderGroupElement) =>
                              genderGroupElement.gender === studentElement.gender,
                      );
            if (
                !groupLevelData.students.find(
                    (studentGroupElement) =>
                        studentGroupElement._student_id.toString() ===
                        studentElement._id.toString(),
                )
            ) {
                newStudentImport.push({
                    _student_id: convertToMongoObjectId(studentElement._id),
                    academic_no: studentElement.user_id,
                    name: studentElement.name,
                    gender: studentElement.gender,
                    mark: 5,
                    imported_on: new Date(),
                    _imported_by: user_id || _user_id,
                    imported_by: { first: 'SIS' },
                    master_group_status: 'published',
                    course_group_status: studentCourseIds,
                });

                if (groupGenderIndex !== -1)
                    studentUngroupIds[groupGenderIndex].ungrouped.push(
                        convertToMongoObjectId(studentElement._id),
                    );
            } else {
                if (courseGroupData.setting.length)
                    for (courseSettingElement of courseGroupData.setting) {
                        if (
                            courseList[0].campus === MIXED
                                ? true
                                : courseSettingElement.gender === studentElement.gender
                        ) {
                            let settingStudentIds = [];
                            for (sessionSettingElement of courseSettingElement.session_setting) {
                                for (groupElement of sessionSettingElement.groups) {
                                    settingStudentIds = [
                                        ...settingStudentIds,
                                        ...groupElement._student_ids,
                                    ];
                                }
                            }
                            if (
                                !courseSettingElement.ungrouped.find(
                                    (ungroupedElement) =>
                                        ungroupedElement.toString() ===
                                        studentElement._id.toString(),
                                ) &&
                                !settingStudentIds.find(
                                    (settingStudentIdElement) =>
                                        settingStudentIdElement.toString() ===
                                        studentElement._id.toString(),
                                )
                            ) {
                                if (groupGenderIndex !== -1)
                                    studentUngroupIds[groupGenderIndex].ungrouped.push(
                                        convertToMongoObjectId(studentElement._id),
                                    );
                            }
                        }
                    }
            }
        }
        let sgPushSetting = {};
        const sgPushSettingFilter = [];
        let studentIds = [];
        if (courseGroupData.setting.length)
            for (studentUngroupIdElement of studentUngroupIds) {
                if (studentUngroupIdElement.ungrouped.length) {
                    const settingIndexKey = `groups.$[i].courses.$[k].setting.$[${studentUngroupIdElement.gender}].ungrouped`;
                    const settingFilterIndexKey = `${studentUngroupIdElement.gender}.gender`;
                    sgPushSetting = {
                        ...sgPushSetting,
                        [settingIndexKey]: studentUngroupIdElement.ungrouped,
                    };
                    studentIds = [...studentIds, ...studentUngroupIdElement.ungrouped];
                    if (
                        !sgPushSettingFilter.find((sgPushSettingFilterElement) =>
                            Object.keys(sgPushSettingFilterElement).find(
                                (key) =>
                                    sgPushSettingFilterElement[key] ===
                                    studentUngroupIdElement.gender,
                            ),
                        )
                    ) {
                        sgPushSettingFilter.push({
                            [settingFilterIndexKey]: studentUngroupIdElement.gender,
                        });
                    }
                }
            }
        const studentGroupObject = {
            ...(!courseGroupData.setting.length && {
                $set: {
                    'groups.$[i].courses.$[k].setting': studentUngroupIds,
                    'groups.$[i].courses.$[k].gender_mixed': !!(courseList[0].campus === MIXED),
                },
            }),
            ...((newStudentImport.length || Object.keys(sgPushSetting).length) && {
                $push: {
                    ...(newStudentImport.length && { 'groups.$[i].students': newStudentImport }),
                    ...sgPushSetting,
                },
            }),
            ...(studentIds.length && {
                $pull: {
                    'groups.$[i].courses.$[k]._removed_student_ids': { $in: studentIds },
                },
            }),
        };
        const studentGroupFilter = {
            arrayFilters: [
                {
                    'i.level': levelNo,
                    'i.term': term,
                },
                { 'k._course_id': convertToMongoObjectId(courseId) },
                ...sgPushSettingFilter,
            ],
        };
        if (Object.keys(studentGroupObject).length)
            console.log(
                await studentGroupSchemas.updateOne(
                    { _id: convertToMongoObjectId(studentGroupId) },
                    studentGroupObject,
                    studentGroupFilter,
                ),
            );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: 'Course StudentGroup Updated',
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const findDifferenceInArray = (fromArray, toArray) => {
    let alterStudentArray = clone(toArray);
    const pushIds = fromArray.filter(
        (fromArrayElement) =>
            !toArray.find(
                (toArrayElement) => toArrayElement.toString() === fromArrayElement.toString(),
            ),
    );
    if (pushIds.length) alterStudentArray = [...alterStudentArray, ...pushIds];
    const pullIds = toArray.filter(
        (toArrayElement) =>
            !fromArray.find(
                (fromArrayElement) => fromArrayElement.toString() === toArrayElement.toString(),
            ),
    );
    if (pullIds.length)
        alterStudentArray = alterStudentArray.filter(
            (alterStudentArrayElement) =>
                !pullIds.find(
                    (pullIdElement) =>
                        pullIdElement.toString() === alterStudentArrayElement.toString(),
                ),
        );
    return { pushIds, pullIds, alterStudentArray };
};

const getStudentGroupStudentPullWithGroup = async ({ body = {}, headers = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            studentGroupId,
            courseId,
            yearNo,
            levelNo,
            term,
        } = body;
        const { user_id, _user_id } = headers;
        // Program Calendar List
        console.time('programCalendarData');
        const programCalendarData = await programCalendarSchemas.findOne(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                'level.year': yearNo,
                'level.level_no': levelNo,
                'level.term': term,
                'level.course._course_id': convertToMongoObjectId(courseId),
            },
            {
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.course._course_id': 1,
                'level.course.courses_number': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
            },
        );
        console.timeEnd('programCalendarData');
        const levelData = programCalendarData.level.find(
            (levelElement) =>
                levelElement.year === yearNo &&
                levelElement.level_no === levelNo &&
                levelElement.term === term,
        );
        if (!levelData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        const courseData = levelData.course.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        const courseCode = courseData.courses_number.split(' ');
        // Course Listing From SIS
        console.time('courseList');
        let courseList = await getCourseList({
            filterKey: 'query',
            startDate: courseData.start_date,
            endDate: courseData.end_date,
            subject: courseCode[0],
            catalogNo: courseCode[1],
        });
        console.timeEnd('courseList');
        if (!courseList || !courseList.length)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        courseList = Array.from(new Map(courseList.map((item) => [item.classNo, item])).values());
        // return { data: courseList };
        let courseDeliveryCode = courseList.map((courseElement) => courseElement.deliveryCode);
        courseDeliveryCode = [...new Set(courseDeliveryCode)];
        console.time('studentGroup');
        const studentGroupData = await studentGroupSchemas.findOne(
            {
                _id: convertToMongoObjectId(studentGroupId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.rotation': 1,
                'groups.group_name': 1,
                'groups.courses._course_id': 1,
                'groups.courses.course_no': 1,
                'groups.courses.session_types.session_type': 1,
                'groups.courses.session_types.symbol': 1,
                'groups.courses.setting.ungrouped': 1,
                'groups.courses.setting.gender': 1,
                'groups.courses.setting.session_setting.delivery_type': 1,
                'groups.courses.setting.session_setting.session_type': 1,
                'groups.courses.setting.session_setting.no_of_group': 1,
                'groups.courses.setting.session_setting.no_of_student': 1,
                'groups.courses.setting.session_setting.groups._student_ids': 1,
                'groups.courses.setting.session_setting.groups.group_no': 1,
                'groups.courses.setting.session_setting.groups._id': 1,
                'groups.students._student_id': 1,
            },
        );
        console.timeEnd('studentGroup');
        if (!studentGroupData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Student Group Not Found',
            };
        const groupLevelData = studentGroupData.groups.find(
            (groupElement) => groupElement.level === levelNo && groupElement.term === term,
        );
        if (!groupLevelData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Student Group Not Found',
            };
        const courseGroupData = groupLevelData.courses.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseGroupData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Course Not Found',
            };

        console.info({
            delivery: courseDeliveryCode,
            genderMixed: courseList[0].campus === MIXED,
        });
        console.time('enrollStudentList');
        const enrollStudentList = await getEnrollStudentList({
            termCode: courseList[0].courseTerm,
            courseId: courseList[0].courseId,
            // classNo: courseList[0].classNo,
        });
        console.timeEnd('enrollStudentList');
        const studentUserIds = enrollStudentList.map((studentElement) => studentElement.studentId);
        console.time('studentDetails');
        const studentDetails = await userSchemas.find(
            { user_id: { $in: studentUserIds } },
            { name: 1, gender: 1, email: 1, user_id: 1 },
        );
        console.timeEnd('studentDetails');
        const nonRegisteredStudent = enrollStudentList.filter(
            (studentElement) =>
                !studentDetails.find(
                    (studentDetailElement) =>
                        studentDetailElement.user_id === studentElement.studentId,
                ),
        );
        const objectDuplicateCheck = (sgPushSettingFilter, checkObjectKey) => {
            return !sgPushSettingFilter.find((sgPushSettingFilterElement) =>
                Object.keys(sgPushSettingFilterElement).some((key) => key === checkObjectKey),
            );
        };
        const studentCourseIds = groupLevelData.courses.map((courseElement) => {
            return { _course_id: courseElement._course_id, status: 'published' };
        });
        let studentGroupObject = {};
        let studentGroupFilter = {};
        let sgPushSetting = {};
        // let sgPullSetting = {};
        let sgSetSetting = {};
        const sgPushSettingFilter = [];
        const sgPushNewGroupSettingFilter = [];
        let sgGenderPushSetting = {};
        const courseScheduleBulkWrite = [];
        const studentGroupName = `${groupLevelData.group_name}-${courseGroupData.course_no}-G`;
        const courseGroupGenderList = [
            ...new Set(courseList.map((courseListElement) => courseListElement.gender)),
        ];
        if (!courseGroupData.setting.length) {
            const courseGroupSetting = [];
            let overAllStudentIds = [];
            for (genderElement of courseGroupGenderList) {
                const deliverySetting = [];
                for (sessionTypeSetting of courseGroupData.session_types) {
                    const courseDeliveryBasedGroups = courseList
                        .filter(
                            (courseElement) =>
                                courseElement.deliveryCode === sessionTypeSetting.symbol &&
                                genderElement === courseElement.gender,
                        )
                        .sort((a, b) => {
                            if (a.gender === b.gender) {
                                return a.groupNo.localeCompare(b.groupNo);
                            }
                            return a.gender.localeCompare(b.gender);
                        });
                    const deliveryGroups = [];
                    let deliveryStudentCount = 0;
                    for (deliveryElement of courseDeliveryBasedGroups) {
                        // console.info({
                        //     symbol: sessionTypeSetting.symbol,
                        //     groupNo: deliveryElement.groupNo,
                        //     gender: deliveryElement.gender,
                        //     classNo: deliveryElement.classNo,
                        //     classType: deliveryElement.classType,
                        // });
                        let deliveryClassNo = deliveryElement.classNo;
                        if (deliveryElement.classType === 'No') {
                            const enrolledClass = courseList.find(
                                (deliveryGroupElement) =>
                                    deliveryGroupElement.gender.toString() ===
                                        deliveryElement.gender.toString() &&
                                    deliveryGroupElement.groupNo.toString() ===
                                        deliveryElement.groupNo.toString() &&
                                    deliveryGroupElement.classType === 'E',
                            );
                            deliveryClassNo =
                                enrolledClass && enrolledClass.classNo
                                    ? enrolledClass.classNo
                                    : deliveryElement.classNo;
                        }
                        const deliveryGroupStudents = enrollStudentList.filter(
                            (enrollStudentElement) =>
                                enrollStudentElement.classNo === deliveryClassNo,
                        );
                        // .map((enrollStudentElement) => enrollStudentElement.studentId);
                        const studentIdsDeliveryGroup = studentDetails
                            .filter((studentElement) =>
                                deliveryGroupStudents.find(
                                    (deliveryGroupElement) =>
                                        deliveryGroupElement.studentId === studentElement.user_id,
                                ),
                            )
                            .map((studentElement) => studentElement._id);
                        overAllStudentIds = [...overAllStudentIds, ...deliveryGroupStudents];
                        deliveryStudentCount += studentIdsDeliveryGroup.length;
                        deliveryGroups.push({
                            _student_ids: studentIdsDeliveryGroup,
                            classNo: deliveryClassNo,
                            group_no: parseInt(deliveryElement.groupNo),
                            group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                        });
                    }
                    if (courseDeliveryBasedGroups.length)
                        deliverySetting.push({
                            group_name: studentGroupName + '-' + sessionTypeSetting.symbol,
                            delivery_type: sessionTypeSetting.session_type,
                            session_type: sessionTypeSetting.symbol,
                            no_of_group: courseDeliveryBasedGroups.length,
                            no_of_student: 500,
                            groups: deliveryGroups,
                        });
                }
                if (deliverySetting.length)
                    courseGroupSetting.push({
                        ungrouped: [],
                        gender: genderElement,
                        session_setting: deliverySetting,
                    });
            }
            const newStudentImport = [];
            for (studentElement of studentDetails) {
                if (
                    !groupLevelData.students.find(
                        (studentGroupElement) =>
                            studentGroupElement._student_id.toString() ===
                            studentElement._id.toString(),
                    )
                ) {
                    newStudentImport.push({
                        _student_id: convertToMongoObjectId(studentElement._id),
                        academic_no: studentElement.user_id,
                        name: studentElement.name,
                        gender: studentElement.gender,
                        mark: 5,
                        imported_on: new Date(),
                        _imported_by: user_id || _user_id,
                        imported_by: { first: 'SIS' },
                        master_group_status: 'published',
                        course_group_status: studentCourseIds,
                    });
                }
            }
            studentGroupObject = {
                ...(!courseGroupData.setting.length && {
                    $set: {
                        'groups.$[i].courses.$[k].setting': courseGroupSetting,
                        'groups.$[i].courses.$[k].gender_mixed': !!courseList.find(
                            (courseListElement) => courseListElement.campus === MIXED,
                        ),
                    },
                }),
                ...(newStudentImport.length /* || Object.keys(sgPushSetting).length */ && {
                    $push: {
                        // ...(newStudentImport.length && {
                        'groups.$[i].students': newStudentImport,
                        // }),
                        // ...sgPushSetting,
                    },
                }),
                // ...(studentIds.length && {
                //     $pull: {
                //         'groups.$[i].courses.$[k]._removed_student_ids': { $in: studentIds },
                //     },
                // }),
            };
            studentGroupFilter = {
                arrayFilters: [
                    {
                        'i.level': levelNo,
                        'i.term': term,
                    },
                    { 'k._course_id': convertToMongoObjectId(courseId) },
                    // ...sgPushSettingFilter,
                ],
            };

            // return {
            //     data: {
            //         studentGroupObject,
            //         studentGroupFilter,
            //         courseList,
            //         courseGroupSetting,
            //         enrollStudentList,
            //         newStudentImport,
            //         nonRegisteredStudent,
            //     },
            // };
        } else {
            // Check New Student Flow
            for (genderElement of courseGroupGenderList) {
                const settingGenderData = courseGroupData.setting.find(
                    (settingElement) => settingElement.gender === genderElement,
                );
                if (settingGenderData) {
                    for (sessionTypeSetting of courseGroupData.session_types) {
                        const deliverySettingData = settingGenderData
                            ? settingGenderData.session_setting.find(
                                  (sessionDeliveryElement) =>
                                      sessionDeliveryElement.session_type ===
                                      sessionTypeSetting.symbol,
                              )
                            : { groups: [] };
                        const courseDeliveryBasedGroups = courseList
                            .filter(
                                (courseElement) =>
                                    courseElement.deliveryCode === sessionTypeSetting.symbol &&
                                    genderElement === courseElement.gender,
                            )
                            .sort((a, b) => {
                                if (a.gender === b.gender) {
                                    return a.groupNo.localeCompare(b.groupNo);
                                }
                                return a.gender.localeCompare(b.gender);
                            });
                        for (deliveryElement of courseDeliveryBasedGroups) {
                            const deliveryGroupData = deliverySettingData.groups.find(
                                (deliveryGroupElement) =>
                                    parseInt(deliveryGroupElement.group_no) ===
                                    parseInt(deliveryElement.groupNo),
                            );
                            if (deliveryGroupData) {
                                // console.info({
                                //     symbol: sessionTypeSetting.symbol,
                                //     groupNo: deliveryElement.groupNo,
                                //     gender: deliveryElement.gender,
                                //     classNo: deliveryElement.classNo,
                                //     classType: deliveryElement.classType,
                                // });
                                let deliveryClassNo = deliveryElement.classNo;
                                if (deliveryElement.classType === 'No') {
                                    const enrolledClass = courseList.find(
                                        (deliveryGroupElement) =>
                                            deliveryGroupElement.gender.toString() ===
                                                deliveryElement.gender.toString() &&
                                            deliveryGroupElement.groupNo.toString() ===
                                                deliveryElement.groupNo.toString() &&
                                            deliveryGroupElement.classType === 'E',
                                    );
                                    deliveryClassNo =
                                        enrolledClass && enrolledClass.classNo
                                            ? enrolledClass.classNo
                                            : deliveryElement.classNo;
                                }
                                const deliveryGroupStudents = enrollStudentList.filter(
                                    (enrollStudentElement) =>
                                        enrollStudentElement.classNo === deliveryClassNo,
                                );
                                const studentIdsDeliveryGroup = studentDetails
                                    .filter((studentElement) =>
                                        deliveryGroupStudents.find(
                                            (deliveryGroupElement) =>
                                                deliveryGroupElement.studentId ===
                                                studentElement.user_id,
                                        ),
                                    )
                                    .map((studentElement) => studentElement._id);
                                const sgDiff = findDifferenceInArray(
                                    studentIdsDeliveryGroup,
                                    deliveryGroupData._student_ids,
                                );
                                // console.info({
                                //     // _student_ids: studentIdsDeliveryGroup,
                                //     group_no: parseInt(deliveryElement.groupNo),
                                //     // deliveryGroupData,
                                //     diff: sgDiff,
                                // });

                                // DB PUSH/PULL Filters
                                // if (sgDiff.pushIds && sgDiff.pushIds.length) {
                                //     sgPushSetting = {
                                //         ...sgPushSetting,
                                //         [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups.$[classNo${
                                //             deliveryElement.classNo
                                //         }]._student_ids`]: { $each: sgDiff.pushIds },
                                //     };
                                // }
                                // if (sgDiff.pullIds && sgDiff.pullIds.length) {
                                //     sgPullSetting = {
                                //         ...sgPullSetting,
                                //         [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups.$[classNo${
                                //             deliveryElement.classNo
                                //         }]._student_ids`]: { $in: sgDiff.pullIds },
                                //     };
                                // }
                                // Delivery Based Group Student Changes
                                if (
                                    (sgDiff.pushIds && sgDiff.pushIds.length) ||
                                    (sgDiff.pullIds && sgDiff.pullIds.length)
                                ) {
                                    sgSetSetting = {
                                        ...sgSetSetting,
                                        [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups.$[classNo${
                                            deliveryElement.classNo
                                        }]._student_ids`]: sgDiff.alterStudentArray,
                                    };
                                }
                                if (
                                    (sgDiff.pushIds && sgDiff.pushIds.length) ||
                                    (sgDiff.pullIds && sgDiff.pullIds.length)
                                ) {
                                    if (
                                        objectDuplicateCheck(
                                            sgPushSettingFilter,
                                            `${genderElement}.gender`,
                                        )
                                    )
                                        sgPushSettingFilter.push({
                                            [`${genderElement}.gender`]: genderElement,
                                        });
                                    if (
                                        objectDuplicateCheck(
                                            sgPushSettingFilter,
                                            `${sessionTypeSetting.symbol.toLowerCase()}.session_type`,
                                        )
                                    )
                                        sgPushSettingFilter.push({
                                            [`${sessionTypeSetting.symbol.toLowerCase()}.session_type`]:
                                                sessionTypeSetting.symbol,
                                        });
                                    if (
                                        objectDuplicateCheck(
                                            sgPushSettingFilter,
                                            `classNo${deliveryElement.classNo}.group_no`,
                                        )
                                    )
                                        sgPushSettingFilter.push({
                                            [`classNo${deliveryElement.classNo}.group_no`]:
                                                parseInt(deliveryElement.groupNo),
                                        });
                                }

                                // Student Course Schedule Update
                                // Pull Student to Course Schedule Based on Course with Delivery group
                                if (sgDiff.pullIds && sgDiff.pullIds.length) {
                                    courseScheduleBulkWrite.push({
                                        updateMany: {
                                            filter: {
                                                isActive: true,
                                                isDeleted: false,
                                                _institution_calendar_id:
                                                    convertToMongoObjectId(institutionCalendarId),
                                                term,
                                                level_no: levelNo,
                                                _course_id: convertToMongoObjectId(courseId),
                                                $or: [
                                                    {
                                                        status: PENDING,
                                                    },
                                                    {
                                                        status: MISSED,
                                                    },
                                                ],
                                                'student_groups.session_group.session_group_id':
                                                    convertToMongoObjectId(deliveryGroupData._id),
                                            },
                                            update: {
                                                $pull: {
                                                    students: { _id: { $in: sgDiff.pullIds } },
                                                },
                                            },
                                        },
                                    });
                                }
                                // Push Student from Course Schedule Based on Course with Delivery group
                                if (sgDiff.pushIds && sgDiff.pushIds.length) {
                                    const studentPushData = [];
                                    // const studentPushDuplicateCheck = [];
                                    for (pushIdElement of sgDiff.pushIds) {
                                        const pushUser = studentDetails.find(
                                            (userElement) =>
                                                userElement._id.toString() ===
                                                pushIdElement.toString(),
                                        );
                                        if (pushUser && pushUser.name) {
                                            studentPushData.push({
                                                _id: pushIdElement,
                                                name: pushUser.name,
                                                user_id: pushUser.user_id,
                                            });
                                            // studentPushDuplicateCheck.push(
                                            //     convertToMongoObjectId(pushIdElement),
                                            // );
                                        }
                                    }
                                    courseScheduleBulkWrite.push({
                                        updateMany: {
                                            filter: {
                                                isActive: true,
                                                isDeleted: false,
                                                _institution_calendar_id:
                                                    convertToMongoObjectId(institutionCalendarId),
                                                term,
                                                level_no: levelNo,
                                                _course_id: convertToMongoObjectId(courseId),
                                                $or: [
                                                    {
                                                        status: PENDING,
                                                    },
                                                    {
                                                        status: MISSED,
                                                    },
                                                ],
                                                'student_groups.session_group.session_group_id':
                                                    convertToMongoObjectId(deliveryGroupData._id),
                                            },
                                            update: {
                                                $push: {
                                                    students: {
                                                        $each: studentPushData,
                                                        // $elemMatch: {
                                                        //     _id: {
                                                        //         $nin: studentPushDuplicateCheck,
                                                        //     }, // Avoid duplicates based on _id
                                                        // },
                                                    },
                                                },
                                            },
                                        },
                                    });
                                }
                            } else {
                                let deliveryClassNo = deliveryElement.classNo;
                                if (deliveryElement.classType === 'No') {
                                    const enrolledClass = courseList.find(
                                        (deliveryGroupElement) =>
                                            deliveryGroupElement.gender.toString() ===
                                                deliveryElement.gender.toString() &&
                                            deliveryGroupElement.groupNo.toString() ===
                                                deliveryElement.groupNo.toString() &&
                                            deliveryGroupElement.classType === 'E',
                                    );
                                    deliveryClassNo =
                                        enrolledClass && enrolledClass.classNo
                                            ? enrolledClass.classNo
                                            : deliveryElement.classNo;
                                }
                                const deliveryGroupStudents = enrollStudentList.filter(
                                    (enrollStudentElement) =>
                                        enrollStudentElement.classNo === deliveryClassNo,
                                );
                                const studentIdsDeliveryGroup = studentDetails
                                    .filter((studentElement) =>
                                        deliveryGroupStudents.find(
                                            (deliveryGroupElement) =>
                                                deliveryGroupElement.studentId ===
                                                studentElement.user_id,
                                        ),
                                    )
                                    .map((studentElement) => studentElement._id);
                                sgPushSetting = {
                                    [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups`]:
                                        {
                                            _student_ids: studentIdsDeliveryGroup,
                                            classNo: deliveryClassNo,
                                            group_no: parseInt(deliveryElement.groupNo),
                                            group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                                        },
                                };
                                if (
                                    objectDuplicateCheck(
                                        sgPushNewGroupSettingFilter,
                                        `${genderElement}.gender`,
                                    )
                                )
                                    sgPushNewGroupSettingFilter.push({
                                        [`${genderElement}.gender`]: genderElement,
                                    });
                                if (
                                    objectDuplicateCheck(
                                        sgPushNewGroupSettingFilter,
                                        `${sessionTypeSetting.symbol.toLowerCase()}.session_type`,
                                    )
                                )
                                    sgPushNewGroupSettingFilter.push({
                                        [`${sessionTypeSetting.symbol.toLowerCase()}.session_type`]:
                                            sessionTypeSetting.symbol,
                                    });
                            }
                        }
                    }
                } else {
                    const deliverySetting = [];
                    for (sessionTypeSetting of courseGroupData.session_types) {
                        const courseDeliveryBasedGroups = courseList
                            .filter(
                                (courseElement) =>
                                    courseElement.deliveryCode === sessionTypeSetting.symbol &&
                                    genderElement === courseElement.gender,
                            )
                            .sort((a, b) => {
                                if (a.gender === b.gender) {
                                    return a.groupNo.localeCompare(b.groupNo);
                                }
                                return a.gender.localeCompare(b.gender);
                            });

                        const deliveryGroups = [];
                        let deliveryStudentCount = 0;
                        for (deliveryElement of courseDeliveryBasedGroups) {
                            let deliveryClassNo = deliveryElement.classNo;
                            if (deliveryElement.classType === 'No') {
                                const enrolledClass = courseList.find(
                                    (deliveryGroupElement) =>
                                        deliveryGroupElement.gender.toString() ===
                                            deliveryElement.gender.toString() &&
                                        deliveryGroupElement.groupNo.toString() ===
                                            deliveryElement.groupNo.toString() &&
                                        deliveryGroupElement.classType === 'E',
                                );
                                deliveryClassNo =
                                    enrolledClass && enrolledClass.classNo
                                        ? enrolledClass.classNo
                                        : deliveryElement.classNo;
                            }
                            const deliveryGroupStudents = enrollStudentList.filter(
                                (enrollStudentElement) =>
                                    enrollStudentElement.classNo === deliveryClassNo,
                            );
                            const studentIdsDeliveryGroup = studentDetails
                                .filter((studentElement) =>
                                    deliveryGroupStudents.find(
                                        (deliveryGroupElement) =>
                                            deliveryGroupElement.studentId ===
                                            studentElement.user_id,
                                    ),
                                )
                                .map((studentElement) => studentElement._id);
                            deliveryStudentCount += studentIdsDeliveryGroup.length;
                            deliveryGroups.push({
                                _student_ids: studentIdsDeliveryGroup,
                                classNo: deliveryClassNo,
                                group_no: parseInt(deliveryElement.groupNo),
                                group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                            });
                        }
                        if (courseDeliveryBasedGroups.length)
                            deliverySetting.push({
                                group_name: studentGroupName + '-' + sessionTypeSetting.symbol,
                                delivery_type: sessionTypeSetting.session_type,
                                session_type: sessionTypeSetting.symbol,
                                no_of_group: courseDeliveryBasedGroups.length,
                                no_of_student: 500,
                                groups: deliveryGroups,
                            });
                    }
                    if (deliverySetting.length) {
                        sgGenderPushSetting = {
                            ...sgGenderPushSetting,
                            [`groups.$[i].courses.$[k].setting`]: {
                                ungrouped: [],
                                gender: genderElement,
                                session_setting: deliverySetting,
                            },
                        };
                    }
                }
            }

            // New Student Delivery Group Pushing
            if (Object.keys(sgPushSetting).length) {
                console.log(
                    await studentGroupSchemas.updateOne(
                        { _id: convertToMongoObjectId(studentGroupId) },
                        {
                            $push: {
                                ...sgPushSetting,
                            },
                        },
                        {
                            arrayFilters: [
                                {
                                    'i.level': levelNo,
                                    'i.term': term,
                                },
                                { 'k._course_id': convertToMongoObjectId(courseId) },
                                ...sgPushNewGroupSettingFilter,
                            ],
                        },
                    ),
                    ' New Student Delivery Group Pushing ',
                );
            }
            if (Object.keys(sgGenderPushSetting).length) {
                console.log(
                    await studentGroupSchemas.updateOne(
                        { _id: convertToMongoObjectId(studentGroupId) },
                        {
                            $push: {
                                ...sgGenderPushSetting,
                            },
                        },
                        {
                            arrayFilters: [
                                {
                                    'i.level': levelNo,
                                    'i.term': term,
                                },
                                { 'k._course_id': convertToMongoObjectId(courseId) },
                            ],
                        },
                    ),
                    'New Gender Student Delivery Group Pushing',
                );
            }
            const newStudentImport = [];
            for (studentElement of studentDetails) {
                if (
                    !groupLevelData.students.find(
                        (studentGroupElement) =>
                            studentGroupElement._student_id.toString() ===
                            studentElement._id.toString(),
                    )
                ) {
                    newStudentImport.push({
                        _student_id: convertToMongoObjectId(studentElement._id),
                        academic_no: studentElement.user_id,
                        name: studentElement.name,
                        gender: studentElement.gender,
                        mark: 5,
                        imported_on: new Date(),
                        _imported_by: user_id || _user_id,
                        imported_by: { first: 'SIS' },
                        master_group_status: 'published',
                        course_group_status: studentCourseIds,
                    });
                }
            }
            studentGroupObject = {
                ...(newStudentImport.length && {
                    $push: {
                        'groups.$[i].students': newStudentImport,
                    },
                }),
                // ...(Object.keys(sgPullSetting).length && {
                //     $pull: {
                //         ...sgPullSetting,
                //     },
                // }),
                ...(Object.keys(sgSetSetting).length && {
                    $set: {
                        ...sgSetSetting,
                    },
                }),
            };
            studentGroupFilter = {
                arrayFilters: [
                    {
                        'i.level': levelNo,
                        'i.term': term,
                    },
                    { 'k._course_id': convertToMongoObjectId(courseId) },
                    ...sgPushSettingFilter,
                ],
            };
            // return {
            //     data: {
            //         newStudentImport,
            //         // pushSetting: { sgPushSetting, sgPullSetting, sgPushSettingFilter },
            //         setting: courseGroupData.setting,
            //         courseList,
            //     },
            // };
        }
        if (Object.keys(studentGroupObject).length) {
            console.log(
                await studentGroupSchemas.updateOne(
                    { _id: convertToMongoObjectId(studentGroupId) },
                    studentGroupObject,
                    studentGroupFilter,
                ),
            );
        }
        if (
            Object.keys(studentGroupObject).length ||
            Object.keys(sgGenderPushSetting).length ||
            Object.keys(sgPushSetting).length
        )
            await updateStudentGroupRedisKey({ courseId, level: levelNo, batch: term });
        if (courseScheduleBulkWrite.length) {
            console.log(await courseScheduleSchemas.bulkWrite(courseScheduleBulkWrite));
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                studentGroupObject,
                studentGroupFilter,
                sgPushSetting,
                sgPushNewGroupSettingFilter,
                sgGenderPushSetting,
                courseScheduleBulkWrite,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentGroupForAllCalendar = async ({ body = {}, headers = {} }) => {
    try {
        const {
            institutionCalendarId,
            courseSetting,
            dbUpdate,
            // programId,
            // studentGroupId,
            // courseId,
            // yearNo,
            // levelNo,
            // term,
        } = body;
        const { user_id, _user_id } = headers;
        // console.time('institutionCalendarLists');
        // const institutionCalendarLists = await institutionCalendarSchemas
        //     .find({
        //         isActive: true,
        //         isDeleted: false,
        //         status: 'published',
        //         // _id: convertToMongoObjectId(institutionCalendarId),
        //     })
        //     .lean();
        // console.timeEnd('institutionCalendarLists');
        console.time('calendarTermList');
        const calendarTermList = await getCalendarTermList({ filterKey: true });
        console.timeEnd('calendarTermList');
        const termCode = calendarTermList[0].termCode;
        // return { data: { /* institutionCalendarLists, */ calendarTermList } };
        // Program Calendar List
        console.time('programCalendarData');
        const programCalendarData = await programCalendarSchemas
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    status: PUBLISHED,
                },
                {
                    _program_id: 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.start_date': 1,
                    'level.end_date': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_number': 1,
                    'level.course.start_date': 1,
                    'level.course.end_date': 1,
                },
            )
            .lean();
        console.timeEnd('programCalendarData');
        console.time('courseList');
        const courseList = await getCourseList({
            filterKey: 'termCode',
            termCode,
        });
        console.timeEnd('courseList');
        console.time('enrollStudentList');
        const enrollStudentList = await getEnrollStudentList({ termCode, filterKey: 'termCode' });
        console.timeEnd('enrollStudentList');
        let studentUserIds = enrollStudentList.map((studentElement) => studentElement.studentId);
        studentUserIds = [...new Set(studentUserIds)];
        console.time('studentGroup');
        const studentGroupData = await studentGroupSchemas
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                },
                {
                    'master._program_id': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.rotation': 1,
                    'groups.group_name': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses.session_types.session_type': 1,
                    'groups.courses.session_types.symbol': 1,
                    'groups.courses.setting.ungrouped': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.delivery_type': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.no_of_group': 1,
                    'groups.courses.setting.session_setting.no_of_student': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.courses.setting.session_setting.groups.group_no': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.students._student_id': 1,
                },
            )
            .lean();
        console.timeEnd('studentGroup');
        if (!studentGroupData)
            return {
                statusCode: 410,
                message: 'DATA_RETRIEVED',
                data: 'Student Group Not Found',
            };
        console.time('studentDetails');
        const studentDetails = await userSchemas
            .find(
                { user_id: { $in: studentUserIds } },
                { name: 1, gender: 1, email: 1, user_id: 1 },
            )
            .lean();
        console.timeEnd('studentDetails');
        const dateFormatter = (inwardDate) => {
            const dateFormate = new Date(inwardDate);
            return dateFormate.toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'numeric',
                year: 'numeric',
            });
        };
        const objectDuplicateCheck = (sgPushSettingFilter, checkObjectKey) => {
            return !sgPushSettingFilter.find((sgPushSettingFilterElement) =>
                Object.keys(sgPushSettingFilterElement).some((key) => key === checkObjectKey),
            );
        };
        const groupBulkWrite = [];
        const courseScheduleBulkWrite = [];
        console.time('CompleteLoop');
        for (programCalendarElement of programCalendarData) {
            console.time(`programBasedFlow-${programCalendarElement._program_id}`);
            const programStudentGroup = studentGroupData.filter(
                (studentGroupElement) =>
                    studentGroupElement.master._program_id.toString() ===
                    programCalendarElement._program_id.toString(),
            );
            if (programStudentGroup.length) {
                for (levelElement of programCalendarElement.level) {
                    const yearGroup = programStudentGroup.find((programStudentGroupElement) =>
                        programStudentGroupElement.groups.find(
                            (levelGroupElement) =>
                                levelGroupElement.level.toString() ===
                                    levelElement.level_no.toString() &&
                                levelGroupElement.term.toString() === levelElement.term.toString(),
                        ),
                    );
                    if (yearGroup) {
                        const levelGroup = yearGroup.groups.find(
                            (levelGroupElement) =>
                                levelGroupElement.level.toString() ===
                                    levelElement.level_no.toString() &&
                                levelGroupElement.term.toString() === levelElement.term.toString(),
                        );
                        if (levelGroup) {
                            const studentCourseIds = levelGroup.courses.map((courseElement) => {
                                return {
                                    _course_id: courseElement._course_id,
                                    status: 'published',
                                };
                            });
                            for (courseElement of levelElement.course) {
                                const courseGroup = levelGroup.courses.find(
                                    (courseGroupElement) =>
                                        courseGroupElement._course_id.toString() ===
                                        courseElement._course_id.toString(),
                                );
                                if (courseGroup) {
                                    const courseCode = courseElement.courses_number.split(' ');
                                    // console.log(courseCode);
                                    const courseSISData = courseList.filter(
                                        (courseListElement) =>
                                            dateFormatter(courseListElement.startDate) ===
                                                dateFormatter(courseElement.start_date) &&
                                            dateFormatter(courseListElement.endDate) ===
                                                dateFormatter(courseElement.end_date) &&
                                            courseListElement.subject === courseCode[0] &&
                                            courseListElement.catalogNo === courseCode[1],
                                    );
                                    if (courseSISData.length) {
                                        const courseSISStudentList = enrollStudentList.filter(
                                            (enrollStudentListElement) =>
                                                enrollStudentListElement.courseId.toString() ===
                                                courseSISData[0].courseId.toString(),
                                        );
                                        let courseDeliveryCode = courseSISData.map(
                                            (courseElement) => courseElement.deliveryCode,
                                        );
                                        courseDeliveryCode = [...new Set(courseDeliveryCode)];
                                        // console.info({
                                        //     delivery: courseDeliveryCode,
                                        //     genderMixed: courseSISData[0].campus === MIXED,
                                        // });
                                        let studentGroupObject = {};
                                        let studentGroupFilter = {};
                                        let sgPushSetting = {};
                                        let sgSetSetting = {};
                                        const sgPushSettingFilter = [];
                                        const sgPushNewGroupSettingFilter = [];
                                        let sgGenderPushSetting = {};
                                        const studentGroupName = `${levelGroup.group_name}-${courseGroup.course_no}-G`;
                                        const courseGroupGenderList = [
                                            ...new Set(
                                                courseSISData.map(
                                                    (courseListElement) => courseListElement.gender,
                                                ),
                                            ),
                                        ];
                                        if (!courseGroup.setting.length) {
                                            if (courseSetting) {
                                                const courseGroupSetting = [];
                                                let overAllStudentIds = [];
                                                for (genderElement of courseGroupGenderList) {
                                                    const deliverySetting = [];
                                                    for (sessionTypeSetting of courseGroup.session_types) {
                                                        const courseDeliveryBasedGroups =
                                                            courseSISData
                                                                .filter(
                                                                    (courseElement) =>
                                                                        courseElement.deliveryCode ===
                                                                            sessionTypeSetting.symbol &&
                                                                        genderElement ===
                                                                            courseElement.gender,
                                                                )
                                                                .sort((a, b) => {
                                                                    if (a.gender === b.gender) {
                                                                        return a.groupNo.localeCompare(
                                                                            b.groupNo,
                                                                        );
                                                                    }
                                                                    return a.gender.localeCompare(
                                                                        b.gender,
                                                                    );
                                                                });
                                                        const deliveryGroups = [];
                                                        let deliveryStudentCount = 0;
                                                        for (deliveryElement of courseDeliveryBasedGroups) {
                                                            let deliveryClassNo =
                                                                deliveryElement.classNo;
                                                            if (
                                                                deliveryElement.classType === 'No'
                                                            ) {
                                                                const enrolledClass =
                                                                    courseSISData.find(
                                                                        (deliveryGroupElement) =>
                                                                            deliveryGroupElement.gender.toString() ===
                                                                                deliveryElement.gender.toString() &&
                                                                            deliveryGroupElement.groupNo.toString() ===
                                                                                deliveryElement.groupNo.toString() &&
                                                                            deliveryGroupElement.classType ===
                                                                                'E',
                                                                    );
                                                                deliveryClassNo =
                                                                    enrolledClass &&
                                                                    enrolledClass.classNo
                                                                        ? enrolledClass.classNo
                                                                        : deliveryElement.classNo;
                                                            }
                                                            const deliveryGroupStudents =
                                                                courseSISStudentList.filter(
                                                                    (enrollStudentElement) =>
                                                                        enrollStudentElement.classNo ===
                                                                        deliveryClassNo,
                                                                );
                                                            const studentIdsDeliveryGroup =
                                                                studentDetails
                                                                    .filter((studentElement) =>
                                                                        deliveryGroupStudents.find(
                                                                            (
                                                                                deliveryGroupElement,
                                                                            ) =>
                                                                                deliveryGroupElement.studentId ===
                                                                                studentElement.user_id,
                                                                        ),
                                                                    )
                                                                    .map(
                                                                        (studentElement) =>
                                                                            studentElement._id,
                                                                    );
                                                            overAllStudentIds = [
                                                                ...overAllStudentIds,
                                                                ...deliveryGroupStudents,
                                                            ];
                                                            deliveryStudentCount +=
                                                                studentIdsDeliveryGroup.length;
                                                            deliveryGroups.push({
                                                                _student_ids:
                                                                    studentIdsDeliveryGroup,
                                                                classNo: deliveryClassNo,
                                                                group_no: parseInt(
                                                                    deliveryElement.groupNo,
                                                                ),
                                                                group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                                                            });
                                                        }
                                                        if (courseDeliveryBasedGroups.length)
                                                            deliverySetting.push({
                                                                group_name:
                                                                    studentGroupName +
                                                                    '-' +
                                                                    sessionTypeSetting.symbol,
                                                                delivery_type:
                                                                    sessionTypeSetting.session_type,
                                                                session_type:
                                                                    sessionTypeSetting.symbol,
                                                                no_of_group:
                                                                    courseDeliveryBasedGroups.length,
                                                                no_of_student: 500,
                                                                groups: deliveryGroups,
                                                            });
                                                    }
                                                    if (deliverySetting.length)
                                                        courseGroupSetting.push({
                                                            ungrouped: [],
                                                            gender: genderElement,
                                                            session_setting: deliverySetting,
                                                        });
                                                }
                                                const newStudentImport = [];
                                                for (studentElement of studentDetails) {
                                                    if (
                                                        !levelGroup.students.find(
                                                            (studentGroupElement) =>
                                                                studentGroupElement._student_id.toString() ===
                                                                studentElement._id.toString(),
                                                        ) &&
                                                        courseSISStudentList.find(
                                                            (courseSISStudentListElement) =>
                                                                courseSISStudentListElement.studentId.toString() ===
                                                                studentElement.user_id.toString(),
                                                        )
                                                    ) {
                                                        newStudentImport.push({
                                                            _student_id: convertToMongoObjectId(
                                                                studentElement._id,
                                                            ),
                                                            academic_no: studentElement.user_id,
                                                            name: studentElement.name,
                                                            gender: studentElement.gender,
                                                            mark: 5,
                                                            imported_on: new Date(),
                                                            _imported_by: user_id || _user_id,
                                                            imported_by: { first: 'SIS' },
                                                            master_group_status: 'published',
                                                            course_group_status: studentCourseIds,
                                                        });
                                                    }
                                                }
                                                studentGroupObject = {
                                                    ...(courseGroupSetting.length && {
                                                        $set: {
                                                            'groups.$[i].courses.$[k].setting':
                                                                courseGroupSetting,
                                                            'groups.$[i].courses.$[k].gender_mixed':
                                                                !!courseSISData.find(
                                                                    (courseListElement) =>
                                                                        courseListElement.campus ===
                                                                        MIXED,
                                                                ),
                                                        },
                                                    }),
                                                    ...(courseGroupSetting.length &&
                                                        newStudentImport.length /* || Object.keys(sgPushSetting).length */ && {
                                                            $push: {
                                                                // ...(newStudentImport.length && {
                                                                'groups.$[i].students':
                                                                    newStudentImport,
                                                                // }),
                                                                // ...sgPushSetting,
                                                            },
                                                        }),
                                                    // ...(studentIds.length && {
                                                    //     $pull: {
                                                    //         'groups.$[i].courses.$[k]._removed_student_ids': { $in: studentIds },
                                                    //     },
                                                    // }),
                                                };
                                                studentGroupFilter = {
                                                    arrayFilters: [
                                                        {
                                                            'i.level': levelElement.level_no,
                                                            'i.term': levelElement.term,
                                                        },
                                                        // {
                                                        //     'k._course_id': convertToMongoObjectId(
                                                        //         courseElement._course_id,
                                                        //     ),
                                                        // },
                                                        // ...sgPushSettingFilter,
                                                    ],
                                                };
                                                if (courseGroupSetting.length)
                                                    studentGroupFilter.arrayFilters.push({
                                                        'k._course_id': convertToMongoObjectId(
                                                            courseElement._course_id,
                                                        ),
                                                    });
                                            }
                                        } else {
                                            // Check New Student Flow
                                            for (genderElement of courseGroupGenderList) {
                                                const settingGenderData = courseGroup.setting.find(
                                                    (settingElement) =>
                                                        settingElement.gender === genderElement,
                                                );
                                                if (settingGenderData) {
                                                    for (sessionTypeSetting of courseGroup.session_types) {
                                                        const deliverySettingData =
                                                            settingGenderData
                                                                ? settingGenderData.session_setting.find(
                                                                      (sessionDeliveryElement) =>
                                                                          sessionDeliveryElement.session_type ===
                                                                          sessionTypeSetting.symbol,
                                                                  )
                                                                : { groups: [] };
                                                        const courseDeliveryBasedGroups =
                                                            courseSISData
                                                                .filter(
                                                                    (courseElement) =>
                                                                        courseElement.deliveryCode ===
                                                                            sessionTypeSetting.symbol &&
                                                                        genderElement ===
                                                                            courseElement.gender,
                                                                )
                                                                .sort((a, b) => {
                                                                    if (a.gender === b.gender) {
                                                                        return a.groupNo.localeCompare(
                                                                            b.groupNo,
                                                                        );
                                                                    }
                                                                    return a.gender.localeCompare(
                                                                        b.gender,
                                                                    );
                                                                });
                                                        const genderNewDelivery = [];
                                                        const genderNewDeliverySettingFilter = [];
                                                        for (deliveryElement of courseDeliveryBasedGroups) {
                                                            if (!deliverySettingData) {
                                                                let deliveryClassNo =
                                                                    deliveryElement.classNo;
                                                                if (
                                                                    deliveryElement.classType ===
                                                                    'No'
                                                                ) {
                                                                    const enrolledClass =
                                                                        courseSISData.find(
                                                                            (
                                                                                deliveryGroupElement,
                                                                            ) =>
                                                                                deliveryGroupElement.gender.toString() ===
                                                                                    deliveryElement.gender.toString() &&
                                                                                deliveryGroupElement.groupNo.toString() ===
                                                                                    deliveryElement.groupNo.toString() &&
                                                                                deliveryGroupElement.classType ===
                                                                                    'E',
                                                                        );
                                                                    deliveryClassNo =
                                                                        enrolledClass &&
                                                                        enrolledClass.classNo
                                                                            ? enrolledClass.classNo
                                                                            : deliveryElement.classNo;
                                                                }
                                                                const deliveryGroupStudents =
                                                                    courseSISStudentList.filter(
                                                                        (enrollStudentElement) =>
                                                                            enrollStudentElement.classNo ===
                                                                            deliveryClassNo,
                                                                    );
                                                                const studentIdsDeliveryGroup =
                                                                    studentDetails
                                                                        .filter((studentElement) =>
                                                                            deliveryGroupStudents.find(
                                                                                (
                                                                                    deliveryGroupElement,
                                                                                ) =>
                                                                                    deliveryGroupElement.studentId ===
                                                                                    studentElement.user_id,
                                                                            ),
                                                                        )
                                                                        .map(
                                                                            (studentElement) =>
                                                                                studentElement._id,
                                                                        );
                                                                genderNewDelivery.push({
                                                                    _student_ids:
                                                                        studentIdsDeliveryGroup,
                                                                    classNo: deliveryClassNo,
                                                                    group_no: parseInt(
                                                                        deliveryElement.groupNo,
                                                                    ),
                                                                    group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                                                                });
                                                                if (
                                                                    objectDuplicateCheck(
                                                                        genderNewDeliverySettingFilter,
                                                                        `${genderElement}.gender`,
                                                                    )
                                                                )
                                                                    genderNewDeliverySettingFilter.push(
                                                                        {
                                                                            [`${genderElement}.gender`]:
                                                                                genderElement,
                                                                        },
                                                                    );
                                                                if (
                                                                    objectDuplicateCheck(
                                                                        genderNewDeliverySettingFilter,
                                                                        `${sessionTypeSetting.symbol.toLowerCase()}.session_type`,
                                                                    )
                                                                )
                                                                    genderNewDeliverySettingFilter.push(
                                                                        {
                                                                            [`${sessionTypeSetting.symbol.toLowerCase()}.session_type`]:
                                                                                sessionTypeSetting.symbol,
                                                                        },
                                                                    );
                                                            } else {
                                                                const deliveryGroupData =
                                                                    deliverySettingData.groups.find(
                                                                        (deliveryGroupElement) =>
                                                                            parseInt(
                                                                                deliveryGroupElement.group_no,
                                                                            ) ===
                                                                            parseInt(
                                                                                deliveryElement.groupNo,
                                                                            ),
                                                                    );
                                                                if (deliveryGroupData) {
                                                                    let deliveryClassNo =
                                                                        deliveryElement.classNo;
                                                                    if (
                                                                        deliveryElement.classType ===
                                                                        'No'
                                                                    ) {
                                                                        const enrolledClass =
                                                                            courseSISData.find(
                                                                                (
                                                                                    deliveryGroupElement,
                                                                                ) =>
                                                                                    deliveryGroupElement.gender.toString() ===
                                                                                        deliveryElement.gender.toString() &&
                                                                                    deliveryGroupElement.groupNo.toString() ===
                                                                                        deliveryElement.groupNo.toString() &&
                                                                                    deliveryGroupElement.classType ===
                                                                                        'E',
                                                                            );
                                                                        deliveryClassNo =
                                                                            enrolledClass &&
                                                                            enrolledClass.classNo
                                                                                ? enrolledClass.classNo
                                                                                : deliveryElement.classNo;
                                                                    }
                                                                    const deliveryGroupStudents =
                                                                        courseSISStudentList.filter(
                                                                            (
                                                                                enrollStudentElement,
                                                                            ) =>
                                                                                enrollStudentElement.classNo ===
                                                                                deliveryClassNo,
                                                                        );
                                                                    const studentIdsDeliveryGroup =
                                                                        studentDetails
                                                                            .filter(
                                                                                (studentElement) =>
                                                                                    deliveryGroupStudents.find(
                                                                                        (
                                                                                            deliveryGroupElement,
                                                                                        ) =>
                                                                                            deliveryGroupElement.studentId ===
                                                                                            studentElement.user_id,
                                                                                    ),
                                                                            )
                                                                            .map(
                                                                                (studentElement) =>
                                                                                    studentElement._id,
                                                                            );
                                                                    const sgDiff =
                                                                        findDifferenceInArray(
                                                                            studentIdsDeliveryGroup,
                                                                            deliveryGroupData._student_ids,
                                                                        );
                                                                    // Delivery Based Group Student Changes
                                                                    if (
                                                                        (sgDiff.pushIds &&
                                                                            sgDiff.pushIds
                                                                                .length) ||
                                                                        (sgDiff.pullIds &&
                                                                            sgDiff.pullIds.length)
                                                                    ) {
                                                                        sgSetSetting = {
                                                                            ...sgSetSetting,
                                                                            [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups.$[classNo${
                                                                                deliveryElement.classNo
                                                                            }]._student_ids`]:
                                                                                sgDiff.alterStudentArray,
                                                                        };
                                                                    }
                                                                    if (
                                                                        (sgDiff.pushIds &&
                                                                            sgDiff.pushIds
                                                                                .length) ||
                                                                        (sgDiff.pullIds &&
                                                                            sgDiff.pullIds.length)
                                                                    ) {
                                                                        if (
                                                                            objectDuplicateCheck(
                                                                                sgPushSettingFilter,
                                                                                `${genderElement}.gender`,
                                                                            )
                                                                        )
                                                                            sgPushSettingFilter.push(
                                                                                {
                                                                                    [`${genderElement}.gender`]:
                                                                                        genderElement,
                                                                                },
                                                                            );
                                                                        if (
                                                                            objectDuplicateCheck(
                                                                                sgPushSettingFilter,
                                                                                `${sessionTypeSetting.symbol.toLowerCase()}.session_type`,
                                                                            )
                                                                        )
                                                                            sgPushSettingFilter.push(
                                                                                {
                                                                                    [`${sessionTypeSetting.symbol.toLowerCase()}.session_type`]:
                                                                                        sessionTypeSetting.symbol,
                                                                                },
                                                                            );
                                                                        if (
                                                                            objectDuplicateCheck(
                                                                                sgPushSettingFilter,
                                                                                `classNo${deliveryElement.classNo}.group_no`,
                                                                            )
                                                                        )
                                                                            sgPushSettingFilter.push(
                                                                                {
                                                                                    [`classNo${deliveryElement.classNo}.group_no`]:
                                                                                        parseInt(
                                                                                            deliveryElement.groupNo,
                                                                                        ),
                                                                                },
                                                                            );
                                                                    }

                                                                    // Student Course Schedule Update
                                                                    // Pull Student to Course Schedule Based on Course with Delivery group
                                                                    if (
                                                                        sgDiff.pullIds &&
                                                                        sgDiff.pullIds.length
                                                                    ) {
                                                                        courseScheduleBulkWrite.push(
                                                                            {
                                                                                updateMany: {
                                                                                    filter: {
                                                                                        isActive: true,
                                                                                        isDeleted: false,
                                                                                        _institution_calendar_id:
                                                                                            convertToMongoObjectId(
                                                                                                institutionCalendarId,
                                                                                            ),
                                                                                        term: levelElement.term,
                                                                                        level_no:
                                                                                            levelElement.level_no,
                                                                                        _course_id:
                                                                                            convertToMongoObjectId(
                                                                                                courseElement._course_id,
                                                                                            ),
                                                                                        $or: [
                                                                                            {
                                                                                                status: PENDING,
                                                                                            },
                                                                                            {
                                                                                                status: MISSED,
                                                                                            },
                                                                                        ],
                                                                                        'student_groups.session_group.session_group_id':
                                                                                            convertToMongoObjectId(
                                                                                                deliveryGroupData._id,
                                                                                            ),
                                                                                    },
                                                                                    update: {
                                                                                        $pull: {
                                                                                            students:
                                                                                                {
                                                                                                    _id: {
                                                                                                        $in: sgDiff.pullIds,
                                                                                                    },
                                                                                                },
                                                                                        },
                                                                                    },
                                                                                },
                                                                            },
                                                                        );
                                                                    }
                                                                    // Push Student from Course Schedule Based on Course with Delivery group
                                                                    if (
                                                                        sgDiff.pushIds &&
                                                                        sgDiff.pushIds.length
                                                                    ) {
                                                                        const studentPushData = [];
                                                                        for (pushIdElement of sgDiff.pushIds) {
                                                                            const pushUser =
                                                                                studentDetails.find(
                                                                                    (userElement) =>
                                                                                        userElement._id.toString() ===
                                                                                        pushIdElement.toString(),
                                                                                );
                                                                            if (
                                                                                pushUser &&
                                                                                pushUser.name
                                                                            ) {
                                                                                studentPushData.push(
                                                                                    {
                                                                                        _id: pushIdElement,
                                                                                        name: pushUser.name,
                                                                                        user_id:
                                                                                            pushUser.user_id,
                                                                                    },
                                                                                );
                                                                            }
                                                                        }
                                                                        courseScheduleBulkWrite.push(
                                                                            {
                                                                                updateMany: {
                                                                                    filter: {
                                                                                        isActive: true,
                                                                                        isDeleted: false,
                                                                                        _institution_calendar_id:
                                                                                            convertToMongoObjectId(
                                                                                                institutionCalendarId,
                                                                                            ),
                                                                                        term: levelElement.term,
                                                                                        level_no:
                                                                                            levelElement.level_no,
                                                                                        _course_id:
                                                                                            convertToMongoObjectId(
                                                                                                courseElement._course_id,
                                                                                            ),
                                                                                        $or: [
                                                                                            {
                                                                                                status: PENDING,
                                                                                            },
                                                                                            {
                                                                                                status: MISSED,
                                                                                            },
                                                                                        ],
                                                                                        'student_groups.session_group.session_group_id':
                                                                                            convertToMongoObjectId(
                                                                                                deliveryGroupData._id,
                                                                                            ),
                                                                                    },
                                                                                    update: {
                                                                                        $push: {
                                                                                            students:
                                                                                                {
                                                                                                    $each: studentPushData,
                                                                                                },
                                                                                        },
                                                                                    },
                                                                                },
                                                                            },
                                                                        );
                                                                    }
                                                                } else {
                                                                    let deliveryClassNo =
                                                                        deliveryElement.classNo;
                                                                    if (
                                                                        deliveryElement.classType ===
                                                                        'No'
                                                                    ) {
                                                                        const enrolledClass =
                                                                            courseSISData.find(
                                                                                (
                                                                                    deliveryGroupElement,
                                                                                ) =>
                                                                                    deliveryGroupElement.gender.toString() ===
                                                                                        deliveryElement.gender.toString() &&
                                                                                    deliveryGroupElement.groupNo.toString() ===
                                                                                        deliveryElement.groupNo.toString() &&
                                                                                    deliveryGroupElement.classType ===
                                                                                        'E',
                                                                            );
                                                                        deliveryClassNo =
                                                                            enrolledClass &&
                                                                            enrolledClass.classNo
                                                                                ? enrolledClass.classNo
                                                                                : deliveryElement.classNo;
                                                                    }
                                                                    const deliveryGroupStudents =
                                                                        courseSISStudentList.filter(
                                                                            (
                                                                                enrollStudentElement,
                                                                            ) =>
                                                                                enrollStudentElement.classNo ===
                                                                                deliveryClassNo,
                                                                        );
                                                                    const studentIdsDeliveryGroup =
                                                                        studentDetails
                                                                            .filter(
                                                                                (studentElement) =>
                                                                                    deliveryGroupStudents.find(
                                                                                        (
                                                                                            deliveryGroupElement,
                                                                                        ) =>
                                                                                            deliveryGroupElement.studentId ===
                                                                                            studentElement.user_id,
                                                                                    ),
                                                                            )
                                                                            .map(
                                                                                (studentElement) =>
                                                                                    studentElement._id,
                                                                            );
                                                                    sgPushSetting = {
                                                                        [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups`]:
                                                                            {
                                                                                _student_ids:
                                                                                    studentIdsDeliveryGroup,
                                                                                classNo:
                                                                                    deliveryClassNo,
                                                                                group_no: parseInt(
                                                                                    deliveryElement.groupNo,
                                                                                ),
                                                                                group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                                                                            },
                                                                    };
                                                                    if (
                                                                        objectDuplicateCheck(
                                                                            sgPushNewGroupSettingFilter,
                                                                            `${genderElement}.gender`,
                                                                        )
                                                                    )
                                                                        sgPushNewGroupSettingFilter.push(
                                                                            {
                                                                                [`${genderElement}.gender`]:
                                                                                    genderElement,
                                                                            },
                                                                        );
                                                                    if (
                                                                        objectDuplicateCheck(
                                                                            sgPushNewGroupSettingFilter,
                                                                            `${sessionTypeSetting.symbol.toLowerCase()}.session_type`,
                                                                        )
                                                                    )
                                                                        sgPushNewGroupSettingFilter.push(
                                                                            {
                                                                                [`${sessionTypeSetting.symbol.toLowerCase()}.session_type`]:
                                                                                    sessionTypeSetting.symbol,
                                                                            },
                                                                        );
                                                                }
                                                            }
                                                        }
                                                        if (
                                                            !deliverySettingData &&
                                                            genderNewDelivery.length
                                                        ) {
                                                            groupBulkWrite.push({
                                                                updateOne: {
                                                                    filter: {
                                                                        _id: convertToMongoObjectId(
                                                                            yearGroup._id,
                                                                        ),
                                                                    },
                                                                    update: {
                                                                        $push: {
                                                                            [`groups.$[i].courses.$[k].setting.$[${genderElement}].session_setting.$[${sessionTypeSetting.symbol.toLowerCase()}].groups`]:
                                                                                genderNewDelivery,
                                                                        },
                                                                    },
                                                                    arrayFilters: [
                                                                        {
                                                                            'i.level':
                                                                                levelElement.level,
                                                                            'i.term':
                                                                                levelElement.term,
                                                                        },
                                                                        {
                                                                            'k._course_id':
                                                                                convertToMongoObjectId(
                                                                                    courseElement._course_id,
                                                                                ),
                                                                        },
                                                                        ...genderNewDeliverySettingFilter,
                                                                    ],
                                                                },
                                                            });
                                                        }
                                                    }
                                                } else {
                                                    const deliverySetting = [];
                                                    for (sessionTypeSetting of courseGroup.session_types) {
                                                        const courseDeliveryBasedGroups =
                                                            courseSISData
                                                                .filter(
                                                                    (courseElement) =>
                                                                        courseElement.deliveryCode ===
                                                                            sessionTypeSetting.symbol &&
                                                                        genderElement ===
                                                                            courseElement.gender,
                                                                )
                                                                .sort((a, b) => {
                                                                    if (a.gender === b.gender) {
                                                                        return a.groupNo.localeCompare(
                                                                            b.groupNo,
                                                                        );
                                                                    }
                                                                    return a.gender.localeCompare(
                                                                        b.gender,
                                                                    );
                                                                });

                                                        const deliveryGroups = [];
                                                        let deliveryStudentCount = 0;
                                                        for (deliveryElement of courseDeliveryBasedGroups) {
                                                            let deliveryClassNo =
                                                                deliveryElement.classNo;
                                                            if (
                                                                deliveryElement.classType === 'No'
                                                            ) {
                                                                const enrolledClass =
                                                                    courseSISData.find(
                                                                        (deliveryGroupElement) =>
                                                                            deliveryGroupElement.gender.toString() ===
                                                                                deliveryElement.gender.toString() &&
                                                                            deliveryGroupElement.groupNo.toString() ===
                                                                                deliveryElement.groupNo.toString() &&
                                                                            deliveryGroupElement.classType ===
                                                                                'E',
                                                                    );
                                                                deliveryClassNo =
                                                                    enrolledClass &&
                                                                    enrolledClass.classNo
                                                                        ? enrolledClass.classNo
                                                                        : deliveryElement.classNo;
                                                            }
                                                            const deliveryGroupStudents =
                                                                courseSISStudentList.filter(
                                                                    (enrollStudentElement) =>
                                                                        enrollStudentElement.classNo ===
                                                                        deliveryClassNo,
                                                                );
                                                            const studentIdsDeliveryGroup =
                                                                studentDetails
                                                                    .filter((studentElement) =>
                                                                        deliveryGroupStudents.find(
                                                                            (
                                                                                deliveryGroupElement,
                                                                            ) =>
                                                                                deliveryGroupElement.studentId ===
                                                                                studentElement.user_id,
                                                                        ),
                                                                    )
                                                                    .map(
                                                                        (studentElement) =>
                                                                            studentElement._id,
                                                                    );
                                                            deliveryStudentCount +=
                                                                studentIdsDeliveryGroup.length;
                                                            deliveryGroups.push({
                                                                _student_ids:
                                                                    studentIdsDeliveryGroup,
                                                                classNo: deliveryClassNo,
                                                                group_no: parseInt(
                                                                    deliveryElement.groupNo,
                                                                ),
                                                                group_name: `${studentGroupName}-${sessionTypeSetting.symbol}-${deliveryElement.groupNo}`,
                                                            });
                                                        }
                                                        if (courseDeliveryBasedGroups.length)
                                                            deliverySetting.push({
                                                                group_name:
                                                                    studentGroupName +
                                                                    '-' +
                                                                    sessionTypeSetting.symbol,
                                                                delivery_type:
                                                                    sessionTypeSetting.session_type,
                                                                session_type:
                                                                    sessionTypeSetting.symbol,
                                                                no_of_group:
                                                                    courseDeliveryBasedGroups.length,
                                                                no_of_student: 500,
                                                                groups: deliveryGroups,
                                                            });
                                                    }
                                                    if (deliverySetting.length) {
                                                        sgGenderPushSetting = {
                                                            ...sgGenderPushSetting,
                                                            [`groups.$[i].courses.$[k].setting`]: {
                                                                ungrouped: [],
                                                                gender: genderElement,
                                                                session_setting: deliverySetting,
                                                            },
                                                        };
                                                    }
                                                }
                                            }

                                            // New Student Delivery Group Pushing
                                            if (Object.keys(sgPushSetting).length) {
                                                groupBulkWrite.push({
                                                    updateOne: {
                                                        filter: {
                                                            _id: convertToMongoObjectId(
                                                                yearGroup._id,
                                                            ),
                                                        },
                                                        update: {
                                                            $push: {
                                                                ...sgPushSetting,
                                                            },
                                                        },
                                                        arrayFilters: [
                                                            {
                                                                'i.level': levelElement.level,
                                                                'i.term': levelElement.term,
                                                            },
                                                            {
                                                                'k._course_id':
                                                                    convertToMongoObjectId(
                                                                        courseElement._course_id,
                                                                    ),
                                                            },
                                                            ...sgPushNewGroupSettingFilter,
                                                        ],
                                                    },
                                                });
                                            }
                                            if (Object.keys(sgGenderPushSetting).length) {
                                                groupBulkWrite.push({
                                                    updateOne: {
                                                        filter: {
                                                            _id: convertToMongoObjectId(
                                                                yearGroup._id,
                                                            ),
                                                        },
                                                        update: {
                                                            $push: {
                                                                ...sgGenderPushSetting,
                                                            },
                                                        },
                                                        arrayFilters: [
                                                            {
                                                                'i.level': levelElement.level_no,
                                                                'i.term': levelElement.term,
                                                            },
                                                            {
                                                                'k._course_id':
                                                                    convertToMongoObjectId(
                                                                        courseElement._course_id,
                                                                    ),
                                                            },
                                                        ],
                                                    },
                                                });
                                            }
                                            const newStudentImport = [];
                                            for (studentElement of studentDetails) {
                                                if (
                                                    !levelGroup.students.find(
                                                        (studentGroupElement) =>
                                                            studentGroupElement._student_id.toString() ===
                                                            studentElement._id.toString(),
                                                    ) &&
                                                    courseSISStudentList.find(
                                                        (courseSISStudentListElement) =>
                                                            courseSISStudentListElement.studentId.toString() ===
                                                            studentElement.user_id.toString(),
                                                    )
                                                ) {
                                                    newStudentImport.push({
                                                        _student_id: convertToMongoObjectId(
                                                            studentElement._id,
                                                        ),
                                                        academic_no: studentElement.user_id,
                                                        name: studentElement.name,
                                                        gender: studentElement.gender,
                                                        mark: 5,
                                                        imported_on: new Date(),
                                                        _imported_by: user_id || _user_id,
                                                        imported_by: { first: 'SIS' },
                                                        master_group_status: 'published',
                                                        course_group_status: studentCourseIds,
                                                    });
                                                }
                                            }
                                            studentGroupObject = {
                                                ...(newStudentImport.length && {
                                                    $push: {
                                                        'groups.$[i].students': newStudentImport,
                                                    },
                                                }),
                                                ...(Object.keys(sgSetSetting).length && {
                                                    $set: {
                                                        ...sgSetSetting,
                                                    },
                                                }),
                                            };
                                            studentGroupFilter = {
                                                arrayFilters: [
                                                    {
                                                        'i.level': levelElement.level_no,
                                                        'i.term': levelElement.term,
                                                    },
                                                    // {
                                                    //     'k._course_id': convertToMongoObjectId(
                                                    //         courseElement._course_id,
                                                    //     ),
                                                    // },
                                                    // ...sgPushSettingFilter,
                                                ],
                                            };
                                            if (Object.keys(sgSetSetting).length)
                                                studentGroupFilter.arrayFilters.push({
                                                    'k._course_id': convertToMongoObjectId(
                                                        courseElement._course_id,
                                                    ),
                                                });
                                            if (Object.keys(sgPushSettingFilter).length)
                                                studentGroupFilter.arrayFilters.push(
                                                    ...sgPushSettingFilter,
                                                );
                                        }
                                        if (Object.keys(studentGroupObject).length) {
                                            groupBulkWrite.push({
                                                updateOne: {
                                                    filter: {
                                                        _id: convertToMongoObjectId(yearGroup._id),
                                                    },
                                                    update: studentGroupObject,
                                                    arrayFilters: studentGroupFilter.arrayFilters,
                                                },
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            console.timeEnd(`programBasedFlow-${programCalendarElement._program_id}`);
        }
        console.timeEnd('CompleteLoop');
        if (dbUpdate) {
            if (groupBulkWrite.length) {
                const sgUpdate = await studentGroupSchemas.bulkWrite(groupBulkWrite);
                console.log(sgUpdate, 'Student Group Update');
                if (sgUpdate) {
                    if (courseScheduleBulkWrite.length) {
                        console.log(
                            await courseScheduleSchemas.bulkWrite(courseScheduleBulkWrite),
                            'Course Schedule Update',
                        );
                    }
                }
            }
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                groupBulkWrite,
                courseScheduleBulkWrite,
                programCalendarData,
                studentGroupData,
                courseList,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programStudentRefresh = async ({ body = {} }) => {
    try {
        const { institutionCalendarId, programId, dbUpdate } = body;
        console.time('studentGroup');
        const studentGroupData = await studentGroupSchemas
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    ...(programId && { 'master._program_id': convertToMongoObjectId(programId) }),
                },
                {
                    // 'master._program_id': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.students.name': 1,
                    'groups.students._student_id': 1,
                    'groups.students.academic_no': 1,
                },
            )
            .lean();
        console.timeEnd('studentGroup');
        const groupCourseData = [];
        for (studentGroupElement of studentGroupData) {
            for (groupElement of studentGroupElement.groups) {
                // console.time(`${groupElement.level}-${groupElement.term}`);
                const seen = new Set();
                const sgStudents = groupElement.students
                    .filter((sgStudentElement) => {
                        const duplicate = seen.has(sgStudentElement._student_id.toString());
                        seen.add(sgStudentElement._student_id.toString());
                        return !duplicate;
                    })
                    .map((sgStudentElement) => {
                        return {
                            name: sgStudentElement.name,
                            user_id: sgStudentElement.academic_no,
                            _id: convertToMongoObjectId(sgStudentElement._student_id),
                        };
                    });
                for (courseElement of groupElement.courses) {
                    for (courseSetting of courseElement.setting) {
                        for (courseSessionSetting of courseSetting.session_setting) {
                            for (courseGroupSetting of courseSessionSetting.groups) {
                                groupCourseData.push({
                                    levelNo: groupElement.level,
                                    term: groupElement.term,
                                    courseId: courseElement._course_id,
                                    session_group_id: courseGroupSetting._id.toString(),
                                    students: sgStudents.filter((studentElement) =>
                                        courseGroupSetting._student_ids.find(
                                            (groupStudentElementId) =>
                                                groupStudentElementId.toString() ===
                                                studentElement._id.toString(),
                                        ),
                                    ),
                                });
                            }
                        }
                    }
                }
                // console.timeEnd(`${groupElement.level}-${groupElement.term}`);
            }
        }
        const scheduleQuery = {
            isDeleted: false,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            ...(programId && { _program_id: convertToMongoObjectId(programId) }),
            type: 'regular',
            status: 'pending',
            // $or: [{ status: 'pending' }, { status: 'missed' }],
        };
        const scheduleProjection = {
            'student_groups.session_group.session_group_id': 1,
            _course_id: 1,
            term: 1,
            level_no: 1,
        };
        console.time('courseSchedule');
        const courseSchedule = await courseScheduleSchemas
            .find(scheduleQuery, scheduleProjection)
            .lean();
        console.timeEnd('courseSchedule');
        console.log('Schedule Count ', courseSchedule.length);
        const bulkWriteData = [];
        if (courseSchedule) {
            for (scheduleElement of courseSchedule) {
                let scheduleStudents = [];
                for (scheduleSGElement of scheduleElement.student_groups) {
                    for (scheduleSGSessionElement of scheduleSGElement.session_group) {
                        const sgSessionStudents = groupCourseData.find(
                            (courseSessionGroupsElement) =>
                                courseSessionGroupsElement.courseId.toString() ===
                                    scheduleElement._course_id.toString() &&
                                courseSessionGroupsElement.levelNo.toString() ===
                                    scheduleElement.level_no.toString() &&
                                courseSessionGroupsElement.term.toString() ===
                                    scheduleElement.term.toString() &&
                                courseSessionGroupsElement.session_group_id.toString() ===
                                    scheduleSGSessionElement.session_group_id.toString(),
                        );
                        if (sgSessionStudents)
                            scheduleStudents = [...scheduleStudents, ...sgSessionStudents.students];
                    }
                }
                if (scheduleStudents.length) {
                    bulkWriteData.push({
                        updateOne: {
                            filter: {
                                _id: convertToMongoObjectId(scheduleElement._id),
                            },
                            update: {
                                $set: {
                                    students: scheduleStudents,
                                },
                            },
                        },
                    });
                }
            }
        }
        if (dbUpdate) {
            console.log(
                await courseScheduleSchemas.bulkWrite(bulkWriteData),
                'Course Schedule Bulk Write',
            );
        }
        return { data: { groupCourseData, bulkWriteData } };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const staffOccurrenceCompare = (type, arrayOne, arrayTwo) => {
    if (arrayOne.length !== arrayTwo.length) {
        return false;
    }
    switch (type) {
        case 'staff': {
            const staffSet1 = new Set(arrayOne.map((item) => item._staff_id.toString()));
            const staffSet2 = new Set(arrayTwo.map((item) => item._staff_id.toString()));
            for (const staffIdElement of staffSet1) {
                if (!staffSet2.has(staffIdElement)) {
                    return false;
                }
            }
            return true;
        }
        case 'occurrence': {
            for (let i = 0; i < arrayOne.length; i++) {
                const occurrence1 = arrayOne[i];
                const occurrence2 = arrayTwo[i];
                if (
                    occurrence1.day !== occurrence2.day ||
                    occurrence1.start.hour !== occurrence2.start.hour ||
                    occurrence1.start.minute !== occurrence2.start.minute ||
                    occurrence1.start.format !== occurrence2.start.format ||
                    occurrence1.end.hour !== occurrence2.end.hour ||
                    occurrence1.end.minute !== occurrence2.end.minute ||
                    occurrence1.end.format !== occurrence2.end.format
                ) {
                    return false;
                }
            }
            return true;
        }
        default:
            break;
    }
};

const courseScheduleSync = async ({ body = {} }) => {
    try {
        const {
            institutionCalendarId,
            studentGroupId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            dbUpdate,
            devMode,
        } = body;
        // Course Listing From SIS
        console.time('calendarTermList');
        const calendarTermList = await getCalendarTermList({ filterKey: true });
        console.timeEnd('calendarTermList');
        const termCode = '2423';
        // calendarTermList && calendarTermList[0] ? calendarTermList[0].termCode : '2323';

        // Program Calendar List
        console.time('programCalendarData');
        const programCalendarData = await programCalendarSchemas
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    'level.year': yearNo,
                    'level.level_no': levelNo,
                    'level.term': term,
                    'level.course._course_id': convertToMongoObjectId(courseId),
                },
                {
                    _program_id: 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.start_date': 1,
                    'level.end_date': 1,
                    'level.events._event_id': 1,
                    'level.events.start_time': 1,
                    'level.events.end_time': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.start_date': 1,
                    'level.course.end_date': 1,
                    'level.course.courses_events._event_id': 1,
                    'level.course.courses_events.start_time': 1,
                    'level.course.courses_events.end_time': 1,
                },
            )
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();
        console.timeEnd('programCalendarData');
        if (!programCalendarData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Program Calendar Not Found ${programId}`,
            };
        const programName =
            programCalendarData &&
            programCalendarData._program_id.length &&
            programCalendarData._program_id[0].name
                ? programCalendarData._program_id[0].name
                : '';
        const levelData = programCalendarData.level.find(
            (levelElement) =>
                levelElement.year === yearNo &&
                levelElement.level_no === levelNo &&
                levelElement.term === term,
        );
        if (!levelData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Program Calendar Level Not Found ${levelNo} ${term}`,
            };
        const courseData = levelData.course.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Program Calendar Course Not Found ${courseId}`,
            };
        const courseCode = courseData.courses_number.split(' ');
        // Course Listing From SIS
        console.time('courseList');
        const courseList = await getCourseList({
            filterKey: 'query',
            startDate: courseData.start_date,
            endDate: courseData.end_date,
            subject: courseCode[0],
            catalogNo: courseCode[1],
        });
        console.timeEnd('courseList');
        if (!courseList || !courseList.length)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Course Not Found in SIS ${courseId}`,
            };

        const courseClassNo = courseList.map((courseListElement) => courseListElement.classNo);
        const courseEnd = [
            ...new Set(courseList.map((courseListElement) => courseListElement.endDate)),
        ];
        const courseEndDate = new Date(courseEnd[0]);
        courseEndDate.setHours(0, 0, 0, 0);
        const todayDate = new Date(); // Current date and time
        todayDate.setHours(0, 0, 0, 0); // Set the time part to midnight (00:00:00)
        if (courseEndDate < todayDate)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Course Completed Based SIS Start & End Date ${courseId}`,
            };
        console.time('courseScheduleSettingData');
        const courseScheduleSettingData = (
            await getCourseScheduleSettingList({
                termCode,
                filterKey: 'query',
                courseId: courseList[0].courseId,
                classNo: courseClassNo,
                // courseList
                // startDate: courseData.start_date,
                // endDate: courseData.end_date,
                // subject: courseCode[0],
                // catalogNo: courseCode[1],
            })
        ).sort((a, b) => {
            if (a.gender === b.gender) {
                return a.groupNo.localeCompare(b.groupNo);
            }
            return a.gender.localeCompare(b.gender);
        });
        console.timeEnd('courseScheduleSettingData');
        if (!courseScheduleSettingData || !courseScheduleSettingData.length)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Course Schedule Setting Not Found in SIS ${courseId}`,
            };

        const staffEmails = [];
        const courseScheduleSettingWithMultiStaff = [];
        for (scheduleSettingElement of courseScheduleSettingData) {
            if (
                scheduleSettingElement.staffEmail &&
                scheduleSettingElement.staffEmail.length &&
                scheduleSettingElement.staffEmail != null &&
                scheduleSettingElement.startTime &&
                scheduleSettingElement.startTime.length &&
                scheduleSettingElement.startTime != null &&
                scheduleSettingElement.endTime &&
                scheduleSettingElement.endTime.length &&
                scheduleSettingElement.endTime != null
            ) {
                const multiIndex = courseScheduleSettingWithMultiStaff.findIndex(
                    (staffElement) =>
                        staffElement.classNo.toString() ===
                            scheduleSettingElement.classNo.toString() &&
                        staffElement.startTime.toString() ===
                            scheduleSettingElement.startTime.toString() &&
                        staffElement.endTime.toString() ===
                            scheduleSettingElement.endTime.toString() &&
                        staffElement.monday.toString() ===
                            scheduleSettingElement.monday.toString() &&
                        staffElement.tuesday.toString() ===
                            scheduleSettingElement.tuesday.toString() &&
                        staffElement.wednesday.toString() ===
                            scheduleSettingElement.wednesday.toString() &&
                        staffElement.thursday.toString() ===
                            scheduleSettingElement.thursday.toString() &&
                        staffElement.friday.toString() ===
                            scheduleSettingElement.friday.toString() &&
                        staffElement.saturday.toString() ===
                            scheduleSettingElement.saturday.toString() &&
                        staffElement.sunday.toString() ===
                            scheduleSettingElement.sunday.toString() &&
                        // Make as Optional if Staff is saying Infra same as it's empty
                        staffElement.facilityId.toString() ===
                            scheduleSettingElement.facilityId.toString(),
                );
                staffEmails.push(scheduleSettingElement.staffEmail);
                if (multiIndex === -1)
                    courseScheduleSettingWithMultiStaff.push({
                        ...scheduleSettingElement,
                        staffs: [
                            {
                                staffId: scheduleSettingElement.staffId,
                                staffName: scheduleSettingElement.staffName,
                                staffEmail: scheduleSettingElement.staffEmail,
                            },
                        ],
                    });
                else
                    courseScheduleSettingWithMultiStaff[multiIndex].staffs.push({
                        staffId: scheduleSettingElement.staffId,
                        staffName: scheduleSettingElement.staffName,
                        staffEmail: scheduleSettingElement.staffEmail,
                    });
            }
        }

        const courseScheduleSettingWithMultiStaffSetting = [];
        for (scheduleSettingElement of courseScheduleSettingWithMultiStaff) {
            const staffMultiIndex = courseScheduleSettingWithMultiStaffSetting.findIndex(
                (settingElement) =>
                    settingElement.classNo === scheduleSettingElement.classNo &&
                    // settingElement.staffEmail === scheduleSettingElement.staffEmail,
                    settingElement.staffs.length === scheduleSettingElement.staffs.length &&
                    settingElement.staffs.filter((staffElement) =>
                        scheduleSettingElement.staffs.find(
                            (settingStaffElement) =>
                                settingStaffElement.staffEmail === staffElement.staffEmail,
                        ),
                    ).length === settingElement.staffs.length &&
                    settingElement.facilityId.trim() === scheduleSettingElement.facilityId.trim(),
            );
            if (staffMultiIndex === -1)
                courseScheduleSettingWithMultiStaffSetting.push({
                    ...scheduleSettingElement,
                    settingOccurrence: [
                        {
                            facilityId: scheduleSettingElement.facilityId,
                            startTime: scheduleSettingElement.startTime,
                            startDateTime: scheduleSettingElement.startDateTime,
                            start: scheduleSettingElement.start,
                            endTime: scheduleSettingElement.endTime,
                            endDateTime: scheduleSettingElement.endDateTime,
                            end: scheduleSettingElement.end,
                            monday: scheduleSettingElement.monday,
                            tuesday: scheduleSettingElement.tuesday,
                            wednesday: scheduleSettingElement.wednesday,
                            thursday: scheduleSettingElement.thursday,
                            friday: scheduleSettingElement.friday,
                            saturday: scheduleSettingElement.saturday,
                            sunday: scheduleSettingElement.sunday,
                        },
                    ],
                });
            else {
                courseScheduleSettingWithMultiStaffSetting[staffMultiIndex].settingOccurrence.push({
                    facilityId: scheduleSettingElement.facilityId,
                    startTime: scheduleSettingElement.startTime,
                    startDateTime: scheduleSettingElement.startDateTime,
                    start: scheduleSettingElement.start,
                    endTime: scheduleSettingElement.endTime,
                    endDateTime: scheduleSettingElement.endDateTime,
                    end: scheduleSettingElement.end,
                    monday: scheduleSettingElement.monday,
                    tuesday: scheduleSettingElement.tuesday,
                    wednesday: scheduleSettingElement.wednesday,
                    thursday: scheduleSettingElement.thursday,
                    friday: scheduleSettingElement.friday,
                    saturday: scheduleSettingElement.saturday,
                    sunday: scheduleSettingElement.sunday,
                });
            }
        }

        console.time('staffDatas');
        const staffDatas = await userSchemas
            .find({ isDeleted: false, email: { $in: staffEmails } }, { name: 1, email: 1 })
            .lean();
        console.timeEnd('staffDatas');
        console.time('infrastructureData');
        const infrastructureData = await infrastructureSchemas
            .find({ isDeleted: false, isActive: true }, { name: 1 })
            .lean();
        console.timeEnd('infrastructureData');
        //Remote/Extra Schedule Get
        console.time('remoteInfraData');
        const remoteInfraData = await remoteInfraSchema
            .findOne(
                {
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'programs._program_id': 1,
                    'programs.remoteScheduling.isDeleted': 1,
                    'programs.remoteScheduling.isActive': 1,
                    'programs.remoteScheduling._id': 1,
                    'programs.remoteScheduling.meetingTitle': 1,
                    'programs.remoteScheduling.term': 1,
                    'programs.remoteScheduling.yearName': 1,
                    'programs.remoteScheduling.levelName': 1,
                },
            )
            .lean();
        console.timeEnd('remoteInfraData');
        const programRemoteInfraData =
            remoteInfraData && remoteInfraData.programs && remoteInfraData.programs.length
                ? remoteInfraData.programs.find(
                      (programElement) => programElement._program_id.toString() === programId,
                  )
                : [];
        const levelRemoteInfraData =
            programRemoteInfraData &&
            programRemoteInfraData.remoteScheduling &&
            programRemoteInfraData.remoteScheduling.length
                ? clone(programRemoteInfraData.remoteScheduling)
                : [];
        console.time('courseSessionData');
        const courseSessionData = await getCourseSessionOrderData({
            programId,
            courseId,
            courseData,
            courseScheduleSettingWithMultiStaffSetting,
        });
        console.timeEnd('courseSessionData');
        if (courseSessionData?.session_flow_data?.length === 0) {
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Course Session Order Not Found`,
            };
        }
        console.time('courseScheduleSettingDBData');
        const courseScheduleSettingDBData = await courseScheduleDeliverySettingSchemas
            .find({ isDeleted: false, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        console.timeEnd('courseScheduleSettingDBData');
        for (scheduleSettingElement of courseScheduleSettingDBData) {
            let studentGroupIds = [];
            for (sessionElement of scheduleSettingElement.session) {
                for (studentGroupElement of sessionElement.student_groups) {
                    studentGroupIds = [
                        ...studentGroupIds,
                        ...studentGroupElement.session_group.map((sessionGroupElement) =>
                            sessionGroupElement.session_group_id.toString(),
                        ),
                    ];
                }
            }
            scheduleSettingElement.studentGroupIds = [...new Set(studentGroupIds)];
            scheduleSettingElement.staffIds = scheduleSettingElement.staffs.map((staffElement) =>
                staffElement._staff_id.toString(),
            );
        }
        console.time('studentGroup');
        const studentGroupData = await studentGroupSchemas
            .findOne(
                {
                    // _id: convertToMongoObjectId(studentGroupId),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'groups.level': levelNo,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                },
                {
                    _id: 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.rotation': 1,
                    'groups.students._student_id': 1,
                    'groups.students.name': 1,
                    'groups.students.academic_no': 1,
                    'groups.courses._course_id': 1,
                    // 'groups.courses.session_types.session_type': 1,
                    // 'groups.courses.session_types.symbol': 1,
                    'groups.courses.setting._id': 1,
                    'groups.courses.setting.gender': 1,
                    // 'groups.courses.setting.session_setting.delivery_type': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups.group_name': 1,
                    'groups.courses.setting.session_setting.groups.group_no': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                },
            )
            .lean();
        console.timeEnd('studentGroup');
        if (!studentGroupData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Student Group Not Found ${courseId}`,
            };
        const groupLevelData = studentGroupData.groups.find(
            (groupElement) => groupElement.level === levelNo && groupElement.term === term,
        );
        if (!groupLevelData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Student Group Level Not Found ${level} ${term}`,
            };
        const courseGroupData = groupLevelData.courses.find(
            (courseElement) => courseElement._course_id.toString() === courseId,
        );
        if (!courseGroupData)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: `Student Group Course Not Found ${courseId}`,
            };
        const courseGroupStructure = [];
        for (courseElement of courseGroupData.setting) {
            for (sessionSettingElement of courseElement.session_setting) {
                for (groupElement of sessionSettingElement.groups) {
                    const courseGroupStudents = groupLevelData.students
                        .filter((studentElement) =>
                            groupElement._student_ids.find(
                                (studentIdElement) =>
                                    studentIdElement.toString() ===
                                    studentElement._student_id.toString(),
                            ),
                        )
                        .map((studentElement) => {
                            return {
                                _id: convertToMongoObjectId(studentElement._student_id),
                                name: studentElement.name,
                                user_id: studentElement.academic_no,
                            };
                        });
                    courseGroupStructure.push({
                        delivery_symbol: sessionSettingElement.session_type,
                        gender: courseElement.gender,
                        group_no: groupElement.group_no,
                        groupId: [groupElement._id],
                        students: courseGroupStudents.filter(
                            (studentElement, index) =>
                                courseGroupStudents.findIndex(
                                    (studentArrayElement) =>
                                        studentArrayElement._id.toString() ===
                                        studentElement._id.toString(),
                                ) === index,
                        ),
                        groupData: {
                            group_id: courseElement._id,
                            gender: courseElement.gender,
                            group_no: groupElement.group_no,
                            group_name:
                                courseElement.gender === GENDER.MALE
                                    ? 'MG-1'
                                    : courseElement.gender === GENDER.FEMALE
                                    ? 'FG-1'
                                    : 'SG1',
                            session_group: [
                                {
                                    session_group_id: groupElement._id,
                                    group_no: groupElement.group_no,
                                    group_name: groupElement.group_name,
                                },
                            ],
                        },
                    });
                }
            }
        }
        const courseSessionDeliveryList = [];
        for (sessionElement of courseSessionData.session_flow_data) {
            const deliveryIndex = courseSessionDeliveryList.findIndex(
                (deliveryElement) =>
                    deliveryElement._session_type_id.toString() ===
                        sessionElement._session_id.toString() &&
                    deliveryElement._delivery_id.toString() ===
                        sessionElement._delivery_id.toString(),
            );
            if (deliveryIndex !== -1) {
                courseSessionDeliveryList[deliveryIndex].session.push({
                    _session_id: convertToMongoObjectId(sessionElement._id),
                    s_no: sessionElement.s_no,
                    delivery_symbol: sessionElement.delivery_symbol,
                    delivery_no: sessionElement.delivery_no,
                    session_type: sessionElement.delivery_type,
                    session_topic: sessionElement.delivery_topic,
                    student_groups: [],
                });
            } else {
                courseSessionDeliveryList.push({
                    delivery_symbol: sessionElement.delivery_symbol,
                    rotation: 'no',
                    remotePlatform: null,
                    _institution_id: convertToMongoObjectId('5e5d0f1a15b4d600173d5692'),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    program_name: programName,
                    _course_id: convertToMongoObjectId(courseId),
                    _session_type_id: convertToMongoObjectId(sessionElement._session_id),
                    _delivery_id: convertToMongoObjectId(sessionElement._delivery_id),
                    year_no: yearNo,
                    level_no: levelNo,
                    term,
                    // mode:
                    //     sessionElement.facilityId && sessionElement.facilityId === 'ONLINE'
                    //         ? 'remote'
                    //         : 'onsite',
                    staffs: [
                        {
                            staff_name: {
                                first: '',
                                middle: '',
                                last: '',
                            },
                            _staff_id: '',
                        },
                    ],
                    subjects: sessionElement.subjects,
                    _student_group_id: convertToMongoObjectId(studentGroupData._id),
                    session: [
                        {
                            _session_id: convertToMongoObjectId(sessionElement._id),
                            s_no: sessionElement.s_no,
                            delivery_symbol: sessionElement.delivery_symbol,
                            delivery_no: sessionElement.delivery_no,
                            session_type: sessionElement.delivery_type,
                            session_topic: sessionElement.delivery_topic,
                            student_groups: [],
                        },
                    ],
                    occurrence: [],
                });
            }
        }

        // Institution Events
        const levelEventList = [...levelData.events, ...courseData.courses_events].filter(
            (eventElement, index) =>
                [...levelData.events, ...courseData.courses_events].findIndex(
                    (checkEventElement) =>
                        checkEventElement._event_id.toString() ===
                        eventElement._event_id.toString(),
                ) === index,
        );
        const scheduleQuery = {
            isDeleted: false,
            // isActive: true,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            ...(programId && { _program_id: convertToMongoObjectId(programId) }),
            ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
            term,
            level_no: levelNo,
            type: 'regular',
            status: { $in: ['completed', 'missed'] },
            schedule_date: { $lte: todayDate },
        };
        const scheduleProjection = {
            'session.s_no': 1,
            'session._session_id': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'student_groups.group_id': 1,
            'student_groups.gender': 1,
            'student_groups.group_no': 1,
            'student_groups.session_group.group_no': 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .find(scheduleQuery, scheduleProjection)
            .sort({ 'session.s_no': -1 })
            .lean();
        console.timeEnd('courseScheduleData');
        const occurrenceDays = (scheduleSettingElement) => {
            const daysWithY = [];
            for (settingOccurrenceElement of scheduleSettingElement.settingOccurrence)
                for (const day in settingOccurrenceElement) {
                    if (settingOccurrenceElement[day] === 'Y') {
                        daysWithY.push({
                            day,
                            start: settingOccurrenceElement.start,
                            end: settingOccurrenceElement.end,
                            startDateTime: settingOccurrenceElement.startDateTime,
                            endDateTime: settingOccurrenceElement.endDateTime,
                        });
                    }
                }
            const daysOfWeek = [
                'Sunday',
                'Monday',
                'Tuesday',
                'Wednesday',
                'Thursday',
                'Friday',
                'Saturday',
            ];
            return daysWithY.sort((a, b) => {
                const indexA = daysOfWeek.indexOf(a);
                const indexB = daysOfWeek.indexOf(b);
                return indexA - indexB;
            });
        };
        const scheduleSettingList = [];
        const scheduleSettingListBulkWrite = [];
        const scheduleBulkWrite = [];
        const studentGroupBasedSessionSetting = [];
        for (scheduleSettingElement of courseScheduleSettingWithMultiStaffSetting) {
            // Course Start Date as Today
            let courseStart = moment();
            courseStart = courseStart.add(1, 'days');
            courseStart = courseStart.startOf('day');
            // let courseStart = moment(courseData.start_date);
            const courseEnd = moment(courseData.end_date);
            if (
                !courseSessionDeliveryList.find(
                    (sessionDeliveryElement) =>
                        sessionDeliveryElement.delivery_symbol ===
                        scheduleSettingElement.deliveryCode,
                )
            )
                continue;
            const deliverySetting = clone(
                courseSessionDeliveryList.find(
                    (sessionDeliveryElement) =>
                        sessionDeliveryElement.delivery_symbol ===
                        scheduleSettingElement.deliveryCode,
                ),
            );

            deliverySetting.classNo = scheduleSettingElement.classNo;
            const settingGroup = courseGroupStructure.find(
                (groupElement) =>
                    groupElement.delivery_symbol === scheduleSettingElement.deliveryCode &&
                    groupElement.gender === scheduleSettingElement.gender &&
                    parseInt(groupElement.group_no) === parseInt(scheduleSettingElement.groupNo),
            );
            const groupSchedule = courseScheduleData.filter(
                (scheduleElement) =>
                    scheduleElement.session.delivery_symbol ===
                        scheduleSettingElement.deliveryCode &&
                    scheduleElement.student_groups.find(
                        (scheduleGroupElement) =>
                            scheduleGroupElement.gender === scheduleSettingElement.gender &&
                            scheduleGroupElement.session_group.find(
                                (scheduleSessionGroupElement) =>
                                    parseInt(scheduleSessionGroupElement.group_no) ===
                                    parseInt(scheduleSettingElement.groupNo),
                            ),
                    ),
            );
            // Only for Dev
            deliverySetting.schedules = groupSchedule;
            const lastSessionNo = groupSchedule.reduce((max, obj) => {
                return obj.session.s_no > max.session.s_no ? obj : max;
            }, groupSchedule[0]);
            deliverySetting.lastSessionNo = lastSessionNo;
            deliverySetting.session = clone(
                deliverySetting.session.filter(
                    (sessionElement) =>
                        lastSessionNo
                            ? parseInt(sessionElement.s_no) > parseInt(lastSessionNo.session.s_no)
                            : true,
                    // (sessionElement) =>
                    //     !groupSchedule.find(
                    //         (scheduleElement) =>
                    //             parseInt(scheduleElement.session.s_no) ===
                    //                 parseInt(sessionElement.s_no) &&
                    //             scheduleElement.session.delivery_symbol ===
                    //                 sessionElement.delivery_symbol &&
                    //             parseInt(scheduleElement.session.delivery_no) ===
                    //                 parseInt(sessionElement.delivery_no) &&
                    //             parseInt(scheduleElement.session.s_no) >
                    //                 parseInt(deliverySetting.lastSessionNo.session.s_no),
                    //     ),
                ),
            );

            // Setting Occurrences
            deliverySetting.occurrence = occurrenceDays(scheduleSettingElement);
            courseStart = courseStart.subtract(1, 'days');
            let matchingDates = [];
            for (occurrenceElement of deliverySetting.occurrence) {
                courseStart = lastSessionNo ? moment() : moment(courseData.start_date);
                if (lastSessionNo) courseStart = courseStart.add(1, 'days');
                courseStart = courseStart.startOf('day');
                courseStart = courseStart.subtract(1, 'days');
                const matchingDate = [];
                let count = 1;
                while (courseStart.format() <= courseEnd.format()) {
                    courseStart = courseStart.add(1, 'days');
                    if (
                        courseStart.format('dddd').toLocaleLowerCase() ===
                        occurrenceElement.day.toString()
                    ) {
                        if (matchingDate.length === 0) count = 1;
                        console.log(courseStart, occurrenceElement.startDateTime);
                        const schedule_date = courseStart.format(); // Get the date from courseStart
                        const startDateTime = moment(
                            courseStart.format('YYYY-MM-DD') +
                                ' ' +
                                moment(occurrenceElement.startDateTime).format('HH:mm:ss'),
                        );
                        const endDateTime = moment(
                            courseStart.format('YYYY-MM-DD') +
                                ' ' +
                                moment(occurrenceElement.endDateTime).format('HH:mm:ss'),
                        );
                        if (
                            !levelEventList.some(
                                (existingEvent) =>
                                    (moment(existingEvent.start_time) <= startDateTime &&
                                        moment(existingEvent.end_time) >= startDateTime) ||
                                    (moment(existingEvent.start_time) <= endDateTime &&
                                        moment(existingEvent.end_time) >= endDateTime),
                            )
                        ) {
                            matchingDate.push({
                                schedule_date,
                                start: occurrenceElement.start,
                                end: occurrenceElement.end,
                                startDateTime,
                                endDateTime,
                                week: count,
                                scheduled: false,
                                day: occurrenceElement.day.toString().toUpperCase().substring(0, 3),
                            });
                        }
                    }
                    if (courseStart.format('dddd') === 'Sunday') count++;
                }
                matchingDates = [...matchingDates, ...matchingDate];
            }
            deliverySetting.matchingDates = matchingDates.sort((a, b) => {
                let comparison = 0;
                if (a.startDateTime < b.startDateTime) {
                    comparison = -1;
                } else if (a.startDateTime > b.startDateTime) {
                    comparison = 1;
                }
                return comparison;
            });

            if (settingGroup && settingGroup.groupData) {
                for (sessionElement of deliverySetting.session) {
                    sessionElement.student_groups = [settingGroup.groupData];
                }
            }
            deliverySetting.students =
                settingGroup && settingGroup.students && settingGroup.students.length
                    ? settingGroup.students
                    : [];
            deliverySetting.staffs = staffDatas
                .filter((staffElement) =>
                    scheduleSettingElement.staffs.find(
                        (settingStaffElement) =>
                            settingStaffElement.staffEmail.toLowerCase() ===
                            staffElement.email.toLowerCase(),
                    ),
                )
                .map((staffElement) => {
                    return {
                        staff_name: staffElement.name,
                        _staff_id: staffElement._id,
                    };
                });
            if (!deliverySetting.staffs.length) continue;
            // Infra detail
            deliverySetting.mode = 'onsite';
            if (
                scheduleSettingElement.facilityId &&
                scheduleSettingElement.facilityId !== '' &&
                scheduleSettingElement.facilityId !== ' '
            ) {
                if (scheduleSettingElement.facilityId === 'ONLINE' && levelRemoteInfraData[0]) {
                    // Remote Infra
                    deliverySetting._infra_id = levelRemoteInfraData[0]._id;
                    deliverySetting.infra_name = levelRemoteInfraData[0].meetingTitle;
                    deliverySetting.mode = 'remote';
                } else {
                    const settingInfra = infrastructureData.find(
                        (infraElement) =>
                            infraElement.name.trim() === scheduleSettingElement.facilityId,
                    );
                    if (settingInfra) {
                        deliverySetting._infra_id = settingInfra._id;
                        deliverySetting.infra_name = settingInfra.name;
                        deliverySetting.mode = 'onsite';
                    }
                }
            }
            scheduleSettingListBulkWrite.push({
                insertOne: { document: deliverySetting },
            });

            const multiSettingIndex = studentGroupBasedSessionSetting.findIndex(
                (multiSettingElement) => multiSettingElement.classNo === deliverySetting.classNo,
            );
            deliverySetting.matchingScheduleIndex = 0;
            if (multiSettingIndex === -1) {
                deliverySetting.settingIndex = 1;
                studentGroupBasedSessionSetting.push({
                    classNo: deliverySetting.classNo,
                    session: deliverySetting.session,
                    settings: [deliverySetting],
                });
            } else {
                const newSessionPush = [...deliverySetting.session];
                for (sessionElement of deliverySetting.session) {
                    if (
                        !studentGroupBasedSessionSetting[multiSettingIndex].session.find(
                            (sessionSettingElement) =>
                                sessionSettingElement.s_no === sessionElement.s_no,
                        )
                    ) {
                        newSessionPush.push(sessionElement);
                    }
                }
                studentGroupBasedSessionSetting[multiSettingIndex].session = newSessionPush.sort(
                    (a, b) => {
                        return a.s_no > b.s_no;
                    },
                );
                deliverySetting.settingIndex =
                    studentGroupBasedSessionSetting[multiSettingIndex].settings.length + 1;
                studentGroupBasedSessionSetting[multiSettingIndex].settings.push(deliverySetting);
            }
            // Todo : Need to Enable this for Efficient flow
            // const dbSettingCheckData = courseScheduleSettingDBData.find(
            //     (dbSettingElement) =>
            //         dbSettingElement.studentGroupIds.length === settingGroup.groupId.length &&
            //         dbSettingElement.studentGroupIds.find((studentGroupIdElement) =>
            //             settingGroup.groupId.find(
            //                 (groupIdElement) =>
            //                     groupIdElement.toString() === studentGroupIdElement.toString(),
            //             ),
            //         ) &&
            //         scheduleSettingElement.deliveryCode ===
            //             dbSettingElement.session[0].delivery_symbol,
            // );
            // if (!dbSettingCheckData && deliverySetting.staffs.length) {
            //     scheduleSettingListBulkWrite.push({
            //         insertOne: { document: deliverySetting },
            //     });
            // } else {
            //     const staffCheck = staffOccurrenceCompare(
            //         'staff',
            //         dbSettingCheckData.staffs,
            //         deliverySetting.staffs,
            //     );
            //     const occurrenceCheck = staffOccurrenceCompare(
            //         'occurrence',
            //         dbSettingCheckData.occurrence,
            //         deliverySetting.occurrence,
            //     );
            //     if (!staffCheck && !occurrenceCheck && deliverySetting.staffs.length) {
            //         scheduleSettingListBulkWrite.push({
            //             updateOne: {
            //                 filter: {
            //                     _id: convertToMongoObjectId(dbSettingCheckData._id),
            //                 },
            //                 update: {
            //                     $set: deliverySetting,
            //                 },
            //             },
            //         });
            //         // Need to Remove Schedule From Course Schedule
            //         scheduleBulkWrite.push({
            //             deleteMany: {
            //                 filter: {
            //                     isDeleted: false,
            //                     _course_id: convertToMongoObjectId(courseId),
            //                     _program_id: convertToMongoObjectId(programId),
            //                     type: 'regular',
            //                     status: 'pending',
            //                     'student_groups.session_group.session_group_id': {
            //                         $in: dbSettingCheckData.studentGroupIds,
            //                     },
            //                     'staffs._staff_id': { $in: dbSettingCheckData.staffIds },
            //                     'session.delivery_symbol':
            //                         dbSettingCheckData.session[0].delivery_symbol,
            //                 },
            //             },
            //         });
            //     }
            // }
            // scheduleSettingList.push({
            //     deliveryCode: scheduleSettingElement.deliveryCode,
            //     deliverySetting,
            //     // scheduleSettingElement,
            // });
        }
        scheduleBulkWrite.push({
            deleteMany: {
                filter: {
                    isDeleted: false,
                    isActive: true,
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _course_id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                    year_no: yearNo,
                    level_no: levelNo,
                    type: 'regular',
                    status: 'pending',
                    schedule_date: { $gte: todayDate },
                    // 'student_groups.session_group.session_group_id': {
                    //     $in: dbSettingCheckData.studentGroupIds,
                    // },
                    // 'staffs._staff_id': { $in: dbSettingCheckData.staffIds },
                    // 'session.delivery_symbol': dbSettingCheckData.session[0].delivery_symbol,
                },
            },
        });
        let scheduleRemove = {};
        if (dbUpdate && scheduleBulkWrite.length) {
            scheduleRemove = await courseScheduleSchemas.bulkWrite(scheduleBulkWrite);
        }
        const scheduleBulkCreate = [];
        for (groupSettingElement of studentGroupBasedSessionSetting) {
            if (groupSettingElement.settings.length && groupSettingElement.settings.length === 1) {
                const sessionSettingElement = groupSettingElement.settings[0];
                const settingMatchingWeek = clone(sessionSettingElement.matchingDates);
                let matchingScheduleIndex = 0;
                for (sessionElement of sessionSettingElement.session) {
                    if (settingMatchingWeek.length > matchingScheduleIndex) {
                        const scheduleObject = {
                            _institution_id: convertToMongoObjectId(
                                sessionSettingElement._institution_id,
                            ),
                            _institution_calendar_id: convertToMongoObjectId(
                                sessionSettingElement._institution_calendar_id,
                            ),
                            _program_id: convertToMongoObjectId(sessionSettingElement._program_id),
                            program_name: sessionSettingElement.program_name,
                            type: 'regular',
                            term: sessionSettingElement.term,
                            _student_group_id: convertToMongoObjectId(
                                sessionSettingElement._student_group_id,
                            ),
                            year_no: sessionSettingElement.year_no,
                            level_no: sessionSettingElement.level_no,
                            _course_id: convertToMongoObjectId(sessionSettingElement._course_id),
                            course_name: courseData.courses_name,
                            course_code: courseData.courses_number,
                            schedule_date: settingMatchingWeek[matchingScheduleIndex].schedule_date,
                            start: settingMatchingWeek[matchingScheduleIndex].start,
                            end: settingMatchingWeek[matchingScheduleIndex].end,
                            scheduleStartDateAndTime:
                                settingMatchingWeek[matchingScheduleIndex].startDateTime,
                            scheduleEndDateAndTime:
                                settingMatchingWeek[matchingScheduleIndex].endDateTime,
                            session: {
                                _session_id: sessionElement._session_id,
                                s_no: sessionElement.s_no,
                                delivery_symbol: sessionElement.delivery_symbol,
                                delivery_no: sessionElement.delivery_no,
                                session_type: sessionElement.session_type,
                                session_topic: sessionElement.session_topic,
                            },
                            student_groups: sessionElement.student_groups,
                            mode: sessionSettingElement.mode,
                            subjects: sessionSettingElement.subjects,
                            staffs: sessionSettingElement.staffs,
                            _infra_id: sessionSettingElement._infra_id,
                            infra_name: sessionSettingElement.infra_name,
                            topic: sessionSettingElement.topic,
                            _topic_id: sessionSettingElement._topic_id,
                            students: sessionSettingElement.students,
                            scheduleBy: 'SIS',
                            remotePlatform: REMOTE_PLATFORM.TEAMS,
                            status:
                                new Date(
                                    settingMatchingWeek[matchingScheduleIndex].schedule_date,
                                ) >= new Date()
                                    ? 'pending'
                                    : 'missed',
                        };
                        scheduleBulkCreate.push({
                            insertOne: {
                                document: scheduleObject,
                            },
                        });
                        matchingScheduleIndex++;
                    }
                }
            } else {
                for (groupSessionElement of groupSettingElement.session) {
                    const groupMultiSetting = [];
                    for (settingElement of groupSettingElement.settings) {
                        const sessionSettingElement = settingElement;
                        const settingMatchingWeek = clone(sessionSettingElement.matchingDates);
                        // let matchingScheduleIndex = 0;
                        // for (sessionElement of sessionSettingElement.session) {
                        if (
                            settingMatchingWeek.length > sessionSettingElement.matchingScheduleIndex
                        ) {
                            const scheduleObject = {
                                _institution_id: convertToMongoObjectId(
                                    sessionSettingElement._institution_id,
                                ),
                                _institution_calendar_id: convertToMongoObjectId(
                                    sessionSettingElement._institution_calendar_id,
                                ),
                                _program_id: convertToMongoObjectId(
                                    sessionSettingElement._program_id,
                                ),
                                program_name: sessionSettingElement.program_name,
                                type: 'regular',
                                term: sessionSettingElement.term,
                                _student_group_id: convertToMongoObjectId(
                                    sessionSettingElement._student_group_id,
                                ),
                                year_no: sessionSettingElement.year_no,
                                level_no: sessionSettingElement.level_no,
                                _course_id: convertToMongoObjectId(
                                    sessionSettingElement._course_id,
                                ),
                                course_name: courseData.courses_name,
                                course_code: courseData.courses_number,
                                schedule_date:
                                    settingMatchingWeek[sessionSettingElement.matchingScheduleIndex]
                                        .schedule_date,
                                start: settingMatchingWeek[
                                    sessionSettingElement.matchingScheduleIndex
                                ].start,
                                end: settingMatchingWeek[
                                    sessionSettingElement.matchingScheduleIndex
                                ].end,
                                scheduleStartDateAndTime:
                                    settingMatchingWeek[sessionSettingElement.matchingScheduleIndex]
                                        .startDateTime,
                                scheduleEndDateAndTime:
                                    settingMatchingWeek[sessionSettingElement.matchingScheduleIndex]
                                        .endDateTime,
                                session: {
                                    _session_id: groupSessionElement._session_id,
                                    s_no: groupSessionElement.s_no,
                                    delivery_symbol: groupSessionElement.delivery_symbol,
                                    delivery_no: groupSessionElement.delivery_no,
                                    session_type: groupSessionElement.session_type,
                                    session_topic: groupSessionElement.session_topic,
                                },
                                student_groups: groupSessionElement.student_groups,
                                mode: sessionSettingElement.mode,
                                subjects: sessionSettingElement.subjects,
                                staffs: sessionSettingElement.staffs,
                                _infra_id: sessionSettingElement._infra_id,
                                infra_name: sessionSettingElement.infra_name,
                                topic: sessionSettingElement.topic,
                                _topic_id: sessionSettingElement._topic_id,
                                students: sessionSettingElement.students,
                                scheduleBy: 'SIS',
                                remotePlatform: REMOTE_PLATFORM.TEAMS,
                                status:
                                    new Date(
                                        settingMatchingWeek[
                                            sessionSettingElement.matchingScheduleIndex
                                        ].schedule_date,
                                    ) >= new Date()
                                        ? 'pending'
                                        : 'missed',
                                settingIndex: sessionSettingElement.settingIndex,
                            };
                            groupMultiSetting.push(scheduleObject);
                            // sessionSettingElement.matchingScheduleIndex++;
                        }
                        // }
                    }
                    const sessionFilterData = groupMultiSetting
                        .filter(
                            (groupMultiSettingElement) =>
                                groupMultiSettingElement.session._session_id.toString() ===
                                groupSessionElement._session_id.toString(),
                        )
                        .sort((a, b) => {
                            const dateA = new Date(a.scheduleStartDateAndTime);
                            const dateB = new Date(b.scheduleStartDateAndTime);
                            return dateA - dateB;
                        });
                    if (sessionFilterData.length) {
                        // Need to Push Sorted Schedule First
                        scheduleBulkCreate.push({
                            insertOne: {
                                document: sessionFilterData[0],
                            },
                        });
                        const settingIndex = groupSettingElement.settings.findIndex(
                            (settingElement) =>
                                settingElement.settingIndex === sessionFilterData[0].settingIndex,
                        );
                        if (settingIndex !== -1)
                            groupSettingElement.settings[settingIndex].matchingScheduleIndex++;
                    }
                }
                // for (groupSessionElement of groupSettingElement.session) {
                //
                // }
                // const groupMultiSetting = [];
                // for (settingElement of groupSettingElement.settings) {
                //     const sessionSettingElement = settingElement;
                //     const settingMatchingWeek = clone(sessionSettingElement.matchingDates);
                //     let matchingScheduleIndex = 0;
                //     for (sessionElement of sessionSettingElement.session) {
                //         if (settingMatchingWeek.length > matchingScheduleIndex) {
                //             const scheduleObject = {
                //                 _institution_id: convertToMongoObjectId(
                //                     sessionSettingElement._institution_id,
                //                 ),
                //                 _institution_calendar_id: convertToMongoObjectId(
                //                     sessionSettingElement._institution_calendar_id,
                //                 ),
                //                 _program_id: convertToMongoObjectId(
                //                     sessionSettingElement._program_id,
                //                 ),
                //                 program_name: sessionSettingElement.program_name,
                //                 type: 'regular',
                //                 term: sessionSettingElement.term,
                //                 _student_group_id: convertToMongoObjectId(
                //                     sessionSettingElement._student_group_id,
                //                 ),
                //                 year_no: sessionSettingElement.year_no,
                //                 level_no: sessionSettingElement.level_no,
                //                 _course_id: convertToMongoObjectId(
                //                     sessionSettingElement._course_id,
                //                 ),
                //                 course_name: courseData.courses_name,
                //                 course_code: courseData.courses_number,
                //                 schedule_date:
                //                     settingMatchingWeek[matchingScheduleIndex].schedule_date,
                //                 start: settingMatchingWeek[matchingScheduleIndex].start,
                //                 end: settingMatchingWeek[matchingScheduleIndex].end,
                //                 scheduleStartDateAndTime:
                //                     settingMatchingWeek[matchingScheduleIndex].startDateTime,
                //                 scheduleEndDateAndTime:
                //                     settingMatchingWeek[matchingScheduleIndex].endDateTime,
                //                 session: {
                //                     _session_id: sessionElement._session_id,
                //                     s_no: sessionElement.s_no,
                //                     delivery_symbol: sessionElement.delivery_symbol,
                //                     delivery_no: sessionElement.delivery_no,
                //                     session_type: sessionElement.session_type,
                //                     session_topic: sessionElement.session_topic,
                //                 },
                //                 student_groups: sessionElement.student_groups,
                //                 mode: sessionSettingElement.mode,
                //                 subjects: sessionSettingElement.subjects,
                //                 staffs: sessionSettingElement.staffs,
                //                 _infra_id: sessionSettingElement._infra_id,
                //                 infra_name: sessionSettingElement.infra_name,
                //                 topic: sessionSettingElement.topic,
                //                 _topic_id: sessionSettingElement._topic_id,
                //                 students: sessionSettingElement.students,
                //                 scheduleBy: 'SIS',
                //                 remotePlatform: REMOTE_PLATFORM.TEAMS,
                //                 status:
                //                     new Date(
                //                         settingMatchingWeek[matchingScheduleIndex].schedule_date,
                //                     ) >= new Date()
                //                         ? 'pending'
                //                         : 'missed',
                //             };
                //             groupMultiSetting.push(scheduleObject);
                //             matchingScheduleIndex++;
                //         }
                //     }
                // }
                // for (groupSessionElement of groupSettingElement.session) {
                //     const sessionFilterData = groupMultiSetting
                //         .filter(
                //             (groupMultiSettingElement) =>
                //                 groupMultiSettingElement.session._session_id.toString() ===
                //                 groupSessionElement._session_id.toString(),
                //         )
                //         .sort((a, b) => {
                //             const dateA = a.scheduleStartDateAndTime;
                //             const dateB = b.scheduleStartDateAndTime;
                //             return dateA - dateB;
                //         });
                //     if (sessionFilterData.length) {
                //         // Need to Push Sorted Schedule First
                //         scheduleBulkCreate.push({
                //             insertOne: {
                //                 document: sessionFilterData[0],
                //             },
                //         });
                //     }
                // }
            }
        }
        // for (scheduleCreateElement of scheduleSettingListBulkWrite) {
        //     const sessionSettingElement = scheduleCreateElement.insertOne.document;
        //     const settingMatchingWeek = clone(sessionSettingElement.matchingDates);
        //     let matchingScheduleIndex = 0;
        //     for (sessionElement of sessionSettingElement.session) {
        //         if (settingMatchingWeek.length > matchingScheduleIndex) {
        //             // const startDateAndTime = convertingRiyadhToUTC(
        //             //     new Date(sessionSetting.matchingDates[matchingWeek.index].schedule_date),
        //             //     startHours,
        //             //     startTime.minute,
        //             // );
        //             // const endDateAndTime = convertingRiyadhToUTC(
        //             //     new Date(sessionSetting.matchingDates[matchingWeek.index].schedule_date),
        //             //     endHours,
        //             //     endTime.minute,
        //             // );
        //             const scheduleObject = {
        //                 _institution_id: convertToMongoObjectId(
        //                     sessionSettingElement._institution_id,
        //                 ),
        //                 _institution_calendar_id: convertToMongoObjectId(
        //                     sessionSettingElement._institution_calendar_id,
        //                 ),
        //                 _program_id: convertToMongoObjectId(sessionSettingElement._program_id),
        //                 program_name: sessionSettingElement.program_name,
        //                 type: 'regular',
        //                 term: sessionSettingElement.term,
        //                 _student_group_id: convertToMongoObjectId(
        //                     sessionSettingElement._student_group_id,
        //                 ),
        //                 year_no: sessionSettingElement.year_no,
        //                 level_no: sessionSettingElement.level_no,
        //                 _course_id: convertToMongoObjectId(sessionSettingElement._course_id),
        //                 course_name: courseData.courses_name,
        //                 course_code: courseData.courses_number,
        //                 schedule_date: settingMatchingWeek[matchingScheduleIndex].schedule_date,
        //                 start: settingMatchingWeek[matchingScheduleIndex].start,
        //                 end: settingMatchingWeek[matchingScheduleIndex].end,
        //                 scheduleStartDateAndTime:
        //                     settingMatchingWeek[matchingScheduleIndex].startDateTime,
        //                 scheduleEndDateAndTime:
        //                     settingMatchingWeek[matchingScheduleIndex].endDateTime,
        //                 session: {
        //                     _session_id: sessionElement._session_id,
        //                     s_no: sessionElement.s_no,
        //                     delivery_symbol: sessionElement.delivery_symbol,
        //                     delivery_no: sessionElement.delivery_no,
        //                     session_type: sessionElement.session_type,
        //                     session_topic: sessionElement.session_topic,
        //                 },
        //                 student_groups: sessionElement.student_groups,
        //                 mode: sessionSettingElement.mode,
        //                 subjects: sessionSettingElement.subjects,
        //                 staffs: sessionSettingElement.staffs,
        //                 _infra_id: sessionSettingElement._infra_id,
        //                 infra_name: sessionSettingElement.infra_name,
        //                 topic: sessionSettingElement.topic,
        //                 _topic_id: sessionSettingElement._topic_id,
        //                 students: sessionSettingElement.students,
        //                 scheduleBy: 'SIS',
        //                 remotePlatform: REMOTE_PLATFORM.TEAMS,
        //                 status:
        //                     new Date(settingMatchingWeek[matchingScheduleIndex].schedule_date) >=
        //                     new Date()
        //                         ? 'pending'
        //                         : 'missed',
        //             };
        //             scheduleBulkCreate.push({
        //                 insertOne: {
        //                     document: scheduleObject,
        //                 },
        //             });
        //             matchingScheduleIndex++;
        //         }
        //     }
        // }
        let scheduleCreate = {};
        if (dbUpdate && scheduleBulkCreate.length) {
            scheduleCreate = await courseScheduleSchemas.bulkWrite(scheduleBulkCreate);
        }
        return {
            data: {
                ...(devMode && {
                    courseScheduleSettingData,
                    courseSessionData,
                    courseScheduleSettingDBData,
                    courseGroupData,
                    courseSessionDeliveryList,
                    courseGroupStructure,
                    scheduleSettingList,
                    courseScheduleData,
                    levelEventList,
                    scheduleSettingListBulkWrite,
                    courseScheduleSettingWithMultiStaff,
                    courseScheduleSettingWithMultiStaffSetting,
                    scheduleBulkWrite,
                    scheduleBulkCreate,
                    studentGroupBasedSessionSetting,
                }),
                scheduleRemove,
                scheduleCreate,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getTermDetails,
    getEnrollStudentDetails,
    getCourseDetails,
    getStudentGroupStudentPull,
    getStudentGroupStudentPullWithGroup,
    getStudentGroupForAllCalendar,
    programStudentRefresh,
    courseScheduleSync,
};
