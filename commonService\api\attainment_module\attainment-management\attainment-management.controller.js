const assessmentManagementSchema = require('../assessment-management/assessment-management.model');
const attainmentManagementSchema = require('./attainment-management.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { getProgramCurriculums } = require('./attainment-management.service');
const { PLO, CLO } = require('../../../utility/constants');

const getSettingAssessmentNode = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const assessmentTypes = await assessmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    types: 1,
                },
            )
            .lean();
        const assessmentTypeList = [];
        for (typeElement of assessmentTypes.types) {
            // const typeObjects = [];
            const subTypeObjects = [];
            for (subtypeElement of typeElement.subTypes) {
                const assessmentTypeObjects = [];
                if (subtypeElement.isActive) {
                    for (assessmentTypeElement of subtypeElement.assessmentTypes) {
                        if (assessmentTypeElement.isActive) {
                            assessmentTypeObjects.push({
                                _id: assessmentTypeElement._id,
                                name: assessmentTypeElement.name,
                            });
                        }
                    }
                    if (assessmentTypeObjects.length)
                        subTypeObjects.push({
                            _id: subtypeElement._id,
                            name: subtypeElement.typeName,
                            assessmentTypes: assessmentTypeObjects,
                        });
                }
            }
            if (subTypeObjects.length)
                assessmentTypeList.push({
                    _id: typeElement._id,
                    name: typeElement.typeName,
                    subTypes: subTypeObjects,
                });
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: assessmentTypeList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttainmentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        const attainmentList = await attainmentManagementSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                    type: 'program',
                },
                {
                    regulationName: 1,
                    regulationYear: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    curriculumName: 1,
                    outcomes: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: attainmentList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttainmentPrerequisite = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        // Curriculum Names & Regularization Years
        let currentYear = new Date().getFullYear();
        const regularizationYears = [currentYear];
        for (let i = 1; i < 5; i++) {
            currentYear -= 1;
            regularizationYears.push(currentYear);
        }
        const programCurriculums = await getProgramCurriculums({ _institution_id, programId });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { regularizationYears, programCurriculums, outComes: [PLO, CLO] },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAttainment = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            programId,
            regulationYear,
            regulationName,
            curriculumId,
            curriculumName,
            outcomes,
        } = body;
        const attainmentOutCome = outcomes.map((outcomeElement) => {
            return {
                outComeType: outcomeElement,
            };
        });
        const addFlow = await attainmentManagementSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            _program_id: convertToMongoObjectId(programId),
            type: 'program',
            regulationYear,
            regulationName,
            _curriculum_id: convertToMongoObjectId(curriculumId),
            curriculumName,
            outcomes,
            evaluationPlan: attainmentOutCome,
            attainmentLevel: attainmentOutCome,
        });
        if (!addFlow) return { statusCode: 410, message: 'UNABLE_TO_ADD' };
        return {
            statusCode: 200,
            message: 'ATTAINMENT_ADDED',
            data: addFlow,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editAttainment = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _id,
            regulationYear,
            regulationName,
            curriculumId,
            curriculumName,
            outcomes,
            type,
        } = body;
        const attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                },
                {
                    outcomes: 1,
                    _program_id: 1,
                },
            )
            .lean();
        if (!attainmentList) return { statusCode: 200, message: 'NOT_FOUND' };
        const updateObject = {
            $set: {
                regulationYear,
                regulationName,
                _curriculum_id: convertToMongoObjectId(curriculumId),
                curriculumName,
                courseUpdate: !!(type && type === 'course'),
            },
        };
        if (outcomes && outcomes.length) {
            for (outComeElement of outcomes) {
                if (
                    !attainmentList.outcomes.find(
                        (outcomeElement) => outcomeElement.toString() === outComeElement.toString(),
                    )
                ) {
                    updateObject.$push = {
                        outcomes: outComeElement,
                        evaluationPlan: {
                            outComeType: outComeElement,
                        },
                        attainmentLevel: {
                            outComeType: outComeElement,
                        },
                    };
                }
            }
        }
        const editFlow = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            updateObject,
        );
        if (!editFlow) return { statusCode: 410, message: 'UNABLE_TO_EDIT' };
        if (type && type === 'program') {
            await attainmentManagementSchema.updateMany(
                {
                    _program_id: convertToMongoObjectId(attainmentList._program_id),
                    _curriculum_id: convertToMongoObjectId(curriculumId),
                    regulationYear,
                    regulationName,
                    type: 'course',
                    courseUpdate: false,
                },
                updateObject,
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_EDITED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeAttainment = async ({ body = {} }) => {
    try {
        const { _id } = body;
        const removeFlow = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            {
                $set: {
                    isDeleted: true,
                },
            },
        );
        if (!removeFlow) return { statusCode: 410, message: 'UNABLE_TO_REMOVE' };
        return {
            statusCode: 200,
            message: 'ATTAINMENT_REMOVE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttainment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id } = query;
        const attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                },
                {
                    outcomes: 1,
                    regulationYear: 1,
                    regulationName: 1,
                    evaluationPlan: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    curriculumName: 1,
                    attainmentLevel: 1,
                    levelColors: 1,
                    type: 1,
                    manageTargetBenchMark: 1,
                },
            )
            .lean();
        if (!attainmentList) return { statusCode: 200, message: 'NOT_FOUND' };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: attainmentList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAttainmentNode = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, masterId, typeName, typeId, weightage, nodeName, type } = body;
        const attainmentData = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                },
                {
                    evaluationPlan: 1,
                    _program_id: 1,
                    regulationYear: 1,
                    regulationName: 1,
                    _curriculum_id: 1,
                },
            )
            .lean();
        const updateObject = {
            _id: convertToMongoObjectId(),
            typeName,
            typeId,
            weightage,
            nodeName,
            isActive: true,
        };
        if (!attainmentData) return { statusCode: 200, message: 'NOT_FOUND' };
        for (evaluationPlanElement of attainmentData.evaluationPlan) {
            if (evaluationPlanElement._id.toString() === masterId) {
                evaluationPlanElement.tree.push(updateObject);
            } else {
                for (treeElement of evaluationPlanElement.tree) {
                    if (treeElement._id.toString() === masterId) {
                        treeElement.subTree.push(updateObject);
                    } else {
                        for (nodeElement of treeElement.subTree) {
                            if (nodeElement._id.toString() === masterId) {
                                nodeElement.node.push(updateObject);
                            }
                        }
                    }
                }
            }
        }
        const updateData = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            {
                $set: {
                    evaluationPlan: attainmentData.evaluationPlan,
                    courseUpdate: !!(type && type === 'course'),
                    programSelections: [],
                },
            },
        );
        if (!updateData) return { statusCode: 410, message: 'UNABLE_TO_ADD_NODE' };
        if (type && type === 'program') {
            await attainmentManagementSchema.updateMany(
                {
                    _program_id: convertToMongoObjectId(attainmentData._program_id),
                    _curriculum_id: convertToMongoObjectId(attainmentData._curriculum_id),
                    regulationYear: attainmentData.regulationYear,
                    regulationName: attainmentData.regulationName,
                    type: 'course',
                    courseUpdate: false,
                },
                { $set: { evaluationPlan: attainmentData.evaluationPlan } },
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_NODE_ADDED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editAttainmentNode = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, masterId, typeName, typeId, weightage, nodeName, type } = body;
        const attainmentData = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                },
                {
                    evaluationPlan: 1,
                    _program_id: 1,
                    regulationYear: 1,
                    regulationName: 1,
                    _curriculum_id: 1,
                },
            )
            .lean();
        if (!attainmentData) return { statusCode: 200, message: 'NOT_FOUND' };
        let updateObject = {
            _id: convertToMongoObjectId(),
            isActive: true,
            weightage,
            nodeName,
        };
        let evaluationPlanUpdateObject = {};
        let evaluationPlanUpdateFilter = {};
        if (typeId && typeId.length) updateObject = { ...updateObject, ...{ typeId, typeName } };
        for (evaluationPlanElement of attainmentData.evaluationPlan) {
            for (treeElement of evaluationPlanElement.tree) {
                if (treeElement._id.toString() === masterId) {
                    if (typeId && typeId.length) updateObject.subTree = treeElement.subTree;
                    evaluationPlanUpdateObject = {
                        'evaluationPlan.$[i].tree.$[j]': updateObject,
                    };
                    evaluationPlanUpdateFilter = {
                        arrayFilters: [
                            { 'i._id': convertToMongoObjectId(evaluationPlanElement._id) },
                            { 'j._id': convertToMongoObjectId(masterId) },
                        ],
                    };
                } else {
                    for (nodeElement of treeElement.subTree) {
                        if (nodeElement._id.toString() === masterId) {
                            if (typeId && typeId.length) updateObject.node = nodeElement.node;
                            evaluationPlanUpdateObject = {
                                'evaluationPlan.$[i].tree.$[j].subTree.$[k]': updateObject,
                            };
                            evaluationPlanUpdateFilter = {
                                arrayFilters: [
                                    {
                                        'i._id': convertToMongoObjectId(evaluationPlanElement._id),
                                    },
                                    { 'j._id': convertToMongoObjectId(treeElement._id) },
                                    { 'k._id': convertToMongoObjectId(masterId) },
                                ],
                            };
                        } else {
                            for (treeNodeElement of nodeElement.node) {
                                if (treeNodeElement._id.toString() === masterId) {
                                    evaluationPlanUpdateObject = {
                                        'evaluationPlan.$[i].tree.$[j].subTree.$[k].node.$[l]':
                                            updateObject,
                                    };
                                    evaluationPlanUpdateFilter = {
                                        arrayFilters: [
                                            {
                                                'i._id': convertToMongoObjectId(
                                                    evaluationPlanElement._id,
                                                ),
                                            },
                                            {
                                                'j._id': convertToMongoObjectId(treeElement._id),
                                            },
                                            {
                                                'k._id': convertToMongoObjectId(nodeElement._id),
                                            },
                                            { 'l._id': convertToMongoObjectId(masterId) },
                                        ],
                                    };
                                }
                            }
                        }
                    }
                }
            }
        }
        const updateData = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            {
                $set: {
                    ...evaluationPlanUpdateObject,
                    courseUpdate: !!(type && type === 'course'),
                    programSelections: [],
                },
            },
            evaluationPlanUpdateFilter,
        );
        if (!updateData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_NODE' };
        if (type && type === 'program') {
            await attainmentManagementSchema.updateMany(
                {
                    _program_id: convertToMongoObjectId(attainmentData._program_id),
                    _curriculum_id: convertToMongoObjectId(attainmentData._curriculum_id),
                    regulationYear: attainmentData.regulationYear,
                    regulationName: attainmentData.regulationName,
                    type: 'course',
                    courseUpdate: false,
                },
                { $set: { ...evaluationPlanUpdateObject } },
                evaluationPlanUpdateFilter,
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_NODE_UPDATED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteAttainmentNode = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, masterId, type } = body;
        const attainmentData = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                },
                {
                    evaluationPlan: 1,
                    _program_id: 1,
                    regulationYear: 1,
                    regulationName: 1,
                    _curriculum_id: 1,
                },
            )
            .lean();
        if (!attainmentData) return { statusCode: 200, message: 'NOT_FOUND' };
        const updateObject = {
            _id: convertToMongoObjectId(masterId),
        };
        let evaluationPlanUpdateObject = {};
        let evaluationPlanUpdateFilter = {};
        for (evaluationPlanElement of attainmentData.evaluationPlan) {
            for (treeElement of evaluationPlanElement.tree) {
                if (treeElement._id.toString() === masterId) {
                    evaluationPlanUpdateObject = {
                        'evaluationPlan.$[i].tree': updateObject,
                    };
                    evaluationPlanUpdateFilter = {
                        arrayFilters: [
                            { 'i._id': convertToMongoObjectId(evaluationPlanElement._id) },
                        ],
                    };
                } else {
                    for (nodeElement of treeElement.subTree) {
                        if (nodeElement._id.toString() === masterId) {
                            evaluationPlanUpdateObject = {
                                'evaluationPlan.$[i].tree.$[j].subTree': updateObject,
                            };
                            evaluationPlanUpdateFilter = {
                                arrayFilters: [
                                    {
                                        'i._id': convertToMongoObjectId(evaluationPlanElement._id),
                                    },
                                    { 'j._id': convertToMongoObjectId(treeElement._id) },
                                ],
                            };
                        } else {
                            for (treeNodeElement of nodeElement.node) {
                                if (treeNodeElement._id.toString() === masterId) {
                                    evaluationPlanUpdateObject = {
                                        'evaluationPlan.$[i].tree.$[j].subTree.$[k].node':
                                            updateObject,
                                    };
                                    evaluationPlanUpdateFilter = {
                                        arrayFilters: [
                                            {
                                                'i._id': convertToMongoObjectId(
                                                    evaluationPlanElement._id,
                                                ),
                                            },
                                            {
                                                'j._id': convertToMongoObjectId(treeElement._id),
                                            },
                                            {
                                                'k._id': convertToMongoObjectId(nodeElement._id),
                                            },
                                        ],
                                    };
                                }
                            }
                        }
                    }
                }
            }
        }
        const updateData = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            {
                $pull: { ...evaluationPlanUpdateObject },
                $set: { courseUpdate: !!(type && type === 'course'), programSelections: [] },
            },
            evaluationPlanUpdateFilter,
        );
        if (!updateData) return { statusCode: 410, message: 'UNABLE_TO_REMOVE_NODE' };
        if (type && type === 'program') {
            await attainmentManagementSchema.updateMany(
                {
                    _program_id: convertToMongoObjectId(attainmentData._program_id),
                    _curriculum_id: convertToMongoObjectId(attainmentData._curriculum_id),
                    regulationYear: attainmentData.regulationYear,
                    regulationName: attainmentData.regulationName,
                    type: 'course',
                    courseUpdate: false,
                },
                { $pull: { ...evaluationPlanUpdateObject } },
                evaluationPlanUpdateFilter,
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_NODE_REMOVED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const attainmentLevel = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _id,
            masterId,
            start,
            end,
            valuesAs,
            nodes,
            levels,
            targetBenchMark,
            levelColors,
            type,
            manageTargetBenchMark,
        } = body;
        const updateObject = {
            $set: {
                'attainmentLevel.$[i].start': start,
                'attainmentLevel.$[i].end': end,
                'attainmentLevel.$[i].valuesAs': valuesAs,
                'attainmentLevel.$[i].nodes': nodes,
                'attainmentLevel.$[i].levels': levels,
                'attainmentLevel.$[i].targetBenchMark': targetBenchMark,
                'attainmentLevel.$[i].levelColors': levelColors,
                courseUpdate: !!(type && type === 'course'),
                'attainmentLevel.$[i].manageTargetBenchMark': manageTargetBenchMark,
            },
        };
        const updateData = await attainmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            updateObject,
            {
                arrayFilters: [
                    {
                        'i._id': convertToMongoObjectId(masterId),
                    },
                ],
            },
        );
        if (!updateData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_ATTAINMENT' };
        if (type && type === 'program') {
            const attainmentData = await attainmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(_id),
                    },
                    {
                        _program_id: 1,
                        regulationYear: 1,
                        regulationName: 1,
                        _curriculum_id: 1,
                    },
                )
                .lean();
            await attainmentManagementSchema.updateMany(
                {
                    _program_id: convertToMongoObjectId(attainmentData._program_id),
                    _curriculum_id: convertToMongoObjectId(attainmentData._curriculum_id),
                    regulationYear: attainmentData.regulationYear,
                    regulationName: attainmentData.regulationName,
                    type: 'course',
                    courseUpdate: false,
                },
                updateObject,
                {
                    arrayFilters: [
                        {
                            'i._id': convertToMongoObjectId(masterId),
                        },
                    ],
                },
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_LEVEL_UPDATED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseAttainment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, courseId, levelNo, term, attainmentId } = query;
        let attainmentData = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                    type: 'course',
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    levelNo,
                    term,
                },
                {
                    outcomes: 1,
                    regulationYear: 1,
                    regulationName: 1,
                    evaluationPlan: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    curriculumName: 1,
                    attainmentLevel: 1,
                    levelColors: 1,
                },
            )
            .lean();
        if (!attainmentData) {
            attainmentData = await attainmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isDeleted: false,
                        isActive: true,
                        type: 'program',
                        _program_id: convertToMongoObjectId(programId),
                        _id: convertToMongoObjectId(attainmentId),
                    },
                    {
                        _id: 0,
                        createdAt: 0,
                        updatedAt: 0,
                        __v: 0,
                        type: 0,
                    },
                )
                .lean();
            await attainmentManagementSchema.create({
                ...attainmentData,
                type: 'course',
                _course_id: convertToMongoObjectId(courseId),
                levelNo,
                term,
            });
            attainmentData = await attainmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isDeleted: false,
                        isActive: true,
                        type: 'course',
                        _program_id: convertToMongoObjectId(programId),
                        _course_id: convertToMongoObjectId(courseId),
                        levelNo,
                        term,
                    },
                    {
                        outcomes: 1,
                        regulationYear: 1,
                        regulationName: 1,
                        evaluationPlan: 1,
                        _program_id: 1,
                        _curriculum_id: 1,
                        curriculumName: 1,
                        attainmentLevel: 1,
                        levelColors: 1,
                    },
                )
                .lean();
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: attainmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getSettingAssessmentNode,
    getAttainmentList,
    getAttainmentPrerequisite,
    addAttainment,
    editAttainment,
    removeAttainment,
    getAttainment,
    addAttainmentNode,
    editAttainmentNode,
    deleteAttainmentNode,
    attainmentLevel,
    getCourseAttainment,
};
