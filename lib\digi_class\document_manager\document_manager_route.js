const express = require('express');
const multer = require('multer');
const route = express.Router();
const {
    getDocuments,
    getCourseDocuments,
    getCourseSessionDocuments,
    getDocumentsBySession,
    createDocument,
    updateDocument,
    deleteDocument,
    selectSessions,
    zoomUpload,
    getDocumentById,
    getCourseAdminDocuments,
    getSessionDocuments,
    getCourseDocumentWithFilter,
    courseParamsUpdateDocuments,
    checkActivityOrDocument,
    getUserRecentDocuments,
} = require('./document_manager_controller');
const { response_function, responseFunctionWithRequest } = require('../../utility/common');
const { multipleFileUpload } = require('../../utility/file_upload');

const { validate } = require('../../../middleware/validation');
const {
    getDocumentSchema: { params: getDocumentParamsSchema, query: getDocumentQuerySchema },
    getCourseDocumentSchema: {
        params: getCourseDocumentParamsSchema,
        query: getCourseDocumentQuerySchema,
    },
    getCourseSessionDocumentSchema: {
        params: getCourseSessionDocumentParamsSchema,
        query: getCourseSessionDocumentQuerySchema,
    },
    getDocumentBySessionSchema: {
        body: getDocumentSessionBodySchema,
        params: getDocumentSessionParamSchema,
    },
    createDocumentSchema: { body: createDocumentBodySchema, params: createDocumentParamsSchema },
    updateDocumentSchema: { body: updateDocumentBodySchema, params: updateDocumentParamsSchema },
    deleteDocumentSchema: { params: deleteDocumentParamsSchema },
    selectSessionsSchema: { params: selectSessionsParamsSchema, query: selectSessionsQuerySchema },
    getCourseSessionDocumentWithFilterSchema: {
        params: getCourseSessionDocumentWithFilterParamsSchema,
        query: getCourseSessionDocumentWithFilterQuerySchema,
    },
    getCourseDocumentWithFilterSchema: {
        params: getCourseDocumentWithFilterParamsSchema,
        query: getCourseDocumentWithFilterQuerySchema,
    },
    activityorDocumentAccessSchema: {
        params: activityorDocumentAccessParamsSchema,
        body: activityorDocumentAccessBodySchema,
    },
    // getCourseSessionDocumentWithFilterSchema,
} = require('./document_validate_schema');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get('/zoom-upload', [userPolicyAuthentication([])], zoomUpload);
// Update course params in Document List
route.get('/courseParamsUpdate', [userPolicyAuthentication([])], courseParamsUpdateDocuments);
route.get(
    '/courseAdmin/:userId/:courseId',
    [userPolicyAuthentication([])],
    validate([
        { schema: getCourseDocumentParamsSchema, property: 'params' },
        { schema: getCourseDocumentQuerySchema, property: 'query' },
    ]),
    getCourseAdminDocuments,
);
route.get(
    '/getById/:_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getDocumentById,
);

// Get Course wise Document List
route.get(
    '/getCourseDocumentWithFilter/:userId/:courseId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    validate([
        { schema: getCourseDocumentWithFilterParamsSchema, property: 'params' },
        { schema: getCourseDocumentWithFilterQuerySchema, property: 'query' },
    ]),
    getCourseDocumentWithFilter,
);

route.get(
    '/userDocuments/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getUserRecentDocuments,
);

route.get(
    '/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    validate([
        { schema: getDocumentParamsSchema, property: 'params' },
        { schema: getDocumentQuerySchema, property: 'query' },
    ]),
    getDocuments,
);
route.get(
    '/select-sessions/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: selectSessionsParamsSchema, property: 'params' },
        { schema: selectSessionsQuerySchema, property: 'query' },
    ]),
    selectSessions,
);
route.get(
    '/:userId/:courseId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    validate([
        { schema: getCourseDocumentParamsSchema, property: 'params' },
        { schema: getCourseDocumentQuerySchema, property: 'query' },
    ]),
    getCourseDocuments,
);
route.get(
    '/courseAdmin/:userId/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([
        { schema: getCourseDocumentParamsSchema, property: 'params' },
        { schema: getCourseDocumentQuerySchema, property: 'query' },
    ]),
    getCourseDocuments,
);

route.get(
    '/:userId/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([
        { schema: getCourseSessionDocumentParamsSchema, property: 'params' },
        { schema: getCourseSessionDocumentQuerySchema, property: 'query' },
    ]),
    getCourseSessionDocuments,
);
route.post(
    '/chat-api/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    validate([
        { schema: activityorDocumentAccessParamsSchema, property: 'params' },
        { schema: activityorDocumentAccessBodySchema, property: 'body' },
    ]),
    checkActivityOrDocument,
);
route.post(
    '/session-documents/:userId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: getDocumentSessionBodySchema, property: 'body' },
        { schema: getDocumentSessionParamSchema, property: 'params' },
    ]),
    getDocumentsBySession,
);
route.post(
    '/:userId',
    (req, res, next) => {
        multipleFileUpload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
            }
            if (err) {
                console.log('AWS error', err);
                return res
                    .status(500)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            500,
                            false,
                            'Please change file format and upload',
                            err.toString(),
                        ),
                    );
            }
            next();
        });
    },
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: createDocumentBodySchema, property: 'body' },
        { schema: createDocumentParamsSchema, property: 'params' },
    ]),
    createDocument,
);
route.put(
    '/:id/:userId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([
        { schema: updateDocumentBodySchema, property: 'body' },
        { schema: updateDocumentParamsSchema, property: 'params' },
    ]),
    updateDocument,
);
route.delete(
    '/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: deleteDocumentParamsSchema, property: 'params' }]),
    deleteDocument,
);

// Get Session wise Document List
route.get(
    '/getSessionDocuments/:userId/:courseId/:sessionId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    validate([
        { schema: getCourseSessionDocumentWithFilterParamsSchema, property: 'params' },
        { schema: getCourseSessionDocumentWithFilterQuerySchema, property: 'query' },
    ]),
    getSessionDocuments,
);

module.exports = route;
