const express = require('express');
const route = express.Router();
const program_calendar_event = require('./program_calendar_event_controller');
const validator = require('./program_calendar_event_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get('/:id', validator.program_calendar_event_id, program_calendar_event.list_id);
route.get(
    '/calendar_event/:id',
    validator.program_calendar_event_id,
    program_calendar_event.calendar_event,
);
route.post('/list', program_calendar_event.list_values);
// route.get('/list_event_date_filter/:id/:start/:end'/* , validator.program_calendar_event_id */, program_calendar_event.list_event_date_filter);
route.get(
    '/course_event_date_filter/:id/:level_no/:course/:start/:end' /* , validator.program_calendar_event_id */,
    program_calendar_event.course_event_date_filter,
);
route.get(
    '/list_event_date_filter/:id/:level_no/:course/:start/:end' /* , validator.program_calendar_event_id */,
    [userPolicyAuthentication(['program_calendar:dashboard:course:view'])],
    program_calendar_event.year_event_date_filter,
);
route.get('/', program_calendar_event.list);
route.post('/', validator.program_calendar_event, program_calendar_event.insert);
route.post(
    '/interim_event_insert',
    validator.program_calendar_batch_event,
    program_calendar_event.interim_event_insert,
);
route.post(
    '/course_event_insert',
    [
        userPolicyAuthentication([
            'program_calendar:dashboard:course:view',
            'program_calendar:dashboard:course:edit',
            'program_calendar:dashboard:add_course',
        ]),
    ],
    validator.program_calendar_course_event,
    program_calendar_event.course_event_insert,
);
route.put(
    '/course_event_edit',
    [
        userPolicyAuthentication([
            'program_calendar:dashboard:course:view',
            'program_calendar:dashboard:course:edit',
        ]),
    ],
    validator.course_event_edit,
    program_calendar_event.course_event_edit,
);
route.put(
    '/rotation_course_event_edit',
    [
        userPolicyAuthentication([
            'program_calendar:dashboard:course:view',
            'program_calendar:dashboard:course:edit',
        ]),
    ],
    validator.rotation_course_event_edit,
    program_calendar_event.rotation_course_event_edit,
);
route.put('/', validator.program_calendar_event_update, program_calendar_event.update);
route.delete('/', validator.program_calendar_event_remove, program_calendar_event.delete_event);
route.delete(
    '/course_event_delete',
    validator.course_event_delete,
    program_calendar_event.course_event_delete,
);
route.delete(
    '/rotation_course_event_delete',
    validator.rotation_course_event_delete,
    program_calendar_event.rotation_course_event_delete,
);
route.post('/event_sync', validator.event_sync, program_calendar_event.event_sync);
route.post(
    '/interim_event_sync',
    validator.interim_event_sync,
    program_calendar_event.interim_event_sync,
);
route.post('/copy_event' /* , validator.copy_event */, program_calendar_event.copy_event);

module.exports = route;
