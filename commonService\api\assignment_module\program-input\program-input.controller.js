const { getProgramList, getRoleModuleList } = require('./program-input.service');
const { getUserRoleProgramList } = require('../../../../lib/utility/utility.service');
const { convertToMongoObjectId } = require('../../../utility/common');
const { DIGI_PROGRAM, DIGI_COURSE } = require('../../../../lib/utility/constants');
const courseSchema = require('mongoose').model(DIGI_COURSE);
const programSchema = require('mongoose').model(DIGI_PROGRAM);
/* const getInstitutePrograms = async ({ query = {}, params = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { staffId } = query;
        const programLists = await getProgramList({ _institution_id, staffId });
        if (!programLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            data: programLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
}; */
const constructProgramData = ({ programLists, isAdmin }) => {
    const programList = [];
    const uniqueProgramIds = new Map();
    const updateProgramList = (programId, programName) => {
        if (!uniqueProgramIds.get(programId)) {
            programList.push({ programId, programName, isAdmin });
            uniqueProgramIds.set(programId, true);
        }
    };
    for (const programElement of programLists) {
        if (isAdmin) {
            const { programId, programName } = programElement;
            updateProgramList(programId, programName);
            continue;
        }
        const { _id: programId, name: programName } = programElement._program_id;
        updateProgramList(programId, programName);
    }
    return programList;
};
const getProgramsFromProgramIds = async ({ userProgramIds }) => {
    const programLists = await programSchema
        .find(
            {
                _id: {
                    $in: userProgramIds.map((userProgramId) =>
                        convertToMongoObjectId(userProgramId),
                    ),
                },
            },
            { programId: '$_id', programName: '$name', _id: 0 },
        )
        .lean();
    return {
        statusCode: 200,
        data: constructProgramData({ programLists, isAdmin: true }),
    };
};
const getProgramsFromCourseIds = async ({ userCourseIds }) => {
    const programLists = await courseSchema
        .find(
            {
                _id: {
                    $in: userCourseIds.map((courseElement) =>
                        convertToMongoObjectId(courseElement._course_id),
                    ),
                },
            },
            { _program_id: 1, _id: 0 },
        )
        .populate({ path: '_program_id', select: { name: 1 } });
    return {
        statusCode: 200,
        data: constructProgramData({ programLists, isAdmin: false }),
    };
};
const getInstitutePrograms = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { staffId, roleId } = query;
        const { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin } =
            await getUserRoleProgramList({
                _institution_id,
                user_id: staffId,
                role_id: roleId,
                institutionCalendarId: [_institution_calendar_id],
            });
        if (isProgramAdmin) return await getProgramsFromProgramIds({ userProgramIds });
        if (isCourseAdmin) return await getProgramsFromCourseIds({ userCourseIds });
        return {
            statusCode: 404,
            message: 'NOT_AN_ADMIN',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getRoleBasedLists = async ({ query = {}, params = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { staffId } = query;
        const moduleLists = await getRoleModuleList({ _institution_id, staffId });
        if (!moduleLists)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            data: moduleLists,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    getInstitutePrograms,
    getRoleBasedLists,
};
