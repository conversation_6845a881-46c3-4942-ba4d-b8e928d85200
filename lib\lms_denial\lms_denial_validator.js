const Joi = require('joi');

const objectId = Joi.string().alphanum().length(24).required();

exports.getUserProgramListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string().alphanum().length(24).required(),
            _user_id: objectId,
            _institution_calendar_id: objectId,
        }).unknown(true),
        query: Joi.object({
            roleId: Joi.string().required(),
            _institution_calendar_id: objectId,
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getProgramLevelsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _institution_calendar_id: objectId,
        }).unknown(true),
        query: Joi.object({
            programId: objectId,
            _institution_calendar_id: objectId,
            roleId: Joi.string().alphanum().length(24).optional(),
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.getCoursesFromProgramLevelValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _institution_calendar_id: objectId,
        }).unknown(true),
        query: Joi.object({
            programId: objectId,
            year: Joi.string().required(),
            level_no: Joi.string().required(),
            term: Joi.string().required(),
            pageNo: Joi.number().required(),
            limit: Joi.number().required(),
            searchKey: Joi.string().optional(),
            rotation: Joi.optional(),
            rotationCount: Joi.optional(), //.when('rotation', { is: 'yes', then: Joi.required() }),
            _institution_calendar_id: objectId,
            roleId: Joi.string().alphanum().length(24).optional(),
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.getStudentsStatsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _institution_calendar_id: objectId,
        }).unknown(true),
        query: Joi.object({
            programId: objectId,
            year: Joi.string().required(),
            level_no: Joi.string().required(),
            term: Joi.string().required(),
            pageNo: Joi.number().required(),
            limit: Joi.number().required(),
            searchKey: Joi.string().optional(),
            courseId: objectId,
            warningStatus: Joi.array().optional(),
            studentIds: Joi.array().optional(),
            _institution_calendar_id: objectId,
            rotation: Joi.optional(),
            rotationCount: Joi.number().when('rotation', { is: 'yes', then: Joi.required() }),
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.updateDenialPercentageValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _institution_calendar_id: objectId,
            _user_id: objectId,
        }).unknown(true),
        body: Joi.object({
            denialCondition: Joi.string().required().valid('cumulative', 'individual'),
            denialManagement: Joi.array().items(
                Joi.object().keys({
                    programId: objectId,
                    courseId: objectId,
                    term: Joi.string().required(),
                    yearNo: Joi.string().required(),
                    levelNo: Joi.string().required(),
                    rotation: Joi.string().required(),
                    rotationCount: Joi.number().when('rotation', {
                        is: 'yes',
                        then: Joi.required(),
                    }),
                    studentId: Joi.string().alphanum().length(24),
                    typeWiseUpdate: Joi.string(),
                    absencePercentage: Joi.number().required(),
                    categoryWisePercentage: Joi.array().items(
                        Joi.object()
                            .keys({
                                categoryId: objectId.optional(),
                                categoryName: Joi.string().required(),
                                percentage: Joi.number().required(),
                            })
                            .unknown(true),
                    ),
                }),
            ),
            _institution_calendar_id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.listDenialDataValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_calendar_id: objectId,
            _institution_id: objectId,
        }).unknown(true),
        body: Joi.object({
            courseWise: Joi.object().keys({
                programId: objectId,
                courseId: objectId,
                term: Joi.string().required(),
                yearNo: Joi.string().required(),
                levelNo: Joi.string().required(),
                studentIds: Joi.array().items(Joi.string()).required(),
            }),
            studentWise: Joi.object().keys({
                programId: objectId,
                studentId: objectId,
                term: Joi.string().required(),
                yearNo: Joi.string().required(),
                levelNo: Joi.string().required(),
                courseIds: Joi.array().items(Joi.string()).required(),
            }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.denialManagementListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _institution_calendar_id: objectId,
        }).unknown(true),
        query: Joi.object({
            roleId: objectId,
            state: Joi.string().valid('fresh', 'regular').required(),
            _institution_calendar_id: objectId,
            pageNo: Joi.number().required(),
            limit: Joi.number().required(),
        }).unknown(true),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.studentsListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_calendar_id: objectId,
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            programId: objectId,
            year: Joi.string().required(),
            level_no: Joi.string().required(),
            term: Joi.string().required(),
            courseId: objectId,
            rotation: Joi.optional(),
            _institution_calendar_id: objectId,
            rotationCount: Joi.number().when('rotation', { is: 'yes', then: Joi.required() }),
        }),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};

exports.checkDenialAccessValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
            _user_id: objectId,
            role_id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
