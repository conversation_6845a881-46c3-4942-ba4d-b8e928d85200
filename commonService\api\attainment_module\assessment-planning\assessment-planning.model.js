const mongoose = require('mongoose');
const { ASSESSMENT_PLANNING } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const assessmentPlanning = new Schema({
    _institution_calendar_id: {
        type: ObjectId,
    },
    name: String,
    isActive: {
        type: Boolean,
        default: false,
    },
    occurring: {
        type: String,
        enum: ['program', 'course'],
    },
    courses: [
        {
            year: String,
            level: String,
            rotationNo: Number,
            courseId: ObjectId,
        },
    ],
    totalMark: {
        type: Number,
    },
});

const assessmentType = new Schema({
    name: String,
    isActive: {
        type: Boolean,
        default: false,
    },
    isDefault: {
        type: Boolean,
        default: false,
    },
    planning: [assessmentPlanning],
});

const assessmentHierarchy = new Schema({
    typeName: { type: String },
    subTypes: [
        {
            typeName: { type: String },
            isDefault: { type: Boolean, default: true },
            isActive: { type: Boolean, default: true },
            assessmentTypes: [assessmentType],
        },
    ],
});

const assessmentManagementSchemas = new Schema(
    {
        types: [assessmentHierarchy],
        _institution_id: {
            type: ObjectId,
        },
        _institution_calendar_id: {
            type: ObjectId,
        },
        _program_id: {
            type: ObjectId,
        },
        term: {
            type: String,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSESSMENT_PLANNING, assessmentManagementSchemas);
