const { Schema, model } = require('mongoose');

const { PORTFOLIO } = require('../../common/utils/constants');
const { portfolioSchema } = require('./portfolio.schema');

const schema = new Schema(
    {
        ...portfolioSchema,
        settings: {
            equivalenceMark: { type: Number, default: 0 },
            overAllPassMark: { type: Number, default: 0 },
            minComponentsForInferencePass: { type: Number, default: 0 },
            overAllPassEnabled: { type: Boolean, default: false },
            isComponentWisePassEnabled: { type: Boolean, default: false },
            completionRules: [
                {
                    overall: { type: String },
                    component: { type: String },
                },
            ],
        },
    },
    { timestamps: true },
);

schema.index({ institutionCalendarId: 1, programId: 1, courseId: 1 });

module.exports = model(PORTFOLIO, schema);
