const { getPresignedUrlsForInstitutes } = require('./institution.util');

module.exports = {
    institution: async (docs, programWithInstitutions) => {
        const formattedInstitutes = [];
        for (const doc of docs) {
            const formattedInstitute = await getPresignedUrlsForInstitutes(doc);
            const institutePrograms = programWithInstitutions.filter(
                (programWithInstitutionElement) =>
                    programWithInstitutionElement._institution_id.toString() === doc._id.toString(),
            );
            formattedInstitute.programCount = institutePrograms.length;
            formattedInstitutes.push(formattedInstitute);
        }
        return formattedInstitutes;
    },
};
