const StudentRoleAllotment = require("./student_role_allotment_model");
const { format } = require("./student_role_allotment_format");

module.exports.create = async (req, res, next) => {
  new StudentRoleAllotment(format(req.body)).save(function (err) {
    if (err) return next(err);
    res.send("Created successfully");
  });
};

module.exports.read = async (req, res) => {
  StudentRoleAllotment.aggregate(
    [
      {
        $group: {
          _id: {
            academic_year: "$academic_year",
            program_id: "$program_id",
          },
          roles: {
            $push: "$$ROOT",
          },
        },
      },
      {
        $group: {
          _id: "$_id.academic_year",
          programs: {
            $push: {
              program_id: "$_id.program_id",
              roles: "$roles",
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          academic_year: "$_id",
          programs: 1,
        },
      },
      { $sort: { academic_year: 1 } },
    ],
    (err, role) => {
      if (err) res.send("Error on getting role allotment");
      res.send(role);
    }
  );
};

module.exports.getFilter = async (req, res) => {
  const filter = {};
  filter[req.params.filter] = req.params.value;
  StudentRoleAllotment.find(filter, (err, role) => {
    if (!err) res.send(role);
    else res.send("Error on getting role allotment");
  });
};

module.exports.update = async (req, res) => {
  StudentRoleAllotment.findByIdAndUpdate(
    req.params._id,
    { $set: req.body },
    function (err, old) {
      if (err) return next(err);
      res.send({ old, msg: "Updated successfully." });
    }
  );
};

module.exports.delete = async (req, res) => {
  StudentRoleAllotment.findByIdAndRemove(req.params._id, function (err) {
    if (err) return next(err);
    res.send("Deleted successfully!");
  });
};
