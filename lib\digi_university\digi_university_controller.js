const constant = require('../utility/constants');
const digi_university = require('mongoose').model(constant.DIGI_UNIVERSITY);
const country_state_city = require('mongoose').model(constant.COUNTRY_STATE_CITY);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = require('mongodb').ObjectID;

async function insert(req, res) {
    try {

        /* let institution_check = await base_control.get(institution, { _id: req.headers._institution_id, 'isDeleted': false });
        if (!institution_check.status) return res.status(410).send(common_files.response_function(res, 500, false, "Institution not found", 'Institution not found')); */
        let obj = {
            institute_name: req.body.institute_name,
            institute_type: req.body.institute_type,
            no_of_college: req.body.no_of_college,
            logo: req.body.logo,
            address_details: {
                address: req.body.address_details.address,
                country: req.body.address_details.country,
                state: req.body.address_details.state,
                district: req.body.address_details.district,
                city: req.body.address_details.city,
                zipcode: req.body.address_details.zipcode,
            },
            _user_id: req.body._user_id
        };
        let doc = await base_control.insert(digi_university, obj);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, "University added successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, "Unable to add University", []));
    }
    catch (error) {
        console.log(error)
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
async function update(req, res) {
    try {

        /* let institution_check = await base_control.get(institution, { _id: req.headers._institution_id, 'isDeleted': false });
        if (!institution_check.status) return res.status(410).send(common_files.response_function(res, 500, false, "Institution not found", 'Institution not found')); */
        // let obj = {
        //     institute_name: req.body.institute_name,
        //     no_of_college: req.body.no_of_college,
        //     // logo: req.body.logo,
        //     address_details: {
        //         address: req.body.address_details.address,
        //         country: req.body.address_details.country,
        //         state: req.body.address_details.state,
        //         district: req.body.address_details.district,
        //         city: req.body.address_details.city,
        //         zipcode: req.body.address_details.zipcode,
        //     }
        // };
        // if (req.body.logo) obj[logo] = req.body.logo;
        let query = { _id: ObjectId(req.params.id) }
        let doc = await base_control.update(digi_university, query, req.body);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, "University updated successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, "Unable to update University", []));
    }
    catch (error) {
        console.log(error)
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
async function list_id(req, res) {
    try {
        let query = { _id: ObjectId(req.params.id) };
        let doc = await base_control.get(digi_university, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        return res.status(200).send(common_files.response_function(res, 200, true, "University Details", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
}
async function get_university_details_by_user_ID(req, res) {
    try {
        let query = { _user_id: ObjectId(req.params.user_id) };
        let doc = await base_control.get(digi_university, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        return res.status(200).send(common_files.response_function(res, 200, true, "University Details By User ID", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
}
async function country_state_city_list(req, res) {
    let country_list = await base_control.get_list(country_state_city, { isDeleted: false }, {})
    let country_state_city_data = {
        "country": [
            {
                "name": "Saudi Arabia",
                "state": [
                    {
                        "name": "Makkah",
                        "city": [
                            {
                                "name": "Al Qunfudhah"
                            },
                            {
                                "name": "At Taif"
                            },
                            {
                                "name": "Jeddah"
                            },
                            {
                                "name": "Makkah"
                            },

                        ],
                        "district": [
                            { "name": "Al Safa" },
                            { "name": "Al Faisaliyah" }
                        ]
                    },
                    {
                        "name": "Ar Riyad",
                        "city": [
                            {
                                "name": "Al Hillah"
                            },
                            {
                                "name": "Al Kharj"
                            },
                            {
                                "name": "Al Quwayiyah"
                            },
                            {
                                "name": "As Sulayyil"
                            },
                            {
                                "name": "Riyadh"
                            }

                        ],
                        "district": [
                            { "name": "Anak" },
                            { "name": "Awwad" }
                        ]
                    }
                ]
            }
        ]
    };
    return res.status(200).send(common_files.response_function(res, 200, true, "University Details", /* country_state_city_data */country_list.data));
}



module.exports = {
    insert,
    list_id,
    update,
    country_state_city_list,
    get_university_details_by_user_ID

}