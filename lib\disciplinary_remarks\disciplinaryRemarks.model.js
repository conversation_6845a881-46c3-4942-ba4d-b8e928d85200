const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    INSTITUTION_CALENDAR,
    COURSE_SCHEDULE,
    USER,
    GLOBAL_SESSION_TARDIS,
    DISCIPLINARY_REMARKS,
    DISCIPLINARY_REMARKS_TYPE,
} = require('../utility/constants');

const mailDetailsSubSchema = new Schema({
    isSent: {
        type: Boolean,
        default: false,
    },
    sentDetails: [
        {
            sentBy: {
                type: ObjectId,
                ref: USER,
            },
            sentOn: {
                type: Date,
                default: Date.now(),
            },
        },
    ],
    _id: false,
});
const disciplinaryRemarksSchema = Schema(
    {
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        scheduleId: {
            type: ObjectId,
            ref: COURSE_SCHEDULE,
        },
        studentId: {
            type: ObjectId,
            ref: USER,
        },
        reportedBy: {
            type: ObjectId,
            ref: USER,
        },
        tardisId: {
            type: ObjectId,
            ref: GLOBAL_SESSION_TARDIS,
        },
        comment: {
            type: String,
        },
        attachments: {
            type: Array,
        },
        type: {
            type: String,
            enums: [
                DISCIPLINARY_REMARKS_TYPE.SCHEDULE_LEVEL,
                DISCIPLINARY_REMARKS_TYPE.INSTITUTION_LEVEL,
            ],
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        mailData: mailDetailsSubSchema,
    },
    { timestamps: true },
);

module.exports = model(DISCIPLINARY_REMARKS, disciplinaryRemarksSchema);
