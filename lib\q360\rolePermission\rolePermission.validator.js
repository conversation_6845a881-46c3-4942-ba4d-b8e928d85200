const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const {
    TAG_LEVEL,
    QAPC_GROUPED_BY_CATEGORY,
    QAPC_GROUPED_BY_PROGRAM,
    ALL,
    EVERY,
} = require('../../utility/constants');

exports.getFormCourseAndGroupValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
exports.createNewRoleValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                roleName: Joi.string()
                    .optional()
                    .error(() => {
                        return 'ROLE_NAME_MUST_BERING';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.updateRoleNameValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                qapcRoleId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'QAPC_ROLE_ID_REQUIRED';
                    }),
                roleName: Joi.string()
                    .required()
                    .optional()
                    .error(() => {
                        return 'ROLE_NAME_MUST_BERING';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getLevelCategoryValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                hierarchy: Joi.string()
                    .valid(QAPC_GROUPED_BY_CATEGORY, QAPC_GROUPED_BY_PROGRAM)
                    .required()
                    .error(() => {
                        return 'HIERARCHY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:QAPC_GROUPED_BY_CATEGORY, QAPC_GROUPED_BY_PROGRAM';
                    }),
                level: Joi.string()
                    .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                    .required()
                    .error(() => {
                        return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE,TAG_LEVEL.INSTITUTION';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
exports.getFormCourseAndGroupValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
                level: Joi.string()
                    .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                    .required()
                    .error(() => {
                        return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE,TAG_LEVEL.INSTITUTION';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.formWiseListProgramValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_FORM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getProgramGroupValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                categoryFormCourseIds: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_COURSE_IDS_MUST_BE_OBJECT';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.qapcSubModulesValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                categoryFormCourseIds: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_COURSE_ID_REQUIRES';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.formApproverLevelValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                categoryFormIds: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.formProgramListValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                programId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                categoryFormIds: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
exports.qapcRoleListValidator = Joi.object().keys({
    Headers: Joi.object()
        .keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        })
        .unknown(true),
});
exports.assignedUserValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object()
            .keys({
                qapcRoleId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'QAPC_ROLE_ID_REQUIRED';
                    }),
                categoryFormCourseId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'QAPC_ROLE_ID_REQUIRED';
                    }),
                hierarchy: Joi.string()
                    .valid(QAPC_GROUPED_BY_CATEGORY, QAPC_GROUPED_BY_PROGRAM)
                    .required()
                    .error(() => {
                        return 'HIERARCHY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:,QAPC_GROUPED_BY_CATEGORY,QAPC_GROUPED_BY_PROGRAM';
                    }),
                levelName: Joi.string()
                    .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                    .required()
                    .error(() => {
                        return 'LEVEL_NAME_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE,TAG_LEVEL.INSTITUTION';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
exports.saveRolePermissionValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                cfpcPermissions: Joi.array()
                    .items({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        isDeleted: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        userId: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'USER_ID_REQUIRED';
                            }),
                        categoryFormCourseId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'CURRICULUM_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        formCourseGroupId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'Form_COURSE_GROUP_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        selectType: Joi.string()
                            .optional()
                            .error(() => {
                                return 'SELECT_TYPE_MUST_BE_STRING';
                            }),
                        selectAll: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        selectedAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        subModuleId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'SUBMODULE_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        assignedInstitutionId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'SUBMODULE_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        subModuleName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'SUB_MODULE_MUST_BE_STRING';
                            }),
                        approverLevelIndex: Joi.number()
                            .optional()
                            .error(() => {
                                return 'APPROVER_LEVEL_INDEX_MUST_BE_STRING';
                            }),
                        actionId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ACTION_ID_REQUIRED';
                            }),
                        qapcRoleId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'QAPC_ROLE_ID_REQUIRED';
                            }),
                        roleId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ROLE_ID_REQUIRED';
                            }),
                        assignType: Joi.string()
                            .optional()
                            .error(() => {
                                return 'ASSIGN_TYPE_MUST_BE_STRING';
                            }),
                        levelName: Joi.string()
                            .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                            .required()
                            .error(() => {
                                return 'LEVEL_NAME_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE,TAG_LEVEL.INSTITUTION';
                            }),
                        academicYear: Joi.string()
                            .valid(ALL, EVERY)
                            .optional()
                            .error(() => {
                                return 'ACADEMIC_YEAR_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, EVERY';
                            }),
                        institutionCalendarIds: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'INSTITUSTION_CALENDAR_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                    })
                    .optional(),
                pccfPermissions: Joi.array()
                    .items({
                        programId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        curriculamId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'CURRICULAM_ID_REQUIRED';
                            }),
                        year: Joi.string()
                            .optional()
                            .error(() => {
                                return 'YEAR_MUST_BE_STRING';
                            }),
                        courseId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        isDeleted: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        userId: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'USER_ID_REQUIRED';
                            }),
                        categoryFormCourseId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'CURRICULUM_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        formCourseGroupId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'Form_COURSE_GROUP_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        selectType: Joi.string()
                            .optional()
                            .error(() => {
                                return 'SELECT_TYPE_MUST_BE_STRING';
                            }),
                        selectAll: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        selectedAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        subModuleId: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'SUBMODULE_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                        subModuleName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'SUB_MODULE_MUST_BE_STRING';
                            }),
                        approverLevelIndex: Joi.number()
                            .optional()
                            .error(() => {
                                return 'APPROVER_LEVEL_INDEX_MUST_BE_STRING';
                            }),
                        actionId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ACTION_ID_REQUIRED';
                            }),
                        qapcRoleId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'QAPC_ROLE_ID_REQUIRED';
                            }),
                        roleId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ROLE_ID_REQUIRED';
                            }),
                        assignType: Joi.string()
                            .optional()
                            .error(() => {
                                return 'ASSIGN_TYPE_MUST_BE_STRING';
                            }),
                        levelName: Joi.string()
                            .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                            .required()
                            .error(() => {
                                return 'LEVEL_NAME_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE,TAG_LEVEL.INSTITUTION';
                            }),
                        academicYear: Joi.string()
                            .valid(ALL, EVERY)
                            .optional()
                            .error(() => {
                                return 'ACADEMIC_YEAR_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, EVERY';
                            }),
                        institutionCalendarIds: Joi.array()
                            .items(
                                Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .optional()
                                    .error(() => {
                                        return 'INSTITUSTION_CALENDAR_ID_REQUIRED';
                                    }),
                            )
                            .optional(),
                    })
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.updateRoleLevel = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                roleLevels: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        levels: Joi.array()
                            .items(Joi.string())
                            .optional()
                            .error(() => {
                                return 'LEVELS_MUST_BE_STRING';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);
