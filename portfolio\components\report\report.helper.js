const { roundToTwoDecimalPlaces } = require('../../common/utils/common.util');
const { PASS, FAIL } = require('../../common/utils/enums');

const calculateProportionalScore = ({ totalMarks = 0, awardedMarks = 0, newTotal = 0 }) => {
    const ratio = awardedMarks / totalMarks;
    const newScore = ratio * newTotal;
    return newScore;
};

const standardGrades = [
    { name: 'A+', from: 95, to: 100, isFail: false },
    { name: 'A', from: 90, to: 94, isFail: false },
    { name: 'B+', from: 85, to: 89, isFail: false },
    { name: 'B', from: 80, to: 84, isFail: false },
    { name: 'C+', from: 75, to: 79, isFail: false },
    { name: 'C', from: 70, to: 74, isFail: false },
    { name: 'D+', from: 65, to: 69, isFail: false },
    { name: 'D', from: 60, to: 64, isFail: false },
    { name: 'F', from: 0, to: 59, isFail: true },
];

const getGradeBasedOnPercentage = ({ percentage = 0, grades = [] }) =>
    grades
        .slice() // copy so we don’t sort original
        .sort((a, b) => b.from - a.from) // highest “from” first
        .find((g) => percentage >= g.from);

const calculatePercentage = ({ awardedMarks = 0, totalMarks = 0 }) => {
    return roundToTwoDecimalPlaces((awardedMarks / totalMarks) * 100);
};

const getCompletionStatus = ({
    componentWisePass = false,
    overAllPass = false,
    completionRules = [],
}) => {
    const current = {
        component: componentWisePass ? PASS : FAIL,
        overall: overAllPass ? PASS : FAIL,
    };

    const isPass = completionRules.some(
        (rule) => rule.component === current.component && rule.overall === current.overall,
    );

    return isPass ? PASS : FAIL;
};

module.exports = {
    calculateProportionalScore,
    standardGrades,
    getGradeBasedOnPercentage,
    calculatePercentage,
    getCompletionStatus,
};
