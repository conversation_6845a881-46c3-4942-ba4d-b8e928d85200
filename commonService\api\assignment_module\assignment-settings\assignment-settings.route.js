const express = require('express');
const route = express.Router();
const assignmentSettings = require('./assignment-settings.controller');
const { uploadDocument, singleDocumentUpload } = require('./assignment-settings.util');
const catchAsync = require('../../../utility/catch-async');
const {
    createProgramSettingValidator,
    getProgramSettingValidator,
    createCourseSettingValidator,
    getCourseSettingValidator,
    getSubjectSettingValidator,
    createSubjectSettingValidator,
} = require('./assignment-settings.validator');
const { validate } = require('../../../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

route.post(
    '/program-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createProgramSettingValidator, property: 'body' }]),
    catchAsync(assignmentSettings.addProgramSettings),
);
route.get(
    '/program-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getProgramSettingValidator, property: 'query' }]),
    catchAsync(assignmentSettings.getProgramSettings),
);
route.post(
    '/course-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createCourseSettingValidator, property: 'body' }]),
    catchAsync(assignmentSettings.addCourseSettings),
);
route.get(
    '/course-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCourseSettingValidator, property: 'query' }]),
    catchAsync(assignmentSettings.getCourseSettings),
);
route.post(
    '/subject-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createSubjectSettingValidator, property: 'body' }]),
    catchAsync(assignmentSettings.addSubjectSettings),
);
route.get(
    '/subject-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getSubjectSettingValidator, property: 'query' }]),
    catchAsync(assignmentSettings.getSubjectSettings),
);
route.post('/upload-attachment', uploadDocument, catchAsync(assignmentSettings.documentUpload));
route.post(
    '/single-upload-attachment',
    singleDocumentUpload,
    catchAsync(assignmentSettings.singleDocumentUpload),
);
route.get('/active-image-url', catchAsync(assignmentSettings.activeImageUrl));
module.exports = route;
