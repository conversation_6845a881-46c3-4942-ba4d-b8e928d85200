const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const { validate } = require('../../../../middleware/validation');
const { paramIDValidator, createRubricsValidator } = require('./assignment-rubrics.validator');
const {
    createAssignmentRubrics,
    getAssignmentRubrics,
    deleteAssignmentRubrics,
    updateAssignmentRubrics,
    listAssignmentRubrics,
    listAllAssignmentRubrics,
} = require('./assignment-rubrics.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.post(
    '/create',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createRubricsValidator, property: 'body' }]),
    catchAsync(createAssignmentRubrics),
);
router.get(
    '/get/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: paramIDValidator, property: 'params' }]),
    catchAsync(getAssignmentRubrics),
);
router.put(
    '/update/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateAssignmentRubrics),
);
router.delete(
    '/delete/:id',
    validate([{ schema: paramIDValidator, property: 'params' }]),
    catchAsync(deleteAssignmentRubrics),
);
router.get(
    '/listAllAssignmentRubrics',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listAllAssignmentRubrics),
);
router.get(
    '/listAssignmentRubrics',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(listAssignmentRubrics),
);
module.exports = router;
