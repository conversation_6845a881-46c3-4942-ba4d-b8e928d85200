const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { INSTITUTION, EXTERNAL_DATA_SYNC } = require('../utility/constants');

const externalDataSyncSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        uniqueId: { type: String },
        sTerm: { type: String },
        level: { type: String },
        commonData: [{ type: Object }],
    },
    { timestamps: true },
);
module.exports = model(EXTERNAL_DATA_SYNC, externalDataSyncSchema);
