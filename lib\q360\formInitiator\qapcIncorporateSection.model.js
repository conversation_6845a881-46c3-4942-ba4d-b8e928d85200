const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_INITIATOR,
    QAPC_FORM_CATEGORY,
    QAPC_FORM_SETTING,
    QAPC_FORM_SETTING_COURSES,
    QAPC_INCORPORATE_SECTIONS,
    INSTITUTION_CALENDAR,
    INSTITUTION,
    DIGI_PROGRAM,
    QAPC_FORM_COURSES_GROUPS,
    TAG_LEVEL,
} = require('../../utility/constants');

const qapcIncorporateSectionSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        formInitiatorIds: [{ type: ObjectId, ref: QAPC_FORM_INITIATOR }],
        sectionName: { type: String },
        createdCalenderId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
        createdFormName: { type: String },
        sectionId: { type: ObjectId },
        institutionCalenderId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
        categoryId: { type: ObjectId, ref: QAPC_FORM_CATEGORY },
        categoryName: { type: String },
        categoryFor: { type: String },
        categoryFormId: { type: ObjectId, ref: QAPC_FORM_SETTING },
        formName: { type: String },
        term: { type: String },
        attemptTypeName: { type: String },
        categoryFormCourseIds: [{ type: ObjectId, ref: QAPC_FORM_SETTING_COURSES }],
        categoryFormGroupIds: [{ type: ObjectId, ref: QAPC_FORM_COURSES_GROUPS }],
        isLike: { type: Boolean, default: false },
        level: {
            type: String,
            enum: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION],
        },
        incorporateTo: [
            {
                sectionName: { type: String },
                sessionId: { type: ObjectId },
            },
        ],
        selectedProgram: [
            {
                programId: { type: ObjectId, ref: DIGI_PROGRAM },
                programName: { type: String },
            },
        ],
        selectedInstitution: [
            {
                assignedInstitutionId: { type: ObjectId, ref: INSTITUTION },
                institutionName: { type: String },
            },
        ],
        isDeleted: { type: Boolean, default: false },
        sectionTemplate: { type: Object },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_INCORPORATE_SECTIONS, qapcIncorporateSectionSchema);
