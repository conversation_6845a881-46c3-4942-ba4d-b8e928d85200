const divide = (divider, divisor, canThrowError = false) => {
    if (typeof divider !== 'number' || typeof divisor !== 'number') {
        if (canThrowError) {
            throw new Error('Both arguments must be numbers');
        }

        return 0;
    }
    if (divisor === 0) {
        if (canThrowError) {
            throw new Error('Division by zero is not allowed');
        }

        return 0;
    }

    return divider / divisor;
};

module.exports = {
    divide,
};
