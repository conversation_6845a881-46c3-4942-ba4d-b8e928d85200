// const middleware = exports;
const Joi = require('joi');
const common_files = require('../../utility/common');
const constant = require('../../utility/constants');

exports.notification_manager_calendar = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            calendar_name: Joi.string().required().error(error => {
                return error;
            }),
            calendar_type: Joi.string().valid(constant.PRIMARY, constant.SECONDARY).error(error => {
                return error;
            }),
            _primary_calendar_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _notification_manager_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            primary_calendar: Joi.string().valid(constant.GREGORIAN, constant.HIJRI).error(error => {
                return error;
            }),
            batch: Joi.string().min(1).max(10).trim().error(error => {
                return error;
            }),
            start_date: Joi.date().error(error => {
                return error;
            }),
            end_date: Joi.date().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.notification_manager_calendar_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}