const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.role_assign_to_staff_Validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _office_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('OFFICEID_REQUIRED');
                        }),
                    _role_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ROLEID_REQUIRED');
                        }),
                    _staff_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STAFFID_REQUIRED');
                        }),
                    office_name: Joi.string()
                        .min(3)
                        .max(100)
                        .required()
                        .error((error) => {
                            return req.t('OFFICENAME_REQUIRED');
                        }),
                    role_name: Joi.string()
                        .min(3)
                        .max(50)
                        .required()
                        .error((error) => {
                            return req.t('ROLENAME_REQUIRED');
                        }),
                    staff_name: Joi.string()
                        .min(3)
                        .max(60)
                        .required()
                        .error((error) => {
                            return req.t('STAFFNAME_REQUIRED');
                        }),
                    acting_staff_status: Joi.boolean().error((error) => {
                        return req.t('ACTIVESTAFFSTATUS_REQUIRED');
                    }),
                    _acting_staff_id: Joi.string().when('acting_staff_status', {
                        is: true,
                        then: Joi.string().alphanum().length(24).required(),
                    }),
                    acting_staff_name: Joi.string().when('acting_staff_status', {
                        is: true,
                        then: Joi.string().min(3).max(60).required(),
                    }),
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('PROGRAM_ID_REQUIRED');
                        }),
                    _dept_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('DEPTID_REQUIRED');
                        }),
                    _subject_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('SUBJECT_ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('COURSEID_REQUIRED');
                        }),
                    _infra_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('INFRAID_REQUIRED');
                        }),
                    program_name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return req.t('PROGRAMNAME_REQUIRED');
                        }),
                    dept_name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return req.t('DEPTNAME_REQUIRED');
                        }),
                    subject_name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return req.t('SUBJECTNAME_REQUIRED');
                        }),
                    course_name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return req.t('COURSENAME_REQUIRED');
                        }),
                    infra_name: Joi.string()
                        .min(3)
                        .max(60)
                        .error((error) => {
                            return req.t('INFRANAME_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.role_assign_to_staff_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.role_assign_to_staff_Validator_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _staff_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STAFFID_REQUIRED');
                        }),
                    staff_name: Joi.string()
                        .min(3)
                        .max(60)
                        .required()
                        .error((error) => {
                            return req.t('STAFFNAME_REQUIRED');
                        }),
                    acting_staff_status: Joi.boolean().error((error) => {
                        return req.t('ACTIVESTAFFSTATUS_REQUIRED');
                    }),
                    _acting_staff_id: Joi.string().when('acting_staff_status', {
                        is: true,
                        then: Joi.string().alphanum().length(24).required(),
                    }),
                    acting_staff_name: Joi.string().when('acting_staff_status', {
                        is: true,
                        then: Joi.string().min(3).max(60).required(),
                    }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
