const RubricModel = require('./rubric.model');
const UserModel = require('../../../lib/models/user');
const { DRAFT, PUBLISHED } = require('../../common/utils/enums');

const {
    addDocuments,
    updateDocument,
    getDocument,
    getDocuments,
} = require('../../base/base.helper');
const { checkIfTrue } = require('../../common/utils/common.util');

const rubricProjection = {
    name: 1,
    desc: 1,
    scoring: 1,
    parameters: 1,
    isGlobal: 1,
    createdBy: 1,
    updatedAt: 1,
    type: 1,
    status: 1,
    publishedDate: 1,
};

const createRubric = async ({
    name = '',
    desc = '',
    scoring = '',
    parameters = [],
    isGlobal = false,
    userId,
    status = DRAFT,
    type = {},
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    const result = await addDocuments({
        Model: RubricModel,
        data: {
            name,
            desc,
            scoring,
            parameters,
            isGlobal,
            createdBy: { name: user.name, id: userId },
            status,
            type,
            ...(status === PUBLISHED && { publishedDate: new Date() }),
        },
    });

    return result;
};

const getRubrics = async ({ rubricId, isGlobal }) => {
    if (rubricId) {
        const rubric = await getDocument({
            Model: RubricModel,
            query: { isDeleted: false, _id: rubricId },
            project: rubricProjection,
            canThrowError: false,
        });

        return rubric;
    }

    if (checkIfTrue(isGlobal)) {
        const rubrics = await RubricModel.find(
            { isDeleted: false, isGlobal: true },
            rubricProjection,
        ).lean();

        return rubrics;
    }

    const rubrics = await RubricModel.find(
        { isDeleted: false, isGlobal: false },
        rubricProjection,
    ).lean();

    return rubrics;
};

const updateRubric = async ({
    rubricId,
    name,
    desc,
    scoring,
    parameters,
    isGlobal,
    status,
    type,
}) => {
    await updateDocument({
        Model: RubricModel,
        query: { isDeleted: false, _id: rubricId },
        updateDoc: {
            $set: {
                name,
                desc,
                scoring,
                parameters,
                isGlobal,
                status,
                type,
                ...(status === PUBLISHED && { publishedDate: new Date() }),
            },
        },
    });
};

const deleteRubric = async ({ rubricId }) => {
    await updateDocument({
        Model: RubricModel,
        query: { isDeleted: false, _id: rubricId },
        updateDoc: { isDeleted: true },
    });
};

const createGlobalRubric = async ({
    name = '',
    desc = '',
    scoring = '',
    parameters = [],
    userId,
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    await RubricModel.deleteMany({ isGlobal: true });

    const result = await addDocuments({
        Model: RubricModel,
        data: { name, desc, scoring, parameters, isGlobal: true, createdBy: user },
    });

    return result;
};

module.exports = {
    createRubric,
    getRubrics,
    updateRubric,
    deleteRubric,
    createGlobalRubric,
};
