const {
    response_function,
    list_all_response_function,
    convertToMongoObjectId,
} = require('../utility/common');
const {
    get_list,
    get,
    insert,
    update,
    get_list_populate,
    update_condition_array_filter,
    bulk_write,
    update_condition,
    delete: remove,
} = require('../base/base_controller');
const {
    INSTITUTION,
    USER_VACCINATION_DETAILS,
    EVENT_WHOM: { STUDENT, STAFF, BOTH },
} = require('../utility/constants');
const { getSignedURL } = require('../utility/common_functions');

const institution = require('mongoose').model(INSTITUTION);
const user_vaccination_details = require('mongoose').model(USER_VACCINATION_DETAILS);

exports.insert = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _user_id,
                user_type,
                vaccination_status,
                vaccination_type_id,
                vaccination_type,
                dosage_taken,
                immunity_period_status,
                immunity_period_end_at,
                //ug_documents,
            },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        //Check vaccination data present for this user
        const vaccinationDetails = await get(
            user_vaccination_details,
            { _user_id: convertToMongoObjectId(_user_id) },
            { _id: 1 },
        );
        if (vaccinationDetails.status)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        'Vaccination details already present for this user',
                        'Vaccination details already present for this user',
                    ),
                );
        let obj = {};
        if (vaccination_status == true) {
            obj = {
                _institution_id,
                _user_id,
                user_type,
                vaccination_status,
                vaccination_type_id,
                vaccination_type,
                dosage_taken,
                //ug_documents,
            };
        } else {
            obj = {
                _institution_id,
                _user_id,
                user_type,
                vaccination_status,
                immunity_period_status,
                immunity_period_end_at,
                //ug_documents,
            };
        }

        const doc = await insert(user_vaccination_details, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to add', []));
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Added successfully', 'Added Successfully'));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.update = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _user_id,
                user_type,
                vaccination_status,
                vaccination_type_id,
                vaccination_type,
                dosage_taken,
                immunity_period_status,
                immunity_period_end_at,
                //ug_documents,
            },
            params: { id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        let obj = {};
        if (vaccination_status == true) {
            obj = {
                vaccination_status,
                vaccination_type_id,
                vaccination_type,
                dosage_taken,
                immunity_period_status: false,
                immunity_period_end_at: null,
                //ug_documents,
            };
        } else {
            obj = {
                vaccination_status,
                immunity_period_status,
                immunity_period_end_at: immunity_period_status ? immunity_period_end_at : null,
                vaccination_type_id: null,
                vaccination_type: '',
                dosage_taken: [],
                //ug_documents,
            };
        }
        const query = { _id: convertToMongoObjectId(id) };
        const doc = await update(user_vaccination_details, query, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to Update', []));
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Updated successfully', 'Updated Successfully'),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.deleteUserVaccinationDetails = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const obj = {
            isDeleted: true,
        };
        const query = { _id: convertToMongoObjectId(id) };
        const doc = await update(user_vaccination_details, query, obj);
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to delete', []));
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Deleted successfully', 'Deleted Successfully'),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.list = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await get_list(
            user_vaccination_details,
            { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
            {},
        );

        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, req.t('ERROR'), []));
        return res.status(200).send(response_function(res, 200, true, req.t('LIST'), doc));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR'), error.toString()));
    }
};
exports.listByUserID = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { user_id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await get(user_vaccination_details, { _user_id: user_id }, {});
        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, req.t('ERROR'), []));
        if (doc.data.ug_documents)
            doc.data.ug_documents = await getSignedURL(doc.data.ug_documents);
        if (doc.data.vaccination_documents)
            doc.data.vaccination_documents = await getSignedURL(doc.data.vaccination_documents);
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('LIST_BY_USERID'), doc));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR'), error.toString()));
    }
};
exports.documentUpload = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: { _user_document, _vaccination_document },
            query: { user_id, user_type },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );

        let obj = {};

        console.log(obj);
        //Check Vaccination details present
        const vaccinationDetails = await get(
            user_vaccination_details,
            { _user_id: convertToMongoObjectId(user_id) },
            { _id: 1 },
        );
        let doc = '';
        let query = '';
        if (vaccinationDetails.status) {
            if (user_type == STUDENT) {
                if (_user_document) obj.ug_documents = _user_document;
                else obj.vaccination_documents = _vaccination_document;
            } else {
                if (_user_document) obj.proof_for_verification = _user_document;
                else obj.vaccination_documents = _vaccination_document;
            }
            query = { _user_id: convertToMongoObjectId(user_id) };
            doc = await update_condition(user_vaccination_details, query, obj);
        } else {
            obj = {
                _institution_id: convertToMongoObjectId(_institution_id),
                _user_id: convertToMongoObjectId(user_id),
                user_type,
            };
            if (user_type == STUDENT) {
                if (_user_document) obj.ug_documents = _user_document;
                else {
                    obj.vaccination_documents = _vaccination_document;
                }
            } else {
                if (_user_document) obj.proof_for_verification = _user_document;
                else obj.vaccination_documents = _vaccination_document;
            }
            doc = await insert(user_vaccination_details, obj);
        }

        if (!doc.status)
            return res.status(410).send(response_function(res, 410, false, 'Unable to Upload', []));
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Uploaded successfully', 'Uploaded Successfully'),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
