const {
    NotFoundError,
    UpdateFailedError,
    BadRequestError,
} = require('../common/utils/api_error_util');
const { checkObjectIsInValid } = require('../common/utils/common.util');

const addDocuments = async ({
    Model,
    data,
    canThrowError = true,
    errorMessage = 'FAILED_TO_ADD',
}) => {
    try {
        return Model.create(data);
    } catch {
        if (canThrowError) throw new BadRequestError(errorMessage);
    }
};

const removeDocuments = async ({ Model, query = {} }) => {
    return Model.deleteMany(query);
};

const updateDocument = async ({
    Model,
    query = {},
    updateDoc = {},
    arrayFilters = [],
    options = {},
    canThrowError = true,
    canThrowMatchedCountError = false,
    errorMessage = 'UPDATE_FAILED',
}) => {
    if (arrayFilters?.length) {
        Object.assign(options, { arrayFilters });
    }

    let updatedDocument;
    try {
        updatedDocument = await Model.updateOne(query, updateDoc, {
            ...(!checkObjectIsInValid(options) && options),
        });
        // throw error
        if (canThrowError && updatedDocument?.matchedCount !== updatedDocument.modifiedCount) {
            throw new UpdateFailedError(errorMessage);
        }
        if (canThrowMatchedCountError && !updatedDocument?.matchedCount) {
            throw new UpdateFailedError(errorMessage);
        }
    } catch (error) {
        if (canThrowError) throw new UpdateFailedError(errorMessage);
    }

    return updatedDocument;
};

const getDocument = async ({
    Model,
    query = {},
    project = {},
    lean = true,
    canThrowError = true,
    errorMessage = 'NO_DATA_FOUND',
}) => {
    let doc = Model.findOne(query, project);
    if (lean) {
        doc = await doc.lean();
    } else {
        doc = await doc;
    }
    // throw error
    if (canThrowError && !doc) {
        throw new NotFoundError(errorMessage);
    }

    return doc;
};

const getDocumentsCount = async ({ Model, query = {} }) => {
    return Model.countDocuments(query);
};

const getDocuments = async ({
    Model,
    query = {},
    project = {},
    limit,
    skip,
    pageNo,
    sort,
    lean = true,
    canThrowError = true,
    errorMessage = 'NO_DATA_FOUND',
}) => {
    let docs = Model.find(query, project);
    // check is apply pagination or not
    const isPaginate = Boolean(skip || limit);
    // sort if avaialble
    if (sort) {
        docs.sort(sort);
    }
    // paginate if available
    if (isPaginate) {
        docs.skip(skip).limit(limit);
    }
    // lean the documents
    if (lean) {
        docs = await docs.lean();
    } else {
        docs = await docs;
    }

    // throw error
    if (canThrowError && !docs?.length) {
        throw new NotFoundError(errorMessage);
    }
    // if not paginate return data only
    if (!isPaginate) {
        return {
            data: docs,
        };
    }
    // if pagination apply return related data's
    const totalCount = await getDocumentsCount({ Model, query });

    return {
        data: docs,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: pageNo,
    };
};

const getNativeQueryDocuments = async ({ Model, query = {}, project = {} }) => {
    const docs = await Model.collection.find(query, { projection: project }).toArray();

    return {
        data: docs,
    };
};

const bulkUpdateDocuments = async ({
    Model,
    bulkUpdates,
    options = {},
    canThrowError = true,
    errorMessage = '',
}) => {
    if (bulkUpdates?.length) {
        return Model.bulkWrite(bulkUpdates, options).catch((err) => {
            if (canThrowError) {
                throw new UpdateFailedError(errorMessage ?? err);
            }
        });
    }
};

const checkDocumentsExists = async ({
    Model,
    query = {},
    canThrowError = true,
    errorMessage = 'NO_DATA_FOUND',
}) => {
    const exists = await Model.exists(query);
    // throw error
    if (canThrowError && !exists) {
        throw new NotFoundError(errorMessage);
    }

    return !!exists;
};

const updateDocuments = async ({
    Model,
    query = {},
    updateDoc = {},
    arrayFilters = [],
    options = {},
    canThrowError = true,
    errorMessage = 'UPDATE_FAILED',
    canThrowMatchedCountError = false,
}) => {
    if (arrayFilters?.length) {
        Object.assign(options, { arrayFilters });
    }

    let updatedDocuments;
    try {
        updatedDocuments = await Model.updateMany(query, updateDoc, {
            ...(!checkObjectIsInValid(options) && options),
        });
        // throw error
        if (canThrowError && updatedDocuments?.matchedCount !== updatedDocuments.modifiedCount) {
            throw new UpdateFailedError(errorMessage);
        }

        // check updated count
        if (canThrowMatchedCountError && updatedDocuments?.matchedCount === 0) {
            throw new UpdateFailedError(errorMessage);
        }
    } catch (err) {
        if (canThrowError) throw new UpdateFailedError(errorMessage);
    }

    return updatedDocuments;
};

module.exports = {
    addDocuments,
    getDocument,
    getDocuments,
    getNativeQueryDocuments,
    bulkUpdateDocuments,
    updateDocument,
    checkDocumentsExists,
    removeDocuments,
    updateDocuments,
    getDocumentsCount,
};
