const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { PORTFOLIO_STUDENT } = require('../../common/utils/constants');
const { ABSENT, PRESENT } = require('../../common/utils/enums');
const { portfolioSchema } = require('./portfolio.schema');

const schema = new Schema(
    {
        ...portfolioSchema,
        portfolioId: { type: ObjectId },
        student: {
            _id: {
                type: ObjectId,
            },
            email: {
                type: String,
            },
            name: {
                first: { type: String, trim: true },
                middle: { type: String, trim: true },
                last: { type: String, trim: true },
                family: { type: String, trim: true },
            },
            academicNo: { type: String, trim: true },
        },
        history: [
            {
                userId: { type: ObjectId },
                awardedMarks: { type: Number },
                createdAt: { type: Date },
            },
        ],
        report: {
            percentage: { type: Number },
            grade: { type: String },
            hasFailed: { type: <PERSON>olean },
            awardedMarks: { type: Number },
            finalScore: { type: Number },
            isReportGenerated: { type: Boolean, default: false },
            attendance: { type: String, enum: [ABSENT, PRESENT] },
            extraMarks: { type: Number },
        },
    },
    { timestamps: true },
);

schema.index({ institutionCalendarId: 1, programId: 1, courseId: 1, 'student._id': 1 });

module.exports = model(PORTFOLIO_STUDENT, schema);
