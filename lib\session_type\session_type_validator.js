const Joi = require('joi');
const common_files = require('../utility/common');

exports.session_type = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                        return error;
                    }),
                    session_type: Joi.string().alphanum().min(3).trim().required().error(error => {
                        return error;
                    }),
                    delivery_type: Joi.string().min(3).trim().required().error(error => {
                        return error;
                    }),
                    delivery_mode: Joi.string().min(3).trim().required().error(error => {
                        return error;
                    }),
                    delivery_symbol: Joi.string().min(1).trim().required().allow(' ').error(error => {
                        return error;
                    }),
                    _credit_calc_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.session_type_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).allow('').error(error => {
                        return error;
                    }),
                    session_type: Joi.string().alphanum().min(3).trim().error(error => {
                        return error;
                    }),
                    delivery_type: Joi.string().min(3).trim().error(error => {
                        return error;
                    }),
                    delivery_mode: Joi.string().min(3).trim().error(error => {
                        return error;
                    }),
                    delivery_symbol: Joi.string().min(1).trim().allow(' ').error(error => {
                        return error;
                    }),
                    _credit_calc_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.session_type_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}