const Taxonomy = require('../../models/taxonomy');

const { insert, dsCustomUpdate, dsDeleteOne, dsGetCount } = require('../../base/base_controller');
const { response_function, responseFunctionWithRequest } = require('../../utility/common');

// constants
const {
    DS_ADDED,
    DS_ADD_FAILED,
    DS_DATA_RETRIEVED,
    DS_UPDATED,
    DS_DELETED,
    DS_UPDATE_FAILED,
} = require('../../utility/constants');

const project = { name: 1 };

// get all Taxonomy
exports.getTaxonomy = async (req, res) => {
    try {
        const { search: searchText } = req;
        let search;
        const taxonomyQuery = { isDeleted: false };
        if (searchText) {
            const searchQuery = {
                $or: [
                    {
                        name: { $regex: searchText, $options: 'i' },
                    },
                ],
            };
            search = { $and: [taxonomyQuery, searchQuery] };
        } else {
            search = { $and: [taxonomyQuery] };
        }

        const taxonomy = await Taxonomy.find(search, project).exec();

        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, DS_DATA_RETRIEVED, taxonomy));
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// create Taxonomy
exports.createTaxonomy = async (req, res) => {
    try {
        const {
            body: { name, createdBy },
        } = req;

        const data = { name, createdBy };

        // check if duplicate exists
        const docQuery = { isDeleted: false, name };

        const docCount = await dsGetCount(Taxonomy, docQuery);

        if (docCount > 0) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('TAXONOMY_NAME_ALREADY_EXISTS')));
        }

        // insert query
        const { status } = await insert(Taxonomy, data);

        if (!status) {
            return res.status(200).send(response_function(res, 200, false, DS_ADD_FAILED));
        }
        return res.status(200).send(response_function(res, 200, true, DS_ADDED));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// update activities
exports.updateTaxonomy = async (req, res) => {
    try {
        const {
            body: { name },
            params: { id },
        } = req;

        // check if document exists
        let docCount = await dsGetCount(Taxonomy, { _id: id });

        if (docCount === 0) {
            return res.status(200).send(response_function(res, 200, false, INVALID_ID));
        }

        // check if duplicate exists
        const docQuery = {
            _id: { $ne: id },
            isDeleted: false,
            name,
        };

        docCount = await dsGetCount(Taxonomy, docQuery);

        if (docCount > 0) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('TAXONOMY_NAME_ALREADY_EXISTS')));
        }

        const updateQuery = { _id: id };

        const data = {
            $set: { name },
        };

        const { success } = await dsCustomUpdate(Taxonomy, updateQuery, data);

        if (!success) {
            return res.status(200).send(response_function(res, 200, false, DS_UPDATE_FAILED));
        }

        return res.status(200).send(response_function(res, 200, true, DS_UPDATED));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// delete activities
exports.deleteTaxonomy = async (req, res) => {
    try {
        const {
            params: { id },
        } = req;

        // check if document exists
        let docCount = await dsGetCount(Taxonomy, { _id: id });

        if (docCount === 0) {
            return res.status(200).send(response_function(res, 200, false, INVALID_ID));
        }

        // check if document exists
        docCount = await dsGetCount(Taxonomy, { _id: id });

        if (docCount === 0) {
            return res.status(200).send(response_function(res, 200, false, INVALID_ID));
        }

        const { success, message } = await dsDeleteOne(Taxonomy, id);
        if (!success) {
            return res.status(200).send(response_function(res, 200, false, message));
        }

        return res.status(200).send(response_function(res, 200, true, DS_DELETED));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
