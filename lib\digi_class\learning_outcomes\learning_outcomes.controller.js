const { sendResponse } = require('../../utility/common');
const { getClosBySloIds, getPlosByCloIds } = require('./learning_outcomes.service');

/**
 * get all clo of slo
 * @param {body:{sloIds}} req
 * @sloIds Array[String]
 * @param {*} res
 * @returns Array[Object]
 */
exports.getAllClo = async (req, res) => {
    try {
        const { sloIds } = req.body;
        const result = await getClosBySloIds(sloIds);
        return sendResponse(res, 200, true, req.t('CLO_LIST'), result);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

/**
 * get all plo of clo
 * @param {body:{cloIds}} req
 * @cloIds Array[String]
 * @param {*} res
 * @returns Array[Object]
 */
exports.getAllPlo = async (req, res) => {
    try {
        const { cloIds } = req.body;
        const result = await getPlosByCloIds(cloIds);
        return sendResponse(res, 200, true, req.t('PLO_LIST'), result);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
