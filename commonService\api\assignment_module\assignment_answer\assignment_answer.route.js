const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const { validate } = require('../../../../middleware/validation');
const { paramIDValidator, assignmentAnswerValidator } = require('./assignment_answer.validator');
const {
    createAssignmentAnswer,
    listAssignmentAnswer,
    getAssignmentAnswer,
    updateAssignmentAnswer,
    deleteAssignmentAnswer,
} = require('./assignment_answer.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.post(
    '/create',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: assignmentAnswerValidator, property: 'body' }]),
    catchAsync(createAssignmentAnswer),
);
router.get(
    '/get/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: paramIDValidator, property: 'params' }]),
    catchAsync(getAssignmentAnswer),
);
router.get(
    '/list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(listAssignmentAnswer),
);
router.put(
    '/update/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: paramIDValidator, property: 'params' },
        { schema: assignmentAnswerValidator, property: 'body' },
    ]),
    catchAsync(updateAssignmentAnswer),
);
router.delete(
    '/delete/:id',
    validate([{ schema: paramIDValidator, property: 'params' }]),
    catchAsync(deleteAssignmentAnswer),
);
module.exports = router;
