const express = require('express');
const route = express.Router();
const controller = require('./controller');
const { dashboardData } = require('./controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get(
    '/leave_list/:user_id/:inst_cal_id/:role_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.getLeaveList,
);
route.get(
    '/program_list/:user_id/:inst_cal_id/:role_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.getProgramList,
);
route.get(
    '/configure_settings/:userId/:institutionCalendar/:roleId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.dashboard /* controller.getConfigureSettingList */,
);
route.get(
    '/monitoringCourses/:userId/:institutionCalendar/:roleId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.monitoringCourses,
);
route.get(
    '/monitoringCourseDetails/:userId/:institutionCalendar/:roleId/:programId/:courseId/:term/:level_no',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.monitoringCourseDetails,
);
route.put(
    '/configure_settings/:user_id/:inst_cal_id/:role_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.updateConfigureSettingYearLevel,
);

// Dashboard Altered API
route.get(
    '/programWise/:userId/:institutionCalendar/:roleId/:tab',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.programWiseData,
);
route.get(
    '/:userId/:institutionCalendar/:roleId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    /* controller.monitoringCourses */ dashboardData,
);

module.exports = route;
