const Joi = require('joi');
const { com_response } = require('../../utility/common');

// create discussion topics schema
function createDiscussion(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                title: Joi.string(),
                topics: Joi.string(),
                description: Joi.string().required().min(1).max(1024),
                author: Joi.string().required(),
                channel_id: Joi.string().required(),
                program_id: Joi.string(),
                level_no: Joi.string(),
                term: Joi.string(),
                rotation: Joi.string(),
                rotation_count: Joi.number().integer(),
                main_group_name: Joi.string(),
                group_name: Joi.string(),
                admin_course: Joi.boolean(),
                merge_status: Joi.boolean(),
                _id: Joi.string(),
                attachments: Joi.array(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function createReply(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                discussion_id: Joi.string().required(),
                comment: Joi.string().required().min(1).max(5000),
                author: Joi.string().required(),
                _id: Joi.string(),
                reply_id: Joi.string().optional(),
                user_type: Joi.string().required().valid('staff', 'student'),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

module.exports = {
    createDiscussion,
    createReply,
};
