let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let time_group = new Schema({
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION
    },
    type: {
        type: String,
        required: true
    },
    start_time: {
        type: Date,
        required: true
    },
    end_time: {
        type: Date,
        required: true
    },
    gender: {
        type: String,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });

// day_groupSchema.index({ name: 1, isDeleted: 1 }, { unique: true });
// module.exports = mongoose.model('conty', day_groupSchema);
module.exports = mongoose.model(constant.TIME_GROUP, time_group);