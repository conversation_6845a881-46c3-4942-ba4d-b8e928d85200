const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = Schema.Types.ObjectId;
const {
    LEADER_BOARD,
    STAFF_ATTENDANCE,
    SUPPORT_SESSION,
    QUIZ,
    ENGAGER,
    OVERALL_MANUAL,
    MANUALLY_CONDUCTED,
} = require('../utility/constants');
const leaderBoardSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        leaderBoard: {
            parameters: [
                {
                    name: { type: String },
                    weight: { type: Number },
                    criteria: [
                        {
                            criteriaName: {
                                type: String,
                                enum: [STAFF_ATTENDANCE, SUPPORT_SESSION, QUIZ, ENGAGER],
                            },
                            isActive: { type: Boolean, default: true },
                            noOfSession: { type: Boolean, default: true },
                            sessionStarted: {
                                noOfStudent: { type: Number },
                                minsOfStart: { type: Number },
                            },
                            noOfEngager: { type: Boolean, default: true },
                            studentParticipated: { type: Number },
                            score: {
                                isActive: { type: Boolean, default: false },
                                noOfScore: { type: Number },
                            },
                            consideration: {
                                isActive: { type: Boolean, default: false },
                                noOfConsideration: { type: Number },
                            },
                            bufferTime: {
                                isActive: { type: Boolean, default: false },
                                noOfBuffer: { type: Number },
                            },
                            allowance: {
                                isActive: { type: Boolean, default: false },
                                noOfAllowance: { type: Number },
                            },
                            supportSession: {
                                regular: { type: Boolean, default: false },
                                activity: { type: Boolean, default: false },
                            },
                        },
                    ],
                },
            ],
            badgeStyle: { type: String },
            badgeName: [
                {
                    name: { type: String },
                    startRange: { type: Number },
                    endRange: { type: Number },
                },
            ],
        },
        anomalyBoard: {
            parameters: [
                {
                    name: { type: String },
                    criteria: [
                        {
                            criteriaName: {
                                type: String,
                                enum: [OVERALL_MANUAL, MANUALLY_CONDUCTED],
                            },
                            isActive: { type: Boolean, default: true },
                            weight: { type: Number },
                            score: {
                                isActive: { type: Boolean, default: false },
                                noOfScore: { type: Number },
                            },
                            allowance: {
                                isActive: { type: Boolean, default: false },
                                noOfAllowance: { type: Number },
                            },
                            noOfSession: { type: Boolean, default: true },
                            sessionPercentage: { type: Number },
                        },
                    ],
                },
            ],
            anomalyLevel: [
                {
                    startRange: { type: Number },
                    endRange: { type: Number },
                    label: { type: String },
                    starRange: {
                        noOfRange: { type: Number },
                        color: { type: String },
                    },
                    imageUrl: { type: String },
                },
            ],
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(LEADER_BOARD, leaderBoardSchema);
