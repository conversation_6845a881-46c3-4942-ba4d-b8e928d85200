let institution_formate = require('../institution/institution_formate');

module.exports = {
    infrastructure: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                institution: institution_formate.institution_ID_Only(element.institution),
                building_no: element.building_no,
                class_room_no: element.class_room_no,
                room_type: element.room_type,
                gender: element.gender,
                allocated_year: element.allocated_year,
                room_size: element.room_size,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    infrastructure_ID: (doc) => {
        let obj = {
            _id: doc._id,
            institution: institution_formate.institution_ID_Only(doc.institution),
            building_no: doc.building_no,
            class_room_no: doc.class_room_no,
            room_type: doc.room_type,
            gender: doc.gender,
            allocated_year: doc.allocated_year,
            room_size: doc.room_size,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    infrastructure_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            institution: doc._institution_id,
            building_no: doc.building_no,
            class_room_no: doc.class_room_no,
            room_type: doc.room_type,
            gender: doc.gender,
            allocated_year: doc.allocated_year,
            room_size: doc.room_size,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    infrastructure_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                institution: element._institution_id,
                building_no: element.building_no,
                class_room_no: element.class_room_no,
                room_type: element.room_type,
                gender: element.gender,
                allocated_year: element.allocated_year,
                room_size: element.room_size,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}