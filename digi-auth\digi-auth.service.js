const digiAuthAxios = require('./digi-auth.axios');
const FormData = require('form-data');
const { createReadStream } = require('fs');
const { AUTH_PASSWORD_SYNC } = require('../lib/utility/util_keys');
/**
 * To import user(s)
 * @param {Object} importUser
 * @param {Users[]} importUser.users
 * @returns {Promise<User[]>}
 */
async function importUser({ users = [], appCode = '' } = {}) {
    if (!users.length) throw Error('Please provide users');

    users.forEach((user) => {
        user.username = user.username || user.email;
        user.employeeOrAcademicId = user.user_id;
        user.userId = user._id;
        user.gender = user.gender[0].toUpperCase();
        user.app = appCode;
        if (AUTH_PASSWORD_SYNC != 'true') {
            delete user.password;
        }

        delete user._id;
        delete user.user_id;
        delete user.user_type;
    });

    return digiAuthAxios({
        url: '/users/import',
        method: 'POST',
        data: { users },
    })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                throw error.response.data.message || error.message;
            } else {
                // Something happened in setting up the request that triggered an Error
                throw error.message;
            }
        });
}

/**
 * sync user's password & mobile
 * @param {string} employeeOrAcademicId
 * @param {ObjectId} userId
 * @param {file} facial.facial
 * @returns {Promise<boolean>}
 */
async function syncUser({ employeeOrAcademicId, userId, facial, password, facialTrained } = {}) {
    if (!employeeOrAcademicId) throw Error('Please provide employee or academic id');

    const updateBody = {
        updateCondition: {
            'apps.userId': userId,
        },
        updateBody: {
            ...(facial && { facial }),
            ...(AUTH_PASSWORD_SYNC === 'true' && password && { password }),
            ...(facialTrained && { facialTrained }),
        },
    };

    return digiAuthAxios({
        url: `/users/sync/DS/${employeeOrAcademicId}`,
        method: 'PATCH',
        data: updateBody,
    })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) throw error.response.data.message || error.response.data;
            else throw error.message;
        });
}

/**
 * verify user's email
 * @param {Object} user
 * @param {string} user.employeeOrAcademicId
 * @param {('email'|'mobile'|'face'|'finger')} user.type
 * @param {string} user.verify
 * @param {string} user.verifiedBy
 * @param {boolean} user.isVerified
 * @returns {Promise<boolean>}
 */
async function verifyUser({
    employeeOrAcademicId,
    type,
    verify,
    verifiedBy,
    isVerified = false,
} = {}) {
    if (!employeeOrAcademicId) throw Error('Please provide employee or academic id');

    const updateBody = {
        type,
        verify,
        verifiedBy,
        isVerified,
    };

    return digiAuthAxios({
        url: `/users/verify/DS/${employeeOrAcademicId}`,
        method: 'PATCH',
        data: updateBody,
    })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) throw error.response.data.message || error.response.data;
            else throw error.message;
        });
}

/**
 * facial auth
 * @param {Object} facial
 * @param {'student'|'faculty'} facial.type
 * @param {string} facial.userId
 * @param {file} facial.facial
 * @param {'DS'|'DC'} facial.app
 * @returns {Promise}
 */
async function verifyFacial({ type = 'student', userId, facial, app = 'DC' } = {}) {
    if (!userId) throw Error('Please provide user id');
    if (!facial) throw Error('Please provide facial image');

    const form = new FormData();
    form.append('app', app);
    form.append('type', type);
    form.append('employeeOrAcademicId', userId);
    form.append('facial', createReadStream(facial.path));

    return digiAuthAxios
        .post('/auth/facial', form, { headers: form.getHeaders() })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) throw error.response.data.message || error.response.data;
            else throw error.message;
        });
}

/**
 * Face Train
 * @param {string} employeeOrAcademicId
 * @returns {Promise<boolean>}
 */
async function faceTrain({ employeeOrAcademicId } = {}) {
    if (!employeeOrAcademicId) throw Error('Please provide employee or academic id');

    const bodyData = {
        app: 'DS',
        employeeOrAcademicId,
    };

    return digiAuthAxios({
        url: `/users/train-facial`,
        method: 'POST',
        data: bodyData,
    })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) throw error.response.data.message || error.response.data;
            else throw error.message;
        });
}

/**
 * User Removal
 * @param {string} employeeOrAcademicId
 * @returns {Promise<boolean>}
 */
async function userRemove({ employeeOrAcademicId } = {}) {
    if (!employeeOrAcademicId) throw Error('Please provide employee or academic id');
    const bodyData = {
        employeeOrAcademicIds: [employeeOrAcademicId],
    };
    return digiAuthAxios({
        url: `/users/dc-user`,
        method: 'DELETE',
        data: bodyData,
    })
        .then((resp) => resp.data)
        .catch(function (error) {
            if (error.response) throw error.response.data.message || error.response.data;
            else throw error.message;
        });
}

module.exports = {
    importUser,
    syncUser,
    verifyUser,
    verifyFacial,
    faceTrain,
    userRemove,
};
