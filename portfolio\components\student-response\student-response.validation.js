const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().alphanum().length(24);

const startFormSchema = Joi.object({
    query: Joi.object({
        componentId: objectId.required(),
        childrenId: objectId.required(),
        formId: objectId.required(),
    }),
}).unknown(true);

const updateStudentResponseSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectId.required().messages({
            'any.required': 'STUDENT_RESPONSE_ID_REQUIRED',
            'string.length': 'STUDENT_RESPONSE_ID_REQUIRED',
        }),
    }),
    body: Joi.object({
        pageId: objectId.required().messages({
            'any.required': 'PAGE_ID_REQUIRED',
            'string.length': 'PAGE_ID_REQUIRED',
        }),
        sectionId: objectId.required().messages({
            'any.required': 'SECTION_ID_REQUIRED',
            'string.length': 'SECTION_ID_REQUIRED',
        }),
        section: Joi.object().required().messages({
            'any.required': 'PAGES_REQUIRED',
        }),
    }),
}).unknown(true);

const submitFormSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectId.required().messages({
            'any.required': 'STUDENT_RESPONSE_ID_REQUIRED',
            'string.length': 'STUDENT_RESPONSE_ID_REQUIRED',
        }),
    }),
    body: Joi.object({
        pages: Joi.array().min(1).required().messages({
            'any.required': 'PAGES_REQUIRED',
            'array.min': 'PAGES_REQUIRED',
        }),
    }),
}).unknown(true);

const getStudentResponseSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().messages({
            'any.required': 'FORM_ID_REQUIRED',
            'string.length': 'FORM_ID_REQUIRED',
        }),
        portfolioId: objectId.required().messages({
            'any.required': 'PORTFOLIO_ID_REQUIRED',
            'string.length': 'PORTFOLIO_ID_REQUIRED',
        }),
        componentId: objectId.required().messages({
            'any.required': 'COMPONENT_ID_REQUIRED',
            'string.length': 'COMPONENT_ID_REQUIRED',
        }),
        childrenId: objectId.required().messages({
            'any.required': 'CHILDREN_ID_REQUIRED',
            'string.length': 'CHILDREN_ID_REQUIRED',
        }),
    }),
}).unknown(true);

module.exports = {
    startFormSchema,
    updateStudentResponseSchema,
    submitFormSchema,
    getStudentResponseSchema,
};
