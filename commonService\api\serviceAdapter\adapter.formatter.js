const { clone, nameFormatter, query, convertToMongoObjectId } = require('../../utility/common');
const {
    LOCAL,
    SLO,
    CLO,
    GENDER: { BOTH, MALE, FEMALE },
    DIGI_PROGRAM,
    COMPLETED,
    STUDENT,
    STUDENT_GROUP_MODE: { FYD, COURSES, ROTATION },
    DIGI_DEPARTMENT_SUBJECT,
} = require('../../utility/constants');
const programSchema = require('mongoose').model(DIGI_PROGRAM);
const departmentSchema = require('mongoose').model(DIGI_DEPARTMENT_SUBJECT);

const programFormatting = ({ programList, dataFrom }) => {
    try {
        const programDatas = [];
        if (dataFrom === LOCAL) {
            for (const programElement of programList) {
                programDatas.push({
                    _id: programElement._id,
                    programName: programElement.name,
                    programCode: programElement.code,
                    programDegree: programElement.degree,
                    type: programElement.type,
                    program_type: programElement.program_type,
                });
            }
        } else {
            // Formate datas from Other Environments
        }
        return programDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const yearLevelFormatting = ({ yearLevelList, dataFrom }) => {
    try {
        const yearLevelDatas = [];
        if (dataFrom === LOCAL) {
            for (const levelElement of yearLevelList.level) {
                yearLevelDatas.push({
                    year: levelElement.year,
                    level: levelElement.level_no,
                    term: levelElement.term,
                });
            }
        } else {
            // Formate datas from Other Environments
        }
        return yearLevelDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseFormatting = ({ courseList, dataFrom }) => {
    try {
        const courseDatas = [];
        if (dataFrom === LOCAL) {
            for (const levelElement of courseList.level) {
                if (levelElement.rotation === 'no')
                    for (courseElement of levelElement.course) {
                        courseDatas.push({
                            year: levelElement.year,
                            level: levelElement.level_no,
                            term: levelElement.term,
                            _course_id: courseElement._course_id,
                            courses_name: courseElement.courses_name,
                            courses_number: courseElement.courses_number,
                            credit_hours: courseElement.credit_hours,
                            rotation: 'no',
                        });
                    }
                else
                    for (rotationElement of levelElement.rotation_course) {
                        for (courseElement of rotationElement.course) {
                            courseDatas.push({
                                year: levelElement.year,
                                level: levelElement.level_no,
                                term: levelElement.term,
                                _course_id: courseElement._course_id,
                                courses_name: courseElement.courses_name,
                                courses_number: courseElement.courses_number,
                                credit_hours: courseElement.credit_hours,
                                rotation_count: rotationElement.rotation_count,
                                rotation: 'yes',
                            });
                        }
                    }
            }
        } else {
            // Formate datas from Other Environments
        }
        return courseDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseSLOFormatting = ({ courseSLO, dataFrom }) => {
    try {
        let courseSloDatas = [];
        if (dataFrom === LOCAL) {
            for (const sloElement of courseSLO.session_flow_data) {
                courseSloDatas = [...courseSloDatas, ...sloElement.slo.flat()];
            }
        } else {
            // Formate datas from Other Environments
        }
        return courseSloDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseCLOFormatting = ({ courseCLO, dataFrom }) => {
    try {
        let courseCloDatas = [];
        if (dataFrom === LOCAL) {
            if (!(courseCLO && courseCLO.framework && courseCLO.framework.domains)) return [];
            for (const sloElement of courseCLO.framework.domains) {
                courseCloDatas = [...courseCloDatas, ...sloElement.clo.flat()];
            }
            courseCloDatas = courseCloDatas.filter(
                (cloElement) => cloElement.isActive && cloElement.isDeleted === false,
            );
        } else {
            // Formate datas from Other Environments
        }
        return courseCloDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const coursePLOFormatting = ({ programCurriculum, yearLevelList, dataFrom }) => {
    try {
        let programPloDatas = [];
        const programCurriculumDatas = [];
        if (dataFrom === LOCAL) {
            for (curriculumElement of programCurriculum) {
                if (
                    !(
                        curriculumElement &&
                        curriculumElement.framework &&
                        curriculumElement.framework.domains
                    )
                )
                    return [];
                for (const sloElement of curriculumElement.framework.domains) {
                    programPloDatas = [...programPloDatas, ...sloElement.plo.flat()];
                }
                programPloDatas = programPloDatas.filter(
                    (cloElement) => cloElement.isActive && cloElement.isDeleted === false,
                );
                if (programPloDatas.length)
                    programCurriculumDatas.push({
                        curriculumName: curriculumElement.curriculum_name,
                        plo: programPloDatas,
                    });
            }
        } else {
            // Formate datas from Other Environments
        }
        return {
            curriculumPLO: programCurriculumDatas,
            curriculumLevel: yearLevelList && yearLevelList.level ? yearLevelList.level : [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseStudentFormatting = ({
    studentGroup,
    courseId,
    term,
    level,
    rotationCount,
    dataFrom,
}) => {
    try {
        let studentList = [];
        const studentDetailsList = [];
        if (dataFrom === LOCAL) {
            const levelTermGroup = studentGroup.groups.find(
                (groupElement) =>
                    groupElement.level.toString() === level &&
                    groupElement.term.toString() === term,
            );
            if (levelTermGroup) {
                const courseGroup = levelTermGroup.courses.find(
                    (courseElement) => courseElement._course_id.toString() === courseId,
                );
                if (courseGroup) {
                    const courseSetting =
                        rotationCount && rotationCount !== 0
                            ? courseGroup.setting.filter(
                                  (sessionElement) =>
                                      sessionElement._group_no.toString() ===
                                      rotationCount.toString(),
                              )
                            : courseGroup.setting;
                    for (settingElement of courseSetting) {
                        for (sessionSettingElement of settingElement.session_setting) {
                            studentList = [
                                ...studentList,
                                ...sessionSettingElement.groups
                                    .map((settingGroupElement) =>
                                        settingGroupElement._student_ids.flat(),
                                    )
                                    .flat(),
                            ];
                        }
                    }
                }
                studentList = studentList.filter(
                    (ele, index) =>
                        studentList.findIndex((ele2) => ele2.toString() === ele.toString()) ===
                        index,
                );
                for (studentIdElement of studentList) {
                    const studentData = clone(
                        levelTermGroup.students.find(
                            (studentElement) =>
                                studentElement._student_id.toString() ===
                                studentIdElement.toString(),
                        ),
                    );
                    if (studentData) {
                        delete studentData._student_id;
                        studentData.name = nameFormatter(studentData.name);
                        studentDetailsList.push(studentData);
                    }
                }
            }
        } else {
            // Formate datas from Other Environments
        }
        return studentDetailsList;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programLevelsStudentFormatting = ({ studentGroup, term, level, dataFrom }) => {
    try {
        let studentList = [];
        const studentDetailsList = [];
        let levelStudentDetailsList = [];
        if (dataFrom === LOCAL) {
            const levelTermGroup = clone(
                studentGroup.groups.filter(
                    (groupElement) =>
                        groupElement.term.toString() === term &&
                        level.find(
                            (levelElement) =>
                                levelElement.toString() === groupElement.level.toString(),
                        ),
                ),
            );
            if (levelTermGroup.length) {
                for (levelTermElement of levelTermGroup) {
                    for (courseElement of levelTermElement.courses) {
                        for (settingElement of courseElement.setting) {
                            for (sessionSettingElement of settingElement.session_setting) {
                                studentList = [
                                    ...studentList,
                                    ...sessionSettingElement.groups
                                        .map((settingGroupElement) =>
                                            settingGroupElement._student_ids.flat(),
                                        )
                                        .flat(),
                                ];
                            }
                        }
                    }
                    levelStudentDetailsList = [
                        ...levelStudentDetailsList,
                        ...levelTermElement.students,
                    ];
                }
            }
            studentList = clone(
                studentList.filter(
                    (ele, index) =>
                        studentList.findIndex((ele2) => ele2.toString() === ele.toString()) ===
                        index,
                ),
            );
            for (studentIdElement of studentList) {
                const studentData = clone(
                    levelStudentDetailsList.find(
                        (studentElement) =>
                            studentElement._student_id.toString() === studentIdElement.toString(),
                    ),
                );
                if (studentData) {
                    delete studentData._student_id;
                    studentData.name = nameFormatter(studentData.name);
                    studentDetailsList.push(studentData);
                }
            }
        } else {
            // Formate datas from Other Environments
        }
        return studentDetailsList;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programWithTermFormatting = ({ programList, dataFrom }) => {
    try {
        const programDatas = [];
        if (dataFrom === LOCAL) {
            for (const programElement of programList) {
                programDatas.push({
                    _id: programElement._id,
                    programName: programElement.name,
                    term: programElement.term.map((termElement) => termElement.term_name),
                });
            }
        } else {
            // Formate datas from Other Environments
        }
        return programDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseLevelTermFormatting = ({ courseList, term, userProgramCourses, dataFrom }) => {
    try {
        const levelDatas = [];
        if (dataFrom === LOCAL) {
            for (const levelElement of courseList.level) {
                if (term === levelElement.term) {
                    const courseDatas = [];
                    if (levelElement.rotation === 'no')
                        for (courseElement of levelElement.course) {
                            if (
                                (userProgramCourses.isAdmin === false &&
                                    userProgramCourses.courseIds.length &&
                                    userProgramCourses.courseIds.find(
                                        (courseIdElement) =>
                                            courseIdElement.toString() ===
                                            courseElement._course_id.toString(),
                                    )) ||
                                userProgramCourses.isAdmin
                            )
                                courseDatas.push({
                                    _course_id: courseElement._course_id,
                                    courses_name: courseElement.courses_name,
                                });
                        }
                    else
                        for (rotationElement of levelElement.rotation_course) {
                            for (courseElement of rotationElement.course) {
                                if (
                                    (userProgramCourses.isAdmin === false &&
                                        userProgramCourses.courseIds.length &&
                                        userProgramCourses.courseIds.find(
                                            (courseIdElement) =>
                                                courseIdElement.toString() ===
                                                courseElement._course_id.toString(),
                                        )) ||
                                    userProgramCourses.isAdmin
                                )
                                    courseDatas.push({
                                        _course_id: courseElement._course_id,
                                        courses_name: courseElement.courses_name,
                                        rotation_count: rotationElement.rotation_count,
                                    });
                            }
                        }
                    if (courseDatas.length)
                        levelDatas.push({
                            level: levelElement.level_no,
                            rotation: levelElement.rotation,
                            courses: courseDatas,
                        });
                }
            }
        } else {
            // Formate datas from Other Environments
        }
        return levelDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const yearLevelCourseFormatting = ({ courseList, term, dataFrom }) => {
    try {
        const levelDatas = [];
        if (dataFrom === LOCAL) {
            for (const levelElement of courseList.level) {
                if (term === levelElement.term) {
                    const courseDatas = [];
                    if (levelElement.rotation === 'no')
                        for (courseElement of levelElement.course) {
                            courseDatas.push({
                                _course_id: courseElement._course_id,
                                courses_name: courseElement.courses_name,
                                courses_number: courseElement.courses_number,
                                model: courseElement.model,
                            });
                        }
                    else
                        for (rotationElement of levelElement.rotation_course) {
                            for (courseElement of rotationElement.course) {
                                if (
                                    !courseDatas.find(
                                        (courseDataElement) =>
                                            courseDataElement._course_id.toString() ===
                                            courseElement._course_id.toString(),
                                    )
                                )
                                    courseDatas.push({
                                        _course_id: courseElement._course_id,
                                        courses_name: courseElement.courses_name,
                                        courses_number: courseElement.courses_number,
                                        model: courseElement.model,
                                    });
                            }
                        }
                    levelDatas.push({
                        year: levelElement.year,
                        levelNo: levelElement.level_no,
                        rotation: levelElement.rotation,
                        courses: courseDatas,
                    });
                }
            }
        } else {
            // Formate datas from Other Environments
        }
        return levelDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programPLOFormatting = ({ programCurriculum, yearLevelList, dataFrom }) => {
    try {
        let programPloDatas = [];
        const programCurriculumDatas = [];
        const yearLevelDatas = [];
        const yearLevelCourses = [];
        if (dataFrom === LOCAL) {
            if (
                !(
                    programCurriculum &&
                    programCurriculum.framework &&
                    programCurriculum.framework.domains
                )
            )
                return [];
            for (const sloElement of programCurriculum.framework.domains) {
                programPloDatas = [
                    ...programPloDatas,
                    ...sloElement.plo
                        .map((ploElement) => {
                            return {
                                ...ploElement,
                                clos: ploElement.clos,
                            };
                        })
                        .flat(),
                ];
            }
            programPloDatas = programPloDatas.filter(
                (cloElement) => cloElement.isActive && cloElement.isDeleted === false,
            );
            if (programPloDatas.length)
                programCurriculumDatas.push({
                    curriculumName: programCurriculum.curriculum_name,
                    plo: programPloDatas,
                });
            for (levelElement of yearLevelList.level) {
                for (courseElement of levelElement.rotation === 'no'
                    ? levelElement.course
                    : levelElement.rotation_course[0].course) {
                    yearLevelDatas.push({
                        year: levelElement.year,
                        levelNo: levelElement.level_no,
                        term: levelElement.term,
                        curriculum: levelElement.curriculum,
                        rotation: levelElement.rotation,
                        _course_id: courseElement._course_id,
                        coursesName: courseElement.courses_name,
                    });
                }
            }
        } else {
            // Formate datas from Other Environments
        }
        return {
            curriculumPLO: programPloDatas,
            course: yearLevelDatas,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const coursesCLOsFormatting = ({ coursesCLO, dataFrom }) => {
    try {
        const coursesCLOs = [];
        if (dataFrom === LOCAL) {
            for (courseElement of coursesCLO) {
                let courseCloDatas = [];
                if (!(courseElement && courseElement.framework && courseElement.framework.domains))
                    return [];
                for (const sloElement of courseElement.framework.domains) {
                    courseCloDatas = [...courseCloDatas, ...sloElement.clo.flat()];
                }
                courseCloDatas = courseCloDatas.filter(
                    (cloElement) => cloElement.isActive && cloElement.isDeleted === false,
                );
                coursesCLOs.push({
                    _course_id: courseElement._id,
                    course_assigned_details: courseElement.course_assigned_details,
                    clo: courseCloDatas.map((cloElement) => cloElement._id).flat(),
                });
            }
        } else {
            // Formate datas from Other Environments
        }
        return coursesCLOs;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const InstitutionCalendarsFormatting = ({ calendarData, institutionCalendarId, dataFrom }) => {
    try {
        let institutionCalendarDatas = [];
        if (dataFrom === LOCAL) {
            const calendarIndex = calendarData.findIndex(
                (calendarDataElement) =>
                    calendarDataElement._id.toString() === institutionCalendarId.toString(),
            );
            institutionCalendarDatas =
                calendarIndex === -1
                    ? calendarData
                    : calendarData.splice(calendarIndex, calendarData.length);
            // institutionCalendarDatas = [
            //     ...institutionCalendarDatas,
            // ...institutionCalendarDatas,
            // ...institutionCalendarDatas,
            // ];
        } else {
            // Formate datas from Other Environments
        }
        return institutionCalendarDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userProgramIdFormatting = ({
    roleAssignData,
    role_id,
    courseCoordinatorData,
    courseScheduleData,
    dataFrom,
}) => {
    try {
        let programIdDatas = [];
        if (dataFrom === LOCAL) {
            if (roleAssignData && roleAssignData.roles)
                for (const roleElement of roleAssignData.roles)
                    if (roleElement._role_id.toString() === role_id)
                        programIdDatas = [
                            ...programIdDatas,
                            ...roleElement.program.map((programElement) =>
                                programElement._program_id.toString(),
                            ),
                        ];
            if (courseCoordinatorData && courseCoordinatorData.length)
                programIdDatas = [
                    ...programIdDatas,
                    ...courseCoordinatorData.map((programElement) =>
                        programElement._program_id.toString(),
                    ),
                ];
            if (courseScheduleData && courseScheduleData.length)
                programIdDatas = [
                    ...programIdDatas,
                    ...courseScheduleData.map((programElement) => programElement.toString()),
                ];
            programIdDatas = [...new Set(programIdDatas)].map((programIdElement) =>
                convertToMongoObjectId(programIdElement),
            );
        } else {
            // Formate datas from Other Environments
        }
        return programIdDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userProgramIdsFormatting = ({
    roleAssignData,
    role_id,
    courseCoordinatorData,
    courseScheduleData,
    dataFrom,
}) => {
    try {
        let programIdDatas = [];
        if (dataFrom === LOCAL) {
            if (roleAssignData && roleAssignData.roles)
                for (const roleElement of roleAssignData.roles)
                    if (roleElement._role_id.toString() === role_id)
                        programIdDatas = [
                            ...programIdDatas,
                            ...roleElement.program.map((programElement) =>
                                programElement._program_id.toString(),
                            ),
                        ];
            if (courseCoordinatorData && courseCoordinatorData.length)
                programIdDatas = [
                    ...programIdDatas,
                    ...courseCoordinatorData.map((programElement) =>
                        programElement._program_id.toString(),
                    ),
                ];
            if (courseScheduleData && courseScheduleData.length)
                programIdDatas = [
                    ...programIdDatas,
                    ...courseScheduleData.map((programElement) => programElement.toString()),
                ];
            programIdDatas = [...new Set(programIdDatas)].map((programIdElement) =>
                convertToMongoObjectId(programIdElement),
            );
        } else {
            // Formate datas from Other Environments
        }
        return programIdDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userCourseIdFormatting = ({ courseCoordinatorData, courseScheduleData, dataFrom }) => {
    try {
        let courseIdDatas = [];
        if (dataFrom === LOCAL) {
            if (courseCoordinatorData && courseCoordinatorData.length)
                courseIdDatas = [
                    ...courseIdDatas,
                    ...courseCoordinatorData.map((courseElement) => courseElement._id.toString()),
                ];
            if (courseScheduleData && courseScheduleData.length)
                courseIdDatas = [
                    ...courseIdDatas,
                    ...courseScheduleData.map((courseElement) => courseElement.toString()),
                ];
            courseIdDatas = [...new Set(courseIdDatas)].map((courseIdElement) =>
                convertToMongoObjectId(courseIdElement),
            );
        } else {
            // Formate datas from Other Environments
        }
        return courseIdDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseAssessmentFormatting = ({ courseList, courseScheduleResult, dataFrom }) => {
    try {
        const courseDatas = [];
        let courseListsForStaff = [];
        // if (dataFrom === LOCAL) {
        for (courseData of courseList) {
            courseDatas.push({
                courseId: courseData.courseId.toString(),
                courseName: courseData.courseName,
                courseCode: courseData.courseCode,
            });
        }

        const courseScheduleLists = [];
        for (const courseScheduleDatas of courseScheduleResult) {
            courseScheduleLists.push({
                courseId: courseScheduleDatas._course_id.toString(),
                courseName: courseScheduleDatas.course_name,
                courseCode: courseScheduleDatas.course_code,
            });
        }
        const addingCourseAndScheduleDatas = [...courseDatas, ...courseScheduleLists];
        //removing duplicates from course lists by using id
        courseListsForStaff = [
            ...new Map(
                addingCourseAndScheduleDatas.map((element) => [element.courseId, element]),
            ).values(),
        ];
        /* } else {
            // Formate datas from Other Environments
        } */
        return courseListsForStaff;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const subjectAssessmentFormatting = ({ subjectList, dataFrom }) => {
    try {
        const subjectDatas = [];
        // if (dataFrom === LOCAL) {
        for (subjectData of subjectList) {
            subjectDatas.push({
                departmentId: subjectData._department_id,
                subjectId: subjectData._subject_id,
                subjectName: subjectData.subject_name,
            });
        }
        /* } else {
            // Formate datas from Other Environments
        } */
        return subjectDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listSessionFormatting = ({ sessionLists, dataFrom }) => {
    try {
        const sessionDatas = [];
        // if (dataFrom === LOCAL) {
        for (sessionData of sessionLists) {
            sessionDatas.push({
                _id: sessionData._id,
                _session_id: sessionData._session_id,
                delivery_no: sessionData.delivery_no,
                delivery_symbol: sessionData.delivery_symbol,
                delivery_topic: sessionData.delivery_topic,
            });
        }
        /* } else {
            // Formate datas from Other Environments
        } */
        return sessionDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const sloPlusCloFormatting = ({ courseList, sessionIds, dataFrom, digiSessions }) => {
    try {
        const sessionSlos = [];
        // if (dataFrom === LOCAL) {
        if (courseList) {
            const domains = courseList.framework.domains;
            domains.forEach((domainItem) => {
                if (!domainItem.clo) return;
                domainItem.clo.forEach((cloItem) => {
                    if (
                        !cloItem.isDeleted &&
                        !sessionSlos.find(
                            (sloId) => sloId._id.toString() === cloItem._id.toString(),
                        )
                    ) {
                        sessionSlos.push({
                            _id: cloItem._id,
                            name: cloItem.name,
                            no: cloItem.no,
                            type: CLO,
                        });
                    }
                });
            });
            if (!sessionSlos.length) {
                domains.forEach((domainItem) => {
                    if (!domainItem.clo) return;
                    domainItem.clo.forEach((cloItem) => {
                        if (
                            !cloItem.isDeleted &&
                            !sessionSlos.find(
                                (sloId) => sloId._id.toString() === cloItem._id.toString(),
                            )
                        ) {
                            sessionSlos.push({
                                _id: cloItem._id,
                                name: cloItem.name,
                                no: cloItem.no,
                                type: CLO,
                            });
                        }
                    });
                });
            }
        }
        // }

        if (digiSessions) {
            digiSessions.forEach((sessionFlow) => {
                sessionFlow.session_flow_data.forEach((sessionEntry) => {
                    sessionIds.forEach((sessionId) => {
                        if (sessionId.toString() === sessionEntry._session_id.toString()) {
                            sessionEntry.slo.forEach((slo) => {
                                sessionSlos.push({
                                    _id: slo._id,
                                    no: slo.no,
                                    name: slo.name,
                                    type: SLO,
                                });
                            });
                        }
                    });
                });
            });
        } else {
            // Formate datas from Other Environments
        }
        return sessionSlos;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstructorFormatting = ({ userLists, dataFrom }) => {
    try {
        //let userListsFormat;
        // if (dataFrom === LOCAL) {
        userListsFormat = userLists;
        /* } else {
            // Formate datas from Other Environments
        } */
        return userListsFormat;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getCoursesListsForStaff = async ({ courseList, courseScheduleList, staffId, dataFrom }) => {
    try {
        let courseListsForStaff = [];
        // if (dataFrom === LOCAL) {
        const courseLists = [];
        for (const courseDatas of courseList) {
            const participatingSubjects = courseDatas.participating.map((element) => {
                return {
                    subjectId: element._subject_id.toString(),
                    subjectName: element.subject_name,
                };
            });
            const administrationSubjects = [];
            if (courseDatas.administration._subject_id) {
                administrationSubjects.push({
                    subjectId: courseDatas.administration._subject_id.toString(),
                    subjectName: courseDatas.administration.subject_name,
                });
            }
            //adding subject from administration and participating
            const subjectLists = [...participatingSubjects, ...administrationSubjects];
            //removing duplicates from subject lists by using id
            const subjects = [
                ...new Map(subjectLists.map((element) => [element.subjectId, element])).values(),
            ];

            courseLists.push({
                programId: courseDatas._program_id,
                programName: courseDatas.program_name,
                courseId: courseDatas._id.toString(),
                courseName: courseDatas.course_name,
                subjects,
            });
        }
        const addingCourseAndScheduleDatas = [...courseScheduleList, ...courseLists];
        //removing duplicates from course lists by using id
        courseListsForStaff = Object.values(
            addingCourseAndScheduleDatas.reduce((a, v) => {
                if (a[v.courseId]) {
                    a[v.courseId].subjects = [...v.subjects, ...a[v.courseId].subjects];
                    a[v.courseId].subjects = [
                        ...new Map(
                            a[v.courseId].subjects.map((element) => [
                                element.subjectId.toString(),
                                element,
                            ]),
                        ).values(),
                    ];
                } else {
                    a[v.courseId] = v;
                    a[v.courseId].subjects = [
                        ...new Map(
                            a[v.courseId].subjects.map((element) => [
                                element.subjectId.toString(),
                                element,
                            ]),
                        ).values(),
                    ];
                }
                return a;
            }, {}),
        );
        const subjectIds = [];
        courseListsForStaff.forEach((element) => {
            element.subjects.forEach((sub) => {
                subjectIds.push(convertToMongoObjectId(sub.subjectId));
            });
        });
        const department = await departmentSchema.find(
            { 'subject._id': { $in: subjectIds } },
            { 'subject._id': 1 },
        );
        courseListsForStaff.forEach((element, index1) => {
            element.subjects.forEach((sub, index2) => {
                department.forEach((dept) => {
                    dept.subject.forEach((sub1) => {
                        if (sub1._id.toString() === sub.subjectId.toString()) {
                            courseListsForStaff[index1].subjects[index2].departmentId = dept._id;
                        }
                    });
                });
            });
        });
        /* } else {
            // Formate datas from Other Environments
        } */

        return courseListsForStaff;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentGroupListsFormatting = ({
    studentGroupData,
    sessionDeliveryType,
    _course_id,
    term,
    dataFrom,
}) => {
    try {
        let sgStudentList = [];
        const master_group = [];
        // if (dataFrom === LOCAL) {
        if (!studentGroupData) return [];
        for (group of studentGroupData.groups) {
            const courseInd = group.courses.findIndex(
                (ele) => ele._course_id.toString() === _course_id.toString(),
            );
            if (term === group.term && courseInd !== -1) {
                sgStudentList = group.students
                    .filter((ele1) =>
                        ele1.course_group_status.some(
                            (ele2) => _course_id.toString() === ele2._course_id.toString(),
                        ),
                    )
                    .map(({ _student_id, academic_no, name, gender }) => ({
                        _student_id,
                        academic_no,
                        name,
                        gender,
                    }));
                switch (group.group_mode.toString()) {
                    case FYD.toString():
                        for (const foundationGroupSetting of group.group_setting) {
                            for (const foundationGroup of foundationGroupSetting.groups) {
                                master_group.push({
                                    _id: convertToMongoObjectId(),
                                    gender: foundationGroupSetting.gender,
                                    group_no: foundationGroup.group_no,
                                    group_name: foundationGroup.group_name,
                                    delivery_type: foundationGroup.delivery_type,
                                    delivery_symbol: foundationGroup.session_type,
                                    // Need to check schedule
                                    schedule_status: false,
                                });
                            }
                        }
                        break;
                    case ROTATION.toString():
                        for (rotationGroupSetting of group.rotation_group_setting) {
                            let delivery;
                            for (const p of sessionDeliveryType) {
                                for (const i of p.delivery_types) {
                                    if (i.delivery_symbol === rotationGroupSetting.session_type)
                                        delivery = i;
                                }
                            }
                            master_group.push({
                                _id: convertToMongoObjectId(),
                                gender: rotationGroupSetting.gender,
                                rotation: 'yes',
                                rotation_count: rotationGroupSetting.group_no,
                                group_no: rotationGroupSetting.group_no,
                                group_name: rotationGroupSetting.group_name,
                                delivery_type: rotationGroupSetting.delivery_type,
                                delivery_symbol: rotationGroupSetting.session_type,
                                delivery_name: delivery ? delivery.delivery_name : null,
                                // Need to check schedule
                                schedule_status: false,
                                session_group: [],
                            });
                        }
                        break;
                    default:
                        break;
                }
                for (const setting of group.courses[courseInd].setting) {
                    if (group.group_mode !== COURSES) {
                        const master_group_loc = master_group.findIndex(
                            (ele) =>
                                ele.group_no.toString() === setting._group_no.toString() &&
                                ele.gender === setting.gender,
                        );
                        const ind = setting.session_setting.findIndex(
                            (ele) => ele.delivery_type === setting.session_setting[0].delivery_type,
                        );
                        if (ind !== -1 && master_group_loc !== -1) {
                            master_group[master_group_loc].session_group = (
                                setting.gender === BOTH
                                    ? sgStudentList.length
                                    : sgStudentList.filter((ele) => ele.gender === setting.gender)
                                          .length !== 0
                            )
                                ? setting.session_setting[ind].groups.map((ele) => {
                                      return {
                                          _id: ele._id,
                                          group_no: ele.group_no,
                                          group_name: ele.group_name,
                                          _student_ids: ele._student_ids,
                                      };
                                  })
                                : [];
                            if (group.group_mode === 'rotation') {
                                let delivery;
                                for (const sessionDeliveryElement of sessionDeliveryType) {
                                    for (const deliveryTypeElement of sessionDeliveryElement.delivery_types) {
                                        if (
                                            deliveryTypeElement.delivery_symbol ===
                                            setting.session_setting[ind].session_type
                                        )
                                            delivery = deliveryTypeElement;
                                    }
                                }
                                master_group[master_group_loc].delivery_symbol =
                                    delivery.delivery_symbol;
                                master_group[master_group_loc].delivery_name =
                                    delivery.delivery_name;
                            }
                        }
                    } else {
                        for (setting_element of setting.session_setting) {
                            if (setting_element.delivery_type) {
                                let delivery;
                                for (const sessionDeliveryElement of sessionDeliveryType) {
                                    for (const deliveryTypeElement of sessionDeliveryElement.delivery_types) {
                                        if (
                                            deliveryTypeElement.delivery_symbol ===
                                            setting_element.session_type
                                        )
                                            delivery = deliveryTypeElement;
                                    }
                                }
                                const group_name =
                                    setting.gender === BOTH
                                        ? 'MFG-1'
                                        : setting.gender === MALE
                                        ? 'MG-1'
                                        : 'FG-1';
                                master_group.push({
                                    _id: convertToMongoObjectId(),
                                    gender: setting.gender,
                                    group_no: 1,
                                    group_name,
                                    delivery_type: setting_element.delivery_type,
                                    delivery_symbol: setting_element.session_type,
                                    delivery_name: delivery ? delivery.delivery_name : null,
                                    // Need to check schedule
                                    schedule_status: false,
                                    session_group: (
                                        setting.gender === BOTH
                                            ? sgStudentList.length
                                            : sgStudentList.filter(
                                                  (ele) => ele.gender === setting.gender,
                                              ).length !== 0
                                    )
                                        ? setting_element.groups.map((ele) => {
                                              return {
                                                  _id: ele._id,
                                                  group_no: ele.group_no,
                                                  group_name: ele.group_name,
                                                  _student_ids: ele._student_ids,
                                              };
                                          })
                                        : [],
                                });
                            }
                        }
                    }
                }
            }
        }
        master_group.forEach((element, index) => {
            let totalStudents = 0;
            if (element && element.session_group)
                element.session_group.forEach((sess_group_ele, indSessG) => {
                    const studentArr = [];
                    sess_group_ele._student_ids.forEach((student_id) => {
                        const userInd = sgStudentList.findIndex(
                            (ele) => ele._student_id.toString() === student_id.toString(),
                        );
                        if (userInd != -1) studentArr.push(sgStudentList[userInd]);
                    });
                    delete master_group[index].session_group[indSessG]._student_ids;
                    master_group[index].session_group[indSessG].students = studentArr;
                    master_group[index].session_group[indSessG].studentsLength = studentArr.length;
                    totalStudents += studentArr.length;
                });
            master_group[index].totalStudents = totalStudents;
        });
        /* } else {
            // Formate datas from Other Environments
        } */
        return master_group;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentCourseGroupListsFormatting = ({
    studentGroupData,
    sessionDeliveryType,
    _course_id,
    term,
    dataFrom,
    deliverySymbol,
}) => {
    try {
        let sgStudentList = [];
        const master_group = [];
        // if (dataFrom === LOCAL) {
        if (!studentGroupData) return [];
        for (group of studentGroupData.groups) {
            const courseInd = group.courses.findIndex(
                (ele) => ele._course_id.toString() === _course_id.toString(),
            );
            if (term === group.term && courseInd !== -1) {
                sgStudentList = group.students
                    .filter((ele1) =>
                        ele1.course_group_status.some(
                            (ele2) => _course_id.toString() === ele2._course_id.toString(),
                        ),
                    )
                    .map(({ _student_id, academic_no, name, gender }) => ({
                        _student_id,
                        academic_no,
                        name,
                        gender,
                    }));
                switch (group.group_mode.toString()) {
                    case FYD.toString():
                        for (const foundationGroupSetting of group.group_setting) {
                            for (const foundationGroup of foundationGroupSetting.groups) {
                                master_group.push({
                                    _id: foundationGroup._id,
                                    gender: foundationGroupSetting.gender,
                                    group_no: foundationGroup.group_no,
                                    group_name: foundationGroup.group_name,
                                    delivery_type: foundationGroup.delivery_type,
                                    delivery_symbol: foundationGroup.session_type,
                                    // Need to check schedule
                                    schedule_status: false,
                                });
                            }
                        }
                        break;
                    case ROTATION.toString():
                        for (const rotationGroupSetting of group.rotation_group_setting) {
                            master_group.push({
                                _id: rotationGroupSetting._id,
                                gender: rotationGroupSetting.gender,
                                rotation: 'yes',
                                rotation_count: rotationGroupSetting.group_no,
                                group_no: rotationGroupSetting.group_no,
                                group_name: rotationGroupSetting.group_name,
                                delivery_type: rotationGroupSetting.delivery_type,
                                delivery_symbol: rotationGroupSetting.session_type,
                                // Need to check schedule
                                schedule_status: false,
                                session_group: [],
                            });
                        }
                        break;
                    default:
                        break;
                }
                for (const setting of group.courses[courseInd].setting) {
                    if (group.group_mode !== COURSES) {
                        const master_group_loc = master_group.findIndex(
                            (ele) =>
                                ele.group_no.toString() === setting._group_no.toString() &&
                                ele.gender === setting.gender,
                        );
                        const ind = setting.session_setting.findIndex(
                            (ele) => ele.delivery_type === setting.session_setting[0].delivery_type,
                        );
                        if (ind !== -1 && master_group_loc !== -1) {
                            master_group[master_group_loc].session_group = (
                                setting.gender === BOTH
                                    ? sgStudentList.length
                                    : sgStudentList.filter((ele) => ele.gender === setting.gender)
                                          .length !== 0
                            )
                                ? setting.session_setting[ind].groups.map((ele) => {
                                      return {
                                          _id: ele._id,
                                          group_no: ele.group_no,
                                          group_name: ele.group_name,
                                          _student_ids: ele._student_ids,
                                      };
                                  })
                                : [];
                        }
                    } else {
                        for (setting_element of setting.session_setting) {
                            if (!deliverySymbol || deliverySymbol.length === 0) {
                                if (setting_element.delivery_type) {
                                    let delivery;
                                    for (const p of sessionDeliveryType) {
                                        for (const i of p.delivery_types) {
                                            if (i.delivery_symbol === setting_element.session_type)
                                                delivery = i;
                                        }
                                    }
                                    setting_element.groups.forEach((values, index) => {
                                        const group_name =
                                            setting.gender === BOTH
                                                ? `MFG-${Number(index) + 1}`
                                                : setting.gender === MALE
                                                ? `MG-${Number(index) + 1}`
                                                : `FG-${Number(index) + 1}`;
                                        master_group.push({
                                            _id: setting._id,
                                            gender: setting.gender,
                                            group_no: Number(index) + 1,
                                            group_name,
                                            delivery_type: setting_element.delivery_type,
                                            delivery_symbol: setting_element.session_type,
                                            delivery_name: delivery.delivery_name,
                                            // Need to check schedule
                                            schedule_status: false,
                                            session_group: (
                                                setting.gender === BOTH
                                                    ? sgStudentList.length
                                                    : sgStudentList.filter(
                                                          (ele) => ele.gender === setting.gender,
                                                      ).length !== 0
                                            )
                                                ? setting_element.groups[index]
                                                : [],
                                        });
                                    });
                                }
                            } else {
                                deliverySymbol.forEach((val) => {
                                    if (setting_element.delivery_type) {
                                        if (val === setting_element.session_type) {
                                            setting_element.groups.forEach((values, index) => {
                                                const group_name =
                                                    setting.gender === BOTH
                                                        ? `MFG${setting_element.session_type}-${
                                                              Number(index) + 1
                                                          }`
                                                        : setting.gender === MALE
                                                        ? `MG${setting_element.session_type}-${
                                                              Number(index) + 1
                                                          }`
                                                        : `FG${setting_element.session_type}-${
                                                              Number(index) + 1
                                                          }`;
                                                master_group.push({
                                                    _id: setting._id,
                                                    gender: setting.gender,
                                                    group_no: Number(index) + 1,
                                                    group_name,
                                                    delivery_type: setting_element.delivery_type,
                                                    delivery_symbol: setting_element.session_type,
                                                    session_group: (
                                                        setting.gender === BOTH
                                                            ? sgStudentList.length
                                                            : sgStudentList.filter(
                                                                  (ele) =>
                                                                      ele.gender === setting.gender,
                                                              ).length !== 0
                                                    )
                                                        ? setting_element.groups[index]
                                                        : [],
                                                });
                                            });
                                        }
                                    }
                                });
                            }
                        }
                    }
                }
            }
        }
        master_group.forEach((element, index) => {
            let totalStudents = 0;
            const students = [];
            console.log(element.session_group);
            if (element && element.session_group) {
                const studentArr = [];
                element.session_group._student_ids.forEach((student_id) => {
                    const userInd = sgStudentList.findIndex(
                        (ele) => ele._student_id.toString() === student_id.toString(),
                    );
                    if (userInd != -1) {
                        studentArr.push(sgStudentList[userInd]);
                        students.push(sgStudentList[userInd]);
                    }
                });
                delete master_group[index].session_group._student_ids;
                master_group[index].session_group.students = studentArr;
                master_group[index].session_group.studentsLength = studentArr.length;
                totalStudents += studentArr.length;
            }
            master_group[index].totalStudents = totalStudents;
            master_group[index].students = students;
            delete master_group[index].session_group;
        });
        /* } else {
            // Formate datas from Other Environments
        } */
        return master_group;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const roleListFormatting = ({ roleAssignedProgramList, dataFrom }) => {
    try {
        /* let roleDatas = [];
        if (dataFrom === LOCAL) { */
        roleDatas = roleAssignedProgramList;
        /* } else {
            // Formate datas from Other Environments
        } */
        return roleDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const programRoleBasedFormatting = async ({
    roleAssignedProgramList,
    courseProgramList,
    dataFrom,
}) => {
    try {
        let programDatas = [];
        // if (dataFrom === LOCAL) {
        let roleAssignedProgramDatas = [];
        const uniqueProgramIds = new Map();
        for (const roleAssignedProgramData of roleAssignedProgramList) {
            for (const rolesData of roleAssignedProgramData.roles) {
                const programRoles = [];
                let programIndex = 0;
                for (const programElement of rolesData.program) {
                    const programId = programElement._program_id.toString();
                    if (!uniqueProgramIds.has(programId)) {
                        uniqueProgramIds.set(programId, programIndex);
                        programRoles.push({
                            isAdmin: rolesData.isAdmin,
                            programId: programElement._program_id,
                            programName: programElement.program_name,
                        });
                        programIndex++;
                    } else {
                        if (rolesData.isAdmin) {
                            programRoles[uniqueProgramIds.get(programId)] = {
                                isAdmin: rolesData.isAdmin,
                                programId: programElement._program_id,
                                programName: programElement.program_name,
                            };
                        }
                    }
                }
                roleAssignedProgramDatas = [...programRoles, ...roleAssignedProgramDatas];
            }
        }
        const getAssignedProgramIds = roleAssignedProgramDatas.map((element) => element.programId);
        let courseProgramListIds = courseProgramList.map((element) => element._program_id);
        courseProgramListIds = courseProgramListIds.filter(
            (element) =>
                !getAssignedProgramIds.some(
                    (elementEntry) => element.toString() === elementEntry.toString(),
                ),
        );
        const programNameList = await programSchema
            .find(
                {
                    ...query,
                    _id: { $in: courseProgramListIds },
                },
                { name: 1 },
            )
            .lean();
        const courseProgramsLists = programNameList.map((element) => {
            return { programName: element.name, programId: element._id, isAdmin: false };
        });

        programDatas = [...new Set([...courseProgramsLists, ...roleAssignedProgramDatas])];
        /*  } else {
            // Formate datas from Other Environments
        } */
        return programDatas;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getTaxonomyLists = ({ taxonomyList, dataFrom }) => {
    try {
        /* let taxonomyLists = [];
        if (dataFrom === LOCAL) { */
        taxonomyLists = taxonomyList;
        /* } else {
            // Formate datas from Other Environments
        } */
        return taxonomyLists;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    programFormatting,
    yearLevelFormatting,
    courseFormatting,
    courseSLOFormatting,
    courseCLOFormatting,
    coursePLOFormatting,
    courseStudentFormatting,
    programLevelsStudentFormatting,
    programWithTermFormatting,
    courseLevelTermFormatting,
    yearLevelCourseFormatting,
    programPLOFormatting,
    courseAssessmentFormatting,
    subjectAssessmentFormatting,
    coursesCLOsFormatting,
    InstitutionCalendarsFormatting,
    userProgramIdFormatting,
    userCourseIdFormatting,
    userProgramIdsFormatting,
    listSessionFormatting,
    sloPlusCloFormatting,
    getStudentGroupListsFormatting,
    getStudentCourseGroupListsFormatting,
    getInstructorFormatting,
    getCoursesListsForStaff,
    roleListFormatting,
    programRoleBasedFormatting,
    getTaxonomyLists,
};
