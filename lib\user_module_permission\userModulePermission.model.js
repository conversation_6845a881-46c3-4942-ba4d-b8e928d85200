const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    USER_MODULE_PERMISSION,
    USER,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../utility/constants');

const courseSchema = new Schema({
    _course_id: { type: ObjectId, ref: DIGI_COURSE },
    courses_name: { type: String },
    courses_number: { type: String },
});

const rotationCourseSchema = new Schema({
    rotation_count: { type: Number },
    course: [courseSchema],
});

const levelSchema = new Schema({
    term: { type: String },
    year: { type: String },
    level_no: { type: String },
    curriculum: { type: String },
    rotation: { type: String, enum: ['yes', 'no'] },
    course: [courseSchema],
    rotation_course: [rotationCourseSchema],
});

const selectedCoursesSchema = new Schema({
    _program_id: { type: ObjectId, ref: DIGI_PROGRAM },
    _institution_calendar_id: { type: ObjectId, ref: INSTITUTION_CALENDAR },
    level: [levelSchema],
});

const userModulePermissionSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        moduleName: { type: String },
        manageUser: { type: ObjectId },
        userId: { type: ObjectId, ref: USER, required: true },
        selectedCourses: [selectedCoursesSchema],
        selectedPrograms: [{ type: ObjectId, ref: DIGI_PROGRAM }],
    },
    { timestamps: true },
);

module.exports = model(USER_MODULE_PERMISSION, userModulePermissionSchema);
