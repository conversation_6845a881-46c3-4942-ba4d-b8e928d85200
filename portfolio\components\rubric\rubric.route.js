const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const RubricController = require('./rubric.controller');
const {
    createRubricSchema,
    getRubricsSchema,
    updateRubricSchema,
    deleteRubricSchema,
    createGlobalRubricSchema,
} = require('./rubric.validation');

router.post('/', validate(createRubricSchema), catchAsync(RubricController.createRubric));

router.get('/', validate(getRubricsSchema), catchAsync(RubricController.getRubrics));

router.put('/', validate(updateRubricSchema), catchAsync(RubricController.updateRubric));

router.delete('/', validate(deleteRubricSchema), catchAsync(RubricController.deleteRubric));

router.post(
    '/global',
    validate(createGlobalRubricSchema),
    catchAsync(RubricController.createGlobalRubric),
);

module.exports = router;
