const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const getProgramListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

const getProgramCourseListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectId.required(),
        programId: objectId.required(),
    }),
}).unknown(true);

module.exports = {
    getProgramListSchema,
    getProgramCourseListSchema,
};
