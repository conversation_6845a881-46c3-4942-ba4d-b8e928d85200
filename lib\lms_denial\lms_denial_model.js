const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = Schema.Types.ObjectId;
const {
    LMS_DENIAL,
    INSTITUTION_CALENDAR,
    DIGI_COURSE,
    DIGI_PROGRAM,
    BATCH: { REGULAR, INTERIM, BOTH },
    USER,
    CUMULATIVE,
    INDIVIDUAL,
    COURSE_WISE,
    STUDENT_WISE,
} = require('../utility/constants');
const lmsDenialSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        _institution_calendar_id: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        programId: { type: ObjectId, ref: DIGI_PROGRAM, required: true },
        courseId: { type: ObjectId, ref: DIGI_COURSE, required: true },
        term: { type: String },
        denialCondition: { type: String, enum: [CUMULATIVE, INDIVIDUAL] },
        yearNo: { type: String },
        levelNo: { type: String },
        rotation: { type: String },
        rotationCount: { type: Number },
        studentId: {
            type: ObjectId,
            ref: USER,
        },
        typeWiseUpdate: { type: String, enum: [COURSE_WISE, STUDENT_WISE] },
        absencePercentage: { type: Number },
        categoryWisePercentage: [
            {
                categoryId: ObjectId,
                categoryName: String,
                percentage: Number,
            },
        ],
        updateBy: {
            type: ObjectId,
            ref: USER,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(LMS_DENIAL, lmsDenialSchema);
