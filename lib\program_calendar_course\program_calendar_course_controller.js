const common_files = require('../utility/common');
const constant = require('../utility/constants');
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const course = require('mongoose').model(constant.COURSE);
const digi_course = require('mongoose').model(constant.DIGI_COURSE);
const calendar_event = require('mongoose').model(constant.CALENDAR_EVENT);
const base_control = require('../base/base_controller');
// const program_formate = require('./program_calendar_course_formate');
const ObjectId = common_files.convertToMongoObjectId;
const moment = require('moment');
const { clearItem, allProgramCalendarDatas } = require('../../service/cache.service');
const { updateProgramCalendarRedisData } = require('../utility/utility.service');
// Updating Program Calendar Flat Caching Data
const updateProgramCalendarFlatCacheData = async () => {
    clearItem('allProgramCalendar');
    await allProgramCalendarDatas();
};

exports.course_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const course_id = ObjectId(req.params.course);
    const aggre = [
        { $match: { _id: calendar_id, isDeleted: false } },
        { $unwind: '$level' },
        { $unwind: '$level.course' },
        { $match: { 'level.course._course_id': course_id } },
        // { $lookup: { from: constant.COURSE, localField: 'level.course._course_id', foreignField: '_id', as: 'level.course.course_details' } },
        {
            $lookup: {
                from: constant.CALENDAR_EVENT,
                localField: 'level.course._event_id',
                foreignField: '_id',
                as: 'level.course.event_details',
            },
        },
        // { $unwind: '$level.course.course_details' },
        {
            $group: {
                _id: '$_id',
                level: { $first: '$level.level_no' },
                course_name: { $first: '$level.course.courses_name' },
                course_type: { $first: '$level.course.model' },
                credit_hours: { $first: '$level.course.credit_hours' },
                start_date: { $first: '$level.course.start_date' },
                end_date: { $first: '$level.course.end_date' },
                color_code: { $first: '$level.course.color_code' },
                events: { $first: '$level.course.event_details' },
            },
        },
        {
            $project: {
                'events.isDeleted': 0,
                'events.isActive': 0,
                'events.event_calendar': 0,
                'events._calendar_id': 0,
                'events.review': 0,
                'events.createdAt': 0,
                'events.updatedAt': 0,
                'events.__v': 0,
            },
        },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_COURSE_DETAILS'),
            doc.data /* program_formate.program_calendar_course_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.rotation_course_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const course_id = ObjectId(req.params.course);
    const aggre = [
        { $match: { _id: calendar_id, isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$level.rotation_course', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.rotation': 'yes' } },
        { $match: { 'level.rotation_course.rotation_count': parseInt(req.params.rotation_no) } },
        { $unwind: { path: '$level.rotation_course.course', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.rotation_course.course._course_id': course_id } },
        // { $lookup: { from: constant.COURSE, localField: 'level.rotation_course.course._course_id', foreignField: '_id', as: 'course_details' } },
        {
            $lookup: {
                from: constant.CALENDAR_EVENT,
                localField: 'level.rotation_course.course._event_id',
                foreignField: '_id',
                as: 'event_details',
            },
        },
        { $unwind: { path: '$course_details', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                year: { $first: '$level.year' },
                level: { $first: '$level.level_no' },
                course_name: { $first: '$level.rotation_course.course.courses_name' },
                course_type: { $first: '$level.rotation_course.course.model' },
                credit_hours: { $first: '$level.rotation_course.course.credit_hours' },
                start_date: { $first: '$level.rotation_course.course.start_date' },
                end_date: { $first: '$level.rotation_course.course.end_date' },
                color_code: { $first: '$level.rotation_course.course.color_code' },
                events: { $first: '$event_details' },
            },
        },
        {
            $project: {
                'events.isDeleted': 0,
                'events.isActive': 0,
                'events.event_calendar': 0,
                'events._calendar_id': 0,
                'events.review': 0,
                'events.createdAt': 0,
                'events.updatedAt': 0,
                'events.__v': 0,
            },
        },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_ROTATION_COURSE_DETAILS'),
            doc.data /* program_formate.program_calendar_course_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_UNABLE_TO_FIND_COURSE'),
            req.t('ERROR_UNABLE_TO_FIND_COURSE:') + doc.data,
        );
    }
};

exports.course_list_level_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false },
        { 'level.level_no': 1, 'level._program_id': 1 },
    );
    if (calendar_check.status) {
        const aggre = [
            { $match: { _id: calendar_id, isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.level_no': req.params.level_no } },
            { $unwind: { path: '$level.course', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: constant.CALENDAR_EVENT,
                    localField: 'level._event_id',
                    foreignField: '_id',
                    as: 'level.event_details',
                },
            },
            {
                $lookup: {
                    from: constant.CALENDAR_EVENT,
                    localField: 'level.course._event_id',
                    foreignField: '_id',
                    as: 'level.course.event_details',
                },
            },
            {
                $group: {
                    _id: '$_id',
                    year: { $first: '$level.year' },
                    level: { $first: '$level.level_no' },
                    event: { $first: '$level.event_details' },
                    course: { $push: '$level.course' },
                },
            },
            // { $project: { 'events.isDeleted': 0, 'events.isActive': 0, 'events.event_calendar': 0, 'events._calendar_id': 0, 'events.review': 0, 'events.createdAt': 0, 'events.updatedAt': 0, 'events.__v': 0 } }
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_LIST'),
                doc.data /* program_formate.program_calendar_course_ID(doc.data[0]) */,
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.course_list_year_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false },
        { 'level.level_no': 1, 'level._program_id': 1 },
    );
    if (calendar_check.status) {
        const aggre = [
            { $match: { _id: calendar_id, isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.year': req.params.year_no } },
            { $unwind: { path: '$level.course', preserveNullAndEmptyArrays: true } },
            {
                $lookup: {
                    from: constant.CALENDAR_EVENT,
                    localField: 'level._event_id',
                    foreignField: '_id',
                    as: 'level_event_details',
                },
            },
            {
                $lookup: {
                    from: constant.CALENDAR_EVENT,
                    localField: 'level.course._event_id',
                    foreignField: '_id',
                    as: 'level.course.event_details',
                },
            },
            {
                $group: {
                    _id: '$level.level_no',
                    id: { $first: '$_id' },
                    year: { $first: '$level.year' },
                    level: { $first: '$level.level_no' },
                    level_event: { $first: '$level_event_details' },
                    course: { $push: '$level.course' },
                },
            },
            { $sort: { level: 1 } },
            {
                $addFields: {
                    level: { level_event: '$level_event', level_no: '$level', course: '$course' },
                },
            },
            {
                $group: {
                    _id: '$id',
                    year: { $first: '$year' },
                    level: { $push: '$level' },
                },
            },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_LIST'),
                doc.data /* program_formate.program_calendar_course_ID(doc.data[0]) */,
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.insert = async (req, res) => {
    try {
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: ObjectId(req.body._calendar_id), isDeleted: false },
            {
                'level.course': 1,
                'level.level_no': 1,
                'level.term': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        const course_checks = await base_control.get(
            digi_course,
            { _id: req.body._course_id, isDeleted: false },
            {},
        );
        if (!course_checks.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        let event_checks = { status: true, data: [] };
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            event_checks = await base_control.check_id(calendar_event, {
                _id: { $in: req.body._event_id },
                isDeleted: false,
            });
            if (!event_checks.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('EVENTS_NOT_FOUND'),
                            req.t('EVENTS_NOT_FOUND'),
                        ),
                    );
        }
        const level_pos = calendar_check.data.level.findIndex(
            (i) =>
                i.level_no === req.body.level_no &&
                i.term.toLowerCase() === req.body.batch.toLowerCase(),
        );
        if (level_pos === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('LEVEL_NUMBER_IS_NOT_MATCHING_ALONG_BATCH'),
                        req.t('LEVEL_NUMBER_IS_NOT_MATCHING_ALONG_BATCH'),
                    ),
                );
        const course_pos = calendar_check.data.level[level_pos].course.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_pos !== -1)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_LEVEL'),
                        req.t('THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_LEVEL'),
                    ),
                );
        const events_data = [];
        if (event_checks.data.length !== 0) {
            event_checks.data.forEach((element) => {
                events_data.push({
                    _event_id: element._id,
                    event_type: element.event_type,
                    event_name: element.event_name.first_language,
                    event_date: element.event_date,
                    start_time: element.start_time,
                    end_time: element.end_time,
                    end_date: element.end_date,
                });
            });
        }
        const credit_hours = [];
        for (credit_element of course_checks.data.credit_hours) {
            credit_hours.push({
                type_name: credit_element.type_name,
                type_symbol: credit_element.type_symbol,
                credit_hours: credit_element.credit_hours,
            });
        }
        const objs = {
            $push: {
                'level.$[i].course': {
                    _course_id: req.body._course_id,
                    courses_name: course_checks.data.course_name,
                    courses_number: course_checks.data.course_code,
                    model: course_checks.data.course_type,
                    credit_hours,
                    start_date: req.body.start_date,
                    end_date: req.body.end_date,
                    courses_events: events_data,
                    color_code: req.body.color_code,
                    versionNo: course_checks.data.versionNo || 1,
                    versioned: course_checks.data.versioned || false,
                    versionName: course_checks.data.versionName || '',
                },
            },
        };
        const cond = { _id: req.body._calendar_id, isDeleted: false };
        const filter = {
            arrayFilters: [{ 'i.level_no': req.body.level_no, 'i.term': req.body.batch }],
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            objs,
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_ADD_COURSE_SO_RETRY'),
                        req.t('ERROR_UNABLE_TO_ADD_COURSE_SO_RETRY'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.update = async (req, res) => {
    try {
        let checks = { status: true };
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: req.body._calendar_id, isDeleted: false, 'level.term': req.body.batch },
            {
                'level.level_no': 1,
                'level.rotation_course': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.course': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        const objs = {};
        if (req.body.start_date !== undefined && req.body.start_date.length !== 0) {
            Object.assign(objs, { 'level.$[i].course.$[j].start_date': req.body.start_date });
        }
        if (req.body.end_date !== undefined && req.body.end_date.length !== 0) {
            Object.assign(objs, { 'level.$[i].course.$[j].end_date': req.body.end_date });
        }
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            checks = await base_control.check_id(calendar_event, {
                _id: { $in: req.body._event_id },
                isDeleted: false,
            });
            if (!checks.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('EVENTS_NOT_FOUND'),
                            req.t('EVENTS_NOT_FOUND'),
                        ),
                    );
            const events_data = [];
            checks.data.forEach((element) => {
                events_data.push({
                    _event_id: element._id,
                    event_type: element.event_type,
                    event_name: element.event_name.first_language,
                    event_date: element.event_date,
                    start_time: element.start_time,
                    end_time: element.end_time,
                    end_date: element.end_date,
                });
            });
            Object.assign(objs, { 'level.$[i].course.$[j].courses_events': events_data });
        }
        if (req.body._event_id !== undefined && req.body._event_id.length === 0) {
            const level_no = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
            );
            const course_no = calendar_check.data.level[level_no].course.findIndex(
                (j) => j._course_id.toString() === req.body._course_id.toString(),
            );
            const event_ids = calendar_check.data.level[level_no].course[
                course_no
            ].courses_events.map((k) => k._event_id);
            Object.assign(objs, { 'level.$[i].course.$[j].courses_events': [] });
            await base_control.update_push_pull_many(
                calendar_event,
                { _id: event_ids, event_calendar: 'course' },
                { $set: { isDeleted: true } },
            );
        }
        if (req.body.color_code !== undefined && req.body.color_code.length !== 0) {
            Object.assign(objs, { 'level.$[i].course.$[j].color_code': req.body.color_code });
        }
        const cond = { _id: req.body._calendar_id, isDeleted: false };
        const filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                { 'j._course_id': req.body._course_id },
            ],
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $set: objs },
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME'),
                        req.t('UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_UPDATED_SUCCESSFULLY'),
                    req.t('UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.delete = async (req, res) => {
    try {
        let doc = {};
        let cond = {};
        let objs = {};
        let filter = {};
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: req.body._calendar_id, isDeleted: false, 'level.term': req.body.batch },
            {
                'level.level_no': 1,
                'level.rotation_course': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.course': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        if (req.body.rotation_no === undefined) {
            const level_no = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
            );
            const course_no = calendar_check.data.level[level_no].course.findIndex(
                (j) => j._course_id.toString() === req.body._course_id.toString(),
            );
            if (course_no !== -1) {
                const event_ids = calendar_check.data.level[level_no].course[
                    course_no
                ].courses_events.map((k) => k._event_id);
                cond = { _id: req.body._calendar_id };
                objs = { 'level.$[i].course': { _course_id: req.body._course_id } };
                filter = {
                    arrayFilters: [{ 'i.level_no': req.body.level_no, 'i.term': req.body.batch }],
                };
                doc = await base_control.update_condition_array_filter(
                    program_calendar,
                    cond,
                    { $pull: objs },
                    filter,
                );
                await base_control.update_push_pull_many(
                    calendar_event,
                    { _id: event_ids, event_calendar: 'course' },
                    { $set: { isDeleted: true } },
                );
            } else {
                doc = { status: false };
            }
        } else {
            const level_no = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
            );
            const rot_count =
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].rotation_count;
            cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
            for (let k = 1; k <= rot_count; k++) {
                const course_no = calendar_check.data.level[level_no].rotation_course[
                    k
                ].course.findIndex(
                    (j) => j._course_id.toString() === req.body._course_id.toString(),
                );
                const event_ids = calendar_check.data.level[level_no].rotation_course[k].course[
                    course_no
                ].courses_events.map((l) => l._event_id);
                filter = {
                    arrayFilters: [
                        { 'j.rotation_count': k },
                        { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                    ],
                };
                objs = {
                    'level.$[i].rotation_course.$[j].course': { _course_id: req.body._course_id },
                };
                doc = await base_control.update_condition_array_filter(
                    program_calendar,
                    cond,
                    { $pull: objs },
                    filter,
                );
                await base_control.update_push_pull_many(
                    calendar_event,
                    { _id: event_ids, event_calendar: 'course' },
                    { $set: { isDeleted: true } },
                );
            }
        }
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_DELETE_COURSE_RETRY'),
                        req.t('ERROR_UNABLE_TO_DELETE_COURSE_RETRY'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_DELETED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_COURSE_DELETED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
exports.rotation_manual_course_delete = async (req, res) => {
    try {
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: req.body._calendar_id, isDeleted: false, 'level.term': req.body.batch },
            {
                'level.level_no': 1,
                'level.rotation_course': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const level_no = calendar_check.data.level.findIndex(
            (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
        );
        const rot_no = calendar_check.data.level[level_no].rotation_course.findIndex(
            (i) => i.rotation_count.toString() === req.body.rotation_no.toString(),
        );
        const course_no = calendar_check.data.level[level_no].rotation_course[
            rot_no
        ].course.findIndex((j) => j._course_id.toString() === req.body._course_id.toString());
        const event_ids = calendar_check.data.level[level_no].rotation_course[rot_no].course[
            course_no
        ].courses_events.map((k) => k._event_id);
        const cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
        const filter = {
            arrayFilters: [
                { 'j.rotation_count': req.body.rotation_no },
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
            ],
        };
        const objs = {
            'level.$[i].rotation_course.$[j].course': { _course_id: req.body._course_id },
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $pull: objs },
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        await base_control.update_push_pull_many(
            calendar_event,
            { _id: event_ids, event_calendar: 'course' },
            { $set: { isDeleted: true } },
        );
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_DELETE_COURSE_RETRY'),
                        req.t('UNABLE_TO_DELETE_COURSE_RETRY'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_DELETED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_COURSE_DELETED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
exports.delete_event = async (req, res) => {
    let doc = {};
    let cond = {};
    let objs = {};
    let filter = {};
    cond = { _id: req.body._calendar_id };
    if (req.body.rotation_no === undefined) {
        objs = { 'level.$[i].course.$[j].courses_events': { _event_id: req.body._event_id } };
        filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                { 'j._course_id': req.body._course_id },
            ],
        };
        // doc = await base_control.update_condition_array_filter(program_calendar, cond, { $pull: objs }, filter);
    } else {
        // cond = { _id: req.body._calendar_id };
        objs = {
            'level.$[i].rotation_course.$[k].course.$[j].courses_events': {
                _event_id: req.body._event_id,
            },
        };
        filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                { 'k.rotation_count': req.body.rotation_no },
                { 'j._course_id': req.body._course_id },
            ],
        };
    }
    doc = await base_control.update_condition_array_filter(
        program_calendar,
        cond,
        { $pull: objs },
        filter,
    );
    updateProgramCalendarFlatCacheData();
    await updateProgramCalendarRedisData({
        _id: req.body._calendar_id,
        term: req.body.batch,
        level_no: req.body.level_no,
    });
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_COURSE_EVENT_DELETED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_UNABLE_TO_DELETE_EVENT_RETRY_SAME'),
            doc.data,
        );
    }
};

exports.update_rotation_course = async (req, res) => {
    let checks = { status: true, data: [] };
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: req.body._calendar_id, isDeleted: false, 'level.term': req.body.batch },
        {
            'level.level_no': 1,
            'level.rotation_course': 1,
            'level.rotation': 1,
            'level.rotation_count': 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.term': 1,
            'level.course': 1,
        },
    );
    if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
        checks = await base_control.check_id(calendar_event, {
            _id: { $in: req.body._event_id },
            isDeleted: false,
        });
    }
    if (calendar_check.status && checks.status) {
        const objs = {};

        // if (req.body.start_date !== undefined && req.body.start_date.length !== 0) {
        //     Object.assign(objs, { 'level.$[i].rotation_course.$[k].course.$[j].start_date': req.body.start_date });
        // }
        // if (req.body.end_date !== undefined && req.body.end_date.length !== 0) {
        //     Object.assign(objs, { 'level.$[i].rotation_course.$[k].course.$[j].end_date': req.body.end_date });
        // }

        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            if (checks.data.length !== 0) {
                const events_data = [];
                checks.data.forEach((element) => {
                    events_data.push({
                        _event_id: element._id,
                        event_type: element.event_type,
                        event_name: element.event_name.first_language,
                        event_date: element.event_date,
                        start_time: element.start_time,
                        end_time: element.end_time,
                        end_date: element.end_date,
                    });
                });
                Object.assign(objs, {
                    'level.$[i].rotation_course.$[k].course.$[j].courses_events': events_data,
                });
            }
        }
        if (req.body._event_id !== undefined && req.body._event_id.length === 0) {
            const level_no = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
            );
            const rot_no = calendar_check.data.level[level_no].rotation_course.findIndex(
                (i) => i.rotation_count.toString() === req.body.rotation_count.toString(),
            );
            const course_no = calendar_check.data.level[level_no].rotation_course[
                rot_no
            ].course.findIndex((j) => j._course_id.toString() === req.body._course_id.toString());
            const event_ids = calendar_check.data.level[level_no].rotation_course[rot_no].course[
                course_no
            ].courses_events.map((k) => k._event_id);
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].courses_events': [],
            });
            await base_control.update_push_pull_many(
                calendar_event,
                { _id: event_ids, event_calendar: 'course' },
                { $set: { isDeleted: true } },
            );
        }
        if (req.body.color_code !== undefined && req.body.color_code.length !== 0) {
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].color_code': req.body.color_code,
            });
        }
        if (req.body._batch_course_id !== undefined && req.body._batch_course_id.length !== 0) {
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j]._batch_course_id':
                    req.body._batch_course_id,
            });
        }
        const cond = { _id: ObjectId(req.body._calendar_id), isDeleted: false };
        const filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                { 'j._course_id': ObjectId(req.body._course_id) },
                { 'k.rotation_count': req.body.rotation_count },
            ],
        };
        // console.log(cond, ' ', { $set: objs }, ' ', filter);
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $set: objs },
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_UPDATED_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME'),
                doc.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.update_rotation_course_manual = async (req, res) => {
    try {
        let checks = { status: true, data: [] };
        const calendar_check = await base_control.get(
            program_calendar,
            {
                _id: ObjectId(req.body._calendar_id),
                isDeleted: false,
                'level.term': req.body.batch,
            },
            {
                'level.level_no': 1,
                'level.events': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.rotation_course': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            checks = await base_control.check_id(calendar_event, {
                _id: { $in: req.body._event_id },
                isDeleted: false,
            });
            if (!checks.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('EVENTS_NOT_FOUND'),
                            req.t('EVENTS_NOT_FOUND'),
                        ),
                    );
        }
        const objs = {};
        if (req.body.by !== undefined) {
            let d1 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].start_date,
            );
            const d2 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].end_date,
            );
            let count = 1;
            const date_response = [];
            let start;
            let end;
            let push_objs = {};
            start = d1.format('YYYY-MM-DD');
            end = d2.format('YYYY-MM-DD');
            while (true) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_response.push(push_objs);
                }
                //if day start with saturday - even its one day,it should be consider as 1 week
                if (d1.format('dddd') === 'Saturday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_response.push(push_objs);
                }
                d1 = d1.add(1, 'days');
                if (d1.format('dddd') === 'Sunday') {
                    start = d1.format('YYYY-MM-DD');
                    count++;
                }
                if (d1.format() === d2.format()) {
                    // if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_response.push(push_objs);
                    // }
                    break;
                }
            }
            let course_start;
            let course_end;
            let start_week = null;
            let end_week = null;
            if (req.body.by === 'date') {
                course_start = req.body.start_date;
                course_end = req.body.end_date;
            } else {
                start_week = req.body.start_week;
                end_week = req.body.end_week;
            }

            if (req.body.by === 'date') {
                date_response.forEach((element) => {
                    if (element.start <= course_start && element.end >= course_start) {
                        start_week = element.acad_week;
                    }
                    if (element.start <= course_end && element.end >= course_end) {
                        end_week = element.acad_week;
                    }
                });
            } else {
                course_start =
                    date_response[
                        date_response.findIndex(
                            (i) => i.acad_week.toString() === start_week.toString(),
                        )
                    ].start;
                course_end =
                    date_response[
                        date_response.findIndex(
                            (i) => i.acad_week.toString() === end_week.toString(),
                        )
                    ].end;
            }
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].start_date': course_start,
            });
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].end_date': course_end,
            });
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].start_week': start_week,
            });
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].end_week': end_week,
            });
        }
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            if (checks.data.length !== 0) {
                const events_data = [];
                checks.data.forEach((element) => {
                    events_data.push({
                        _event_id: element._id,
                        event_type: element.event_type,
                        event_name: element.event_name.first_language,
                        event_date: element.event_date,
                        start_time: element.start_time,
                        end_time: element.end_time,
                        end_date: element.end_date,
                    });
                });
                Object.assign(objs, {
                    'level.$[i].rotation_course.$[k].course.$[j].courses_events': events_data,
                });
            }
        }
        if (req.body._event_id !== undefined && req.body._event_id.length === 0) {
            const level_no = calendar_check.data.level.findIndex(
                (i) =>
                    i.level_no.toString() === req.body.level_no.toString() &&
                    i.term === req.body.batch,
            );
            const rot_no = calendar_check.data.level[level_no].rotation_course.findIndex(
                (i) => i.rotation_count.toString() === req.body.rotation_count.toString(),
            );
            const course_no = calendar_check.data.level[level_no].rotation_course[
                rot_no
            ].course.findIndex((j) => j._course_id.toString() === req.body._course_id.toString());
            const event_ids = calendar_check.data.level[level_no].rotation_course[rot_no].course[
                course_no
            ].courses_events.map((k) => k._event_id);
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].courses_events': [],
            });
            await base_control.update_push_pull_many(
                calendar_event,
                { _id: event_ids, event_calendar: 'course' },
                { $set: { isDeleted: true } },
            );
        }
        if (req.body.color_code !== undefined && req.body.color_code.length !== 0) {
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j].color_code': req.body.color_code,
            });
        }
        if (req.body._batch_course_id !== undefined && req.body._batch_course_id.length !== 0) {
            Object.assign(objs, {
                'level.$[i].rotation_course.$[k].course.$[j]._batch_course_id':
                    req.body._batch_course_id,
            });
        }
        const cond = { _id: ObjectId(req.body._calendar_id), isDeleted: false };
        const filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no, 'i.term': req.body.batch },
                { 'j._course_id': ObjectId(req.body._course_id) },
                { 'k.rotation_count': req.body.rotation_count },
            ],
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $set: objs },
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_UPDATED_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.com_response(
                res,
                404,
                false,
                req.t('UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME'),
                doc.data,
            );
        }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.get_course_list = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.term': req.params.batch } },
        { $match: { 'level.level_no': req.params.level } },
        { $project: { _id: 1, level: 1 } },
    ];
    const calendar_check = await base_control.get_aggregate(program_calendar, aggre);
    if (!calendar_check.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('NO_COURSE_FOUND'),
                    calendar_check.data,
                ),
            );
    // return res.send(calendar_check)

    // let query = { _program_id: calendar_check.data[0].level._program_id, study_level: parseInt(req.params.level), model: req.params.type, isDeleted: false };
    // let project = { courses_name: 1 };
    // let course_list = await base_control.get_list(course, query, project);
    // if (!course_list.status) return res.status(200).send(common_files.response_function(res, 200, true, "level wise course list", course_list));
    const query = { course_assigned_details: { $exists: true }, isDeleted: false, isActive: true };
    const course_datas = await digi_course
        .find(query, {
            course_name: 1,
            course_assigned_details: 1,
            course_type: 1,
            versionNo: 1,
            versioned: 1,
            // versioning: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        })
        .populate({ path: 'versionedCourseIds', select: { versionName: 1 } })
        .populate({ path: 'versionedFrom', select: { versionName: 1 } })
        .lean();
    if (!course_datas || !course_datas.length) {
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('COURSE_NOT_FOUND'), []));
    }
    const separate_list = [];
    const course_list = [];
    for (course_element of course_datas) {
        for (assigned_element of course_element.course_assigned_details) {
            if (
                assigned_element._program_id.toString() ===
                    calendar_check.data[0].level._program_id.toString() &&
                assigned_element.curriculum_name.toString() ===
                    calendar_check.data[0].level.curriculum.toString() &&
                assigned_element.year &&
                assigned_element.level_no &&
                assigned_element.year.toString() === calendar_check.data[0].level.year.toString() &&
                assigned_element.level_no.toString() ===
                    calendar_check.data[0].level.level_no.toString()
            ) {
                course_list.push({
                    _id: course_element._id,
                    courses_name: course_element.course_name,
                    course_type: course_element.course_type,
                    versionNo: course_element.versionNo,
                    versioned: course_element.versioned || false,
                    versionName: course_element.versionName || '',
                    versionedFrom: course_element.versionedFrom || '',
                    versionedCourseIds: course_element.versionedCourseIds || [],
                    // versioning: course_element.versioning,
                });
            } else {
                const loc = assigned_element.course_shared_with.findIndex(
                    (i) =>
                        i._program_id.toString() ===
                            calendar_check.data[0].level._program_id.toString() &&
                        i.curriculum_name.toString() ===
                            calendar_check.data[0].level.curriculum.toString() &&
                        i.year.toString() === calendar_check.data[0].level.year.toString() &&
                        i.level_no.toString() === calendar_check.data[0].level.level_no.toString(),
                );
                if (loc !== -1) {
                    course_list.push({
                        _id: course_element._id,
                        courses_name: course_element.course_name,
                        course_type: course_element.course_type,
                        versionNo: course_element.versionNo,
                        versioned: course_element.versioned || false,
                        versionName: course_element.versionName || '',
                        versionedFrom: course_element.versionedFrom || '',
                        versionedCourseIds: course_element.versionedCourseIds || [],
                        // versioning: course_element.versioning,
                    });
                }
            }
        }
    }
    let course_ids;
    if (calendar_check.data[0].level.rotation === 'yes') {
        course_ids = calendar_check.data[0].level.rotation_course[0].course.map(
            (i) => i._course_id,
        );
    } else {
        course_ids = calendar_check.data[0].level.course.map((i) => i._course_id.toString());
    }
    course_list.forEach((element) => {
        if (course_ids.findIndex((i) => i.toString() === element._id.toString()) === -1) {
            separate_list.push(element);
        }
    });
    return res.status(200).send(
        common_files.responseFunctionWithRequest(req, 200, true, req.t('LEVEL_WISE_COURSE_LIST'), {
            status: true,
            data: separate_list,
        }),
    );
    // common_files.responseFunctionWithRequest(req, 200, true, req.t('LEVEL_WISE_COURSE_LIST'), {
    //     status: true,
    //     data: separate_list,
    // });
};

exports.rotation_course = async (req, res) => {
    try {
        const aggre = [
            { $match: { _id: ObjectId(req.params.id) } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.term': req.params.batch } },
            { $match: { 'level.level_no': req.params.level } },
            { $project: { _id: 1, level: 1 } },
        ];
        const calendar_check = await base_control.get_aggregate(program_calendar, aggre);
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const query = { course_assigned_details: { $exists: true }, isDeleted: false };
        const course_datas = await base_control.get_list(digi_course, query, {
            course_name: 1,
            course_assigned_details: 1,
            course_type: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        });
        if (!course_datas.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        if (calendar_check.data[0].level.rotation !== 'yes')
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ITS_NOT_ROTATION_LEVEL'),
                        req.t('ITS_NOT_ROTATION_LEVEL'),
                    ),
                );
        const rot_no = calendar_check.data[0].level.rotation_course.findIndex(
            (i) => parseInt(i.rotation_count) === parseInt(req.params.rotation),
        );
        console.log(calendar_check.data[0].level.rotation_course);
        if (rot_no === -1)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('ROTATION_LEVEL_NO_NOT_MATCH'),
                        req.t('ROTATION_LEVEL_NO_NOT_MATCH'),
                    ),
                );
        const course_ids = calendar_check.data[0].level.rotation_course[rot_no].course.map(
            (i) => i._course_id,
        );
        const separate_list = [];
        const course_list = [];
        for (course_element of course_datas.data) {
            for (assigned_element of course_element.course_assigned_details) {
                if (
                    assigned_element._program_id.toString() ===
                        calendar_check.data[0].level._program_id.toString() &&
                    assigned_element.curriculum_name.toString() ===
                        calendar_check.data[0].level.curriculum.toString() &&
                    assigned_element.year &&
                    assigned_element.level_no &&
                    assigned_element.year.toString() ===
                        calendar_check.data[0].level.year.toString() &&
                    assigned_element.level_no.toString() ===
                        calendar_check.data[0].level.level_no.toString()
                ) {
                    course_list.push({
                        _id: course_element._id,
                        courses_name: course_element.course_name,
                        course_type: course_element.course_type,
                        versioned: course_element.versioned || false,
                        versionName: course_element.versionName || '',
                        versionedFrom: course_element.versionedFrom || '',
                        versionedCourseIds: course_element.versionedCourseIds || [],
                    });
                } else {
                    const loc = assigned_element.course_shared_with.findIndex(
                        (i) =>
                            i._program_id.toString() ===
                                calendar_check.data[0].level._program_id.toString() &&
                            i.curriculum_name.toString() ===
                                calendar_check.data[0].level.curriculum.toString() &&
                            i.year.toString() === calendar_check.data[0].level.year.toString() &&
                            i.level_no.toString() ===
                                calendar_check.data[0].level.level_no.toString(),
                    );
                    if (loc !== -1) {
                        course_list.push({
                            _id: course_element._id,
                            courses_name: course_element.course_name,
                            course_type: course_element.course_type,
                            versioned: course_element.versioned || false,
                            versionName: course_element.versionName || '',
                            versionedFrom: course_element.versionedFrom || '',
                            versionedCourseIds: course_element.versionedCourseIds || [],
                        });
                    }
                }
            }
        }
        course_list.forEach((element) => {
            if (course_ids.findIndex((i) => i.toString() === element._id.toString()) === -1) {
                separate_list.push(element);
            }
        });
        common_files.com_response(res, 200, true, req.t('LEVEL_WISE_COURSE_LIST'), {
            status: true,
            data: separate_list,
        });
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_add = async (req, res) => {
    const calendar_id = ObjectId(req.body._calendar_id);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false, 'level.term': req.body.batch },
        {
            'level.level_no': 1,
            'level.rotation': 1,
            'level.rotation_count': 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.term': 1,
            'level.rotation_course': 1,
        },
    );
    const course_checks = await base_control.get(
        course,
        { _id: req.body._course_id, isDeleted: false },
        {},
    );
    let event_checks = { status: true, data: [] };
    if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
        event_checks = await base_control.check_id(calendar_event, {
            _id: { $in: req.body._event_id },
            isDeleted: false,
        });
    }
    if (calendar_check.status && course_checks.status && event_checks.status) {
        let d1 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].start_date,
        );
        const d2 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].end_date,
        );
        let count = 1;
        const date_responce = [];
        let start;
        let end;
        let push_objs = {};
        start = d1.format('YYYY-MM-DD');
        end = d2.format('YYYY-MM-DD');
        while (true) {
            if (d1.format('dddd') === 'Thursday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
            }
            d1 = d1.add(1, 'days');
            if (d1.format('dddd') === 'Sunday') {
                start = d1.format('YYYY-MM-DD');
                count++;
            }
            if (d1.format() === d2.format()) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                }
                break;
            }
        }
        // console.log(date_responce);
        // let objs = {};
        // let cond = {};
        // let filter = {};
        let doc = { status: false, data: {} };
        if (
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].rotation === 'yes'
        ) {
            if (
                req.body.rotation_count !== 0 &&
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].rotation_count >= req.body.rotation_count
            ) {
                const rot_count =
                    calendar_check.data.level[
                        calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                    ].rotation_count;

                const level_pos = calendar_check.data.level.findIndex(
                    (i) => i.level_no === req.body.level_no,
                );

                let course_start;
                let course_end;
                course_start = req.body.start_date;
                course_end = req.body.end_date;

                let start_week = null;
                let end_week = null;
                let len = 1;
                let next = 0;
                const course_data = {
                    _course_id: req.body._course_id,
                    courses_name: course_checks.data.courses_name,
                    courses_number: course_checks.data.courses_number,
                    study_level: course_checks.data.study_level,
                    model: course_checks.data.model,
                    credit_hours: {
                        theory: course_checks.data.theory_credit,
                        practical: course_checks.data.practical_credit,
                        clinical: course_checks.data.clinical_credit,
                    },
                    color_code: req.body.color_code,
                };
                for (let k = 1; k <= rot_count; k++) {
                    date_responce.forEach((element) => {
                        if (element.start <= course_start && element.end >= course_start) {
                            start_week = element.acad_week;
                        }
                        if (element.start <= course_end && element.end >= course_end) {
                            end_week = element.acad_week;
                        }
                    });
                    if (start_week !== null && end_week !== null) {
                        if (len === 1) {
                            len = end_week - start_week;
                        }
                        next = end_week + len;
                        Object.assign(course_data, {
                            start_date: course_start,
                            end_date: course_end,
                        });
                        Object.assign(course_data, { start_week, end_week });
                        if (req.body._batch_course_id !== undefined) {
                            Object.assign(course_data, {
                                _batch_course_id: req.body._batch_course_id,
                            });
                        }
                        objs = {
                            $push: {
                                'level.$[j].rotation_course.$[i].course': [course_data],
                            },
                        };
                        filter = {
                            arrayFilters: [
                                { 'i.rotation_count': k },
                                { 'j.level_no': req.body.level_no, 'j.term': req.body.batch },
                            ],
                        };
                        cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
                        // doc = await base_control.update_condition_array_filter(program_calendar, cond, objs, filter);

                        if (k !== rot_count) {
                            const courses_data =
                                calendar_check.data.level[level_pos].rotation_course[k].course;
                            // console.log(courses_data);
                            let week_no = end_week + 1;
                            courses_data.forEach((element) => {
                                week_no = element.end_week;
                                while (true) {
                                    if (week_no + len < count - 1) {
                                        // console.log((week_no + len), ' ', (count - 1));
                                        if (
                                            element._course_id.toString() !==
                                            req.body._course_id.toString()
                                        ) {
                                            // console.log(k, ' ', (element.start_week), ' ', (element.end_week));
                                            // console.log('tst ', week_no > (element.start_week) && week_no < (element.end_week));
                                            if (
                                                element.start_week > week_no < element.end_week &&
                                                element.start_week >
                                                    week_no + len <
                                                    element.end_week
                                            ) {
                                                console.log(
                                                    'Available ',
                                                    len,
                                                    ' ',
                                                    date_responce[week_no].acad_week,
                                                    ' ',
                                                    date_responce[week_no + len].acad_week,
                                                );
                                                // console.log(k, ' Available');
                                                course_start = date_responce[week_no].start;
                                                course_end = date_responce[week_no + len].end;

                                                week_no = date_responce[week_no + len].acad_week;
                                                break;
                                            }
                                        }
                                    }
                                    // else {
                                    //     course_start = date_responce[0].start;
                                    //     course_end = date_responce[len].end;
                                    // }
                                    next = week_no + len;
                                    if (next < count - 1) {
                                        week_no++;
                                    } else {
                                        week_no = 0;
                                    }
                                }
                            });
                        }
                        // if (next < (count - 1) && date_responce.length !== end_week) {
                        //     course_start = date_responce[end_week].start;
                        //     // course_start = date_responce[(end_week - 1)].start;
                        //     course_end = date_responce[next].end;
                        // } else {
                        //     course_start = date_responce[0].start;
                        //     course_end = date_responce[len].end;
                        // }
                    } else {
                        doc = { status: false, data: 'Pls check Dates' };
                        break;
                    }
                }
            } else {
                doc = { status: false, data: 'Rotation Count Mismatch' };
            }
        } else {
            doc = {
                status: false,
                data: 'This course not belong in rotation so feed it in non rotation',
            };
        }
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_UNABLE_TO_ADD_COURSE_IN_ROTATION_SO_RETRY'),
                doc.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.rotation_course_move = async (req, res) => {
    const calendar_id = ObjectId(req.body._calendar_id);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false, 'level.term': req.body.batch },
        {
            'level.level_no': 1,
            'level.rotation': 1,
            'level.rotation_count': 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.term': 1,
            'level.rotation_course': 1,
        },
    );

    if (calendar_check.status) {
        let d1 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].start_date,
        );
        const d2 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].end_date,
        );
        let count = 1;
        const date_responce = [];
        let start;
        let end;
        let push_objs = {};
        start = d1.format('YYYY-MM-DD');
        end = d2.format('YYYY-MM-DD');
        while (true) {
            if (d1.format('dddd') === 'Thursday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
            }
            d1 = d1.add(1, 'days');
            if (d1.format('dddd') === 'Sunday') {
                start = d1.format('YYYY-MM-DD');
                count++;
            }
            if (d1.format() === d2.format()) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                }
                break;
            }
        }
        let objs = {};
        let cond = {};
        let filter = {};
        let doc = { status: false, data: {} };

        let course_start;
        let course_end;
        let start_week = 0;
        let end_week = 0;
        let len = 0;
        let next = 0;

        if (
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].rotation === 'yes'
        ) {
            const level_pos = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no,
            );
            // let test1 = calendar_check.data.level[level_pos];
            const rot_count =
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].rotation_count;
            const course_pos = calendar_check.data.level[
                level_pos
            ].rotation_course[1].course.findIndex(
                (i) => i._course_id.toString() === req.body._course_id.toString(),
            );
            const course_datas =
                calendar_check.data.level[level_pos].rotation_course[1].course[course_pos];
            // console.log(level_pos, course_pos, course_datas);
            course_start = moment(course_datas.start_date);
            course_end = moment(course_datas.end_date);
            course_start = course_start.format('YYYY-MM-DD');
            course_end = course_end.format('YYYY-MM-DD');
            if (req.body.direction === 'down') {
                for (let k = 2; k <= rot_count; k++) {
                    date_responce.forEach((element) => {
                        if (element.start <= course_start && element.end >= course_start) {
                            start_week = element.acad_week;
                        }
                        if (element.start <= course_end && element.end >= course_end) {
                            end_week = element.acad_week;
                        }
                    });
                    // console.log('week : ', start_week, end_week);
                    len = end_week - start_week;
                    next = end_week + len;

                    if (next <= count - 1) {
                        course_start = date_responce[end_week - 1].start;
                        course_end = date_responce[next].end;
                    } else {
                        course_start = date_responce[0].start;
                        course_end = date_responce[len].end;
                    }
                    // console.log(date_responce);
                    // console.log(len, next, count);
                    // console.log(start_week, end_week);
                    // console.log(k, ' : ', course_start, course_end);
                    objs = {
                        $set: {
                            'level.$[j].rotation_course.$[i].course.$[l].start_date': course_start,
                            'level.$[j].rotation_course.$[i].course.$[l].end_date': course_end,
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i.rotation_count': k },
                            { 'j.level_no': req.body.level_no, 'j.term': req.body.batch },
                            { 'l._course_id': req.body._course_id },
                        ],
                    };
                    cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
                    doc = await base_control.update_condition_array_filter(
                        program_calendar,
                        cond,
                        objs,
                        filter,
                    );
                    // console.log(doc);
                }
            } else {
                doc = { status: false, data: 'Upwards course moving is on going' };
            }
        } else {
            console.log('Wrong API');
        }
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
                req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_UNABLE_TO_MOVE_COURSE_IN_ROTATION_SO_RETRY'),
                doc.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.rotation_course_list_get = async (req, res) => {
    try {
        const aggre = [
            { $match: { _id: ObjectId(req.params.id), isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.level_no': req.params.level_no, 'level.term': req.params.batch } },
            { $unwind: { path: '$level.rotation_course', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.rotation_course.rotation_count': 1 } },
            { $addFields: { course_list: '$level.rotation_course.course._course_id' } },
            {
                $lookup: {
                    from: 'courses',
                    localField: 'course_list',
                    foreignField: '_id',
                    as: 'courses',
                },
            },
            {
                $project: {
                    'courses._id': 1,
                    'courses.courses_name': 1,
                    'courses.courses_number': 1,
                },
            },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_COURSE'),
                        req.t('ERROR_UNABLE_TO_FIND_COURSE '),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_ROTATION_COURSE_LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_direction_move = async (req, res) => {
    const calendar_id = ObjectId(req.body._calendar_id);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false, 'level.term': req.body.batch },
        {
            'level.level_no': 1,
            'level.rotation': 1,
            'level.rotation_count': 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.term': 1,
            'level.rotation_course': 1,
        },
    );
    if (calendar_check.status) {
        let d1 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].start_date,
        );
        const d2 = moment(
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].end_date,
        );
        let count = 1;
        const date_responce = [];
        let start;
        let end;
        let push_objs = {};
        start = d1.format('YYYY-MM-DD');
        end = d2.format('YYYY-MM-DD');
        while (true) {
            if (d1.format('dddd') === 'Thursday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
            }
            d1 = d1.add(1, 'days');
            if (d1.format('dddd') === 'Sunday') {
                start = d1.format('YYYY-MM-DD');
                count++;
            }
            if (d1.format() === d2.format()) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                }
                break;
            }
        }
        // const objs = {};
        // const cond = {};
        // const filter = {};
        let doc = { status: false, data: {} };

        // let course_start;
        // let course_end;
        // let start_week = 0;
        let end_week = 0;
        // const len = 0;
        // const next = 0;

        if (
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            ].rotation === 'yes'
        ) {
            const level_pos = calendar_check.data.level.findIndex(
                (i) => i.level_no === req.body.level_no,
            );
            // const rot_count =
            //     calendar_check.data.level[
            //         calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
            //     ].rotation_count;
            const course_pos = calendar_check.data.level[
                level_pos
            ].rotation_course[1].course.findIndex(
                (i) => i._course_id.toString() === req.body._course_id.toString(),
            );
            const course_datas =
                calendar_check.data.level[level_pos].rotation_course[1].course[course_pos];
            // console.log(level_pos, course_pos, course_datas);
            course_start = moment(course_datas.start_date);
            course_end = moment(course_datas.end_date);
            course_start = course_start.format('YYYY-MM-DD');
            course_end = course_end.format('YYYY-MM-DD');
            start_week = course_datas.start_week;
            end_week = course_datas.end_week;
            if (req.body.direction === 'down') {
                // date_responce.forEach(element => {
                //     if (element.start <= course_start && element.end >= course_start) {
                //         start_week = element.acad_week;
                //     }
                //     if (element.start <= course_end && element.end >= course_end) {
                //         end_week = element.acad_week;
                //     }
                // });

                const next_week = end_week + 1;
                const courses_data = calendar_check.data.level[level_pos].rotation_course[1].course;
                console.log(date_responce);
                courses_data.forEach((element) => {
                    console.log(next_week, ' ', element.start_week);
                    if (next_week === element.start_week) {
                        console.log(element);
                    }
                });

                // len = end_week - start_week;
                // next = end_week + len;
                // if (next <= (count - 1)) {
                //     course_start = date_responce[(end_week - 1)].start;
                //     course_end = date_responce[next].end;
                // } else {
                //     course_start = date_responce[0].start;
                //     course_end = date_responce[len].end;
                // }

                /* for (let k = 2; k <= rot_count; k++) {
                    date_responce.forEach(element => {
                        if (element.start <= course_start && element.end >= course_start) {
                            start_week = element.acad_week;
                        }
                        if (element.start <= course_end && element.end >= course_end) {
                            end_week = element.acad_week;
                        }
                    });
                    len = end_week - start_week;
                    next = end_week + len;
                    if (next <= (count - 1)) {
                        course_start = date_responce[(end_week - 1)].start;
                        course_end = date_responce[next].end;
                    } else {
                        course_start = date_responce[0].start;
                        course_end = date_responce[len].end;
                    }
                    objs = {
                        $set: {
                            'level.$[j].rotation_course.$[i].course.$[l].start_date': course_start,
                            'level.$[j].rotation_course.$[i].course.$[l].end_date': course_end
                        }
                    };
                    filter = { arrayFilters: [{ 'i.rotation_count': k }, { 'j.level_no': req.body.level_no, 'j.term': req.body.batch }, { 'l._course_id': req.body._course_id }] };
                    cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
                    // doc = await base_control.update_condition_array_filter(program_calendar, cond, objs, filter);
                } */
                doc = { status: false, data: courses_data };
            } else {
                doc = { status: false, data: 'Upwards course moving is on going' };
            }
        } else {
            console.log('Wrong API');
            doc = { status: false, data: 'Wrong API' };
        }
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
                req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_UNABLE_TO_MOVE_COURSE_IN_ROTATION_SO_RETRY'),
                doc.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.rotation_course_adding = async (req, res) => {
    try {
        const calendar_id = ObjectId(req.body._calendar_id);
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: calendar_id, isDeleted: false, 'level.term': req.body.batch },
            {
                'level.level_no': 1,
                'level.events': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.rotation_course': 1,
            },
        );
        const course_checks = await base_control.get(
            course,
            { _id: req.body._course_id, isDeleted: false },
            {},
        );
        let event_checks = { status: true, data: [] };
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            event_checks = await base_control.check_id(calendar_event, {
                _id: { $in: req.body._event_id },
                isDeleted: false,
            });
        }
        if (calendar_check.status && course_checks.status && event_checks.status) {
            const events_data = [];
            if (event_checks.data.length !== 0) {
                event_checks.data.forEach((element) => {
                    events_data.push({
                        _event_id: element._id,
                        event_type: element.event_type,
                        event_name: element.event_name.first_language,
                        event_date: element.event_date,
                        start_time: element.start_time,
                        end_time: element.end_time,
                        end_date: element.end_date,
                    });
                });
            }
            if (
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].start_date === undefined
            )
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('PLS_SET_START_&_END_FIRST'),
                    req.t('PLS_SET_START_&_END_FIRST'),
                );
            let d1 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].start_date,
            );
            const d2 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].end_date,
            );
            let count = 1;
            const date_responce = [];
            let start;
            let end;
            let push_objs = {};
            start = d1.format('YYYY-MM-DD');
            end = d2.format('YYYY-MM-DD');
            while (true) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                }
                d1 = d1.add(1, 'days');
                if (d1.format('dddd') === 'Sunday') {
                    start = d1.format('YYYY-MM-DD');
                    count++;
                }
                if (d1.format() === d2.format()) {
                    // if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                    // }
                    break;
                }
            }
            // console.log(date_responce);
            let objs = {};
            let cond = {};
            let filter = {};
            let doc = { status: false, data: {} };
            if (
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].rotation === 'yes'
            ) {
                if (
                    req.body.rotation_count !== 0 &&
                    calendar_check.data.level[
                        calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                    ].rotation_count >= req.body.rotation_count
                ) {
                    const rot_count =
                        calendar_check.data.level[
                            calendar_check.data.level.findIndex(
                                (i) => i.level_no === req.body.level_no,
                            )
                        ].rotation_count;
                    const level_pos = calendar_check.data.level.findIndex(
                        (i) => i.level_no === req.body.level_no,
                    );
                    let course_start;
                    let course_end;
                    let start_week = null;
                    let end_week = null;
                    let len = 1;
                    const course_date_list = [];

                    if (req.body.by === 'date') {
                        course_start = req.body.start_date;
                        course_end = req.body.end_date;
                    } else {
                        start_week = req.body.start_week;
                        end_week = req.body.end_week;
                    }
                    let course_data = {
                        _course_id: req.body._course_id,
                        courses_name: course_checks.data.courses_name,
                        courses_number: course_checks.data.courses_number,
                        study_level: course_checks.data.study_level,
                        model: course_checks.data.model,
                        credit_hours: {
                            theory: course_checks.data.theory_credit,
                            practical: course_checks.data.practical_credit,
                            clinical: course_checks.data.clinical_credit,
                        },
                        color_code: req.body.color_code,
                    };
                    for (let k = 1; k <= rot_count; k++) {
                        if (req.body.by === 'date') {
                            date_responce.forEach((element) => {
                                if (element.start <= course_start && element.end >= course_start) {
                                    start_week = element.acad_week;
                                }
                                if (element.start <= course_end && element.end >= course_end) {
                                    end_week = element.acad_week;
                                }
                            });
                        } else {
                            course_start =
                                date_responce[
                                    date_responce.findIndex((i) => i.acad_week === start_week)
                                ].start;
                            course_end =
                                date_responce[
                                    date_responce.findIndex((i) => i.acad_week === end_week)
                                ].end;
                        }
                        if (start_week !== null && end_week !== null) {
                            if (len === 1) {
                                len = end_week - start_week;
                                if (events_data.length !== 0) {
                                    Object.assign(course_data, { courses_events: events_data });
                                }
                            } else {
                                course_data = {
                                    _course_id: req.body._course_id,
                                    courses_name: course_checks.data.courses_name,
                                    courses_number: course_checks.data.courses_number,
                                    study_level: course_checks.data.study_level,
                                    model: course_checks.data.model,
                                    credit_hours: {
                                        theory: course_checks.data.theory_credit,
                                        practical: course_checks.data.practical_credit,
                                        clinical: course_checks.data.clinical_credit,
                                    },
                                    color_code: req.body.color_code,
                                };
                            }
                            next = end_week + len;
                            Object.assign(course_data, {
                                start_date: course_start,
                                end_date: course_end,
                            });
                            Object.assign(course_data, {
                                start_week,
                                end_week,
                            });
                            if (req.body._batch_course_id !== undefined) {
                                Object.assign(course_data, {
                                    _batch_course_id: req.body._batch_course_id,
                                });
                            }
                            objs = {
                                $push: {
                                    'level.$[j].rotation_course.$[i].course': [course_data],
                                },
                            };
                            filter = {
                                arrayFilters: [
                                    { 'i.rotation_count': k },
                                    { 'j.level_no': req.body.level_no, 'j.term': req.body.batch },
                                ],
                            };
                            cond = {
                                _id: req.body._calendar_id,
                                'level.level_no': req.body.level_no,
                            };
                            doc = await base_control.update_condition_array_filter(
                                program_calendar,
                                cond,
                                objs,
                                filter,
                            );
                            course_date_list.push({
                                rotation_no: k,
                                start_week,
                                end_week,
                            });
                            if (k !== rot_count) {
                                if (course_checks.data.model !== constant.MODEL.ELECTIVE) {
                                    let week_no = end_week + 1;
                                    const level_events =
                                        calendar_check.data.level[level_pos].events;
                                    level_events.forEach((event_element) => {
                                        if (
                                            event_element.event_type === constant.EVENT_TYPE.HOLIDAY
                                        ) {
                                            const event_start_date = moment(
                                                event_element.event_date,
                                            );
                                            const event_end_date = moment(event_element.end_date);
                                            let event_start = 0;
                                            let event_end = 0;
                                            date_responce.forEach((element) => {
                                                if (
                                                    element.start <=
                                                        event_start_date.format('YYYY-MM-DD') &&
                                                    element.end >=
                                                        event_start_date.format('YYYY-MM-DD')
                                                ) {
                                                    event_start = element.acad_week;
                                                }
                                                if (
                                                    element.start <=
                                                        event_end_date.format('YYYY-MM-DD') &&
                                                    element.end >=
                                                        event_end_date.format('YYYY-MM-DD')
                                                ) {
                                                    event_end = element.acad_week;
                                                }
                                            });
                                            if (
                                                event_start > week_no &&
                                                event_end < week_no + len
                                            ) {
                                                week_no = event_end + 1;
                                            }
                                        }
                                    });
                                    if (week_no + len < count - 1) {
                                        course_start =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no,
                                                )
                                            ].start;
                                        course_end =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no + len,
                                                )
                                            ].end;
                                        start_week = week_no;
                                        end_week = week_no + len;
                                        week_no = date_responce[week_no + len].acad_week;
                                    } else {
                                        week_no = 1;
                                        course_start =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no,
                                                )
                                            ].start;
                                        course_end =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no + len,
                                                )
                                            ].end;
                                        start_week = week_no;
                                        end_week = week_no + len;
                                        week_no = date_responce[week_no + len].acad_week;
                                    }
                                    // console.log(week_no, ' ', end_week);
                                }
                            }
                        } else {
                            doc = { status: false, data: 'Pls check Dates' };
                            break;
                        }
                    }
                    // console.log(course_date_list);
                } else {
                    doc = { status: false, data: 'Rotation Count Mismatch' };
                }
            } else {
                doc = {
                    status: false,
                    data: 'This course not belong in rotation so feed it in non rotation',
                };
            }
            updateProgramCalendarFlatCacheData();
            await updateProgramCalendarRedisData({
                _id: req.body._calendar_id,
                term: req.body.batch,
                level_no: req.body.level_no,
            });
            if (doc.status) {
                common_files.com_response(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                );
            } else {
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('ERROR_UNABLE_TO_ADD_COURSE_IN_ROTATION_SO_RETRY'),
                    doc.data,
                );
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_ID_NOT_MATCH'),
                req.t('CHECK_PARSING_REFERENCE_ID'),
            );
        }
    } catch (error) {
        console.log(error);
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// Currently using manual mode
exports.rotation_course_add_manual = async (req, res) => {
    try {
        const calendar_check = await base_control.get(
            program_calendar,
            {
                _id: ObjectId(req.body._calendar_id),
                isDeleted: false,
                'level.term': req.body.batch,
            },
            {
                'level.level_no': 1,
                'level.events': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.rotation_course': 1,
            },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const course_checks = await base_control.get(
            digi_course,
            { _id: req.body._course_id, isDeleted: false },
            {},
        );
        if (!course_checks.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );

        let event_checks = { status: true, data: [] };
        const events_data = [];
        if (req.body._event_id !== undefined && req.body._event_id.length !== 0) {
            event_checks = await base_control.check_id(calendar_event, {
                _id: { $in: req.body._event_id },
                isDeleted: false,
            });
            if (!event_checks.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('EVENTS_NOT_FOUND'),
                            req.t('EVENTS_NOT_FOUND'),
                        ),
                    );
            event_checks.data.forEach((element) => {
                events_data.push({
                    _event_id: element._id,
                    event_type: element.event_type,
                    event_name: element.event_name.first_language,
                    event_date: element.event_date,
                    start_time: element.start_time,
                    end_time: element.end_time,
                    end_date: element.end_date,
                });
            });
        }
        const level_pos = calendar_check.data.level.findIndex(
            (i) => i.level_no === req.body.level_no && i.term === req.body.batch,
        );
        if (level_pos === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('LEVEL_NUMBER_IS_NOT_MATCHING_ALONG_BATCH'),
                        req.t('LEVEL_NUMBER_IS_NOT_MATCHING_ALONG_BATCH'),
                    ),
                );
        const rotation_pos = calendar_check.data.level[level_pos].rotation_course.findIndex(
            (i) => i.rotation_count.toString() === req.body.rotation_count.toString(),
        );
        if (rotation_pos === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ROTATION_NOT_MATCHING_IN_THIS_LEVEL'),
                        req.t('ROTATION_NOT_MATCHING_IN_THIS_LEVEL'),
                    ),
                );
        const course_pos = calendar_check.data.level[level_pos].rotation_course[
            rotation_pos
        ].course.findIndex((i) => i._course_id.toString() === req.body._course_id.toString());
        if (course_pos !== -1)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_ROTATION'),
                        req.t('THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_ROTATION'),
                    ),
                );

        if (calendar_check.data.level[level_pos].start_date === undefined)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('PLS_SET_START_&_END_FIRST'),
                        req.t('PLS_SET_START_&_END_FIRST'),
                    ),
                );
        let d1 = moment(calendar_check.data.level[level_pos].start_date);
        const d2 = moment(calendar_check.data.level[level_pos].end_date);
        let count = 1;
        const date_responce = [];
        let start;
        let end;
        let push_objs = {};
        start = d1.format('YYYY-MM-DD');
        end = d2.format('YYYY-MM-DD');
        while (true) {
            if (d1.format('dddd') === 'Thursday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
            }
            //if day start with saturday - even its one day,it should be consider as 1 week
            if (d1.format('dddd') === 'Saturday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
            }
            d1 = d1.add(1, 'days');
            if (d1.format('dddd') === 'Sunday') {
                start = d1.format('YYYY-MM-DD');
                count++;
            }
            if (d1.format() === d2.format()) {
                // if (d1.format('dddd') === 'Thursday') {
                end = d1.format('YYYY-MM-DD');
                push_objs = {
                    start,
                    end,
                    acad_week: count,
                };
                date_responce.push(push_objs);
                // }
                break;
            }
        }
        // if (calendar_check.data.level[calendar_check.data.level.findIndex(i => i.level_no === req.body.level_no)].rotation === 'yes') {
        if (
            req.body.rotation_count !== 0 &&
            calendar_check.data.level[level_pos].rotation_count >= req.body.rotation_count
        ) {
            let course_start;
            let course_end;
            let start_week = null;
            let end_week = null;
            let len = 1;
            if (req.body.by === 'date') {
                course_start = req.body.start_date;
                course_end = req.body.end_date;
            } else {
                start_week = req.body.start_week;
                end_week = req.body.end_week;
            }
            const credit_hours = [];
            for (credit_element of course_checks.data.credit_hours) {
                credit_hours.push({
                    type_name: credit_element.type_name,
                    type_symbol: credit_element.type_symbol,
                    credit_hours: credit_element.credit_hours,
                });
            }
            let course_data = {
                _course_id: req.body._course_id,
                courses_name: course_checks.data.course_name,
                courses_number: course_checks.data.course_code,
                versionNo: course_checks.data.versionNo || 1,
                versioned: course_checks.data.versioned || false,
                versionName: course_checks.data.versionName || '',
                model: course_checks.data.course_type,
                credit_hours,
                color_code: req.body.color_code,
            };

            if (req.body.by === 'date') {
                date_responce.forEach((element) => {
                    if (element.start <= course_start && element.end >= course_start) {
                        start_week = element.acad_week;
                    }
                    if (element.start <= course_end && element.end >= course_end) {
                        end_week = element.acad_week;
                    }
                });
            } else {
                course_start =
                    date_responce[
                        date_responce.findIndex(
                            (i) => i.acad_week.toString() === start_week.toString(),
                        )
                    ].start;
                course_end =
                    date_responce[
                        date_responce.findIndex(
                            (i) => i.acad_week.toString() === end_week.toString(),
                        )
                    ].end;
            }
            if (start_week === null && end_week === null)
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('DATE_MISMATCH_PLS_CHECK_DATES'),
                            req.t('DATE_MISMATCH_PLS_CHECK_DATES'),
                        ),
                    );
            if (len === 1) {
                len = end_week - start_week;
                if (events_data.length !== 0) {
                    Object.assign(course_data, { courses_events: events_data });
                }
            } else {
                course_data = {
                    _course_id: req.body._course_id,
                    courses_name: course_checks.data.courses_name,
                    courses_number: course_checks.data.courses_number,
                    versionNo: course_checks.data.versionNo || 1,
                    versioned: course_checks.data.versioned || false,
                    versionName: course_checks.data.versionName || '',
                    study_level: course_checks.data.study_level,
                    model: course_checks.data.model,
                    credit_hours,
                    color_code: req.body.color_code,
                };
            }
            Object.assign(course_data, { start_date: course_start, end_date: course_end });
            Object.assign(course_data, { start_week, end_week });
            if (req.body._batch_course_id !== undefined) {
                Object.assign(course_data, { _batch_course_id: req.body._batch_course_id });
            }
            const objs = {
                $push: {
                    'level.$[j].rotation_course.$[i].course': [course_data],
                },
            };
            const filter = {
                arrayFilters: [
                    { 'i.rotation_count': req.body.rotation_count },
                    { 'j.level_no': req.body.level_no, 'j.term': req.body.batch },
                ],
            };
            const cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
            // doc = { status: false, data: objs };
            const doc = await base_control.update_condition_array_filter(
                program_calendar,
                cond,
                objs,
                filter,
            );
            updateProgramCalendarFlatCacheData();
            await updateProgramCalendarRedisData({
                _id: req.body._calendar_id,
                term: req.body.batch,
                level_no: req.body.level_no,
            });
            if (!doc.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('ERROR_UNABLE_TO_ADD_COURSE_IN_ROTATION_SO_RETRY'),
                            doc.data,
                        ),
                    );
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                        req.t('PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY'),
                    ),
                );
        }
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ROTATION_COUNT_MISMATCH'),
                    req.t('ROTATION_COUNT_MISMATCH'),
                ),
            );

        // } else {
        //     common_files.com_response(res, 404, false, "This course not belong in rotation so feed it in non rotation", 'This course not belong in rotation so feed it in non rotation');
        // }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_swap = async (req, res) => {
    try {
        const calendar_id = ObjectId(req.body._calendar_id);
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: calendar_id, isDeleted: false, 'level.term': req.body.batch },
            {
                'level.level_no': 1,
                'level.events': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.start_date': 1,
                'level.end_date': 1,
                'level.term': 1,
                'level.rotation_course': 1,
            },
        );
        if (calendar_check.status) {
            let d1 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].start_date,
            );
            const d2 = moment(
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].end_date,
            );
            let count = 1;
            const date_responce = [];
            let start;
            let end;
            let push_objs = {};
            start = d1.format('YYYY-MM-DD');
            end = d2.format('YYYY-MM-DD');
            while (true) {
                if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                }
                d1 = d1.add(1, 'days');
                if (d1.format('dddd') === 'Sunday') {
                    start = d1.format('YYYY-MM-DD');
                    count++;
                }
                if (d1.format() === d2.format()) {
                    // if (d1.format('dddd') === 'Thursday') {
                    end = d1.format('YYYY-MM-DD');
                    push_objs = {
                        start,
                        end,
                        acad_week: count,
                    };
                    date_responce.push(push_objs);
                    // }
                    break;
                }
            }
            let objs = {};
            let cond = {};
            let filter = {};
            let doc = { status: true, data: {} };
            let course_start;
            let course_end;
            let start_week = 0;
            let end_week = 0;
            let len = 0;
            if (
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                ].rotation === 'yes'
            ) {
                const level_pos = calendar_check.data.level.findIndex(
                    (i) => i.level_no === req.body.level_no,
                );
                const rot_count =
                    calendar_check.data.level[
                        calendar_check.data.level.findIndex((i) => i.level_no === req.body.level_no)
                    ].rotation_count;
                const rot = calendar_check.data.level[level_pos].rotation_course[0].course;
                const crs_data = [];
                rot.forEach((element) => {
                    crs_data.push({
                        rotation_no: 1,
                        _course_id: element._course_id,
                        start_week: element.start_week,
                        end_week: element.end_week,
                    });
                });
                if (req.body.direction === 'down') {
                    for (let k = 2; k <= rot_count; k++) {
                        const rotation_pos = calendar_check.data.level[
                            level_pos
                        ].rotation_course.findIndex(
                            (i) => i.rotation_count.toString() === k.toString(),
                        );
                        const cor =
                            calendar_check.data.level[level_pos].rotation_course[rotation_pos]
                                .course;
                        cor.forEach(async (element) => {
                            // console.log(element.model);
                            if (element.model !== constant.MODEL.ELECTIVE) {
                                start_week = element.start_week;
                                end_week = element.end_week;
                                len = end_week - start_week;
                                week_no = end_week + 1;

                                if (week_no + len >= count - 1) {
                                    week_no = 1;
                                }
                                crs_data.forEach(async (crs_element, index) => {
                                    if (
                                        crs_element._course_id.toString() ===
                                        element._course_id.toString()
                                    ) {
                                        // console.log(index, ' Start : ', crs_element.start_week, ' ', week_no, ' End ', crs_element.end_week, ' ', (week_no + len));
                                        if (week_no + len < count - 1) {
                                            if (
                                                crs_element.start_week !== week_no &&
                                                crs_element.end_week !== week_no + len
                                            ) {
                                                const level_events =
                                                    calendar_check.data.level[level_pos].events;
                                                level_events.forEach((event_element) => {
                                                    if (
                                                        event_element.event_type ===
                                                        constant.EVENT_TYPE.HOLIDAY
                                                    ) {
                                                        const event_start_date = moment(
                                                            event_element.event_date,
                                                        );
                                                        const event_end_date = moment(
                                                            event_element.end_date,
                                                        );
                                                        let event_start = 0;
                                                        let event_end = 0;
                                                        date_responce.forEach((sub_element) => {
                                                            if (
                                                                sub_element.start <=
                                                                    event_start_date.format(
                                                                        'YYYY-MM-DD',
                                                                    ) &&
                                                                sub_element.end >=
                                                                    event_start_date.format(
                                                                        'YYYY-MM-DD',
                                                                    )
                                                            ) {
                                                                event_start = sub_element.acad_week;
                                                            }
                                                            if (
                                                                sub_element.start <=
                                                                    event_end_date.format(
                                                                        'YYYY-MM-DD',
                                                                    ) &&
                                                                sub_element.end >=
                                                                    event_end_date.format(
                                                                        'YYYY-MM-DD',
                                                                    )
                                                            ) {
                                                                event_end = sub_element.acad_week;
                                                            }
                                                        });
                                                        if (
                                                            event_start > week_no &&
                                                            event_end < week_no + len
                                                        ) {
                                                            week_no = event_end + 1;
                                                        }
                                                    }
                                                });

                                                course_start =
                                                    date_responce[
                                                        date_responce.findIndex(
                                                            (i) => i.acad_week === week_no,
                                                        )
                                                    ].start;
                                                course_end =
                                                    date_responce[
                                                        date_responce.findIndex(
                                                            (i) => i.acad_week === week_no + len,
                                                        )
                                                    ].end;
                                                start_week = week_no;
                                                end_week = week_no + len;
                                            } else {
                                                if (week_no + len >= count - 1) {
                                                    week_no = 1;
                                                } else {
                                                    week_no = week_no + len + 1;
                                                }
                                            }
                                        } else {
                                            week_no = 1;
                                        }
                                        if (week_no + len >= count - 1) {
                                            week_no = 1;
                                        }

                                        course_start =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no,
                                                )
                                            ].start;
                                        course_end =
                                            date_responce[
                                                date_responce.findIndex(
                                                    (i) => i.acad_week === week_no + len,
                                                )
                                            ].end;
                                        start_week = week_no;
                                        end_week = week_no + len;
                                    }
                                    if (index + 1 === crs_data.length) {
                                        objs = {
                                            $set: {
                                                'level.$[j].rotation_course.$[i].course.$[l].start_date':
                                                    course_start,
                                                'level.$[j].rotation_course.$[i].course.$[l].end_date':
                                                    course_end,
                                                'level.$[j].rotation_course.$[i].course.$[l].start_week':
                                                    start_week,
                                                'level.$[j].rotation_course.$[i].course.$[l].end_week':
                                                    end_week,
                                            },
                                        };
                                        // console.log('Rot ', k, ' Crs ', index, ' ', start_week, ' ', end_week);
                                        filter = {
                                            arrayFilters: [
                                                { 'i.rotation_count': k },
                                                {
                                                    'j.level_no': req.body.level_no,
                                                    'j.term': req.body.batch,
                                                },
                                                { 'l._course_id': element._course_id },
                                            ],
                                        };
                                        cond = {
                                            _id: req.body._calendar_id,
                                            'level.level_no': req.body.level_no,
                                        };
                                        doc = await base_control.update_condition_array_filter(
                                            program_calendar,
                                            cond,
                                            objs,
                                            filter,
                                        );
                                        crs_data.push({
                                            rotation_no: k,
                                            _course_id: element._course_id,
                                            start_week: week_no,
                                            end_week: week_no + len,
                                        });
                                        // console.log(crs_data);
                                    }
                                });
                            }
                        });
                    }
                } else {
                    doc = { status: false, data: 'Upwards course moving is on going' };
                }
            } else {
                console.log('Wrong API');
            }
            updateProgramCalendarFlatCacheData();
            await updateProgramCalendarRedisData({
                _id: req.body._calendar_id,
                term: req.body.batch,
                level_no: req.body.level_no,
            });
            if (doc.status) {
                common_files.com_response(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY'),
                );
            } else {
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('ERROR_UNABLE_TO_MOVE_COURSE_IN_ROTATION_SO_RETRY'),
                    doc.data,
                );
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_ID_NOT_MATCH'),
                req.t('CHECK_PARSING_REFERENCE_ID'),
            );
        }
    } catch (error) {
        console.log(error);
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
