const express = require('express');
const route = express.Router();
const infra_delivery_medium = require('./infrastructure_delivery_medium_controller');
const validator = require('./infrastructure_delivery_medium_validator');

route.post('/select_remote_onsite' ,validator.infra_delivery_medium,infra_delivery_medium.create);
route.get('/get_remote_onsite' ,infra_delivery_medium.get);
route.get('/get_remote_onsite_id/:id' ,validator.infra_delivery_medium_id,infra_delivery_medium.get_id);
route.put('/update_remote_onsite/:id' ,validator.infra_delivery_medium_id,validator.infra_delivery_medium,infra_delivery_medium.update);
route.put('/update_remote_onsite_only/:id' ,validator.infra_delivery_medium_id,validator.delivery_medium,infra_delivery_medium.update_remote_onsite);
route.delete('/delete_remote_onsite/:id' ,validator.infra_delivery_medium_id,infra_delivery_medium.delete);
route.get('/get_delivery_symbols' ,infra_delivery_medium.get_symbols);

module.exports = route;