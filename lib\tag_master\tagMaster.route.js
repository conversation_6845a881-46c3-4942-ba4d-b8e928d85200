const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    manageTagMasterSettingsValidator,
    updatedTagMasterSettingsValidator,
    deleteTagMasterSettingsValidator,
    updatedOverAllTagMasterSettingsValidator,
} = require('./tagMaster.validator');
const {
    getTagMasterSettings,
    manageTagMasterGroupFamilySettings,
    updatedTagMasterSettings,
    deleteTagMasterSettings,
    updatedOverAllTagMasterSettings,
} = require('./tagMaster.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.get(
    '/getTagMasterSettings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getTagMasterSettings),
);
router.post(
    '/manageTagMasterGroupFamilySettings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    manageTagMasterSettingsValidator,
    catchAsync(manageTagMasterGroupFamilySettings),
);
router.put(
    '/updatedTagMasterSettings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updatedTagMasterSettingsValidator,
    catchAsync(updatedTagMasterSettings),
);
router.delete(
    '/deleteTagMasterSettings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    deleteTagMasterSettingsValidator,
    catchAsync(deleteTagMasterSettings),
);
router.put(
    '/updatedOverAllTagMasterSettings/:_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updatedOverAllTagMasterSettingsValidator,
    catchAsync(updatedOverAllTagMasterSettings),
);
module.exports = router;
