const departmentHierarchySchema = require('./department-hierarchy.model');
const { convertToMongoObjectId, getModel } = require('../../utility/common');
const {
    DS_UPDATE,
    DS_CREATE,
    DEPARTMENT_HIERARCHY,
    DS_SAVE_FAILED,
    DS_SAVED,
    DS_UPDATED,
    DS_UPDATE_FAILED,
    DS_NO_DATA_FOUND,
} = require('../../utility/constants');

const addHierarchy = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const hierarchyModel = getModel(tenantURL, DEPARTMENT_HIERARCHY, departmentHierarchySchema);
        const { _institution_id, _department_id, hierarchy, _program_id, mode, _id, type } = body;

        if (mode === DS_CREATE) {
            const chkHierarchy = await hierarchyModel.findOne({
                _department_id: convertToMongoObjectId(_department_id),
                _program_id: convertToMongoObjectId(_program_id),
                _institution_id: convertToMongoObjectId(_institution_id),
            });

            if (chkHierarchy) {
                return { statusCode: 400, message: 'DEPARTMENT_AND_SUBJECT_EXISTS' };
            }
            const addHierarchy = await hierarchyModel.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                _department_id: convertToMongoObjectId(_department_id),
                _program_id: convertToMongoObjectId(_program_id),
                hierarchy,
                type,
            });
            if (!addHierarchy) {
                return { statusCode: 410, message: DS_SAVE_FAILED };
            }
            return { statusCode: 200, message: DS_SAVED };
        }
        if (mode === DS_UPDATE) {
            const chkHierarchy = await hierarchyModel.findOne({
                _id: convertToMongoObjectId(_id),
            });
            if (!chkHierarchy) {
                return { statusCode: 404, message: 'DS_NOT_FOUND' };
            }
            const updateHierarchy = await hierarchyModel.update(
                { _id: convertToMongoObjectId(_id) },
                { $set: { hierarchy } },
            );
            if (!updateHierarchy) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getHierarchy = async ({ headers = {}, query = {} }) => {
    try {
        const { tenantURL } = headers;
        const hierarchyModel = getModel(tenantURL, DEPARTMENT_HIERARCHY, departmentHierarchySchema);
        const { _institution_id, _department_id, _program_id, _id, type } = query;
        const queryProject = {
            _institution_id: 1,
            _department_id: 1,
            _program_id: 1,
            type: 1,
            _id: 1,
            hierarchy: 1,
        };

        const dbQuery = {
            ...(_id && {
                _id: convertToMongoObjectId(_id),
            }),
            ...(_institution_id && {
                _institution_id: convertToMongoObjectId(_institution_id),
            }),
            ...(_department_id && {
                _department_id: convertToMongoObjectId(_department_id),
            }),
            ...(_program_id && {
                _program_id: convertToMongoObjectId(_program_id),
            }),
            ...(type && {
                type: convertToMongoObjectId(type),
            }),
        };

        const getHierarchy = await hierarchyModel.findOne(dbQuery, queryProject);
        if (getHierarchy) return { statusCode: 200, data: getHierarchy };
        return { statusCode: 404, message: DS_NO_DATA_FOUND };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteHierarchy = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const hierarchyModel = getModel(tenantURL, DEPARTMENT_HIERARCHY, departmentHierarchySchema);
        const { _hierarchy_id, _label_id } = query;
        const { isActive } = body;
        const queryProject = {
            _id: 1,
        };

        const dbQuery = {
            ...(_hierarchy_id && {
                _id: convertToMongoObjectId(_hierarchy_id),
            }),
        };

        const getHierarchy = await hierarchyModel.findOne(dbQuery, queryProject);
        if (!getHierarchy) return { statusCode: 404, message: DS_NO_DATA_FOUND };

        const updateDepartment = await hierarchyModel.updateOne(
            {
                _id: convertToMongoObjectId(_hierarchy_id),
            },
            {
                $set: {
                    'hierarchy.$[labelId].isActive': isActive,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': convertToMongoObjectId(_label_id),
                    },
                ],
            },
        );

        if (!updateDepartment) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = { addHierarchy, getHierarchy, deleteHierarchy };
