const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const keys = require('./util_keys');
const { ensureDir } = require('fs-extra');
const s3 = new AWS.S3({
    accessKeyId: keys.AWS_ACCESS_KEY,
    secretAccessKey: keys.AWS_SECRET_KEY,
});

const s3Oci = new AWS.S3({
    region: keys.OCI_REGION,
    accessKeyId: keys.OCI_ACCESS_KEY_ID,
    secretAccessKey: keys.OCI_SECRET_ACCESS_KEY,
    endpoint: keys.OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = keys.DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const s3FileURL = ({ filePath, fileName }) => {
    return keys.DIGIVAL_CLOUD_PROVIDER === 'OCI'
        ? `${keys.OCI_AWS_S3_API}/${filePath}/${fileName}`
        : `https://s3-${keys.AWS_REGION}.amazonaws.com/${filePath}/${fileName}`;
};

const uploadfile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_NAME_DOC,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            let fileName = Date.now() + '-' + file.originalname;
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_NAME_DOC,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    limits: {
        fileSize: 1024 * 1024 * 40, // limit size for files
    },
});

const uploadfile3 = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_NAME_USER_DATA, // bucket path
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            let fileName = Date.now() + '-' + file.originalname;
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_NAME_USER_DATA,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    limits: {
        fileSize: 1024 * 1024 * 40, // limit size for files
    },
});

const { logger } = require('./util_keys');
const uploadfile2 = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_NAME_DOC, // bucket path
        // acl: 'public-read',
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            let fileName = Date.now() + '-' + file.originalname;
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_NAME_DOC,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    limits: {
        fileSize: 1024 * 1024 * 40, // limit size for files
    },
});

/**
 * Local Image storing
 */
const storage = multer.diskStorage({
    destination(req, file, cb) {
        const dir = `public/facial/images/${req.body.email}`;
        ensureDir(dir)
            .then(() => cb(null, dir))
            .catch((err) => {
                logger.error(err, 'multerConfig -> storage -> destination:');
                cb(null, dir);
            });
        // cb(null, 'public/facial/images');
    },
    filename(req, file, cb) {
        cb(null, file.originalname);
    },
});

// const path = require("path");
const uploadfile4 = multer({
    storage,
    limits: {
        fileSize: 1024 * 1024 * 40, // limit size for files
    },
    fileFilter(req, file, cb) {
        // var filetypes = /png/;
        // var mimetype = filetypes.test(file.mimetype);
        // var extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        // if (mimetype && extname) {
        const fileName = file.originalname;
        req.body[file.fieldname] = fileName;
        console.log(file.fieldname, ' ', file.originalname);
        return cb(null, true);
        // }
        // cb("Error: Supported filetypes - " + filetypes);
    },
});

const uploadfile5 = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_DOCUMENT, // bucket path
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_DOCUMENT,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype == 'application/vnd.ms-powerpoint' ||
            file.mimetype == 'application/vnd.ms-excel' ||
            file.mimetype == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
            file.mimetype ==
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimetype == 'application/msword' ||
            file.mimetype == 'application/pdf' ||
            file.mimetype == 'application/vnd.ms-powerpoint' ||
            file.mimetype ==
                'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
            file.mimetype == 'text/plain' ||
            file.mimetype == 'image/tiff' ||
            file.mimetype == 'image/gif' ||
            file.mimetype == 'image/bmp' ||
            file.mimetype == 'video/mp4' ||
            file.mimetype == 'video/x-ms-wmv' ||
            file.mimetype == 'video/webm' ||
            file.mimetype == 'video/mpeg' ||
            file.mimetype == 'video/quicktime' ||
            file.mimetype == 'video/x-flv' ||
            file.mimetype == 'video/3gpp' ||
            file.mimetype == 'video/x-msvideo' ||
            file.mimetype == 'image/png' ||
            file.mimetype == 'image/jpg' ||
            file.mimetype == 'audio/mpeg' ||
            file.mimetype == 'image/jpeg'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    } /* ,
    limits: {
        fileSize: 1024 * 1024 * 40 // limit size for files
    } */,
});
// const multipleFileUpload = multer({
//     storage: multerS3({
//         s3: s3Config,
//         bucket: keys.BUCKET_DOCUMENT, // bucket path
//         metadata(req, file, cb) {
//             cb(null, { fieldName: file.fieldname });
//         },
//         key(req, file, cb) {
//             const originalname = file.originalname;
//             let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
//             cb(null, fileName);
//             const fieldName = file.fieldname;
//             fileName = s3FileURL({
//                 filePath: keys.BUCKET_DOCUMENT,
//                 fileName,
//             });
//             req.body[fieldName] = fileName;
//         },
//     }),
// }).array('file', Infinity);
exports.remove = async (bucket, file_name) => {
    const params = { Bucket: bucket, Key: file_name };
    console.log(params);
    s3Config.deleteObject(params, function (err, data) {
        if (err) {
            console.log(err);
        } else {
            console.log('Removed');
        }
    });
};

exports.moveFile = async (oldName, newName, bucketUrls) => {
    console.log('move called', oldName, newName);
    // const BUCKET_NAME = keys.BUCKET_DOCUMENT;
    // Copy the object to a new location
    s3Config
        .copyObject({
            Bucket: bucketUrls,
            CopySource: `${bucketUrls}${oldName}`,
            Key: newName,
        })
        .promise()
        .then(() => {
            console.log('New File Name ', newName);
        })
        // Error handling is left up to reader
        .catch((e) => console.error(e));
};

const uploadDocumentFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_NAME_DOC,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^\p{L}\p{N}.]/gu, '_');
            cb(null, `digiclass/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_NAME_DOC + '/digiclass',
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'audio/mp3' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'audio/wav' ||
            file.mimetype === 'application/vnd.ms-excel' ||
            file.mimetype === 'video/mp2t' ||
            file.mimetype === 'application/msword' ||
            file.mimetype ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'image/svg+xml' ||
            file.mimetype === 'application/pdf'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

const scheduleUploadDocument = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_DOCUMENT,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^\p{L}\p{N}.]/gu, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_DOCUMENT,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'audio/mp3' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'audio/wav' ||
            file.mimetype === 'application/vnd.ms-excel' ||
            file.mimetype === 'video/mp2t' ||
            file.mimetype === 'application/msword' ||
            file.mimetype ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'image/svg+xml' ||
            file.mimetype === 'application/pdf'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});
const uploadOutsideFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_OUTSIDE_CAMPUS,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, `outside/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_OUTSIDE_CAMPUS,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});
const multipleFileUpload = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_DOCUMENT, // bucket path
        contentType: multerS3.AUTO_CONTENT_TYPE, // to disable automatic download in browser
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_DOCUMENT,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
}).array('file', Infinity);

// Creating new function to upload mutiple file to an particular bucket

const multipleFileUploadForUserRegistration = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_NAME_USER_DATA, // bucket path
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_NAME_USER_DATA,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
});

const uploadSessionDocumentFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_SESSION_ORDER_DOCUMENTS,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_SESSION_ORDER_DOCUMENTS,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'application/msword' ||
            file.mimetype ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

exports.multipleFileUploadWithRestrictions = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_DOCUMENT, // bucket path
        contentType: multerS3.AUTO_CONTENT_TYPE, // to disable automatic download in browser
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            fileName = s3FileURL({
                filePath: keys.BUCKET_DOCUMENT,
                fileName,
            });
            if (!req.body?.urls?.length) {
                req.body.urls = [fileName];
            } else {
                req.body.urls.push(fileName);
            }
        },
    }),
    limits: { fileSize: 70 * 1024 * 1024 }, //max 50mb
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
}).array('file', Infinity);

//q360 file upload
const qapcMultipleFileUpload = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_QAPC_DOCUMENT, // bucket path
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_QAPC_DOCUMENT,
                fileName,
            });
            if (!req.body.urls) {
                req.body.urls = [{ url: fileName, name: originalname }];
            } else {
                req.body.urls.push({ url: fileName, name: originalname });
            }
        },
    }),
    // limits: { fileSize: 250 * 1024 * 1024 },
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/pdf'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
}).array('file', Infinity);

exports.uploadfile5 = uploadfile5;
exports.uploadfile4 = uploadfile4;
exports.uploadfile3 = uploadfile3;
exports.uploadfile2 = uploadfile2;
exports.uploadfile = uploadfile;
exports.uploadDocumentFile = uploadDocumentFile;
exports.scheduleUploadDocument = scheduleUploadDocument;
exports.uploadOutsideFile = uploadOutsideFile;
exports.multipleFileUpload = multipleFileUpload;
exports.qapcMultipleFileUpload = qapcMultipleFileUpload;
exports.s3FileURL = s3FileURL;
exports.uploadSessionDocumentFile = uploadSessionDocumentFile;
exports.multipleFileUploadForUserRegistration = multipleFileUploadForUserRegistration;
