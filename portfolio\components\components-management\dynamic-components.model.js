const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { DYNAMIC_OUTCOME_COMPONENT } = require('../../common/utils/constants');
const { childrenSchema, hierarchySchema } = require('./dynamic-components.schema');

const schema = new Schema(
    {
        name: { type: String },
        children: [childrenSchema],
        hierarchies: [hierarchySchema],
        createdBy: { type: ObjectId },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);

module.exports = model(DYNAMIC_OUTCOME_COMPONENT, schema);
