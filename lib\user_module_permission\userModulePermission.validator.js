const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.getModuleListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
exports.getModuleUsersValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            moduleName: Joi.string()
                .required()
                .error(() => 'MODULE NAME REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
exports.getUserModuleExistingPermissionValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            moduleName: Joi.string()
                .required()
                .error(() => 'MODULE NAME REQUIRED'),
            userId: objectId.error(() => ' USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
