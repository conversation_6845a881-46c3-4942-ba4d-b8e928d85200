/* eslint-disable prefer-const */
let mongoose = require('mongoose');
const { Schema } = mongoose;
let constant = require('../../utility/constants');
const { getConnectionString } = require('../../config/db.config');

let curriculum = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
        },
        curriculumName: String,
        creditHours: {
            hourAs: { type: String, enum: [constant.TOTAL, constant.SPLIT_UP] },
            valueAs: { type: String, enum: [constant.FIXED_VALUE, constant.RANGE_VALUES] },
            credit: [
                {
                    sessionType: String,
                    min: Number,
                    max: Number,
                },
            ],
        },
        programDuration: {
            startAt: Number,
            endAt: Number,
        },
        framework: {
            frameworkName: String,
            _framework_id: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
            },
        },
        yearLevel: [
            {
                preRequisiteName: String,
                _preRequisite_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.PROGRAM,
                },
                yType: String,
                startWeek: Number,
                endWeek: Number,
                noOfLevel: Number,
                levels: [
                    {
                        levelName: String,
                        startWeek: Number,
                        endWeek: Number,
                    },
                ],
                certificate: { certificateName: String },
            },
        ],
        isPhaseLevel: {
            type: Boolean,
            default: false,
        },
        withoutLevel: {
            type: Boolean,
            default: false,
        },
        selectiveCourseConfiguration: [
            {
                _program_id: {
                    type: Schema.Types.ObjectId,
                },
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                noOfSelection: {
                    type: Number,
                },
                hoursType: {
                    type: String,
                },
                selectives: [],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = curriculum;
