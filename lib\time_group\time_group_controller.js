const constant = require('../utility/constants');
const time_group = require('mongoose').model(constant.TIME_GROUP);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const time_group_formate = require('./time_group_formate');
const moment = require('moment');
const ObjectId = common_files.convertToMongoObjectId;
const institution = require('mongoose').model(constant.INSTITUTION);
const infrastructure_management = require('mongoose').model(constant.INFRASTRUCTURE_MANAGEMENT);

exports.list = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { isDeleted: false, _institution_id: ObjectId(req.headers._institution_id) };
        const project = {
            _id: 1,
            type: 1,
            start_time: 1,
            end_time: 1,
            gender: 1,
            isDeleted: 1,
            isActive: 1,
        };
        const doc = await base_control.list(
            time_group,
            req.query.limit,
            req.query.pageNo,
            query,
            project,
        );
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('TIME_GROUP_NOT_FOUND'),
                        doc.data,
                    ),
                );

        //Time group check
        const query_infra = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query_infra,
        );
        const new_timing_arr = [];
        if (!infrastructure_management_data.status) {
            for (let i = 0; i < doc.data.length; i++) {
                const obj = {
                    isDeleted: doc.data[i].isDeleted,
                    isActive: doc.data[i].isActive,
                    _id: doc.data[i]._id,
                    type: doc.data[i].type,
                    start_time: doc.data[i].start_time,
                    end_time: doc.data[i].end_time,
                    gender: doc.data[i].gender,
                    isAssigned: false,
                };
                new_timing_arr.push(obj);
            }
            doc.data = new_timing_arr;
            return res
                .status(200)
                .send(
                    common_files.listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('TIME_GROUP_LIST'),
                        doc.totalDoc,
                        doc.totalPages,
                        doc.currentPage,
                        doc.data,
                    ),
                );
        }

        for (let i = 0; i < doc.data.length; i++) {
            let flag = 0;
            for (let j = 0; j < infrastructure_management_data.data.length; j++) {
                const timing_ind = infrastructure_management_data.data[j].timing.findIndex((ele) =>
                    ele._time_id.equals(doc.data[i]._id),
                );
                if (timing_ind != -1) {
                    flag = 1;
                    break;
                }
            }
            const obj = {
                isDeleted: doc.data[i].isDeleted,
                isActive: doc.data[i].isActive,
                _id: doc.data[i]._id,
                type: doc.data[i].type,
                start_time: doc.data[i].start_time,
                end_time: doc.data[i].end_time,
                gender: doc.data[i].gender,
            };
            let target_temp = {};
            if (flag == 1) target_temp = Object.assign(obj, { isAssigned: true });
            else target_temp = Object.assign(obj, { isAssigned: false });
            new_timing_arr.push(target_temp);
        }
        doc.data = new_timing_arr;

        return res
            .status(200)
            .send(
                common_files.listAllResponseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('TIME_GROUP_LIST'),
                    doc.totalDoc,
                    doc.totalPages,
                    doc.currentPage,
                    doc.data,
                ),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.list_id = async (req, res) => {
    try {
        const id = req.params.id;
        const query = { _id: id, isDeleted: false };
        const project = { _id: 1, type: 1, start_time: 1, end_time: 1, isDeleted: 1, isActive: 1 };
        const doc = await base_control.get(time_group, query, project);
        if (doc.status) {
            const formatted = time_group_formate.time_group_ID(doc.data);
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('TIME_GROUP_LIST'),
                formatted,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.insert = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = {
            _institution_id: ObjectId(req.headers._institution_id),
            type: req.body.type,
            start_time: req.body.start_time,
            end_time: req.body.end_time,
            gender: req.body.gender,
        };
        const doc = await base_control.insert(time_group, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('TIME_GROUP_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = {
            type: req.body.type,
            start_time: req.body.start_time,
            end_time: req.body.end_time,
            gender: req.body.gender,
        };
        const doc = await base_control.update(time_group, req.params.id, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('UPDATE_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.delete = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Time group check
        const query = { isDeleted: false, _institution_id: ObjectId(req.headers._institution_id) };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query,
        );
        if (infrastructure_management_data.status) {
            for (let i = 0; i < infrastructure_management_data.data.length; i++) {
                const timing_ind = infrastructure_management_data.data[i].timing.findIndex((ele) =>
                    ele._time_id.equals(req.params.id),
                );
                if (timing_ind != -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t(
                                    'TIMINGS_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT',
                                ),
                                req.t(
                                    'TIMINGS_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT',
                                ),
                            ),
                        );
            }
        }
        const doc = await base_control.delete(time_group, req.params.id);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(req, 500, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.get_setting_timings = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { isDeleted: false, _institution_id: ObjectId(req.headers._institution_id) };
        const time_group_data = await base_control.get_list(time_group, query, {
            _id: 1,
            start_time: 1,
            end_time: 1,
            gender: 1,
            type: 1,
        });
        if (!time_group_data.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('TIME_GROUP_NOT_FOUND'),
                        time_group_data.data,
                    ),
                );
        const query_infra = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query_infra,
        );
        if (!infrastructure_management_data.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, req.t('INFRA_NOT_FOUND'), []));
        const new_time_group_data = [];
        for (let t = 0; t < time_group_data.data.length; t++) {
            let flag = 0;
            for (let im = 0; im < infrastructure_management_data.data.length; im++) {
                const infra_ind = infrastructure_management_data.data[im].timing.findIndex((ele) =>
                    ele._time_id.equals(time_group_data.data[t]._id),
                );
                if (infra_ind != -1) {
                    flag = 1;
                    break;
                }
            }
            const obj = {
                _id: time_group_data.data[t]._id,
                type: time_group_data.data[t].type,
                start_time: time_group_data.data[t].start_time,
                end_time: time_group_data.data[t].end_time,
                gender: time_group_data.data[t].gender,
                isAssigned: flag == 1,
            };
            new_time_group_data.push(obj);
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('TIME_GROUP_LIST'),
                    new_time_group_data,
                ),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
