let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');
// const privilege = require('./privilege');

let role = new Schema({
    _permission_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PERMISSION,
        required: true
    },
    _privilege_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.PRIVILEGE,
        required: true
    }],
    description: {
        type: String
    },
    code: {
        type: String
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.PERMISSION_SET, role);