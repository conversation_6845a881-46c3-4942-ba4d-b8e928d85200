const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');


exports.list = (req, res, next) => {
    const schema = Joi.object().keys({
       body: Joi.object().keys({
          type: Joi.string().min(3).max(30).required().error(error =>{
              return error;
          }),
          name: Joi.string().min(3).max(50).required().error(error =>{
              return error;
          })
       }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}


exports.list_id = (req,res,next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}