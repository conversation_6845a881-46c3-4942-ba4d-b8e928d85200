const {
    CALENDAR: { INSTITUTION, PROGRAM },
} = require('../utility/constants');
const programSchema = require('../models/digi_programs');
const curriculumSchema = require('../models/digi_curriculum');
const departmentSubjectSchema = require('../models/digi_department_subject');
const sessionDeliveryTypeSchema = require('../models/digi_session_delivery_types');
const courseSchema = require('../models/digi_course');
const courseSessionOrderSchema = require('../models/digi_session_order');
const institutionCalendarsSchema = require('../models/institution_calendar');
const programCalendarSchema = require('../models/program_calendar');
const studentGroupSchema = require('../models/student_group');
const courseScheduleSchemas = require('../models/course_schedule');
const { convertToMongoObjectId } = require('../utility/common');

const getInstitutionCalendarAndProgramName = async ({ moduleType, moduleName }) => {
    let moduleData = {};
    if (moduleType === INSTITUTION) {
        moduleData = await institutionCalendarsSchema
            .findOne(
                {
                    calendar_name: moduleName,
                },
                { _id: 1 },
            )
            .lean();
    } else if (moduleType === PROGRAM) {
        moduleData = await programSchema
            .findOne(
                {
                    name: moduleName,
                },
                { _id: 1 },
            )
            .lean();
    }
    return moduleData ? moduleData._id : undefined;
};

const fetchId = async (moduleType, moduleName) => {
    const id = await getInstitutionCalendarAndProgramName({ moduleType, moduleName });
    if (!id) {
        return {
            statusCode: 404,
            message: `${moduleType} Not Found`,
        };
    }
    return id;
};

const removeInstitutionCalendar = async ({ calendarId }) => {
    console.log(
        await institutionCalendarsSchema.deleteOne({
            _id: convertToMongoObjectId(calendarId),
        }),
        ' InstitutionCalendar',
    );
};

const removeProgramCalendar = async ({ calendarId, programId }) => {
    console.log(
        await programCalendarSchema.deleteMany({
            ...(calendarId && { _institution_calendar_id: convertToMongoObjectId(calendarId) }),
            _program_id: convertToMongoObjectId(programId),
        }),
        ' ProgramCalendar',
    );
};

const removeStudentGroup = async ({ calendarId, programId }) => {
    console.log(
        await studentGroupSchema.deleteMany({
            ...(calendarId && { _institution_calendar_id: convertToMongoObjectId(calendarId) }),
            'master._program_id': convertToMongoObjectId(programId),
        }),
        ' StudentGroup',
    );
};

const removeCourseSchedule = async ({ calendarId, programId }) => {
    console.log(
        await courseScheduleSchemas.deleteMany({
            ...(calendarId && { _institution_calendar_id: convertToMongoObjectId(calendarId) }),
            _program_id: convertToMongoObjectId(programId),
        }),
        ' CourseSchedule',
    );
};

const removeProgram = async ({ programId }) => {
    console.log(
        await programSchema.deleteOne({
            _id: convertToMongoObjectId(programId),
        }),
        ' Program',
    );
};

const removeCurriculum = async ({ programId }) => {
    console.log(
        await curriculumSchema.deleteMany({
            _program_id: convertToMongoObjectId(programId),
        }),
        ' Curriculum',
    );
};

const removeDepartmentSubject = async ({ programId }) => {
    console.log(
        await departmentSubjectSchema.deleteMany({
            program_id: convertToMongoObjectId(programId),
        }),
        ' Department Subject',
    );
};

const removeSessionDeliveryType = async ({ programId }) => {
    console.log(
        await sessionDeliveryTypeSchema.deleteMany({
            _program_id: convertToMongoObjectId(programId),
        }),
        ' Session Delivery Type',
    );
};

const removeCourse = async ({ programId }) => {
    console.log(
        await courseSchema.deleteMany({
            _program_id: convertToMongoObjectId(programId),
        }),
        ' Course',
    );
};

const removeCourseSessionOrder = async ({ programId }) => {
    console.log(
        await courseSessionOrderSchema.deleteMany({
            _program_id: convertToMongoObjectId(programId),
        }),
        ' Course Session Order',
    );
};

module.exports = {
    fetchId,
    removeInstitutionCalendar,
    removeProgramCalendar,
    removeStudentGroup,
    removeCourseSchedule,
    removeProgram,
    removeCurriculum,
    removeDepartmentSubject,
    removeSessionDeliveryType,
    removeCourse,
    removeCourseSessionOrder,
};
