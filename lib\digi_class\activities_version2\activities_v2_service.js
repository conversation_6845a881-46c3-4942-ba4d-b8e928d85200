const { convertToMongoObjectId } = require('../../utility/common');
const {
    DC_STAFF,
    DC_STUDENT,
    DRAFT,
    NOT_DRAFT,
    SCHEDULE_TYPES: { REGULAR },
} = require('../../utility/constants');
const courseScheduledSchema = require('../../models/course_schedule');
const activitySchema = require('../../models/activities');
const questionSchema = require('../../models/question');
const { getCoursesForActivity } = require('../document_manager/document_manager_service');

const formattedQuestions = async ({ activityQuestions, answeredQuestions }) => {
    const questions = [];
    let answeredCount = 0;
    let studentTextAnswer;
    let studentCorrectAnswered = 0;
    for (const activityQuestionElement of activityQuestions) {
        const { _id, options } = activityQuestionElement;
        if (options && options.length > 0) {
            for (const option of options) {
                const { _id: optionId, text: optionText, answer } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntryElement) =>
                            studentEntryElement._optionId &&
                            studentEntryElement._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (answer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = answer;
                    studentAnsweredOptionId = optionId;
                    studentTextAnswer = optionText;
                }
            }
        }
        if (!options.length) {
            studentTextAnswer =
                answeredQuestions &&
                answeredQuestions.find(
                    (studentEntryElement) =>
                        studentEntryElement._questionId.toString() === _id.toString(),
                );
            studentTextAnswer = studentTextAnswer ? studentTextAnswer.textAnswer : null;
        }

        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestionElement) =>
                    answeredQuestionElement._questionId.toString() === _id.toString(),
            )
        ) {
            answeredCount++;
        }

        questions.push({
            _id,
        });
    }
    const questionEntry = questions.sort((a, b) => {
        return a.order - b.order;
    });

    return {
        questions: questionEntry,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

const getCorrectionStatus = ({ correctionType, students, userType = 'staff', userId }) => {
    if (correctionType === 'manual' && userType === 'student' && userId !== '') {
        const filteredStudent = students.find(
            (studentElement) => studentElement._studentId.toString() === userId.toString(),
        );
        if (filteredStudent) {
            return filteredStudent.status === 'published' ? 'VIEW RESULT' : 'NOT YET PUBLISHED';
        }
        return 'NOT YET PUBLISHED';
    }

    if (correctionType === 'manual' && userType === 'staff') {
        const totalStudentsCount = students.length;
        const savedStudents = students.filter(
            (studentElement) =>
                studentElement && studentElement.status && studentElement.status === 'saved',
        );
        const publishedStudents = students.filter(
            (studentElement) =>
                studentElement && studentElement.status && studentElement.status === 'published',
        );
        const savedStudentsCount = savedStudents.length;
        const publishedStudentsCount = publishedStudents.length;
        return totalStudentsCount !== 0
            ? totalStudentsCount === publishedStudentsCount
                ? 'VIEW RESULT'
                : totalStudentsCount > savedStudentsCount
                ? 'CORRECTION PENDING'
                : 'MAKE CORRECTION'
            : '';
    }
    return '';
};

const studentGroupByGroupName = (studentGroups) => {
    const grouped = {};
    studentGroups.forEach((groupElement) => {
        const { group_name } = groupElement;
        if (grouped.hasOwnProperty(group_name)) {
            grouped[group_name].session_group.push(...groupElement.session_group);
        } else {
            grouped[group_name] = {
                group_name,
                session_group: [...groupElement.session_group],
            };
        }
    });
    const formattedOutput = Object.values(grouped);
    return formattedOutput;
};

const getAllActivity = async ({
    type,
    userId,
    limit,
    page,
    _institution_calendar_id,
    courseAdmin,
    courseId,
    sessionId,
    scheduleId,
    mergeStatus,
    search,
    mode,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    headerInstitutionCalendarId,
    rotation_count,
}) => {
    try {
        const project = {
            createdAt: 0,
            updatedAt: 0,
            shuffleQuestionOrder: 0,
            quizStartedBy: 0,
            seconds: 0,
            isActive: 0,
            isDeleted: 0,
        };

        let activityQuery;
        let notInSchedule = true;
        const activityOrQuery = [];
        const activityAndQuery = [];
        if (sessionId) {
            activityQuery = {
                $or: [
                    {
                        'sessionFlowIds._id': convertToMongoObjectId(sessionId),
                        createdBy: { $ne: convertToMongoObjectId(userId) },
                        status: { $ne: DRAFT },
                    },
                    {
                        'sessionFlowIds._id': convertToMongoObjectId(sessionId),
                        createdBy: convertToMongoObjectId(userId),
                    },
                    {
                        sessionId: convertToMongoObjectId(sessionId),
                        createdBy: { $ne: convertToMongoObjectId(userId) },
                        status: { $ne: DRAFT },
                    },
                    {
                        sessionId: convertToMongoObjectId(sessionId),
                        createdBy: convertToMongoObjectId(userId),
                    },
                ],
                isDeleted: false,
            };
        } else {
            activityQuery = { isDeleted: false };
        }

        if (courseId) {
            activityQuery._program_id = convertToMongoObjectId(_program_id);
            activityQuery.year_no = year_no;
            activityQuery.level_no = level_no;
            activityQuery.term = term;
            activityQuery.rotation = rotation;
            activityQuery._institution_calendar_id = headerInstitutionCalendarId;
            if (rotation_count) {
                activityQuery.rotation_count = parseInt(rotation_count);
            }
        }

        if (search) {
            activityQuery.name = { $regex: search, $options: 'i' };
        }

        if (mode && mode !== NOT_DRAFT) {
            activityQuery.status = mode;
        }
        if (scheduleId) activityQuery.scheduleIds = convertToMongoObjectId(scheduleId);
        if (courseId) activityQuery.courseId = convertToMongoObjectId(courseId);
        if (type === DC_STUDENT) {
            if (mode && mode === NOT_DRAFT) {
                activityQuery.status = { $ne: DRAFT };
            } else {
                if (mode && mode !== NOT_DRAFT) {
                    activityQuery.$and = [{ status: { $ne: DRAFT } }, { status: mode }];
                } else {
                    activityQuery.status = { $ne: DRAFT };
                }
            }
            if (!scheduleId) {
                const studentQuery = {
                    'students._id': convertToMongoObjectId(userId),
                    _institution_calendar_id: convertToMongoObjectId(headerInstitutionCalendarId),
                    isDeleted: false,
                    isActive: true,
                };
                const courseSchedules = await courseScheduledSchema
                    .find(studentQuery, {
                        _id: 1,
                    })
                    .lean();
                if (courseSchedules.length) {
                    const scheduleIds = courseSchedules.map((scheduleElement) =>
                        convertToMongoObjectId(scheduleElement._id),
                    );
                    activityQuery.scheduleIds = { $in: scheduleIds };
                } else {
                    notInSchedule = false;
                }
            }
            if (mergeStatus) {
                const scheduleQuery = {
                    'session._session_id': convertToMongoObjectId(sessionId),
                    merge_status: mergeStatus,
                    isDeleted: false,
                    isActive: true,
                };
                const courseSchedule = await courseScheduledSchema
                    .findOne(scheduleQuery, {
                        'merge_with.schedule_id': 1,
                    })
                    .lean();
                if (courseSchedule) {
                    const { merge_with } = courseSchedule;
                    const courseSchedules = merge_with.map(
                        (mergeWithElement) => mergeWithElement.schedule_id,
                    );

                    if (courseSchedules.length) {
                        const scheduleIds = courseSchedules.map((scheduleElement) =>
                            convertToMongoObjectId(scheduleElement),
                        );
                        activityQuery.scheduleId = { $in: scheduleIds };
                    }
                }
            }
        }
        let courseIds = [];
        if (!courseId)
            courseIds = await getCoursesForActivity(
                userId,
                courseAdmin,
                headerInstitutionCalendarId,
            );
        const activityList = [];
        if (!courseId && courseIds.length) {
            courseIds = courseIds.map((courseElement) => {
                const mappedCourseInfo = {
                    _institution_calendar_id: courseElement._institution_calendar_id,
                    _program_id: courseElement._program_id,
                    term: courseElement.term,
                    year_no: courseElement.year_no,
                    level_no: courseElement.level_no,
                    courseId: courseElement._id,
                };
                if (courseElement.rotation_count)
                    mappedCourseInfo.rotation_count = courseElement.rotation_count;
                activityOrQuery.push(mappedCourseInfo);
                return mappedCourseInfo;
            });
        } else if (!courseId) {
            return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: activityList };
        }
        if (type === DC_STAFF) {
            const staffQuery = {
                isDeleted: false,
            };
            if (courseAdmin !== 'true') {
                staffQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
            if (courseId) {
                staffQuery._course_id = convertToMongoObjectId(courseId);
                staffQuery._program_id = convertToMongoObjectId(_program_id);
                staffQuery.year_no = year_no;
                staffQuery.level_no = level_no;
                staffQuery.term = term;
                staffQuery.rotation = rotation;
                staffQuery._institution_calendar_id = convertToMongoObjectId(
                    headerInstitutionCalendarId,
                );
                if (rotation_count) {
                    staffQuery.rotation_count = parseInt(rotation_count);
                }
            }

            const courseSchedules = await courseScheduledSchema
                .find(staffQuery, {
                    _id: 1,
                })
                .lean();

            if (mode && mode === NOT_DRAFT) {
                activityQuery.status = { $ne: DRAFT };
            } else {
                const courseScheduleIds = [
                    ...new Set(
                        courseSchedules.map((scheduleIdElement) =>
                            convertToMongoObjectId(scheduleIdElement._id),
                        ),
                    ),
                ];
                if (
                    headerInstitutionCalendarId &&
                    headerInstitutionCalendarId.toString() !== _institution_calendar_id.toString()
                ) {
                    activityAndQuery.push(
                        {
                            createdBy: { $ne: convertToMongoObjectId(userId) },
                            status: COMPLETED,
                        },
                        { createdBy: convertToMongoObjectId(userId), status: COMPLETED },
                    );
                } else {
                    activityAndQuery.push(
                        {
                            createdBy: { $ne: convertToMongoObjectId(userId) },
                            status: { $ne: DRAFT },
                        },
                        { createdBy: convertToMongoObjectId(userId) },
                    );
                }

                if (courseScheduleIds.length === 0) {
                    activityQuery.createdBy = convertToMongoObjectId(userId);
                }
            }
        }
        if (activityAndQuery.length || activityOrQuery.length) {
            const activityAndQueryArray = [];
            if (activityOrQuery.length) activityAndQueryArray.push({ $or: activityOrQuery });
            if (activityAndQuery.length) activityAndQueryArray.push({ $or: activityAndQuery });
            activityQuery.$and = activityAndQueryArray;
        }
        // get query activities data
        let activities;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            if (notInSchedule) {
                activities = await activitySchema
                    .find(activityQuery, project)
                    .populate({ path: 'createdBy', select: { _id: 1, name: 1 } })
                    .populate({
                        path: 'courseId',
                        select: {
                            course_code: 1,
                            course_name: 1,
                            versionNo: 1,
                            versioned: 1,
                            versionName: 1,
                            versionedFrom: 1,
                            versionedCourseIds: 1,
                        },
                    })
                    .lean()
                    .sort({ status: -1, createdAt: -1 })
                    .skip(perPage * (pageNo - 1))
                    .limit(perPage)
                    .exec();

                //get all document
                totalDoc = await activitySchema.find(activityQuery).lean().countDocuments().exec();
            } else {
                activities = [];
                totalDoc = 0;
            }

            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        }

        // Questions gathering
        let activityQuestionIds = [];
        let activitySessionIds = [];
        let activityCourseScheduleIds = [];
        for (const activityElement of activities) {
            activityQuestionIds = [
                ...activityQuestionIds,
                ...activityElement.questions.map((questionElement) =>
                    convertToMongoObjectId(questionElement._id),
                ),
            ];
            if (activityElement.sessionFlowIds) {
                activitySessionIds = [
                    ...activitySessionIds,
                    ...activityElement.sessionFlowIds.map((sessionFlowElement) =>
                        convertToMongoObjectId(sessionFlowElement._id),
                    ),
                ];
            }
            if (activityElement.scheduleIds) {
                activityCourseScheduleIds = [
                    ...activityCourseScheduleIds,
                    ...activityElement.scheduleIds.map((scheduleElement) =>
                        convertToMongoObjectId(scheduleElement),
                    ),
                ];
            }
        }

        const activityQuestionData = await questionSchema
            .find(
                {
                    _id: { $in: activityQuestionIds },
                    isDeleted: false,
                },
                {
                    _id: 1,
                    type: 1,
                    order: 1,
                    options: 1,
                },
            )
            .lean();

        // Course Schedule Data gathering
        const courseScheduleList = await courseScheduledSchema
            .find(
                {
                    $or: [
                        {
                            'session._session_id': { $in: activitySessionIds },
                        },
                        {
                            _id: { $in: activityCourseScheduleIds },
                        },
                    ],
                },
                {
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    'students._studentId': 1,
                    'students._id': 1,
                    session: 1,
                    'staffs._staff_id': 1,
                    'student_groups.group_id': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    'student_groups.session_group.session_group_id': 1,
                },
            )
            .lean();
        // Merged Session gathering
        let mergedScheduleIds = [];
        for (const scheduleElement of courseScheduleList) {
            if (scheduleElement.merge_status) {
                mergedScheduleIds = [
                    ...mergedScheduleIds,
                    ...scheduleElement.merge_with.map((mergeWithElement) =>
                        convertToMongoObjectId(mergeWithElement.schedule_id),
                    ),
                ];
            }
        }
        let mergedCourseScheduleData = [];
        if (mergedScheduleIds.length)
            mergedCourseScheduleData = await courseScheduledSchema
                .find(
                    {
                        _id: { $in: mergedScheduleIds },
                    },
                    {
                        'students._studentId': 1,
                        'students._id': 1,
                        session: 1,
                        'staffs._staff_id': 1,
                        'student_groups.group_id': 1,
                        'student_groups.group_name': 1,
                        'student_groups.session_group.group_name': 1,
                        'student_groups.session_group.session_group_id': 1,
                    },
                )
                .lean();

        for (const activityElement of activities) {
            const {
                questions,
                students,
                studentCompletedQuiz,
                scheduleIds,
                sessionFlowIds,
                correctionType,
            } = activityElement;
            let uniqueStudents;
            let studentGroupName;
            const questionDetails = [];
            if (questions.length) {
                const questionIds = questions
                    .sort((a, b) => {
                        return a.order - b.order;
                    })
                    .map((questionElement) => questionElement._id.toString());
                for (questionIdElement of questionIds) {
                    const questionDataElement = activityQuestionData.find(
                        (ele) => ele._id.toString() === questionIdElement.toString(),
                    );
                    if (questionDataElement) questionDetails.push(questionDataElement);
                }
            }
            let sessionDetails = [];
            if (sessionFlowIds) {
                const sessionIds = sessionFlowIds.map((sessionFlowElement) =>
                    sessionFlowElement._id.toString(),
                );

                const sessions = courseScheduleList
                    .filter(
                        (sessionElement) =>
                            sessionElement.session &&
                            sessionElement.session._session_id &&
                            sessionIds.find(
                                (sessionIdElement) =>
                                    sessionIdElement.toString() ===
                                    sessionElement.session._session_id.toString(),
                            ),
                    )
                    .map((sessionElement) => {
                        return {
                            _id: sessionElement._id,
                            title: sessionElement.title,
                            type: sessionElement.type,
                            session: sessionElement.session,
                        };
                    });
                sessionDetails = sessionFlowIds.map((sessionIdElement) => {
                    const sessionDetail = sessions.find(
                        (sessionEntry) =>
                            (sessionEntry.session &&
                                sessionEntry.session._session_id.toString() ===
                                    sessionIdElement._id.toString()) ||
                            (sessionEntry.type === sessionIdElement.type &&
                                sessionEntry._id.toString() === sessionIdElement._id.toString()),
                    );
                    if (sessionDetail) {
                        const { session, _id, title, type } = sessionDetail;
                        if (session) {
                            const { _session_id } = session;
                            if (
                                session &&
                                _session_id &&
                                sessionIds.includes(_session_id.toString())
                            ) {
                                return {
                                    _id: session._session_id,
                                    s_no: session.s_no,
                                    delivery_symbol: session.delivery_symbol,
                                    delivery_no: session.delivery_no,
                                    session_type: session.session_type,
                                    session_topic: session.session_topic,
                                    type: REGULAR,
                                };
                            }
                        }
                        if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                            return { _id, title, type };
                        }
                    }
                });
            }

            // if student end the quiz time based
            const userCompletedQuizStatus = studentCompletedQuiz.find(
                (studentCompletedElement) =>
                    studentCompletedElement.toString() === userId.toString(),
            );
            const completedQuizStatus = !!userCompletedQuizStatus;
            let answeredQuestions;
            if (type === DC_STUDENT) {
                answeredStudent = students.find(
                    (studentElement) => studentElement._studentId.toString() === userId.toString(),
                );
                if (answeredStudent) answeredQuestions = answeredStudent.questions;
            }
            let activityQuestions;
            if (questionDetails && questionDetails.length) {
                activityQuestions = questionDetails.map((selectedQuestionElement) => {
                    const { _id } = selectedQuestionElement;
                    const questionType = questions.find((q) => q._id.toString() === _id.toString());
                    if (questionType) {
                        selectedQuestionElement.type = questionType.type;
                        selectedQuestionElement.order = questionType.order;
                    }
                    return selectedQuestionElement;
                });
                activityQuestions = await formattedQuestions({
                    activityQuestions,
                    answeredQuestions,
                });
            }

            let courseSchedule;

            if (scheduleIds && scheduleIds.length) {
                const courseScheduleData = courseScheduleList.filter((courseElement) =>
                    scheduleIds.find(
                        (scheduleElement) =>
                            scheduleElement.toString() === courseElement._id.toString(),
                    ),
                );

                const mergedStudents = new Set();
                const mergedStudentGroups = new Set();
                for (const courseScheduleElement of courseScheduleData) {
                    const { students, merge_status, merge_with, student_groups } =
                        courseScheduleElement;
                    let scheduleStudents = students;
                    let scheduleStudentGroups = student_groups;
                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWithElement) =>
                            convertToMongoObjectId(mergeWithElement.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const mergeScheduleData = mergedCourseScheduleData.filter((ele) =>
                                scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                            );
                            scheduleStudents = mergeScheduleData.flatMap(
                                (scheduleElement) => scheduleElement.students,
                            );
                            scheduleStudentGroups = mergeScheduleData.flatMap(
                                (scheduleElement) => scheduleElement.student_groups,
                            );
                        }
                    }
                    scheduleStudents.forEach((studentElement) =>
                        mergedStudents.add(JSON.stringify(studentElement)),
                    );
                    scheduleStudentGroups.forEach((groupsElement) =>
                        mergedStudentGroups.add(JSON.stringify(groupsElement)),
                    );
                }

                uniqueStudents = [...mergedStudents].map((studentElement) =>
                    JSON.parse(studentElement),
                );
                const uniqueStudentGroups = [...mergedStudentGroups].map((groupsElement) =>
                    JSON.parse(groupsElement),
                );

                studentGroupName = studentGroupByGroupName(uniqueStudentGroups)
                    .map((studentGroupElement) => {
                        const { group_name, session_group } = studentGroupElement;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((sessionGroupElement) => {
                                let groupNames = sessionGroupElement.group_name
                                    .split('-')
                                    .slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    })
                    .join(', ');
                courseSchedule = courseScheduleData.find(
                    (scheduleElement) =>
                        scheduleElement.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleElement.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );
            }

            const correctionStatus = getCorrectionStatus({
                correctionType,
                students,
                userType: type,
                userId,
            });

            const activityData = {
                ...activityElement,
                studentGroupName,
                studentCompletedQuiz: completedQuizStatus,
                scheduleIds,
                sessionFlowIds: sessionDetails || [],
                questions: activityQuestions ? activityQuestions.questions : [],
                answeredCount: activityQuestions ? activityQuestions.answeredCount : 0,
                totalQuestionCount: activityQuestions ? activityQuestions.totalQuestionCount : 0,
                studentCorrectAnsweredCount: activityQuestions
                    ? activityQuestions.studentCorrectAnswered
                    : 0,
                totalStudentAnsweredCount: students.length,
                totalStudentCount: uniqueStudents ? uniqueStudents.length : 0,
                sessionType:
                    courseSchedule && courseSchedule.type ? courseSchedule.type : undefined,
                _course_id:
                    courseSchedule && courseSchedule._course_id
                        ? courseSchedule._course_id
                        : undefined,
                course_name:
                    courseSchedule && courseSchedule.course_name
                        ? courseSchedule.course_name
                        : undefined,
                merge_status: courseSchedule ? courseSchedule.merge_status : undefined,
                scheduleId: courseSchedule && courseSchedule._id ? courseSchedule._id : undefined,
                sessionId:
                    courseSchedule && courseSchedule.session
                        ? courseSchedule.session._session_id
                        : undefined,
                isNewActivity: !!(
                    activityElement.correctionType && activityElement.correctionType !== ''
                ),
                correctionStatus,
            };

            delete activityData.students;

            activityList.push(activityData);
        }

        if (limit && page) {
            return { totalDoc, totalPages, currentPage, activities: activityList };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: activityList };
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = {
    getAllActivity,
    studentGroupByGroupName,
};
