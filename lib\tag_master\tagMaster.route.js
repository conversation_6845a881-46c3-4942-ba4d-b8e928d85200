const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    manageTagMasterSettingsValidator,
    updatedTagMasterSettingsValidator,
    deleteTagMasterSettingsValidator,
    updatedOverAllTagMasterSettingsValidator,
} = require('./tagMaster.validator');
const {
    getTagMasterSettings,
    manageTagMasterGroupFamilySettings,
    updatedTagMasterSettings,
    deleteTagMasterSettings,
    updatedOverAllTagMasterSettings,
} = require('./tagMaster.controller');

router.get('/getTagMasterSettings', catchAsync(getTagMasterSettings));
router.post(
    '/manageTagMasterGroupFamilySettings',
    manageTagMasterSettingsValidator,
    catchAsync(manageTagMasterGroupFamilySettings),
);
router.put(
    '/updatedTagMasterSettings',
    updatedTagMasterSettingsValidator,
    catchAsync(updatedTagMasterSettings),
);
router.delete(
    '/deleteTagMasterSettings',
    deleteTagMasterSettingsValidator,
    catchAsync(deleteTagMasterSettings),
);
router.put(
    '/updatedOverAllTagMasterSettings/:_id',
    updatedOverAllTagMasterSettingsValidator,
    catchAsync(updatedOverAllTagMasterSettings),
);
module.exports = router;
