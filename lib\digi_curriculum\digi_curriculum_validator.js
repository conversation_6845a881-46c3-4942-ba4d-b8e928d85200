// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.year_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    year_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.level_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    level_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.curriculum_insert = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _program_id: Joi.string().alphanum().length(24),
                    program_name: Joi.string().min(2).max(100).required(),
                    curriculum_name: Joi.string().min(2).max(100).required(),
                    hour_as: Joi.string().min(2).max(100).required(),
                    value_as: Joi.string().min(2).max(100).required(),
                    _framework_id: Joi.string().alphanum().length(24),
                    framework_name: Joi.string().min(2).max(100),
                    start_week: Joi.number(),
                    end_week: Joi.number(),
                    start_at: Joi.number(),
                    end_at: Joi.number(),
                    is_pre_requisite: Joi.bool().required(),
                    year_level: Joi.array().items(
                        Joi.object({
                            pre_requisite_name: Joi.string()
                                .min(2)
                                .max(100)
                                .when('is_pre_requisite', {
                                    is: true,
                                    then: Joi.required(),
                                    otherwise: Joi.allow('').optional(),
                                }),
                            _pre_requisite_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .when('is_pre_requisite', {
                                    is: true,
                                    then: Joi.required(),
                                    otherwise: Joi.allow('').optional(),
                                }),
                            y_type: Joi.string().min(2).max(100).required(),
                            no_of_level: Joi.number(),
                            levels: Joi.array().items(
                                Joi.object({
                                    level_name: Joi.string().min(1).max(100).required(),
                                    start_week: Joi.number(),
                                    end_week: Joi.number(),
                                }),
                            ),
                        }),
                    ),
                    /* _pre_requisite_id: Joi.string().alphanum().length(24).when('is_pre_requisite', { is: true, then: Joi.required(), otherwise: Joi.allow('').optional() }).error(error => {
                return error;
            }),
            _pre_requisite_name: Joi.string().min(2).max(100).when('is_pre_requisite', { is: true, then: Joi.required(), otherwise: Joi.allow('').optional() }).error(error => {
                return error;
            }), */
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
