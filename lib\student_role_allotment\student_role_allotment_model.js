const mongoose = require("mongoose");
const { Schema } = mongoose;

const schema = new Schema(
  {
    academic_year: {
      type: String,
      required: true,
      trim: true,
    },

    program_id: {
      type: String,
      required: true,
      trim: true,
    },

    term: {
      type: String,
      required: true,
      trim: true,
    },

    student_group_id: {
      type: String,
      required: true,
      trim: true,
    },

    student_id: {
      type: String,
      required: true,
      trim: true,
    },

    gender: {
      type: String,
      required: true,
      trim: true,
    },

    role_id: {
      type: String,
      required: true,
      trim: true,
    },

    course_id: {
      type: String,
      required: true,
      trim: true,
    },

    course_name: {
      type: String,
      required: true,
      trim: true,
    },

    isDeleted: {
      type: Boolean,
      default: false,
    },

    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);
module.exports = mongoose.model("student_role_allotments", schema);
