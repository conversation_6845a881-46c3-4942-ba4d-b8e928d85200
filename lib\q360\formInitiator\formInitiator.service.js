const { convertToMongoObjectId, clone } = require('../../utility/common');
const qapcPermissionSchema = require('../rolePermission/qapcPermission.model');
const formCourseGroupSchema = require('../categoryForm/formCourseGroups.module');
const qapcFormInitiatorSchema = require('./qapcFormInitiator.model');
const qapcGuideResourceSchema = require('./formGuideResources.model');
const qapcIncorporateSectionSchema = require('./qapcIncorporateSection.model');
const qapcActionSchema = require('../rolePermission/action.model');
const formSettingCourseSchema = require('../categoryForm/formSettingCourses.model');
const formSettingSchema = require('../categoryForm/formSetting.model');
const qapcFormCategorySchema = require('../categories/qapcCategory.model');
const {
    PUBLISHED,
    FORM_INITIATOR,
    FORM_APPROVER,
    VIEW,
    EDIT,
    DRAFT,
    YET_TO_START,
} = require('../../utility/constants');
const { send_email } = require('../../utility/common_functions');
const {
    getUniqueGroupIdsAndFormDetails,
    getPermissionEmails,
    removeCurrentUserEmail,
} = require('../formApprover/formApprover.service');

const QAPCUserPermissionList = async ({ userId, roleId, subModuleType }) => {
    try {
        const formRolePermission = [
            { $eq: ['$$item.isDeleted', false] },
            { $eq: ['$$item.subModuleName', subModuleType] },
            {
                $eq: ['$$item.qapcRoleId', convertToMongoObjectId(roleId)],
            },
        ];
        const subModuleAction = {
            subName: '$_id.subName',
            actionIds: {
                $reduce: {
                    input: '$actionIds',
                    initialValue: [],
                    in: { $setUnion: ['$$value', '$$this'] },
                },
            },
            levelIndex: '$_id.levelIndex',
        };
        const qapcPermissionList = await qapcPermissionSchema.aggregate([
            {
                $match: {
                    $and: [
                        {
                            $or: [
                                { userId: convertToMongoObjectId(userId) },
                                { guestUserId: convertToMongoObjectId(userId) },
                            ],
                        },
                        {
                            qapcRoleIds: { $in: [convertToMongoObjectId(roleId)] },
                        },
                    ],
                },
            },
            {
                $addFields: {
                    'permissions.cfpc': {
                        $filter: {
                            input: '$permissions.cfpc',
                            as: 'item',
                            cond: {
                                $and: formRolePermission,
                            },
                        },
                    },
                    'permissions.pccf': {
                        $filter: {
                            input: '$permissions.pccf',
                            as: 'item',
                            cond: {
                                $and: formRolePermission,
                            },
                        },
                    },
                },
            },
            {
                $addFields: {
                    formGroupIds: {
                        $concatArrays: [
                            {
                                $filter: {
                                    input: '$permissions.cfpc',
                                    as: 'item',
                                    cond: {
                                        $and: [{ $gt: [{ $size: '$$item.formCourseGroupId' }, 0] }],
                                    },
                                },
                            },
                            {
                                $filter: {
                                    input: '$permissions.pccf',
                                    as: 'item',
                                    cond: {
                                        $and: [{ $gt: [{ $size: '$$item.formCourseGroupId' }, 0] }],
                                    },
                                },
                            },
                        ],
                    },
                },
            },
            {
                $unwind: {
                    path: '$formGroupIds',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: '$formGroupIds.formCourseGroupId',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $unwind: {
                    path: '$formGroupIds.institutionCalendarIds',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    groupingFormGroupKey: {
                        formGroupId: '$formGroupIds.formCourseGroupId',
                        subName: '$formGroupIds.subModuleName',
                        calendarId: {
                            $cond: {
                                if: { $eq: ['$formGroupIds.selectedAcademic', true] },
                                then: '$formGroupIds.institutionCalendarIds',
                                else: '$$REMOVE',
                            },
                        },
                        levelIndex: {
                            $cond: {
                                if: { $eq: ['$formGroupIds.subModuleName', 'Form Approver'] },
                                then: '$formGroupIds.approverLevelIndex',
                                else: '$$REMOVE',
                            },
                        },
                    },
                },
            },
            {
                $group: {
                    _id: '$groupingFormGroupKey',
                    actionIds: { $addToSet: '$formGroupIds.actionId' },
                    calendarIds: { $addToSet: '$groupingFormGroupKey.calendarId' },
                    selectedAcademicTrue: { $first: '$formGroupIds.selectedAcademic' },
                    academicYear: { $first: '$formGroupIds.academicYear' },
                },
            },
            {
                $project: {
                    _id: 0,
                    formGroupId: '$_id.formGroupId',
                    subModuleName: subModuleAction,
                    calendarIds: 1,
                    selectedAcademicTrue: 1,
                    academicYear: 1,
                },
            },
            {
                $group: {
                    _id: null,
                    categoryFormGroupData: {
                        $addToSet: {
                            formGroupId: '$formGroupId',
                            subModuleName: '$subModuleName.subName',
                            actionIds: '$subModuleName.actionIds',
                            levelIndex: '$subModuleName.levelIndex',
                            calendarIds: '$calendarIds',
                            selectedAcademicTrue: '$selectedAcademicTrue',
                            academicYear: '$academicYear',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    categoryFormGroupData: 1,
                },
            },
        ]);
        return { qapcPermissionList: qapcPermissionList[0] };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const updateCreateDocument = async ({ updateMinimumCount }) => {
    try {
        const categoryFormBulkUpdate = [];
        let filter;
        let update;
        updateMinimumCount.forEach((formElement) => {
            filter = {
                _id: convertToMongoObjectId(formElement.categoryFormGroupId),
            };
            update = {
                $set: {
                    createdDocument: formElement.createdDocument,
                },
            };
            categoryFormBulkUpdate.push({
                updateOne: {
                    filter,
                    update,
                    upsert: true,
                },
            });
        });
        await formCourseGroupSchema.bulkWrite(categoryFormBulkUpdate);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const increaseMinimumCount = async ({ selectedInstitution }) => {
    try {
        const categoryFormGroupData = await formCourseGroupSchema
            .find(
                {
                    _id: {
                        $in: selectedInstitution.map((groupElement) =>
                            convertToMongoObjectId(groupElement.categoryFormGroupId),
                        ),
                    },
                },
                {
                    createdDocument: 1,
                },
            )
            .lean();
        const updateMinimumCount = [];
        categoryFormGroupData.forEach((groupElement) => {
            selectedInstitution.forEach((selectedElement) => {
                if (
                    selectedElement.categoryFormGroupId &&
                    selectedElement.categoryFormGroupId.toString() === groupElement._id.toString()
                ) {
                    const updatedDocuments = groupElement.createdDocument.map((documentElement) => {
                        const existingCalender = selectedElement.formCalenderIds.find(
                            (calenderIdElement) =>
                                calenderIdElement.institutionCalenderId.toString() ===
                                documentElement.institutionCalenderId.toString(),
                        );

                        if (existingCalender) {
                            return {
                                ...documentElement,
                                minimum:
                                    existingCalender.countIncrease && !existingCalender.isDeleted
                                        ? documentElement.minimum + 1
                                        : Math.max(documentElement.minimum - 1, 0),
                            };
                        }

                        return documentElement;
                    });
                    const newCalender = selectedElement.formCalenderIds
                        .filter(
                            (calenderElement) =>
                                !groupElement.createdDocument.some(
                                    (documentElement) =>
                                        documentElement.institutionCalenderId.toString() ===
                                        calenderElement.institutionCalenderId.toString(),
                                ) &&
                                !calenderElement.isDeleted &&
                                calenderElement.countIncrease,
                        )
                        .map((calenderElement) => ({
                            institutionCalenderId: convertToMongoObjectId(
                                calenderElement.institutionCalenderId,
                            ),
                            minimum: 1,
                        }));

                    updateMinimumCount.push({
                        categoryFormGroupId: groupElement._id,
                        createdDocument: [...updatedDocuments, ...newCalender],
                    });
                }
            });
        });
        return { updateMinimumCount };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCreatedFormList = async ({ selectedInstitutionForm }) => {
    try {
        //get form id
        const formInitiatorIds = selectedInstitutionForm
            .filter((formElement) => formElement.formInitiatorId)
            .map((formElement) => convertToMongoObjectId(formElement.formInitiatorId));
        return await qapcFormInitiatorSchema
            .find(
                {
                    _id: { $in: formInitiatorIds },
                },
                {
                    formCalenderIds: 1,
                    createdUserIds: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const updateFormData = async ({ existingFormInitiatorData, selectedInstitutionForm, userId }) => {
    try {
        const updatedFormData = [];
        const createUser = convertToMongoObjectId(userId);
        existingFormInitiatorData.forEach((formInitiatorElement) => {
            const selectedElement = selectedInstitutionForm.find(
                (selectedElement) =>
                    selectedElement.formInitiatorId &&
                    selectedElement.formInitiatorId.toString() ===
                        formInitiatorElement._id.toString(),
            );

            if (selectedElement) {
                // update existing element
                const calendarMap = new Map();
                formInitiatorElement.formCalenderIds.forEach((calendarElement) => {
                    calendarMap.set(calendarElement.institutionCalenderId.toString(), {
                        ...calendarElement,
                    });
                });
                const userIdMap = new Map();
                formInitiatorElement.createdUserIds.forEach((userIdElement) => {
                    userIdMap.set(userIdElement.userId, {
                        ...userIdElement,
                    });
                });
                if (userIdMap.has(createUser)) {
                    const userData = userIdMap.get(createUser);
                    userData.isDeleted = userData.isDeleted || false;
                }

                selectedElement.formCalenderIds.forEach((formCalendarIdElement) => {
                    const calendarId = formCalendarIdElement.institutionCalenderId.toString();

                    if (calendarMap.has(calendarId)) {
                        const calendarData = calendarMap.get(calendarId);
                        if (formCalendarIdElement.isDeleted) {
                            calendarData.isDeleted = true;
                            calendarData.countIncrease = false;
                        } else {
                            calendarData.countIncrease = true;
                            calendarData.isDeleted = false;
                        }
                    } else {
                        calendarMap.set(calendarId, {
                            institutionCalenderId: convertToMongoObjectId(
                                formCalendarIdElement.institutionCalenderId,
                            ),
                            isDeleted: formCalendarIdElement.isDeleted,
                            countIncrease: !formCalendarIdElement.isDeleted,
                        });
                    }
                });
                formInitiatorElement.formCalenderIds = Array.from(calendarMap.values());
                const existingUser = formInitiatorElement.createdUserIds.some(
                    (userElement) =>
                        userElement.userId && userElement.userId.toString() === userId.toString(),
                );
                if (!existingUser) {
                    formInitiatorElement.createdUserIds.push({
                        userId: convertToMongoObjectId(userId),
                    });
                }
                updatedFormData.push({
                    _id: formInitiatorElement._id,
                    startMonth: selectedElement.startMonth,
                    endMonth: selectedElement.endMonth,
                    categoryFormGroupId: selectedElement.categoryFormGroupId,
                    createdUserIds: formInitiatorElement.createdUserIds,
                    formCalenderIds: formInitiatorElement.formCalenderIds,
                    isDeleted: selectedElement.isDeleted,
                });
            }
        });
        selectedInstitutionForm.forEach((selectedElement) => {
            const existingElement = updatedFormData.find(
                (updatedElement) =>
                    updatedElement._id &&
                    updatedElement._id.toString() === selectedElement.formInitiatorId &&
                    selectedElement.formInitiatorId.toString(),
            );
            if (!existingElement) {
                updatedFormData.push({
                    categoryFormGroupId: convertToMongoObjectId(
                        selectedElement.categoryFormGroupId,
                    ),
                    createdUserIds: [{ userId: createUser }],
                    startMonth: selectedElement.startMonth,
                    endMonth: selectedElement.endMonth,
                    formCalenderIds: selectedElement.formCalenderIds.map((calenderElement) => {
                        return {
                            ...calenderElement,
                            countIncrease: true,
                        };
                    }),
                });
            }
        });
        return { updatedFormData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getFormInitiatedData = async ({ assuranceProcessQuery }) => {
    try {
        return await qapcFormInitiatorSchema
            .find(assuranceProcessQuery, {
                formAttachment: 1,
                selectedGroupName: 1,
                categoryId: 1,
                categoryName: 1,
                formName: 1,
                categoryFormId: 1,
                categoryFormCourseId: 1,
                programId: 1,
                programName: 1,
                curriculumId: 1,
                curriculumName: 1,
                year: 1,
                courseName: 1,
                assignedInstitutionId: 1,
                institutionName: 1,
                courseId: 1,
                categoryFormGroupId: 1,
                'mergedFormId.formInitiatorId': 1,
                mergeStatus: 1,
                submissionStatus: 1,
                archive: 1,
                updatedAt: 1,
                status: 1,
                submissionDate: 1,
                level: 1,
                term: 1,
                attemptTypeName: 1,
                'approverList.level': 1,
                'approverList.levelStatus': 1,
                'resubmissionLog.level': 1,
                'resubmissionLog.roleId': 1,
                'resubmissionLog.userId': 1,
                'resubmissionLog.reason': 1,
                'resubmissionLog.resubmissionDate': 1,
            })
            .populate({ path: 'resubmissionLog.userId', select: { name: 1 } })
            .populate({ path: 'resubmissionLog.roleId', select: { roleName: 1 } })
            .populate({
                path: 'categoryFormId',
                select: {
                    categoryFormType: 1,
                    formType: 1,
                    incorporateMandatory: 1,
                    'approvalLevel.name': 1,
                    'approvalLevel.levelNumber': 1,
                    archive: 1,
                    isActive: 1,
                    isDeleted: 1,
                },
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const filterMatchingGroup = ({ formInitiatorData }) => {
    try {
        const filterMatchingForm = [];
        const matchingFormId = new Set();
        let mergedFormIds;
        formInitiatorData.forEach((formInitiatorElement) => {
            if (formInitiatorElement.mergeStatus) {
                mergedFormIds = formInitiatorElement.mergedFormId.length
                    ? formInitiatorElement.mergedFormId.map((mergeFormIdElement) => {
                          return mergeFormIdElement.formInitiatorId;
                      })
                    : [];
                if (
                    !mergedFormIds.some((mergeIdElement) =>
                        matchingFormId.has(mergeIdElement.toString()),
                    )
                ) {
                    mergedFormIds.forEach((mergeIdElement) =>
                        matchingFormId.add(mergeIdElement.toString()),
                    );
                    matchingFormId.add(formInitiatorElement._id.toString());
                    filterMatchingForm.push({ ...formInitiatorElement });
                }
            } else {
                filterMatchingForm.push({ ...formInitiatorElement });
            }
        });
        return { filterMatchingForm };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const formSubmittedCount = async ({ filterMatchingForm }) => {
    try {
        return filterMatchingForm.reduce(
            (counts, formElement) => {
                if (formElement.archive) {
                    counts.archiveCount++;
                } else {
                    counts.submittedCount++;
                }
                return counts;
            },
            { submittedCount: 0, archiveCount: 0 },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getAttachmentDocument = async ({ initiatorIds }) => {
    try {
        const formGuideResourcesData = await qapcGuideResourceSchema
            .find(
                {
                    formInitiatorIds: { $in: initiatorIds },
                },
                {
                    formEvidenceAttachment: 1,
                    'sectionAttachments.evidenceAttachment': 1,
                    'sectionAttachments.sectionName': 1,
                    'displayCapture.evidenceAttachment': 1,
                    'displayCapture.attachments.displayName': 1,
                    formInitiatorIds: 1,
                    addComment: 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                },
            )
            .populate({ path: 'addComment.userId', select: { name: 1 } })
            .populate({ path: 'addComment.roleId', select: { roleName: 1 } })
            .lean();
        return { formGuideResourcesData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const categoryDocumentStructure = async ({ formInitiatorCalenderList }) => {
    try {
        const uniqueCalenderIds = [];
        formInitiatorCalenderList.forEach((institutionElement) => {
            institutionElement.formCalenderIds.forEach((formCalenderElement) => {
                if (formCalenderElement.isDeleted) return;

                let existingCalender = uniqueCalenderIds.find(
                    (calendarElement) =>
                        calendarElement.institutionCalenderId.toString() ===
                        formCalenderElement.institutionCalenderId._id.toString(),
                );
                if (!existingCalender) {
                    existingCalender = {
                        institutionCalenderId: formCalenderElement.institutionCalenderId._id,
                        calendar_name: formCalenderElement.institutionCalenderId.calendar_name,
                        category: [],
                    };
                    uniqueCalenderIds.push(existingCalender);
                }

                let existingCategory = existingCalender.category.find(
                    (categoryElement) =>
                        categoryElement.categoryId.toString() ===
                        institutionElement.categoryId.toString(),
                );

                if (!existingCategory) {
                    existingCategory = {
                        categoryName: institutionElement.categoryName,
                        categoryId: institutionElement.categoryId,
                        forms: [],
                    };
                    existingCalender.category.push(existingCategory);
                }
                const existingForm = existingCategory.forms.find(
                    (formElement) =>
                        formElement.categoryFormId.toString() ===
                        institutionElement.categoryFormId.toString(),
                );
                if (existingForm) {
                    if (!existingForm.formInitiatorIds) {
                        existingForm.formInitiatorIds = [];
                    }
                    if (
                        !existingForm.formInitiatorIds
                            .toString()
                            .includes(institutionElement._id.toString())
                    ) {
                        existingForm.formInitiatorIds.push(institutionElement._id);
                    }
                } else {
                    existingCategory.forms.push({
                        formName: institutionElement.formName,
                        categoryFormId: institutionElement.categoryFormId,
                        formInitiatorIds: [institutionElement._id],
                    });
                }
            });
        });
        return { uniqueCalenderIds };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const listPublishedCategoryGroup = async ({ uniqueGroupIds }) => {
    try {
        return await formCourseGroupSchema
            .find(
                {
                    _id: uniqueGroupIds.map((groupElement) => convertToMongoObjectId(groupElement)),
                    isActive: true,
                    isDeleted: false,
                    status: PUBLISHED,
                },
                {
                    'createdDocument.institutionCalenderId': 1,
                    'createdDocument.minimum': 1,
                    minimum: 1,
                    group: 1,
                    startMonth: 1,
                    endMonth: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const mergedPublishedGroup = ({ publishedGroupData, categoryFormGroupData }) => {
    try {
        categoryFormGroupData.forEach((groupElement) => {
            const matchingFormData = publishedGroupData.find(
                (publishedElement) =>
                    publishedElement._id.toString() === groupElement.formGroupId.toString(),
            );
            if (matchingFormData) {
                groupElement.minimum = matchingFormData.minimum;
                groupElement.createdDocument = matchingFormData.createdDocument
                    ? matchingFormData.createdDocument
                    : [];
                groupElement.group = matchingFormData.group;
                groupElement.startMonth = matchingFormData.startMonth;
                groupElement.endMonth = matchingFormData.endMonth;
            }
        });
        return { categoryFormGroupData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const categoryFormList = async ({ qapcFormCategoryData, formSettingData }) => {
    try {
        const categoryData = [];
        qapcFormCategoryData.forEach((categoryElement) => {
            const formList = formSettingData
                .filter(
                    (formSettingElement) =>
                        formSettingElement.categoryId.toString() === categoryElement._id.toString(),
                )
                .map((formSettingElement) => ({
                    categoryFormId: formSettingElement._id,
                    formName: formSettingElement.formName,
                    categoryFormType: formSettingElement.categoryFormType,
                    formType: formSettingElement.formType,
                }));
            if (formList.length) {
                categoryData.push({
                    ...categoryElement,
                    formList,
                });
            }
        });
        if (!categoryData.length) {
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: [],
            };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: categoryData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const addActionKeyFormInitiator = async ({
    filterMatchingForm,
    categoryFormGroupData,
    subModuleType,
}) => {
    try {
        const updatedFilterMatchingForm = [];
        filterMatchingForm.forEach((matchingFormElement) => {
            const matchingGroup = categoryFormGroupData.find(
                (groupElement) =>
                    groupElement.subModuleName === subModuleType &&
                    groupElement.formGroupId.toString() ===
                        matchingFormElement.categoryFormGroupId.toString(),
            );
            if (matchingGroup) {
                matchingFormElement.actionIds = matchingGroup.actionIds;
                if (subModuleType === FORM_APPROVER) {
                    matchingFormElement.level = matchingGroup.levelIndex;
                }
                updatedFilterMatchingForm.push(matchingFormElement);
            }
        });
        return { filterMatchingForm: updatedFilterMatchingForm };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const termAndAttemptList = async ({ formCourseGroupData }) => {
    try {
        const uniqueTerm = {};
        formCourseGroupData.forEach((groupElement) => {
            if (!uniqueTerm[groupElement.term]) {
                uniqueTerm[groupElement.term] = {
                    term: groupElement.term,
                    attemptTypes: new Set(),
                };
            }
            uniqueTerm[groupElement.term].attemptTypes.add(groupElement.attemptTypeName);
        });
        const terms = Object.values(uniqueTerm).map((termElement) => ({
            term: termElement.term,
            attemptTypes: Array.from(termElement.attemptTypes),
        }));
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: terms };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const filterTeamAndAttemptType = async ({ formQuery, term, attemptTypeName }) => {
    try {
        return await formCourseGroupSchema.find(
            {
                ...formQuery,
                ...(term && { term }),
                ...(attemptTypeName && { attemptTypeName }),
            },
            {
                categoryFormCourseId: 1,
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const uniqueFormCourseData = async ({ formGroupData, formCourseData }) => {
    try {
        const updatedFormCourseData = [];
        formCourseData.forEach((formCourseElement) => {
            const categoryFormGroupIds = formGroupData
                .filter(
                    (groupElement) =>
                        groupElement.categoryFormCourseId.toString() ===
                        formCourseElement._id.toString(),
                )
                .map((groupElement) => groupElement._id);
            if (categoryFormGroupIds.length) {
                updatedFormCourseData.push({
                    ...formCourseElement,
                    categoryFormGroupIds,
                });
            }
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: updatedFormCourseData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const incorporateSectionList = async ({ formList }) => {
    try {
        let incorporateWith = [];
        let incorporateFrom = [];
        const incorporateData = await qapcIncorporateSectionSchema
            .find(
                {
                    isDeleted: false,
                },
                {
                    categoryId: 1,
                    categoryName: 1,
                    categoryFormId: 1,
                    formName: 1,
                    institutionCalenderId: 1,
                    categoryFormCourseIds: 1,
                    categoryFormGroupIds: 1,
                    formInitiatorIds: 1,
                    sectionName: 1,
                    'incorporateTo.sectionName': 1,
                    createdCalenderId: 1,
                    createdFormName: 1,
                    selectedProgram: 1,
                    selectedInstitution: 1,
                    sectionTemplate: 1,
                },
            )
            .populate({ path: 'createdCalenderId', select: { calendar_name: 1 } })
            .populate({ path: 'institutionCalenderId', select: { calendar_name: 1 } })
            .lean();
        formList.forEach((formElement) => {
            incorporateWith = [];
            incorporateFrom = [];
            incorporateData.forEach((incorporateElement) => {
                const isIncorporateWith = incorporateElement.formInitiatorIds
                    .toString()
                    .includes(formElement._id.toString());
                if (isIncorporateWith) {
                    incorporateWith.push({
                        formName: incorporateElement.formName,
                        institutionCalenderId: incorporateElement.institutionCalenderId._id,
                        calendar_name: incorporateElement.institutionCalenderId.calendar_name,
                        sectionFrom: incorporateElement.sectionName,
                        sectionTemplate: incorporateElement.sectionTemplate,
                        sectionTo: incorporateElement.incorporateTo,
                        selectedInstitution: incorporateElement.selectedInstitution,
                        selectedProgram: incorporateElement.selectedProgram,
                    });
                }
                if (formElement?.formGuideResource?.length) {
                    formElement.formGuideResource.forEach((guideResourceElement) => {
                        let isLike = false;
                        const matchedIncorporateFrom = guideResourceElement?.incorporateFrom?.find(
                            (guideFormElement) =>
                                guideFormElement.incorporateId.toString() ===
                                incorporateElement._id.toString(),
                        );
                        if (matchedIncorporateFrom) {
                            isLike =
                                matchedIncorporateFrom && matchedIncorporateFrom.isLike
                                    ? matchedIncorporateFrom.isLike
                                    : false;
                        }
                        const existingIncorporate = incorporateFrom.some(
                            (item) =>
                                item.incorporateId.toString() === incorporateElement._id.toString(),
                        );
                        if (!existingIncorporate) {
                            if (incorporateElement.categoryFormGroupIds.length) {
                                const isIncorporateFrom = incorporateElement.categoryFormGroupIds
                                    .toString()
                                    .includes(formElement.categoryFormGroupId.toString());
                                if (isIncorporateFrom) {
                                    incorporateFrom.push({
                                        formName: incorporateElement.createdFormName,
                                        institutionCalenderId:
                                            incorporateElement.createdCalenderId._id,
                                        calendar_name:
                                            incorporateElement.createdCalenderId.calendar_name,
                                        isLike,
                                        incorporateId: incorporateElement._id,
                                        sectionFrom: incorporateElement.sectionName,
                                        sectionTemplate: incorporateElement.sectionTemplate,
                                        sectionTo: incorporateElement.incorporateTo,
                                        selectedInstitution: incorporateElement.selectedInstitution,
                                        selectedProgram: incorporateElement.selectedProgram,
                                    });
                                }
                            } else if (incorporateElement.categoryFormCourseIds.length) {
                                const isIncorporateFrom = incorporateElement.categoryFormCourseIds
                                    .toString()
                                    .includes(formElement.categoryFormCourseId.toString());
                                if (isIncorporateFrom) {
                                    incorporateFrom.push({
                                        formName: incorporateElement.createdFormName,
                                        institutionCalenderId:
                                            incorporateElement.createdCalenderId._id,
                                        calendar_name:
                                            incorporateElement.createdCalenderId.calendar_name,
                                        isLike,
                                        incorporateId: incorporateElement._id,
                                        sectionFrom: incorporateElement.sectionName,
                                        sectionTemplate: incorporateElement.sectionTemplate,
                                        sectionTo: incorporateElement.incorporateTo,
                                        selectedInstitution: incorporateElement.selectedInstitution,
                                        selectedProgram: incorporateElement.selectedProgram,
                                    });
                                }
                            }
                        }
                    });
                } else {
                    const existingIncorporate = incorporateFrom.some(
                        (item) =>
                            item.incorporateId.toString() === incorporateElement._id.toString(),
                    );
                    if (!existingIncorporate) {
                        if (incorporateElement.categoryFormGroupIds.length) {
                            const isIncorporateFrom = incorporateElement.categoryFormGroupIds
                                .toString()
                                .includes(formElement.categoryFormGroupId.toString());
                            if (isIncorporateFrom) {
                                incorporateFrom.push({
                                    formName: incorporateElement.createdFormName,
                                    institutionCalenderId: incorporateElement.createdCalenderId._id,
                                    calendar_name:
                                        incorporateElement.createdCalenderId.calendar_name,
                                    isLike: false,
                                    incorporateId: incorporateElement._id,
                                    sectionFrom: incorporateElement.sectionName,
                                    sectionTemplate: incorporateElement.sectionTemplate,
                                    sectionTo: incorporateElement.incorporateTo,
                                    selectedInstitution: incorporateElement.selectedInstitution,
                                    selectedProgram: incorporateElement.selectedProgram,
                                });
                            }
                        } else if (incorporateElement.categoryFormCourseIds.length) {
                            const isIncorporateFrom = incorporateElement.categoryFormCourseIds
                                .toString()
                                .includes(formElement.categoryFormCourseId.toString());
                            if (isIncorporateFrom) {
                                incorporateFrom.push({
                                    formName: incorporateElement.createdFormName,
                                    institutionCalenderId: incorporateElement.createdCalenderId._id,
                                    calendar_name:
                                        incorporateElement.createdCalenderId.calendar_name,
                                    isLike: false,
                                    incorporateId: incorporateElement._id,
                                    sectionFrom: incorporateElement.sectionName,
                                    sectionTemplate: incorporateElement.sectionTemplate,
                                    sectionTo: incorporateElement.incorporateTo,
                                    selectedInstitution: incorporateElement.selectedInstitution,
                                    selectedProgram: incorporateElement.selectedProgram,
                                });
                            }
                        }
                    }
                }
            });
            formElement.incorporateFrom = incorporateFrom;
            formElement.incorporateWith = incorporateWith;
        });
        return { formList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const mergeGroupInstitutionCalenderIds = async ({ categoryFormGroupData }) => {
    try {
        const qapcActionData = await qapcActionSchema
            .find(
                {
                    actionName: { $in: [VIEW, EDIT] },
                },
                {
                    _id: -1,
                },
            )
            .lean();
        const groupedData = {};
        categoryFormGroupData.forEach((formGroupElement) => {
            const matchingAction = formGroupElement.actionIds.filter((actionElement) =>
                qapcActionData.filter(
                    (qapcActionElement) =>
                        qapcActionElement._id.toString() === actionElement.toString(),
                ),
            );
            if (matchingAction.length >= qapcActionData.length) {
                if (!groupedData[formGroupElement.formGroupId]) {
                    groupedData[formGroupElement.formGroupId] = {
                        formGroupId: formGroupElement.formGroupId,
                        subModuleName: formGroupElement.subModuleName,
                        actionIds: formGroupElement.actionIds,
                        selectedAcademicTrue: formGroupElement.selectedAcademicTrue,
                        calendarIds: [],
                    };
                }
                groupedData[formGroupElement.formGroupId].calendarIds.push(
                    ...formGroupElement.calendarIds,
                );
            }
        });
        return { categoryGroupData: Object.values(groupedData) };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const updateCreatedDocument = async ({ formInitiatorIds }) => {
    try {
        const updateMinimumCount = [];
        const formInitiatorData = await qapcFormInitiatorSchema
            .findOne(
                {
                    _id: {
                        $in: formInitiatorIds.map((formIdElement) =>
                            convertToMongoObjectId(formIdElement),
                        ),
                    },
                },
                {
                    formCalenderIds: 1,
                    categoryFormGroupId: 1,
                },
            )
            .populate({ path: 'categoryFormGroupId', select: { createdDocument: 1 } })
            .lean();

        if (formInitiatorData && formInitiatorData.categoryFormGroupId) {
            const createdDocument = formInitiatorData.categoryFormGroupId.createdDocument.map(
                (createdElement) => {
                    const isMatchingCalender = formInitiatorData.formCalenderIds.some(
                        (formCalenderElement) =>
                            formCalenderElement.institutionCalenderId.toString() ===
                                createdElement.institutionCalenderId.toString() &&
                            !formCalenderElement.isDeleted,
                    );

                    return {
                        institutionCalenderId: createdElement.institutionCalenderId,
                        minimum: createdElement.minimum - (isMatchingCalender ? 1 : 0),
                        _id: createdElement._id,
                    };
                },
            );

            updateMinimumCount.push({
                categoryFormGroupId: formInitiatorData.categoryFormGroupId._id,
                createdDocument,
            });
        }
        await updateCreateDocument({ updateMinimumCount });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const displayCaptureNameList = ({ displayCapture }) => {
    try {
        let displaySection = [];
        let displayName = [];
        displayCapture.forEach((displayElement) => {
            if (displayElement.sectionName) {
                displaySection.push(displayElement.sectionName);
            } else if (!displayElement.sectionName) {
                displayElement.attachments.forEach((attachmentElement) => {
                    displayName.push(attachmentElement.displayName);
                });
            }
        });
        displaySection = [...new Set(displaySection)];
        displayName = [...new Set(displayName)];
        return { displaySection, displayName };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const matchingFormAttachment = async ({ formInitiatorList, formAttachmentData, subModuleType }) => {
    try {
        formInitiatorList.forEach((filterMatchElement) => {
            if (
                formAttachmentData.formGuideResourcesData &&
                formAttachmentData.formGuideResourcesData.length
            ) {
                const matchingAttachment = formAttachmentData.formGuideResourcesData.find(
                    (attachmentElement) => {
                        // Check if the form initiator IDs match
                        return attachmentElement.formInitiatorIds
                            .map((initiatedElement) => initiatedElement.toString())
                            .includes(filterMatchElement._id.toString());
                    },
                );
                //add comment
                filterMatchElement.addComment = matchingAttachment?.addComment || [];
                // attachment
                const attachmentCount = matchingAttachment
                    ? matchingAttachment.formEvidenceAttachment.length
                    : 0;
                // session attachment
                const sectionAttachmentsCount = matchingAttachment
                    ? matchingAttachment.sectionAttachments.reduce(
                          (count, sectionElement) =>
                              count + sectionElement.evidenceAttachment.length,
                          0,
                      )
                    : 0;
                // attachment count
                filterMatchElement.attachmentCount = attachmentCount + sectionAttachmentsCount;
                if (matchingAttachment) {
                    displayCapture = displayCaptureNameList({
                        displayCapture: matchingAttachment.displayCapture,
                    });
                    filterMatchElement.displaySection = displayCapture
                        ? displayCapture.displaySection
                        : [];
                    filterMatchElement.displayName = displayCapture
                        ? displayCapture.displayName
                        : [];
                }
            } else {
                // If formGuideResourcesData set default values
                filterMatchElement.attachmentCount = 0;
                filterMatchElement.displaySection = [];
                filterMatchElement.displayName = [];
            }
            //remove the approverUserList key
            if (subModuleType === FORM_APPROVER) {
                delete filterMatchElement?.approverList;
            }
            return filterMatchElement;
        });
        return { formInitiatorList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const filterViewActionData = async ({ mergedPublishedGroupData }) => {
    try {
        const qapcActionData = await qapcActionSchema
            .find(
                {
                    actionName: { $in: [VIEW, EDIT] },
                },
                {
                    _id: -1,
                },
            )
            .lean();
        const filterActionData = [];
        mergedPublishedGroupData.categoryFormGroupData.forEach((categoryElement) => {
            const matchingAction = categoryElement.actionIds.filter((actionElement) =>
                qapcActionData.filter(
                    (qapcActionElement) =>
                        qapcActionElement._id.toString() === actionElement.toString(),
                ),
            );
            if (matchingAction.length >= qapcActionData.length) {
                filterActionData.push({
                    formGroupId: categoryElement.formGroupId,
                    selectedAcademicTrue: categoryElement.selectedAcademicTrue
                        ? categoryElement.selectedAcademicTrue
                        : false,
                    RPSelectedCalender: categoryElement.calendarIds
                        ? categoryElement.calendarIds
                        : [],
                });
            }
        });
        return { filterActionData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCategoryFormGroupData = async ({ categoryFormGroup }) => {
    try {
        const listCategoryFormGroup = await formCourseGroupSchema
            .find(
                {
                    _id: {
                        $in: categoryFormGroup.map((groupElement) =>
                            convertToMongoObjectId(groupElement.formGroupId),
                        ),
                    },
                },
                {
                    categoryFormCourseId: 1,
                    term: 1,
                    attemptTypeName: 1,
                    executionsPer: 1,
                    academicYear: 1,
                    groupName: 1,
                    minimum: 1,
                    startMonth: 1,
                    endMonth: 1,
                    createdDocument: 1,
                    categoryFormId: 1,
                    categoryId: 1,
                },
            )
            .lean();
        const uniqueFormIds = new Set();
        listCategoryFormGroup.forEach((formGroupElement) => {
            uniqueFormIds.add(formGroupElement.categoryFormId.toString());
            const matchingGroup = categoryFormGroup.find(
                (formElement) =>
                    formElement.formGroupId.toString() === formGroupElement._id.toString(),
            );
            formGroupElement.selectedAcademicTrue = matchingGroup
                ? matchingGroup.selectedAcademicTrue
                : false;
            formGroupElement.RPSelectedCalender = matchingGroup
                ? matchingGroup.RPSelectedCalender
                : [];
        });
        return {
            listCategoryFormGroup,
            uniqueFormIds: [...new Set(uniqueFormIds)],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCategoryFormCourseData = async ({ categoryFormCourseIds }) => {
    try {
        return await formSettingCourseSchema
            .find(
                {
                    _id: {
                        $in: categoryFormCourseIds.map((courseIdElement) =>
                            convertToMongoObjectId(courseIdElement),
                        ),
                    },
                },
                {
                    categoryId: 1,
                    categoryFormId: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    actions: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCategoryFormData = async ({ categoryFormIds }) => {
    try {
        return await formSettingSchema
            .find(
                {
                    _id: {
                        $in: categoryFormIds.map((formIdElement) =>
                            convertToMongoObjectId(formIdElement),
                        ),
                    },
                    isActive: true,
                    isDeleted: false,
                    archive: false,
                    status: PUBLISHED,
                },
                {
                    categoryId: 1,
                    formName: 1,
                    incorporateMandatory: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getCategoryData = async ({ categoryIds }) => {
    try {
        return await qapcFormCategorySchema
            .find(
                {
                    _id: {
                        $in: categoryIds.map((categoryIdElement) =>
                            convertToMongoObjectId(categoryIdElement),
                        ),
                    },
                },
                {
                    categoryName: 1,
                    level: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const userFromInitiatedList = async ({
    userId,
    roleId,
    isTodoMissed = false,
    subModuleType = FORM_INITIATOR,
}) => {
    try {
        const userList = await QAPCUserPermissionList({
            userId,
            roleId,
            subModuleType,
        });
        if (!userList.qapcPermissionList) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        let categoryFormGroupData = userList.qapcPermissionList.categoryFormGroupData
            ? userList.qapcPermissionList.categoryFormGroupData.filter(
                  (groupElement) => groupElement.formGroupId,
              )
            : [];
        const mergeGroupInstitutionData = await mergeGroupInstitutionCalenderIds({
            categoryFormGroupData,
        });
        if (!mergeGroupInstitutionData?.categoryGroupData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        categoryFormGroupData = clone(mergeGroupInstitutionData.categoryGroupData);
        let publishedGroupData;
        let mergedPublishedGroupData;
        const matchingCategoryGroupList = [];
        let uniqueCategoryIds = new Set();
        let uniqueFormCourseIds = new Set();
        if (categoryFormGroupData.length) {
            const groupIds = categoryFormGroupData.map((groupElement) => {
                return groupElement.formGroupId.toString();
            });
            const uniqueGroupIds = [...new Set(groupIds)];
            if (uniqueGroupIds.length) {
                publishedGroupData = await listPublishedCategoryGroup({ uniqueGroupIds });
            }
            if (!publishedGroupData.length) {
                return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
            }
            mergedPublishedGroupData = mergedPublishedGroup({
                publishedGroupData,
                categoryFormGroupData,
            });
            //filter the action view & edit
            mergedPublishedGroupData = await filterViewActionData({ mergedPublishedGroupData });
            if (!mergedPublishedGroupData.filterActionData) {
                return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
            }
            const categoryFormGroupList = await getCategoryFormGroupData({
                categoryFormGroup: mergedPublishedGroupData.filterActionData,
            });
            const categoryFormData = await getCategoryFormData({
                categoryFormIds: categoryFormGroupList.uniqueFormIds,
            });
            //active form List
            categoryFormGroupList?.listCategoryFormGroup.forEach((groupElement) => {
                categoryFormData.forEach((formElement) => {
                    if (String(groupElement.categoryFormId) === String(formElement._id)) {
                        matchingCategoryGroupList.push(groupElement);
                        uniqueFormCourseIds.add(groupElement.categoryFormCourseId.toString());
                        uniqueCategoryIds.add(groupElement.categoryId.toString());
                    }
                });
            });
            uniqueFormCourseIds = [...new Set(uniqueFormCourseIds)];
            uniqueCategoryIds = [...new Set(uniqueCategoryIds)];
            const categoryFormCourseData = await getCategoryFormCourseData({
                categoryFormCourseIds: uniqueFormCourseIds,
            });
            const categoryData = await getCategoryData({
                categoryIds: uniqueCategoryIds,
            });
            if (isTodoMissed) {
                return {
                    categoryFormGroupData: matchingCategoryGroupList,
                    categoryFormCourseData,
                };
            }
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: {
                    categoryFormGroupData: matchingCategoryGroupList,
                    categoryFormCourseData,
                    categoryFormData,
                    categoryData,
                },
            };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const removedIncorporateSection = async ({ removedFomInitiatorIds }) => {
    await qapcIncorporateSectionSchema.deleteMany({
        formInitiatorIds: { $in: removedFomInitiatorIds },
    });
};

const updateRevokeApproverList = ({ approverList, resubmitUser }) => {
    try {
        const { level, roleId, userId } = resubmitUser;
        approverList.forEach((approverElement, approverIndex) => {
            if (approverElement.level === level) {
                approverElement.roleUser.forEach((roleUserElement, roleUserIndex) => {
                    if (roleUserElement.roleId.toString() === roleId.toString()) {
                        roleUserElement.userIds = roleUserElement.userIds.filter(
                            (userElement) => userElement.userId.toString() !== userId.toString(),
                        );
                        if (roleUserElement.userIds.length === 0) {
                            approverElement.roleUser.splice(roleUserIndex, 1);
                        }
                    }
                });
                if (approverElement.roleUser.length === 0) {
                    approverList.splice(approverIndex, 1);
                }
            }
        });
        return approverList;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getFormOccurrence = async ({ categoryFormGroupId, institutionCalenderId }) => {
    try {
        let isFormOccurrence = false;
        const formCourseGroupData = await formCourseGroupSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(categoryFormGroupId),
                },
                {
                    minimum: 1,
                    createdDocument: 1,
                },
            )
            .lean();
        if (formCourseGroupData) {
            const createdDocumentCount = formCourseGroupData.createdDocument.find(
                (documentElement) =>
                    String(documentElement.institutionCalenderId) === String(institutionCalenderId),
            );
            if (createdDocumentCount) {
                isFormOccurrence = createdDocumentCount.minimum < formCourseGroupData.minimum;
            }
        }
        return isFormOccurrence;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const getFormGuideResourcesData = async ({ formInitiatedId }) => {
    try {
        return await qapcGuideResourceSchema
            .findOne(
                {
                    formInitiatorIds: convertToMongoObjectId(formInitiatedId),
                },
                {
                    categoryFormId: 1,
                    formEvidenceAttachment: 1,
                    sectionAttachments: 1,
                    tags: 1,
                    displayCapture: 1,
                    formTemplate: 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                    _id: 0,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const newOccurrenceFormCreate = async ({
    formInitiatedId,
    institutionCalenderId,
    formGuideResourcesData,
    _institution_id,
}) => {
    try {
        const formInitiatedBulkWrite = [];
        const createdFormIds = [];
        let mergedFormCategoryGroup = [];
        const selectedInstitution = [];
        const formCalenderIds = [
            {
                institutionCalenderId: convertToMongoObjectId(institutionCalenderId),
                isDeleted: false,
            },
        ];
        const formInitiatedData = await qapcFormInitiatorSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(formInitiatedId),
                },
                {
                    categoryId: 1,
                    categoryName: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    level: 1,
                    categoryFormId: 1,
                    formName: 1,
                    categoryFormCourseId: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    year: 1,
                    term: 1,
                    selectedGroupName: 1,
                    categoryFormGroupId: 1,
                    mergedFormId: 1,
                    mergeStatus: 1,
                    startMonth: 1,
                    endMonth: 1,
                    createdUserIds: 1,
                    formAttachment: 1,
                    _id: 0,
                },
            )
            .lean();
        if (formInitiatedData.mergeStatus) {
            const InitiatedMergedFormIds = formInitiatedData.mergedFormId
                .filter((mergedFormElement) => !mergedFormElement.isDeleted)
                .map((mergedFormElement) =>
                    convertToMongoObjectId(mergedFormElement.formInitiatorId),
                );
            mergedFormCategoryGroup = await qapcFormInitiatorSchema
                .find(
                    {
                        _id: InitiatedMergedFormIds,
                    },
                    {
                        _id: 0,
                        categoryFormGroupId: 1,
                    },
                )
                .lean();
        }
        //add the current categoryFormGroupId to the group
        mergedFormCategoryGroup.push({
            categoryFormGroupId: formInitiatedData?.categoryFormGroupId,
        });
        if (mergedFormCategoryGroup?.length) {
            mergedFormCategoryGroup.forEach((groupElement) => {
                const formId = convertToMongoObjectId();
                createdFormIds.push(formId);
                formInitiatedBulkWrite.push({
                    _id: formId,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(formInitiatedData.categoryId),
                    categoryName: formInitiatedData.categoryName,
                    level: formInitiatedData.level,
                    assignedInstitutionId: convertToMongoObjectId(
                        formInitiatedData.assignedInstitutionId,
                    ),
                    institutionName: formInitiatedData.institutionName,
                    categoryFormId: convertToMongoObjectId(formInitiatedData.categoryFormId),
                    formName: formInitiatedData.formName,
                    categoryFormCourseId: convertToMongoObjectId(
                        formInitiatedData.categoryFormCourseId,
                    ),
                    programId: convertToMongoObjectId(formInitiatedData.programId),
                    programName: formInitiatedData.programName,
                    curriculumName: formInitiatedData.curriculumName,
                    formAttachment: formInitiatedData.formAttachment,
                    courseId: convertToMongoObjectId(formInitiatedData.courseId),
                    courseName: formInitiatedData.courseName,
                    courseCode: formInitiatedData.courseCode,
                    selectedGroupName: formInitiatedData.selectedGroupName,
                    categoryFormGroupId: convertToMongoObjectId(groupElement.categoryFormGroupId),
                    mergedFormId: [],
                    mergeStatus: formInitiatedData.mergeStatus,
                    startMonth: formInitiatedData.startMonth,
                    endMonth: formInitiatedData.endMonth,
                    createdUserIds: formInitiatedData.createdUserIds,
                    status: DRAFT,
                    formCalenderIds,
                    submissionStatus: DRAFT,
                    submissionDate: new Date(),
                });
                selectedInstitution.push({
                    categoryFormGroupId: groupElement.categoryFormGroupId,
                    formCalenderIds: formCalenderIds.map((calenderElement) => {
                        return {
                            ...calenderElement,
                            countIncrease: true,
                            isDeleted: false,
                        };
                    }),
                });
            });
        }
        if (formInitiatedBulkWrite.length) {
            formInitiatedBulkWrite.forEach((formElement, index) => {
                formElement.mergedFormId = [
                    ...createdFormIds.slice(0, index),
                    ...createdFormIds.slice(index + 1),
                ].map((formIdElement) => ({ formInitiatorId: formIdElement }));
            });
        }
        //created new form
        await qapcFormInitiatorSchema.insertMany(formInitiatedBulkWrite);
        //create guide resources
        if (formGuideResourcesData) {
            await qapcGuideResourceSchema.insertMany({
                ...formGuideResourcesData,
                _institution_id: convertToMongoObjectId(_institution_id),
                formInitiatorIds: createdFormIds,
            });
        }
        // update the created form institution calender and minimum count
        if (selectedInstitution.length) {
            const selectedInstitutionData = await increaseMinimumCount({
                selectedInstitution,
            });
            if (selectedInstitutionData.updateMinimumCount.length) {
                updateCreateDocument({
                    updateMinimumCount: selectedInstitutionData.updateMinimumCount,
                });
            }
        }
        return {
            statusCode: 200,
            message: 'FORM_CREATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const deleteIncorporateSection = async ({ formInitiatorIds }) => {
    try {
        await qapcIncorporateSectionSchema.updateMany(
            {
                formInitiatorIds: {
                    $in: formInitiatorIds.map((formIdElement) =>
                        convertToMongoObjectId(formIdElement),
                    ),
                },
            },
            {
                $set: { isDeleted: true },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const deleteGuideResources = async ({ formInitiatorIds }) => {
    try {
        await qapcGuideResourceSchema.updateMany(
            {
                formInitiatorIds: {
                    $in: formInitiatorIds.map((formIdElement) =>
                        convertToMongoObjectId(formIdElement),
                    ),
                },
            },
            {
                $set: { isDeleted: true },
            },
        );
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const removeDuplicateForm = async ({ qapcFormInitiatorData }) => {
    try {
        const formInitiatorList = [];
        const categoryFormIdMap = new Map();
        qapcFormInitiatorData.forEach((initiatedElement) => {
            const categoryFormId = initiatedElement.categoryFormId;
            if (!categoryFormIdMap.has(categoryFormId)) {
                const formList = {
                    categoryFormId,
                    formName: initiatedElement.formName,
                    formInitiatedIds: [initiatedElement._id],
                };
                formInitiatorList.push(formList);
                categoryFormIdMap.set(categoryFormId, formList);
            } else {
                const existingEntry = categoryFormIdMap.get(categoryFormId);
                existingEntry.formInitiatedIds.push(initiatedElement._id);
            }
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: formInitiatorList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const sendFormStatusUpdateEmail = async ({ formInitiatorIds, _user_id, status, qapcRoleId }) => {
    if (status !== YET_TO_START) {
        const formData = await qapcFormInitiatorSchema
            .find(
                {
                    _id: { $in: formInitiatorIds.map((id) => convertToMongoObjectId(id)) },
                },
                {
                    categoryFormGroupId: 1,
                    formName: 1,
                    _id: 1,
                    formCalenderIds: 1,
                    submissionDate: 1,
                },
            )
            .lean();
        const { groupIds } = getUniqueGroupIdsAndFormDetails(formData);
        const { users, emails } = await getPermissionEmails(groupIds, FORM_APPROVER, qapcRoleId);
        const emailList = removeCurrentUserEmail(emails, users, _user_id);
        if (emailList.length) {
            const form = formData[0] || {};
            const toEmails = emailList.join(', ');
            const initiatorDetail = users.find(
                (userElement) =>
                    (userElement.userId && String(userElement.userId._id) === String(_user_id)) ||
                    (userElement.guestUserId &&
                        String(userElement.guestUserId._id) === String(_user_id)),
            );
            const initiatorName =
                initiatorDetail?.userId?.email || initiatorDetail?.guestUserId?.email;
            const subject = `New Form Submitted for Approval`;
            const body = `<p>Dear ${toEmails || 'Approver'},</p>
                     <p>A new form has been submitted by <b>${
                         initiatorName || 'the initiator'
                     }</b> and is awaiting your approval.</p>
                    <h4>Form Details:</h4>
                     <ul>
                        <li><b>Form Title:</b> ${form.formName}</li>
                        <li><b>Submitted By:</b> ${initiatorName || 'N/A'}</li>
                        <li><b>Date of Submission:</b> ${form.submissionDate}</li>
                     </ul>
                     <p>Please review and take the necessary action at your earliest convenience.<br>
                        Once approved, the form will be visible on the QA Dashboard for tracking and further processing.<br>
                        If you have any questions, feel free to reach out to the initiator or QA team.</p>
    `;
            await send_email(emailList, subject, body);
        }
    }
};

module.exports = {
    updateCreateDocument,
    increaseMinimumCount,
    getCreatedFormList,
    updateFormData,
    getFormInitiatedData,
    filterMatchingGroup,
    getAttachmentDocument,
    categoryDocumentStructure,
    QAPCUserPermissionList,
    formSubmittedCount,
    listPublishedCategoryGroup,
    mergedPublishedGroup,
    categoryFormList,
    addActionKeyFormInitiator,
    termAndAttemptList,
    filterTeamAndAttemptType,
    uniqueFormCourseData,
    incorporateSectionList,
    updateCreatedDocument,
    matchingFormAttachment,
    displayCaptureNameList,
    filterViewActionData,
    getCategoryFormGroupData,
    getCategoryFormCourseData,
    getCategoryFormData,
    getCategoryData,
    mergeGroupInstitutionCalenderIds,
    removedIncorporateSection,
    userFromInitiatedList,
    updateRevokeApproverList,
    getFormOccurrence,
    newOccurrenceFormCreate,
    getFormGuideResourcesData,
    deleteIncorporateSection,
    deleteGuideResources,
    removeDuplicateForm,
    sendFormStatusUpdateEmail,
};
