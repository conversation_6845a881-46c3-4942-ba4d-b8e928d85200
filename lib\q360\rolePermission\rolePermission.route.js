const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    createNewRole,
    qapcRoleList,
    updateRoleName,
    getLevelCategory,
    formWiseListProgram,
    getProgramGroup,
    qapcSubModules,
    formApproverLevel,
    formProgramList,
    getFormCourseAndGroup,
    getAcademicYear,
    saveRolePermission,
    assignedUserList,
    qapcAction,
    updateRoleLevel,
    getUserQAPCRole,
    userList,
} = require('./rolePermission.controller');

const validator = require('./rolePermission.validator');
const { validate } = require('../../../middleware/validation');

//add new role
router.post(
    '/createNewRole',
    validate(validator.createNewRoleValidator),
    catchAsync(createNewRole),
);
router.get('/qapcRoleList', validate(validator.qapcRoleListValidator), catchAsync(qapcRoleList));
router.put(
    '/updateRoleName',
    validate(validator.updateRoleNameValidator),
    catchAsync(updateRoleName),
);
//level category list
router.get(
    '/getLevelCategory',
    validate(validator.getLevelCategoryValidator),
    catchAsync(getLevelCategory),
);
router.get(
    '/getFormCourseAndGroup',
    validate(validator.getFormCourseAndGroupValidator),
    catchAsync(getFormCourseAndGroup),
);
//not using this api
router.get(
    '/formWiseListProgram',
    validate(validator.formWiseListProgramValidator),
    catchAsync(formWiseListProgram),
);
router.get(
    '/getProgramGroup',
    validate(validator.getProgramGroupValidator),
    catchAsync(getProgramGroup),
);
router.get(
    '/qapcSubModules',
    validate(validator.qapcRoleListValidator),
    catchAsync(qapcSubModules),
);
router.get('/qapcAction', validate(validator.qapcRoleListValidator), catchAsync(qapcAction));
//approval Level
router.get(
    '/formApproverLevel',
    validate(validator.formApproverLevelValidator),
    catchAsync(formApproverLevel),
);
//form program list
router.get(
    '/formProgramList',
    validate(validator.formProgramListValidator),
    catchAsync(formProgramList),
);
router.get(
    '/getAcademicYear',
    validate(validator.qapcRoleListValidator),
    catchAsync(getAcademicYear),
);
//update role permission
router.put(
    '/saveRolePermission',
    validate(validator.saveRolePermissionValidator),
    catchAsync(saveRolePermission),
);
router.get(
    '/assignedUserList',
    validate(validator.assignedUserValidator),
    catchAsync(assignedUserList),
);
router.put('/updateRoleLevel', validate(validator.updateRoleLevel), catchAsync(updateRoleLevel));
//user login qapc role
router.get('/getUserQAPCRole', catchAsync(getUserQAPCRole));
//user list
router.get('/userList', catchAsync(userList));
module.exports = router;
