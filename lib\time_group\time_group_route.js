const express = require('express');
const route = express.Router();
const time_group = require('./time_group_controller');
const validator = require('./time_group_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get('/get_setting_timings', [userPolicyAuthentication([])], time_group.get_setting_timings);
route.get(
    '/:id',
    [userPolicyAuthentication(['infrastructure_management:onsite:settings:time_groups:view'])],
    validator.time_group_id,
    time_group.list_id,
);
route.get(
    '/',
    [userPolicyAuthentication(['infrastructure_management:onsite:settings:time_groups:view'])],
    time_group.list,
);
route.post(
    '/',
    [userPolicyAuthentication(['infrastructure_management:onsite:settings:time_groups:add'])],
    validator.time_group,
    time_group.insert,
);
route.put(
    '/:id',
    [userPolicyAuthentication(['infrastructure_management:onsite:settings:time_groups:edit'])],
    validator.time_group_id,
    validator.time_group,
    time_group.update,
);
route.delete(
    '/:id',
    [userPolicyAuthentication(['infrastructure_management:onsite:settings:time_groups:delete'])],
    validator.time_group_id,
    time_group.delete,
);
module.exports = route;
