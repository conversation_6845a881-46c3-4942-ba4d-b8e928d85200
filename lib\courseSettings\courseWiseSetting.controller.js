const { DIGI_COURSE, COURSE_SCHEDULE } = require('../utility/constants');
const courseModal = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const { convertToMongoObjectId } = require('../utility/common');

const updateCourseWiseFacialSettings = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            studentFacial,
            staffFacial,
            sessionChange,
            autoEndAttendance,
            programId,
            courseId,
        } = body;
        const result = await courseModal.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(courseId),
                _program_id: convertToMongoObjectId(programId),
                'settings.updatedBy': convertToMongoObjectId(_user_id),
            },
            {
                $set: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    'settings.studentFacial': studentFacial,
                    'settings.staffFacial': staffFacial,
                    'settings.sessionChange': sessionChange,
                    'settings.autoEndAttendance': autoEndAttendance,
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
            },
            {
                upsert: true,
            },
        );

        const action = result.upsertedCount > 0 ? 'created' : 'updated';

        return {
            statusCode: 200,
            message: `Facial settings ${action} successfully`,
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseFacialData = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { programId, courseId } = query;

        const courseWiseData = await courseModal
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
                {
                    _id: 1,
                    _program_id: 1,
                    settings: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseWiseData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseUserDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { programId, courseId, year, level, term, rotation, rotationCount } = query;

        const courseWiseData = await courseScheduleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _course_id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    term: convertToMongoObjectId(term),
                    year_no: convertToMongoObjectId(year),
                    level_no: convertToMongoObjectId(level),
                    rotation: convertToMongoObjectId(rotation),
                    rotation_count: convertToMongoObjectId(rotationCount),
                },
                {
                    staffs: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseWiseData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    updateCourseWiseFacialSettings,
    getCourseWiseFacialData,
    getCourseWiseUserDetails,
};
