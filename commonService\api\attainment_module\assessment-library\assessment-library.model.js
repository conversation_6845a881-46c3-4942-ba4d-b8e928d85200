const mongoose = require('mongoose');
const {
    DIGI_COURSE,
    DIGI_PROGRAM,
    GENDER: { MALE, FEMALE, BOTH },
    ASSESSMENT_LIBRARY,
    PLO,
    CLO,
    SLO,
    PENDING,
} = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const questionMarks = new Schema({
    questionName: String,
    outComeIds: [{ type: ObjectId }],
    totalMark: Number,
    attainmentBenchMark: Number,
});

const studentMarks = new Schema({
    // questionId: { type: ObjectId },
    questionName: String,
    mark: { type: Number },
});

const studentDetails = new Schema({
    // _student_id: { type: ObjectId },
    studentId: String,
    name: String,
    gender: {
        type: String,
        enum: [MALE, FEMALE, BOTH],
    },
    attendance: {
        type: Boolean,
        // default: true,
    },
    studentMarks: [studentMarks],
    isDefault: {
        type: Boolean,
        default: false,
    },
});

const assessmentManagementSchemas = new Schema(
    {
        _institution_id: {
            type: ObjectId,
        },
        _institution_calendar_id: {
            type: ObjectId,
        },
        _program_id: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
        },
        typeName: String,
        typeId: ObjectId,
        subTypeName: String,
        subTypeId: ObjectId,
        assessmentTypeId: ObjectId,
        assessmentTypeName: String,
        curriculumName: String,
        levelNo: [{ type: String }],
        type: { type: String, enum: ['program', 'course'] },
        _assessment_id: {
            type: ObjectId,
        },
        assessmentName: String,
        assessmentMark: Number,
        term: {
            type: String,
        },
        // year: {
        //     type: String,
        // },
        level: {
            type: String,
        },
        rotationNo: {
            type: String,
        },
        _course_id: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        noQuestions: {
            type: Number,
        },
        questionOutcome: { type: String, enum: [PLO, CLO, SLO] },
        benchMark: Number,
        studentDetails: [studentDetails],
        questionMarks: [questionMarks],
        publishedTime: { type: Date },
        status: { default: PENDING, type: String },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSESSMENT_LIBRARY, assessmentManagementSchemas);
