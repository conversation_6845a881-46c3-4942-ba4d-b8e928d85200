const express = require('express');
const route = express.Router();
const controller = require('./controller');
const validator = require('./validator');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');

route.get('/', controller.getDashboard);
route.get('/get_curriculum/:program_id', controller.getCurriculum);
// route.get('/program/:id', controller.get_program_Dashboard);
route.get(
    '/program/:id/:curriculumId',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    controller.get_program_Dashboard,
);
route.post('/get_mapping_tree', controller.getMappingTree);
route.post('/get_mapping_matrix', controller.getMappingMatrix);
route.get('/mapping_matrix/:course_id', controller.getMappingMatrixNewGET);
route.put('/update_mapping_matrix', controller.updateMappingMatrix);
route.post(
    '/add_framework',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    controller.addFramework,
);
route.post('/remove_framework', controller.removeFramework);

route.get('/list_plo/:curriculum_id/:domain_id', controller.getPLO);
route.post('/add_plo', validator.add_clo, controller.addPLO);
route.put('/update_plo/:id', validator.id, validator.add_clo, controller.updatePLO);
route.delete('/delete_plo/:id/:curriculum_id/:domain_id', controller.deletePLO);

route.post('/slo', validator.add_clo, controller.addSlo);
route.put('/slo', validator.add_clo, controller.updateSlo);
route.delete('/slo', controller.deleteSlo);

route.get('/list_clo/:course_id/:domain_id', controller.list_clo);
route.post('/add_clo', validator.add_clo, controller.addCLO);
route.put('/update_clo/:id', validator.id, validator.add_clo, controller.update_clo);
route.delete('/delete_clo/:id/:course_id/:domain_id', controller.delete_clo);
route.put(
    '/update_mapping_type/:id',
    validator.update_mapping_type,
    validator.id,
    controller.update_mapping_type,
);
route.put(
    '/update_content_mapping_type/:id',
    validator.update_content_mapping_type,
    validator.id,
    controller.update_content_mapping_type,
);

route.put(
    '/update_mapping_type_course/:course_id/:course_assigned_id/:curriculum_id',
    validator.update_mapping_type_course,
    controller.update_mapping_type_course,
);
route.put(
    '/update_content_mapping_type_course/:course_id/:course_assigned_id/:curriculum_id',
    validator.update_content_mapping_type_course,
    controller.update_content_mapping_type_course,
);

route.post('/update_matrix_clo_plo', controller.update_matrix_clo_plo);
route.post('/update_matrix_slo_clo', controller.update_matrix_slo_clo);
route.post('/get_curriculum_without_framework', controller.getCurriculumWithoutFramework);
route.get(
    '/get_slo_clo_graph_data/:course_id',
    controller.getCourseSLOGraphData /* controller.getSLOGraphData */,
);

module.exports = route;
