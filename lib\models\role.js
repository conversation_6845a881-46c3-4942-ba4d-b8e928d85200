const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { INSTITUTION, ROLE } = require('../utility/constants');
const modules = require('./module').schema;

const roleSchema = new Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: INSTITUTION,
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
        modules: [modules],
        policy: [{ type: String }],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = model(ROLE, roleSchema);
