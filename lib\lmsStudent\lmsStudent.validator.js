const Joi = require('joi');
const {
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    PENDING,
    CANCELLED,
    APPROVED,
    WITHDRAWN,
} = require('../utility/constants');
const objectIdValidation = Joi.string().alphanum().length(24).required();
const arrayValidation = Joi.array().items(Joi.string()).min(1);
const lmsApplyValidator = async () => {
    Joi.object()
        .keys({
            headers: Joi.object().keys({
                _institution_id: Joi.string().alphanum().length(24),
            }),
            body: Joi.object()
                .keys({
                    classificationType: Joi.string().valid(ONDUTY, PERMISSION, LEAVE),
                    studentId: Joi.string().alphanum().length(24),
                    categoryName: Joi.string(),
                    categoryId: Joi.string().alphanum().length(24),
                    typeName: Joi.string(),
                    typeId: Joi.string().alphanum().length(24),
                    applyingForDays: Joi.number(),
                    reason: Joi.string(),
                    attachments: Joi.array().items(
                        Joi.object().keys({
                            url: Joi.string(),
                            signedUrl: Joi.string(),
                            sizeInKb: Joi.number(),
                            name: Joi.string(),
                        }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
};

const updateStatusValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().keys({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        body: Joi.object().keys({
            id: Joi.string().alphanum().length(24),
            categoryId: Joi.string().alphanum().length(24),
            typeId: Joi.string().alphanum().length(24),
            approvalStatus: Joi.string().valid(PENDING, CANCELLED, APPROVED, WITHDRAWN),
            reason: Joi.string(),
        }),
    });
};

const listDataValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().keys({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        query: Joi.object().keys({
            studentId: Joi.string().alphanum().length(24),
            classificationType: Joi.string().valid(ONDUTY, PERMISSION, LEAVE),
        }),
    });
};
const previouslyLeaveStatusValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().keys({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        query: Joi.object().keys({
            classificationType: Joi.string().valid(ONDUTY, PERMISSION, LEAVE),
        }),
    });
};

const listScheduleValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        query: Joi.object().keys({
            _student_id: Joi.ObjectId(),
            _institution_calendar_id: Joi.ObjectId(),
            startDate: Joi.date().format(),
            endDate: Joi.date().format(),
        }),
    });
};

const updateAgreeValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().keys({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        body: Joi.object().keys({
            classificationType: Joi.string().valid(ONDUTY, PERMISSION, LEAVE),
            studentId: Joi.string().alphanum().length(24),
            agree: Joi.boolean(),
        }),
    });
};
const staffApprovalValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().key({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        body: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required(),
            approvalFrom: Joi.array.items(
                Joi.object().keys({
                    levelId: Joi.ObjectId().alphanum().length(24),
                    roleId: Joi.ObjectId().alphanum().length(24),
                    userId: Joi.ObjectId().alphanum().length(24),
                    categoryBased: Joi.string(),
                    reason: Joi.string(),
                    status: Joi.string(),
                }),
            ),
        }),
    });
};
const revokeApprovalValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().key({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        body: Joi.object().key({
            id: Joi.string().alphanum().length().require(),
            approvalFormId: Joi.string().alphanum().length(24),
        }),
    });
};
const editStaffApprovalValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().key({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        body: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required(),
            approvalFormId: Joi.string().alphanum().length(24),
            levelId: Joi.ObjectId().alphanum().length(24),
            roleId: Joi.ObjectId().alphanum().length(24),
            userId: Joi.ObjectId().alphanum().length(24),
            categoryBased: Joi.string(),
            reason: Joi.string(),
            status: Joi.string(),
        }),
    });
};
//lms report validator
const getLeaveManagementValidator = async (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectIdValidation,
            user_id: objectIdValidation,
        }).unknown(true),
        query: Joi.object({
            state: Joi.string().valid('fresh', 'regular').required(),
            programId: arrayValidation.required(),
            classificationType: Joi.array()
                .items(Joi.string().valid('leave', 'permission', 'on_duty'))
                .required(),
            from: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            to: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            programSearch: arrayValidation,
            sortField: Joi.string().valid(
                'totalApprovedPercentage',
                'totalRejectedPercentage',
                'totalPendingPercentage',
            ),
            sortType: Joi.string().valid('asc', 'dsc'),
            _institution_calendar_id: objectIdValidation,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errorMessage = error.details[0].message.replace(/['"]/g, '');
        return res.status(400).json({ message: error.name, error: errorMessage });
    }
    next();
};
const getApplicationStatusValidator = async (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectIdValidation,
            user_id: objectIdValidation,
        }).unknown(true),
        query: Joi.object({
            state: Joi.string().valid('fresh', 'regular').required(),
            programId: arrayValidation.required(),
            type: Joi.string().valid('headers', 'query').required(),
            classificationType: Joi.array()
                .items(Joi.string().valid('leave', 'permission', 'on_duty'))
                .required(),
            from: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            to: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            studentSearch: arrayValidation,
            programSearchKey: arrayValidation,
            programSearch: arrayValidation,
            appClassificationSearch: Joi.array().items(
                Joi.string().valid('leave', 'permission', 'on_duty'),
            ),
            statusSearch: arrayValidation,
            year: arrayValidation,
            level: arrayValidation,
            term: arrayValidation,
            _institution_calendar_id: objectIdValidation,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errorMessage = error.details[0].message.replace(/['"]/g, '');
        return res.status(400).json({ message: error.name, error: errorMessage });
    }
    next();
};
const getStudentApplicationValidator = async (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectIdValidation,
            user_id: objectIdValidation,
        }).unknown(true),
        query: Joi.object({
            studentId: objectIdValidation,
            programId: objectIdValidation,
            startDate: Joi.string(),
            endDate: Joi.string(),
            year: Joi.string().required(),
            level: Joi.string().required(),
            term: Joi.string().required(),
            _institution_calendar_id: objectIdValidation,
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errorMessage = error.details[0].message.replace(/['"]/g, '');
        return res.status(400).json({ message: error.name, error: errorMessage });
    }
    next();
};
const programYearWiseReportValidator = async (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectIdValidation,
            user_id: objectIdValidation,
        }).unknown(true),
        query: Joi.object({
            programId: objectIdValidation,
            from: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            to: Joi.string()
                .regex(/^(\d{4})-(\d{2})-(\d{2})$/)
                .required(),
            term: arrayValidation,
            _institution_calendar_id: objectIdValidation,
            state: Joi.string().valid('fresh', 'regular').required(),
            classificationType: arrayValidation,
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errorMessage = error.details[0].message.replace(/['"]/g, '');
        return res.status(400).json({ message: error.name, error: errorMessage });
    }
    next();
};
const sessionWiseLeaveApplyValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().key({
            _institution_id: Joi.ObjectId().required(),
            _institution_calendar_id: Joi.ObjectId().required(),
        }),
    });
};
const sessionWiseLeaveListForApproverValidator = async () => {
    Joi.object().key({
        headers: Joi.object().key({
            _institution_id: Joi.ObjectId().required(),
            _institution_calendar_id: Joi.ObjectId().required(),
        }),
        query: Joi.object().keys({
            userId: Joi.ObjectId().required(),
            roleId: Joi.ObjectId().required(),
            classificationType: Joi.string().required(),
        }),
    });
};
const sessionWiseStaffApprovalValidator = async () => {
    Joi.object().key({
        headers: Joi.object().key({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            leaveApplicationWithStatus: Joi.array().items(Joi.ObjectId().required()),
            approvedStaffId: Joi.ObjectId().required(),
            studentId: Joi.ObjectId().required(),
            roleId: Joi.ObjectId().required(),
            levelName: Joi.string().required(),
        }),
    });
};
const sessionWiseLeaveHistoryListValidator = async () => {
    Joi.object().key({
        headers: Joi.object().key({
            _institution_id: Joi.ObjectId().required(),
            _institution_calendar_id: Joi.ObjectId().required(),
        }),
        query: Joi.object().keys({
            userId: Joi.ObjectId().required(),
            roleId: Joi.ObjectId().required(),
            classificationType: Joi.string().required(),
        }),
    });
};
module.exports = {
    lmsApplyValidator,
    updateStatusValidator,
    listDataValidator,
    previouslyLeaveStatusValidator,
    listScheduleValidator,
    updateAgreeValidator,
    staffApprovalValidator,
    revokeApprovalValidator,
    editStaffApprovalValidator,
    getLeaveManagementValidator,
    getApplicationStatusValidator,
    getStudentApplicationValidator,
    programYearWiseReportValidator,
    sessionWiseLeaveApplyValidator,
    sessionWiseLeaveListForApproverValidator,
    sessionWiseStaffApprovalValidator,
    sessionWiseLeaveHistoryListValidator,
};
