const mongoose = require('mongoose');
const { getConnectionString } = require('../../config/db.config');
const Schema = mongoose.Schema;
const {
    INSTITUTION,
    PROGRAM,
    CURRICULUM,
    DEPARTMENT,
    DEPARTMENT_SUBJECT,
    SESSION_DELIVERY_TYPES,
    INSTITUTION_CALENDAR,
    USER,
    COURSE,
    SESSION_ORDER,
} = require('../../utility/constants');

const courseSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        courseType: {
            type: String,
            required: true,
        },
        _curriculum_id: {
            type: Schema.Types.ObjectId,
            ref: CURRICULUM,
        },
        duration: {
            type: Number,
        },
        isYearLongCourse: {
            type: Boolean,
            default: false,
        },
        isPhaseFlowWithOutLevel: {
            type: Boolean,
            default: false,
        },
        courseRecurringYearWise: [
            {
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        courseOccurringYearWise: [
            {
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        courseRecurring: [
            {
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: {
                    type: String,
                },
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                levelNo: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        courseOccurring: [
            {
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: {
                    type: String,
                },
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                levelNo: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        preRequisiteCourses: [
            {
                _course_id: {
                    type: Schema.Types.ObjectId,
                },
                courseName: {
                    type: String,
                },
                courseCode: {
                    type: String,
                },
            },
        ],
        coRequisiteCourses: [
            {
                _course_id: {
                    type: Schema.Types.ObjectId,
                },
                courseName: {
                    type: String,
                },
                courseCode: {
                    type: String,
                },
            },
        ],
        courseCode: {
            type: String,
        },
        courseName: {
            type: String,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: PROGRAM,
        },
        administration: {
            _program_id: {
                type: Schema.Types.ObjectId,
                ref: PROGRAM,
            },
            programName: String,
            _department_id: {
                type: Schema.Types.ObjectId,
                ref: DEPARTMENT,
            },
            departmentName: String,
            _subject_id: {
                type: Schema.Types.ObjectId,
                ref: DEPARTMENT_SUBJECT,
            },
            subjectName: String,
        },
        courseEditor: {
            _staff_id: {
                type: Schema.Types.ObjectId,
            },
            name: {
                first: {
                    type: String,
                    trim: true,
                },
                middle: {
                    type: String,
                    trim: true,
                },
                last: {
                    type: String,
                    trim: true,
                },
                family: {
                    type: String,
                    trim: true,
                },
            },
        },
        participating: [
            {
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: PROGRAM,
                },
                programName: String,
                _department_id: {
                    type: Schema.Types.ObjectId,
                    ref: DEPARTMENT,
                },
                departmentName: String,
                _subject_id: {
                    type: Schema.Types.ObjectId,
                    ref: DEPARTMENT_SUBJECT,
                },
                subjectName: String,
            },
        ],
        portfolio: [
            {
                url: String,
                title: String,
                description: String,
            },
        ],
        courseAssignedDetails: [
            {
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: PROGRAM,
                },
                programName: String,
                _curriculum_id: {
                    type: Schema.Types.ObjectId,
                    ref: CURRICULUM,
                },
                curriculumName: String,
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: String,
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                levelNo: {
                    type: String,
                },
                courseDuration: {
                    startWeek: Number,
                    endWeek: Number,
                    total: Number,
                },
                isActive: {
                    type: Boolean,
                    default: true,
                },
                isSessionOrderWeekActive: {
                    type: Boolean,
                    default: false,
                },
                isSharedWithIndependentCourse: {
                    type: Boolean,
                    default: false,
                },
                courseSharedWith: [
                    {
                        _program_id: {
                            type: Schema.Types.ObjectId,
                            ref: PROGRAM,
                        },
                        programName: String,
                        _curriculum_id: {
                            type: Schema.Types.ObjectId,
                            ref: CURRICULUM,
                        },
                        curriculumName: String,
                        _year_id: {
                            type: Schema.Types.ObjectId,
                        },
                        year: String,
                        _level_id: {
                            type: Schema.Types.ObjectId,
                        },
                        levelNo: String,
                    },
                ],
            },
        ],
        sessionDeliveryType: [
            {
                typeName: String,
                typeSymbol: String,
                creditHours: Number,
                contactHours: Number,
                durationSplit: Boolean,
                duration: Number,
                _session_id: {
                    type: Schema.Types.ObjectId,
                    ref: SESSION_DELIVERY_TYPES,
                },
                deliveryType: [
                    {
                        _delivery_id: {
                            type: Schema.Types.ObjectId,
                            ref: SESSION_DELIVERY_TYPES,
                        },
                        deliveryType: String,
                        deliverySymbol: String,
                        duration: Number,
                        isActive: Boolean,
                    },
                ],
            },
        ],
        disableCreditHoursIndianSystem: {
            type: Boolean,
            default: false,
        },
        disableDeliveryDurationIndianSystem: {
            type: Boolean,
            default: false,
        },
        disablePeriodWeekIndianSystem: {
            type: Boolean,
            default: false,
        },
        allowEditing: {
            type: Boolean,
            default: false,
        },
        achieveTarget: Boolean,
        isConfigured: {
            type: Boolean,
            default: false,
        },
        linkSessions: [
            {
                sessionOrder: [
                    {
                        _session_id: {
                            type: Schema.Types.ObjectId,
                            ref: SESSION_ORDER,
                        },
                    },
                ],
            },
        ],
        linkSessionsOrder: [
            {
                startSession: Number,
                endSession: Number,
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: String,
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                levelNo: String,
            },
        ],
        themes: [
            {
                name: String,
                symbol: String,
                color: String,
                description: String,
                subThemes: [
                    {
                        name: String,
                        symbol: String,
                        color: String,
                        description: String,
                    },
                ],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isWeekActiveForIndependentCourse: {
            type: Boolean,
            default: false,
        },
        isDrafted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = courseSchema;
