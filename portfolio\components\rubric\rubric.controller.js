const RubricService = require('./rubric.service');

const createRubric = async ({
    body: {
        name = '',
        desc = '',
        scoring = '',
        parameters = [],
        isGlobal = false,
        status = DRAFT,
        type = {},
    },
    headers: { user_id: userId } = {},
}) => {
    const rubric = await RubricService.createRubric({
        name,
        desc,
        scoring,
        parameters,
        isGlobal,
        userId,
        status,
        type,
    });

    return { statusCode: 200, message: 'CREATED_SUCCESSFULLY', data: { _id: rubric._id } };
};

const getRubrics = async ({ query: { rubricId, isGlobal } }) => {
    const rubrics = await RubricService.getRubrics({ rubricId, isGlobal });

    return { statusCode: 200, message: 'SUCCESS', data: rubrics };
};

const updateRubric = async ({
    query: { rubricId },
    body: { name = '', desc = '', scoring = '', parameters = [], isGlobal, status, type },
}) => {
    await RubricService.updateRubric({
        rubricId,
        name,
        desc,
        scoring,
        parameters,
        status,
        isGlobal,
        type,
    });

    return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
};

const deleteRubric = async ({ query: { rubricId } }) => {
    await RubricService.deleteRubric({ rubricId });

    return { statusCode: 200, message: 'DELETED_SUCCESSFULLY' };
};

const createGlobalRubric = async ({
    body: { name = '', desc = '', scoring = '', parameters = [] },
    headers: { user_id: userId } = {},
}) => {
    const rubric = await RubricService.createGlobalRubric({
        name,
        desc,
        scoring,
        parameters,
        userId,
    });

    return { statusCode: 200, message: 'CREATED_SUCCESSFULLY', data: { _id: rubric._id } };
};

module.exports = { createRubric, getRubrics, updateRubric, deleteRubric, createGlobalRubric };
