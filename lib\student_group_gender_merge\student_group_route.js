const express = require('express');
const route = express.Router();
const student_group = require('./student_group_controller');
const student_grouping = require('./student_grouping_controller');
const student_group_course = require('./student_group_course_controller');
const student_group_rotation = require('./student_group_rotation_controller');
const student_group_individual_course = require('./student_group_individual_course_controller');
const student_group_elective = require('./student_group_individual_elective_controller');
const student_group_rotation_course = require('./student_group_rotation_course_controller');
const student_group_dashboard = require('./student_group_dashboard');
const student_group_publish = require('./student_group_publish_controller');
const validator = require('./student_group_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

//Dashboard Main API
route.get(
    '/dashboard/:program/:institution',
    [userPolicyAuthentication(['student_grouping:dashboard:course_view'])],
    validator.main_dashboard,
    student_group_dashboard.dashboard,
);
route.get(
    '/program_ic_list_dashboard',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'leave_management:student_register:view',
            'student_grouping:dashboard:view',
        ]),
    ],
    student_group_dashboard.program_ic_list_dashboard,
);
route.get(
    '/course_group_lists/:_id/:level/:batch/:course',
    [userPolicyAuthentication(['student_grouping:dashboard:course_view'])],
    validator.dashboard_course_groups,
    student_group_dashboard.course_group_lists,
);
route.get(
    '/student_global_search/:program/:institution/:academic_no',
    [userPolicyAuthentication(['student_grouping:dashboard:search'])],
    validator.std_global_search,
    student_group_dashboard.student_global_search,
);
route.put(
    '/student_global_edit',
    /* validator.std_global_edit, */
    [userPolicyAuthentication(['student_grouping:dashboard:search'])],
    student_group_dashboard.student_global_edit,
);
route.post(
    '/group_publish',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:publish'])],
    student_group_publish.group_publish,
);
route.post(
    '/course_publish',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:publish'])],
    student_group_publish.course_publish,
);
route.delete('/hard_delete', student_group_dashboard.hard_delete);

//Foundation Grouping
route.post(
    '/add_student_group',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:search'])],
    validator.add_student_group,
    student_group.add_student_group,
);
route.get(
    '/student_search/:academic_no/:id/:batch/:level',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:search'])],
    validator.student_search,
    student_group.student_search,
);
route.post(
    '/import',
    [userPolicyAuthentication(['student_grouping:dashboard:import'])],
    validator.import,
    student_group.fyd_import,
);
route.get(
    '/group_list_filter/:_id/:level/:batch/:gender/:group',
    [userPolicyAuthentication(['student_grouping:dashboard:dashboard_master_group:edit'])],
    validator.group_list_student_list,
    student_group.group_list_filter,
);
route.get(
    '/setting_get/:id/:level/:batch',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group_settings:view'])],
    validator.student_count_list,
    student_group.setting_get,
);
route.post(
    '/group_setting',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group_settings:update'])],
    validator.group_setting,
    student_group.group_setting,
);
route.post(
    '/grouping',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:group'])],
    validator.grouping,
    student_grouping.grouping,
);
route.put(
    '/group_change',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:edit'])],
    validator.group_change,
    student_group.group_change,
);
route.put(
    '/excess_count_change',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:extra_allowed'])],
    validator.excess_count_change,
    student_group.excess_count_change,
);
route.delete(
    '/fyd_student_delete',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:delete'])],
    validator.fyd_bulk_student_delete,
    student_group.fyd_student_delete,
);
route.post(
    '/data_check',
    [userPolicyAuthentication(['student_grouping:dashboard:import'])],
    validator.data_check_validator,
    student_group.data_check,
);
route.post('/import_students', validator.data_check_validator, student_group.import_students);
route.get(
    '/fyd_student_count_group/:program/:institution/:batch',
    validator.fyd_student_count,
    student_group.fyd_student_count_group,
);
route.post(
    '/save_import_students',
    validator.save_import_students_validator,
    student_group.save_import_students,
);

//Foundation Course
route.post(
    '/course_group_setting',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:edit'])],
    validator.course_group_setting,
    student_group_course.course_group_setting,
);
route.post(
    '/sub_course_grouping',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:ungrouped'])],
    validator.course_grouping,
    student_grouping.sub_course_grouping,
);
route.get(
    '/course_grouping_data/:_id/:level/:batch/:gender/:course/:group_no',
    validator.student_group_course_list,
    student_group_course.course_grouping_data,
);
route.get(
    '/course_grouped_list_filter/:_id/:level/:batch/:gender/:group',
    [userPolicyAuthentication(['student_grouping:dashboard:dashboard_master_group:edit'])],
    validator.group_list_student_list,
    student_group_course.course_grouped_list_filter,
);
route.post(
    '/add_student_course',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:add'])],
    validator.add_student_course,
    student_group_course.add_student_course,
);
route.post(
    '/fyd_course_clone',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:clone'])],
    validator.fyd_course_clone,
    student_group_course.fyd_course_clone,
);
route.put(
    '/fyd_course_student_edit',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:edit'])],
    validator.fyd_course_group_change,
    student_group_course.fyd_course_std_edit,
);
route.delete(
    '/fyd_course_student_delete',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:delete'])],
    validator.course_student_delete,
    student_group_course.fyd_course_std_delete,
);
route.get(
    '/fyd_get_clone_group/:_id/:batch/:level/:_course_id',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:clone'])],
    validator.get_clone,
    student_group_course.get_clone_group,
);
route.delete(
    '/foundation_students_delete',
    validator.fyd_bulk_student_delete,
    student_group.student_bulk_delete,
);
route.get(
    '/foundation_get_course_setting/:_id/:level/:batch/:_course_id/:gender',
    [userPolicyAuthentication(['student_grouping:dashboard:dashboard_master_group:edit'])],
    validator.fyd_get_course_setting,
    student_group_course.fyd_get_course_setting,
);

//Exports
route.get(
    '/fyd_export/:_id/:level/:batch',
    [userPolicyAuthentication(['student_grouping:dashboard:dashboard_master_group:view'])],
    validator.fyd_export,
    student_group.fyd_export,
);
route.get(
    '/fyd_course_group_export/:_id/:level/:batch/:course/:fyd_group/:gender/:delivery/:d_group_no',
    [userPolicyAuthentication(['student_grouping:dashboard:course_view'])],
    validator.fyd_course_group_export,
    student_group_course.fyd_course_group_export,
);
route.get(
    '/fyd_group_export/:_id/:level/:batch/:gender/:fyd_group/:course/:delivery',
    validator.fyd_group_export,
    student_group.fyd_group_export,
);

//Individual Course
route.post(
    '/course_import',
    [userPolicyAuthentication(['student_grouping:dashboard:import'])],
    validator.course_import,
    student_group_individual_course.course_group_create,
);
route.post(
    '/individual_data_check',
    [userPolicyAuthentication(['student_grouping:dashboard:import'])],
    validator.year2_data_check_validator,
    student_group.individual_data_check,
);
route.get(
    '/individual_courses_list/:_id/:level/:batch/:gender',
    [
        userPolicyAuthentication([
            'student_grouping:dashboard:export',
            'student_grouping:dashboard:course_group:view',
        ]),
    ],
    validator.individual_course_student_list,
    student_group_individual_course.individual_courses_list,
);
route.get(
    '/individual_course_grouped_list_filter/:_id/:level/:batch/:gender/:course',
    [
        userPolicyAuthentication([
            'student_grouping:dashboard:export',
            'student_grouping:dashboard:course_group:view',
        ]),
    ],
    validator.individual_course_student_list_filter,
    student_group_individual_course.individual_course_grouped_list_filter,
);
route.post(
    '/individual_course_group_setting',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group_settings:update'])],
    validator.individual_course_group_setting,
    student_group_individual_course.individual_course_group_setting,
);
route.post(
    '/individual_course_grouping',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:group'])],
    validator.course_grouping,
    student_grouping.individual_course_grouping,
);
route.get(
    '/get_course_setting/:_id/:level/:batch/:_course_id',
    [
        userPolicyAuthentication([
            'student_grouping:dashboard:course_group:view',
            'student_grouping:dashboard:course_group_settings:view',
        ]),
    ],
    validator.individual_get_course_setting,
    student_group_course.get_course_setting,
);
route.get(
    '/course_group_export/:_id/:level/:batch/:course/:gender/:delivery/:d_group_no',
    validator.ind_course_group_export,
    student_group_course.fyd_course_group_export,
);
route.put(
    '/student_group_change',
    validator.course_group_change,
    student_group_course.std_group_change,
);
route.delete(
    '/student_delete',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:delete'])],
    validator.course_student_delete,
    student_group_course.individual_std_delete,
);
route.get(
    '/get_clone_group/:_id/:batch/:level/:_course_id',
    validator.get_clone,
    student_group_course.nr_get_clone_group,
);
route.get(
    '/get_student/:academic_no/:id/:level/:batch/:course_id',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:add'])],
    validator.get_student_validator,
    student_group.get_student,
);
route.post(
    '/add_student_to_course',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:add'])],
    validator.add_student_to_course_validator,
    student_group.add_student_to_course,
);
route.get(
    '/get_student_courses_year_two/:academic_no/:_id/:level/:batch/:_course_id',
    validator.get_student_courses_year_two_validator,
    student_group.get_student_courses_year_two,
);
route.get(
    '/course_student_list_dashboard/:_id/:level/:batch/:_course_id',
    validator.ind_course_std_list_dashboard,
    student_group_individual_course.individual_student_list_dashboard,
);

//Rotation
route.post(
    '/rotation_group_setting',
    validator.rotation_group_setting,
    student_group_rotation.rotation_group_setting,
);
route.get(
    '/rotation_setting_get/:_id/:level/:term',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group_settings:view'])],
    /* validator.student_count_list, */ student_group_rotation.rotation_setting_get,
);
route.post(
    '/rotation_grouping',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:group'])],
    /* validator.rotation_grouping, */ student_grouping.rotation_grouping,
);
route.get(
    '/rotation_get_student/:academic_no/:_id/:level/:batch',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:add'])],
    validator.rotation_get_student,
    student_group_rotation.rotation_get_student,
);
route.post(
    '/rotation_add_student',
    validator.rotation_add_student,
    student_group_rotation.rotation_add_student,
);
route.get(
    '/rotation_course_grouped_list_filter/:id/:batch/:gender/:group',
    validator.group_list_student_list,
    student_group_rotation_course.rotation_course_grouped_list_filter,
);
route.delete(
    '/rotation_bulk_delete',
    [userPolicyAuthentication(['student_grouping:dashboard:master_group:delete'])],
    validator.rotation_bulk_student_delete,
    student_group_rotation.rotation_student_bulk_delete,
);

//Rotation Course
route.get(
    '/rotation_course_setting/:_id/:level/:batch/:_course_id/:gender/:group_no',
    validator.get_course_setting,
    student_group_rotation_course.get_rotation_course_setting,
);
route.get(
    '/rotation_elective_setting/:_id/:level/:batch/:_course_id/:gender/:group_no',
    validator.get_course_setting,
    student_group_rotation_course.get_rotation_elective_setting,
);
route.delete(
    '/rotation_course_student_delete',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:delete'])],
    validator.course_student_delete,
    student_group_rotation_course.rotation_course_std_delete,
);
route.put(
    '/rotation_course_student_group_change',
    validator.course_group_change,
    student_group_rotation_course.rotation_course_group_change,
);
route.post(
    '/rotation_add_student_course',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:add'])],
    validator.rotation_add_student_course,
    student_group_rotation_course.rotation_add_student_course,
);
route.get(
    '/rotation_get_student_course/:academic_no/:_id/:level/:batch/:_course_id',
    [userPolicyAuthentication(['student_grouping:dashboard:course_group:add'])],
    validator.rotation_get_student_course,
    student_group_rotation_course.rotation_get_student_course,
);

//Old

route.get(
    '/course_group_list_filter/:id/:batch/:gender',
    validator.course_groups_list,
    student_group_course.course_group_list_filter,
);
// route.get('/group_list_filter/:id/:batch/:gender/:group', validator.group_list_student_list, student_group.group_list_filter);
route.get(
    '/group_student_count/:id/:batch',
    validator.student_count_list,
    student_group.group_student_count,
);
route.get(
    '/course_setting_get/:id/:batch/:course',
    validator.settings_get,
    student_group_course.course_setting_get,
);
// route.get('/level_list', student_group.level_list);

//Individual
route.get(
    '/individual_course_list/:id/:curriculum/:level',
    validator.individual_course_list,
    student_group_individual_course.individual_course_list,
);
// route.get('/individual_course_list/:id/:curriculum/:level', validator.individual_course_list, student_group_individual_course.individual_course_list);
route.get(
    '/individual_course_setting_get/:id/:batch/:course',
    validator.settings_get,
    student_group_individual_course.individual_course_setting_get,
);

//Rotation
route.get(
    '/rotation_course_grouping_data/:id/:batch/:gender/:course/:group_no',
    validator.student_group_course_list,
    student_group_rotation_course.rotation_course_grouping_data,
);
route.get(
    '/rotation_group_list_filter/:id/:batch/:gender/:group',
    validator.group_list_student_list,
    student_group_rotation.rotation_group_list_filter,
);
// route.get('/rotation_setting_get/:id/:batch', validator.student_count_list, student_group_rotation.rotation_setting_get);
route.get(
    '/rotation_course_setting_get/:id/:batch/:course',
    validator.settings_get,
    student_group_rotation_course.rotation_course_setting_get,
);

//route.post('/import', student_group.group_create);
//route.get('/get_student/:academic_no/:course_id/:id/:year/:level/:term', validator.get_student_validator, student_group.get_student);

//route.post('/add_student', validator.add_student_validator, student_group.add_student);

// route.post('/course_group_setting', validator.course_group_setting, student_group_course.course_group_setting);
// route.post('/sub_course_grouping', validator.course_grouping, student_grouping.sub_course_grouping);
route.put(
    '/sub_course_group_change',
    validator.course_group_change,
    student_group_course.sub_course_group_change,
);
route.put(
    '/elective_group_change',
    validator.elective_group_change,
    student_group_rotation_course.elective_group_change,
);

//Individual
route.put(
    '/individual_elective_group_change',
    validator.individual_elective_group_change,
    student_group_individual_course.elective_group_change,
);
route.post(
    '/course_import',
    [userPolicyAuthentication(['student_grouping:dashboard:import'])],
    validator.course_import,
    student_group_individual_course.course_group_create,
);

//Rotation
// route.post('/rotation_group_setting', validator.rotation_group_setting, student_group_rotation.rotation_group_setting);
// route.post('/rotation_grouping', validator.rotation_grouping, student_grouping.rotation_grouping);
route.post(
    '/rotation_course_grouping',
    validator.course_grouping,
    student_grouping.rotation_course_grouping,
);
route.put(
    '/rotation_group_change',
    validator.group_change,
    student_group_rotation_course.rotation_group_change,
);
route.put(
    '/rotation_course_group_change',
    validator.rotation_course_group_change,
    student_group_rotation_course.sub_course_group_change,
);
route.post('/rotation_course_student_add', student_group_course.rotation_course_student_add);
route.delete('/rotation_course_student', student_group_course.rotation_course_student);

route.get('/elective_setting_get/:id/:batch/:course', student_group_elective.elective_setting_get);

module.exports = route;
