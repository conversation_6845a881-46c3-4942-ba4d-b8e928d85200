const express = require('express');
const route = express.Router();
const institution = require('./calendar_event_controller');
const validater = require('./calendar_event_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
route.post('/list', [userPolicyAuthentication([])], institution.list_values);
route.get(
    '/:id',
    [userPolicyAuthentication(['calendar:institution_calendar:calendar_list_view'])],
    validater.institution_id,
    institution.list_id,
);
route.get(
    '/list_id_reviewer/:id',
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:calendar_list_view',
            defaultPolicy.DC_STAFF,
        ]),
    ],
    validater.institution_id,
    institution.list_id_reviewer,
);
route.get(
    '/list_calendar/:id',
    [userPolicyAuthentication(['program_calendar:dashboard:calendar_settings'])],
    validater.institution_id,
    institution.list_calendar_event,
);
route.get(
    '/list_event/:id',
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:calendar_list_view',
            defaultPolicy.DC_STUDENT,
            defaultPolicy.DC_STAFF,
        ]),
    ],
    validater.institution_id,
    institution.list_event,
);
route.get(
    '/list_calendar_event_reviewer/:id',
    [userPolicyAuthentication(['calendar:institution_calendar:review:search'])],
    validater.institution_id,
    institution.list_calendar_event_reviewer,
);
route.get(
    '/date_filter/:id',
    [userPolicyAuthentication([])],
    validater.institution_event_with_filter,
    institution.list_calendar_event_date_filter,
);
route.get('/', [userPolicyAuthentication([])], institution.list);
route.post(
    '/',
    [userPolicyAuthentication(['calendar:institution_calendar:add_event'])],
    validater.institution,
    institution.insert,
);
route.put(
    '/:id',
    [userPolicyAuthentication(['calendar:institution_calendar:event_details:edit'])],
    validater.institution_update_id,
    institution.update,
);
route.delete(
    '/:id',
    [userPolicyAuthentication(['calendar:institution_calendar:event_details:delete'])],
    validater.institution_id,
    institution.delete,
);
route.post(
    '/add_reviewer/:id',
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:review:search',
            'calendar:institution_calendar:review:edit',
        ]),
    ],
    validater.institution_add_reviewer,
    institution.add_reviewer,
);
route.delete(
    '/remove_reviewer/:id/:reviewer',
    [userPolicyAuthentication(['calendar:institution_calendar:review:edit'])],
    validater.remove_reviewer,
    institution.remove_reviewer,
);
route.post(
    '/add_reviewer_review',
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:review:search',
            'calendar:institution_calendar:review:edit',
        ]),
    ],
    validater.institution_add_reviewer_review,
    institution.add_reviewer_review,
);
route.post(
    '/send_reviewer_notification/:id' /* , validater.institution_add_reviewer_review */,
    [userPolicyAuthentication(['calendar:institution_calendar:review:send_review_request'])],
    institution.send_reviewer_notification,
);
route.post(
    '/send_notification' /* , validater.institution_add_reviewer_review */,
    [
        userPolicyAuthentication([
            'calendar:institution_calendar:review:publish',
            'calendar:institution_calendar:publish',
            defaultPolicy.DC_STAFF,
        ]),
    ],
    institution.send_dean_reviewer_notification,
);

module.exports = route;
