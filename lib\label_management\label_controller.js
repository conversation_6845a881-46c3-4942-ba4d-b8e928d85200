const { courseUpdate } = require('./label_service');

exports.updateCourseLable = async (req, res) => {
    try {
        const { _id, language, field, label } = req.body;
        const { status, course } = await courseUpdate(_id, language, field, label);
        if (status) res.status(200).send({ status, message: 'updated successfully', course });
        else res.send('error on update');
    } catch (err) {
        res.status(500).send(err.message);
    }
};
