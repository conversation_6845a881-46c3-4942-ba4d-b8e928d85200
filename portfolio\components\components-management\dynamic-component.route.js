const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const DynamicComponentController = require('./dynamic-component.controller');
const {
    createDynamicComponentSchema,
    getDynamicComponentSchema,
    deleteDynamicComponentSchema,
} = require('./dynamic-component.validation');

router.post(
    '/',
    validate(createDynamicComponentSchema),
    catchAsync(DynamicComponentController.createDynamicOutcomeComponent),
);

router.get(
    '/',
    validate(getDynamicComponentSchema),
    catchAsync(DynamicComponentController.getDynamicOutcomeComponents),
);

router.delete(
    '/',
    validate(deleteDynamicComponentSchema),
    catchAsync(DynamicComponentController.deleteDynamicOutcomeComponent),
);

module.exports = router;
