const express = require('express');
const route = express.Router();
const role = require('./role_assign_controller');
const validator = require('./role_assign_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get(
    '/:id',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'user_management:staff_management:registered:assign_role',
        ]),
    ],
    validator.role_id,
    role.list_id,
);
route.post(
    '/department_list',
    [userPolicyAuthentication(['user_management:staff_management:registered:assign_role'])],
    role.department_list,
);
route.post('/report_to_list', role.report_to_list);
route.post(
    '/',
    [userPolicyAuthentication(['user_management:staff_management:registered:assign_role'])],
    validator.role,
    role.insert,
);
//create policy key
route.put('/addPolicyKey', role.addPolicyKey);

module.exports = route;
