const assessmentManagementSchema = require('./assessment-management.model');
const assessmentCourseProgramSchema = require('../assessment-course-program/assessment-course-program.model');
const assessmentPlanningSchema = require('../assessment-planning/assessment-planning.model');
const { getProgramList, getUserRoleProgramList } = require('./assessment-management.service');
const {
    ASSESSMENT_MANAGEMENT,
    DIRECT,
    INDIRECT,
    INTERNAL,
    EXTERNAL,
} = require('../../../utility/constants');
const { convertToMongoObjectId } = require('../../../utility/common');

const getAssessmentTypes = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        let assessmentTypes = await assessmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    types: 1,
                },
            )
            .lean();
        if (!assessmentTypes) {
            await assessmentManagementSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                types: [
                    {
                        typeName: 'direct',
                        subTypes: [
                            {
                                typeName: 'internal',
                                isDefault: true,
                                isActive: true,
                                assessmentTypes: [
                                    {
                                        name: 'Quiz',
                                        isActive: true,
                                        isDefault: true,
                                    },
                                ],
                            },
                            {
                                typeName: 'external',
                                isDefault: true,
                                isActive: true,
                                assessmentTypes: [
                                    {
                                        name: 'Assignment',
                                        isActive: true,
                                        isDefault: true,
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        typeName: 'indirect',
                        subTypes: [
                            {
                                typeName: 'internal',
                                isDefault: true,
                                isActive: false,
                                assessmentTypes: [],
                            },
                            {
                                typeName: 'external',
                                isDefault: true,
                                isActive: true,
                                assessmentTypes: [
                                    {
                                        name: 'Survey',
                                        isActive: true,
                                        isDefault: true,
                                    },
                                    {
                                        name: 'Poll',
                                        isActive: true,
                                        isDefault: true,
                                    },
                                ],
                            },
                        ],
                    },
                ],
                // direct: {
                //     internal: {
                //         isActive: true,
                //         types: [{ name: 'Quiz', isActive: true, isDefault: true }],
                //     },
                //     external: {
                //         isActive: true,
                //         types: [{ name: 'Assignment', isActive: true, isDefault: true }],
                //     },
                // },
                // indirect: {
                //     external: {
                //         isActive: true,
                //         types: [
                //             { name: 'Survey', isActive: true, isDefault: true },
                //             { name: 'Poll', isActive: true, isDefault: true },
                //         ],
                //     },
                // },
            });
            assessmentTypes = await assessmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    {
                        types: 1,
                    },
                )
                .lean();
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: assessmentTypes };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAssessmentType = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, type, subType, assessmentName } = body;
        const dbUpdate = {
            $push: {
                'types.$[i].subTypes.$[j].assessmentTypes': {
                    name: assessmentName,
                    isActive: true,
                },
            },
        };
        let dbUpdateFilter = {};
        if (type === DIRECT) {
            if (subType === INTERNAL) {
                dbUpdateFilter = {
                    arrayFilters: [{ 'i.typeName': DIRECT }, { 'j.typeName': INTERNAL }],
                };
            } else {
                dbUpdateFilter = {
                    arrayFilters: [{ 'i.typeName': DIRECT }, { 'j.typeName': EXTERNAL }],
                };
            }
        } else {
            if (subType === INTERNAL) {
                dbUpdateFilter = {
                    arrayFilters: [{ 'i.typeName': INDIRECT }, { 'j.typeName': INTERNAL }],
                };
            } else {
                dbUpdateFilter = {
                    arrayFilters: [{ 'i.typeName': INDIRECT }, { 'j.typeName': EXTERNAL }],
                };
            }
        }
        const assessmentSetting = await assessmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        // Adding Details with Planning & Course Program Area
        const assessmentSettingData = await assessmentManagementSchema.findOne(
            { _id: convertToMongoObjectId(_id) },
            { types: 1 },
        );
        let assessmentPushObject;
        for (typeElement of assessmentSettingData.types) {
            for (subTypesElement of typeElement.subTypes) {
                const assessmentData = subTypesElement.assessmentTypes.find(
                    (assTypeElement) => assTypeElement.name === assessmentName,
                );
                if (assessmentData)
                    assessmentPushObject = {
                        $push: {
                            'types.$[i].subTypes.$[j].assessmentTypes': {
                                _id: convertToMongoObjectId(assessmentData._id),
                                name: assessmentName,
                                isActive: true,
                            },
                        },
                    };
            }
        }
        if (assessmentPushObject) {
            await assessmentCourseProgramSchema.updateMany(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                assessmentPushObject,
                dbUpdateFilter,
            );
            await assessmentPlanningSchema.updateMany(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                assessmentPushObject,
                dbUpdateFilter,
            );
        }
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT ADDED' };
        return { statusCode: 200, message: 'ASSESSMENT ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAssessmentType = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, type, subType, assessmentId, assessmentName } = body;
        const dbUpdate = {
            $set: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].name': assessmentName,
            },
        };
        let dbUpdateFilter = {};
        if (type === DIRECT) {
            if (subType === INTERNAL) {
                dbUpdateFilter = {
                    arrayFilters: [
                        { 'i.typeName': DIRECT },
                        { 'j.typeName': INTERNAL },
                        { 'k._id': convertToMongoObjectId(assessmentId) },
                    ],
                };
            } else {
                dbUpdateFilter = {
                    arrayFilters: [
                        { 'i.typeName': DIRECT },
                        { 'j.typeName': EXTERNAL },
                        { 'k._id': convertToMongoObjectId(assessmentId) },
                    ],
                };
            }
        } else {
            if (subType === INTERNAL) {
                dbUpdateFilter = {
                    arrayFilters: [
                        { 'i.typeName': INDIRECT },
                        { 'j.typeName': INTERNAL },
                        { 'k._id': convertToMongoObjectId(assessmentId) },
                    ],
                };
            } else {
                dbUpdateFilter = {
                    arrayFilters: [
                        { 'i.typeName': INDIRECT },
                        { 'j.typeName': EXTERNAL },
                        { 'k._id': convertToMongoObjectId(assessmentId) },
                    ],
                };
            }
        }
        const assessmentSetting = await assessmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        // Updating Details with Planning & Course Program Area
        await assessmentCourseProgramSchema.updateMany(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        await assessmentPlanningSchema.updateMany(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT UPDATED' };
        return { statusCode: 200, message: 'ASSESSMENT UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeAssessmentType = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, type, subType, assessmentId } = body;
        // Check Details with Planning & Course Program Area
        const courseProgramData = await assessmentCourseProgramSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                'types.subTypes.assessmentTypes._id': convertToMongoObjectId(assessmentId),
                'types.subTypes.assessmentTypes.planning': { $exists: true, $ne: [] },
            },
            { _id: 1 },
        );
        const planningData = await assessmentPlanningSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                'types.subTypes.assessmentTypes._id': convertToMongoObjectId(assessmentId),
                'types.subTypes.assessmentTypes.planning': { $exists: true, $ne: [] },
            },
            { _id: 1 },
        );
        if (courseProgramData || planningData)
            return {
                statusCode: 410,
                message: 'Assessment Has Planning in Program/Course Level',
                data: { courseProgramData, planningData },
            };

        const dbUpdate = {
            $pull: {
                'types.$[i].subTypes.$[j].assessmentTypes': {
                    _id: convertToMongoObjectId(assessmentId),
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [{ 'i.typeName': type }, { 'j.typeName': subType }],
        };
        const assessmentSetting = await assessmentManagementSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        // Updating Details with Planning & Course Program Area
        await assessmentCourseProgramSchema.updateMany(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        await assessmentPlanningSchema.updateMany(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT REMOVED' };
        return { statusCode: 200, message: 'ASSESSMENT REMOVED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleTypes = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, types } = body;
        const assessmentTypes = await assessmentManagementSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    types: 1,
                },
            )
            .lean();
        if (!assessmentTypes) return { statusCode: 404, message: 'NOT_FOUND' };

        // Need to check with Assessment Planning & Course-Program Area
        const toggleCheckQuery = [];
        for (typeElement of types) {
            for (subTypeElement of typeElement.subTypes) {
                if (!subTypeElement.isActive) {
                    toggleCheckQuery.push({
                        $and: [
                            {
                                'types.typeName': typeElement.typeName,
                            },
                            {
                                'types.subTypes.typeName': subTypeElement.typeName,
                            },
                        ],
                    });
                }
            }
        }
        if (toggleCheckQuery.length) {
            const planResponse = {
                'types.typeName': 1,
                'types.subTypes.typeName': 1,
                'types.subTypes.isActive': 1,
                'types.subTypes.assessmentTypes.isActive': 1,
                'types.subTypes.assessmentTypes.planning.isActive': 1,
            };
            const courseProgramData = await assessmentCourseProgramSchema.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    $or: toggleCheckQuery,
                },
                planResponse,
            );
            const planningData = await assessmentPlanningSchema.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    $or: toggleCheckQuery,
                },
                planResponse,
            );
            const validationCheckSubTypes = [];
            for (courseProgramElement of courseProgramData) {
                for (typeElement of courseProgramElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (
                            !validationCheckSubTypes.find(
                                (validationElement) =>
                                    validationElement.typeName === typeElement.typeName &&
                                    validationElement.subTypeName === subTypeElement.typeName,
                            ) &&
                            subTypeElement.isActive &&
                            subTypeElement.assessmentTypes.length
                        ) {
                            for (assessmentElement of subTypeElement.assessmentTypes) {
                                if (
                                    assessmentElement.isActive &&
                                    assessmentElement.planning.length
                                ) {
                                    if (
                                        !validationCheckSubTypes.find(
                                            (validationElement) =>
                                                validationElement.typeName ===
                                                    typeElement.typeName &&
                                                validationElement.subTypeName ===
                                                    subTypeElement.typeName,
                                        )
                                    ) {
                                        validationCheckSubTypes.push({
                                            typeName: typeElement.typeName,
                                            subTypeName: subTypeElement.typeName,
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            for (programElement of planningData) {
                for (typeElement of programElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (
                            !validationCheckSubTypes.find(
                                (validationElement) =>
                                    validationElement.typeName === typeElement.typeName &&
                                    validationElement.subTypeName === subTypeElement.typeName,
                            ) &&
                            subTypeElement.isActive &&
                            subTypeElement.assessmentTypes.length
                        ) {
                            for (assessmentElement of subTypeElement.assessmentTypes) {
                                if (
                                    assessmentElement.isActive &&
                                    assessmentElement.planning.length
                                ) {
                                    if (
                                        !validationCheckSubTypes.find(
                                            (validationElement) =>
                                                validationElement.typeName ===
                                                    typeElement.typeName &&
                                                validationElement.subTypeName ===
                                                    subTypeElement.typeName,
                                        )
                                    ) {
                                        validationCheckSubTypes.push({
                                            typeName: typeElement.typeName,
                                            subTypeName: subTypeElement.typeName,
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            for (typeElement of types) {
                for (subTypeElement of typeElement.subTypes) {
                    if (
                        !subTypeElement.isActive &&
                        validationCheckSubTypes.find(
                            (validationElement) =>
                                validationElement.typeName === typeElement.typeName &&
                                validationElement.subTypeName === subTypeElement.typeName,
                        )
                    )
                        return {
                            statusCode: 410,
                            message: 'Assessment Has Planning in Program/Course Level',
                        };
                }
            }
        }

        for (typeElement of types) {
            const assessmentTypeElement = assessmentTypes.types.findIndex(
                (assessTypeElement) =>
                    assessTypeElement.typeName.toString() === typeElement.typeName.toString(),
            );
            if (assessmentTypeElement !== -1)
                for (subtypeElement of typeElement.subTypes) {
                    const assessmentSubTypeElement = assessmentTypes.types[
                        assessmentTypeElement
                    ].subTypes.findIndex(
                        (assessSubTypeElement) =>
                            assessSubTypeElement.typeName.toString() ===
                            subtypeElement.typeName.toString(),
                    );
                    if (assessmentSubTypeElement !== -1)
                        assessmentTypes.types[assessmentTypeElement].subTypes[
                            assessmentSubTypeElement
                        ].isActive = subtypeElement.isActive;
                }
        }
        const assessmentSetting = await assessmentManagementSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(_id),
            },
            { $set: { types: assessmentTypes.types } },
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'NOT_UPDATED' };
        return { statusCode: 200, message: 'UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleAssessmentTypes = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { id, type, subType } = query;
        const { assessmentId, isActive } = body;

        let dbQuery = {};
        let findQuery = { _institution_id: convertToMongoObjectId(id) };
        if (type === DIRECT) {
            if (subType === INTERNAL) {
                findQuery = {
                    ...findQuery,
                    'direct.internal.types._id': convertToMongoObjectId(assessmentId),
                };
                dbQuery = {
                    ...dbQuery,
                    $set: { 'direct.internal.types.$.isActive': isActive === 'true' },
                };
            } else {
                findQuery = {
                    ...findQuery,
                    'direct.external.types._id': convertToMongoObjectId(assessmentId),
                };
                dbQuery = {
                    ...dbQuery,
                    $set: { 'direct.external.types.$.isActive': isActive === 'true' },
                };
            }
        } else {
            if (subType === INTERNAL) {
                findQuery = {
                    ...findQuery,
                    'indirect.internal.types._id': convertToMongoObjectId(assessmentId),
                };
                dbQuery = {
                    ...dbQuery,
                    $set: { 'indirect.internal.types.$.isActive': isActive === 'true' },
                };
            } else {
                findQuery = {
                    ...findQuery,
                    'indirect.external.types._id': convertToMongoObjectId(assessmentId),
                };
                dbQuery = {
                    ...dbQuery,
                    $set: { 'indirect.external.types.$.isActive': isActive === 'true' },
                };
            }
        }
        await assessmentManagementSchema.findOneAndUpdate(findQuery, dbQuery);
        return { statusCode: 200, message: 'UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { institutionCalendarId } = query;
        const userRoleProgram = await getUserRoleProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
        });
        const assessmentTypes = await assessmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    'types.typeName': 1,
                },
            )
            .lean();
        const assessmentPlanning = await assessmentPlanningSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: { $in: userRoleProgram },
                },
                {
                    types: 1,
                    _program_id: 1,
                    term: 1,
                },
            )
            .lean();
        const programList = await getProgramList({ _institution_id, userRoleProgram });
        let courseProgramData = await assessmentCourseProgramSchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: { $in: userRoleProgram },
            },
            {
                _course_id: 1,
                _program_id: 1,
                term: 1,
                level: 1,
                types: 1,
                type: 1,
            },
        );
        if (!courseProgramData) courseProgramData = [];
        const assignmentPlanned = [];
        for (programElement of programList) {
            for (const assessmentTypeList of assessmentTypes.types) {
                programElement[assessmentTypeList.typeName.toString()] = 0;
            }
            if (assessmentPlanning) {
                const programAssessment = assessmentPlanning.filter(
                    (assessmentElement) =>
                        assessmentElement._program_id.toString() === programElement._id.toString(),
                );
                if (programAssessment.length) {
                    for (const programAssessmentTypesElement of programAssessment)
                        for (typeElement of programAssessmentTypesElement.types) {
                            for (subTypeElement of typeElement.subTypes) {
                                if (subTypeElement.isActive)
                                    for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                                        if (assessmentTypeElement.isActive)
                                            for (planningElement of assessmentTypeElement.planning) {
                                                if (planningElement.isActive)
                                                    if (planningElement.occurring === 'program') {
                                                        programElement[
                                                            typeElement.typeName.toString()
                                                        ]++;
                                                        assignmentPlanned.push({
                                                            type: typeElement.typeName.toString(),
                                                            occurring: 'program',
                                                            name: planningElement.name,
                                                            term: programAssessmentTypesElement.term,
                                                        });
                                                    } else {
                                                        for (courseElement of planningElement.courses) {
                                                            programElement[
                                                                typeElement.typeName.toString()
                                                            ]++;
                                                            assignmentPlanned.push({
                                                                type: typeElement.typeName.toString(),
                                                                occurring: 'course',
                                                                courseId:
                                                                    courseElement.courseId.toString(),
                                                                name: planningElement.name,
                                                                term: programAssessmentTypesElement.term,
                                                            });
                                                        }
                                                    }
                                            }
                                    }
                            }
                        }
                }
            }
            for (courseProgramElement of courseProgramData.filter(
                (courseProgramData) =>
                    courseProgramData._program_id.toString() === programElement._id.toString(),
            ))
                for (typeElement of courseProgramElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (subTypeElement.isActive)
                            for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                                if (assessmentTypeElement.isActive) {
                                    const courseProgramAssignmentPlanned = assignmentPlanned.filter(
                                        (assignmentPlannedElement) =>
                                            courseProgramElement.type === 'program'
                                                ? !assignmentPlannedElement.courseId &&
                                                  assignmentPlannedElement.term ===
                                                      courseProgramElement.term
                                                : assignmentPlannedElement.courseId &&
                                                  assignmentPlannedElement.courseId.toString() ===
                                                      courseProgramElement._course_id.toString() &&
                                                  assignmentPlannedElement.term ===
                                                      courseProgramElement.term,
                                    );
                                    for (planningElement of assessmentTypeElement.planning) {
                                        if (
                                            !courseProgramAssignmentPlanned.find(
                                                (planElement) =>
                                                    planElement.type === typeElement.typeName &&
                                                    planElement.name === planningElement.name,
                                            )
                                        )
                                            programElement[typeElement.typeName.toString()]++;
                                    }
                                }
                            }
                    }
                }
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programList };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAssessmentTypes,
    addAssessmentType,
    updateAssessmentType,
    removeAssessmentType,
    toggleTypes,
    toggleAssessmentTypes,
    programList,
};
