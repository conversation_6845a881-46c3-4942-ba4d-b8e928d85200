// const assessmentManagementSchema = require('../assessment-management/assessment-management.model');
const attainmentManagementSchema = require('../attainment-management/attainment-management.model');
const assessmentCourseProgramSchema = require('../assessment-course-program/assessment-course-program.model');
const assessmentLibrarySchema = require('../assessment-library/assessment-library.model');
const assessmentReportSchema = require('./attainment-report.model');
const { convertToMongoObjectId, clone } = require('../../../utility/common');
const { RANGE, EQUAL } = require('../../../utility/constants');
const {
    getProgramWithTermList,
    getLevelCourseList,
    getProgramPLOWithCLO,
    getCoursesCLOs,
    getInstitutionCalendars,
    getYearLevelCourseList,
    getUserRoleProgramListIds,
} = require('./attainment-report.service');
const { getCourseCLO } = require('../assessment-library/assessment-library.service');
const {
    getUserCourseBasedProgramList,
} = require('../assessment-planning/assessment-planning.service');

const getAttainmentProgramList = async ({ headers = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const userRoleProgram = await getUserRoleProgramListIds({
            _institution_id,
            user_id,
            role_id,
        });
        const programList = await getProgramWithTermList({ _institution_id, userRoleProgram });
        const attainmentList = await attainmentManagementSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: {
                        $in: programList.map((programElement) =>
                            convertToMongoObjectId(programElement._id),
                        ),
                    },
                    type: 'program',
                },
                {
                    regulationName: 1,
                    _program_id: 1,
                    outcomes: 1,
                },
            )
            .lean();
        for (programElement of programList) {
            programElement.attainmentList = attainmentList.filter(
                (attainmentElement) =>
                    attainmentElement._program_id.toString() === programElement._id.toString(),
            );
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: programList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Getting Course List along with Level & Rotation in Program Calendar
const getReportCourseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { programId, institutionCalendarId, term } = query;
        const userProgramCourses = await getUserCourseBasedProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
        });
        const courseList = await getLevelCourseList({
            _institution_id,
            programId,
            institutionCalendarId,
            term,
            userProgramCourses,
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseAttainmentReport = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId, attainmentId, level, programId, institutionCalendarId, term, outCome } =
            query;
        const reportStruct = {
            percentage: null,
            level: '',
        };
        const courseCLO = (await getCourseCLO({ _institution_id, courseId })).map((cloElement) => {
            return {
                _id: cloElement._id,
                no: cloElement.no,
                name: cloElement.name,
                ...reportStruct,
            };
        });
        const attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                },
            )
            .lean();
        if (!attainmentList) return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const attainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        if (!attainmentOutCome || !attainmentPlanOutCome)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                term,
                level,
            },
            {
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                updatedAt: 1,
            },
        );
        // Check Details with Planning & Course Program Area
        // const courseProgramData = await assessmentCourseProgramSchema.findOne(
        //     {
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //         _program_id: convertToMongoObjectId(programId),
        //         _course_id: convertToMongoObjectId(courseId),
        //         term,
        //         level,
        //     },
        //     { types: 1 },
        // );

        libraryData = clone(libraryData);
        const libraryList = [];
        const courseCLOList = courseCLO.map((courseCloElement) => {
            return {
                _id: courseCloElement._id,
                ...reportStruct,
            };
        });
        for (libraryElement of libraryData) {
            for (questionElement of libraryElement.questionMarks) {
                questionElement.attendedCount = 0;
                questionElement.passedCount = 0;
                for (studentElement of libraryElement.studentDetails) {
                    const studentQuestion = studentElement.studentMarks.find(
                        (studentQuestionMarkElement) =>
                            studentQuestionMarkElement.questionName ===
                            questionElement.questionName,
                    );
                    if (studentQuestion && studentQuestion.mark !== null) {
                        questionElement.attendedCount++;
                        if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                            questionElement.passedCount++;
                    }
                }
                if (questionElement.attendedCount > 0)
                    questionElement.average = (
                        questionElement.passedCount / questionElement.attendedCount
                    ).toFixed(4);
            }
            const assessmentClo = clone(courseCLO);
            for (courseCloElement of assessmentClo) {
                const cloQuestions = libraryElement.questionMarks.filter(
                    (questionElement) =>
                        questionElement.attendedCount > 0 &&
                        questionElement.outComeIds.find(
                            (outComeIdElement) =>
                                outComeIdElement.toString() === courseCloElement._id.toString(),
                        ),
                );
                courseCloElement.percentage =
                    cloQuestions.reduce((n, cloElement) => n + parseFloat(cloElement.average), 0) /
                    cloQuestions.length;
                if (courseCloElement.percentage)
                    courseCloElement.percentage = courseCloElement.percentage.toFixed(4) * 100;
            }
            libraryElement.clo = assessmentClo.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    percentage: cloElement.percentage,
                    level: '',
                };
            });
            libraryList.push({
                _id: libraryElement._id,
                _assessment_id: libraryElement._assessment_id,
                assessmentName: libraryElement.assessmentName,
                assessmentMark: libraryElement.assessmentMark,
                typeName: libraryElement.typeName,
                typeId: libraryElement.typeId,
                subTypeName: libraryElement.subTypeName,
                subTypeId: libraryElement.subTypeId,
                assessmentTypeName: libraryElement.assessmentTypeName,
                assessmentTypeId: libraryElement.assessmentTypeId,
                questionOutcome: libraryElement.questionOutcome,
                questionMarks: libraryElement.questionMarks,
                noQuestions: libraryElement.noQuestions,
                benchMark: libraryElement.benchMark,
                clo: libraryElement.clo,
                updatedAt: libraryElement.updatedAt,
            });
        }
        // return { data: { libraryList, attainmentPlanOutCome } };
        // Attainment Outcome Mode
        const attainmentNodeList = [
            {
                _id: convertToMongoObjectId(),
                typeName: 'Overall Attainment',
                subTree: [],
                clo: [],
            },
        ];
        const overAllCLO = [];
        for (treeElement of attainmentPlanOutCome.tree) {
            const subTreeList = [];
            const treeCLO = [];
            const levelTree = attainmentOutCome.levels.find((levelElement) =>
                levelElement.typeId
                    ? levelElement.typeId.toString() === treeElement.typeId.toString()
                    : levelElement.typeName === treeElement.typeName,
            );
            for (subTreeElement of treeElement.subTree) {
                const nodeList = [];
                const subTreeLevel =
                    levelTree && levelTree.subTree
                        ? levelTree.subTree.find(
                              (levelSubTreeElement) =>
                                  levelSubTreeElement.typeId.toString() ===
                                  subTreeElement.typeId.toString(),
                          )
                        : undefined;
                if (!subTreeElement.nodeName) {
                    const subTreeCLO = [];
                    for (nodeElement of subTreeElement.node) {
                        const nodeClo = [];
                        const nodeAttainments = clone(
                            libraryList.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        nodeElement.typeId.toString() &&
                                    treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() &&
                                    subTreeElement.typeId.toString() ===
                                        libraryElement.subTypeId.toString(),
                            ),
                        );
                        const nodeLevel =
                            subTreeLevel && subTreeLevel.node
                                ? subTreeLevel.node.find(
                                      (nodeLevelElement) =>
                                          nodeLevelElement.typeId.toString() ===
                                          nodeElement.typeId.toString(),
                                  )
                                : undefined;
                        for (nodeAttainmentElement of nodeAttainments) {
                            for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                    if (
                                        assignmentQuestionElement.outComeIds.find(
                                            (outComeIdElement) =>
                                                outComeIdElement.toString() ===
                                                nodeAttainmentCLOElement._id.toString(),
                                        )
                                    ) {
                                        const nodeCloIndex = nodeClo.findIndex(
                                            (closElement) =>
                                                closElement._id.toString() ===
                                                nodeAttainmentCLOElement._id.toString(),
                                        );
                                        if (nodeCloIndex == -1)
                                            nodeClo.push({
                                                _id: nodeAttainmentCLOElement._id.toString(),
                                                percentage: nodeAttainmentCLOElement.percentage,
                                                count:
                                                    nodeAttainmentCLOElement.percentage === null
                                                        ? 0
                                                        : 1,
                                                level: '',
                                            });
                                        else {
                                            nodeClo[nodeCloIndex].percentage +=
                                                nodeAttainmentCLOElement.percentage;
                                            nodeClo[nodeCloIndex].count++;
                                        }
                                    }
                                }
                            }
                        }
                        for (nodeCloElement of nodeClo)
                            if (nodeCloElement.count) {
                                if (nodeCloElement.percentage !== null)
                                    nodeCloElement.percentage /= nodeCloElement.count;
                                const percentageLevel =
                                    nodeLevel && nodeLevel.levelValues
                                        ? nodeLevel.levelValues.find(
                                              (levelValueElement) =>
                                                  parseFloat(levelValueElement.min) <=
                                                      parseInt(nodeCloElement.percentage) &&
                                                  parseFloat(levelValueElement.max) >=
                                                      parseInt(nodeCloElement.percentage),
                                          )
                                        : '';
                                nodeCloElement.level =
                                    percentageLevel && percentageLevel.level
                                        ? percentageLevel.level
                                        : '';
                            }

                        nodeList.push({
                            _id: nodeElement._id,
                            typeName: nodeElement.typeName,
                            typeId: nodeElement.typeId,
                            nodeId: nodeElement.nodeId,
                            weightage: nodeElement.weightage,
                            nodeName: nodeElement.nodeName,
                            clo: nodeClo,
                            attainments: nodeAttainments,
                        });
                    }
                    for (nodeListElement of nodeList) {
                        for (nodeAttainmentCLOElement of nodeListElement.clo) {
                            const cloIndex = subTreeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    nodeAttainmentCLOElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                subTreeCLO.push({
                                    _id: nodeAttainmentCLOElement._id.toString(),
                                    percentage:
                                        nodeAttainmentCLOElement.percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : (nodeAttainmentCLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100,
                                    count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            else {
                                subTreeCLO[cloIndex].percentage +=
                                    nodeAttainmentCLOElement.percentage === null
                                        ? nodeAttainmentCLOElement.percentage
                                        : (nodeAttainmentCLOElement.percentage *
                                              nodeListElement.weightage) /
                                          100;
                                subTreeCLO[cloIndex].count++;
                            }

                            // const percentageLevel = nodeLevel.levelValues.find(
                            //     (levelValueElement) =>
                            //         parseFloat(levelValueElement.min) <=
                            //             parseFloat(nodeAttainmentCLOElement.percentage) &&
                            //         parseFloat(levelValueElement.max) >=
                            //             parseFloat(nodeAttainmentCLOElement.percentage),
                            // );
                            // nodeAttainmentCLOElement.level =
                            //     percentageLevel && percentageLevel.level
                            //         ? percentageLevel.level
                            //         : '';
                        }
                    }

                    for (cloSubTreeElement of subTreeCLO) {
                        // Tree
                        const percentageLevel =
                            subTreeLevel && subTreeLevel.levelValues
                                ? subTreeLevel.levelValues.find(
                                      (levelValueElement) =>
                                          parseFloat(levelValueElement.min) <=
                                              parseInt(cloSubTreeElement.percentage) &&
                                          parseFloat(levelValueElement.max) >=
                                              parseInt(cloSubTreeElement.percentage),
                                  )
                                : '';
                        cloSubTreeElement.level =
                            percentageLevel && percentageLevel.level ? percentageLevel.level : '';

                        const cloPercentage =
                            cloSubTreeElement.percentage === null
                                ? cloSubTreeElement.percentage
                                : (cloSubTreeElement.percentage * subTreeElement.weightage) / 100;
                        const cloIndex = treeCLO.findIndex(
                            (closElement) =>
                                closElement._id.toString() === cloSubTreeElement._id.toString(),
                        );
                        if (cloIndex == -1)
                            treeCLO.push({
                                _id: cloSubTreeElement._id.toString(),
                                percentage: cloPercentage,
                                count: cloPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (cloPercentage !== null) {
                            treeCLO[cloIndex].percentage += cloPercentage;
                            treeCLO[cloIndex].count++;
                        }
                    }
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        weightage: subTreeElement.weightage,
                        node: nodeList,
                        clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                } else {
                    const subTreeCLO = [];
                    const nodeAttainments = clone(
                        libraryList.filter(
                            (libraryElement) =>
                                libraryElement.assessmentTypeId &&
                                libraryElement.assessmentTypeId.toString() ===
                                    subTreeElement.typeId.toString() &&
                                (treeElement.typeId.toString() ===
                                    libraryElement.typeId.toString() ||
                                    treeElement.typeId.toString() ===
                                        libraryElement.subTypeId.toString()),
                        ),
                    );
                    for (nodeAttainmentElement of nodeAttainments) {
                        // const nodeLevel =
                        //     subTreeLevel && subTreeLevel.node
                        //         ? subTreeLevel.node.find(
                        //               (nodeLevelElement) =>
                        //                   nodeLevelElement.typeId.toString() ===
                        //                   nodeAttainmentElement.assessmentTypeId.toString(),
                        //           )
                        //         : undefined;
                        for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                            const cloIndex = subTreeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    nodeAttainmentCLOElement._id.toString(),
                            );
                            if (cloIndex == -1) {
                                subTreeCLO.push({
                                    _id: nodeAttainmentCLOElement._id.toString(),
                                    percentage: nodeAttainmentCLOElement.percentage,
                                    count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            } else if (nodeAttainmentCLOElement.percentage !== null) {
                                subTreeCLO[cloIndex].percentage =
                                    subTreeCLO[cloIndex].percentage === null
                                        ? nodeAttainmentCLOElement.percentage
                                        : subTreeCLO[cloIndex].percentage +
                                          nodeAttainmentCLOElement.percentage;
                                subTreeCLO[cloIndex].count++;
                            }
                            const percentageLevel =
                                subTreeLevel && subTreeLevel.levelValues
                                    ? subTreeLevel.levelValues.find(
                                          (levelValueElement) =>
                                              parseFloat(levelValueElement.min) <=
                                                  parseInt(nodeAttainmentCLOElement.percentage) &&
                                              parseFloat(levelValueElement.max) >=
                                                  parseInt(nodeAttainmentCLOElement.percentage),
                                      )
                                    : '';
                            nodeAttainmentCLOElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';
                        }
                    }

                    for (cloSubTreeElement of subTreeCLO) {
                        // Tree
                        const percentageLevel =
                            subTreeLevel && subTreeLevel.levelValues
                                ? subTreeLevel.levelValues.find(
                                      (levelValueElement) =>
                                          parseFloat(levelValueElement.min) <=
                                              parseInt(cloSubTreeElement.percentage) &&
                                          parseFloat(levelValueElement.max) >=
                                              parseInt(cloSubTreeElement.percentage),
                                  )
                                : '';
                        cloSubTreeElement.level =
                            percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                        const cloPercentage =
                            cloSubTreeElement.percentage === null
                                ? cloSubTreeElement.percentage
                                : (cloSubTreeElement.percentage * subTreeElement.weightage) / 100;

                        const cloIndex = treeCLO.findIndex(
                            (closElement) =>
                                closElement._id.toString() === cloSubTreeElement._id.toString(),
                        );
                        if (cloIndex == -1)
                            treeCLO.push({
                                _id: cloSubTreeElement._id.toString(),
                                percentage: cloPercentage,
                                count: cloPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (cloPercentage !== null) {
                            treeCLO[cloIndex].percentage += cloPercentage;
                            treeCLO[cloIndex].count++;
                        }
                    }
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        nodeId: subTreeElement.nodeId,
                        nodeName: subTreeElement.nodeName,
                        attainments: nodeAttainments,
                        clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                }
            }
            for (cloTreeElement of treeCLO) {
                // OverAll CLO
                const percentageLevel =
                    levelTree && levelTree.levelValues
                        ? levelTree.levelValues.find(
                              (levelValueElement) =>
                                  parseFloat(levelValueElement.min) <=
                                      parseInt(cloTreeElement.percentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(cloTreeElement.percentage),
                          )
                        : [];
                cloTreeElement.level =
                    percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                const cloPercentage =
                    cloTreeElement.percentage === null
                        ? cloTreeElement.percentage
                        : (cloTreeElement.percentage * treeElement.weightage) / 100;

                const cloIndex = overAllCLO.findIndex(
                    (closElement) => closElement._id.toString() === cloTreeElement._id.toString(),
                );
                if (cloIndex === -1) {
                    overAllCLO.push({
                        _id: cloTreeElement._id.toString(),
                        percentage: cloPercentage,
                        count: 1,
                        level: '',
                    });
                } else if (cloPercentage !== null) {
                    overAllCLO[cloIndex].percentage += cloPercentage;
                    overAllCLO[cloIndex].count++;
                }
            }
            attainmentNodeList.push({
                _id: treeElement._id,
                typeName: treeElement.typeName,
                typeId: treeElement.typeId,
                weightage: treeElement.weightage,
                subTree: subTreeList,
                clo: treeCLO.length ? treeCLO : courseCLOList,
            });
        }
        if (overAllCLO.length) {
            const overAllAttainmentIndex = attainmentNodeList.findIndex(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            const attElement = attainmentOutCome.levels.find(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            if (overAllAttainmentIndex !== -1) {
                for (overAllCLOElement of overAllCLO) {
                    const percentageLevel =
                        attElement && attElement.levelValues
                            ? attElement.levelValues.find(
                                  (levelValueElement) =>
                                      parseFloat(levelValueElement.min) <=
                                          parseInt(overAllCLOElement.percentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(overAllCLOElement.percentage),
                              )
                            : '';
                    overAllCLOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                }
                attainmentNodeList[overAllAttainmentIndex].clo = overAllCLO;
            }
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                courseCLO,
                attainmentNodeList,
                attainmentLevel: attainmentOutCome.levels,
                attainmentValuesAs: attainmentOutCome.valuesAs,
                attainmentTargetBenchMark: attainmentOutCome.targetBenchMark,
                levelColors: attainmentOutCome.levelColors,
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Course Attainment Report With Assessment Ids
const getCourseAttainmentReportWithFilter = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId, attainmentId, level, programId, institutionCalendarId, term, outCome } =
            body;
        let { assessmentIds } = body;
        const reportStruct = {
            percentage: null,
            level: '',
        };
        const courseCLO = (await getCourseCLO({ _institution_id, courseId })).map((cloElement) => {
            return {
                _id: cloElement._id,
                no: cloElement.no,
                name: cloElement.name,
                ...reportStruct,
            };
        });
        let attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                    type: 'course',
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    levelNo: level,
                    term,
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    assessmentSelections: 1,
                },
            )
            .lean();
        const programAttainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    assessmentSelections: 1,
                },
            )
            .lean();
        if (!attainmentList) attainmentList = programAttainmentList;
        if (!attainmentList || !programAttainmentList)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const assessmentSelections =
            programAttainmentList.assessmentSelections &&
            programAttainmentList.assessmentSelections.length
                ? programAttainmentList.assessmentSelections
                : [];
        const courseAssessmentIdIndex = assessmentSelections.findIndex(
            (selectionElement) =>
                selectionElement.institutionCalendarId.toString() ===
                    institutionCalendarId.toString() &&
                selectionElement.courseId.toString() === courseId.toString(),
        );
        if (assessmentIds)
            if (courseAssessmentIdIndex !== -1)
                assessmentSelections[courseAssessmentIdIndex].assessmentIds = assessmentIds;
            else
                assessmentSelections.push({
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    courseId: convertToMongoObjectId(courseId),
                    assessmentIds: assessmentIds.map((assessmentElement) =>
                        convertToMongoObjectId(assessmentElement),
                    ),
                });
        if (courseAssessmentIdIndex !== -1)
            assessmentIds = assessmentSelections[courseAssessmentIdIndex].assessmentIds;
        const attainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const levelValuesAs = attainmentOutCome.valuesAs;
        if (!attainmentOutCome || !attainmentPlanOutCome || !levelValuesAs)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };

        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                term,
                level,
            },
            {
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                updatedAt: 1,
            },
        );
        libraryData = clone(libraryData);
        const libraryList = [];
        const courseCLOList = courseCLO.map((courseCloElement) => {
            return {
                _id: courseCloElement._id,
                ...reportStruct,
            };
        });
        for (libraryElement of libraryData) {
            if (
                assessmentIds &&
                assessmentIds.find(
                    (assessmentIdElement) =>
                        assessmentIdElement.toString() === libraryElement._assessment_id.toString(),
                )
            )
                for (questionElement of libraryElement.questionMarks) {
                    questionElement.attendedCount = 0;
                    questionElement.passedCount = 0;
                    for (studentElement of libraryElement.studentDetails) {
                        const studentQuestion = studentElement.studentMarks.find(
                            (studentQuestionMarkElement) =>
                                studentQuestionMarkElement.questionName ===
                                questionElement.questionName,
                        );
                        if (studentQuestion && studentQuestion.mark !== null) {
                            questionElement.attendedCount++;
                            if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                                questionElement.passedCount++;
                        }
                    }
                    if (questionElement.attendedCount > 0)
                        questionElement.average = (
                            questionElement.passedCount / questionElement.attendedCount
                        ).toFixed(4);
                }
            const assessmentClo = clone(courseCLO);
            for (courseCloElement of assessmentClo) {
                const cloQuestions = libraryElement.questionMarks.filter(
                    (questionElement) =>
                        questionElement.attendedCount > 0 &&
                        questionElement.outComeIds.find(
                            (outComeIdElement) =>
                                outComeIdElement.toString() === courseCloElement._id.toString(),
                        ),
                );
                courseCloElement.percentage =
                    cloQuestions.reduce((n, cloElement) => n + parseFloat(cloElement.average), 0) /
                    cloQuestions.length;
                if (courseCloElement.percentage)
                    courseCloElement.percentage = courseCloElement.percentage.toFixed(4) * 100;
            }
            libraryElement.clo = assessmentClo.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    percentage: cloElement.percentage,
                    level: '',
                };
            });
            libraryList.push({
                _id: libraryElement._id,
                _assessment_id: libraryElement._assessment_id,
                assessmentName: libraryElement.assessmentName,
                assessmentMark: libraryElement.assessmentMark,
                typeName: libraryElement.typeName,
                typeId: libraryElement.typeId,
                subTypeName: libraryElement.subTypeName,
                subTypeId: libraryElement.subTypeId,
                assessmentTypeName: libraryElement.assessmentTypeName,
                assessmentTypeId: libraryElement.assessmentTypeId,
                questionOutcome: libraryElement.questionOutcome,
                questionMarks: libraryElement.questionMarks,
                noQuestions: libraryElement.noQuestions,
                benchMark: libraryElement.benchMark,
                clo: libraryElement.clo,
                updatedAt: libraryElement.updatedAt,
            });
        }
        // Attainment Outcome Mode
        const attainmentNodeList = [
            {
                _id: convertToMongoObjectId(),
                typeName: 'Overall Attainment',
                subTree: [],
                clo: [],
            },
        ];
        const overAllCLO = [];
        for (treeElement of attainmentPlanOutCome.tree) {
            const subTreeList = [];
            const treeCLO = [];
            const levelTree = attainmentOutCome.levels.find((levelElement) =>
                levelElement.typeId
                    ? levelElement.typeId.toString() === treeElement.typeId.toString()
                    : levelElement.typeName === treeElement.typeName,
            );
            for (subTreeElement of treeElement.subTree) {
                const nodeList = [];
                const subTreeLevel =
                    levelTree && levelTree.subTree
                        ? levelTree.subTree.find(
                              (levelSubTreeElement) =>
                                  levelSubTreeElement.typeId.toString() ===
                                  subTreeElement.typeId.toString(),
                          )
                        : undefined;
                if (!subTreeElement.nodeName) {
                    const subTreeCLO = [];
                    for (nodeElement of subTreeElement.node) {
                        const nodeClo = [];
                        const nodeAttainments = clone(
                            libraryList.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        nodeElement.typeId.toString() &&
                                    treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() &&
                                    subTreeElement.typeId.toString() ===
                                        libraryElement.subTypeId.toString(),
                            ),
                        );
                        const nodeLevel =
                            subTreeLevel && subTreeLevel.node
                                ? subTreeLevel.node.find(
                                      (nodeLevelElement) =>
                                          nodeLevelElement.typeId.toString() ===
                                          nodeElement.typeId.toString(),
                                  )
                                : undefined;
                        for (nodeAttainmentElement of nodeAttainments) {
                            if (
                                assessmentIds &&
                                assessmentIds.find(
                                    (assessmentIdElement) =>
                                        assessmentIdElement.toString() ===
                                        nodeAttainmentElement._assessment_id.toString(),
                                )
                            ) {
                                for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                    for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                        if (
                                            assignmentQuestionElement.outComeIds.find(
                                                (outComeIdElement) =>
                                                    outComeIdElement.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            )
                                        ) {
                                            const nodeCloIndex = nodeClo.findIndex(
                                                (closElement) =>
                                                    closElement._id.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            );
                                            if (nodeCloIndex == -1)
                                                nodeClo.push({
                                                    _id: nodeAttainmentCLOElement._id.toString(),
                                                    percentage: nodeAttainmentCLOElement.percentage,
                                                    count:
                                                        nodeAttainmentCLOElement.percentage === null
                                                            ? 0
                                                            : 1,
                                                    level: '',
                                                });
                                            else {
                                                nodeClo[nodeCloIndex].percentage +=
                                                    nodeAttainmentCLOElement.percentage;
                                                nodeClo[nodeCloIndex].count++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        const nodeSortedValues =
                            nodeLevel && nodeLevel.levelValues
                                ? nodeLevel.levelValues.sort((a, b) => {
                                      let comparison = 0;
                                      if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                          comparison = -1;
                                      } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                          comparison = 1;
                                      }
                                      return comparison;
                                  })
                                : [];
                        for (nodeCloElement of nodeClo)
                            if (nodeCloElement.count) {
                                if (nodeCloElement.percentage !== null)
                                    nodeCloElement.percentage /= nodeCloElement.count;
                                const percentageLevel = nodeSortedValues.find((levelValueElement) =>
                                    levelValuesAs === RANGE
                                        ? parseFloat(levelValueElement.min) <=
                                              parseInt(nodeCloElement.percentage) &&
                                          parseFloat(levelValueElement.max) >=
                                              parseInt(nodeCloElement.percentage)
                                        : levelValueElement.condition === EQUAL
                                        ? parseInt(levelValueElement.percentage) <
                                          parseInt(nodeCloElement.percentage)
                                        : parseInt(levelValueElement.percentage) <=
                                          parseInt(nodeCloElement.percentage),
                                );
                                nodeCloElement.level =
                                    percentageLevel && percentageLevel.level
                                        ? percentageLevel.level
                                        : '';
                            }

                        nodeList.push({
                            _id: nodeElement._id,
                            typeName: nodeElement.typeName,
                            typeId: nodeElement.typeId,
                            nodeId: nodeElement.nodeId,
                            weightage: nodeElement.weightage,
                            nodeName: nodeElement.nodeName,
                            clo: nodeClo,
                            attainments: nodeAttainments,
                        });
                    }
                    for (nodeListElement of nodeList) {
                        for (nodeAttainmentCLOElement of nodeListElement.clo) {
                            const cloIndex = subTreeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    nodeAttainmentCLOElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                subTreeCLO.push({
                                    _id: nodeAttainmentCLOElement._id.toString(),
                                    percentage:
                                        nodeAttainmentCLOElement.percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : (nodeAttainmentCLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100,
                                    count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            else {
                                subTreeCLO[cloIndex].percentage +=
                                    nodeAttainmentCLOElement.percentage === null
                                        ? nodeAttainmentCLOElement.percentage
                                        : (nodeAttainmentCLOElement.percentage *
                                              nodeListElement.weightage) /
                                          100;
                                subTreeCLO[cloIndex].count++;
                            }
                        }
                    }

                    for (cloSubTreeElement of subTreeCLO) {
                        // Tree
                        const subtreeSortedValues =
                            subTreeLevel && subTreeLevel.levelValues
                                ? subTreeLevel.levelValues.sort((a, b) => {
                                      let comparison = 0;
                                      if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                          comparison = -1;
                                      } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                          comparison = 1;
                                      }
                                      return comparison;
                                  })
                                : [];
                        const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(cloSubTreeElement.percentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(cloSubTreeElement.percentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(cloSubTreeElement.percentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(cloSubTreeElement.percentage),
                        );
                        cloSubTreeElement.level =
                            percentageLevel && percentageLevel.level ? percentageLevel.level : '';

                        const cloPercentage =
                            cloSubTreeElement.percentage === null
                                ? cloSubTreeElement.percentage
                                : (cloSubTreeElement.percentage * subTreeElement.weightage) / 100;
                        const cloIndex = treeCLO.findIndex(
                            (closElement) =>
                                closElement._id.toString() === cloSubTreeElement._id.toString(),
                        );
                        if (cloIndex == -1)
                            treeCLO.push({
                                _id: cloSubTreeElement._id.toString(),
                                percentage: cloPercentage,
                                count: cloPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (cloPercentage !== null) {
                            treeCLO[cloIndex].percentage += cloPercentage;
                            treeCLO[cloIndex].count++;
                        }
                    }
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        weightage: subTreeElement.weightage,
                        node: nodeList,
                        clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                } else {
                    const subTreeCLO = [];
                    const nodeAttainments = clone(
                        libraryList.filter(
                            (libraryElement) =>
                                libraryElement.assessmentTypeId &&
                                libraryElement.assessmentTypeId.toString() ===
                                    subTreeElement.typeId.toString() &&
                                (treeElement.typeId.toString() ===
                                    libraryElement.typeId.toString() ||
                                    treeElement.typeId.toString() ===
                                        libraryElement.subTypeId.toString()),
                        ),
                    );
                    const subtreeSortedValues =
                        subTreeLevel && subTreeLevel.levelValues
                            ? subTreeLevel.levelValues.sort((a, b) => {
                                  let comparison = 0;
                                  if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                      comparison = -1;
                                  } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                      comparison = 1;
                                  }
                                  return comparison;
                              })
                            : [];
                    for (nodeAttainmentElement of nodeAttainments) {
                        for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                            const cloIndex = subTreeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    nodeAttainmentCLOElement._id.toString(),
                            );
                            if (cloIndex == -1) {
                                subTreeCLO.push({
                                    _id: nodeAttainmentCLOElement._id.toString(),
                                    percentage: nodeAttainmentCLOElement.percentage,
                                    count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            } else if (nodeAttainmentCLOElement.percentage !== null) {
                                subTreeCLO[cloIndex].percentage =
                                    subTreeCLO[cloIndex].percentage === null
                                        ? nodeAttainmentCLOElement.percentage
                                        : subTreeCLO[cloIndex].percentage +
                                          nodeAttainmentCLOElement.percentage;
                                subTreeCLO[cloIndex].count++;
                            }
                            const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(nodeAttainmentCLOElement.percentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(nodeAttainmentCLOElement.percentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(nodeAttainmentCLOElement.percentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(nodeAttainmentCLOElement.percentage),
                            );
                            nodeAttainmentCLOElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';
                        }
                    }

                    for (cloSubTreeElement of subTreeCLO) {
                        if (cloSubTreeElement.percentage !== null)
                            cloSubTreeElement.percentage /= cloSubTreeElement.count;
                        // Tree
                        const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(cloSubTreeElement.percentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(cloSubTreeElement.percentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(cloSubTreeElement.percentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(cloSubTreeElement.percentage),
                        );
                        cloSubTreeElement.level =
                            percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                        const cloPercentage =
                            cloSubTreeElement.percentage === null
                                ? cloSubTreeElement.percentage
                                : (cloSubTreeElement.percentage * subTreeElement.weightage) / 100;

                        const cloIndex = treeCLO.findIndex(
                            (closElement) =>
                                closElement._id.toString() === cloSubTreeElement._id.toString(),
                        );
                        if (cloIndex == -1)
                            treeCLO.push({
                                _id: cloSubTreeElement._id.toString(),
                                percentage: cloPercentage,
                                count: cloPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (cloPercentage !== null) {
                            treeCLO[cloIndex].percentage += cloPercentage;
                            treeCLO[cloIndex].count++;
                        }
                    }
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        nodeId: subTreeElement.nodeId,
                        nodeName: subTreeElement.nodeName,
                        weightage: subTreeElement.weightage,
                        attainments: nodeAttainments,
                        clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                }
            }
            const treeSortedValues =
                levelTree && levelTree.levelValues
                    ? levelTree.levelValues.sort((a, b) => {
                          let comparison = 0;
                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                              comparison = -1;
                          } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                              comparison = 1;
                          }
                          return comparison;
                      })
                    : [];
            for (cloTreeElement of treeCLO) {
                // OverAll CLO
                const percentageLevel = treeSortedValues.find((levelValueElement) =>
                    levelValuesAs === RANGE
                        ? parseFloat(levelValueElement.min) <=
                              parseInt(cloTreeElement.percentage) &&
                          parseFloat(levelValueElement.max) >= parseInt(cloTreeElement.percentage)
                        : levelValueElement.condition === EQUAL
                        ? parseInt(levelValueElement.percentage) <
                          parseInt(cloTreeElement.percentage)
                        : parseInt(levelValueElement.percentage) <=
                          parseInt(cloTreeElement.percentage),
                );
                cloTreeElement.level =
                    percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                const cloPercentage =
                    cloTreeElement.percentage === null
                        ? cloTreeElement.percentage
                        : (cloTreeElement.percentage * treeElement.weightage) / 100;

                const cloIndex = overAllCLO.findIndex(
                    (closElement) => closElement._id.toString() === cloTreeElement._id.toString(),
                );
                if (cloIndex === -1) {
                    overAllCLO.push({
                        _id: cloTreeElement._id.toString(),
                        percentage: cloPercentage,
                        count: 1,
                        level: '',
                    });
                } else if (cloPercentage !== null) {
                    overAllCLO[cloIndex].percentage += cloPercentage;
                    overAllCLO[cloIndex].count++;
                }
            }
            attainmentNodeList.push({
                _id: treeElement._id,
                typeName: treeElement.typeName,
                typeId: treeElement.typeId,
                weightage: treeElement.weightage,
                subTree: subTreeList,
                clo: treeCLO.length ? treeCLO : courseCLOList,
            });
        }
        if (overAllCLO.length) {
            const overAllAttainmentIndex = attainmentNodeList.findIndex(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            const attElement = attainmentOutCome.levels.find(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            if (overAllAttainmentIndex !== -1) {
                const overAllSortedValues =
                    attElement && attElement.levelValues
                        ? attElement.levelValues.sort((a, b) => {
                              let comparison = 0;
                              if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                  comparison = -1;
                              } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                  comparison = 1;
                              }
                              return comparison;
                          })
                        : [];
                for (overAllCLOElement of overAllCLO) {
                    const percentageLevel = overAllSortedValues.find((levelValueElement) =>
                        levelValuesAs === RANGE
                            ? parseFloat(levelValueElement.min) <=
                                  parseInt(overAllCLOElement.percentage) &&
                              parseFloat(levelValueElement.max) >=
                                  parseInt(overAllCLOElement.percentage)
                            : levelValueElement.condition === EQUAL
                            ? parseInt(levelValueElement.percentage) <
                              parseInt(overAllCLOElement.percentage)
                            : parseInt(levelValueElement.percentage) <=
                              parseInt(overAllCLOElement.percentage),
                    );
                    overAllCLOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                }
                attainmentNodeList[overAllAttainmentIndex].clo = overAllCLO;
            }
        }
        if (assessmentIds)
            await attainmentManagementSchema.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    $set: { assessmentSelections },
                },
            );
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                courseCLO,
                attainmentNodeList,
                attainmentLevel: attainmentOutCome.levels,
                attainmentValuesAs: attainmentOutCome.valuesAs,
                attainmentTargetBenchMark: attainmentOutCome.targetBenchMark,
                levelColors: attainmentOutCome.levelColors,
                manageTargetBenchMark: attainmentOutCome.manageTargetBenchMark
                    ? attainmentOutCome.manageTargetBenchMark
                    : {},
                assessmentIds:
                    courseAssessmentIdIndex !== -1
                        ? assessmentSelections[courseAssessmentIdIndex].assessmentIds
                        : [],
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Course Attainment Report With Assessment Ids
const getCourseStudentAttainmentReport = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { courseId, attainmentId, level, programId, institutionCalendarId, term, outCome } =
            body;
        let { assessmentIds } = body;
        const reportStruct = {
            percentage: null,
            level: '',
        };
        const courseCLO = (await getCourseCLO({ _institution_id, courseId })).map((cloElement) => {
            return {
                _id: cloElement._id,
                no: cloElement.no,
                name: cloElement.name,
                ...reportStruct,
            };
        });
        let attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                    type: 'course',
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    levelNo: level,
                    term,
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    assessmentSelections: 1,
                },
            )
            .lean();
        const programAttainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    assessmentSelections: 1,
                },
            )
            .lean();
        if (!attainmentList) attainmentList = programAttainmentList;
        if (!attainmentList || !programAttainmentList)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const assessmentSelections =
            programAttainmentList.assessmentSelections &&
            programAttainmentList.assessmentSelections.length
                ? programAttainmentList.assessmentSelections
                : [];
        const courseAssessmentIdIndex = assessmentSelections.findIndex(
            (selectionElement) =>
                selectionElement.institutionCalendarId.toString() ===
                    institutionCalendarId.toString() &&
                selectionElement.courseId.toString() === courseId.toString(),
        );
        if (assessmentIds)
            if (courseAssessmentIdIndex !== -1)
                assessmentSelections[courseAssessmentIdIndex].assessmentIds = assessmentIds;
            else
                assessmentSelections.push({
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    courseId: convertToMongoObjectId(courseId),
                    assessmentIds: assessmentIds.map((assessmentElement) =>
                        convertToMongoObjectId(assessmentElement),
                    ),
                });
        if (courseAssessmentIdIndex !== -1)
            assessmentIds = assessmentSelections[courseAssessmentIdIndex].assessmentIds;
        const attainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const levelValuesAs = attainmentOutCome.valuesAs;
        if (!attainmentOutCome || !attainmentPlanOutCome || !levelValuesAs)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const typeIds = [];
        for (attainmentElement of attainmentPlanOutCome.tree) {
            if (attainmentElement.subTree)
                for (subTreeElement of attainmentElement.subTree) {
                    if (subTreeElement.node)
                        for (nodeElement of subTreeElement.node) {
                            typeIds.push(nodeElement.typeId);
                        }
                    typeIds.push(subTreeElement.typeId);
                }
            typeIds.push(attainmentElement.typeId);
        }
        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                term,
                level,
                assessmentTypeId: { $in: typeIds },
            },
            {
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                updatedAt: 1,
            },
        );
        libraryData = clone(libraryData);
        let studentList = [];
        let studentAssessments = [];
        for (libraryElement of libraryData) {
            for (studentDetailsElement of libraryElement.studentDetails) {
                if (studentDetailsElement.attendance) {
                    const studentIndex = studentAssessments.findIndex(
                        (studentElement) =>
                            studentElement.studentId === studentDetailsElement.studentId,
                    );
                    const studentCOList = [];
                    for (studentMarkElement of studentDetailsElement.studentMarks) {
                        if (studentMarkElement.mark !== null) {
                            const studentQuestions = libraryElement.questionMarks.find(
                                (questionMarkElement) =>
                                    questionMarkElement.questionName ===
                                    studentMarkElement.questionName,
                            );
                            if (studentQuestions) {
                                for (outComeIdElement of studentQuestions.outComeIds) {
                                    const studentCOIndex = studentCOList.findIndex(
                                        (studentCOListElement) =>
                                            studentCOListElement._id.toString() ===
                                            outComeIdElement.toString(),
                                    );
                                    if (studentCOIndex === -1) {
                                        studentCOList.push({
                                            _id: outComeIdElement.toString(),
                                            attendedCount: 1,
                                            passedCount:
                                                studentMarkElement.mark >=
                                                studentQuestions.attainmentBenchMark
                                                    ? 1
                                                    : 0,
                                            ...reportStruct,
                                        });
                                    } else {
                                        studentCOList[studentCOIndex].attendedCount++;
                                        studentCOList[studentCOIndex].passedCount +=
                                            studentMarkElement.mark >=
                                            studentQuestions.attainmentBenchMark
                                                ? 1
                                                : 0;
                                    }
                                }
                            }
                        }
                    }
                    for (studentCOElement of studentCOList)
                        if (studentCOElement.attendedCount)
                            studentCOElement.percentage =
                                (studentCOElement.passedCount / studentCOElement.attendedCount) *
                                100;

                    if (studentIndex === -1) {
                        studentList.push({
                            studentId: studentDetailsElement.studentId,
                            name: studentDetailsElement.name,
                            gender: studentDetailsElement.gender,
                        });
                        studentAssessments.push({
                            studentId: studentDetailsElement.studentId,
                            assessments: [
                                {
                                    assessmentId: libraryElement._assessment_id,
                                    assessmentTypeId: libraryElement.assessmentTypeId,
                                    typeId: libraryElement.typeId,
                                    subTypeId: libraryElement.subTypeId,
                                    studentCOList,
                                },
                            ],
                        });
                    } else {
                        studentAssessments[studentIndex].assessments.push({
                            assessmentId: libraryElement._assessment_id,
                            assessmentTypeId: libraryElement.assessmentTypeId,
                            typeId: libraryElement.typeId,
                            subTypeId: libraryElement.subTypeId,
                            studentCOList,
                        });
                    }
                }
            }
        }
        studentList = studentList.sort();
        studentAssessments = studentAssessments.sort((a, b) => {
            let comparison = 0;
            if (a.studentId > b.studentId) {
                comparison = -1;
            } else if (a.studentId < b.studentId) {
                comparison = 1;
            }
            return comparison;
        });
        // return { data: { studentList, studentAssessments, libraryData } };
        // Attainment Outcome Mode
        const attainmentNodeList = [
            {
                _id: convertToMongoObjectId(),
                typeName: 'Overall Attainment',
                subTree: [],
                // clo: [],
            },
        ];
        const overAllStudentCLO = [];
        for (treeElement of attainmentPlanOutCome.tree) {
            const subTreeList = [];
            const treeCLO = [];
            const treeStudentList = [];
            const levelTree = attainmentOutCome.levels.find((levelElement) =>
                levelElement.typeId
                    ? levelElement.typeId.toString() === treeElement.typeId.toString()
                    : levelElement.typeName === treeElement.typeName,
            );
            for (subTreeElement of treeElement.subTree) {
                const nodeList = [];
                const subTreeLevel =
                    levelTree && levelTree.subTree
                        ? levelTree.subTree.find(
                              (levelSubTreeElement) =>
                                  levelSubTreeElement.typeId.toString() ===
                                  subTreeElement.typeId.toString(),
                          )
                        : undefined;

                if (!subTreeElement.nodeName) {
                    const subTreeCLO = [];
                    const subTreeStudentList = [];
                    for (nodeElement of subTreeElement.node) {
                        const nodeClo = [];
                        const nodeLevel =
                            subTreeLevel && subTreeLevel.node
                                ? subTreeLevel.node.find(
                                      (nodeLevelElement) =>
                                          nodeLevelElement.typeId.toString() ===
                                          nodeElement.typeId.toString(),
                                  )
                                : undefined;
                        const nodeSortedValues =
                            nodeLevel && nodeLevel.levelValues
                                ? nodeLevel.levelValues.sort((a, b) => {
                                      let comparison = 0;
                                      if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                          comparison = -1;
                                      } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                          comparison = 1;
                                      }
                                      return comparison;
                                  })
                                : [];
                        // Student Flow
                        const nodeStudentLists = [];
                        for (studentAssessmentElement of studentAssessments) {
                            const studentCO = [];
                            const studentAssessmentLists =
                                studentAssessmentElement.assessments.filter(
                                    (assessmentElement) =>
                                        assessmentElement.assessmentTypeId &&
                                        assessmentElement.assessmentTypeId.toString() ===
                                            nodeElement.typeId.toString() &&
                                        treeElement.typeId.toString() ===
                                            assessmentElement.typeId.toString() &&
                                        subTreeElement.typeId.toString() ===
                                            assessmentElement.subTypeId.toString(),
                                );
                            for (studentAssessmentListElement of studentAssessmentLists) {
                                for (studentCOElement of studentAssessmentListElement.studentCOList) {
                                    const studentCOIndex = studentCO.findIndex(
                                        (studentCOListElement) =>
                                            studentCOListElement._id.toString() ===
                                            studentCOElement._id.toString(),
                                    );
                                    if (studentCOIndex === -1)
                                        studentCO.push({
                                            _id: studentCOElement._id.toString(),
                                            attendedCount: studentCOElement.attendedCount,
                                            passedCount: studentCOElement.passedCount,
                                        });
                                    else {
                                        studentCO[studentCOIndex].attendedCount +=
                                            studentCOElement.attendedCount;
                                        studentCO[studentCOIndex].passedCount +=
                                            studentCOElement.passedCount;
                                    }
                                }
                            }
                            const nodeStudentValue = {
                                percentage: 0,
                                count: 0,
                            };
                            for (studentCODataElement of studentCO) {
                                const studentCOPercentage =
                                    (studentCODataElement.passedCount /
                                        studentCODataElement.attendedCount) *
                                    100;
                                studentCODataElement.percentage = studentCOPercentage;
                                nodeStudentValue.percentage += studentCOPercentage;
                                nodeStudentValue.count++;
                                const percentageLevel = nodeSortedValues.find((levelValueElement) =>
                                    levelValuesAs === RANGE
                                        ? parseFloat(levelValueElement.min) <=
                                              parseInt(studentCOPercentage) &&
                                          parseFloat(levelValueElement.max) >=
                                              parseInt(studentCOPercentage)
                                        : levelValueElement.condition === EQUAL
                                        ? parseInt(levelValueElement.percentage) <
                                          parseInt(studentCOPercentage)
                                        : parseInt(levelValueElement.percentage) <=
                                          parseInt(studentCOPercentage),
                                );
                                studentCODataElement.level =
                                    percentageLevel && percentageLevel.level
                                        ? percentageLevel.level
                                        : '';
                            }
                            const nodeStudentPercentage =
                                nodeStudentValue.percentage / nodeStudentValue.count;
                            const nodeStudentLevel = nodeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(nodeStudentPercentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(nodeStudentPercentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(nodeStudentPercentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(nodeStudentPercentage),
                            );
                            nodeStudentLists.push({
                                studentId: studentAssessmentElement.studentId,
                                percentage: nodeStudentPercentage,
                                level:
                                    nodeStudentLevel && nodeStudentLevel.level
                                        ? nodeStudentLevel.level
                                        : '',
                                studentCO,
                            });
                        }
                        nodeList.push({
                            _id: nodeElement._id,
                            typeName: nodeElement.typeName,
                            typeId: nodeElement.typeId,
                            nodeId: nodeElement.nodeId,
                            weightage: nodeElement.weightage,
                            nodeName: nodeElement.nodeName,
                            studentList: nodeStudentLists,
                            // clo: nodeClo,
                            // attainments: nodeAttainments,
                        });
                    }
                    for (nodeListElement of nodeList) {
                        // Student CO
                        for (StudentListElement of nodeListElement.studentList) {
                            const subTreeStudentIndex = subTreeStudentList.findIndex(
                                (subTreeStudentElement) =>
                                    subTreeStudentElement.studentId ===
                                    StudentListElement.studentId,
                            );
                            const subTreeStudentCO =
                                subTreeStudentIndex === -1
                                    ? []
                                    : subTreeStudentList[subTreeStudentIndex].studentCO;

                            for (studentCOElement of StudentListElement.studentCO) {
                                const subTreeStudentCOIndex = subTreeStudentCO.findIndex(
                                    (subTreeStudentCOElement) =>
                                        subTreeStudentCOElement._id.toString() ===
                                        studentCOElement._id.toString(),
                                );
                                const subTreePercentage =
                                    studentCOElement.percentage === null
                                        ? studentCOElement.percentage
                                        : (studentCOElement.percentage *
                                              nodeListElement.weightage) /
                                          100;
                                if (subTreeStudentCOIndex === -1)
                                    subTreeStudentCO.push({
                                        _id: studentCOElement._id.toString(),
                                        percentage: subTreePercentage,
                                        level: '',
                                    });
                                else {
                                    subTreeStudentCO[subTreeStudentCOIndex].percentage +=
                                        subTreePercentage;
                                }
                            }
                            if (subTreeStudentIndex === -1)
                                subTreeStudentList.push({
                                    studentId: StudentListElement.studentId,
                                    studentCO: subTreeStudentCO,
                                });
                            else
                                subTreeStudentList[subTreeStudentIndex].studentCO =
                                    subTreeStudentCO;
                        }
                    }

                    // Student subTreeStudentList
                    const subtreeSortedValues =
                        subTreeLevel && subTreeLevel.levelValues
                            ? subTreeLevel.levelValues.sort((a, b) => {
                                  let comparison = 0;
                                  if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                      comparison = -1;
                                  } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                      comparison = 1;
                                  }
                                  return comparison;
                              })
                            : [];
                    for (subTreeStudentElement of subTreeStudentList) {
                        const subTreeStudentValue = {
                            percentage: 0,
                            count: 0,
                        };
                        const treeStudentIndex = treeStudentList.findIndex(
                            (treeStudentListElement) =>
                                treeStudentListElement.studentId ===
                                subTreeStudentElement.studentId,
                        );
                        // Tree
                        const treeStudentCLO =
                            treeStudentIndex === -1
                                ? []
                                : treeStudentList[treeStudentIndex].studentCO;
                        for (subTreeStudentCOElement of subTreeStudentElement.studentCO) {
                            const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(subTreeStudentCOElement.percentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(subTreeStudentCOElement.percentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(subTreeStudentCOElement.percentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(subTreeStudentCOElement.percentage),
                            );
                            subTreeStudentCOElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';

                            const cloPercentage =
                                subTreeStudentCOElement.percentage === null
                                    ? subTreeStudentCOElement.percentage
                                    : (subTreeStudentCOElement.percentage *
                                          subTreeElement.weightage) /
                                      100;

                            subTreeStudentValue.percentage +=
                                subTreeStudentCOElement.percentage !== null
                                    ? subTreeStudentCOElement.percentage
                                    : 0;
                            subTreeStudentValue.count++;
                            const cloIndex = treeStudentCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    subTreeStudentCOElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeStudentCLO.push({
                                    _id: subTreeStudentCOElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeStudentCLO[cloIndex].percentage += cloPercentage;
                            }
                        }
                        const subTreeStudentPercentage =
                            subTreeStudentValue.percentage / subTreeStudentValue.count;
                        const subTreeStudentLevel = subtreeSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(subTreeStudentPercentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(subTreeStudentPercentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(subTreeStudentPercentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(subTreeStudentPercentage),
                        );
                        subTreeStudentElement.percentage = subTreeStudentPercentage;
                        subTreeStudentElement.level =
                            subTreeStudentLevel && subTreeStudentLevel.level
                                ? subTreeStudentLevel.level
                                : '';
                        if (treeStudentIndex === -1)
                            treeStudentList.push({
                                studentId: subTreeStudentElement.studentId,
                                studentCO: treeStudentCLO,
                                percentage: subTreeStudentPercentage,
                                level:
                                    subTreeStudentLevel && subTreeStudentLevel.level
                                        ? subTreeStudentLevel.level
                                        : '',
                            });
                        else treeStudentList[treeStudentIndex].studentCO = treeStudentCLO;
                    }
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        weightage: subTreeElement.weightage,
                        node: nodeList,
                        studentList: subTreeStudentList,
                        // clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                } else {
                    const subTreeCLO = [];
                    const subtreeSortedValues =
                        subTreeLevel && subTreeLevel.levelValues
                            ? subTreeLevel.levelValues.sort((a, b) => {
                                  let comparison = 0;
                                  if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                      comparison = -1;
                                  } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                      comparison = 1;
                                  }
                                  return comparison;
                              })
                            : [];
                    // Student Flow
                    const nodeSortedValues =
                        subtreeSortedValues && subtreeSortedValues.levelValues
                            ? subtreeSortedValues.levelValues.sort((a, b) => {
                                  let comparison = 0;
                                  if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                      comparison = -1;
                                  } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                      comparison = 1;
                                  }
                                  return comparison;
                              })
                            : [];
                    const nodeStudentLists = [];
                    for (studentAssessmentElement of studentAssessments) {
                        const studentCO = [];
                        const studentAssessmentLists = studentAssessmentElement.assessments.filter(
                            (assessmentElement) =>
                                assessmentElement.assessmentTypeId &&
                                assessmentElement.assessmentTypeId.toString() ===
                                    subTreeElement.typeId.toString() &&
                                (treeElement.typeId.toString() ===
                                    assessmentElement.typeId.toString() ||
                                    treeElement.typeId.toString() ===
                                        assessmentElement.subTypeId.toString()),
                        );
                        for (studentAssessmentListElement of studentAssessmentLists) {
                            for (studentCOElement of studentAssessmentListElement.studentCOList) {
                                const studentCOIndex = studentCO.findIndex(
                                    (studentCOListElement) =>
                                        studentCOListElement._id.toString() ===
                                        studentCOElement._id.toString(),
                                );
                                if (studentCOIndex === -1)
                                    studentCO.push({
                                        _id: studentCOElement._id.toString(),
                                        attendedCount: studentCOElement.attendedCount,
                                        passedCount: studentCOElement.passedCount,
                                    });
                                else {
                                    studentCO[studentCOIndex].attendedCount +=
                                        studentCOElement.attendedCount;
                                    studentCO[studentCOIndex].passedCount +=
                                        studentCOElement.passedCount;
                                }
                            }
                        }
                        const nodeStudentValue = {
                            percentage: 0,
                            count: 0,
                        };
                        for (studentCODataElement of studentCO) {
                            const studentCOPercentage =
                                (studentCODataElement.passedCount /
                                    studentCODataElement.attendedCount) *
                                100;
                            studentCODataElement.percentage = studentCOPercentage;
                            nodeStudentValue.percentage += studentCOPercentage;
                            nodeStudentValue.count++;
                            const percentageLevel = nodeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(studentCOPercentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(studentCOPercentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(studentCOPercentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(studentCOPercentage),
                            );
                            studentCODataElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';
                        }
                        const nodeStudentPercentage =
                            nodeStudentValue.percentage / nodeStudentValue.count;
                        const nodeStudentLevel = nodeSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(nodeStudentPercentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(nodeStudentPercentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(nodeStudentPercentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(nodeStudentPercentage),
                        );
                        nodeStudentLists.push({
                            studentId: studentAssessmentElement.studentId,
                            studentCO,
                            level:
                                nodeStudentLevel && nodeStudentLevel.level
                                    ? nodeStudentLevel.level
                                    : '',
                            percentage: nodeStudentPercentage,
                        });
                    }
                    for (subTreeStudentElement of nodeStudentLists) {
                        const subTreeStudentValue = {
                            percentage: 0,
                            count: 0,
                        };
                        const treeStudentIndex = treeStudentList.findIndex(
                            (treeStudentListElement) =>
                                treeStudentListElement.studentId ===
                                subTreeStudentElement.studentId,
                        );
                        // Tree
                        const treeStudentCLO =
                            treeStudentIndex === -1
                                ? []
                                : treeStudentList[treeStudentIndex].studentCO;
                        for (subTreeStudentCOElement of subTreeStudentElement.studentCO) {
                            const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(subTreeStudentCOElement.percentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(subTreeStudentCOElement.percentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(subTreeStudentCOElement.percentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(subTreeStudentCOElement.percentage),
                            );
                            subTreeStudentCOElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';

                            const cloPercentage =
                                subTreeStudentCOElement.percentage === null
                                    ? subTreeStudentCOElement.percentage
                                    : (subTreeStudentCOElement.percentage *
                                          subTreeElement.weightage) /
                                      100;
                            subTreeStudentValue.percentage +=
                                subTreeStudentCOElement.percentage !== null
                                    ? subTreeStudentCOElement.percentage
                                    : 0;
                            subTreeStudentValue.count++;
                            const cloIndex = treeStudentCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() ===
                                    subTreeStudentCOElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeStudentCLO.push({
                                    _id: subTreeStudentCOElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeStudentCLO[cloIndex].percentage += cloPercentage;
                            }
                        }
                        const subTreeStudentPercentage =
                            subTreeStudentValue.percentage / subTreeStudentValue.count;
                        const subTreeStudentLevel = subtreeSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(subTreeStudentPercentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(subTreeStudentPercentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(subTreeStudentPercentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(subTreeStudentPercentage),
                        );
                        subTreeStudentElement.percentage = subTreeStudentPercentage;
                        subTreeStudentElement.level =
                            subTreeStudentLevel && subTreeStudentLevel.level
                                ? subTreeStudentLevel.level
                                : '';
                        if (treeStudentIndex === -1)
                            treeStudentList.push({
                                studentId: subTreeStudentElement.studentId,
                                studentCO: treeStudentCLO,
                            });
                        else treeStudentList[treeStudentIndex].studentCO = treeStudentCLO;
                    }

                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        nodeId: subTreeElement.nodeId,
                        nodeName: subTreeElement.nodeName,
                        studentList: nodeStudentLists,
                        // attainments: nodeAttainments,
                        // clo: subTreeCLO.length ? subTreeCLO : courseCLOList,
                    });
                }
            }
            const treeSortedValues =
                levelTree && levelTree.levelValues
                    ? levelTree.levelValues.sort((a, b) => {
                          let comparison = 0;
                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                              comparison = -1;
                          } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                              comparison = 1;
                          }
                          return comparison;
                      })
                    : [];
            // Student Tree
            // OverAll CLO
            for (cloTreeElement of treeStudentList) {
                const treeStudentValue = {
                    percentage: 0,
                    count: 0,
                };
                // overAllStudentCLO
                const overAllStudentCLOIndex = overAllStudentCLO.findIndex(
                    (overAllStudentCLOElement) =>
                        overAllStudentCLOElement.studentId.toString() ===
                        cloTreeElement.studentId.toString(),
                );
                const overAllStudentTypeCO =
                    overAllStudentCLOIndex === -1
                        ? []
                        : overAllStudentCLO[overAllStudentCLOIndex].studentCO;
                for (treeStudentCOElement of cloTreeElement.studentCO) {
                    const percentageLevel = treeSortedValues.find((levelValueElement) =>
                        levelValuesAs === RANGE
                            ? parseFloat(levelValueElement.min) <=
                                  parseInt(treeStudentCOElement.percentage) &&
                              parseFloat(levelValueElement.max) >=
                                  parseInt(treeStudentCOElement.percentage)
                            : levelValueElement.condition === EQUAL
                            ? parseInt(levelValueElement.percentage) <
                              parseInt(treeStudentCOElement.percentage)
                            : parseInt(levelValueElement.percentage) <=
                              parseInt(treeStudentCOElement.percentage),
                    );
                    treeStudentCOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                    const cloPercentage =
                        treeStudentCOElement.percentage === null
                            ? treeStudentCOElement.percentage
                            : (treeStudentCOElement.percentage * treeElement.weightage) / 100;
                    treeStudentValue.percentage +=
                        treeStudentCOElement.percentage !== null
                            ? treeStudentCOElement.percentage
                            : 0;
                    treeStudentValue.count++;
                    const cloIndex = overAllStudentTypeCO.findIndex(
                        (closElement) =>
                            closElement._id.toString() === treeStudentCOElement._id.toString(),
                    );
                    if (cloIndex === -1) {
                        overAllStudentTypeCO.push({
                            _id: treeStudentCOElement._id.toString(),
                            percentage: cloPercentage,
                            count: 1,
                            level: '',
                        });
                    } else if (cloPercentage !== null) {
                        overAllStudentTypeCO[cloIndex].percentage += cloPercentage;
                        overAllStudentTypeCO[cloIndex].count++;
                    }
                }
                const treeStudentPercentage = treeStudentValue.percentage / treeStudentValue.count;
                const treeStudentLevel = treeSortedValues.find((levelValueElement) =>
                    levelValuesAs === RANGE
                        ? parseFloat(levelValueElement.min) <= parseInt(treeStudentPercentage) &&
                          parseFloat(levelValueElement.max) >= parseInt(treeStudentPercentage)
                        : levelValueElement.condition === EQUAL
                        ? parseInt(levelValueElement.percentage) < parseInt(treeStudentPercentage)
                        : parseInt(levelValueElement.percentage) <= parseInt(treeStudentPercentage),
                );
                cloTreeElement.percentage = treeStudentPercentage;
                cloTreeElement.level =
                    treeStudentLevel && treeStudentLevel.level ? treeStudentLevel.level : '';
                if (overAllStudentCLOIndex === -1)
                    overAllStudentCLO.push({
                        studentId: cloTreeElement.studentId,
                        studentCO: overAllStudentTypeCO,
                    });
                else overAllStudentCLO[overAllStudentCLOIndex].studentCO = overAllStudentTypeCO;
            }

            attainmentNodeList.push({
                _id: treeElement._id,
                typeName: treeElement.typeName,
                typeId: treeElement.typeId,
                weightage: treeElement.weightage,
                subTree: subTreeList,
                studentList: treeStudentList,
                // clo: treeCLO.length ? treeCLO : courseCLOList,
            });
        }
        // Student Flow
        if (overAllStudentCLO) {
            const overAllAttainmentIndex = attainmentNodeList.findIndex(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            const attElement = attainmentOutCome.levels.find(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            if (overAllAttainmentIndex !== -1) {
                const overAllSortedValues =
                    attElement && attElement.levelValues
                        ? attElement.levelValues.sort((a, b) => {
                              let comparison = 0;
                              if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                  comparison = -1;
                              } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                  comparison = 1;
                              }
                              return comparison;
                          })
                        : [];
                for (overAllCLOElement of overAllStudentCLO) {
                    const studentCOPercentage = {
                        percentage: 0,
                        // level: '',
                        count: 0,
                    };
                    for (overAllStudentCOElement of overAllCLOElement.studentCO) {
                        studentCOPercentage.percentage += overAllStudentCOElement.percentage;
                        studentCOPercentage.count++;
                        const percentageLevel = overAllSortedValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(overAllStudentCOElement.percentage) &&
                                  parseFloat(levelValueElement.max) >=
                                      parseInt(overAllStudentCOElement.percentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(overAllStudentCOElement.percentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(overAllStudentCOElement.percentage),
                        );
                        overAllStudentCOElement.level =
                            percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                    }
                    const studentPercentage =
                        studentCOPercentage.percentage / studentCOPercentage.count;
                    overAllCLOElement.percentage = studentPercentage;
                    const percentageLevel = overAllSortedValues.find((levelValueElement) =>
                        levelValuesAs === RANGE
                            ? parseFloat(levelValueElement.min) <= parseInt(studentPercentage) &&
                              parseFloat(levelValueElement.max) >= parseInt(studentPercentage)
                            : levelValueElement.condition === EQUAL
                            ? parseInt(levelValueElement.percentage) < parseInt(studentPercentage)
                            : parseInt(levelValueElement.percentage) <= parseInt(studentPercentage),
                    );
                    overAllCLOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                }
                attainmentNodeList[overAllAttainmentIndex].studentList = overAllStudentCLO;
            }
        }
        if (assessmentIds)
            await attainmentManagementSchema.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    $set: { assessmentSelections },
                },
            );
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                studentList,
                courseCLO,
                attainmentNodeList,
                attainmentLevel: attainmentOutCome.levels,
                attainmentValuesAs: attainmentOutCome.valuesAs,
                attainmentTargetBenchMark: attainmentOutCome.targetBenchMark,
                levelColors: attainmentOutCome.levelColors,
                manageTargetBenchMark: attainmentOutCome.manageTargetBenchMark
                    ? attainmentOutCome.manageTargetBenchMark
                    : {},
                assessmentIds:
                    courseAssessmentIdIndex !== -1
                        ? assessmentSelections[courseAssessmentIdIndex].assessmentIds
                        : [],
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCourseAttainmentReport = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { attainmentId, programId, institutionCalendarId, term, outCome } = query;
        const reportStruct = {
            percentage: 0,
            level: '',
        };
        const institutionCalendarData = await getInstitutionCalendars({
            _institution_id,
            institutionCalendarId,
        });
        const attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    _curriculum_id: 1,
                    curriculumName: 1,
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    programCourseList: 1,
                    regulationYear: 1,
                    regulationName: 1,
                },
            )
            .lean();
        if (!attainmentList) return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const attainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentPOCourse = attainmentList.programCourseList
            ? attainmentList.programCourseList
            : [];
        const levelValuesAs = attainmentOutCome.valuesAs;
        if (
            !attainmentOutCome ||
            !attainmentPlanOutCome ||
            !attainmentList._curriculum_id ||
            !levelValuesAs
        )
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const coAttainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === 'clo',
        );
        const coAttainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === 'clo',
        );
        if (!coAttainmentOutCome || !coAttainmentPlanOutCome)
            return { statusCode: 200, message: 'CLO_ATTAINMENT_NOT_FOUND' };
        const overAllLevelValues = attainmentOutCome.levels.find(
            (levelElement) => levelElement.typeName === 'Overall Attainment',
        )
            ? clone(
                  attainmentOutCome.levels.find(
                      (levelElement) => levelElement.typeName === 'Overall Attainment',
                  ).levelValues,
              )
            : [];
        if (overAllLevelValues.length === 0)
            return { statusCode: 200, message: 'ATTAINMENT_PLO_NOT_FOUND' };

        // const coAttainmentCourseAssessment = coAttainmentOutCome.filter(
        //     (selectionElement) =>
        //         selectionElement.institutionCalendarId.toString() ===
        //         institutionCalendarId.toString(),
        // );
        // return { data: coAttainmentCourseAssessment };
        // if (assessmentIds)
        //     if (courseAssessmentIdIndex !== -1)
        //         assessmentSelections[courseAssessmentIdIndex].assessmentIds = assessmentIds;
        //     else
        //         assessmentSelections.push({
        //             institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        //             courseId: convertToMongoObjectId(courseId),
        //             assessmentIds: assessmentIds.map((assessmentElement) =>
        //                 convertToMongoObjectId(assessmentElement),
        //             ),
        //         });
        // if (courseAssessmentIdIndex !== -1)
        //     assessmentIds = assessmentSelections[courseAssessmentIdIndex].assessmentIds;

        // for (let i = 1; i <= overAllLevelValues.length; i++) overAllLevelValues[i].levelValue = i;
        let i = 1;
        const baseLevelValue = overAllLevelValues.length / 3;
        for (overAllLevelValueElement of overAllLevelValues) {
            overAllLevelValueElement.levelValue = i;
            i++;
        }
        const overAllLevelValuesWithOutSort = clone(overAllLevelValues);
        // return { data: overAllLevelValues };
        const curriculumYearLevelData = await getProgramPLOWithCLO({
            _institution_id,
            programId,
            institutionCalendarId,
            curriculumId: attainmentList._curriculum_id,
        });
        if (!curriculumYearLevelData)
            return { statusCode: 410, message: 'Program Calendar/Curriculam Data Not Found' };
        const curriculumPLOs = clone(
            curriculumYearLevelData.curriculumPLO.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    no: cloElement.no,
                    name: cloElement.name,
                    ...reportStruct,
                };
            }),
        );
        const curriculumPLOStruct = curriculumPLOs.map((ploElement) => {
            return {
                _id: ploElement._id,
                value: 0,
                count: 0,
                // ...reportStruct,
            };
        });
        // const curriculumCourses = curriculumYearLevelData.course.map((courseElement) => {
        //     return {
        //         ...courseElement,
        //         ...reportStruct,
        //         plo: curriculumPLOs.map((ploElement) => {
        //             return {
        //                 _id: ploElement._id,
        //                 ...reportStruct,
        //             };
        //         }),
        //     };
        // });
        let yearDetails = curriculumYearLevelData.course
            .map((calendarYearElement) => calendarYearElement.year.toString())
            .reverse();
        yearDetails = [...new Set(yearDetails)];
        let attainmentLibraryQuery = [];
        let calendarIndex = 0;
        for (yearElement of yearDetails) {
            const courseList = curriculumYearLevelData.course
                .filter(
                    (courseElement) =>
                        courseElement.year === yearElement &&
                        courseElement.term === term &&
                        (attainmentPOCourse.find(
                            (poCourseElement) =>
                                poCourseElement.levelNo === courseElement.levelNo &&
                                poCourseElement.term === courseElement.term &&
                                poCourseElement.courseId.toString() ===
                                    courseElement._course_id.toString(),
                        )
                            ? attainmentPOCourse.find(
                                  (poCourseElement) =>
                                      poCourseElement.levelNo === courseElement.levelNo &&
                                      poCourseElement.term === courseElement.term &&
                                      poCourseElement.courseId.toString() ===
                                          courseElement._course_id.toString(),
                              ).status
                            : true),
                )
                .map((courseElement) => {
                    return {
                        level: courseElement.levelNo,
                        term: courseElement.term,
                        _course_id: convertToMongoObjectId(courseElement._course_id),
                        _institution_calendar_id: convertToMongoObjectId(
                            institutionCalendarData[calendarIndex]._id,
                        ),
                    };
                });
            if (courseList && courseList.length) {
                attainmentLibraryQuery = [...attainmentLibraryQuery, ...courseList];
            }
            calendarIndex =
                calendarIndex + 1 > institutionCalendarData.length - 1 ? 0 : calendarIndex + 1;
        }
        attainmentLibraryQuery = attainmentLibraryQuery.sort((a, b) => {
            let comparison = 0;
            if (a.level > b.level) {
                comparison = -1;
            } else if (a.level < b.level) {
                comparison = 1;
            }
            return comparison;
        });
        const courseIds = attainmentLibraryQuery.map((courseElement) =>
            convertToMongoObjectId(courseElement._course_id),
        );
        if (!attainmentLibraryQuery.length)
            return { statusCode: 410, message: 'No Course Selected' };
        // Course Attainment Settings
        const courseAttainmentSettings = await attainmentManagementSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                    type: 'course',
                    _program_id: convertToMongoObjectId(programId),
                    $or: attainmentLibraryQuery.map((courseElement) => {
                        return {
                            level: courseElement.levelNo,
                            term: courseElement.term,
                            _course_id: convertToMongoObjectId(courseElement._course_id),
                        };
                    }),
                },
                { evaluationPlan: 1, _course_id: 1, levelNo: 1, term: 1 },
            )
            .lean();
        const courseCLO = await getCoursesCLOs({ _institution_id, courseIds, programId });
        // Course LibraryData
        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                // _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                $or: attainmentLibraryQuery,
                // _course_id: { $in: courseIds },
                // term,
                // level,
            },
            {
                _course_id: 1,
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                updatedAt: 1,
            },
        );
        libraryData = clone(libraryData);
        const libraryList = [];
        for (libraryElement of libraryData) {
            for (questionElement of libraryElement.questionMarks) {
                questionElement.attendedCount = 0;
                questionElement.passedCount = 0;
                for (studentElement of libraryElement.studentDetails) {
                    const studentQuestion = studentElement.studentMarks.find(
                        (studentQuestionMarkElement) =>
                            studentQuestionMarkElement.questionName ===
                            questionElement.questionName,
                    );
                    if (studentQuestion && studentQuestion.mark !== null) {
                        questionElement.attendedCount++;
                        if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                            questionElement.passedCount++;
                    }
                }
                if (questionElement.attendedCount > 0)
                    questionElement.average = (
                        questionElement.passedCount / questionElement.attendedCount
                    ).toFixed(4);
            }
            const courseWiseClo = courseCLO.find(
                (courseElement) =>
                    courseElement._course_id.toString() === libraryElement._course_id.toString(),
            );
            const assessmentClo = courseWiseClo
                ? clone(
                      courseWiseClo.clo.map((cloElement) => {
                          return {
                              _id: cloElement,
                              ...reportStruct,
                          };
                      }),
                  )
                : [];
            for (courseCloElement of assessmentClo) {
                const cloQuestions = libraryElement.questionMarks.filter(
                    (questionElement) =>
                        questionElement.attendedCount > 0 &&
                        questionElement.outComeIds.find(
                            (outComeIdElement) =>
                                outComeIdElement.toString() === courseCloElement._id.toString(),
                        ),
                );
                courseCloElement.percentage =
                    cloQuestions.reduce((n, cloElement) => n + parseFloat(cloElement.average), 0) /
                    cloQuestions.length;
                if (courseCloElement.percentage)
                    courseCloElement.percentage = courseCloElement.percentage.toFixed(4) * 100;
            }
            libraryElement.clo = assessmentClo.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    percentage: cloElement.percentage,
                    level: '',
                };
            });
            libraryList.push({
                _id: libraryElement._id,
                _course_id: libraryElement._course_id,
                _assessment_id: libraryElement._assessment_id,
                assessmentName: libraryElement.assessmentName,
                assessmentMark: libraryElement.assessmentMark,
                typeName: libraryElement.typeName,
                typeId: libraryElement.typeId,
                subTypeName: libraryElement.subTypeName,
                subTypeId: libraryElement.subTypeId,
                assessmentTypeName: libraryElement.assessmentTypeName,
                assessmentTypeId: libraryElement.assessmentTypeId,
                questionOutcome: libraryElement.questionOutcome,
                questionMarks: libraryElement.questionMarks,
                noQuestions: libraryElement.noQuestions,
                benchMark: libraryElement.benchMark,
                clo: libraryElement.clo,
                updatedAt: libraryElement.updatedAt,
            });
        }
        // return { data: { libraryData, libraryList } };
        // return { data: { attainmentLibraryQuery, libraryList } };
        const courseCOReport = [];
        const attainmentNodeList = [];
        for (courseIdElement of attainmentLibraryQuery) {
            const courseAttainment = libraryList.filter(
                (libraryElement) =>
                    libraryElement._course_id.toString() === courseIdElement._course_id.toString(),
            );
            const overAllCLO = [];
            const courseAttainmentPlan = courseAttainmentSettings.find(
                (courseSettingElement) =>
                    courseSettingElement._course_id.toString() ===
                        courseIdElement._course_id.toString() &&
                    courseSettingElement.levelNo === courseIdElement.level &&
                    courseSettingElement.term === courseIdElement.term,
            );
            const courseAttainmentOutcome =
                courseAttainmentPlan && courseAttainmentPlan.evaluationPlan
                    ? courseAttainmentPlan.evaluationPlan.find(
                          (evaluationPlanElement) => evaluationPlanElement.outComeType === 'clo',
                      )
                    : undefined;
            for (treeElement of courseAttainmentOutcome
                ? courseAttainmentOutcome.tree
                : coAttainmentPlanOutCome.tree) {
                const subTreeList = [];
                const treeCLO = [];
                const levelTree = coAttainmentOutCome.levels.find((levelElement) =>
                    levelElement.typeId
                        ? levelElement.typeId.toString() === treeElement.typeId.toString()
                        : levelElement.typeName === treeElement.typeName,
                );
                for (subTreeElement of treeElement.subTree) {
                    const nodeList = [];
                    const subTreeLevel =
                        levelTree && levelTree.subTree
                            ? levelTree.subTree.find(
                                  (levelSubTreeElement) =>
                                      levelSubTreeElement.typeId.toString() ===
                                      subTreeElement.typeId.toString(),
                              )
                            : undefined;
                    if (!subTreeElement.nodeName) {
                        const subTreeCLO = [];
                        for (nodeElement of subTreeElement.node) {
                            const nodeClo = [];
                            const nodeAttainments = clone(
                                courseAttainment.filter(
                                    (libraryElement) =>
                                        libraryElement.assessmentTypeId &&
                                        libraryElement.assessmentTypeId.toString() ===
                                            nodeElement.typeId.toString() &&
                                        treeElement.typeId.toString() ===
                                            libraryElement.typeId.toString() &&
                                        subTreeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString(),
                                ),
                            );
                            const nodeLevel =
                                subTreeLevel && subTreeLevel.node
                                    ? subTreeLevel.node.find(
                                          (nodeLevelElement) =>
                                              nodeLevelElement.typeId.toString() ===
                                              nodeElement.typeId.toString(),
                                      )
                                    : undefined;
                            for (nodeAttainmentElement of nodeAttainments) {
                                for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                    for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                        if (
                                            assignmentQuestionElement.outComeIds.find(
                                                (outComeIdElement) =>
                                                    outComeIdElement.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            )
                                        ) {
                                            const nodeCloIndex = nodeClo.findIndex(
                                                (closElement) =>
                                                    closElement._id.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            );
                                            if (nodeCloIndex == -1)
                                                nodeClo.push({
                                                    _id: nodeAttainmentCLOElement._id.toString(),
                                                    percentage: nodeAttainmentCLOElement.percentage,
                                                    count:
                                                        nodeAttainmentCLOElement.percentage === null
                                                            ? 0
                                                            : 1,
                                                    level: '',
                                                });
                                            else {
                                                nodeClo[nodeCloIndex].percentage +=
                                                    nodeAttainmentCLOElement.percentage;
                                                nodeClo[nodeCloIndex].count++;
                                            }
                                        }
                                    }
                                }
                            }
                            for (nodeCloElement of nodeClo)
                                if (nodeCloElement.count) {
                                    if (nodeCloElement.percentage !== null)
                                        nodeCloElement.percentage /= nodeCloElement.count;
                                    const percentageLevel =
                                        nodeLevel && nodeLevel.levelValues
                                            ? nodeLevel.levelValues.find((levelValueElement) =>
                                                  levelValuesAs === RANGE
                                                      ? parseFloat(levelValueElement.min) <=
                                                            parseInt(nodeCloElement.percentage) &&
                                                        parseFloat(levelValueElement.max) >=
                                                            parseInt(nodeCloElement.percentage)
                                                      : levelValueElement.condition === EQUAL
                                                      ? parseInt(levelValueElement.percentage) <
                                                        parseInt(nodeCloElement.percentage)
                                                      : parseInt(levelValueElement.percentage) <=
                                                        parseInt(nodeCloElement.percentage),
                                              )
                                            : '';
                                    nodeCloElement.level =
                                        percentageLevel && percentageLevel.level
                                            ? percentageLevel.level
                                            : '';
                                }

                            nodeList.push({
                                _id: nodeElement._id,
                                typeName: nodeElement.typeName,
                                typeId: nodeElement.typeId,
                                nodeId: nodeElement.nodeId,
                                weightage: nodeElement.weightage,
                                nodeName: nodeElement.nodeName,
                                clo: nodeClo,
                                attainments: nodeAttainments,
                            });
                        }
                        for (nodeListElement of nodeList) {
                            for (nodeAttainmentCLOElement of nodeListElement.clo) {
                                const cloIndex = subTreeCLO.findIndex(
                                    (closElement) =>
                                        closElement._id.toString() ===
                                        nodeAttainmentCLOElement._id.toString(),
                                );
                                if (cloIndex == -1)
                                    subTreeCLO.push({
                                        _id: nodeAttainmentCLOElement._id.toString(),
                                        percentage:
                                            nodeAttainmentCLOElement.percentage === null
                                                ? nodeAttainmentCLOElement.percentage
                                                : (nodeAttainmentCLOElement.percentage *
                                                      nodeListElement.weightage) /
                                                  100,
                                        count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                        level: '',
                                    });
                                else {
                                    subTreeCLO[cloIndex].percentage +=
                                        nodeAttainmentCLOElement.percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : (nodeAttainmentCLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100;
                                    subTreeCLO[cloIndex].count++;
                                }
                            }
                        }

                        for (cloSubTreeElement of subTreeCLO) {
                            // Tree
                            const percentageLevel =
                                subTreeLevel && subTreeLevel.levelValues
                                    ? subTreeLevel.levelValues.find((levelValueElement) =>
                                          levelValuesAs === RANGE
                                              ? parseFloat(levelValueElement.min) <=
                                                    parseInt(cloSubTreeElement.percentage) &&
                                                parseFloat(levelValueElement.max) >=
                                                    parseInt(cloSubTreeElement.percentage)
                                              : levelValueElement.condition === EQUAL
                                              ? parseInt(levelValueElement.percentage) <
                                                parseInt(cloSubTreeElement.percentage)
                                              : parseInt(levelValueElement.percentage) <=
                                                parseInt(cloSubTreeElement.percentage),
                                      )
                                    : '';
                            cloSubTreeElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';

                            const cloPercentage =
                                cloSubTreeElement.percentage === null
                                    ? cloSubTreeElement.percentage
                                    : (cloSubTreeElement.percentage * subTreeElement.weightage) /
                                      100;
                            const cloIndex = treeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() === cloSubTreeElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeCLO.push({
                                    _id: cloSubTreeElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeCLO[cloIndex].percentage += cloPercentage;
                                treeCLO[cloIndex].count++;
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            weightage: subTreeElement.weightage,
                            node: nodeList,
                            clo: subTreeCLO.length ? subTreeCLO : [],
                        });
                    } else {
                        const subTreeCLO = [];
                        const nodeAttainments = clone(
                            courseAttainment.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        subTreeElement.typeId.toString() &&
                                    (treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() ||
                                        treeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString()),
                            ),
                        );
                        for (nodeAttainmentElement of nodeAttainments) {
                            for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                const cloIndex = subTreeCLO.findIndex(
                                    (closElement) =>
                                        closElement._id.toString() ===
                                        nodeAttainmentCLOElement._id.toString(),
                                );
                                if (cloIndex == -1) {
                                    subTreeCLO.push({
                                        _id: nodeAttainmentCLOElement._id.toString(),
                                        percentage: nodeAttainmentCLOElement.percentage,
                                        count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                        level: '',
                                    });
                                } else if (nodeAttainmentCLOElement.percentage !== null) {
                                    subTreeCLO[cloIndex].percentage =
                                        subTreeCLO[cloIndex].percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : subTreeCLO[cloIndex].percentage +
                                              nodeAttainmentCLOElement.percentage;
                                    subTreeCLO[cloIndex].count++;
                                }
                                const percentageLevel =
                                    subTreeLevel && subTreeLevel.levelValues
                                        ? subTreeLevel.levelValues.find((levelValueElement) =>
                                              levelValuesAs === RANGE
                                                  ? parseFloat(levelValueElement.min) <=
                                                        parseInt(
                                                            nodeAttainmentCLOElement.percentage,
                                                        ) &&
                                                    parseFloat(levelValueElement.max) >=
                                                        parseInt(
                                                            nodeAttainmentCLOElement.percentage,
                                                        )
                                                  : levelValueElement.condition === EQUAL
                                                  ? parseInt(levelValueElement.percentage) <
                                                    parseInt(nodeAttainmentCLOElement.percentage)
                                                  : parseInt(levelValueElement.percentage) <=
                                                    parseInt(nodeAttainmentCLOElement.percentage),
                                          )
                                        : '';
                                nodeAttainmentCLOElement.level =
                                    percentageLevel && percentageLevel.level
                                        ? percentageLevel.level
                                        : '';
                            }
                        }

                        for (cloSubTreeElement of subTreeCLO) {
                            // Tree
                            const percentageLevel =
                                subTreeLevel && subTreeLevel.levelValues
                                    ? subTreeLevel.levelValues.find((levelValueElement) =>
                                          levelValuesAs === RANGE
                                              ? parseFloat(levelValueElement.min) <=
                                                    parseInt(cloSubTreeElement.percentage) &&
                                                parseFloat(levelValueElement.max) >=
                                                    parseInt(cloSubTreeElement.percentage)
                                              : levelValueElement.condition === EQUAL
                                              ? parseInt(levelValueElement.percentage) <
                                                parseInt(cloSubTreeElement.percentage)
                                              : parseInt(levelValueElement.percentage) <=
                                                parseInt(cloSubTreeElement.percentage),
                                      )
                                    : '';
                            cloSubTreeElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';
                            const cloPercentage =
                                cloSubTreeElement.percentage === null
                                    ? cloSubTreeElement.percentage
                                    : (cloSubTreeElement.percentage * subTreeElement.weightage) /
                                      100;

                            const cloIndex = treeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() === cloSubTreeElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeCLO.push({
                                    _id: cloSubTreeElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeCLO[cloIndex].percentage += cloPercentage;
                                treeCLO[cloIndex].count++;
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            nodeId: subTreeElement.nodeId,
                            nodeName: subTreeElement.nodeName,
                            attainments: nodeAttainments,
                            clo: subTreeCLO.length ? subTreeCLO : [],
                        });
                    }
                }
                for (cloTreeElement of treeCLO) {
                    // OverAll CLO
                    const percentageLevel =
                        levelTree && levelTree.levelValues
                            ? levelTree.levelValues.find((levelValueElement) =>
                                  levelValuesAs === RANGE
                                      ? parseFloat(levelValueElement.min) <=
                                            parseInt(cloTreeElement.percentage) &&
                                        parseFloat(levelValueElement.max) >=
                                            parseInt(cloTreeElement.percentage)
                                      : levelValueElement.condition === EQUAL
                                      ? parseInt(levelValueElement.percentage) <
                                        parseInt(cloTreeElement.percentage)
                                      : parseInt(levelValueElement.percentage) <=
                                        parseInt(cloTreeElement.percentage),
                              )
                            : [];
                    cloTreeElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                    const cloPercentage =
                        cloTreeElement.percentage === null
                            ? cloTreeElement.percentage
                            : (cloTreeElement.percentage * treeElement.weightage) / 100;

                    const cloIndex = overAllCLO.findIndex(
                        (closElement) =>
                            closElement._id.toString() === cloTreeElement._id.toString(),
                    );
                    if (cloIndex === -1) {
                        overAllCLO.push({
                            _id: cloTreeElement._id.toString(),
                            percentage: cloPercentage,
                            count: 1,
                            level: '',
                        });
                    } else if (cloPercentage !== null) {
                        overAllCLO[cloIndex].percentage += cloPercentage;
                        overAllCLO[cloIndex].count++;
                    }
                }
                // attainmentNodeList.push({
                //     _id: treeElement._id,
                //     typeName: treeElement.typeName,
                //     typeId: treeElement.typeId,
                //     weightage: treeElement.weightage,
                //     subTree: subTreeList,
                //     clo: treeCLO.length ? treeCLO : courseCLOList,
                // });
            }
            if (overAllCLO.length) {
                // const overAllAttainmentIndex = attainmentNodeList.findIndex(
                //     (attainmentNodeListElement) =>
                //         attainmentNodeListElement.typeName === 'Overall Attainment',
                // );
                const attElement = coAttainmentOutCome.levels.find(
                    (attainmentNodeListElement) =>
                        attainmentNodeListElement.typeName === 'Overall Attainment',
                );
                // if (overAllAttainmentIndex !== -1) {
                for (overAllCLOElement of overAllCLO) {
                    const percentageLevel =
                        attElement && attElement.levelValues
                            ? attElement.levelValues.find((levelValueElement) =>
                                  levelValuesAs === RANGE
                                      ? parseFloat(levelValueElement.min) <=
                                            parseInt(overAllCLOElement.percentage) &&
                                        parseFloat(levelValueElement.max) >=
                                            parseInt(overAllCLOElement.percentage)
                                      : levelValueElement.condition === EQUAL
                                      ? parseInt(levelValueElement.percentage) <
                                        parseInt(overAllCLOElement.percentage)
                                      : parseInt(levelValueElement.percentage) <=
                                        parseInt(overAllCLOElement.percentage),
                              )
                            : '';
                    overAllCLOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                }
                // attainmentNodeList[overAllAttainmentIndex].clo = overAllCLO;
                // }
            }
            courseCOReport.push({ courseId: courseIdElement._course_id, overAllCLO });
        }
        // return { data: courseCOReport };

        // Course Merge with Year Level Program Calendar
        const curriculumCourses = [];
        const curriculumCoursesAverage = { percentage: 0, count: 0, level: '' };
        const termCourses = curriculumYearLevelData.course.filter(
            (courseElement) =>
                courseElement.term === term &&
                courseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._course_id.toString(),
                ),
        );
        for (courseElement of termCourses) {
            const courseCOAttainmentReport = courseCOReport.find(
                (courseCOReportElement) =>
                    courseCOReportElement.courseId.toString() ===
                    courseElement._course_id.toString(),
            );
            courseElement.clo = [];
            courseElement.plo = [];
            const courseObtainedCLOs = courseCLO.find(
                (courseMasterElement) =>
                    courseMasterElement._course_id.toString() ===
                        courseElement._course_id.toString() &&
                    courseMasterElement.course_assigned_details.find(
                        (assignedDetailsElement) =>
                            assignedDetailsElement.level_no === courseElement.levelNo,
                    ),
            );
            if (courseObtainedCLOs) {
                courseElement.clo = courseObtainedCLOs.clo;
                const courseAssignedData = courseObtainedCLOs.course_assigned_details.find(
                    (courseAssignedElement) =>
                        courseAssignedElement.level_no === courseElement.levelNo,
                );
                if (courseAssignedData) {
                    courseElement.mapping_type = courseAssignedData.mapping_type;
                    courseElement.content_mapping_type = courseAssignedData.content_mapping_type;
                }
            }
            const courseOverAllPlo = { percentage: null, count: 0 };
            if (courseElement.clo.length) {
                for (ploElement of curriculumYearLevelData.curriculumPLO) {
                    const courseCLOFiltered = ploElement.clos.filter((cloElement) =>
                        courseElement.clo.find(
                            (courseCloElement) =>
                                courseCloElement.toString() === cloElement.clo_id.toString(),
                        ),
                    );
                    if (courseCLOFiltered.length) {
                        const impactCalculation = {
                            values: 0,
                            count: 0,
                        };
                        if (courseElement.mapping_type === 'impact') {
                            for (courseCloElement of courseCLOFiltered) {
                                switch (courseCloElement.mapped_value) {
                                    case 'H':
                                        impactCalculation.values += 3;
                                        impactCalculation.count++;
                                        break;
                                    case 'M':
                                        impactCalculation.values += 2;
                                        impactCalculation.count++;
                                        break;
                                    case 'L':
                                        impactCalculation.values += 1;
                                        impactCalculation.count++;
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log(impactCalculation);
                        // const courseCLOIds = courseCLOFiltered.map((filteredCloElement) =>
                        //     filteredCloElement.clo_id.toString(),
                        // );
                        const coursePLOPercentage = { ...reportStruct };
                        let courseImpactPercentageValue = 0;
                        const courseImpactPercentageValues = [];
                        if (courseCOAttainmentReport) {
                            // for (courseCLOFilteredElement of courseCLOFiltered) {
                            const courseOverAllCLOs = courseCOAttainmentReport.overAllCLO.filter(
                                (reportCLOReport) =>
                                    courseCLOFiltered.find(
                                        (filteredCLOElement) =>
                                            filteredCLOElement.clo_id.toString() ===
                                            reportCLOReport._id.toString(),
                                    ),
                            );
                            if (courseElement.mapping_type === 'impact') {
                                const courseImpactValue = {
                                    min:
                                        levelValuesAs === RANGE
                                            ? overAllLevelValuesWithOutSort[0].max
                                            : overAllLevelValuesWithOutSort[0].percentage,
                                    max:
                                        levelValuesAs === RANGE
                                            ? overAllLevelValuesWithOutSort[
                                                  overAllLevelValuesWithOutSort.length - 1
                                              ].max
                                            : overAllLevelValuesWithOutSort[
                                                  overAllLevelValuesWithOutSort.length - 1
                                              ].percentage,
                                };
                                // console.log('Impact Min Max Value ', courseImpactValue);
                                for (const courseOverAllCLOElement of courseOverAllCLOs) {
                                    // if (
                                    //     courseElement._course_id.toString() ===
                                    //     '635f86efbb2931426e1c84b9'
                                    // )
                                    //     console.log(courseOverAllCLOElement.percentage);
                                    /* if (courseOverAllCLOElement.percentage === 0) {
                                        courseImpactPercentageValues.push({
                                            values: 0,
                                            cloId: courseOverAllCLOElement._id,
                                        });
                                    } else */
                                    if (
                                        courseImpactValue.min >=
                                        parseInt(courseOverAllCLOElement.percentage)
                                    ) {
                                        courseImpactPercentageValue++;
                                        courseImpactPercentageValues.push({
                                            values: 1,
                                            cloId: courseOverAllCLOElement._id,
                                        });
                                        // console.log(
                                        //     'less',
                                        //     courseImpactValue.min,
                                        //     courseOverAllCLOElement.percentage,
                                        // );
                                    } else if (
                                        courseImpactValue.max <=
                                        parseInt(courseOverAllCLOElement.percentage)
                                    ) {
                                        courseImpactPercentageValue += 3;
                                        // console.log(
                                        //     'greater',
                                        //     courseImpactValue.max,
                                        //     courseOverAllCLOElement.percentage,
                                        // );
                                        courseImpactPercentageValues.push({
                                            values: 3,
                                            cloId: courseOverAllCLOElement._id,
                                        });
                                    } else {
                                        const percentageLevel = (
                                            levelValuesAs !== RANGE
                                                ? overAllLevelValues.sort((a, b) => {
                                                      let comparison = 0;
                                                      if (
                                                          parseInt(a.percentage) >
                                                          parseInt(b.percentage)
                                                      ) {
                                                          comparison = -1;
                                                      } else if (
                                                          parseInt(a.percentage) <
                                                          parseInt(b.percentage)
                                                      ) {
                                                          comparison = 1;
                                                      }
                                                      return comparison;
                                                  })
                                                : overAllLevelValues
                                        ).findIndex((levelValueElement) =>
                                            levelValuesAs === RANGE
                                                ? parseFloat(levelValueElement.min) <=
                                                      parseInt(
                                                          courseOverAllCLOElement.percentage,
                                                      ) &&
                                                  parseFloat(levelValueElement.max) >=
                                                      parseInt(courseOverAllCLOElement.percentage)
                                                : levelValueElement.condition === EQUAL
                                                ? parseInt(levelValueElement.percentage) <
                                                  parseInt(courseOverAllCLOElement.percentage)
                                                : parseInt(levelValueElement.percentage) <=
                                                  parseInt(courseOverAllCLOElement.percentage),
                                        );
                                        // console.log(percentageLevel < overAllLevelValues.length);
                                        // console.log(percentageLevel > overAllLevelValues.length);
                                        // console.log(
                                        //     'Val \n',
                                        //     percentageLevel,
                                        //     courseOverAllCLOElement.percentage,
                                        //     overAllLevelValues[percentageLevel],
                                        //     overAllLevelValues[percentageLevel + 1],
                                        //     '\n',
                                        // );
                                        // console.info({
                                        //     x: courseOverAllCLOElement.percentage,
                                        //     x1: overAllLevelValues[percentageLevel - 1].max,
                                        //     x2: overAllLevelValues[percentageLevel].max,
                                        //     y1: overAllLevelValues[percentageLevel - 1].levelValue,
                                        //     y2: overAllLevelValues[percentageLevel].levelValue,
                                        // });
                                        if (percentageLevel !== -1) {
                                            // console.info({
                                            //     percentageLevel,
                                            //     x: courseOverAllCLOElement.percentage,
                                            //     x1: overAllLevelValues[percentageLevel - 1].max,
                                            //     x2: overAllLevelValues[percentageLevel].max,
                                            //     y1: overAllLevelValues[percentageLevel - 1]
                                            //         .levelValue,
                                            //     y2: overAllLevelValues[percentageLevel].levelValue,
                                            // });
                                            const x = courseOverAllCLOElement.percentage;
                                            const x1 =
                                                levelValuesAs === RANGE
                                                    ? overAllLevelValues[percentageLevel - 1].max
                                                    : overAllLevelValues[percentageLevel - 1]
                                                          .percentage;
                                            const x2 =
                                                levelValuesAs === RANGE
                                                    ? overAllLevelValues[percentageLevel].max
                                                    : overAllLevelValues[percentageLevel]
                                                          .percentage;
                                            const y1 =
                                                overAllLevelValues[percentageLevel - 1].levelValue;
                                            const y2 =
                                                overAllLevelValues[percentageLevel].levelValue;
                                            const cloImpactLevelValue =
                                                y1 + (x - x1) * ((y2 - y1) / (x2 - x1));
                                            // console.log({ cloImpactLevelValue });
                                            courseImpactPercentageValue += cloImpactLevelValue;
                                            courseImpactPercentageValues.push({
                                                values: cloImpactLevelValue,
                                                cloId: courseOverAllCLOElement._id,
                                            });
                                        }
                                    }
                                }
                            } else {
                                coursePLOPercentage.percentage =
                                    courseOverAllCLOs.reduce(
                                        (n, cloElement) => n + parseFloat(cloElement.percentage),
                                        0,
                                    ) / courseOverAllCLOs.length;
                            }
                            // }
                        }
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log({
                        //         courseImpactPercentageValue,
                        //         courseImpactPercentageValues,
                        //     });
                        const poOutcome = impactCalculation.values / impactCalculation.count;
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log({ poOutcome });
                        const coInLevel =
                            courseImpactPercentageValue / courseImpactPercentageValues.length;
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log({ coInLevel });
                        const coursePOAttainment = coInLevel * (poOutcome / 3);
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log({ outcome: poOutcome / 3, coursePOAttainment });
                        const coursePoPercentage =
                            (coursePOAttainment * 100) / overAllLevelValues.length;
                        // if (courseElement._course_id.toString() === '635f86efbb2931426e1c84b9')
                        //     console.log({ coursePoPercentage });
                        if (courseOverAllPlo.percentage === null) {
                            courseOverAllPlo.percentage = coursePoPercentage || 0;
                            courseOverAllPlo.count = 1;
                        } else {
                            courseOverAllPlo.percentage += coursePoPercentage || 0;
                            courseOverAllPlo.count++;
                        }
                        const poIndex = curriculumPLOStruct.findIndex(
                            (poElement) => poElement._id.toString() === ploElement._id.toString(),
                        );
                        if (poIndex !== -1 && coursePOAttainment) {
                            curriculumPLOStruct[poIndex].value += coursePOAttainment;
                            curriculumPLOStruct[poIndex].count++;
                        }
                        const poPercentageLevel = overAllLevelValues.find((levelValueElement) =>
                            levelValuesAs === RANGE
                                ? parseFloat(levelValueElement.min) <=
                                      parseInt(coursePoPercentage) &&
                                  parseFloat(levelValueElement.max) >= parseInt(coursePoPercentage)
                                : levelValueElement.condition === EQUAL
                                ? parseInt(levelValueElement.percentage) <
                                  parseInt(coursePoPercentage)
                                : parseInt(levelValueElement.percentage) <=
                                  parseInt(coursePoPercentage),
                        );
                        const coursePloObject = {
                            _id: ploElement._id,
                            percentage: coursePoPercentage,
                            level: poPercentageLevel ? poPercentageLevel.level : '',
                            coursePOAttainment,
                            // poOutcome,
                            // clo: courseCLOFiltered.map((filteredCloElement) =>
                            //     filteredCloElement.clo_id.toString(),
                            // ),
                            // coInLevel,
                            // courseImpactPercentageValue,
                            // courseImpactPercentageValues,
                            // ...coursePLOPercentage,
                        };
                        courseElement.plo.push(coursePloObject);
                    }
                }
            }
            delete courseElement.clo;
            const coursePercentage = courseOverAllPlo.percentage / courseOverAllPlo.count;
            const coursePercentageLevel = overAllLevelValues.find((levelValueElement) =>
                levelValuesAs === RANGE
                    ? parseFloat(levelValueElement.min) <= parseInt(coursePercentage) &&
                      parseFloat(levelValueElement.max) >= parseInt(coursePercentage)
                    : levelValueElement.condition === EQUAL
                    ? parseInt(levelValueElement.percentage) < parseInt(coursePercentage)
                    : parseInt(levelValueElement.percentage) <= parseInt(coursePercentage),
            );
            if (coursePercentage) {
                curriculumCoursesAverage.percentage += coursePercentage;
                curriculumCoursesAverage.count++;
            }
            curriculumCourses.push({
                ...courseElement,
                percentage: coursePercentage,
                level: coursePercentageLevel ? coursePercentageLevel.level : '',
                // ...reportStruct,
                // courseCOAttainmentReport,
                // plo: curriculumPLOStruct,
            });
        }
        if (curriculumCoursesAverage.percentage) {
            curriculumCoursesAverage.percentage /= curriculumCoursesAverage.count;
            const allCourseLevel = overAllLevelValues.find((levelValueElement) =>
                levelValuesAs === RANGE
                    ? parseFloat(levelValueElement.min) <=
                          parseInt(curriculumCoursesAverage.percentage) &&
                      parseFloat(levelValueElement.max) >=
                          parseInt(curriculumCoursesAverage.percentage)
                    : levelValueElement.condition === EQUAL
                    ? parseInt(levelValueElement.percentage) <
                      parseInt(curriculumCoursesAverage.percentage)
                    : parseInt(levelValueElement.percentage) <=
                      parseInt(curriculumCoursesAverage.percentage),
            );
            curriculumCoursesAverage.level = allCourseLevel ? allCourseLevel.level : '';
        }
        const attainmentDirectWeightage =
            attainmentPlanOutCome &&
            attainmentPlanOutCome.tree &&
            attainmentPlanOutCome.tree.find((treeElement) => treeElement.typeName === 'direct')
                ? attainmentPlanOutCome.tree.find(
                      (treeElement) => treeElement.typeName === 'direct',
                  ).weightage
                : 100;
        for (curriculumPLOElement of curriculumPLOs) {
            const curPlo = curriculumPLOStruct.find(
                (poElement) => poElement._id.toString() === curriculumPLOElement._id.toString(),
            );
            if (curPlo.count) {
                const curriculumPOLevel =
                    ((curPlo.value / curPlo.count) * 100) / overAllLevelValues.length;
                const poPercentageLevel = overAllLevelValues.find((levelValueElement) =>
                    levelValuesAs === RANGE
                        ? parseFloat(levelValueElement.min) <= parseInt(curriculumPOLevel) &&
                          parseFloat(levelValueElement.max) >= parseInt(curriculumPOLevel)
                        : levelValueElement.condition === EQUAL
                        ? parseInt(levelValueElement.percentage) < parseInt(curriculumPOLevel)
                        : parseInt(levelValueElement.percentage) <= parseInt(curriculumPOLevel),
                );
                // curriculumPLOElement.percentage = curriculumPOLevel;
                curriculumPLOElement.percentage =
                    (curriculumPOLevel * attainmentDirectWeightage) / 100;
                curriculumPLOElement.level = poPercentageLevel ? poPercentageLevel.level : '';
            }
        }
        const attainmentIndirect =
            attainmentPlanOutCome &&
            attainmentPlanOutCome.tree &&
            attainmentPlanOutCome.tree.find((treeElement) => treeElement.typeName === 'indirect');
        if (attainmentIndirect) {
            const inDirectPLO = [];
            let libraryData = await assessmentLibrarySchema.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                    type: 'program',
                    term,
                },
                {
                    _assessment_id: 1,
                    studentDetails: 1,
                    questionMarks: 1,
                    typeId: 1,
                    subTypeId: 1,
                    assessmentTypeId: 1,
                    _institution_calendar_id: 1,
                },
            );
            libraryData = clone(libraryData);
            const libraryList = [];
            const coursePLOList = curriculumPLOs.map((coursePloElement) => {
                return {
                    _id: coursePloElement._id,
                    ...reportStruct,
                };
            });
            for (libraryElement of libraryData) {
                for (questionElement of libraryElement.questionMarks) {
                    questionElement.attendedCount = 0;
                    questionElement.passedCount = 0;
                    for (studentElement of libraryElement.studentDetails) {
                        const studentQuestion = studentElement.studentMarks.find(
                            (studentQuestionMarkElement) =>
                                studentQuestionMarkElement.questionName ===
                                questionElement.questionName,
                        );
                        if (studentQuestion && studentQuestion.mark !== null) {
                            questionElement.attendedCount++;
                            if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                                questionElement.passedCount++;
                        }
                    }
                    if (questionElement.attendedCount > 0)
                        questionElement.average = (
                            questionElement.passedCount / questionElement.attendedCount
                        ).toFixed(4);
                }
                const assessmentPlo = clone(curriculumPLOs);
                for (coursePloElement of assessmentPlo) {
                    const ploQuestions = libraryElement.questionMarks.filter(
                        (questionElement) =>
                            questionElement.attendedCount > 0 &&
                            questionElement.outComeIds.find(
                                (outComeIdElement) =>
                                    outComeIdElement.toString() === coursePloElement._id.toString(),
                            ),
                    );
                    coursePloElement.percentage =
                        ploQuestions.reduce(
                            (n, ploElement) => n + parseFloat(ploElement.average),
                            0,
                        ) / ploQuestions.length;
                    if (coursePloElement.percentage)
                        coursePloElement.percentage = coursePloElement.percentage.toFixed(4) * 100;
                }
                libraryElement.plo = assessmentPlo.map((ploElement) => {
                    return {
                        _id: ploElement._id,
                        percentage: ploElement.percentage,
                        level: '',
                    };
                });
                libraryList.push({
                    _id: libraryElement._id,
                    _assessment_id: libraryElement._assessment_id,
                    typeId: libraryElement.typeId,
                    subTypeId: libraryElement.subTypeId,
                    assessmentTypeId: libraryElement.assessmentTypeId,
                    questionMarks: libraryElement.questionMarks,
                    plo: libraryElement.plo,
                    institutionCalendar: institutionCalendarData.find(
                        (calendarElement) =>
                            calendarElement._id.toString() ===
                            libraryElement._institution_calendar_id.toString(),
                    )
                        ? institutionCalendarData.find(
                              (calendarElement) =>
                                  calendarElement._id.toString() ===
                                  libraryElement._institution_calendar_id.toString(),
                          ).calendar_name
                        : '',
                });
            }
            const subTreeList = [];
            for (subTreeElement of attainmentIndirect.subTree) {
                const nodeList = [];
                if (!subTreeElement.nodeName) {
                    const subTreePLO = [];
                    for (nodeElement of subTreeElement.node) {
                        const nodePlo = [];
                        const nodeAttainments = clone(
                            libraryList.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        nodeElement.typeId.toString() &&
                                    treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() &&
                                    subTreeElement.typeId.toString() ===
                                        libraryElement.subTypeId.toString(),
                            ),
                        );

                        for (nodeAttainmentElement of nodeAttainments) {
                            for (nodeAttainmentPLOElement of nodeAttainmentElement.plo) {
                                for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                    if (
                                        assignmentQuestionElement.outComeIds.find(
                                            (outComeIdElement) =>
                                                outComeIdElement.toString() ===
                                                nodeAttainmentPLOElement._id.toString(),
                                        )
                                    ) {
                                        const nodePloIndex = nodePlo.findIndex(
                                            (plosElement) =>
                                                plosElement._id.toString() ===
                                                nodeAttainmentPLOElement._id.toString(),
                                        );
                                        if (nodePloIndex == -1)
                                            nodePlo.push({
                                                _id: nodeAttainmentPLOElement._id.toString(),
                                                percentage: nodeAttainmentPLOElement.percentage,
                                                count:
                                                    nodeAttainmentPLOElement.percentage === null
                                                        ? 0
                                                        : 1,
                                                level: '',
                                            });
                                        else {
                                            nodePlo[nodePloIndex].percentage +=
                                                nodeAttainmentPLOElement.percentage;
                                            nodePlo[nodePloIndex].count++;
                                        }
                                    }
                                }
                            }
                        }
                        for (nodePloElement of nodePlo)
                            if (nodePloElement.count) {
                                nodePloElement.percentage /= nodePloElement.count;
                            }

                        nodeList.push({
                            plo: nodePlo,
                        });
                    }
                    for (nodeListElement of nodeList) {
                        for (nodeAttainmentPLOElement of nodeListElement.plo) {
                            const ploIndex = subTreePLO.findIndex(
                                (plosElement) =>
                                    plosElement._id.toString() ===
                                    nodeAttainmentPLOElement._id.toString(),
                            );
                            if (ploIndex == -1)
                                subTreePLO.push({
                                    _id: nodeAttainmentPLOElement._id.toString(),
                                    percentage:
                                        nodeAttainmentPLOElement.percentage === null
                                            ? nodeAttainmentPLOElement.percentage
                                            : (nodeAttainmentPLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100,
                                    count: nodeAttainmentPLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            else {
                                subTreePLO[ploIndex].percentage +=
                                    nodeAttainmentPLOElement.percentage === null
                                        ? nodeAttainmentPLOElement.percentage
                                        : (nodeAttainmentPLOElement.percentage *
                                              nodeListElement.weightage) /
                                          100;
                                subTreePLO[ploIndex].count++;
                            }
                        }
                    }

                    for (ploSubTreeElement of subTreePLO) {
                        if (ploSubTreeElement.percentage !== null)
                            ploSubTreeElement.percentage /= ploSubTreeElement.count;

                        const ploPercentage =
                            ploSubTreeElement.percentage === null
                                ? ploSubTreeElement.percentage
                                : (ploSubTreeElement.percentage * subTreeElement.weightage) / 100;
                        const ploIndex = inDirectPLO.findIndex(
                            (plosElement) =>
                                plosElement._id.toString() === ploSubTreeElement._id.toString(),
                        );
                        if (ploIndex == -1)
                            inDirectPLO.push({
                                _id: ploSubTreeElement._id.toString(),
                                percentage: ploPercentage,
                                count: ploPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (ploPercentage !== null && !!ploPercentage) {
                            inDirectPLO[ploIndex].percentage += ploPercentage;
                            inDirectPLO[ploIndex].count++;
                        }
                    }
                    subTreeList.push({
                        plo: subTreePLO.length ? subTreePLO : coursePLOList,
                    });
                } else {
                    const subTreePLO = [];
                    const nodeAttainments = clone(
                        libraryList.filter(
                            (libraryElement) =>
                                libraryElement.assessmentTypeId &&
                                libraryElement.assessmentTypeId.toString() ===
                                    subTreeElement.typeId.toString(),
                        ),
                    );
                    for (nodeAttainmentElement of nodeAttainments) {
                        for (nodeAttainmentPLOElement of nodeAttainmentElement.plo) {
                            const ploIndex = subTreePLO.findIndex(
                                (plosElement) =>
                                    plosElement._id.toString() ===
                                    nodeAttainmentPLOElement._id.toString(),
                            );
                            if (ploIndex == -1) {
                                subTreePLO.push({
                                    _id: nodeAttainmentPLOElement._id.toString(),
                                    percentage: nodeAttainmentPLOElement.percentage,
                                    count: nodeAttainmentPLOElement.percentage === null ? 0 : 1,
                                    level: '',
                                });
                            } else if (nodeAttainmentPLOElement.percentage !== null) {
                                subTreePLO[ploIndex].percentage =
                                    subTreePLO[ploIndex].percentage === null
                                        ? nodeAttainmentPLOElement.percentage
                                        : subTreePLO[ploIndex].percentage +
                                          nodeAttainmentPLOElement.percentage;
                                subTreePLO[ploIndex].count++;
                            }
                        }
                    }
                    for (ploSubTreeElement of subTreePLO) {
                        if (ploSubTreeElement.percentage !== null)
                            ploSubTreeElement.percentage /= ploSubTreeElement.count;
                        const ploPercentage =
                            ploSubTreeElement.percentage === null
                                ? ploSubTreeElement.percentage
                                : (ploSubTreeElement.percentage * subTreeElement.weightage) / 100;
                        const ploIndex = inDirectPLO.findIndex(
                            (plosElement) =>
                                plosElement._id.toString() === ploSubTreeElement._id.toString(),
                        );
                        if (ploIndex == -1)
                            inDirectPLO.push({
                                _id: ploSubTreeElement._id.toString(),
                                percentage: ploPercentage,
                                count: ploPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (ploPercentage !== null && !!ploPercentage) {
                            inDirectPLO[ploIndex].percentage += ploPercentage;
                            inDirectPLO[ploIndex].count++;
                        }
                    }
                    subTreeList.push({
                        plo: subTreePLO.length ? subTreePLO : coursePLOList,
                    });
                }
            }
            // subTreeList
            for (inDirectPLOElement of inDirectPLO) {
                if (inDirectPLOElement.percentage !== null) {
                    inDirectPLOElement.percentage /= inDirectPLOElement.count;

                    const ploIndex = curriculumPLOs.findIndex(
                        (plosElement) =>
                            plosElement._id.toString() === inDirectPLOElement._id.toString(),
                    );
                    const curriculumPOLevel =
                        curriculumPLOs[ploIndex].percentage +
                        (inDirectPLOElement.percentage * attainmentIndirect.weightage) / 100;
                    const poPercentageLevel = overAllLevelValues.find((levelValueElement) =>
                        levelValuesAs === RANGE
                            ? parseFloat(levelValueElement.min) <= parseInt(curriculumPOLevel) &&
                              parseFloat(levelValueElement.max) >= parseInt(curriculumPOLevel)
                            : levelValueElement.condition === EQUAL
                            ? parseInt(levelValueElement.percentage) < parseInt(curriculumPOLevel)
                            : parseInt(levelValueElement.percentage) <= parseInt(curriculumPOLevel),
                    );
                    if (ploIndex == -1)
                        curriculumPLOs.push({
                            _id: inDirectPLOElement._id.toString(),
                            percentage: curriculumPOLevel,
                            level: poPercentageLevel ? poPercentageLevel.level : '',
                        });
                    else if (curriculumPOLevel !== null && !!curriculumPOLevel) {
                        curriculumPLOs[ploIndex].percentage = curriculumPOLevel;
                        curriculumPLOs[ploIndex].level = poPercentageLevel
                            ? poPercentageLevel.level
                            : '';
                    }
                }
            }
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                curriculumPLOs,
                curriculumCourses,
                curriculumCoursesAverage,
                attainmentLevel: attainmentOutCome.levels,
                attainmentValuesAs: attainmentOutCome.valuesAs,
                attainmentTargetBenchMark: attainmentOutCome.targetBenchMark,
                levelColors: attainmentOutCome.levelColors,
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCourseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, institutionCalendarId, term, attainmentId } = query;
        const institutionCalendarData = await getInstitutionCalendars({
            _institution_id,
            institutionCalendarId,
        });
        let curriculumYearLevelData = await getYearLevelCourseList({
            programId,
            institutionCalendarId,
            term,
        });
        curriculumYearLevelData = curriculumYearLevelData.sort((a, b) => {
            let comparison = 0;
            if (a.levelNo > b.levelNo) {
                comparison = -1;
            } else if (a.levelNo < b.levelNo) {
                comparison = 1;
            }
            return comparison;
        });
        let attainmentData = await attainmentManagementSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(attainmentId),
            },
            { programCourseList: 1 },
        );
        if (!attainmentData || !(attainmentData && attainmentData.programCourseList))
            attainmentData = { programCourseList: [] };
        let calendarIndex = 0;
        const yearList = [curriculumYearLevelData[0].year];
        for (levelElement of curriculumYearLevelData) {
            if (!yearList.find((yearElement) => yearElement === levelElement.year)) {
                calendarIndex =
                    calendarIndex + 1 > institutionCalendarData.length - 1 ? 0 : calendarIndex + 1;
                yearList.push(levelElement.year);
            }
            levelElement._institution_calendar_id = convertToMongoObjectId(
                institutionCalendarData[calendarIndex]._id,
            );
            levelElement.calendar_name = institutionCalendarData[calendarIndex].calendar_name;
            for (courseElement of levelElement.courses) {
                courseElement.status = attainmentData.programCourseList.length
                    ? attainmentData.programCourseList.find(
                          (programCourseElement) =>
                              programCourseElement.term === term &&
                              programCourseElement.levelNo === levelElement.levelNo &&
                              programCourseElement.courseId.toString() ===
                                  courseElement._course_id.toString(),
                      )
                        ? attainmentData.programCourseList.find(
                              (programCourseElement) =>
                                  programCourseElement.term === term &&
                                  programCourseElement.levelNo === levelElement.levelNo &&
                                  programCourseElement.courseId.toString() ===
                                      courseElement._course_id.toString(),
                          ).status
                        : true
                    : true;
            }
        }
        curriculumYearLevelData = curriculumYearLevelData.sort((a, b) => {
            let comparison = 0;
            if (a.levelNo > b.levelNo) {
                comparison = 1;
            } else if (a.levelNo < b.levelNo) {
                comparison = -1;
            }
            return comparison;
        });
        return {
            statusCode: 200,
            message: 'ATTAINMENT_COURSE_LIST_RETRIEVED',
            data: curriculumYearLevelData,
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateProgramCourseList = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { attainmentId, courseList } = body;
        const attainmentSetObject = {
            $set: {
                programCourseList: courseList,
            },
        };
        const updateResponse = await attainmentManagementSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(attainmentId),
            },
            attainmentSetObject,
        );
        if (!updateResponse) return { statusCode: 404, message: 'UNABLE_TO_UPDATE' };
        return {
            statusCode: 200,
            message: 'ATTAINMENT_COURSE_LIST_UPDATED',
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramAttainmentReportWithFilter = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { attainmentId, programId, institutionCalendarId, term, outCome, coTreeIds } = body;
        let { assessmentIds } = body;
        const reportStruct = {
            percentage: null,
            level: '',
        };
        const reportQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _program_id: convertToMongoObjectId(programId),
            outCome,
            term,
            _attainment_id: convertToMongoObjectId(attainmentId),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };
        const generatedReport = await assessmentReportSchema.findOne(
            {
                ...reportQuery,
            },
            { /* courses: 1, */ courseTreePO: 1 },
        );
        if (!generatedReport) return { status: 410, message: 'PLS Generate Report First' };
        const institutionCalendarData = await getInstitutionCalendars({
            _institution_id,
            institutionCalendarId,
        });
        const programAttainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    // assessmentSelections: 1,
                    _curriculum_id: 1,
                    programSelections: 1,
                    programCourseList: 1,
                },
            )
            .lean();
        if (!programAttainmentList) return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        // const attainmentPOCourse = programAttainmentList.programCourseList
        //     ? /* programAttainmentList.programCourseList */ []
        //     : [];
        const assessmentSelections =
            programAttainmentList.programSelections &&
            programAttainmentList.programSelections.length
                ? programAttainmentList.programSelections
                : [];
        const programAssessmentIdIndex = assessmentSelections.findIndex(
            (selectionElement) => selectionElement.mode === 'assessment',
        );
        // if (assessmentIds)
        //     if (programAssessmentIdIndex !== -1)
        //         assessmentSelections[programAssessmentIdIndex].assessmentIds = assessmentIds;
        //     else
        //         assessmentSelections.push({
        //             mode: 'assessment',
        //             assessmentIds: assessmentIds.map((assessmentElement) =>
        //                 convertToMongoObjectId(assessmentElement),
        //             ),
        //         });
        if (!assessmentIds && programAssessmentIdIndex !== -1)
            assessmentIds = assessmentSelections[programAssessmentIdIndex].assessmentIds;

        let programCOs = assessmentSelections.filter(
            (selectionElement) => selectionElement.mode === 'co',
        );
        if (coTreeIds) {
            if (coTreeIds.length === 0) programCOs = [];
            else programCOs = coTreeIds;
            // for (coTreeElement of coTreeIds) {
            //     const poTreeIndex = programCOs.findIndex(
            //         (programCOElement) =>
            //             programCOElement.typeId.toString() === coTreeElement.typeId.toString(),
            //     );
            //     if (poTreeIndex === -1) programCOs.push({ ...coTreeElement, mode: 'co' });
            //     else programCOs[poTreeIndex].coTreeId = coTreeElement.coTreeId;
            // }
        }
        const attainmentPlanOutCome = programAttainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = programAttainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const coAttainmentPlanOutCome = programAttainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === 'clo',
        );
        if (!coAttainmentPlanOutCome)
            return { statusCode: 200, message: 'CLO_ATTAINMENT_NOT_FOUND' };
        // return { data: { attainmentPlanOutCome, coAttainmentPlanOutCome } };
        // Program Course Outcome Listing
        // for (treeElement of coAttainmentPlanOutCome.tree) {
        //     const treeCoursePO = generatedReport.courseTreePO.find(
        //         (reportElement) =>
        //             reportElement.typeId &&
        //             reportElement.typeId.toString() === treeElement.typeId.toString(),
        //     );
        //     treeElement.coursePLO = treeCoursePO && treeCoursePO.plo ? treeCoursePO.plo : [];
        //     for (subTreeElement of treeElement.subTree) {
        //         if (subTreeElement.nodeName) {
        //             const subTreeCoursePO = generatedReport.courseTreePO.find(
        //                 (reportElement) =>
        //                     reportElement.typeId &&
        //                     reportElement.typeId.toString() === subTreeElement.typeId.toString(),
        //             );
        //             subTreeElement.coursePLO =
        //                 subTreeCoursePO && subTreeCoursePO.plo ? subTreeCoursePO.plo : [];
        //         } else {
        //             for (nodeElement of subTreeElement.node) {
        //                 const nodeCoursePO = generatedReport.courseTreePO.find(
        //                     (reportElement) =>
        //                         reportElement.typeId &&
        //                         reportElement.typeId.toString() === nodeElement.typeId.toString(),
        //                 );
        //                 nodeElement.coursePLO =
        //                     nodeCoursePO && nodeCoursePO.plo ? nodeCoursePO.plo : [];
        //             }
        //         }
        //     }
        // }
        // for (treeElement of attainmentPlanOutCome.tree) {
        //     const courseTree =
        //         coAttainmentPlanOutCome && coAttainmentPlanOutCome.tree
        //             ? coAttainmentPlanOutCome.tree.find(
        //                   (courseTreeElement) =>
        //                       courseTreeElement.typeId.toString() === treeElement.typeId.toString(),
        //               )
        //             : [];
        //     for (subTreeElement of treeElement.subTree) {
        //         const courseSubTree =
        //             courseTree && courseTree.subTree
        //                 ? courseTree.subTree.find(
        //                       (courseSubTreeElement) =>
        //                           courseSubTreeElement.typeId.toString() ===
        //                           subTreeElement.typeId.toString(),
        //                   )
        //                 : [];
        //         if (subTreeElement.nodeName) {
        //             subTreeElement.coTree = courseTree;
        //         } else
        //             for (nodeElement of subTreeElement.node) {
        //                 nodeElement.coTree = courseSubTree;
        //             }
        //     }
        //     if (!treeElement.subTree.length) {
        //         treeElement.coTree = [
        //             {
        //                 typeName: 'Overall Attainment',
        //             },
        //         ];
        //     }
        // }
        const levelValuesAs = attainmentOutCome.valuesAs;
        if (!attainmentOutCome || !attainmentPlanOutCome || !levelValuesAs)
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const overAllLevelValues = attainmentOutCome.levels.find(
            (levelElement) => levelElement.typeName === 'Overall Attainment',
        )
            ? clone(
                  attainmentOutCome.levels.find(
                      (levelElement) => levelElement.typeName === 'Overall Attainment',
                  ).levelValues,
              )
            : [];
        if (overAllLevelValues.length === 0)
            return { statusCode: 200, message: 'ATTAINMENT_PLO_NOT_FOUND' };
        // const overAllLevelValuesWithOutSort = clone(overAllLevelValues);
        const curriculumYearLevelData = await getProgramPLOWithCLO({
            _institution_id,
            programId,
            institutionCalendarId,
            curriculumId: programAttainmentList._curriculum_id,
        });
        if (!curriculumYearLevelData)
            return { statusCode: 410, message: 'Program Calendar/Curriculam Data Not Found' };
        const curriculumPLOs = clone(
            curriculumYearLevelData.curriculumPLO.map((ploElement) => {
                return {
                    _id: ploElement._id,
                    no: ploElement.no,
                    name: ploElement.name,
                    ...reportStruct,
                };
            }),
        );
        const yearLevelData = [];
        let calendarIndex = 0;
        for (yearLevelElement of curriculumYearLevelData.course.reverse()) {
            const yearIndex = yearLevelData.findIndex(
                (yearElement) => yearElement.year === yearLevelElement.year.toString(),
            );
            if (yearIndex === -1) {
                yearLevelData.push({
                    year: yearLevelElement.year.toString(),
                    levelNo: [yearLevelElement.levelNo],
                    _institution_calendar_id: convertToMongoObjectId(
                        institutionCalendarData[calendarIndex]._id,
                    ),
                });
                calendarIndex =
                    calendarIndex + 1 > institutionCalendarData.length - 1 ? 0 : calendarIndex + 1;
            } else if (
                !yearLevelData[yearIndex].levelNo.find(
                    (levelElement) => levelElement === yearLevelElement.levelNo,
                )
            )
                yearLevelData[yearIndex].levelNo.push(yearLevelElement.levelNo);
        }
        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                type: 'program',
                term,
                $or: yearLevelData.map((yearLevelElement) => {
                    return {
                        levelNo: { $in: yearLevelElement.levelNo },
                        _institution_calendar_id: convertToMongoObjectId(
                            yearLevelElement._institution_calendar_id,
                        ),
                    };
                }),
            },
            {
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                _institution_calendar_id: 1,
                updatedAt: 1,
            },
        );
        libraryData = clone(libraryData);
        const libraryList = [];
        const coursePLOList = curriculumPLOs.map((coursePloElement) => {
            return {
                _id: coursePloElement._id,
                ...reportStruct,
            };
        });
        for (libraryElement of libraryData) {
            if (
                assessmentIds &&
                assessmentIds.find(
                    (assessmentIdElement) =>
                        assessmentIdElement.toString() === libraryElement._assessment_id.toString(),
                )
            )
                for (questionElement of libraryElement.questionMarks) {
                    questionElement.attendedCount = 0;
                    questionElement.passedCount = 0;
                    for (studentElement of libraryElement.studentDetails) {
                        const studentQuestion = studentElement.studentMarks.find(
                            (studentQuestionMarkElement) =>
                                studentQuestionMarkElement.questionName ===
                                questionElement.questionName,
                        );
                        if (studentQuestion && studentQuestion.mark !== null) {
                            questionElement.attendedCount++;
                            if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                                questionElement.passedCount++;
                        }
                    }
                    if (questionElement.attendedCount > 0)
                        questionElement.average = (
                            questionElement.passedCount / questionElement.attendedCount
                        ).toFixed(4);
                }
            const assessmentPlo = clone(curriculumPLOs);
            for (coursePloElement of assessmentPlo) {
                const ploQuestions = libraryElement.questionMarks.filter(
                    (questionElement) =>
                        questionElement.attendedCount > 0 &&
                        questionElement.outComeIds.find(
                            (outComeIdElement) =>
                                outComeIdElement.toString() === coursePloElement._id.toString(),
                        ),
                );
                coursePloElement.percentage =
                    ploQuestions.reduce((n, ploElement) => n + parseFloat(ploElement.average), 0) /
                    ploQuestions.length;
                if (coursePloElement.percentage)
                    coursePloElement.percentage = coursePloElement.percentage.toFixed(4) * 100;
            }
            libraryElement.plo = assessmentPlo.map((ploElement) => {
                return {
                    _id: ploElement._id,
                    percentage: ploElement.percentage,
                    level: '',
                };
            });
            libraryList.push({
                _id: libraryElement._id,
                _assessment_id: libraryElement._assessment_id,
                assessmentName: libraryElement.assessmentName,
                assessmentMark: libraryElement.assessmentMark,
                typeName: libraryElement.typeName,
                typeId: libraryElement.typeId,
                subTypeName: libraryElement.subTypeName,
                subTypeId: libraryElement.subTypeId,
                assessmentTypeName: libraryElement.assessmentTypeName,
                assessmentTypeId: libraryElement.assessmentTypeId,
                questionOutcome: libraryElement.questionOutcome,
                questionMarks: libraryElement.questionMarks,
                noQuestions: libraryElement.noQuestions,
                benchMark: libraryElement.benchMark,
                plo: libraryElement.plo,
                institutionCalendar: institutionCalendarData.find(
                    (calendarElement) =>
                        calendarElement._id.toString() ===
                        libraryElement._institution_calendar_id.toString(),
                )
                    ? institutionCalendarData.find(
                          (calendarElement) =>
                              calendarElement._id.toString() ===
                              libraryElement._institution_calendar_id.toString(),
                      ).calendar_name
                    : '',
                updatedAt: libraryElement.updatedAt,
            });
        }
        // Attainment Outcome Mode
        const attainmentNodeList = [
            {
                _id: convertToMongoObjectId(),
                typeName: 'Overall Attainment',
                subTree: [],
                plo: [],
            },
        ];
        const overAllPLO = [];
        for (treeElement of attainmentPlanOutCome.tree) {
            const courseTree =
                coAttainmentPlanOutCome && coAttainmentPlanOutCome.tree
                    ? coAttainmentPlanOutCome.tree.find(
                          (courseTreeElement) =>
                              courseTreeElement.typeId.toString() === treeElement.typeId.toString(),
                      )
                    : [];
            const subTreeList = [];
            let treePLO = [];
            const levelTree = attainmentOutCome.levels.find((levelElement) =>
                levelElement.typeId
                    ? levelElement.typeId.toString() === treeElement.typeId.toString()
                    : levelElement.typeName === treeElement.typeName,
            );
            for (subTreeElement of treeElement.subTree) {
                const courseSubTree =
                    courseTree && courseTree.subTree
                        ? courseTree.subTree.find(
                              (courseSubTreeElement) =>
                                  courseSubTreeElement.typeId.toString() ===
                                  subTreeElement.typeId.toString(),
                          )
                        : [];
                const nodeList = [];
                const subTreeLevel =
                    levelTree && levelTree.subTree
                        ? levelTree.subTree.find(
                              (levelSubTreeElement) =>
                                  levelSubTreeElement.typeId.toString() ===
                                  subTreeElement.typeId.toString(),
                          )
                        : undefined;
                // Need to Check is Course Tree Flow Or Attainment flow
                const subTreeCOTreeValue = programCOs
                    ? programCOs.find(
                          (coTreeIdElement) =>
                              coTreeIdElement.typeId.toString() ===
                              subTreeElement.typeId.toString(),
                      )
                    : undefined;
                if (subTreeCOTreeValue) {
                    const subtreeSortedValues =
                        subTreeLevel && subTreeLevel.levelValues
                            ? subTreeLevel.levelValues.sort((a, b) => {
                                  let comparison = 0;
                                  if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                      comparison = -1;
                                  } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                      comparison = 1;
                                  }
                                  return comparison;
                              })
                            : [];
                    const subTreeCoursePO = generatedReport.courseTreePO.find(
                        (reportElement) =>
                            reportElement.typeId &&
                            reportElement.typeId.toString() ===
                                subTreeCOTreeValue.coTreeId.toString(),
                    );
                    const subTreePLO =
                        subTreeCoursePO && subTreeCoursePO.plo
                            ? subTreeCoursePO.plo.map((ploElement) => {
                                  const percentageLevel = subtreeSortedValues.find(
                                      (levelValueElement) =>
                                          levelValuesAs === RANGE
                                              ? parseFloat(levelValueElement.min) <=
                                                    parseInt(ploElement.percentage) &&
                                                parseFloat(levelValueElement.max) >=
                                                    parseInt(ploElement.percentage)
                                              : levelValueElement.condition === EQUAL
                                              ? parseInt(levelValueElement.percentage) <
                                                parseInt(ploElement.percentage)
                                              : parseInt(levelValueElement.percentage) <=
                                                parseInt(ploElement.percentage),
                                  );
                                  return {
                                      _id: ploElement._id,
                                      percentage: parseFloat(ploElement.percentage),
                                      level:
                                          percentageLevel && percentageLevel.level
                                              ? percentageLevel.level
                                              : '',
                                  };
                              })
                            : [];
                    for (ploSubTreeElement of subTreePLO) {
                        const ploIndex = treePLO.findIndex(
                            (plosElement) =>
                                plosElement._id.toString() === ploSubTreeElement._id.toString(),
                        );
                        const ploPercentage =
                            ploSubTreeElement.percentage === null
                                ? ploSubTreeElement.percentage
                                : (ploSubTreeElement.percentage * subTreeElement.weightage) / 100;
                        if (ploIndex == -1)
                            treePLO.push({
                                _id: ploSubTreeElement._id.toString(),
                                percentage: ploPercentage,
                                count: ploPercentage === null ? 0 : 1,
                                level: '',
                            });
                        else if (ploPercentage !== null && !!ploPercentage) {
                            treePLO[ploIndex].percentage += ploPercentage;
                            treePLO[ploIndex].count++;
                        }
                    }
                    const nodeAttainments = clone(
                        libraryList.filter(
                            (libraryElement) =>
                                libraryElement.assessmentTypeId &&
                                (subTreeElement.nodeName
                                    ? libraryElement.assessmentTypeId.toString() ===
                                          subTreeElement.typeId.toString() &&
                                      (treeElement.typeId.toString() ===
                                          libraryElement.typeId.toString() ||
                                          treeElement.typeId.toString() ===
                                              libraryElement.subTypeId.toString())
                                    : libraryElement.assessmentTypeId.toString() ===
                                          subTreeElement.typeId.toString() &&
                                      treeElement.typeId.toString() ===
                                          libraryElement.typeId.toString() &&
                                      subTreeElement.typeId.toString() ===
                                          libraryElement.subTypeId.toString()),
                        ),
                    );
                    subTreeList.push({
                        _id: subTreeElement._id,
                        typeName: subTreeElement.typeName,
                        typeId: subTreeElement.typeId,
                        nodeId: subTreeElement.nodeId,
                        nodeName: subTreeElement.nodeName,
                        weightage: subTreeElement.weightage,
                        // node: [],
                        attainments: nodeAttainments,
                        plo: subTreePLO.length ? subTreePLO : coursePLOList,
                        coTree: courseTree,
                    });
                } else {
                    if (!subTreeElement.nodeName) {
                        const subTreePLO = [];
                        for (nodeElement of subTreeElement.node) {
                            let nodePlo = [];
                            const nodeAttainments = clone(
                                libraryList.filter(
                                    (libraryElement) =>
                                        libraryElement.assessmentTypeId &&
                                        libraryElement.assessmentTypeId.toString() ===
                                            nodeElement.typeId.toString() &&
                                        treeElement.typeId.toString() ===
                                            libraryElement.typeId.toString() &&
                                        subTreeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString(),
                                ),
                            );
                            const nodeLevel =
                                subTreeLevel && subTreeLevel.node
                                    ? subTreeLevel.node.find(
                                          (nodeLevelElement) =>
                                              nodeLevelElement.typeId.toString() ===
                                              nodeElement.typeId.toString(),
                                      )
                                    : undefined;
                            const nodeSortedValues =
                                nodeLevel && nodeLevel.levelValues
                                    ? nodeLevel.levelValues.sort((a, b) => {
                                          let comparison = 0;
                                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                              comparison = -1;
                                          } else if (
                                              parseInt(a.percentage) < parseInt(b.percentage)
                                          ) {
                                              comparison = 1;
                                          }
                                          return comparison;
                                      })
                                    : [];
                            // Need to Check is Course Tree Flow Or Attainment flow
                            const nodeCOTreeValue = programCOs
                                ? programCOs.find(
                                      (coTreeIdElement) =>
                                          coTreeIdElement.typeId.toString() ===
                                          nodeElement.typeId.toString(),
                                  )
                                : undefined;
                            if (nodeCOTreeValue) {
                                const coursePO = generatedReport.courseTreePO.find(
                                    (reportElement) =>
                                        reportElement.typeId &&
                                        reportElement.typeId.toString() ===
                                            nodeCOTreeValue.coTreeId.toString(),
                                );
                                nodePlo =
                                    coursePO && coursePO.plo
                                        ? coursePO.plo.map((ploElement) => {
                                              const percentageLevel = nodeSortedValues.find(
                                                  (levelValueElement) =>
                                                      levelValuesAs === RANGE
                                                          ? parseFloat(levelValueElement.min) <=
                                                                parseInt(ploElement.percentage) &&
                                                            parseFloat(levelValueElement.max) >=
                                                                parseInt(ploElement.percentage)
                                                          : levelValueElement.condition === EQUAL
                                                          ? parseInt(levelValueElement.percentage) <
                                                            parseInt(ploElement.percentage)
                                                          : parseInt(
                                                                levelValueElement.percentage,
                                                            ) <= parseInt(ploElement.percentage),
                                              );
                                              return {
                                                  _id: ploElement._id,
                                                  percentage: parseFloat(ploElement.percentage),
                                                  level:
                                                      percentageLevel && percentageLevel.level
                                                          ? percentageLevel.level
                                                          : '',
                                              };
                                          })
                                        : [];
                            } else {
                                for (nodeAttainmentElement of nodeAttainments) {
                                    if (
                                        assessmentIds &&
                                        assessmentIds.find(
                                            (assessmentIdElement) =>
                                                assessmentIdElement.toString() ===
                                                nodeAttainmentElement._assessment_id.toString(),
                                        )
                                    ) {
                                        for (nodeAttainmentPLOElement of nodeAttainmentElement.plo) {
                                            for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                                if (
                                                    assignmentQuestionElement.outComeIds.find(
                                                        (outComeIdElement) =>
                                                            outComeIdElement.toString() ===
                                                            nodeAttainmentPLOElement._id.toString(),
                                                    )
                                                ) {
                                                    const nodePloIndex = nodePlo.findIndex(
                                                        (plosElement) =>
                                                            plosElement._id.toString() ===
                                                            nodeAttainmentPLOElement._id.toString(),
                                                    );
                                                    if (nodePloIndex == -1)
                                                        nodePlo.push({
                                                            _id: nodeAttainmentPLOElement._id.toString(),
                                                            percentage:
                                                                nodeAttainmentPLOElement.percentage,
                                                            count:
                                                                nodeAttainmentPLOElement.percentage ===
                                                                null
                                                                    ? 0
                                                                    : 1,
                                                            level: '',
                                                        });
                                                    else {
                                                        nodePlo[nodePloIndex].percentage +=
                                                            nodeAttainmentPLOElement.percentage;
                                                        nodePlo[nodePloIndex].count++;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                for (nodePloElement of nodePlo)
                                    if (nodePloElement.count) {
                                        nodePloElement.percentage /= nodePloElement.count;
                                        const percentageLevel = nodeSortedValues.find(
                                            (levelValueElement) =>
                                                levelValuesAs === RANGE
                                                    ? parseFloat(levelValueElement.min) <=
                                                          parseInt(nodePloElement.percentage) &&
                                                      parseFloat(levelValueElement.max) >=
                                                          parseInt(nodePloElement.percentage)
                                                    : levelValueElement.condition === EQUAL
                                                    ? parseInt(levelValueElement.percentage) <
                                                      parseInt(nodePloElement.percentage)
                                                    : parseInt(levelValueElement.percentage) <=
                                                      parseInt(nodePloElement.percentage),
                                        );
                                        nodePloElement.level =
                                            percentageLevel && percentageLevel.level
                                                ? percentageLevel.level
                                                : '';
                                    }
                            }
                            nodeList.push({
                                _id: nodeElement._id,
                                typeName: nodeElement.typeName,
                                typeId: nodeElement.typeId,
                                nodeId: nodeElement.nodeId,
                                weightage: nodeElement.weightage,
                                nodeName: nodeElement.nodeName,
                                plo: nodePlo,
                                attainments: nodeAttainments,
                                coTree: courseSubTree,
                            });
                        }
                        for (nodeListElement of nodeList) {
                            for (nodeAttainmentPLOElement of nodeListElement.plo) {
                                const ploIndex = subTreePLO.findIndex(
                                    (plosElement) =>
                                        plosElement._id.toString() ===
                                        nodeAttainmentPLOElement._id.toString(),
                                );
                                if (ploIndex == -1)
                                    subTreePLO.push({
                                        _id: nodeAttainmentPLOElement._id.toString(),
                                        percentage:
                                            nodeAttainmentPLOElement.percentage === null
                                                ? nodeAttainmentPLOElement.percentage
                                                : (nodeAttainmentPLOElement.percentage *
                                                      nodeListElement.weightage) /
                                                  100,
                                        count: nodeAttainmentPLOElement.percentage === null ? 0 : 1,
                                        level: '',
                                    });
                                else {
                                    subTreePLO[ploIndex].percentage +=
                                        nodeAttainmentPLOElement.percentage === null
                                            ? nodeAttainmentPLOElement.percentage
                                            : (nodeAttainmentPLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100;
                                    subTreePLO[ploIndex].count++;
                                }
                            }
                        }

                        for (ploSubTreeElement of subTreePLO) {
                            if (ploSubTreeElement.percentage !== null)
                                ploSubTreeElement.percentage /= ploSubTreeElement.count;
                            // Tree
                            const subtreeSortedValues =
                                subTreeLevel && subTreeLevel.levelValues
                                    ? subTreeLevel.levelValues.sort((a, b) => {
                                          let comparison = 0;
                                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                              comparison = -1;
                                          } else if (
                                              parseInt(a.percentage) < parseInt(b.percentage)
                                          ) {
                                              comparison = 1;
                                          }
                                          return comparison;
                                      })
                                    : [];
                            const percentageLevel = subtreeSortedValues.find((levelValueElement) =>
                                levelValuesAs === RANGE
                                    ? parseFloat(levelValueElement.min) <=
                                          parseInt(ploSubTreeElement.percentage) &&
                                      parseFloat(levelValueElement.max) >=
                                          parseInt(ploSubTreeElement.percentage)
                                    : levelValueElement.condition === EQUAL
                                    ? parseInt(levelValueElement.percentage) <
                                      parseInt(ploSubTreeElement.percentage)
                                    : parseInt(levelValueElement.percentage) <=
                                      parseInt(ploSubTreeElement.percentage),
                            );
                            ploSubTreeElement.level =
                                percentageLevel && percentageLevel.level
                                    ? percentageLevel.level
                                    : '';

                            const ploPercentage =
                                ploSubTreeElement.percentage === null
                                    ? ploSubTreeElement.percentage
                                    : (ploSubTreeElement.percentage * subTreeElement.weightage) /
                                      100;
                            const ploIndex = treePLO.findIndex(
                                (plosElement) =>
                                    plosElement._id.toString() === ploSubTreeElement._id.toString(),
                            );
                            if (ploIndex == -1)
                                treePLO.push({
                                    _id: ploSubTreeElement._id.toString(),
                                    percentage: ploPercentage,
                                    count: ploPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (ploPercentage !== null && !!ploPercentage) {
                                treePLO[ploIndex].percentage += ploPercentage;
                                treePLO[ploIndex].count++;
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            weightage: subTreeElement.weightage,
                            node: nodeList,
                            plo: subTreePLO.length ? subTreePLO : coursePLOList,
                            coTree: nodeList.length ? undefined : courseTree,
                        });
                    } else {
                        let subTreePLO = [];
                        const nodeAttainments = clone(
                            libraryList.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        subTreeElement.typeId.toString() &&
                                    (treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() ||
                                        treeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString()),
                            ),
                        );
                        const subtreeSortedValues =
                            subTreeLevel && subTreeLevel.levelValues
                                ? subTreeLevel.levelValues.sort((a, b) => {
                                      let comparison = 0;
                                      if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                          comparison = -1;
                                      } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                          comparison = 1;
                                      }
                                      return comparison;
                                  })
                                : [];
                        // Need to Check is Course Tree Flow Or Attainment flow
                        const subTreeCOTreeValue = programCOs
                            ? programCOs.find(
                                  (coTreeIdElement) =>
                                      coTreeIdElement.typeId.toString() ===
                                      subTreeElement.typeId.toString(),
                              )
                            : undefined;
                        if (subTreeCOTreeValue) {
                            const subTreeCoursePO = generatedReport.courseTreePO.find(
                                (reportElement) =>
                                    reportElement.typeId &&
                                    reportElement.typeId.toString() ===
                                        subTreeCOTreeValue.coTreeId.toString(),
                            );
                            subTreePLO =
                                subTreeCoursePO && subTreeCoursePO.plo
                                    ? subTreeCoursePO.plo.map((ploElement) => {
                                          const percentageLevel = subtreeSortedValues.find(
                                              (levelValueElement) =>
                                                  levelValuesAs === RANGE
                                                      ? parseFloat(levelValueElement.min) <=
                                                            parseInt(ploElement.percentage) &&
                                                        parseFloat(levelValueElement.max) >=
                                                            parseInt(ploElement.percentage)
                                                      : levelValueElement.condition === EQUAL
                                                      ? parseInt(levelValueElement.percentage) <
                                                        parseInt(ploElement.percentage)
                                                      : parseInt(levelValueElement.percentage) <=
                                                        parseInt(ploElement.percentage),
                                          );
                                          return {
                                              _id: ploElement._id,
                                              percentage: parseFloat(ploElement.percentage),
                                              level:
                                                  percentageLevel && percentageLevel.level
                                                      ? percentageLevel.level
                                                      : '',
                                          };
                                      })
                                    : [];
                        } else {
                            for (nodeAttainmentElement of nodeAttainments) {
                                for (nodeAttainmentPLOElement of nodeAttainmentElement.plo) {
                                    const ploIndex = subTreePLO.findIndex(
                                        (plosElement) =>
                                            plosElement._id.toString() ===
                                            nodeAttainmentPLOElement._id.toString(),
                                    );
                                    if (ploIndex == -1) {
                                        subTreePLO.push({
                                            _id: nodeAttainmentPLOElement._id.toString(),
                                            percentage: nodeAttainmentPLOElement.percentage,
                                            count:
                                                nodeAttainmentPLOElement.percentage === null
                                                    ? 0
                                                    : 1,
                                            level: '',
                                        });
                                    } else if (nodeAttainmentPLOElement.percentage !== null) {
                                        subTreePLO[ploIndex].percentage =
                                            subTreePLO[ploIndex].percentage === null
                                                ? nodeAttainmentPLOElement.percentage
                                                : subTreePLO[ploIndex].percentage +
                                                  nodeAttainmentPLOElement.percentage;
                                        subTreePLO[ploIndex].count++;
                                    }
                                    const percentageLevel = subtreeSortedValues.find(
                                        (levelValueElement) =>
                                            levelValuesAs === RANGE
                                                ? parseFloat(levelValueElement.min) <=
                                                      parseInt(
                                                          nodeAttainmentPLOElement.percentage,
                                                      ) &&
                                                  parseFloat(levelValueElement.max) >=
                                                      parseInt(nodeAttainmentPLOElement.percentage)
                                                : levelValueElement.condition === EQUAL
                                                ? parseInt(levelValueElement.percentage) <
                                                  parseInt(nodeAttainmentPLOElement.percentage)
                                                : parseInt(levelValueElement.percentage) <=
                                                  parseInt(nodeAttainmentPLOElement.percentage),
                                    );
                                    nodeAttainmentPLOElement.level =
                                        percentageLevel && percentageLevel.level
                                            ? percentageLevel.level
                                            : '';
                                }
                            }

                            for (ploSubTreeElement of subTreePLO) {
                                if (ploSubTreeElement.percentage !== null)
                                    ploSubTreeElement.percentage /= ploSubTreeElement.count;
                                // Tree
                                const percentageLevel = subtreeSortedValues.find(
                                    (levelValueElement) =>
                                        levelValuesAs === RANGE
                                            ? parseFloat(levelValueElement.min) <=
                                                  parseInt(ploSubTreeElement.percentage) &&
                                              parseFloat(levelValueElement.max) >=
                                                  parseInt(ploSubTreeElement.percentage)
                                            : levelValueElement.condition === EQUAL
                                            ? parseInt(levelValueElement.percentage) <
                                              parseInt(ploSubTreeElement.percentage)
                                            : parseInt(levelValueElement.percentage) <=
                                              parseInt(ploSubTreeElement.percentage),
                                );
                                ploSubTreeElement.level =
                                    percentageLevel && percentageLevel.level
                                        ? percentageLevel.level
                                        : '';
                                const ploPercentage =
                                    ploSubTreeElement.percentage === null
                                        ? ploSubTreeElement.percentage
                                        : (ploSubTreeElement.percentage *
                                              subTreeElement.weightage) /
                                          100;

                                const ploIndex = treePLO.findIndex(
                                    (plosElement) =>
                                        plosElement._id.toString() ===
                                        ploSubTreeElement._id.toString(),
                                );
                                if (ploIndex == -1)
                                    treePLO.push({
                                        _id: ploSubTreeElement._id.toString(),
                                        percentage: ploPercentage,
                                        count: ploPercentage === null ? 0 : 1,
                                        level: '',
                                    });
                                else if (ploPercentage !== null && !!ploPercentage) {
                                    treePLO[ploIndex].percentage += ploPercentage;
                                    treePLO[ploIndex].count++;
                                }
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            nodeId: subTreeElement.nodeId,
                            nodeName: subTreeElement.nodeName,
                            weightage: subTreeElement.weightage,
                            attainments: nodeAttainments,
                            plo: subTreePLO.length ? subTreePLO : coursePLOList,
                            coTree: courseTree,
                        });
                    }
                }
            }
            const treeSortedValues =
                levelTree && levelTree.levelValues
                    ? levelTree.levelValues.sort((a, b) => {
                          let comparison = 0;
                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                              comparison = -1;
                          } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                              comparison = 1;
                          }
                          return comparison;
                      })
                    : [];
            if (treeElement.subTree.length) {
                // Need to Check is Course Tree Flow Or Attainment flow
                const nodeCOTreeValue = programCOs
                    ? programCOs.find(
                          (coTreeIdElement) =>
                              coTreeIdElement.typeId.toString() === treeElement.typeId.toString(),
                      )
                    : undefined;
                if (nodeCOTreeValue) {
                    const coursePO = generatedReport.courseTreePO.find(
                        (reportElement) =>
                            reportElement.typeId &&
                            reportElement.typeId.toString() === nodeCOTreeValue.coTreeId.toString(),
                    );
                    treePLO =
                        coursePO && coursePO.plo
                            ? coursePO.plo.map((ploElement) => {
                                  const percentageLevel = treeSortedValues.find(
                                      (levelValueElement) =>
                                          levelValuesAs === RANGE
                                              ? parseFloat(levelValueElement.min) <=
                                                    parseInt(ploElement.percentage) &&
                                                parseFloat(levelValueElement.max) >=
                                                    parseInt(ploElement.percentage)
                                              : levelValueElement.condition === EQUAL
                                              ? parseInt(levelValueElement.percentage) <
                                                parseInt(ploElement.percentage)
                                              : parseInt(levelValueElement.percentage) <=
                                                parseInt(ploElement.percentage),
                                  );
                                  return {
                                      _id: ploElement._id,
                                      percentage: parseFloat(ploElement.percentage),
                                      level:
                                          percentageLevel && percentageLevel.level
                                              ? percentageLevel.level
                                              : '',
                                  };
                              })
                            : [];
                }
            }
            for (ploTreeElement of treePLO) {
                // OverAll PLO
                const percentageLevel = treeSortedValues.find((levelValueElement) =>
                    levelValuesAs === RANGE
                        ? parseFloat(levelValueElement.min) <=
                              parseInt(ploTreeElement.percentage) &&
                          parseFloat(levelValueElement.max) >= parseInt(ploTreeElement.percentage)
                        : levelValueElement.condition === EQUAL
                        ? parseInt(levelValueElement.percentage) <
                          parseInt(ploTreeElement.percentage)
                        : parseInt(levelValueElement.percentage) <=
                          parseInt(ploTreeElement.percentage),
                );
                ploTreeElement.level =
                    percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                const ploPercentage =
                    ploTreeElement.percentage === null
                        ? ploTreeElement.percentage
                        : (ploTreeElement.percentage * treeElement.weightage) / 100;

                const ploIndex = overAllPLO.findIndex(
                    (plosElement) => plosElement._id.toString() === ploTreeElement._id.toString(),
                );
                if (ploIndex === -1) {
                    overAllPLO.push({
                        _id: ploTreeElement._id.toString(),
                        percentage: ploPercentage,
                        count: 1,
                        level: '',
                    });
                } else if (ploPercentage !== null && !!ploPercentage) {
                    overAllPLO[ploIndex].percentage += ploPercentage;
                    overAllPLO[ploIndex].count++;
                }
            }
            attainmentNodeList.push({
                _id: treeElement._id,
                typeName: treeElement.typeName,
                typeId: treeElement.typeId,
                weightage: treeElement.weightage,
                subTree: subTreeList,
                plo: treePLO.length ? treePLO : coursePLOList,
                coTree:
                    subTreeList && !subTreeList.length
                        ? {
                              isActive: true,
                              typeId: generatedReport.courseTreePO.find(
                                  (reportElement) =>
                                      reportElement.typeName.toString() === 'Overall Attainment',
                              ).typeId,
                              typeName: 'Overall Attainment',
                              tree: coAttainmentPlanOutCome.tree,
                          }
                        : [],
            });
        }
        if (overAllPLO.length) {
            const overAllAttainmentIndex = attainmentNodeList.findIndex(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            const attElement = attainmentOutCome.levels.find(
                (attainmentNodeListElement) =>
                    attainmentNodeListElement.typeName === 'Overall Attainment',
            );
            if (overAllAttainmentIndex !== -1) {
                const overAllSortedValues =
                    attElement && attElement.levelValues
                        ? attElement.levelValues.sort((a, b) => {
                              let comparison = 0;
                              if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                  comparison = -1;
                              } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                  comparison = 1;
                              }
                              return comparison;
                          })
                        : [];
                for (overAllPLOElement of overAllPLO) {
                    const percentageLevel = overAllSortedValues.find((levelValueElement) =>
                        levelValuesAs === RANGE
                            ? parseFloat(levelValueElement.min) <=
                                  parseInt(overAllPLOElement.percentage) &&
                              parseFloat(levelValueElement.max) >=
                                  parseInt(overAllPLOElement.percentage)
                            : levelValueElement.condition === EQUAL
                            ? parseInt(levelValueElement.percentage) <
                              parseInt(overAllPLOElement.percentage)
                            : parseInt(levelValueElement.percentage) <=
                              parseInt(overAllPLOElement.percentage),
                    );
                    overAllPLOElement.level =
                        percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                }
                attainmentNodeList[overAllAttainmentIndex].plo = overAllPLO;
            }
        }
        if (assessmentIds) {
            const coAssessmentIndex = programCOs.findIndex(
                (programCoElement) => programCoElement.mode === 'assessment',
            );
            if (coAssessmentIndex === -1)
                programCOs.push({
                    mode: 'assessment',
                    assessmentIds: assessmentIds.map((assessmentElement) =>
                        convertToMongoObjectId(assessmentElement),
                    ),
                });
            else
                programCOs[coAssessmentIndex].assessmentIds = assessmentIds.map(
                    (assessmentElement) => convertToMongoObjectId(assessmentElement),
                );
            await attainmentManagementSchema.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    $set: { programSelections: programCOs },
                },
            );
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                programPLO: curriculumPLOs,
                attainmentNodeList,
                attainmentLevel: attainmentOutCome.levels,
                attainmentValuesAs: attainmentOutCome.valuesAs,
                attainmentTargetBenchMark: attainmentOutCome.targetBenchMark,
                levelColors: attainmentOutCome.levelColors,
                manageTargetBenchMark: attainmentOutCome.manageTargetBenchMark
                    ? attainmentOutCome.manageTargetBenchMark
                    : {},
                assessmentIds: assessmentIds || [],
                coTreeIds: programCOs,
                curriculumCourses: programAttainmentList.programCourseList
                    ? programAttainmentList.programCourseList.filter(
                          (courseElement) => courseElement.status,
                      ).length
                    : 0,
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const outComeCalculations = ({
    poId,
    mapping_type,
    courseImpactValue,
    overAllLevelValues,
    levelValuesAs,
    impactCalculation,
    courseCLOs,
}) => {
    const coursePLOPercentage = { percentage: 0, level: '' };
    let courseImpactPercentageValue = 0;
    const courseImpactPercentageValues = [];
    if (mapping_type === 'impact') {
        for (const courseOverAllCLOElement of courseCLOs) {
            if (courseImpactValue.min >= parseInt(courseOverAllCLOElement.percentage)) {
                courseImpactPercentageValue++;
                courseImpactPercentageValues.push({
                    values: 1,
                    cloId: courseOverAllCLOElement._id,
                });
            } else if (courseImpactValue.max <= parseInt(courseOverAllCLOElement.percentage)) {
                courseImpactPercentageValue += 3;
                courseImpactPercentageValues.push({
                    values: 3,
                    cloId: courseOverAllCLOElement._id,
                });
            } else {
                const percentageLevel = (
                    levelValuesAs !== RANGE
                        ? overAllLevelValues.sort((a, b) => {
                              let comparison = 0;
                              if (parseInt(a.percentage) > parseInt(b.percentage)) {
                                  comparison = -1;
                              } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                                  comparison = 1;
                              }
                              return comparison;
                          })
                        : overAllLevelValues
                ).findIndex((levelValueElement) =>
                    levelValuesAs === RANGE
                        ? parseFloat(levelValueElement.min) <=
                              parseInt(courseOverAllCLOElement.percentage) &&
                          parseFloat(levelValueElement.max) >=
                              parseInt(courseOverAllCLOElement.percentage)
                        : levelValueElement.condition === EQUAL
                        ? parseInt(levelValueElement.percentage) <
                          parseInt(courseOverAllCLOElement.percentage)
                        : parseInt(levelValueElement.percentage) <=
                          parseInt(courseOverAllCLOElement.percentage),
                );
                if (percentageLevel !== -1) {
                    const x = courseOverAllCLOElement.percentage;
                    const x1 =
                        levelValuesAs === RANGE
                            ? overAllLevelValues[percentageLevel - 1].max
                            : overAllLevelValues[percentageLevel - 1].percentage;
                    const x2 =
                        levelValuesAs === RANGE
                            ? overAllLevelValues[percentageLevel].max
                            : overAllLevelValues[percentageLevel].percentage;
                    const y1 = overAllLevelValues[percentageLevel - 1].levelValue;
                    const y2 = overAllLevelValues[percentageLevel].levelValue;
                    const cloImpactLevelValue = y1 + (x - x1) * ((y2 - y1) / (x2 - x1));
                    courseImpactPercentageValue += cloImpactLevelValue;
                    courseImpactPercentageValues.push({
                        values: cloImpactLevelValue,
                        cloId: courseOverAllCLOElement._id,
                    });
                }
            }
        }
    } else {
        coursePLOPercentage.percentage =
            courseCLOs.reduce((n, cloElement) => n + parseFloat(cloElement.percentage), 0) /
            courseCLOs.length;
    }
    const poOutcome = impactCalculation.values / impactCalculation.count;
    const coInLevel = courseImpactPercentageValue / courseImpactPercentageValues.length;
    const coursePOAttainment = coInLevel * (poOutcome / 3);
    const coursePoPercentage = (coursePOAttainment * 100) / overAllLevelValues.length;
    // if (courseOverAllPlo.percentage === null) {
    //     courseOverAllPlo.percentage = coursePoPercentage || 0;
    //     courseOverAllPlo.count = 1;
    // } else {
    //     courseOverAllPlo.percentage += coursePoPercentage || 0;
    //     courseOverAllPlo.count++;
    // }
    // const poIndex = curriculumPLOStruct.findIndex(
    //     (poElement) => poElement._id.toString() === ploElement._id.toString(),
    // );
    // if (poIndex !== -1 && coursePOAttainment) {
    //     curriculumPLOStruct[poIndex].value += coursePOAttainment;
    //     curriculumPLOStruct[poIndex].count++;
    // }
    return {
        _id: poId,
        percentage: coursePoPercentage,
        coursePOAttainment,
    };
};

const generateProgramCourseAttainmentReport = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { attainmentId, programId, institutionCalendarId, term, outCome } = query;
        const reportStruct = {
            percentage: 0,
            level: '',
        };
        const institutionCalendarData = await getInstitutionCalendars({
            _institution_id,
            institutionCalendarId,
        });
        const attainmentList = await attainmentManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(attainmentId),
                },
                {
                    _curriculum_id: 1,
                    curriculumName: 1,
                    evaluationPlan: 1,
                    attainmentLevel: 1,
                    programCourseList: 1,
                    regulationYear: 1,
                    regulationName: 1,
                },
            )
            .lean();
        if (!attainmentList) return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const attainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === outCome,
        );
        const attainmentPOCourse = attainmentList.programCourseList
            ? attainmentList.programCourseList
            : [];
        // const attainmentPOCourse = [];
        const levelValuesAs = attainmentOutCome.valuesAs;
        if (
            !attainmentOutCome ||
            !attainmentPlanOutCome ||
            !attainmentList._curriculum_id ||
            !levelValuesAs
        )
            return { statusCode: 200, message: 'ATTAINMENT_NOT_FOUND' };
        const coAttainmentPlanOutCome = attainmentList.evaluationPlan.find(
            (attainmentElement) => attainmentElement.outComeType === 'clo',
        );
        const coAttainmentOutCome = attainmentList.attainmentLevel.find(
            (attainmentElement) => attainmentElement.outComeType === 'clo',
        );
        if (!coAttainmentOutCome || !coAttainmentPlanOutCome)
            return { statusCode: 200, message: 'CLO_ATTAINMENT_NOT_FOUND' };
        const overAllLevelValues = attainmentOutCome.levels.find(
            (levelElement) => levelElement.typeName === 'Overall Attainment',
        )
            ? clone(
                  attainmentOutCome.levels.find(
                      (levelElement) => levelElement.typeName === 'Overall Attainment',
                  ).levelValues,
              )
            : [];
        if (overAllLevelValues.length === 0)
            return { statusCode: 200, message: 'ATTAINMENT_PLO_NOT_FOUND' };

        let i = 1;
        for (overAllLevelValueElement of overAllLevelValues) {
            overAllLevelValueElement.levelValue = i;
            i++;
        }
        const overAllLevelValuesWithOutSort = clone(overAllLevelValues);
        const curriculumYearLevelData = await getProgramPLOWithCLO({
            _institution_id,
            programId,
            institutionCalendarId,
            curriculumId: attainmentList._curriculum_id,
        });
        if (!curriculumYearLevelData)
            return { statusCode: 410, message: 'Program Calendar/Curriculam Data Not Found' };
        const curriculumPLOs = clone(
            curriculumYearLevelData.curriculumPLO.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    no: cloElement.no,
                    name: cloElement.name,
                    ...reportStruct,
                };
            }),
        );
        const curriculumPLOStruct = curriculumPLOs.map((ploElement) => {
            return {
                _id: ploElement._id,
                value: 0,
                count: 0,
                // ...reportStruct,
            };
        });
        let yearDetails = curriculumYearLevelData.course
            .map((calendarYearElement) => calendarYearElement.year.toString())
            .reverse();
        yearDetails = [...new Set(yearDetails)];
        let attainmentLibraryQuery = [];
        let calendarIndex = 0;
        for (yearElement of yearDetails) {
            const courseList = curriculumYearLevelData.course
                .filter(
                    (courseElement) =>
                        // courseElement._course_id.toString() === '639054ceb6505c9b03be2e0a' &&
                        courseElement.year === yearElement &&
                        courseElement.term === term &&
                        (attainmentPOCourse.find(
                            (poCourseElement) =>
                                poCourseElement.levelNo === courseElement.levelNo &&
                                poCourseElement.term === courseElement.term &&
                                poCourseElement.courseId.toString() ===
                                    courseElement._course_id.toString(),
                        )
                            ? attainmentPOCourse.find(
                                  (poCourseElement) =>
                                      poCourseElement.levelNo === courseElement.levelNo &&
                                      poCourseElement.term === courseElement.term &&
                                      poCourseElement.courseId.toString() ===
                                          courseElement._course_id.toString(),
                              ).status
                            : true),
                )
                .map((courseElement) => {
                    return {
                        level: courseElement.levelNo,
                        term: courseElement.term,
                        _course_id: convertToMongoObjectId(courseElement._course_id),
                        _institution_calendar_id: convertToMongoObjectId(
                            institutionCalendarData[calendarIndex]._id,
                        ),
                    };
                });
            if (courseList && courseList.length) {
                attainmentLibraryQuery = [...attainmentLibraryQuery, ...courseList];
            }
            calendarIndex =
                calendarIndex + 1 > institutionCalendarData.length - 1 ? 0 : calendarIndex + 1;
        }
        attainmentLibraryQuery = attainmentLibraryQuery.sort((a, b) => {
            let comparison = 0;
            if (a.level > b.level) {
                comparison = -1;
            } else if (a.level < b.level) {
                comparison = 1;
            }
            return comparison;
        });
        const courseIds = attainmentLibraryQuery.map((courseElement) =>
            convertToMongoObjectId(courseElement._course_id),
        );
        // Course Attainment Settings
        const courseAttainmentSettings = await attainmentManagementSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                    type: 'course',
                    _program_id: convertToMongoObjectId(programId),
                    $or: attainmentLibraryQuery.map((courseElement) => {
                        return {
                            level: courseElement.levelNo,
                            term: courseElement.term,
                            _course_id: convertToMongoObjectId(courseElement._course_id),
                        };
                    }),
                },
                { evaluationPlan: 1, _course_id: 1, levelNo: 1, term: 1 },
            )
            .lean();
        const courseCLO = await getCoursesCLOs({ _institution_id, courseIds, programId });
        // Course LibraryData
        let libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                $or: attainmentLibraryQuery,
            },
            {
                _course_id: 1,
                _assessment_id: 1,
                assessmentName: 1,
                assessmentMark: 1,
                studentDetails: 1,
                questionMarks: 1,
                benchMark: 1,
                noQuestions: 1,
                questionOutcome: 1,
                typeName: 1,
                typeId: 1,
                subTypeName: 1,
                subTypeId: 1,
                assessmentTypeId: 1,
                assessmentTypeName: 1,
                updatedAt: 1,
            },
        );
        libraryData = clone(libraryData);
        const libraryList = [];
        for (libraryElement of libraryData) {
            for (questionElement of libraryElement.questionMarks) {
                questionElement.attendedCount = 0;
                questionElement.passedCount = 0;
                for (studentElement of libraryElement.studentDetails) {
                    const studentQuestion = studentElement.studentMarks.find(
                        (studentQuestionMarkElement) =>
                            studentQuestionMarkElement.questionName ===
                            questionElement.questionName,
                    );
                    if (studentQuestion && studentQuestion.mark !== null) {
                        questionElement.attendedCount++;
                        if (studentQuestion.mark >= questionElement.attainmentBenchMark)
                            questionElement.passedCount++;
                    }
                }
                if (questionElement.attendedCount > 0)
                    questionElement.average = (
                        questionElement.passedCount / questionElement.attendedCount
                    ).toFixed(4);
            }
            const courseWiseClo = courseCLO.find(
                (courseElement) =>
                    courseElement._course_id.toString() === libraryElement._course_id.toString(),
            );
            const assessmentClo = courseWiseClo
                ? clone(
                      courseWiseClo.clo.map((cloElement) => {
                          return {
                              _id: cloElement,
                              ...reportStruct,
                          };
                      }),
                  )
                : [];
            for (courseCloElement of assessmentClo) {
                const cloQuestions = libraryElement.questionMarks.filter(
                    (questionElement) =>
                        questionElement.attendedCount > 0 &&
                        questionElement.outComeIds.find(
                            (outComeIdElement) =>
                                outComeIdElement.toString() === courseCloElement._id.toString(),
                        ),
                );
                courseCloElement.percentage =
                    cloQuestions.reduce((n, cloElement) => n + parseFloat(cloElement.average), 0) /
                    cloQuestions.length;
                if (courseCloElement.percentage)
                    courseCloElement.percentage = courseCloElement.percentage.toFixed(4) * 100;
            }
            libraryElement.clo = assessmentClo.map((cloElement) => {
                return {
                    _id: cloElement._id,
                    percentage: cloElement.percentage,
                    level: '',
                };
            });
            libraryList.push({
                _id: libraryElement._id,
                _course_id: libraryElement._course_id,
                _assessment_id: libraryElement._assessment_id,
                assessmentName: libraryElement.assessmentName,
                assessmentMark: libraryElement.assessmentMark,
                typeName: libraryElement.typeName,
                typeId: libraryElement.typeId,
                subTypeName: libraryElement.subTypeName,
                subTypeId: libraryElement.subTypeId,
                assessmentTypeName: libraryElement.assessmentTypeName,
                assessmentTypeId: libraryElement.assessmentTypeId,
                questionOutcome: libraryElement.questionOutcome,
                questionMarks: libraryElement.questionMarks,
                noQuestions: libraryElement.noQuestions,
                benchMark: libraryElement.benchMark,
                clo: libraryElement.clo,
                updatedAt: libraryElement.updatedAt,
            });
        }
        // return { data: { libraryData, libraryList } };
        // return { data: { attainmentLibraryQuery, libraryList } };
        // return { data: { attainmentPlanOutCome, courseAttainmentSettings } };
        const courseCOReport = [];
        for (courseIdElement of attainmentLibraryQuery) {
            const attainmentNodeList = [];
            const courseAttainment = libraryList.filter(
                (libraryElement) =>
                    libraryElement._course_id.toString() === courseIdElement._course_id.toString(),
            );
            const overAllCLO = [];
            const courseAttainmentPlan = courseAttainmentSettings.find(
                (courseSettingElement) =>
                    courseSettingElement._course_id.toString() ===
                        courseIdElement._course_id.toString() &&
                    courseSettingElement.levelNo === courseIdElement.level &&
                    courseSettingElement.term === courseIdElement.term,
            );
            const courseAttainmentOutcome =
                courseAttainmentPlan && courseAttainmentPlan.evaluationPlan
                    ? courseAttainmentPlan.evaluationPlan.find(
                          (evaluationPlanElement) => evaluationPlanElement.outComeType === 'clo',
                      )
                    : undefined;
            for (treeElement of courseAttainmentOutcome
                ? courseAttainmentOutcome.tree
                : coAttainmentPlanOutCome.tree) {
                const subTreeList = [];
                const treeCLO = [];
                // const levelTree = coAttainmentOutCome.levels.find((levelElement) =>
                //     levelElement.typeId
                //         ? levelElement.typeId.toString() === treeElement.typeId.toString()
                //         : levelElement.typeName === treeElement.typeName,
                // );
                for (subTreeElement of treeElement.subTree) {
                    const nodeList = [];
                    // const subTreeLevel =
                    //     levelTree && levelTree.subTree
                    //         ? levelTree.subTree.find(
                    //               (levelSubTreeElement) =>
                    //                   levelSubTreeElement.typeId.toString() ===
                    //                   subTreeElement.typeId.toString(),
                    //           )
                    //         : undefined;
                    if (!subTreeElement.nodeName) {
                        const subTreeCLO = [];
                        for (nodeElement of subTreeElement.node) {
                            const nodeClo = [];
                            const nodeAttainments = clone(
                                courseAttainment.filter(
                                    (libraryElement) =>
                                        libraryElement.assessmentTypeId &&
                                        libraryElement.assessmentTypeId.toString() ===
                                            nodeElement.typeId.toString() &&
                                        treeElement.typeId.toString() ===
                                            libraryElement.typeId.toString() &&
                                        subTreeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString(),
                                ),
                            );
                            // const nodeLevel =
                            //     subTreeLevel && subTreeLevel.node
                            //         ? subTreeLevel.node.find(
                            //               (nodeLevelElement) =>
                            //                   nodeLevelElement.typeId.toString() ===
                            //                   nodeElement.typeId.toString(),
                            //           )
                            //         : undefined;
                            for (nodeAttainmentElement of nodeAttainments) {
                                for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                    for (assignmentQuestionElement of nodeAttainmentElement.questionMarks) {
                                        if (
                                            assignmentQuestionElement.outComeIds.find(
                                                (outComeIdElement) =>
                                                    outComeIdElement.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            )
                                        ) {
                                            const nodeCloIndex = nodeClo.findIndex(
                                                (closElement) =>
                                                    closElement._id.toString() ===
                                                    nodeAttainmentCLOElement._id.toString(),
                                            );
                                            if (nodeCloIndex == -1)
                                                nodeClo.push({
                                                    _id: nodeAttainmentCLOElement._id.toString(),
                                                    percentage: nodeAttainmentCLOElement.percentage,
                                                    count:
                                                        nodeAttainmentCLOElement.percentage === null
                                                            ? 0
                                                            : 1,
                                                    level: '',
                                                });
                                            else {
                                                nodeClo[nodeCloIndex].percentage +=
                                                    nodeAttainmentCLOElement.percentage;
                                                nodeClo[nodeCloIndex].count++;
                                            }
                                        }
                                    }
                                }
                            }
                            for (nodeCloElement of nodeClo)
                                if (nodeCloElement.count) {
                                    nodeCloElement.percentage /= nodeCloElement.count;
                                    // const percentageLevel =
                                    //     nodeLevel && nodeLevel.levelValues
                                    //         ? nodeLevel.levelValues.find((levelValueElement) =>
                                    //               levelValuesAs === RANGE
                                    //                   ? parseFloat(levelValueElement.min) <=
                                    //                         parseInt(nodeCloElement.percentage) &&
                                    //                     parseFloat(levelValueElement.max) >=
                                    //                         parseInt(nodeCloElement.percentage)
                                    //                   : levelValueElement.condition === EQUAL
                                    //                   ? parseInt(levelValueElement.percentage) <
                                    //                     parseInt(nodeCloElement.percentage)
                                    //                   : parseInt(levelValueElement.percentage) <=
                                    //                     parseInt(nodeCloElement.percentage),
                                    //           )
                                    //         : '';
                                    // nodeCloElement.level =
                                    //     percentageLevel && percentageLevel.level
                                    //         ? percentageLevel.level
                                    //         : '';
                                }

                            nodeList.push({
                                _id: nodeElement._id,
                                typeName: nodeElement.typeName,
                                typeId: nodeElement.typeId,
                                nodeId: nodeElement.nodeId,
                                weightage: nodeElement.weightage,
                                nodeName: nodeElement.nodeName,
                                clo: nodeClo,
                                // attainments: nodeAttainments,
                            });
                        }
                        for (nodeListElement of nodeList) {
                            for (nodeAttainmentCLOElement of nodeListElement.clo) {
                                const cloIndex = subTreeCLO.findIndex(
                                    (closElement) =>
                                        closElement._id.toString() ===
                                        nodeAttainmentCLOElement._id.toString(),
                                );
                                if (cloIndex == -1)
                                    subTreeCLO.push({
                                        _id: nodeAttainmentCLOElement._id.toString(),
                                        percentage:
                                            nodeAttainmentCLOElement.percentage === null
                                                ? nodeAttainmentCLOElement.percentage
                                                : (nodeAttainmentCLOElement.percentage *
                                                      nodeListElement.weightage) /
                                                  100,
                                        count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                        level: '',
                                    });
                                else {
                                    subTreeCLO[cloIndex].percentage +=
                                        nodeAttainmentCLOElement.percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : (nodeAttainmentCLOElement.percentage *
                                                  nodeListElement.weightage) /
                                              100;
                                    subTreeCLO[cloIndex].count++;
                                }
                            }
                        }

                        for (cloSubTreeElement of subTreeCLO) {
                            if (cloSubTreeElement.percentage !== null)
                                cloSubTreeElement.percentage /= cloSubTreeElement.count;
                            // Tree
                            // const percentageLevel =
                            //     subTreeLevel && subTreeLevel.levelValues
                            //         ? subTreeLevel.levelValues.find((levelValueElement) =>
                            //               levelValuesAs === RANGE
                            //                   ? parseFloat(levelValueElement.min) <=
                            //                         parseInt(cloSubTreeElement.percentage) &&
                            //                     parseFloat(levelValueElement.max) >=
                            //                         parseInt(cloSubTreeElement.percentage)
                            //                   : levelValueElement.condition === EQUAL
                            //                   ? parseInt(levelValueElement.percentage) <
                            //                     parseInt(cloSubTreeElement.percentage)
                            //                   : parseInt(levelValueElement.percentage) <=
                            //                     parseInt(cloSubTreeElement.percentage),
                            //           )
                            //         : '';
                            // cloSubTreeElement.level =
                            //     percentageLevel && percentageLevel.level
                            //         ? percentageLevel.level
                            //         : '';

                            const cloPercentage =
                                cloSubTreeElement.percentage === null
                                    ? cloSubTreeElement.percentage
                                    : (cloSubTreeElement.percentage * subTreeElement.weightage) /
                                      100;
                            const cloIndex = treeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() === cloSubTreeElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeCLO.push({
                                    _id: cloSubTreeElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeCLO[cloIndex].percentage += cloPercentage;
                                treeCLO[cloIndex].count++;
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            weightage: subTreeElement.weightage,
                            node: nodeList,
                            clo: subTreeCLO.length ? subTreeCLO : [],
                        });
                    } else {
                        const subTreeCLO = [];
                        const nodeAttainments = clone(
                            courseAttainment.filter(
                                (libraryElement) =>
                                    libraryElement.assessmentTypeId &&
                                    libraryElement.assessmentTypeId.toString() ===
                                        subTreeElement.typeId.toString() &&
                                    (treeElement.typeId.toString() ===
                                        libraryElement.typeId.toString() ||
                                        treeElement.typeId.toString() ===
                                            libraryElement.subTypeId.toString()),
                            ),
                        );
                        for (nodeAttainmentElement of nodeAttainments) {
                            for (nodeAttainmentCLOElement of nodeAttainmentElement.clo) {
                                const cloIndex = subTreeCLO.findIndex(
                                    (closElement) =>
                                        closElement._id.toString() ===
                                        nodeAttainmentCLOElement._id.toString(),
                                );
                                if (cloIndex == -1) {
                                    subTreeCLO.push({
                                        _id: nodeAttainmentCLOElement._id.toString(),
                                        percentage: nodeAttainmentCLOElement.percentage,
                                        count: nodeAttainmentCLOElement.percentage === null ? 0 : 1,
                                        level: '',
                                    });
                                } else if (nodeAttainmentCLOElement.percentage !== null) {
                                    subTreeCLO[cloIndex].percentage =
                                        subTreeCLO[cloIndex].percentage === null
                                            ? nodeAttainmentCLOElement.percentage
                                            : subTreeCLO[cloIndex].percentage +
                                              nodeAttainmentCLOElement.percentage;
                                    subTreeCLO[cloIndex].count++;
                                }
                                // const percentageLevel =
                                //     subTreeLevel && subTreeLevel.levelValues
                                //         ? subTreeLevel.levelValues.find((levelValueElement) =>
                                //               levelValuesAs === RANGE
                                //                   ? parseFloat(levelValueElement.min) <=
                                //                         parseInt(
                                //                             nodeAttainmentCLOElement.percentage,
                                //                         ) &&
                                //                     parseFloat(levelValueElement.max) >=
                                //                         parseInt(
                                //                             nodeAttainmentCLOElement.percentage,
                                //                         )
                                //                   : levelValueElement.condition === EQUAL
                                //                   ? parseInt(levelValueElement.percentage) <
                                //                     parseInt(nodeAttainmentCLOElement.percentage)
                                //                   : parseInt(levelValueElement.percentage) <=
                                //                     parseInt(nodeAttainmentCLOElement.percentage),
                                //           )
                                //         : '';
                                // nodeAttainmentCLOElement.level =
                                //     percentageLevel && percentageLevel.level
                                //         ? percentageLevel.level
                                //         : '';
                            }
                        }

                        for (cloSubTreeElement of subTreeCLO) {
                            if (cloSubTreeElement.percentage !== null)
                                cloSubTreeElement.percentage /= cloSubTreeElement.count;
                            // Tree
                            // const percentageLevel =
                            //     subTreeLevel && subTreeLevel.levelValues
                            //         ? subTreeLevel.levelValues.find((levelValueElement) =>
                            //               levelValuesAs === RANGE
                            //                   ? parseFloat(levelValueElement.min) <=
                            //                         parseInt(cloSubTreeElement.percentage) &&
                            //                     parseFloat(levelValueElement.max) >=
                            //                         parseInt(cloSubTreeElement.percentage)
                            //                   : levelValueElement.condition === EQUAL
                            //                   ? parseInt(levelValueElement.percentage) <
                            //                     parseInt(cloSubTreeElement.percentage)
                            //                   : parseInt(levelValueElement.percentage) <=
                            //                     parseInt(cloSubTreeElement.percentage),
                            //           )
                            //         : '';
                            // cloSubTreeElement.level =
                            //     percentageLevel && percentageLevel.level
                            //         ? percentageLevel.level
                            //         : '';
                            const cloPercentage =
                                cloSubTreeElement.percentage === null
                                    ? cloSubTreeElement.percentage
                                    : (cloSubTreeElement.percentage * subTreeElement.weightage) /
                                      100;

                            const cloIndex = treeCLO.findIndex(
                                (closElement) =>
                                    closElement._id.toString() === cloSubTreeElement._id.toString(),
                            );
                            if (cloIndex == -1)
                                treeCLO.push({
                                    _id: cloSubTreeElement._id.toString(),
                                    percentage: cloPercentage,
                                    count: cloPercentage === null ? 0 : 1,
                                    level: '',
                                });
                            else if (cloPercentage !== null) {
                                treeCLO[cloIndex].percentage += cloPercentage;
                                treeCLO[cloIndex].count++;
                            }
                        }
                        subTreeList.push({
                            _id: subTreeElement._id,
                            typeName: subTreeElement.typeName,
                            typeId: subTreeElement.typeId,
                            nodeId: subTreeElement.nodeId,
                            nodeName: subTreeElement.nodeName,
                            // attainments: nodeAttainments,
                            clo: subTreeCLO.length ? subTreeCLO : [],
                        });
                    }
                }
                for (cloTreeElement of treeCLO) {
                    // OverAll CLO
                    // const percentageLevel =
                    //     levelTree && levelTree.levelValues
                    //         ? levelTree.levelValues.find((levelValueElement) =>
                    //               levelValuesAs === RANGE
                    //                   ? parseFloat(levelValueElement.min) <=
                    //                         parseInt(cloTreeElement.percentage) &&
                    //                     parseFloat(levelValueElement.max) >=
                    //                         parseInt(cloTreeElement.percentage)
                    //                   : levelValueElement.condition === EQUAL
                    //                   ? parseInt(levelValueElement.percentage) <
                    //                     parseInt(cloTreeElement.percentage)
                    //                   : parseInt(levelValueElement.percentage) <=
                    //                     parseInt(cloTreeElement.percentage),
                    //           )
                    //         : [];
                    // cloTreeElement.level =
                    //     percentageLevel && percentageLevel.level ? percentageLevel.level : '';
                    const cloPercentage =
                        cloTreeElement.percentage === null
                            ? cloTreeElement.percentage
                            : (cloTreeElement.percentage * treeElement.weightage) / 100;

                    const cloIndex = overAllCLO.findIndex(
                        (closElement) =>
                            closElement._id.toString() === cloTreeElement._id.toString(),
                    );
                    if (cloIndex === -1) {
                        overAllCLO.push({
                            _id: cloTreeElement._id.toString(),
                            percentage: cloPercentage,
                            count: 1,
                            level: '',
                        });
                    } else if (cloPercentage !== null) {
                        overAllCLO[cloIndex].percentage += cloPercentage;
                        overAllCLO[cloIndex].count++;
                    }
                }
                attainmentNodeList.push({
                    _id: treeElement._id,
                    typeName: treeElement.typeName,
                    typeId: treeElement.typeId,
                    weightage: treeElement.weightage,
                    subTree: subTreeList,
                    clo: treeCLO.length ? treeCLO : [],
                });
            }
            // if (overAllCLO.length) {
            //     const attElement = coAttainmentOutCome.levels.find(
            //         (attainmentNodeListElement) =>
            //             attainmentNodeListElement.typeName === 'Overall Attainment',
            //     );
            //     for (overAllCLOElement of overAllCLO) {
            //         const percentageLevel =
            //             attElement && attElement.levelValues
            //                 ? attElement.levelValues.find((levelValueElement) =>
            //                       levelValuesAs === RANGE
            //                           ? parseFloat(levelValueElement.min) <=
            //                                 parseInt(overAllCLOElement.percentage) &&
            //                             parseFloat(levelValueElement.max) >=
            //                                 parseInt(overAllCLOElement.percentage)
            //                           : levelValueElement.condition === EQUAL
            //                           ? parseInt(levelValueElement.percentage) <
            //                             parseInt(overAllCLOElement.percentage)
            //                           : parseInt(levelValueElement.percentage) <=
            //                             parseInt(overAllCLOElement.percentage),
            //                   )
            //                 : '';
            //         overAllCLOElement.level =
            //             percentageLevel && percentageLevel.level ? percentageLevel.level : '';
            //     }
            // }
            courseCOReport.push({
                courseId: courseIdElement._course_id,
                overAllCLO,
                attainmentNodeList,
            });
        }
        // return { data: courseCOReport };

        // Course Merge with Year Level Program Calendar
        const curriculumCourses = [];
        // const curriculumCoursesAverage = { percentage: 0, count: 0, level: '' };
        const termCourses = curriculumYearLevelData.course.filter(
            (courseElement) =>
                courseElement.term === term &&
                courseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._course_id.toString(),
                ),
        );

        const courseImpactValue = {
            min:
                levelValuesAs === RANGE
                    ? overAllLevelValuesWithOutSort[0].max
                    : overAllLevelValuesWithOutSort[0].percentage,
            max:
                levelValuesAs === RANGE
                    ? overAllLevelValuesWithOutSort[overAllLevelValuesWithOutSort.length - 1].max
                    : overAllLevelValuesWithOutSort[overAllLevelValuesWithOutSort.length - 1]
                          .percentage,
        };
        for (courseElement of termCourses) {
            const courseCOAttainmentReport = courseCOReport.find(
                (courseCOReportElement) =>
                    courseCOReportElement.courseId.toString() ===
                    courseElement._course_id.toString(),
            );
            courseElement.clo = [];
            // courseElement.overAllPLO = [];
            courseElement.tree = [];
            const courseObtainedCLOs = courseCLO.find(
                (courseMasterElement) =>
                    courseMasterElement._course_id.toString() ===
                        courseElement._course_id.toString() &&
                    courseMasterElement.course_assigned_details.find(
                        (assignedDetailsElement) =>
                            assignedDetailsElement.level_no === courseElement.levelNo,
                    ),
            );
            if (courseObtainedCLOs) {
                courseElement.clo = courseObtainedCLOs.clo;
                const courseAssignedData = courseObtainedCLOs.course_assigned_details.find(
                    (courseAssignedElement) =>
                        courseAssignedElement.level_no === courseElement.levelNo,
                );
                if (courseAssignedData) {
                    courseElement.mapping_type = courseAssignedData.mapping_type;
                    courseElement.content_mapping_type = courseAssignedData.content_mapping_type;
                }
            }
            const courseOverAllPlo = { percentage: null, count: 0 };
            if (courseElement.clo.length) {
                for (ploElement of curriculumYearLevelData.curriculumPLO) {
                    const courseCLOFiltered = ploElement.clos.filter((cloElement) =>
                        courseElement.clo.find(
                            (courseCloElement) =>
                                courseCloElement.toString() === cloElement.clo_id.toString(),
                        ),
                    );
                    if (courseCLOFiltered.length) {
                        const impactCalculation = {
                            values: 0,
                            count: 0,
                        };
                        if (courseElement.mapping_type === 'impact') {
                            for (courseCloElement of courseCLOFiltered) {
                                switch (courseCloElement.mapped_value) {
                                    case 'H':
                                        impactCalculation.values += 3;
                                        impactCalculation.count++;
                                        break;
                                    case 'M':
                                        impactCalculation.values += 2;
                                        impactCalculation.count++;
                                        break;
                                    case 'L':
                                        impactCalculation.values += 1;
                                        impactCalculation.count++;
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                        // Course Tree Based Flow
                        const courseOverAllCLOs = courseCOAttainmentReport.overAllCLO.filter(
                            (reportCLOReport) =>
                                courseCLOFiltered.find(
                                    (filteredCLOElement) =>
                                        filteredCLOElement.clo_id.toString() ===
                                        reportCLOReport._id.toString(),
                                ),
                        );
                        const poOutcomeData = outComeCalculations({
                            poId: ploElement._id,
                            mapping_type: courseElement.mapping_type,
                            courseImpactValue,
                            overAllLevelValues,
                            levelValuesAs,
                            impactCalculation,
                            courseCLOs: courseOverAllCLOs,
                        });
                        // Only for Over All
                        if (courseOverAllPlo.percentage === null) {
                            courseOverAllPlo.percentage = poOutcomeData.percentage || 0;
                            courseOverAllPlo.count = 1;
                        } else {
                            courseOverAllPlo.percentage += poOutcomeData.percentage || 0;
                            courseOverAllPlo.count++;
                        }
                        const poIndex = curriculumPLOStruct.findIndex(
                            (poElement) => poElement._id.toString() === ploElement._id.toString(),
                        );
                        if (poIndex !== -1 && poOutcomeData.coursePOAttainment) {
                            curriculumPLOStruct[poIndex].value += poOutcomeData.coursePOAttainment;
                            curriculumPLOStruct[poIndex].count++;
                        }

                        const treeIndex = courseElement.tree.findIndex(
                            (treeElement) => treeElement.typeName === 'Overall Attainment',
                        );
                        if (treeIndex === -1) {
                            courseElement.tree.push({
                                typeId: convertToMongoObjectId(),
                                typeName: 'Overall Attainment',
                                plo: [poOutcomeData],
                            });
                        } else {
                            courseElement.tree[treeIndex].plo.push(poOutcomeData);
                        }
                        const outComeTypeBase = ({ typeName, typeId, nodeName, courseCLOs }) => {
                            const treeOutcomeData = outComeCalculations({
                                poId: ploElement._id,
                                mapping_type: courseElement.mapping_type,
                                courseImpactValue,
                                overAllLevelValues,
                                levelValuesAs,
                                impactCalculation,
                                courseCLOs,
                            });
                            const outComeIndex = courseElement.tree.findIndex(
                                (treeElement) =>
                                    treeElement.typeName === typeName &&
                                    treeElement.typeId === typeId,
                            );
                            if (outComeIndex === -1) {
                                courseElement.tree.push({
                                    typeName,
                                    typeId,
                                    nodeName,
                                    plo: [treeOutcomeData],
                                });
                            } else {
                                courseElement.tree[outComeIndex].plo.push(treeOutcomeData);
                            }
                        };
                        // Course Tree Flow
                        for (treeElement of courseCOAttainmentReport.attainmentNodeList) {
                            const treeCO = treeElement.clo.filter((reportCLOReport) =>
                                courseCLOFiltered.find(
                                    (filteredCLOElement) =>
                                        filteredCLOElement.clo_id.toString() ===
                                        reportCLOReport._id.toString(),
                                ),
                            );
                            outComeTypeBase({
                                typeName: treeElement.typeName,
                                typeId: treeElement.typeId,
                                courseCLOs: treeCO,
                            });
                            for (subTreeElement of treeElement.subTree) {
                                const subTreeCO = subTreeElement.clo.filter((reportCLOReport) =>
                                    courseCLOFiltered.find(
                                        (filteredCLOElement) =>
                                            filteredCLOElement.clo_id.toString() ===
                                            reportCLOReport._id.toString(),
                                    ),
                                );
                                outComeTypeBase({
                                    typeName: subTreeElement.typeName,
                                    typeId: subTreeElement.typeId,
                                    nodeName: subTreeElement.nodeName,
                                    courseCLOs: subTreeCO,
                                });
                                if (!subTreeElement.nodeName) {
                                    for (nodeElement of subTreeElement.node) {
                                        const nodeCO = nodeElement.clo.filter((reportCLOReport) =>
                                            courseCLOFiltered.find(
                                                (filteredCLOElement) =>
                                                    filteredCLOElement.clo_id.toString() ===
                                                    reportCLOReport._id.toString(),
                                            ),
                                        );
                                        outComeTypeBase({
                                            typeName: nodeElement.typeName,
                                            typeId: nodeElement.typeId,
                                            nodeName: nodeElement.nodeName,
                                            courseCLOs: nodeCO,
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            delete courseElement.clo;
            const coursePercentage = courseOverAllPlo.percentage / courseOverAllPlo.count;
            // if (coursePercentage) {
            //     curriculumCoursesAverage.percentage += coursePercentage;
            //     curriculumCoursesAverage.count++;
            // }
            curriculumCourses.push({
                ...courseElement,
                percentage: coursePercentage,
            });
        }
        const courseTreePOReports = [];
        for (courseElement of curriculumCourses) {
            for (courseTreeElement of courseElement.tree) {
                const outComeIndex = courseTreePOReports.findIndex(
                    (treeElement) =>
                        treeElement.typeName.toString() === courseTreeElement.typeName.toString() &&
                        (courseTreeElement.typeId
                            ? treeElement.typeId.toString() === courseTreeElement.typeId.toString()
                            : true),
                );
                const courseTreePLO = [];
                for (ploElement of courseTreeElement.plo) {
                    if (ploElement.percentage !== null && !!ploElement.percentage)
                        courseTreePLO.push({
                            _id: ploElement._id,
                            percentage: ploElement.percentage,
                            count: 1,
                        });
                }
                if (outComeIndex === -1) {
                    courseTreePOReports.push({
                        typeName: courseTreeElement.typeName,
                        typeId: courseTreeElement.typeId,
                        nodeName: courseTreeElement.nodeName,
                        plo: courseTreePLO,
                    });
                } else {
                    for (courseTreePLOElement of courseTreePLO) {
                        const ploIndex = courseTreePOReports[outComeIndex].plo.findIndex(
                            (treePloElement) =>
                                treePloElement._id.toString() ===
                                courseTreePLOElement._id.toString(),
                        );
                        if (ploIndex === -1)
                            courseTreePOReports[outComeIndex].plo.push({
                                _id: courseTreePLOElement._id,
                                percentage: courseTreePLOElement.percentage,
                                count: 1,
                            });
                        else if (
                            courseTreePLOElement.percentage !== null &&
                            !!courseTreePLOElement.percentage
                        ) {
                            courseTreePOReports[outComeIndex].plo[ploIndex].percentage +=
                                courseTreePLOElement.percentage;
                            courseTreePOReports[outComeIndex].plo[ploIndex].count++;
                        }
                    }
                }
            }
        }
        for (courseTreePOElement of courseTreePOReports)
            for (ploTreeElement of courseTreePOElement.plo)
                if (ploTreeElement.percentage !== null)
                    ploTreeElement.percentage /= ploTreeElement.count;

        const reportQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _program_id: convertToMongoObjectId(programId),
            outCome,
            term,
            _attainment_id: convertToMongoObjectId(attainmentId),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };
        const reportCheck = await assessmentReportSchema.findOne(
            {
                ...reportQuery,
            },
            { _id: 1 },
        );
        if (!reportCheck) {
            await assessmentReportSchema.create({
                ...reportQuery,
                courses: curriculumCourses,
                courseTreePO: courseTreePOReports,
            });
            console.log('Report Created');
        } else {
            await assessmentReportSchema.updateOne(
                {
                    ...reportQuery,
                },
                {
                    $set: {
                        courses: curriculumCourses,
                        courseTreePO: courseTreePOReports,
                    },
                },
            );
            console.log('Report Updated');
        }
        return {
            statusCode: 200,
            message: 'ATTAINMENT_DATA_RETRIEVED',
            data: {
                curriculumCourses,
                courseTreePOReports,
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAttainmentProgramList,
    getReportCourseList,
    getCourseAttainmentReport,
    getCourseAttainmentReportWithFilter,
    getCourseStudentAttainmentReport,
    getProgramCourseAttainmentReport,
    getProgramCourseList,
    updateProgramCourseList,
    getProgramAttainmentReportWithFilter,
    generateProgramCourseAttainmentReport,
};
