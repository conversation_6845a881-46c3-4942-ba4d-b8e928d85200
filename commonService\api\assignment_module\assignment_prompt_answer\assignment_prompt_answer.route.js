const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const { validate } = require('../../../../middleware/validation');
const {
    assignmentPromptValidator,
    assignmentPromptAnswerGetValidator,
} = require('./assignment_prompt_answer.validator');
const {
    createAssignmentPromptAnswer,
    listAssignmentPromptAnswer,
    getAssignmentPromptAnswer,
    deleteAssignmentPromptAnswer,
    updateAssignmentPromptAnswer,
    assignmentPromptAnswerBulkUpdate,
} = require('./assignment_prompt_answer.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.post(
    '/create',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(createAssignmentPromptAnswer),
);
router.post(
    '/bulk-create',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(assignmentPromptAnswerBulkUpdate),
);
router.get(
    '/get/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(assignmentPromptAnswerGetValidator),
    catchAsync(getAssignmentPromptAnswer),
);
router.delete(
    '/delete/:id',
    validate(assignmentPromptAnswerGetValidator),
    catchAsync(deleteAssignmentPromptAnswer),
);
router.put(
    '/update/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateAssignmentPromptAnswer),
);
router.get(
    '/list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listAssignmentPromptAnswer),
);

module.exports = router;
