const {
    DC_STAFF,
    DISCIPLINARY_REMARKS_TYPE: { SCHEDULE_LEVEL },
    SCHEDULE_TYPES: { REGULAR, EVENT, SUPPORT_SESSION },
} = require('../utility/constants');
const globalSessionSettingsSchema = require('../global_session_settings/global_session_settings_model');
const { nameFormatter } = require('../utility/common_functions');

exports.checkVisibility = async ({ userType }) => {
    const visibility = { studentVisibility: true, commentVisibility: true };
    if (userType === DC_STAFF) {
        return visibility;
    }
    visibility.studentVisibility = false;
    visibility.commentVisibility = false;
    const global_session_settings = await globalSessionSettingsSchema
        .findOne({}, { disciplinaryRemarks: 1 })
        .lean();

    return global_session_settings?.disciplinaryRemarks ?? visibility;
};

exports.makeRemarksMailContent = ({ disciplinaryRemarkData = {} }) => {
    const studentName = nameFormatter(disciplinaryRemarkData.studentId?.name);
    const staffName = nameFormatter(disciplinaryRemarkData.reportedBy?.name);
    const scheduleDetails = disciplinaryRemarkData?.scheduleId;
    const toMail = disciplinaryRemarkData.studentId?.email;
    const subject = 'Disciplinary Warning Notice';
    const formattedUpdatedAt = new Date(disciplinaryRemarkData.updatedAt).toDateString();
    const formattedScheduleDate = scheduleDetails
        ? new Date(scheduleDetails.schedule_date).toLocaleDateString()
        : '';
    let sessionDetails = '';
    if (scheduleDetails) {
        if (scheduleDetails.type === REGULAR) {
            if (scheduleDetails.merge_with.length) {
                sessionDetails = `<p><b>Merge Session Details:</b> ${scheduleDetails.session.delivery_symbol}${scheduleDetails.session.delivery_no}`;
                scheduleDetails.merge_with.forEach((mergeScheduleElement) => {
                    sessionDetails += `, ${mergeScheduleElement.schedule_id.session.delivery_symbol}${mergeScheduleElement.schedule_id.session.delivery_no}`;
                });
                sessionDetails += ` <br/>`;
            } else
                sessionDetails = `<p><b>Session Details:</b> ${scheduleDetails.session.delivery_symbol}${scheduleDetails.session.delivery_no} - ${scheduleDetails.session.session_topic} <br/>`;
        } else if (scheduleDetails.type === EVENT) {
            sessionDetails = `<p><b>Event Details:</b> ${scheduleDetails.title} <br/>`;
        } else if (scheduleDetails.type === SUPPORT_SESSION) {
            sessionDetails = `<p><b>Support Session Details:</b> ${scheduleDetails.title} <br/>`;
        }
    }
    const courseDetails = scheduleDetails
        ? `${scheduleDetails.course_code} - ${scheduleDetails.course_name} <br/>${scheduleDetails.program_name}/${scheduleDetails.year_no}/${scheduleDetails.level_no} <br/>${formattedScheduleDate} at ${scheduleDetails.start.hour}:${scheduleDetails.start.minute} ${scheduleDetails.start.format}`
        : '';
    const mailContent = `<p><b>Dear ${studentName},</b></p><p><b>Disciplinary Remark:</b> ${
        disciplinaryRemarkData.tardisId?.name
    }</p><p><b>Comments:</b> ${
        disciplinaryRemarkData.comment
    }</p><p><b>Reported By:</b> ${staffName}</p><p><b>Reported On:</b> ${formattedUpdatedAt}</p>${
        disciplinaryRemarkData.type === SCHEDULE_LEVEL
            ? `${sessionDetails}${courseDetails}</p>`
            : ''
    }`;
    return { toMail, subject, mailContent };
};
