const mongoose = require('mongoose');
const Schema = mongoose.Schema;
// constants
const { QUESTION_TYPE, ACTIVITIES, QUESTION } = require('../utility/constants');

// enums
const {
    CORRECT_ANSWER,
    INCORRECT_ANSWER,
    GENERAL,
    LIKERTSCALE,
    OPEN_ENDED,
    MIN,
    MAX,
    EXACTLY,
    CLOSELY,
} = require('../utility/enums');

const questionSchema = new Schema(
    {
        _activityId: {
            type: Schema.Types.ObjectId,
            ref: ACTIVITIES,
        },
        text: {
            type: String,
        },
        questionType: {
            type: String,
            enum: QUESTION_TYPE,
        },
        attachments: [
            {
                link: {
                    type: String,
                    trim: true,
                },
                size: {
                    type: Number,
                    default: 0,
                },
            },
        ],
        surveyQuestionType: {
            type: String,
            enum: [LIKERTSCALE, OPEN_ENDED],
        },
        maxCharacterLimit: { type: Number },
        options: [
            {
                text: {
                    type: String,
                },
                attachments: [
                    {
                        link: {
                            type: String,
                            trim: true,
                        },
                        size: {
                            type: Number,
                            default: 0,
                        },
                    },
                ],
                answer: {
                    type: Boolean,
                    default: false,
                },
                order: {
                    type: Number,
                    default: 1,
                },
            },
        ],
        answer: {
            type: String,
        },
        feedback: [
            {
                text: {
                    type: String,
                },
                type: {
                    type: String,
                    enum: [CORRECT_ANSWER, INCORRECT_ANSWER, GENERAL],
                    default: GENERAL,
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        order: {
            type: Number,
            default: 1,
        },
        sessionId: {
            type: Schema.Types.ObjectId,
        },
        sloIds: [{ type: Schema.Types.ObjectId }],
        taxonomyIds: [],
        questionViewType: {
            type: String,
        },
        describeYourQuestion: {
            type: String,
        },
        itemCategory: {
            type: String,
        },
        aiNoOfOptions: { type: Number },
        generateFeedback: {
            type: Boolean,
            default: true,
        },
        mark: {
            type: Number,
            default: 0,
        },
        mandatory: {
            type: Boolean,
            default: false,
        },
        description: {
            type: String,
        },
        descriptionEnable: {
            type: Boolean,
            default: false,
        },
        shuffleOptionOrder: {
            type: Boolean,
            default: false,
        },
        characterLength: {
            type: String,
            enum: [MIN, MAX],
        },
        matchingOptions: [
            {
                rowText: {
                    type: String,
                },
                columnText: {
                    type: String,
                },
                answer: {
                    type: String,
                },
                order: {
                    type: Number,
                    default: 1,
                },
            },
        ],
        answerMatchingType: {
            type: String,
            enum: [EXACTLY, CLOSELY],
        },
        benchMark: {
            type: Number,
        },
        answerTextVariant: [
            {
                firstVariant: {
                    type: String,
                },
                secondVariant: {
                    type: String,
                },
                mark: {
                    type: Number,
                },
            },
        ],
    },
    { timestamps: true },
);

module.exports = mongoose.model(QUESTION, questionSchema);
