const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const {
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
} = require('../utility/constants');
const getStudentLMSSettingValidator = async () => {
    Joi.object().keys({
        headers: Joi.object().keys({
            _institution_id: Joi.string().alphanum().length(24),
        }),
        query: joi.object().keys({
            classificationType: Joi.string().valid('on_duty', 'leave', 'permission').required(),
        }),
    });
};
const updateGeneraConfigValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        params: Joi.object().keys({
            settingId: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            generalConfig: Joi.object().key({
                parentApplyPermission: Joi.boolean(),
                parentAcknowledgePermission: Joi.boolean(),
            }),
        }),
    });
};
const createCatagoriesValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            categoryName: Joi.string().required(),
        }),
    });
};
const deleteCatagoriesValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            categoryId: Joi.ObjectId().required(),
        }),
    });
};
const createCatagoriesTypesValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            categoryId: Joi.string().alphanum().length(24),
            types: Joi.array().items(
                Joi.object().keys({
                    _id: Joi.string().alphanum().length(24),
                    typeName: Joi.string(),
                    typeDescription: Joi.string(),
                    typeColor: Joi.string(),
                    noHours: Joi.number(),
                    attachmentMandatory: Joi.boolean(),
                    allowedDuringExam: Joi.boolean(),
                    attendanceStatus: Joi.boolean(),
                    isActive: Joi.boolean(),
                    percentage: Joi.number(),
                    isReasonFromApplicant: Joi.boolean(),
                    isProofToBeAttached: Joi.boolean(),
                    frequency: Joi.object().keys({
                        setFrequency: Joi.Number(),
                        frequencyBy: Joi.string().valid('year', 'month').required(),
                        frequencyByMonth: Joi.Number(),
                    }),
                }),
            ),
        }),
    });
};
const updateCategoryTypesValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            typesId: Joi.string().alphanum().length(24),
            types: Joi.array().items(
                Joi.object().keys({
                    typeName: Joi.string(),
                    typeDescription: Joi.string(),
                    typeColor: Joi.string(),
                    noHours: Joi.number(),
                    attachmentMandatory: Joi.boolean(),
                    allowedDuringExam: Joi.boolean(),
                    attendanceStatus: Joi.boolean(),
                    isActive: Joi.boolean(),
                    percentage: Joi.number(),
                    isReasonFromApplicant: Joi.boolean(),
                    isProofToBeAttached: Joi.boolean(),
                    frequency: Joi.object().keys({
                        setFrequency: Joi.Number(),
                        frequencyBy: Joi.string().valid('year', 'month').required(),
                        frequencyByMonth: Joi.Number(),
                    }),
                }),
            ),
        }),
    });
};

const deleteCatagoriesTypesValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            categoryId: Joi.ObjectId().required(),
            typesId: Joi.ObjectId().required(),
        }),
    });
};
const updateTermsAndConditionValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            termsAndCondition: Joi.string().alphanum(),
            leaveCalculation: Joi.string().alphanum(),
        }),
    });
};
const addLeaveApprovalLevelValidate = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            levelApproverId: Joi.ObjectId().required(),
            level: Joi.array().items(
                Joi.object().key({
                    levelName: Joi.string(),
                    gender: Joi.string().valid('male', 'female', 'both').required(),
                    approvalConfig: Joi.string().valid('all', 'any').required(),
                    categoryBased: Joi.string().valid('user', 'role').required(),
                    turnAroundTime: Joi.number(),
                    escalateRequest: Joi.boolean(),
                    userIds: Joi.array().items(Joi.ObjectId().required()),
                    roleIds: Joi.array().items(Joi.ObjectId().required()),
                }),
            ),
        }),
    });
};
const deleteLevelValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            levelApproverId: Joi.ObjectId().required(),
            levelId: Joi.ObjectId().required(),
        }),
    });
};
const editLevelsValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            levelId: Joi.string().alphanum().length(24),
            level: Joi.array().items(
                Joi.object().key({
                    levelName: Joi.string(),
                    gender: Joi.string().valid('male', 'female', 'both').required(),
                    approvalConfig: Joi.string().valid('all', 'any').required(),
                    categoryBased: Joi.string().valid('user', 'role').required(),
                    turnAroundTime: Joi.number(),
                    escalateRequest: Joi.boolean(),
                    userIds: Joi.array().items(Joi.ObjectId().required()),
                    roleIds: Joi.array().items(Joi.ObjectId().required()),
                }),
            ),
        }),
    });
};
const deleteLevelApproverValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            levelApproverId: Joi.ObjectId().required(),
        }),
    });
};
const addProgramLevelValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            isExceptionalProgram: Joi.boolean(),
            programIds: Joi.array(Joi.ObjectId().required()),
            classificationType: Joi.string().valid('on_duty', 'leave', 'permission').required(),
        }),
    });
};
const editProgramLevelValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            isExceptionalProgram: Joi.boolean(),
            programIds: Joi.array(Joi.ObjectId().required()),
            classificationType: Joi.string().valid('on_duty', 'leave', 'permission').required(),
            levelApproverId: Joi.ObjectId().required(),
            removedProgramIds: Joi.array(Joi.ObjectId().required()),
            addedProgramIds: Joi.array(Joi.ObjectId().required()),
        }),
    });
};
const updateWarningConfigValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            warningConfig: Joi.array().items(
                Joi.object().key({
                    warningConfigId: Joi.ObjectId(),
                    isActive: Joi.boolean(),
                }),
            ),
        }),
    });
};

const divideGenderSegregationValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            genderSegregation: Joi.boolean(),
            classificationType: Joi.string().valid('on_duty', 'leave', 'permission').required(),
            levelApproverId: Joi.ObjectId().required(),
        }),
    });
};
const updateCategoryStatusValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            categoryId: Joi.ObjectId().required(),
            isActive: Joi.boolean(),
        }),
    });
};
const listProgramValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
    });
};
const addLeavePolicyValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            leavePolicy: Joi.array().items(
                Joi.object().key({
                    documentTitle: Joi.string(),
                    description: Joi.string(),
                    attachmentDocument: Joi.array().items(
                        Joi.object.key({
                            url: Joi.string(),
                            signedUrl: Joi.string(),
                            name: Joi.string(),
                        }),
                    ),
                    isDocumentValidity: Joi.boolean(),
                    noExpiryDate: Joi.boolean(),
                    allowStudentVisibilityStatus: Joi.boolean(),
                }),
            ),
        }),
    });
};
const editLeavePolicyValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.String().alphanum().length(24),
        }),
        body: Joi.object().keys({
            settingId: Joi.string().alphanum().length(24),
            leavePolicyId: Joi.ObjectId(),
            leavePolicy: Joi.array().items(
                Joi.object().key({
                    documentTitle: Joi.string(),
                    description: Joi.string(),
                    attachmentDocument: Joi.array().items(
                        Joi.object.key({
                            url: Joi.string(),
                            signedUrl: Joi.string(),
                            name: Joi.string(),
                        }),
                    ),
                    isDocumentValidity: Joi.boolean(),
                    noExpiryDate: Joi.boolean(),
                    allowStudentVisibilityStatus: Joi.boolean(),
                }),
            ),
        }),
    });
};
const deleteLeavePolicyValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId(),
            leavePolicyId: Joi.ObjectId(),
        }),
    });
};
const editCategoryNameValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            categoryId: Joi.ObjectId().required(),
            categoryName: Joi.string().required(),
            isActive: Joi.boolean(),
            isDefault: Joi.boolean(),
        }),
    });
};
const addWarningDenialConfigurationValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            settingId: Joi.ObjectId().required(),
            warningConfig: Joi.array().items(
                Joi.object().key({
                    typeName: Joi.number(),
                    labelName: Joi.String(),
                    percentage: Joi.Number(),
                    colorCode: Joi.String(),
                    message: Joi.String(),
                    denialCondition: Joi.string().valid('cumulative', 'individual').required(),
                    denialManagement: Joi.object.key({
                        accessType: Joi.String(),
                        roleIds: Joi.array().items(Joi.ObjectId()),
                        userIds: Joi.array().items(Joi.ObjectId()),
                    }),
                    categoryWisePercentage: Joi.array().items(
                        Joi.object().key({
                            categoryId: Joi.ObjectId(),
                            categoryName: Joi.String(),
                            percentage: Joi.Number(),
                        }),
                    ),
                    notificationToParent: Joi.object().Key({
                        isActive: Joi.boolean(),
                        setType: Joi.string().valid('automatic', 'manual').required(),
                    }),
                    isAdditionStaffNotify: Joi.boolean(),
                    notificationRoleIds: Joi.array().items(Joi.ObjectId()),
                    notificationToStudent: Joi.object().Key({
                        isActive: Joi.boolean(),
                        setType: Joi.string().valid('automatic', 'manual').required(),
                    }),
                    notificationToStaff: Joi.boolean(),
                    restrictCourseAccess: Joi.boolean(),
                    isActive: Joi.boolean(),
                    meetingWithStudent: Joi.boolean(),
                    meetingRoleIds: Joi.array().items(Joi.ObjectId()),
                    acknowledgeToStudent: Joi.boolean(),
                    markItMandatory: Joi.boolean(),
                    turnAroundTime: Joi.Number(),
                    escalationLevels: Joi.array().items(
                        Joi.object().key({
                            escalatingRoleIds: Joi.ObjectId(),
                            levelName: Joi.String(),
                            turnAroundTime: Joi.Number(),
                        }),
                    ),
                }),
            ),
        }),
    });
};

const getLeavePoliceForStudentValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            classificationType: Joi.string().valid('on_duty', 'leave', 'permission').required(),
        }),
    });
};
const listScheduleValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        body: Joi.object().keys({
            _student_id: Joi.ObjectId(),
            _institution_calendar_id: Joi.ObjectId(),
            startDate: Joi.date().format(),
            endDate: Joi.date().format(),
        }),
    });
};
const courseOrComprehensiveValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            warningModeValue: Joi.string()
                .required()
                .error(() => 'WARNING_MODE_VALUE_REQUIRED'),
            settingId: objectId.error(() => 'SETTING_ID_REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
const deleteLmsSettingDataValidator = async () => {
    Joi.object().key({
        headers: Joi.object().keys({
            _institution_id: Joi.ObjectId().required(),
        }),
        query: Joi.object().keys({
            classificationType: Joi.ObjectId().required(),
        }),
    });
};

module.exports = {
    getStudentLMSSettingValidator,
    updateGeneraConfigValidator,
    createCatagoriesValidator,
    deleteCatagoriesValidator,
    createCatagoriesTypesValidator,
    deleteCatagoriesTypesValidator,
    updateTermsAndConditionValidator,
    addLeaveApprovalLevelValidate,
    deleteLevelValidator,
    editLevelsValidator,
    updateCategoryTypesValidator,
    updateCategoryStatusValidator,
    listProgramValidator,
    deleteLevelApproverValidator,
    addProgramLevelValidator,
    editProgramLevelValidator,
    updateWarningConfigValidator,
    divideGenderSegregationValidator,
    addLeavePolicyValidator,
    deleteLeavePolicyValidator,
    editLeavePolicyValidator,
    editCategoryNameValidator,
    addWarningDenialConfigurationValidator,
    getLeavePoliceForStudentValidator,
    listScheduleValidator,
    courseOrComprehensiveValidator,
    deleteLmsSettingDataValidator,
};
