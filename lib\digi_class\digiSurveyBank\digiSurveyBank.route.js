const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    getSurveyBankList,
    getSurveyTemplate,
    getAllProgramList,
    getSingleProgramDetails,
    createNewSurvey,
    updateSurveyTemplate,
    getSingleSurveyTemplate,
    getPloDetails,
    getCloDetails,
    getUserPermissionProgramList,
    getUserPermissionCurriculumList,
    getUserPermissionLevelList,
    getUserPermissionCourseList,
    getUserSelectedProgramDetails,
    getUserPermissionCalendarList,
    publishSurvey,
    createUserSurveyTypeList,
    getUserSurveyTypeList,
    getUserPermissionTermList,
    getUserPermissionYearList,
    getTotalUserList,
    getDraftSurvey,
} = require('./digiSurveyBank.controller');
const {
    getSurveyBankListValidator,
    getSurveyTemplateValidator,
    getAllProgramListValidator,
    getSingleProgramDetailValidator,
    updateSurveyTemplateValidator,
    getSingleSurveyTemplateValidator,
    getUserPermissionProgramListValidator,
    getUserPermissionCurriculumListValidator,
    getUserPermissionLevelListValidator,
    getUserPermissionCourseListValidator,
    getPloDetailValidator,
    getCloDetailValidator,
    getUserSelectedProgramDetailValidator,
    getUserSurveyTypeListValidator,
    createNewSurveyValidator,
    createUserSurveyTypeListValidator,
    getUserPermissionYearListValidator,
    getTotalUserListValidator,
    publishSurveyValidator,
    getDraftSurveyValidator,
} = require('./digiSurveyBank.validator');
//template creation flow
router.get('/getSurveyBankList', getSurveyBankListValidator, catchAsync(getSurveyBankList));
router.get('/getAllProgramList', getAllProgramListValidator, catchAsync(getAllProgramList));
router.get(
    '/getSingleProgramDetails',
    getSingleProgramDetailValidator,
    catchAsync(getSingleProgramDetails),
);
//survey template creation new flow
router.get('/getSurveyTemplate', getSurveyTemplateValidator, catchAsync(getSurveyTemplate));
router.get('/getDraftSurvey', getDraftSurveyValidator, catchAsync(getDraftSurvey));
router.post('/createNewSurvey', createNewSurveyValidator, catchAsync(createNewSurvey));
router.put(
    '/createUserSurveyTypeList',
    createUserSurveyTypeListValidator,
    catchAsync(createUserSurveyTypeList),
);
router.get(
    '/getUserSurveyTypeList',
    getUserSurveyTypeListValidator,
    catchAsync(getUserSurveyTypeList),
);
router.put(
    '/updateSurveyTemplate',
    updateSurveyTemplateValidator,
    catchAsync(updateSurveyTemplate),
);
router.get(
    '/getSingleSurveyTemplate',
    getSingleSurveyTemplateValidator,
    catchAsync(getSingleSurveyTemplate),
);
//survey template runner new flow
router.get(
    '/getUserPermissionCalendarList',
    getSurveyTemplateValidator,
    catchAsync(getUserPermissionCalendarList),
);
router.get(
    '/getUserPermissionProgramList',
    getUserPermissionProgramListValidator,
    catchAsync(getUserPermissionProgramList),
);
router.get(
    '/getUserPermissionCurriculumList',
    getUserPermissionCurriculumListValidator,
    catchAsync(getUserPermissionCurriculumList),
);
router.get('/getPloDetails', getPloDetailValidator, catchAsync(getPloDetails));
router.get(
    '/getUserPermissionTermList',
    getUserPermissionCurriculumListValidator,
    catchAsync(getUserPermissionTermList),
);
router.get(
    '/getUserPermissionYearList',
    getUserPermissionYearListValidator,
    catchAsync(getUserPermissionYearList),
);
router.get(
    '/getUserPermissionLevelList',
    getUserPermissionLevelListValidator,
    catchAsync(getUserPermissionLevelList),
);
router.get(
    '/getUserPermissionCourseList',
    getUserPermissionCourseListValidator,
    catchAsync(getUserPermissionCourseList),
);
router.get('/getCloDetails', getCloDetailValidator, catchAsync(getCloDetails));
router.get(
    '/getUserSelectedProgramDetails',
    getUserSelectedProgramDetailValidator,
    catchAsync(getUserSelectedProgramDetails),
);
router.put('/getTotalUserList', getTotalUserListValidator, catchAsync(getTotalUserList));
router.post('/publishSurvey', publishSurveyValidator, catchAsync(publishSurvey));

module.exports = router;
