const { convertToMongoObjectId } = require('../../../utility/common');
const assignmentSchema = require('../assignment/assignment.model');
const assignmentReportSchema = require('./assignment.report.model');
const assignmentStudentReportSchema = require('./assignment.report.student.model');
const {
    studentGenderCount,
    getStudentListBasedOnAssignment,
    getDataFromAssignment,
    getDataAndTypesCountFromAssignment,
    constructReportCreateAndUpdateData,
    mailServiceForStaff,
    mailServiceForStudent,
    getPromptData,
    getSessionData,
    getPromptAnswer,
    getCourseOutcomeData,
    getAssignmentStudentData,
    getCurriculumOutcomeData,
    getOutcomeAndMappedOutcomeData,
    filterAssignmentsByReportType,
    filterStudentAssignmentsByReportType,
    filterAssignmentQueryByReportType,
} = require('./assignment.report.service');
const {
    DIGI_COURSE,
    USER,
    PROMPT,
    RELEASED,
    ASSIGNMENT,
    ASSIGNMENT_EVALUATION_NONE,
    RAT,
    RAT_REPORT,
    ASSIGNMENT_REPORT_TYPE,
} = require('../../../utility/constants');
const digi_course = require('mongoose').model(DIGI_COURSE);
const Users = require('mongoose').model(USER);
const assignmentSettingsModel = require('../assignment-settings/assignment-settings.model');
const assignmentPromptModel = require('../assignment-prompt/assignment-prompt.model');
const assignmentAnswerModel = require('../assignment_answer/assignment_answer.model');
const assignmentRubricsModel = require('../assignment_rubrics/assignment-rubrics.model');

const assignmentDetailsProject = {
    assignmentType: '$basic.type',
    scoringType: '$basic.scoringType',
    gradeAs: '$basic.gradeAs',
    totalScore: '$basic.totalScore',
    courseName: 1,
    ratType: 1,
};

const assignmentDetailsReQuiredData = ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { _program_id, term, year_no, level_no, _course_id, rotation_count } = query;
        if (
            !_program_id ||
            !term ||
            !year_no ||
            !level_no ||
            !_course_id ||
            !_institution_id ||
            !_institution_calendar_id
        ) {
            return { valid: false };
        }
        const institutionId = convertToMongoObjectId(_institution_id);
        const institutionCalendarId = convertToMongoObjectId(_institution_calendar_id);
        const programId = convertToMongoObjectId(_program_id);
        const courseId = convertToMongoObjectId(_course_id);
        const rotationCount = Number(rotation_count);
        const assignmentQuery = {
            _institution_id: institutionId,
            _institution_calendar_id: institutionCalendarId,
            _program_id: programId,
            _course_id: courseId,
            term,
            year_no,
            level_no,
            isDeleted: false,
            isActive: true,
            ...(rotationCount && { rotation_count: rotationCount }),
        };
        const assignmentReportQuery = {
            _institution_id: institutionId,
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            term,
            year: year_no,
            level: level_no,
            ...(rotationCount && { rotation_count: rotationCount }),
            isDeleted: false,
        };
        return {
            valid: true,
            institutionId,
            institutionCalendarId,
            programId,
            courseId,
            rotationCount,
            term,
            year_no,
            level_no,
            assignmentQuery,
            assignmentReportQuery,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const assignmentDetails = async ({ headers = {}, query = {} }) => {
    try {
        const {
            valid,
            institutionId,
            institutionCalendarId,
            programId,
            courseId,
            term,
            year_no,
            level_no,
            rotationCount,
            assignmentQuery,
        } = assignmentDetailsReQuiredData({ headers, query });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };
        // assignment type and mark count
        const assignmentTypeCountData = await getDataAndTypesCountFromAssignment({
            assignmentQuery,
            assignmentProject: assignmentDetailsProject,
        });

        // get course coordinator details
        const courseQuery = {
            _id: courseId,
            _institution_id: institutionId,
        };
        const courseAdminData = await digi_course
            .findOne(courseQuery, { coordinators: 1, course_name: 1 })
            .lean();
        const courseAdmin = {};
        if (courseAdminData && courseAdminData.coordinators) {
            for (const courseData of courseAdminData.coordinators) {
                if (
                    courseData._institution_calendar_id.toString() ===
                    institutionCalendarId.toString()
                ) {
                    courseAdmin._user_id = courseData._user_id;
                    courseAdmin.user_name = courseData.user_name;
                }
            }
        }
        const findRotationCountValue = rotationCount || 0;
        // student details
        const { totalStudentCount, maleStudentCount, femaleStudentCount } =
            await studentGenderCount({
                institutionId,
                institutionCalendarId,
                programId,
                courseId,
                term,
                year_no,
                level_no,
                rotationCount: findRotationCountValue,
            });

        const AssignmentSettings = await assignmentSettingsModel
            .findOne(
                {
                    _institution_id: institutionId,
                    _institution_calendar_id: institutionCalendarId,
                    'courseSettings.programId': programId,
                    'courseSettings.courseId': courseId,
                },
                {
                    summativeTotalMarks:
                        '$courseSettings.general.summative.totalAssignmentMarks.value',
                    summativeLimit: '$courseSettings.general.summative.assignmentLimit',
                    formativeTotalMarks:
                        '$courseSettings.general.formative.totalAssignmentMarks.value',
                    formativeLimit: '$courseSettings.general.formative.assignmentLimit',
                    summativeGradeAndStatus: '$courseSettings.evaluation.summative.scoring',
                    formativeGradeAndStatus: '$courseSettings.evaluation.formative.scoring',
                },
            )
            .lean();
        const AssignmentProgramSettingsData = await assignmentSettingsModel
            .findOne(
                {
                    _institution_id: institutionId,
                    _institution_calendar_id: institutionCalendarId,
                    'programSettings.programId': programId,
                },
                {
                    gradingSystem: '$programSettings.gradingSystem.letterGrade',
                },
            )
            .lean();
        const assignmentReportData = {
            courseDetails: {
                courseName: courseAdminData ? courseAdminData.course_name : '',
                courseId: courseAdminData ? courseAdminData._id : '',
                courseAdmin: Object.keys(courseAdmin).length !== 0 ? courseAdmin : {},
                totalStudentCount,
                maleStudentCount,
                femaleStudentCount,
            },
            tabDetails: {
                summativeAllotedMark: AssignmentSettings
                    ? AssignmentSettings.summativeTotalMarks
                    : 0,
                formativeAllotedMark: AssignmentSettings
                    ? AssignmentSettings.formativeTotalMarks
                    : 0,
                summativeGradeAndStatus: AssignmentSettings
                    ? AssignmentSettings.summativeGradeAndStatus
                    : {},
                formativeGradeAndStatus: AssignmentSettings
                    ? AssignmentSettings.formativeGradeAndStatus
                    : {},
                gradingSystem: AssignmentProgramSettingsData
                    ? AssignmentProgramSettingsData.gradingSystem
                    : {},
                ...(assignmentTypeCountData && { ...assignmentTypeCountData }),
            },
        };
        return { statusCode: 200, message: 'Assignment Data', data: assignmentReportData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const assignmentTypesDataProject = {
    assignmentTitle: '$basic.title',
    learningOutcome: '$basic.learningOutcome',
    totalScore: '$basic.totalScore',
    prompts: '$basic.prompts',
    taxonomyScope: '$basic.taxonomyScope',
    evaluators: '$evaluation.evaluators',
    subject: 1,
    createdBy: 1,
    ratId: 1,
    ratType: 1,
    ratMarks: '$basic.ratMarks',
};
const assignmentStudentProjectData = {
    assignmentId: 1,
    studentId: 1,
    studentMark: '$staffEvaluation.totalMark',
};
const assignmentTypesData = async ({ headers = {}, query = {} }) => {
    try {
        const { valid, assignmentQuery, institutionId } = assignmentDetailsReQuiredData({
            headers,
            query,
        });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };
        const {
            pageNo,
            limit,
            assignmentType,
            scoringType,
            searchKey,
            subjects,
            isTypeCountRequired,
            reportType,
        } = query;

        filterAssignmentQueryByReportType(assignmentQuery, reportType);

        assignmentQuery['basic.type'] = assignmentType;
        assignmentQuery['basic.scoringType'] = scoringType;
        if (searchKey) {
            const regexPattern = new RegExp(searchKey, 'gi');
            assignmentQuery['basic.title'] = { $regex: regexPattern };
        }
        if (subjects) {
            assignmentQuery['subject.subjectName'] = { $in: subjects };
        }
        // assignment data
        const assignmentData = await getDataFromAssignment({
            assignmentQuery,
            assignmentProject: assignmentTypesDataProject,
            limitData: { pageNo, limit },
        });
        if (assignmentData.length === 0) return { statusCode: 210, message: 'No Data Found' };

        // assignment type count data
        let assignmentTypeCountedData;
        if (isTypeCountRequired) {
            assignmentTypeCountedData = await getDataAndTypesCountFromAssignment({
                assignmentQuery,
                assignmentProject: assignmentTypesDataProject,
            });
        }
        // assignment student data
        const assignmentIds = [];
        for (const assignmentElement of assignmentData) {
            assignmentIds.push(assignmentElement._id);
        }
        const assignmentStudentDetailsQuery = {
            isDeleted: false,
            _institution_id: institutionId,
            assignmentId: { $in: assignmentIds },
        };
        const assignmentStudentData = await getStudentListBasedOnAssignment({
            assignmentQuery: assignmentStudentDetailsQuery,
            assignmentProject: assignmentStudentProjectData,
        });
        const assignmentDataIds = Object.keys(assignmentStudentData);
        for (const assignmentElement of assignmentData) {
            const assignmentId = assignmentElement._id.toString();
            if (assignmentDataIds.includes(assignmentId)) {
                assignmentElement.studentData = assignmentStudentData[assignmentId];
            }
        }
        return {
            statusCode: 200,
            message: 'Assignment Type Data',
            data: {
                assignmentData,
                ...(isTypeCountRequired && { ...assignmentTypeCountedData }),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const assignmentTypesAllDataProject = {
    assignmentTitle: '$basic.title',
    assignmentType: '$basic.type',
    scoringType: '$basic.scoringType',
    learningOutcome: '$basic.learningOutcome',
    totalScore: '$basic.totalScore',
    evaluators: '$evaluation.evaluators',
    subject: 1,
    contributor: 1,
    ratId: 1,
    ratType: 1,
    ratMarks: '$basic.ratMarks',
    assignTo: '$submission.assignTo',
};
const assignmentStudentProject = {
    assignmentId: 1,
    studentId: 1,
    staffEvaluation: 1,
    status: 1,
};
const assignmentTypesAllData = async ({ headers = {}, query = {} }) => {
    try {
        const { valid, assignmentQuery, institutionId } = assignmentDetailsReQuiredData({
            headers,
            query,
        });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };

        const { reportType } = query;
        filterAssignmentQueryByReportType(assignmentQuery, reportType);

        const assignmentData = await getDataFromAssignment({
            assignmentQuery,
            assignmentProject: assignmentTypesAllDataProject,
            shouldSkipAndLimit: true,
        });
        if (!assignmentData || assignmentData.length === 0)
            return { statusCode: 210, message: 'Assignment Data Not Found' };
        // all assignment student details
        const assignmentIds = [];
        for (const assignment of assignmentData) {
            assignmentIds.push(assignment._id);
        }
        const assignmentStudentDetailsQuery = {
            isDeleted: false,
            _institution_id: institutionId,
            assignmentId: { $in: assignmentIds },
        };
        const assignmentStudentData = await getStudentListBasedOnAssignment({
            assignmentQuery: assignmentStudentDetailsQuery,
            assignmentProject: assignmentStudentProject,
        });
        if (assignmentStudentData.size === 0)
            return { statusCode: 210, message: 'Student Data Not Found' };
        const studentDataKeys = Object.keys(assignmentStudentData);
        for (const assignment of assignmentData) {
            const objectAssignmentId = assignment._id.toString();
            if (studentDataKeys.includes(objectAssignmentId)) {
                assignment.studentDetails = assignmentStudentData[objectAssignmentId];
            }
        }

        return {
            statusCode: 200,
            message: 'Assignment Type Data',
            data: assignmentData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const assignmentStudentData = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const institutionId = convertToMongoObjectId(_institution_id);
        let { assignmentIds } = query;
        assignmentIds = assignmentIds
            .split(',')
            .map((assignmentId) => convertToMongoObjectId(assignmentId));
        const assignmentQuery = {
            isDeleted: false,
            _institution_id: institutionId,
            assignmentId: { $in: assignmentIds },
        };
        const assignmentData = await getStudentListBasedOnAssignment({
            assignmentQuery,
            assignmentProject: assignmentStudentProject,
        });
        if (assignmentData.size === 0) return { statusCode: 210, message: 'No Data Found' };

        // assignment types details
        return { statusCode: 200, message: 'Assignment Type Data', data: assignmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const staffAndStudentMailCommonService = async ({
    courseName,
    programName,
    studentId,
    staffId,
}) => {
    try {
        if (!courseName) return false;

        // student mail service
        const studentEmailData = await Users.find({ _id: { $in: studentId } }, { email: 1 }).lean();
        if (studentEmailData.length) {
            const studentMailService = await mailServiceForStudent({
                mailData: studentEmailData.map((studentElement) => studentElement.email),
                courseName,
            });
            if (!studentMailService) return false;
        }

        // staff mail service
        const assignmentReportEvaluatorData = await Users.find(
            { _id: { $in: staffId } },
            { email: 1 },
        ).lean();
        if (assignmentReportEvaluatorData.length) {
            if (!programName) return false;
            const staffMailService = await mailServiceForStaff({
                requiredMails: assignmentReportEvaluatorData.map(
                    (evaluatorElement) => evaluatorElement.email,
                ),
                courseName,
                programName,
            });
            if (!staffMailService) return false;
        }
        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createAssignmentReport = async ({ headers = {}, body = {} }) => {
    try {
        const {
            courseName,
            programName,
            assignmentIds,
            emailStaffIds,
            studentObjectId,
            assignmentReportData,
            reportStudentDataCreateBulkWrite,
            markAdjustmentIsActive,
            deductMarkIsActive,
        } = await constructReportCreateAndUpdateData({
            headers,
            body,
        });
        // create assignment report data
        const publishedAssignmentReportData = await assignmentReportSchema.create({
            ...assignmentReportData,
            ...(markAdjustmentIsActive && { 'finalReport.markAdjustment': markAdjustmentIsActive }),
            ...(deductMarkIsActive && { 'finalReport.deductMark': deductMarkIsActive }),
        });
        if (!publishedAssignmentReportData) {
            return {
                statusCode: 410,
                message: 'Error In Publish Assignment Report',
            };
        }
        // create assignment report student data
        let assignmentStudentReportDataCreate;
        if (reportStudentDataCreateBulkWrite.length) {
            assignmentStudentReportDataCreate = await assignmentStudentReportSchema.bulkWrite(
                reportStudentDataCreateBulkWrite,
            );
        }
        if (!assignmentStudentReportDataCreate) {
            return {
                statusCode: 410,
                message: 'Error In Publish Assignment Report',
            };
        }

        const assignmentUpdate = await assignmentSchema.updateMany(
            { _id: { $in: assignmentIds } },
            {
                $set: { reportCreated: true },
            },
        );

        if (!assignmentUpdate)
            return { statusCode: 410, message: 'Error In Publish Assignment Report' };

        if (emailStaffIds.length && studentObjectId.length) {
            const studentAndStaffMail = await staffAndStudentMailCommonService({
                courseName,
                programName,
                staffId: emailStaffIds,
                studentId: studentObjectId,
            });

            if (!studentAndStaffMail)
                return {
                    statusCode: 410,
                    message: 'Error In Sending Mail',
                };
        }

        return { statusCode: 200, message: 'ASSIGNMENT REPORT PUBLISHED SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatePublishedReportAndStudentData = async ({ headers = {}, body = {} }) => {
    try {
        const {
            _id,
            courseName,
            programName,
            assignmentIds,
            emailStaffIds,
            isMailRequired,
            studentObjectId,
            assignmentReportData,
            markAdjustmentIsActive,
            deductMarkIsActive,
            extraMarksIsActive,
            reportStudentDataUpdateBulkWrite,
        } = await constructReportCreateAndUpdateData({
            headers,
            body,
        });

        const updatePublishedAssignmentReportData = await assignmentReportSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            {
                $set: {
                    ...assignmentReportData,
                    'finalReport.markAdjustment': markAdjustmentIsActive,
                    'finalReport.deductMark': deductMarkIsActive,
                    ...(assignmentReportData.reportType && {
                        reportType: assignmentReportData.reportType,
                    }),
                },
                $unset: {
                    ...(!extraMarksIsActive && {
                        'extraMarks.markType': 1,
                        'extraMarks.mark': 1,
                        'extraMarks.position': 1,
                    }),
                },
            },
        );
        if (!updatePublishedAssignmentReportData) {
            return {
                statusCode: 410,
                message: 'Error In Publish Assignment Report',
            };
        }
        let assignmentStudentReportDataCreate;
        if (reportStudentDataUpdateBulkWrite.length) {
            assignmentStudentReportDataCreate = await assignmentStudentReportSchema.bulkWrite(
                reportStudentDataUpdateBulkWrite,
            );
        }
        if (!assignmentStudentReportDataCreate) {
            return {
                statusCode: 410,
                message: 'Error In Publish Assignment Report',
            };
        }

        const assignmentUpdate = await assignmentSchema.updateMany(
            { _id: { $in: assignmentIds } },
            {
                $set: { reportCreated: true },
            },
        );

        if (!assignmentUpdate)
            return { statusCode: 410, message: 'Error In Publish Assignment Report' };

        if (emailStaffIds.length || studentObjectId.length) {
            const studentAndStaffMail = await staffAndStudentMailCommonService({
                courseName,
                programName,
                staffId: emailStaffIds,
                studentId: studentObjectId,
            });

            if (!studentAndStaffMail)
                return {
                    statusCode: 410,
                    message: 'Error In Sending Mail',
                };
        }

        return {
            statusCode: 200,
            message: 'Report Updated Successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const reportStudentProject = {
    studentName: 1,
    studentId: 1,
    status: 1,
    assignmentDetails: 1,
    markAdjustment: 1,
    obtained: 1,
    extraMark: 1,
    deductMark: 1,
    finalObtainedMark: 1,
    markEquivalence: 1,
    finalObtainedPercentage: 1,
    grade: 1,
    criticalStatus: 1,
};
const reportProject = {
    staffId: 1,
    programName: 1,
    courseName: 1,
    rotation_count: 1,
    assignments: 1,
    equivalence: 1,
    extraMarks: 1,
    finalReport: 1,
    reportType: 1,
};
const assignmentAllReportData = async ({ headers = {}, query = {} }) => {
    try {
        const { valid, assignmentReportQuery } = assignmentDetailsReQuiredData({
            headers,
            query,
        });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };
        const { reportType } = query;
        assignmentReportQuery.reportType = reportType || ASSIGNMENT_REPORT_TYPE;

        const assignmentReport = await assignmentReportSchema
            .findOne(assignmentReportQuery, reportProject)
            .lean();
        if (!assignmentReport) {
            return { statusCode: 404, message: 'Assignment Report Data Not Found' };
        }
        assignmentReport.assignments = filterAssignmentsByReportType(
            assignmentReport.assignments,
            reportType,
        );

        const assignmentStudentData = await assignmentStudentReportSchema
            .find({ reportId: convertToMongoObjectId(assignmentReport._id) }, reportStudentProject)
            .lean();

        if (!assignmentStudentData) {
            return {
                statusCode: 404,
                message: 'Assignment Report Student Data Not Found',
            };
        }

        const filteredStudentData = assignmentStudentData.map((student) => {
            const filteredAssignments = filterStudentAssignmentsByReportType(
                student.assignmentDetails?.assignment,
                reportType,
            );

            return {
                ...student,
                assignmentDetails: {
                    ...student.assignmentDetails,
                    assignment: filteredAssignments,
                },
            };
        });
        const finalStudentData =
            filteredStudentData.filter(
                (student) => student.assignmentDetails.assignment.length > 0,
            ) || [];

        const assignmentReportData = {
            reportData: assignmentReport,
            reportStudentData: finalStudentData,
        };
        return { statusCode: 200, message: 'Assignment Report Data', data: assignmentReportData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const unPublishReportData = async ({ body = {} }) => {
    try {
        const { listOfStudentReportId, reportId } = body;
        if (!reportId && (!listOfStudentReportId || listOfStudentReportId.length === 0))
            return {
                statusCode: 410,
                message: 'Either reportId or listOfStudentReportId Required',
            };

        const reportStudentDataUpdateQuery = {
            ...(reportId && { reportId: convertToMongoObjectId(reportId) }),
            ...(listOfStudentReportId && {
                _id: {
                    $in: listOfStudentReportId.map((objectId) => convertToMongoObjectId(objectId)),
                },
            }),
        };

        const assignmentStudentReportDataUpdate = await assignmentStudentReportSchema.updateMany(
            reportStudentDataUpdateQuery,
            {
                status: 'draft',
            },
        );

        if (!assignmentStudentReportDataUpdate)
            return {
                statusCode: 410,
                message: 'Student Report UnPublish Update Failed',
            };

        return { statusCode: 200, message: 'UnPublished Student Report Data' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAssignmentReportStudentData = async ({ query = {}, headers = {} }) => {
    try {
        const { studentId } = query;
        const { _institution_calendar_id } = headers;
        if (!studentId)
            return {
                statusCode: 400,
                message: 'student Id Required',
            };

        const reportStudentData = await assignmentStudentReportSchema.aggregate([
            {
                $match: {
                    studentId: convertToMongoObjectId(studentId),
                    status: 'publish',
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
            },
            {
                $lookup: {
                    from: 'assignment_reports',
                    localField: 'reportId',
                    foreignField: '_id',
                    as: 'report_data',
                },
            },
            {
                $unwind: {
                    path: '$report_data',
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'report_data.staffId',
                    foreignField: '_id',
                    as: 'staffName',
                },
            },
            {
                $unwind: {
                    path: '$staffName',
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'studentId',
                    foreignField: '_id',
                    as: 'userId',
                },
            },
            {
                $unwind: {
                    path: '$userId',
                },
            },
            {
                $project: {
                    studentId: 1,
                    studentName: 1,
                    reportId: 1,
                    status: 1,
                    report_data: 1,
                    'staffName.name': 1,
                    'userId.user_id': 1,
                    assignmentDetails: 1,
                    markAdjustment: 1,
                    obtained: 1,
                    extraMark: 1,
                    deductMark: 1,
                    finalObtainedMark: 1,
                    markEquivalence: 1,
                    finalObtainedPercentage: 1,
                    grade: 1,
                    criticalStatus: 1,
                },
            },
        ]);

        if (reportStudentData.length === 0)
            return {
                statusCode: 404,
                message: 'Assignment Student Report Data Not Found',
            };
        const assignmentId = [];
        for (const studentData of reportStudentData) {
            for (const assignment of studentData.assignmentDetails.assignment) {
                assignmentId.push(assignment.assignmentId);
            }
        }
        const assignmentQuery = {
            _id: { $in: assignmentId },
        };
        const assignmentData = await getDataFromAssignment({
            assignmentQuery,
            assignmentProject: assignmentTypesAllDataProject,
            shouldSkipAndLimit: true,
        });
        if (!assignmentData || assignmentData.length === 0)
            return { statusCode: 210, message: 'Assignment Data Not Found' };
        return {
            data: {
                reportStudentData,
                assignmentData,
                user_id: reportStudentData[0].userId.user_id,
                studentName: reportStudentData[0].studentName,
                staffName: reportStudentData[0].staffName.name,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const publishIndividualAssignmentReport = async ({ body = {}, headers = {} }) => {
    try {
        const { reportId, studentData, commonFieldData, courseName } = body;
        const { _institution_calendar_id } = headers;
        if (!reportId) return { statusCode: 400, message: 'reportId Required' };
        if (!studentData || Object.keys(studentData).length === 0)
            return { statusCode: 400, message: 'Student Data Required' };
        if (!commonFieldData || Object.keys(commonFieldData).length === 0)
            return { statusCode: 400, message: 'Common Field Data Required' };

        const {
            studentName,
            obtained,
            finalObtainedMark,
            assignment,
            deductMark,
            criticalStatus,
            status,
            finalObtainedPercentage,
            studentId,
            grade,
            markAdjustment,
            extraMark,
            markEquivalence,
        } = studentData;
        const {
            markAdjustmentIsActive,
            deductMarkIsActive,
            isAssignment,
            isMarkAdjustment,
            isMarkAdjustmentReason,
            isObtained,
            isExtraMark,
            isDeductMark,
            isDeductReason,
            isFinalObtainedMark,
            isMarkEquivalence,
            isFinalObtainedPercentage,
            isGrade,
            isCriticalStatus,
        } = commonFieldData;
        if (!status) return { statusCode: 400, message: 'Student Status Required' };
        const assignmentData = [];
        for (const assignmentObject of assignment) {
            assignmentData.push({
                assignmentId: convertToMongoObjectId(assignmentObject.assignmentId),
                assignmentName: assignmentObject.assignmentName,
                assignmentMark: assignmentObject.assignmentMark,
                studentMark: assignmentObject.studentMark,
                adjustMark: assignmentObject.adjustMark,
            });
        }
        const assignmentReportStudentData = {
            ...(studentName && { studentName }),
            ...(reportId && { reportId: convertToMongoObjectId(reportId) }),
            ...(studentId && { studentId }),
            ...(status && { status }),
            ...(assignmentData.length !== 0 && {
                'assignmentDetails.assignment': assignmentData,
                'assignmentDetails.isChecked': isAssignment,
            }),
            ...(markAdjustment && {
                'markAdjustment.isActive': markAdjustmentIsActive,
                'markAdjustment.isChecked': isMarkAdjustment,
                'markAdjustment.mark': markAdjustment.mark,
                'markAdjustment.reasonForMarkUpdate.reason': markAdjustment.reasonForMarkUpdate,
                'markAdjustment.reasonForMarkUpdate.isChecked': isMarkAdjustmentReason,
            }),
            ...(obtained && {
                'obtained.mark': obtained.mark,
                'obtained.isChecked': isObtained,
                'obtained.percentage': obtained.percentage,
            }),
            ...(extraMark && {
                'extraMark.mark': extraMark,
                'extraMark.isChecked': isExtraMark,
            }),
            ...(deductMark && {
                'deductMark.isActive': deductMarkIsActive,
                'deductMark.isChecked': isDeductMark,
                'deductMark.mark': deductMark.mark,
                'deductMark.reasonForMarkUpdate.reason': deductMark.reasonForMarkUpdate,
                'deductMark.reasonForMarkUpdate.isChecked': isDeductReason,
            }),
            ...(finalObtainedMark && {
                'finalObtainedMark.mark': finalObtainedMark,
                'finalObtainedMark.isChecked': isFinalObtainedMark,
            }),
            ...(markEquivalence && {
                'markEquivalence.equivalenceMark': markEquivalence.equivalenceMark,
                'markEquivalence.studentMark': markEquivalence.studentMark,
                'markEquivalence.isChecked': isMarkEquivalence,
            }),
            ...(finalObtainedPercentage && {
                'finalObtainedPercentage.percentage': finalObtainedPercentage,
                'finalObtainedPercentage.isChecked': isFinalObtainedPercentage,
            }),
            ...(grade && {
                'grade.studentGrade': grade,
                'grade.isChecked': isGrade,
            }),
            ...(criticalStatus && {
                'criticalStatus.status': criticalStatus,
                'criticalStatus.isChecked': isCriticalStatus,
            }),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        };

        const publishedAssignmentReportStudentData = await assignmentStudentReportSchema.updateOne(
            {
                reportId: convertToMongoObjectId(reportId),
                studentId: convertToMongoObjectId(studentData.studentId),
            },
            assignmentReportStudentData,
        );
        if (!publishedAssignmentReportStudentData)
            return {
                statusCode: 410,
                message: 'Error In Publish Assignment Report',
            };

        //mail service
        if (courseName) {
            const studentMailService = await staffAndStudentMailCommonService({
                courseName,
                studentId: [studentData.studentId],
            });

            if (!studentMailService)
                return {
                    statusCode: 410,
                    message: 'Error In Sending Mail',
                };
        }
        return { statusCode: 200, message: 'Report Published' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteExistingReportData = async ({ body = {} }) => {
    try {
        const { reportId, assignmentIds } = body;
        if (!reportId) return { statusCode: 400, message: 'reportId Required' };
        const assignmentQuery = {
            _id: {
                $in: assignmentIds.map((assignmentIdElement) =>
                    convertToMongoObjectId(assignmentIdElement),
                ),
            },
        };

        const assignmentUpdate = await assignmentSchema.updateMany(assignmentQuery, {
            reportCreated: false,
        });

        if (!assignmentUpdate) return { statusCode: 404, message: 'Assignment Data No Found' };

        const deletedReportData = await assignmentReportSchema.updateOne(
            {
                _id: convertToMongoObjectId(reportId),
            },
            {
                isDeleted: true,
            },
        );
        if (!deletedReportData)
            return {
                statusCode: 410,
                message: 'Report Data Delete Failed',
            };
        const deletedStudentReportData = await assignmentStudentReportSchema.updateMany(
            {
                reportId: convertToMongoObjectId(reportId),
            },
            {
                isDeleted: true,
            },
        );
        if (!deletedStudentReportData)
            return {
                statusCode: 410,
                message: 'Report Data Delete Failed',
            };
        return { statusCode: 200, message: 'Deleted Report Data' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getFrameworkDetails = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        let { _institution_id } = headers;
        let { _program_id, _course_id } = query;

        if (!_course_id || !_program_id)
            return {
                statusCode: 400,
                message: `${!_course_id ? 'Course ID' : ''}${
                    !_course_id && !_program_id ? ' and ' : ''
                }${!_program_id ? 'Program ID' : ''} is Empty or Undefined`,
            };

        _course_id = convertToMongoObjectId(_course_id);
        _program_id = convertToMongoObjectId(_program_id);
        _institution_id = convertToMongoObjectId(_institution_id);

        const sloList = await getSessionData({
            _course_id,
            _program_id,
            _institution_id,
        });

        const curriculumData = await getCurriculumOutcomeData({
            _program_id,
            _institution_id,
        });

        if (!curriculumData)
            return {
                statusCode: 404,
                message: 'Curriculum Data Not Found',
            };

        const courseData = await getCourseOutcomeData({
            _course_id,
            _program_id,
            _institution_id,
            _curriculum_id: curriculumData.curriculamId,
        });

        if (!courseData)
            return {
                statusCode: 404,
                message: 'Course Data Not Found',
            };

        const updatedOutcomeData = await getOutcomeAndMappedOutcomeData({
            courseData,
            curriculumData,
        });

        if (!updatedOutcomeData)
            return { statusCode: 404, message: 'Error In Getting Outcome Data' };

        updatedOutcomeData.sloList = sloList;
        updatedOutcomeData.courseId = courseData.courseId;

        return {
            message: 'DATA RETRIEVED SUCCESSFULLY',
            statusCode: 200,
            data: updatedOutcomeData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getOutcomeBasedAssignmentDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { valid, assignmentQuery } = assignmentDetailsReQuiredData({ headers, query });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };

        const mergedAssignmentQuery = {
            $and: [
                assignmentQuery,
                { 'basic.learningOutcome': { $exists: true } },
                { $expr: { $gt: [{ $size: '$basic.learningOutcome.lo' }, 0] } },
            ],
            'submission.assignTo.isAllStudent': true,
        };

        const assignmentProject = {
            _id: 1,
            'basic.type': 1,
            'basic.title': 1,
            'basic.prompts': 1,
            'basic.taxonomy': 1,
            'basic.evaluation': 1,
            'basic.totalScore': 1,
            'basic.scoringType': 1,
            'basic.learningOutcome': 1,
        };

        const allAssignmentData = await assignmentSchema
            .find(mergedAssignmentQuery, assignmentProject)
            .lean();

        if (allAssignmentData.length === 0)
            return { statusCode: 404, message: 'Assignment data No found' };

        const { promptId, studentData } = await getAssignmentStudentData({
            assignmentData: allAssignmentData,
        });

        if (!studentData) return { statusCode: 404, message: 'Student Data No Found' };

        const { promptData, parentPromptData } = await getPromptData({
            promptId,
            assignmentData: allAssignmentData,
        });

        if (promptId.length && !promptData)
            return {
                statusCode: 404,
                message: 'Error in getting assignment prompt data',
            };
        let promptAnswerData;

        if (promptData) {
            promptAnswerData = await getPromptAnswer({
                promptId,
                studentData,
                parentPromptData,
            });

            if (!promptAnswerData)
                return { statusCode: 410, message: 'Error in getting prompt answer data' };
        }

        //construct student and assignment data
        const studentKeys = Object.keys(studentData);
        for (const assignmentKeyElement of allAssignmentData) {
            const assignmentStudentData = [];
            for (const studentKeysElement of studentKeys) {
                const assignmentId = studentKeysElement.split(',');
                if (assignmentKeyElement._id.toString() === assignmentId[0]) {
                    const individualStudentData = studentData[studentKeysElement];
                    assignmentStudentData.push(individualStudentData[0]);
                }
            }
            assignmentKeyElement.studentData = assignmentStudentData;
        }

        return {
            statusCode: 200,
            message: 'DATA RETRIEVED SUCCESSFULLY',
            data: allAssignmentData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//Rubric Report

const getRubricReportAssignmentsList = async ({ headers = {}, query = {} }) => {
    try {
        const { valid, assignmentQuery } = assignmentDetailsReQuiredData({ headers, query });
        if (!valid) return { statusCode: 400, message: 'All Filter Field Data Required' };
        const assignmentProject = {
            _id: 1,
            evaluationType: '$basic.evaluation.type',
            rubricId: '$basic.evaluation.rubricsId',
            assignmentTitle: '$basic.title',
        };
        const allAssignmentData = await assignmentSchema
            .find(
                {
                    ...assignmentQuery,
                    // 'basic.prompts': { $ne: [] },
                    'basic.evaluation.type': { $ne: ASSIGNMENT_EVALUATION_NONE },
                },
                assignmentProject,
            )
            .lean();
        if (!allAssignmentData.length)
            return { statusCode: 404, message: 'Assignment data Not found' };
        return {
            statusCode: 200,
            message: 'DATA RETRIEVED SUCCESSFULLY',
            data: allAssignmentData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getRubricReportPromptList = async ({ headers = {}, query = {} }) => {
    try {
        const { assignmentId, rubricId, evaluationType } = query;
        const { _institution_id } = headers;
        if (!assignmentId || !_institution_id) {
            return { statusCode: 400, message: 'Missing required fields' };
        }
        let promptArray = [];
        if (evaluationType !== ASSIGNMENT) {
            promptArray = await assignmentPromptModel
                .find(
                    {
                        _institution_id,
                        assignmentId,
                        isDeleted: false,
                    },
                    {
                        assignmentId: 1,
                        rubricsId: 1,
                        promptName: '$score.questionText',
                    },
                )
                .lean();
            if (!promptArray.length) return { statusCode: 404, message: 'Prompt data Not found' };
        }
        const rubricsIds = promptArray.map((promptElement) => promptElement.rubricsId);
        const rubricsData = await assignmentRubricsModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id:
                        evaluationType === ASSIGNMENT
                            ? convertToMongoObjectId(rubricId)
                            : {
                                  $in: rubricsIds.map((rubricIdElement) =>
                                      convertToMongoObjectId(rubricIdElement),
                                  ),
                              },
                    isDeleted: false,
                },
                {
                    name: 1,
                    rubrics: 1,
                },
            )
            .lean();
        if (rubricsData.length === 0) return { statusCode: 404, message: 'Rubric data Not found' };

        return {
            statusCode: 200,
            message: 'DATA RETRIEVED SUCCESSFULLY',
            data: { promptArray, rubricsData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getRubricReportData = async ({ headers = {}, query = {} }) => {
    try {
        const { assignmentId, evaluationType } = query;
        const { _institution_id } = headers;
        const selectedAssignmentData = await assignmentSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(assignmentId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    assignmentTitle: '$basic.title',
                    totalScore: '$basic.totalScore',
                    prompts: '$basic.prompts',
                    evaluationTool: '$basic.evaluation.type',
                    assessmentType: '$basic.type',
                    examCategory: '$basic.category',
                    subject: 1,
                    course_code: 1,
                    courseName: 1,
                },
            )
            .lean();
        if (!selectedAssignmentData)
            return { statusCode: 404, message: 'Assignment data Not found' };
        const studentList = await assignmentAnswerModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    assignmentId: convertToMongoObjectId(assignmentId),
                    'staffEvaluation.allStaffData': { $ne: [] },
                    'staffEvaluation.staffStatus': RELEASED,
                    isDeleted: false,
                },
                {
                    assignmentId: 1,
                    staffEvaluation: 1,
                    studentId: 1,
                    promptsAnswer: 1,
                },
            )
            .populate({ path: 'studentId', select: { name: 1, user_id: 1 } })
            .populate({
                path: 'staffEvaluation.allStaffData.staffId',
                select: { name: 1, user_id: 1 },
            })
            .populate({
                path: 'promptsAnswer',
                foreignField: 'assignmentPromptId',
                select: {
                    studentId: 1,
                    assignmentPromptId: 1,
                    staffEvaluation: 1,
                },
            })
            .lean();
        if (!studentList.length) return { statusCode: 404, message: 'Student data Not found' };
        let filteredStudentList = studentList;
        if (evaluationType === PROMPT) {
            filteredStudentList = studentList.map((studentElement) => ({
                ...studentElement,
                promptsAnswer:
                    studentElement.promptsAnswer.length > 0
                        ? studentElement.promptsAnswer.flatMap((promptArrayElement) => {
                              if (!promptArrayElement) return [];
                              if (Array.isArray(promptArrayElement)) {
                                  return promptArrayElement.filter(
                                      (promptElement) =>
                                          promptElement.studentId.toString() ===
                                          studentElement.studentId._id.toString(),
                                  );
                              }
                              return (
                                  promptArrayElement.studentId.toString() ===
                                      studentElement.studentId._id.toString() && promptArrayElement
                              );
                          })
                        : [],
            }));
        }
        return {
            statusCode: 200,
            message: 'DATA RETRIEVED SUCCESSFULLY',
            data: {
                selectedAssignmentData,
                allStudentData: filteredStudentList,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    assignmentDetails,
    getFrameworkDetails,
    createAssignmentReport,
    assignmentAllReportData,
    assignmentTypesData,
    assignmentTypesAllData,
    assignmentStudentData,
    getOutcomeBasedAssignmentDetails,
    updatePublishedReportAndStudentData,
    unPublishReportData,
    listAssignmentReportStudentData,
    publishIndividualAssignmentReport,
    deleteExistingReportData,
    getRubricReportAssignmentsList,
    getRubricReportPromptList,
    getRubricReportData,
};
