const mongoose = require('mongoose');
const { ASSIGNMENT_CATEGORY } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const assignmentCategorySchemas = new Schema(
    {
        category: { type: String },
        _course_id: {
            type: ObjectId,
        },
        _institution_id: {
            type: ObjectId,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSIGNMENT_CATEGORY, assignmentCategorySchemas);
