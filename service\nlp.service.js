const Sentiment = require('sentiment');
const { analyze } = new Sentiment();
const {
    SENTIMENT_ANALYSIS: { POSITIVE, NEGATIVE, NEUTRAL },
} = require('../lib/utility/constants');
const analyzeSentiment = (inputTextArray) => {
    try {
        if (Array.isArray(inputTextArray)) {
            // const studentResults = [];
            const sentimentValues = [];
            for (const inputTextArrayElement of inputTextArray) {
                const analysis = analyze(inputTextArrayElement);
                sentimentValues.push(analysis.score);
                // studentResults.push({
                //     text: inputTextArrayElement,
                //     sentiment:
                //         analysis.positive > analysis.negative
                //             ? POSITIVE
                //             : analysis.positive < analysis.negative
                //             ? NEGATIVE
                //             : NEUTRAL,
                //     score: analysis.score,
                // });
            }
            const positiveCount = sentimentValues.filter((value) => value > 0).length;
            const negativeCount = sentimentValues.filter((value) => value < 0).length;
            if (positiveCount > negativeCount) {
                return POSITIVE;
            }
            if (negativeCount > positiveCount) {
                return NEGATIVE;
            }
            return NEUTRAL;
        }
        const analyzedValue = analyze(inputTextArray);
        if (analyzedValue.score < 0) {
            return NEGATIVE;
        }
        if (analyzedValue.score === 0) {
            return NEUTRAL;
        }
        return POSITIVE;
    } catch (error) {
        console.error('Error during sentiment analysis:', error);
        // Handle errors appropriately (e.g., return a default sentiment value)
        return 'Unknown sentiment'; // Example placeholder
    }
};

module.exports = { analyzeSentiment };
