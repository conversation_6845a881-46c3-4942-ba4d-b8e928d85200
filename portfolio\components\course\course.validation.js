const { Joi } = require('../../common/middlewares/validation');

const getUserCoursesSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: Joi.string().hex().length(24).required(),
        type: Joi.string().valid('student', 'staff').required(),
    }),
}).unknown(true);

const getStudentGroupSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: Joi.string().hex().length(24).required(),
        programId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getFacultyListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: Joi.string().hex().length(24).required(),
        programId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getCoursesByProgramSchema = Joi.object({
    query: Joi.object({
        programId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: Joi.string().hex().length(24).required(),
        courseId: Joi.string().hex().length(24).required(),
        institutionCalendarId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

const getDeliveryTypesSchema = Joi.object({
    query: Joi.object({
        courseId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

module.exports = {
    getUserCoursesSchema,
    getStudentGroupSchema,
    getFacultyListSchema,
    getCoursesByProgramSchema,
    getPortfolioSchema,
    getDeliveryTypesSchema,
};
