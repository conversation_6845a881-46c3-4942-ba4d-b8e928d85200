const crypto = require('crypto');
const { CRYPTO_KEY, FE_CRYPTO_KEY } = require('./util_keys');
const { SERVER } = require('./constants');
const algorithm = { TYPE: 'aes-256-cbc', IV_SIZE: 16 };

exports.encryptData = ({ content = {}, handledBy = SERVER }) => {
    const iv = crypto.randomBytes(algorithm.IV_SIZE);
    const encryptContent = typeof content === 'object' ? JSON.stringify(content) : content;
    const cipher = crypto.createCipheriv(
        algorithm.TYPE,
        handledBy === SERVER ? CRYPTO_KEY : FE_CRYPTO_KEY,
        iv,
    );
    const encrypted = Buffer.concat([cipher.update(encryptContent), cipher.final(), iv]);
    return encrypted.toString('hex');
};

exports.decryptData = ({ content = {}, handledBy = SERVER }) => {
    let decryptContent = Buffer.from(content, 'hex');
    const iv = decryptContent.slice(-algorithm.IV_SIZE);
    decryptContent = decryptContent.slice(0, -algorithm.IV_SIZE);
    const decipher = crypto.createDecipheriv(
        algorithm.TYPE,
        handledBy === SERVER ? CRYPTO_KEY : FE_CRYPTO_KEY,
        iv,
    );
    const decrypted = Buffer.concat([decipher.update(decryptContent), decipher.final()]);
    return JSON.parse(decrypted);
};
