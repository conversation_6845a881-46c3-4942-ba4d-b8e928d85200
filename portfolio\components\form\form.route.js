const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const FormController = require('./form.controller');
const {
    getFormsSchema,
    updateFormSchema,
    deleteFormSchema,
    getFormsByUserSchema,
    publishFormSchema,
    getAssignedRolesSchema,
} = require('./form.validation');

router.get('/', validate(getFormsSchema), catchAsync(FormController.getForms));

router.put('/', validate(updateFormSchema), catchAsync(FormController.updateForm));

router.delete('/', validate(deleteFormSchema), catchAsync(FormController.deleteForm));

router.get('/user', validate(getFormsByUserSchema), catchAsync(FormController.getFormsByUser));

router.put('/publish', validate(publishFormSchema), catchAsync(FormController.publishForm));

router.get('/type', catchAsync(FormController.getFormType));

router.get(
    '/assigned-role',
    validate(getAssignedRolesSchema),
    catchAsync(FormController.getAssignedRolesInForm),
);

module.exports = router;
