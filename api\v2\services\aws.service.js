const AWS = require('aws-sdk');

const { AWS_ACCESS_KEY_V2, AWS_SECRET_KEY_V2 } = require('../utility/util_keys');

const s3 = new AWS.S3({
    accessKeyId: AWS_ACCESS_KEY_V2,
    secretAccessKey: AWS_SECRET_KEY_V2,
    httpOptions: { timeout: 3600000, connectTimeout: 3600000 },
});

const getS3Object = ({ bucket, key }) =>
    s3
        .getObject({
            Bucket: bucket,
            Key: key,
        })
        .promise();

const putS3Object = ({ bucket, key, body }) =>
    s3
        .putObject({
            Bucket: bucket,
            Key: key,
            Body: body,
        })
        .promise();

const deleteS3Object = ({ bucket, key }) =>
    s3
        .deleteObject({
            Bucket: bucket,
            Key: key,
        })
        .promise();

const getS3SignedUrl = ({ bucket, key, expiresInSec = 28800 /* 8 hours */ }) => {
    const url = s3.getSignedUrlPromise('getObject', {
        Bucket: bucket,
        Key: key,
        Expires: expiresInSec,
    });
    return url;
};

const getS3UnsignedUrl = ({ bucket, key, expiresInSec = 604800 /* 1 week */ }) =>
    s3.getSignedUrlPromise('getObject', {
        Bucket: bucket,
        Key: key,
        Expires: expiresInSec,
    });

module.exports = {
    s3,
    getS3Object,
    putS3Object,
    deleteS3Object,
    getS3SignedUrl,
    getS3UnsignedUrl,
};
