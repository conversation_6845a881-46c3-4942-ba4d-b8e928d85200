const topic = require('./topic_model');
const base_control = require('../base/base_controller');
const common_files = require('../../utility/common');
const topic_formate = require('./topic_formate');
const ObjectId = require('mongodb').ObjectID;
const course = require('../course/course_model');
const constant = require('../../utility/constants');

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        { $unwind: '$course' },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(topic, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "topic list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ topic_formate.topic(doc.data));
        // common_files.list_all_response(res, 200, true, "topic list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* topic_formate.topic(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COURSE, localField: '_course_id', foreignField: '_id', as: 'course' } },
        { $unwind: '$course' },
    ];
    let doc = await base_control.get_aggregate(topic, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "topic details", /* doc.data */topic_formate.topic_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let checks = await base_control.check_id(course, { _id: { $in: req.body._course_id }, 'isDeleted': false });
    if (checks.status) {
        let aggre = [
            { $match: { '_course_id': ObjectId(req.body._course_id) } },
            { $match: { 'label': req.body.label } },
            { $match: { 'isDeleted': false } }
        ];
        let label_check = await base_control.get_aggregate_duplicate_check(topic, aggre);
        //console.log(label_check);
        if (label_check.data.length == 0) {
            let doc = await base_control.insert(topic, req.body);
            if (doc.status) {
                common_files.com_response(res, 201, true, "topic Added successfully", doc.data);
            } else {
                common_files.com_response(res, 500, false, "Error", doc.data);
            }
        } else {
            common_files.com_response(res, 500, false, "Error", 'Check Label getting duplicate');
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._course_id != undefined) {
        checks = await base_control.check_id(course, { _id: { $in: req.body._course_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(topic, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "topic update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(topic, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "topic deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(topic, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "topic List", topic_formate.topic_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};