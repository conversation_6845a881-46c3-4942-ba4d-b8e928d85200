let department_formate = require('../department/department_formate');
const department_subject = require('../department_subject/department_subject_formate');

exports.department_division_ID_Only = (doc) => {
    let obj = {
        _id: doc._id,
        title: doc.title,
        department: doc._department_id,
        subject: doc._subject_id,
        isActive: doc.isActive,
        isDeleted: doc.isDeleted
    }
    return obj;
}

module.exports = {
    department_division: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                title: element.title,
                department: department_formate.department_ID_Only(element.department),
                subject: department_subject.department_subject_ID_Array_Only(element.subject),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    department_division_ID: (doc) => {
        let obj = {
            _id: doc._id,
            title: doc.title,
            department: department_formate.department_ID_Only(doc.department),
            subject: department_subject.department_subject_ID_Array_Only(doc.subject),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    department_division_ID_Onlys: (doc) => {
        let obj = {
            _id: doc._id,
            title: doc.title,
            department: doc._department_id,
            subject: doc._subject_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    department_division_ID_Subject: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                title: element.title,
                department: element._department_id,
                subject: department_subject.department_subject_ID_Array_Only(element.subjects),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    department_division_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                title: element.title,
                department: element._department_id,
                subject: element._subject_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },
}