const express = require('express');
const route = express.Router();
const validator = require('./lms_review_validator');
const lms_review = require('./lms_review_controller');
const file_upload = require('../utility/file_upload');
const multer = require('multer');
const {
    program_course_list,
    students_register,
    course_students_register,
    students_attendance_sheet,
    reason_update,
    updateStudentAbsencePercentage,
    criteriaManipulatedStudentList,
    leave_list_web,
    students_attendance_sheet_status,
    studentAttendanceHistory,
} = require('./lms_student_controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const leave_reason_doc = file_upload.uploadfile2.fields([
    { name: '_leave_reason_doc', maxCount: 1 },
]);
route.get(
    '/attendance_history/:scheduleId/:studentId',
    [
        userPolicyAuthentication([
            'leave_management:student_register:attendance_sheet:view',
            'global_search:dashboard:view',
        ]),
    ],
    studentAttendanceHistory,
);
route.get(
    '/students_attendance_sheet/:institutionCalendarId/:programId/:courseId/:year_no/:levelNo/:term/:type/:user_id',
    [
        userPolicyAuthentication([
            'leave_management:student_register:attendance_sheet:view',
            'global_search:dashboard:view',
        ]),
    ],
    students_attendance_sheet,
);
route.put(
    '/reason_update/:schedule_id/:student_id',
    [
        userPolicyAuthentication([
            'leave_management:student_register:attendance_sheet:edit',
            'global_search:dashboard:view',
        ]),
    ],
    validator.reason_update,
    reason_update,
);
// Student Flow
route.put('/update_student_absence_percentage', updateStudentAbsencePercentage);
route.get(
    '/criteria_manipulation_absence/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:gender/:absencePercentage',
    criteriaManipulatedStudentList,
);

route.get('/list', lms_review.list_leaves);
route.get(
    '/report_absence/:user_type/:_institution_calendar_id',
    lms_review.list_leaves_report_absence,
);

route.get('/student/:_institution_calendar_id', lms_review.list_student_leaves);
route.get('/:id', lms_review.list_leave_id);
//route.get('/user/:user_id', lms_review.list_leave_user_id);
route.get('/user/:user_id/:inst_cal_id/:leave_type', lms_review.my_leaves); //My Leaves--->Not used this.Some other api is working

route.get(
    '/user_s/:user_id/:type/:inst_cal_id/:role_id',
    lms_review.list_leave_user_type_and_leave_type,
); // Staff Leave
// route.get('/user_s/:user_type/:type/:role/:inst_cal_id', lms_review.list_leave_user_type_and_leave_type); // Staff Leave

route.get('/leave_search/:user_type/:type/:role/:inst_cal_id/:text', lms_review.leave_search); // Staff Leave
route.get(
    '/user/leave_overview/:user_id/:user_type/:type/:inst_cal_id',
    lms_review.user_leave_overview,
);
route.get(
    '/check_available/:institutionCalendarId/:userId/:startDate/:endDate',
    lms_review.list_schedule_based_date,
);
route.get(
    '/get_student_register/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:gender',
    students_register,
);
route.get(
    '/get_course_students_register/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:gender',
    [userPolicyAuthentication(['leave_management:student_register:view'])],
    course_students_register,
);
route.get('/student_list/:inst_cal_id/:programId', lms_review.getStudentListByProgramId);
route.get('/leave_list_web/:institutionCalendarId/:userId', leave_list_web);
route.get(
    '/program_course_list/:institutionCalendarId/:userId/:roleId',
    [userPolicyAuthentication(['leave_management:student_register:view'])],
    program_course_list,
);
route.get('/permission_overview/:user_type/:user_id/:inst_cal_id', lms_review.permission_overview);
route.get('/report_absence_list/:user_type/:inst_cal_id', lms_review.report_absence_list);
route.get('/get_report_student_absence/:id', lms_review.get_report_student_absence);
route.get('/list', lms_review.list_leaves);
route.get('/student/:_institution_calendar_id', lms_review.list_student_leaves);
route.get('/:institution/:user/:type', lms_review.list_lms);
route.get('/:id', lms_review.list_leave_id);

route.post(
    '/',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    /* validator.apply_leave, */ lms_review.insert_apply_leave,
);

route.post(
    '/report_staff_absence',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    /* validator.staff_report_absence_insert, */ lms_review.insert_report_staff_absence,
);

route.put(
    '/report_staff_absence/:id',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    validator.apply_leave_id,
    validator.staff_report_absence_insert,
    lms_review.update_report_staff_absence,
);

route.post(
    '/edit/:id',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    /* validator.apply_leave, */ lms_review.update_leave_permission_on_duty,
);

route.put('/update_leave_flow_process/:lmsReviewId', lms_review.update_leave_flow_process);
route.put(
    '/report_absence/cancel/:id',
    validator.apply_leave_id,
    /* validator.cancel_leave, */ lms_review.cancel_leave,
);
route.put(
    '/cancel/:id',
    validator.apply_leave_id,
    /* validator.cancel_leave, */ lms_review.cancel_leave,
);
route.put('/leave/:id', validator.apply_leave_id, lms_review.update_leave);
route.put('/:id', validator.apply_leave_id, lms_review.update_apply_leave);

//Student Leave Management System
route.post(
    '/student_report_absence_insert',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    /* validator.student_report_absence_insert ,*/ lms_review.student_report_absence_insert,
);
route.put(
    '/student_report_absence_update/:id',
    (req, res, next) => {
        leave_reason_doc(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    /* validator.student_report_absence_insert ,*/ lms_review.student_report_absence_update,
);
route.delete('/delete_report_student_absence/:id', lms_review.delete_report_student_absence);
module.exports = route;
