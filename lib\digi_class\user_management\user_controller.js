const User = require('../../models/user');
const Course = require('../../models/digi_course');
const Program = require('../../models/digi_programs');
const InstitutionCalendar = require('../../models/institution_calendar');
const DepartmentSubject = require('../../models/digi_department_subject');
const studentGroupSchema = require('../../models/student_group');
const { hashSync, compareSync } = require('bcryptjs');
const { update, get } = require('../../base/base_controller');
const assessmentLibrarySchema = require('../../../commonService/api/attainment_module/assessment-library/assessment-library.model');
const roleAssignSchema = require('../../models/role_assign');
const courseScheduleSchema = require('../../models/course_schedule');
const parentUserSchema = require('../../models/parent.user');
const courseSchedule = require('../../models/course_schedule');
const childEmergencySchema = require('../../models/childEmergency');
const curriculumSchema = require('../../models/digi_curriculum');
const scheduleAnomalySchema = require('../../models/schedule_anomaly');
const institutionSchema = require('../../models/institution');
const userDeviceDetailSchema = require('../../models/userDeviceDetails');
const globalSettingSchema = require('../../global_session_settings/global_session_settings_model');
const { sendNotificationPush } = require('../../../service/pushNotification.service');
const {
    convertToMongoObjectId,
    response_function,
    sendResponse,
    responseFunctionWithRequest,
    sendResponseWithRequest,
} = require('../../utility/common');
const {
    getFourDigitOTP,
    timestampNow,
    send_email,
    send_sms,
    send_otp_to_slack,
    getSecondsDifference,
    deleteFileOrFolderUtil,
    getSignedURL,
    getSixDigitOTP,
    nameFormatter,
} = require('../../utility/common_functions');
const {
    OTP_EXPIRY_DURATION_IN_SECS,
    logger,
    SERVICES,
    AUTH_PASSWORD_SYNC,
} = require('../../utility/util_keys');
const { generateAuthTokens } = require('../../utility/token.util');
const {
    PRIMARY,
    DC_STAFF,
    PUBLISHED,
    REDIS_FOLDERS: { USER_INSTITUTION_CALENDAR },
    USER,
    PRESENT,
    ABSENT,
    COMPLETED,
    CLO,
    SLO,
    PLO,
    DS_TYPE_STUDENT,
    SSO_PROVIDER,
    CLIENT_END,
} = require('../../utility/constants');
const { sendAnnouncement } = require('../../announcement/announcement_service');
const { digiAuthService } = require('../../../digi-auth');
// const { getInstitutionCalendar } = require('../../../service/cache.service');
const { getCourses } = require('../course_session/course_session_service');
const { redisClient } = require('../../../config/redis-connection');
const {
    getCourseCLO,
    getCourseSLO,
} = require('../../../commonService/api/attainment_module/assessment-library/assessment-library.service');
const { msTeamUserData } = require('../../../service/teams.service');
const { userRoleGet } = require('./user.service');
const { encryptedRoutes } = require('../../../service/endpoint.util');
const { defaultPolicy } = require('../../../middleware/policy.middleware');

// user login staff and student
exports.login = async (req, res) => {
    try {
        const {
            body: { email, password, user_type, fcm_token, device_type },
        } = req;
        logger.info('userController -> login -> %s Login %s - start', email, device_type);
        // user query and project
        const userQuery = { email, isDeleted: false, isActive: true };
        const userProject = {};
        // get user details
        const { status, data: user } = await get(User, userQuery, userProject);
        // if user not found
        if (!status) {
            logger.warn('userController -> login -> %s Email id Not found', email);
            return sendResponse(res, 200, false, 'Email id Not found', null);
        }
        //  if check user completed profile verification
        if (user.status !== 'completed') {
            logger.warn(
                'userController -> login -> %s Your Profile is not completed ask Admin',
                email,
            );
            return sendResponse(res, 200, false, 'Your Profile is not completed ask Admin', null);
        }
        //   check user face verification
        if (!user.verification && !user.mobile) {
            logger.warn('userController -> login -> %s Only signed user able to access ', email);
            return sendResponse(res, 200, false, 'Only signed user able to access ', null);
        }
        //  if check user using application type
        if (user_type && user.user_type !== user_type) {
            logger.warn('userController -> login -> %s Email id Not found check App', email);
            return sendResponse(res, 200, false, 'Email id Not found check App', null);
        }
        if (password !== user.password && compareSync(password, user.password)) {
            // create socket events
            const sockets = {
                dashboardEventId: 'dashboard-' + user._id,
                activityEventId: 'activity-' + user._id,
                chatEventId: 'chat-' + user._id,
                sessionEventId: 'session-' + user._id,
                courseEventId: 'course-' + user._id,
            };
            const updateData = {
                device_type,
                last_login_device_type: device_type,
                socketEventId: sockets,
            };
            // Update Mobile FCM token
            if (fcm_token && fcm_token.length !== 0) {
                if (device_type === 'web') {
                    updateData.web_fcm_token = fcm_token;
                } else {
                    updateData.fcm_token = fcm_token;
                }
            }

            await update(User, convertToMongoObjectId(user._id), updateData);

            // get Program
            const _id = convertToMongoObjectId(user.program._program_id);
            const program_no = user.program.program_no;
            const programQuery = {
                $or: [{ _id }, { code: program_no }],
                isActive: true,
                isDeleted: false,
            };
            const programProject = { _id, name: 1, code: 1, program_type: 1 };
            let program = await get(Program, programQuery, programProject);
            program = program.status ? program.data : user.program;
            //generate token
            const tokens = await generateAuthTokens({ user_id: user.user_id });

            // get institution calendar
            const institutionCalendar = await InstitutionCalendar.findOne({
                isDeleted: false,
                isActive: true,
            })
                .sort({ _id: -1 })
                .lean();

            // designation
            const academicAllocations = user.academic_allocation;
            let designation;
            let subject;
            let departmentName;
            if (academicAllocations.length) {
                const primaryAcademicAllocation = academicAllocations.find(
                    (academicAllocation) => academicAllocation.allocation_type === PRIMARY,
                );
                if (primaryAcademicAllocation) {
                    const {
                        _program_id: programId,
                        _department_id: departmentId,
                        _department_subject_id: departmentSubjectIds,
                    } = primaryAcademicAllocation;
                    const programQuery = {
                        _id: programId,
                        isActive: true,
                        isDeleted: false,
                    };
                    const programProject = { _id, name: 1, code: 1, program_type: 1 };
                    let program = await get(Program, programQuery, programProject);
                    program = program.status ? program.data : user.program;
                    designation = program.name;
                    if (departmentId && departmentSubjectIds.length) {
                        const departmentSubjects = await DepartmentSubject.findOne({
                            _id: convertToMongoObjectId(departmentId),
                            program_id: convertToMongoObjectId(programId),
                        });
                        const { subject: subjects, department_name } = departmentSubjects;
                        let filterSubjects = subjects.filter((subject) =>
                            departmentSubjectIds.includes(subject._id),
                        );
                        filterSubjects = filterSubjects.map(
                            (filterSubject) => filterSubject.subject_name,
                        );
                        subject = filterSubjects.toString();
                        departmentName = department_name;
                    }
                }
            }

            const data = {
                _id: user._id,
                user_id: user.user_id,
                name: user.name,
                email: user.email,
                gender: user.gender,
                enrolledProgram: program,
                enrolledTerm: user.batch,
                enrolledDate: user.createdAt,
                mobile: user.mobile,
                verificationEmail: user.verification.email,
                verificationMobile: user.verification.mobile,
                user_type: user.user_type,
                biometric_data: user.biometric_data.face[0],
                employment: user.employment,
                institutionCalendar: institutionCalendar._id,
                designation,
                subject,
                departmentName,
                tokens,
                socketEventId: !user.socketEventId ? sockets : user.socketEventId,
                ioToken: user.ioToken || null,
            };
            logger.info('userController -> login -> %s  Welcome to DigiClass', email);
            return sendResponse(res, 200, true, 'Welcome to DigiClass', data);
        }
        logger.warn('userController -> login -> %s Password not match', email);
        return sendResponse(res, 200, false, 'Password not match', null);
    } catch (error) {
        logger.error({ error }, 'userController -> login -> %s Module : %s error');
        return sendResponse(res, 500, false, 'Error Catch', error.toString());
    }
};

// user forget password staff and student
exports.forgetPassword = async (req, res) => {
    try {
        const {
            body: { email, user_type, validation_mode, face },
            files: { face: [facialFile] = [] },
        } = req;
        // make user query
        const userQuery = { email, isDeleted: false, isActive: true };
        const userProject = {
            _id: 1,
            name: 1,
            email: 1,
            user_type: 1,
            password: 1,
            mobile: 1,
            verification: 1,
            status: 1,
            'biometric_data.face': 1,
            user_id: 1,
        };
        // if user not found
        const { status, data: user } = await get(User, userQuery, userProject);
        // if user not found
        if (!status)
            return sendResponseWithRequest(req, res, 200, false, 'Email id Not found', null);
        //  if check user completed profile verification
        if (user.status !== 'completed')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Your Profile is not completed ask Admin',
                null,
            );
        //  if check user verification email and mobile
        if (!user.verification.email && !user.verification.mobile)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Only signed user able to access ',
                null,
            );
        //  if check user using application type
        if (user_type && user.user_type !== user_type)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Email id Not found check App',
                null,
            );
        //  if check user face verification
        if (validation_mode !== 'face') {
            const otp = getFourDigitOTP();
            await update(User, user._id, {
                'otp.no': otp,
                'otp.expiry_date': timestampNow(),
            });
            const messages =
                '<p>Dear DigiClass User,<br>' +
                otp +
                ' is SECRET OTP for your password change, this valid for 3min. Pls do not share OTP with anyone</p>';
            if (validation_mode === 'email') {
                send_email(user.email, 'DigiClass Alert', messages);
            } else if (validation_mode === 'mobile') {
                const sms_messages = `${otp} is your DigiClass password change OTP.`;
                if (user.mobile) {
                    send_sms(user.mobile, sms_messages);
                }
            }
            // send OTP to slack channel
            const slack_chat_data = `DigiClass Forget -> Email : ${user.email} - OTP : ${otp}`;
            send_otp_to_slack(slack_chat_data);
            const response = {
                _id: user._id,
                email: user.email,
                mobile: user.mobile || '',
                token: await generateAuthTokens({
                    userId: user._id,
                    _id: user._id,
                    userType: defaultPolicy.FORGET,
                }),
            };
            return sendResponseWithRequest(req, res, 200, true, 'OTP Send', response);
        }
        // check face verification
        if (user.biometric_data.face && user.biometric_data.face[0]) {
            console.log({
                type: user.user_type,
                userId: user.user_id,
                facial: facialFile,
                app: 'DC',
            });
            await digiAuthService
                .verifyFacial({
                    type: user.user_type,
                    userId: user.user_id,
                    facial: facialFile,
                    app: 'DC',
                })
                .then((resp) => {
                    logger.info(resp, 'userService -> facial -> resp:');
                    deleteFileOrFolderUtil({ path: facialFile.destination });
                    if (resp.success)
                        return sendResponseWithRequest(
                            req,
                            res,
                            200,
                            true,
                            'Authentication successful',
                            null,
                        );
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        'Sorry, Authentication Failed',
                        null,
                    );
                })
                .catch((err) => {
                    logger.error({ err }, 'userService -> facial -> err:');
                    if (facialFile) deleteFileOrFolderUtil({ path: facialFile.destination });
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        'Sorry, Authentication Failed',
                        err,
                    );
                });
            // const url = user.biometric_data.face[0];
            // const face_res = await Face.faceVerify(url, 'public/facial/images/' + face);
            // if (face_res) return sendResponse(res, 200, true, 'Authentication successful', null);
            // return sendResponse(res, 200, false, 'Sorry, Authentication Failed', null);
        }
        // return sendResponse(res, 200, false, 'Sorry, Authentication Failed', null);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Error Catch', error.toString());
    }
};

// user otp verify staff and student
exports.otpVerify = async (req, res) => {
    try {
        const {
            body: { _id, otp },
        } = req;
        // make user query
        const userQuery = { _id, isDeleted: false, isActive: true };
        const userProject = {
            _id: 1,
            name: 1,
            email: 1,
            user_type: 1,
            password: 1,
            mobile: 1,
            otp: 1,
            verification: 1,
            status: 1,
            'biometric_data.face': 1,
        };
        // get user details
        const { status, data: user } = await get(User, userQuery, userProject);
        // if user not found
        if (!status)
            return sendResponseWithRequest(req, res, 200, false, 'Email id Not found', null);
        //  if check user completed profile verification
        if (user.status !== 'completed')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Your Profile is not completed ask Admin',
                null,
            );
        //  if check user verification email and mobile
        if (!user.verification.email && !user.verification.mobile)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Only signed user able to access ',
                null,
            );
        // if check otp
        if (parseInt(user.otp.no) !== parseInt(otp))
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'The code you entered is invalid',
                null,
            );
        const secondsDifference = getSecondsDifference(user.otp.expiry_date, timestampNow());
        // if check otp Expired
        if (secondsDifference <= OTP_EXPIRY_DURATION_IN_SECS) {
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                'OTP verified successfully',
                'OTP verified successfully',
            );
        }
        return sendResponseWithRequest(req, res, 200, false, 'OTP Expires', null);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Error Catch', error.toString());
    }
};

// user update password staff and student
exports.updatePassword = async (req, res) => {
    try {
        const {
            body: { _id, new_password },
        } = req;
        // make user query
        const userQuery = { _id, isDeleted: false, isActive: true };
        const userProject = {
            _id: 1,
            user_id: 1,
            name: 1,
            email: 1,
            user_type: 1,
            password: 1,
            mobile: 1,
            otp: 1,
            verification: 1,
            status: 1,
            'biometric_data.face': 1,
        };
        // get user details
        const { status, data: user } = await get(User, userQuery, userProject);
        // if user not found
        if (!status)
            return sendResponseWithRequest(req, res, 200, false, 'Email id Not found', null);
        // update password
        const update_pass = await update(User, user._id, {
            password: hashSync(new_password, 10),
        });
        // once update password done send user details
        if (update_pass.status) {
            const data = {
                _id: user._id,
                user_id: user.user_id,
                name: user.name,
                email: user.email,
                mobile: user.mobile,
                user_type: user.user_type,
                biometric_data: user.biometric_data.face[0],
                designation: 'Prof',
                subject: 'Biochemistry',
            };
            if (AUTH_PASSWORD_SYNC === 'true') {
                digiAuthService
                    .syncUser({
                        employeeOrAcademicId: user.user_id,
                        userId: user._id,
                        password: new_password,
                    })
                    .then((updatedUser) => {
                        console.log('userController -> set_password -> updatedUser:', updatedUser);
                    })
                    .catch((err) => {
                        console.log('userController -> set_password -> err:', err);
                    });
            }
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                'Password successfully changed',
                data,
            );
        }
        return sendResponseWithRequest(req, res, 200, false, 'Unable to change password', null);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Error Catch', error.toString());
    }
};

//user face verify staff and student
exports.faceVerify = async (req, res) => {
    try {
        const {
            body: { email, face },
            files: { face: [facialFile] = [] },
        } = req;
        // make user query
        const userQuery = { email, isDeleted: false, isActive: true };
        const userProject = {
            _id: 1,
            name: 1,
            email: 1,
            user_type: 1,
            password: 1,
            mobile: 1,
            verification: 1,
            status: 1,
            'biometric_data.face': 1,
            user_id: 1,
        };
        // get user details
        const { status, data: user } = await get(User, userQuery, userProject);
        // if user not found
        if (!status) return sendResponse(res, 200, false, 'Email id Not found', null);
        //  if check user completed profile verification
        if (user.status !== 'completed')
            return sendResponse(res, 200, false, 'Your Profile is not completed ask Admin', null);
        //  if check user verification email and mobile
        if (!user.verification.email && !user.verification.mobile)
            return sendResponse(res, 200, false, 'Only signed user able to access ', null);
        //   check user face verification
        if (user.biometric_data.face && user.biometric_data.face[0]) {
            await digiAuthService
                .verifyFacial({
                    type: user.user_type,
                    userId: user.user_id,
                    facial: facialFile,
                    app: 'DC',
                })
                .then((resp) => {
                    logger.info(resp, 'userService -> facial -> resp:');
                    deleteFileOrFolderUtil({ path: facialFile.destination });
                    if (resp.success)
                        return sendResponse(res, 200, true, 'Authentication successful', null);
                    return sendResponse(res, 200, false, 'Sorry, Authentication Failed', null);
                })
                .catch((err) => {
                    logger.error({ err }, 'userService -> facial -> err:');
                    if (facialFile) deleteFileOrFolderUtil({ path: facialFile.destination });
                    return sendResponse(res, 200, false, 'Sorry, Authentication Failed', err);
                });
            // const url = user.biometric_data.face[0];
            // const face_res = await Face.faceVerify(url, 'public/facial/images/' + face);
            // if (face_res) return sendResponse(res, 200, true, 'Authentication successful', null);
            // return sendResponse(res, 200, false, 'Sorry, Authentication Failed', null);
        }
        // return sendResponse(res, 200, false, 'Sorry, Authentication Failed', null);
    } catch (error) {
        return sendResponse(res, 500, false, 'Server Error', error.toString());
    }
};

exports.token = async (req, res) => {
    try {
        const {
            body: { _id, fcm_token, device_type },
        } = req;
        const cond = { _id: convertToMongoObjectId(_id), isDeleted: false, isActive: true };
        const doc = await get(User, cond, {});

        if (!doc.status)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, false, 'User Not found', null));

        if (doc.data.status !== 'completed')
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        'Your Profile is not completed ask Admin',
                        null,
                    ),
                );

        if (!doc.data.verification.email && !doc.data.verification.mobile)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        'Only signed user able to access ',
                        null,
                    ),
                );

        // Update Mobile FCM token
        if (!fcm_token)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, false, 'Token Not found', null));

        let query;
        if (device_type === 'web') {
            query = {
                device_type,
                web_fcm_token: fcm_token,
            };
        } else {
            query = {
                device_type,
                fcm_token,
            };
        }
        const data = await update(User, convertToMongoObjectId(doc.data._id), query);
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, 'Welcome to DigiClass', null));
    } catch (error) {
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 500, false, 'Server Error', error.toString()));
    }
};

//user logout staff and student
exports.logout = async (req, res) => {
    try {
        const {
            body: { id, device_type },
        } = req;
        // get user details
        const { status } = await get(User, { _id: convertToMongoObjectId(id) }, {});
        // if user not found
        if (!status) return sendResponseWithRequest(req, res, 200, false, 'User Not found', null);
        let query;
        if (device_type === 'web') {
            query = { web_fcm_token: '', logout_time: timestampNow() };
        } else {
            query = { fcm_token: '', logout_time: timestampNow() };
        }
        //update fcm token empty
        await update(User, convertToMongoObjectId(id), query);
        return sendResponseWithRequest(req, res, 200, true, 'Logged out Successfully', null);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, 'Server Error', error.toString());
    }
};
//fcm update for user
exports.fcmUpdate = async (req, res) => {
    try {
        const {
            params: { id },
            body: { fcm_token, device_type },
        } = req;
        // get user details
        const { status } = await get(User, { _id: convertToMongoObjectId(id) }, {});
        // if user not found
        if (!status) return sendResponse(res, 200, false, 'User Not found', null);
        let query;
        if (device_type === 'web') {
            query = { web_fcm_token: fcm_token };
        } else {
            query = { fcm_token };
        }
        //update fcm token
        await update(User, convertToMongoObjectId(id), query);
        return sendResponse(res, 200, true, 'Updated Successfully', null);
    } catch (error) {
        return sendResponse(res, 500, false, 'Server Error', error.toString());
    }
};

// user login staff and student
exports.loginService = async (req, res) => {
    try {
        const {
            body: { email, password, user_type, device_type },
        } = req;
        logger.info('userController -> loginService -> %s Login %s - start', email, device_type);
        const userQuery = { email, isDeleted: false /* , isActive: true */ };
        const userProject = { password: 1, status: 1, user_id: 1, user_type: 1, isActive: 1 };
        const { status, data: user } = await get(User, userQuery, userProject);
        if (!status) {
            logger.warn('userController -> login -> %s Incorrect Email/Password', email);
            return sendResponseWithRequest(req, res, 200, false, 'Incorrect Email/Password', null);
        }
        if (user.status !== 'completed') {
            logger.warn(
                'userController -> login -> %s Your Profile is not completed ask Admin',
                email,
            );
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Your Profile is not completed ask Admin',
                null,
            );
        }
        if (!status || !(password !== user.password && compareSync(password, user.password))) {
            logger.warn('userController -> login -> %s Incorrect Email/Password', email);
            return sendResponseWithRequest(req, res, 200, false, 'Incorrect Email/Password', null);
        }
        if (!user.verification && !user.mobile) {
            logger.warn('userController -> login -> %s Only signed user able to access ', email);
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Only signed user able to access ',
                null,
            );
        }
        if (user.isActive === false) {
            logger.warn(
                'userController -> login -> %s Your Account has been Deactivated, please contact Administrator.',
                email,
            );
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Your Account has been Deactivated, please contact Administrator.',
                null,
            );
        }
        if (user_type && user.user_type !== user_type) {
            logger.warn('userController -> login -> %s Your not associated for this App', email);
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Your not associated for this App',
                null,
            );
        }

        // Anomaly Pure Check
        if (
            SERVICES.ANOMALY_RESTRICT &&
            SERVICES.ANOMALY_RESTRICT === 'true' &&
            user_type &&
            user.user_type === DS_TYPE_STUDENT
        ) {
            const institutionData = await institutionSchema.findOne(
                {
                    _id: convertToMongoObjectId(SERVICES.REACT_APP_INSTITUTION_ID),
                },
                { anomalyRestrict: 1 },
            );
            if (institutionData && institutionData.anomalyRestrict) {
                const studentAnomalyData = await scheduleAnomalySchema.findOne(
                    {
                        isDeleted: false,
                        studentId: convertToMongoObjectId(user._id),
                        pure: false,
                    },
                    { _id: 1 },
                );
                if (studentAnomalyData)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        SERVICES.ANOMALY_RESTRICT_MESSAGE,
                        null,
                    );
            }
        }
        const userRoleData = await userRoleGet({ userType: user.user_type, userId: user._id });
        const token = await generateAuthTokens({
            userId: user.user_id,
            _id: user._id,
            userType: user.user_type,
            userRoleData,
        });
        const responseData = {
            _id: user._id,
            email,
            tokens: token,
            services: SERVICES,
        };
        logger.info('userController -> login -> %s  Welcome to DigiClass', email);
        return sendResponseWithRequest(req, res, 200, true, 'Welcome to DigiClass', responseData);
    } catch (error) {
        console.error(error);
        logger.error({ error }, 'userController -> loginService -> %s Module : %s error');
        return sendResponseWithRequest(req, res, 500, false, 'Error Catch', error.toString());
    }
};

exports.getCourseAdmin = async (userId, institutionCalendarId) => {
    let courseAdmin = false;
    let courses = await getCourses(userId);
    courses = courses.filter(
        (course) => course._institution_calendar_id.toString() === institutionCalendarId.toString(),
    );
    courseQuery = courses.map((course) => {
        return {
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
            _id: convertToMongoObjectId(course._id),
            'coordinators.term': course.term,
            'coordinators.year': course.year_no,
            'coordinators.level_no': course.level_no,
        };
    });
    const courseFindQuery = {
        'coordinators._user_id': convertToMongoObjectId(userId),
        'coordinators.status': true,
    };
    //if (courseQuery.length > 0) courseFindQuery.$or = courseQuery;
    const coordinators = await Course.find(courseFindQuery, { _id: 1 }).lean();
    if (coordinators.length) {
        courseAdmin = true;
    }
    return courseAdmin;
};

// user login staff and student
exports.loggedInService = async (req, res) => {
    try {
        const {
            body: {
                _id,
                fcm_token,
                device_type,
                deviceBrandName,
                deviceID,
                deviceNumber,
                deviceOSVersion,
            },
            // headers: { _institution_id },
        } = req;
        logger.info('userController -> loggedInService -> %s via %s - start', _id, device_type);
        const userQuery = { _id: convertToMongoObjectId(_id), isDeleted: false, isActive: true };
        const userProject = {
            name: 1,
            user_id: 1,
            email: 1,
            gender: 1,
            batch: 1,
            createdAt: 1,
            user_type: 1,
            biometric_data: 1,
            employment: 1,
            program: 1,
            academic_allocation: 1,
            // mobile: 1,
            ioToken: 1,
            _role_id: 1,
            contact: 1,
        };
        const { status: userStatus, data: user } = await get(User, userQuery, userProject);
        let roles;
        const role_details = [];
        if (user._role_id) {
            roles_data = await roleAssignSchema
                .findOne({
                    _id: convertToMongoObjectId(user._role_id),
                })
                .populate({
                    path: 'roles._role_id',
                    select: { isActive: 1, name: 1, isDeleted: 1, modules: 1 },
                })
                .lean();
            roles_data.roles.forEach((roleElement) => {
                if (roleElement._role_id && roleElement._role_id.isActive) {
                    role_details.push({
                        ...roleElement._role_id,
                        isAdmin: roleElement.isAdmin,
                        isDefault: roleElement.isDefault,
                    });
                }
            });
            roles = role_details;
        }

        if (!userStatus) {
            logger.info('userController -> loggedInService -> %s User not found', _id, device_type);
            return sendResponseWithRequest(req, res, 200, false, 'User not found', null);
        }
        const userUpdateData = {
            device_type,
            last_login_device_type: device_type,
            socketEventId: {
                dashboardEventId: 'dashboard-' + _id,
                activityEventId: 'activity-' + _id,
                chatEventId: 'chat-' + _id,
                sessionEventId: 'session-' + _id,
                courseEventId: 'course-' + _id,
            },
        };
        if (fcm_token && fcm_token.length) {
            if (device_type === 'web') {
                userUpdateData.web_fcm_token = fcm_token;
            } else {
                userUpdateData.fcm_token = fcm_token;
            }
        }
        await update(User, convertToMongoObjectId(_id), userUpdateData);
        const programQuery = {
            isActive: true,
            isDeleted: false,
        };
        // TODO work with cache service now i am changes to DB query now
        //const { _id: institutionCalendarId } = await getInstitutionCalendar();
        const institutionCalendarData = await InstitutionCalendar.find(
            { isDeleted: false, status: PUBLISHED },
            {
                _id: 1,
                calendar_name: 1,
                isActive: 1,
                // start_date: 1,
                // end_date: 1,
            },
        )
            .sort({ _id: -1 })
            // .limit(1)
            .lean();
        if (!institutionCalendarData.length)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                'Institution Calendar Not Found',
                null,
            );
        const activeInstitutionCalendarIds = institutionCalendarData
            .filter((institutionCalendarElement) => institutionCalendarElement.isActive === true)
            .map((institutionCalendarElement) =>
                convertToMongoObjectId(institutionCalendarElement._id),
            );
        let userInstitutionCalendarId = activeInstitutionCalendarIds[0];
        // course Admin checks
        let courseAdmin = false;
        if (user.user_type === DC_STAFF) {
            // courseAdmin = await this.getCourseAdmin(user._id, institutionCalendarData._id);
            // Course Coordinator Check
            courseAdmin = !!(await Course.find(
                {
                    'coordinators._user_id': convertToMongoObjectId(_id),
                    'coordinators._institution_calendar_id': {
                        $in: activeInstitutionCalendarIds,
                    },
                },
                { _id: 1 },
            ));
            const scheduleQuery = {
                isDeleted: false,
                'staffs._staff_id': convertToMongoObjectId(_id),
            };
            console.time('courseScheduleData');
            const courseScheduleData = await courseScheduleSchema
                .distinct('_institution_calendar_id', scheduleQuery)
                .lean();
            console.timeEnd('courseScheduleData');
            if (courseScheduleData && courseScheduleData.length) {
                const scheduleBasedActive = activeInstitutionCalendarIds.find(
                    (activeInstitutionCalendarIdElement) =>
                        courseScheduleData.find(
                            (courseScheduleElement) =>
                                courseScheduleElement._id.toString() ===
                                activeInstitutionCalendarIdElement.toString(),
                        ),
                );
                if (scheduleBasedActive) userInstitutionCalendarId = scheduleBasedActive;
                else {
                    const userCalendarData = institutionCalendarData.find(
                        (institutionCalendarElement) =>
                            courseScheduleData.find(
                                (courseScheduleElement) =>
                                    courseScheduleElement._id.toString() ===
                                    institutionCalendarElement._id.toString(),
                            ),
                    );
                    if (userCalendarData) userInstitutionCalendarId = userCalendarData._id;
                }
            }
        } else {
            // Get Student Current Active Institution Calendar Id
            const userInstitutionCalendarRedisKey = `${USER_INSTITUTION_CALENDAR}:${_id}`;
            const userInstitutionCalendarCache = await redisClient.Client.get(
                userInstitutionCalendarRedisKey,
            );
            if (userInstitutionCalendarCache && JSON.parse(userInstitutionCalendarCache).length) {
                userInstitutionCalendarId = JSON.parse(userInstitutionCalendarCache)[0]._id;
            } else {
                const studentStudentGroupData = await studentGroupSchema.distinct(
                    '_institution_calendar_id',
                    {
                        'groups.courses.setting.session_setting.groups._student_ids':
                            convertToMongoObjectId(_id),
                    },
                );
                const userInstitutionCalendarIds = institutionCalendarData.filter(
                    (activeInstitutionCalendarIdElement) =>
                        studentStudentGroupData.find(
                            (studentStudentGroupElement) =>
                                studentStudentGroupElement.toString() ===
                                activeInstitutionCalendarIdElement._id.toString(),
                        ),
                );
                if (userInstitutionCalendarIds.length) {
                    await redisClient.Client.set(
                        userInstitutionCalendarRedisKey,
                        JSON.stringify(userInstitutionCalendarIds),
                    );
                    userInstitutionCalendarId = userInstitutionCalendarIds[0]._id;
                }
            }
        }
        const sessionGlobalAttendanceSetting = await globalSettingSchema
            .findOne(
                {
                    // _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    scheduleAttendanceConfig: 1,
                    disciplinaryRemarks: 1,
                    ...(user.user_type === DC_STAFF && {
                        'basicDetails.staffFacial': 1,
                        'basicDetails.schedulePermission': 1,
                    }),
                    warningMode: 1,
                    studentFacial: '$basicDetails.studentFacial',
                    selfRegistrationDocument: '$basicDetails.selfRegistrationDocument',
                },
            )
            .lean();
        const responseData = {
            _id: user._id,
            user_id: user.user_id,
            name: user.name,
            email: user.email,
            gender: user.gender,
            enrolledTerm: user.batch,
            enrolledDate: user.createdAt,
            user_type: user.user_type,
            // biometric_data: user.biometric_data.face[0],
            biometric_data:
                user && user.biometric_data && user.biometric_data.face[0]
                    ? await getSignedURL(user.biometric_data.face[0], 'userData')
                    : '',
            institutionCalendar: userInstitutionCalendarId,
            ...(user.user_type === DC_STAFF && { activeInstitutionCalendarIds }),
            socketEventId: userUpdateData.socketEventId,
            ioToken: user.ioToken || null,
            courseAdmin,
            services: SERVICES,
            scheduleAttendanceConfig:
                sessionGlobalAttendanceSetting &&
                sessionGlobalAttendanceSetting.scheduleAttendanceConfig
                    ? sessionGlobalAttendanceSetting.scheduleAttendanceConfig
                    : { staffCaptcha: false, studentCaptcha: false },
            contact: user.contact,
            disciplinaryRemarks: sessionGlobalAttendanceSetting?.disciplinaryRemarks,
            warningMode: sessionGlobalAttendanceSetting?.warningMode,
            studentFacial: sessionGlobalAttendanceSetting?.studentFacial ?? true,
            selfRegistrationDocument:
                sessionGlobalAttendanceSetting?.selfRegistrationDocument ?? true,
        };
        let staffAcademics;
        if (user.user_type === DC_STAFF) {
            responseData.staffFacial =
                sessionGlobalAttendanceSetting &&
                sessionGlobalAttendanceSetting.basicDetails &&
                sessionGlobalAttendanceSetting.basicDetails.staffFacial
                    ? sessionGlobalAttendanceSetting.basicDetails.staffFacial
                    : false;
            responseData.employment = user.employment;
            staffAcademics = user.academic_allocation.find(
                (academicAllocation) =>
                    academicAllocation &&
                    academicAllocation.allocation_type &&
                    academicAllocation.allocation_type === PRIMARY,
            );
            responseData.schedulePermission = {
                create: false,
                edit: false,
            };
            if (
                sessionGlobalAttendanceSetting &&
                sessionGlobalAttendanceSetting.basicDetails &&
                sessionGlobalAttendanceSetting.basicDetails.schedulePermission
            ) {
                if (sessionGlobalAttendanceSetting.basicDetails.schedulePermission.create) {
                    responseData.schedulePermission.create = true;
                }

                if (sessionGlobalAttendanceSetting.basicDetails.schedulePermission.edit) {
                    responseData.schedulePermission.edit = true;
                }
            }

            if (staffAcademics) {
                const {
                    _program_id: programId,
                    _department_id: departmentId,
                    _department_subject_id: departmentSubjectIds,
                } = staffAcademics;
                programQuery._id = convertToMongoObjectId(programId);
                if (departmentId && departmentSubjectIds.length) {
                    const departmentSubjects = await DepartmentSubject.findOne({
                        _id: convertToMongoObjectId(departmentId),
                        // program_id: convertToMongoObjectId(programId),
                    });
                    const filterSubjects = departmentSubjects?.subject
                        .filter((subject) => departmentSubjectIds.includes(subject._id))
                        .map((filterSubject) => filterSubject.subject_name);
                    responseData.subject = filterSubjects?.toString();
                    responseData.departmentName = departmentSubjects?.department_name;
                }
            }
        } else {
            programQuery.code =
                user.program && user.program.program_no ? user.program.program_no : '';
        }
        const programProject = { _id: 1, name: 1, code: 1, program_type: 1 };
        let program = await get(Program, programQuery, programProject);
        program = program.status ? program.data : user.program;
        responseData.enrolledProgram = program;
        responseData.designation = program && program.name ? program.name : '';
        if (device_type !== 'web' && deviceID)
            await userDeviceDetailSchema.create({
                userId: _id,
                userType: user.user_type,
                deviceType: device_type,
                deviceBrandName,
                deviceID,
                deviceNumber,
                deviceOSVersion,
            });
        logger.info('userController -> login -> %s  Welcome to DigiClass', _id);
        return sendResponseWithRequest(req, res, 200, true, 'Welcome to DigiClass', {
            ...responseData,
            roles,
            encryptedRoutes: encryptedRoutes(),
        });
    } catch (error) {
        logger.error(error, 'userController -> login -> %s error : %o ', req.body._id, error.err);
        return sendResponseWithRequest(req, res, 500, false, 'Error Catch', error.toString());
    }
};

// Parent App
exports.parentAuthSignup = async (req, res) => {
    try {
        const {
            body: { email, mobile },
        } = req;
        if (process.env.PARENT_APP && process.env.PARENT_APP === 'false') {
            return sendResponse(res, 200, false, 'This service unavailable for this Institution');
        }
        const queryParsing = { email, mobile };
        logger.info(queryParsing, 'DCUser -> parentAuthSignup -> authSignup -> Start');
        let userMobile;
        if (mobile) userMobile = `${SERVICES.REACT_APP_COUNTRY_CODE}${mobile}`;
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            $or: [],
        };
        if (email)
            parentUserQuery.$or.push({
                email: { $in: [email, email.toUpperCase(), email.toLowerCase()] },
            });
        if (mobile) parentUserQuery.$or.push({ mobile: { $in: [userMobile, mobile] } });
        console.time('parentCheck');
        const parentCheck = await parentUserSchema.findOne(parentUserQuery, { password: 1 });
        console.timeEnd('parentCheck');
        if (parentCheck && parentCheck.password)
            return sendResponse(res, 200, false, 'Email/Mobile Already Signed-UP Do Login');
        const otp = getSixDigitOTP();
        if (parentCheck && !parentCheck.password) {
            if (email) {
                const messages =
                    '<p>' +
                    'Dear Parent/Spouse/Guardian' +
                    '<br>' +
                    otp +
                    ' is you Secret OTP for DigiParent App' +
                    '</p>';
                send_email(email, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (mobile) {
                send_sms(userMobile, `${otp} is you Secret OTP for DigiParent App`);
            }
            const parentOTPUpdate = await parentUserSchema.updateOne(parentUserQuery, {
                $set: {
                    otp: { no: otp, expiry_date: timestampNow() },
                },
            });
            if (!parentOTPUpdate)
                return sendResponse(res, 200, false, 'Unable to do Sign-up', parentOTPUpdate);
            return sendResponse(res, 200, true, 'DATA_RETRIEVED', { _id: parentCheck._id });
        }
        const userParentQuery = {
            isDeleted: false,
            $or: [],
        };
        if (email)
            userParentQuery.$or.push({
                'contact.email': { $in: [email, email.toUpperCase(), email.toLowerCase()] },
            });
        if (mobile)
            userParentQuery.$or.push({
                'contact.mobile': { $in: [userMobile, mobile] },
            });
        console.time('userDatas');
        const userDatas = await User.find(userParentQuery, { _id: 1, contact: 1 });
        console.timeEnd('userDatas');
        if (!userDatas.length) return sendResponse(res, 200, false, 'User Not Found');
        if (email) {
            const messages =
                '<p>' +
                'Dear Parent/Spouse/Guardian' +
                '<br>' +
                otp +
                ' is you Secret OTP for DigiParent App' +
                '</p>';
            send_email(email, req.t('DIGISCHEDULER_ALERT'), messages);
        }
        if (mobile) {
            send_sms(userMobile, `${otp} is you Secret OTP for DigiParent App`);
        }
        const parentObject = { name: '', relationType: '' };
        for (userDataElement of userDatas) {
            if (parentObject.name === '') {
                const userContact = userDataElement.contact.find(
                    (contactElement) =>
                        (email
                            ? contactElement.email
                                ? contactElement.email.toLowerCase() === email.toLowerCase()
                                : false
                            : true) &&
                        (mobile
                            ? contactElement.mobile
                                ? contactElement.mobile.toString() === userMobile.toString() ||
                                  contactElement.mobile.toString() === mobile.toString()
                                : false
                            : true),
                );
                if (userContact) {
                    parentObject.name = userContact.name || '';
                    parentObject.relationType = userContact.relation_type || '';
                    parentObject.email = userContact.email || '';
                    parentObject.mobile = userContact.mobile || '';
                    break;
                }
            }
        }
        const parentEmailMobileCheck = await parentUserSchema.findOne(
            { $or: [{ email: parentObject.email }, { mobile: parentObject.mobile }] },
            { email: 1, mobile: 1 },
        );
        if (parentEmailMobileCheck) {
            if (parentEmailMobileCheck.email === parentObject.email)
                return sendResponse(
                    res,
                    200,
                    false,
                    'Duplicate Email, Same Email Address Registered with Different Account',
                );
            if (parentEmailMobileCheck.mobile === parentObject.mobile)
                return sendResponse(
                    res,
                    200,
                    false,
                    'Duplicate Mobile No, Same Mobile No Registered with Different Account',
                );
        }
        const parentCreate = await parentUserSchema.create({
            email: parentObject.email,
            mobile: parentObject.mobile,
            relationType: parentObject.relationType,
            name: parentObject.name,
            childIds: userDatas.map((userDataElement) =>
                convertToMongoObjectId(userDataElement._id),
            ),
            otp: { no: otp, expiry_date: timestampNow() },
        });
        await sendAnnouncement(parentCreate._id, parentCreate.relationType);
        if (!parentCreate)
            return sendResponse(
                res,
                200,
                false,
                'Unable to Do SignUp, Pls try after some time',
                parentCreate,
            );
        logger.info(queryParsing, 'DCUser -> parentAuthSignup -> authSignup -> End');
        return sendResponse(res, 200, true, 'DATA_RETRIEVED', { _id: parentCreate._id });
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentAuthSignup -> authSignup -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.parentOTPVerify = async (req, res) => {
    try {
        const {
            body: { _id, otp },
        } = req;
        const queryParsing = { _id };
        logger.info(queryParsing, 'DCUser -> parentOTPVerify -> authSignup -> Start');
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            _id: convertToMongoObjectId(_id),
        };
        console.time('parentData');
        const parentData = await parentUserSchema.findOne(parentUserQuery, { otp: 1 }).lean();
        console.timeEnd('parentData');
        if (!parentData) return sendResponse(res, 200, false, 'User Not Found');
        if (parentData.otp.no !== otp) return sendResponse(res, 200, false, req.t('INVALID_OTP'));
        const secondsDifference = getSecondsDifference(parentData.otp.expiry_date, timestampNow());
        logger.info(queryParsing, 'DCUser -> parentAuthSignup -> authSignup -> End');
        if (secondsDifference <= OTP_EXPIRY_DURATION_IN_SECS) {
            const tokens = await generateAuthTokens(_id);
            const respData = { _id, tokens };
            return sendResponse(res, 200, true, req.t('OTP_VERIFIED_SUCCESSFULLY'), respData);
        }
        return sendResponse(res, 200, false, req.t('OTP_EXPIRES'));
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentAuthSignup -> authSignup -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.parentSetPassword = async (req, res) => {
    try {
        const {
            body: { _id, password },
        } = req;
        const queryParsing = { _id };
        logger.info(queryParsing, 'DCUser -> parentSetPassword -> authSignup -> Start');
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            _id: convertToMongoObjectId(_id),
        };
        const parentUserPassUpdate = await parentUserSchema.updateOne(parentUserQuery, {
            $set: {
                password: hashSync(password, 10),
            },
        });
        logger.info(queryParsing, 'DCUser -> parentSetPassword -> authSignup -> End');
        if (parentUserPassUpdate)
            return sendResponse(res, 200, true, req.t('PASSWORD_SUCCESSFULLY_CHANGED'));
        return sendResponse(res, 200, false, req.t('UNABLE_TO_CHANGE_PASSWORD'));
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentSetPassword -> authSignup -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.parentAuthLogin = async (req, res) => {
    try {
        const {
            body: { email, mobile, password },
        } = req;
        if (process.env.PARENT_APP && process.env.PARENT_APP === 'false') {
            return sendResponse(res, 200, false, 'This service unavailable for this Institution');
        }
        const queryParsing = { email, mobile };
        logger.info(queryParsing, 'DCUser -> parentAuthLogin -> Start');
        let userMobile;
        if (mobile) userMobile = `${SERVICES.REACT_APP_COUNTRY_CODE}${mobile}`;
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            $or: [],
            password: { $exists: true },
        };
        if (email)
            parentUserQuery.$or.push({
                email: { $in: [email, email.toUpperCase(), email.toLowerCase()] },
            });
        if (mobile) parentUserQuery.$or.push({ mobile: { $in: [userMobile, mobile] } });
        console.time('parentCheck');
        const parentCheck = await parentUserSchema.findOne(parentUserQuery, {
            password: 1,
            user_id: 1,
        });
        console.timeEnd('parentCheck');
        if (!parentCheck) return sendResponse(res, 200, false, 'Incorrect Email/Password');
        if (!(password !== parentCheck.password && compareSync(password, parentCheck.password))) {
            logger.warn('DCUser -> parentAuthLogin -> %s Incorrect Email/Password', email);
            return sendResponse(res, 200, false, 'Incorrect Email/Password', null);
        }
        logger.info(queryParsing, 'DCUser -> parentAuthLogin -> End');
        return sendResponse(res, 200, true, 'Welcome to DigiParent', {
            _id: parentCheck._id,
            tokens: await generateAuthTokens({ user_id: parentCheck.user_id }),
            services: SERVICES,
        });
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentAuthLogin -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.parentAuthLoggedInService = async (req, res) => {
    try {
        const {
            body: { _id, fcm_token, device_type },
        } = req;
        const queryParsing = { _id, fcm_token, device_type };
        logger.info(queryParsing, 'DCUser -> parentAuthLoggedInService -> Start');
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            _id: convertToMongoObjectId(_id),
        };
        console.time('parentData');
        const parentData = await parentUserSchema
            .findOne(parentUserQuery, {
                name: 1,
                email: 1,
                mobile: 1,
                password: 1,
                user_id: 1,
                childIds: 1,
            })
            .populate({
                path: 'childIds',
                select: { name: 1, user_id: 1, 'biometric_data.face': 1, mobile: 1 },
            })
            .lean();
        console.timeEnd('parentData');
        if (!parentData) return sendResponse(res, 200, false, 'User Not Found');
        const userUpdateData = {
            device_type,
            last_login_device_type: device_type,
        };
        if (fcm_token && fcm_token.length) {
            if (device_type === 'web') {
                userUpdateData.web_fcm_token = fcm_token;
            } else {
                userUpdateData.fcm_token = fcm_token;
            }
        }
        await parentUserSchema.updateOne(parentUserQuery, userUpdateData);
        const childDatas = [];
        for (childElement of parentData.childIds) {
            if (childElement.biometric_data && childElement.biometric_data.face[0]) {
                childDatas.push({
                    _id: childElement._id,
                    user_id: childElement.user_id,
                    name: childElement.name,
                    biometric_data: await getSignedURL(
                        childElement.biometric_data.face[0],
                        'userData',
                    ),
                    mobile: childElement.mobile,
                });
            }
        }
        parentData.childIds = childDatas;
        const institutionCalendarData = await InstitutionCalendar.findOne(
            { isDeleted: false, /* isActive: true,  */ status: PUBLISHED },
            { _id: 1 },
        )
            .sort({ _id: -1 })
            .limit(1)
            .lean();
        return sendResponse(res, 200, true, 'User Details', {
            ...parentData,
            institutionCalendar: institutionCalendarData._id,
        });
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentAuthLogin -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSessions = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { userId, sessionDate },
        } = req;
        let scheduledData = await courseSchedule
            .find(
                {
                    'students._id': convertToMongoObjectId(userId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    schedule_date: new Date(sessionDate),
                    isDeleted: false,
                },
                {
                    isActive: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    mode: 1,
                    course_name: 1,
                    status: 1,
                    merge_status: 1,
                    merge_with: 1,
                    students: { $elemMatch: { _id: convertToMongoObjectId(userId) } },
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_topic': 1,
                    type: 1,
                    title: 1,
                    sub_type: 1,
                },
            )
            .populate({
                path: 'merge_with.schedule_id',
                select: {
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                },
            })
            .lean();
        if (!scheduledData.length) {
            return sendResponse(res, 200, false, 'NO SESSION PRESENT IN THIS DATE');
        }
        let mergedScheduleId = [];
        scheduledData = scheduledData
            .filter((scheduleElement) => {
                if (scheduleElement.merge_status) {
                    const mergeScheduleId = scheduleElement.merge_with.map((mergeScheduleElement) =>
                        mergeScheduleElement.schedule_id._id.toString(),
                    );
                    mergeScheduleId.push(scheduleElement._id.toString());
                    if (
                        !mergeScheduleId.find((mergeScheduleIdElement) =>
                            mergedScheduleId.find(
                                (mergedScheduleIdElement) =>
                                    mergedScheduleIdElement.toString() ===
                                    mergeScheduleIdElement.toString(),
                            ),
                        )
                    ) {
                        mergedScheduleId = [...mergedScheduleId, ...mergeScheduleId];
                        return true;
                    }
                    return false;
                }
                return true;
            })
            .map((scheduleElement) => {
                const studentStatus = scheduleElement.students[0].status;
                delete scheduleElement.students;
                return {
                    ...scheduleElement,
                    studentStatus,
                };
            });
        return sendResponse(res, 200, true, 'SESSION DETAILS', scheduledData);
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentGetSession -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Parent App
exports.parentAuthForget = async (req, res) => {
    try {
        const {
            body: { email, mobile },
        } = req;
        const queryParsing = { email, mobile };
        logger.info(queryParsing, 'DCUser -> parentAuthForget -> Start');
        const parentUserQuery = {
            isDeleted: false,
            isActive: true,
            $or: [],
        };
        let userMobile;
        if (mobile) userMobile = `${SERVICES.REACT_APP_COUNTRY_CODE}${mobile}`;
        if (email)
            parentUserQuery.$or.push({
                email: { $in: [email, email.toUpperCase(), email.toLowerCase()] },
            });
        if (mobile) parentUserQuery.$or.push({ mobile: { $in: [userMobile, mobile] } });
        console.time('parentCheck');
        const parentCheck = await parentUserSchema.findOne(parentUserQuery, { _id: 1 });
        console.timeEnd('parentCheck');
        if (!parentCheck) return sendResponse(res, 200, false, 'User Not Found');
        const otp = getSixDigitOTP();
        if (email) {
            const messages =
                '<p>' +
                'Dear Parent/Spouse/Guardian' +
                '<br>' +
                otp +
                ' is you Secret OTP for DigiParent App' +
                '</p>';
            send_email(email, req.t('DIGISCHEDULER_ALERT'), messages);
        }
        if (mobile) {
            send_sms(userMobile, `${otp} is you Secret OTP for DigiParent App`);
        }
        const parentCreate = await parentUserSchema.updateOne(parentUserQuery, {
            $set: {
                otp: { no: otp, expiry_date: timestampNow() },
            },
        });
        if (!parentCreate)
            return sendResponse(res, 200, false, 'Unable to do Forget', parentCreate);
        logger.info(queryParsing, 'DCUser -> parentAuthSignup -> authSignup -> End');
        return sendResponse(res, 200, true, 'DATA_RETRIEVED', { _id: parentCheck._id });
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> parentAuthSignup -> authSignup -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.viewCourseStaffs = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { _course_id, year_no, level_no, _program_id },
        } = req;
        logger.info('DCUser -> View-staffDetails -> Start');
        console.time('parentData');
        const courseScheduleData = await courseSchedule.aggregate([
            {
                $match: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    _course_id: convertToMongoObjectId(_course_id),
                    year_no,
                    level_no,
                },
            },
            {
                $lookup: {
                    from: USER,
                    localField: 'staffs._staff_id',
                    foreignField: '_id',
                    as: 'staffDetails',
                },
            },

            {
                $project: {
                    'staffDetails.name': 1,
                    'staffDetails.email': 1,
                    'staffDetails.mobile': 1,
                    _id: 0,
                },
            },
            { $unwind: '$staffDetails' },
            {
                $group: {
                    _id: {
                        _id: '$staffDetails._id',
                        name: '$staffDetails.name',
                        email: '$staffDetails.email',
                        mobile: '$staffDetails.mobile',
                    },
                },
            },
            {
                $group: {
                    _id: null,
                    staffDetails: {
                        $addToSet: '$_id',
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    staffDetails: 1,
                },
            },
        ]);
        console.timeEnd('parentData');
        return sendResponse(
            res,
            200,
            true,
            'staffDetails',
            courseScheduleData.length ? courseScheduleData[0] : [],
        );
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> View-staffDetails -> Error');
    }
};

exports.getScheduleCount = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { userId, sessionDate },
        } = req;
        logger.info('DCUser -> Total Schedule Count -> Start');
        const scheduledData = await courseSchedule
            .find(
                {
                    'students._id': convertToMongoObjectId(userId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    schedule_date: new Date(sessionDate),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    status: 1,
                    students: { $elemMatch: { _id: convertToMongoObjectId(userId) } },
                    'merge_with.schedule_id': 1,
                },
            )
            .lean()
            .sort({ updatedAt: -1 });
        if (!scheduledData.length) {
            return sendResponse(res, 200, false, 'NO SCHEDULE PRESENT IN THIS DATE');
        }
        const countDetails = {
            totalScheduleCount: scheduledData.length,
            totalScheduleCompletedCount: 0,
            presentCount: 0,
            abscentCount: 0,
        };
        let scheduleIds = [];
        for (const scheduledElement of scheduledData) {
            if (scheduledElement.status === COMPLETED) {
                countDetails.totalScheduleCompletedCount += 1;
            }
            scheduledElement.students.filter((studentElement) => {
                if (!scheduledElement.merge_with.length) {
                    if (studentElement.status === PRESENT) {
                        countDetails.presentCount += 1;
                    } else if (studentElement.status === ABSENT) {
                        countDetails.abscentCount += 1;
                    }
                } else if (
                    !scheduleIds.find(
                        (scheduleIdElement) =>
                            scheduleIdElement.toString() === scheduledElement._id.toString(),
                    )
                ) {
                    if (studentElement.status === PRESENT) {
                        countDetails.presentCount += 1 + scheduledElement.merge_with.length;
                    } else if (studentElement.status === ABSENT) {
                        countDetails.abscentCount += 1 + scheduledElement.merge_with.length;
                    }
                    scheduleIds = [
                        ...scheduleIds,
                        ...scheduledElement.merge_with.map((mergeWithElement) =>
                            mergeWithElement.schedule_id.toString(),
                        ),
                    ];
                }
            });
        }
        return sendResponse(res, 200, true, 'TOTAL SCHEDULE', countDetails);
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'DCUser -> Total Schedule Count -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.recentAssessment = async (req, res) => {
    try {
        const {
            headers: { _institution_id, _institution_calendar_id },
            query: { childId },
        } = req;
        const queryParsing = { _institution_id, _institution_calendar_id, childId };
        const assessmentData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(queryParsing._institution_id),
                type: 'course',
                status: 'published',
                // _institution_calendar_id: convertToMongoObjectId(
                //     queryParsing._institution_calendar_id,
                // ),
                'studentDetails.studentId': String(queryParsing.childId),
            },
            {
                assessmentTypeName: 1,
                assessmentTypeId: 1,
            },
        );
        if (!assessmentData) return sendResponse(res, 200, false, 'NO_Data');

        const assessmentTypeCounts = assessmentData.reduce((assessment, elementTypeId) => {
            const { assessmentTypeId } = elementTypeId;

            if (!assessment[assessmentTypeId]) {
                assessment[assessmentTypeId] = { assessmentTypeId, count: 0 };
            }

            assessment[assessmentTypeId].count++;

            return assessment;
        }, {});

        const recentAssessmentCount = Object.values(assessmentTypeCounts).map(
            ({ assessmentTypeId, count }) => {
                const assessmentTypeName = assessmentData.find(
                    (assessment) =>
                        assessment.assessmentTypeId.toString() === assessmentTypeId.toString(),
                ).assessmentTypeName;
                return { assessmentTypeName, count };
            },
        );
        return sendResponse(res, 200, true, 'List_Data', recentAssessmentCount);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.courseAssignmentLog = async (req, res) => {
    try {
        const {
            headers: { _institution_id, _institution_calendar_id },
            query: { childId },
        } = req;
        const queryParsing = { _institution_id, _institution_calendar_id, childId };
        const assessmentData = await assessmentLibrarySchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(queryParsing._institution_id),
                    type: 'course',
                    status: 'published',
                    // _institution_calendar_id: convertToMongoObjectId(
                    //     queryParsing._institution_calendar_id,
                    // ),
                    'studentDetails.studentId': String(queryParsing.childId),
                },
                {
                    _course_id: 1,
                    level: 1,
                    'studentDetails.$': 1,
                    'questionMarks.totalMark': 1,
                    // 'questionMarks.questionName': 1,
                    assessmentTypeName: 1,
                    assessmentTypeId: 1,
                    assessmentName: 1,
                    _assessment_id: 1,
                    assessmentMark: 1,
                    publishedTime: 1,
                    type: 1,
                },
            )
            .populate({ path: '_program_id', select: { name: 1 } })
            .populate({ path: '_course_id', select: { course_code: 1, course_name: 1 } })
            .sort({ publishedTime: -1 })
            .lean();
        if (!assessmentData) sendResponse(res, 200, false, 'NO_Data');
        const assessmentResponse = [];
        for (assessmentElement of assessmentData) {
            assessmentResponse.push({
                _id: assessmentElement._id,
                level: assessmentElement.level,
                ...(assessmentElement.type === 'program'
                    ? {
                          programName: assessmentElement._program_id.name,
                      }
                    : {
                          _course_id: assessmentElement._course_id._id,
                          course_name: assessmentElement._course_id.course_name,
                          course_code: assessmentElement._course_id.course_code,
                      }),

                _assessment_id: assessmentElement._assessment_id,
                assessmentName: assessmentElement.assessmentName,
                questionCount: assessmentElement.questionMarks.length,
                questionTotalMark: assessmentElement.assessmentMark,
                studentTotalMark: assessmentElement.studentDetails[0].studentMarks
                    .filter((studentMarkElement) => studentMarkElement.mark)
                    .reduce((acc, curr) => curr.mark + acc, 0),
                studentStatus: assessmentElement.studentDetails[0].attendance,
                assessmentTypeName: assessmentElement.assessmentTypeName,
                publishedTime: assessmentElement.publishedTime,
            });
        }
        return sendResponse(res, 200, true, 'List_Data', assessmentResponse);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleCourseAssignmentLogin = async (req, res) => {
    try {
        const {
            query: { childId, assessmentId, view },
            headers: { _institution_id },
        } = req;
        const queryParsing = { childId, assessmentId, view };
        const assessmentData = await assessmentLibrarySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(queryParsing.assessmentId),
                    'studentDetails.studentId': String(queryParsing.childId),
                },
                {
                    _course_id: 1,
                    _program_id: 1,
                    level: 1,
                    'studentDetails.$': 1,
                    questionMarks: 1,
                    assessmentTypeName: 1,
                    assessmentTypeId: 1,
                    assessmentName: 1,
                    _assessment_id: 1,
                    questionOutcome: 1,
                    type: 1,
                },
            )
            .populate({ path: '_course_id', select: { course_name: 1, course_code: 1 } })
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();
        if (!assessmentData) return sendResponse(res, 200, false, 'NO_Data');
        if (view && view === 'full') {
            let ourComeData = [];
            if (assessmentData.questionOutcome.toLowerCase() === CLO) {
                ourComeData = await getCourseCLO({
                    _institution_id,
                    courseId: assessmentData._course_id._id,
                });
            } else if (assessmentData.questionOutcome.toLowerCase() === SLO) {
                ourComeData = (
                    await getCourseSLO({
                        _institution_id,
                        programId: assessmentData._program_id,
                        courseId: assessmentData._course_id._id,
                    })
                ).session_flow_data
                    .map((sessionElement) => sessionElement.slo.flat())
                    .flat();
            } else {
                ourComeData = (
                    await curriculumSchema
                        .find(
                            {
                                _institution_id: convertToMongoObjectId(_institution_id),
                                _program_id: convertToMongoObjectId(assessmentData._program_id._id),
                            },
                            {
                                'framework.domains.plo._id': 1,
                                'framework.domains.plo.no': 1,
                                'framework.domains.plo.name': 1,
                            },
                        )
                        .lean()
                )
                    .map((programPLOElement) =>
                        programPLOElement.framework.domains
                            .map((domainElement) => domainElement.plo.flat())
                            .flat(),
                    )
                    .flat();
            }
            assessmentData.questionMarks.forEach((questionMarkElement) => {
                questionMarkElement.outCome = questionMarkElement.outComeIds.map(
                    (outComeIdElement) =>
                        ourComeData.find(
                            (ourComeDataElement) =>
                                ourComeDataElement._id.toString() === outComeIdElement.toString(),
                        ),
                );
            });
        }
        return sendResponse(res, 200, true, 'List_Data', assessmentData);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.outComeDetails = async (req, res) => {
    try {
        const {
            query: { outcomeId, questionOutcome, programId, courseId },
            headers: { _institution_id },
        } = req;
        let ourComeData = [];
        if (questionOutcome.toLowerCase() === CLO) {
            ourComeData = await getCourseCLO({
                _institution_id,
                courseId,
            });
        } else if (questionOutcome.toLowerCase() === SLO) {
            ourComeData = (
                await getCourseSLO({
                    _institution_id,
                    programId,
                    courseId,
                })
            ).session_flow_data
                .map((sessionElement) => sessionElement.slo.flat())
                .flat();
        } else if (questionOutcome.toLowerCase() === PLO) {
            ourComeData = (
                await curriculumSchema
                    .find(
                        {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            _program_id: convertToMongoObjectId(programId),
                        },
                        {
                            'framework.domains.plo._id': 1,
                            'framework.domains.plo.no': 1,
                            'framework.domains.plo.name': 1,
                        },
                    )
                    .lean()
            )
                .map((programPLOElement) =>
                    programPLOElement.framework.domains
                        .map((domainElement) => domainElement.plo.flat())
                        .flat(),
                )
                .flat();
        }

        ourComeData = ourComeData.find(
            (outcomeIdElement) => outcomeIdElement._id.toString() === outcomeId.toString(),
        );

        if (!ourComeData) return sendResponse(res, 200, false, 'NO_Data');
        return sendResponse(res, 200, true, 'List_Data', ourComeData);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.childEmergencyDetails = async (req, res) => {
    try {
        const {
            body: { childId, latitude, longitude },
            headers: { _institution_id },
        } = req;
        const queryParsing = { childId, latitude, longitude, _institution_id };
        const childEmergencyData = await childEmergencySchema.create({
            childId: convertToMongoObjectId(queryParsing.childId),
            _institution_id: convertToMongoObjectId(queryParsing._institution_id),
            latitude: queryParsing.latitude,
            longitude: queryParsing.longitude,
        });
        if (!childEmergencyData) return sendResponse(res, 200, false, 'UNABLE_TO_ADD');

        const parentData = await parentUserSchema
            .findOne(
                {
                    childIds: {
                        $elemMatch: { $eq: convertToMongoObjectId(queryParsing.childId) },
                    },
                },
                {
                    device_type: 1,
                    fcm_token: 1,
                    'childIds.$': 1,
                },
            )
            .populate({ path: 'childIds', select: { name: 1 } });
        fcm_token = parentData.fcm_token;
        device_type = parentData.device_type;
        const message = `${nameFormatter(
            parentData.childIds[0].name,
        )} is currently in an emergency situation`;
        const title = 'EMERGENCY ALERT';
        const pushData = {
            Title: title,
            Body: message,
            ClickAction: 'sos_start',
            SosCreatedAt: childEmergencyData.createdAt,
            childId: queryParsing.childId,
        };
        sendNotificationPush(fcm_token, pushData, device_type);

        return sendResponse(res, 200, true, 'DATA_ADDED');
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.cancelledEmergencyAlert = async (req, res) => {
    try {
        const {
            body: { childId, isActive },
            headers: { _institution_id },
        } = req;
        const queryParsing = { childId, isActive, _institution_id };
        const childEmergencyData = await childEmergencySchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(queryParsing._institution_id),
                childId: convertToMongoObjectId(queryParsing.childId),
            },
            {
                isActive: queryParsing.isActive,
            },
        );
        if (!childEmergencyData) return sendResponse(res, 200, false, 'UNABLE_TO_UPDATE');
        const parentData = await parentUserSchema
            .findOne(
                { childIds: { $elemMatch: { $eq: convertToMongoObjectId(queryParsing.childId) } } },
                {
                    device_type: 1,
                    fcm_token: 1,
                    'childIds.$': 1,
                },
            )
            .populate({ path: 'childIds', select: { name: 1 } });
        const title = 'EMERGENCY ALERT CANCELLED';
        const message = `${nameFormatter(
            parentData.childIds[0].name,
        )} has cancelled the emergency alert, it has been notified to all other contacts`;
        fcm_token = parentData.fcm_token;
        device_type = parentData.device_type;
        const pushData = {
            Title: title,
            Body: message,
            ClickAction: 'sos_end',
            childId: queryParsing.childId,
        };
        sendNotificationPush(fcm_token, pushData, device_type);
        return sendResponse(res, 200, true, 'UPDATE_DATA');
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.emergencyAlertStartTime = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { childId },
        } = req;
        queryParsing = { childId, _institution_id };
        const childData = await childEmergencySchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(queryParsing._institution_id),
                    childId: convertToMongoObjectId(queryParsing.childId),
                    isActive: true,
                },
                {
                    createdAt: 1,
                },
            )
            .sort({ _id: -1 });
        if (!childData) return sendResponse(res, 200, false, 'NO_DATA', childData);
        return sendResponse(res, 200, true, 'List_Data', childData);
    } catch (error) {
        console.log(error);
    }
    if (error instanceof Error) {
        throw error;
    } else {
        throw new Error(error);
    }
};
exports.viewNotificationInParent = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { childId },
        } = req;
        const queryParsing = { childId, _institution_id };
        const childData = await childEmergencySchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(queryParsing._institution_id),
                    childId: convertToMongoObjectId(queryParsing.childId),
                },
                {
                    latitude: 1,
                    longitude: 1,
                    isActive: 1,
                    createdAt: 1,
                    childId: 1,
                },
            )
            .sort({ _id: -1 })
            .populate({ path: 'childId', select: { name: 1, _id: 0 } });

        if (!childData) return sendResponse(res, 200, false, 'NO_DATA', childData);
        return sendResponse(res, 200, true, 'List_Data', childData);
    } catch (error) {
        console.log(error);
    }
    if (error instanceof Error) {
        throw error;
    } else {
        throw new Error(error);
    }
};

// user login staff and student
exports.ssoLoginService = async (req, res) => {
    try {
        const {
            body: { msTeamToken, userId, email, device_type, user_type, ssoProvider },
        } = req;
        logger.info('userController -> ssoLoginService -> %s Login %s - start', email, device_type);
        const userQuery = { email, isDeleted: false };
        const userProject = {
            status: 1,
            user_id: 1,
            user_type: 1,
            isActive: 1,
            verification: 1,
            mobile: 1,
        };
        const { status, data: user } = await get(User, userQuery, userProject);
        if (!status) {
            logger.warn('userController -> ssoLoginService -> %s Incorrect Email/Password', email);
            return sendResponse(res, 200, false, 'Incorrect Email/Password', null);
        }
        if (!user.verification && !user.mobile) {
            logger.warn(
                'userController -> ssoLoginService -> %s Only signed user able to access ',
                email,
            );
            return sendResponse(res, 200, false, 'Only signed user able to access ', null);
        }
        if (user.status !== 'completed') {
            logger.warn(
                'userController -> ssoLoginService -> %s Your Profile is not completed ask Admin',
                email,
            );
            return sendResponse(res, 200, false, 'Your Profile is not completed ask Admin', null);
        }
        if (user.isActive === false) {
            logger.warn(
                'userController -> ssoLoginService -> %s Your Account has been Deactivated, please contact Administrator.',
                email,
            );
            return sendResponse(
                res,
                200,
                false,
                'Your Account has been Deactivated, please contact Administrator.',
                null,
            );
        }
        if (user_type && user.user_type !== user_type) {
            logger.warn(
                'userController -> ssoLoginService -> %s Your not associated for this App',
                email,
            );
            return sendResponse(res, 200, false, 'Your not associated for this App', null);
        }

        if (!ssoProvider || ssoProvider === SSO_PROVIDER.TEAMS) {
            const ssoLoginService = await msTeamUserData({
                token: msTeamToken,
                userId,
            });
            if (ssoLoginService && ssoLoginService.error)
                return sendResponse(res, 200, false, 'Incorrect Email/Password', null);
        }
        // Anomaly Pure Check
        if (
            SERVICES.ANOMALY_RESTRICT &&
            SERVICES.ANOMALY_RESTRICT === 'true' &&
            user_type &&
            user.user_type === DS_TYPE_STUDENT
        ) {
            const institutionData = await institutionSchema.findOne(
                {
                    _id: convertToMongoObjectId(SERVICES.REACT_APP_INSTITUTION_ID),
                },
                { anomalyRestrict: 1 },
            );
            if (institutionData && institutionData.anomalyRestrict) {
                const studentAnomalyData = await scheduleAnomalySchema.findOne(
                    {
                        isDeleted: false,
                        studentId: convertToMongoObjectId(user._id),
                        pure: false,
                    },
                    { _id: 1 },
                );
                if (studentAnomalyData)
                    return sendResponse(res, 200, false, SERVICES.ANOMALY_RESTRICT_MESSAGE, null);
            }
        }
        const userRoleData = await userRoleGet({ userType: user.user_type, userId: user._id });
        const token = await generateAuthTokens({
            userId: user.user_id,
            _id: user._id,
            userType: user.user_type,
            userRoleData,
        });
        const responseData = {
            _id: user._id,
            email,
            tokens: token,
            services: SERVICES,
        };
        logger.info('userController -> ssoLoginService -> %s  Welcome to DigiClass', email);
        return sendResponseWithRequest(req, res, 200, true, 'Welcome to DigiClass', responseData);
    } catch (error) {
        console.error(error);
        return sendResponse(res, 500, false, 'Error Catch', error.toString());
    }
};

// user login staff and student
exports.mobileDeviceLogs = async (req, res) => {
    try {
        const {
            query: { userId, userType, deviceType, filterDate },
        } = req;
        let startOfDay;
        let endOfDay;
        if (filterDate) {
            const filterDateKey = new Date(filterDate);
            startOfDay = new Date(filterDateKey.setHours(0, 0, 0, 0)).toISOString();
            endOfDay = new Date(filterDateKey.setHours(23, 59, 59, 999)).toISOString();
        }
        const mobileDeviceLogs = await userDeviceDetailSchema
            .find(
                {
                    ...(userId && { userId }),
                    ...(userType && { userType }),
                    ...(deviceType && { deviceType }),
                    ...(filterDate && {
                        createdAt: {
                            $gte: startOfDay,
                            $lte: endOfDay,
                        },
                    }),
                },
                {
                    userId: 1,
                    userType: 1,
                    deviceType: 1,
                    deviceBrandName: 1,
                    deviceID: 1,
                    deviceNumber: 1,
                    deviceOSVersion: 1,
                    createdAt: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        return sendResponse(res, 200, true, 'DC Mobile Access Logs', mobileDeviceLogs);
    } catch (error) {
        console.error(error);
        return sendResponse(res, 500, false, 'Error Catch', error.toString());
    }
};

// user login staff and student
exports.ssoService = async (req, res) => {
    try {
        const {
            body: { userId, device_type, user_type },
        } = req;
        logger.info(
            JSON.stringify({ userId, device_type }),
            'userController -> ssoLoginService -> %s Login - start',
        );
        const userQuery = { isDeleted: false, _id: convertToMongoObjectId(userId) };
        const userProject = {
            status: 1,
            user_id: 1,
            isActive: 1,
            verification: 1,
            mobile: 1,
            email: 1,
            user_type: 1,
        };
        const userData = await User.findOne(userQuery, userProject).lean();

        if (!userData) {
            logger.warn('userController -> ssoLoginService -> %s Incorrect Email/Password', '');
            return sendResponse(res, 200, false, 'Incorrect Email/Password', null);
        }
        if (!userData.verification) {
            logger.warn(
                'userController -> ssoLoginService -> %s Only signed user able to access ',
                userData.email,
            );
            return sendResponse(res, 200, false, 'Only signed user able to access ', null);
        }
        if (userData.status !== COMPLETED) {
            logger.warn(
                'userController -> ssoLoginService -> %s Your Profile is not completed ask Admin',
                userData.email,
            );
            return sendResponse(res, 200, false, 'Your Profile is not completed ask Admin', null);
        }
        if (userData.isActive === false) {
            logger.warn(
                'userController -> ssoLoginService -> %s Your Account has been Deactivated, please contact Administrator.',
                userData.email,
            );
            return sendResponse(
                res,
                200,
                false,
                'Your Account has been Deactivated, please contact Administrator.',
                null,
            );
        }
        if (user_type && userData.user_type !== user_type) {
            logger.warn(
                'userController -> ssoLoginService -> %s Your not associated for this App',
                userData.email,
            );
            return sendResponse(res, 200, false, 'Your not associated for this App', null);
        }
        const userRoleData = await userRoleGet({
            userType: userData.user_type,
            userId: userData._id,
        });
        const responseData = {
            _id: userData._id,
            email: userData.email,
            tokens: await generateAuthTokens({
                userId: userData.user_id,
                _id: userData._id,
                userType: userData.user_type,
                userRoleData,
            }),
            services: SERVICES,
        };
        logger.info(
            JSON.stringify({ userId, device_type }),
            'userController -> ssoLoginService -> Welcome to DigiClass',
        );
        return sendResponse(res, 200, true, 'Welcome to DigiClass', responseData);
    } catch (error) {
        console.error(error);
        return sendResponse(res, 500, false, 'Error Catch', error.toString());
    }
};
