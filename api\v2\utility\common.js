const { TIMEZONE, LOCAL_TIMEZONE, APP_STATE } = require('./util_keys');
const { getS3SignedUrl } = require('../services');
const mongoose = require('mongoose');
const Chance = require('chance');

const getSignedURL = (url, from) => {
    const fileName = url.split('/').pop();
    const urlSplit = url.split('/');
    let bucketUrls = `${urlSplit[3]}`;
    for (let i = 4; i < urlSplit.length - 1; i++) {
        bucketUrls += `/${urlSplit[i]}`;
    }
    return getS3SignedUrl({
        // bucket: from && from === 'userData' ? BUCKET_NAME_USER_DATA : BUCKET_DOCUMENT,
        bucket: bucketUrls,
        key: fileName,
    });
};

const getDateString = () => {
    const today = new Date();
    const logfileByDate =
        today.getUTCDate() + '-' + parseInt(today.getUTCMonth() + 1) + '-' + today.getUTCFullYear();
    return logfileByDate;
};

const scheduleDateFormatChange = (dateTime) => {
    const timeZone = APP_STATE.toLowerCase() === 'production' ? TIMEZONE : LOCAL_TIMEZONE;
    const eveDate = new Date(dateTime).toLocaleString('en-US', {
        timeZone,
    });
    const splitData = eveDate.split(',');
    const dateSplit = splitData[0].split('/');
    return `${dateSplit[1] + '-' + dateSplit[0] + '-' + dateSplit[2]}`;
};

const convertToMongoObjectId = (id) => mongoose.Types.ObjectId(id);

const comResponse = (res, status_code, status, message, result_message) => {
    const server_response = {
        status_code,
        status,
        message,
        data: result_message,
    };
    return res.status(status_code).send(server_response);
};

const isIDEquals = (firstString = '', secondString = '') => {
    let status = false;

    if (firstString === '' || secondString === '') {
        return status;
    }

    if (firstString.toString() === secondString.toString()) {
        status = true;
    }
    return status;
};
const convertToAllCase = (String) => {
    const letterCase = [
        String,
        String.charAt(0).toUpperCase() + String.slice(1),
        String.toUpperCase(),
        String.toLowerCase(),
        String.trim(),
    ];
    return letterCase;
};
const convertToUpperCase = (String) => {
    return String.toUpperCase();
};
const response_function = (res, status_code, status, message, result_message) => {
    const server_response = {
        status_code,
        status,
        message,
        data: result_message,
    };
    return server_response;
};

const convertToMongoId = (id) => mongoose.Types.ObjectId(id);

// const getModel = (nativeConnection, modelName, schema) => nativeConnection.model(modelName, schema);

const getModel = (nativeConnection, modelName, schema) =>
    nativeConnection.models[modelName] || nativeConnection.model(modelName, schema);

const generateRandomNumber = (length) => {
    const max = 100000000;
    const min = 100;
    const randomNo = Math.floor(Math.random() * (max - min + 1) + min);
    const chance = new Chance(Date.now() + randomNo);
    const randomStr = chance.string({
        length,
        pool: 'abcdefghijklmnopqrstuvwxyz0123456789@',
    });
    return randomStr;
};

const check_value_or_null = (obj, key) => {
    return key.split('.').reduce(function (o, x) {
        return typeof o == 'undefined' || o === null ? o : o[x];
    }, obj);
};

const nameFormatter = (name) => {
    return (
        name.middle && name.middle.length !== 0
            ? name.first +
              ' ' +
              (name.middle && name.middle.length !== 0 ? name.middle : '') +
              ' ' +
              (name.last && name.last.length !== 0 ? name.last : '') +
              ' ' +
              (name.family && name.family.length !== 0 ? name.family : '')
            : name.first +
              ' ' +
              (name.last && name.last.length !== 0 ? name.last : '') +
              ' ' +
              (name.family && name.family.length !== 0 ? name.family : '')
    ).trim();
};

const getCountryCodes = () => {
    return [
        93, 355, 213, 1684, 376, 244, 1264, 672, 1268, 54, 374, 297, 61, 43, 994, 1242, 973, 880,
        1246, 375, 32, 501, 229, 1441, 975, 591, 387, 267, 55, 673, 359, 226, 257, 855, 237, 1, 238,
        1345, 236, 235, 56, 86, 53, 61, 57, 269, 243, 242, 682, 506, 225, 385, 53, 357, 420, 45,
        253, 1767, 1809, 1829, 670, 593, 20, 503, 240, 291, 372, 251, 500, 298, 679, 358, 33, 594,
        689, 241, 220, 995, 49, 233, 350, 30, 299, 1473, 590, 1671, 502, 224, 245, 592, 509, 504,
        852, 36, 354, 91, 62, 98, 964, 353, 972, 39, 1876, 81, 962, 7, 254, 686, 850, 82, 965, 996,
        856, 371, 961, 266, 231, 218, 423, 370, 352, 853, 389, 261, 265, 60, 960, 223, 356, 692,
        596, 222, 230, 269, 52, 691, 373, 377, 976, 1664, 212, 258, 95, 264, 674, 977, 31, 599, 687,
        64, 505, 227, 234, 683, 672, 1670, 47, 968, 92, 680, 970, 507, 675, 595, 51, 63, 48, 351,
        1787, 1939, 974, 262, 40, 7, 250, 290, 1869, 1758, 508, 1784, 685, 378, 239, 966, 221, 248,
        232, 65, 421, 386, 677, 252, 27, 34, 94, 249, 597, 268, 46, 41, 963, 886, 992, 255, 66, 690,
        676, 1868, 216, 90, 993, 1649, 688, 256, 380, 971, 44, 1, 598, 998, 678, 418, 58, 84, 1284,
        1340, 681, 967, 260, 263,
    ];
};

module.exports = {
    getSignedURL,
    getDateString,
    scheduleDateFormatChange,
    convertToMongoObjectId,
    comResponse,
    isIDEquals,
    response_function,
    convertToAllCase,
    convertToMongoId,
    convertToUpperCase,
    getModel,
    generateRandomNumber,
    check_value_or_null,
    nameFormatter,
    getCountryCodes,
};
