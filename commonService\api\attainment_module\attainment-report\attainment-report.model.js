const mongoose = require('mongoose');
const { ATTAINMENT_REPORT } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const coursePOTreeSchema = new Schema({
    typeName: String,
    typeId: { type: ObjectId },
    plo: [
        {
            _id: { type: ObjectId },
            percentage: { type: String },
            count: String,
        },
    ],
});

const treeSchema = new Schema({
    typeName: String,
    plo: [
        {
            _id: { type: ObjectId },
            percentage: { type: String },
            coursePOAttainment: String,
        },
    ],
});

const courseSchema = new Schema({
    year: String,
    levelNo: String,
    term: String,
    curriculum: String,
    rotation: String,
    _course_id: { type: ObjectId },
    coursesName: String,
    tree: [treeSchema],
    mapping_type: String,
    content_mapping_type: String,
    percentage: { type: String },
});

const assessmentReportSchemas = new Schema(
    {
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        _institution_id: {
            type: ObjectId,
        },
        _program_id: {
            type: ObjectId,
        },
        outCome: String,
        term: String,
        _attainment_id: { type: ObjectId },
        _institution_calendar_id: { type: ObjectId },
        courses: [courseSchema],
        courseTreePO: [coursePOTreeSchema],
    },
    { timestamps: true },
);

module.exports = mongoose.model(ATTAINMENT_REPORT, assessmentReportSchemas);
