const constant = require('../utility/constants');
var position = require('mongoose').model(constant.POSITION);
var institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const position_formate = require('./position_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(position, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "position list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ position_formate.position(doc.data));
        // common_files.list_all_response(res, 200, true, "position list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* position_formate.position(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
    ];
    let doc = await base_control.get_aggregate(position, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "position details", /* doc.data */position_formate.position_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let checks = await base_control.check_id(institution, { _id: { $in: req.body._institution_id }, 'isDeleted': false });
    if (checks.status) {
        let doc = await base_control.insert(position, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "position Added successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._institution_id != undefined) {
        checks = await base_control.check_id(institution, { _id: { $in: req.body._institution_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(position, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "position update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(position, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "position deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(position, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "position List", position_formate.position_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};