const departmentSubjectSchema = require('./department-subject.model');
const programSchema = require('../program-input/program-input.model');
const institutionSchema = require('../institution/institution.model');
const sessionOrderSchema = require('../session-order/session-order.model');
const courseSchema = require('../course/course.model');
const userSchema = require('../user-management/user.model');
const { getPaginationValues } = require('../../utility/pagination');
const {
    DS_DELETED,
    DS_GET_FAILED,
    DS_DELETE_FAILED,
    DS_SAVED,
    DS_SAVE_FAILED,
    ACADEMIC,
    ADMIN,
    DEPARTMENT_SUBJECT,
    INSTITUTION,
    PROGRAM,
    USER,
    SESSION_ORDER,
    COURSE,
    ADD,
    REMOVE,
} = require('../../utility/constants');
const {
    getDepartmentStats,
    filterDepartmentSubjects,
    filterSharedSubjects,
    populateInstituteDepartments,
} = require('./department-subject.util');
const { convertToMongoObjectId, convertToAllCase, getModel } = require('../../utility/common');

const addDepartment = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { departmentName, _program_id, programName, _institution_id, type } = body;
        if (type === ACADEMIC && !_program_id) {
            return { statusCode: 400, message: 'PROGRAM_ID_REQUIRED' };
        }
        let createQuery = {
            departmentName,
            _institution_id,
            type,
        };
        if (type === ACADEMIC && _program_id) {
            createQuery = { ...createQuery, _program_id, programName };
        }
        const institution = await institutionModel.findById(_institution_id).lean();
        if (!institution) return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        if (institution && institution.parentInstitute) {
            createQuery = { ...createQuery, _parent_id: institution.parentInstitute };
        }
        let existsQuery = {
            _institution_id,
            isDeleted: false,
            isActive: true,
        };
        if (_program_id && type === ACADEMIC) existsQuery = { ...existsQuery, _program_id };
        else existsQuery = { ...existsQuery, type: ADMIN };
        const departments = await departmentSubjectModel.find(existsQuery).lean();

        let duplicateDepartmentExists = false;
        // Start checking Department with all cases in existing Department array
        const departmentList = departments.map((elementEntry) => {
            return elementEntry.departmentName.toLowerCase();
        });

        const isExists = departmentList.includes(departmentName.toLowerCase());
        if (isExists) {
            duplicateDepartmentExists = true;
        }
        if (duplicateDepartmentExists) return { statusCode: 400, message: 'NAME_EXISTS' };
        // End checking Department with all cases in existing Department array

        const department = await departmentSubjectModel.create(createQuery);
        if (!department) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDepartment = async ({ params = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        let department = await departmentSubjectModel
            .findOne(
                {
                    _id: id,
                    isActive: true,
                    isDeleted: false,
                },
                { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 },
            )
            .lean();
        department = filterDepartmentSubjects(department);
        if (!department) {
            return { statusCode: 500, message: 'DEPARTMENT_NOT_EXISTS' };
        }
        return { statusCode: 200, data: { department } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDepartments = async ({ query = {}, body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        const { programId, type, searchKey } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        let dbQuery = {
            _institution_id: id,
            type,
            isActive: true,
            isDeleted: false,
        };
        if (!programId && type === ACADEMIC) {
            return { statusCode: 400, message: 'PROGRAM_ID_REQUIRED' };
        }
        if (programId && type === ACADEMIC) {
            const programExists = await programModel.findById(programId).lean();
            if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
            dbQuery = { ...dbQuery, _program_id: programId };
        }
        if (searchKey && searchKey.length) {
            dbQuery = {
                ...dbQuery,
                departmentName: { $regex: searchKey, $options: 'i' },
            };
        }
        let departments = await departmentSubjectModel
            .find(dbQuery, {
                departmentName: 1,
                subject: 1,
                sharedWith: 1,
                isActive: 1,
            })
            .skip(skip)
            .limit(limit)
            .lean();
        departments = filterDepartmentSubjects(departments);
        const totalDepartments = await departmentSubjectModel.find(dbQuery).countDocuments().lean();
        let departmentsSharedToThisProgram = await departmentSubjectModel
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    sharedWith: { $elemMatch: { _program_id: programId } },
                },
                {
                    'subject.subjectName': 1,
                    'subject._id': 1,
                    'subject.isDeleted': 1,
                    'subject.isActive': 1,
                    departmentName: 1,
                    programName: 1,
                    _program_id: 1,
                },
            )
            .lean();
        departmentsSharedToThisProgram = filterDepartmentSubjects(departmentsSharedToThisProgram);
        let subjectsSharedToThisProgram = await departmentSubjectModel.find(
            {
                isDeleted: false,
                isActive: true,
                // 'sharedWith._program_id': { $nin: [programId] },
                subject: {
                    $elemMatch: {
                        isActive: true,
                        isDeleted: false,
                        'sharedWith._program_id': programId,
                    },
                },
            },
            {
                subject: 1,
                programName: 1,
                _program_id: 1,
                departmentName: 1,
            },
        );
        subjectsSharedToThisProgram = filterSharedSubjects(subjectsSharedToThisProgram, programId);
        if (
            !departments ||
            Number.isNaN(totalDepartments) ||
            !subjectsSharedToThisProgram ||
            !departmentsSharedToThisProgram
        ) {
            return { statusCode: 500, message: DS_GET_FAILED };
        }
        return {
            statusCode: 200,
            data: {
                subjectsSharedToThisProgram,
                departmentsSharedToThisProgram,
                departments,
                currentPage: pageNo,
                totalPages: Math.ceil(totalDepartments / limit),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstituteDepartments = async ({ params = {}, query = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const programInputModel = getModel(tenantURL, PROGRAM, programSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { id } = params;
        const { type, showShared, searchKey } = query;
        const {
            filterProgramIds,
            filterSubjectIds,
            filterSharedWithIds,
            filterSharedFromIds,
            filterDepartmentIds,
        } = body;
        const { limit, pageNo, skip } = getPaginationValues(query);
        let programQuery = {
            _institution_id: id,
            isActive: true,
            isDeleted: false,
        };
        if (filterProgramIds && filterProgramIds.length) {
            programQuery = { ...programQuery, _id: { $in: filterProgramIds } };
        }
        const allPrograms = await programInputModel.find(programQuery, { _id: 1 }).lean();
        const programIds = allPrograms.map((program) => program._id.toString());
        let dbQuery = {
            _institution_id: id,
            type,
            isActive: true,
            isDeleted: false,
        };
        if (type === 'academic') {
            dbQuery = {
                ...dbQuery,
                _program_id: { $in: programIds },
            };
        }
        const filterConditions = [];
        const allDepartmentsQuery = { _institution_id: id, isActive: true, isDeleted: false };
        if (searchKey && searchKey.length) {
            const searchKeyConditions = [
                { departmentName: { $regex: searchKey, $options: 'i' } },
                { programName: { $regex: searchKey, $options: 'i' } },
                { 'subject.subjectName': { $regex: searchKey, $options: 'i' } },
            ];
            filterConditions.push(...searchKeyConditions);
        }
        if (filterSubjectIds && filterSubjectIds.length) {
            filterConditions.push({ 'subject._id': { $in: filterSubjectIds } });
        }
        if (filterSharedWithIds && filterSharedWithIds.length) {
            filterConditions.push({ 'sharedWith._program_id': { $in: filterSharedWithIds } });
        } else if (filterSharedFromIds && filterSharedFromIds.length) {
            filterConditions.push({ _id: { $in: filterSharedFromIds } });
        }
        if (filterDepartmentIds && filterDepartmentIds.length) {
            filterConditions.push({ _id: { $in: filterDepartmentIds } });
        }
        if (filterConditions && filterConditions.length) {
            dbQuery = { ...dbQuery, $or: filterConditions };
        }
        const allDepartments = await departmentSubjectModel.find(allDepartmentsQuery).lean();
        let departments = await departmentSubjectModel.find(dbQuery).skip(skip).limit(limit).lean();
        for (const department of departments) {
            const isDeptAssigned = await courseModel
                .findOne(
                    {
                        $or: [
                            { 'administration._department_id': department._id },
                            { 'participating._department._id': department._id },
                        ],
                    },
                    { _id: 1 },
                )
                .lean();
            if (isDeptAssigned) department.isDepartmentAssigned = true;
            else department.isDepartmentAssigned = false;
            const isDepartmentOrSubjectShared =
                department.sharedWith.length ||
                department.subject
                    .map((subject) => subject.sharedWith.length)
                    .filter((length) => length != 0).length;
            department.isDepartmentOrSubjectShared = isDepartmentOrSubjectShared > 0;
        }
        departments = filterDepartmentSubjects(departments);
        if (showShared === 'true')
            departments = populateInstituteDepartments({
                departments,
                allDepartments,
                programIds,
                type,
            });
        const totalDepartments = await departmentSubjectModel.countDocuments(dbQuery).lean();
        return {
            statusCode: 200,
            data: {
                departments,
                currentPage: pageNo,
                totalPages: Math.ceil(totalDepartments / limit),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramWiseDepts = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const aggregateStages = [
            {
                $match: {
                    _institution_id: convertToMongoObjectId(id),
                    isActive: true,
                    isDeleted: false,
                },
            },
            {
                $lookup: {
                    from: DEPARTMENT_SUBJECT,
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'departments',
                    pipeline: [
                        {
                            $match: { isDeleted: false, isActive: true },
                        },
                    ],
                },
            },
            {
                $group: {
                    _id: '$_id',
                    name: { $first: '$name' },
                    departments: { $first: '$departments' },
                },
            },
            {
                $project: {
                    programId: '$_id',
                    programName: '$name',
                    departments: '$departments',
                    _id: 0,
                },
            },
            {
                $project: {
                    programId: 1,
                    programName: 1,
                    'departments._id': 1,
                    'departments.departmentName': 1,
                },
            },
        ];
        const programWiseDepts = await programModel.aggregate(aggregateStages);
        if (!programWiseDepts) {
            return { statusCode: 500, message: DS_GET_FAILED };
        }
        return { statusCode: 200, data: { programWiseDepts } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addSubject = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        const { subjects } = body;
        const departmentExists = await departmentSubjectModel.findById(id).lean();
        if (!departmentExists) return { statusCode: 400, message: 'DEPARTMENT_NOT_EXISTS' };

        let areSubjectNamesSame = false;
        const hashMap = {};
        subjects.forEach((subject) => {
            if (!hashMap[[subject.subjectName.toLowerCase()]])
                hashMap[[subject.subjectName.toLowerCase()]] = true;
            else areSubjectNamesSame = true;
        });

        if (areSubjectNamesSame) {
            return { statusCode: 400, message: 'DUPLICATE_NAMES' };
        }

        const programId = departmentExists._program_id;
        const departments = await departmentSubjectModel.find({
            _program_id: programId,
            isDeleted: false,
        });
        let duplicateSubjectExists = false;
        // Start checking subjects with all cases in existing subject array
        departments.forEach((departmentEntry) => {
            if (departmentEntry && departmentEntry.subject && departmentEntry.subject.length > 0) {
                const checkedSubjects = departmentEntry.subject
                    .filter(function (element) {
                        return element.isDeleted === false;
                    })
                    .map(function (elementEntry) {
                        return elementEntry.subjectName.toLowerCase();
                    });
                const givenSubjects = subjects.map(function (elementEntry) {
                    return elementEntry.subjectName.toLowerCase();
                });
                const isExists = checkedSubjects.some(function (element) {
                    return givenSubjects.indexOf(element) !== -1;
                });
                if (isExists) {
                    duplicateSubjectExists = true;
                }
            }
        });
        if (duplicateSubjectExists) return { statusCode: 400, message: 'NAME_EXISTS' };
        // End checking subjects with all cases in existing subject array
        const department = await departmentSubjectModel.findOneAndUpdate(
            {
                _id: id,
            },
            { $push: { subject: { $each: subjects } } },
            { new: true, select: { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 } },
        );
        if (!department) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editDepartment = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { departmentName, _program_id } = body;
        const { id } = params;
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const userModel = getModel(tenantURL, USER, userSchema);
        let existsQuery = {
            _id: { $ne: convertToMongoObjectId(id) },
            isDeleted: false,
            isActive: true,
        };
        if (_program_id) existsQuery = { ...existsQuery, _program_id };
        else existsQuery = { ...existsQuery, type: ADMIN };
        const departments = await departmentSubjectModel.find(existsQuery);
        let duplicateDepartmentExists = false;
        // Start checking Department with all cases in existing Department array
        const departmentList = departments.map((elementEntry) => {
            return elementEntry.departmentName.toLowerCase();
        });
        const isExists = departmentList.includes(departmentName.toLowerCase());
        if (isExists) {
            duplicateDepartmentExists = true;
        }
        if (duplicateDepartmentExists) return { statusCode: 400, message: 'NAME_EXISTS' };
        // End checking Department with all cases in existing Department array

        const department = await departmentSubjectModel
            .findByIdAndUpdate(
                id,
                {
                    departmentName,
                },
                {
                    new: true,
                    select: { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 },
                },
            )
            .lean();

        const updateAllocation = await userModel.updateMany(
            {
                'academic_allocation._department_id': convertToMongoObjectId(id),
            },
            {
                'academic_allocation.$[i].departmentName': departmentName,
            },
            {
                arrayFilters: [
                    {
                        'i._department_id': convertToMongoObjectId(id),
                    },
                ],
            },
        );
        if (!department) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }

        // start updating department subject table
        const DepartmentSubjects = await departmentSubjectModel.find(
            { 'subject.sharedWith._department_id': convertToMongoObjectId(id) },
            {},
        );
        const bulkUpdate = [];
        for (DepartmentSubjectList of DepartmentSubjects) {
            if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                for (subjectList of DepartmentSubjectList.subject) {
                    if (subjectList.sharedWith.length > 0) {
                        const programIdCheck = subjectList.sharedWith.filter(function (element) {
                            return (
                                element._department_id.toString() === id.toString() &&
                                element._program_id.toString() === _program_id.toString()
                            );
                        });
                        if (programIdCheck.length > 0) {
                            for (sharedProgram of programIdCheck)
                                bulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoObjectId(DepartmentSubjectList._id),
                                        },
                                        update: {
                                            $set: {
                                                'subject.$[subjectId].sharedWith.$[programId].departmentName':
                                                    departmentName,
                                            },
                                        },
                                        arrayFilters: [
                                            {
                                                'programId._id': convertToMongoObjectId(
                                                    sharedProgram._id,
                                                ),
                                            },
                                            {
                                                'subjectId._id': convertToMongoObjectId(
                                                    subjectList._id,
                                                ),
                                            },
                                        ],
                                    },
                                });
                        }
                    }
                }
            }
        }
        await departmentSubjectModel.bulkWrite(bulkUpdate);
        const courseDoc = await courseModel.updateMany(
            {
                'participating._department_id': convertToMongoObjectId(id),
                isDeleted: false,
            },
            { $set: { 'participating.$[dept].departmentName': departmentName } },
            {
                arrayFilters: [
                    {
                        'dept._department_id': convertToMongoObjectId(id),
                    },
                ],
            },
        );
        const courseDocFroAdmin = await courseModel.updateMany(
            {
                'administration._department_id': convertToMongoObjectId(id),
                isDeleted: false,
            },
            { $set: { 'administration.departmentName': departmentName } },
        );
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const editDepartmentRestructure = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { departmentName, type, programName, _program_id, isDepartmentAssignedOrShared } =
            body;
        const { id } = params;

        // Start checking Department with all cases in existing Department array
        let existsQuery = {
            _id: { $ne: convertToMongoObjectId(id) },
            isDeleted: false,
            isActive: true,
        };
        if (_program_id) existsQuery = { ...existsQuery, _program_id };
        else existsQuery = { ...existsQuery, type: ADMIN };
        const departments = await departmentSubjectModel.find(existsQuery).lean();
        let duplicateDepartmentExists = false;
        const departmentList = departments.map((elementEntry) => {
            return elementEntry.departmentName.toLowerCase();
        });
        const isExists = departmentList.includes(departmentName.toLowerCase());
        if (isExists) {
            duplicateDepartmentExists = true;
        }
        if (duplicateDepartmentExists) return { statusCode: 400, message: 'NAME_EXISTS' };
        // End checking Department with all cases in existing Department array

        let departmentUpdateQuery = { departmentName };
        if (!isDepartmentAssignedOrShared) {
            if (type === ADMIN)
                departmentUpdateQuery = {
                    ...departmentUpdateQuery,
                    type,
                    $unset: { _program_id: '', programName: '' },
                };
            else
                departmentUpdateQuery = {
                    ...departmentUpdateQuery,
                    type,
                    programName,
                    _program_id,
                };
        }
        const department = await departmentSubjectModel
            .findByIdAndUpdate(id, departmentUpdateQuery, {
                new: true,
                select: { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 },
            })
            .lean();
        if (!department) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }

        // start updating department subject table
        if (isDepartmentAssignedOrShared) {
            const DepartmentSubjects = await departmentSubjectModel.find(
                { 'subject.sharedWith._department_id': convertToMongoObjectId(id) },
                {},
            );
            const bulkUpdate = [];
            for (DepartmentSubjectList of DepartmentSubjects) {
                if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                    for (subjectList of DepartmentSubjectList.subject) {
                        if (subjectList.sharedWith.length > 0) {
                            const programIdCheck = subjectList.sharedWith.filter(function (
                                element,
                            ) {
                                return (
                                    element._department_id.toString() === id.toString() &&
                                    element._program_id.toString() === _program_id.toString()
                                );
                            });
                            if (programIdCheck.length > 0) {
                                for (sharedProgram of programIdCheck)
                                    bulkUpdate.push({
                                        updateOne: {
                                            filter: {
                                                _id: convertToMongoObjectId(
                                                    DepartmentSubjectList._id,
                                                ),
                                            },
                                            update: {
                                                $set: {
                                                    'subject.$[subjectId].sharedWith.$[programId].departmentName':
                                                        departmentName,
                                                },
                                            },
                                            arrayFilters: [
                                                {
                                                    'programId._id': convertToMongoObjectId(
                                                        sharedProgram._id,
                                                    ),
                                                },
                                                {
                                                    'subjectId._id': convertToMongoObjectId(
                                                        subjectList._id,
                                                    ),
                                                },
                                            ],
                                        },
                                    });
                            }
                        }
                    }
                }
            }
            await departmentSubjectModel.bulkWrite(bulkUpdate);
            const courseDoc = await courseModel.updateMany(
                {
                    'participating._department_id': convertToMongoObjectId(id),
                    isDeleted: false,
                },
                { $set: { 'participating.$[dept].departmentName': departmentName } },
                {
                    arrayFilters: [
                        {
                            'dept._department_id': convertToMongoObjectId(id),
                        },
                    ],
                },
            );
            const courseDocFroAdmin = await courseModel.updateMany(
                {
                    'administration._department_id': convertToMongoObjectId(id),
                    isDeleted: false,
                },
                { $set: { 'administration.departmentName': departmentName } },
            );
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const shareDepartment = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { sharedPrograms, departmentId, status } = body;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        if (status === REMOVE) {
            const courseDoc = await courseModel
                .find({
                    $or: [
                        { 'participating._department_id': convertToMongoObjectId(departmentId) },
                        { 'administration._department_id': convertToMongoObjectId(departmentId) },
                    ],
                    isDeleted: false,
                })
                .select('_program_id courseName');
            let checkProgramMappedInCourse = false;
            if (courseDoc.length) {
                const departmentCheck = await departmentSubjectModel.findById(departmentId, {
                    departmentName: 1,
                    subject: 1,
                    sharedWith: 1,
                    isActive: 1,
                });
                const oldSharedPrograms = [];
                departmentCheck.sharedWith.forEach((sharedWit) => {
                    if (sharedWit._program_id)
                        oldSharedPrograms.push(sharedWit._program_id.toString());
                });
                const sharingProgram = [];
                sharedPrograms.forEach((prog) => {
                    sharingProgram.push(prog._program_id);
                });
                oldSharedPrograms.forEach((program) => {
                    if (!sharingProgram.includes(program)) {
                        courseDoc.forEach((course) => {
                            if (course._program_id && course._program_id.toString() === program) {
                                checkProgramMappedInCourse = true;
                            }
                        });
                    }
                });
            }
            if (checkProgramMappedInCourse) {
                return {
                    statusCode: 500,
                    message: 'SHARED_DEPARTMENT_SUBJECT_IS_MAPPED_WITH_ANOTHER_PROGRAM_COURSE',
                };
            }
        }
        const department = await departmentSubjectModel.findByIdAndUpdate(
            departmentId,
            {
                sharedWith: sharedPrograms,
            },
            { new: true, select: { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 } },
        );
        if (!department) {
            return { statusCode: 500, message: 'DEPARTMENT_FAILED_TO_BE_SHARED' };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const shareSubjects = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { subjectId, departmentId, sharedProgramsAndDepartments, status } = body;
        if (status === REMOVE) {
            const courseDoc = await courseModel
                .find({
                    $or: [
                        { 'participating._subject_id': convertToMongoObjectId(subjectId) },
                        { 'administration._subject_id': convertToMongoObjectId(subjectId) },
                    ],
                    isDeleted: false,
                })
                .select('_program_id courseName');
            let checkProgramMappedInCourse = false;
            if (courseDoc.length) {
                const departmentCheck = await departmentSubjectModel.findById(departmentId, {
                    departmentName: 1,
                    subject: 1,
                    sharedWith: 1,
                    isActive: 1,
                });
                const oldSharedPrograms = [];
                departmentCheck.subject.forEach((sub) => {
                    sub.sharedWith.forEach((sharedWit) => {
                        if (sharedWit._program_id)
                            oldSharedPrograms.push(sharedWit._program_id.toString());
                    });
                });
                const sharingProgram = [];
                sharedProgramsAndDepartments.forEach((prog) => {
                    sharingProgram.push(prog._program_id);
                });
                oldSharedPrograms.forEach((program) => {
                    if (!sharingProgram.includes(program)) {
                        courseDoc.forEach((course) => {
                            if (course._program_id && course._program_id.toString() === program) {
                                checkProgramMappedInCourse = true;
                            }
                        });
                    }
                });
            }
            if (checkProgramMappedInCourse) {
                return {
                    statusCode: 500,
                    message: 'SHARED_SUBJECT_IS_MAPPED_WITH_ANOTHER_PROGRAM_COURSE',
                };
            }
        }
        const department = await departmentSubjectModel.findOneAndUpdate(
            {
                _id: departmentId,
                subject: { $elemMatch: { _id: subjectId } },
            },
            { $set: { 'subject.$.sharedWith': sharedProgramsAndDepartments } },
            { new: true },
        );
        if (!department) {
            return { statusCode: 500, message: 'FAILED_TO_SHARE_SUBJECTS' };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteDepartment = async ({ headers = {}, params = {} }) => {
    try {
        const { id } = params;
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const courseDoc = await courseModel.find({
            $or: [
                { 'participating._department_id': convertToMongoObjectId(id) },
                { 'administration._department_id': convertToMongoObjectId(id) },
            ],
            isDeleted: false,
        });
        if (courseDoc.length > 0) {
            return { statusCode: 500, message: 'DEPARTMENT_IS_ASSIGNED_TO_COURSE' };
        }
        const department = await departmentSubjectModel.findByIdAndUpdate(
            id,
            {
                isDeleted: true,
                sharedWith: [],
            },
            { new: true },
        );
        // start updating department subject table
        const DepartmentSubjects = await departmentSubjectModel.find(
            { 'subject.sharedWith._department_id': convertToMongoObjectId(id) },
            { subject: 1 },
        );
        const bulkUpdate = [];
        for (DepartmentSubjectList of DepartmentSubjects) {
            if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                for (subjectList of DepartmentSubjectList.subject) {
                    if (subjectList.sharedWith.length > 0) {
                        const departmentIdCheck = subjectList.sharedWith.filter(function (element) {
                            return element._department_id.toString() === id.toString();
                        });
                        if (departmentIdCheck.length > 0) {
                            for (sharedProgram of departmentIdCheck)
                                bulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoObjectId(DepartmentSubjectList._id),
                                        },
                                        update: {
                                            $pull: {
                                                'subject.$[i].sharedWith': { _department_id: id },
                                            },
                                        },
                                        arrayFilters: [
                                            {
                                                'i._id': convertToMongoObjectId(subjectList._id),
                                            },
                                        ],
                                    },
                                });
                        }
                    }
                }
            }
        }
        await departmentSubjectModel.bulkWrite(bulkUpdate);
        if (!department) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editSubject = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentModel = getModel(tenantURL, DEPARTMENT_SUBJECT, departmentSubjectSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const userModel = getModel(tenantURL, USER, userSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { departmentId, subjectId, subjectName } = body;
        const dept = await departmentModel.findOne(
            { _id: departmentId },
            { 'subject._id': 1, _program_id: 1, _id: 1 },
        );
        const programId = dept._program_id;

        const doesSubjectExist = await departmentModel.find({
            _program_id: programId,
            isActive: true,
            isDeleted: false,
            subject: {
                $elemMatch: {
                    _id: { $ne: convertToMongoObjectId(subjectId) },
                    subjectName: new RegExp('^' + subjectName + '$', 'i'),
                },
            },
        });

        if (doesSubjectExist && doesSubjectExist.length)
            return { statusCode: 400, message: 'NAME_EXISTS' };

        const department = await departmentModel.findOneAndUpdate(
            {
                _id: departmentId,
                subject: { $elemMatch: { _id: subjectId } },
            },
            { $set: { 'subject.$.subjectName': subjectName } },
            { new: true, select: { departmentName: 1, subject: 1, sharedWith: 1, isActive: 1 } },
        );

        const updateAllocation = await userModel.updateMany(
            {
                'academic_allocation._department_id': convertToMongoObjectId(departmentId),
                'academic_allocation.departmentSubject._department_subject_id':
                    convertToMongoObjectId(subjectId),
            },
            {
                'academic_allocation.$[i].departmentSubject.$[j].subjectName': subjectName,
            },
            {
                arrayFilters: [
                    { 'i._department_id': convertToMongoObjectId(departmentId) },
                    { 'j._department_subject_id': convertToMongoObjectId(subjectId) },
                ],
            },
        );

        if (!department) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        // start updating department subject table
        const DepartmentSubjects = await departmentModel.find({ 'subject._id': subjectId }, {});
        const bulkUpdate = [];
        const bulkUpdateForSessionOrder = [];
        for (DepartmentSubjectList of DepartmentSubjects) {
            if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                const subjectLists = DepartmentSubjectList.subject.filter(function (element) {
                    return element._id.toString() === subjectId.toString();
                });
                for (subjects of subjectLists) {
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                _id: convertToMongoObjectId(DepartmentSubjectList._id),
                            },
                            update: {
                                $set: {
                                    'subject.$[subjectId].subjectName': subjectName,
                                },
                            },
                            arrayFilters: [
                                {
                                    'subjectId._id': convertToMongoObjectId(subjects._id),
                                },
                            ],
                        },
                    });
                    bulkUpdateForSessionOrder.push({
                        updateMany: {
                            filter: {
                                isDeleted: false,
                                'subjects._subject_id': convertToMongoObjectId(subjects._id),
                            },
                            update: {
                                $set: {
                                    'subjects.$[subjectId].subjectName': subjectName,
                                },
                            },
                            arrayFilters: [
                                {
                                    'subjectId._subject_id': convertToMongoObjectId(subjects._id),
                                },
                            ],
                        },
                    });
                }
            }
        }

        await departmentModel.bulkWrite(bulkUpdate);
        await sessionOrderModel.bulkWrite(bulkUpdateForSessionOrder);
        const courseDoc = await courseModel.updateMany(
            {
                'participating._subject_id': convertToMongoObjectId(subjectId),
                isDeleted: false,
            },
            { $set: { 'participating.$[sub].subjectName': subjectName } },
            {
                arrayFilters: [
                    {
                        'sub._subject_id': convertToMongoObjectId(subjectId),
                    },
                ],
            },
        );
        const courseDocFroAdmin = await courseModel.updateMany(
            {
                'administration._subject_id': convertToMongoObjectId(subjectId),
                isDeleted: false,
            },
            { $set: { 'administration.subjectName': subjectName } },
        );
        // end updating department subject table

        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteSubject = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { departmentId, subjectId } = body;
        const courseDoc = await courseModel.find({
            $or: [
                { 'participating._subject_id': convertToMongoObjectId(subjectId) },
                { 'administration._subject_id': convertToMongoObjectId(subjectId) },
            ],
            isDeleted: false,
        });
        if (courseDoc.length > 0) {
            return { statusCode: 500, message: 'SUBJECT_IS_ASSIGNED_TO_COURSE' };
        }
        const departmentSubject = await departmentSubjectModel.findOneAndUpdate(
            {
                _id: departmentId,
                'subject._id': subjectId,
            },
            {
                $set: {
                    'subject.$.isDeleted': true,
                    'subject.$.sharedWith': [],
                },
            },
            { new: true },
        );
        if (!departmentSubject) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDashboardStats = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        const searchQuery = {
            _institution_id: convertToMongoObjectId(id),
            isActive: true,
            isDeleted: false,
        };
        const programs = await programModel.find(
            {
                _institution_id: convertToMongoObjectId(id),
                isActive: true,
                isDeleted: false,
            },
            { _id: 1, name: 1 },
        );
        const departments = await departmentSubjectModel.find(searchQuery).lean();
        const subjects = [];
        departments.forEach((department) => {
            subjects.push(
                ...department.subject.map((subject) => ({ ...subject, type: department.type })),
            );
        });
        const { subjectCount, academicCount, adminCount, programDistribution } =
            getDepartmentStats(departments);
        return {
            statusCode: 200,
            data: {
                departmentCount: departments.length,
                subjectCount,
                academicCount,
                adminCount,
                programDistribution,
                allPrograms: programs,
                allDepartments: departments,
                allSubjects: subjects,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseDepartmentList = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { _program_id, _institution_id } = params;
        const institution = await institutionModel.findById(_institution_id).lean();
        if (!institution) return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        const departmentSubjectQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const departmentSubjectLists = await departmentSubjectModel.find(
            departmentSubjectQuery,
            {},
        );
        if (!departmentSubjectLists) return { statusCode: 400, message: 'DEPARTMENT_NOT_FOUND' };
        let departmentSubjectList = [];
        const sharedSubject = [];
        let departmentSubjects = {};

        for (const departmentSubject of departmentSubjectLists) {
            if (
                departmentSubject._program_id &&
                departmentSubject._program_id.toString() === _program_id.toString()
            ) {
                const subjectList = departmentSubject.subject.filter(
                    (entry) => entry.isDeleted === false && entry.isActive === true,
                );
                departmentSubjects = {
                    _id: departmentSubject._id,
                    isActive: departmentSubject.isActive,
                    isDeleted: departmentSubject.isDeleted,
                    _program_id: departmentSubject._program_id,
                    programName: departmentSubject.programName,
                    departmentName: departmentSubject.departmentName,
                    subject: subjectList,
                    sharedWith: departmentSubject.sharedWith,
                    createdAt: departmentSubject.createdAt,
                    updatedAt: departmentSubject.updatedAt,
                };
                departmentSubjectList.push(departmentSubjects);
                sharedSubject.push(departmentSubjects);
            } else {
                if (
                    departmentSubject.sharedWith.findIndex(
                        (entry) => entry._program_id.toString() === _program_id.toString(),
                    ) !== -1
                ) {
                    const subjectList = departmentSubject.subject.filter(
                        (entry) => entry.isDeleted === false,
                    );
                    const program = await programModel
                        .findById(departmentSubject._program_id, { isActive: 1 })
                        .lean();
                    if (program && program.isActive === true) {
                        departmentSubjects = {
                            _id: departmentSubject._id,
                            isActive: departmentSubject.isActive,
                            isDeleted: departmentSubject.isDeleted,
                            _program_id: departmentSubject._program_id,
                            programName: departmentSubject.programName,
                            departmentName: departmentSubject.departmentName,
                            subject: subjectList,
                            sharedWith: departmentSubject.sharedWith,
                            createdAt: departmentSubject.createdAt,
                            updatedAt: departmentSubject.updatedAt,
                        };
                        departmentSubjectList.push(departmentSubjects);
                        sharedSubject.push(departmentSubjects);
                    }
                    if (departmentSubject.type === ADMIN) {
                        departmentSubjects = {
                            _id: departmentSubject._id,
                            isActive: departmentSubject.isActive,
                            isDeleted: departmentSubject.isDeleted,
                            departmentName: departmentSubject.departmentName,
                            subject: subjectList,
                            sharedWith: departmentSubject.sharedWith,
                            createdAt: departmentSubject.createdAt,
                            updatedAt: departmentSubject.updatedAt,
                        };
                        departmentSubjectList.push(departmentSubjects);
                        sharedSubject.push(departmentSubjects);
                    }
                } else {
                    const subjectList = departmentSubject.subject.filter(
                        (entry) => entry.isDeleted === false,
                    );
                    const sharedSubjectList = [];
                    for (subjectElement of subjectList) {
                        if (
                            subjectElement.sharedWith.findIndex(
                                (entry) => entry._program_id.toString() === _program_id.toString(),
                            ) !== -1
                        ) {
                            if (departmentSubject._program_id) {
                                const program = await programModel
                                    .findById(departmentSubject._program_id, {
                                        isActive: 1,
                                    })
                                    .lean();
                                if (program && program.isActive === true) {
                                    sharedSubjectList.push(subjectElement);
                                }
                            }
                            if (departmentSubject.type === ADMIN) {
                                sharedSubjectList.push(subjectElement);
                            }
                        }
                    }
                    if (sharedSubjectList.length !== 0) {
                        for (subjectElement of sharedSubjectList) {
                            for (shareSubjectElement of subjectElement.sharedWith) {
                                if (
                                    shareSubjectElement._program_id.toString() ===
                                    _program_id.toString()
                                ) {
                                    if (departmentSubject._program_id) {
                                        const program = await programModel
                                            .findById(departmentSubject._program_id, {
                                                isActive: 1,
                                            })
                                            .lean();
                                        if (program && program.isActive === true) {
                                            departmentSubjects = {
                                                _id: shareSubjectElement._department_id,
                                                isActive: subjectElement.isActive,
                                                isDeleted: subjectElement.isDeleted,
                                                _program_id: shareSubjectElement._program_id,
                                                programName: shareSubjectElement.programName,
                                                departmentName: shareSubjectElement.departmentName,
                                                subject: [subjectElement],
                                                shareWith: subjectElement.sharedWith,
                                                createdAt: subjectElement.createdAt,
                                                updatedAt: subjectElement.updatedAt,
                                            };
                                            departmentSubjectList.push(departmentSubjects);
                                        }
                                    } else {
                                        departmentSubjects = {
                                            _id: shareSubjectElement._department_id,
                                            isActive: subjectElement.isActive,
                                            isDeleted: subjectElement.isDeleted,
                                            departmentName: shareSubjectElement.departmentName,
                                            subject: [subjectElement],
                                            shareWith: subjectElement.sharedWith,
                                            createdAt: subjectElement.createdAt,
                                            updatedAt: subjectElement.updatedAt,
                                        };
                                        departmentSubjectList.push(departmentSubjects);
                                    }
                                }
                            }
                        }
                        departmentSubjects = {
                            _id: departmentSubject._id,
                            isActive: departmentSubject.isActive,
                            isDeleted: departmentSubject.isDeleted,
                            _program_id: departmentSubject._program_id,
                            programName: departmentSubject.programName,
                            departmentName: departmentSubject.departmentName,
                            subject: sharedSubjectList,
                            shareWith: departmentSubject.sharedWith,
                            createdAt: departmentSubject.createdAt,
                            updatedAt: departmentSubject.updatedAt,
                        };
                        sharedSubject.push(departmentSubjects);
                    }
                }
            }
        }
        departmentSubjectList = [...new Set(departmentSubjectList)];

        return {
            statusCode: 200,
            data: {
                participationSubject: sharedSubject,
                adminDepartment: departmentSubjectList,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deliveringSubjectList = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const departments = await departmentSubjectModel
            .find(
                {
                    _institution_id: id,
                    isActive: true,
                    isDeleted: false,
                    'subject.isActive': true,
                    'subject.isDeleted': false,
                },
                {
                    programName: 1,
                    departmentName: 1,
                    'subject.subjectName': 1,
                    'subject._id': 1,
                    _program_id: 1,
                },
            )
            .lean();
        const program = await programModel
            .find(
                { _institution_id: convertToMongoObjectId(id), isActive: true, isDeleted: false },
                { isActive: 1 },
            )
            .lean();
        const subjects = [];
        departments.map((departmentEntry) => {
            departmentEntry.subject.map((subjectEntry) => {
                if (departmentEntry._program_id) {
                    const programData = program.filter(
                        (entry) => entry._id.toString() === departmentEntry._program_id.toString(),
                    );
                    if (programData.length && programData[0].isActive === true) {
                        subjects.push({
                            _id: subjectEntry._id,
                            subjectName: subjectEntry.subjectName,
                            _department_id: departmentEntry._id,
                            departmentName: departmentEntry.departmentName,
                            _program_id: departmentEntry._program_id,
                            programName: departmentEntry.programName,
                            adminstrating:
                                subjectEntry.subjectName +
                                ' / ' +
                                departmentEntry.departmentName +
                                ' / ' +
                                departmentEntry.programName,
                        });
                    }
                } else {
                    subjects.push({
                        _id: subjectEntry._id,
                        subjectName: subjectEntry.subjectName,
                        _department_id: departmentEntry._id,
                        departmentName: departmentEntry.departmentName,
                        _program_id: departmentEntry._program_id,
                        programName: departmentEntry.programName,
                        adminstrating:
                            subjectEntry.subjectName +
                            ' / ' +
                            departmentEntry.departmentName +
                            ' / ' +
                            'admin',
                    });
                }
            });
        });
        return { statusCode: 200, data: { subjects } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    addDepartment,
    getDepartment,
    getDepartments,
    addSubject,
    editDepartment,
    shareDepartment,
    shareSubjects,
    deleteDepartment,
    editSubject,
    deleteSubject,
    getDashboardStats,
    getProgramWiseDepts,
    courseDepartmentList,
    deliveringSubjectList,
    getInstituteDepartments,
    editDepartmentRestructure,
};
