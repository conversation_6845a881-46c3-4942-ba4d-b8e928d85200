const { convertToMongoObjectId, query } = require('../../../utility/common');
const { LOCAL, DIGI_CURRICULUM } = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
// const {} = require('../../serviceAdapter/adapter.formatter');
const curriculumSchema = require('mongoose').model(DIGI_CURRICULUM);

const getProgramCurriculums = async ({ _institution_id, programId }) => {
    try {
        let programCurriculum;
        if (BASIC_DATA_FROM === LOCAL) {
            programCurriculum = await curriculumSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                    },
                    {
                        curriculum_name: 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!programCurriculum) return [];
        return programCurriculum;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = { getProgramCurriculums };
