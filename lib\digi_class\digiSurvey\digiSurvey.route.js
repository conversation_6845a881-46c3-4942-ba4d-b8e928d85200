const express = require('express');
const router = express.Router();
const catchAsync = require('../../utility/catch-async');
const { middlewareValidator } = require('../../utility/utility.service');
const {
    getSurveyList,
    getStudentSurveyList,
    surveyAdd,
    surveyAddSectionQuestion,
    surveyStudentSubmission,
    surveyStudentAutoSubmission,
    surveyReportSummaryHeader,
    surveyReportSection,
    surveyReportStatisticalAnalysis,
    surveyReportQuestion,
    surveyReportStudentResponse,
    surveyReportIndividualStudentResponse,
    surveyReportSentimentAnalysisSection,
    surveyReportSentimentAnalysisQuestion,
    surveyReportStudentFeeling,
    getPublishedSurveyCalendarList,
    getSinglePublishedSurveyDetails,
    updatedPublishedSurvey,
    getTotalPublishedSurveyCount,
    getPublishedSurveyHeaderFilters,
    getPublishedSurveyUserList,
    getTemplateRunnerUserList,
    getTotalPublishedSurveyUserList,
    sentimentAnalysisOpenEndedQuestions,
    createUserOutcomeReportResponseSetting,
    getUserOutcomeReportResponseSetting,
    getSurveyOutcomeReportThemes,
    getThemeBasedReports,
    extendSurveyDuration,
} = require('./digiSurvey.controller');
const {
    getPublishedSurveyCalendarListValidator,
    getSurveyListValidator,
    getSinglePublishedSurveyDetailValidator,
    updatedPublishedSurveyValidator,
    getTotalPublishedSurveyCountValidator,
    getTemplateRunnerUserValidator,
    getTotalPublishedSurveyUserValidator,
    createUserOutcomeReportResponseSettingValidator,
    getUserOutcomeReportResponseSettingValidator,
    extendSurveyDurationValidator,
} = require('./digiSurvey.validator');

//published survey list
router.get('/getSurveyList', getSurveyListValidator, catchAsync(getSurveyList));
router.get('/getStudentSurveyList', getSurveyListValidator, catchAsync(getStudentSurveyList));
router.get(
    '/getPublishedSurveyCalendarList',
    getPublishedSurveyCalendarListValidator,
    catchAsync(getPublishedSurveyCalendarList),
);
router.get(
    '/getSinglePublishedSurveyDetails',
    getSinglePublishedSurveyDetailValidator,
    catchAsync(getSinglePublishedSurveyDetails),
);
router.put(
    '/updatedPublishedSurvey',
    updatedPublishedSurveyValidator,
    catchAsync(updatedPublishedSurvey),
);
router.get(
    '/getTotalPublishedSurveyCount',
    getTotalPublishedSurveyCountValidator,
    catchAsync(getTotalPublishedSurveyCount),
);
router.get(
    '/getPublishedSurveyHeaderFilters',
    getSurveyListValidator,
    catchAsync(getPublishedSurveyHeaderFilters),
);
//For runner - total user list in view details
router.get(
    '/getPublishedSurveyUserList',
    getSinglePublishedSurveyDetailValidator,
    catchAsync(getPublishedSurveyUserList),
);

//For creator - If he view the user list fist list template runner list and total attended user list
router.get(
    '/getTemplateRunnerUserList',
    getTemplateRunnerUserValidator,
    catchAsync(getTemplateRunnerUserList),
);
router.get(
    '/getTotalPublishedSurveyUserList',
    getTotalPublishedSurveyUserValidator,
    catchAsync(getTotalPublishedSurveyUserList),
);
router.get(
    '/extendSurveyDuration',
    middlewareValidator(extendSurveyDurationValidator),
    catchAsync(extendSurveyDuration),
);

router.post('/surveyCreate', catchAsync(surveyAdd));
router.post('/surveyAddSectionQuestion', catchAsync(surveyAddSectionQuestion));
router.post('/surveyStudentSubmission', catchAsync(surveyStudentSubmission));
router.post('/surveyStudentAutoSubmission', catchAsync(surveyStudentAutoSubmission));

// Survey Report
router.get('/surveyReportSummaryHeader', catchAsync(surveyReportSummaryHeader));
router.get('/surveyReportSection', catchAsync(surveyReportSection));
router.get('/surveyReportStatisticalAnalysis', catchAsync(surveyReportStatisticalAnalysis));
router.get('/surveyReportQuestion', catchAsync(surveyReportQuestion));
router.get('/surveyReportStudentResponse', catchAsync(surveyReportStudentResponse));
router.get(
    '/surveyReportIndividualStudentResponse',
    catchAsync(surveyReportIndividualStudentResponse),
);
router.get(
    '/surveyReportSentimentAnalysisSection',
    catchAsync(surveyReportSentimentAnalysisSection),
);
router.get('/sentimentAnalysisOpenEndedQuestions', catchAsync(sentimentAnalysisOpenEndedQuestions));
router.get(
    '/surveyReportSentimentAnalysisQuestion',
    catchAsync(surveyReportSentimentAnalysisQuestion),
);

// AI Service
router.get('/surveyReportStudentFeeling', catchAsync(surveyReportStudentFeeling));

//outcome report max response setting api's
router.put(
    '/createUserOutcomeReportResponseSetting',
    createUserOutcomeReportResponseSettingValidator,
    catchAsync(createUserOutcomeReportResponseSetting),
);
router.get(
    '/getUserOutcomeReportResponseSetting',
    getUserOutcomeReportResponseSettingValidator,
    catchAsync(getUserOutcomeReportResponseSetting),
);
router.get('/getSurveyOutcomeReportThemes', catchAsync(getSurveyOutcomeReportThemes));
router.get('/getThemeBasedReports', catchAsync(getThemeBasedReports));

module.exports = router;
