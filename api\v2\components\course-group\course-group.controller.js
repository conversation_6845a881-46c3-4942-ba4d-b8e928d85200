const courseGroupSchema = require('./course-group.model');
const institutionSchema = require('../institution/institution.model');
const courseSchema = require('../course/course.model');
const curriculumSchema = require('../curriculum/curriculum.model');
const {
    DS_GET_FAILED,
    COURSE,
    DS_GROUPED,
    DS_UNGROUPED,
    DS_GROUP_FAILED,
    DS_UNGROUP_FAILED,
    ASSIGNED,
    DRAFT,
    INSTITUTION,
    DS_COURSE_GROUP,
    DS_UPDATED,
    DS_UPDATE_FAILED,
    CURRICULUM,
} = require('../../utility/constants');
const {
    getGroupedCourses,
    populateCourseDetails,
    courseSharedWithDetail,
} = require('./course-group.util');
const { convertToMongoObjectId, getModel } = require('../../utility/common');

const groupCourses = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const {
            _institution_id,
            _curriculum_id,
            _program_id,
            _year_id,
            _level_id,
            _course_ids,
            groupName,
            isPhaseFlowWithOutLevel,
        } = body;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseGroupModel = getModel(tenantURL, DS_COURSE_GROUP, courseGroupSchema);
        const institutionExists = await institutionModel.findById(_institution_id);
        if (!institutionExists) {
            return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        }
        const createQuery = {
            _institution_id,
            _curriculum_id,
            _program_id,
            _year_id,
            _level_id,
            _course_ids,
            groupName,
            isPhaseFlowWithOutLevel,
        };
        const groupedCourses = await courseGroupModel.create(createQuery);
        if (!groupedCourses) return { statusCode: 500, message: DS_UPDATE_FAILED };
        return { statusCode: 201, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listGroups = async ({ headers = {}, query = {}, params = {} }) => {
    try {
        const { id } = params;
        const { tenantURL } = headers;
        const { programId, curriculumId, yearId, levelId } = query;
        const courseGroupModel = getModel(tenantURL, DS_COURSE_GROUP, courseGroupSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const dbQuery = {
            _institution_id: convertToMongoObjectId(id),
            _program_id: convertToMongoObjectId(programId),
            _curriculum_id: convertToMongoObjectId(curriculumId),
            _year_id: convertToMongoObjectId(yearId),
        };
        if (levelId) {
            dbQuery._level_id = convertToMongoObjectId(levelId);
        }
        let groups = await courseGroupModel.find(dbQuery).sort({ _id: 1 });
        if (!groups.length) {
            const dbQuery = {
                _institution_id: convertToMongoObjectId(id),
                _year_id: convertToMongoObjectId(yearId),
            };
            if (levelId) {
                dbQuery._level_id = convertToMongoObjectId(levelId);
            }
            groups = await courseGroupModel.find(dbQuery).sort({ _id: 1 });
            console.log(groups);
            if (!groups) return { statusCode: 500, message: DS_GET_FAILED };
        }
        const groupedCourseIds = getGroupedCourses(groups);
        const groupedCourses = await courseModel
            .find(
                {
                    _institution_id: id,
                    isActive: true,
                    isDeleted: false,
                    _id: { $in: groupedCourseIds },
                },
                {
                    startWeek: 1,
                    endWeek: 1,
                    courseName: 1,
                    courseCode: 1,
                    courseType: 1,
                    isActive: 1,
                    isPhaseFlowWithOutLevel: 1,
                    'courseAssignedDetails.programName': 1,
                    'courseAssignedDetails._program_id': 1,
                    'courseAssignedDetails.curriculumName': 1,
                    'courseAssignedDetails._curriculum_id': 1,
                    'courseAssignedDetails.year': 1,
                    'courseAssignedDetails._year_id': 1,
                    'courseAssignedDetails.levelNo': 1,
                    'courseAssignedDetails._level_id': 1,
                    'courseAssignedDetails.isActive': 1,
                    'courseAssignedDetails.courseDuration': 1,
                    'courseAssignedDetails.courseSharedWith': 1,
                    'sessionDeliveryType.creditHours': 1,
                    'sessionDeliveryType.typeName': 1,
                },
            )
            .lean();
        if (groups.length && groups[0].isPhaseFlowWithOutLevel === true) {
            const ungroupedCourses = await courseModel
                .find(
                    {
                        _institution_id: id,
                        _id: { $nin: groupedCourseIds },
                        $or: [
                            {
                                $or: [
                                    {
                                        'courseOccurringYearWise._year_id':
                                            convertToMongoObjectId(yearId),
                                    },
                                    {
                                        'courseRecurringYearWise._year_id':
                                            convertToMongoObjectId(yearId),
                                    },
                                ],
                            },
                            {
                                'courseAssignedDetails._year_id': convertToMongoObjectId(yearId),
                            },
                            {
                                'courseAssignedDetails.courseSharedWith._year_id':
                                    convertToMongoObjectId(yearId),
                            },
                        ],
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        startWeek: 1,
                        endWeek: 1,
                        courseName: 1,
                        courseCode: 1,
                        courseType: 1,
                        isActive: 1,
                        'courseAssignedDetails.programName': 1,
                        'courseAssignedDetails._program_id': 1,
                        'courseAssignedDetails.curriculumName': 1,
                        'courseAssignedDetails._curriculum_id': 1,
                        'courseAssignedDetails.year': 1,
                        'courseAssignedDetails._year_id': 1,
                        'courseAssignedDetails.levelNo': 1,
                        'courseAssignedDetails._level_id': 1,
                        'courseAssignedDetails.isActive': 1,
                        'courseAssignedDetails.courseDuration': 1,
                        'courseAssignedDetails.courseSharedWith': 1,
                        'sessionDeliveryType.creditHours': 1,
                        'sessionDeliveryType.typeName': 1,
                    },
                )
                .lean();
            if (ungroupedCourses.length) {
                ungroupedCourses.forEach((course, index) => {
                    if (
                        (course._program_id && course._program_id.toString() !== programId) ||
                        (course._curriculum_id && course._curriculum_id.toString() !== curriculumId)
                    ) {
                        ungroupedCourses[index].isPreRequisite = true;
                    }
                    const checkCourse = course.courseAssignedDetails.find((assignElement) => {
                        if (assignElement._program_id && assignElement._curriculum_id) {
                            return (
                                assignElement._program_id.toString() !== programId ||
                                assignElement._curriculum_id.toString() !== curriculumId
                            );
                        }
                    });
                    if (checkCourse) {
                        ungroupedCourses[index].isPreRequisite = true;
                    }
                    const checkShareCourse = course.courseAssignedDetails.find((assignElement) => {
                        assignElement.courseSharedWith.find((shareElement) => {
                            if (shareElement._program_id && shareElement._curriculum_id) {
                                return (
                                    shareElement._program_id.toString() !== programId ||
                                    shareElement._curriculum_id.toString() !== curriculumId
                                );
                            }
                        });
                    });
                    if (checkShareCourse) {
                        ungroupedCourses[index].isPreRequisite = true;
                    }
                    if (!course._program_id) {
                        ungroupedCourses[index].isPreRequisite = true;
                    }
                });
            }
            const isPhaseFlowWithOutLevel = true;
            groups = populateCourseDetails(
                groups,
                groupedCourses,
                programId,
                curriculumId,
                yearId,
                isPhaseFlowWithOutLevel,
            );
            return {
                statusCode: 200,
                data: {
                    ungroupedCourses: courseSharedWithDetail({
                        courseDatas: ungroupedCourses,
                        programId,
                        curriculumId,
                        yearId,
                        isPhaseFlowWithOutLevel,
                    }),
                    groups,
                },
            };
        }
        const ungroupedCourses = await courseModel
            .find(
                {
                    _institution_id: id,
                    _id: { $nin: groupedCourseIds },
                    $or: [
                        {
                            $or: [
                                {
                                    'courseOccurring._level_id': convertToMongoObjectId(levelId),
                                },
                                {
                                    'courseRecurring._level_id': convertToMongoObjectId(levelId),
                                },
                            ],
                        },
                        {
                            'courseAssignedDetails._level_id': convertToMongoObjectId(levelId),
                            'courseAssignedDetails._year_id': convertToMongoObjectId(yearId),
                        },
                        {
                            'courseAssignedDetails.courseSharedWith._level_id':
                                convertToMongoObjectId(levelId),
                            'courseAssignedDetails.courseSharedWith._year_id':
                                convertToMongoObjectId(yearId),
                        },
                    ],
                    isActive: true,
                    isDeleted: false,
                },
                {
                    startWeek: 1,
                    endWeek: 1,
                    courseName: 1,
                    courseCode: 1,
                    courseType: 1,
                    isActive: 1,
                    'courseAssignedDetails.programName': 1,
                    'courseAssignedDetails._program_id': 1,
                    'courseAssignedDetails.curriculumName': 1,
                    'courseAssignedDetails._curriculum_id': 1,
                    'courseAssignedDetails.year': 1,
                    'courseAssignedDetails._year_id': 1,
                    'courseAssignedDetails.levelNo': 1,
                    'courseAssignedDetails._level_id': 1,
                    'courseAssignedDetails.isActive': 1,
                    'courseAssignedDetails.courseDuration': 1,
                    'courseAssignedDetails.courseSharedWith': 1,
                    'sessionDeliveryType.creditHours': 1,
                    'sessionDeliveryType.typeName': 1,
                },
            )
            .lean();
        if (ungroupedCourses.length) {
            ungroupedCourses.forEach((course, index) => {
                if (
                    (course._program_id && course._program_id.toString() !== programId) ||
                    (course._curriculum_id && course._curriculum_id.toString() !== curriculumId)
                ) {
                    ungroupedCourses[index].isPreRequisite = true;
                }
                const checkCourse = course.courseAssignedDetails.find((assignElement) => {
                    if (assignElement._program_id && assignElement._curriculum_id) {
                        return (
                            assignElement._program_id.toString() !== programId ||
                            assignElement._curriculum_id.toString() !== curriculumId
                        );
                    }
                });
                if (checkCourse) {
                    ungroupedCourses[index].isPreRequisite = true;
                }
                const checkShareCourse = course.courseAssignedDetails.find((assignElement) => {
                    assignElement.courseSharedWith.find((shareElement) => {
                        if (shareElement._program_id && shareElement._curriculum_id) {
                            return (
                                shareElement._program_id.toString() !== programId ||
                                shareElement._curriculum_id.toString() !== curriculumId
                            );
                        }
                    });
                });
                if (checkShareCourse) {
                    ungroupedCourses[index].isPreRequisite = true;
                }
                if (!course._program_id) {
                    ungroupedCourses[index].isPreRequisite = true;
                }
            });
        }
        groups = populateCourseDetails(
            groups,
            groupedCourses,
            programId,
            curriculumId,
            yearId,
            levelId,
        );
        return {
            statusCode: 200,
            data: {
                ungroupedCourses: courseSharedWithDetail({
                    courseDatas: ungroupedCourses,
                    programId,
                    curriculumId,
                    yearId,
                    levelId,
                }),
                groups,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const unGroupOrEditCourses = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { _institution_id, groupId, _course_ids } = body;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseGroupModel = getModel(tenantURL, DS_COURSE_GROUP, courseGroupSchema);
        const institutionExists = await institutionModel.findById(_institution_id);
        if (!institutionExists) {
            return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        }
        if (!_course_ids.length) {
            await courseGroupModel.findByIdAndDelete(groupId);
            return { statusCode: 200, message: DS_UPDATED };
        }
        await courseGroupModel.findByIdAndUpdate(groupId, { $set: { _course_ids } });
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const renameGroupName = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { _institution_id, groupId, groupName } = body;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseGroupModel = getModel(tenantURL, DS_COURSE_GROUP, courseGroupSchema);
        const institutionExists = await institutionModel.findById(_institution_id);
        if (!institutionExists) {
            return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        }
        await courseGroupModel.findByIdAndUpdate(groupId, { $set: { groupName } });
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = { groupCourses, listGroups, unGroupOrEditCourses, renameGroupName };
