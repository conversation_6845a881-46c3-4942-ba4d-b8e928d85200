const RoleService = require('./role.service');

const createRole = async ({ body: { name, type, isDefault } = {} }) => {
    const result = await RoleService.createRole({ name, type, isDefault });

    return { statusCode: 200, message: 'ROLE_CREATED_SUCCESSFULLY', data: result };
};

const getRoles = async () => {
    const result = await RoleService.getRoles();

    return { statusCode: 200, message: 'ROLES_FETCHED_SUCCESSFULLY', data: result };
};

const updateRole = async ({ body: { name, type, isDefault } = {}, query: { roleId } = {} }) => {
    const result = await RoleService.updateRole({ roleId, name, type, isDefault });

    return { statusCode: 200, message: 'ROLE_UPDATED_SUCCESSFULLY', data: result };
};

const deleteRole = async ({ query: { roleId } = {} }) => {
    const result = await RoleService.deleteRole({ roleId });

    return { statusCode: 200, message: 'ROLE_DELETED_SUCCESSFULLY' };
};

module.exports = {
    createRole,
    getRoles,
    updateRole,
    deleteRole,
};
