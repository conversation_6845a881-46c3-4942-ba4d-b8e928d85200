const express = require('express');
const route = express.Router();
const digi_institute = require('../digi_institute/digi_institute_controller');
const validator = require('./digi_institute_validator');

route.get('/archived_list', digi_institute.archived_list);
route.get('/:id', digi_institute.list_id);
route.get('/', digi_institute.list);

route.post('/', validator.institute_add, digi_institute.insert);

route.put('/archive/:id', validator.digi_institute_id, digi_institute.archive);
route.put('/:id', validator.digi_institute_id, validator.institute_add, digi_institute.update);

route.delete('/:id', validator.digi_institute_id, digi_institute.delete);

module.exports = route;