const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {
    STUDENT_SESSION_SURVEY,
    DIGI_COURSE,
    DIGI_PROGRAM,
    USER,
    INSTITUTION_CALENDAR,
} = require('../utility/constants');

const studentSessionSurvey = new Schema(
    {
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: USER,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        yearNo: {
            type: String,
            required: true,
        },
        levelNo: {
            type: String,
            required: true,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotationCount: {
            type: Number,
        },
        _session_order_id: {
            type: Schema.Types.ObjectId,
            required: true,
        },
        ratingValue: {
            type: Number,
            default: 5,
        },
        slos: [
            {
                _slo_id: {
                    type: Schema.Types.ObjectId,
                    required: true,
                },
                sloRating: {
                    type: Number,
                    default: 0,
                },
            },
        ],
        feedBack: {
            type: String,
            trim: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(STUDENT_SESSION_SURVEY, studentSessionSurvey);
