const { redisClient } = require('../../config/redis-connection');
const {
    SCHEDULE_TYPES: { REGULAR },
    MISSED,
    COMPLETED,
    SCHEDULE_STATUS: { CANCELLED, MISSED_TO_COMPLETE, NOT_SCHEDULED },
} = require('../utility/constants');
const programCalendarModel = require('../models/program_calendar');
const courseScheduleModel = require('../models/course_schedule');
const sessionOrderModel = require('../models/digi_session_order');
const sessionDeliveryTypeModel = require('../models/digi_session_delivery_types');
const studentGroupModel = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');

exports.getLevelsFromRedis = async ({ programId, _institution_calendar_id }) => {
    const key = `course_list:${programId}-${_institution_calendar_id}`;
    const levels = await redisClient.Client.get(key);
    if (!(levels && JSON.parse(levels).length)) {
        const programLevels = await programCalendarModel
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    status: 'published',
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            )
            .lean();
        if (!(programLevels && programLevels.level)) {
            return null;
        }
        await redisClient.Client.set(key, JSON.stringify(programLevels.level));
        return programLevels.level;
    }
    return JSON.parse(levels);
};

exports.courseTopicNotCoveredList = async ({
    institutionCalendarId,
    programId,
    courseId,
    year,
    level,
    term,
    rotationNo,
    groupIds,
}) => {
    const courseSessionOrderData = await sessionOrderModel
        .findOne(
            { isDeleted: false, isActive: true, _course_id: convertToMongoObjectId(courseId) },
            {
                _id: 0,
                'session_flow_data._id': 1,
                'session_flow_data._session_id': 1,
                'session_flow_data.s_no': 1,
                'session_flow_data.delivery_type': 1,
                'session_flow_data.delivery_symbol': 1,
                'session_flow_data.delivery_no': 1,
                'session_flow_data.delivery_topic': 1,
            },
        )
        .sort({ _id: -1 })
        .lean();
    const courseSessionType = await sessionDeliveryTypeModel
        .find(
            {
                _id: [
                    ...new Set(
                        courseSessionOrderData.session_flow_data.map((sessionFlowElement) =>
                            String(sessionFlowElement._session_id),
                        ),
                    ),
                ],
            },
            {
                session_name: 1,
            },
        )
        .lean();
    const studentGroupData = await studentGroupModel
        .findOne(
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.level': level,
                'groups.term': term,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.term': 1,
                'groups.level': 1,
                'groups.courses._course_id': 1,
                'groups.courses.setting._group_no': 1,
                'groups.courses.setting.session_setting.session_type': 1,
                'groups.courses.setting.session_setting.groups._id': 1,
            },
        )
        .lean();
    if (!studentGroupData) return [];
    const levelGroup = studentGroupData.groups.find(
        (levelGroupElement) =>
            levelGroupElement.term.toLowerCase() === term.toLowerCase() &&
            levelGroupElement.level === level,
    );
    if (!levelGroup) return [];
    const courseGroup = levelGroup?.courses.find(
        (courseElement) => String(courseElement._course_id) === courseId,
    );
    if (!courseGroup) return [];
    const courseBasedDeliveryGroups = [];
    courseGroup.setting.forEach((settingElement) => {
        if (rotationNo ? parseInt(settingElement._group_no) === parseInt(rotationNo) : true) {
            settingElement.session_setting.forEach((sessionSettingElement) => {
                const existingSession = courseBasedDeliveryGroups.findIndex(
                    (groupDeliverElement) =>
                        groupDeliverElement.deliverySymbol === sessionSettingElement.session_type,
                );
                const filteredGroupIds = sessionSettingElement.groups
                    .map((groupElement) => String(groupElement._id))
                    .filter(
                        (groupId) => !groupIds || !groupIds.length || groupIds.includes(groupId),
                    );
                if (existingSession !== -1) {
                    courseBasedDeliveryGroups[existingSession].groupId.push(...filteredGroupIds);
                } else if (filteredGroupIds.length) {
                    courseBasedDeliveryGroups.push({
                        deliverySymbol: sessionSettingElement.session_type,
                        groupId: filteredGroupIds,
                    });
                }
            });
        }
    });
    const courseScheduleData = await courseScheduleModel
        .find(
            {
                isDeleted: false,
                type: REGULAR,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: level,
                term,
                ...(rotationNo && { rotation_count: rotationNo }),
                // ...(groupIds &&
                //     groupIds.length && {
                //         'student_groups.session_group.session_group_id': {
                //             $in: groupIds.map((groupIdElement) =>
                //                 convertToMongoObjectId(groupIdElement),
                //             ),
                //         },
                //     }),
            },
            {
                status: 1,
                isActive: 1,
                'session._session_id': 1,
                'student_groups.session_group.session_group_id': 1,
                isMissedToComplete: 1,
            },
        )
        .lean();
    const notConductedTopic = [];
    courseSessionOrderData.session_flow_data.forEach((sessionFlowElement) => {
        const sessionTypeName = courseSessionType.find(
            (courseSessionTypeElement) =>
                String(courseSessionTypeElement._id) === String(sessionFlowElement._session_id),
        );
        const sessionOrderData = {
            _id: sessionFlowElement._id,
            sNo: sessionFlowElement.s_no,
            sessionType: sessionTypeName?.session_name || '',
            deliverySymbol: sessionFlowElement.delivery_symbol,
            deliveryNo: sessionFlowElement.delivery_no,
            deliveryType: sessionFlowElement.delivery_type,
            deliveryTopic: sessionFlowElement.delivery_topic,
            status: NOT_SCHEDULED,
        };
        const deliveryGroups = courseBasedDeliveryGroups.find(
            (groupDeliveryElement) =>
                groupDeliveryElement.deliverySymbol === sessionFlowElement.delivery_symbol,
        );
        const sessionScheduleData = courseScheduleData.filter(
            (scheduleElement) =>
                String(scheduleElement.session._session_id) === String(sessionFlowElement._id),
        );
        if (deliveryGroups) {
            if (sessionScheduleData.length) {
                if (
                    sessionScheduleData.find(
                        (scheduleElement) =>
                            scheduleElement.isActive === false ||
                            scheduleElement.status === MISSED ||
                            (scheduleElement.status === COMPLETED &&
                                scheduleElement.isMissedToComplete),
                    )
                ) {
                    sessionOrderData.status = sessionScheduleData.find(
                        (scheduleElement) => scheduleElement.isActive === false,
                    )
                        ? CANCELLED
                        : sessionScheduleData.find(
                              (scheduleElement) => scheduleElement.status === MISSED,
                          )
                        ? MISSED
                        : MISSED_TO_COMPLETE;
                    notConductedTopic.push(sessionOrderData);
                } else {
                    deliveryGroups.groupId.forEach((deliveryGroupElement) => {
                        let groupSchedule = false;
                        sessionScheduleData.forEach((scheduleElement) => {
                            scheduleElement.student_groups.forEach((sgElement) => {
                                sgElement.session_group.forEach((sessionGroupElement) => {
                                    if (!groupSchedule)
                                        groupSchedule =
                                            String(deliveryGroupElement) ===
                                            String(sessionGroupElement.session_group_id);
                                });
                            });
                        });
                        if (
                            !groupSchedule &&
                            !notConductedTopic.find(
                                (topicElement) =>
                                    String(topicElement._id) === String(sessionFlowElement._id),
                            )
                        ) {
                            notConductedTopic.push(sessionOrderData);
                        }
                    });
                }
            } else {
                notConductedTopic.push(sessionOrderData);
            }
        }
    });
    return notConductedTopic;
};
