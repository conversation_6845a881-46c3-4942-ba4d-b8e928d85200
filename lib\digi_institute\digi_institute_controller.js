let constant = require('../utility/constants');
const digi_institute_model = require('../models/digi_institute');
const digi_institute = require('mongoose').model(constant.DIGI_INSTITUTE);
const digi_university = require('mongoose').model(constant.DIGI_UNIVERSITY);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    try {
        let query = { 'isDeleted': false };
        let doc = await base_control.get_list(digi_institute, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Institutions not found", doc.data));
        let responses = [];
        doc.data.forEach(element => {
            responses.push({ ...element.toObject(), programs: 5, courses: 7 })
        });
        return res.status(200).send(common_files.list_all_response_function(res, 200, true, "Institution List", responses));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};

exports.archived_list = async (req, res) => {
    try {
        let query = { 'isActive': false };
        let doc = await base_control.get_list(digi_institute, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Institutions not found", doc.data));
        return res.status(200).send(common_files.list_all_response_function(res, 200, true, "Institution List", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};

exports.list_id = async (req, res) => {
    try {
        let id = req.params.id;
        let query = { _id: id, 'isDeleted': false };
        let doc = await base_control.get(digi_institute, query, {});
        if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Institution Details", doc.data));
        return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};

exports.insert = async (req, res) => {
    try {
        let query = { isDeleted: false };
        let doc = await base_control.get(digi_university, query, {});
        // if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        // return res.send(doc)
        if (doc.status) {
            let query = { 'isDeleted': false };
            let ins = await base_control.get_list(digi_institute, query, {});
            if (ins.status) {
                if (doc.data.no_of_college > ins.data.length) return res.status(410).send(common_files.response_function(res, 410, false, "Institution Count is Exceeding", 'Institution Count is Exceeding'));
            }
        }
        let obj = {
            "college_name": req.body.college_name,
            "street": req.body.street,
            "city": req.body.city,
            "state": req.body.state
        }
        let docs = await base_control.insert(digi_institute, obj);
        if (docs.status) return res.status(200).send(common_files.response_function(res, 200, true, "Institution Added successfully", docs.data));
        return res.status(410).send(common_files.response_function(res, 410, false, "Error", []));
    }
    catch (error) {
        console.log(error)
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};

exports.update = async (req, res) => {
    try {
        let obj = {
            "college_name": req.body.college_name,
            "street": req.body.street,
            "city": req.body.city,
            "state": req.body.state
        }
        let doc = await base_control.update(digi_institute, req.params.id, obj);
        if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Update successfully", doc.data));
        return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
    }
    catch (error) {
        //console.log(error);
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};

exports.archive = async (req, res) => {
    try {
        let obj = {
            "isActive": req.body.isActive,
        }
        let doc = await base_control.update(digi_institute, req.params.id, obj);
        if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Update successfully", doc.data));
        return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
    }
    catch (error) {
        //console.log(error);
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
exports.delete = async (req, res) => {
    try {
        let doc = await base_control.delete(digi_institute, req.params.id);
        if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Deleted successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 500, false, "Error", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
