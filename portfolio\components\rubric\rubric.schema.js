const {
    Types: { ObjectId },
} = require('mongoose');

const {
    POINT,
    PERCENTAGE,
    DIMENSIONAL,
    COMPONENTIAL,
    HEADER,
    SUB_HEADER,
} = require('../../common/utils/enums');

const nameAndDesc = {
    name: {
        type: String,
        trim: true,
    },
    desc: {
        type: String,
        trim: true,
    },
};

const nameAndCode = {
    name: {
        type: String,
        trim: true,
    },
    code: {
        type: String,
        trim: true,
    },
};

// outcome schema
const outcomesSchema = {
    _id: {
        type: ObjectId,
    },
    ...nameAndCode,
    hierarchy: {
        program: nameAndCode,
        curriculum: nameAndCode,
        course: nameAndCode,
        topic: nameAndCode,
    },
    framework: [
        {
            _id: {
                type: ObjectId,
            },
            ...nameAndCode,
            domains: [
                {
                    _id: { type: ObjectId },
                    label: {
                        type: String,
                        trim: true,
                    },
                    desc: {
                        type: String,
                        trim: true,
                    },
                    clos: [
                        {
                            label: {
                                type: String,
                                trim: true,
                            },
                            desc: {
                                type: String,
                                trim: true,
                            },
                        },
                    ],
                },
            ],
        },
    ],
    examCourseGroupId: {
        type: ObjectId,
    },
    reportCourseId: {
        type: ObjectId,
    },
    assessmentId: {
        type: ObjectId,
    },
};

// Dimension schema
const dimensionSchema = {
    ...nameAndDesc,
    feedback: {
        type: String,
    },
    maxValue: {
        type: Number,
    },
    isSelected: {
        type: Boolean,
    },
    awardedValue: {
        type: Number,
    },
};

// Parameter schema
const parameterSchema = {
    name: {
        type: String,
        trim: true,
        required: true,
    },
    type: {
        type: String,
        trim: true,
        required: true,
        enum: [DIMENSIONAL, COMPONENTIAL, HEADER, SUB_HEADER],
    },
    dimensions: [dimensionSchema],
    outcomes: [outcomesSchema],
};

const rubric = {
    ...nameAndDesc,
    isGlobal: {
        type: Boolean,
        default: false,
    },
    isTemplate: {
        type: Boolean,
        default: false,
    },
    scoring: {
        type: String,
        trim: true,
        required: true,
        enum: [POINT, PERCENTAGE],
    },
    parameters: [parameterSchema],
    createdBy: {
        id: { type: ObjectId },
        name: {
            first: {
                type: String,
                trim: true,
            },
            middle: {
                type: String,
                trim: true,
            },
            last: {
                type: String,
                trim: true,
            },
        },
    },
};

module.exports = {
    nameAndDesc,
    rubric,
    outcomesSchema,
};
