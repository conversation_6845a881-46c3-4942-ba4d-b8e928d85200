let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let roles_offices_list = new Schema({
    type: {
       type : String,
       required : true
    },
    name: {
       type : String,
       required : true
    },
    isActive: {
       type: Boolean,
       default: true
   },
    isDeleted: {
       type: Boolean,
       default: false
   }
},
    { timestamps: true });


module.exports = mongoose.model(constant.ROLES_OFFICES_LIST,roles_offices_list)
