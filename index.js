/**
 * Module dependencies.
 */

const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const compression = require('compression');
const favicon = require('serve-favicon');
const cors = require('cors');
const i18next = require('i18next');
const i18nextMiddleware = require('i18next-http-middleware');
const Backend = require('i18next-fs-backend');
const pinoHTTP = require('pino-http');
const {
    APP_STATE,
    SAAS_ENABLE,
    V2_ENABLE,
    ELASTIC_ENABLE,
    SERVICES: { ENCRYPT_PAYLOAD },
    logger,
    CORS_WHITELIST_ENABLED,
    WHITELIST_IPS,
    WHITELIST_DOMAINS,
    RATE_LIMIT_ENABLED,
    // DSCRONKEY,
} = require('./lib/utility/util_keys');

const i18nInstanceV1 = i18next.createInstance();

i18nInstanceV1
    .use(Backend)
    .use(i18nextMiddleware.LanguageDetector)
    .init({
        fallbackLng: 'en',
        backend: { loadPath: './locales/{{lng}}/translations.json' },
        supportedLngs: ['en', 'ar'], // Add your supported languages here
        detection: {
            order: ['querystring', 'cookie', 'headers'],
            // caches: ['cookie'],
        },
    });

require('dotenv').config();

// To ensure Log Dir is present or have to create
const { ensureDirectory } = require('./lib/utility/common_functions');
ensureDirectory('./public/logs');

const helmet = require('helmet');

const passport = require('passport');
const {
    passportConfig: { jwtStrategy },
    // morganConfig,
} = require('./config');

const app = express();

app.use(i18nextMiddleware.handle(i18nInstanceV1));
/**
 * Set `views` directory for module
 */

app.set('views', path.join(__dirname, 'views'));

/**
 * Set `view engine` to `pug`.
 */

app.set('view engine', 'pug');

/**
 * middleware for favicon
 */

// Adding Endpoint wise log
// if (APP_STATE !== 'test') {
//     app.use(morganConfig.successHandler);
//     app.use(morganConfig.errorHandler);
// }

// eslint-disable-next-line no-path-concat
app.use(favicon(__dirname + '/public/images/favicon.ico'));
app.use(express.static(path.join(__dirname, 'public')));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));

// Compress all responses
app.use(compression());

// protect against vulnerability
app.use(helmet());

// set to true if served behind proxy or load balancer
app.set('trust proxy', true);

// Disable the 'X-Powered-By' header
app.disable('x-powered-by');

/* Log every thing Request & Response */
app.use(
    pinoHTTP({
        logger,
        serializers: {
            req: (req) => ({
                method: req.method,
                body: req?.raw?.body || req?.body,
                url: req.url,
                headers: req.headers,
                query: req.query,
                remoteAddress: req.remoteAddress,
                remotePort: req.remotePort,
            }),
            res: (res) => ({
                statusCode: res.statusCode,
            }),
        },
        customLogLevel: (req, res, err) => {
            if (res.statusCode >= 500) return 'error';
            if (res.statusCode >= 400) return 'warn';
            return 'info'; // Default logging level
        },
        customSuccessMessage: (req, res) => ` ${req.method} ${req.originalUrl}`,
        customErrorMessage: (req, res, err) => `Error occurred: ${err.message}`,
        // timestamp: formatLogTimeStamp,
    }),
);

// Add a catch-all middleware to ensure security headers for all routes
app.use('*', (req, res, next) => {
    // This middleware will run for all routes and methods
    // Security headers are already set by the first middleware
    next();
});

// CORS Options Delegate
const corsOptionsDelegate = async (req, callback) => {
    const corsOptions = {
        origin: true,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        exposedHeaders: [
            'Access-Control-Allow-Origin',
            'Access-Control-Allow-Credentials',
            'Strict-Transport-Security',
        ],
        preflightContinue: false,
        optionsSuccessStatus: 204,
    };
    const userAgent = req.get('user-agent') || '';
    console.log('userAgent', userAgent);
    if (userAgent.includes('ELB-HealthChecker')) {
        // allow ELB health check requests
        return callback(null, corsOptions);
    }

    // Allow requests from mobile apps and tablets
    if (
        userAgent.includes('Android') ||
        userAgent.includes('iOS') ||
        userAgent.includes('iPhone') ||
        userAgent.includes('iPad') ||
        userAgent.includes('Huawei') ||
        userAgent.includes('Tablet')
    ) {
        return callback(null, corsOptions);
    }

    if (CORS_WHITELIST_ENABLED === 'false') {
        // whitelist disabled from env
        return callback(null, corsOptions);
    }

    const whitelistIPs = WHITELIST_IPS;
    const whitelistDomains = WHITELIST_DOMAINS;

    if (!whitelistIPs.length && !whitelistDomains.length) {
        return callback(null, corsOptions);
    }
    const sourceIP = req?.ip;
    const origin = req.header('Origin');
    const isDomainAllowed = whitelistDomains.includes(origin);
    const isIpAllowed = whitelistIPs.includes(sourceIP);
    const isAllowed = isDomainAllowed || isIpAllowed;
    // const isAllowed =
    //     isDomainAllowed ||
    //     isIpAllowed ||
    //     req.headers['digicronkey'] === DSCRONKEY ||
    //     apiKeyRoutes.find(
    //         (route) =>
    //             route.route === urlWithoutQueryParams &&
    //             route.method === req.method &&
    //             req.headers?.api_key,
    //     );

    if (isAllowed) {
        callback(null, corsOptions);
    } else {
        logger.info('Not allowed due to CORS: ip %s, origin %s', sourceIP, origin);
        callback(new Error(`Not allowed from ${sourceIP} origin ${origin}`));
    }
};

app.use(cors(corsOptionsDelegate));
app.options('*', cors(corsOptionsDelegate));

// Handle preflight requests with security headers
app.options('*', (req, res) => {
    res.status(204).end();
});

if (SAAS_ENABLE === 'true') {
    console.log('Saas Enabled');
    const { connectAllDb } = require('./api/v2/db-management/db-connection.service');
    connectAllDb();
    const { subscribeNewTenant } = require('./api/v2/db-management/db-connection.service');
    subscribeNewTenant();
}

// Middleware to set security headers
app.use((req, res, next) => {
    // Set HSTS header with proper configuration
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');

    // Set other security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'SAMEORIGIN');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader(
        'Permission-Policy',
        'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=()',
    );
    next();
});
app.use(
    [
        '/api/v1/user/authLoggedIn',
        '/api/v1/digiclass/user/authLogin',
        '/api/v1/digiclass/user/authLoggedIn',
        '/api/v1/digiclass/user/logout',
    ],
    (req, res, next) => {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        next();
    },
);

// Rate Limiting
const {
    getClientIpAddress,
    doRateLimit,
    getRateLimiterConfig,
    createRateLimiter,
    consumeRateLimiter,
    handleTooManyRequests,
    setRateLimitHeaders,
    getRateLimiterBasedOnRoute,
    getKeyForRateLimit,
} = require('./helper/rateLimit.helper');

const {
    publicRoutes: {
        LOGIN,
        FORGOT_PASSWORD,
        SIGNUP,
        IP_ADDRESS,
        FORGOT_PASSWORD_SEND_OTP,
        SELF_SIGNUP,
    },
} = require('./service/endpoint.util');

if (RATE_LIMIT_ENABLED === 'true') {
    app.use(async (req, res, next) => {
        const ipAddress = getClientIpAddress(req);
        const isRateLimitEnabled = await doRateLimit(req);
        if (isRateLimitEnabled) {
            // for specific ip address validate
            const ipRateLimiterConfig = getRateLimiterConfig(IP_ADDRESS);
            const ipRateLimiter = createRateLimiter(ipRateLimiterConfig);
            try {
                const ipRateLimiterResponse = await consumeRateLimiter(
                    ipRateLimiter,
                    `${ipAddress}`,
                );
                setRateLimitHeaders(res, 'IP', ipRateLimiterConfig, ipRateLimiterResponse);
            } catch (error) {
                console.log('Rate Limiting error', error);
                return handleTooManyRequests(res, req.url);
            }

            const url = req?.url.split('api/v1')[1];
            const rlConfig = getRateLimiterBasedOnRoute(url);
            const rateLimiterConfig = rlConfig.rateLimiterConfig;
            const rateLimiter = rlConfig.rateLimiter;
            try {
                if (
                    [
                        LOGIN,
                        FORGOT_PASSWORD,
                        SIGNUP,
                        FORGOT_PASSWORD_SEND_OTP,
                        SELF_SIGNUP,
                    ].includes(url)
                ) {
                    const emailRateLimitKey = getKeyForRateLimit(ipAddress, req.body?.email, url);
                    const emailRateLimiterResponse = await consumeRateLimiter(
                        rateLimiter,
                        emailRateLimitKey,
                    );
                    setRateLimitHeaders(res, 'Email', rateLimiterConfig, emailRateLimiterResponse);
                } else {
                    const userId = req?.tokenPayload?._id;
                    if (userId) {
                        const userRateLimitKey = getKeyForRateLimit(ipAddress, userId);
                        const userRateLimiterResponse = await consumeRateLimiter(
                            rateLimiter,
                            userRateLimitKey,
                        );
                        setRateLimitHeaders(
                            res,
                            'UserId',
                            rateLimiterConfig,
                            userRateLimiterResponse,
                        );
                    }
                }
            } catch (error) {
                console.log('Rate Limiting Error User Or Login', error);
                return handleTooManyRequests(res, url);
            }
        }
        next();
    });
}

if (ENCRYPT_PAYLOAD === 'true') {
    const { isEncryptedRoute, encryptDecryptApplicable } = require('./service/endpoint.util');
    const { decryptData } = require('./lib/utility/encrypt_decrypt.util');
    const { decryptPayload } = require('./middleware/encryptionDecryption.service');
    const { CLIENT_END } = require('./lib/utility/constants');
    app.use(function (req, res, next) {
        const url = req && req.originalUrl ? req.originalUrl.split('?')[0] : '';
        if (
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.request,
            })
        ) {
            try {
                if (typeof req.body?.data !== 'string') {
                    return res.status(400).send({
                        status_code: 400,
                        status: false,
                        message: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                        data: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                    });
                }
                if (req.headers?.['x-session-key'] && req.headers?.['x-session-key'].length) {
                    const sessionKey = req.headers?.['x-session-key'];
                    const sessionData = decryptPayload({
                        encryptedSessionKey: sessionKey,
                        encryptedPayload: req.body?.data,
                    });
                    Object.assign(req, { body: sessionData });
                } else {
                    const bodyData = decryptData({
                        content: req.body?.data,
                        handledBy: CLIENT_END,
                    });
                    delete req.body?.data;
                    Object.assign(req, { body: bodyData });
                }
            } catch (e) {
                return res.status(400).send({
                    status_code: 400,
                    status: false,
                    message: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                    data: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                });
            }
        }
        next();
    });
}

require('./lib/models')(app);

/**
 * routes application
 */

require('./routes')(app);

// Common V1 & V2 Services
require('./commonService/routes/index')(app);

/**
 * GET index page.
 */

app.get('*', function (req, res) {
    res.render('index', {
        title: 'DigiScheduler Backend API',
    });
});

if (V2_ENABLE === 'true') {
    console.log('V2 Enabled');
    // v2 version
    require('./api/bin/index');

    const i18nInstanceV2 = i18next.createInstance();
    i18nInstanceV2
        .use(Backend)
        .use(i18nextMiddleware.LanguageDetector)
        .init({
            fallbackLng: 'en',
            backend: { loadPath: './api/v2/locales/{{lng}}/translations.json' },
        });

    app.use('/api', i18nextMiddleware.handle(i18nInstanceV2));
    app.use('/api', require('./api'));
    // v2 version end
}

if (ELASTIC_ENABLE === 'true') {
    console.log('ELASTIC SEARCH ENABLED');
    require('./config/elasticSearch');
}

/**
 * Load auth routes and
 * login strategies with
 * passport
 */
app.use(passport.initialize());
passport.use('jwt', jwtStrategy);

app.get('/health', (req, res) => {
    const data = {
        host: os.hostname(),
        uptime: process.uptime(),
        message: 'Ok',
        date: new Date(),
    };
    res.status(200).send(data);
});

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    const err = new Error('Not Found');
    err.status = 404;
    next(err);
});

// error handler
app.use(function (err, req, res, next) {
    const isProduction = APP_STATE === 'production';

    // Handle CORS errors specifically
    if (err.message && err.message.includes('Not allowed')) {
        return res.status(403).json({
            status_code: 403,
            status: false,
            message: 'Access Denied: CORS not allowed',
            data: isProduction
                ? undefined
                : {
                      ip: req.ip,
                      origin: req.header('Origin'),
                  },
        });
    }

    const statusCode = err.status || 500;
    const errorResponse = {
        status_code: statusCode,
        status: false,
        message: statusCode === 500 ? 'Internal Server Error' : err.message,
        data: isProduction ? undefined : err.message,
    };

    if (!isProduction) {
        errorResponse.error = {
            message: err.message,
            stack: err.stack,
        };
    }
    res.status(statusCode).json(errorResponse);
});

const { importCacheDataOnload } = require('./service/localCache.service');
const { cronInit } = require('./service/cronService');
importCacheDataOnload();
cronInit();

module.exports = app;

/**
 * initiating redis service cache initialization
 */
const connection = require('./config/redis-connection');
async function launchRedis() {
    await connection.redisClient.connect();
    await connection.redisClient.duplicate();
    await connection.redisClient.Client.send_command('config', [
        'set',
        'notify-keyspace-events',
        'Ex',
    ]);
    if (SAAS_ENABLE === 'true') {
        const { subscribeNewTenant } = require('./api/v2/db-management/db-connection.service');
        subscribeNewTenant();
    }
    console.log('redis launched');
}
launchRedis();

// /**
//  * Firebase Initialization for Push Notification
//  */
// const firebase = require('firebase-admin');
// const serviceAccount = require('./lib/utility/firebase_key.json');

// firebase.initializeApp({
//     credential: firebase.credential.cert(serviceAccount),
// });
