const mongoose = require('mongoose');
const {
    LMS_STUDENT,
    USER,
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    ROL<PERSON>,
    PENDING,
    CANCELLED,
    APPROVED,
    W<PERSON><PERSON><PERSON>WN,
    LMS_STUDENT_SETTING,
    NOT_INITIATED,
    <PERSON>IGI_PROGRAM,
    <PERSON>OR<PERSON>RD,
    REJECT,
    USER_BASED,
    ROLE_BASED,
    ALL_USERS,
    ANY_ONE_IN_EACH_ROLE,
    ANY_ONE_USER,
    ANY_ONE_IN_ANY_ROLE,
    MALE,
    FEMALE,
    BOTH,
    INSTITUTION_CALENDAR,
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
    COURSE_SCHEDULE,
} = require('../utility/constants');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;

const lmsStudentSchemas = new Schema(
    {
        _institution_id: { type: ObjectId },
        _institution_calendar_id: { type: ObjectId, ref: INSTITUTION_CALENDAR },
        classificationType: { type: String, enum: [ONDUTY, PERMISSION, LEAVE] },
        studentId: { type: ObjectId, ref: USER },
        academicId: { type: String },
        settingId: { type: ObjectId, ref: LMS_STUDENT_SETTING },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        level: { type: String },
        levelApprover: {
            programIds: [{ type: ObjectId, ref: DIGI_PROGRAM }],
            genderSegregation: { type: Boolean, default: false },
            isExceptionalProgram: { type: Boolean },
            level: [
                {
                    levelName: { type: String },
                    roleIds: [{ type: ObjectId, ref: ROLE }],
                    approvalConfig: {
                        type: String,
                        enum: [ALL_USERS, ANY_ONE_IN_EACH_ROLE, ANY_ONE_USER, ANY_ONE_IN_ANY_ROLE],
                    },
                    roleUserDetails: [
                        {
                            roleName: { type: String },
                            roleId: { type: ObjectId, ref: USER },
                            userDetails: [
                                {
                                    staffId: { type: ObjectId, ref: USER },
                                    approvalStatus: { type: String },
                                },
                            ],
                        },
                    ],
                    categoryBased: { type: String, enum: [USER_BASED, ROLE_BASED] },
                    userIds: [{ type: ObjectId, ref: USER }],
                    turnAroundTime: { type: Number },
                    escalateRequest: { type: Boolean, default: false },
                    skipPreviousLevelApproval: { type: Boolean, default: false },
                    gender: { type: String, enum: [MALE, FEMALE, BOTH] },
                    overwritePreviousLevelApproval: { type: Boolean, default: false },
                },
            ],
        },
        year: { type: String },
        term: { type: String },
        doNotShowAgain: { type: Boolean, default: false },
        agree: { type: Boolean, default: false },
        categoryId: ObjectId,
        categoryName: String,
        typeId: ObjectId,
        typeName: String,
        dateAndTimeRange: {
            startDate: { type: Date },
            endDate: { type: Date },
            startSession: { type: Object },
            endSession: { type: Object },
        },
        noOfHours: Number,
        reason: String,
        roleIds: [{ type: ObjectId, ref: ROLE }],
        userIds: [{ type: ObjectId, ref: USER }],
        roleUsers: [{ type: ObjectId }],
        approvalFrom: [
            {
                levelId: { type: ObjectId },
                roleId: { type: ObjectId, ref: ROLE },
                userId: { type: ObjectId, ref: USER },
                reason: { type: String },
                categoryBased: { type: String, enum: [USER_BASED, ROLE_BASED] },
                status: {
                    type: String,
                    enum: [PENDING, CANCELLED, APPROVED, WITHDRAWN, FORWARD, REJECT],
                    default: PENDING,
                },
                date: Date,
            },
        ],
        approvalStatus: {
            type: String,
        },
        comment: { type: String },
        attachments: [
            {
                url: String,
                signedUrl: String,
                sizeInKb: Number,
                name: String,
            },
        ],
        leaveApplicationCriteria: { type: String, enum: [SESSION, DAY] },
        scheduleId: { type: ObjectId, ref: COURSE_SCHEDULE },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(LMS_STUDENT, lmsStudentSchemas);
