const mongoose = require('mongoose');
mongoose.Promise = global.Promise;
const MongoClientSource = require('mongodb').MongoClient;
const MongoClientTarget = require('mongodb').MongoClient;
const { institutionSchema } = require('../../components');
const { INSTITUTION } = require('../../utility/constants');

const clientOption = {
    socketTimeoutMS: 30000,
    keepAlive: true,
    poolSize: 1,
    useNewUrlParser: true,
    useUnifiedTopology: true,
    useFindAndModify: false,
    useCreateIndex: true,
};

// CONNECTION EVENTS
// When successfully connected
mongoose.connection.on('connected', () => {
    console.log('Mongoose default connection open');
});
const schema = new mongoose.Schema({
    name: String,
});

const setInitialEntry = async (dbConn) => {
    const model = dbConn.model('init', schema);
    const entries = await model.find();
    if (!entries || !entries.length) {
        await model.create({ name: 'db connected and tested' });
    }
};

// If the connection throws an error
mongoose.connection.on('error', (err) => {
    console.log('Mongoose default connection error: ' + err);
});

// When the connection is disconnected
mongoose.connection.on('disconnected', () => {
    console.log('Mongoose default connection disconnected');
});

// If the Node process ends, close the Mongoose connection
process.on('SIGINT', () => {
    mongoose.connection.close(() => {
        console.log('Mongoose default connection disconnected through app termination');
        process.exit(0);
    });
});

const initTenantDbConnection = (DB_URL) => {
    try {
        const db = mongoose.createConnection(DB_URL, clientOption);

        db.on(
            'error',
            console.error.bind(console, 'initTenantDbConnection MongoDB Connection Error>> : '),
        );
        db.once('open', async () => {
            // console.log('initTenantDbConnection client MongoDB Connection ok!');
            // require all schemas !?
            // eslint-disable-next-line global-require
            // require('../../components');
            await setInitialEntry(db);
        });
        return db;
    } catch (error) {
        console.log('initTenantDbConnection error', error);
    }
};

const chunks = 1000;
const timeOut = 7000;

function insertDocuments(documents, urlTarget, dbTarget, collectionTarget) {
    MongoClientTarget.connect(urlTarget, { useNewUrlParser: true }, function (error, mongo) {
        if (error) throw error;

        const db = mongo.db(dbTarget);
        db.collection(collectionTarget).insertMany(documents, function (err, result) {
            if (err) throw err;
            mongo.close();
        });
    });
}

function copyDocumentsInChunks(
    skip,
    limit,
    count,
    urlSource,
    dbSource,
    collectionSource,
    urlTarget,
    dbTarget,
    collectionTarget,
) {
    if (skip >= count) {
        return;
    }
    MongoClientSource.connect(urlSource, { useNewUrlParser: true }, function (error, mongo) {
        if (error) {
            throw error;
        }

        const db = mongo.db(dbSource);
        db.collection(collectionSource)
            .find({})
            .sort({ _id: 1 })
            .skip(skip)
            .limit(limit)
            .toArray(function (err, result) {
                if (err) {
                    throw err;
                }

                insertDocuments(result, urlTarget, dbTarget, collectionTarget);
                setTimeout(
                    copyDocumentsInChunks,
                    timeOut,
                    skip + limit,
                    limit,
                    count,
                    urlSource,
                    dbSource,
                    collectionSource,
                    urlTarget,
                    dbTarget,
                    collectionTarget,
                );
                //copyDocumentsInChunks(skip + limit, limit, count);
                mongo.close();
            });
    });
}

function countDocumentsDBSource(
    callback,
    limit,
    urlSource,
    dbSource,
    collectionSource,
    urlTarget,
    dbTarget,
    collectionTarget,
) {
    MongoClientSource.connect(urlSource, { useNewUrlParser: true }, function (error, mongo) {
        if (error) throw error;

        const db = mongo.db(dbSource);
        db.collection(collectionSource)
            .countDocuments()
            .then((count) => {
                callback(
                    0,
                    limit,
                    count,
                    urlSource,
                    dbSource,
                    collectionSource,
                    urlTarget,
                    dbTarget,
                    collectionTarget,
                );
                mongo.close();
            });
    });
}

const startMigration = async (
    urlSource,
    dbSource,
    collectionSource,
    urlTarget,
    dbTarget,
    collectionTarget,
) => {
    countDocumentsDBSource(
        copyDocumentsInChunks,
        chunks,
        urlSource,
        dbSource,
        collectionSource,
        urlTarget,
        dbTarget,
        collectionTarget,
    );
};

module.exports = {
    initTenantDbConnection,
    startMigration,
};
