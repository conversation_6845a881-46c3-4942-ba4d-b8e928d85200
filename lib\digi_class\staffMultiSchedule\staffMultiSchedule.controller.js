const courseScheduleSchema = require('../../models/course_schedule');
const courseSchema = require('../../models/digi_course');
const scheduleMultiAttendanceSchema = require('../../models/scheduleMultiAttendance');
const scheduleAttendanceSchema = require('../../models/schedule_attendance');
const userSchema = require('../../models/user');
const { sendResponse, convertToMongoObjectId, axiosCall, clone } = require('../../utility/common');
const {
    ATTENDANCE_DEFAULT_END_TIME,
    ONGOING,
    COMPLETED,
    PRESENT,
    RUNNING,
    PENDING,
    ABSENT,
    TIME_GROUP_BOOKING_TYPE: { REMOTE },
    MANUAL,
} = require('../../utility/constants');
const { logger } = require('../../utility/util_keys');
const { v4: uuidv4 } = require('uuid');
const { makeTitleAndMessage } = require('../sessions/session_service');
const { notification_push } = require('../../utility/notification_push');

exports.multiScheduleListing = async (req, res) => {
    try {
        const { scheduleId, staffId, scheduleStart, scheduleEnd } = req.query;
        const requestDatas = { scheduleId, staffId, scheduleStart, scheduleEnd };
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> multiScheduleListing -> start',
        );
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            scheduleStartDateAndTime: new Date(scheduleStart),
            scheduleEndDateAndTime: new Date(scheduleEnd),
            'staffs._staff_id': convertToMongoObjectId(staffId),
        };
        const scheduleProject = {
            _id: 1,
            session: 1,
            'students.status': 1,
            'students._id': 1,
            'students.name': 1,
            'staffs.staff_name': 1,
            'staffs._staff_id': 1,
            'staffs.mode': 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            rotation_count: 1,
            infra_name: 1,
            _infra_id: 1,
            course_code: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .find(scheduleQuery, scheduleProject)
            .lean();
        console.timeEnd('courseScheduleData');
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> multiScheduleListing -> end',
        );
        return sendResponse(res, 200, true, req.t('SESSION_REPORT_PENDING'), courseScheduleData);
    } catch (error) {
        logger.error(
            { error: error.stack },
            'staffMultiSchedule -> multiScheduleListing -> %s error',
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleStart = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const {
            primaryScheduleId,
            startByStaffId,
            mode,
            faceAuthentication,
            manualType,
            mergeScheduleDetails,
            excludeMeOtherSchedule,
            isLive,
            scheduleStartFrom,
            excludeMeOther,
            otherScheduleIds,
        } = req.body;
        const requestDatas = {
            primaryScheduleId,
            startByStaffId,
            mode,
            faceAuthentication,
            manualType,
            mergeScheduleDetails,
            excludeMeOtherSchedule,
            isLive,
            scheduleStartFrom,
        };
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleStart -> start');
        const scheduleIds = [
            ...mergeScheduleDetails.map((mergeScheduleElement) =>
                convertToMongoObjectId(mergeScheduleElement.scheduleId),
            ),
        ];
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            _id: scheduleIds,
        };
        const scheduleProject = {
            _id: 1,
            'staffs._staff_id': 1,
            'students._id': 1,
            _course_id: 1,
            _program_id: 1,
            year_no: 1,
            level_no: 1,
            status: 1,
            'sessionDetail.attendance_mode': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session.session_type': 1,
            'session._session_id': 1,
            rotation: 1,
            rotation_count: 1,
            _institution_calendar_id: 1,
            term: 1,
            type: 1,
            course_name: 1,
            course_code: 1,
            merge_status: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .find(scheduleQuery, scheduleProject)
            .lean();
        console.timeEnd('courseScheduleData');
        if (courseScheduleData.length !== scheduleIds.length)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        courseScheduleData.forEach((scheduleElement) => {
            if (scheduleElement.status === ONGOING) {
                return sendResponse(
                    res,
                    200,
                    false,
                    req.t('ALREADY_YOU_HAVE_RUNNING_SESSION'),
                    null,
                );
            }
            if (scheduleElement.attendance_mode === COMPLETED) {
                return sendResponse(res, 200, false, req.t('ATTENDANCE_ALREADY_CLOSED'), null);
            }
            if (scheduleElement.attendance_mode === ONGOING) {
                return sendResponse(res, 200, false, req.t('ATTENDANCE_ALREADY_STARTED'), null);
            }
        });
        const courseDetails = await courseSchema
            .find(
                {
                    _id: courseScheduleData.map((scheduleElement) =>
                        convertToMongoObjectId(scheduleElement._course_id),
                    ),
                },
                {
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details.year': 1,
                    'course_assigned_details.level_no': 1,
                    'course_assigned_details.auto_end_attendance_in': 1,
                },
            )
            .lean();
        let maxAutoEndAttendance = ATTENDANCE_DEFAULT_END_TIME;
        let socketEventName = '';
        const sessionUUID = uuidv4();
        let userIds = [];
        courseScheduleData.forEach((scheduleElement) => {
            const courseDocElement = courseDetails.find((courseElement) => {
                return (
                    String(courseElement._id) === String(scheduleElement._course_id) &&
                    courseElement.course_assigned_details.some((assignElement) => {
                        return (
                            assignElement.year === scheduleElement.year_no &&
                            assignElement.level_no === scheduleElement.level_no &&
                            String(assignElement._program_id) ===
                                String(scheduleElement._program_id)
                        );
                    })
                );
            });
            if (courseDocElement) {
                courseDocElement.course_assigned_details.forEach((assignElement) => {
                    if (
                        assignElement.year === scheduleElement.year_no &&
                        assignElement.level_no === scheduleElement.level_no &&
                        String(assignElement._program_id) === String(scheduleElement._program_id)
                    ) {
                        maxAutoEndAttendance = Math.max(
                            maxAutoEndAttendance,
                            assignElement.auto_end_attendance_in,
                        );
                    }
                });
            }
            // Socket Name Generation
            socketEventName += `${scheduleElement._id}-`;
            if (scheduleElement.session.delivery_symbol)
                socketEventName += `${scheduleElement.session.delivery_symbol}-${scheduleElement.session.delivery_no}-`;
            scheduleElement.students.forEach((studentElement) => {
                userIds.push(String(studentElement._id));
            });
            scheduleElement.staffs.forEach((staffElement) => {
                userIds.push(String(staffElement._staff_id));
            });
        });
        userIds = [...new Set(userIds)];
        const userDetails = await userSchema
            .find(
                {
                    _id: { $in: userIds },
                },
                {
                    isActive: 1,
                    user_id: 1,
                    device_type: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    type: 1,
                },
            )
            .lean();
        const scheduleBasedStudentData = [];
        courseScheduleData.forEach((scheduleElement) => {
            const userDevicePushData = [];
            scheduleElement.students.forEach((studentElement) => {
                const studentDetail = userDetails.find(
                    (userElement) => String(userElement._id) === String(studentElement._id),
                );
                if (studentDetail) {
                    userDevicePushData.push({
                        device_type: studentDetail.device_type,
                        token: studentDetail.fcm_token,
                        web_fcm_token: studentDetail.web_fcm_token,
                        _user_id: studentDetail._id,
                        scheduleId: scheduleElement._id,
                        sessionId: scheduleElement.session._id,
                        StudentGroup: '',
                    });
                }
            });
            const { title, message } = makeTitleAndMessage(scheduleElement);
            const data = {
                _id: scheduleElement._id,
                sessionId: scheduleElement.session._session_id,
                courseId: scheduleElement._course_id,
                programId: scheduleElement._program_id,
                rotation: scheduleElement.rotation,
                rotation_count: scheduleElement.rotation_count,
                institutionCalendarId: scheduleElement._institution_calendar_id,
                yearNo: scheduleElement.year_no,
                levelNo: scheduleElement.level_no,
                term: scheduleElement.term,
                mergeStatus: scheduleElement.merge_status,
                mergeType: scheduleElement.type,
                clickAction: 'session_start',
                notificationType: 'session',
                scheduleStartFrom,
            };
            scheduleBasedStudentData.push({ tokens: userDevicePushData, title, message, data });
        });
        if (socketEventName.endsWith('-')) {
            socketEventName = socketEventName.slice(0, -1);
        }
        const scheduleBulkWrite = [];
        const scheduleSessionDetails = {
            sessionDetail: {
                retake: false,
                attendance_mode: ONGOING,
                startBy: startByStaffId,
                start_time: new Date(),
            },
        };
        courseScheduleData.forEach((scheduleElement) => {
            const scheduleStaffChanges = mergeScheduleDetails.find(
                (mergeScheduleElement) =>
                    mergeScheduleElement.scheduleId === String(scheduleElement._id),
            );
            scheduleBulkWrite.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(scheduleElement._id),
                    },
                    update: {
                        $set: {
                            isLive: isLive || false,
                            status: ONGOING,
                            socket_port: socketEventName,
                            uuid: sessionUUID,
                            ...scheduleSessionDetails,
                            faceAuthentication:
                                typeof faceAuthentication !== 'undefined'
                                    ? faceAuthentication
                                    : true,
                            ...(manualType && { manualType }),
                            scheduleStartFrom,
                            'staffs.$[staffIndex].status': PRESENT,
                            'staffs.$[staffIndex].time': new Date(),
                        },
                    },
                    arrayFilters: [
                        {
                            'staffIndex._staff_id': convertToMongoObjectId(startByStaffId),
                        },
                    ],
                },
            });
            // if (
            //     scheduleStaffChanges &&
            //     scheduleStaffChanges.excludeStaffId &&
            //     scheduleStaffChanges.excludeStaffId.length
            // ) {
            //     scheduleBulkWrite.push({
            //         updateOne: {
            //             filter: {
            //                 _id: convertToMongoObjectId(scheduleElement._id),
            //             },
            //             update: {
            //                 $pull: {
            //                     staffs: {
            //                         _staff_id: { $in: scheduleStaffChanges.excludeStaffId },
            //                     },
            //                 },
            //             },
            //         },
            //     });
            // }
        });
        const scheduleMultiData = await scheduleMultiAttendanceSchema.create({
            _institution_id,
            primaryScheduleId,
            startByStaffId,
            mergeScheduleDetails,
            excludeMeOther,
            otherScheduleIds,
            status: RUNNING,
        });
        let bulkWrite;
        if (scheduleBulkWrite.length)
            bulkWrite = await courseScheduleSchema.bulkWrite(scheduleBulkWrite);
        logger.info(
            JSON.stringify({ scheduleMultiData, bulkWrite }),
            'staffMultiSchedule -> scheduleStart -> DBUpdate Status',
        );
        if (!bulkWrite.modifiedCount)
            return sendResponse(res, 200, false, req.t('UNABLE_TO_START_SESSION'), null);
        const responseObject = {
            ...scheduleSessionDetails,
            uuid: sessionUUID,
            socketPort: socketEventName,
            auto_end_attendance_in: maxAutoEndAttendance,
        };
        if (scheduleBasedStudentData.length) {
            for (scheduleStudentElement of scheduleBasedStudentData)
                await notification_push(
                    scheduleStudentElement.tokens,
                    scheduleStudentElement.title,
                    scheduleStudentElement.message,
                    scheduleStudentElement.data,
                );
        }
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleStart -> end');
        return sendResponse(
            res,
            200,
            true,
            req.t('SESSION_ATTENDANCE_STARTED'),
            responseObject,
            //! Todo Will Remove Dev Code will moving production
            // {
            //     responseObject,
            //     scheduleBulkWrite,
            //     courseScheduleData,
            //     courseDetails,
            // },
        );
    } catch (error) {
        logger.error({ error: error.stack }, 'staffMultiSchedule -> scheduleStart -> %s error');
        console.error(error);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleEnd = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { scheduleId, staffId } = req.body;
        const requestDatas = {
            scheduleId,
            staffId,
        };
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleEnd -> start');
        const multiScheduleQuery = {
            $or: [
                { primaryScheduleId: convertToMongoObjectId(scheduleId) },
                { 'mergeScheduleDetails.scheduleId': convertToMongoObjectId(scheduleId) },
            ],
        };
        const scheduleMultiData = await scheduleMultiAttendanceSchema
            .findOne(multiScheduleQuery, {
                // primaryScheduleId: 1,
                'mergeScheduleDetails.scheduleId': 1,
            })
            .lean();
        if (!scheduleMultiData)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const courseScheduleData = await courseScheduleSchema
            .find(
                {
                    _id: {
                        $in: scheduleMultiData.mergeScheduleDetails.map((multiScheduleElement) =>
                            convertToMongoObjectId(multiScheduleElement.scheduleId),
                        ),
                    },
                },
                {
                    staffs: 1,
                    students: 1,
                    status: 1,
                },
            )
            .lean();

        const scheduleBulkWrite = [];
        courseScheduleData.forEach((scheduleElement) => {
            scheduleElement.staffs.forEach((staffElement) => {
                if (staffElement.status === PENDING) {
                    staffElement.status = ABSENT;
                    staffElement.time = new Date();
                }
            });
            scheduleElement.students.forEach((studentElement) => {
                if (studentElement.status === PENDING) {
                    studentElement.status = ABSENT;
                    studentElement.primaryStatus = ABSENT;
                    studentElement.time = new Date();
                }
            });
            scheduleBulkWrite.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(scheduleElement._id),
                    },
                    update: {
                        $set: {
                            'sessionDetail.attendance_mode': COMPLETED,
                            'sessionDetail.stop_time': new Date(),
                            'sessionDetail.mode': 'M',
                            students: scheduleElement.students,
                            staffs: scheduleElement.staffs,
                            status: ONGOING,
                        },
                    },
                },
            });
        });
        let bulkWrite;
        if (scheduleBulkWrite.length)
            bulkWrite = await courseScheduleSchema.bulkWrite(scheduleBulkWrite);

        await scheduleMultiAttendanceSchema.updateOne(multiScheduleQuery, {
            $set: { status: COMPLETED },
        });
        logger.info(
            JSON.stringify({ bulkWrite, scheduleBulkWrite }),
            'staffMultiSchedule -> scheduleEnd -> DBUpdate Status',
        );
        if (!bulkWrite.modifiedCount)
            return sendResponse(res, 200, false, req.t('UNABLE_TO_START_SESSION'), null);

        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleEnd -> end');
        return sendResponse(
            res,
            200,
            true,
            req.t('SESSION_ATTENDANCE_STOPPED'),
            {},
            //! Todo Will Remove Dev Code will moving production
            // {
            //     scheduleMultiData,
            // courseScheduleData,
            // scheduleBulkWrite,
            // },
        );
    } catch (error) {
        logger.error({ error: error.stack }, 'staffMultiSchedule -> scheduleEnd -> %s error');
        console.error(error);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.attendanceReport = async (req, res) => {
    try {
        const {
            body: { _id, staffId, status },
        } = req;
        const requestDatas = {
            scheduleId: _id,
            staffId,
            status,
        };
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> attendanceReport -> start',
        );
        const multiScheduleQuery = {
            $or: [
                { primaryScheduleId: convertToMongoObjectId(_id) },
                { 'mergeScheduleDetails.scheduleId': convertToMongoObjectId(_id) },
            ],
        };
        const scheduleMultiData = await scheduleMultiAttendanceSchema
            .findOne(multiScheduleQuery, {
                // primaryScheduleId: 1,
                'mergeScheduleDetails.scheduleId': 1,
            })
            .lean();
        if (!scheduleMultiData)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const scheduleIds = scheduleMultiData.mergeScheduleDetails.map((multiScheduleElement) =>
            convertToMongoObjectId(multiScheduleElement.scheduleId),
        );
        const scheduleAttendance = await scheduleAttendanceSchema
            .find(
                {
                    scheduleId: { $in: scheduleIds },
                    status: { $exists: true },
                },
                {
                    scheduleId: 1,
                    modeBy: 1,
                    status: 1,
                    'students.status': 1,
                    'students.primaryStatus': 1,
                    'students._student_id': 1,
                    'students.time': 1,
                    createdAt: 1,
                    isCompared: 1,
                    _staff_id: 1,
                    updatedAt: 1,
                    updateTime: 1,
                    attendanceCondition: 1,
                },
            )
            .populate({ path: '_staff_id', select: { name: 1, user_id: 1 } })
            .sort({ createdAt: -1 })
            .lean();
        const courseScheduleAttendance = await courseScheduleSchema
            .find(
                {
                    _id: scheduleIds,
                },
                {
                    students: 1,
                    staffs: 1,
                    // sessionDetail: 1,
                    // merge_status: 1,
                    // merge_with: 1,
                },
            )
            .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
            // .populate('merge_with.schedule_id')
            .lean();
        let userIds = new Set();
        const allStudents = [];
        const allStaffs = [];
        courseScheduleAttendance.forEach((scheduleElement) => {
            scheduleElement.students.forEach((studentElement) => {
                userIds.add(String(studentElement._id));
                if (
                    !allStudents.find(
                        (userElement) => String(userElement._id) === String(studentElement._id),
                    )
                )
                    allStudents.push(studentElement);
            });
            scheduleElement.staffs.forEach((staffElement) => {
                userIds.add(String(staffElement._staff_id));
                if (
                    !allStaffs.find(
                        (userElement) =>
                            String(userElement._staff_id) === String(staffElement._staff_id),
                    )
                )
                    allStaffs.push(staffElement);
            });
        });
        userIds = [...userIds];
        const getUserIds = await userSchema
            .find(
                {
                    _id: { $in: userIds },
                },
                {
                    isActive: 1,
                    user_id: 1,
                },
            )
            .lean();
        let filteredStudents = allStudents.filter((studentElement) => {
            const matchedStudent = getUserIds.find(
                (userElement) =>
                    userElement._id.toString() === studentElement._id.toString() &&
                    userElement.isActive,
            );
            if (matchedStudent) {
                studentElement.user_id = matchedStudent.user_id;
                return true;
            }
            return false;
        });
        allStaffs.forEach((staffElement) => {
            const getUserId = getUserIds.find(
                (userEntry) => String(userEntry._id) === String(staffElement._staff_id),
            );
            staffElement.user_id = getUserId.user_id;
        });
        if (status) {
            filteredStudents = filteredStudents.filter((element) => element.status === status);
        }
        courseScheduleAttendance[0].students = filteredStudents;
        courseScheduleAttendance[0].staffs = allStaffs;
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> attendanceReport -> end');
        return sendResponse(res, 200, true, req.t('DATA_RETRIEVED'), {
            isCompared:
                scheduleAttendance && scheduleAttendance.length && scheduleAttendance[0].isCompared
                    ? scheduleAttendance[0].isCompared
                    : null,
            attendanceCondition:
                scheduleAttendance &&
                scheduleAttendance.length &&
                scheduleAttendance[0].attendanceCondition
                    ? scheduleAttendance[0].attendanceCondition
                    : null,
            scheduleAttendance,
            courseScheduleAttendance: courseScheduleAttendance[0],
            scheduleAnomalyStudents: [],
        });
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.attendanceChange = async (req, res) => {
    try {
        const {
            body: {
                _id,
                _student_ids,
                studentsTardisUpdate,
                _staff_ids,
                scheduleAttendanceIds,
                attendanceCondition,
            },
        } = req;
        const requestDatas = {
            scheduleId: _id,
            _student_ids,
            studentsTardisUpdate,
            _staff_ids,
            scheduleAttendanceIds,
            attendanceCondition,
        };
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> attendanceChange -> start',
        );
        const multiScheduleQuery = {
            $or: [
                { primaryScheduleId: convertToMongoObjectId(_id) },
                { 'mergeScheduleDetails.scheduleId': convertToMongoObjectId(_id) },
            ],
        };
        const scheduleMultiData = await scheduleMultiAttendanceSchema
            .findOne(multiScheduleQuery, {
                // primaryScheduleId: 1,
                'mergeScheduleDetails.scheduleId': 1,
            })
            .lean();
        if (!scheduleMultiData)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const scheduleIds = scheduleMultiData.mergeScheduleDetails.map((multiScheduleElement) =>
            convertToMongoObjectId(multiScheduleElement.scheduleId),
        );
        const courseScheduleAttendance = await courseScheduleSchema
            .find(
                {
                    _id: scheduleIds,
                },
                {
                    students: 1,
                    staffs: 1,
                    status: 1,
                    'sessionDetail.attendance_mode': 1,
                    mode: 1,
                    // sessionDetail: 1,
                    // merge_status: 1,
                    // merge_with: 1,
                },
            )
            .lean();
        if (!courseScheduleAttendance.length)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        if (courseScheduleAttendance[0].status === PENDING)
            return sendResponse(res, 200, false, req.t('SESSION_NOT_YET_STARTED'), null);
        if (courseScheduleAttendance[0].sessionDetail.attendance_mode === ONGOING)
            return sendResponse(res, 200, false, req.t('ATTENDANCE_NOT_CLOSED'), null);
        if (scheduleAttendanceIds || attendanceCondition) {
            await scheduleAttendanceSchema.updateMany(
                {
                    scheduleId: { $in: scheduleIds },
                },
                {
                    $set: {
                        ...(scheduleAttendanceIds &&
                            scheduleAttendanceIds.length && { isCompared: scheduleAttendanceIds }),
                        ...(attendanceCondition && { attendanceCondition }),
                    },
                },
                { multi: true },
            );
        }
        const scheduleBulkWrite = [];
        const userIds = [];
        courseScheduleAttendance.forEach((scheduleElement) => {
            const { students, staffs, mode } = scheduleElement;
            _student_ids.forEach((studentIdElement) => {
                const stdIndex = students.findIndex(
                    (i) => i._id.toString() === studentIdElement.toString(),
                );
                if (stdIndex >= 0) {
                    userIds.push(convertToMongoObjectId(studentIdElement));
                    students[stdIndex].status =
                        students[stdIndex].status === PRESENT ? ABSENT : PRESENT;
                    students[stdIndex].mode = MANUAL;
                    if (mode !== REMOTE) {
                        students[stdIndex].time = new Date();
                    }
                }
            });
            _staff_ids.forEach((staffIdElement) => {
                const staffIndex = staffs.findIndex(
                    (i) => i._staff_id.toString() === staffIdElement.toString(),
                );
                if (staffIndex >= 0) {
                    userIds.push(convertToMongoObjectId(staffIdElement));
                    staffs[staffIndex].status =
                        staffs[staffIndex].status === PRESENT ? ABSENT : PRESENT;
                    staffs[staffIndex].time = new Date();
                    staffs[staffIndex].mode = MANUAL;
                }
            });
            scheduleBulkWrite.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(scheduleElement._id),
                    },
                    update: {
                        $set: {
                            students,
                            staffs,
                        },
                    },
                },
            });
        });
        let bulkWrite;
        if (scheduleBulkWrite.length)
            bulkWrite = await courseScheduleSchema.bulkWrite(scheduleBulkWrite);

        logger.info(
            JSON.stringify({ bulkWrite, scheduleBulkWrite }),
            'staffMultiSchedule -> attendanceChange -> DBUpdate Status',
        );
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> attendanceChange -> end');
        return sendResponse(
            res,
            200,
            true,
            req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
            {},
            //! Todo Will Remove Dev Code will moving production
            // {
            //     scheduleBulkWrite,
            // },
        );
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleClose = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { scheduleId, staffId } = req.body;
        const requestDatas = {
            scheduleId,
            staffId,
        };
        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleClose -> start');
        const multiScheduleQuery = {
            $or: [
                { primaryScheduleId: convertToMongoObjectId(scheduleId) },
                { 'mergeScheduleDetails.scheduleId': convertToMongoObjectId(scheduleId) },
            ],
        };
        const scheduleMultiData = await scheduleMultiAttendanceSchema
            .findOne(multiScheduleQuery, {
                // primaryScheduleId: 1,
                'mergeScheduleDetails.scheduleId': 1,
            })
            .lean();
        if (!scheduleMultiData)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const courseScheduleData = await courseScheduleSchema
            .find(
                {
                    _id: {
                        $in: scheduleMultiData.mergeScheduleDetails.map((multiScheduleElement) =>
                            convertToMongoObjectId(multiScheduleElement.scheduleId),
                        ),
                    },
                },
                {
                    staffs: 1,
                    students: 1,
                    status: 1,
                    'sessionDetail.attendance_mode': 1,
                    mode: 1,
                    _course_id: 1,
                    type: 1,
                    _program_id: 1,
                    year_no: 1,
                    level_no: 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_type': 1,
                    'session._session_id': 1,
                    rotation: 1,
                    rotation_count: 1,
                    _institution_calendar_id: 1,
                    term: 1,
                    course_name: 1,
                    course_code: 1,
                    merge_status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                },
            )
            .lean();
        if (!courseScheduleData.length)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        if (courseScheduleData[0].status === PENDING)
            return sendResponse(res, 200, false, req.t('SESSION_NOT_YET_STARTED'), null);
        if (courseScheduleData[0].sessionDetail.attendance_mode === ONGOING)
            return sendResponse(res, 200, false, req.t('ATTENDANCE_NOT_CLOSED'), null);
        const scheduleBulkWrite = [];
        const userIdsBasedSchedule = [];
        courseScheduleData.forEach((scheduleElement) => {
            const staffAttendanceChange = scheduleElement.staffs.find(
                (userElement) => userElement.status === PENDING,
            );
            const studentAttendanceChange = scheduleElement.students.find(
                (userElement) => userElement.status === PENDING,
            );
            const scheduleData = clone(scheduleElement);
            scheduleData.status = COMPLETED;
            delete scheduleData.students;
            scheduleElement.staffs.forEach((staffElement) => {
                if (staffElement.status === PENDING) {
                    staffElement.status = ABSENT;
                    staffElement.time = new Date();
                }
                userIdsBasedSchedule.push({
                    eventId: String(staffElement._staff_id),
                    data: JSON.stringify({
                        sessions: scheduleData,
                    }),
                });
            });
            scheduleElement.students.forEach((studentElement) => {
                if (studentElement.status === PENDING) {
                    studentElement.status = ABSENT;
                    studentElement.time = new Date();
                }
                userIdsBasedSchedule.push({
                    eventId: String(studentElement._id),
                    data: JSON.stringify({
                        sessions: { ...scheduleData, students: [studentElement] },
                    }),
                });
            });
            scheduleBulkWrite.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(scheduleElement._id),
                    },
                    update: {
                        $set: {
                            ...(studentAttendanceChange && { students: scheduleElement.students }),
                            ...(staffAttendanceChange && { students: scheduleElement.staffs }),
                            status: COMPLETED,
                        },
                    },
                },
            });
        });
        let bulkWrite;
        if (scheduleBulkWrite.length)
            bulkWrite = await courseScheduleSchema.bulkWrite(scheduleBulkWrite);

        logger.info(
            JSON.stringify({ bulkWrite, scheduleBulkWrite }),
            'staffMultiSchedule -> scheduleClose -> DBUpdate Status',
        );
        if (!bulkWrite.modifiedCount)
            return sendResponse(res, 200, false, req.t('UNABLE_TO_STOP_ATTENDANCE'), null);
        if (userIdsBasedSchedule.length) axiosCall(userIdsBasedSchedule);

        logger.info(JSON.stringify(requestDatas), 'staffMultiSchedule -> scheduleClose -> end');
        return sendResponse(res, 200, true, req.t('SESSION_ATTENDANCE_STOPPED'), {});
    } catch (error) {
        logger.error({ error: error.stack }, 'staffMultiSchedule -> scheduleClose -> %s error');
        console.error(error);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.mergedScheduleListing = async (req, res) => {
    try {
        const { scheduleId, staffId } = req.query;
        const requestDatas = { scheduleId, staffId };
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> mergedScheduleListing -> start',
        );
        const multiScheduleQuery = {
            $or: [
                { primaryScheduleId: convertToMongoObjectId(scheduleId) },
                { 'mergeScheduleDetails.scheduleId': convertToMongoObjectId(scheduleId) },
            ],
        };
        const scheduleMultiData = await scheduleMultiAttendanceSchema
            .findOne(multiScheduleQuery, {
                // primaryScheduleId: 1,
                'mergeScheduleDetails.scheduleId': 1,
            })
            .lean();
        if (!scheduleMultiData)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const scheduleIds = scheduleMultiData.mergeScheduleDetails.map((multiScheduleElement) =>
            convertToMongoObjectId(multiScheduleElement.scheduleId),
        );
        const courseScheduleAttendance = await courseScheduleSchema
            .find(
                {
                    _id: { $in: scheduleIds },
                },
                {
                    _id: 1,
                    session: 1,
                    'students.status': 1,
                    'students._id': 1,
                    'students.name': 1,
                    'staffs.staff_name': 1,
                    'staffs._staff_id': 1,
                    'staffs.mode': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    rotation_count: 1,
                    infra_name: 1,
                    _infra_id: 1,
                    course_code: 1,
                },
            )
            .lean();
        logger.info(
            JSON.stringify(requestDatas),
            'staffMultiSchedule -> mergedScheduleListing -> end',
        );
        return sendResponse(
            res,
            200,
            true,
            req.t('SESSION_REPORT_PENDING'),
            courseScheduleAttendance,
        );
    } catch (error) {
        logger.error(
            { error: error.stack },
            'staffMultiSchedule -> mergedScheduleListing -> %s error',
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};
