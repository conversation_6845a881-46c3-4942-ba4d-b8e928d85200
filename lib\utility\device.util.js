const userRegisteredDetailSchema = require('../models/userRegisteredDetail');
const userDeviceDetailSchema = require('../models/userDeviceDetails');
const { logger } = require('./util_keys');
const { ACTIVE, INACTIVE } = require('./constants');
const { convertToMongoObjectId } = require('./common');

/**
 * Check if user can login from the specified device
 * @param {string} userId - User ID
 * @param {string} userType - User Type
 * @param {string} deviceType - Device Type
 * @param {string} deviceBrandName - Device Brand Name
 * @param {string} deviceID - Device ID
 * @param {string} deviceNumber - Device Number
 * @param {string} deviceOSVersion - Device OS Version
 * @returns {string} - A message
 */
const userDeviceRegistration = async ({
    userId,
    userType,
    deviceType,
    deviceBrandName,
    deviceID,
    deviceNumber,
    deviceOSVersion,
}) => {
    try {
        // First, check if this device is already registered with another user
        const deviceRegisteredWithOtherUser = await userRegisteredDetailSchema
            .findOne(
                {
                    deviceID,
                    userId: { $ne: userId }, // Exclude current user
                    status: ACTIVE,
                },
                { userId: 1, deviceID: 1 },
            )
            .lean();

        if (deviceRegisteredWithOtherUser) {
            logger.warn(
                'device.util -> userDeviceRegistration -> %s Device %s is already registered with user %s',
                userId,
                deviceID,
                deviceRegisteredWithOtherUser.userId,
            );
            return 'This device is already registered with another user. Please use a different device or contact support.';
        }

        logger.info(
            'device.util -> userDeviceRegistration -> %s User Device Registration, allowing access',
            userId,
        );
        // Allowed to Login to Create Record in User RegisteredDetail
        await userRegisteredDetailSchema.create({
            userId,
            userType,
            deviceType,
            deviceBrandName,
            deviceID,
            deviceNumber,
            deviceOSVersion,
            registeredOn: new Date(new Date().setHours(0, 0, 0, 0)),
            status: ACTIVE,
        });
        return 'User Device Registration Successfully';
    } catch (error) {
        logger.error(
            { error },
            'device.util -> userDeviceRegistration -> Error checking device login permission',
        );
        // In case of error, allow login to avoid blocking users
        return 'User Device Registration Failed';
    }
};

/**
 * Check if user can login from the specified device
 * @param {string} userId - User ID
 * @param {string} userType - User Type
 * @param {string} deviceType - Device Type
 * @param {string} deviceBrandName - Device Brand Name
 * @param {string} deviceID - Device ID
 * @param {string} deviceNumber - Device Number
 * @param {string} deviceOSVersion - Device OS Version
 * @returns {Object} - { canLogin: boolean, freshDevice: boolean }
 */
const checkDeviceLoginPermission = async ({
    userId,
    userType,
    deviceType,
    deviceBrandName,
    deviceID,
    deviceNumber,
    deviceOSVersion,
}) => {
    try {
        // First, check if this device is already registered with another user
        const deviceRegisteredWithOtherUser = await userRegisteredDetailSchema
            .findOne(
                {
                    deviceID,
                    userId: { $ne: userId }, // Exclude current user
                    status: ACTIVE,
                },
                { userId: 1, deviceID: 1 },
            )
            .lean();

        if (deviceRegisteredWithOtherUser) {
            logger.warn(
                'device.util -> checkDeviceLoginPermission -> %s Device %s is already registered with user %s',
                userId,
                deviceID,
                deviceRegisteredWithOtherUser.userId,
            );
            return {
                canLogin: false,
                message:
                    'This device is already registered with another user. Please use a different device or contact support.',
            };
        }

        // Get the last login for this user
        const lastLogin = await userRegisteredDetailSchema
            .findOne(
                {
                    userId,
                    status: ACTIVE,
                },
                { deviceID: 1, status: 1 },
            )
            .sort({ registeredOn: -1 })
            .lean();

        if (!lastLogin) {
            logger.info(
                'device.util -> checkDeviceLoginPermission -> %s User Device Registration, allowing access',
                userId,
            );
            // Allowed to Login to Create Record in User RegisteredDetail
            // await userRegisteredDetailSchema.create({
            //     userId,
            //     userType,
            //     deviceType,
            //     deviceBrandName,
            //     deviceID,
            //     deviceNumber,
            //     deviceOSVersion,
            //     registeredOn: new Date(new Date().setHours(0, 0, 0, 0)),
            //     status: ACTIVE,
            // });
            return { canLogin: true, freshDevice: true };
        }

        // User has logged in before, check if it's the same device
        if (lastLogin.deviceID === deviceID) {
            // Same device as last login, allow login
            logger.info(
                'device.util -> checkDeviceLoginPermission -> %s Same device as last login, allowing access',
                userId,
            );
            return { canLogin: true, freshDevice: false };
        }

        // Different device from last login, deny access
        logger.warn(
            'device.util -> checkDeviceLoginPermission -> %s Different device login attempt from last login',
            userId,
        );
        return {
            canLogin: false,
            message: 'Please login with the same device you registered with earlier.',
        };
    } catch (error) {
        logger.error(
            { error },
            'device.util -> checkDeviceLoginPermission -> Error checking device login permission',
        );
        // In case of error, allow login to avoid blocking users
        return { canLogin: true };
    }
};

/**
 * Log device login details
 * @param {Object} deviceData - Device information
 * @returns {Promise<Object>} - Created device log entry
 */
const logDeviceLogin = async (deviceData) => {
    try {
        const deviceLog = await userDeviceDetailSchema.create(deviceData);
        logger.info(
            'device.util -> logDeviceLogin -> Device login logged for user %s on device %s',
            deviceData.userId,
            deviceData.deviceID,
        );
        return deviceLog;
    } catch (error) {
        logger.error({ error }, 'device.util -> logDeviceLogin -> Error logging device login');
        throw error;
    }
};

/**
 * Get device login history for a user
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - Device login history
 */
const getDeviceLoginHistory = async ({ userId }) => {
    try {
        const query = { userId };

        const deviceHistory = await userRegisteredDetailSchema
            .find(query)
            .sort({ createdAt: -1 })
            .lean();

        return deviceHistory;
    } catch (error) {
        logger.error(
            { error },
            'device.util -> getDeviceLoginHistory -> Error getting device login history',
        );
        throw error;
    }
};

/**
 * Unregister an existing device for a user
 * @param {Object} params - Unregistration parameters
 * @param {string} params.userId - User ID
 * @param {string} params.deviceID - Device ID to unregister
 * @param {string} params.unregisteredBy - User ID who is performing the unregistration
 * @param {string} params.unregisteredReason - Reason for unregistration
 * @returns {Promise<Object>} - Updated device registration record
 */
const unregisterDevice = async ({ userId, deviceID, unregisteredBy, unregisteredReason }) => {
    try {
        // Find the active device registration for this user and device
        const deviceRegistration = await userRegisteredDetailSchema.findOne({
            userId,
            deviceID,
            status: ACTIVE,
        });

        if (!deviceRegistration) {
            logger.warn(
                'device.util -> unregisterDevice -> No active device registration found for user %s and device %s',
                userId,
                deviceID,
            );
            throw new Error('No active device registration found');
        }

        // Update the device registration to inactive
        const updatedDevice = await userRegisteredDetailSchema.updateOne(
            { _id: convertToMongoObjectId(deviceRegistration._id) },
            {
                $set: {
                    status: INACTIVE,
                    unregisteredBy,
                    unregisteredOn: new Date(new Date().setHours(0, 0, 0, 0)),
                    unregisteredReason,
                },
            },
        );

        logger.info(
            'device.util -> unregisterDevice -> Device %s unregistered for user %s by %s',
            deviceID,
            userId,
            unregisteredBy,
        );

        return updatedDevice;
    } catch (error) {
        logger.error(
            { error },
            'device.util -> unregisterDevice -> Error unregistering device for user %s and device %s',
            userId,
            deviceID,
        );
        throw error;
    }
};

module.exports = {
    userDeviceRegistration,
    checkDeviceLoginPermission,
    logDeviceLogin,
    getDeviceLoginHistory,
    unregisterDevice,
};
