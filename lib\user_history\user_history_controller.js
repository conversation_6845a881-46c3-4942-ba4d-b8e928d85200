const constant = require('../utility/constants');
var user_history = require('mongoose').model(constant.USER_HISTORY);
var program = require('mongoose').model(constant.PROGRAM);
var department = require('mongoose').model(constant.DEPARTMENT);
var position = require('mongoose').model(constant.POSITION);
var role = require('mongoose').model(constant.ROLE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const user_formate = require('./user_history_formate');
const ObjectId = require('mongodb').ObjectID;
const common_fun = require('../utility/common_functions');
const util_key = require('../utility/util_keys');
const crypto = require('crypto');
const moment = require('moment');

exports.list_student = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { 'user_type': constant.STUDENTS } },
        { $sort: { updatedAt: -1 } },
        { $lookup: { from: constant.USER, localField: '_user_id', foreignField: '_id', as: 'user' } },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        { $addFields: { user_no: '$user.user_id', user_name: '$user.name' } },
        { $lookup: { from: constant.USER, localField: '_editor_id', foreignField: '_id', as: 'staff' } },
        { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
        { $addFields: { editor: '$staff.name' } },
        { $project: { _id: 1, user_no: 1, _user_id: 1, user_name: 1, user_type: 1, section: 1, title: 1, before_update: 1, after_update: 1, reason: 1, _editor_id: 1, editor: 1, updatedAt: 1 } },
        { $skip: skips }, { $limit: limits }
    ];
    let query = { 'isDeleted': false, 'user_type': constant.STUDENTS };
    let doc = await base_control.get_aggregate_with_count(user_history, aggre, query);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        common_files.list_all_response(res, 200, true, "Student History list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* user_formate.user(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_staff = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { 'user_type': 'staff' } },
        { $sort: { updatedAt: -1 } },
        { $lookup: { from: constant.USER, localField: '_user_id', foreignField: '_id', as: 'user' } },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        { $addFields: { user_no: '$user.user_id' } },
        { $lookup: { from: constant.USER, localField: '_editor_id', foreignField: '_id', as: 'staff' } },
        { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
        { $addFields: { editor: '$staff.name' } },
        { $project: { _id: 1, user_no: 1, _user_id: 1, user_type: 1, section: 1, title: 1, before_update: 1, after_update: 1, _editor_id: 1, editor: 1, reason: 1, updatedAt: 1 } },
        { $skip: skips }, { $limit: limits }
    ];
    let query = { 'isDeleted': false, 'user_type': 'staff' };
    let doc = await base_control.get_aggregate_with_count(user_history, aggre, query);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        common_files.list_all_response(res, 200, true, "Staff History list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* user_formate.user(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = ObjectId(req.params.id);
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { _id: id } },
        { $lookup: { from: constant.USER, localField: '_user_id', foreignField: '_id', as: 'user' } },
        { $unwind: { path: '$user', preserveNullAndEmptyArrays: true } },
        { $addFields: { user_no: '$user.user_id', user_name: '$user.name', user_program: '' } },
        { $lookup: { from: constant.USER, localField: '_editor_id', foreignField: '_id', as: 'staff' } },
        { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
        { $addFields: { editor: '$staff.name', editor_no: '$staff.user_id', editor_department: '' } },
        { $project: { _id: 1, user_no: 1, user_name: 1, user_program: 1, _user_id: 1, user_type: 1, section: 1, title: 1, before_update: 1, after_update: 1, reason: 1, _editor_id: 1, editor: 1, editor_no: 1, editor_department: 1, updatedAt: 1 } },

    ];
    let doc = await base_control.get_aggregate(user_history, aggre);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        common_files.com_response(res, 200, true, "History ID list", doc.data /* user_formate.user(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};