.rules_staging:
    rules:
        - if: $CI_COMMIT_BRANCH == "staging1"
          when: manual

build staging:
    stage: build
    environment: staging1
    interruptible: true
    extends:
        - .rules_staging
        - .build
    variables:
        ECR_STAGING: $ECR:staging1
    script:
        - docker build --tag $ECR_STAGING .
        - docker push $ECR_STAGING

deploy to staging:
    stage: deploy
    environment: staging1
    extends:
        - .aws_image
        - .ecs_update_service
