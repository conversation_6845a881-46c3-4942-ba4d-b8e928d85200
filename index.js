/**
 * Module dependencies.
 */

const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const favicon = require('serve-favicon');
const cors = require('cors');
const i18next = require('i18next');
const i18nextMiddleware = require('i18next-http-middleware');
const Backend = require('i18next-fs-backend');
const pinoHTTP = require('pino-http');
const {
    APP_STATE,
    SAAS_ENABLE,
    V2_ENABLE,
    ELASTIC_ENABLE,
    SERVICES: { ENCRYPT_PAYLOAD },
    logger,
} = require('./lib/utility/util_keys');

const i18nInstanceV1 = i18next.createInstance();

i18nInstanceV1
    .use(Backend)
    .use(i18nextMiddleware.LanguageDetector)
    .init({
        fallbackLng: 'en',
        backend: { loadPath: './locales/{{lng}}/translations.json' },
        supportedLngs: ['en', 'ar'], // Add your supported languages here
        detection: {
            order: ['querystring', 'cookie', 'headers'],
            caches: ['cookie'],
        },
    });

require('dotenv').config();

// To ensure Log Dir is present or have to create
const { ensureDirectory } = require('./lib/utility/common_functions');
ensureDirectory('./public/logs');

const helmet = require('helmet');

const passport = require('passport');
const {
    passportConfig: { jwtStrategy },
    // morganConfig,
} = require('./config');

const app = express();

app.use(i18nextMiddleware.handle(i18nInstanceV1));
/**
 * Set `views` directory for module
 */

app.set('views', path.join(__dirname, 'views'));

/**
 * Set `view engine` to `pug`.
 */

app.set('view engine', 'pug');

/**
 * middleware for favicon
 */

// Adding Endpoint wise log
// if (APP_STATE !== 'test') {
//     app.use(morganConfig.successHandler);
//     app.use(morganConfig.errorHandler);
// }

// eslint-disable-next-line no-path-concat
app.use(favicon(__dirname + '/public/images/favicon.ico'));
app.use(express.static(path.join(__dirname, 'public')));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ limit: '10mb', extended: true }));
app.use(cookieParser());
app.options('*', cors());
app.use(cors());
// Compress all responses
app.use(compression());

// protect against vulnerability
app.use(helmet());

// set to true if served behind proxy or load balancer
app.set('trust proxy', true);

// Disable the 'X-Powered-By' header
app.disable('x-powered-by');

/* Log every thing Request & Response */
app.use(
    pinoHTTP({
        logger,
        serializers: {
            req: (req) => ({
                method: req.method,
                body: req?.raw?.body || req?.body,
                url: req.url,
                headers: req.headers,
                query: req.query,
                remoteAddress: req.remoteAddress,
                remotePort: req.remotePort,
            }),
            res: (res) => ({
                statusCode: res.statusCode,
            }),
        },
        customLogLevel: (req, res, err) => {
            if (res.statusCode >= 500) return 'error';
            if (res.statusCode >= 400) return 'warn';
            return 'info'; // Default logging level
        },
        customSuccessMessage: (req, res) => ` ${req.method} ${req.originalUrl}`,
        customErrorMessage: (req, res, err) => `Error occurred: ${err.message}`,
        // timestamp: formatLogTimeStamp,
    }),
);

if (SAAS_ENABLE === 'true') {
    console.log('Saas Enabled');
    const { connectAllDb } = require('./api/v2/db-management/db-connection.service');
    connectAllDb();
    const { subscribeNewTenant } = require('./api/v2/db-management/db-connection.service');
    subscribeNewTenant();
}

// Middleware to set the Permission-Policy header
app.use((req, res, next) => {
    res.setHeader(
        'Permission-Policy',
        'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=()',
    );
    next();
});

if (ENCRYPT_PAYLOAD === 'true') {
    const { isEncryptedRoute, encryptDecryptApplicable } = require('./service/endpoint.util');
    const { decryptData } = require('./lib/utility/encrypt_decrypt.util');
    const { CLIENT_END } = require('./lib/utility/constants');
    app.use(function (req, res, next) {
        const url = req && req.originalUrl ? req.originalUrl.split('?')[0] : '';
        if (
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.request,
            })
        ) {
            try {
                if (typeof req.body?.data !== 'string') {
                    return res.status(400).send({
                        status_code: 400,
                        status: false,
                        message: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                        data: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                    });
                }
                const bodyData = decryptData({ content: req.body?.data, handledBy: CLIENT_END });
                delete req.body?.data;
                Object.assign(req, { body: bodyData });
            } catch (e) {
                return res.status(400).send({
                    status_code: 400,
                    status: false,
                    message: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                    data: req.t('ERROR_PARSE_FIELD_IN_BODY'),
                });
            }
        }
        next();
    });
}

require('./lib/models')(app);

/**
 * routes application
 */

require('./routes')(app);

if (V2_ENABLE === 'true') {
    console.log('V2 Enabled');
    // v2 version
    require('./api/bin/index');

    const i18nInstanceV2 = i18next.createInstance();
    i18nInstanceV2
        .use(Backend)
        .use(i18nextMiddleware.LanguageDetector)
        .init({
            fallbackLng: 'en',
            backend: { loadPath: './api/v2/locales/{{lng}}/translations.json' },
        });

    app.use('/api', i18nextMiddleware.handle(i18nInstanceV2));
    app.use('/api', require('./api'));
    // v2 version end
}

if (ELASTIC_ENABLE === 'true') {
    console.log('ELASTIC SEARCH ENABLED');
    require('./config/elasticSearch');
}

/**
 * Load auth routes and
 * login strategies with
 * passport
 */
app.use(passport.initialize());
passport.use('jwt', jwtStrategy);

// Common V1 & V2 Services
require('./commonService/routes/index')(app);

app.get('/health', (req, res) => {
    const data = {
        host: os.hostname(),
        uptime: process.uptime(),
        message: 'Ok',
        date: new Date(),
    };
    res.status(200).send(data);
});

/**
 * GET index page.
 */

app.get('*', function (req, res) {
    res.render('index', {
        title: 'DigiScheduler Backend API',
    });
});

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    const err = new Error('Not Found');
    err.status = 404;
    next(err);
});

// error handler
app.use(function (err, req, res, next) {
    const isProduction = APP_STATE === 'production';
    res.status(err.status || 500).json({
        status_code: 500,
        status: false,
        message: 'Unable to Process',
        data: err.message,
        ...(isProduction ? {} : { error: err }),
    });
});

const { importCacheDataOnload } = require('./service/localCache.service');
const { cronInit } = require('./service/cronService');
importCacheDataOnload();
cronInit();

module.exports = app;

/**
 * initiating redis service cache initialization
 */
const connection = require('./config/redis-connection');
async function launchRedis() {
    await connection.redisClient.connect();
    await connection.redisClient.duplicate();
    await connection.redisClient.Client.send_command('config', [
        'set',
        'notify-keyspace-events',
        'Ex',
    ]);
    if (SAAS_ENABLE === 'true') {
        const { subscribeNewTenant } = require('./api/v2/db-management/db-connection.service');
        subscribeNewTenant();
    }
    console.log('redis launched');
}
launchRedis();

// /**
//  * Firebase Initialization for Push Notification
//  */
// const firebase = require('firebase-admin');
// const serviceAccount = require('./lib/utility/firebase_key.json');

// firebase.initializeApp({
//     credential: firebase.credential.cert(serviceAccount),
// });
