/* eslint-disable no-useless-escape */
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');
const {
    Name,
    Family_Name,
    USER_TYPE,
    ACADEMIC,
    GENDER,
    EMAIL,
    BATCH,
    ENROLLMENT_YEAR,
    DOB,
    MOBILE,
    NATIONALITY_ID,
    USER_STATE,
    STATUS,
} = require('../utility/enums');
exports.user = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _employee_id_doc: Joi.any(),
                    employee_id: Joi.string().alphanum().min(1).max(20).required(),
                    first_name: Joi.string().trim().min(3).max(25).required(),
                    middle_name: Joi.string().allow('').trim().max(25),
                    last_name: Joi.string().allow(''),
                    // .trim()
                    // .min(1)
                    // .max(25)
                    // .required()
                    family_name: Joi.string().allow('').trim().max(25),
                    dob: Joi.string(),
                    // .min(3)
                    // .max(20)
                    // .required()
                    email: Joi.string()
                        .trim()
                        .regex(
                            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                        )
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    mobile: Joi.number().min(8).max(1000000000000000).required(),
                    nationality_id: Joi.string().allow(''),
                    // .min(5)
                    // .max(25)
                    // .required()
                    _nationality_id: Joi.string().length(24).required(),
                    _nationality_id_doc: Joi.any(),

                    building: Joi.string().allow('').min(3).max(20).required(),
                    city: Joi.string().min(3).max(25).required(),
                    district: Joi.string().min(3).max(25).required(),
                    zip_code: Joi.number().min(3).max(1000000).required(),
                    unit: Joi.string().min(1).max(50).required(),
                    street_no: Joi.string().min(3).max(50).required(),
                    passport_no: Joi.string().allow('').alphanum().min(3).max(20),
                    _address_doc: Joi.any(),
                    degree_name: Joi.string().allow('').min(3).max(20).required(),
                    _degree_doc: Joi.any(),
                    _appointment_order_doc: Joi.any(),

                    _program_id: Joi.string().alphanum().length(24).required(),
                    _department_id: Joi.string().alphanum().length(24).required(),
                    _department_division_id: Joi.string().alphanum().allow('').length(24),
                    _department_subject_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fields = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object()
                .keys({
                    fields: Joi.array()
                        .items(
                            Joi.string().valid(
                                ...[
                                    Name,
                                    Family_Name,
                                    USER_TYPE,
                                    ACADEMIC,
                                    GENDER,
                                    EMAIL,
                                    BATCH,
                                    ENROLLMENT_YEAR,
                                    DOB,
                                    MOBILE,
                                    NATIONALITY_ID,
                                    USER_STATE,
                                    STATUS,
                                ],
                            ),
                        )
                        .required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.users_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    user_type: Joi.string().alphanum().valid('staff', 'student').required(),
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_mobile_mail = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    match: Joi.string().alphanum().valid('mobile', 'employee_id').required(),
                    value: Joi.string().alphanum().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

const getErrorMessage = (detail) => {
    const fieldName = detail.path[3];
    switch (detail.type) {
        case 'string.min':
            return `${fieldName} must be at least ${detail.context.limit} characters long.`;
        case 'string.max':
            return `${fieldName} must not exceed ${detail.context.limit} characters.`;
        case 'string.empty':
            return `${fieldName} is required and cannot be empty.`;
        case 'any.required':
            return `${fieldName} is required.`;
        case 'string.base':
            return `${fieldName} must be a valid string.`;
        case 'string.trim':
            return `${fieldName} must not have leading or trailing spaces.`;
        default:
            return `${fieldName} validation failed: ${detail.message}`;
    }
};

exports.user_import = (req, res, next) => {
    let schema;
    if (req.body.user_type === constant.EVENT_WHOM.STAFF) {
        schema = Joi.object()
            .keys({
                body: Joi.object()
                    .keys({
                        data: Joi.array().items(
                            Joi.object()
                                .keys({
                                    // username: Joi.string().alphanum().min(3).max(20).required().error(error => {
                                    //     return error;
                                    // }),
                                    employee_id: Joi.string().trim().alphanum().min(1).required(),
                                    first_name: Joi.string().trim().min(1).required(),
                                    middle_name: Joi.string().allow('').trim(),
                                    last_name: Joi.string().allow(''),
                                    // .trim()
                                    // .min(1)
                                    // .required()
                                    family_name: Joi.string().allow('').trim(),
                                    email: Joi.string()
                                        .trim()
                                        .regex(
                                            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                                        )
                                        .required(),
                                    gender: Joi.string()
                                        .trim()
                                        .valid(
                                            'MALE',
                                            'FEMALE',
                                            'Male',
                                            'Female',
                                            constant.GENDER.MALE,
                                            constant.GENDER.FEMALE,
                                        )
                                        .required(),
                                    nationality_id: Joi.string().allow(''),
                                    // .min(5)
                                    // .max(25)
                                    // .required()
                                })
                                .unknown(true),
                        ),
                    })
                    .unknown(true),
            })
            .unknown(true);
    } else if (req.body.user_type === constant.EVENT_WHOM.STUDENT) {
        schema = Joi.object()
            .keys({
                body: Joi.object()
                    .keys({
                        data: Joi.array().items(
                            Joi.object()
                                .keys({
                                    // username: Joi.string().alphanum().min(3).max(20).required().error(error => {
                                    //     return error;
                                    // }),
                                    academic_no: Joi.string().alphanum().trim().min(1).required(),
                                    first_name: Joi.string().trim().min(1).required(),
                                    middle_name: Joi.string().allow('').trim(),
                                    last_name: Joi.string().allow(''),
                                    // .trim()
                                    // .min(1)
                                    // .required()
                                    family_name: Joi.string().allow('').trim(),
                                    email: Joi.string()
                                        .trim()
                                        .regex(
                                            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                                        )
                                        .required(),
                                    gender: Joi.string()
                                        .trim()
                                        .valid(
                                            'MALE',
                                            'FEMALE',
                                            'Male',
                                            'Female',
                                            constant.GENDER.MALE,
                                            constant.GENDER.FEMALE,
                                        )
                                        .required(),
                                    nationality_id: Joi.string().allow(''),
                                    // .min(5)
                                    // .max(25)
                                    // .required()
                                    program_no: Joi.string().trim().min(3).max(10).required(),
                                    enrollment_year: Joi.string().trim(),
                                    batch: Joi.string().trim(),
                                    // .valid(
                                    //     'REGULAR',
                                    //     'INTERIM',
                                    //     'Regular',
                                    //     'Interim',
                                    //     constant.BATCH.REGULAR,
                                    //     constant.BATCH.INTERIM,
                                    // )
                                })
                                .unknown(true),
                        ),
                    })
                    .unknown(true),
            })
            .unknown(true);
    }

    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => getErrorMessage(detail));
        const uniqueErrorMessages = [...new Set(errorMessage)];
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            uniqueErrorMessages,
        );
    }
    next();
};

exports.signup = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                email: Joi.string().trim().email().required(),
                password: Joi.string().min(8).max(25).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.login = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    email: Joi.string().trim().email().required(),
                    password: Joi.string().min(8).max(25).required(),
                    otp_mode: Joi.string().valid('email', 'mobile').required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.university_user_login = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    email: Joi.string().trim().email().required(),
                    password: Joi.string().min(2).max(25).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.set_password = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    old_password: Joi.string().min(8).max(25).required(),
                    new_password: Joi.string().min(8).max(25).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.mobile_register = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    mobile: Joi.string().min(8).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.otp_verify = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    otp: Joi.number().max(9999).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.change_mobile = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    otp: Joi.number().max(9999).required(),
                    reason: Joi.string().min(3).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.profile_insert = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    mobile: Joi.number().min(8).max(100000000000).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.profile_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    dob: Joi.string(),
                    // .min(3)
                    // .max(20)
                    // .required()
                    _nationality_id: Joi.string().length(24).required(),
                    building: Joi.string().allow('').min(3).max(250).required(),
                    city: Joi.string().min(3).max(25).required(),
                    district: Joi.string().min(3).max(25).required(),
                    zip_code: Joi.number().min(3).max(1000000).required(),
                    unit: Joi.string().min(1).max(50).required(),
                    // street_no: Joi.string().min(3).max(50).required().error(error => {
                    //     return error;
                    // }),
                    passport_no: Joi.string().allow('').alphanum().min(3).max(20),
                    office_extension: Joi.number().min(0).max(99999).required(),
                    office_room_no: Joi.string().allow('').alphanum().min(1).max(20).required(),
                    first_name: Joi.boolean().required(),
                    middle_name: Joi.boolean(),
                    last_name: Joi.boolean(),
                    // .required()
                    gender: Joi.boolean().required(),
                    employee_id: Joi.boolean().required(),
                    nationality_id: Joi.boolean(),
                    // .required()
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.profile_document_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    _school_certificate_doc: Joi.any(),
                    _entrance_exam_certificate_doc: Joi.any(),
                    _admission_order_doc: Joi.any(),
                    _college_id_doc: Joi.any(),
                    _nationality_id_doc: Joi.any(),
                    _address_doc: Joi.any(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.profile_staff_document_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    _employee_id_doc: Joi.any(),
                    _nationality_id_doc: Joi.any(),
                    _address_doc: Joi.any(),
                    _degree_doc: Joi.any(),
                    _appointment_order_doc: Joi.any(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.profile_staff_student_document_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                id: Joi.string().alphanum().length(24).required(),
                _employee_id_doc: Joi.any(),
                _nationality_id_doc: Joi.any(),
                _address_doc: Joi.any(),
                _degree_doc: Joi.any(),
                _appointment_order_doc: Joi.any(),
                _school_certificate_doc: Joi.any(),
                _entrance_exam_certificate_doc: Joi.any(),
                _admission_order_doc: Joi.any(),
                _college_id_doc: Joi.any(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.user_add_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required().allow(''),
                    username: Joi.string().alphanum().min(3).max(20).required(),
                    employee_id: Joi.string().alphanum().min(1).max(20).required(),
                    first_name: Joi.string().allow('').min(2).max(25).required(),
                    middle_name: Joi.string().allow('').max(25),
                    last_name: Joi.string().allow(''),
                    // .min(1)
                    // .max(25)
                    // .required()
                    email: Joi.string()
                        .trim()
                        .regex(
                            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                        )
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    nationality_id: Joi.string().allow(''),
                    // .min(5)
                    // .max(25)
                    // .required()
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.user_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    employee_id: Joi.string().alphanum().min(1).max(20),
                    first_name: Joi.string().min(3).max(25),
                    middle_name: Joi.string().allow('').max(25),
                    last_name: Joi.string().allow(''),
                    // .min(1)
                    // .max(25)
                    gender: Joi.string().valid(constant.GENDER.MALE, constant.GENDER.FEMALE),
                    nationality_id: Joi.string().allow(''),
                    // .min(5)
                    // .max(25)
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_profile_validation = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    data: Joi.string().valid(constant.VALID, constant.INVALID).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_face_bio_register = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    face: Joi.boolean().required(),
                    finger: Joi.boolean().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.user_academic_allocation = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    data: Joi.array().items(
                        Joi.object()
                            .keys({
                                allocation_type: Joi.string().valid(
                                    constant.PRIMARY,
                                    constant.AUXILIARY,
                                ),
                                _program_id: Joi.string().alphanum().length(24).required(),
                                _department_id: Joi.string().alphanum().length(24).required(),
                                _department_division_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .allow(''),
                                _department_subject_id: Joi.array().items(
                                    Joi.string().alphanum().length(24).required(),
                                ),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_active_inactive = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    status: Joi.boolean().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_face_registration = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    center: Joi.any().required(),
                    left: Joi.any().required(),
                    right: Joi.any().required(),
                    up: Joi.any().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.user_finger_registration = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    data: Joi.array().items(
                        Joi.object()
                            .keys({
                                name: Joi.string().required(),
                                active: Joi.boolean().required(),
                                image: Joi.string().required(),
                                IsoTemplate: Joi.string().required(),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.user_program_add_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    _program_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
