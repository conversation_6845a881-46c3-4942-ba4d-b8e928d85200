const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;
const { ANNOUNCEMENT_SETTING } = require('../utility/constants');
const announcementSettingSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        tagAnnouncementType: { type: Boolean },
        priorityAnnouncement: { type: Boolean },
        announcementType: [
            {
                name: { type: String },
            },
        ],
        priorityType: [
            {
                name: { type: String },
                colorCode: { type: String },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(ANNOUNCEMENT_SETTING, announcementSettingSchema);
