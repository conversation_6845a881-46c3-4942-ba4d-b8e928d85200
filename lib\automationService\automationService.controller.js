const {
    fetchId,
    removeInstitutionCalendar,
    removeProgramCalendar,
    removeStudentGroup,
    removeCourseSchedule,
    removeProgram,
    removeCurriculum,
    removeDepartmentSubject,
    removeSessionDeliveryType,
    removeCourse,
    removeCourseSessionOrder,
} = require('./automationService.service');
const {
    CALENDAR: { INSTITUTION, PROGRAM },
} = require('../utility/constants');

// In This Service We are Removing Calendar based Flow like as IC & PC & SG & CS
const calendarBasedCleanService = async ({ body = {} }) => {
    try {
        const { calendarName, programName, moduleName } = body;
        let calendarId;
        let programId;
        if (calendarName) {
            const calendarResult = await fetchId(INSTITUTION, calendarName);
            if (calendarResult.statusCode) {
                return calendarResult;
            }
            calendarId = calendarResult;
            if (programName) {
                const programResult = await fetchId(PROGRAM, programName);
                if (programResult.statusCode) {
                    return programResult;
                }
                programId = programResult;
            }
        }

        const removalActions = {
            institutionCalendar: async () => {
                await removeInstitutionCalendar({ calendarId });
                await removeProgramCalendar({ calendarId, programId });
                await removeStudentGroup({ calendarId, programId });
                await removeCourseSchedule({ calendarId, programId });
            },
            programCalendar: async () => {
                await removeProgramCalendar({ calendarId, programId });
                await removeStudentGroup({ calendarId, programId });
                await removeCourseSchedule({ calendarId, programId });
            },
            studentGroup: async () => {
                await removeStudentGroup({ calendarId, programId });
                await removeCourseSchedule({ calendarId, programId });
            },
            courseSchedule: async () => {
                await removeCourseSchedule({ calendarId, programId });
            },
        };

        if (removalActions[moduleName]) {
            await removalActions[moduleName]();
        }

        return {
            statusCode: 200,
            message: 'Process Completed',
            data: null,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

// In This Service We are Removing Program Input Datas (Program, Session Type and Delivery Type, Curriculum, Department and Subjects, Course, Session Order)
const programInputCleanService = async ({ body = {} }) => {
    try {
        const { programName } = body;

        const programResult = await fetchId(PROGRAM, programName);
        if (programResult.statusCode) {
            return programResult;
        }
        const programId = programResult;

        // Calendar Based on Data
        await removeProgramCalendar({ programId });
        await removeStudentGroup({ programId });
        await removeCourseSchedule({ programId });

        // Program Input Removal
        await removeProgram({ programId });
        await removeCurriculum({ programId });
        await removeDepartmentSubject({ programId });
        await removeSessionDeliveryType({ programId });
        await removeCourse({ programId });
        await removeCourseSessionOrder({ programId });

        return {
            statusCode: 200,
            message: 'Process Completed',
            data: null,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

module.exports = {
    calendarBasedCleanService,
    programInputCleanService,
};
