const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.updateLevelUserAccessValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId,
        }),
        body: Joi.object()
            .keys({
                level: Joi.string().required(),
                programId: objectId,
                curriculumId: objectId,
                studentGroup: Joi.boolean().optional(),
                schedule: Joi.boolean().optional(),
                userIds: Joi.array().items(Joi.string().length(24).hex()),
            })
            .unknown(true),
    })
    .unknown(true);

exports.listOfProgramValidator = Joi.object().keys({
    Headers: Joi.object()
        .keys({
            _institution_id: objectId,
        })
        .unknown(true),
});

exports.getCurriculumByProgramIdValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId,
        }),
        params: Joi.object()
            .keys({
                programId: objectId,
            })
            .unknown(true),
    })
    .unknown(true);

exports.listOfProgramValidator = Joi.object().keys({
    Headers: Joi.object()
        .keys({
            _user_id: objectId,
            _institution_id: objectId,
        })
        .unknown(true),
});
