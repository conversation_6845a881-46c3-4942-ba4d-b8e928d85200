const express = require('express');
const route = express.Router();
const country = require('./country_controller');
const validater = require('./country_validator');
route.post('/list', country.list_values);
route.get('/:id', validater.Country_id, country.list_id);
route.get('/', country.list);
route.post('/', validater.country, country.insert);
route.post('/bulk_import' /* , validater.country */, country.bulk_import);
route.put('/:id', validater.Country_id, validater.country, country.update);
route.delete('/:id', validater.Country_id, country.delete);

module.exports = route;
