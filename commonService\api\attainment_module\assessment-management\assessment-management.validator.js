const Joi = require('joi');

exports.getAssessmentValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                id: Joi.string()
                    .required()
                    .error((error) => 'INSTITUTION_ID_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);

exports.addAssessmentValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _id: Joi.string().alphanum().length(24),
                type: Joi.string()
                    .required()
                    .error(() => 'ASSESSMENT_TYPE_REQUIRED'),
                subType: Joi.string()
                    .required()
                    .error(() => 'ASSESSMENT_SUB-TYPE_REQUIRED'),
                assessmentName: Joi.string()
                    .required()
                    .error(() => 'ASSESSMENT_NAME'),
            })
            .unknown(true),
    })
    .unknown(true);

exports.removeAssessmentValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _id: Joi.string().alphanum().length(24),
                type: Joi.string()
                    .required()
                    .error(() => 'ASSESSMENT_TYPE_REQUIRED'),
                subType: Joi.string()
                    .required()
                    .error(() => 'ASSESSMENT_SUB-TYPE_REQUIRED'),
                assessmentId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => 'ASSESSMENT_NAME'),
            })
            .unknown(true),
    })
    .unknown(true);

exports.toggleAssessmentTypeValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => 'INSTITUTION_ID_REQUIRED'),
                type: Joi.string()
                    .required()
                    .error(() => 'TYPE_REQUIRED'),
                subType: Joi.string()
                    .required()
                    .error(() => 'SUBTYPE_REQUIRED'),
            })
            .unknown(true),
        body: Joi.object()
            .keys({
                assessmentId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => 'ASSESSMENT_ID_REQUIRED'),
                isActive: Joi.string()
                    .required()
                    .error(() => 'ISACTIVE_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);
