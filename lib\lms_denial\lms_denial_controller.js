const {
    getUser<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist,
    getLateAutoAndManualRange,
    changePresentScheduleBasedLateConfigure,
    changePresentScheduleBasedLateConfigureForSingleStudent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    lmsNewSetting,
    getWarningDataFromRedis,
    getAutoRange,
} = require('../utility/utility.service');
const { convertToMongoObjectId } = require('../utility/common');
const programsModel = require('../models/digi_programs');
const studentLmsSettingModel = require('../lmsStudentSetting/lmsStudentSetting.model');
const studentLmsSettingCalendarModel = require('../lmsStudentSetting/lmsStudentSettingCalendar.model');
const { getPaginationValues } = require('../../commonService/utility/pagination');
const {
    LEAVE_TYPE,
    CUMULATIVE,
    INDIVIDUAL,
    LEAVE,
    COURSE_WISE,
    STUDENT_WISE,
    GLOBAL_WISE,
    EXCLUDE,
} = require('../utility/constants');
const courseScheduleModel = require('../models/course_schedule');
const institutionCalendarsModel = require('../models/institution_calendar');
const lmsDenialModel = require('./lms_denial_model');
const lmsDenialLogModel = require('./lms_denial_log_model');
const { nameFormatter } = require('../../commonService/utility/common');
const lmsStudentModel = require('../lmsStudent/lmsStudent.model');
const constants = require('../utility/constants');
const {
    getWarningSettingBasedCalendar,
} = require('../lmsStudentSetting/lmsStudentSetting.controller');
const {
    getAllCourseStudentsFromRedis,
    getParticularCourseStudentsFromRedis,
    getLevelsFromRedis,
    sortArrayByName,
    getDenialFromCache,
} = require('./lms_denial_service');
const roleAssignModel = require('../models/role_assign');
const { allProgramCalendarDatas } = require('../../service/cache.service');
const { PUBLISHED } = require('../utility/enums');
const warningMailModel = require('../models/warning_mail');
const studentWarningRecordSchema = require('../models/student_warning_records');
const { redisClient } = require('../../config/redis-connection');
const userSchema = require('../models/user');
exports.getUserProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { roleId, _institution_calendar_id, denialPercentage } = query;
        const { userProgramIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: roleId,
            institutionCalendarId: [_institution_calendar_id],
        });
        if (!userProgramIds.length) {
            return { message: 'success', data: [] };
        }
        const activePrograms = [];
        const programCalendar = await allProgramCalendarDatas();
        for (const programId of userProgramIds) {
            const programData = programCalendar.find(
                (programCalendarElement) =>
                    programCalendarElement._institution_calendar_id.toString() ===
                        _institution_calendar_id.toString() &&
                    programCalendarElement.status === 'published' &&
                    programCalendarElement._program_id.toString() === programId.toString(),
            );
            if (programData) {
                activePrograms.push(convertToMongoObjectId(programId));
            }
        }
        const programDocs = await programsModel
            .find(
                { _id: { $in: activePrograms } },
                { name: 1, code: 1, type: 1, degree: 1, program_type: 1 },
            )
            .lean();
        if (!programDocs.length) {
            return { message: 'NO_DATA_FOUND', data: [], statusCode: 200 };
        }
        return {
            message: 'SUCCESS',
            data: {
                programList: programDocs,
                denialPercentage,
            },
            statusCode: 200,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getProgramLevels = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { programId, _institution_calendar_id, roleId } = query;
        const programLevels = await getLevelsFromRedis(programId, _institution_calendar_id);
        if (!programLevels) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        const levelWiseCourses = [];
        const { userCourseIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: roleId,
            institutionCalendarId: [_institution_calendar_id],
        });
        for (levelElement of programLevels) {
            const { level_no, year, term, rotation, rotation_course, start_date, end_date } =
                levelElement;
            let { course, rotation_count } = levelElement;
            course = course.map((courseElement) => {
                return {
                    ...courseElement,
                    _course_id: courseElement._course_id._id,
                };
            });
            if (rotation === 'yes') {
                if (rotation_course && rotation_course.length) {
                    for (const rotationCourseElement of rotation_course) {
                        const { rotation_count: rotationCount, course: rCourses } =
                            rotationCourseElement;
                        rotation_count = rotationCount;
                        for (const courseElement of rCourses) {
                            course.push({
                                ...courseElement,
                                _course_id: courseElement._course_id._id,
                            });
                        }
                    }
                }
            }
            if (userCourseIds.length && course.length) {
                const validCourses = [];
                for (const courseIdElement of userCourseIds) {
                    const accessibleCourse = course.find(
                        (courseElement) =>
                            courseElement._course_id.toString() ===
                                courseIdElement._course_id.toString() &&
                            term === courseIdElement.term &&
                            level_no === courseIdElement.level_no,
                    );
                    if (accessibleCourse) {
                        validCourses.push(accessibleCourse);
                    }
                }
                course = validCourses;
            }
            if (course && course.length) {
                const students = await getAllCourseStudentsFromRedis({
                    programId,
                    year,
                    level: level_no,
                    term,
                    rotation,
                    rotationCount: rotation_count,
                    _institution_id,
                    _institution_calendar_id,
                });

                const totalStudents = [];
                if (students && students.length) {
                    for (const studentElement of students) {
                        if (!totalStudents.includes(studentElement.studentId.toString())) {
                            totalStudents.push(studentElement.studentId.toString());
                        }
                    }
                }
                const courseModel = course.reduce(
                    (acc, curr) => {
                        if (curr.model === 'standard') {
                            acc.standard++;
                        } else {
                            acc.selective++;
                        }
                        return acc;
                    },
                    { selective: 0, standard: 0 },
                );
                const courseDurationInMilliseconds =
                    new Date(end_date).getTime() - new Date(start_date).getTime();
                const mSecondsPerDay = 24 * 60 * 60 * 1000;
                const courseDurationInDays = courseDurationInMilliseconds / mSecondsPerDay;
                const courseDurationInWeeks = Math.ceil(courseDurationInDays / 7);
                levelWiseCourses.push({
                    level_no,
                    year,
                    term,
                    durationWeeks: courseDurationInWeeks,
                    start_date,
                    end_date,
                    programId,
                    rotation,
                    ...courseModel,
                    totalStudents: totalStudents.length,
                });
            }
        }
        const lmsStudentSettings = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                    'warningConfig.isActive': true,
                }, // leave type  is hardcoded
                {
                    'warningConfig.percentage': 1,
                    'warningConfig.isActive': 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: { levels: levelWiseCourses },
            };
        }
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        let { warningConfig } = lmsStudentSettings;
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig;
        let isViewMode = false;
        for (let i = warningConfig.length - 1; i >= 0; i--) {
            if (
                (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
                denialConfig == null
            ) {
                denialConfig = warningConfig[i];
            }
            if (!warningConfig[i].isActive) {
                warningConfig.splice(i, 1);
            }
        }
        if (!denialConfig.isActive) {
            isViewMode = true;
        }
        const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
            let comparison = 0;
            if (Number(a.percentage) > Number(b.percentage)) {
                comparison = -1;
            } else if (Number(a.percentage) < Number(b.percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: {
                levels: levelWiseCourses,
                denial: isViewMode
                    ? denialConfig.percentage
                    : reverseSortedWarningConfig[0].percentage,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCoursesFromProgramLevel = async ({ query = {}, headers = {} }) => {
    try {
        const {
            level_no,
            year,
            programId,
            term,
            pageNo,
            limit,
            searchKey,
            // rotation,
            roleId,
            _institution_calendar_id,
        } = query;
        const { _institution_id, _user_id } = headers;
        const levels = await getLevelsFromRedis(programId, _institution_calendar_id);
        if (!levels) {
            return { statusCode: 200, data: [] };
        }
        const level = levels.find(
            (levelElement) =>
                levelElement._program_id.toString() === programId.toString() &&
                levelElement.level_no === level_no &&
                levelElement.term === term &&
                levelElement.year === year,
            // levelElement.rotation === rotation &&
            // (rotation === 'yes'
            //     ? levelElement.rotation_course &&
            //       levelElement.rotation_course.filter(
            //           (levelElement) => levelElement.rotation_count === parseInt(rotationCount),
            //       ).length
            //     : true),
        );
        if (level && level.rotation === 'yes') {
            //&& rotation === 'yes' && level.rotation_course && level.rotation_course.length
            for (const rotationCourses of level.rotation_course) {
                for (const courseElement of rotationCourses.course) {
                    level.course.push({
                        ...courseElement,
                        rotation: level.rotation,
                        rotation_count: rotationCourses.rotation_count,
                        year: level.year,
                        term: year.term,
                        level_no: level.level_no,
                    });
                }
            }
        }
        let courses = [];
        if (searchKey) {
            for (const courseElement of level.course) {
                const regex = new RegExp(searchKey, 'gi');
                if (
                    regex.test(courseElement.courses_name) ||
                    regex.test(courseElement.courses_number)
                ) {
                    courses.push({
                        versionNo: courseElement._course_id.versionNo || 1,
                        versioned: courseElement._course_id.versioned || false,
                        versionName: courseElement._course_id.versionName || '',
                        versionedFrom: courseElement._course_id.versionedFrom || null,
                        versionedCourseIds: courseElement._course_id.versionedCourseIds || [],
                        _course_id: courseElement._course_id._id,
                        course_name: courseElement.courses_name,
                        course_number: courseElement.courses_number,
                        model: courseElement.model,
                        rotation: courseElement.rotation,
                        rotation_count: courseElement.rotation_count,
                        term: level.term,
                        year: level.year,
                        denialStudents: [],
                    });
                }
            }
        } else {
            courses = level.course.map((courseElement) => ({
                versionNo: courseElement._course_id.versionNo || 1,
                versioned: courseElement._course_id.versioned || false,
                versionName: courseElement._course_id.versionName || '',
                versionedFrom: courseElement._course_id.versionedFrom || null,
                versionedCourseIds: courseElement._course_id.versionedCourseIds || [],
                _course_id: courseElement._course_id._id,
                course_name: courseElement.courses_name,
                course_number: courseElement.courses_number,
                model: courseElement.model,
                rotation: courseElement.rotation || 'no',
                rotation_count: courseElement.rotation === 'yes' ? courseElement.rotation_count : 0,
                term: level.term,
                year: level.year,
                denialStudents: [],
            }));
        }
        const { userCourseIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: roleId,
            institutionCalendarId: [_institution_calendar_id],
        });
        if (userCourseIds.length) {
            courses = courses.filter((courseElement) =>
                userCourseIds.find(
                    (courseIdElement) =>
                        courseIdElement._course_id.toString() ===
                        courseElement._course_id.toString(),
                ),
            );
        }
        const courseIds = courses.map((courseIdElement) =>
            convertToMongoObjectId(courseIdElement._course_id),
        );
        const courseStudentsStrength = await getParticularCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            term,
            _institution_calendar_id,
            _institution_id,
            courseIds: courseIds.map((courseIdElement) => courseIdElement.toString()),
        });
        for (courseIdElement of courses) {
            const studentsCount = courseStudentsStrength.filter(
                (courseElement) =>
                    courseElement._course_id &&
                    courseElement._course_id.toString() === courseIdElement._course_id.toString() &&
                    (courseElement.rotation === 'yes'
                        ? courseIdElement.rotation_count === courseElement.rotation_count
                        : true) &&
                    courseElement.term === courseIdElement.term,
            ).length;
            courseIdElement.totalStudents = studentsCount;
        }
        const lmsStudentSettings = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                    'warningConfig.isActive': true,
                }, // leave type  is hardcoded
                {
                    'warningConfig.percentage': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.categoryWisePercentage.categoryName': 1,
                    'warningConfig.categoryWisePercentage.categoryId': 1,
                    'warningConfig.categoryWisePercentage.percentage': 1,
                    'warningConfig.unappliedLeaveConsideredAs': 1,
                    'warningConfig.denialCondition': 1,
                    leaveCalculation: 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.isActive': 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const { leaveCalculation } = lmsStudentSettings;
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        let { warningConfig } = lmsStudentSettings;
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig;
        for (let i = warningConfig.length - 1; i >= 0; i--) {
            if (
                (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
                denialConfig == null
            ) {
                denialConfig = warningConfig[i];
            }
            if (!warningConfig[i].isActive) {
                warningConfig.splice(i, 1);
            }
        }
        const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
            let comparison = 0;
            if (Number(a.percentage) > Number(b.percentage)) {
                comparison = -1;
            } else if (Number(a.percentage) < Number(b.percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId: courseIds,
            programId,
            yearNo: year,
            levelNo: level_no,
            term,
        });
        // const pagination = getPaginationValues({ limit, pageNo });
        let courseSchedules;
        if (leaveCalculation === 'hours') {
            courseSchedules = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _program_id: convertToMongoObjectId(programId),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        year_no: year,
                        level_no,
                        _course_id: { $in: courseIds },
                        type: 'regular',
                        term,
                        // rotation,
                        // ...(rotation === 'yes' && { rotation_count: rotationCount }),
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $addFields: {
                        totalStudents: { $size: '$students' },
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $group: {
                        _id: {
                            studentId: '$students._id',
                            courseId: '$_course_id',
                            sessionType: '$session.session_type',
                        },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$status', 'completed'] },
                                            { $not: { $eq: ['$students.status', EXCLUDE] } },
                                        ],
                                    },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        studentName: { $first: '$students.name' },
                        course_name: { $first: '$course_name' },
                        _course_id: { $first: '$_course_id' },
                        course_code: { $first: '$course_code' },
                        rotation: { $first: '$rotation' },
                        rotation_count: { $first: '$rotation_count' },
                        totalSchedules: {
                            $sum: {
                                $cond: [{ $not: { $eq: ['$students.status', EXCLUDE] } }, 1, 0],
                            },
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$status', 'completed'] },
                                            { $not: { $eq: ['$students.status', EXCLUDE] } },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        unAppliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'absent'] },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'leave'],
                                            },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        let: { courseId: '$_id.courseId', sessionType: '$_id.sessionType' },
                        pipeline: [
                            { $unwind: '$credit_hours' },
                            { $unwind: '$credit_hours.delivery_type' },
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$_id', '$$courseId'] },
                                            {
                                                $eq: [
                                                    '$credit_hours.delivery_type.delivery_type',
                                                    '$$sessionType',
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    'credit_hours.delivery_type.duration': 1,
                                },
                            },
                        ],
                        as: 'courseCreditHours',
                    },
                },
                {
                    $addFields: {
                        sessionCreditHours: {
                            $divide: [
                                {
                                    $arrayElemAt: [
                                        '$courseCreditHours.credit_hours.delivery_type.duration',
                                        0,
                                    ],
                                },
                                60,
                            ],
                        },
                    },
                },
                {
                    $addFields: {
                        totalSchedules: {
                            $multiply: ['$totalSchedules', '$sessionCreditHours'],
                        },
                        totalCompletedSchedules: {
                            $multiply: ['$totalCompletedSchedules', '$sessionCreditHours'],
                        },
                        leavedSchedules: {
                            $multiply: ['$leavedSchedules', '$sessionCreditHours'],
                        },
                        presentedSchedules: {
                            $multiply: ['$presentedSchedules', '$sessionCreditHours'],
                        },
                    },
                },
                {
                    $project: {
                        sessionType: '$_id.sessionType',
                        rotation: '$rotation',
                        rotation_count: '$rotation_count',
                        studentId: '$_id.studentId',
                        studentName: '$studentName',
                        sessionCreditHours: '$sessionCreditHours',
                        courseId: '$_course_id',
                        totalSchedules: '$totalSchedules',
                        totalCompletedSchedules: '$totalCompletedSchedules',
                        leavedSchedules: '$leavedSchedules',
                        presentedSchedules: '$presentedSchedules',
                        course_name: '$course_name',
                        course_code: '$course_code',
                        absentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalSchedules', 0] },
                                        0,
                                        { $divide: ['$leavedSchedules', '$totalSchedules'] },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$data.totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$data.presentedSchedules',
                                                '$data.totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        unAppliedLeaves: '$unAppliedLeaves',
                        appliedLeaves: '$appliedLeaves',
                        scheduled_dates: '$scheduled_dates',
                    },
                },
                {
                    $unwind: {
                        path: '$scheduled_dates',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $group: {
                        _id: {
                            studentId: '$studentId',
                            courseId: '$courseId',
                        },
                        sessionType: { $push: '$sessionType' },
                        rotation: { $first: '$rotation' },
                        rotation_count: { $first: '$rotation_count' },
                        studentId: { $first: '$studentId' },
                        studentName: { $first: '$studentName' },
                        sessionCreditHours: { $push: '$sessionCreditHours' },
                        courseId: { $first: '$courseId' },
                        totalSchedules: { $sum: '$totalSchedules' },
                        totalCompletedSchedules: { $sum: '$totalCompletedSchedules' },
                        leavedSchedules: { $sum: '$leavedSchedules' },
                        presentedSchedules: { $sum: '$presentedSchedules' },
                        course_name: { $first: '$course_name' },
                        course_code: { $first: '$course_code' },
                        unAppliedLeaves: { $sum: '$unAppliedLeaves' },
                        appliedLeaves: { $sum: '$appliedLeaves' },
                        scheduled_dates: { $push: '$scheduled_dates' },
                    },
                },
                {
                    $addFields: {
                        absentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalSchedules', 0] },
                                        0,
                                        { $divide: ['$leavedSchedules', '$totalSchedules'] },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$presentedSchedules',
                                                '$totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                    },
                },
                // {
                //     $match: {
                //         ...(reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                //             CUMULATIVE && {
                //             absentPercentage: {
                //                 $gt: reverseSortedWarningConfig[0].percentage,
                //             },
                //         }),
                //     },
                // },
                {
                    $lookup: {
                        from: constants.USER,
                        localField: 'studentId',
                        foreignField: '_id',
                        as: 'user',
                    },
                },
                {
                    $group: {
                        _id: {
                            courseId: '$courseId',
                            rotation_count: '$rotation_count',
                            rotation: '$rotation',
                        },
                        data: {
                            $push: {
                                studentId: '$studentId',
                                rotation: '$rotation',
                                scheduled_dates: '$scheduled_dates',
                                rotation_count: '$rotation_count',
                                studentName: '$studentName',
                                absentPercentage: '$absentPercentage',
                                presentPercentage: '$presentPercentage',
                                totalSchedules: '$totalSchedules',
                                totalCompletedSchedules: '$totalCompletedSchedules',
                                appliedLeaves: '$appliedLeaves',
                                unAppliedLeaves: '$unAppliedLeaves',
                                academicId: { $arrayElemAt: ['$user.user_id', 0] },
                            },
                        },
                    },
                },
                // {
                //     $sort: { _id: 1 },
                // },
                // {
                //     $skip: pagination.skip,
                // },
                // {
                //     $limit: pagination.limit,
                // },
            ]);
        } else {
            courseSchedules = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        year_no: year,
                        level_no,
                        _program_id: convertToMongoObjectId(programId),
                        _course_id: { $in: courseIds },
                        type: 'regular',
                        term,
                        // rotation,
                        // ...(rotation === 'yes' && { rotation_count: rotationCount }),
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $addFields: {
                        totalStudents: { $size: '$students' },
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $group: {
                        _id: { studentId: '$students._id', courseId: '$_course_id' },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$status', 'completed'] },
                                            { $not: { $eq: ['$students.status', EXCLUDE] } },
                                        ],
                                    },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        schedules: {
                            $push: {
                                students: '$students',
                                status: '$status',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                sessionDetail: '$sessionDetail',
                                _institution_calendar_id: '$_institution_calendar_id',
                                programId: '$_program_id',
                                courseId: '$_course_id',
                                term: '$term',
                                levelNo: '$level_no',
                                rotationCount: '$rotation_count',
                                session: '$session',
                            },
                        },
                        studentName: { $first: '$students.name' },
                        course_name: { $first: '$course_name' },
                        _course_id: { $first: '$_course_id' },
                        course_code: { $first: '$course_code' },
                        rotation: { $first: '$rotation' },
                        rotation_count: { $first: '$rotation_count' },
                        totalSchedules: {
                            $sum: {
                                $cond: [{ $not: { $eq: ['$students.status', EXCLUDE] } }, 1, 0],
                            },
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$status', 'completed'] },
                                            { $not: { $eq: ['$students.status', EXCLUDE] } },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedulesWithoutOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'present'] },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedulesBasedOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        unAppliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'absent'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'leave'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $project: {
                        studentId: '$_id.studentId',
                        studentName: '$studentName',
                        schedules: '$schedules',
                        rotation: '$rotation',
                        scheduled_dates: '$scheduled_dates',
                        rotation_count: '$rotation_count',
                        courseId: '$_course_id',
                        totalSchedules: '$totalSchedules',
                        totalCompletedSchedules: '$totalCompletedSchedules',
                        course_name: '$course_name',
                        course_code: '$course_code',
                        absentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalSchedules', 0] },
                                        0,
                                        { $divide: ['$leavedSchedules', '$totalSchedules'] },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$presentedSchedules',
                                                '$totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentedSchedulesWithoutOnDuty: '$presentedSchedulesWithoutOnDuty',
                        presentedSchedulesBasedOnDuty: '$presentedSchedulesBasedOnDuty',
                        leavedSchedules: '$leavedSchedules',
                        unAppliedLeaves: '$unAppliedLeaves',
                        appliedLeaves: '$appliedLeaves',
                    },
                },
                // {
                //     $match: {
                //         ...(reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                //             CUMULATIVE && {
                //             absentPercentage: {
                //                 $gt: reverseSortedWarningConfig[0].percentage,
                //             },
                //         }),
                //     },
                // },
                {
                    $lookup: {
                        from: constants.USER,
                        localField: 'studentId',
                        foreignField: '_id',
                        as: 'user',
                    },
                },
                {
                    $group: {
                        _id: {
                            courseId: '$courseId',
                            rotation_count: '$rotation_count',
                            rotation: '$rotation',
                        },
                        data: {
                            $push: {
                                studentId: '$studentId',
                                schedules: '$schedules',
                                studentName: '$studentName',
                                rotation: '$rotation',
                                scheduled_dates: '$scheduled_dates',
                                rotation_count: '$rotation_count',
                                absentPercentage: '$absentPercentage',
                                presentPercentage: '$presentPercentage',
                                totalSchedules: '$totalSchedules',
                                appliedLeaves: '$appliedLeaves',
                                unAppliedLeaves: '$unAppliedLeaves',
                                totalCompletedSchedules: '$totalCompletedSchedules',
                                presentedSchedulesWithoutOnDuty: '$presentedSchedulesWithoutOnDuty',
                                presentedSchedulesBasedOnDuty: '$presentedSchedulesBasedOnDuty',
                                leavedSchedules: '$leavedSchedules',
                                academicId: { $arrayElemAt: ['$user.user_id', 0] },
                            },
                        },
                    },
                },
                // {
                //     $sort: { _id: 1 },
                // },
                // {
                //     $skip: pagination.skip,
                // },
                // {
                //     $limit: pagination.limit,
                // },
            ]);
        }
        await changePresentScheduleBasedLateConfigure({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules,
            lateExcludeManagement,
        });
        const mixedDenialStudents = await lmsDenialModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    programId: convertToMongoObjectId(programId),
                    courseId: { $in: courseIds },
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    denialCondition: 1,
                    absencePercentage: 1,
                    studentId: 1,
                    courseId: 1,
                    rotation: 1,
                    term: 1,
                    rotationCount: 1,
                    typeWiseUpdate: 1,
                    'categoryWisePercentage.categoryName': 1,
                    'categoryWisePercentage.categoryId': 1,
                    'categoryWisePercentage.percentage': 1,
                },
            )
            .sort({ updatedAt: -1 })
            .lean();
        if (!courseSchedules || !courseSchedules.length) {
            for (const studentCourse of courses) {
                const isMixedStudent = mixedDenialStudents.find(
                    (denialStudentElement) =>
                        (studentCourse.rotation === 'yes'
                            ? parseInt(studentCourse.rotation_count) ===
                              denialStudentElement.rotationCount
                            : true) &&
                        denialStudentElement.term === term &&
                        denialStudentElement.courseId.toString() ===
                            studentCourse._course_id.toString(),
                );
                const isMixedStudentForCourseFlow = mixedDenialStudents.find(
                    (denialStudentElement) =>
                        (studentCourse.rotation === 'yes'
                            ? parseInt(studentCourse.rotation_count) ===
                              denialStudentElement.rotationCount
                            : true) &&
                        denialStudentElement.term === term &&
                        denialStudentElement.courseId.toString() ===
                            studentCourse._course_id.toString() &&
                        denialStudentElement.typeWiseUpdate === COURSE_WISE,
                );
                if (isMixedStudentForCourseFlow) {
                    studentCourse.courseWisePercentage =
                        isMixedStudentForCourseFlow.absencePercentage;
                }
                if (isMixedStudent && isMixedStudent.typeWiseUpdate === STUDENT_WISE) {
                    if (
                        isMixedStudent.absencePercentage !==
                        (isMixedStudentForCourseFlow
                            ? isMixedStudentForCourseFlow.absencePercentage
                            : reverseSortedWarningConfig[0].percentage)
                    ) {
                        studentCourse.status = 'mixed';
                        studentCourse.denialPercentage = 'mixed';
                    } else {
                        studentCourse.denialPercentage = isMixedStudentForCourseFlow
                            ? isMixedStudentForCourseFlow.absencePercentage
                            : reverseSortedWarningConfig[0].percentage;
                        studentCourse.categoryWisePercentage = isMixedStudentForCourseFlow
                            ? isMixedStudentForCourseFlow.categoryWisePercentage
                            : reverseSortedWarningConfig[0].categoryWisePercentage;
                    }
                    studentCourse.manipulated = true;
                    if (
                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() === INDIVIDUAL
                    ) {
                        if (studentCourse.absentDetails) {
                            let isDenial;
                            for (const categoryElement of studentCourse.absentDetails) {
                                const categoryPercentage =
                                    isMixedStudent.categoryWisePercentage.find(
                                        (percentageElement) =>
                                            percentageElement.categoryName.toLowerCase() ===
                                            categoryElement.categoryName.toLowerCase(),
                                    );
                                if (
                                    categoryPercentage &&
                                    Number(categoryElement.percentage) >
                                        Number(categoryPercentage.percentage)
                                ) {
                                    isDenial = true;
                                } else {
                                    studentCourse.manipulated = true;
                                }
                            }
                            if (isDenial) {
                                delete studentCourse.manipulated;
                            }
                        }
                    }
                    studentCourse.mixedConfig = {
                        absencePercentage: isMixedStudent.absencePercentage,
                        categoryWisePercentage: isMixedStudent.categoryWisePercentage,
                        denialCondition: isMixedStudent.denialCondition,
                    };
                } else if (isMixedStudent && isMixedStudent.typeWiseUpdate === COURSE_WISE) {
                    //for check if overall course denial percentage is changed
                    studentCourse.denialPercentage = isMixedStudent.absencePercentage;
                    studentCourse.courseWisePercentage = isMixedStudent.absencePercentage;
                    studentCourse.categoryWisePercentage =
                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                            INDIVIDUAL && isMixedStudent.categoryWisePercentage;
                    if (
                        Number(studentCourse.absentPercentage) >
                        Number(isMixedStudent.absencePercentage)
                    ) {
                        studentCourse.status = reverseSortedWarningConfig[0].labelName;
                    } else {
                        studentCourse.manipulated = true;
                    }
                    if (
                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() === INDIVIDUAL
                    ) {
                        if (studentCourse.absentDetails) {
                            let isDenial;
                            for (const categoryElement of studentCourse.absentDetails) {
                                const categoryPercentage =
                                    isMixedStudent.categoryWisePercentage.find(
                                        (percentageElement) =>
                                            percentageElement.categoryName.toLowerCase() ===
                                            categoryElement.categoryName.toLowerCase(),
                                    );
                                if (
                                    categoryPercentage &&
                                    Number(categoryElement.percentage) >
                                        Number(categoryPercentage.percentage)
                                ) {
                                    isDenial = true;
                                } else {
                                    studentCourse.manipulated = true;
                                }
                            }
                            if (isDenial) {
                                delete studentCourse.manipulated;
                            }
                            studentCourse.status = isDenial
                                ? reverseSortedWarningConfig[0].labelName
                                : reverseSortedWarningConfig[1].labelName;
                        }
                    }
                    studentCourse.mixedConfig = {
                        absencePercentage: isMixedStudent.absencePercentage,
                        categoryWisePercentage: isMixedStudent.categoryWisePercentage,
                        denialCondition: isMixedStudent.denialCondition,
                    };
                } else {
                    if (
                        Number(studentCourse.absentPercentage) >
                        Number(reverseSortedWarningConfig[0].percentage)
                    ) {
                        studentCourse.status = reverseSortedWarningConfig[0].labelName;
                    }
                    if (
                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() === INDIVIDUAL
                    ) {
                        if (studentCourse.absentDetails) {
                            let isDenial;
                            for (const categoryElement of studentCourse.absentDetails) {
                                const categoryPercentage =
                                    reverseSortedWarningConfig[0].categoryWisePercentage.find(
                                        (percentageElement) =>
                                            percentageElement.categoryName.toLowerCase() ===
                                            categoryElement.categoryName.toLowerCase(),
                                    );
                                if (
                                    categoryPercentage &&
                                    Number(categoryElement.percentage) >
                                        Number(categoryPercentage.percentage)
                                ) {
                                    isDenial = true;
                                } else {
                                    studentCourse.manipulated = true;
                                }
                            }
                            studentCourse.status = isDenial
                                ? reverseSortedWarningConfig[0].labelName
                                : reverseSortedWarningConfig[1]
                                ? reverseSortedWarningConfig[1].labelName
                                : '';
                            if (isDenial) {
                                delete studentCourse.manipulated;
                            }
                        }
                    }
                }
            }
            return {
                statusCode: 200,
                data: {
                    courses,
                    denialPercentage: denialConfig.isActive
                        ? reverseSortedWarningConfig[0].percentage
                        : denialConfig.percentage,
                    categoryWisePercentage: denialConfig.isActive
                        ? reverseSortedWarningConfig[0].categoryWisePercentage
                        : denialConfig.categoryWisePercentage,
                    denialCondition: reverseSortedWarningConfig[0].denialCondition.toLowerCase(),
                },
                message: 'no matched schedules',
            };
        }
        const denialStudentIds = [];
        const denialCourseIds = [];

        for (const courseScheduleElement of courseSchedules) {
            denialCourseIds.push(convertToMongoObjectId(courseScheduleElement._id.courseId));
            denialStudentIds.push(
                ...courseScheduleElement.data.map((studentElement) =>
                    convertToMongoObjectId(studentElement.studentId),
                ),
            );
        }
        const lmsStudent = await lmsStudentModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    studentId: {
                        $in: denialStudentIds,
                    },
                    classificationType: LEAVE_TYPE.LEAVE,
                    programId: convertToMongoObjectId(programId),
                    level: level_no,
                    year,
                    approvalStatus: constants.APPROVED,
                    isDeleted: false,
                    isActive: true,
                },
                { categoryName: 1, studentId: 1, noOfHours: 1, dateAndTimeRange: 1 },
            )
            .lean();
        for (const scheduleElement of courseSchedules) {
            const studentCourse = courses.find(
                (levelElement) =>
                    scheduleElement._id.courseId.toString() ===
                        levelElement._course_id.toString() &&
                    term === levelElement.term &&
                    (scheduleElement._id.rotation === 'yes'
                        ? scheduleElement._id.rotation_count === levelElement.rotation_count
                        : true),
            );
            if (studentCourse) {
                studentCourse.isScheduled = true;
                const isCourseHaveDenialStudents = scheduleElement.data.filter(
                    (studentElement) =>
                        studentElement.absentPercentage > reverseSortedWarningConfig[0].percentage,
                );
                const isDenialChangedForThisCourse = mixedDenialStudents.filter(
                    (denialElement) =>
                        denialElement.courseId.toString() ===
                            scheduleElement._id.courseId.toString() &&
                        (scheduleElement._id.rotation === 'yes'
                            ? denialElement.rotationCount === scheduleElement._id.rotation_count
                            : true),
                );

                if (isCourseHaveDenialStudents.length) {
                    scheduleElement.data = isCourseHaveDenialStudents.filter(
                        (denialStudentElement) =>
                            courseStudentsStrength.find(
                                (sgElement) =>
                                    sgElement.studentId.toString() ===
                                        denialStudentElement.studentId.toString() &&
                                    sgElement._course_id &&
                                    scheduleElement._id.courseId.toString() ===
                                        sgElement._course_id.toString() &&
                                    (scheduleElement._id.rotation === 'yes'
                                        ? scheduleElement._id.rotation_count ===
                                          sgElement.rotation_count
                                        : true),
                            ),
                    );
                    const denialStudents = [];
                    const courseStatus = {};
                    const denialStudentsWithLeave = lmsStudent.reduce((acc, curr) => {
                        const studentFromCourseSchedules = scheduleElement.data.find(
                            (courseStudentElement) =>
                                curr.studentId.toString() ===
                                courseStudentElement.studentId.toString(),
                        );
                        if (
                            studentFromCourseSchedules &&
                            studentFromCourseSchedules.scheduled_dates
                        ) {
                            const isScheduleAffectedByLeave =
                                studentFromCourseSchedules.scheduled_dates.find(
                                    (scheduleStartDateElement) =>
                                        scheduleStartDateElement &&
                                        new Date(scheduleStartDateElement) >=
                                            new Date(curr.dateAndTimeRange.startDate) &&
                                        new Date(scheduleStartDateElement) <=
                                            new Date(curr.dateAndTimeRange.endDate),
                                );
                            if (isScheduleAffectedByLeave) {
                                if (acc[curr.studentId]) {
                                    if (acc[curr.studentId][curr.categoryName.toLowerCase()]) {
                                        acc[curr.studentId][curr.categoryName.toLowerCase()] += 1;
                                    } else {
                                        acc[curr.studentId][curr.categoryName.toLowerCase()] = 1;
                                    }
                                } else {
                                    acc[curr.studentId] = {
                                        [curr.categoryName.toLowerCase()]: 1,
                                    };
                                }
                            }
                        }
                        return acc;
                    }, {});
                    for (const studentElement of scheduleElement.data) {
                        if (studentElement.studentId) {
                            //check here rotation and rotation count
                            const isStudentMatchedWithRotation = courses.find((courseElement) =>
                                studentElement.rotation === 'yes'
                                    ? studentElement.rotation_count === courseElement.rotation_count
                                    : true,
                            );
                            if (isStudentMatchedWithRotation) {
                                if (studentElement.unAppliedLeaves > 0) {
                                    if (reverseSortedWarningConfig[0].unappliedLeaveConsideredAs) {
                                        if (
                                            !denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ]
                                        ) {
                                            denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ] = {};
                                        }
                                        if (
                                            denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ][
                                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                            ]
                                        ) {
                                            denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ][
                                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                            ] += studentElement.unAppliedLeaves;
                                        } else {
                                            denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ][
                                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                            ] = studentElement.unAppliedLeaves;
                                        }
                                    }
                                }
                                if (
                                    denialStudentsWithLeave[studentElement.studentId.toString()] &&
                                    reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                ) {
                                    const absentDetails = Object.keys(
                                        denialStudentsWithLeave[
                                            studentElement.studentId.toString()
                                        ],
                                    ).reduce((acc, curr) => {
                                        const absentDetailObject = {
                                            categoryName: curr.toLowerCase(),
                                            percentage:
                                                (denialStudentsWithLeave[
                                                    studentElement.studentId.toString()
                                                ][curr] /
                                                    studentElement.totalSchedules) *
                                                100,
                                        };
                                        acc.push(absentDetailObject);
                                        return acc;
                                    }, []);
                                    studentElement.absentDetails = absentDetails;
                                }
                                const isMixedStudent = mixedDenialStudents.find(
                                    (denialStudentElement) =>
                                        (studentElement.rotation === 'yes'
                                            ? parseInt(studentElement.rotation_count) ===
                                              denialStudentElement.rotationCount
                                            : true) &&
                                        denialStudentElement.term === term &&
                                        denialStudentElement.courseId.toString() ===
                                            scheduleElement._id.courseId.toString() &&
                                        (denialStudentElement.studentId
                                            ? studentElement.studentId.toString() ===
                                              denialStudentElement.studentId.toString()
                                            : denialStudentElement.typeWiseUpdate === COURSE_WISE),
                                );
                                const isMixedStudentForCourseFlow = mixedDenialStudents.find(
                                    (denialStudentElement) =>
                                        denialStudentElement.courseId.toString() ===
                                            scheduleElement._id.courseId.toString() &&
                                        denialStudentElement.typeWiseUpdate === COURSE_WISE &&
                                        (studentElement.rotation === 'yes'
                                            ? parseInt(studentElement.rotation_count) ===
                                              denialStudentElement.rotationCount
                                            : true),
                                );
                                if (
                                    isMixedStudent &&
                                    isMixedStudent.typeWiseUpdate === STUDENT_WISE
                                ) {
                                    if (
                                        isMixedStudent.absencePercentage !==
                                        ((isMixedStudentForCourseFlow &&
                                            isMixedStudentForCourseFlow.absencePercentage) ||
                                            reverseSortedWarningConfig[0].percentage)
                                    ) {
                                        courseStatus.status = 'mixed';
                                        courseStatus.denialPercentage = isMixedStudentForCourseFlow
                                            ? isMixedStudentForCourseFlow.absencePercentage
                                            : reverseSortedWarningConfig[0].percentage;
                                    }
                                    if (
                                        Number(studentElement.absentPercentage) >
                                        Number(isMixedStudent.absencePercentage)
                                    ) {
                                        studentElement.status =
                                            reverseSortedWarningConfig[0].labelName;
                                    } else {
                                        studentElement.manipulated = true;
                                    }
                                    if (
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                    ) {
                                        if (studentElement.absentDetails) {
                                            let isDenial;
                                            for (const categoryElement of studentElement.absentDetails) {
                                                const categoryPercentage =
                                                    isMixedStudent.categoryWisePercentage.find(
                                                        (percentageElement) =>
                                                            percentageElement.categoryName.toLowerCase() ===
                                                            categoryElement.categoryName.toLowerCase(),
                                                    );
                                                if (
                                                    categoryPercentage &&
                                                    Number(categoryElement.percentage) >
                                                        Number(categoryPercentage.percentage)
                                                ) {
                                                    isDenial = true;
                                                } else {
                                                    studentElement.manipulated = true;
                                                }
                                            }
                                            if (isDenial) {
                                                delete studentElement.manipulated;
                                            }
                                            studentElement.status = isDenial
                                                ? reverseSortedWarningConfig[0].labelName
                                                : reverseSortedWarningConfig[1].labelName;
                                        }
                                    }
                                    studentElement.mixedConfig = {
                                        absencePercentage: isMixedStudent.absencePercentage,
                                        categoryWisePercentage:
                                            isMixedStudent.categoryWisePercentage,
                                        denialCondition: isMixedStudent.denialCondition,
                                    };
                                    if (
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                    ) {
                                        if (
                                            studentElement.absentPercentage >
                                                ((isMixedStudentForCourseFlow &&
                                                    isMixedStudentForCourseFlow.absencePercentage) ||
                                                    reverseSortedWarningConfig[0].percentage) ||
                                            studentElement.status ===
                                                reverseSortedWarningConfig[0].labelName ||
                                            studentElement.manipulated
                                        ) {
                                            if (denialConfig ? denialConfig.isActive : true)
                                                denialStudents.push(studentElement);
                                        }
                                    } else {
                                        if (denialConfig ? denialConfig.isActive : true)
                                            denialStudents.push(studentElement);
                                    }
                                } else if (
                                    isMixedStudent &&
                                    isMixedStudent.typeWiseUpdate === COURSE_WISE
                                ) {
                                    //for check if overall course denial percentage is changed
                                    courseStatus.denialPercentage =
                                        isMixedStudent.absencePercentage;
                                    courseStatus.categoryWisePercentage =
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                            INDIVIDUAL && isMixedStudent.categoryWisePercentage;
                                    if (
                                        Number(studentElement.absentPercentage) >
                                        Number(isMixedStudent.absencePercentage)
                                    ) {
                                        studentElement.status =
                                            reverseSortedWarningConfig[0].labelName;
                                    } else {
                                        studentElement.manipulated = true;
                                    }
                                    if (
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                    ) {
                                        if (studentElement.absentDetails) {
                                            let isDenial;
                                            for (const categoryElement of studentElement.absentDetails) {
                                                const categoryPercentage =
                                                    isMixedStudent.categoryWisePercentage.find(
                                                        (percentageElement) =>
                                                            percentageElement.categoryName.toLowerCase() ===
                                                            categoryElement.categoryName.toLowerCase(),
                                                    );
                                                if (
                                                    categoryPercentage &&
                                                    Number(categoryElement.percentage) >
                                                        Number(categoryPercentage.percentage)
                                                ) {
                                                    isDenial = true;
                                                } else {
                                                    studentElement.manipulated = true;
                                                }
                                            }
                                            if (isDenial) {
                                                delete studentElement.manipulated;
                                            }
                                            studentElement.status = isDenial
                                                ? reverseSortedWarningConfig[0].labelName
                                                : reverseSortedWarningConfig[1].labelName;
                                        }
                                    }
                                    studentElement.mixedConfig = {
                                        absencePercentage: isMixedStudent.absencePercentage,
                                        categoryWisePercentage:
                                            isMixedStudent.categoryWisePercentage,
                                        denialCondition: isMixedStudent.denialCondition,
                                    };
                                    if (
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                    ) {
                                        if (
                                            studentElement.absentPercentage >
                                                ((isMixedStudentForCourseFlow &&
                                                    isMixedStudentForCourseFlow.absencePercentage) ||
                                                    reverseSortedWarningConfig[0].percentage) ||
                                            studentElement.status ===
                                                reverseSortedWarningConfig[0].labelName ||
                                            studentElement.manipulated
                                        ) {
                                            if (denialConfig ? denialConfig.isActive : true)
                                                denialStudents.push(studentElement);
                                        }
                                    } else {
                                        if (denialConfig ? denialConfig.isActive : true)
                                            denialStudents.push(studentElement);
                                    }
                                } else {
                                    if (
                                        Number(studentElement.absentPercentage) >
                                        Number(reverseSortedWarningConfig[0].percentage)
                                    ) {
                                        studentElement.status =
                                            reverseSortedWarningConfig[0].labelName;
                                    }
                                    if (
                                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                        INDIVIDUAL
                                    ) {
                                        if (studentElement.absentDetails) {
                                            let isDenial;
                                            for (const categoryElement of studentElement.absentDetails) {
                                                const categoryPercentage =
                                                    reverseSortedWarningConfig[0].categoryWisePercentage.find(
                                                        (percentageElement) =>
                                                            percentageElement.categoryName.toLowerCase() ===
                                                            categoryElement.categoryName.toLowerCase(),
                                                    );
                                                if (
                                                    categoryPercentage &&
                                                    Number(categoryElement.percentage) >
                                                        Number(categoryPercentage.percentage)
                                                ) {
                                                    isDenial = true;
                                                } else {
                                                    studentElement.manipulated = true;
                                                }
                                            }
                                            studentElement.status = isDenial
                                                ? reverseSortedWarningConfig[0].labelName
                                                : reverseSortedWarningConfig[1]
                                                ? reverseSortedWarningConfig[1].labelName
                                                : '';
                                            if (isDenial) {
                                                delete studentElement.manipulated;
                                            }
                                        }
                                        if (
                                            studentElement.absentPercentage >
                                                ((isMixedStudentForCourseFlow &&
                                                    isMixedStudentForCourseFlow.absencePercentage) ||
                                                    reverseSortedWarningConfig[0].percentage) ||
                                            studentElement.status ===
                                                reverseSortedWarningConfig[0].labelName ||
                                            studentElement.manipulated
                                        ) {
                                            if (denialConfig ? denialConfig.isActive : true)
                                                denialStudents.push(studentElement);
                                        }
                                    } else {
                                        if (denialConfig ? denialConfig.isActive : true)
                                            denialStudents.push(studentElement);
                                    }
                                }
                                delete studentElement.totalSchedules;
                                delete studentElement.appliedLeaves;
                                delete studentElement.unAppliedLeaves;
                            }
                        }
                    }
                    studentCourse.denialStudents = sortArrayByName(denialStudents);
                    studentCourse.categoryWisePercentage =
                        reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                            INDIVIDUAL && courseStatus.categoryWisePercentage;
                    studentCourse.denialPercentage =
                        courseStatus.status || courseStatus.denialPercentage;
                    studentCourse.courseWisePercentage = courseStatus.denialPercentage;
                }
                if (isDenialChangedForThisCourse.length) {
                    const courseWiseChanges = isDenialChangedForThisCourse.find(
                        (denialElement) => denialElement.typeWiseUpdate === COURSE_WISE,
                    );
                    studentCourse.courseWisePercentage =
                        courseWiseChanges && courseWiseChanges.absencePercentage;
                    studentCourse.denialPercentage =
                        isDenialChangedForThisCourse[0].typeWiseUpdate === STUDENT_WISE
                            ? isDenialChangedForThisCourse[0].absencePercentage !==
                              ((courseWiseChanges && courseWiseChanges.absencePercentage) ||
                                  reverseSortedWarningConfig[0].percentage)
                                ? 'mixed'
                                : (courseWiseChanges && courseWiseChanges.absencePercentage) ||
                                  reverseSortedWarningConfig[0].percentage
                            : isDenialChangedForThisCourse[0].absencePercentage;
                    // isDenialChangedForThisCourse.absencePercentage;
                    //Handle if courseWiseChanges absencePercentage lower than setting denial percentage
                    if (
                        courseWiseChanges &&
                        courseWiseChanges.absencePercentage <
                            reverseSortedWarningConfig[0].percentage
                    ) {
                        const isCourseHaveDenialStudents = scheduleElement.data.filter(
                            (studentElement) =>
                                studentElement.absentPercentage >
                                studentCourse.courseWisePercentage,
                        );
                        if (isCourseHaveDenialStudents.length) {
                            scheduleElement.data = isCourseHaveDenialStudents.filter(
                                (denialStudentElement) =>
                                    courseStudentsStrength.find(
                                        (sgElement) =>
                                            sgElement.studentId.toString() ===
                                                denialStudentElement.studentId.toString() &&
                                            sgElement._course_id &&
                                            scheduleElement._id.courseId.toString() ===
                                                sgElement._course_id.toString() &&
                                            (scheduleElement._id.rotation === 'yes'
                                                ? scheduleElement._id.rotation_count ===
                                                  sgElement.rotation_count
                                                : true),
                                    ),
                            );
                            const denialStudents = [];
                            const courseStatus = {};
                            const denialStudentsWithLeave = lmsStudent.reduce((acc, curr) => {
                                const studentFromCourseSchedules = scheduleElement.data.find(
                                    (courseStudentElement) =>
                                        curr.studentId.toString() ===
                                        courseStudentElement.studentId.toString(),
                                );
                                if (
                                    studentFromCourseSchedules &&
                                    studentFromCourseSchedules.scheduled_dates
                                ) {
                                    const isScheduleAffectedByLeave =
                                        studentFromCourseSchedules.scheduled_dates.find(
                                            (scheduleStartDateElement) =>
                                                scheduleStartDateElement &&
                                                new Date(scheduleStartDateElement) >=
                                                    new Date(curr.dateAndTimeRange.startDate) &&
                                                new Date(scheduleStartDateElement) <=
                                                    new Date(curr.dateAndTimeRange.endDate),
                                        );
                                    if (isScheduleAffectedByLeave) {
                                        if (acc[curr.studentId]) {
                                            if (
                                                acc[curr.studentId][curr.categoryName.toLowerCase()]
                                            ) {
                                                acc[curr.studentId][
                                                    curr.categoryName.toLowerCase()
                                                ] += 1;
                                            } else {
                                                acc[curr.studentId][
                                                    curr.categoryName.toLowerCase()
                                                ] = 1;
                                            }
                                        } else {
                                            acc[curr.studentId] = {
                                                [curr.categoryName.toLowerCase()]: 1,
                                            };
                                        }
                                    }
                                }
                                return acc;
                            }, {});
                            for (const studentElement of scheduleElement.data) {
                                if (studentElement.studentId) {
                                    //check here rotation and rotation count
                                    const isStudentMatchedWithRotation = courses.find(
                                        (courseElement) =>
                                            studentElement.rotation === 'yes'
                                                ? studentElement.rotation_count ===
                                                  courseElement.rotation_count
                                                : true,
                                    );
                                    if (isStudentMatchedWithRotation) {
                                        if (studentElement.unAppliedLeaves > 0) {
                                            if (
                                                reverseSortedWarningConfig[0]
                                                    .unappliedLeaveConsideredAs
                                            ) {
                                                if (
                                                    !denialStudentsWithLeave[
                                                        studentElement.studentId.toString()
                                                    ]
                                                ) {
                                                    denialStudentsWithLeave[
                                                        studentElement.studentId.toString()
                                                    ] = {};
                                                }
                                                if (
                                                    denialStudentsWithLeave[
                                                        studentElement.studentId.toString()
                                                    ][
                                                        reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                                    ]
                                                ) {
                                                    denialStudentsWithLeave[
                                                        studentElement.studentId.toString()
                                                    ][
                                                        reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                                    ] += studentElement.unAppliedLeaves;
                                                } else {
                                                    denialStudentsWithLeave[
                                                        studentElement.studentId.toString()
                                                    ][
                                                        reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                                                    ] = studentElement.unAppliedLeaves;
                                                }
                                            }
                                        }
                                        if (
                                            denialStudentsWithLeave[
                                                studentElement.studentId.toString()
                                            ] &&
                                            reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                        ) {
                                            const absentDetails = Object.keys(
                                                denialStudentsWithLeave[
                                                    studentElement.studentId.toString()
                                                ],
                                            ).reduce((acc, curr) => {
                                                const absentDetailObject = {
                                                    categoryName: curr.toLowerCase(),
                                                    percentage:
                                                        (denialStudentsWithLeave[
                                                            studentElement.studentId.toString()
                                                        ][curr] /
                                                            studentElement.totalSchedules) *
                                                        100,
                                                };
                                                acc.push(absentDetailObject);
                                                return acc;
                                            }, []);
                                            studentElement.absentDetails = absentDetails;
                                        }
                                        const isMixedStudent = mixedDenialStudents.find(
                                            (denialStudentElement) =>
                                                (studentElement.rotation === 'yes'
                                                    ? parseInt(studentElement.rotation_count) ===
                                                      denialStudentElement.rotationCount
                                                    : true) &&
                                                denialStudentElement.term === term &&
                                                denialStudentElement.courseId.toString() ===
                                                    scheduleElement._id.courseId.toString() &&
                                                (denialStudentElement.studentId
                                                    ? studentElement.studentId.toString() ===
                                                      denialStudentElement.studentId.toString()
                                                    : denialStudentElement.typeWiseUpdate ===
                                                      COURSE_WISE),
                                        );
                                        const isMixedStudentForCourseFlow =
                                            mixedDenialStudents.find(
                                                (denialStudentElement) =>
                                                    denialStudentElement.courseId.toString() ===
                                                        scheduleElement._id.courseId.toString() &&
                                                    denialStudentElement.typeWiseUpdate ===
                                                        COURSE_WISE &&
                                                    (studentElement.rotation === 'yes'
                                                        ? parseInt(
                                                              studentElement.rotation_count,
                                                          ) === denialStudentElement.rotationCount
                                                        : true),
                                            );
                                        if (
                                            isMixedStudent &&
                                            isMixedStudent.typeWiseUpdate === STUDENT_WISE
                                        ) {
                                            if (
                                                isMixedStudent.absencePercentage !==
                                                ((isMixedStudentForCourseFlow &&
                                                    isMixedStudentForCourseFlow.absencePercentage) ||
                                                    reverseSortedWarningConfig[0].percentage)
                                            ) {
                                                courseStatus.status = 'mixed';
                                                courseStatus.denialPercentage =
                                                    isMixedStudentForCourseFlow
                                                        ? isMixedStudentForCourseFlow.absencePercentage
                                                        : reverseSortedWarningConfig[0].percentage;
                                            }
                                            if (
                                                Number(studentElement.absentPercentage) >
                                                Number(isMixedStudent.absencePercentage)
                                            ) {
                                                studentElement.status =
                                                    reverseSortedWarningConfig[0].labelName;
                                            } else {
                                                studentElement.manipulated = true;
                                            }
                                            if (
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                            ) {
                                                if (studentElement.absentDetails) {
                                                    let isDenial;
                                                    for (const categoryElement of studentElement.absentDetails) {
                                                        const categoryPercentage =
                                                            isMixedStudent.categoryWisePercentage.find(
                                                                (percentageElement) =>
                                                                    percentageElement.categoryName.toLowerCase() ===
                                                                    categoryElement.categoryName.toLowerCase(),
                                                            );
                                                        if (
                                                            categoryPercentage &&
                                                            Number(categoryElement.percentage) >
                                                                Number(
                                                                    categoryPercentage.percentage,
                                                                )
                                                        ) {
                                                            isDenial = true;
                                                        } else {
                                                            studentElement.manipulated = true;
                                                        }
                                                    }
                                                    if (isDenial) {
                                                        delete studentElement.manipulated;
                                                    }
                                                    studentElement.status = isDenial
                                                        ? reverseSortedWarningConfig[0].labelName
                                                        : reverseSortedWarningConfig[1].labelName;
                                                }
                                            }
                                            studentElement.mixedConfig = {
                                                absencePercentage: isMixedStudent.absencePercentage,
                                                categoryWisePercentage:
                                                    isMixedStudent.categoryWisePercentage,
                                                denialCondition: isMixedStudent.denialCondition,
                                            };
                                            if (
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                            ) {
                                                if (
                                                    studentElement.absentPercentage >
                                                        ((isMixedStudentForCourseFlow &&
                                                            isMixedStudentForCourseFlow.absencePercentage) ||
                                                            reverseSortedWarningConfig[0]
                                                                .percentage) ||
                                                    studentElement.status ===
                                                        reverseSortedWarningConfig[0].labelName ||
                                                    studentElement.manipulated
                                                ) {
                                                    if (denialConfig ? denialConfig.isActive : true)
                                                        denialStudents.push(studentElement);
                                                }
                                            } else {
                                                if (denialConfig ? denialConfig.isActive : true)
                                                    denialStudents.push(studentElement);
                                            }
                                        } else if (
                                            isMixedStudent &&
                                            isMixedStudent.typeWiseUpdate === COURSE_WISE
                                        ) {
                                            //for check if overall course denial percentage is changed
                                            courseStatus.denialPercentage =
                                                isMixedStudent.absencePercentage;
                                            courseStatus.categoryWisePercentage =
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                    INDIVIDUAL &&
                                                isMixedStudent.categoryWisePercentage;
                                            if (
                                                Number(studentElement.absentPercentage) >
                                                Number(isMixedStudent.absencePercentage)
                                            ) {
                                                studentElement.status =
                                                    reverseSortedWarningConfig[0].labelName;
                                            } else {
                                                studentElement.manipulated = true;
                                            }
                                            if (
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                            ) {
                                                if (studentElement.absentDetails) {
                                                    let isDenial;
                                                    for (const categoryElement of studentElement.absentDetails) {
                                                        const categoryPercentage =
                                                            isMixedStudent.categoryWisePercentage.find(
                                                                (percentageElement) =>
                                                                    percentageElement.categoryName.toLowerCase() ===
                                                                    categoryElement.categoryName.toLowerCase(),
                                                            );
                                                        if (
                                                            categoryPercentage &&
                                                            Number(categoryElement.percentage) >
                                                                Number(
                                                                    categoryPercentage.percentage,
                                                                )
                                                        ) {
                                                            isDenial = true;
                                                        } else {
                                                            studentElement.manipulated = true;
                                                        }
                                                    }
                                                    if (isDenial) {
                                                        delete studentElement.manipulated;
                                                    }
                                                    studentElement.status = isDenial
                                                        ? reverseSortedWarningConfig[0].labelName
                                                        : reverseSortedWarningConfig[1].labelName;
                                                }
                                            }
                                            studentElement.mixedConfig = {
                                                absencePercentage: isMixedStudent.absencePercentage,
                                                categoryWisePercentage:
                                                    isMixedStudent.categoryWisePercentage,
                                                denialCondition: isMixedStudent.denialCondition,
                                            };
                                            if (
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                            ) {
                                                if (
                                                    studentElement.absentPercentage >
                                                        ((isMixedStudentForCourseFlow &&
                                                            isMixedStudentForCourseFlow.absencePercentage) ||
                                                            reverseSortedWarningConfig[0]
                                                                .percentage) ||
                                                    studentElement.status ===
                                                        reverseSortedWarningConfig[0].labelName ||
                                                    studentElement.manipulated
                                                ) {
                                                    if (denialConfig ? denialConfig.isActive : true)
                                                        denialStudents.push(studentElement);
                                                }
                                            } else {
                                                if (denialConfig ? denialConfig.isActive : true)
                                                    denialStudents.push(studentElement);
                                            }
                                        } else {
                                            if (
                                                Number(studentElement.absentPercentage) >
                                                Number(reverseSortedWarningConfig[0].percentage)
                                            ) {
                                                studentElement.status =
                                                    reverseSortedWarningConfig[0].labelName;
                                            }
                                            if (
                                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                                INDIVIDUAL
                                            ) {
                                                if (studentElement.absentDetails) {
                                                    let isDenial;
                                                    for (const categoryElement of studentElement.absentDetails) {
                                                        const categoryPercentage =
                                                            reverseSortedWarningConfig[0].categoryWisePercentage.find(
                                                                (percentageElement) =>
                                                                    percentageElement.categoryName.toLowerCase() ===
                                                                    categoryElement.categoryName.toLowerCase(),
                                                            );
                                                        if (
                                                            categoryPercentage &&
                                                            Number(categoryElement.percentage) >
                                                                Number(
                                                                    categoryPercentage.percentage,
                                                                )
                                                        ) {
                                                            isDenial = true;
                                                        } else {
                                                            studentElement.manipulated = true;
                                                        }
                                                    }
                                                    studentElement.status = isDenial
                                                        ? reverseSortedWarningConfig[0].labelName
                                                        : reverseSortedWarningConfig[1]
                                                        ? reverseSortedWarningConfig[1].labelName
                                                        : '';
                                                    if (isDenial) {
                                                        delete studentElement.manipulated;
                                                    }
                                                }
                                                if (
                                                    studentElement.absentPercentage >
                                                        ((isMixedStudentForCourseFlow &&
                                                            isMixedStudentForCourseFlow.absencePercentage) ||
                                                            reverseSortedWarningConfig[0]
                                                                .percentage) ||
                                                    studentElement.status ===
                                                        reverseSortedWarningConfig[0].labelName ||
                                                    studentElement.manipulated
                                                ) {
                                                    if (denialConfig ? denialConfig.isActive : true)
                                                        denialStudents.push(studentElement);
                                                }
                                            } else {
                                                if (denialConfig ? denialConfig.isActive : true)
                                                    denialStudents.push(studentElement);
                                            }
                                        }
                                        delete studentElement.totalSchedules;
                                        delete studentElement.appliedLeaves;
                                        delete studentElement.unAppliedLeaves;
                                    }
                                }
                            }
                            studentCourse.denialStudents = sortArrayByName(denialStudents);
                            studentCourse.categoryWisePercentage =
                                reverseSortedWarningConfig[0].denialCondition.toLowerCase() ===
                                    INDIVIDUAL && courseStatus.categoryWisePercentage;
                            studentCourse.denialPercentage =
                                courseStatus.status || courseStatus.denialPercentage;
                            studentCourse.courseWisePercentage = courseStatus.denialPercentage;
                        }
                    }
                }
            }
        }
        const colorCode = reverseSortedWarningConfig.map((warningElement) => ({
            warning: warningElement.labelName,
            colorCode: warningElement.colorCode,
        }));
        return {
            statusCode: 200,
            data: {
                courses: courses.sort((a, b) => {
                    if (a.course_name < b.course_name) return -1;
                    if (a.course_name > b.course_name) return 1;
                    return 0;
                }),
                colorCode,
                denialPercentage: denialConfig.isActive
                    ? reverseSortedWarningConfig[0].percentage
                    : denialConfig.percentage,
                categoryWisePercentage: denialConfig.isActive
                    ? reverseSortedWarningConfig[0].categoryWisePercentage
                    : denialConfig.categoryWisePercentage,
                denialCondition: reverseSortedWarningConfig[0].denialCondition.toLowerCase(),
                denialWarning: reverseSortedWarningConfig[0].labelName,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getCoursesFromProgramLevelRefactored = async ({ query = {}, headers = {} }) => {
    try {
        const { level_no, year, programId, term, searchKey, roleId, _institution_calendar_id } =
            query;
        const { _institution_id, _user_id } = headers;
        const levels = await getLevelsFromRedis(programId, _institution_calendar_id);
        if (!levels) {
            return { statusCode: 200, data: [] };
        }
        const filteredLevels = levels.find(
            (levelElement) =>
                levelElement.level_no === level_no &&
                levelElement.term === term &&
                levelElement.year === year,
        );
        if (filteredLevels.rotation === 'yes') {
            filteredLevels.course.push(
                ...filteredLevels.rotation_course.flatMap((rotationCourses) =>
                    rotationCourses.course.map((courseElement) => ({
                        ...courseElement,
                        rotation: filteredLevels.rotation,
                        rotation_count: rotationCourses.rotation_count,
                        year: filteredLevels.year,
                        term: filteredLevels.term,
                        level_no: filteredLevels.level_no,
                    })),
                ),
            );
        }
        let courses = filteredLevels.course
            .filter(
                (courseElement) =>
                    !searchKey ||
                    new RegExp(searchKey, 'gi').test(courseElement.courses_name) ||
                    new RegExp(searchKey, 'gi').test(courseElement.courses_number),
            )
            .map((courseElement) => ({
                versionNo: courseElement._course_id.versionNo || 1,
                versioned: courseElement._course_id.versioned || false,
                versionName: courseElement._course_id.versionName || '',
                versionedFrom: courseElement._course_id.versionedFrom || null,
                versionedCourseIds: courseElement._course_id.versionedCourseIds || [],
                _course_id: courseElement._course_id._id,
                course_name: courseElement.courses_name,
                course_number: courseElement.courses_number,
                model: courseElement.model,
                rotation: courseElement.rotation || 'no',
                rotation_count: courseElement.rotation === 'yes' ? courseElement.rotation_count : 0,
                term: filteredLevels.term,
                year: filteredLevels.year,
                denialStudents: [],
            }));
        const { userCourseIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: roleId,
            institutionCalendarId: [_institution_calendar_id],
        });
        if (userCourseIds.length) {
            const userCourseIdSet = new Set(
                userCourseIds.map((courseIdElement) => courseIdElement._course_id.toString()),
            );
            courses = courses.filter((courseElement) =>
                userCourseIdSet.has(courseElement._course_id.toString()),
            );
        }
        const courseIds = [];
        const courseIdsStrings = [];
        for (const courseIdElement of courses) {
            courseIds.push(courseIdElement._course_id);
            courseIdsStrings.push(courseIdElement._course_id.toString());
        }
        const courseStudentsStrength = await getParticularCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            term,
            _institution_calendar_id,
            _institution_id,
            courseIds: courseIdsStrings,
        });
        const courseStudentsStrengthMap = {};
        for (const courseElement of courseStudentsStrength) {
            let courseIdKey = courseElement._course_id.toString() + '_' + courseElement.term;
            if (courseElement.rotation === 'yes') {
                courseIdKey += '_' + courseElement.rotation_count;
            }
            if (!courseStudentsStrengthMap[courseIdKey]) {
                courseStudentsStrengthMap[courseIdKey] = 0;
            }
            courseStudentsStrengthMap[courseIdKey]++;
        }
        const studentCourses = new Map();
        for (const courseIdElement of courses) {
            let courseIdKey = courseIdElement._course_id.toString() + '_' + courseIdElement.term;
            if (courseIdElement.rotation === 'yes') {
                courseIdKey += '_' + courseIdElement.rotation_count;
            }
            courseIdElement.totalStudents = courseStudentsStrengthMap[courseIdKey] || 0;
            studentCourses.set(courseIdKey, courseIdElement);
        }
        const lmsStudentSettings = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                },
                {
                    'warningConfig.percentage': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.denialCondition': 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        let { warningConfig } = lmsStudentSettings;
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig = null;
        warningConfig = warningConfig
            .filter((config) => config.isActive)
            .sort((a, b) => b.percentage - a.percentage);
        const {
            percentage = 0,
            labelName = '',
            denialCondition = 'cummulative',
        } = warningConfig?.[0] ?? {};
        if (warningConfig.length) denialConfig = warningConfig[0];
        const { lateDurationRange } = await getLateAutoAndManualRange({
            _institution_id,
        });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId: courseIds,
            programId,
            yearNo: year,
            levelNo: level_no,
            term,
        });
        const courseSchedulesDocs = await courseScheduleModel
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    year_no: year,
                    level_no,
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: { $in: courseIds },
                    type: 'regular',
                    term,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _course_id: 1,
                    rotation: 1,
                    rotation_count: 1,
                    status: 1,
                    'students._id': 1,
                    'students.name': 1,
                    'students.status': 1,
                    'students.primaryTime': 1,
                    'students.lateExclude': 1,
                    'session._session_id': 1,
                    'sessionDetail.start_time': 1,
                },
            )
            .lean();
        const mixedDenialStudents = await lmsDenialModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    programId: convertToMongoObjectId(programId),
                    courseId: { $in: courseIds },
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    absencePercentage: 1,
                    studentId: 1,
                    courseId: 1,
                    rotation: 1,
                    rotationCount: 1,
                    typeWiseUpdate: 1,
                },
            )
            .sort({ updatedAt: -1 })
            .lean();
        const userDocs = await userSchema
            .find(
                {
                    _id: {
                        $in: courseSchedulesDocs.flatMap((scheduleElement) =>
                            scheduleElement.students.map((studentElement) => studentElement._id),
                        ),
                    },
                },
                {
                    user_id: 1,
                },
            )
            .lean();
        const userData = new Map();
        for (const userElement of userDocs) {
            userData.set(userElement._id.toString(), userElement.user_id);
        }
        const userCourse = new Map();
        for (const courseScheduleElement of courseSchedulesDocs) {
            const { _course_id, rotation, rotation_count, status } = courseScheduleElement;
            let userCourseKey = _course_id.toString();
            let lateExclude = null;
            if (rotation === 'yes') userCourseKey += '_' + rotation_count;
            if (!userCourse.has(userCourseKey)) {
                const isDenialChangedForThisCourse = mixedDenialStudents.filter(
                    (denialElement) =>
                        denialElement.courseId.toString() === _course_id.toString() &&
                        (rotation === 'yes'
                            ? denialElement.rotationCount === rotation_count
                            : true),
                );
                const courseWiseChanges = isDenialChangedForThisCourse.find(
                    (denialElement) => denialElement.typeWiseUpdate === COURSE_WISE,
                );
                userCourse.set(userCourseKey, {
                    _id: {
                        courseId: _course_id,
                        rotation,
                        rotation_count,
                        isDenialChangedForThisCourse,
                        courseWiseChanges,
                    },
                    data: new Map(),
                });
                const { lateExclude: updatedLateExclude } =
                    checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id,
                        programId,
                        courseId: _course_id,
                        levelNo: level_no,
                        term,
                        rotationCount: rotation_count,
                        lateExcludeManagement,
                    });
                lateExclude = updatedLateExclude;
            }
            const userCourseEntry = userCourse.get(userCourseKey);
            for (const studentElement of courseScheduleElement.students) {
                const { _id } = studentElement;
                const userCourseData = userCourseEntry.data;
                studentElement.academicId = userData.get(_id.toString());
                let userCourseDataKey = _course_id.toString() + '_' + _id.toString();
                if (rotation === 'yes') userCourseDataKey += '_' + rotation_count;
                if (!userCourseData.has(userCourseDataKey)) {
                    userCourseData.set(userCourseDataKey, {
                        studentId: _id,
                        studentName: studentElement.name,
                        academicId: studentElement.academicId,
                        totalSchedules: 0,
                        totalCompletedSchedules: 0,
                        leavedSchedules: 0,
                        presentedSchedules: 0,
                        absentPercentage: 0,
                        presentPercentage: 0,
                        lateConfig: {},
                        studentLateAbsent: 0,
                    });
                }
                const userCourseDataEntry = userCourseData.get(userCourseDataKey);
                const excludeStatus = studentElement.status === EXCLUDE;
                const completedStatus = status === 'completed';
                if (!excludeStatus) {
                    userCourseDataEntry.totalSchedules++;
                }
                if (completedStatus && !excludeStatus) {
                    userCourseDataEntry.totalCompletedSchedules++;
                }
                if (
                    completedStatus &&
                    (studentElement.status === 'absent' || studentElement.status === 'leave')
                ) {
                    userCourseDataEntry.leavedSchedules++;
                }
                if (
                    completedStatus &&
                    (studentElement.status === 'present' ||
                        studentElement.status === 'on_duty' ||
                        studentElement.status === 'permission')
                ) {
                    userCourseDataEntry.presentedSchedules++;
                }
                let lateConfig = userCourseDataEntry.lateConfig;
                let studentLateAbsent = userCourseDataEntry.studentLateAbsent;
                if (lateDurationRange.length && !lateExclude) {
                    const lateExludeForStudents = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id,
                        programId,
                        levelNo: level_no,
                        term,
                        courseId: courseScheduleElement._course_id,
                        studentId: studentElement._id,
                        rotationCount: courseScheduleElement.rotation_count,
                        lateExcludeManagement,
                    }).lateExclude;
                    const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id,
                        programId,
                        levelNo: level_no,
                        term,
                        courseId: courseScheduleElement._course_id,
                        studentId: studentElement._id,
                        rotationCount: courseScheduleElement.rotation_count,
                        sessionId: courseScheduleElement.session._session_id,
                        lateExcludeManagement,
                    }).lateExclude;
                    if (
                        completedStatus &&
                        studentElement.status === constants.PRESENT &&
                        studentElement.primaryTime !== undefined &&
                        !studentElement.lateExclude &&
                        !lateExludeForStudents &&
                        !lateExludeForSessions
                    ) {
                        const { lateConfig: updatedLateConfig } = getAutoRange({
                            lateDurationRange,
                            lateConfig,
                            studentLateAbsent,
                            primaryTime: studentElement.primaryTime,
                            scheduleStartDateAndTime:
                                courseScheduleElement.sessionDetail.start_time,
                        });
                        lateConfig = updatedLateConfig;
                    }
                    userCourseDataEntry.lateConfig = lateConfig;
                    for (const lateLabel in userCourseDataEntry.lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent = lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent =
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                }
                userCourseDataEntry.studentLateAbsent = studentLateAbsent;
                userCourseDataEntry.absentPercentage =
                    ((userCourseDataEntry.leavedSchedules + userCourseDataEntry.studentLateAbsent) /
                        userCourseDataEntry.totalSchedules) *
                    100;
                if (userCourseDataEntry.totalCompletedSchedules) {
                    userCourseDataEntry.presentPercentage =
                        ((userCourseDataEntry.presentedSchedules -
                            userCourseDataEntry.studentLateAbsent) /
                            userCourseDataEntry.totalCompletedSchedules) *
                        100;
                }
            }
        }
        const courseSchedules = [...userCourse.values()];
        if (!courseSchedules || !courseSchedules.length) {
            for (const studentCourse of courses) {
                const isMixedStudent = mixedDenialStudents.find(
                    (denialStudentElement) =>
                        (studentCourse.rotation === 'yes'
                            ? parseInt(studentCourse.rotation_count) ===
                              denialStudentElement.rotationCount
                            : true) &&
                        denialStudentElement.courseId.toString() ===
                            studentCourse._course_id.toString(),
                );
                const isMixedStudentForCourseFlow = mixedDenialStudents.find(
                    (denialStudentElement) =>
                        (studentCourse.rotation === 'yes'
                            ? parseInt(studentCourse.rotation_count) ===
                              denialStudentElement.rotationCount
                            : true) &&
                        denialStudentElement.courseId.toString() ===
                            studentCourse._course_id.toString() &&
                        denialStudentElement.typeWiseUpdate === COURSE_WISE,
                );
                if (isMixedStudentForCourseFlow) {
                    studentCourse.courseWisePercentage =
                        isMixedStudentForCourseFlow.absencePercentage;
                }
                if (isMixedStudent) {
                    if (isMixedStudent.typeWiseUpdate === STUDENT_WISE) {
                        if (
                            isMixedStudent.absencePercentage !==
                            (isMixedStudentForCourseFlow
                                ? isMixedStudentForCourseFlow.absencePercentage
                                : warningConfig[0].percentage)
                        ) {
                            studentCourse.status = 'mixed';
                            studentCourse.denialPercentage = 'mixed';
                        } else {
                            studentCourse.denialPercentage = isMixedStudentForCourseFlow
                                ? isMixedStudentForCourseFlow.absencePercentage
                                : warningConfig[0].percentage;
                        }
                        studentCourse.manipulated = true;
                        studentCourse.mixedConfig = {
                            absencePercentage: isMixedStudent.absencePercentage,
                            denialCondition: isMixedStudent.denialCondition,
                        };
                    } else if (isMixedStudent.typeWiseUpdate === COURSE_WISE) {
                        studentCourse.denialPercentage = isMixedStudent.absencePercentage;
                        studentCourse.courseWisePercentage = isMixedStudent.absencePercentage;
                        if (
                            Number(studentCourse.absentPercentage) >
                            Number(isMixedStudent.absencePercentage)
                        ) {
                            studentCourse.status = warningConfig[0].labelName;
                        } else {
                            studentCourse.manipulated = true;
                        }
                        studentCourse.mixedConfig = {
                            absencePercentage: isMixedStudent.absencePercentage,
                            denialCondition: isMixedStudent.denialCondition,
                        };
                    }
                } else {
                    if (
                        Number(studentCourse.absentPercentage) > Number(warningConfig[0].percentage)
                    ) {
                        studentCourse.status = warningConfig[0].labelName;
                    }
                }
            }
            return {
                statusCode: 200,
                data: {
                    courses,
                    denialPercentage: denialConfig.isActive
                        ? warningConfig[0].percentage
                        : denialConfig.percentage,
                    denialCondition: warningConfig[0].denialCondition.toLowerCase(),
                },
                message: 'no matched schedules',
            };
        }
        for (const scheduleElement of courseSchedules) {
            scheduleElement.data = [...scheduleElement.data.values()];
            let courseIdKey = scheduleElement._id.courseId.toString() + '_' + term;
            if (scheduleElement._id.rotation === 'yes') {
                courseIdKey += '_' + scheduleElement._id.rotation_count;
            }
            const studentCourse = studentCourses.get(courseIdKey);
            if (!studentCourse) {
                continue;
            }
            studentCourse.isScheduled = true;
            const isCourseHaveDenialStudents = scheduleElement.data.filter(
                (studentElement) => studentElement.absentPercentage > warningConfig[0].percentage,
            );
            const isDenialChangedForThisCourse = scheduleElement._id.isDenialChangedForThisCourse;
            if (isCourseHaveDenialStudents.length) {
                scheduleElement.data = isCourseHaveDenialStudents.filter((denialStudentElement) =>
                    courseStudentsStrength.find(
                        (sgElement) =>
                            sgElement.studentId.toString() ===
                                denialStudentElement.studentId.toString() &&
                            sgElement._course_id &&
                            scheduleElement._id.courseId.toString() ===
                                sgElement._course_id.toString() &&
                            (scheduleElement._id.rotation === 'yes'
                                ? scheduleElement._id.rotation_count === sgElement.rotation_count
                                : true),
                    ),
                );
                const denialStudents = [];
                const courseStatus = {};
                for (const studentElement of scheduleElement.data) {
                    const [isMixedStudent, isMixedStudentForCourseFlow] =
                        mixedDenialStudents.reduce(
                            (acc, denialStudentElement) => {
                                const {
                                    _id: { courseId },
                                } = scheduleElement;
                                const { rotation, rotation_count, studentId } = studentElement;
                                const {
                                    courseId: denialCourseId,
                                    rotationCount,
                                    term: denialTerm,
                                    studentId: denialStudentId,
                                    typeWiseUpdate,
                                } = denialStudentElement;
                                const isSameCourse =
                                    denialCourseId.toString() === courseId.toString();
                                const isRotationMatch =
                                    rotation === 'yes'
                                        ? parseInt(rotation_count) === rotationCount
                                        : true;
                                const isSameStudent = denialStudentId
                                    ? studentId.toString() === denialStudentId.toString()
                                    : typeWiseUpdate === COURSE_WISE;

                                if (
                                    isSameCourse &&
                                    isRotationMatch &&
                                    (isSameStudent || typeWiseUpdate === COURSE_WISE)
                                ) {
                                    if (!acc[0]) {
                                        acc[0] = denialStudentElement;
                                    }
                                    if (
                                        !acc[1] &&
                                        denialStudentElement.typeWiseUpdate === COURSE_WISE
                                    ) {
                                        acc[1] = denialStudentElement;
                                    }
                                }
                                return acc;
                            },
                            [null, null],
                        );
                    const absencePercentageToCompare = isMixedStudentForCourseFlow
                        ? isMixedStudentForCourseFlow.absencePercentage
                        : warningConfig[0].percentage;
                    if (isMixedStudent && isMixedStudent.typeWiseUpdate === STUDENT_WISE) {
                        if (isMixedStudent.absencePercentage !== absencePercentageToCompare) {
                            courseStatus.status = 'mixed';
                            courseStatus.denialPercentage = absencePercentageToCompare;
                        }
                        studentElement.mixedConfig = {
                            absencePercentage: isMixedStudent.absencePercentage,
                            denialCondition: isMixedStudent.denialCondition,
                        };
                    } else if (isMixedStudent && isMixedStudent.typeWiseUpdate === COURSE_WISE) {
                        courseStatus.denialPercentage = isMixedStudent.absencePercentage;
                        studentElement.mixedConfig = {
                            absencePercentage: isMixedStudent.absencePercentage,
                            denialCondition: isMixedStudent.denialCondition,
                        };
                    } else if (
                        Number(studentElement.absentPercentage) >
                        Number(warningConfig[0].percentage)
                    ) {
                        studentElement.status = warningConfig[0].labelName;
                    }
                    if (isMixedStudent) {
                        if (
                            Number(studentElement.absentPercentage) >
                            Number(isMixedStudent.absencePercentage)
                        ) {
                            studentElement.status = warningConfig[0].labelName;
                        } else {
                            studentElement.manipulated = true;
                        }
                    }
                    if (denialConfig ? denialConfig.isActive : true)
                        denialStudents.push(studentElement);
                    delete studentElement.totalSchedules;
                    delete studentElement.appliedLeaves;
                    delete studentElement.unAppliedLeaves;
                }
                studentCourse.denialStudents = denialStudents;
                studentCourse.denialPercentage =
                    courseStatus.status || courseStatus.denialPercentage;
                studentCourse.courseWisePercentage = courseStatus.denialPercentage;
            }
            if (isDenialChangedForThisCourse.length) {
                const courseWiseChanges = scheduleElement._id.courseWiseChanges;
                const courseWisePercentage = courseWiseChanges
                    ? courseWiseChanges.absencePercentage
                    : null;
                const studentWiseUpdate =
                    isDenialChangedForThisCourse[0].typeWiseUpdate === STUDENT_WISE;
                const mixedDenialPercentage =
                    courseWiseChanges && courseWiseChanges.absencePercentage;
                const denialPercentage = isDenialChangedForThisCourse[0].absencePercentage;
                const warningPercentage = warningConfig[0].percentage;
                studentCourse.courseWisePercentage = courseWisePercentage;
                studentCourse.denialPercentage = studentWiseUpdate
                    ? denialPercentage !== (mixedDenialPercentage || warningPercentage)
                        ? 'mixed'
                        : mixedDenialPercentage || warningPercentage
                    : denialPercentage;
                if (courseWiseChanges && courseWiseChanges.absencePercentage < warningPercentage) {
                    const isCourseHaveDenialStudents = scheduleElement.data.filter(
                        (studentElement) =>
                            studentElement.absentPercentage > studentCourse.courseWisePercentage,
                    );
                    if (isCourseHaveDenialStudents.length) {
                        scheduleElement.data = isCourseHaveDenialStudents.filter(
                            (denialStudentElement) =>
                                courseStudentsStrength.find(
                                    (sgElement) =>
                                        sgElement.studentId.toString() ===
                                            denialStudentElement.studentId.toString() &&
                                        sgElement._course_id &&
                                        scheduleElement._id.courseId.toString() ===
                                            sgElement._course_id.toString() &&
                                        (scheduleElement._id.rotation === 'yes'
                                            ? scheduleElement._id.rotation_count ===
                                              sgElement.rotation_count
                                            : true),
                                ),
                        );
                        const denialStudents = [];
                        const courseStatus = {};
                        for (const studentElement of scheduleElement.data) {
                            const [isMixedStudent, isMixedStudentForCourseFlow] =
                                mixedDenialStudents.reduce(
                                    (acc, denialStudentElement) => {
                                        const {
                                            _id: { courseId },
                                        } = scheduleElement;
                                        const { rotation, rotation_count, studentId } =
                                            studentElement;
                                        const {
                                            courseId: denialCourseId,
                                            rotationCount,
                                            term: denialTerm,
                                            studentId: denialStudentId,
                                            typeWiseUpdate,
                                        } = denialStudentElement;
                                        const isSameCourse =
                                            denialCourseId.toString() === courseId.toString();
                                        const isRotationMatch =
                                            rotation === 'yes'
                                                ? parseInt(rotation_count) === rotationCount
                                                : true;
                                        const isSameStudent = denialStudentId
                                            ? studentId.toString() === denialStudentId.toString()
                                            : typeWiseUpdate === COURSE_WISE;

                                        if (
                                            isSameCourse &&
                                            isRotationMatch &&
                                            (isSameStudent || typeWiseUpdate === COURSE_WISE)
                                        ) {
                                            if (!acc[0]) {
                                                acc[0] = denialStudentElement;
                                            }
                                            if (
                                                !acc[1] &&
                                                denialStudentElement.typeWiseUpdate === COURSE_WISE
                                            ) {
                                                acc[1] = denialStudentElement;
                                            }
                                        }
                                        return acc;
                                    },
                                    [null, null],
                                );
                            const absencePercentageToCompare = isMixedStudentForCourseFlow
                                ? isMixedStudentForCourseFlow.absencePercentage
                                : warningConfig[0].percentage;
                            if (isMixedStudent && isMixedStudent.typeWiseUpdate === STUDENT_WISE) {
                                if (
                                    isMixedStudent.absencePercentage !== absencePercentageToCompare
                                ) {
                                    courseStatus.status = 'mixed';
                                    courseStatus.denialPercentage = absencePercentageToCompare;
                                }
                                studentElement.mixedConfig = {
                                    absencePercentage: isMixedStudent.absencePercentage,
                                    denialCondition: isMixedStudent.denialCondition,
                                };
                            } else if (
                                isMixedStudent &&
                                isMixedStudent.typeWiseUpdate === COURSE_WISE
                            ) {
                                courseStatus.denialPercentage = isMixedStudent.absencePercentage;
                                studentElement.mixedConfig = {
                                    absencePercentage: isMixedStudent.absencePercentage,
                                    denialCondition: isMixedStudent.denialCondition,
                                };
                            } else if (
                                Number(studentElement.absentPercentage) >
                                Number(warningConfig[0].percentage)
                            ) {
                                studentElement.status = warningConfig[0].labelName;
                            }
                            if (isMixedStudent) {
                                if (
                                    Number(studentElement.absentPercentage) >
                                    Number(isMixedStudent.absencePercentage)
                                ) {
                                    studentElement.status = warningConfig[0].labelName;
                                } else {
                                    studentElement.manipulated = true;
                                }
                            }
                            if (denialConfig ? denialConfig.isActive : true)
                                denialStudents.push(studentElement);
                            delete studentElement.totalSchedules;
                            delete studentElement.appliedLeaves;
                            delete studentElement.unAppliedLeaves;
                        }
                        studentCourse.denialStudents = denialStudents;
                        studentCourse.denialPercentage =
                            courseStatus.status || courseStatus.denialPercentage;
                        studentCourse.courseWisePercentage = courseStatus.denialPercentage;
                    }
                }
            }
        }
        const colorCode = warningConfig.map((warningElement) => ({
            warning: warningElement.labelName,
            colorCode: warningElement.colorCode,
        }));
        return {
            statusCode: 200,
            data: {
                courses: courses.sort((a, b) => {
                    if (a.course_name < b.course_name) return -1;
                    if (a.course_name > b.course_name) return 1;
                    return 0;
                }),
                colorCode,
                denialPercentage: denialConfig.isActive
                    ? warningConfig[0].percentage
                    : denialConfig.percentage,
                denialCondition: warningConfig[0].denialCondition.toLowerCase(),
                denialWarning: warningConfig[0].labelName,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getStudentsWithDenialCourses = async ({ headers = {}, query = {} }) => {
    try {
        const {
            level_no,
            year,
            programId,
            term,
            pageNo,
            limit,
            searchKey,
            rotation,
            rotationCount,
            _institution_calendar_id,
        } = query;
        const { _institution_id } = headers;
        const studentIdsWithCourses = await getAllCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            rotation,
            rotationCount,
            term,
            _institution_calendar_id,
            _institution_id,
        });
        if (!studentIdsWithCourses || !studentIdsWithCourses.length) {
            return { message: 'NO_DATA_FOUND', statusCode: 200 };
        }
        const courseIdsForDenialCheck = [];
        const groupedStudentsWithCourses = studentIdsWithCourses.reduce((acc, curr) => {
            if (!acc[curr.studentId]) {
                acc[curr.studentId] = {
                    courseId: [
                        {
                            courseId: curr._course_id,
                            courseName: curr.course_name,
                            courseNo: curr.course_no,
                            versionNo: curr.versionNo || 1,
                            versioned: curr.versioned || false,
                            versionName: curr.versionName || '',
                            versionedFrom: curr.versionedFrom || null,
                            versionedCourseIds: curr.versionedCourseIds || [],
                            status: '',
                            rotation_count: curr.rotation === 'yes' ? curr.rotation_count : 0,
                            rotation: curr.rotation,
                            term: curr.term,
                            absentPercentage: 0,
                            presentPercentage: 0,
                        },
                    ],
                    name: curr.studentName,
                    academicId: curr.academicId,
                };
            } else {
                // if(!acc[curr.studentId].courseId.find(courseElement=>courseElement.courseId === curr._course_id && courseElement.rotation_count === curr.rotation_count && courseElement.term === curr.term )){

                // }
                acc[curr.studentId].courseId.push({
                    courseId: curr._course_id,
                    courseName: curr.course_name,
                    courseNo: curr.course_no,
                    versionNo: curr.versionNo || 1,
                    versioned: curr.versioned || false,
                    versionName: curr.versionName || '',
                    versionedFrom: curr.versionedFrom || null,
                    versionedCourseIds: curr.versionedCourseIds || [],
                    status: '',
                    rotation_count: curr.rotation === 'yes' ? curr.rotation_count : 0,
                    rotation: curr.rotation,
                    term: curr.term,
                    absentPercentage: 0,
                    presentPercentage: 0,
                });
            }
            if (!courseIdsForDenialCheck.includes(curr._course_id)) {
                courseIdsForDenialCheck.push(curr._course_id);
            }
            return acc;
        }, {});
        const { limit: limits, skip } = getPaginationValues({ limit, pageNo });
        let studentIds = [];
        if (searchKey) {
            // eslint-disable-next-line guard-for-in
            for (const studentElement in groupedStudentsWithCourses) {
                const studentName = nameFormatter(groupedStudentsWithCourses[studentElement].name);
                const academicId = groupedStudentsWithCourses[studentElement].academicId;
                const regex = new RegExp(searchKey, 'ig');

                if (regex.test(studentName) || regex.test(academicId)) {
                    studentIds.push(convertToMongoObjectId(studentElement));
                }
            }
        } else {
            studentIds = Object.keys(groupedStudentsWithCourses)
                .splice(skip, limits)
                .map((studentElement) => convertToMongoObjectId(studentElement));
        }
        const lmsStudentSettings = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                    'warningConfig.isActive': true,
                }, // leave type  is hardcoded
                {
                    leaveCalculation: 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.denialCondition': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.unappliedLeaveConsideredAs': 1,
                    'warningConfig.categoryWisePercentage.categoryName': 1,
                    'warningConfig.categoryWisePercentage.categoryId': 1,
                    'warningConfig.categoryWisePercentage.percentage': 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId: courseIdsForDenialCheck,
            programId,
            yearNo: year,
            levelNo: level_no,
            term,
            rotationCount,
        });
        const { leaveCalculation } = lmsStudentSettings;
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        let { warningConfig } = lmsStudentSettings;
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig;
        for (let i = warningConfig.length - 1; i >= 0; i--) {
            if (
                (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
                denialConfig == null
            ) {
                denialConfig = warningConfig[i];
            }
            if (!warningConfig[i].isActive) {
                warningConfig.splice(i, 1);
            }
        }
        const sortedWarningConfig = warningConfig.sort((a, b) => {
            let comparison = 0;
            if (Number(a.percentage) > Number(b.percentage)) {
                comparison = 1;
            } else if (Number(a.percentage) < Number(b.percentage)) {
                comparison = -1;
            }
            return comparison;
        });
        let denialCourses;
        if (leaveCalculation === 'hours') {
            denialCourses = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        'students._id': {
                            $in: studentIds,
                        },
                        type: 'regular',
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $match: {
                        'students._id': {
                            $in: studentIds,
                        },
                        'students.status': { $not: { $eq: EXCLUDE } },
                    },
                },
                {
                    $group: {
                        _id: {
                            studentId: '$students._id',
                            courseId: '$_course_id',
                            sessionType: '$session.session_type',
                            rotation_count: '$rotation_count',
                        },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    { $eq: ['$status', 'completed'] },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        studentName: { $first: '$students.name' },
                        totalSchedules: {
                            $sum: 1,
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [{ $eq: ['$status', 'completed'] }, 1, 0],
                            },
                        },
                        courseName: { $first: '$course_name' },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        unAppliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'absent'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'leave'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $match: {
                        totalSchedules: { $gt: 0 },
                    },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        let: {
                            courseId: '$_id.courseId',
                            sessionType: '$_id.sessionType',
                        },
                        pipeline: [
                            {
                                $unwind: {
                                    path: '$credit_hours',
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $unwind: {
                                    path: '$credit_hours.delivery_type',
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$_id', '$$courseId'] },
                                            {
                                                $eq: [
                                                    '$credit_hours.delivery_type.delivery_type',
                                                    '$$sessionType',
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    'credit_hours.delivery_type.duration': 1,
                                },
                            },
                        ],
                        as: 'creditHoursDuration',
                    },
                },
                {
                    $addFields: {
                        sessionCreditHours: {
                            $divide: [
                                {
                                    $arrayElemAt: [
                                        '$creditHoursDuration.credit_hours.delivery_type.duration',
                                        0,
                                    ],
                                },
                                60,
                            ],
                        },
                    },
                },
                {
                    $addFields: {
                        leavedSchedules: { $multiply: ['$sessionCreditHours', '$leavedSchedules'] },
                        presentedSchedules: {
                            $multiply: ['$sessionCreditHours', '$presentedSchedules'],
                        },
                        totalCompletedSchedules: {
                            $multiply: ['$sessionCreditHours', '$totalCompletedSchedules'],
                        },
                        totalSchedules: { $multiply: ['$sessionCreditHours', '$totalSchedules'] },
                        appliedSchedules: { $multiply: ['$sessionCreditHours', '$appliedLeaves'] },
                        unAppliedLeaves: { $multiply: ['$sessionCreditHours', '$unAppliedLeaves'] },
                    },
                },
                {
                    $unwind: {
                        path: '$scheduled_dates',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $group: {
                        _id: {
                            courseId: '$_id.courseId',
                            studentId: '$_id.studentId',
                            rotation_count: '$_id.rotation_count',
                        },
                        scheduled_dates: { $push: '$scheduled_dates' },
                        courseName: { $first: '$courseName' },
                        studentName: { $first: '$studentName' },
                        totalSchedules: { $sum: '$totalSchedules' },
                        totalCompletedSchedules: { $sum: '$totalCompletedSchedules' },
                        leavedSchedules: { $sum: '$leavedSchedules' },
                        presentedSchedules: { $sum: '$presentedSchedules' },
                        unAppliedLeaves: { $sum: '$unAppliedLeaves' },
                        appliedLeaves: { $sum: '$appliedLeaves' },
                    },
                },
                {
                    $match: {
                        totalSchedules: { $gt: 0 },
                    },
                },
                {
                    $addFields: {
                        absentPercentage: {
                            $multiply: [{ $divide: ['$leavedSchedules', '$totalSchedules'] }, 100],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$presentedSchedules',
                                                '$totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                    },
                },
                {
                    $group: {
                        _id: '$_id.studentId',
                        data: {
                            $push: {
                                scheduled_dates: '$scheduled_dates',
                                rotation_count: '$_id.rotation_count',
                                courseId: '$_id.courseId',
                                courseName: '$courseName',
                                absentPercentage: '$absentPercentage',
                                presentPercentage: '$presentPercentage',
                                appliedLeaves: '$appliedLeaves',
                                unAppliedLeaves: '$unAppliedLeaves',
                                totalSchedules: '$totalSchedules',
                                totalCompletedSchedules: '$totalCompletedSchedules',
                            },
                        },
                    },
                },
            ]);
        } else {
            denialCourses = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        'students._id': {
                            $in: studentIds,
                        },
                        type: 'regular',
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $match: {
                        'students._id': {
                            $in: studentIds,
                        },
                        'students.status': { $not: { $eq: EXCLUDE } },
                    },
                },
                {
                    $group: {
                        _id: {
                            studentId: '$students._id',
                            courseId: '$_course_id',
                            rotation_count: '$rotation_count',
                        },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    { $eq: ['$status', 'completed'] },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        schedules: {
                            $push: {
                                students: '$students',
                                status: '$status',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                _institution_calendar_id: '$_institution_calendar_id',
                                programId: '$_program_id',
                                courseId: '$_course_id',
                                term: '$term',
                                levelNo: '$level_no',
                                rotationCount: '$rotation_count',
                                session: '$session',
                                sessionDetail: '$sessionDetail',
                            },
                        },
                        presentedSchedulesWithoutOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'present'] },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedulesBasedOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        studentName: { $first: '$students.name' },
                        totalSchedules: {
                            $sum: 1,
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [{ $eq: ['$status', 'completed'] }, 1, 0],
                            },
                        },
                        rotation_count: { $push: '$rotation_count' },
                        courseName: { $first: '$course_name' },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        unAppliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'absent'] },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'leave'] },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $match: {
                        totalSchedules: { $gt: 0 },
                    },
                },
                {
                    $project: {
                        studentId: '$_id.studentId',
                        rotation_count: '$_id.rotation_count',
                        studentName: '$studentName',
                        schedules: '$schedules',
                        presentedSchedulesWithoutOnDuty: '$presentedSchedulesWithoutOnDuty',
                        presentedSchedulesBasedOnDuty: '$presentedSchedulesBasedOnDuty',
                        leavedSchedules: '$leavedSchedules',
                        courseId: '$_id.courseId',
                        courseName: '$courseName',
                        absentPercentage: {
                            $multiply: [{ $divide: ['$leavedSchedules', '$totalSchedules'] }, 100],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$presentSchedules',
                                                '$totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        scheduled_dates: '$scheduled_dates',
                        appliedLeaves: '$appliedLeaves',
                        unAppliedLeaves: '$unAppliedLeaves',
                        totalSchedules: '$totalSchedules',
                        totalCompletedSchedules: '$totalCompletedSchedules',
                    },
                },
                {
                    $unwind: {
                        path: '$scheduled_dates',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $group: {
                        _id: { studentId: '$studentId', courseId: '$courseId' },
                        scheduled_dates: { $push: '$scheduled_dates' },
                        rotation_count: { $first: '$rotationCount' },
                        courseName: { $first: '$courseName' },
                        absentPercentage: { $first: '$absentPercentage' },
                        presentPercentage: { $first: '$presentPercentage' },
                        appliedLeaves: { $first: '$appliedLeaves' },
                        unAppliedLeaves: { $first: '$unAppliedLeaves' },
                        totalSchedules: { $first: '$totalSchedules' },
                        presentedSchedulesWithoutOnDuty: {
                            $first: '$presentedSchedulesWithoutOnDuty',
                        },
                        schedules: {
                            $first: '$schedules',
                        },
                        presentedSchedulesBasedOnDuty: { $first: '$presentedSchedulesBasedOnDuty' },
                        leavedSchedules: { $first: '$leavedSchedules' },
                        totalCompletedSchedules: { $first: '$totalCompletedSchedules' },
                    },
                },
                {
                    $group: {
                        _id: '$_id.studentId',
                        data: {
                            $push: {
                                courseId: '$_id.courseId',
                                scheduled_dates: '$scheduled_dates',
                                rotation_count: '$rotationCount',
                                presentedSchedulesWithoutOnDuty: '$presentedSchedulesWithoutOnDuty',
                                presentedSchedulesBasedOnDuty: '$presentedSchedulesBasedOnDuty',
                                leavedSchedules: '$leavedSchedules',
                                schedules: '$schedules',
                                courseName: '$courseName',
                                absentPercentage: '$absentPercentage',
                                presentPercentage: '$presentPercentage',
                                appliedLeaves: '$appliedLeaves',
                                unAppliedLeaves: '$unAppliedLeaves',
                                totalSchedules: '$totalSchedules',
                                totalCompletedSchedules: '$totalCompletedSchedules',
                            },
                        },
                    },
                },
            ]);
        }
        const colorCode = sortedWarningConfig.map((warningElement) => ({
            warning: warningElement.labelName,
            colorCode: warningElement.colorCode,
        }));
        await changePresentScheduleBasedLateConfigure({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules: denialCourses,
            lateExcludeManagement,
        });
        const mixedDenialStudents = await lmsDenialModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    programId: convertToMongoObjectId(programId),
                    courseId: {
                        $in: courseIdsForDenialCheck.map((courseElement) =>
                            convertToMongoObjectId(courseElement),
                        ),
                    },
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    studentId: 1,
                    courseId: 1,
                    absencePercentage: 1,
                    term: 1,
                    rotation: 1,
                    rotationCount: 1,
                    'categoryWisePercentage.categoryName': 1,
                    'categoryWisePercentage.categoryId': 1,
                    'categoryWisePercentage.percentage': 1,
                    denialCondition: 1,
                    typeWiseUpdate: 1,
                },
                {
                    sort: { updatedAt: -1 },
                },
            )
            .lean();
        if (!denialCourses.length) {
            const studentsWithDenialCourseForNoSchedule = [];
            for (const studentElement of studentIds) {
                let mixedCount = 0;
                const isMixed = [];

                for (courseElement of groupedStudentsWithCourses[studentElement].courseId) {
                    const mixedDenialPercentage = mixedDenialStudents.find(
                        (denialStudentElement) =>
                            denialStudentElement.studentId &&
                            studentElement.toString() ===
                                denialStudentElement.studentId.toString() &&
                            denialStudentElement.term === term &&
                            (studentElement.rotation === 'yes'
                                ? parseInt(studentElement.rotation_count) ===
                                  denialStudentElement.rotationCount
                                : true) &&
                            denialStudentElement.term === term &&
                            denialStudentElement.courseId.toString() ===
                                courseElement.courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === STUDENT_WISE,
                    );
                    const mixedDenialPercentageFromCourse = mixedDenialStudents.find(
                        (lmsDenialStudentElement) =>
                            lmsDenialStudentElement.courseId.toString() ===
                                courseElement.courseId.toString() &&
                            lmsDenialStudentElement.typeWiseUpdate === COURSE_WISE &&
                            lmsDenialStudentElement.term === term &&
                            (studentElement.rotation === 'yes'
                                ? lmsDenialStudentElement.rotationCount ===
                                  parseInt(studentElement.rotation_count)
                                : true),
                    );
                    const mixedData = mixedDenialPercentage || mixedDenialPercentageFromCourse;
                    const status = sortedWarningConfig.reduce((acc, curr) => {
                        if (Number(curr.percentage) < 0) {
                            if (mixedData) {
                                if (Number(mixedData.absencePercentage) < 0) {
                                    acc =
                                        sortedWarningConfig[sortedWarningConfig.length - 1]
                                            .labelName;
                                } else {
                                    acc = sortedWarningConfig[sortedWarningConfig.length - 2]
                                        ? sortedWarningConfig[sortedWarningConfig.length - 2]
                                              .labelName
                                        : '';
                                }
                            } else {
                                if (
                                    Number(
                                        sortedWarningConfig[sortedWarningConfig.length - 1]
                                            .percentage,
                                    ) < 0
                                ) {
                                    acc =
                                        sortedWarningConfig[sortedWarningConfig.length - 1]
                                            .labelName;
                                } else {
                                    acc = curr.labelName;
                                }
                            }
                        }
                        if (
                            sortedWarningConfig[
                                sortedWarningConfig.length - 1
                            ].denialCondition.toLowerCase() === INDIVIDUAL
                        ) {
                            if (courseElement.absentDetails && courseElement.absentDetails.length) {
                                const newMixedData =
                                    mixedData ||
                                    sortedWarningConfig[sortedWarningConfig.length - 1];

                                for (const categoryElement of newMixedData.categoryWisePercentage) {
                                    const absentPercentage = courseElement.absentDetails.find(
                                        (percentageElement) =>
                                            percentageElement.categoryName.toLowerCase() ===
                                            categoryElement.categoryName.toLowerCase(),
                                    );
                                    if (
                                        absentPercentage &&
                                        Number(absentPercentage.percentage) >
                                            Number(categoryElement.percentage)
                                    ) {
                                        acc =
                                            sortedWarningConfig[sortedWarningConfig.length - 1]
                                                .labelName;
                                    }
                                }
                            }
                        }
                        return acc;
                    }, '');
                    courseElement.status = status;
                    courseElement.absentPercentage = 0;
                    courseElement.presentPercentage = 0;

                    const mixedConfig = mixedDenialPercentage || mixedDenialPercentageFromCourse;
                    if (mixedConfig) {
                        mixedCount++;
                        courseElement.mixedConfig = {
                            absencePercentage: mixedConfig.absencePercentage,
                            categoryWisePercentage: mixedConfig.categoryWisePercentage,
                            denialCondition: mixedConfig.denialCondition,
                        };
                        if (!isMixed.includes(mixedConfig.absencePercentage)) {
                            isMixed.push(mixedConfig.absencePercentage);
                        }
                    } else {
                        courseElement.mixedConfig = null;
                    }
                }
                studentsWithDenialCourseForNoSchedule.push({
                    studentName: groupedStudentsWithCourses[studentElement].name,
                    studentId: studentElement,
                    academicId: groupedStudentsWithCourses[studentElement].academicId,
                    enrolledCourses: groupedStudentsWithCourses[studentElement].courseId.sort(
                        (a, b) => {
                            if (a.courseName.toLowerCase() < b.courseName.toLowerCase()) return -1;
                            if (a.courseName.toLowerCase() > b.courseName.toLowerCase()) return 1;
                            return 0;
                        },
                    ),
                    // eslint-disable-next-line no-unneeded-ternary
                    mixed: mixedCount > 0 ? (isMixed.length > 1 ? true : isMixed[0]) : false,
                });
            }
            return {
                data: {
                    studentsWithDenialCourse: sortArrayByName(
                        studentsWithDenialCourseForNoSchedule,
                    ),
                    denialPercentage: denialConfig.isActive
                        ? sortedWarningConfig[sortedWarningConfig.length - 1].percentage
                        : denialConfig.percentage,
                    categoryWisePercentage: denialConfig.isActive
                        ? sortedWarningConfig[sortedWarningConfig.length - 1].categoryWisePercentage
                        : denialConfig.categoryWisePercentage,
                    denialCondition:
                        sortedWarningConfig[sortedWarningConfig.length - 1].denialCondition,
                    colorCode,
                    totalCount: Object.keys(groupedStudentsWithCourses).length,
                },
                statusCode: 200,
                message: 'SUCCESS',
            };
        }
        const denialCourseIds = [];
        const denialStudentIds = [];
        for (const denialCourseElement of denialCourses) {
            denialStudentIds.push(convertToMongoObjectId(denialCourseElement._id));
            denialCourseIds.push(
                ...denialCourseElement.data.map((courseElement) =>
                    convertToMongoObjectId(courseElement.courseId),
                ),
            );
        }
        const lmsStudent = await lmsStudentModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    studentId: {
                        $in: denialStudentIds,
                    },
                    classificationType: LEAVE_TYPE.LEAVE,
                    programId: convertToMongoObjectId(programId),
                    level: level_no,
                    year,
                    approvalStatus: constants.APPROVED,
                    isDeleted: false,
                    isActive: true,
                },
                { categoryName: 1, studentId: 1, noOfHours: 1 },
            )
            .lean();
        const studentsWithDenialCourse = [];
        for (const studentElement of studentIds) {
            let mixedCount = 0;
            const isMixed = [];

            for (const courseElement of groupedStudentsWithCourses[studentElement].courseId) {
                const studentCourse = denialCourses.find(
                    (courseScheduleElement) =>
                        courseScheduleElement._id.toString() === studentElement.toString(),
                );
                if (studentCourse) {
                    const course = studentCourse.data.find(
                        (studentGroupElement) =>
                            studentGroupElement.courseId.toString() ===
                            courseElement.courseId.toString(),
                    );
                    const mixedDenialPercentage = mixedDenialStudents.find(
                        (denialStudentElement) =>
                            denialStudentElement.studentId &&
                            studentElement.toString() ===
                                denialStudentElement.studentId.toString() &&
                            denialStudentElement.term === term &&
                            (courseElement.rotation === 'yes'
                                ? parseInt(courseElement.rotation_count) ===
                                  denialStudentElement.rotationCount
                                : true) &&
                            denialStudentElement.courseId.toString() ===
                                courseElement.courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === STUDENT_WISE,
                    );
                    const mixedDenialPercentageFromCourse = mixedDenialStudents.find(
                        (lmsDenialStudentElement) =>
                            lmsDenialStudentElement.courseId.toString() ===
                                courseElement.courseId.toString() &&
                            lmsDenialStudentElement.typeWiseUpdate === COURSE_WISE &&
                            lmsDenialStudentElement.term === term &&
                            (courseElement.rotation === 'yes'
                                ? lmsDenialStudentElement.rotationCount ===
                                  parseInt(courseElement.rotation_count)
                                : true),
                    );
                    if (course) {
                        courseElement.isScheduled = true;
                        const denialStudentsWithLeave = lmsStudent.reduce((acc, curr) => {
                            if (curr.studentId.toString() === studentElement) {
                                if (course.scheduled_dates.length) {
                                    const isScheduleAffectedByLeave = course.scheduled_dates.find(
                                        (scheduleStartDateElement) =>
                                            scheduleStartDateElement &&
                                            new Date(scheduleStartDateElement) >=
                                                new Date(curr.dateAndTimeRange.startDate) &&
                                            new Date(scheduleStartDateElement) <=
                                                new Date(curr.dateAndTimeRange.endDate),
                                    );
                                    if (isScheduleAffectedByLeave) {
                                        if (acc[curr.studentId]) {
                                            if (acc[curr.studentId][curr.categoryName]) {
                                                acc[curr.studentId][curr.categoryName] += 1;
                                            } else {
                                                acc[curr.studentId][curr.categoryName] = 1;
                                            }
                                        } else {
                                            acc[curr.studentId] = {
                                                [curr.categoryName]: 1,
                                            };
                                        }
                                    }
                                }
                            }
                            return acc;
                        }, {});
                        if (course.unAppliedLeaves > 0) {
                            if (
                                sortedWarningConfig[sortedWarningConfig.length - 1]
                                    .unappliedLeaveConsideredAs !== ''
                            ) {
                                if (!denialStudentsWithLeave[studentElement]) {
                                    denialStudentsWithLeave[studentElement] = {};
                                }
                                if (
                                    denialStudentsWithLeave[studentElement][
                                        sortedWarningConfig[sortedWarningConfig.length - 1]
                                            .unappliedLeaveConsideredAs
                                    ]
                                ) {
                                    denialStudentsWithLeave[studentElement][
                                        sortedWarningConfig[
                                            sortedWarningConfig.length - 1
                                        ].unappliedLeaveConsideredAs
                                    ] += course.unAppliedLeaves;
                                } else {
                                    denialStudentsWithLeave[studentElement][
                                        sortedWarningConfig[
                                            sortedWarningConfig.length - 1
                                        ].unappliedLeaveConsideredAs
                                    ] = course.unAppliedLeaves;
                                }
                            }
                        }
                        if (denialStudentsWithLeave[studentElement]) {
                            const absentDetails = Object.keys(
                                denialStudentsWithLeave[studentElement],
                            ).reduce((acc, curr) => {
                                const absentDetailObject = {
                                    categoryName: curr,
                                    percentage:
                                        (denialStudentsWithLeave[studentElement][curr] /
                                            course.totalSchedules) *
                                        100,
                                };
                                acc.push(absentDetailObject);
                                return acc;
                            }, []);
                            courseElement.absentDetails = absentDetails;
                        }
                        const mixedData = mixedDenialPercentage || mixedDenialPercentageFromCourse;
                        const status = sortedWarningConfig.reduce((acc, curr) => {
                            if (Number(course.absentPercentage) > Number(curr.percentage)) {
                                if (mixedData) {
                                    if (
                                        Number(course.absentPercentage) >
                                        Number(mixedData.absencePercentage)
                                    ) {
                                        acc =
                                            sortedWarningConfig[sortedWarningConfig.length - 1]
                                                .labelName;
                                    } else {
                                        acc = sortedWarningConfig[sortedWarningConfig.length - 2]
                                            ? sortedWarningConfig[sortedWarningConfig.length - 2]
                                                  .labelName
                                            : '';
                                    }
                                } else {
                                    if (
                                        Number(course.absentPercentage) >
                                        Number(
                                            sortedWarningConfig[sortedWarningConfig.length - 1]
                                                .percentage,
                                        )
                                    ) {
                                        acc =
                                            sortedWarningConfig[sortedWarningConfig.length - 1]
                                                .labelName;
                                    } else {
                                        acc = curr.labelName;
                                    }
                                }
                            }
                            if (
                                sortedWarningConfig[
                                    sortedWarningConfig.length - 1
                                ].denialCondition.toLowerCase() === INDIVIDUAL
                            ) {
                                if (
                                    courseElement.absentDetails &&
                                    courseElement.absentDetails.length
                                ) {
                                    const newMixedData =
                                        mixedData ||
                                        sortedWarningConfig[sortedWarningConfig.length - 1];

                                    for (const categoryElement of newMixedData.categoryWisePercentage) {
                                        const absentPercentage = courseElement.absentDetails.find(
                                            (percentageElement) =>
                                                percentageElement.categoryName.toLowerCase() ===
                                                categoryElement.categoryName.toLowerCase(),
                                        );
                                        if (
                                            absentPercentage &&
                                            Number(absentPercentage.percentage) >
                                                Number(categoryElement.percentage)
                                        ) {
                                            acc =
                                                sortedWarningConfig[sortedWarningConfig.length - 1]
                                                    .labelName;
                                        }
                                    }
                                }
                            }
                            return acc;
                        }, '');
                        courseElement.status = status;
                        courseElement.absentPercentage = Number(course.absentPercentage);
                        courseElement.presentPercentage = Number(course.presentPercentage);
                    }
                    const mixedConfig = mixedDenialPercentage || mixedDenialPercentageFromCourse;
                    if (
                        mixedConfig &&
                        mixedConfig.absencePercentage !==
                            sortedWarningConfig[sortedWarningConfig.length - 1].percentage
                    ) {
                        mixedCount++;
                        courseElement.mixedConfig = {
                            absencePercentage: mixedConfig.absencePercentage,
                            categoryWisePercentage: mixedConfig.categoryWisePercentage,
                            denialCondition: mixedConfig.denialCondition,
                        };
                        if (!isMixed.includes(mixedConfig.absencePercentage)) {
                            isMixed.push(mixedConfig.absencePercentage);
                        }
                    } else {
                        courseElement.mixedConfig = null;
                    }
                }
            }
            studentsWithDenialCourse.push({
                studentName: groupedStudentsWithCourses[studentElement].name,
                studentId: studentElement,
                academicId: groupedStudentsWithCourses[studentElement].academicId,
                enrolledCourses: groupedStudentsWithCourses[studentElement].courseId.sort(
                    (a, b) => {
                        if (a.courseName.toLowerCase() < b.courseName.toLowerCase()) return -1;
                        if (a.courseName.toLowerCase() > b.courseName.toLowerCase()) return 1;
                        return 0;
                    },
                ),
                // denialWarning: sortedWarningConfig[sortedWarningConfig.length - 1].labelName,
                mixed:
                    mixedCount > 0
                        ? mixedCount > 1
                            ? isMixed.length && isMixed.length === 1
                                ? isMixed[0]
                                : true
                            : groupedStudentsWithCourses[studentElement].courseId.length === 1
                            ? isMixed[0]
                            : groupedStudentsWithCourses[studentElement].courseId.length !== 1
                        : false,
                // if all the courses have same denial  it becomes the denial of  the student
                // if only one course is changed or multiple courses with different percentage  it's mixed
            });
        }
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: {
                studentsWithDenialCourse: sortArrayByName(studentsWithDenialCourse),
                denialPercentage: denialConfig.isActive
                    ? sortedWarningConfig[sortedWarningConfig.length - 1].percentage
                    : denialConfig.percentage,
                categoryWisePercentage: denialConfig.isActive
                    ? sortedWarningConfig[sortedWarningConfig.length - 1].categoryWisePercentage
                    : denialConfig.categoryWisePercentage,
                denialCondition:
                    sortedWarningConfig[sortedWarningConfig.length - 1].denialCondition,
                colorCode,
                totalCount: Object.keys(groupedStudentsWithCourses).length,
                denialWarning: denialConfig.isActive
                    ? sortedWarningConfig[sortedWarningConfig.length - 1].labelName
                    : '',
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateDenialPercentage = async ({ body = {}, headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { denialCondition, _institution_calendar_id } = body;
        let { denialManagement } = body;
        const bulkUpdate = [];
        const bulkInserts = [];
        const checkTypeWiseUpdate = denialManagement.find(
            (denialElement) => denialElement.typeWiseUpdate === COURSE_WISE,
        );
        if (checkTypeWiseUpdate) {
            await lmsDenialModel.deleteMany({
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                programId: convertToMongoObjectId(checkTypeWiseUpdate.programId),
                courseId: convertToMongoObjectId(checkTypeWiseUpdate.courseId),
                term: checkTypeWiseUpdate.term,
                yearNo: checkTypeWiseUpdate.yearNo,
                levelNo: checkTypeWiseUpdate.levelNo,
                rotation: checkTypeWiseUpdate.rotation,
                ...(checkTypeWiseUpdate.rotation === 'yes' && {
                    rotationCount: checkTypeWiseUpdate.rotationCount,
                }),
            });
            await lmsDenialModel.create({
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                programId: convertToMongoObjectId(checkTypeWiseUpdate.programId),
                courseId: convertToMongoObjectId(checkTypeWiseUpdate.courseId),
                denialCondition,
                updateBy: convertToMongoObjectId(_user_id),
                term: checkTypeWiseUpdate.term,
                yearNo: checkTypeWiseUpdate.yearNo,
                levelNo: checkTypeWiseUpdate.levelNo,
                rotation: checkTypeWiseUpdate.rotation,
                typeWiseUpdate: checkTypeWiseUpdate.typeWiseUpdate,
                ...(checkTypeWiseUpdate.rotation === 'yes' && {
                    rotationCount: checkTypeWiseUpdate.rotationCount,
                }),
                ...(checkTypeWiseUpdate.studentId && {
                    studentId: convertToMongoObjectId(checkTypeWiseUpdate.studentId),
                }),
                absencePercentage: checkTypeWiseUpdate.absencePercentage,
                categoryWisePercentage: checkTypeWiseUpdate.categoryWisePercentage,
                isDeleted: false,
                isActive: true,
            });
            await lmsDenialLogModel.create({
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                programId: convertToMongoObjectId(checkTypeWiseUpdate.programId),
                courseId: convertToMongoObjectId(checkTypeWiseUpdate.courseId),
                denialCondition,
                updateBy: convertToMongoObjectId(_user_id),
                term: checkTypeWiseUpdate.term,
                yearNo: checkTypeWiseUpdate.yearNo,
                levelNo: checkTypeWiseUpdate.levelNo,
                rotation: checkTypeWiseUpdate.rotation,
                typeWiseUpdate: checkTypeWiseUpdate.typeWiseUpdate,
                ...(checkTypeWiseUpdate.rotation === 'yes' && {
                    rotationCount: checkTypeWiseUpdate.rotationCount,
                }),
                ...(checkTypeWiseUpdate.studentId && {
                    studentId: convertToMongoObjectId(checkTypeWiseUpdate.studentId),
                }),
                absencePercentage: checkTypeWiseUpdate.absencePercentage,
                categoryWisePercentage: checkTypeWiseUpdate.categoryWisePercentage,
                isDeleted: false,
                isActive: true,
            });
        }
        denialManagement = denialManagement.filter(
            (manageElement) => manageElement.typeWiseUpdate === STUDENT_WISE,
        );
        if (denialManagement.length) {
            if (denialCondition === CUMULATIVE) {
                for (const denialManagementElement of denialManagement) {
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                _institution_calendar_id:
                                    convertToMongoObjectId(_institution_calendar_id),
                                _institution_id: convertToMongoObjectId(_institution_id),
                                programId: convertToMongoObjectId(
                                    denialManagementElement.programId,
                                ),
                                courseId: convertToMongoObjectId(denialManagementElement.courseId),
                                denialCondition: CUMULATIVE,
                                updateBy: convertToMongoObjectId(_user_id),
                                term: denialManagementElement.term,
                                yearNo: denialManagementElement.yearNo,
                                levelNo: denialManagementElement.levelNo,
                                rotation: denialManagementElement.rotation,
                                typeWiseUpdate: denialManagementElement.typeWiseUpdate,
                                ...(denialManagementElement.rotation === 'yes' && {
                                    rotationCount: denialManagementElement.rotationCount,
                                }),
                                ...(denialManagementElement.studentId && {
                                    studentId: convertToMongoObjectId(
                                        denialManagementElement.studentId,
                                    ),
                                }),
                                isDeleted: false,
                                isActive: true,
                            },
                            update: {
                                absencePercentage: denialManagementElement.absencePercentage,
                            },
                            upsert: true,
                            setDefaultsOnInsert: true,
                        },
                    });
                    bulkInserts.push({
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        programId: convertToMongoObjectId(denialManagementElement.programId),
                        courseId: convertToMongoObjectId(denialManagementElement.courseId),
                        denialCondition: CUMULATIVE,
                        updateBy: convertToMongoObjectId(_user_id),
                        term: denialManagementElement.term,
                        yearNo: denialManagementElement.yearNo,
                        levelNo: denialManagementElement.levelNo,
                        rotation: denialManagementElement.rotation,
                        ...(denialManagementElement.rotation === 'yes' && {
                            rotationCount: denialManagementElement.rotationCount,
                        }),
                        typeWiseUpdate: denialManagementElement.typeWiseUpdate,
                        ...(denialManagementElement.studentId && {
                            studentId: convertToMongoObjectId(denialManagementElement.studentId),
                        }),
                        absencePercentage: denialManagementElement.absencePercentage,
                    });
                }
            }
            if (denialCondition === INDIVIDUAL) {
                for (const denialManagementElement of denialManagement) {
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                _institution_calendar_id:
                                    convertToMongoObjectId(_institution_calendar_id),
                                _institution_id: convertToMongoObjectId(_institution_id),
                                programId: convertToMongoObjectId(
                                    denialManagementElement.programId,
                                ),
                                courseId: convertToMongoObjectId(denialManagementElement.courseId),
                                denialCondition: INDIVIDUAL,
                                updateBy: convertToMongoObjectId(_user_id),
                                term: denialManagementElement.term,
                                yearNo: denialManagementElement.yearNo,
                                levelNo: denialManagementElement.levelNo,
                                rotation: denialManagementElement.rotation,
                                typeWiseUpdate: denialManagementElement.typeWiseUpdate,
                                ...(denialManagementElement.rotation === 'yes' && {
                                    rotationCount: denialManagementElement.rotationCount,
                                }),
                                ...(denialManagementElement.studentId && {
                                    studentId: convertToMongoObjectId(
                                        denialManagementElement.studentId,
                                    ),
                                }),
                                isDeleted: false,
                                isActive: true,
                            },
                            update: {
                                absencePercentage: denialManagementElement.absencePercentage,
                                categoryWisePercentage:
                                    denialManagementElement.categoryWisePercentage,
                            },
                            upsert: true,
                            setDefaultsOnInsert: true,
                        },
                    });
                    bulkInserts.push({
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        programId: convertToMongoObjectId(denialManagementElement.programId),
                        courseId: convertToMongoObjectId(denialManagementElement.courseId),
                        denialCondition: INDIVIDUAL,
                        updateBy: convertToMongoObjectId(_user_id),
                        term: denialManagementElement.term,
                        yearNo: denialManagementElement.yearNo,
                        levelNo: denialManagementElement.levelNo,
                        rotation: denialManagementElement.rotation,
                        typeWiseUpdate: denialManagementElement.typeWiseUpdate,
                        categoryWisePercentage: denialManagementElement.categoryWisePercentage,
                        absencePercentage: denialManagementElement.absencePercentage,
                        ...(denialManagementElement.rotation === 'yes' && {
                            rotationCount: denialManagementElement.rotationCount,
                        }),
                        ...(denialManagementElement.studentId && {
                            studentId: convertToMongoObjectId(denialManagementElement.studentId),
                        }),
                    });
                }
            }
            if (bulkUpdate.length) {
                const data = await lmsDenialModel.bulkWrite(bulkUpdate);
                const dataInsertLogModel = await lmsDenialLogModel.insertMany(bulkInserts);
            }
        }
        const { denialLabel, warningAbsenceData } = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id,
        });
        const { _id: denialWarningId } = warningAbsenceData.find(
            (warningElement) => warningElement.labelName === denialLabel,
        ) || { _id: '' };
        // previous warning of denial
        const thirdWarningId = warningAbsenceData[1]?._id;

        const denialPercentage = warningAbsenceData[0]?.percentage;
        if (checkTypeWiseUpdate) {
            const {
                programId,
                yearNo,
                levelNo,
                rotation,
                rotationCount,
                term,
                courseId,
                absencePercentage,
            } = checkTypeWiseUpdate;
            if (denialPercentage <= absencePercentage) {
                const warningRecordKey = `${
                    constants.STUDENT_WARNING_REDIS
                }:${_institution_calendar_id.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
                    rotationCount || null
                }`;
                const studentWarningRecordsMap = await getWarningDataFromRedis(
                    new Map().set(warningRecordKey, null),
                );
                studentWarningRecords = studentWarningRecordsMap.get(warningRecordKey);
                if (studentWarningRecords) {
                    const denialWarningStudents =
                        studentWarningRecords[denialWarningId?.toString()] ?? [];
                    const thirdWarningStudents =
                        studentWarningRecords[thirdWarningId?.toString()] ?? [];
                    if (thirdWarningId) {
                        studentWarningRecords[thirdWarningId?.toString()] = [
                            ...thirdWarningStudents,
                            ...denialWarningStudents,
                        ];
                    }
                    studentWarningRecords[denialWarningId.toString()] = [];
                    await studentWarningRecordSchema.updateOne(
                        {
                            institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
                            programId: convertToMongoObjectId(programId),
                            courseId: convertToMongoObjectId(courseId),
                            yearNo,
                            levelNo,
                            term,
                            ...(rotationCount && { rotationCount }),
                        },
                        {
                            $set: {
                                warnings: studentWarningRecords,
                            },
                        },
                    );
                    await redisClient.Client.del(warningRecordKey);
                }
            }
        } else {
            // handle each student separately
            const bulkUpdateList = [];
            const redisKeyRemoveList = [];
            const { programId, courseId, yearNo, levelNo, term } = denialManagement.reduce(
                (acc, curr) => {
                    acc.courseId.push(convertToMongoObjectId(curr.courseId));
                    acc.programId.push(convertToMongoObjectId(curr.programId));
                    if (!acc.yearNo.includes(curr.yearNo)) acc.yearNo.push(curr.yearNo);
                    if (!acc.levelNo.includes(curr.levelNo)) acc.levelNo.push(curr.levelNo);
                    if (!acc.term.includes(curr.term)) acc.term.push(curr.term);
                    return acc;
                },
                { programId: [], courseId: [], yearNo: [], levelNo: [], term: [] },
            );
            const denialStudents = await studentWarningRecordSchema
                .find({
                    institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
                    programId: { $in: programId },
                    courseId: {
                        $in: courseId,
                    },
                    yearNo: { $in: yearNo },
                    levelNo: { $in: levelNo },
                    term: { $in: term },
                })
                .lean();
            if (denialStudents.length) {
                for (const studentElement of denialManagement) {
                    if (denialPercentage <= studentElement.absencePercentage) {
                        const matchedDoc = denialStudents.find(
                            ({ programId, courseId, yearNo, levelNo, term, rotationCount }) =>
                                studentElement.programId.toString() === programId.toString() &&
                                studentElement.courseId.toString() === courseId.toString() &&
                                yearNo === studentElement.yearNo &&
                                levelNo === studentElement.levelNo &&
                                term === studentElement.term &&
                                (studentElement.rotationCount
                                    ? studentElement.rotationCount == rotationCount
                                    : true),
                        );

                        if (matchedDoc) {
                            const existingWarnings = matchedDoc.warnings;
                            const denialWarningStudents =
                                matchedDoc.warnings[denialWarningId?.toString()] ?? [];
                            const thirdWarningStudents =
                                matchedDoc.warnings[thirdWarningId?.toString()] ?? [];
                            existingWarnings[thirdWarningId?.toString()] = [
                                ...thirdWarningStudents,
                                studentElement.studentId.toString(),
                            ];
                            existingWarnings[denialWarningId?.toString()] =
                                denialWarningStudents.filter(
                                    (denialWarningStElement) =>
                                        denialWarningStElement !==
                                        studentElement.studentId.toString(),
                                );

                            bulkUpdateList.push({
                                updateOne: {
                                    filter: {
                                        institutionCalendarId:
                                            convertToMongoObjectId(_institution_calendar_id),
                                        programId: convertToMongoObjectId(studentElement.programId),
                                        courseId: convertToMongoObjectId(studentElement.courseId),
                                        yearNo: studentElement.yearNo,
                                        levelNo: studentElement.levelNo,
                                        term: studentElement.term,
                                        ...(studentElement.rotationCount && {
                                            rotationCount: studentElement.rotationCount,
                                        }),
                                    },
                                    update: {
                                        $set: { warnings: existingWarnings },
                                    },
                                },
                            });
                            redisKeyRemoveList.push(
                                `${
                                    constants.STUDENT_WARNING_REDIS
                                }:${_institution_calendar_id.toString()}_${studentElement.programId.toString()}_${studentElement.courseId.toString()}_${
                                    studentElement.yearNo
                                }_${studentElement.levelNo}_${studentElement.term}_${
                                    studentElement.rotationCount
                                        ? studentElement.rotationCount
                                        : null
                                }`,
                            );
                        }
                    }
                }
                if (bulkUpdateList.length)
                    await studentWarningRecordSchema.bulkWrite(bulkUpdateList);
                if (redisKeyRemoveList.length) await redisClient.Client.del(redisKeyRemoveList);
            }
        }
        return { status: 200, message: 'DATA_UPDATE' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.listDenialData = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_calendar_id, _institution_id } = headers;
        const { courseWise, studentWise } = body;
        if (courseWise) {
            const lmsDenialLogData = await lmsDenialLogModel
                .find({
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    programId: convertToMongoObjectId(courseWise.programId),
                    courseId: convertToMongoObjectId(courseWise.courseId),
                    term: courseWise.term,
                    levelNo: courseWise.levelNo,
                    yearNo: courseWise.yearNo,
                    isDeleted: false,
                    isActive: true,
                    studentId: {
                        $in: courseWise.studentIds.map((id) => convertToMongoObjectId(id)),
                    },
                })
                .populate({ path: 'programId', select: { name: 1, code: 1 } })
                .populate({ path: 'courseId', select: { course_name: 1, course_code: 1 } })
                .populate({ path: 'studentId', select: { name: 1, user_id: 1 } });
            if (!lmsDenialData) return { statusCode: 410, message: 'DATA_NOT_FOUND' };
            return { statusCode: 200, message: 'LIST_DATA', data: lmsDenialData };
        }
        if (studentWise) {
            const lmsDenialLogData = await lmsDenialLogModel
                .find({
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    programId: convertToMongoObjectId(studentWise.programId),
                    term: studentWise.term,
                    levelNo: studentWise.levelNo,
                    yearNo: studentWise.yearNo,
                    isDeleted: false,
                    isActive: true,
                    studentId: convertToMongoObjectId(studentWise.studentId),
                    courseId: {
                        $in: studentWise.courseIds.map((id) => convertToMongoObjectId(id)),
                    },
                })
                .populate({ path: 'programId', select: { name: 1, code: 1 } })
                .populate({ path: 'courseId', select: { course_name: 1, course_code: 1 } })
                .populate({ path: 'studentId', select: { name: 1, user_id: 1 } });
            if (!lmsDenialLogData) return { statusCode: 410, message: 'DATA_NOT_FOUND' };
            return { statusCode: 200, message: 'LIST_DATA', data: lmsDenialLogData };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getStudentsWithWarningStats = async ({ query = {}, headers = {} }) => {
    try {
        const {
            level_no,
            year,
            programId,
            term,
            rotation,
            rotationCount,
            courseId,
            pageNo,
            limit,
            searchKey,
            warningStatus,
            studentIds,
            _institution_calendar_id,
        } = query;
        const { _institution_id } = headers;
        const pagination = getPaginationValues({ limit, pageNo });
        const lmsStudentSettings = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                    'warningConfig.isActive': true,
                }, // leave type  is hardcoded
                {
                    leaveCalculation: 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.denialCondition': 1,
                    'warningConfig.categoryWisePercentage': 1,
                    'warningConfig.unappliedLeaveConsideredAs': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.isActive': 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const { leaveCalculation } = lmsStudentSettings;
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId,
            programId,
            yearNo: year,
            levelNo: level_no,
            term,
            rotationCount,
        });
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        let { warningConfig } = lmsStudentSettings;
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig;
        const denialLabelName = warningConfig[warningConfig.length - 1].labelName;
        for (let i = warningConfig.length - 1; i >= 0; i--) {
            if (
                (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
                denialConfig == null
            ) {
                denialConfig = warningConfig[i];
            }
            if (!warningConfig[i].isActive) {
                warningConfig.splice(i, 1);
            }
        }
        const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
            let comparison = 0;
            if (Number(a.percentage) > Number(b.percentage)) {
                comparison = -1;
            } else if (Number(a.percentage) < Number(b.percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        const totalStudents = await getParticularCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            rotation,
            rotationCount: parseInt(rotationCount),
            term,
            _institution_calendar_id,
            _institution_id,
            courseIds: [courseId],
        });
        let totalCount;
        let studentsFilter = {};
        let nameFilter = {};
        if (studentIds && studentIds.length) {
            studentsFilter = {
                _id: {
                    $in: studentIds.map((studentElement) => convertToMongoObjectId(studentElement)),
                },
            };
        }
        if (searchKey) {
            const searchWords = searchKey.trim().split(/\s+/);
            nameFilter = {
                $or: [
                    {
                        $and: [
                            { 'studentName.first': { $regex: new RegExp(searchWords[0], 'i') } },
                            { 'studentName.last': { $regex: new RegExp(searchWords[1], 'i') } },
                        ],
                    },
                    { 'studentName.first': { $regex: new RegExp(searchKey, 'i') } },
                    { 'studentName.last': { $regex: new RegExp(searchKey, 'i') } },
                    { academicId: { $regex: new RegExp(searchKey, 'i') } },
                ],
            };
        }
        let courseSchedules;
        if (leaveCalculation === 'hours') {
            courseSchedules = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                        _course_id: convertToMongoObjectId(courseId),
                        type: 'regular',
                        year_no: year,
                        rotation: rotation || 'no',
                        ...(rotation === 'yes' && { rotation_count: rotationCount }),
                        level_no,
                        term,
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $match: {
                        'students._id': {
                            $in: totalStudents.map((student) =>
                                convertToMongoObjectId(student.studentId),
                            ),
                        },
                        'students.status': { $not: { $eq: EXCLUDE } },
                    },
                },
                {
                    $group: {
                        _id: {
                            studentId: '$students._id',
                            sessionType: '$session.session_type',
                        },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    { $eq: ['$status', 'completed'] },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        studentName: { $first: '$students.name' },
                        totalSchedules: {
                            $sum: 1,
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [{ $eq: ['$status', 'completed'] }, 1, 0],
                            },
                        },
                        rotation: { $first: '$rotation' },
                        rotation_count: { $first: '$rotation_count' },
                        courseName: { $first: '$course_name' },
                        course_code: { $first: '$course_code' },
                        courseId: { $first: '$_course_id' },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        unAppliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'absent'] },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeaves: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'leave'] },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $match: {
                        totalSchedules: { $gt: 0 },
                    },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        let: {
                            courseId: '$courseId',
                            sessionType: '$_id.sessionType',
                        },
                        pipeline: [
                            {
                                $unwind: {
                                    path: '$credit_hours',
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $unwind: {
                                    path: '$credit_hours.delivery_type',
                                    preserveNullAndEmptyArrays: true,
                                },
                            },
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$_id', '$$courseId'] },
                                            {
                                                $eq: [
                                                    '$credit_hours.delivery_type.delivery_type',
                                                    '$$sessionType',
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    'credit_hours.delivery_type.duration': 1,
                                },
                            },
                        ],
                        as: 'creditHoursDuration',
                    },
                },
                {
                    $addFields: {
                        sessionCreditHours: {
                            $divide: [
                                {
                                    $arrayElemAt: [
                                        '$creditHoursDuration.credit_hours.delivery_type.duration',
                                        0,
                                    ],
                                },
                                60,
                            ],
                        },
                    },
                },
                {
                    $addFields: {
                        leavedSchedules: { $multiply: ['$sessionCreditHours', '$leavedSchedules'] },
                        presentedSchedules: {
                            $multiply: ['$sessionCreditHours', '$presentedSchedules'],
                        },
                        totalCompletedSchedules: {
                            $multiply: ['$sessionCreditHours', '$totalCompletedSchedules'],
                        },
                        totalSchedules: { $multiply: ['$sessionCreditHours', '$totalSchedules'] },
                        appliedSchedules: { $multiply: ['$sessionCreditHours', '$appliedLeaves'] },
                        unAppliedLeaves: { $multiply: ['$sessionCreditHours', '$unAppliedLeaves'] },
                    },
                },
                {
                    $unwind: {
                        path: '$scheduled_dates',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $group: {
                        _id: '$_id.studentId',
                        scheduled_dates: {
                            $push: '$scheduled_dates',
                        },
                        courseName: { $first: '$courseName' },
                        rotation: { $first: '$rotation' },
                        rotation_count: { $first: '$rotation_count' },
                        course_code: { $first: '$course_code' },
                        courseId: { $first: '$courseId' },
                        studentName: { $first: '$studentName' },
                        totalSchedules: { $sum: '$totalSchedules' },
                        totalCompletedSchedules: { $sum: '$totalCompletedSchedules' },
                        leavedSchedules: { $sum: '$leavedSchedules' },
                        presentedSchedules: { $sum: '$presentedSchedules' },
                        unAppliedLeaves: { $sum: '$unAppliedLeaves' },
                        appliedLeaves: { $sum: '$appliedLeaves' },
                    },
                },
                {
                    $addFields: {
                        totalCount: { $add: 1 },
                    },
                },
                {
                    $match: studentsFilter,
                },
                {
                    $lookup: {
                        from: constants.USER,
                        localField: '_id',
                        foreignField: '_id',
                        as: 'user',
                        pipeline: [
                            {
                                $project: {
                                    _id: 0,
                                    academicId: '$user_id',
                                },
                            },
                        ],
                    },
                },
                {
                    $group: {
                        _id: null,
                        data: { $push: '$$ROOT' },
                        totalCount: { $sum: 1 },
                    },
                },
                {
                    $unwind: '$data',
                },
                {
                    $project: {
                        _id: 0,
                        totalCount: '$totalCount',
                        studentId: '$data._id',
                        studentName: '$data.studentName',
                        courseId: '$data.courseId',
                        totalSchedules: '$data.totalSchedules',
                        totalCompletedSchedules: '$data.totalCompletedSchedules',
                        course_name: '$data.courseName',
                        course_code: '$data.course_code',
                        scheduled_dates: '$data.scheduled_dates',
                        absentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$data.totalSchedules', 0] },
                                        0,
                                        {
                                            $divide: [
                                                '$data.leavedSchedules',
                                                '$data.totalSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$data.totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$data.presentedSchedules',
                                                '$data.totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        academicId: { $arrayElemAt: ['$data.user.academicId', 0] },
                        unAppliedLeaveSchedules: '$data.unAppliedLeaves',
                        // status: reverseSortedWarningConfig[0].labelName,
                    },
                },
                {
                    $match: nameFilter,
                },
                {
                    $sort: { studentName: 1 },
                },
            ]);
        } else {
            courseSchedules = await courseScheduleModel.aggregate([
                {
                    $match: {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                        _course_id: convertToMongoObjectId(courseId),
                        type: 'regular',
                        year_no: year,
                        level_no,
                        term,
                        rotation: rotation || 'no',
                        ...(rotation === 'yes' && { rotation_count: parseInt(rotationCount) }),
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $unwind: {
                        path: '$students',
                    },
                },
                {
                    $match: {
                        'students._id': {
                            $in: totalStudents.map((student) =>
                                convertToMongoObjectId(student.studentId),
                            ),
                        },
                        'students.status': { $not: { $eq: EXCLUDE } },
                    },
                },
                {
                    $group: {
                        _id: '$students._id',
                        rotation: { $first: '$rotation' },
                        scheduled_dates: {
                            $push: {
                                $cond: [
                                    { $eq: ['$status', 'completed'] },
                                    '$scheduleStartDateAndTime',
                                    { $literal: null },
                                ],
                            },
                        },
                        schedules: {
                            $push: {
                                students: '$students',
                                status: '$status',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                _institution_calendar_id: '$_institution_calendar_id',
                                programId: '$_program_id',
                                courseId: '$_course_id',
                                term: '$term',
                                levelNo: '$level_no',
                                rotationCount: '$rotation_count',
                                session: '$session',
                                sessionDetail: '$sessionDetail',
                            },
                        },
                        presentedSchedulesWithoutOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            { $eq: ['$students.status', 'present'] },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedulesBasedOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        rotation_count: { $first: '$rotation_count' },
                        studentName: { $first: '$students.name' },
                        courseName: { $first: '$course_name' },
                        course_code: { $first: '$course_code' },
                        courseId: { $first: '$_course_id' },
                        totalSchedules: {
                            $sum: 1,
                        },
                        totalCompletedSchedules: {
                            $sum: {
                                $cond: [{ $eq: ['$status', 'completed'] }, 1, 0],
                            },
                        },
                        unAppliedLeavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'absent'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        appliedLeavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: ['$students.status', 'leave'],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        leavedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            {
                                                $eq: ['$status', 'completed'],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        presentedSchedules: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'present'] },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.LEAVE_TYPE.PERMISSION,
                                                        ],
                                                    },
                                                    {
                                                        $eq: [
                                                            '$students.status',
                                                            constants.ON_DUTY,
                                                        ],
                                                    },
                                                ],
                                            },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $match: {
                        totalSchedules: { $gt: 0 },
                    },
                },
                {
                    $addFields: {
                        totalCount: { $add: 1 },
                    },
                },
                {
                    $match: studentsFilter,
                },
                {
                    $lookup: {
                        from: constants.USER,
                        localField: '_id',
                        foreignField: '_id',
                        as: 'user',
                        // pipeline: [
                        //     {
                        //         $project: {
                        //             _id: 0,
                        //             academicId: '$user_id',
                        //         },
                        //     },
                        // ],
                    },
                },
                {
                    $group: {
                        _id: null,
                        data: { $push: '$$ROOT' },
                        totalCount: { $sum: 1 },
                    },
                },
                {
                    $unwind: '$data',
                },
                {
                    $project: {
                        _id: 0,
                        rotation: '$data.rotation',
                        rotation_count: '$data.rotation_count',
                        studentId: '$data._id',
                        scheduled_dates: '$data.scheduled_dates',
                        studentName: '$data.studentName',
                        courseId: '$data.courseId',
                        totalCount: '$totalCount',
                        leavedSchedules: '$data.leavedSchedules',
                        presentedSchedulesWithoutOnDuty: '$data.presentedSchedulesWithoutOnDuty',
                        presentedSchedulesBasedOnDuty: '$data.presentedSchedulesBasedOnDuty',
                        schedules: '$data.schedules',
                        unAppliedLeaveSchedules: '$data.unAppliedLeavedSchedules',
                        totalSchedules: '$data.totalSchedules',
                        totalCompletedSchedules: '$data.totalCompletedSchedules',
                        course_name: '$data.courseName',
                        course_code: '$data.course_code',
                        academicId: { $arrayElemAt: ['$data.user.user_id', 0] },
                        absentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$data.totalSchedules', 0] },
                                        0,
                                        {
                                            $divide: [
                                                '$data.leavedSchedules',
                                                '$data.totalSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        presentPercentage: {
                            $multiply: [
                                {
                                    $cond: [
                                        { $eq: ['$data.totalCompletedSchedules', 0] }, // Check if totalCompletedSchedules is 0
                                        0, // Return 0 as the default percentage
                                        {
                                            $divide: [
                                                '$data.presentedSchedules',
                                                '$data.totalCompletedSchedules',
                                            ],
                                        },
                                    ],
                                },
                                100,
                            ],
                        },
                        appliedLeavedSchedules: '$data.appliedLeavedSchedules',
                        // status: reverseSortedWarningConfig[0].labelName,
                    },
                },
                {
                    $match: nameFilter,
                },
                {
                    $sort: { studentName: 1 },
                },
            ]);
        }
        await changePresentScheduleBasedLateConfigureForSingleStudent({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules,
            lateExcludeManagement,
        });
        const mixedDenialStudents = await lmsDenialModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    programId: convertToMongoObjectId(programId),
                    courseId: convertToMongoObjectId(courseId),
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    rotation: rotation || 'no',
                    ...(rotation === 'yes' && { rotationCount: parseInt(rotationCount) }),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    studentId: 1,
                    courseId: 1,
                    term: 1,
                    absencePercentage: 1,
                    rotation: 1,
                    rotationCount: 1,
                    'categoryWisePercentage.categoryName': 1,
                    'categoryWisePercentage.categoryId': 1,
                    'categoryWisePercentage.percentage': 1,
                    denialCondition: 1,
                    typeWiseUpdate: 1,
                },
                {
                    sort: { updatedAt: -1 },
                },
            )
            .lean();
        const lmsStudent = await lmsStudentModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    studentId: {
                        $in: courseSchedules.map((scheduleElement) =>
                            convertToMongoObjectId(scheduleElement.studentId),
                        ),
                    },
                    classificationType: LEAVE_TYPE.LEAVE,
                    programId: convertToMongoObjectId(programId),
                    level: level_no,
                    year,
                    approvalStatus: constants.APPROVED,
                    isDeleted: false,
                    isActive: true,
                },
                { categoryName: 1, studentId: 1, noOfHours: 1, dateAndTimeRange: 1 },
            )
            .lean();
        const denialStudentsWithLeave = lmsStudent.reduce((acc, curr) => {
            const studentFromCourseSchedules = courseSchedules.find(
                (scheduleElement) =>
                    scheduleElement.studentId.toString() === curr.studentId.toString(),
            );
            if (studentFromCourseSchedules && studentFromCourseSchedules.scheduled_dates) {
                const isScheduleAffectedByLeave = studentFromCourseSchedules.scheduled_dates.find(
                    (scheduleStartDateElement) =>
                        scheduleStartDateElement &&
                        new Date(scheduleStartDateElement) >=
                            new Date(curr.dateAndTimeRange.startSession.sessionStart) &&
                        new Date(scheduleStartDateElement) <=
                            new Date(curr.dateAndTimeRange.endSession.sessionEnd),
                );
                if (isScheduleAffectedByLeave) {
                    if (acc[curr.studentId]) {
                        if (acc[curr.studentId][curr.categoryName.toLowerCase()]) {
                            acc[curr.studentId][curr.categoryName.toLowerCase()] += 1;
                        } else {
                            acc[curr.studentId][curr.categoryName.toLowerCase()] = 1;
                        }
                    } else {
                        acc[curr.studentId] = { [curr.categoryName.toLowerCase()]: 1 };
                    }
                }
            }
            return acc;
        }, {});
        let courseName;
        let courseCode;
        let versionNo;
        let versioned;
        let versionName;
        let versionedFrom;
        let versionedCourseIds;
        let students = [];
        for (const studentElement of totalStudents) {
            const scheduleElement = courseSchedules.find(
                (scheduleElement) =>
                    scheduleElement.studentId.toString() === studentElement.studentId.toString() &&
                    (studentElement.rotation === 'yes'
                        ? studentElement.rotation_count === scheduleElement.rotation_count
                        : true),
            );
            // add course versioned details
            versionNo = studentElement.versionNo || 1;
            versioned = studentElement.versioned || false;
            versionName = studentElement.versionName || '';
            versionedFrom = studentElement.versionedFrom || null;
            versionedCourseIds = studentElement.versionedCourseIds || [];
            if (!scheduleElement) {
                students.push({
                    ...studentElement,
                    presentPercentage: 0,
                    absentPercentage: 0,
                    course_code: studentElement.course_no,
                    totalSchedules: 0,
                    unAppliedLeaveSchedules: 0,
                });
                let absentDetails = [];
                if (denialStudentsWithLeave[students[students.length - 1].studentId.toString()]) {
                    absentDetails = Object.keys(
                        denialStudentsWithLeave[students[students.length - 1].studentId.toString()],
                    ).reduce((acc, curr) => {
                        const absentDetailObject = {
                            categoryName: curr,
                            percentage: 0,
                        };
                        acc.push(absentDetailObject);
                        return acc;
                    }, []);
                }
                students[students.length - 1].absentDetails = absentDetails;
                const warningName = reverseSortedWarningConfig.find(
                    (warningConfigElement) =>
                        students[students.length - 1].absentPercentage >
                        warningConfigElement.percentage,
                );
                students[students.length - 1].status = warningName ? warningName.labelName : '';

                let mixedData;
                if (mixedDenialStudents && mixedDenialStudents.length) {
                    const isMixedStudent = mixedDenialStudents.find(
                        (denialStudentElement) =>
                            (denialStudentElement.studentId &&
                                students[students.length - 1].studentId.toString() ===
                                    denialStudentElement.studentId.toString() &&
                                denialStudentElement.courseId.toString() ===
                                    students[students.length - 1]._course_id.toString() &&
                                denialStudentElement.term === term) ||
                            ((studentElement.rotation === 'yes'
                                ? parseInt(studentElement.rotation_count) ===
                                  denialStudentElement.rotationCount
                                : true) &&
                                denialStudentElement.term === term &&
                                denialStudentElement.courseId.toString() ===
                                    students[students.length - 1]._course_id.toString() &&
                                denialStudentElement.typeWiseUpdate === COURSE_WISE),
                    );
                    mixedData = isMixedStudent;
                    if (mixedData) {
                        if (
                            Number(students[students.length - 1].absentPercentage) >
                            Number(mixedData.absencePercentage)
                        ) {
                            //for check if  individual student  denial percentage is changed
                            students[students.length - 1].status =
                                reverseSortedWarningConfig[0].labelName;
                            // }
                        }
                        students[students.length - 1].mixedConfig = {
                            absencePercentage: mixedData.absencePercentage,
                            categoryWisePercentage: mixedData.categoryWisePercentage,
                            denialCondition: mixedData.denialCondition,
                        };
                    }
                }
                if (
                    reverseSortedWarningConfig[0].denialCondition.toLowerCase() === INDIVIDUAL &&
                    students[students.length - 1].absentDetails &&
                    students[students.length - 1].absentDetails.length
                ) {
                    const mixedCategoryPercentage = mixedData || reverseSortedWarningConfig[0];
                    for (const categoryElement of mixedCategoryPercentage.categoryWisePercentage) {
                        const categoryPercentage = students[students.length - 1].absentDetails.find(
                            (absentElement) =>
                                absentElement.categoryName.toLowerCase() ===
                                categoryElement.categoryName.toLowerCase(),
                        );
                        if (categoryPercentage) {
                            if (
                                Number(categoryPercentage.percentage) >
                                Number(categoryElement.percentage)
                            ) {
                                students[students.length - 1].status =
                                    reverseSortedWarningConfig[0].labelName;
                            }
                        }
                    }
                }
                if (!courseName || !courseCode) {
                    courseName = students[students.length - 1].course_name;
                    courseCode = students[students.length - 1].course_code;
                }
                delete students[students.length - 1].course_name;
                delete students[students.length - 1].course_code;
            } else {
                scheduleElement.isScheduled = true;
                if (scheduleElement.unAppliedLeaveSchedules > 0) {
                    if (reverseSortedWarningConfig[0].unappliedLeaveConsideredAs) {
                        if (!denialStudentsWithLeave[scheduleElement.studentId.toString()]) {
                            denialStudentsWithLeave[scheduleElement.studentId.toString()] = {};
                        }
                        if (
                            denialStudentsWithLeave[scheduleElement.studentId.toString()][
                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                            ]
                        ) {
                            denialStudentsWithLeave[scheduleElement.studentId.toString()][
                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                            ] += scheduleElement.unAppliedLeaveSchedules;
                        } else {
                            denialStudentsWithLeave[scheduleElement.studentId.toString()][
                                reverseSortedWarningConfig[0].unappliedLeaveConsideredAs.toLowerCase()
                            ] = scheduleElement.unAppliedLeaveSchedules;
                        }
                    }
                }
                let absentDetails = [];
                if (denialStudentsWithLeave[scheduleElement.studentId.toString()]) {
                    absentDetails = Object.keys(
                        denialStudentsWithLeave[scheduleElement.studentId.toString()],
                    ).reduce((acc, curr) => {
                        const absentDetailObject = {
                            categoryName: curr,
                            percentage:
                                (denialStudentsWithLeave[scheduleElement.studentId.toString()][
                                    curr
                                ] /
                                    scheduleElement.totalSchedules) *
                                100,
                        };
                        acc.push(absentDetailObject);
                        return acc;
                    }, []);
                }
                scheduleElement.absentDetails = absentDetails;
                const warningName = reverseSortedWarningConfig.find(
                    (warningConfigElement) =>
                        scheduleElement.absentPercentage > warningConfigElement.percentage,
                );
                scheduleElement.status = warningName ? warningName.labelName : '';

                let mixedData;
                if (mixedDenialStudents && mixedDenialStudents.length) {
                    const isMixedStudent = mixedDenialStudents.find(
                        (denialStudentElement) =>
                            (denialStudentElement.studentId &&
                                scheduleElement.studentId.toString() ===
                                    denialStudentElement.studentId.toString() &&
                                denialStudentElement.courseId.toString() ===
                                    scheduleElement.courseId.toString() &&
                                denialStudentElement.term === term) ||
                            ((studentElement.rotation === 'yes'
                                ? parseInt(studentElement.rotation_count) ===
                                  denialStudentElement.rotationCount
                                : true) &&
                                denialStudentElement.term === term &&
                                denialStudentElement.courseId.toString() ===
                                    scheduleElement.courseId.toString() &&
                                denialStudentElement.typeWiseUpdate === COURSE_WISE),
                    );
                    const isMixedStudentForCourseFlow = mixedDenialStudents.find(
                        (denialStudentElement) =>
                            denialStudentElement.courseId.toString() ===
                                scheduleElement.courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE,
                    );
                    mixedData = isMixedStudent || isMixedStudentForCourseFlow;
                    if (mixedData) {
                        if (
                            Number(scheduleElement.absentPercentage) >
                            Number(mixedData.absencePercentage)
                        ) {
                            //for check if  individual student  denial percentage is changed
                            scheduleElement.status = reverseSortedWarningConfig[0].labelName;
                        } else if (scheduleElement.status === denialLabelName) {
                            const warningName = reverseSortedWarningConfig.find(
                                (warningConfigElement) =>
                                    scheduleElement.absentPercentage >
                                    warningConfigElement.percentage,
                            );
                            if (warningName && warningName.labelName) {
                                if (
                                    warningName.labelName ===
                                    reverseSortedWarningConfig[0].labelName
                                ) {
                                    scheduleElement.status = reverseSortedWarningConfig[1]
                                        ? reverseSortedWarningConfig[1].labelName
                                        : '';
                                } else {
                                    scheduleElement.status = warningName.labelName;
                                }
                            }
                            scheduleElement.manipulated = true;
                        }
                        scheduleElement.mixedConfig = {
                            absencePercentage: mixedData.absencePercentage,
                            categoryWisePercentage: mixedData.categoryWisePercentage,
                            denialCondition: mixedData.denialCondition,
                        };
                    }
                }
                if (
                    reverseSortedWarningConfig[0].denialCondition.toLowerCase() === INDIVIDUAL &&
                    scheduleElement.absentDetails &&
                    scheduleElement.absentDetails.length
                ) {
                    const mixedCategoryPercentage = mixedData || reverseSortedWarningConfig[0];
                    for (const categoryElement of mixedCategoryPercentage.categoryWisePercentage) {
                        const categoryPercentage = scheduleElement.absentDetails.find(
                            (absentElement) =>
                                absentElement.categoryName.toLowerCase() ===
                                categoryElement.categoryName.toLowerCase(),
                        );
                        if (categoryPercentage) {
                            if (
                                Number(categoryPercentage.percentage) >
                                Number(categoryElement.percentage)
                            ) {
                                scheduleElement.status = reverseSortedWarningConfig[0].labelName;
                            }
                        }
                    }
                }
                if (!courseName || !courseCode) {
                    courseName = scheduleElement.course_name;
                    courseCode = scheduleElement.course_code;
                }
                delete scheduleElement.course_name;
                delete scheduleElement.course_code;
                if (warningStatus && warningStatus.length) {
                    if (
                        scheduleElement.status &&
                        warningStatus
                            .map((statusElement) => statusElement.toLowerCase())
                            .includes(scheduleElement.status.toLowerCase())
                    ) {
                        students.push(scheduleElement);
                    }
                } else {
                    students.push(scheduleElement);
                }
            }
        }
        if (Object.keys(nameFilter).length !== 0) {
            students = students.filter((student) => {
                const searchWords = searchKey.trim().split(/\s+/);
                if (searchWords.length === 2 && student.studentName) {
                    const isMatch =
                        student.studentName.first
                            .toLowerCase()
                            .includes(searchWords[0].toLowerCase()) &&
                        student.studentName.last
                            .toLowerCase()
                            .includes(searchWords[1].toLowerCase());

                    return isMatch;
                }
                const isMatch =
                    (student.studentName &&
                        searchWords.length === 1 &&
                        (student.studentName.first
                            .toLowerCase()
                            .includes(searchWords[0].toLowerCase()) ||
                            student.studentName.last
                                .toLowerCase()
                                .includes(searchWords[0].toLowerCase()))) ||
                    (student.academicId &&
                        searchWords.some((word) =>
                            student.academicId.toLowerCase().includes(word.toLowerCase()),
                        ));

                return isMatch;
            });
        }
        if (studentIds && studentIds.length) {
            students = students.filter((student) => {
                return studentIds.includes(student.studentId.toString());
            });
        }
        if (warningStatus && warningStatus.length) {
            students = students.filter((student) => {
                return student.status && warningStatus.includes(student.status);
            });
        }
        const colorCode = reverseSortedWarningConfig.map((warningElement) => ({
            warning: warningElement.labelName,
            colorCode: warningElement.colorCode,
        }));
        if (students.length) {
            totalCount = [...students].length;
            students = students
                .sort((a, b) => a.studentName.first.localeCompare(b.studentName.first))
                .splice(pagination.skip, pagination.limit);
        }
        return {
            data: {
                students: sortArrayByName(students),
                courseName,
                courseCode,
                versionNo,
                versioned,
                versionName,
                versionedFrom,
                versionedCourseIds,
                totalCount,
                currentPage: pageNo,
                limit,
                denialPercentage: denialConfig.isActive
                    ? reverseSortedWarningConfig[0].percentage
                    : denialConfig.percentage,
                categoryWisePercentage: denialConfig.isActive
                    ? reverseSortedWarningConfig[0].categoryWisePercentage
                    : denialConfig.categoryWisePercentage,
                denialCondition: reverseSortedWarningConfig[0].denialCondition,
                warningList: reverseSortedWarningConfig.map(
                    (warningElement) => warningElement.labelName,
                ),
                colorCode,
                denialWarning: reverseSortedWarningConfig[0].labelName,
            },
            statusCode: 200,
            message: 'SUCCESS',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw Error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getStudentsWithWarningStatsRefactored = async ({ query = {}, headers = {} }) => {
    try {
        const {
            level_no,
            year,
            programId,
            term,
            rotation,
            rotationCount,
            courseId,
            pageNo,
            limit,
            searchKey,
            warningStatus,
            _institution_calendar_id,
        } = query;
        const { _institution_id } = headers;
        let { warningConfig } = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                },
                {
                    'warningConfig.denialCondition': 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.isActive': 1,
                },
            )
            .lean();
        if (!warningConfig) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        if (warningConfigForCalendar.length) {
            warningConfig = warningConfigForCalendar;
        }
        let denialConfig = null;
        warningConfig = warningConfig
            .filter((config) => config.isActive)
            .sort((a, b) => b.percentage - a.percentage);
        const {
            percentage = -1,
            labelName = '',
            denialCondition = 'cummulative',
        } = warningConfig?.[0] ?? {};
        const { labelName: labelName2 = '' } = warningConfig?.[1] ?? {};
        if (warningConfig.length) denialConfig = warningConfig[0];
        const colorCode = warningConfig.map((warningElement) => ({
            warning: warningElement.labelName,
            colorCode: warningElement.colorCode,
        }));
        const pagination = getPaginationValues({ limit, pageNo });
        let totalStudents = await getParticularCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            rotation,
            rotationCount: parseInt(rotationCount),
            term,
            _institution_calendar_id,
            _institution_id,
            courseIds: [courseId],
        });
        const totalCount = totalStudents.length;
        if (searchKey) {
            const searchWords = searchKey.trim().toLowerCase().split(/\s+/);
            totalStudents = totalStudents.filter((studentElement) => {
                if (!studentElement.studentName && !studentElement.academicId) return false;
                const studentNameFirst = studentElement.studentName
                    ? studentElement.studentName.first.toLowerCase()
                    : '';
                const studentNameLast = studentElement.studentName
                    ? studentElement.studentName.last.toLowerCase()
                    : '';
                const academicId = studentElement.academicId
                    ? studentElement.academicId.toLowerCase()
                    : '';
                if (searchWords.length === 2 && studentNameFirst && studentNameLast) {
                    return (
                        studentNameFirst.includes(searchWords[0]) &&
                        studentNameLast.includes(searchWords[1])
                    );
                }
                return searchWords.some(
                    (word) =>
                        studentNameFirst.includes(word) ||
                        studentNameLast.includes(word) ||
                        academicId.includes(word),
                );
            });
        }
        if (!warningStatus && totalStudents.length) {
            totalStudents = totalStudents
                .sort((a, b) => a.studentName.first.localeCompare(b.studentName.first))
                .splice(pagination.skip, pagination.limit);
        }
        const studentSet = new Set();
        for (const studentElement of totalStudents) {
            studentSet.add(studentElement.studentId.toString());
        }
        const { lateDurationRange } = await getLateAutoAndManualRange({
            _institution_id,
        });
        const { lateExcludeManagement } = await checkExclude({
            _institution_calendar_id,
            courseId,
            programId,
            yearNo: year,
            levelNo: level_no,
            term,
            rotationCount,
        });
        const mixedDenialStudents = await lmsDenialModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    programId: convertToMongoObjectId(programId),
                    courseId: convertToMongoObjectId(courseId),
                    yearNo: year,
                    levelNo: level_no,
                    term,
                    rotation: rotation || 'no',
                    ...(rotation === 'yes' && { rotationCount: parseInt(rotationCount) }),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    studentId: 1,
                    absencePercentage: 1,
                    typeWiseUpdate: 1,
                    denialCondition: 1,
                },
                {
                    sort: { updatedAt: -1 },
                },
            )
            .lean();
        const courseSchedulesDocs = await courseScheduleModel
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                    _course_id: convertToMongoObjectId(courseId),
                    type: 'regular',
                    year_no: year,
                    level_no,
                    term,
                    rotation: rotation || 'no',
                    ...(rotation === 'yes' && { rotation_count: parseInt(rotationCount) }),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    status: 1,
                    'students._id': 1,
                    'students.status': 1,
                    'students.primaryTime': 1,
                    'students.lateExclude': 1,
                    'session._session_id': 1,
                    'sessionDetail.start_time': 1,
                },
            )
            .lean();
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id,
            programId,
            courseId,
            levelNo: level_no,
            term,
            rotationCount,
            lateExcludeManagement,
        });
        const isMixedStudentForCourseFlow = mixedDenialStudents.find(
            (denialStudentElement) => denialStudentElement.typeWiseUpdate === COURSE_WISE,
        );
        const denialStudentMap = new Map();
        for (const denialElement of mixedDenialStudents) {
            if (!studentSet.has(denialElement?.studentId?.toString())) continue;
            if (denialElement.typeWiseUpdate === STUDENT_WISE) {
                denialStudentMap.set(denialElement?.studentId?.toString(), denialElement);
            }
        }
        const userCourseData = new Map();
        for (const courseScheduleElement of courseSchedulesDocs) {
            const { status } = courseScheduleElement;
            for (const studentElement of courseScheduleElement.students) {
                const { _id } = studentElement;
                const userCourseDataKey = _id.toString();
                if (!studentSet.has(userCourseDataKey)) continue;
                if (!userCourseData.has(userCourseDataKey)) {
                    const isMixedStudent =
                        denialStudentMap.get(userCourseDataKey) || isMixedStudentForCourseFlow;
                    userCourseData.set(userCourseDataKey, {
                        studentId: _id,
                        totalSchedules: 0,
                        totalCompletedSchedules: 0,
                        leavedSchedules: 0,
                        presentedSchedules: 0,
                        absentPercentage: 0,
                        presentPercentage: 0,
                        lateConfig: {},
                        studentLateAbsent: 0,
                        isMixedStudent,
                    });
                }
                const userCourseDataEntry = userCourseData.get(userCourseDataKey);
                const excludeStatus = studentElement.status === EXCLUDE;
                const completedStatus = status === 'completed';
                if (!excludeStatus) {
                    userCourseDataEntry.totalSchedules++;
                }
                if (completedStatus && !excludeStatus) {
                    userCourseDataEntry.totalCompletedSchedules++;
                }
                if (
                    completedStatus &&
                    (studentElement.status === 'absent' || studentElement.status === 'leave')
                ) {
                    userCourseDataEntry.leavedSchedules++;
                }
                if (
                    completedStatus &&
                    (studentElement.status === 'present' ||
                        studentElement.status === 'on_duty' ||
                        studentElement.status === 'permission')
                ) {
                    userCourseDataEntry.presentedSchedules++;
                }
                let lateConfig = userCourseDataEntry.lateConfig;
                let studentLateAbsent = userCourseDataEntry.studentLateAbsent;
                if (lateDurationRange.length && !lateExclude) {
                    const lateExludeForStudents = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id,
                        programId,
                        levelNo: level_no,
                        term,
                        courseId,
                        studentId: _id,
                        rotationCount,
                        lateExcludeManagement,
                    }).lateExclude;
                    const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id,
                        programId,
                        levelNo: level_no,
                        term,
                        courseId,
                        studentId: _id,
                        rotationCount,
                        sessionId: courseScheduleElement.session._session_id,
                        lateExcludeManagement,
                    }).lateExclude;
                    if (
                        completedStatus &&
                        studentElement.status === constants.PRESENT &&
                        studentElement.primaryTime !== undefined &&
                        !studentElement.lateExclude &&
                        !lateExludeForStudents &&
                        !lateExludeForSessions
                    ) {
                        const { lateConfig: updatedLateConfig } = getAutoRange({
                            lateDurationRange,
                            lateConfig,
                            studentLateAbsent,
                            primaryTime: studentElement.primaryTime,
                            scheduleStartDateAndTime:
                                courseScheduleElement.sessionDetail.start_time,
                        });
                        lateConfig = updatedLateConfig;
                    }
                    userCourseDataEntry.lateConfig = lateConfig;
                    for (const lateLabel in userCourseDataEntry.lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent = lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent =
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                }
                userCourseDataEntry.studentLateAbsent = studentLateAbsent;
                if (userCourseDataEntry.totalSchedules) {
                    userCourseDataEntry.absentPercentage =
                        ((userCourseDataEntry.leavedSchedules +
                            userCourseDataEntry.studentLateAbsent) /
                            userCourseDataEntry.totalSchedules) *
                        100;
                }
                if (userCourseDataEntry.totalCompletedSchedules) {
                    userCourseDataEntry.presentPercentage =
                        ((userCourseDataEntry.presentedSchedules -
                            userCourseDataEntry.studentLateAbsent) /
                            userCourseDataEntry.totalCompletedSchedules) *
                        100;
                }
            }
        }
        let courseName;
        let courseCode;
        let versionNo;
        let versioned;
        let versionName;
        let versionedFrom;
        let versionedCourseIds;
        let students = [];
        for (const studentElement of totalStudents) {
            let scheduleElement = userCourseData.get(studentElement.studentId.toString());
            scheduleElement = {
                ...scheduleElement,
                academicId: studentElement.academicId,
                studentName: studentElement.studentName,
            };
            if (
                !courseName ||
                !courseCode ||
                !versionNo ||
                !versioned ||
                !versionName ||
                !versionedFrom ||
                !versionedCourseIds
            ) {
                courseName = studentElement.course_name;
                courseCode = studentElement.course_no;
                versionNo = studentElement.versionNo || 1;
                versioned = studentElement.versioned || false;
                versionName = studentElement.versionName || '';
                versionedFrom = studentElement.versionedFrom || null;
                versionedCourseIds = studentElement.versionedCourseIds || [];
            }
            delete studentElement.course_name;
            delete studentElement.course_code;
            delete studentElement.versionNo;
            delete studentElement.versioned;
            delete studentElement.versionName;
            delete studentElement.versionedFrom;
            delete studentElement.versionedCourseIds;
            if (!scheduleElement) {
                if (warningStatus && warningStatus.length) {
                    if (warningStatus.includes(scheduleElement.status))
                        students.push(scheduleElement);
                } else {
                    students.push({
                        ...studentElement,
                        presentPercentage: 0,
                        absentPercentage: 0,
                        course_code: studentElement.course_no,
                        totalSchedules: 0,
                    });
                    studentElement.status = '';
                    const mixedData =
                        scheduleElement?.isMixedStudent || isMixedStudentForCourseFlow;
                    if (mixedData) {
                        studentElement.mixedConfig = {
                            absencePercentage: mixedData.absencePercentage,
                            denialCondition: mixedData.denialCondition,
                        };
                    }
                }
            } else {
                scheduleElement.isScheduled = true;
                const warningName = warningConfig.find(
                    (warningConfigElement) =>
                        scheduleElement.absentPercentage > warningConfigElement.percentage,
                );
                scheduleElement.status = warningName?.labelName;
                const isMixedStudent = scheduleElement.isMixedStudent;
                const mixedData = isMixedStudent || isMixedStudentForCourseFlow;
                if (mixedData) {
                    if (
                        Number(scheduleElement.absentPercentage) >
                        Number(mixedData.absencePercentage)
                    ) {
                        scheduleElement.status = warningConfig[0].labelName;
                    } else if (scheduleElement.status === denialConfig?.labelName) {
                        scheduleElement.status = warningConfig[1]?.labelName;
                        scheduleElement.manipulated = true;
                    }
                    scheduleElement.mixedConfig = {
                        absencePercentage: mixedData.absencePercentage,
                        denialCondition: mixedData.denialCondition,
                    };
                }
                if (warningStatus && warningStatus.length) {
                    if (warningStatus.includes(scheduleElement.status))
                        students.push(scheduleElement);
                } else {
                    students.push(scheduleElement);
                }
            }
        }
        if (warningStatus?.length && students?.length) {
            students = students
                .sort((a, b) => a.studentName.first.localeCompare(b.studentName.first))
                .splice(pagination.skip, pagination.limit);
        }
        return {
            data: {
                students,
                courseName,
                courseCode,
                versionNo,
                versioned,
                versionName,
                versionedFrom,
                versionedCourseIds,
                totalCount,
                currentPage: pageNo,
                limit,
                denialPercentage: denialConfig.isActive
                    ? warningConfig[0].percentage
                    : denialConfig.percentage,
                denialCondition: warningConfig[0].denialCondition,
                warningList: warningConfig.map((warningElement) => warningElement.labelName),
                colorCode,
                denialWarning: warningConfig[0].labelName,
            },
            statusCode: 200,
            message: 'SUCCESS',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw Error;
        } else {
            throw new Error(error);
        }
    }
};

exports.denialManagementList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            roleId,
            _institution_calendar_id,
            state,
            payloadProgramIds,
            payloadTerms,
            isChangeLog,
            payloadYearLevels,
            programSearch,
            courseSearch,
            paginationChanged,
            headerOptions,
            payloadCourseIds,
            payloadChangedByIds,
            changedBySearch,
            studentSearch,
            type,
            payloadStudentIds,
        } = query;
        const { userProgramIds, userCourseIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: roleId,
            institutionCalendarId: [_institution_calendar_id],
        });
        if (!userProgramIds) return { statusCode: 410, message: 'DATA_NOT_FOUND' };
        const pagination = getPaginationValues(query);
        let lmsDenialData = await getDenialFromCache(
            _institution_id,
            _institution_calendar_id,
            _user_id,
            state,
        );
        lmsDenialData = lmsDenialData.filter((denialElement) => {
            //we are applying the restriction like this one user only show particular program or course
            if (userCourseIds || userProgramIds || denialElement.typeWiseUpdate === GLOBAL_WISE) {
                if (
                    (userCourseIds &&
                        userCourseIds.length &&
                        denialElement.courseId &&
                        userCourseIds.toString().includes(denialElement.courseId._id.toString())) ||
                    (userProgramIds &&
                        userProgramIds.length &&
                        denialElement.programId &&
                        userProgramIds
                            .toString()
                            .includes(denialElement.programId._id.toString())) ||
                    denialElement.typeWiseUpdate === GLOBAL_WISE
                ) {
                    return true;
                }
            } else {
                return true;
            }
        });
        const program = {
            programIds: [],
            programData: [],
        };
        const student = {
            studentIds: [],
            studentData: [],
        };
        const course = {
            courseIds: [],
            courseData: [],
        };
        const changedBy = {
            changedByIds: [],
            changedByData: [],
        };
        const termYearLevel = {
            term: [],
            yearLevel: [],
            yearLevelWithTerm: [], //this header we are reusing for criteria management in reports
        };
        if (!paginationChanged) {
            const programSize =
                programSearch !== '' && payloadProgramIds ? payloadProgramIds.length + 10 : 10;
            const courseSize =
                courseSearch !== '' && payloadCourseIds ? payloadCourseIds.length + 10 : 10;
            const studentSize =
                studentSearch !== '' && payloadStudentIds ? payloadStudentIds.length + 10 : 10;
            const changedBySize =
                changedBySearch !== '' && payloadChangedByIds
                    ? payloadChangedByIds.length + 10
                    : 10;

            const addProgram = ({ programId }) => {
                program.programIds.push(programId._id.toString());
                program.programData.push({
                    name: programId.code + ' - ' + programId.name,
                    value: programId._id.toString(),
                });
            };
            const addCourseData = ({ courseId }) => {
                const courseVersionedName =
                    courseId.versioned || false
                        ? ` - ${courseId.versionName}` || ''
                        : (!courseId.versioned || false) &&
                          (courseId.versionedCourseIds || []).length
                        ? ' - Default'
                        : '';
                course.courseIds.push(courseId._id.toString());
                course.courseData.push({
                    name: `${courseId.course_code} - ${courseId.course_name}${courseVersionedName}`,
                    value: courseId._id.toString(),
                }); //courseIds is a Object contain course data
            };
            const addStudentData = ({ studentId }) => {
                student.studentIds.push(studentId._id.toString());
                student.studentData.push({
                    name: studentId.name.first + ' - ' + studentId.name.last,
                    value: studentId._id.toString(),
                });
            };
            const viceVersaFilterApplying = (checkingKey, data) => {
                let checkCondition = true;
                if (
                    checkingKey !== 'program' &&
                    payloadProgramIds &&
                    payloadProgramIds.length !== 0 &&
                    !payloadProgramIds.includes(data.programId._id.toString())
                )
                    checkCondition = false;
                if (
                    checkingKey !== 'course' &&
                    payloadCourseIds &&
                    payloadCourseIds.length !== 0 &&
                    !payloadCourseIds.includes(data.courseId._id.toString())
                )
                    checkCondition = false;
                if (
                    checkingKey !== 'term' &&
                    payloadTerms &&
                    payloadTerms.length !== 0 &&
                    !payloadTerms.includes(data.term)
                )
                    checkCondition = false;
                if (
                    checkingKey !== 'yearLevel' &&
                    payloadYearLevels &&
                    payloadYearLevels.length !== 0 &&
                    !payloadYearLevels.includes(data.yearNo + ' / ' + data.levelNo)
                )
                    checkCondition = false;
                if (
                    checkingKey === 'program' &&
                    programSearch !== '' &&
                    !new RegExp(programSearch, 'i').test(
                        `${data.programId.code} - ${data.programId.name}`,
                    )
                )
                    checkCondition = false;
                if (
                    checkingKey === 'course' &&
                    courseSearch !== '' &&
                    !new RegExp(courseSearch, 'i').test(
                        `${data.courseId.course_code} - ${data.courseId.course_name}`,
                    )
                )
                    checkCondition = false;
                if (
                    !isChangeLog &&
                    data.typeWiseUpdate !== constants.COURSE_WISE &&
                    checkingKey === 'student' &&
                    studentSearch !== '' &&
                    !new RegExp(studentSearch, 'i').test(
                        `${data.studentId.name.first} ${data.studentId.name.last}`,
                    )
                )
                    checkCondition = false;
                if (
                    checkingKey === 'changedBy' &&
                    changedBySearch !== '' &&
                    !new RegExp(changedBySearch, 'i').test(
                        `${data.updateBy.name.first} ${data.updateBy.name.last}`,
                    )
                )
                    checkCondition = false;
                return checkCondition;
            };
            for (const lmsDenialElement of lmsDenialData) {
                if (lmsDenialElement.typeWiseUpdate !== constants.GLOBAL_WISE) {
                    if (
                        program.programIds.length <= programSize && //we check when ever user do program Search we give 1 st 10data only
                        !program.programIds.includes(lmsDenialElement.programId._id.toString()) //duplicate programId restricting
                    ) {
                        if (
                            (payloadProgramIds &&
                                payloadProgramIds.includes(
                                    lmsDenialElement.programId._id.toString(),
                                )) || //is already in payload just pushing the programId below filters is also same like that
                            viceVersaFilterApplying('program', lmsDenialElement)
                        )
                            addProgram(lmsDenialElement);
                    }
                    if (
                        course.courseIds.length <= courseSize &&
                        !course.courseIds.includes(lmsDenialElement.courseId._id.toString())
                    ) {
                        if (
                            (payloadCourseIds &&
                                payloadCourseIds.includes(
                                    lmsDenialElement.courseId._id.toString(),
                                )) ||
                            viceVersaFilterApplying('course', lmsDenialElement)
                        )
                            addCourseData(lmsDenialElement);
                    }
                    if (
                        lmsDenialElement.typeWiseUpdate !== constants.COURSE_WISE &&
                        student.studentIds.length <= studentSize &&
                        !student.studentIds.includes(lmsDenialElement.studentId._id.toString())
                    ) {
                        if (
                            !isChangeLog &&
                            ((payloadStudentIds &&
                                payloadStudentIds.includes(
                                    lmsDenialElement.studentId._id.toString(),
                                )) ||
                                viceVersaFilterApplying('student', lmsDenialElement))
                        )
                            addStudentData(lmsDenialElement);
                    }
                    if (!termYearLevel.term.includes(lmsDenialElement.term)) {
                        if (
                            (payloadTerms && payloadTerms.includes(lmsDenialElement.term)) ||
                            viceVersaFilterApplying('term', lmsDenialElement)
                        )
                            termYearLevel.term.push(lmsDenialElement.term);
                    }
                    if (
                        !termYearLevel.yearLevel.includes(
                            lmsDenialElement.yearNo + ' / ' + lmsDenialElement.levelNo,
                        )
                    ) {
                        if (
                            (payloadYearLevels &&
                                payloadYearLevels.includes(
                                    lmsDenialElement.yearNo + ' / ' + lmsDenialElement.levelNo,
                                )) ||
                            viceVersaFilterApplying('yearLevel', lmsDenialElement)
                        ) {
                            termYearLevel.yearLevel.push(
                                lmsDenialElement.yearNo + ' / ' + lmsDenialElement.levelNo,
                            );
                            termYearLevel.yearLevelWithTerm.push(
                                lmsDenialElement.yearNo +
                                    ' / ' +
                                    lmsDenialElement.levelNo +
                                    ' / ' +
                                    lmsDenialElement.term,
                            );
                        }
                    }
                    if (
                        changedBy.changedByIds.length <= changedBySize &&
                        !changedBy.changedByIds.includes(lmsDenialElement.updateBy._id.toString())
                    ) {
                        if (
                            (payloadChangedByIds &&
                                payloadChangedByIds.includes(
                                    lmsDenialElement.updateBy._id.toString(),
                                )) ||
                            viceVersaFilterApplying('changedBy', lmsDenialElement)
                        ) {
                            changedBy.changedByIds.push(lmsDenialElement.updateBy._id.toString());
                            changedBy.changedByData.push({
                                name:
                                    lmsDenialElement.updateBy.name.first +
                                    ' - ' +
                                    lmsDenialElement.updateBy.name.last,
                                value: lmsDenialElement.updateBy._id.toString(),
                            });
                        }
                    }
                }
                //now we progress the program term year level
            }
        }

        lmsDenialData = lmsDenialData.filter((eachDenial) => {
            if (eachDenial.typeWiseUpdate === GLOBAL_WISE) return true;

            const isFilterApplied =
                (!payloadProgramIds ||
                    payloadProgramIds.length === 0 ||
                    payloadProgramIds.includes(eachDenial.programId._id.toString())) &&
                (!payloadTerms ||
                    payloadTerms.length === 0 ||
                    payloadTerms.includes(eachDenial.term)) &&
                (!payloadYearLevels ||
                    payloadYearLevels.length === 0 ||
                    payloadYearLevels.includes(eachDenial.yearNo + ' / ' + eachDenial.levelNo)) &&
                (!payloadCourseIds ||
                    payloadCourseIds.length === 0 ||
                    payloadCourseIds.includes(eachDenial.courseId._id.toString())) &&
                (!payloadChangedByIds ||
                    payloadChangedByIds.length === 0 ||
                    payloadChangedByIds.includes(eachDenial.updateBy._id.toString())) &&
                (!payloadStudentIds ||
                    eachDenial.typeWiseUpdate === constants.COURSE_WISE ||
                    payloadStudentIds.length === 0 ||
                    payloadStudentIds.includes(eachDenial.studentId._id.toString())) &&
                (!isChangeLog ||
                    eachDenial.typeWiseUpdate === constants.COURSE_WISE ||
                    studentSearch === '' ||
                    `${eachDenial.studentId.name.first} ${eachDenial.studentId.name.last}`
                        .toLowerCase()
                        .includes(studentSearch));
            return isFilterApplied;
        });
        const totalCountForPagination = lmsDenialData.length;
        const lmsPaginatedDate =
            type === 'export' //for export we want to set actual percentage for all filtered data
                ? lmsDenialData
                : lmsDenialData.slice(pagination.skip, pagination.skip + pagination.limit);

        const studentSettingData = await studentLmsSettingModel.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType: LEAVE,
                'warningConfig.isActive': true,
            },
            {
                'warningConfig.percentage': 1,
                'warningConfig.denialCondition': 1,
                'warningConfig.categoryWisePercentage': 1,
            },
        );
        const warningConfigForCalendar = await getWarningSettingBasedCalendar({
            _institution_calendar_id,
        });
        if (warningConfigForCalendar.length) {
            studentSettingData.warningConfig = warningConfigForCalendar;
        }
        const lastWarningConfig =
            studentSettingData.warningConfig[studentSettingData.warningConfig.length - 1];
        for (const [index, denialData] of lmsPaginatedDate.entries()) {
            const checkCourseWise = lmsDenialData[index].typeWiseUpdate === COURSE_WISE;
            let isStudentManipulatedForCourseFlow;
            if (checkCourseWise) {
                isStudentManipulatedForCourseFlow = lmsDenialData.find(
                    (studentElement, denialIndex) =>
                        (denialIndex > index && studentElement.typeWiseUpdate === GLOBAL_WISE) ||
                        (denialIndex > index &&
                            studentElement.courseId &&
                            denialData.courseId &&
                            studentElement.courseId._id &&
                            studentElement.courseId._id.toString() ===
                                denialData.courseId._id.toString() &&
                            studentElement.typeWiseUpdate === COURSE_WISE),
                );
            }
            const isStudentManipulated = lmsDenialData.find(
                (studentElement, denialIndex) =>
                    (denialIndex > index && studentElement.typeWiseUpdate === GLOBAL_WISE) ||
                    (denialIndex > index &&
                        studentElement.courseId &&
                        denialData.courseId &&
                        studentElement.courseId._id &&
                        studentElement.courseId._id.toString() ===
                            denialData.courseId._id.toString()) ||
                    (denialIndex > index &&
                        studentElement.studentId &&
                        denialData.studentId &&
                        studentElement.studentId._id &&
                        studentElement.studentId._id.toString() ===
                            denialData.studentId._id.toString() &&
                        studentElement.courseId._id.toString() ===
                            denialData.courseId._id.toString()),
            );
            if (!checkCourseWise && isStudentManipulated) {
                denialData.actualPercentage = isStudentManipulated.absencePercentage;
                denialData.actualCategoryWisePercentage =
                    isStudentManipulated.categoryWisePercentage;
            } else if (checkCourseWise && isStudentManipulatedForCourseFlow) {
                denialData.actualPercentage = isStudentManipulatedForCourseFlow.absencePercentage;
                denialData.actualCategoryWisePercentage =
                    isStudentManipulatedForCourseFlow.categoryWisePercentage;
            } else {
                denialData.actualPercentage = lastWarningConfig.percentage;
                denialData.actualCategoryWisePercentage = lastWarningConfig.categoryWisePercentage;
            }
        }
        if (type === 'export') {
            return {
                statusCode: 200,
                message: 'LIST_DATA',
                data: lmsDenialData, //for export we want to sent filteredData without pagination
            };
        }
        const actualData = {
            actualPercentage: 0,
            denialCondition: 0,
            categoryWisePercentage: 0,
        };
        actualData.actualPercentage = lastWarningConfig.percentage;
        actualData.denialCondition = lastWarningConfig.denialCondition;
        if (!lmsDenialData) return { statusCode: 410, message: 'DATA_NOT_FOUND' };
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: {
                lmsDenialData: lmsPaginatedDate,
                actualData,
                totalDocs: totalCountForPagination,
                headerOptions: paginationChanged
                    ? JSON.parse(headerOptions) //we are just returning headerOptions ,what we receive from frontend because we don't want to do the give table header options ,because header not change while we change pagination
                    : {
                          program: program.programData,
                          student: student.studentData,
                          course: course.courseData,
                          term: termYearLevel.term,
                          yearLevel: termYearLevel.yearLevel,
                          yearLevelWithTerm: termYearLevel.yearLevelWithTerm,
                          changedBy: changedBy.changedByData,
                      },
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getStudentsListForStudentsWarningStats = async ({ headers = {}, query = {} }) => {
    try {
        const {
            level_no,
            year,
            programId,
            term,
            courseId,
            rotation,
            rotationCount,
            _institution_calendar_id,
        } = query;
        const { _institution_id } = headers;
        const totalStudents = await getParticularCourseStudentsFromRedis({
            programId,
            year,
            level: level_no,
            rotation,
            ...(rotation === 'yes' && { rotationCount: parseInt(rotationCount) }),
            term,
            _institution_calendar_id,
            _institution_id,
            courseIds: [courseId],
        });
        const studentsList = totalStudents.map(({ studentName, academicId, studentId }) => ({
            studentName,
            academicId,
            studentId,
        }));
        return { data: studentsList, statusCode: 200, message: 'SUCCESS' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.checkDenialAccess = async ({ headers = {} }) => {
    try {
        const { _institution_id, _user_id, role_id } = headers;
        const lmsStudentSettings = await studentLmsSettingModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    classificationType: 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.denialManagement': 1,
                    'levelApprover.level.roleIds': 1,
                    isActive: 1,
                },
            )
            .lean();
        if (!lmsStudentSettings) {
            return {
                statusCode: 200,
                message: 'leave management settings not found / active',
                data: [],
            };
        }
        const leaveSettings = lmsStudentSettings.find(
            (leaveElement) =>
                leaveElement.classificationType === constants.LEAVE_TYPE.LEAVE &&
                leaveElement.isActive,
        );
        const { warningConfig } = leaveSettings || { warningConfig: [] };
        let denialConfig;
        let isViewMode = false;
        for (let i = warningConfig.length - 1; i >= 0; i--) {
            if (
                (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
                denialConfig == null
            ) {
                denialConfig = warningConfig[i];
            }
            if (!warningConfig[i].isActive) {
                warningConfig.splice(i, 1);
            }
        }
        if (denialConfig && !denialConfig.isActive) {
            isViewMode = true;
        }
        const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
            let comparison = 0;
            if (Number(a.percentage) > Number(b.percentage)) {
                comparison = -1;
            } else if (Number(a.percentage) < Number(b.percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        const accessType = isViewMode
            ? denialConfig && denialConfig.denialManagement.accessType
            : reverseSortedWarningConfig.length &&
              reverseSortedWarningConfig[0].denialManagement.accessType;
        const selectAccessIds = isViewMode
            ? denialConfig && denialConfig.denialManagement
            : reverseSortedWarningConfig.length && reverseSortedWarningConfig[0].denialManagement;
        const accessIds =
            accessType === 'user'
                ? selectAccessIds.userIds
                : accessType === 'role'
                ? selectAccessIds.roleIds
                : [];
        const assignedRoles = await roleAssignModel
            .findOne(
                {
                    _user_id: convertToMongoObjectId(_user_id),
                    'roles._role_id': convertToMongoObjectId(role_id),
                    isActive: true,
                    isDeleted: false,
                },
                { 'roles.$': 1 },
            )
            .lean();
        const stringedAccessIds = accessIds.map((accessIdElement) => accessIdElement.toString());
        //check denial access
        let hasDenialAccess = false;
        if (accessType === 'user') {
            if (accessIds.find((userIdElement) => userIdElement.toString() === _user_id)) {
                hasDenialAccess = true;
            }
        } else if (accessType === 'role') {
            const isValidRole =
                !!assignedRoles &&
                assignedRoles.roles.find(
                    (roleElement) =>
                        (roleElement.role_name.toString() === constants.COURSE_COORDINATOR ||
                            roleElement.isAdmin === true) &&
                        stringedAccessIds.includes(roleElement._role_id.toString()),
                );
            if (isValidRole) {
                hasDenialAccess = true;
            }
        }
        const permissionSettings = lmsStudentSettings.find(
            (leaveElement) =>
                leaveElement.classificationType === constants.LEAVE_TYPE.PERMISSION &&
                leaveElement.isActive,
        );
        const onDutySettings = lmsStudentSettings.find(
            (leaveElement) =>
                leaveElement.classificationType === constants.LEAVE_TYPE.ONDUTY &&
                leaveElement.isActive,
        );
        const roleIdsFromUserIds =
            assignedRoles && assignedRoles.roles.map((roleId) => roleId._role_id.toString());
        //check leaveAccess
        const hasLeaveAccess =
            leaveSettings &&
            assignedRoles &&
            leaveSettings.levelApprover.find(({ level }) =>
                level.find(({ roleIds }) =>
                    roleIds.find((roleIdElement) =>
                        roleIdsFromUserIds.includes(roleIdElement.toString()),
                    ),
                ),
            );
        //check onDuty access
        const hasOnDutyAccess =
            onDutySettings &&
            assignedRoles &&
            onDutySettings.levelApprover.find(({ level }) =>
                level.find(({ roleIds }) =>
                    roleIds.find((roleIdElement) =>
                        roleIdsFromUserIds.includes(roleIdElement.toString()),
                    ),
                ),
            );
        //check permissionAccess
        const hasPermissionAccess =
            permissionSettings &&
            assignedRoles &&
            permissionSettings.levelApprover.find(({ level }) =>
                level.find(({ roleIds }) =>
                    roleIds.find((roleIdElement) =>
                        roleIdsFromUserIds.includes(roleIdElement.toString()),
                    ),
                ),
            );
        return {
            statusCode: 200,
            message: 'success',
            data: {
                denialAccess: !!hasDenialAccess,
                isViewMode,
                leaveAccess: !!hasLeaveAccess,
                permissionAccess: !!hasPermissionAccess,
                onDutyAccess: !!hasOnDutyAccess,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.resetDenials = async ({ headers = {} }) => {
    try {
        const { _institution_id, user_id } = headers;
        if (!_institution_id) {
            return { statusCode: 400, message: '_institution_id is required' };
        }
        const lmsDenialLogData = await lmsDenialLogModel
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                },
                {
                    typeWiseUpdate: 1,
                },
            )
            .sort({
                createdAt: -1,
            })
            .lean();
        const currentDate = new Date();
        const activeCalendar = await institutionCalendarsModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                    status: constants.PUBLISHED,
                    start_date: { $lte: currentDate },
                    end_date: { $gte: currentDate },
                },
                {
                    _id: 1,
                    calendar_name: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        if (!(activeCalendar && activeCalendar.length)) {
            return { statusCode: 200, message: ' no data found' };
        }
        if (lmsDenialLogData.length > 1) {
            await lmsDenialModel.updateMany(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: {
                        $in: activeCalendar.map((calendarElement) => calendarElement._id),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    isDeleted: true,
                    isActive: false,
                },
            );
            await lmsDenialLogModel.updateMany(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: {
                        $in: activeCalendar.map((calendarElement) => calendarElement._id),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    isDeleted: true,
                    isActive: false,
                },
            );
        }
        const lmsSetting = await studentLmsSettingModel
            .findOne(
                {
                    _institution_id,
                    classificationType: constants.LEAVE_TYPE.LEAVE,
                },
                {
                    'warningConfig.percentage': 1,
                    'warningConfig.categoryWisePercentage': 1,
                },
            )
            .lean();
        const { warningConfig } = lmsSetting;
        if (warningConfig && warningConfig.length > 0) {
            const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
                return b.percentage - a.percentage;
            });
            await lmsDenialLogModel.deleteOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: {
                    $in: activeCalendar.map((calendarElement) => calendarElement._id),
                },
                isActive: true,
                isDeleted: false,
                typeWiseUpdate: GLOBAL_WISE,
            });
            await lmsDenialLogModel.updateOne(
                {
                    _institution_calendar_id: {
                        $in: activeCalendar.map((calendarElement) => calendarElement._id),
                    },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    typeWiseUpdate: GLOBAL_WISE,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    absencePercentage: reverseSortedWarningConfig[0].percentage,
                    ...(reverseSortedWarningConfig[0].categoryWisePercentage && {
                        categoryWisePercentage:
                            reverseSortedWarningConfig[0].categoryWisePercentage,
                    }),
                    updateBy: convertToMongoObjectId(user_id),
                },
                { upsert: true },
            );
        }
        return { statusCode: 200, message: 'success' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
