const Joi = require('joi');

const getGuestUserRegisterValidate = () => {
    return {
        body: Joi.object().keys({
            name: Joi.object({
                first: Joi.string().required(),
                last: Joi.string().allow(''),
            }).required(),
            email: Joi.string().email().required(),
            mobile: Joi.number(),
            password: Joi.string(),
            module: Joi.array().items(Joi.string()),
            userType: Joi.string().required(),
        }),
    };
};

const getAllGuestUsersValidate = () => {
    return {
        query: Joi.object().keys({
            searchKey: Joi.string().allow(''),
            limit: Joi.number().integer().min(1),
            skip: Joi.number().integer().min(0),
            pageNo: Joi.number().integer().min(1),
        }),
    };
};

const getGuestUserByIdValidate = () => {
    return {
        query: Joi.object().keys({
            guestUserId: Joi.string().length(24).required(),
        }),
    };
};

const updateGuestUserByIdValidate = () => {
    return {
        query: Joi.object().keys({
            guestUserId: Joi.string().length(24).required(),
        }),
        body: Joi.object().keys({
            name: Joi.object({
                first: Joi.string().required(),
                last: Joi.string().allow(''),
            }),
            email: Joi.string().email().required(),
            mobile: Joi.number(),
            password: Joi.string(),
            module: Joi.array().items(Joi.string()),
            userType: Joi.string().required(),
        }),
    };
};

const deleteGuestUserByIdValidate = () => {
    return {
        query: Joi.object().keys({
            guestUserId: Joi.string().length(24).required(),
        }),
    };
};

const loginGuestUserValidate = () => {
    return {
        body: Joi.object().keys({
            email: Joi.string().email().required(),
            password: Joi.string().required(),
        }),
    };
};

const sendEmailToGuestUserValidate = () => {
    return {
        body: Joi.object().keys({
            to: Joi.array().items(Joi.string().email()).required(),
            mailSubject: Joi.string().required(),
            mailContent: Joi.string().required(),
        }),
    };
};

module.exports = {
    getGuestUserRegisterValidate,
    getAllGuestUsersValidate,
    getGuestUserByIdValidate,
    updateGuestUserByIdValidate,
    deleteGuestUserByIdValidate,
    loginGuestUserValidate,
    sendEmailToGuestUserValidate,
};
