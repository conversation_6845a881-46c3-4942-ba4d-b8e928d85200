const FormTypeService = require('./form-type.service');

const createFormType = async ({
    body: { name, code, isLogBook } = {},
    headers: { user_id: userId } = {},
}) => {
    const formType = await FormTypeService.createFormType({ name, code, userId, isLogBook });

    return { message: 'FORM_TYPE_CREATED_SUCCESSFULLY', data: formType };
};

const getFormTypes = async () => {
    const formTypes = await FormTypeService.getFormTypes();

    return { message: 'FORM_TYPE_RETRIEVED_SUCCESSFULLY', data: formTypes };
};

const updateFormType = async ({ query: { formTypeId }, body: { name, code, isLogBook } = {} }) => {
    await FormTypeService.updateFormType({
        formTypeId,
        name,
        code,
        isLogBook,
    });

    return { message: 'FORM_TYPE_UPDATED_SUCCESSFULLY' };
};

const deleteFormType = async ({ query: { formTypeId } }) => {
    await FormTypeService.deleteFormType({ formTypeId });

    return { message: 'FORM_TYPE_DELETE_SUCCESSFULLY' };
};

module.exports = {
    createFormType,
    getFormTypes,
    updateFormType,
    deleteFormType,
};
