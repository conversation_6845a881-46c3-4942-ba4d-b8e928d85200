const moment = require('moment-timezone');
const CourseSchedule = require('../../models/course_schedule');
const Document = require('../../models/document_manager');
const Activities = require('../../models/activities');
const Question = require('../../models/question');
const Notification = require('../../models/notification');

const { dsGetAllWithSortAsJSON: getJSON } = require('../../base/base_controller');
const {
    convertToMongoObjectId,
    convertToUtcFormat,
    convertToUtcEndFormat,
    convertToUtcTimeFormat,
} = require('../../utility/common');

const {
    scheduleDateTimeFormatter,
    convertingRiyadhToUTC,
} = require('../../utility/common_functions');

const {
    getCourseIds,
    getCourse,
    getStudentCourseIds,
    getFeedbackByStaff,
    getRatingBySessions,
    getAllCourses,
} = require('../course_session/course_session_service');

const { getAllActivity } = require('../activities/activities_service');

const { getDocumentList } = require('../document_manager/document_manager_service');
const {
    DS_STUDENT,
    ONGOING,
    STUDENTS,
    DC_STUDENT,
    SCHEDULE_TYPES: { REGULAR },
    NOT_STARTED,
    STARTED,
    SCHEDULE,
    DASHBOARD: { ACTIVITIES, SESSIONS, COURSES, DOCUMENTS, RATINGS },
    TIME_GROUP_BOOKING_TYPE,
} = require('../../utility/constants');
const { COMPLETED, PM } = require('../../utility/enums');
const { getRemoteInfra } = require('../../utility/data.service');
const { logger } = require('../../utility/util_keys');

const project = {
    _id: 1,
    _sessionId: 1,
    name: 1,
    type: 1,
    status: 1,
    startTime: 1,
    endTime: 1,
    createdBy: 1,
    quizType: 1,
    setQuizTime: 1,
    socketEventStaffId: 1,
    socketEventStudentId: 1,
    staffStartWithExam: 1,
    studentCompletedQuiz: 1,
    students: 1,
    courseId: 1,
    sessionFlowIds: 1,
    questions: 1,
};

const getSchedules = async (userId, userType) => {
    try {
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            $or: [
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
                { 'students._id': convertToMongoObjectId(userId) },
            ],
        };
        const currentDate = moment().format('YYYY-MM-DD');
        const currentDateAndTime = moment();
        const currentDateAndTimeFormat = moment().format();

        scheduleQuery.schedule_date = moment().format('YYYY-MM-DD[T00:00:00.000Z]');
        scheduleQuery.status = { $ne: COMPLETED.toLowerCase() };
        const scheduleProject = {
            _course_id: 1,
            session: 1,
            student_groups: 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            end: 1,
            start: 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            _infra_id: 1,
            infra_id: '$_infra_id',
            infra_name: 1,
            staffs: 1,
            status: 1,
            sessionDetail: 1,
            students: 1,
            uuid: 1,
            socket_port: 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            isActive: 1,
            isDeleted: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
        };
        let courseSchedules = await CourseSchedule.find(scheduleQuery, scheduleProject)
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .lean();
        // date based sessions

        courseSchedules = courseSchedules.filter((courseSchedule) => {
            const {
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
            } = courseSchedule;

            const endDateAndTimeFormatter = scheduleDateTimeFormatter(schedule_date, {
                hour: endHour,
                minute: endMinute,
                format: endFormat,
            });

            currentDateAndTimes = new Date(currentDateAndTimeFormat); // converting local time to UTC
            endDateAndTime = endDateAndTimeFormatter; // coming as a UTC time format

            if (endDateAndTime >= currentDateAndTimes) {
                return true;
            }
            return false;
        });
        const SessionIds = courseSchedules.map((session) => {
            return session._id;
        });

        const feedBackData = await getRatingBySessions(SessionIds, userId);
        const remoteInfra = await getRemoteInfra();
        // if merged session exists
        courseSchedules = courseSchedules.map((courseSchedule) => {
            const {
                start: { hour: startHour, minute: startMinute, format: startFormat },
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
                _infra_id,
            } = courseSchedule;

            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                courseSchedule.infra_name = infraName;
            }
            if (courseSchedule.mode === TIME_GROUP_BOOKING_TYPE.REMOTE) {
                courseSchedule.infraDatas = remoteInfra.find(
                    (infraElement) =>
                        courseSchedule.infra_id &&
                        infraElement._id.toString() === courseSchedule.infra_id.toString(),
                );
            }
            if (
                !courseSchedule.scheduleStartDateAndTime &&
                !courseSchedule.scheduleEndDateAndTime
            ) {
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertingRiyadhToUTC(
                    schedule_date,
                    startHours,
                    startMinute,
                );
                const endDateAndTime = convertingRiyadhToUTC(schedule_date, endHours, endMinute);
                courseSchedule.scheduleStartDateAndTime = startDateAndTime;
                courseSchedule.scheduleEndDateAndTime = endDateAndTime;
            }
            if (courseSchedule.merge_status) {
                const mergedSessions = courseSchedule.merge_with.map((mergeWith) => {
                    const sessionDetails = courseSchedules.find(
                        (courseScheduleEntry) =>
                            courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString() &&
                            courseScheduleEntry.session &&
                            courseScheduleEntry.session._session_id.toString() ===
                                mergeWith.session_id.toString(),
                    );
                    if (sessionDetails) {
                        mergeWith.session = {
                            _session_id: sessionDetails.session._session_id,
                            s_no: sessionDetails.session.s_no,
                            delivery_symbol: sessionDetails.session.delivery_symbol,
                            delivery_no: sessionDetails.session.delivery_no,
                        };
                    }
                    return mergeWith;
                });
                courseSchedule.merge_with = mergedSessions;
            }
            courseSchedule.feedBack = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._session_id.toString() === courseSchedule._id.toString(),
            );
            return courseSchedule;
        });
        // session duplicate removed
        let mergedCourseSchedules = [];
        courseSchedules.forEach((courseSchedule) => {
            const { merge_status, merge_with, student_groups } = courseSchedule;
            if (merge_status) {
                const mergedScheduleIds = merge_with.map((mergedWith) => mergedWith.schedule_id);
                const duplicateSessions = mergedScheduleIds.filter((mergedScheduleId) =>
                    mergedCourseSchedules.find(
                        (mergedCourseSchedule) =>
                            mergedCourseSchedule._id.toString() === mergedScheduleId.toString(),
                    ),
                );
                if (duplicateSessions.length === 0) {
                    const mergedSessionSchedules = courseSchedules.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    mergedCourseSchedules.push(courseSchedule);
                }
            } else {
                mergedCourseSchedules.push(courseSchedule);
            }
        });

        // today activity
        const activities = await Activities.find({
            'schedule.startDateAndTime': {
                $lte: new Date(currentDate + 'T23:59:59Z'),
                $gte: new Date(currentDateAndTimeFormat),
            },
            isDeleted: false,
            status: { $in: [NOT_STARTED, STARTED] },
            type: SCHEDULE,
        });
        if (activities.length) {
            const activityDetails = [];

            for (const activity of activities) {
                const { scheduleIds, sessionId, sessionFlowIds, questions, students } = activity;

                let questionDetails;
                if (questions.length) {
                    const questionIds = questions.map((question) =>
                        convertToMongoObjectId(question._id),
                    );
                    questionDetails = await Question.find({
                        _id: { $in: questionIds },
                        isDeleted: false,
                    });
                }
                const courseSchedules = await CourseSchedule.find({
                    _id: { $in: scheduleIds },
                });
                let studentGroupsName = '';
                for (const scheduleId of courseSchedules) {
                    const { merge_status, merge_with } = scheduleId;

                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const mergedSchedules = await CourseSchedule.find({
                                _id: { $in: scheduleIds },
                            });

                            mergedSchedules.push(scheduleId);
                            mergedSchedules.forEach((courseSchedule) => {
                                const { student_groups, _id } = courseSchedule;
                                if (student_groups && student_groups.length) {
                                    let studentGroups = student_groups.map((student_group) => {
                                        const { group_name, session_group } = student_group;
                                        let groupName = group_name.split('-').slice(-2);
                                        groupName = groupName[1]
                                            ? groupName[0] + '-' + groupName[1]
                                            : groupName[0];
                                        if (session_group && session_group.length) {
                                            let sessionGroup = session_group.map(
                                                (groupNameEntry) => {
                                                    let groupNames = groupNameEntry.group_name
                                                        .split('-')
                                                        .slice(-2);
                                                    groupNames = groupNames[1]
                                                        ? groupNames[0] + '-' + groupNames[1]
                                                        : groupNames[0];
                                                    return groupNames;
                                                },
                                            );
                                            sessionGroup = sessionGroup.toString();
                                            groupName += '(' + sessionGroup + ')';
                                        }
                                        return groupName;
                                    });
                                    studentGroups = studentGroups.toString();
                                    studentGroupsName += studentGroups;
                                }
                            });
                        }
                    } else {
                        const { student_groups } = scheduleId;
                        if (student_groups && student_groups.length) {
                            let studentGroups = student_groups.map((student_group) => {
                                const { group_name, session_group } = student_group;
                                let groupName = group_name.split('-').slice(-2);
                                groupName = groupName[1]
                                    ? groupName[0] + '-' + groupName[1]
                                    : groupName[0];
                                if (session_group && session_group.length) {
                                    let sessionGroup = session_group.map((groupNameEntry) => {
                                        let groupNames = groupNameEntry.group_name
                                            .split('-')
                                            .slice(-2);
                                        groupNames = groupNames[1]
                                            ? groupNames[0] + '-' + groupNames[1]
                                            : groupNames[0];
                                        return groupNames;
                                    });
                                    sessionGroup = sessionGroup.toString();
                                    groupName += '(' + sessionGroup + ')';
                                }
                                return groupName;
                            });
                            studentGroups = studentGroups.toString();
                            studentGroupsName += studentGroups;
                        }
                    }
                }
                let sessionIds = sessionFlowIds.map((sessionFlowId) =>
                    convertToMongoObjectId(sessionFlowId._id),
                );

                const sessions = await CourseSchedule.find(
                    {
                        $or: [
                            {
                                'session._session_id': { $in: sessionIds },
                            },
                            {
                                _id: { $in: sessionIds },
                            },
                        ],
                    },
                    { session: 1, title: 1, type: 1, _id: 1 },
                ).lean();
                sessionIds = sessionIds.map((sessionFlow) => sessionFlow.toString());
                const sessionDetails = sessionFlowIds.map((sessionId) => {
                    const sessionDetail = sessions.find(
                        (sessionEntry) =>
                            (sessionEntry.session &&
                                sessionEntry.session._session_id.toString() ===
                                    sessionId._id.toString()) ||
                            (sessionEntry.type === sessionId.type &&
                                sessionEntry._id.toString() === sessionId._id.toString()),
                    );
                    if (sessionDetail) {
                        const { session, _id, title, type } = sessionDetail;
                        if (session) {
                            const { _session_id } = session;
                            if (
                                session &&
                                _session_id &&
                                sessionIds.includes(_session_id.toString())
                            ) {
                                return {
                                    _id: session._session_id,
                                    s_no: session.s_no,
                                    delivery_symbol: session.delivery_symbol,
                                    delivery_no: session.delivery_no,
                                    session_type: session.session_type,
                                    session_topic: session.session_topic,
                                    type: REGULAR,
                                };
                            }
                        }
                        if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                            return { _id, title, type };
                        }
                    }
                });

                let answeredQuestions;
                if (userType && userType === DC_STUDENT) {
                    answeredStudent = students.find(
                        (student) => student._studentId.toString() === userId.toString(),
                    );
                    if (answeredStudent) answeredQuestions = answeredStudent.questions;
                }
                let activityQuestions;
                if (questionDetails && questionDetails.length) {
                    activityQuestions = questionDetails.map((selectedQuestion) => {
                        const { _id } = selectedQuestion;
                        const questionType = questions.find(
                            (q) => q._id.toString() === _id.toString(),
                        );
                        if (questionType) {
                            selectedQuestion.type = questionType.type;
                            selectedQuestion.order = questionType.order;
                        }
                        return selectedQuestion;
                    });
                    activityQuestions = await formatQuestions(activityQuestions, answeredQuestions);
                }
                const schedule = courseSchedules.find(
                    (scheduleId) =>
                        scheduleId.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleId.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );

                activityDetails.push({
                    activityId: activity._id,
                    status: activity.status,
                    name: activity.name,
                    quizType: activity.quizType,
                    schedule: activity.schedule,
                    questions: activity.questions,
                    socketEventStaffId: activity.socketEventStaffId,
                    staffStartWithExam: activity.staffStartWithExam,
                    socketEventStudentId: activity.socketEventStudentId,
                    totalStudentAnsweredCount: students ? students.length : 0,
                    totalStudentCount: schedule && schedule.students ? schedule.students.length : 0,
                    createdBy: activity.createdBy,
                    setQuizTime: activity.setQuizTime,
                    type: 'activity',
                    studentGroupName: studentGroupsName,
                    sessionId,
                    sessionType: schedule && schedule.type ? schedule.type : undefined,
                    _institution_calendar_id:
                        schedule && schedule._institution_calendar_id
                            ? schedule._institution_calendar_id
                            : undefined,
                    year_no: schedule && schedule.year_no ? schedule.year_no : undefined,
                    level_no: schedule && schedule.level_no ? schedule.level_no : undefined,
                    _program_id:
                        schedule && schedule._program_id ? schedule._program_id : undefined,
                    _course_id: schedule && schedule._course_id ? schedule._course_id : undefined,
                    course_name:
                        schedule && schedule.course_name ? schedule.course_name : undefined,
                    merge_status: schedule ? schedule.merge_status : undefined,
                    _id: schedule && schedule._id ? schedule._id : undefined,
                    sessionFlowIds: sessionDetails,
                    rotation: schedule && schedule.rotation ? schedule.rotation : undefined,
                    rotation_count:
                        schedule && schedule.rotation_count ? schedule.rotation_count : undefined,
                    start_date: activity.schedule.startDateAndTime,
                    end_date: activity.schedule.endDateAndTime,
                });
            }
            mergedCourseSchedules = mergedCourseSchedules.concat(activityDetails);
        }
        mergedCourseSchedules.sort(function (a, b) {
            return new Date(a.start_date) - new Date(b.start_date);
        });
        return mergedCourseSchedules;
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentSchedules = async (studentId) => {
    try {
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            'students._id': studentId,
        };
        const scheduleProject = {
            isActive: 0,
            isDeleted: 0,
            _institution_id: 0,
            _institution_calendar_id: 0,
            createdAt: 0,
            updatedAt: 0,
        };
        return (await getJSON(CourseSchedule, scheduleQuery, scheduleProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getStaffActivities = async (staffId) => {
    try {
        const schedules = await getSchedules(staffId);
        const sessionIds = schedules.map((schedule) =>
            convertToMongoObjectId(schedule.session._session_id),
        );
        const activityQuery = { isDeleted: false, isActive: true };
        activityQuery._sessionId = { $in: sessionIds };
        return await Activities.find(activityQuery, project)
            .sort({
                createdAt: -1,
            })
            .limit(5)
            .populate({
                path: '_sessionId',
                select: { session_date: 1, start_time: 1, end_time: 1, students: 1, _staff_id: 1 },
            })
            .exec();
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentActivities = async (studentId) => {
    try {
        const schedules = await getStudentSchedules(studentId);
        const courseIds = schedules.map((schedule) => convertToMongoObjectId(schedule._course_id));
        const activityQuery = { isDeleted: false, isActive: true };
        activityQuery.courseId = { $in: courseIds };
        return (await getJSON(Activities, activityQuery, {})).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getStaffDocuments = async (staffId) => {
    try {
        const query = { isDeleted: false, _user_id: convertToMongoObjectId(staffId) };
        const project = {};

        const documents = await Document.find(query, project)
            .sort({
                createdAt: -1,
            })
            .exec();

        const sessionIds = [];
        const courseIds = [];

        documents.map((session) => {
            session._session_flow_id.map((sessionId) => {
                if (sessionId && sessionIds.indexOf(sessionId.toString()) < 0) {
                    sessionIds.push(sessionId.toString());
                }
            });
            if (session._course_id && courseIds.indexOf(session._course_id) < 0) {
                courseIds.push(session._course_id);
            }
        });

        const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const courseSchdules = await CourseSchedule.find(
            {
                isDeleted: false,
                $or: [
                    { _course_id: { $in: courseIds } },
                    { 'session._session_id': { $in: sessions } },
                ],
            },
            { _course_id: 1, course_name: 1, course_code: 1, session: 1 },
        ).exec();

        const DocData = documents.map(function (Docs) {
            Items = {
                starred: Docs.starred,
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                createdAt: Docs.createdAt,
                _session_flow_id: Docs._session_flow_id,
            };

            const sessionDatas = [];

            Docs._session_flow_id.map(function (element) {
                const sessionDetails = courseSchdules.find(
                    (session) =>
                        session.session._session_id &&
                        session.session._session_id.toString() === element.toString(),
                );

                if (sessionDetails) {
                    sessionDatas.push({
                        delivery_symbol: sessionDetails.session.delivery_symbol,
                        delivery_no: sessionDetails.session.delivery_no,
                        _session_id: sessionDetails.session._session_id,
                    });
                }
            });

            const courseDetails = courseSchdules.find(
                (course) =>
                    Docs._course_id &&
                    course._course_id &&
                    course._course_id.toString() === Docs._course_id.toString(),
            );

            if (courseDetails) {
                Items.courseData = {
                    course_name: courseDetails.course_name,
                    course_code: courseDetails.course_code,
                };
            }
            Items.sessions = sessionDatas;
            return Items;
        });
        return DocData;
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentDocuments = async (studentId) => {
    try {
        const courseIds = await getCourseIds(studentId, DS_STUDENT);
        const documentQuery = { isDeleted: false, isActive: true, _course_id: { $in: courseIds } };
        const documentProject = {
            url: 1,
            name: 1,
            type: 1,
            title: 1,
            _session_flow_id: 1,
            _course_id: 1,
            _user_id: 1,
            createdAt: 1,
        };
        const documents = (await getJSON(Document, documentQuery, documentProject)).data;
        return documents;
    } catch (error) {
        throw new Error(error);
    }
};

const getLastFiveDocuments = async (staffId) => {
    // TODO
    try {
        const query = { isDeleted: false, _user_id: convertToMongoObjectId(staffId) };
        const project = {};

        const documents = await Document.find(query, project)
            .sort({
                createdAt: -1,
            })
            .limit(5)
            .exec();

        const sessionIds = [];
        const courseIds = [];
        documents.map((sess) => {
            sess._session_flow_id.map((ids) => {
                if (ids && sessionIds.indexOf(ids.toString()) < 0) {
                    sessionIds.push(ids.toString());
                }
            });
            if (courseIds.indexOf(sess._course_id) < 0 && sess._course_id !== undefined) {
                courseIds.push(sess._course_id);
            }
        });
        const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const courseSchdules = await CourseSchedule.find(
            {
                isDeleted: false,
                $or: [
                    { _course_id: { $in: courseIds } },
                    { 'session._session_id': { $in: sessions } },
                ],
            },
            { _course_id: 1, course_name: 1, course_code: 1, session: 1 },
        ).exec();

        const DocData = documents.map(function (Docs) {
            Items = {
                starred: Docs.starred,
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                createdAt: Docs.createdAt,
                _session_flow_id: Docs._session_flow_id,
            };

            const sessionDatas = [];

            Docs._session_flow_id.map(function (element) {
                const sessionDetails = courseSchdules.find(
                    (session) =>
                        session.session._session_id !== '' &&
                        session.session._session_id !== undefined &&
                        session.session._session_id.toString() === element.toString(),
                );

                if (sessionDetails) {
                    sessionDatas.push({
                        delivery_symbol: sessionDetails.session.delivery_symbol,
                        delivery_no: sessionDetails.session.delivery_no,
                        _session_id: sessionDetails.session._session_id,
                    });
                }
            });

            const courseDetails = courseSchdules.find(
                (course) =>
                    Docs._course_id &&
                    course._course_id &&
                    course._course_id.toString() === Docs._course_id.toString(),
            );

            if (courseDetails) {
                Items.courseData = {
                    course_name: courseDetails.course_name,
                    course_code: courseDetails.course_code,
                };
            }
            Items.sessions = sessionDatas;
            return Items;
        });
        return DocData;
    } catch (error) {
        throw new Error(error);
    }
};

const getLastFiveActivities = async (staffId, userType, courseAdmin, _institution_id) => {
    try {
        return await getAllActivity(userType, staffId, 5, 1, _institution_id, courseAdmin);
    } catch (error) {
        throw new Error(error);
    }
};

const getAllCountNotification = async (userIds) => {
    try {
        userIds = userIds.map((userId) => convertToMongoObjectId(userId));
        const notifications = await Notification.find({
            'users._id': { $in: userIds },
            isDeleted: false,
        }).lean();
        const userNotifications = [];
        userIds.forEach((userId) => {
            const counts = notifications.filter((notification) => {
                if (
                    notification.users.find(
                        (user) => user._id.toString() === userId.toString() && !user.isViewed,
                    )
                ) {
                    return true;
                }
                return false;
            }).length;
            userNotifications.push({ userId, counts });
        });
        return userNotifications;
    } catch (error) {
        throw new Error(error);
    }
};

const getOnGoingSchedule = (sessions) => {
    try {
        const schedule = sessions.find((session) => session.status === ONGOING);
        return schedule;
    } catch (error) {
        throw new Error(error);
    }
};
const getStaffData = async (staffId, moduleType, courseAdmin, _institution_id) => {
    try {
        staffId = convertToMongoObjectId(staffId);
        if (moduleType === ACTIVITIES) {
            const { activities } = await getLastFiveActivities(
                staffId,
                'staff',
                courseAdmin,
                _institution_id,
            );
            return { activities };
        }
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentData = async (studentId, moduleType, _institution_id) => {
    try {
        studentId = convertToMongoObjectId(studentId);
        if (moduleType === ACTIVITIES) {
            const { activities } = await getLastFiveActivities(
                studentId,
                'student',
                '',
                _institution_id,
            );
            return { activities };
        }
    } catch (error) {
        throw new Error(error);
    }
};

const formatQuestionsAttachment = (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentAnswered;
    let studentAnsweredOptionId;
    let studentCorrectAnswered = 0;

    for (const activityQuestion of activityQuestions) {
        const {
            _id,
            _activityId,
            text,
            questionType,
            attachments,
            options,
            answer,
            feedback,
            isDeleted,
            order,
            questionMoved,
            acceptQuestionResponse,
        } = activityQuestion;

        // get option attachments unsigned url
        if (options && options.length > 0) {
            for (const option of options) {
                const { _id: optionId, answer: optionAnswer } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    studentAnswered = optionAnswer;
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnsweredOptionId = optionId;
                }
            }
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }

        questions.push({
            _id,
            _activityId,
            text,
            questionType,
            attachments,
            options,
            answer,
            feedback,
            isDeleted,
            order,
            questionMoved,
            questionAnswered,
            studentAnswered,
            studentAnsweredOptionId,
            acceptQuestionResponse,
        });
    }
    return {
        questions,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

module.exports = {
    getStaffData,
    getStudentData,
    getSchedules,
    getAllCountNotification,
};
