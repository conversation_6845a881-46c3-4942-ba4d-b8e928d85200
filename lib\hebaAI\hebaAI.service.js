const { redisClient } = require('../../config/redis-connection');
const constants = require('../utility/constants');
const programCalendarModel = require('../models/program_calendar');
const { convertToMongoObjectId } = require('../utility/common');

exports.getLevelsFromRedis = async ({ programId, _institution_calendar_id }) => {
    const key = `course_list:${programId}-${_institution_calendar_id}`;
    const levels = await redisClient.Client.get(key);
    if (!(levels && JSON.parse(levels).length)) {
        const programLevels = await programCalendarModel
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    status: 'published',
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            )
            .lean();
        if (!(programLevels && programLevels.level)) {
            return null;
        }
        await redisClient.Client.set(key, JSON.stringify(programLevels.level));
        return programLevels.level;
    }
    return JSON.parse(levels);
};
