const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const generatePortfolioReportSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectId.required(),
        studentIds: Joi.array().optional(),
    }),
}).unknown(true);

const getPortfolioReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
    }),
}).unknown(true);

const addExtraMarkSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectId.required(),
        studentId: objectId.required(),
        marks: Joi.number().integer().required(),
    }),
}).unknown(true);

const updateMarkSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectId.required(),
        marks: Joi.number().integer().required(),
        type: Joi.string()
            .valid('equivalenceMark', 'overAllPassMark', 'minComponentsForInferencePass')
            .required(),
    }),
}).unknown(true);

const updateReportSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectId.required(),
        overAllPassEnabled: Joi.boolean().required(),
        isComponentWisePassEnabled: Joi.boolean().required(),
    }),
}).unknown(true);

const updateComponentWisePassSettingsSchema = Joi.object({
    body: Joi.object({
        portfolioId: objectId.required(),
        componentId: objectId.optional(),
        childId: objectId.optional(),
        marks: Joi.number().integer().optional(),
        mustPass: Joi.boolean().optional(),
        completionRules: Joi.array().optional(),
    }),
}).unknown(true);

const getIndividualStudentReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
        studentId: objectId.required(),
    }),
}).unknown(true);

const getPortfolioComponentReportSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
        componentId: objectId.required(),
        childrenId: objectId.optional(),
    }),
}).unknown(true);
module.exports = {
    generatePortfolioReportSchema,
    getPortfolioReportSchema,
    addExtraMarkSchema,
    updateMarkSettingsSchema,
    updateReportSettingsSchema,
    updateComponentWisePassSettingsSchema,
    getIndividualStudentReportSchema,
    getPortfolioComponentReportSchema,
};
