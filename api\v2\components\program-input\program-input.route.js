const router = require('express').Router();
const {
    addProgram,
    getInstitutePrograms,
    editProgram,
    archieveProgram,
    deleteProgram,
    configureProgram,
    addOrEditProgramGoals,
    addOrEditProgramObjectives,
    addProgramPortfolio,
    editProgramPortfolio,
    addBreaks,
    removeBreaks,
    editBreaks,
    addEventType,
    editEventType,
    removeEventType,
    updateCurriculum,
    updateCreditHours,
    deleteParentInstituteBreak,
    deleteParentInstituteEvent,
    restoreArchivedProgram,
    getProgram,
    getProgramSettings,
    deleteProgramObjectives,
    deleteProgramGoals,
    deleteProgramPortfolio,
    updateTimeZone,
    updateGenderSegregation,
    updateProgramDurationFormat,
} = require('./program-input.controller');
const { uploadProgramMediaFile } = require('./program-input.util');
const {
    addProgramValidator,
    getProgramsValidator,
    programIdValidator,
    addBreakValidator,
    editBreakValidator,
    removeBreakValidator,
    addEventTypeValidator,
    editEventTypeValidator,
    removeEventTypeValidator,
    updateCurriculumValidator,
    updateCreditHoursValidator,
    deleteParentInstituteBreakValidator,
    deleteParentInstituteEventValidator,
    editProgramValidator,
    addGoalsValidator,
    getProgramInstitutionSettingsValidator,
    deleteProgramGoalsOrObjectivesValidator,
    deleteProgramPortfolioValidator,
    updateGenderSegregationValidator,
    updateTimeZoneValidator,
} = require('./program-input.validator');
const catchAsync = require('../../utility/catch-async');
const { validate } = require('../../utility/input-validation');

router.get('/program/:id', validate(programIdValidator), catchAsync(getProgram));
router.post('/add-program', validate(addProgramValidator), catchAsync(addProgram));
router.get('/list-programs/:id', validate(getProgramsValidator), catchAsync(getInstitutePrograms));
router.put('/update-program/:id', validate(editProgramValidator), catchAsync(editProgram));
router.patch('/archive-program/:id', validate(programIdValidator), catchAsync(archieveProgram));
router.patch(
    '/restore-program/:id',
    validate(programIdValidator),
    catchAsync(restoreArchivedProgram),
);
router.patch('/remove-program/:id', validate(programIdValidator), catchAsync(deleteProgram));
router.patch('/configure-program/:id', validate(programIdValidator), catchAsync(configureProgram));
router.patch(
    '/add-program-goal/:id',
    uploadProgramMediaFile,
    validate(addGoalsValidator),
    catchAsync(addOrEditProgramGoals),
);
router.patch(
    '/add-program-objective/:id',
    uploadProgramMediaFile,
    catchAsync(addOrEditProgramObjectives),
);
router.patch('/add-program-portfolio/:id', uploadProgramMediaFile, catchAsync(addProgramPortfolio));
router.patch(
    '/edit-program-portfolio/:id',
    uploadProgramMediaFile,
    catchAsync(editProgramPortfolio),
);
router.patch('/add-breaks', validate(addBreakValidator), catchAsync(addBreaks));
router.patch('/edit-break/:id', validate(editBreakValidator), catchAsync(editBreaks));
router.patch('/remove-break/:id', validate(removeBreakValidator), catchAsync(removeBreaks));
router.patch('/add-eventtype', validate(addEventTypeValidator), catchAsync(addEventType));
router.patch('/edit-eventtype/:id', validate(editEventTypeValidator), catchAsync(editEventType));
router.put(
    '/update-gender-segregation',
    validate(updateGenderSegregationValidator),
    catchAsync(updateGenderSegregation),
);
router.put('/update-time-zone', validate(updateTimeZoneValidator), catchAsync(updateTimeZone));
router.patch(
    '/remove-eventtype/:id',
    validate(removeEventTypeValidator),
    catchAsync(removeEventType),
);
router.patch(
    '/update-curriculum/:id',
    validate(updateCurriculumValidator),
    catchAsync(updateCurriculum),
);
router.patch(
    '/update-credithours/:id',
    validate(updateCreditHoursValidator),
    catchAsync(updateCreditHours),
);
router.patch(
    '/update-program-duration-format/:id',
    validate(updateCreditHoursValidator),
    catchAsync(updateProgramDurationFormat),
);
router.patch(
    '/delete-university-break',
    validate(deleteParentInstituteBreakValidator),
    catchAsync(deleteParentInstituteBreak),
);
router.patch(
    '/delete-university-event',
    validate(deleteParentInstituteEventValidator),
    catchAsync(deleteParentInstituteEvent),
);
router.get(
    '/program-institution-settings/:id',
    validate(getProgramInstitutionSettingsValidator),
    catchAsync(getProgramSettings),
);
router.delete(
    '/program-goal',
    validate(deleteProgramGoalsOrObjectivesValidator),
    catchAsync(deleteProgramGoals),
);
router.delete(
    '/program-objectives',
    validate(deleteProgramGoalsOrObjectivesValidator),
    catchAsync(deleteProgramObjectives),
);
router.delete(
    '/program-portfolio',
    validate(deleteProgramPortfolioValidator),
    catchAsync(deleteProgramPortfolio),
);

module.exports = router;
