const Joi = require('joi');
const {
    DC_STAFF,
    DC_STUDENT,
    SCHEDULE,
    PUBLISHED,
    DRAFT,
    NOT_DRAFT,
    STARTED,
    NOT_STARTED,
    COMPLETED,
} = require('../../utility/constants');
const { com_response, comResponseWithRequest } = require('../../utility/common');
const {
    TIME,
    ONE_BY_ONE,
    QUIZ,
    POLL,
    SURVEY,
    OLD,
    NEW,
    MANUAL,
    SYSTEM,
    MIN,
    MAX,
    EXACTLY,
    CLOSELY,
} = require('../../utility/enums');

const {
    objectIdSchema,
    objectIdRQSchema,
    stringRQSchema,
    booleanRQSchema,
    numberSchema,
    stringSchema,
    dateSchema,
    booleanSchema,
} = require('../../utility/validationSchemas');

function createActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                courseId: Joi.string().length(24),
                sessionFlowIds: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string().length(24).optional(),
                        type: Joi.string(),
                    }),
                ),
                name: Joi.string().optional(),
                quizType: Joi.string()
                    .valid(...[QUIZ, POLL, SURVEY])
                    .optional(),
                activityId: Joi.string().length(24).optional(),
                createdBy: Joi.string().length(24).optional(),
                _program_id: Joi.string().length(24),
                year_no: Joi.string(),
                term: Joi.string(),
                level_no: Joi.string(),
                rotation: Joi.string().optional(),
                description: Joi.string().optional(),
                rotation_count: Joi.number().optional(),
                _institution_calendar_id: Joi.string().length(24),
                type: Joi.string()
                    .valid(...[SCHEDULE])
                    .optional(),
                status: Joi.string()
                    .valid(...[DRAFT, PUBLISHED])
                    .optional(),
                courseAdmin: Joi.boolean().optional(),
                questions: Joi.array()
                    .items(
                        Joi.object().keys({
                            questionId: Joi.string().length(24).optional(),
                            name: Joi.string().optional(),
                            options: Joi.array().optional(),
                            feedback: Joi.array().optional(),
                            createdBy: Joi.string().length(24).optional(),
                            sessionId: Joi.string().length(24),
                            sloIds: Joi.array().optional(),
                            taxonomyIds: Joi.array().optional(),
                            attachments: Joi.array().optional(),
                            order: Joi.number(),
                            type: Joi.string()
                                .valid(...[OLD, NEW])
                                .optional(),
                            questionType: Joi.string().optional(),
                            questionViewType: Joi.string().optional(),
                            describeYourQuestion: Joi.string().optional(),
                            itemCategory: Joi.string().optional(),
                            aiNoOfOptions: Joi.number().optional(),
                            generateFeedback: Joi.boolean().optional(),
                            surveyQuestionType: Joi.string().optional(),
                            maxCharacterLimit: Joi.number().optional(),

                            mark: Joi.number().optional(),
                            mandatory: Joi.boolean().optional(),
                            description: Joi.string().optional().allow(''),
                            descriptionEnable: Joi.boolean().optional(),
                            shuffleOptionOrder: Joi.boolean().optional(),
                            characterLength: Joi.string()
                                .valid(...[MIN, MAX])
                                .optional(),
                            matchingOptions: Joi.array().optional(),
                            answerMatchingType: Joi.string()
                                .valid(...[EXACTLY, CLOSELY])
                                .optional(),
                            benchMark: Joi.number().optional(),
                            answerTextVariant: Joi.array().optional(),
                            isFromHeba: Joi.boolean().optional(),
                        }),
                    )
                    .optional(),
                correctionType: Joi.string()
                    .valid(...[MANUAL, SYSTEM])
                    .optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// update activities schema
function updateActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                status: Joi.string(),
                isDelete: Joi.boolean().optional(),
                shuffleQuestionOrder: Joi.boolean().optional(),
                sessionFlowIds: Joi.array().optional(),
                name: Joi.string().optional(),
                description: Joi.string().optional(),
                quizType: Joi.string().optional(),
                type: Joi.string().optional(),
                questionType: Joi.string().optional(),
                courseAdmin: Joi.boolean(),
                questions: Joi.array()
                    .items(
                        Joi.object().keys({
                            questionId: objectIdSchema.allow(''),
                            name: Joi.string().optional(),
                            questionType: Joi.string().optional(),
                            options: Joi.array().optional(),
                            type: Joi.string()
                                .valid(...[OLD, NEW])
                                .optional(),
                            feedback: Joi.array().optional(),
                            createdBy: objectIdSchema.allow(''),
                            sessionId: objectIdSchema.allow(''),
                            sloIds: Joi.array().optional(),
                            taxonomyIds: Joi.array().optional(),
                            attachments: Joi.array().optional(),
                            order: Joi.number(),
                            questionViewType: Joi.string().optional(),
                            describeYourQuestion: Joi.string().optional(),
                            itemCategory: Joi.string().optional(),
                            aiNoOfOptions: Joi.number().optional(),
                            generateFeedback: Joi.boolean().optional(),
                            surveyQuestionType: Joi.string()
                                .valid('Likert Scale', 'Open-Ended')
                                .optional(),
                            maxCharacterLimit: Joi.number().optional(),

                            mark: Joi.number().optional(),
                            mandatory: Joi.boolean().optional(),
                            description: Joi.string().optional().allow(''),
                            descriptionEnable: Joi.boolean().optional(),
                            shuffleOptionOrder: Joi.boolean().optional(),
                            characterLength: Joi.string()
                                .valid(...[MIN, MAX])
                                .optional(),
                            matchingOptions: Joi.array().optional(),
                            answerMatchingType: Joi.string()
                                .valid(...[EXACTLY, CLOSELY])
                                .optional(),
                            benchMark: Joi.number().optional(),
                            answerTextVariant: Joi.array().optional(),
                            isFromHeba: Joi.boolean().optional(),
                        }),
                    )
                    .optional(),
                schedule: Joi.object()
                    .keys({
                        startDateAndTime: Joi.string(),
                        endDateAndTime: Joi.string(),
                    })
                    .optional(),
                sessionId: Joi.string().length(24).optional(),
                scheduleIds: Joi.array().optional(),
                studentGroupId: Joi.string().length(24).optional(),
                sessionRemoved: Joi.boolean().optional(),
                correctionType: Joi.string()
                    .valid(...[MANUAL, SYSTEM])
                    .optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// delete activities schema
function deleteActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                type: Joi.string()
                    .valid(...[SCHEDULE])
                    .optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// get activities schema
function getActivitySchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// get result schema
function getResultSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string().length(24),
            },
            query: {
                type: Joi.string().valid(...[DC_STAFF, DC_STUDENT]),
                userId: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function quizStartByStaffSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string().length(24),
                sessionId: Joi.string().length(24),
                scheduleIds: Joi.array(),
                staffStartWithExam: Joi.string().valid(...[TIME, ONE_BY_ONE]),
                time: Joi.number(),
                questionId: Joi.string().length(24).optional(),
                courseAdmin: Joi.boolean().optional(),
                shuffleQuestionOrder: Joi.boolean().optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function quizStopByStaffSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string().length(24),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// answered question schema
function questionAnsweredByStudentSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string().length(24),
                questions: Joi.array(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function acceptQuestionResponseSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string().length(24),
                questionId: Joi.string().length(24),
                acceptQuestion: Joi.boolean(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// select session schema
function selectSessionsSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string().length(24).required(),
            },
            query: {
                userId: Joi.string().length(24).required(),
                _program_id: Joi.string().length(24).optional(),
                year_no: Joi.string().optional(),
                level_no: Joi.string().optional(),
                term: Joi.string().optional(),
                rotation: Joi.string().optional(),
                rotation_count: Joi.number().optional(),
                _institution_calendar_id: Joi.string().length(24).optional(),
                courseAdmin: Joi.boolean().optional(),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// get students schema
function getStudentParamSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// update activities schema
function saveStudentMarksSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                questions: Joi.array()
                    .items(
                        Joi.object().keys({
                            _questionId: Joi.string().length(24),
                            staffCorrectedAnswer: Joi.string().valid('correct', 'wrong'),
                            marks: Joi.number(),
                        }),
                    )
                    .optional(),
            }),
            query: {
                activityId: Joi.string().length(24),
                studentId: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// get activities schema
function getActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                userId: Joi.string().length(24),
                courseId: Joi.string().length(24).optional(),
                type: Joi.string().valid(...[DC_STAFF, DC_STUDENT]),
                sessionId: Joi.string().length(24),
                scheduleId: Joi.string().length(24),
                mergeStatus: Joi.boolean().optional(),
                page: Joi.number().optional(),
                limit: Joi.number().optional(),
                courseAdmin: Joi.boolean().optional(),
                search: Joi.string().optional(),
                mode: Joi.string()
                    .valid(...[NOT_STARTED, COMPLETED, DRAFT, STARTED, NOT_DRAFT])
                    .optional(),
                _program_id: Joi.string().length(24).optional(),
                year_no: Joi.string().optional(),
                level_no: Joi.string().optional(),
                term: Joi.string().optional(),
                rotation: Joi.string().optional(),
                rotation_count: Joi.number().optional(),
                _institution_calendar_id: Joi.string().length(24).optional(),
                mergedStatus: Joi.boolean().optional(),
            },
            headers: Joi.object({
                _institution_calendar_id: Joi.string().length(24),
            }).options({ allowUnknown: true }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// Publish student marks activities schema
function publishStudentMarksSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                activityId: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function questionBankStaffList(req, res, next) {
    const schema = Joi.object({
        body: Joi.object({
            courseId: Joi.array().items(Joi.string().hex().length(24)).min(1).required(),
        }),
    }).unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

function questionBankAllQuestion(req, res, next) {
    const schema = Joi.object({
        body: Joi.object({
            courseIds: Joi.array().items(Joi.string().hex().length(24)).min(1).required(),
            userIds: Joi.array().items(Joi.string().hex().length(24)).optional(),
            questionTypes: Joi.array().items(Joi.string()).optional(),
        }),
    }).unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
function createOrUpdateQuestionValidator(req, res, next) {
    const schema = Joi.object({
        body: Joi.object({
            courseId: Joi.string().length(24).required(),
            name: Joi.string().optional(),
            options: Joi.array().optional(),
            feedback: Joi.array().optional(),
            createdBy: Joi.string().length(24).optional(),
            sloIds: Joi.array().optional(),
            taxonomyIds: Joi.array().optional(),
            attachments: Joi.array().optional(),
            order: Joi.number(),
            type: Joi.string()
                .valid(...[OLD, NEW])
                .optional(),
            questionType: Joi.string().optional(),
            questionViewType: Joi.string().optional(),
            describeYourQuestion: Joi.string().optional(),
            itemCategory: Joi.string().optional(),
            aiNoOfOptions: Joi.number().optional(),
            generateFeedback: Joi.boolean().optional(),
            surveyQuestionType: Joi.string().optional(),
            maxCharacterLimit: Joi.number().optional(),
            mark: Joi.number().optional(),
            mandatory: Joi.boolean().optional(),
            description: Joi.string().optional().allow(''),
            descriptionEnable: Joi.boolean().optional(),
            shuffleOptionOrder: Joi.boolean().optional(),
            characterLength: Joi.string()
                .valid(...[MIN, MAX])
                .optional(),
            matchingOptions: Joi.array().optional(),
            answerMatchingType: Joi.string()
                .valid(...[EXACTLY, CLOSELY])
                .optional(),
            benchMark: Joi.number().optional(),
            answerTextVariant: Joi.array().optional(),
        }).unknown(true),
    }).unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

module.exports = {
    getActivitySchema,
    quizStartByStaffSchema,
    quizStopByStaffSchema,
    questionAnsweredByStudentSchema,
    getResultSchema,
    acceptQuestionResponseSchema,
    createActivitiesSchema,
    updateActivitiesSchema,
    deleteActivitiesSchema,
    selectSessionsSchema,
    getStudentParamSchema,
    saveStudentMarksSchema,
    getActivitiesSchema,
    publishStudentMarksSchema,
    questionBankStaffList,
    questionBankAllQuestion,
    createOrUpdateQuestionValidator,
};
