const { PUBLISHED } = require('../utility/constants');
const { logger } = require('../utility/util_keys');
const { redisClient } = require('../../../config/redis-connection');
const institutionCalendar = require('../components/institution-calendar/institution-calendar.model');

const insCalenderV2CacheID = 'v2institutionCalendar';

const getInstitutionCalendarFromRedis = async () => {
    const isExist = await redisClient.Client.exists(insCalenderV2CacheID);
    //return 0 if key is not exist
    if (isExist === 0) {
        logger.info(
            'service --> redis cache.service --> getInstitutionCalendar --> Get Institution Calendar from DB',
        );
        const icData = await institutionCalendar
            .find({ isDeleted: false, status: PUBLISHED })
            .lean();

        const setKeys = await redisClient.Client.set(insCalenderV2CacheID, JSON.stringify(icData));
    } else {
        logger.info(
            'service -->redis cache.service --> getInstitutionCalendar --> Get cached Institution Calendar v2 cache',
        );
    }
    return isExist === 0 ? icData : await redisClient.Client.get(insCalenderV2CacheID);
};

module.exports = {
    getInstitutionCalendarFromRedis,
};
