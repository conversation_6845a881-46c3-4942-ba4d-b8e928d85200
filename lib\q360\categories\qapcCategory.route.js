const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    createCategoryConfigure,
    getConfigureTemplate,
    updateCategoryConfigure,
    singleCategoryConfig,
} = require('./qapcCategory.controller');
const validator = require('./qapcCategory.validator');
const { validate } = require('../../../middleware/validation');

router.post(
    '/createCategoryConfigure',
    validate(validator.createCategoryConfigureValidator),
    catchAsync(createCategoryConfigure),
);
router.get(
    '/getConfigureTemplate',
    validate(validator.getConfigureTemplateValidator),
    catchAsync(getConfigureTemplate),
);
router.put(
    '/updateCategoryConfigure',
    validate(validator.updateCategoryConfigureValidator),
    catchAsync(updateCategoryConfigure),
);
router.get(
    '/singleCategoryConfig',
    validate(validator.singleCategoryConfigValidator),
    catchAsync(singleCategoryConfig),
);
module.exports = router;
