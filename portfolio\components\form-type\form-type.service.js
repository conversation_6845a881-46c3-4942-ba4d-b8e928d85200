const FormTypeModel = require('./form-type.model');
const UserModel = require('../../../lib/models/user');
const {
    UpdateFailedError,
    NotFoundError,
    BadRequestError,
} = require('../../common/utils/api_error_util');

const formTypeProject = {
    name: 1,
    code: 1,
    createdBy: 1,
    createdAt: 1,
    updatedAt: 1,
};

const createFormType = async ({ name, code, userId }) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    const existingFormType = await FormTypeModel.findOne({ code }, { isDeleted: 1 }).lean();
    if (existingFormType && !existingFormType?.isDeleted) {
        throw new BadRequestError('FORM_TYPE_ALREADY_EXISTS');
    }

    if (existingFormType && existingFormType?.isDeleted) {
        await FormTypeModel.updateOne(
            { _id: existingFormType._id },
            { $set: { isDeleted: false } },
        );

        return existingFormType._id;
    }

    const { _id } = await FormTypeModel.create({
        name,
        code,
        createdBy: { id: userId, name: user.name },
    });

    return _id;
};

const getFormTypes = async () => {
    const project = { ...formTypeProject };

    const formTypes = await FormTypeModel.find({ isDeleted: false }, project).lean();

    return formTypes;
};

const updateFormType = async ({ formTypeId, name, code }) => {
    const updateDoc = {
        ...(name && { name }),
        ...(code && { code }),
    };

    const formType = await FormTypeModel.updateOne({ _id: formTypeId }, updateDoc);
    if (!formType.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }
};

const deleteFormType = async ({ formTypeId }) => {
    const formType = await FormTypeModel.updateOne(
        { _id: formTypeId },
        { $set: { isDeleted: true } },
    );
    if (!formType.modifiedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    return formType;
};

module.exports = {
    createFormType,
    getFormTypes,
    updateFormType,
    deleteFormType,
};
