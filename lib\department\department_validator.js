const Joi = require('joi');
const common_files = require('../utility/common');

exports.department = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                return error;
            }),
            department_title: Joi.string().allow(' ').min(3).max(100).trim().required().error(error => {
                return error;
            }),
            _division_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _subject_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.department_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            department_title: Joi.string().allow(' ').min(3).max(100).trim().error(error => {
                return error;
            }),
            _division_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _subject_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.department_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.program_no = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            no: Joi.string().alphanum().required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.department_division_subject_get = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _department_id: Joi.array().items(Joi.string().alphanum().length(24).allow('').error(error => {
                return error;
            })),
            _division_id: Joi.array().items(Joi.string().alphanum().length(24).allow('').error(error => {
                return error;
            }))
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}