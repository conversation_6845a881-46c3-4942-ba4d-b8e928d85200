const express = require('express');
const route = express.Router();
const {
    getProgramReportSetting,
    getProgramReportSettingBenchMark,
    updateProgramReportSetting,
    updateProgramReportSettingCourse,
} = require('./program_report_setting_controller');
const {
    getProgramReportSettingSchema: { query: getProgramReportSettingQuerySchema },
    updateProgramReportSettingSchema: {
        query: updateProgramReportSettingSchemaQuerySchema,
        body: updateProgramReportSettingSchemaBodySchema,
    },
    getProgramReportSettingBenchMarkSchema: { query: getProgramReportSettingBenchMarkQuerySchema },
    updateProgramReportSettingCourseSchema: {
        query: updateProgramReportSettingCourseQuerySchema,
        body: updateProgramReportSettingCourseBodySchema,
    },
} = require('./program_report_setting_validator');
const { validate } = require('../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
route.get(
    '/benchMark',
    validate([{ schema: getProgramReportSettingBenchMarkQuerySchema, property: 'query' }]),
    getProgramReportSettingBenchMark,
);
route.get(
    '/',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, ...defaultProgramInputPolicy.VIEW])],
    validate([{ schema: getProgramReportSettingQuerySchema, property: 'query' }]),
    getProgramReportSetting,
);

route.put(
    '/course',
    validate([
        { schema: updateProgramReportSettingCourseQuerySchema, property: 'query' },
        { schema: updateProgramReportSettingCourseBodySchema, property: 'body' },
    ]),
    updateProgramReportSettingCourse,
);
route.put(
    '/',
    [userPolicyAuthentication(['program_input:programs:add_program'])],
    validate([
        { schema: updateProgramReportSettingSchemaQuerySchema, property: 'query' },
        { schema: updateProgramReportSettingSchemaBodySchema, property: 'body' },
    ]),
    updateProgramReportSetting,
);

module.exports = route;
