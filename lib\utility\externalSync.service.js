const institutionCalendarSchema = require('../models/institution_calendar');
const programCalendarSchema = require('../models/program_calendar');
const programSchema = require('../models/digi_programs');
// const courseSchema = require('../models/digi_course');
const userSchema = require('../models/user');
const { DS_LEVEL_KEY, DS_YEAR_KEY, PUBLISHED } = require('./constants');
const { convertToMongoObjectId } = require('./common');
const commonFilter = { isActive: true, isDeleted: false };

const findTermName = ({ inputTerm, terms = [] }) => {
    const termShorthand = inputTerm.charAt(0);
    const regex = new RegExp('^' + termShorthand, 'i');
    return terms.find((term) => regex.test(term.term_name));
};

const findYearLevelName = ({ inputData, type }) => {
    switch (type) {
        case DS_LEVEL_KEY:
            return `Level ${inputData.slice(1)}`;
        case DS_YEAR_KEY:
            return `year${inputData.slice(1)}`;
        default:
            return '';
    }
};

const daInputDataMigrateDCInputs = async (req, res, next) => {
    try {
        const {
            institutionCalendarId,
            academicStart,
            academicEnd,
            programCode,
            programTerm,
            programYear,
            programLevel,
            courseCode,
            rotationNo,
            studentId,
        } = req.body;
        const alteredCourseCode = courseCode.replace(/\s+/g, '').toLowerCase();
        if (academicStart && academicEnd) {
            const institutionCalendarData = await institutionCalendarSchema
                .findOne(
                    {
                        isDeleted: false,
                        isActive: true,
                        status: PUBLISHED,
                        start_date: {
                            $gte: new Date(academicStart, 0, 1),
                            $lte: new Date(academicEnd, 11, 31),
                        },
                        end_date: {
                            $gte: new Date(academicStart, 0, 1),
                            $lte: new Date(academicEnd, 11, 31),
                        },
                    },
                    { _id: 1 },
                )
                .sort({ _id: -1 })
                .lean();
            if (institutionCalendarData)
                req.body.institutionCalendarId = String(institutionCalendarData._id);
        }
        if (institutionCalendarId) req.body.institutionCalendarId = institutionCalendarId;
        // Year & Level Conversion
        req.body.year = findYearLevelName({ inputData: programYear, type: DS_YEAR_KEY });
        req.body.level = findYearLevelName({ inputData: programLevel, type: DS_LEVEL_KEY });
        const programData = await programSchema.findOne(
            { ...commonFilter, code: programCode },
            { _id: 1, 'term.term_name': 1 },
        );
        if (studentId) {
            const userData = await userSchema.findOne({ user_id: studentId }, { _id: 1 }).lean();
            if (userData) {
                req.body.studentId = String(userData._id);
            }
        }
        if (programData && req.body.institutionCalendarId) {
            req.body.programId = String(programData._id);
            const programTermData = findTermName({
                inputTerm: programTerm,
                terms: programData.term,
            });
            req.body.term =
                programTermData && programTermData.term_name ? programTermData.term_name : '';

            // Course Code Conversion
            const programCalendarData = await programCalendarSchema.findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(
                        req.body.institutionCalendarId,
                    ),
                    _program_id: convertToMongoObjectId(programData._id),
                    status: PUBLISHED,
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_number': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            );
            if (programCalendarData) {
                const programLevelData = programCalendarData.level.find(
                    (levelElement) =>
                        levelElement.term.toLowerCase() === req.body.term.toLowerCase() &&
                        levelElement.year === req.body.year &&
                        levelElement.level_no === req.body.level,
                );
                if (programLevelData) {
                    req.body.term = programLevelData.term;
                    if (rotationNo) {
                        const rotationDetails = programLevelData.rotation_course.find(
                            (rotationElement) =>
                                String(rotationElement.rotation_count) === String(rotationNo),
                        );
                        if (rotationDetails) {
                            const courseData = rotationDetails.course.find(
                                (courseElement) =>
                                    courseElement.courses_number
                                        .replace(/\s+/g, '')
                                        .toLowerCase() === alteredCourseCode,
                            );
                            if (courseData) req.body.courseId = String(courseData._course_id);
                        }
                    } else {
                        const courseData = programLevelData.course.find(
                            (courseElement) =>
                                courseElement.courses_number.replace(/\s+/g, '').toLowerCase() ===
                                alteredCourseCode,
                        );
                        if (courseData) req.body.courseId = String(courseData._course_id);
                    }
                }
            }
        }
        next();
    } catch (error) {
        next(error);
    }
};
module.exports = { daInputDataMigrateDCInputs };
