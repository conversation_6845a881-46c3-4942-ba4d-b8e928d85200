const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    INSTITUTION,
    COURSE_SCHEDULE,
    USER,
    SCHEDULE_MULTI_DEVICE_ATTENDANCE,
    // PRESENT,
    // ABSENT,
    // PENDING,
    RUNNING,
    COMPLETED,
    RETAKE,
    PRIMARY,
} = require('../utility/constants');

const scheduleMultiDeviceAttendance = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        scheduleId: {
            type: Schema.Types.ObjectId,
            ref: COURSE_SCHEDULE,
        },
        _staff_id: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        modeBy: {
            type: String,
            enum: [RETAKE, PRIMARY],
        },
        status: {
            type: String,
            enum: [RUNNING, COMPLETED],
        },
        scheduleDate: { type: Date },
        scheduleSecurityCode: { type: String },
        studentIds: [{ type: Schema.Types.ObjectId, ref: USER }],
        // students: [
        //     {
        //         _student_id: {
        //             type: Schema.Types.ObjectId,
        //             ref: USER,
        //         },
        //         status: {
        //             type: String,
        //             enum: [PRESENT, ABSENT],
        //             default: PENDING,
        //         },
        //         time: Date,
        //     },
        // ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(SCHEDULE_MULTI_DEVICE_ATTENDANCE, scheduleMultiDeviceAttendance);
