// require('dotenv').config();
const Notification = require('../models/notification');
const { insert } = require('../base/base_controller');
const { logger, FCM_SECRET_KEY } = require('./util_keys');
const { convertToMongoObjectId } = require('./common');
const { makeTitleAndMessage } = require('../digi_class/sessions/session_service');
const { sendNotificationPush } = require('../../service/pushNotification.service');

const notification_push = async (tokens, push_title, push_body, data) => {
    try {
        const document = {
            users: [],
            title: push_title,
            description: push_body,
            buttonAction: data.clickAction,
            scheduleId: data._id,
            ...data,
        };
        for (const key in data) {
            if (Object.hasOwnProperty.call(data, key)) {
                data[key] = data[key] ? data[key].toString() : undefined;
            }
        }
        const messageDatum = {
            Title: push_title,
            Body: push_body,
            ClickAction: data.clickAction.toString(),
            rotation: data.rotation,
            rotation_count: data.rotation_count || undefined,
            ScheduleId: data._id || undefined,
            programId: data.programId || undefined,
            institutionCalendarId: data.institutionCalendarId || undefined,
            yearNo: data.yearNo || undefined,
            levelNo: data.levelNo || undefined,
            term: data.term || undefined,
            mergeStatus: data.mergeStatus || undefined,
            mergeType: data.mergeType || undefined,
            CourseId: data.courseId || undefined,
            activityId: data.activityId || undefined,
            StaffStartWithExam: data.StaffStartWithExam || undefined,
            notificationPeriod: data.notificationPeriod || undefined,
            scheduleStartFrom: data?.scheduleStartFrom,
        };
        Object.keys(messageDatum).forEach((key) => {
            if (messageDatum[key] === undefined) {
                delete messageDatum[key];
            }
        });
        for (element of tokens) {
            const notification = { title: push_title, body: push_body };
            const push_status = 'success';
            const messageData = {
                ...messageDatum,
                Session: element.sessionId ? element.sessionId.toString() : data.sessionId || '',
            };
            if (element.schedule) {
                const { title, message } = makeTitleAndMessage(element.schedule);
                messageData.Title = title;
                messageData.Body = message;
                notification.title = title;
                notification.body = message;
            }
            notification.push_title = messageData.Title;
            if (element.scheduleId) messageData.ScheduleId = element.scheduleId.toString();
            await sendNotificationPush(
                element.token || element.web_fcm_token,
                messageData,
                element.device_type,
            );
            const result = {
                _id: convertToMongoObjectId(element._user_id),
                push_status,
                redirect: messageData.Session
                    ? convertToMongoObjectId(messageData.Session)
                    : convertToMongoObjectId(messageData.ScheduleId),
                title: messageData.Title,
                body: messageData.Body,
                scheduleId: convertToMongoObjectId(messageData.ScheduleId),
            };
            document.users.push(result);
        }
        delete document._id;
        if (document.users.length) {
            const documents = [];
            const sessionIds = [...new Set(document.users.map((user) => user.redirect.toString()))];
            sessionIds.forEach((id) => {
                const tempDoc = { ...document };
                tempDoc.users = document.users.filter(
                    (user) => user.redirect.toString() === id.toString(),
                );
                tempDoc.sessionId = tempDoc.users[0].redirect;
                tempDoc.title = tempDoc.users[0].title;
                tempDoc.description = tempDoc.users[0].body;
                tempDoc.scheduleId = tempDoc.users[0].scheduleId;
                documents.push(tempDoc);
            });
            Notification.insertMany(documents);
        }
    } catch (error) {
        throw new Error(error);
    }
};

const saveDiscussionNotification = async (data) => {
    try {
        Notification.insertMany([data]);
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = {
    notification_push,
    saveDiscussionNotification,
};
