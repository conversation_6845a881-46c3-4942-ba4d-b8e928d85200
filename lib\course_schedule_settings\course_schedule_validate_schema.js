const Joi = require('joi');
// constants
const {
    GENDER: { MALE, FEMALE, BOTH },
    DAYS_MODE: { DAILY, WEEKLY },
    COURSE_MANAGEMENT_SESSION_TYPE: { EXTRA_CURRICULAR, BREAK },
    DAYS: { SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY },
} = require('../utility/constants');

// enums
const { AM, PM } = require('../utility/enums');

// create activities schema
function createExtraCurricularBreakTiming() {
    const schema = {
        body: Joi.object().keys({
            title: Joi.string(),
            gender: Joi.string().valid(...[MALE, FEMALE, BOTH]),
            mode: Joi.string().valid(...[DAILY, WEEKLY]),
            startTime: Joi.object().pattern(/.*/, [
                Joi.number(),
                Joi.number(),
                Joi.string().valid(...[AM, PM]),
            ]),
            endTime: Joi.object().pattern(/.*/, [
                Joi.number(),
                Joi.number(),
                Joi.string().valid(...[AM, PM]),
            ]),
            allowCourseCoordinatesToEdit: Joi.boolean(),
            days: Joi.array().items(
                Joi.string().valid(
                    ...[SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY],
                ),
            ),
            type: Joi.string().valid(...[EXTRA_CURRICULAR, BREAK]),
        }),
    };
    return schema;
}
function updateExtraCurricularBreakTiming() {
    const schema = {
        body: Joi.object().keys({
            activeStatus: Joi.boolean(),
        }),
    };
    return schema;
}

module.exports = {
    createExtraCurricularBreakTiming: createExtraCurricularBreakTiming(),
    updateExtraCurricularBreakTiming: updateExtraCurricularBreakTiming(),
};
