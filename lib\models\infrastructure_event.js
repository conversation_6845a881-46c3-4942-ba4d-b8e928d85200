const mongoose = require("mongoose");
const { INFRASTRUCTURE_EVENTS } = require("../utility/constants");
const { Schema } = mongoose;
const constant = require('../utility/constants');

const schema = new Schema(
  {
    infrastructure_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      trim: true,
    },
    creator_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      trim: true,
    },
    event_type: {
      type: String,
      required: true,
      trim: true,
    },
    event_name: {
      type: String,
      required: true,
      trim: true,
    },
    agenta: [String],
    event_for: {
      type: String,
      required: true,
      trim: true
    },
    gender: {
      type: String,
      required: true,
      trim: true,
    },
    no_of_attendees: {
      type: Number,
      required: true,
      trim: true,
    },
    comittes: [mongoose.Schema.Types.ObjectId],
    participants: [mongoose.Schema.Types.ObjectId],
    start_date: {
      type: String,
      required: true,
      trim: true,
    },
    end_date: {
      type: String,
      required: true,
      trim: true,
    },
    start_time: {
      type: String,
      required: true,
      trim: true,
    },
    end_time: {
      type: String,
      required: true,
      trim: true,
    },
    event_mode: {
      type: String,
      required: true,
      trim: true,
    },
    status: {
      type: String,
      required: true,
      trim: true,
    },
    infra_approved: {
      type: Boolean,
      required: true,
      trim: true,
      default: false,
    },
    infra_canceled: {
      type: Boolean,
      required: true,
      trim: true,
      default: false,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true, versionKey: false }
);
module.exports = mongoose.model(INFRASTRUCTURE_EVENTS, schema);
