const express = require('express');
const router = express.Router();
const {
    getAssignmentCategory,
    addAssignmentCategory,
    editAssignmentCategory,
    deleteAssignmentCategory,
} = require('./assignment_category.controller');
const catchAsync = require('../../../utility/catch-async');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.get(
    '/list-category/:_course_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getAssignmentCategory),
);
router.post(
    '/add-category',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(addAssignmentCategory),
);
router.put(
    '/edit-category/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(editAssignmentCategory),
);
router.delete(
    '/delete-category/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(deleteAssignmentCategory),
);

module.exports = router;
