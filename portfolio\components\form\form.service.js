const FormModel = require('./form.model');
const StudentResponseModel = require('../student-response/student-response.model');
const UserModel = require('../../../lib/models/user');
const PortfolioRoleModel = require('../role/role.model');

const { ON_GOING, PUBLISHED, NOT_STARTED } = require('../../common/utils/enums');
const {
    UpdateFailedError,
    NotFoundError,
    BadRequestError,
} = require('../../common/utils/api_error_util');
const { getAssignedUserSections, buildFormQuery } = require('./form.helper');
const { isNumber, isIDEquals } = require('../../common/utils/common.util');
const { calculateRubricPoint } = require('../rubric/rubric.helper');
const { STUDENT, STAFF } = require('../../common/utils/constants');

const formProject = {
    title: 1,
    description: 1,
    pages: 1,
    type: 1,
    publishedDate: 1,
    createdBy: 1,
    status: 1,
    rubrics: 1,
    evaluations: 1,
};

const createForm = async ({ title, type, description, pages = [], rubrics = [], userId }) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    const { _id } = await FormModel.create({
        title,
        type,
        ...(description && { description }),
        pages,
        status: ON_GOING,
        createdBy: { id: userId, name: user.name },
        isTemplate: true,
        rubrics,
    });

    return _id;
};

const getForms = async ({ formId, isPages, type, userId, status }) => {
    const project = {
        title: 1,
        description: 1,
        type: 1,
        publishedDate: 1,
        createdBy: 1,
        status: 1,
        pages: 1,
    };

    if (formId) {
        const form = await FormModel.findOne(
            { _id: formId, isDeleted: false },
            isPages ? formProject : project,
        ).lean();
        if (!form) {
            throw new NotFoundError('FORM_NOT_FOUND');
        }

        return form;
    }

    const forms = await FormModel.find(
        {
            isDeleted: false,
            isTemplate: true,
            ...(type && { 'type.code': type }),
            ...(status && { status }),
            ...(userId && { 'createdBy.id': userId }),
        },
        project,
    ).lean();
    if (!forms?.length) {
        throw new NotFoundError('FORM_NOT_FOUND');
    }

    return forms;
};

const updateForm = async ({ formId, title, type, description, pages, evaluations }) => {
    const updateDoc = {
        ...(title && { title }),
        ...(type && { type }),
        ...(description && { description }),
        ...(pages.length && { pages }),
        ...(evaluations.length && { evaluations }),
    };

    if (evaluations?.length) {
        let totalEvaluationMarks = 0;
        evaluations.forEach((evaluation) => {
            if (evaluation?.rubrics?.length) {
                const totalRubricPoints = calculateRubricPoint({ rubrics: evaluation.rubrics });
                totalEvaluationMarks += totalRubricPoints;
                evaluation.marks = totalRubricPoints;
            } else if (isNumber(evaluation?.marks)) {
                totalEvaluationMarks += evaluation.marks;
            }
        });

        Object.assign(updateDoc, { marks: totalEvaluationMarks });
    }

    const form = await FormModel.updateOne({ _id: formId }, updateDoc);
    if (!form.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }
};

const deleteForm = async ({ formId }) => {
    const form = await FormModel.updateOne({ _id: formId }, { $set: { isDeleted: true } });
    if (!form.modifiedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    return form;
};

const getFormsByUser = async ({ email, type, formId, isPages }) => {
    const forms = await FormModel.find(buildFormQuery({ email, formId }), {
        title: 1,
        type: 1,
        description: 1,
        ...(isPages && { pages: 1 }),
        status: 1,
        createdBy: 1,
        publishedDate: 1,
        marks: 1,
    }).lean();

    if (!type === STUDENT)
        return isPages ? forms.map((form) => getAssignedUserSections({ form, email })) : forms;

    const studentResponses = await StudentResponseModel.find(
        { 'student.email': email },
        {
            ...(isPages && { pages: 1 }),
            status: 1,
            formId: 1,
            totalMarks: 1,
            awardedMarks: 1,
            evaluationStatus: 1,
        },
    ).lean();

    const targetForms = isPages
        ? forms.map((form) => getAssignedUserSections({ form, email }))
        : forms;

    return targetForms.map((form) => {
        const response = studentResponses.find((response) => isIDEquals(response.formId, form._id));
        return {
            ...form,
            ...(response && response),
            status: response?.status || NOT_STARTED,
        };
    });
};

const validateForm = async ({ formId }) => {
    const form = await FormModel.findOne(
        { _id: formId, isDeleted: false },
        { pages: 1, status: 1 },
    );

    if (!form) return 'FORM_NOT_FOUND';

    if (form.status === PUBLISHED) return 'FORM_ALREADY_PUBLISHED';

    if (!form.pages?.length) return 'FORM_HAS_NO_ELEMENTS';

    const isEvaluatorExists = form.pages.every((page) => {
        return page.elements.every((element) => {
            const isEvaluateByRole = element.roles?.some((role) => role.evaluate);
            const isEvaluateByUser = element.users?.some((user) => user.evaluate);

            return isEvaluateByRole || isEvaluateByUser;
        });
    });

    if (!isEvaluatorExists) return 'PLEASE_ASSIGN_EVALUATOR_FOR_EVERY_SECTION';

    return null;
};

const publishForm = async ({ formId }) => {
    const result = await FormModel.updateOne(
        { _id: formId },
        { $set: { status: PUBLISHED, publishedDate: new Date() } },
    );
    if (!result.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }
};

const getAssignedRolesInForm = async ({ formId }) => {
    const form = await FormModel.findOne(
        { _id: formId, status: PUBLISHED },
        { 'pages.elements.roles': 1, title: 1, 'type.name': 1 },
    ).lean();
    if (!form) {
        throw new NotFoundError('FORM_NOT_FOUND');
    }

    const roles = form.pages.flatMap((page) => page.elements.flatMap((element) => element.roles));

    const filteredRoles = roles.filter((role) => role.evaluate);

    const assignedRoles = await PortfolioRoleModel.find(
        { _id: { $in: filteredRoles.map(({ roleId }) => roleId) }, type: STAFF },
        { name: 1, roleId: '$_id', _id: 0 },
    ).lean();

    return { roles: assignedRoles, name: form.title, type: form.type.name };
};

module.exports = {
    createForm,
    getForms,
    updateForm,
    deleteForm,
    getFormsByUser,
    publishForm,
    validateForm,
    getAssignedRolesInForm,
};
