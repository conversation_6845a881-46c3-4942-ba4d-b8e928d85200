const passport = require('passport');
const { APP_STATE, DSCRONKEY } = require('../lib/utility/util_keys');
const jwtDecode = require('jwt-decode');
const { decryptData } = require('../lib/utility/encrypt_decrypt.util');

const verifyCallback = (req, resolve, reject) => async (err, user, info) => {
    if (APP_STATE !== 'local')
        if (err || info || !user) {
            return reject({
                status_code: 401,
                status: false,
                message: 'Please authenticate',
                data: 'Authentication not matching',
            });
        }
    req.user = user;
    resolve();
};

const auth = async (req, res, next) => {
    try {
        if (APP_STATE === 'local') next();
        else if (
            APP_STATE !== 'local' &&
            req.header('authorization') &&
            req.header('authorization').split(' ')
        ) {
            const TokenArray = req.header('authorization').split(' ');
            if (TokenArray.length && TokenArray[1]) {
                req.tokenPayload = decryptData({ content: jwtDecode(TokenArray[1]).payload });
                return new Promise((resolve, reject) => {
                    passport.authenticate(
                        'jwt',
                        { session: false },
                        verifyCallback(req, resolve, reject),
                    )(req, res, next);
                })
                    .then(() => {
                        const userIdFromHeaders = req.headers._user_id || req.headers.user_id;
                        if (
                            userIdFromHeaders &&
                            req.tokenPayload?._id &&
                            String(req.tokenPayload?._id) === String(userIdFromHeaders)
                        )
                            next();
                        else
                            return res.status(401).send({
                                status_code: 401,
                                status: false,
                                message: 'Please authenticate',
                                data: 'Authentication not matching',
                            });
                    })
                    .catch((err) => res.status(401).send(err));
            }
        } else {
            return res.status(401).send({
                status_code: 401,
                status: false,
                message: 'Please authenticate',
                data: 'Authentication not matching',
            });
        }
    } catch (error) {
        return res.status(401).send({
            status_code: 401,
            status: false,
            message: 'Please authenticate',
            data: 'Authentication not matching',
        });
    }
    // .catch((err) => next(err));
};

const serviceAuth = async (req, res, next) => {
    return new Promise(() => {
        if (
            req.headers &&
            req.headers.digicronkey &&
            req.headers.digicronkey === DSCRONKEY.toString()
        )
            next();
        else
            res.status(401).send({
                status_code: 401,
                status: false,
                message: 'Please authenticate',
                data: 'Authentication not matching you are not allowed',
            });
    });
};

const serviceStaticAuth = async (req, res, next) => {
    return new Promise(() => {
        if (
            req.headers &&
            req.headers.security_key &&
            req.headers.security_key === DSCRONKEY.toString()
        )
            next();
        else
            res.status(401).send({
                status_code: 401,
                status: false,
                message: 'Please authenticate',
                data: 'Authentication not matching you are not allowed',
            });
    });
};

module.exports = { auth, serviceAuth, serviceStaticAuth };
