const constant = require('../utility/constants');
var notification_manager = require('mongoose').model(constant.NOTIFICATIONS);
var Institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const institution_formate = require('./notification_manager_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        // { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        // { $unwind: '$institution' },
        // { $lookup: { from: constant.notification_manager, localField: '_primary_calendar_id', foreignField: '_id', as: 'primary_calendar' } },
        // { $unwind: { path: '$primary_calendar', preserveNullAndEmptyArrays: true } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(notification_manager, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "notification_manager list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.notification_manager(doc.data));
        common_files.list_all_response(res, 200, true, "notification_manager list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* institution_formate.notification_manager(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
        { $lookup: { from: constant.notification_manager, localField: '_primary_calendar_id', foreignField: '_id', as: 'primary_calendar' } },
        { $unwind: { path: '$primary_calendar', preserveNullAndEmptyArrays: true } }
    ];
    let doc = await base_control.get_aggregate(notification_manager, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "notification_manager details", /* doc.data */institution_formate.institution_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "notification_manager details", doc.data/* institution_formate.institution_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (data) => {
    let objs = {};
    // let checks = await base_control.check_id(notification_manager, { _master_id: { $in: data._master_id }, 'isDeleted': false });
    // if (checks.status) {
    objs = {
        notification_type: data.notification_type,
        notification_priority: data.notification_priority,
        title: data.title,
        notification_dates: {
            start_date: data.start_date,
            start_time: data.start_time,
            end_date: data.end_date,
            end_time: data.end_time
        },
        _master_id: data._master_id,
        web_link: data.web_link,
        _notify_to_id: data._notify_to_id,
        // notification_type: data.notification_type,
    };
    if (data._infra_id != undefined && data._infra_id.length == 24) {
        Object.assign(objs, { '_infra_id': data._infra_id });
    }
    await base_control.insert(notification_manager, objs);
    return true;
    // if (doc.status) {
    //     common_files.com_response(res, 201, true, "notification_manager Added successfully", doc.data);
    // } else {
    //     common_files.com_response(res, 500, false, "Error", doc.data);
    // }
    // } else {

    // }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(notification_manager, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "notification_manager List", institution_formate.institution_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};