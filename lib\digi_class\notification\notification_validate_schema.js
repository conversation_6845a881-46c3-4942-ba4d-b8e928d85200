const Joi = require('joi');
const { com_response } = require('../../utility/common');

// create activities schema
function createNotificationSchema(req, res, next) {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            userId: Joi.string().length(24),
            title: Joi.string().optional(),
            description: Joi.string().optional(),
            buttonName: Joi.string().optional(),
            buttonAction: Joi.string().optional(),
            courseId: Joi.string().length(24),
            sessionId: Joi.string().length(24),
            scheduleId: Joi.string().length(24),
        }),
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// get result schema
function getNotificationSchema(req, res, next) {
    const schema = Joi.object().keys({
        params: {
            userId: Joi.string().length(24),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// update activities schema
function updateNotificationSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                read: Joi.boolean().optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// delete activities schema
function deleteNotificationSchema(req, res, next) {
    const schema = Joi.object().keys({
        params: {
            id: Joi.string().length(24),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

module.exports = {
    createNotificationSchema: createNotificationSchema(),
    getNotificationSchema: getNotificationSchema(),
    updateNotificationSchema: updateNotificationSchema(),
    deleteNotificationSchema: deleteNotificationSchema(),
};
