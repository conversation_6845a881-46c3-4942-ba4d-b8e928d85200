const { convertToMongoObjectId } = require('../../utility/common');
const qapcFormInitiatorSchema = require('./qapcFormInitiator.model');
const formCourseGroupSchema = require('../categoryForm/formCourseGroups.module');
const qapcGuideResourceSchema = require('./formGuideResources.model');
const formSettingCourseSchema = require('../categoryForm/formSettingCourses.model');
const qapcSettingTagSchema = require('../qapcSetting/qapcSettingTag.model');
const formSettingSchema = require('../categoryForm/formSetting.model');
const {
    ARCHIVE,
    PUBLISHED,
    FORM_INITIATOR,
    RESUBMISSION,
    PENDING,
    APPROVED,
    TAG_LEVEL: { PROGRAM, COURSE, INSTITUTION },
} = require('../../utility/constants');
const qapcFormCategorySchema = require('../categories/qapcCategory.model');
const qapcIncorporateSectionSchema = require('./qapcIncorporateSection.model');
const { getPaginationValues } = require('../../utility/pagination');
const { searchKeyFunction } = require('../../utility/common_functions');
const {
    updateCreateDocument,
    increaseMinimumCount,
    getCreatedFormList,
    updateFormData,
    getFormInitiatedData,
    filterMatchingGroup,
    getAttachmentDocument,
    categoryDocumentStructure,
    QAPCUserPermissionList,
    formSubmittedCount,
    listPublishedCategoryGroup,
    mergedPublishedGroup,
    categoryFormList,
    filterTeamAndAttemptType,
    uniqueFormCourseData,
    addActionKeyFormInitiator,
    termAndAttemptList,
    incorporateSectionList,
    updateCreatedDocument,
    matchingFormAttachment,
    removedIncorporateSection,
    userFromInitiatedList,
    updateRevokeApproverList,
    getFormOccurrence,
    newOccurrenceFormCreate,
    getFormGuideResourcesData,
    deleteIncorporateSection,
    deleteGuideResources,
    removeDuplicateForm,
} = require('./formInitiator.service');

exports.getCreateForm = async ({ headers, query }) => {
    try {
        const { _user_id, role_id } = headers;
        const { subModuleType } = query;
        return userFromInitiatedList({ userId: _user_id, roleId: role_id, subModuleType });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qapcLogUserList = async ({ headers = {}, query = {} }) => {
    try {
        const { _user_id, role_id, _institution_id } = headers;
        const {
            institutionCalenderId,
            categoryId,
            formType,
            searchKey,
            courseIds,
            institutionsIds,
            subModuleType,
            queryFormInitiatorIds,
            queryCategoryFormGroupIds,
        } = query;
        const pagination = getPaginationValues(query);
        const assuranceProcessQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            formCalenderIds: {
                $elemMatch: {
                    institutionCalenderId: convertToMongoObjectId(institutionCalenderId),
                    isDeleted: false,
                },
            },
            ...(searchKey && {
                $or: [
                    { programName: searchKeyFunction({ searchKey }) },
                    { courseName: searchKeyFunction({ searchKey }) },
                ],
            }),
            ...(categoryId && { categoryId: convertToMongoObjectId(categoryId) }),
            isDeleted: false,
            //filter course and institution for dashboard
            ...(courseIds?.length && {
                courseId: {
                    $in: courseIds.map((courseElement) => convertToMongoObjectId(courseElement)),
                },
            }),
            ...(institutionsIds?.length && {
                assignedInstitutionId: {
                    $in: institutionsIds.map((institutionElement) =>
                        convertToMongoObjectId(institutionElement),
                    ),
                },
            }),
            ...(queryFormInitiatorIds?.length && {
                _id: {
                    $in: queryFormInitiatorIds.map((formInitiatedElement) =>
                        convertToMongoObjectId(formInitiatedElement),
                    ),
                },
            }),
            ...(queryCategoryFormGroupIds?.length && {
                categoryFormGroupId: {
                    $in: queryCategoryFormGroupIds.map((formGroupElement) =>
                        convertToMongoObjectId(formGroupElement),
                    ),
                },
            }),
        };
        const userList = await QAPCUserPermissionList({
            userId: _user_id,
            roleId: role_id,
            subModuleType: subModuleType || FORM_INITIATOR,
        });
        if (!userList?.qapcPermissionList?.categoryFormGroupData?.[0].formGroupId) {
            return { statusCode: 200, message: 'NO_DATA_FOUND' };
        }
        const categoryFormGroupData = userList.qapcPermissionList.categoryFormGroupData
            ? userList.qapcPermissionList.categoryFormGroupData.filter(
                  (groupElement) => groupElement.formGroupId,
              )
            : [];
        let publishedGroupData;
        let mergedPublishedGroupData;
        if (categoryFormGroupData.length) {
            const groupIds = categoryFormGroupData.map((groupElement) => {
                return groupElement.formGroupId.toString();
            });
            const uniqueGroupIds = [...new Set(groupIds)];
            if (uniqueGroupIds.length) {
                publishedGroupData = await listPublishedCategoryGroup({ uniqueGroupIds });
            }
            if (!publishedGroupData.length) {
                return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
            }
            mergedPublishedGroupData = mergedPublishedGroup({
                publishedGroupData,
                categoryFormGroupData,
            });
        }
        const formInitiatorData = await getFormInitiatedData({ assuranceProcessQuery });
        let formAttachmentData;
        let formCount;
        let formList = [];
        if (!formInitiatorData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        const filterMatchingGroupData = filterMatchingGroup({ formInitiatorData });
        const matchingFormList = await addActionKeyFormInitiator({
            filterMatchingForm: filterMatchingGroupData.filterMatchingForm,
            categoryFormGroupData: mergedPublishedGroupData.categoryFormGroupData,
            subModuleType: subModuleType || FORM_INITIATOR,
        });
        if (!matchingFormList.filterMatchingForm) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        formList = matchingFormList.filterMatchingForm ? matchingFormList.filterMatchingForm : [];
        if (formList.length) {
            formCount = await formSubmittedCount({
                filterMatchingForm: formList,
            });
        }
        //form type based filter
        formList = formList.filter((filterFormElement) => {
            if (formType === ARCHIVE) {
                return filterFormElement.archive === true;
            }
            return filterFormElement.archive === false;
        });
        formList.totalCount = formList.length;
        //sort form
        formList = formList.sort((a, b) => {
            return b.updatedAt - a.updatedAt;
        });
        //pagination
        formList = formList.slice(pagination.skip, pagination.skip + pagination.limit);

        if (formList && formList.length) {
            const initiatorIds = formList.map((formIdElement) => {
                return convertToMongoObjectId(formIdElement._id);
            });
            formAttachmentData = await getAttachmentDocument({
                initiatorIds,
            });
            //add the attachment key
            const matchingFormAttachmentData = await matchingFormAttachment({
                formInitiatorList: formList,
                formAttachmentData,
            });
            formList = matchingFormAttachmentData
                ? matchingFormAttachmentData.formInitiatorList
                : formList;
        }
        //incorporate section
        const incorporateData = await incorporateSectionList({ formList });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                filterMatchingFormData: incorporateData.formList,
                submittedCount: formCount ? formCount.submittedCount : 0,
                archiveCount: formCount ? formCount.archiveCount : 0,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//create new form
exports.createNewForm = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            categoryId,
            categoryFormId,
            categoryFormCourseId,
            assignedInstitutionId,
            institutionName,
            programId,
            programName,
            curriculumId,
            curriculumName,
            year,
            courseId,
            courseName,
            courseCode,
            categoryFormGroupIds,
            selectedInstitutionForm,
            mergeStatus,
            selectedGroupName,
            formName,
            categoryName,
            level,
            term,
            attemptTypeName,
        } = body;
        const formBulkWrite = [];
        const newForm = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryId: convertToMongoObjectId(categoryId),
            categoryFormId: convertToMongoObjectId(categoryFormId),
            categoryFormCourseId: convertToMongoObjectId(categoryFormCourseId),
            categoryName,
            ...(term && { term }),
            ...(attemptTypeName && { attemptTypeName }),
            ...(assignedInstitutionId && {
                assignedInstitutionId: convertToMongoObjectId(assignedInstitutionId),
            }),
            ...(institutionName && { institutionName }),
            ...(programId && {
                programId: convertToMongoObjectId(programId),
            }),
            ...(programName && { programName }),
            ...(curriculumId && {
                curriculumId: convertToMongoObjectId(curriculumId),
            }),
            ...(curriculumName && { curriculumName }),
            ...(year && { year }),
            ...(courseId && {
                courseId: convertToMongoObjectId(courseId),
            }),
            ...(courseName && { courseName }),
            ...(courseCode && { courseCode }),
            ...(selectedGroupName && { selectedGroupName }),
            ...(formName && { formName }),
            ...(_user_id && {
                createdUserIds: [{ userId: convertToMongoObjectId(_user_id) }],
            }),
            level,
            submissionDate: new Date(),
            categoryFormGroupIds,
            mergeStatus,
        };
        const createdFormIds = [];
        const selectedInstitution = [];
        selectedInstitutionForm.forEach(
            ({ categoryFormGroupId, startMonth, endMonth, formCalenderIds }) => {
                const formId = convertToMongoObjectId();
                createdFormIds.push(formId);
                formBulkWrite.push({
                    _id: formId,
                    categoryFormGroupId: convertToMongoObjectId(categoryFormGroupId),
                    startMonth,
                    endMonth,
                    mergedFormId: [],
                    formCalenderIds,
                    ...newForm,
                });
                selectedInstitution.push({
                    categoryFormGroupId,
                    formCalenderIds: formCalenderIds.map((calenderElement) => {
                        return {
                            ...calenderElement,
                            countIncrease: true,
                            isDeleted: false,
                        };
                    }),
                });
            },
        );
        //merge form ids
        if (mergeStatus) {
            formBulkWrite.forEach((formElement, index) => {
                formElement.mergedFormId = [
                    ...createdFormIds.slice(0, index),
                    ...createdFormIds.slice(index + 1),
                ].map((formIdElement) => ({ formInitiatorId: formIdElement }));
            });
        }
        const createNewFormData = await qapcFormInitiatorSchema.insertMany(formBulkWrite);
        // update the created form institution calender and minimum count
        if (selectedInstitution.length) {
            const selectedInstitutionData = await increaseMinimumCount({
                selectedInstitution,
            });
            if (selectedInstitutionData.updateMinimumCount.length) {
                updateCreateDocument({
                    updateMinimumCount: selectedInstitutionData.updateMinimumCount,
                });
            }
        }
        if (!createNewFormData) {
            return { statusCode: 400, message: 'FORM_NOT_CREATED' };
        }
        return {
            statusCode: 200,
            message: 'FORM_CREATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateCreateNewForm = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            categoryId,
            categoryFormId,
            categoryFormCourseId,
            assignedInstitutionId,
            institutionName,
            programId,
            programName,
            curriculumId,
            curriculumName,
            year,
            courseId,
            courseName,
            courseCode,
            submissionStatus,
            status,
            mergeStatus,
            selectedInstitutionForm,
            selectedGroupName,
            formName,
            categoryName,
            level,
            term,
            attemptTypeName,
        } = body;
        const formBulkWrite = [];
        const formBulkUpdate = [];
        const newForm = {
            _institution_id: convertToMongoObjectId(_institution_id),
            ...(categoryName && { categoryName }),
            ...(categoryId && {
                categoryId: convertToMongoObjectId(categoryId),
            }),
            ...(term && { term }),
            ...(attemptTypeName && { attemptTypeName }),
            ...(categoryFormId && {
                categoryFormId: convertToMongoObjectId(categoryFormId),
            }),
            ...(categoryFormCourseId && {
                categoryFormCourseId: convertToMongoObjectId(categoryFormCourseId),
            }),
            ...(assignedInstitutionId && {
                assignedInstitutionId: convertToMongoObjectId(assignedInstitutionId),
            }),
            ...(institutionName && { institutionName }),
            ...(programId && {
                programId: convertToMongoObjectId(programId),
            }),
            ...(programName && { programName }),
            ...(curriculumId && {
                curriculumId: convertToMongoObjectId(curriculumId),
            }),
            ...(curriculumName && { curriculumName }),
            ...(year && { year }),
            ...(courseId && {
                courseId: convertToMongoObjectId(courseId),
            }),
            ...(courseName && { courseName }),
            ...(courseCode && { courseCode }),
            ...(submissionStatus && { submissionStatus }),
            ...(status && { status }),
            ...(selectedGroupName && { selectedGroupName }),
            ...(formName && { formName }),
            ...(level && { level }),
            submissionDate: new Date(),
            ...(typeof mergeStatus === 'boolean' && { mergeStatus }),
        };
        const createdFormIds = [];
        const removedFomInitiatorIds = [];
        const existingFormInitiatorData = await getCreatedFormList({ selectedInstitutionForm });
        const formInitiatorData = await updateFormData({
            existingFormInitiatorData,
            selectedInstitutionForm,
            userId: _user_id,
        });
        if (formInitiatorData.updatedFormData && formInitiatorData.updatedFormData.length) {
            formInitiatorData.updatedFormData.forEach((selectedFormElement) => {
                const {
                    _id,
                    startMonth,
                    endMonth,
                    isDeleted,
                    categoryFormGroupId,
                    formCalenderIds,
                    createdUserIds,
                } = selectedFormElement;
                if (_id) {
                    if (!isDeleted) {
                        createdFormIds.push(convertToMongoObjectId(_id));
                    }
                    if (isDeleted) {
                        removedFomInitiatorIds.push(convertToMongoObjectId(_id));
                    }
                    filter = {
                        _id: convertToMongoObjectId(_id),
                    };
                    update = {
                        $set: {
                            ...(startMonth && { startMonth }),
                            ...(endMonth && { endMonth }),
                            ...(typeof isDeleted === 'boolean' && { isDeleted }),
                            ...(categoryFormGroupId && { categoryFormGroupId }),
                            ...(formCalenderIds && { formCalenderIds }),
                            ...(createdUserIds && { createdUserIds }),
                            ...newForm,
                        },
                    };
                    formBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    const formId = convertToMongoObjectId();
                    createdFormIds.push(formId);
                    formBulkWrite.push({
                        _id: formId,
                        categoryFormGroupId: convertToMongoObjectId(categoryFormGroupId),
                        startMonth,
                        endMonth,
                        mergedFormId: [],
                        ...newForm,
                    });
                }
            });
        }
        if (removedFomInitiatorIds?.length) {
            removedIncorporateSection(removedFomInitiatorIds);
        }
        if (mergeStatus) {
            formBulkUpdate.forEach(({ updateOne }, index) => {
                const mergedFormIds = [
                    ...createdFormIds.slice(0, index),
                    ...createdFormIds.slice(index + 1),
                ].map((formIdElement) => ({
                    formInitiatorId: formIdElement,
                    _id: convertToMongoObjectId(),
                }));
                updateOne.update.$set.mergedFormId = mergedFormIds;
            });
            formBulkWrite.forEach((formElement, index) => {
                formElement.mergedFormId = [
                    ...createdFormIds.slice(0, index),
                    ...createdFormIds.slice(index + 1),
                ].map((formIdElement) => ({ formInitiatorId: formIdElement }));
            });
        }
        if (formBulkWrite.length) {
            await qapcFormInitiatorSchema.insertMany(formBulkWrite);
        }
        if (formBulkUpdate.length) {
            await qapcFormInitiatorSchema.bulkWrite(formBulkUpdate);
        }
        const selectedInstitution = formInitiatorData.updatedFormData;

        if (selectedInstitution && selectedInstitution.length) {
            const selectedInstitutionData = await increaseMinimumCount({
                selectedInstitution,
            });
            if (selectedInstitutionData.updateMinimumCount.length) {
                updateCreateDocument({
                    updateMinimumCount: selectedInstitutionData.updateMinimumCount,
                });
            }
        }
        return {
            statusCode: 200,
            message: 'UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.singleFormList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorId } = query;
        const formInitiatorData = await qapcFormInitiatorSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(formInitiatorId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    categoryId: 1,
                    categoryName: 1,
                    categoryFormId: 1,
                    formName: 1,
                    categoryFormCourseId: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    selectedGroupName: 1,
                    categoryFormGroupId: 1,
                    'mergedFormId.formInitiatorId': 1,
                    'formCalenderIds.institutionCalenderId': 1,
                    'formCalenderIds.isDeleted': 1,
                    formAttachment: 1,
                    level: 1,
                },
            )
            //remove the populate of categoryFormId
            .populate({
                path: 'categoryFormId',
                select: {
                    categoryFormType: 1,
                    formType: 1,
                    'approvalLevel.turnAroundTime': 1,
                    'approvalLevel.name': 1,
                    'approvalLevel.category': 1,
                    'approvalLevel.specificSections': 1,
                },
            })
            .populate({
                path: 'categoryFormGroupId',
                select: { term: 1, attemptTypeName: 1, academicYear: 1, groupName: 1, group: 1 },
            })
            .populate({
                path: 'formCalenderIds.institutionCalenderId',
                select: { calendar_name: 1 },
            })
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formInitiatorData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formAttachment = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            formInitiatorIds,
            formEvidenceAttachment,
            sectionAttachments,
            formGuideResourcesId,
            tags,
            categoryFormId,
            displayCapture,
            formTemplate,
            incorporateFrom,
            incorporateFromReason,
            pdfAttachment,
        } = body;
        const formBulkWrite = [];
        const formBulkUpdate = [];
        let filter;
        let update;
        if (formGuideResourcesId) {
            filter = {
                _id: convertToMongoObjectId(formGuideResourcesId),
                _institution_id: convertToMongoObjectId(_institution_id),
            };
            update = {
                $set: {
                    ...(formEvidenceAttachment && { formEvidenceAttachment }),
                    ...(sectionAttachments && { sectionAttachments }),
                },
                ...(categoryFormId && { categoryFormId: convertToMongoObjectId(categoryFormId) }),
                ...(formInitiatorIds && {
                    $addToSet: {
                        $each: { formInitiatorIds },
                    },
                }),
                ...(incorporateFromReason && { incorporateFromReason }),
                ...(formTemplate && { formTemplate }),
                ...(displayCapture && { displayCapture }),
                ...(tags && { tags }),
                ...(incorporateFrom && { incorporateFrom }),
                ...(pdfAttachment && { pdfAttachment }),
            };
            formBulkUpdate.push({
                updateOne: {
                    filter,
                    update,
                    upsert: true,
                },
            });
        } else {
            formBulkWrite.push({
                _institution_id: convertToMongoObjectId(_institution_id),
                formInitiatorIds,
                formEvidenceAttachment,
                sectionAttachments,
                tags,
                displayCapture,
                formTemplate,
                categoryFormId: convertToMongoObjectId(categoryFormId),
                incorporateFrom,
                ...(pdfAttachment && { pdfAttachment }),
            });
        }
        if (formBulkWrite.length) {
            await qapcGuideResourceSchema.insertMany(formBulkWrite);
        }
        if (formBulkUpdate.length) {
            await qapcGuideResourceSchema.bulkWrite(formBulkUpdate);
        }
        //update form Attachment key
        if (formInitiatorIds.length) {
            await qapcFormInitiatorSchema.updateMany(
                {
                    _id: formInitiatorIds.map((formElement) => convertToMongoObjectId(formElement)),
                },
                {
                    $set: {
                        formAttachment: true,
                    },
                },
            );
        }
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getFormAttachment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorId } = query;
        const formAttachmentData = await qapcGuideResourceSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    formInitiatorIds: { $in: convertToMongoObjectId(formInitiatorId) },
                },
                {
                    formEvidenceAttachment: 1,
                    sectionAttachments: 1,
                    tags: 1,
                    displayCapture: 1,
                    categoryFormId: 1,
                    formTemplate: 1,
                    incorporateFromReason: 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                },
            )
            .populate({
                path: 'categoryFormId',
                select: { 'attachments.url': 1, 'attachments.name': 1 },
            })
            .lean();
        if (!formAttachmentData) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formAttachmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateFormStatus = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorIds, status, isDeleted, archive, submissionStatus } = body;
        if (typeof isDeleted === 'boolean' && isDeleted) {
            await updateCreatedDocument({ formInitiatorIds });
            await deleteIncorporateSection({ formInitiatorIds });
            await deleteGuideResources({ formInitiatorIds });
        }
        let approverList;
        let approverDetails = {};
        let levelStartTime;
        let resubmission = false;
        if (submissionStatus === RESUBMISSION) {
            const qapcFormInitiatorData = await qapcFormInitiatorSchema
                .findOne(
                    {
                        _id: {
                            $in: formInitiatorIds.map((formIdElement) =>
                                convertToMongoObjectId(formIdElement),
                            ),
                        },
                    },
                    {
                        approverList: 1,
                        approverDetails: 1,
                        resubmissionLog: 1,
                        levelStartTime: 1,
                    },
                )
                .lean();
            //remove the last level
            qapcFormInitiatorData?.levelStartTime?.pop();
            levelStartTime = qapcFormInitiatorData?.levelStartTime;
            const resubmitUser = qapcFormInitiatorData?.resubmissionLog?.pop();
            if (resubmitUser) {
                resubmission = true;
                approverList = updateRevokeApproverList({
                    approverList: qapcFormInitiatorData?.approverList || [],
                    resubmitUser,
                });
                approverDetails = {
                    levelStatus: PENDING,
                    currentLevel: qapcFormInitiatorData?.approverDetails?.currentLevel,
                    startDate: new Date(),
                };
                levelStartTime.push({
                    level: qapcFormInitiatorData?.approverDetails?.currentLevel,
                    startDate: new Date(),
                });
            }
        }
        await qapcFormInitiatorSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: {
                    $in: formInitiatorIds.map((formIdElement) =>
                        convertToMongoObjectId(formIdElement),
                    ),
                },
            },
            {
                $set: {
                    ...(submissionStatus && {
                        submissionStatus,
                        submissionDate: new Date(),
                    }),
                    ...(submissionStatus &&
                        !resubmission && {
                            levelStartTime: [
                                {
                                    level: 1,
                                    startDate: new Date(),
                                },
                            ],
                        }),
                    ...(status && { status }),
                    ...(typeof archive === 'boolean' && { archive }),
                    ...(typeof isDeleted === 'boolean' && { isDeleted }),
                    ...(resubmission && {
                        approverDetails,
                        approverList,
                        levelStartTime,
                    }),
                },
            },
        );
        return {
            statusCode: 200,
            message: 'SAVED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//form setting tags
exports.settingTag = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormCourseId, formLevel } = query;
        let configureSettingTag = await formSettingCourseSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(categoryFormCourseId),
                },
                {
                    tags: 1,
                    categoryId: 1,
                },
            )
            .populate({ path: 'categoryId', select: { level: 1 } })
            .lean();
        const settingTagData = await qapcSettingTagSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    level: formLevel,
                    ...(configureSettingTag &&
                        configureSettingTag.tags && {
                            _id: {
                                $nin: configureSettingTag.tags.map((tagElement) =>
                                    convertToMongoObjectId(tagElement._id),
                                ),
                            },
                        }),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                    isDefault: 1,
                    subTag: 1,
                },
            )
            .lean();
        configureSettingTag = configureSettingTag ? configureSettingTag.tags : [];
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { configureSettingTag, settingTagData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.referenceDocumentAcademicYear = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { level } = query;
        const formInitiatorCalenderList = await qapcFormInitiatorSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    level,
                    status: { $in: [APPROVED, PUBLISHED] },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'formCalenderIds.institutionCalenderId': 1,
                    'formCalenderIds.isDeleted': 1,
                    categoryName: 1,
                    categoryId: 1,
                    formName: 1,
                    categoryFormId: 1,
                    mergeStatus: 1,
                    'mergedFormId.formInitiatorId': 1,
                },
            )
            .populate({
                path: 'formCalenderIds.institutionCalenderId',
                select: { calendar_name: 1 },
            })
            .lean();
        if (!formInitiatorCalenderList?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        const categoryFormData = await categoryDocumentStructure({ formInitiatorCalenderList });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: categoryFormData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.searchReferenceDocument = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatedIds, searchKey } = query;
        const formInitiatorData = await qapcFormInitiatorSchema
            .find(
                {
                    _id: {
                        $in: formInitiatedIds.map((formInitiatorElement) =>
                            convertToMongoObjectId(formInitiatorElement),
                        ),
                    },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(searchKey && {
                        $or: [
                            { curriculumName: searchKeyFunction({ searchKey }) },
                            { courseName: searchKeyFunction({ searchKey }) },
                            { institutionName: searchKeyFunction({ searchKey }) },
                        ],
                    }),
                },
                {
                    'mergedFormId.formInitiatorId': 1,
                    selectedGroupName: 1,
                    courseName: 1,
                    programName: 1,
                    institutionName: 1,
                    formName: 1,
                    categoryName: 1,
                    mergeStatus: 1,
                },
            )
            .lean();
        let filterMatchingGroupData = filterMatchingGroup({ formInitiatorData });
        filterMatchingGroupData = filterMatchingGroupData.filterMatchingForm.map(
            ({ mergedFormId, mergeStatus, ...rest }) => rest,
        );
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: filterMatchingGroupData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.conclusionPhase = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatedId, categoryFormId } = query;
        let formSettingData = await formSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(categoryFormId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    formName: 1,
                    'approvalLevel.turnAroundTime': 1,
                    'approvalLevel.name': 1,
                    'approvalLevel.category': 1,
                },
            )
            .lean();
        let formInitiatedTags = await qapcGuideResourceSchema
            .findOne(
                {
                    formInitiatorIds: convertToMongoObjectId(formInitiatedId),
                },
                {
                    'tags.name': 1,
                    'tags.subTag': 1,
                },
            )
            .lean();
        formInitiatedTags = formInitiatedTags ? formInitiatedTags.tags : [];
        formSettingData = formSettingData ? formSettingData.approvalLevel : [];
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { formSettingData, formInitiatedTags },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.referenceFormAttachment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorId } = query;
        const formAttachmentData = await qapcGuideResourceSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    formInitiatorIds: { $in: convertToMongoObjectId(formInitiatorId) },
                },
                {
                    sectionAttachments: 1,
                    formTemplate: 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                    categoryFormId: 1,
                },
            )
            .populate({
                path: 'categoryFormId',
                select: { categoryFormType: 1, formType: 1, formName: 1 },
            })
            .lean();
        if (!formAttachmentData) {
            return { statusCode: 204, message: 'NO_DATA_FOUND', data: [] }; // in frontend we are redirecting component in success callback.
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formAttachmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCategoryAndForm = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const qapcFormCategoryData = await qapcFormCategorySchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    categoryName: 1,
                    categoryFor: 1,
                    level: 1,
                    'actions.attemptType': 1,
                    'actions.academicTerms': 1,
                },
            )
            .lean();
        const formSettingData = await formSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: {
                        $in: qapcFormCategoryData.map((categoryElement) =>
                            convertToMongoObjectId(categoryElement._id),
                        ),
                    },
                    status: PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                    archive: false,
                },
                {
                    categoryId: 1,
                    formName: 1,
                    categoryFormType: 1,
                    formType: 1,
                },
            )
            .lean();
        return categoryFormList({ qapcFormCategoryData, formSettingData });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId, categoryFormId, term, attemptTypeName } = query;
        const formQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryId: convertToMongoObjectId(categoryId),
            categoryFormId: convertToMongoObjectId(categoryFormId),
            status: PUBLISHED,
            isActive: true,
            isDeleted: false,
        };
        const formCourseData = await formSettingCourseSchema
            .find(
                { ...formQuery, isConfigure: true, isEnable: true },
                {
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    programId: 1,
                    programName: 1,
                    courseId: 1,
                    courseName: 1,
                    year: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    sharedWithOthers: 1,
                    sharedFormOthers: 1,
                    courseType: 1,
                },
            )
            .lean();
        if (term || attemptTypeName) {
            const formGroupData = await filterTeamAndAttemptType({
                formQuery,
                term,
                attemptTypeName,
            });
            return uniqueFormCourseData({ formGroupData, formCourseData });
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formCourseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getIncorporateSection = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorIds } = query;
        const incorporateSectionData = await qapcIncorporateSectionSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    formInitiatorIds: {
                        $in: formInitiatorIds.map((initiatorElement) =>
                            convertToMongoObjectId(initiatorElement),
                        ),
                    },
                    isDeleted: false,
                },
                {
                    sectionName: 1,
                    sectionId: 1,
                    categoryId: 1,
                    categoryName: 1,
                    categoryFor: 1,
                    categoryFormId: 1,
                    formName: 1,
                    categoryFormCourseIds: 1,
                    categoryFormGroupIds: 1,
                    selectedProgram: 1,
                    selectedInstitution: 1,
                    institutionCalenderId: 1,
                    attemptTypeName: 1,
                    incorporateTo: 1,
                    term: 1,
                    level: 1,
                },
            )
            .populate({
                path: 'categoryId',
                select: { 'actions.academicTerms': 1, 'actions.attemptType': 1 },
            })
            .populate({ path: 'institutionCalenderId', select: { calendar_name: 1 } })
            .sort({ updatedAt: -1 })
            .lean();
        if (!incorporateSectionData.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: incorporateSectionData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createIncorporate = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { incorporateSections } = body;
        const incorporateBulkWrite = [];
        const incorporateBulkUpdate = [];
        let filter;
        let update;
        incorporateSections.forEach((sectionElement) => {
            const {
                incorporateId,
                formInitiatorIds,
                sectionName,
                sectionId,
                categoryId,
                categoryName,
                categoryFor,
                categoryFormId,
                formName,
                categoryFormCourseIds,
                categoryFormGroupIds,
                selectedProgram,
                selectedInstitution,
                institutionCalenderId,
                isDeleted,
                term,
                attemptTypeName,
                incorporateTo,
                level,
                createdCalenderId,
                createdFormName,
                sectionTemplate,
            } = sectionElement;
            if (incorporateId) {
                filter = {
                    _id: convertToMongoObjectId(incorporateId),
                };
                update = {
                    $set: {
                        ...(formInitiatorIds && { formInitiatorIds }),
                        ...(sectionName && { sectionName }),
                        ...(sectionTemplate && { sectionTemplate }),
                        ...(sectionId && { sectionId }),
                        ...(categoryId && { categoryId: convertToMongoObjectId(categoryId) }),
                        ...(categoryName && { categoryName }),
                        ...(categoryFor && { categoryFor }),
                        ...(categoryFormId && {
                            categoryFormId: convertToMongoObjectId(categoryFormId),
                        }),
                        ...(createdCalenderId && {
                            createdCalenderId: convertToMongoObjectId(createdCalenderId),
                        }),
                        ...(createdFormName && { createdFormName }),
                        ...(formName && { formName }),
                        ...(categoryFormCourseIds && { categoryFormCourseIds }),
                        ...(categoryFormGroupIds && { categoryFormGroupIds }),
                        ...(selectedProgram && { selectedProgram }),
                        ...(selectedInstitution && { selectedInstitution }),
                        ...(term && { term }),
                        ...(attemptTypeName && { attemptTypeName }),
                        ...(institutionCalenderId && { institutionCalenderId }),
                        ...(incorporateTo && { incorporateTo }),
                        ...(typeof isDeleted === 'boolean' && { isDeleted }),
                        ...(level && { level }),
                    },
                };
                incorporateBulkUpdate.push({
                    updateOne: {
                        filter,
                        update,
                        upsert: true,
                    },
                });
            } else {
                incorporateBulkWrite.push({
                    _institution_id: convertToMongoObjectId(_institution_id),
                    formInitiatorIds,
                    sectionId: convertToMongoObjectId(sectionId),
                    institutionCalenderId: convertToMongoObjectId(institutionCalenderId),
                    sectionName,
                    categoryId: convertToMongoObjectId(categoryId),
                    categoryName,
                    categoryFor,
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    formName,
                    categoryFormCourseIds,
                    incorporateTo,
                    ...(createdCalenderId && {
                        createdCalenderId: convertToMongoObjectId(createdCalenderId),
                    }),
                    ...(sectionTemplate && { sectionTemplate }),
                    ...(createdFormName && { createdFormName }),
                    ...(term && { term }),
                    ...(attemptTypeName && { attemptTypeName }),
                    ...(categoryFormGroupIds && { categoryFormGroupIds }),
                    ...(selectedProgram && { selectedProgram }),
                    ...(selectedInstitution && { selectedInstitution }),
                    ...(level && { level }),
                });
            }
        });
        if (incorporateBulkWrite.length) {
            await qapcIncorporateSectionSchema.insertMany(incorporateBulkWrite);
        }
        if (incorporateBulkUpdate.length) {
            await qapcIncorporateSectionSchema.bulkWrite(incorporateBulkUpdate);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTermAndAttempt = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId, categoryFormId } = query;
        const formCourseGroupData = await formCourseGroupSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(categoryId),
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    status: PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    term: 1,
                    attemptTypeName: 1,
                },
            )
            .lean();
        if (!formCourseGroupData.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return termAndAttemptList({ formCourseGroupData });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getIncorporate = async ({ query = {} }) => {
    try {
        const { categoryFormCourseId, categoryFormGroupId, formInitiatedId } = query;
        const formGuideResourcesData = await qapcGuideResourceSchema
            .find(
                {
                    formInitiatorIds: convertToMongoObjectId(formInitiatedId),
                },
                {
                    'incorporateFrom.incorporateId': 1,
                    'incorporateFrom.isLike': 1,
                    formInitiatorIds: 1,
                },
            )
            .lean();
        const formList = [
            {
                categoryFormCourseId,
                categoryFormGroupId,
                formGuideResource: formGuideResourcesData?.length ? formGuideResourcesData : [],
                _id: formInitiatedId,
            },
        ];
        const incorporateData = await incorporateSectionList({ formList });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                incorporateFrom: incorporateData.formList[0].incorporateFrom,
                incorporateWith: incorporateData.formList[0].incorporateWith,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTodoMissed = async ({ headers = {}, query = {} }) => {
    try {
        const { _user_id, role_id } = headers;
        const { institutionCalenderId, subModuleType } = query;
        const userFormList = await userFromInitiatedList({
            userId: _user_id,
            roleId: role_id,
            subModuleType,
            isTodoMissed: true,
        });
        const toDoList = [];
        const missedList = [];
        let uniqueCategoryIds = [];
        if (!userFormList?.categoryFormGroupData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        userFormList?.categoryFormGroupData.forEach(
            ({
                RPSelectedCalender,
                startMonth,
                endMonth,
                _id,
                categoryFormCourseId,
                categoryId,
                term,
                attemptTypeName,
                academicYear,
                groupName,
                minimum,
                createdDocument,
            }) => {
                const isMatchingCalenderId = String(RPSelectedCalender).includes(
                    String(institutionCalenderId),
                );
                if (isMatchingCalenderId) {
                    const currentDate = new Date();
                    const startDate = new Date(currentDate.getFullYear(), startMonth - 1, 1);
                    const endDate = new Date(currentDate.getFullYear(), endMonth, 0);
                    if (startMonth >= endMonth) {
                        endDate.setFullYear(endDate.getFullYear() + 1);
                    }
                    const formatDate = (referenceDate) => {
                        return `${referenceDate.getFullYear()}-${String(
                            referenceDate.getMonth() + 1,
                        ).padStart(2, '0')}-${String(referenceDate.getDate()).padStart(2, '0')}`;
                    };
                    const isCurrentDateWithinRange =
                        (formatDate(currentDate) >= formatDate(startDate) &&
                            formatDate(currentDate) <= formatDate(endDate)) ||
                        formatDate(currentDate) < formatDate(endDate);
                    const courseData = userFormList?.categoryFormCourseData.find(
                        (courseElement) =>
                            String(courseElement._id) === String(categoryFormCourseId),
                    );
                    const actions = courseData?.actions || {};
                    const existingCreatedForm = createdDocument.find(
                        (createdElement) =>
                            String(createdElement.institutionCalenderId) ===
                            String(institutionCalenderId),
                    );
                    const createdDocumentCount = existingCreatedForm?.minimum || 0;
                    const pendingFormData = {
                        categoryFormCourseId,
                        categoryFormGroupId: _id,
                        term,
                        attemptTypeName,
                        groupName,
                        academicYear,
                        categoryId,
                        actions,
                        year: courseData?.year,
                        courseName: courseData?.courseName,
                        programName: courseData?.programName,
                        institutionName: courseData?.institutionName,
                        curriculumName: courseData?.curriculumName,
                        remainingCount: minimum - createdDocumentCount,
                    };
                    const listToPush = isCurrentDateWithinRange ? toDoList : missedList;
                    if (minimum !== createdDocumentCount) {
                        uniqueCategoryIds.push(String(categoryId));
                        listToPush.push(pendingFormData);
                    }
                }
            },
        );
        let categoryData;
        if (uniqueCategoryIds.length) {
            uniqueCategoryIds = [...new Set(uniqueCategoryIds)];
            categoryData = await qapcFormCategorySchema
                .find(
                    {
                        _id: {
                            $in: uniqueCategoryIds.map((categoryElement) =>
                                convertToMongoObjectId(categoryElement),
                            ),
                        },
                    },
                    { _id: 1, categoryFor: 1, categoryName: 1, level: 1 },
                )
                .lean();
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { toDoList, missedList, categoryData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCategoryList = async ({ headers }) => {
    try {
        const { _institution_id } = headers;
        const categoryList = await qapcFormCategorySchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { _id: 1, categoryName: 1 },
            )
            .lean();
        if (!categoryList.length) return { startCode: 404, message: 'NO_DATA_FOUND' };
        return { startCode: 200, message: 'DATA_RETRIEVED', data: categoryList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getFormViewAttachment = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatorId } = query;
        const formAttachmentData = await qapcGuideResourceSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    formInitiatorIds: { $in: convertToMongoObjectId(formInitiatorId) },
                },
                {
                    formEvidenceAttachment: 1,
                    'sectionAttachments.sectionName': 1,
                    'sectionAttachments.description': 1,
                    'sectionAttachments.evidenceAttachment': 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                },
            )
            .lean();
        if (!formAttachmentData) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formAttachmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.searchDocumentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formInitiatedIds, searchKey, formLevel } = query;
        const formInitiatorData = await qapcFormInitiatorSchema
            .find(
                {
                    _id: {
                        $in: formInitiatedIds.map((formInitiatorElement) =>
                            convertToMongoObjectId(formInitiatorElement),
                        ),
                    },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(searchKey && {
                        $or: [
                            { programName: searchKeyFunction({ searchKey }) },
                            { courseName: searchKeyFunction({ searchKey }) },
                            { institutionName: searchKeyFunction({ searchKey }) },
                        ],
                    }),
                },
                {
                    courseName: 1,
                    curriculumName: 1,
                    institutionName: 1,
                },
            )
            .lean();
        if (!formInitiatorData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        let searchList = new Set();
        switch (formLevel) {
            case COURSE:
                formInitiatorData.forEach((initiatedElement) => {
                    searchList.add(initiatedElement.courseName);
                });
                break;
            case PROGRAM:
                formInitiatorData.forEach((initiatedElement) => {
                    searchList.add(initiatedElement.curriculumName);
                });
                break;
            case INSTITUTION:
                formInitiatorData.forEach((initiatedElement) => {
                    searchList.add(initiatedElement.institutionName);
                });
                break;
            default:
                break;
        }
        searchList = Array.from(searchList);
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: searchList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//created duplicate form
exports.createDuplicateForm = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormGroupId, formInitiatedId, institutionCalenderId } = query;
        const isFormOccurrence = await getFormOccurrence({
            categoryFormGroupId,
            institutionCalenderId,
        });
        if (!isFormOccurrence) {
            return { statusCode: 200, message: 'OCCURRENCE_EXCEEDED_IN_THIS_FORM' };
        }
        const formGuideResourcesData = await getFormGuideResourcesData({ formInitiatedId });
        return newOccurrenceFormCreate({
            formInitiatedId,
            institutionCalenderId,
            formGuideResourcesData,
            _institution_id,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.toDoCreatedFormList = async ({ query = {} }) => {
    try {
        const { categoryFormGroupId } = query;
        const createdFormListData = await formCourseGroupSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(categoryFormGroupId),
                },
                {
                    group: 1,
                    categoryFormCourseId: 1,
                    term: 1,
                    attemptTypeName: 1,
                    executionsPer: 1,
                    academicYear: 1,
                    groupName: 1,
                    minimum: 1,
                    startMonth: 1,
                    endMonth: 1,
                    createdDocument: 1,
                    categoryFormId: 1,
                    categoryId: 1,
                },
            )
            .populate({ path: 'categoryId', select: { categoryName: 1, level: 1 } })
            .populate({
                path: 'categoryFormId',
                select: { formName: 1, incorporateMandatory: 1, categoryId: 1 },
            })
            .populate({
                path: 'categoryFormCourseId',
                select: {
                    categoryId: 1,
                    categoryFormId: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    actions: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                },
            });
        return { data: createdFormListData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.publishedFormInitiator = async ({ query = {} }) => {
    try {
        const { institutionCalenderId, formLevel } = query;
        const qapcFormInitiatorData = await qapcFormInitiatorSchema
            .find(
                {
                    level: formLevel,
                    status: { $in: [APPROVED, PUBLISHED] },
                    formCalenderIds: {
                        $elemMatch: {
                            institutionCalenderId: convertToMongoObjectId(institutionCalenderId),
                            isDeleted: false,
                        },
                    },
                },
                {
                    formName: 1,
                    categoryFormId: 1,
                },
            )
            .populate({ path: 'categoryFormId', select: { formType: 1, categoryFormType: 1 } })
            .lean();
        if (!qapcFormInitiatorData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return removeDuplicateForm({ qapcFormInitiatorData });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

exports.selectedFormList = async ({ query = {} }) => {
    try {
        const { formInitiatedIds } = query;
        const formInitiatedData = await qapcFormInitiatorSchema
            .find(
                {
                    _id: {
                        $in: formInitiatedIds.map((initiatedElement) =>
                            convertToMongoObjectId(initiatedElement),
                        ),
                    },
                },
                {
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    categoryFormCourseId: 1,
                },
            )
            .populate({
                path: 'categoryFormCourseId',
                select: { sharedFormOthers: 1, sharedWithOthers: 1 },
            })
            .lean();
        if (!formInitiatedData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formInitiatedData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

exports.formInitiatedAttachments = async ({ query = {} }) => {
    try {
        const { formInitiatorId } = query;
        const formAttachmentData = await qapcGuideResourceSchema
            .findOne(
                {
                    formInitiatorIds: { $in: convertToMongoObjectId(formInitiatorId) },
                },
                {
                    sectionAttachments: 1,
                    formTemplate: 1,
                    'pdfAttachment.url': 1,
                    'pdfAttachment.name': 1,
                },
            )
            .lean();
        if (!formAttachmentData) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: {} };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formAttachmentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
