const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const {
    pushStudentSchema,
    updateCourseSessionSchema,
    getCoursesParamSchema,
    updateClassModeSchema,
    updateAttendanceSchema,
    updateStaffCourseExport,
} = require('./course_session_validate_schema');

const {
    course_session_flow_list,
    document_course_session_flow_list,
    courseSessionFlow,
    courseSessionFlowSlo,
    course,
    getCourseSessionTab,
    getCourseDocuments,
    getSessions,
    getSchedule,
    getScheduleByDate,
    getCourseOnly,
    getTodaySchedule,
    getStudentDocuments,
    updateFeedback,
    getDropdownSchedule,
    getCourseSession,
    getScheduleBySessionOrScheduleId,
    riyadhToUTC,
    getCourseSessionWithSchedule,
    getCourses,
    getSessionsOfCourse,
    userCourses,
    userCourseSessionDetails,
    userCourseRedisReset,
    scheduleDetailsWithSession,
    userCalendars,
    staffCourseExport,
    updateClassMode,
    updateOfflineAttendance,
    getStudentList,
    getSessionListBasedGroups,
    userLevelComprehensiveWarning,
    userLevelComprehensiveAttendance,
    getStaffCourseScheduleStudentGroup,
    getCourseDeliveryGroupAdmin,
} = require('./course_session_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get(
    '/course_session_flow_list/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    course_session_flow_list,
);
route.get(
    '/course/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    course,
);
route.get(
    '/get-courses/:staffId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCoursesParamSchema,
    getCourses,
);
route.get(
    '/getStudentDocuments/:studentId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getStudentDocuments,
);
route.get(
    '/course/:userId/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    course,
);
route.get(
    '/get-course-session-tab/:userId/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourseSessionTab,
);
route.get(
    '/documents/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourseDocuments,
);
route.get(
    '/documents/session/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourseDocuments,
);
route.get(
    '/documents/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourseDocuments,
);
route.get(
    '/get-schedules/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getSessions,
);
route.get(
    '/get-schedule/:scheduleId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getSchedule,
);
route.get(
    '/get-schedule-by-date/:userId/:date',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getScheduleByDate,
);
route.get(
    '/course-session-flow/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    courseSessionFlow,
);
route.get(
    '/get-course-only/:staffId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCourseOnly,
);
route.get(
    '/get-today-schedule/:staffId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getTodaySchedule,
);
route.get(
    '/get-session-schedule/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getDropdownSchedule,
);
route.get(
    '/course-session-flow-slo/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    courseSessionFlowSlo,
);
route.get(
    '/document_course_session_flow_list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    document_course_session_flow_list,
);
route.get('/riyadhToUTC', [userPolicyAuthentication([])], riyadhToUTC);
route.put(
    '/feedback/:studentId/:scheduleId',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    updateCourseSessionSchema,
    updateFeedback,
);

route.get(
    '/getCourseSession',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getCourseSession,
);
route.get(
    '/getSchedules/:sessionOrScheduleId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getScheduleBySessionOrScheduleId,
);
route.get(
    '/getCourseSessiBasedGroupsonWithSchedule/:userId/:courseId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getCourseSessionWithSchedule,
);
route.get(
    '/getSessionsOfCourse/:userId/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getSessionsOfCourse,
);

// User Course List
route.get(
    '/userCourses/:userId',
    userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
        queryName: 'type',
    }),
    userCourses,
);
route.get(
    '/userCourseSessionDetails/:userId/:courseId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    userCourseSessionDetails,
);
route.get(
    '/userCourseRedisReset/:userId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    userCourseRedisReset,
);
route.get(
    '/scheduleDetailsWithSession/:userId',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'userType',
        }),
    ],
    scheduleDetailsWithSession,
);
route.get(
    '/userCalendars/:userId/:userType',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            paramName: 'userType',
        }),
    ],
    userCalendars,
);

// Course Staff Based Schedule Student Attendance Export
route.get(
    '/staffCourseExport',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateStaffCourseExport,
    staffCourseExport,
);
route.post(
    '/updateClassMode',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateClassModeSchema,
    updateClassMode,
);
route.post(
    '/updateOfflineAttendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateAttendanceSchema,
    updateOfflineAttendance,
);
route.post(
    '/courseExport',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getStaffCourseScheduleStudentGroup,
);

//Get Student List For Course
route.get('/getStudentList', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], getStudentList);
route.get(
    '/getSessionListBasedGroups',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getSessionListBasedGroups,
);

// User Level Based Comprehensive Warning
route.get(
    '/userLevelComprehensiveWarning',
    [
        userPolicyAuthentication([defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    userLevelComprehensiveWarning,
);
route.get(
    '/userLevelComprehensiveAttendance',
    [
        userPolicyAuthentication([defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    userLevelComprehensiveAttendance,
);
route.get(
    '/getCourseDeliveryGroupAdmin',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCourseDeliveryGroupAdmin,
);
module.exports = route;
