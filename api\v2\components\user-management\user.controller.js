const constant = require('../../utility/constants');
const userSchema = require('./user.model');
const programInputSchema = require('../program-input/program-input.model');
const institutionSchema = require('../institution/institution.model');
const settingsSchema = require('../setting/setting.model');
const roleAssignSchema = require('../roles-and-permission/role-assign/role-assign.model');
const bcrypt = require('bcrypt');
const { generateAuthTokens, generateAuthTokensForSignUp } = require('../../utility/token.util');
const { getPresignedUrlsForInstitutes } = require('../institution/institution.util');
const moment = require('moment');
const {
    getSignedURL,
    getModel,
    convertToMongoObjectId,
    generateRandomNumber,
    nameFormatter,
} = require('../../utility/common');
const Excel = require('exceljs');
const { user_list_formate } = require('./user.formate');
const { getInstitutionCalendarFromRedis } = require('../../services/redisCache.service');
const util_key = require('../../utility/util_keys');
const {
    validUser,
    getSignedLogoUrl,
    requestMailReplace,
    sendStaffMail,
    sendStaffMailForPasswordReset,
} = require('./user.util');
const { getPaginationValues } = require('../../utility/pagination');
const { SETTINGS, USER, ALL, SELECTED, PENDING } = require('../../utility/constants');
const { sendStaffEmail } = require('../../services/email.service');
const constants = require('../../utility/constants');
const { elasticSearch } = require('../elasticSearch/elasticsSearch.controller');
const { USER_INSTITUTIONS, SERVICES } = require('../../utility/util_keys');
const {
    bulkWrite,
    removeRecords,
    updateRecords,
    getRecords,
} = require('../../services/elasticSearch.service');

const DocumentMissingCheck = async (document, tenantURL) => {
    const usersModel = getModel(tenantURL, USER, userSchema);
    const userProject = {
        _id: 1,
        email: 1,
        _institution_id: 1,
        status: 1,
        name: 1,
        uploadedDocuments: 1,
    };
    const userQuery = {
        isDeleted: false,
        isActive: true,
        _id: convertToMongoObjectId(document.userId),
    };
    const userLists = await usersModel.find(userQuery, userProject).lean();
    if (userLists) {
        const dbUpdateQuery = [];
        for (userData of userLists) {
            let categoryCheck = false;
            let documentCheck = false;

            categoryFound = userData.uploadedDocuments.find(
                (element) => element._category_id.toString() === document._category_id.toString(),
            );
            if (!categoryFound) {
                categoryCheck = true;
                const categoryUpdate = await usersModel.updateOne(
                    { _id: userData._id },
                    { $push: { uploadedDocuments: { _category_id: document._category_id } } },
                );
            }
            if (categoryFound || categoryCheck) {
                let documentFound = false;
                if (categoryFound) {
                    documentFound = categoryFound.document.find(
                        (element) =>
                            element._document_id &&
                            element._document_id.toString() === document._id.toString(),
                    );
                }

                if (!documentFound || categoryCheck) {
                    documentCheck = true;
                    const documentUpdate = await usersModel.findOneAndUpdate(
                        { _id: userData._id },
                        {
                            $push: {
                                'uploadedDocuments.$[categoryId].document': {
                                    _document_id: document._id,
                                    globalMandatory: document.isMandatory,
                                },
                            },
                        },
                        {
                            arrayFilters: [
                                {
                                    'categoryId._category_id': convertToMongoObjectId(
                                        document._category_id,
                                    ),
                                },
                            ],
                        },
                    );
                }
            }

            const documentLists = [];

            for (uploadedDocumentLists of userData.uploadedDocuments) {
                for (documents of uploadedDocumentLists.document) {
                    documentLists.push({
                        _document_id: documents._document_id,
                        url: documents.url,
                    });
                }
            }

            const checkingDocuments = documentLists.find(
                (element) =>
                    element._document_id &&
                    element._document_id.toString() === document._id.toString(),
            );
            if (!categoryCheck && !documentCheck) {
                dbUpdateQuery.push({
                    updateMany: {
                        filter: {
                            _id: convertToMongoObjectId(userData._id),
                            uploadedDocuments: { $ne: null },
                        },
                        update: {
                            $set: {
                                'uploadedDocuments.$[categoryId].document.$[documentId].globalMandatory':
                                    document.isMandatory,
                            },
                        },
                        arrayFilters: [
                            {
                                'categoryId._category_id': convertToMongoObjectId(
                                    document._category_id,
                                ),
                            },
                            {
                                'documentId._document_id': convertToMongoObjectId(document._id),
                            },
                        ],
                    },
                });
            }
        }
        if (dbUpdateQuery.length > 0) {
            await usersModel.bulkWrite(dbUpdateQuery).catch((error) => {
                throw new Error(error);
            });
        }
    }
};

// user login staff and student
const loginService = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const { email, password, user_type } = body;
        const userQuery = { email, isDeleted: false };
        const userProject = {
            _id: 1,
            password: 1,
            status: 1,
            isRegistered: 1,
            user_id: 1,
            user_type: 1,
            isActive: 1,
            mobile: 1,
            verification: 1,
            _institution_id: 1,
        };
        const user = await userModel.findOne(userQuery, userProject).lean();
        if (!user || !(password !== user.password && bcrypt.compareSync(password, user.password))) {
            return {
                statusCode: 200,
                message: 'INCORRECT_EMAIL_OR_PASSWORD',
            };
        }
        if (!user.isActive) {
            return {
                statusCode: 200,
                message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
            };
        }
        if (user_type && user.user_type !== user_type) {
            return {
                statusCode: 200,
                message: 'YOU_ARE_NOT_ASSOCIATED_FOR_THIS_APP',
            };
        }
        const isAdmin = email === '<EMAIL>';
        const defaultResponse = {
            verification: user.verification,
            status: user.status,
            signup: !!(
                user.verification &&
                user.verification.profile &&
                user.verification.profile &&
                user.verification.profile.basicDetails &&
                user.verification.profile.profileDetails
            ),
            isAdmin,
        };
        if (isAdmin === false) {
            if (user.verification && !user.verification.email && !user.verification.password) {
                return {
                    statusCode: 200,
                    message: 'ONLY_SIGNED_USER_ABLE_TO_ACCESS_PLS_DO_SIGNUP',
                };
            }
            let userDocumentStatus = false;
            let userBiometricStatus = false;
            if (
                user._institution_id &&
                user.verification.profile.basicDetails &&
                user.verification.profile.profileDetails
            ) {
                const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
                const setting = await settingsModel
                    .findOne(
                        {
                            _institution_id: convertToMongoObjectId(user._institution_id),
                        },
                        {
                            'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                            'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.document.isActive': 1,
                        },
                    )
                    .lean();
                if (
                    setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments
                ) {
                    const documentsSetting =
                        setting.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments
                            .map((documentElement) =>
                                documentElement.document
                                    .map((docElement) => docElement.isActive)
                                    .flat(),
                            )
                            .flat();
                    userDocumentStatus = !!documentsSetting.find(
                        (docSetting) => docSetting === true,
                    );
                }
                if (
                    setting.globalConfiguration.staffUserManagement.biometricConfiguration &&
                    setting.globalConfiguration.staffUserManagement.biometricConfiguration.length
                ) {
                    userBiometricStatus =
                        setting.globalConfiguration.staffUserManagement.biometricConfiguration.find(
                            (conElement) => conElement.isActive,
                        ).labelName !== 'Offline verification';
                }
                if (userDocumentStatus) defaultResponse.signup = user.verification.document;
                if (userBiometricStatus)
                    defaultResponse.signup = defaultResponse.signup && user.verification.face;
            }
        }
        const responseData = {
            _id: user._id,
            email,
            _institution_id: user._institution_id,
            user_type: user.user_type,
            tokens: await generateAuthTokens({
                user_id: user.user_id.value ? user.user_id.value : user.user_id,
                _id: user._id,
                type: user.user_type,
            }),
            ...defaultResponse,
            services: SERVICES,
        };
        return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const loginServiceForWeb = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { email, password, user_type } = body;
        const userQuery = { email, isDeleted: false };
        const userProject = {
            _id: 1,
            password: 1,
            status: 1,
            isRegistered: 1,
            user_id: 1,
            email: 1,
            user_type: 1,
            isActive: 1,
            mobile: 1,
            verification: 1,
            _institution_id: 1,
        };
        if (!_institution_id) {
            const userInstitutionSearch = await getRecords({
                indexName: USER_INSTITUTIONS,
                query: {
                    match_phrase: { email },
                },
            });
            const institutionData =
                userInstitutionSearch &&
                userInstitutionSearch.body &&
                userInstitutionSearch.body.hits &&
                userInstitutionSearch.body.hits.hits &&
                userInstitutionSearch.body.hits.hits.length
                    ? userInstitutionSearch.body.hits.hits.map(
                          (userResultElement) => userResultElement._source,
                      )
                    : [];
            let isDefaultInstitute = null;
            if (institutionData.length) {
                isDefaultInstitute = institutionData.find((element) => {
                    return element.isDefault === 'true';
                });
            }
            let responseData = {
                email,
                _institution_id:
                    isDefaultInstitute != undefined ? isDefaultInstitute._institution_id : null,
                isDefault: isDefaultInstitute != undefined,
            };
            let institutes = [];
            if (!isDefaultInstitute) {
                const instituteIds = [];
                institutionData.forEach((data) => {
                    instituteIds.push(data._institution_id);
                });
                const instituteObjectIds = instituteIds.map((i) => convertToMongoObjectId(i));
                institutes = await institutionModel
                    .find(
                        { _id: { $in: instituteObjectIds } },
                        {
                            address: 1,
                            code: 1,
                            type: 1,
                            name: 1,
                            logo: 1,
                        },
                    )
                    .lean();
                institutes = await getSignedLogoUrl(institutes);
                responseData = { ...responseData, institutes };
                return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
            }
            userQuery._institution_id = isDefaultInstitute._institution_id;
            const user = await userModel.findOne(userQuery, userProject).lean();
            if (
                !user ||
                !(password !== user.password && bcrypt.compareSync(password, user.password))
            ) {
                return {
                    statusCode: 200,
                    message: 'INCORRECT_EMAIL_OR_PASSWORD',
                };
            }
            if (!user.isActive) {
                return {
                    statusCode: 200,
                    message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
                };
            }
            if (user_type && user.user_type !== user_type) {
                return {
                    statusCode: 200,
                    message: 'YOU_ARE_NOT_ASSOCIATED_FOR_THIS_APP',
                };
            }
            const isAdmin = email === '<EMAIL>';
            const defaultResponse = {
                verification: user.verification,
                status: user.status,
                signup: !!(
                    user.verification &&
                    user.verification.profile &&
                    user.verification.profile &&
                    user.verification.profile.basicDetails &&
                    user.verification.profile.profileDetails
                ),
                isAdmin,
            };
            if (isAdmin === false) {
                if (user.verification && !user.verification.email && !user.verification.password) {
                    return {
                        statusCode: 200,
                        message: 'ONLY_SIGNED_USER_ABLE_TO_ACCESS_PLS_DO_SIGNUP',
                    };
                }
                let userDocumentStatus = false;
                let userBiometricStatus = false;
                if (
                    user._institution_id &&
                    user.verification.profile.basicDetails &&
                    user.verification.profile.profileDetails
                ) {
                    const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
                    const setting = await settingsModel
                        .findOne(
                            {
                                _institution_id: convertToMongoObjectId(user._institution_id),
                            },
                            {
                                'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                                'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.document.isActive': 1,
                            },
                        )
                        .lean();
                    if (
                        setting.globalConfiguration.staffUserManagement.documentConfiguration
                            .chooseDocuments
                    ) {
                        const documentsSetting =
                            setting.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments
                                .map((documentElement) =>
                                    documentElement.document
                                        .map((docElement) => docElement.isActive)
                                        .flat(),
                                )
                                .flat();
                        userDocumentStatus = !!documentsSetting.find(
                            (docSetting) => docSetting === true,
                        );
                    }
                    if (
                        setting.globalConfiguration.staffUserManagement.biometricConfiguration &&
                        setting.globalConfiguration.staffUserManagement.biometricConfiguration
                            .length
                    ) {
                        userBiometricStatus =
                            setting.globalConfiguration.staffUserManagement.biometricConfiguration.find(
                                (conElement) => conElement.isActive,
                            ).labelName !== 'Offline verification';
                    }
                    // defaultResponse.settings = setting;
                    if (userDocumentStatus) defaultResponse.signup = user.verification.document;
                    if (userBiometricStatus)
                        defaultResponse.signup = defaultResponse.signup && user.verification.face;
                }
            }
            const institute = await institutionModel
                .findById(
                    { _id: convertToMongoObjectId(user._institution_id) },
                    {
                        type: 1,
                        name: 1,
                    },
                )
                .lean();
            responseData = {
                _id: user._id,
                email,
                _institution_id: user._institution_id,
                institute,
                user_type: user.user_type,
                tokens: await generateAuthTokens({
                    user_id: user.user_id.value ? user.user_id.value : user.user_id,
                    _id: user._id,
                    type: user.user_type,
                }),
                ...defaultResponse,
                services: SERVICES,
            };

            responseData = { ...responseData };
            return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
        }
        userQuery._institution_id = _institution_id;
        const user = await userModel.findOne(userQuery, userProject).lean();
        if (!user || !(password !== user.password && bcrypt.compareSync(password, user.password))) {
            return {
                statusCode: 200,
                message: 'INCORRECT_EMAIL_OR_PASSWORD',
            };
        }
        if (!user.isActive) {
            return {
                statusCode: 200,
                message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
            };
        }
        if (user_type && user.user_type !== user_type) {
            return {
                statusCode: 200,
                message: 'YOU_ARE_NOT_ASSOCIATED_FOR_THIS_APP',
            };
        }
        const isAdmin = email === '<EMAIL>';
        const defaultResponse = {
            verification: user.verification,
            status: user.status,
            signup: !!(
                user.verification &&
                user.verification.profile &&
                user.verification.profile &&
                user.verification.profile.basicDetails &&
                user.verification.profile.profileDetails
            ),
            isAdmin,
        };
        if (isAdmin === false) {
            if (user.verification && !user.verification.email && !user.verification.password) {
                return {
                    statusCode: 200,
                    message: 'ONLY_SIGNED_USER_ABLE_TO_ACCESS_PLS_DO_SIGNUP',
                };
            }
            let userDocumentStatus = false;
            let userBiometricStatus = false;
            if (
                user._institution_id &&
                user.verification.profile.basicDetails &&
                user.verification.profile.profileDetails
            ) {
                const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
                const setting = await settingsModel
                    .findOne(
                        {
                            _institution_id: convertToMongoObjectId(user._institution_id),
                        },
                        {
                            'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                            'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.document.isActive': 1,
                        },
                    )
                    .lean();
                if (
                    setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments
                ) {
                    const documentsSetting =
                        setting.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments
                            .map((documentElement) =>
                                documentElement.document
                                    .map((docElement) => docElement.isActive)
                                    .flat(),
                            )
                            .flat();
                    userDocumentStatus = !!documentsSetting.find(
                        (docSetting) => docSetting === true,
                    );
                }
                if (
                    setting.globalConfiguration.staffUserManagement.biometricConfiguration &&
                    setting.globalConfiguration.staffUserManagement.biometricConfiguration.length
                ) {
                    userBiometricStatus =
                        setting.globalConfiguration.staffUserManagement.biometricConfiguration.find(
                            (conElement) => conElement.isActive,
                        ).labelName !== 'Offline verification';
                }
                if (userDocumentStatus) defaultResponse.signup = user.verification.document;
                if (userBiometricStatus)
                    defaultResponse.signup = defaultResponse.signup && user.verification.face;
            }
        }
        const institute = await institutionModel
            .findById(
                { _id: convertToMongoObjectId(_institution_id) },
                {
                    type: 1,
                    name: 1,
                },
            )
            .lean();
        let responseData = {
            _id: user._id,
            email,
            _institution_id,
            institute,
            user_type: user.user_type,
            tokens: await generateAuthTokens({
                user_id: user.user_id.value ? user.user_id.value : user.user_id,
                _id: user._id,
                type: user.user_type,
            }),
            ...defaultResponse,
            services: SERVICES,
        };

        responseData = { ...responseData };
        return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listInstitution = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { email } = params;
        const userInstitutionSearch = await getRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                match_phrase: { email },
            },
        });
        const institutionData =
            userInstitutionSearch &&
            userInstitutionSearch.body &&
            userInstitutionSearch.body.hits &&
            userInstitutionSearch.body.hits.hits &&
            userInstitutionSearch.body.hits.hits.length
                ? userInstitutionSearch.body.hits.hits.map(
                      (userResultElement) => userResultElement._source,
                  )
                : [];
        let isDefaultInstitute = null;
        if (institutionData.length) {
            isDefaultInstitute = institutionData.find((element) => {
                return element.isDefault === 'true';
            });
        }
        let responseData = {
            email,
            _institution_id:
                isDefaultInstitute != undefined ? isDefaultInstitute._institution_id : false,
            isDefault: isDefaultInstitute != undefined,
        };
        let institutes = [];
        const instituteIds = [];
        institutionData.forEach((data) => {
            instituteIds.push(data._institution_id);
        });
        const instituteObjectIds = instituteIds.map((i) => convertToMongoObjectId(i));
        institutes = await institutionModel.find(
            { _id: { $in: instituteObjectIds } },
            {
                address: 1,
                code: 1,
                type: 1,
                name: 1,
                logo: 1,
            },
        );
        institutes = await getSignedLogoUrl(institutes);
        responseData = { ...responseData, institutes };
        return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// user login staff and student Institution Listing
const authUserInstitutions = async ({ body = {}, headers = {} }) => {
    try {
        const { email } = body;
        // const { origin } = headers;
        const userInstitutionSearch = await getRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                match: {
                    email,
                },
            },
            // _source: {
            //     includes: ['email', 'status', '_institution_id', 'subDomain'],
            // },
        });
        const responseData =
            userInstitutionSearch &&
            userInstitutionSearch.body &&
            userInstitutionSearch.body.hits &&
            userInstitutionSearch.body.hits.hits &&
            userInstitutionSearch.body.hits.hits.length
                ? userInstitutionSearch.body.hits.hits.map(
                      (userResultElement) => userResultElement._source,
                  )
                : [];
        return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// user login staff and student
const loggedInService = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        // const programModel = getModel(tenantURL, constant.PROGRAM, programInputSchema);
        const { fcm_token, device_type } = body;
        const { user_id } = headers.payload;
        const userQuery = {
            _id: convertToMongoObjectId(user_id),
            isDeleted: false,
            isActive: true,
        };
        const userProject = {
            name: 1,
            user_id: 1,
            email: 1,
            gender: 1,
            batch: 1,
            createdAt: 1,
            user_type: 1,
            biometric_data: 1,
            employment: 1,
            program: 1,
            academic_allocation: 1,
            ioToken: 1,
            institutionId: 1,
            _institution_id: 1,
            _role_id: 1,
            socketEventId: 1,
            status: 1,
            isRegistered: 1,
        };
        const user = await userModel.findOne(userQuery, userProject).lean();
        if (!user) {
            return { statusCode: 200, message: 'USER_NOT_FOUND' };
        }
        const userUpdateData = {
            device_type,
            last_login_device_type: device_type,
            // socketEventId: {
            //     dashboardEventId: 'dashboard-' + user_id,
            //     activityEventId: 'activity-' + user_id,
            //     chatEventId: 'chat-' + user_id,
            //     sessionEventId: 'session-' + user_id,
            //     courseEventId: 'course-' + user_id,
            // },
        };
        if (!user.socketEventId || (user.socketEventId && !user.socketEventId.dashboardEventId))
            userUpdateData.socketEventId = {
                dashboardEventId: 'dashboard-' + user_id,
                activityEventId: 'activity-' + user_id,
                chatEventId: 'chat-' + user_id,
                sessionEventId: 'session-' + user_id,
                courseEventId: 'course-' + user_id,
            };
        if (fcm_token && fcm_token.length) {
            if (device_type === 'web') {
                userUpdateData.web_fcm_token = fcm_token;
            } else {
                userUpdateData.fcm_token = fcm_token;
            }
        }
        await userModel.updateOne({ _id: user_id }, { $set: userUpdateData });
        const programQuery = {
            isActive: true,
            isDeleted: false,
        };
        const { _id: institutionCalendarId } = await getInstitutionCalendarFromRedis();
        let university = {
            status: false,
            institutionId: user.institutionId ? user.institutionId : user._institution_id,
        };
        if (user._role_id) {
            const roleAssignModel = getModel(tenantURL, constant.ROLE_ASSIGN, roleAssignSchema);
            const roleAssignQuery = {
                _id: convertToMongoObjectId(user._role_id),
                'roles.role_name': 'univ_admin', //as spoke with kabeer statically added
                isDeleted: false,
                isActive: true,
            };

            const roleAssignData = await roleAssignModel
                .findOne(roleAssignQuery, {
                    _id: 1,
                    roles: 1,
                })
                .lean();
            if (roleAssignData) {
                const institutionData = await institutionModel
                    .findOne(
                        {
                            $or: [
                                {
                                    parentInstitute: null, //university flow
                                    isDeleted: false,
                                    isUniversity: true,
                                    isActive: true,
                                },
                                {
                                    parentInstitute: null, // individual college flow
                                    isDeleted: false,
                                    isUniversity: false,
                                    isActive: true,
                                },
                            ],
                        },
                        {
                            _id: 1,
                            parentInstitute: 1,
                            isUniversity: 1,
                        },
                    )
                    .lean();
                if (institutionData) {
                    university = {
                        status: institutionData.isUniversity,
                        institutionId: institutionData._id,
                    };
                }
            }
        }

        const responseData = {
            _id: user._id,
            user_id: user.user_id.value ? user.user_id.value : user.user_id,
            name: user.name,
            email: user.email,
            gender: user.gender,
            enrolledTerm: user.batch,
            enrolledDate: user.createdAt,
            user_type: user.user_type,
            biometric_data:
                user.face && user.face[0] ? await getSignedURL(user.face[0], 'userData') : '',
            institutionCalendar: institutionCalendarId,
            socketEventId: userUpdateData.socketEventId,
            ioToken: user.ioToken || null,
            university,
            verification: user.status === 'completed' || user.status === 'valid',
        };
        // let staffAcademics;
        if (user.user_type === constant.DC_STAFF) {
            responseData.employment = user.employment;
            // staffAcademics = user.academic_allocation.find(
            //     (academicAllocation) =>
            //         academicAllocation &&
            //         academicAllocation.allocation_type &&
            //         academicAllocation.allocation_type === constant.PRIMARY,
            // );
            // if (staffAcademics) {
            //     const {
            //         _program_id: programId,
            //         _department_id: departmentId,
            //         _department_subject_id: departmentSubjectIds,
            //     } = staffAcademics;
            //     programQuery._id = convertToMongoObjectId(programId);
            // }
        } else {
            programQuery.code =
                user.program && user.program.program_no ? user.program.program_no : '';
        }
        // const programProject = { _id: 1, name: 1, code: 1, program_type: 1 };
        // let Program = await ProgramInput.findOne(programQuery, programProject).lean();
        // Program = Program || user.program;
        // responseData.enrolledProgram = Program;
        // responseData.designation = Program && Program.name ? Program.name : '';
        return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userStaffStudentImportTemplates = async ({ headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const settingList = await settingsModel
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
            })
            .lean()
            .select('globalConfiguration');
        const fields = [];
        settingList.globalConfiguration.staffUserManagement.labelFieldConfiguration.forEach(
            (userDetails) => {
                userDetails.basicDetails.forEach((val) => {
                    if (val.isActive === true) {
                        fields.push(val.name);
                    }
                });
            },
        );

        const wb = new Excel.Workbook();
        const worksheet = wb.addWorksheet('User Details', {
            properties: { showGridLines: false },
            views: [{ state: 'frozen', ySplit: 1 }],
        });

        //add column headers
        const columns = [];
        let temp;
        for (const field of fields) {
            temp = field.toLowerCase().replace(/\s/g, '_');
            columns.push({ header: field, key: temp });
        }
        worksheet.columns = columns;
        const fileName = `templates.xlsx`;
        return {
            statusCode: 200,
            data: {
                wb,
                fileName,
            },
        };
    } catch (e) {
        if (e instanceof Error) {
            throw e;
        } else {
            throw new Error(e);
        }
    }
};

const userStaffStudentImport = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const programModel = getModel(tenantURL, constant.PROGRAM, programInputSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const collegeName = await institutionModel.findOne(
            {
                _id: convertToMongoObjectId(_institution_id),
            },
            { name: 1 },
        );
        const settingDoc = await settingModel
            .findOne(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                {
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                },
            )
            .lean();
        const { user_type, userData } = body;
        const existingData = [];
        const userImports = [];
        const userImportsCopy = [];
        const duplicates = [];
        const impEmail = [];
        const impUserId = [];
        const impNationality = [];
        let mailBody;
        if (user_type === constant.EVENT_WHOM.STAFF) {
            userData.forEach((user) => {
                const userTempPassword = generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
                const userImportedTimeStamp = new Date();
                const objs = {
                    insertOne: {
                        document: {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            user_type,
                            user_id: { value: user.employeeId ? user.employeeId.trim() : null },
                            email: user.email.trim().toLowerCase(),
                            name: {
                                first: { value: user.first_name },
                                last: { value: user.last_name },
                                middle: { value: user.middle_name },
                                family: { value: user.family_name },
                            },
                            gender: {
                                value: user.gender ? user.gender.toLowerCase().trim() : null,
                            },
                            password: bcrypt.hashSync(userTempPassword, 10),
                            mobile: { code: user.code, no: user.mobile },
                            enrollmentYear: user.enrollmentYear,
                            role: 'Staff',
                            status: 'imported',
                            'verification.invitedAt': userImportedTimeStamp,
                            activityLog: [
                                {
                                    activity: 'Request sent for verification',
                                    activityAt: userImportedTimeStamp,
                                },
                            ],
                        },
                    },
                };
                userImportsCopy.push({
                    _institution_id: convertToMongoObjectId(_institution_id),
                    user_type,
                    firstName: user.first_name,
                    middleName: user.middle_name,
                    lastName: user.last_name,
                    familyName: user.family_name,
                    tempPassword: userTempPassword,
                    mobile: { code: user.code, no: user.mobile },
                    invitedAt: userImportedTimeStamp,
                    gender: user.gender ? user.gender.toLowerCase().trim() : '',
                    user_id: user.employeeId ? user.employeeId.trim() : null,
                    email: user.email.trim().toLowerCase(),
                    isActive: true,
                    isDeleted: false,
                    isDefault: false,
                    status: 'imported',
                });
                if (user.middle_name != undefined && user.middle_name.length === 0) {
                    delete objs.insertOne.document.name.middle;
                }
                if (user.family_name != undefined && user.family_name.length === 0) {
                    delete objs.insertOne.document.name.family;
                }
                userImports.push(objs);
                impEmail.push(user.email.trim().toLowerCase());
                if (user.employeeId) impUserId.push(user.employeeId.trim().toLowerCase());
                if (user.nationalityId)
                    impNationality.push(user.nationalityId.trim().toLowerCase());
            });
        } else if (user_type === constant.EVENT_WHOM.STUDENT) {
            const programData = await programModel.find(
                { isDeleted: false, isActive: true },
                { _id: 1, name: 1 },
            );
            userData.forEach((user) => {
                const userTempPassword = generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
                const userImportedTimeStamp = new Date();
                const objs = {
                    insertOne: {
                        document: {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            user_type,
                            user_id: { value: user.academicNo ? user.academicNo.trim() : null },
                            email: user.email.trim().toLowerCase(),
                            name: {
                                first: { value: user.first_name },
                                last: { value: user.last_name },
                                middle: { value: user.middle_name },
                                family: { value: user.family_name },
                            },
                            gender: {
                                value: user.gender ? user.gender.toLowerCase().trim() : null,
                            },
                            batch: { value: user.batch.trim() },
                            password: bcrypt.hashSync(userTempPassword, 10),
                            mobile: { code: user.code, no: user.mobile },
                            program: {
                                value: user.programName.trim(),
                                _program_id:
                                    programData.findIndex(
                                        (i) => i.name == user.programName.trim(),
                                    ) != -1
                                        ? programData[
                                              programData.findIndex(
                                                  (i) => i.name === user.programName.trim(),
                                              )
                                          ]._id
                                        : null,
                            },
                            enrollmentYear: { value: user.enrollmentYear },
                            role: 'Student',
                            status: 'imported',
                            'verification.invitedAt': userImportedTimeStamp,
                            activityLog: [
                                {
                                    activity: 'Request sent for verification',
                                    activityAt: userImportedTimeStamp,
                                },
                            ],
                        },
                    },
                };
                userImports.push(objs);
                userImportsCopy.push({
                    _institution_id: convertToMongoObjectId(_institution_id),
                    user_type,
                    firstName: user.first_name,
                    middleName: user.middle_name,
                    lastName: user.last_name,
                    familyName: user.family_name,
                    tempPassword: userTempPassword,
                    mobile: { code: user.code, no: user.mobile },
                    invitedAt: userImportedTimeStamp,
                    gender: user.gender ? user.gender.toLowerCase().trim() : '',
                    user_id: user.academicNo ? user.academicNo.trim() : null,
                    email: user.email.trim().toLowerCase(),
                    isActive: true,
                    isDeleted: false,
                    isDefault: false,
                    status: 'imported',
                });
                impEmail.push(user.email.trim().toLowerCase());
                impUserId.push(user.academicNo);
            });
        }
        if (userImports.length != 0) {
            const userCreate = await userModel.bulkWrite(userImports);
            const dataset = userImportsCopy.map(({ tempPassword, ...rest }) => rest);
            const userRecords = bulkWrite({ indexName: USER_INSTITUTIONS, dataset });
            if (userCreate) {
                const auth = [];
                for (let i = 0; i < userImportsCopy.length; i++) {
                    const pushObjs = {
                        _id: userCreate.insertedIds[i],
                        userType: userImportsCopy[i].user_type.toUpperCase(),
                        user_id: userImportsCopy[i].user_id,
                        email: userImportsCopy[i].email,
                        name:
                            userImportsCopy[i].firstName != undefined
                                ? userImportsCopy[i].firstName
                                : '' + userImportsCopy[i].middleName != undefined
                                ? userImportsCopy[i].middleName
                                : '' + userImportsCopy[i].lastName != undefined
                                ? userImportsCopy[i].lastName
                                : '' + userImportsCopy[i].familyName != undefined
                                ? userImportsCopy[i].familyName
                                : '',
                        gender: userImportsCopy[i].gender,
                    };
                    auth.push(pushObjs);
                }
                const mailPush = sendStaffMail(userImportsCopy, settingDoc, collegeName, origin);
                // logger.info({ auth }, 'Auth Import Users ');
                // const appCode = getAppCodeByDomain(req.headers);
                // digiAuthService
                //     .importUser({ users: auth })
                //     .then((importedUsers) => {
                //         logger.info(
                //             { importedUsers },
                //             'userController -> exports.user_import -> importedUsers : %s',
                //         );
                //     })
                //     .catch((err) => {
                //         logger.error({ err }, 'userController -> exports.user_import -> err');
                //     });
                const responseData = {
                    importedRecordsCount: auth.length,
                    importedRecords: auth,
                };

                return {
                    statusCode: 200,
                    message: 'USER_DATA_IMPORTED_SUCCESSFULLY',
                    data: responseData,
                };
            }
            return {
                statusCode: 200,
                message: 'ERROR_UNABLE_TO_IMPORT_USER_DETAILS',
                data: userCreate.userData,
            };
        }
        const responseData = {
            importedRecordsCount: userImports.length,
            invalidRecordsCount: existingData.length + duplicates.length,
            importedRecords: userImports,
            invalidRecords: existingData,
            duplicateRecordsCount: duplicates.length,
            duplicateRecords: duplicates,
        };
        return {
            statusCode: 200,
            message: 'ERROR_FOUND_DUPLICATE_INVALID_DATA_IN_UPLOAD_FILE',
            data: responseData,
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const forgotPassword = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL, origin } = headers;
        const { email } = params;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const userData = await userModel.findOne({ email, isDeleted: false });
        if (!userData) {
            return {
                statusCode: 404,
                message: 'DS_GET_FAILED',
            };
        }
        const collegeName = await institutionModel.findOne(
            {
                _id: convertToMongoObjectId(userData._institution_id),
            },
            { name: 1 },
        );
        let settingDoc;
        if (userData.user_type === 'staff') {
            settingDoc = await settingModel
                .findOne(
                    { _institution_id: convertToMongoObjectId(userData._institution_id) },
                    {
                        'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                        'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                        'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                    },
                )
                .lean();
        } else {
            settingDoc = await settingModel
                .findOne(
                    { _institution_id: convertToMongoObjectId(userData._institution_id) },
                    {
                        'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                        'globalConfiguration.studentUserManagement.mailSettingsConfiguration': 1,
                        'globalConfiguration.studentUserManagement.mailConfiguration': 1,
                    },
                )
                .lean();
        }

        const userTempPassword = generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
        const user = {
            _institution_id: convertToMongoObjectId(userData._institution_id),
            user_type: userData.user_type,
            firstName: userData.name.first.value,
            middleName: userData.name.middle.value,
            lastName: userData.name.last.value,
            familyName: userData.name.family.value,
            tempPassword: userTempPassword,
            mobile: { code: userData.mobile.code, no: userData.mobile.no },
            gender: userData.gender.value,
            user_id: userData.user_id.value,
            email: userData.email,
            _id: userData._id,
        };
        const mailPush = sendStaffMailForPasswordReset(user, settingDoc, collegeName, origin);
        return {
            statusCode: 200,
            message: 'MAIL_SEND_SUCCESSFULLY',
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sendRequest = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const collegeName = await institutionModel.findOne(
            {
                _id: convertToMongoObjectId(_institution_id),
            },
            { name: 1 },
        );
        const settingDoc = await settingModel
            .findOne(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                {
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                },
            )
            .lean();
        const { user_type, userData, content } = body;
        const userImportsCopy = [];
        const bulkUpdate = [];
        const userId = userData.map((element) => convertToMongoObjectId(element._id));
        const usersDoc = await userModel.find(
            { _id: { $in: userId }, 'verification.email': false },
            { _id: 1, name: 1, email: 1 },
        );
        const userDataToSendEmail = userData.filter((element) =>
            usersDoc.some((elementEntry) => element.email === elementEntry.email),
        );
        userDataToSendEmail.forEach((user) => {
            const userTempPassword = generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
            const userImportedTimeStamp = new Date();
            if (user.isRegistered === true) {
                bulkUpdate.push({
                    updateMany: {
                        filter: {
                            _id: convertToMongoObjectId(user._id),
                        },
                        update: {
                            password: bcrypt.hashSync(userTempPassword, 10),
                            isActive: true,
                        },
                    },
                });
            } else {
                bulkUpdate.push({
                    updateMany: {
                        filter: {
                            _id: convertToMongoObjectId(user._id),
                        },
                        update: {
                            password: bcrypt.hashSync(userTempPassword, 10),
                            isActive: true,
                            status: constant.IMPORTED,
                        },
                    },
                });
            }
            userImportsCopy.push({
                _institution_id: convertToMongoObjectId(_institution_id),
                user_type,
                firstName: user.first_name ? user.first_name : '',
                middleName: user.middle_name ? user.middle_name : '',
                lastName: user.last_name ? user.last_name : '',
                familyName: user.family_name ? user.family_name : '',
                tempPassword: userTempPassword,
                invitedAt: userImportedTimeStamp,
                email: user.email.trim().toLowerCase(),
            });
        });
        if (bulkUpdate.length != 0) {
            const userCreate = await userModel.bulkWrite(bulkUpdate);
            const mailPush = sendStaffMail(
                userImportsCopy,
                settingDoc,
                collegeName,
                origin,
                content,
                settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[0].labelName,
            );

            return {
                statusCode: 200,
                message: 'SEND_MAIL_SUCCESSFULLY',
            };
        }
        return {
            statusCode: 200,
            message: 'FAILED_TO_SEND',
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteUser = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;
        const getUser = await userModel.findById(
            { _id: convertToMongoObjectId(id) },
            { status: 1, isActive: 1, isDeleted: 1, email: 1, _institution_id: 1 },
        );
        const userDoc = await userModel.deleteMany({ _id: convertToMongoObjectId(id) });
        if (!userDoc) {
            return {
                statusCode: 404,
                message: 'DS_DELETE_FAILED',
            };
        }
        const userRecords = removeRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                dis_max: {
                    queries: [
                        { match: { email: getUser.email } },
                        { match: { _institution_id: String(getUser._institution_id) } },
                    ],
                },
            },
        });
        return {
            statusCode: 200,
            message: 'DS_DELETED',
        };
    } catch (e) {
        if (e instanceof Error) {
            throw e;
        } else {
            throw new Error(e);
        }
    }
};

const updateUser = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;
        const {
            email,
            firstName,
            lastName,
            middleName,
            familyName,
            gender,
            nationalityId,
            building,
            city,
            district,
            zipCode,
            unit,
            passportNo,
            dob,
            _nationality_id,
            employee_id,
            officeRoomNo,
            officeExtension,
            academicNo,
            programNo,
            enrollmentYear,
            batch,
            contact,
            mobile,
        } = body;
        let docs = { status: false };
        const obj = {};
        const contacts = [];
        let courseScheduleQuery = {};
        let courseScheduleUpdate = {};
        const nameObject = {};
        const userCheck = await userModel.findOne({ _id: id, isDeleted: false, isActive: true });
        const changedFields = userCheck.changedFields
            ? new Set([])
            : new Set(userCheck.changedFields);
        if (userCheck) {
            if (email != undefined && email.length != 0) {
                Object.assign(obj, { email: email.toLowerCase() });
                changedFields.add('email');
            }
            if (firstName != undefined && firstName.length != 0) {
                Object.assign(obj, { 'name.first.value': firstName });
                nameObject.first.value = firstName;
                changedFields.add('first_name');
            }
            if (lastName != undefined && lastName.length != 0) {
                Object.assign(obj, { 'name.last.value': lastName });
                nameObject.last.value = lastName;
                changedFields.add('last_name');
            }
            if (middleName != undefined) {
                Object.assign(obj, { 'name.middle.value': middleName });
                if (middleName.length !== 0) nameObject.middle.value = middleName;
                changedFields.add('middle_name');
            }
            if (familyName != undefined) {
                Object.assign(obj, { 'name.family.value': familyName });
                if (familyName.length !== 0) nameObject.family.value = familyName;
                changedFields.add('family_name');
            }
            if (gender != undefined && gender.length != 0) {
                Object.assign(obj, { 'gender.value': gender });
                changedFields.add('gender');
            }
            if (programNo != undefined && programNo.length != 0) {
                Object.assign(obj, { 'program.value': programNo });
                changedFields.add('programNo');
            }
            if (batch != undefined && batch.length != 0) {
                Object.assign(obj, { 'batch.value': batch });
                changedFields.add('batch');
            }
            if (enrollmentYear != undefined && enrollmentYear.length != 0) {
                Object.assign(obj, { 'enrollmentYear.value': enrollmentYear });
                changedFields.add('enrollmentYear');
            }
            if (!mobile) {
                Object.assign(obj, { 'mobile.no': mobile });
                changedFields.add('mobile_no');
            }
            if (nationalityId != undefined && nationalityId.length != 0) {
                const userCheck = await userModel.findOne(
                    {
                        _id: { $ne: convertToMongoObjectId(id) },
                        'profileDetails.nationalityId.value': nationalityId.trim(),
                        isDeleted: false,
                    },
                    { _id: 1 },
                );
                if (userCheck) {
                    return {
                        statusCode: 404,
                        message: 'DUPLICATE_NATIONALITY_ID_FOUND',
                    };
                }
                Object.assign(obj, { 'profileDetails.nationalityId.value': nationalityId.trim() });
            }
            if (building != undefined && building.length != 0) {
                Object.assign(obj, { 'addressDetails.buildingStreetName.value': building });
            }
            if (city != undefined && city.length != 0) {
                Object.assign(obj, { 'addressDetails.city.value': city });
            }
            if (district != undefined && district.length != 0) {
                Object.assign(obj, { 'addressDetails.district.value': district });
            }
            if (zipCode != undefined && zipCode.length != 0) {
                Object.assign(obj, { 'addressDetails.zipCode.value': zipCode });
            }
            if (unit != undefined && unit.length != 0) {
                Object.assign(obj, { 'addressDetails.unit.value': unit });
            }
            if (dob != undefined && dob.length != 0) {
                Object.assign(obj, { 'profileDetails.dob.value': dob });
            }
            if (passportNo != undefined && passportNo.length != 0) {
                Object.assign(obj, { 'profileDetails.passportNo.value': passportNo });
            }
            if (_nationality_id != undefined && _nationality_id.length != 0) {
                Object.assign(obj, { 'profileDetails._nationality_id.value': _nationality_id });
            }
            if (userCheck.user_type == constant.EVENT_WHOM.STAFF) {
                if (employee_id != undefined && employee_id.length != 0) {
                    const query = {
                        user_type: {
                            $in: [
                                employee_id,
                                employee_id.toUpperCase(),
                                employee_id.toLowerCase(),
                            ],
                        },
                        isDeleted: false,
                    };
                    const project = { user_id: 1, email: 1, name: 1, user_type: 1 };
                    const userDocValidation = await userModel.find(query, project);
                    if (userDocValidation.status) {
                        return {
                            statusCode: 404,
                            message: 'DUPLICATE_EMPLOYEE_ID',
                        };
                    }
                    Object.assign(obj, { 'user_id.value': employee_id });
                    changedFields.add('employee_id');
                }
                if (officeRoomNo != undefined && officeRoomNo.length != 0) {
                    Object.assign(obj, { 'office.office_room_no': officeRoomNo });
                }
                if (officeExtension != undefined && officeExtension.length != 0) {
                    Object.assign(obj, { 'office.office_extension': officeExtension });
                }
                // Updating Name In Course Schedule
                if (nameObject !== {}) {
                    courseScheduleQuery = {
                        isDeleted: false,
                        'staffs._staff_id': convertToMongoObjectId(id),
                    };
                    courseScheduleUpdate = {
                        $set: {
                            'staffs.$.staff_name': nameObject,
                        },
                    };
                }
            }
            //TODO:Need to update student data

            Object.assign(obj, { changedFields: Array.from(changedFields) });
            docs = await userModel.updateOne({ _id: convertToMongoObjectId(id) }, obj);
            //TODO:Need to update course schedule and student_group for students
            if (docs) {
                return {
                    statusCode: 200,
                    message: 'USER_DATA_MODIFIED_SUCCESSFULLY',
                };
            }
            return {
                statusCode: 500,
                message: 'ERROR',
            };
        }
        return {
            statusCode: 404,
            message: 'ERROR_USER_ID_NOT_MATCH',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUser = async ({ body = {}, params = {}, headers = {} }) => {
    const { limit, pageNo, user_type, tab } = params;
    const { tenantURL } = headers;
    const courseModel = getModel(tenantURL, COURSE, courseSchema);
    const skips = Number(limit * (pageNo - 1));
    const limits = Number(limit) || 0;
    let query;

    if (tab === 'all') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: { $not: { $eq: 'completed' } },
            // status: { $not: 'imported' },
        };
    } else if (tab === 'inactive') {
        query = {
            isActive: false,
            isDeleted: false,
            user_type,
        };
    } else if (tab === 'imported') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'imported',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
        };
    } else if (tab === 'submitted') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECT },
                // { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (tab === 'mismatch') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (tab === 'valid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'verified',
            $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
        };
    } else if (tab === 'invalid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'verified',
            $or: [
                { 'verification.data': constant.INVALID },
                { 'verification.data': constant.DONE },
            ],
        };
    } else if (tab === 'invited') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (tab === 'expired') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (tab === 'completed') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type,
            status: 'completed',
            $or: [
                { 'verification.data': constant.DONE },
                { 'verification.face': true },
                { 'verification.finger': true },
            ],
        };
    }

    const allUser = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: { $not: { $eq: 'completed' } },
    };
    const inactiveUser = {
        isActive: false,
        isDeleted: false,
        user_type,
    };
    const imported = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'imported',
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const submitted = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'submitted',
        $and: [
            { 'verification.data': constant.CORRECT },
            // { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.face': false },
            { 'verification.finger': false },
        ],
    };
    const mismatch = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'submitted',
        $and: [
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.face': false },
            { 'verification.finger': false },
        ],
    };
    const valid = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'verified',
        $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
    };
    const invalid = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'verified',
        $or: [{ 'verification.data': constant.INVALID }, { 'verification.data': constant.DONE }],
    };
    const invited = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'invited',
        $expr: { $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.data': constant.CORRECT },
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const expired = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'invited',
        $expr: { $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.data': constant.CORRECT },
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const completed = {
        isActive: true,
        isDeleted: false,
        user_type,
        status: 'completed',
        $or: [
            { 'verification.data': constant.DONE },
            { 'verification.face': true },
            { 'verification.finger': true },
        ],
    };

    const allUserCount = await userModel.find(allUser).countDocuments().exec();
    const inactiveUserCount = await userModel.find(inactiveUser).countDocuments().exec();
    const importedCount = await userModel.find(imported).countDocuments().exec();
    const submittedCount = await userModel.find(submitted).countDocuments().exec();
    const mismatchCount = await userModel.find(mismatch).countDocuments().exec();
    const validCount = await userModel.find(valid).countDocuments().exec();
    const invalidCount = await userModel.find(invalid).countDocuments().exec();
    const invitedCount = await userModel.find(invited).countDocuments().exec();
    const expiredCount = await userModel.find(expired).countDocuments().exec();
    const completedCount = await userModel.find(completed).countDocuments().exec();

    const userAggregate = [{ $match: query }];
    if (user_type === 'staff') {
        if (tab === 'completed') {
            userAggregate.push({ $addFields: { program_ids: '$academic_allocation._program_id' } });
            userAggregate.push(
                {
                    $lookup: {
                        from: 'role_assigns',
                        localField: '_role_id',
                        foreignField: '_id',
                        as: 'role_data',
                    },
                },
                { $unwind: { path: '$role_data', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role_check: {
                            $filter: {
                                input: '$role_data.roles',
                                as: 'item',
                                cond: { $gte: ['$$item.isDefault', true] },
                            },
                        },
                    },
                },
                { $unwind: { path: '$role_check', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role: {
                            $cond: {
                                if: { $ne: ['$role_check', null] },
                                then: '$role_check.role_name',
                                else: '',
                            },
                        },
                    },
                },
            );
        }
    } else {
        userAggregate.push({
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'program._program_id',
                foreignField: '_id',
                as: 'programs',
            },
        });
        userAggregate.push({ $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } });
        userAggregate.push({ $addFields: { program_name: '$programs.name' } });
    }
    if (tab === 'all') {
        userAggregate.push({
            $addFields: {
                user_state: {
                    $switch: {
                        branches: [
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'imported'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Imported',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'correct'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "correct"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Submitted',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'error'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "error"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Mismatch',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'valid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Valid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'invalid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invalid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $lte: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invited',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $gt: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Expired',
                            },
                        ],
                        default: 'Unknown',
                    },
                },
            },
        });
    }
    userAggregate.push({ $sort: { updatedAt: -1 } }, { $skip: skips }, { $limit: limits });
    const userDoc = await userModel.aggregate(userAggregate);
    const totalDoc = await userModel.find(query).countDocuments().exec();
    const userData = Array.isArray(userDoc) ? userDoc : [];
    const totalPages = Math.ceil(totalDoc / limits);
    return {
        status_code: 200,
        message: 'USER_LIST',
        data: {
            totalDoc,
            totalPages,
            currentPage: Number(pageNo),
            count: {
                all_active_user_count: allUserCount,
                inactiveUserCount,
                importedCount,
                invitedCount,
                submittedCount,
                mismatchCount,
                validCount,
                invalidCount,
                expiredCount,
                completedCount,
            },
            data: userData,
        },
    };
};

const userDetails = async ({ headers = {}, params = {} }) => {
    const { _institution_id, tenantURL } = headers;
    const userModel = getModel(tenantURL, USER, userSchema);
    const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
    const { id, language } = params;
    const userProjection = {
        _institution_id: 1,
        email: 1,
        user_type: 1,
        name: 1,
        user_id: 1,
        mobile: 1,
        profileDetails: 1,
        biometricData: 1,
        gender: 1,
        verification: 1,
        program: 1,
        batch: 1,
        enrollmentYear: 1,
        countryId: 1,
        districtId: 1,
        isActive: 1,
        isDeleted: 1,
        status: 1,
        isRegistered: 1,
        statusHistory: 1,
        activityLog: 1,
        uploadedDocuments: 1,
        isGlobalMandatoryStatus: 1,
        approvingTypeAll: 1,
        isRejectHandlingStatus: 1,
    };
    const userData = await userModel
        .findOne({ _id: convertToMongoObjectId(id) }, userProjection)
        .lean();

    const setting = await settingsModel
        .findOne(
            {
                _institution_id: convertToMongoObjectId(userData._institution_id),
            },
            {
                'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                'globalConfiguration.staffUserManagement.vaccineConfiguration': 1,
                'globalConfiguration.staffUserManagement.vaccineLabelDetails': 1,
                'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                'globalConfiguration.studentUserManagement.vaccineConfiguration': 1,
                'globalConfiguration.studentUserManagement.vaccineLabelDetails': 1,
                'globalConfiguration.studentUserManagement.biometricConfiguration': 1,
                'globalConfiguration.studentUserManagement.documentConfiguration': 1,
            },
        )
        .lean();
    if (!setting) return { statusCode: 404, message: 'Label / Field not exists' };
    let settingsGet;
    if (
        language &&
        setting.globalConfiguration.staffUserManagement.labelFieldConfiguration &&
        setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.length > 0
    ) {
        settingsGet = setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.find(
            (element) => element.language === language,
        );
    }

    // let vaccineDetails = [];
    // if (
    //     setting.globalConfiguration.staffUserManagement.vaccineConfiguration &&
    //     setting.globalConfiguration.staffUserManagement.vaccineConfiguration.length > 0
    // ) {
    //     vaccineDetails = setting.globalConfiguration.staffUserManagement.vaccineConfiguration.map(
    //         (element) => {
    //             return {
    //                 _id: element._id,
    //                 categoryName: element.categoryName,
    //                 isActive: element.isActive,
    //                 isMandatory: element.isMandatory,
    //             };
    //         },
    //     );
    // }
    return {
        statusCode: 200,
        data: {
            userData,
            labels: {
                // VaccineDetailsCheckBox:
                //     setting.globalConfiguration.staffUserManagement.vaccineLabelDetails,
                vaccineDetails:
                    userData.user_type === 'staff'
                        ? setting.globalConfiguration.staffUserManagement.vaccineConfiguration
                        : setting.globalConfiguration.studentUserManagement.vaccineConfiguration,
                labelDetails: language
                    ? settingsGet
                    : userData.user_type === 'staff'
                    ? setting.globalConfiguration.staffUserManagement.labelFieldConfiguration
                    : setting.globalConfiguration.studentUserManagement.labelFieldConfiguration,
                biometricConfiguration:
                    userData.user_type === 'staff'
                        ? setting.globalConfiguration.staffUserManagement.biometricConfiguration
                        : setting.globalConfiguration.studentUserManagement.biometricConfiguration,
                documentConfiguration:
                    userData.user_type === 'staff'
                        ? setting.globalConfiguration.staffUserManagement.documentConfiguration
                        : setting.globalConfiguration.studentUserManagement.documentConfiguration,
            },
        },
    };
};

const userSignUp = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { email, password } = body;
        const userQuery = {
            email: email.toLowerCase(),
            isDeleted: false,
        };
        const userProjection = {
            _institution_id: 1,
            password: 1,
            user_type: 1,
            verification: 1,
            isActive: 1,
            status: 1,
            isRegistered: 1,
        };
        let userDetails = await userModel.findOne(userQuery, userProjection).lean();
        if (
            !userDetails ||
            !(
                password !== userDetails.password &&
                bcrypt.compareSync(password, userDetails.password)
            )
        )
            return {
                statusCode: 401,
                message: 'INCORRECT_EMAIL_OR_PASSWORD',
            };

        if (userDetails.verification.email === true && userDetails.verification.password === true) {
            return {
                statusCode: 404,
                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(userDetails._institution_id),
                },
                {
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.staffUserManagement.vaccineConfiguration': 1,
                    'globalConfiguration.staffUserManagement.vaccineLabelDetails': 1,
                    'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                    'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.studentUserManagement.vaccineConfiguration': 1,
                    'globalConfiguration.studentUserManagement.vaccineLabelDetails': 1,
                    'globalConfiguration.studentUserManagement.biometricConfiguration': 1,
                    'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                },
            )
            .lean();
        if (userDetails.status === 'completed' || userDetails.status === 'valid')
            return {
                statusCode: 404,
                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
            };
        let checkBiometric;
        if (userDetails.user_type === 'staff') {
            setting.globalConfiguration.staffUserManagement.biometricConfiguration.find((label) => {
                if (label.labelName === 'Offline verification') {
                    checkBiometric = label.isActive;
                }
            });
            if (
                userDetails.verification.profile.basicDetails === true &&
                userDetails.verification.profile.profileDetails === true &&
                userDetails.verification.profile.vaccinationDetails === true &&
                userDetails.verification.email === true &&
                userDetails.verification.mobile === true &&
                userDetails.verification.password === true
            ) {
                if (
                    (setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments.length > 0 &&
                        userDetails.verification.document === true) ||
                    setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments.length <= 0
                ) {
                    if (
                        (!checkBiometric && userDetails.verification.face === true) ||
                        checkBiometric
                    ) {
                        return {
                            statusCode: 404,
                            message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                        };
                    }
                }
            }
        } else {
            setting.globalConfiguration.studentUserManagement.biometricConfiguration.find(
                (label) => {
                    if (label.labelName === 'Offline verification') {
                        checkBiometric = label.isActive;
                    }
                },
            );
            if (
                userDetails.verification.profile.basicDetails === true &&
                userDetails.verification.profile.profileDetails === true &&
                userDetails.verification.profile.vaccinationDetails === true &&
                userDetails.verification.email === true &&
                userDetails.verification.mobile === true &&
                userDetails.verification.password === true
            ) {
                if (
                    (setting.globalConfiguration.studentUserManagement.documentConfiguration
                        .chooseDocuments.length > 0 &&
                        userDetails.verification.document === true) ||
                    setting.globalConfiguration.studentUserManagement.documentConfiguration
                        .chooseDocuments.length <= 0
                ) {
                    if (
                        (!checkBiometric && userDetails.verification.face === true) ||
                        checkBiometric
                    ) {
                        return {
                            statusCode: 404,
                            message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                        };
                    }
                }
            }
        }
        if (!userDetails.isActive)
            return {
                statusCode: 404,
                message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
            };
        let userUpdateObject;
        if (
            userDetails.status === constant.INVALID ||
            userDetails.status === constant.VALID ||
            userDetails.status === constant.PROFILE_UPDATED
        ) {
            userUpdateObject = {
                'verification.email': true,
            };
        } else {
            userUpdateObject = {
                'verification.email': true,
                status: 'signed',
            };
        }
        userUpdateObject.$addToSet = {
            activityLog: {
                activity: `Profile Moved to Registered list`,
                activityAt: new Date(),
            },
        };
        userDetails.verification.email = true;
        userDetails = {
            ...userDetails,
            tokens: await generateAuthTokensForSignUp({
                _id: userDetails._id,
            }),
        };
        const userUpdate = await userModel.updateOne(userQuery, userUpdateObject);
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'SIGNED_IN_SUCCESSFULLY', data: userDetails };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userSignUpForWeb = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { email, password } = body;
        const userQuery = {
            email: email.toLowerCase(),
            isDeleted: false,
        };
        const userProjection = {
            _institution_id: 1,
            password: 1,
            user_type: 1,
            verification: 1,
            isActive: 1,
            status: 1,
            isRegistered: 1,
        };
        if (!_institution_id) {
            const userInstitutionSearch = await getRecords({
                indexName: USER_INSTITUTIONS,
                query: {
                    match_phrase: { email },
                },
            });
            const institutionData =
                userInstitutionSearch &&
                userInstitutionSearch.body &&
                userInstitutionSearch.body.hits &&
                userInstitutionSearch.body.hits.hits &&
                userInstitutionSearch.body.hits.hits.length
                    ? userInstitutionSearch.body.hits.hits.map(
                          (userResultElement) => userResultElement._source,
                      )
                    : [];
            let isDefaultInstitute = null;
            if (institutionData.length) {
                isDefaultInstitute = institutionData.find((element) => {
                    return element.isDefault === 'true';
                });
            }
            let responseData = {
                email,
                _institution_id:
                    isDefaultInstitute != undefined ? isDefaultInstitute._institution_id : null,
                isDefault: isDefaultInstitute != undefined,
            };
            let institutes = [];
            if (!isDefaultInstitute) {
                const instituteIds = [];
                institutionData.forEach((data) => {
                    instituteIds.push(data._institution_id);
                });
                const instituteObjectIds = instituteIds.map((i) => convertToMongoObjectId(i));
                institutes = await institutionModel.find(
                    { _id: { $in: instituteObjectIds } },
                    {
                        address: 1,
                        code: 1,
                        type: 1,
                        name: 1,
                        logo: 1,
                    },
                );
                const data = await getPresignedUrlsForInstitutes(institutes);
                responseData = { ...responseData, data };
                return { statusCode: 200, message: 'WELCOME_TO_DIGICLASS', data: responseData };
            }
            userQuery._institution_id = isDefaultInstitute._institution_id;
            let userDetails = await userModel.findOne(userQuery, userProjection).lean();
            if (
                !userDetails ||
                !(
                    password !== userDetails.password &&
                    bcrypt.compareSync(password, userDetails.password)
                )
            )
                return {
                    statusCode: 401,
                    message: 'INCORRECT_EMAIL_OR_PASSWORD',
                };

            if (
                userDetails.verification.email === true &&
                userDetails.verification.password === true
            ) {
                return {
                    statusCode: 404,
                    message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                };
            }
            const setting = await settingsModel
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(userDetails._institution_id),
                    },
                    {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                        'globalConfiguration.staffUserManagement.vaccineConfiguration': 1,
                        'globalConfiguration.staffUserManagement.vaccineLabelDetails': 1,
                        'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                        'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                        'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                        'globalConfiguration.studentUserManagement.vaccineConfiguration': 1,
                        'globalConfiguration.studentUserManagement.vaccineLabelDetails': 1,
                        'globalConfiguration.studentUserManagement.biometricConfiguration': 1,
                        'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                    },
                )
                .lean();
            if (userDetails.status === 'completed' || userDetails.status === 'valid')
                return {
                    statusCode: 404,
                    message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                };
            let checkBiometric;
            if (userDetails.user_type === 'staff') {
                setting.globalConfiguration.staffUserManagement.biometricConfiguration.find(
                    (label) => {
                        if (label.labelName === 'Offline verification') {
                            checkBiometric = label.isActive;
                        }
                    },
                );
                if (
                    userDetails.verification.profile.basicDetails === true &&
                    userDetails.verification.profile.profileDetails === true &&
                    userDetails.verification.profile.vaccinationDetails === true &&
                    userDetails.verification.email === true &&
                    userDetails.verification.mobile === true &&
                    userDetails.verification.password === true
                ) {
                    if (
                        (setting.globalConfiguration.staffUserManagement.documentConfiguration
                            .chooseDocuments.length > 0 &&
                            userDetails.verification.document === true) ||
                        setting.globalConfiguration.staffUserManagement.documentConfiguration
                            .chooseDocuments.length <= 0
                    ) {
                        if (
                            (!checkBiometric && userDetails.verification.face === true) ||
                            checkBiometric
                        ) {
                            return {
                                statusCode: 404,
                                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                            };
                        }
                    }
                }
            } else {
                setting.globalConfiguration.studentUserManagement.biometricConfiguration.find(
                    (label) => {
                        if (label.labelName === 'Offline verification') {
                            checkBiometric = label.isActive;
                        }
                    },
                );
                if (
                    userDetails.verification.profile.basicDetails === true &&
                    userDetails.verification.profile.profileDetails === true &&
                    userDetails.verification.profile.vaccinationDetails === true &&
                    userDetails.verification.email === true &&
                    userDetails.verification.mobile === true &&
                    userDetails.verification.password === true
                ) {
                    if (
                        (setting.globalConfiguration.studentUserManagement.documentConfiguration
                            .chooseDocuments.length > 0 &&
                            userDetails.verification.document === true) ||
                        setting.globalConfiguration.studentUserManagement.documentConfiguration
                            .chooseDocuments.length <= 0
                    ) {
                        if (
                            (!checkBiometric && userDetails.verification.face === true) ||
                            checkBiometric
                        ) {
                            return {
                                statusCode: 404,
                                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                            };
                        }
                    }
                }
            }
            if (!userDetails.isActive)
                return {
                    statusCode: 404,
                    message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
                };
            let userUpdateObject;
            if (
                userDetails.status === constant.INVALID ||
                userDetails.status === constant.VALID ||
                userDetails.status === constant.PROFILE_UPDATED
            ) {
                userUpdateObject = {
                    'verification.email': true,
                };
            } else {
                userUpdateObject = {
                    'verification.email': true,
                    status: 'signed',
                };
            }
            userUpdateObject.$addToSet = {
                activityLog: {
                    activity: `Profile Moved to Registered list`,
                    activityAt: new Date(),
                },
            };
            userDetails.verification.email = true;
            userDetails = {
                ...userDetails,
                tokens: await generateAuthTokensForSignUp({
                    _id: userDetails._id,
                }),
            };
            const userUpdate = await userModel.updateOne(userQuery, userUpdateObject);
            if (!userUpdate)
                return {
                    statusCode: 400,
                    message: 'FAILED_TO_UPDATE',
                };
            return { statusCode: 200, message: 'SIGNED_IN_SUCCESSFULLY', data: userDetails };
        }
        userQuery._institution_id = convertToMongoObjectId(_institution_id);
        let userDetails = await userModel.findOne(userQuery, userProjection).lean();
        if (
            !userDetails ||
            !(
                password !== userDetails.password &&
                bcrypt.compareSync(password, userDetails.password)
            )
        )
            return {
                statusCode: 401,
                message: 'INCORRECT_EMAIL_OR_PASSWORD',
            };

        if (userDetails.verification.email === true && userDetails.verification.password === true) {
            return {
                statusCode: 404,
                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(userDetails._institution_id),
                },
                {
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.staffUserManagement.vaccineConfiguration': 1,
                    'globalConfiguration.staffUserManagement.vaccineLabelDetails': 1,
                    'globalConfiguration.staffUserManagement.biometricConfiguration': 1,
                    'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.studentUserManagement.vaccineConfiguration': 1,
                    'globalConfiguration.studentUserManagement.vaccineLabelDetails': 1,
                    'globalConfiguration.studentUserManagement.biometricConfiguration': 1,
                    'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                },
            )
            .lean();
        if (userDetails.status === 'completed' || userDetails.status === 'valid')
            return {
                statusCode: 404,
                message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
            };
        let checkBiometric;
        if (userDetails.user_type === 'staff') {
            setting.globalConfiguration.staffUserManagement.biometricConfiguration.find((label) => {
                if (label.labelName === 'Offline verification') {
                    checkBiometric = label.isActive;
                }
            });
            if (
                userDetails.verification.profile.basicDetails === true &&
                userDetails.verification.profile.profileDetails === true &&
                userDetails.verification.profile.vaccinationDetails === true &&
                userDetails.verification.email === true &&
                userDetails.verification.mobile === true &&
                userDetails.verification.password === true
            ) {
                if (
                    (setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments.length > 0 &&
                        userDetails.verification.document === true) ||
                    setting.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments.length <= 0
                ) {
                    if (
                        (!checkBiometric && userDetails.verification.face === true) ||
                        checkBiometric
                    ) {
                        return {
                            statusCode: 404,
                            message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                        };
                    }
                }
            }
        } else {
            setting.globalConfiguration.studentUserManagement.biometricConfiguration.find(
                (label) => {
                    if (label.labelName === 'Offline verification') {
                        checkBiometric = label.isActive;
                    }
                },
            );
            if (
                userDetails.verification.profile.basicDetails === true &&
                userDetails.verification.profile.profileDetails === true &&
                userDetails.verification.profile.vaccinationDetails === true &&
                userDetails.verification.email === true &&
                userDetails.verification.mobile === true &&
                userDetails.verification.password === true
            ) {
                if (
                    (setting.globalConfiguration.studentUserManagement.documentConfiguration
                        .chooseDocuments.length > 0 &&
                        userDetails.verification.document === true) ||
                    setting.globalConfiguration.studentUserManagement.documentConfiguration
                        .chooseDocuments.length <= 0
                ) {
                    if (
                        (!checkBiometric && userDetails.verification.face === true) ||
                        checkBiometric
                    ) {
                        return {
                            statusCode: 404,
                            message: 'YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN',
                        };
                    }
                }
            }
        }
        if (!userDetails.isActive)
            return {
                statusCode: 404,
                message: 'YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR',
            };
        let userUpdateObject;
        if (
            userDetails.status === constant.INVALID ||
            userDetails.status === constant.VALID ||
            userDetails.status === constant.PROFILE_UPDATED
        ) {
            userUpdateObject = {
                'verification.email': true,
            };
        } else {
            userUpdateObject = {
                'verification.email': true,
                status: 'signed',
            };
        }
        userUpdateObject.$addToSet = {
            activityLog: {
                activity: `Profile Moved to Registered list`,
                activityAt: new Date(),
            },
        };
        userDetails.verification.email = true;
        userDetails = {
            ...userDetails,
            tokens: await generateAuthTokensForSignUp({
                _id: userDetails._id,
            }),
        };
        const userUpdate = await userModel.updateOne(userQuery, userUpdateObject);
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'SIGNED_IN_SUCCESSFULLY', data: userDetails };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userRegistration = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const programModel = getModel(tenantURL, constant.PROGRAM, programInputSchema);
        const { id } = params;
        const { user_type, setMode, newPassword, updateObject, edited, isAdmin, forgotPassword } =
            body;
        const userQuery = {
            _id: convertToMongoObjectId(id),
            isDeleted: false,
        };
        const userDocs = await userModel
            .findById(
                { _id: convertToMongoObjectId(id) },
                {
                    name: 1,
                    gender: 1,
                    mobile: 1,
                    _institution_id: 1,
                    email: 1,
                    status: 1,
                    isRegistered: 1,
                    user_id: 1,
                    program: 1,
                    batch: 1,
                    enrollmentYear: 1,
                },
            )
            .lean();
        const programData = await programModel.find(
            { isDeleted: false, isActive: true },
            { _id: 1, name: 1 },
        );

        let userUpdateObject = {};
        let script = null;
        if (user_type === constant.EVENT_WHOM.STAFF) {
            switch (setMode) {
                case 'password':
                    userUpdateObject = {
                        password: bcrypt.hashSync(newPassword, 10),
                        'verification.password': true,
                        status: forgotPassword ? userDocs.status : constant.PASSWORD_CONFIRMED,
                    };
                    script = `ctx._source.status = '${userUpdateObject.status}'`;
                    if (!forgotPassword) {
                        userUpdateObject.$addToSet = {
                            activityLog: {
                                activity: `Staff's password verified successfully`,
                                activityAt: new Date(),
                            },
                        };
                    } else {
                        userUpdateObject.$addToSet = {
                            activityLog: {
                                activity: `Staff's password changed successfully`,
                                activityAt: new Date(),
                            },
                        };
                    }
                    break;
                case 'basicDetails':
                    if (isAdmin === true) {
                        userUpdateObject = {
                            ...(updateObject.email && { email: updateObject.email }),
                            ...((updateObject.user_id === '' || updateObject.user_id) && {
                                'user_id.value': updateObject.user_id,
                            }),
                            ...((updateObject.firstName === '' || updateObject.firstName) && {
                                'name.first.value': updateObject.firstName,
                            }),
                            ...((updateObject.middleName === '' || updateObject.middleName) && {
                                'name.middle.value': updateObject.middleName,
                            }),
                            ...((updateObject.lastName === '' || updateObject.lastName) && {
                                'name.last.value': updateObject.lastName,
                            }),
                            ...((updateObject.familyName === '' || updateObject.familyName) && {
                                'name.family.value': updateObject.familyName,
                            }),
                            ...((updateObject.gender === '' || updateObject.gender) && {
                                'gender.value': updateObject.gender,
                            }),
                            ...((updateObject.code === '' || updateObject.code) && {
                                'mobile.code': updateObject.code,
                            }),
                            ...((updateObject.no === '' || updateObject.no) && {
                                'mobile.no': updateObject.no,
                            }),
                        };
                    } else {
                        userUpdateObject = {
                            'verification.profile.basicDetails': true,
                            ...(updateObject.email && { email: updateObject.email }),
                            ...((updateObject.user_id === '' || updateObject.user_id) && {
                                'user_id.value': updateObject.user_id,
                                'user_id.edited': edited,
                                'user_id.userEdited':
                                    userDocs.user_id.userEdited === true
                                        ? true
                                        : userDocs.user_id.value !== updateObject.user_id,
                            }),
                            ...((updateObject.user_id === '' || updateObject.user_id) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'user_id.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.firstName === '' || updateObject.firstName) && {
                                'name.first.value': updateObject.firstName,
                                'name.first.edited': edited,
                                'name.first.userEdited':
                                    userDocs.name.first.userEdited === true
                                        ? true
                                        : userDocs.name.first.value !== updateObject.firstName,
                            }),
                            ...((updateObject.firstName === '' || updateObject.firstName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.first.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.middleName === '' || updateObject.middleName) && {
                                'name.middle.value': updateObject.middleName,
                                'name.middle.edited': edited,
                                'name.middle.userEdited':
                                    userDocs.name.middle.userEdited === true
                                        ? true
                                        : userDocs.name.middle.value !== updateObject.middleName,
                            }),
                            ...((updateObject.middleName === '' || updateObject.middleName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.middle.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.lastName === '' || updateObject.lastName) && {
                                'name.last.value': updateObject.lastName,
                                'name.last.edited': edited,
                                'name.last.userEdited':
                                    userDocs.name.last.userEdited === true
                                        ? true
                                        : userDocs.name.last.value !== updateObject.lastName,
                                'name.last.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                            ...((updateObject.lastName === '' || updateObject.lastName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.last.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.familyName === '' || updateObject.familyName) && {
                                'name.family.value': updateObject.familyName,
                                'name.family.edited': edited,
                                'name.family.userEdited':
                                    userDocs.name.family.userEdited === true
                                        ? true
                                        : userDocs.name.family.value !== updateObject.familyName,
                            }),
                            ...((updateObject.familyName === '' || updateObject.familyName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.family.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.gender === '' || updateObject.gender) && {
                                'gender.value': updateObject.gender,
                                'gender.edited': edited,
                                'gender.userEdited':
                                    userDocs.gender.userEdited === true
                                        ? true
                                        : userDocs.gender.value !== updateObject.gender,
                            }),
                            ...((updateObject.gender === '' || updateObject.gender) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'gender.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.code === '' || updateObject.code) && {
                                'mobile.code': updateObject.code,
                                'mobile.userEdited':
                                    userDocs.mobile.userEdited === true
                                        ? true
                                        : userDocs.mobile.code.toString() !==
                                          updateObject.code.toString(),
                                'mobile.globalMandatoryEdited': userDocs.status === constant.VALID,
                            }),
                            ...((updateObject.code === '' || updateObject.code) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'mobile.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.no === '' || updateObject.no) && {
                                'mobile.no': updateObject.no,
                                'mobile.userEdited':
                                    userDocs.mobile.userEdited === true
                                        ? true
                                        : userDocs.mobile.no.toString() !==
                                          updateObject.no.toString(),
                            }),
                            ...((updateObject.no === '' || updateObject.no) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'mobile.globalMandatoryEdited': true,
                                }),
                        };
                    }
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Staff's basic details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'profileInformation':
                    userUpdateObject = {
                        'verification.profile.basicDetails': true,
                        'verification.profile.profileDetails': true,
                        ...((updateObject.passportNo === '' || updateObject.passportNo) && {
                            'profileDetails.passportNo.value': updateObject.passportNo,
                            'profileDetails.passportNo.edited': edited,
                        }),
                        ...((updateObject.passportNo === '' || updateObject.passportNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.passportNo.globalMandatoryEdited': true,
                            }),
                        ...((updateObject.dob === '' || updateObject.dob) && {
                            'profileDetails.dob.value': updateObject.dob,
                            'profileDetails.dob.edited': edited,
                        }),
                        ...((updateObject.dob === '' || updateObject.dob) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.dob.globalMandatoryEdited': true,
                            }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) && {
                            'profileDetails.nationalityId.value': updateObject.nationalityId,
                            'profileDetails.nationalityId.edited': edited,
                        }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.nationalityId.globalMandatoryEdited': true,
                            }),
                        ...((updateObject._nationality_id === '' ||
                            updateObject._nationality_id) && {
                            'profileDetails._nationality_id.value': updateObject._nationality_id,
                            'profileDetails._nationality_id.edited': edited,
                        }),
                        ...((updateObject._nationality_id === '' || updateObject._nationality_id) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails._nationality_id.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) && {
                                'profileDetails.addressDetails.buildingStreetName.value':
                                    updateObject.addressDetails.buildingStreetName,
                                'profileDetails.addressDetails.buildingStreetName.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.buildingStreetName.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) && {
                                'profileDetails.addressDetails.floorNo.value':
                                    updateObject.addressDetails.floorNo,
                                'profileDetails.addressDetails.floorNo.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.floorNo.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) && {
                                'profileDetails.addressDetails.country.value':
                                    updateObject.addressDetails.country,
                                'profileDetails.addressDetails.country.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.country.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.countryId === '' ||
                                updateObject.addressDetails.countryId) && {
                                'profileDetails.addressDetails.countryId':
                                    updateObject.addressDetails.countryId,
                            }),

                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.districtId === '' ||
                                updateObject.addressDetails.districtId) && {
                                'profileDetails.addressDetails.districtId':
                                    updateObject.addressDetails.districtId,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) && {
                                'profileDetails.addressDetails.district.value':
                                    updateObject.addressDetails.district,
                                'profileDetails.addressDetails.district.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.district.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) && {
                                'profileDetails.addressDetails.city.value':
                                    updateObject.addressDetails.city,
                                'profileDetails.addressDetails.city.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.city.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) && {
                                'profileDetails.addressDetails.zipCode.value':
                                    updateObject.addressDetails.zipCode,
                                'profileDetails.addressDetails.zipCode.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.zipCode.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) && {
                                'profileDetails.addressDetails.unit.value':
                                    updateObject.addressDetails.unit,
                                'profileDetails.addressDetails.unit.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.unit.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeExtension === '' ||
                                updateObject.office.officeExtension) && {
                                'profileDetails.office.officeExtension.value':
                                    updateObject.office.officeExtension,
                                'profileDetails.office.officeExtension.edited': edited,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeExtension === '' ||
                                updateObject.office.officeExtension) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.office.officeExtension.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeRoomNo === '' ||
                                updateObject.office.officeRoomNo) && {
                                'profileDetails.office.officeRoomNo.value':
                                    updateObject.office.officeRoomNo,
                                'profileDetails.office.officeRoomNo.edited': edited,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeRoomNo === '' ||
                                updateObject.office.officeRoomNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.office.officeRoomNo.globalMandatoryEdited': true,
                            }),
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Staff's profile details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'vaccineDetails':
                    userUpdateObject = {
                        'verification.profile.vaccinationDetails': true,
                        vaccineConfiguration: updateObject,
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Staff's vaccination details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'profileVaccineInformation':
                    userUpdateObject = {
                        'verification.profile.profileDetails': true,
                        'verification.profile.vaccinationDetails': true,
                        ...((updateObject.passportNo === '' || updateObject.passportNo) && {
                            'profileDetails.passportNo.value': updateObject.passportNo,
                            'profileDetails.passportNo.edited': edited,
                            'profileDetails.passportNo.globalMandatoryEdited':
                                userDocs.status === constant.VALID,
                        }),
                        ...((updateObject.dob === '' || updateObject.dob) && {
                            'profileDetails.dob.value': updateObject.dob,
                            'profileDetails.dob.edited': edited,
                            'profileDetails.dob.globalMandatoryEdited':
                                userDocs.status === constant.VALID,
                        }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) && {
                            'profileDetails.nationalityId.value': updateObject.nationalityId,
                            'profileDetails.nationalityId.edited': edited,
                            'profileDetails.nationalityId.globalMandatoryEdited':
                                userDocs.status === constant.VALID,
                        }),
                        ...((updateObject._nationality_id === '' ||
                            updateObject._nationality_id) && {
                            'profileDetails._nationality_id': updateObject._nationality_id,
                            'profileDetails._nationality_id.globalMandatoryEdited':
                                userDocs.status === constant.VALID,
                        }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) && {
                                'profileDetails.addressDetails.buildingStreetName.value':
                                    updateObject.addressDetails.buildingStreetName,
                                'profileDetails.addressDetails.buildingStreetName.edited': edited,
                                'profileDetails.addressDetails.buildingStreetName.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) && {
                                'profileDetails.addressDetails.floorNo.value':
                                    updateObject.addressDetails.floorNo,
                                'profileDetails.addressDetails.floorNo.edited': edited,
                                'profileDetails.addressDetails.floorNo.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) && {
                                'profileDetails.addressDetails.country.value':
                                    updateObject.addressDetails.country,
                                'profileDetails.addressDetails.country.edited': edited,
                                'profileDetails.addressDetails.country.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.countryId === '' ||
                                updateObject.addressDetails.countryId) && {
                                'profileDetails.addressDetails.countryId':
                                    updateObject.addressDetails.countryId,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.districtId === '' ||
                                updateObject.addressDetails.districtId) && {
                                'profileDetails.addressDetails.districtId':
                                    updateObject.addressDetails.districtId,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) && {
                                'profileDetails.addressDetails.district.value':
                                    updateObject.addressDetails.district,
                                'profileDetails.addressDetails.district.edited': edited,
                                'profileDetails.addressDetails.district.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) && {
                                'profileDetails.addressDetails.city.value':
                                    updateObject.addressDetails.city,
                                'profileDetails.addressDetails.city.edited': edited,
                                'profileDetails.addressDetails.city.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) && {
                                'profileDetails.addressDetails.zipCode.value':
                                    updateObject.addressDetails.zipCode,
                                'profileDetails.addressDetails.zipCode.edited': edited,
                                'profileDetails.addressDetails.zipCode.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) && {
                                'profileDetails.addressDetails.unit.value':
                                    updateObject.addressDetails.unit,
                                'profileDetails.addressDetails.unit.edited': edited,
                                'profileDetails.addressDetails.unit.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeRoomNo === '' ||
                                updateObject.office.officeExtension) && {
                                'profileDetails.office.officeExtension.value':
                                    updateObject.office.officeExtension,
                                'profileDetails.office.officeExtension.edited': edited,
                                'profileDetails.office.officeExtension.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        ...(updateObject.office &&
                            (updateObject.office.officeRoomNo === '' ||
                                updateObject.office.officeRoomNo) && {
                                'profileDetails.office.officeRoomNo.value':
                                    updateObject.office.officeRoomNo,
                                'profileDetails.office.officeRoomNo.edited': edited,
                                'profileDetails.office.officeRoomNo.globalMandatoryEdited':
                                    userDocs.status === constant.VALID,
                            }),
                        vaccineConfiguration: updateObject.vaccineConfiguration,
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Staff's profile and vaccination details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                default:
                    break;
            }
        } else {
            switch (setMode) {
                case 'password':
                    userUpdateObject = {
                        password: bcrypt.hashSync(newPassword, 10),
                        'verification.password': true,
                        status: forgotPassword ? userDocs.status : constant.PASSWORD_CONFIRMED,
                    };
                    script = `ctx._source.status = '${userUpdateObject.status}'`;
                    if (!forgotPassword) {
                        userUpdateObject.$addToSet = {
                            activityLog: {
                                activity: `Student's password verified successfully`,
                                activityAt: new Date(),
                            },
                        };
                    } else {
                        userUpdateObject.$addToSet = {
                            activityLog: {
                                activity: `Student's password changed successfully`,
                                activityAt: new Date(),
                            },
                        };
                    }
                    break;
                case 'basicDetails':
                    if (isAdmin === true) {
                        userUpdateObject = {
                            ...(updateObject.email && { email: updateObject.email }),
                            ...((updateObject.user_id === '' || updateObject.user_id) && {
                                'user_id.value': updateObject.user_id,
                            }),
                            ...((updateObject.firstName === '' || updateObject.firstName) && {
                                'name.first.value': updateObject.firstName,
                            }),
                            ...((updateObject.middleName === '' || updateObject.middleName) && {
                                'name.middle.value': updateObject.middleName,
                            }),
                            ...((updateObject.lastName === '' || updateObject.lastName) && {
                                'name.last.value': updateObject.lastName,
                            }),
                            ...((updateObject.familyName === '' || updateObject.familyName) && {
                                'name.family.value': updateObject.familyName,
                            }),
                            ...((updateObject.gender === '' || updateObject.gender) && {
                                'gender.value': updateObject.gender,
                            }),
                            ...((updateObject.program === '' || updateObject.program) && {
                                'program.value': updateObject.program,
                                'program._program_id':
                                    programData.findIndex(
                                        (i) => i.name == userDocs.program.value.trim(),
                                    ) != -1
                                        ? programData[
                                              programData.findIndex(
                                                  (i) => i.name === userDocs.program.value.trim(),
                                              )
                                          ]._id
                                        : null,
                            }),
                            ...((updateObject.batch === '' || updateObject.batch) && {
                                'batch.value': updateObject.batch,
                            }),
                            ...((updateObject.enrollmentYear === '' ||
                                updateObject.enrollmentYear) && {
                                'enrollmentYear.value': updateObject.enrollmentYear,
                            }),
                            ...((updateObject.code === '' || updateObject.code) && {
                                'mobile.code': updateObject.code,
                            }),
                            ...((updateObject.no === '' || updateObject.no) && {
                                'mobile.no': updateObject.no,
                            }),
                        };
                    } else {
                        userUpdateObject = {
                            'verification.profile.basicDetails': true,
                            ...(updateObject.email && { email: updateObject.email }),
                            ...((updateObject.user_id === '' || updateObject.user_id) && {
                                'user_id.value': updateObject.user_id,
                                'user_id.edited': edited,
                                'user_id.userEdited':
                                    userDocs.user_id.userEdited === true
                                        ? true
                                        : userDocs.user_id.value !== updateObject.user_id,
                            }),
                            ...((updateObject.user_id === '' || updateObject.user_id) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'user_id.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.firstName === '' || updateObject.firstName) && {
                                'name.first.value': updateObject.firstName,
                                'name.first.edited': edited,
                                'name.first.userEdited':
                                    userDocs.name.first.userEdited === true
                                        ? true
                                        : userDocs.name.first.value !== updateObject.firstName,
                            }),
                            ...((updateObject.firstName === '' || updateObject.firstName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.first.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.middleName === '' || updateObject.middleName) && {
                                'name.middle.value': updateObject.middleName,
                                'name.middle.edited': edited,
                                'name.middle.userEdited':
                                    userDocs.name.middle.userEdited === true
                                        ? true
                                        : userDocs.name.middle.value !== updateObject.middleName,
                            }),
                            ...((updateObject.middleName === '' || updateObject.middleName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.middle.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.lastName === '' || updateObject.lastName) && {
                                'name.last.value': updateObject.lastName,
                                'name.last.edited': edited,
                                'name.last.userEdited':
                                    userDocs.name.last.userEdited === true
                                        ? true
                                        : userDocs.name.last.value !== updateObject.lastName,
                            }),
                            ...((updateObject.lastName === '' || updateObject.lastName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.last.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.familyName === '' || updateObject.familyName) && {
                                'name.family.value': updateObject.familyName,
                                'name.family.edited': edited,
                                'name.family.userEdited':
                                    userDocs.name.family.userEdited === true
                                        ? true
                                        : userDocs.name.family.value !== updateObject.familyName,
                            }),
                            ...((updateObject.familyName === '' || updateObject.familyName) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'name.family.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.gender === '' || updateObject.gender) && {
                                'gender.value': updateObject.gender,
                                'gender.edited': edited,
                                'gender.userEdited':
                                    userDocs.gender.userEdited === true
                                        ? true
                                        : userDocs.gender.value !== updateObject.gender,
                            }),
                            ...((updateObject.gender === '' || updateObject.gender) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'gender.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.code === '' || updateObject.code) && {
                                'mobile.code': updateObject.code,
                                'mobile.userEdited':
                                    userDocs.mobile.userEdited === true
                                        ? true
                                        : userDocs.mobile.code.toString() !==
                                          updateObject.code.toString(),
                            }),
                            ...((updateObject.no === '' || updateObject.no) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'mobile.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.no === '' || updateObject.no) && {
                                'mobile.no': updateObject.no,
                                'mobile.userEdited':
                                    userDocs.mobile.userEdited === true
                                        ? true
                                        : userDocs.mobile.no.toString() !==
                                          updateObject.no.toString(),
                            }),

                            ...((updateObject.program === '' || updateObject.program) && {
                                'program.value': updateObject.program,
                                'program._program_id':
                                    programData.findIndex(
                                        (i) => i.name == userDocs.program.value.trim(),
                                    ) != -1
                                        ? programData[
                                              programData.findIndex(
                                                  (i) => i.name === userDocs.program.value.trim(),
                                              )
                                          ]._id
                                        : null,
                                'program.edited': edited,
                                'program.userEdited':
                                    userDocs.program.userEdited === true
                                        ? true
                                        : userDocs.program.value !== updateObject.program,
                            }),
                            ...((updateObject.batch === '' || updateObject.batch) && {
                                'batch.value': updateObject.batch,
                                'batch.edited': edited,
                                'batch.userEdited':
                                    userDocs.batch.userEdited === true
                                        ? true
                                        : userDocs.batch.value !== updateObject.batch,
                            }),
                            ...((updateObject.batch === '' || updateObject.batch) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'batch.globalMandatoryEdited': true,
                                }),
                            ...((updateObject.enrollmentYear === '' ||
                                updateObject.enrollmentYear) && {
                                'enrollmentYear.value': updateObject.enrollmentYear,
                                'enrollmentYear.edited': edited,
                                'enrollmentYear.userEdited':
                                    userDocs.enrollmentYear.userEdited === true
                                        ? true
                                        : String(userDocs.enrollmentYear.value.getFullYear()) !==
                                          String(updateObject.enrollmentYear),
                            }),
                            ...((updateObject.enrollmentYear === '' ||
                                updateObject.enrollmentYear) &&
                                userDocs.status === constant.VALID &&
                                isAdmin !== true && {
                                    'enrollmentYear.globalMandatoryEdited': true,
                                }),
                        };
                    }
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Student's basic details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'profileInformation':
                    userUpdateObject = {
                        'verification.profile.basicDetails': true,
                        'verification.profile.profileDetails': true,
                        ...((updateObject.passportNo === '' || updateObject.passportNo) && {
                            'profileDetails.passportNo.value': updateObject.passportNo,
                            'profileDetails.passportNo.edited': edited,
                        }),
                        ...((updateObject.passportNo === '' || updateObject.passportNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.passportNo.globalMandatoryEdited': true,
                            }),
                        ...((updateObject.dob === '' || updateObject.dob) && {
                            'profileDetails.dob.value': updateObject.dob,
                            'profileDetails.dob.edited': edited,
                        }),
                        ...((updateObject.dob === '' || updateObject.dob) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.dob.globalMandatoryEdited': true,
                            }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) && {
                            'profileDetails.nationalityId.value': updateObject.nationalityId,
                            'profileDetails.nationalityId.edited': edited,
                        }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.nationalityId.globalMandatoryEdited': true,
                            }),
                        ...((updateObject._nationality_id === '' ||
                            updateObject._nationality_id) && {
                            'profileDetails._nationality_id.value': updateObject._nationality_id,
                            'profileDetails._nationality_id.edited': edited,
                        }),
                        ...((updateObject._nationality_id === '' || updateObject._nationality_id) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails._nationality_id.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) && {
                                'profileDetails.addressDetails.buildingStreetName.value':
                                    updateObject.addressDetails.buildingStreetName,
                                'profileDetails.addressDetails.buildingStreetName.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.buildingStreetName.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) && {
                                'profileDetails.addressDetails.floorNo.value':
                                    updateObject.addressDetails.floorNo,
                                'profileDetails.addressDetails.floorNo.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.floorNo.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) && {
                                'profileDetails.addressDetails.country.value':
                                    updateObject.addressDetails.country,
                                'profileDetails.addressDetails.country.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.country.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.countryId === '' ||
                                updateObject.addressDetails.countryId) && {
                                'profileDetails.addressDetails.countryId':
                                    updateObject.addressDetails.countryId,
                            }),

                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.districtId === '' ||
                                updateObject.addressDetails.districtId) && {
                                'profileDetails.addressDetails.districtId':
                                    updateObject.addressDetails.districtId,
                            }),

                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) && {
                                'profileDetails.addressDetails.district.value':
                                    updateObject.addressDetails.district,
                                'profileDetails.addressDetails.district.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.district.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) && {
                                'profileDetails.addressDetails.city.value':
                                    updateObject.addressDetails.city,
                                'profileDetails.addressDetails.city.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.city.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) && {
                                'profileDetails.addressDetails.zipCode.value':
                                    updateObject.addressDetails.zipCode,
                                'profileDetails.addressDetails.zipCode.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.zipCode.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) && {
                                'profileDetails.addressDetails.unit.value':
                                    updateObject.addressDetails.unit,
                                'profileDetails.addressDetails.unit.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.addressDetails.unit.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.parentGuardianPhoneNo) && {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.value':
                                    updateObject.otherContactDetails.parentGuardianPhoneNo,
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.edited':
                                    edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.parentGuardianPhoneNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianEmailId === '' ||
                                updateObject.otherContactDetails.parentGuardianEmailId) && {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.value':
                                    updateObject.otherContactDetails.parentGuardianEmailId,
                                'profileDetails.otherContactDetails.parentGuardianEmailId.edited':
                                    edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianEmailId === '' ||
                                updateObject.otherContactDetails.parentGuardianEmailId) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatoryEdited': true,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.spouseGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.spouseGuardianPhoneNo) && {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.value':
                                    updateObject.otherContactDetails.spouseGuardianPhoneNo,
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.edited':
                                    edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.spouseGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.spouseGuardianPhoneNo) &&
                            userDocs.status === constant.VALID &&
                            isAdmin !== true && {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatoryEdited': true,
                            }),
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Student's profile details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'vaccineDetails':
                    userUpdateObject = {
                        'verification.profile.vaccinationDetails': true,
                        vaccineConfiguration: updateObject,
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Student's vaccination details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                case 'profileVaccineInformation':
                    userUpdateObject = {
                        'verification.profile.profileDetails': true,
                        'verification.profile.vaccinationDetails': true,
                        ...((updateObject.passportNo === '' || updateObject.passportNo) && {
                            'profileDetails.passportNo.value': updateObject.passportNo,
                            'profileDetails.passportNo.edited': edited,
                        }),
                        ...((updateObject.dob === '' || updateObject.dob) && {
                            'profileDetails.dob.value': updateObject.dob,
                            'profileDetails.dob.edited': edited,
                        }),
                        ...((updateObject.nationalityId === '' || updateObject.nationalityId) && {
                            'profileDetails.nationalityId.value': updateObject.nationalityId,
                            'profileDetails.nationalityId.edited': edited,
                        }),
                        ...((updateObject._nationality_id === '' ||
                            updateObject._nationality_id) && {
                            'profileDetails._nationality_id': updateObject._nationality_id,
                        }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.buildingStreetName === '' ||
                                updateObject.addressDetails.buildingStreetName) && {
                                'profileDetails.addressDetails.buildingStreetName.value':
                                    updateObject.addressDetails.buildingStreetName,
                                'profileDetails.addressDetails.buildingStreetName.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.floorNo === '' ||
                                updateObject.addressDetails.floorNo) && {
                                'profileDetails.addressDetails.floorNo.value':
                                    updateObject.addressDetails.floorNo,
                                'profileDetails.addressDetails.floorNo.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.country === '' ||
                                updateObject.addressDetails.country) && {
                                'profileDetails.addressDetails.country.value':
                                    updateObject.addressDetails.country,
                                'profileDetails.addressDetails.country.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.countryId === '' ||
                                updateObject.addressDetails.countryId) && {
                                'profileDetails.addressDetails.countryId':
                                    updateObject.addressDetails.countryId,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.districtId === '' ||
                                updateObject.addressDetails.districtId) && {
                                'profileDetails.addressDetails.districtId':
                                    updateObject.addressDetails.districtId,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.district === '' ||
                                updateObject.addressDetails.district) && {
                                'profileDetails.addressDetails.district.value':
                                    updateObject.addressDetails.district,
                                'profileDetails.addressDetails.district.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.city === '' ||
                                updateObject.addressDetails.city) && {
                                'profileDetails.addressDetails.city.value':
                                    updateObject.addressDetails.city,
                                'profileDetails.addressDetails.city.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.zipCode === '' ||
                                updateObject.addressDetails.zipCode) && {
                                'profileDetails.addressDetails.zipCode.value':
                                    updateObject.addressDetails.zipCode,
                                'profileDetails.addressDetails.zipCode.edited': edited,
                            }),
                        ...(updateObject.addressDetails &&
                            (updateObject.addressDetails.unit === '' ||
                                updateObject.addressDetails.unit) && {
                                'profileDetails.addressDetails.unit.value':
                                    updateObject.addressDetails.unit,
                                'profileDetails.addressDetails.unit.edited': edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.parentGuardianPhoneNo) && {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.value':
                                    updateObject.otherContactDetails.parentGuardianPhoneNo,
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.edited':
                                    edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.parentGuardianEmailId === '' ||
                                updateObject.otherContactDetails.parentGuardianEmailId) && {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.value':
                                    updateObject.otherContactDetails.parentGuardianEmailId,
                                'profileDetails.otherContactDetails.parentGuardianEmailId.edited':
                                    edited,
                            }),
                        ...(updateObject.otherContactDetails &&
                            (updateObject.otherContactDetails.spouseGuardianPhoneNo === '' ||
                                updateObject.otherContactDetails.spouseGuardianPhoneNo) && {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.value':
                                    updateObject.otherContactDetails.spouseGuardianPhoneNo,
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.edited':
                                    edited,
                            }),
                        vaccineConfiguration: updateObject.vaccineConfiguration,
                    };
                    userUpdateObject.$addToSet = {
                        activityLog: {
                            activity: `Student's profile and vaccination details updated successfully`,
                            activityAt: new Date(),
                        },
                    };
                    break;
                default:
                    break;
            }
        }
        if (script !== null) {
            await updateRecords({
                indexName: USER_INSTITUTIONS,
                query: {
                    bool: {
                        should: [{ match: { email: userDocs.email } }],
                        must: [{ term: { _institution_id: String(userDocs._institution_id) } }],
                    },
                },
                script,
            });
        }
        if (edited) {
            userUpdateObject.validationPendingStatus = userDocs.status === constant.VALID;
        }
        if (userUpdateObject !== {}) {
            const userUpdate = await userModel.updateOne(userQuery, userUpdateObject);
            if (!userUpdate)
                return {
                    statusCode: 400,
                    message: 'FAILED_TO_UPDATE',
                };
        }
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Only for Dev Propose
const userReSet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { email } = params;
        const userQuery = {
            email: email.toLowerCase(),
            isDeleted: false,
        };
        const userUpdateObject = {
            password: bcrypt.hashSync('123456789', 10),
            verification: {
                email: false,
                password: false,
                mobile: false,
                profile: {
                    basicDetails: false,
                    vaccinationDetails: false,
                },
                document: false,
                face: false,
            },
            status: constant.IMPORTED,
        };
        const userUpdate = await userModel.updateOne(userQuery, userUpdateObject);
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// get document configuration details by using _institution_id
const getDocumentConfiguration = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { _institution_id } = params;

        const settings = await settingsModel
            .findOne({ _institution_id }, { globalConfiguration: 1 })
            .lean();
        if (!settings.globalConfiguration.staffUserManagement.documentConfiguration)
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: settings.globalConfiguration.staffUserManagement.documentConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// upload individual file and return to URL Location
const documentUpload = async ({ body = {} }) => {
    try {
        const { file } = body;
        return {
            statusCode: 200,
            data: { url: file, signedUrl: await getSignedURL(file) },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// upload all users uploaded document
const usersDocumentUpload = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const {
            updateObject,
            isReSubmitted,
            isRegistered,
            isGlobalMandatoryStatus,
            isRejectHandlingStatus,
        } = body;
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            isDeleted: false,
        };
        const userProjection = {
            _id: 1,
            user_type: 1,
            status: 1,
            isRegistered: 1,
        };
        const userDetails = await userModel.findOne(userQuery, userProjection);
        if (!userDetails)
            return {
                statusCode: 401,
                message: 'USER_ID_NOT_FOUND',
            };
        const userUpdateObject = {
            'verification.document': true,
            status: isRegistered
                ? userDetails.status
                : isReSubmitted
                ? constant.RESUBMITTED
                : constant.PROFILE_UPDATED,
            uploadedDocuments: updateObject,
            isRegistered: isRegistered === true ? isRegistered : false,
            validationPendingStatus: userDetails.status === constant.VALID,
            ...(isRejectHandlingStatus === true && { isRejectHandlingStatus }),
            ...(isGlobalMandatoryStatus === true && {
                isGlobalMandatoryStatus,
                'name.first.status': '',
                'name.last.status': '',
                'name.middle.status': '',
                'name.family.status': '',
                'user_id.status': '',
                'gender.status': '',
                'mobile.status': '',
                'profileDetails.passportNo.status': '',
                'profileDetails.dob.status': '',
                'profileDetails.nationalityId.status': '',
                'profileDetails._nationality_id.status': '',
                'profileDetails.office.officeRoomNo.status': '',
                'profileDetails.office.officeExtension.status': '',
                'profileDetails.addressDetails.buildingStreetName.status': '',
                'profileDetails.addressDetails.floorNo.status': '',
                'profileDetails.addressDetails.country.status': '',
                'profileDetails.addressDetails.district.status': '',
                'profileDetails.addressDetails.city.status': '',
                'profileDetails.addressDetails.zipCode.status': '',
                'profileDetails.otherContactDetails.parentGuardianPhoneNo.status': '',
                'profileDetails.otherContactDetails.parentGuardianEmailId.status': '',
                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.status': '',
                'vaccineConfiguration.$[].status': '',
                'biometricData.face.status': '',
                'enrollmentYear.status': '',
                'program.status': '',
                'batch.status': '',
            }),
        };
        if (!isReSubmitted) {
            userUpdateObject.$addToSet = {
                activityLog: {
                    activity:
                        userDetails.user_type === 'staff'
                            ? `Staff's document uploaded successfully`
                            : `Student's document uploaded successfully`,
                    activityAt: new Date(),
                },
            };
        } else {
            userUpdateObject.$addToSet = {
                activityLog: {
                    activity: `ReSubmitted`,
                    activityAt: new Date(),
                },
            };
        }
        const script = `ctx._source.status = '${userUpdateObject.status}'`;
        const userUpdate = await userModel.updateOne(
            { _id: convertToMongoObjectId(_user_id) },
            userUpdateObject,
        );
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userSingleDocumentUpload = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { updateObject } = body;
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            'uploadedDocuments.document.url._id': convertToMongoObjectId(
                updateObject.document.url._url_id,
            ),
            'uploadedDocuments.document._id': convertToMongoObjectId(
                updateObject.document._document_id,
            ),
            'uploadedDocuments._id': convertToMongoObjectId(updateObject._category_id),
            isDeleted: false,
        };
        const userProjection = {
            _id: 1,
        };
        const userDetails = await userModel.findOne(userQuery, userProjection);
        let doc;
        if (userDetails) {
            doc = await userModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_user_id) },
                {
                    $set: {
                        'uploadedDocuments.$[i].document.$[j].url.$[k].value':
                            updateObject.document.url.value,
                    },
                },
                {
                    arrayFilters: [
                        { 'i._id': convertToMongoObjectId(updateObject._category_id) },
                        { 'j._id': convertToMongoObjectId(updateObject.document._document_id) },
                        { 'k._id': convertToMongoObjectId(updateObject.document.url._url_id) },
                    ],
                    new: true,
                },
            );
        } else {
            doc = await userModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(_user_id) },
                {
                    $addToSet: {
                        'uploadedDocuments.$[i].document.$[j].url': {
                            value: updateObject.document.url.value,
                        },
                    },
                },
                {
                    arrayFilters: [
                        { 'i._id': convertToMongoObjectId(updateObject._category_id) },
                        { 'j._id': convertToMongoObjectId(updateObject.document._document_id) },
                    ],
                },
            );
        }
        if (!doc)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// get all documents by using user id
const getUsersDocumentUpload = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            isDeleted: false,
        };
        const userProjection = {
            uploadedDocuments: 1,
        };
        const users = await userModel.findOne(userQuery, userProjection).lean();
        if (!users && !users.uploadedDocuments)
            return {
                statusCode: 400,
                message: 'DS_GET_FAILED',
            };
        const documentUploaded = [];
        for (documentCategory of users.uploadedDocuments) {
            const signedDocuments = [];
            for (documents of documentCategory.document) {
                const urlLists = [];
                for (Url of documents.url) {
                    urlLists.push({
                        _url_id: Url._id,
                        url: Url.value,
                        signedUrl: await getSignedURL(Url.value),
                        flagged: Url.flagged,
                        edited: Url.edited,
                        status: Url.status,
                        globalMandatoryEdited: Url.globalMandatoryEdited,
                    });
                }
                signedDocuments.push({
                    _id: documents._id,
                    _document_id: documents._document_id,
                    globalMandatory: documents.globalMandatory,
                    urls: urlLists,
                });
            }
            documentUploaded.push({
                _id: documentCategory._id,
                _category_id: documentCategory._category_id,
                document: signedDocuments,
            });
        }
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: documentUploaded,
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const verifyUserData = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { userData } = body;
        let duplicateData = [];
        const invalidData = [];
        const existsInDb = [];

        const setting = await settingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                },
            )
            .lean();
        const settingsGet =
            setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.find(
                (element) => element.language === 'en',
            );
        const mandatoryKey = [];
        const mandatoryElement = settingsGet.basicDetails.filter((element) => {
            if (element.isMandatory === true) {
                mandatoryKey.push(element.mappingKey);
            }
        });
        const duplicateMobile = userData
            .map((v) => v.mobile)
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmployeeId = userData
            .map((v) => {
                if (v.employeeId) {
                    return v.employeeId.toLowerCase();
                }
            })
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmail = userData
            .map((v) => v.email.toLowerCase())
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        let duplicateAllWithStatus = [];
        let duplicateEmpIdsWithStatus = [];
        let duplicateMobilesAndEmpIdsWithStatus = [];
        let duplicateEmailsAndEmpIdsWithStatus = [];

        if (duplicateEmployeeId.length !== 0 && duplicateEmployeeId[0]) {
            const duplicateAll = userData.filter((obj) => {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
                );
            });
            duplicateAllWithStatus = duplicateAll.map((v) => ({
                ...v,
                status: ['mobile', 'email', 'employeeId'],
            }));
            const duplicateEmpIds = userData.filter((obj) => {
                return (
                    obj.employeeId !== '' &&
                    !duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
                );
            });
            duplicateEmpIdsWithStatus = duplicateEmpIds.map((v) => ({
                ...v,
                status: ['employeeId'],
            }));
            const duplicateMobilesAndEmpIds = userData.filter((obj) => {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
                );
            });
            duplicateMobilesAndEmpIdsWithStatus = duplicateMobilesAndEmpIds.map((v) => ({
                ...v,
                status: ['mobile', 'employeeId'],
            }));
            const duplicateEmailsAndEmpIds = userData.filter((obj) => {
                return (
                    !duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
                );
            });
            duplicateEmailsAndEmpIdsWithStatus = duplicateEmailsAndEmpIds.map((v) => ({
                ...v,
                status: ['email', 'employeeId'],
            }));
        }
        const duplicateMobiles = userData.filter((obj) => {
            if (!obj.employeeId) {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                duplicateMobile.includes(obj.mobile) &&
                !duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
            );
        });
        const duplicateMobilesWithStatus = duplicateMobiles.map((v) => ({
            ...v,
            status: ['mobile'],
        }));
        const duplicateEmails = userData.filter((obj) => {
            if (!obj.employeeId) {
                return (
                    !duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                !duplicateMobile.includes(obj.mobile) &&
                duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
            );
        });

        const duplicateEmailsWithStatus = duplicateEmails.map((v) => ({
            ...v,
            status: ['email'],
        }));
        const duplicateMobilesAndEmail = userData.filter((obj) => {
            if (!obj.employeeId) {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                duplicateMobile.includes(obj.mobile) &&
                duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateEmployeeId.includes(obj.employeeId.toLowerCase())
            );
        });
        const duplicateMobilesAndEmailWithStatus = duplicateMobilesAndEmail.map((v) => ({
            ...v,
            status: ['mobile', 'email'],
        }));
        duplicateData = [
            ...duplicateEmailsAndEmpIdsWithStatus,
            ...duplicateMobilesAndEmpIdsWithStatus,
            ...duplicateMobilesAndEmailWithStatus,
            ...duplicateEmpIdsWithStatus,
            ...duplicateEmailsWithStatus,
            ...duplicateMobilesWithStatus,
            ...duplicateAllWithStatus,
        ];
        const duplicateMobileForDb = duplicateData
            .map((v) => v.mobile)
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmployeeIdForDb = duplicateData
            .map((v) => {
                if (v.employeeId) {
                    return v.employeeId.toLowerCase();
                }
            })
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmailForDb = duplicateData
            .map((v) => v.email.toLowerCase())
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const userExists = await userModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { _id: 1, user_id: 1, email: 1, 'mobile.no': 1 },
            )
            .lean();
        userData.forEach((user, index) => {
            const { employeeId, email, mobile } = user;
            //invalid data
            const status = validUser(user, mandatoryKey, 'staff');
            if (status.length) {
                if (
                    (!employeeId &&
                        !duplicateMobileForDb.includes(mobile) &&
                        !duplicateEmailForDb.includes(email.toLowerCase())) ||
                    (employeeId &&
                        !duplicateMobileForDb.includes(mobile) &&
                        !duplicateEmployeeIdForDb.includes(employeeId.toLowerCase()) &&
                        !duplicateEmailForDb.includes(email.toLowerCase()))
                ) {
                    invalidData.push({ ...user, status });
                }
            }
            userData[index].status = [];
            //duplicates in db
            if (
                (!employeeId &&
                    !duplicateMobileForDb.includes(mobile) &&
                    !duplicateEmailForDb.includes(email.toLowerCase())) ||
                (employeeId &&
                    !duplicateMobileForDb.includes(mobile) &&
                    !duplicateEmployeeIdForDb.includes(employeeId.toLowerCase()) &&
                    !duplicateEmailForDb.includes(email.toLowerCase()))
            ) {
                userExists.find((dbUser) => {
                    if (
                        dbUser.user_id !== null &&
                        ((typeof dbUser.user_id === 'string' && dbUser.user_id !== null) ||
                            (typeof dbUser.user_id === 'object' &&
                                dbUser.user_id.value !== null)) &&
                        employeeId &&
                        ((typeof dbUser.user_id === 'string' &&
                            dbUser.user_id.toLowerCase() === employeeId.toLowerCase()) ||
                            (typeof dbUser.user_id === 'object' &&
                                dbUser.user_id.value.toLowerCase() === employeeId.toLowerCase()))
                    ) {
                        userData[index].status.push('employeeId');
                        existsInDb.push(user);
                    }
                    if (dbUser.mobile.no === mobile) {
                        if (userData[index].status.length) {
                            userData[index].status.push('mobile');
                        } else {
                            userData[index].status.push('mobile');
                            existsInDb.push(user);
                        }
                    }
                    if (dbUser.email.toLowerCase() === email.toLowerCase()) {
                        if (userData[index].status.length) {
                            userData[index].status.push('email');
                        } else {
                            userData[index].status.push('email');
                            existsInDb.push(user);
                        }
                    }
                });
            }
        });
        return { statusCode: 200, data: { duplicateData, invalidData, existsInDb } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const verifyStudentData = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const programModel = getModel(tenantURL, constant.PROGRAM, programInputSchema);
        const { userData } = body;
        let duplicateData = [];
        const invalidData = [];
        const existsInDb = [];

        const setting = await settingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                },
            )
            .lean();
        const programData = await programModel.find(
            { isDeleted: false, isActive: true },
            { _id: 1, name: 1, terms: 1 },
        );
        const settingsGet =
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration.find(
                (element) => element.language === 'en',
            );
        const mandatoryKey = [];
        const mandatoryElement = settingsGet.basicDetails.filter((element) => {
            if (element.isMandatory === true) {
                mandatoryKey.push(element.mappingKey);
            }
        });
        const duplicateMobile = userData
            .map((v) => v.mobile)
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateAcademicNo = userData
            .map((v) => {
                if (v.academicNo) {
                    return v.academicNo.toLowerCase();
                }
            })
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmail = userData
            .map((v) => v.email.toLowerCase())
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        let duplicateAllWithStatus = [];
        let duplicateEmpIdsWithStatus = [];
        let duplicateMobilesAndEmpIdsWithStatus = [];
        let duplicateEmailsAndEmpIdsWithStatus = [];

        if (duplicateAcademicNo.length !== 0 && duplicateAcademicNo[0]) {
            const duplicateAll = userData.filter((obj) => {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
                );
            });
            duplicateAllWithStatus = duplicateAll.map((v) => ({
                ...v,
                status: ['mobile', 'email', 'academicNo'],
            }));
            const duplicateEmpIds = userData.filter((obj) => {
                return (
                    obj.academicNo !== '' &&
                    !duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
                );
            });
            duplicateEmpIdsWithStatus = duplicateEmpIds.map((v) => ({
                ...v,
                status: ['academicNo'],
            }));
            const duplicateMobilesAndEmpIds = userData.filter((obj) => {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
                );
            });
            duplicateMobilesAndEmpIdsWithStatus = duplicateMobilesAndEmpIds.map((v) => ({
                ...v,
                status: ['mobile', 'academicNo'],
            }));
            const duplicateEmailsAndEmpIds = userData.filter((obj) => {
                return (
                    !duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase()) &&
                    duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
                );
            });
            duplicateEmailsAndEmpIdsWithStatus = duplicateEmailsAndEmpIds.map((v) => ({
                ...v,
                status: ['email', 'academicNo'],
            }));
        }
        const duplicateMobiles = userData.filter((obj) => {
            if (!obj.academicNo) {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    !duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                duplicateMobile.includes(obj.mobile) &&
                !duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
            );
        });
        const duplicateMobilesWithStatus = duplicateMobiles.map((v) => ({
            ...v,
            status: ['mobile'],
        }));
        const duplicateEmails = userData.filter((obj) => {
            if (!obj.academicNo) {
                return (
                    !duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                !duplicateMobile.includes(obj.mobile) &&
                duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
            );
        });

        const duplicateEmailsWithStatus = duplicateEmails.map((v) => ({
            ...v,
            status: ['email'],
        }));
        const duplicateMobilesAndEmail = userData.filter((obj) => {
            if (!obj.academicNo) {
                return (
                    duplicateMobile.includes(obj.mobile) &&
                    duplicateEmail.includes(obj.email.toLowerCase())
                );
            }
            return (
                duplicateMobile.includes(obj.mobile) &&
                duplicateEmail.includes(obj.email.toLowerCase()) &&
                !duplicateAcademicNo.includes(obj.academicNo.toLowerCase())
            );
        });
        const duplicateMobilesAndEmailWithStatus = duplicateMobilesAndEmail.map((v) => ({
            ...v,
            status: ['mobile', 'email'],
        }));
        duplicateData = [
            ...duplicateEmailsAndEmpIdsWithStatus,
            ...duplicateMobilesAndEmpIdsWithStatus,
            ...duplicateMobilesAndEmailWithStatus,
            ...duplicateEmpIdsWithStatus,
            ...duplicateEmailsWithStatus,
            ...duplicateMobilesWithStatus,
            ...duplicateAllWithStatus,
        ];
        const duplicateMobileForDb = duplicateData
            .map((v) => v.mobile)
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateAcademicNoForDb = duplicateData
            .map((v) => {
                if (v.academicNo) {
                    return v.academicNo.toLowerCase();
                }
            })
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const duplicateEmailForDb = duplicateData
            .map((v) => v.email.toLowerCase())
            .filter((v, i, vIds) => vIds.indexOf(v) !== i);
        const userExists = await userModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { _id: 1, user_id: 1, email: 1, 'mobile.no': 1 },
            )
            .lean();
        userData.forEach((user, index) => {
            const { academicNo, email, mobile } = user;
            //invalid data
            const status = validUser(user, mandatoryKey, 'student', programData);
            if (status.length) {
                if (
                    (!academicNo &&
                        !duplicateMobileForDb.includes(mobile) &&
                        !duplicateEmailForDb.includes(email.toLowerCase())) ||
                    (academicNo &&
                        !duplicateMobileForDb.includes(mobile) &&
                        !duplicateAcademicNoForDb.includes(academicNo.toLowerCase()) &&
                        !duplicateEmailForDb.includes(email.toLowerCase()))
                ) {
                    invalidData.push({ ...user, status });
                }
            }
            userData[index].status = [];
            //duplicates in db
            if (
                (!academicNo &&
                    !duplicateMobileForDb.includes(mobile) &&
                    !duplicateEmailForDb.includes(email.toLowerCase())) ||
                (academicNo &&
                    !duplicateMobileForDb.includes(mobile) &&
                    !duplicateAcademicNoForDb.includes(academicNo.toLowerCase()) &&
                    !duplicateEmailForDb.includes(email.toLowerCase()))
            ) {
                userExists.find((dbUser) => {
                    if (
                        dbUser.user_id !== null &&
                        ((typeof dbUser.user_id === 'string' && dbUser.user_id !== null) ||
                            (typeof dbUser.user_id === 'object' &&
                                dbUser.user_id.value !== null)) &&
                        academicNo &&
                        ((typeof dbUser.user_id === 'string' &&
                            dbUser.user_id.toLowerCase() === academicNo.toLowerCase()) ||
                            (typeof dbUser.user_id === 'object' &&
                                dbUser.user_id.value.toLowerCase() === academicNo.toLowerCase()))
                    ) {
                        userData[index].status.push('academicNo');
                        existsInDb.push(user);
                    }
                    if (dbUser.mobile.no === mobile) {
                        if (userData[index].status.length) {
                            userData[index].status.push('mobile');
                        } else {
                            userData[index].status.push('mobile');
                            existsInDb.push(user);
                        }
                    }
                    if (dbUser.email.toLowerCase() === email.toLowerCase()) {
                        if (userData[index].status.length) {
                            userData[index].status.push('email');
                        } else {
                            userData[index].status.push('email');
                            existsInDb.push(user);
                        }
                    }
                });
            }
        });
        return { statusCode: 200, data: { duplicateData, invalidData, existsInDb } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//get biometric configuration by using institution id
const getUsersBiometricConfiguration = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { _institution_id } = params;

        const settings = await settingsModel
            .findOne({ _institution_id }, { globalConfiguration: 1 })
            .lean();
        if (!settings.globalConfiguration.staffUserManagement.biometricConfiguration)
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: settings.globalConfiguration.staffUserManagement.biometricConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// upload biometric images
const usersBiometricUpload = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { updateObject, flagged, edited } = body;
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            isDeleted: false,
        };
        const userProjection = {
            _id: 1,
            user_type: 1,
            status: 1,
        };
        const userDetails = await userModel.findOne(userQuery, userProjection);
        if (!userDetails)
            return {
                statusCode: 401,
                message: 'USER_ID_NOT_FOUND',
            };
        const userUpdateObject = {
            'verification.face': true,
            biometricData: {
                'face.value': updateObject,
                'face.flagged': flagged,
                'face.edited': edited,
                ...(userDetails.status === constant.VALID && {
                    'face.globalMandatoryEdited': true,
                }),
            },
        };
        userUpdateObject.$addToSet = {
            activityLog: {
                activity:
                    userDetails.user_type === 'staff'
                        ? `Staff's biometric face uploaded successfully`
                        : `Student's biometric face uploaded successfully`,
                activityAt: new Date(),
            },
        };
        const userUpdate = await userModel.updateOne(
            { _id: convertToMongoObjectId(_user_id) },
            userUpdateObject,
        );
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userRegistrationSkip = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;
        const userUpdate = await userModel.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            { 'verification.skip': true },
        );
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//get all biometric images by using user id
const getUsersBiometricUpload = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            isDeleted: false,
        };
        const userProjection = {
            'biometricData.face': 1,
        };
        const users = await userModel.findOne(userQuery, userProjection).lean();
        if (!users && !users.biometricData && !users.biometricData.face)
            return {
                statusCode: 400,
                message: 'DS_GET_FAILED',
            };
        const biometricUploaded = [];
        for (biometricFace of users.biometricData.face.value) {
            biometricUploaded.push({
                url: biometricFace,
                signedUrl: await getSignedURL(biometricFace),
            });
        }
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: {
                biometricUploaded,
                flag: users.biometricData.face.flagged,
                edit: users.biometricData.face.edited,
                status: users.biometricData.face.status,
                globalMandatoryEdited: users.biometricData.face.globalMandatoryEdited,
            },
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listUsers = async ({ headers = {}, query = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const {
            tab,
            sortName,
            sortId,
            searchKey,
            subTab,
            userType,
            gender,
            program,
            batch,
            yearFrom,
            yearTo,
        } = query;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { limit, pageNo, skip } = getPaginationValues(query);
        let dbQuery = {
            isActive: true,
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userType,
        };
        let sort = { createdAt: -1 };
        if (gender) {
            dbQuery = { ...dbQuery, 'gender.value': gender };
        }
        if (program) {
            dbQuery = { ...dbQuery, 'program.value': { $in: program } };
        }
        if (batch) {
            dbQuery = { ...dbQuery, 'batch.value': batch };
        }
        if (yearFrom && yearTo) {
            dbQuery = {
                ...dbQuery,
                'enrollmentYear.value': {
                    $gte: new Date(yearFrom),
                    $lte: new Date(yearTo),
                },
            };
        }
        if (yearFrom && !yearTo) {
            dbQuery = {
                ...dbQuery,
                'enrollmentYear.value': {
                    $gte: new Date(yearFrom),
                },
            };
        }
        if (!yearFrom && yearTo) {
            dbQuery = {
                ...dbQuery,
                'enrollmentYear.value': {
                    $lte: new Date(yearTo),
                },
            };
        }

        if (tab === constant.USER_STATUS.REGISTERED) {
            dbQuery = {
                ...dbQuery,
                $or: [{ status: constant.VALID }, { status: constant.COMPLETED }],
            };
        } else if (tab === constant.USER_STATUS.INACTIVE) {
            dbQuery = {
                _institution_id: convertToMongoObjectId(_institution_id),
                user_type: userType,
                isActive: false,
                isDeleted: false,
                $or: [{ isActive: false }],
            };
        } else if (tab === constant.USER_STATUS.REGISTRATION_PENDING) {
            if (subTab === constant.PENDING) {
                dbQuery = {
                    ...dbQuery,
                    $or: [{ status: constant.PROFILE_UPDATED }, { status: constant.RESUBMITTED }],
                };
            } else if (subTab === constant.INVALID) {
                dbQuery = {
                    ...dbQuery,
                    status: constant.INVALID,
                    $or: [{ status: constant.INVALID }],
                };
            } else if (subTab === constant.USER_STATUS.REQUEST_SENT) {
                dbQuery = {
                    ...dbQuery,
                    $or: [{ status: constant.IMPORTED }, { status: constant.PASSWORD_CONFIRMED }],
                };
            } else if (subTab === constant.EXPIRED) {
                dbQuery = {
                    ...dbQuery,
                    'verification.email': { $ne: true },
                    'verification.password': { $ne: true },
                    status: constant.EXPIRED,
                    $or: [{ status: constant.EXPIRED }],
                };
            } else {
                dbQuery = {
                    ...dbQuery,
                    isActive: true,
                    isDeleted: false,
                    status: 'invited',
                    $or: [
                        { 'verification.data': constant.PENDING },
                        { 'verification.data': constant.CORRECT },
                        { 'verification.data': constant.CORRECTION_REQUIRED },
                        { 'verification.email': false },
                        { 'verification.mobile': false },
                    ],
                    $expr: {
                        $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72],
                    },
                };
            }
        }

        if (searchKey && searchKey.length) {
            const word = searchKey.split(/\s+/);
            if (word.length < 2) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { $or: [...dbQuery.$or] },
                        {
                            $or: [
                                { 'name.first.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.middle.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.last.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.family.value': { $regex: searchKey, $options: 'i' } },
                                { email: { $regex: searchKey, $options: 'i' } },
                                { 'user_id.value': { $regex: searchKey, $options: 'i' } },
                                { 'gender.value': { $regex: searchKey, $options: 'i' } },
                            ],
                        },
                    ],
                };
                delete dbQuery.$or;
            } else if (word.length === 2) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { $or: [...dbQuery.$or] },
                        {
                            $and: [
                                {
                                    $or: [
                                        { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                        { 'name.middle.value': { $regex: word[0], $options: 'i' } },
                                    ],
                                },
                                {
                                    $or: [
                                        { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                        { 'name.last.value': { $regex: word[1], $options: 'i' } },
                                        { 'name.family.value': { $regex: word[1], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
                delete dbQuery.$or;
            } else if (word.length === 3) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { $or: [...dbQuery.$or] },
                        {
                            $and: [
                                { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                {
                                    $or: [
                                        { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                        { 'name.family.value': { $regex: word[2], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
                delete dbQuery.$or;
            } else if (word.length === 4) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { $or: [...dbQuery.$or] },
                        {
                            $and: [
                                { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                {
                                    $or: [
                                        { 'name.family.value': { $regex: word[3], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
                delete dbQuery.$or;
            }
        }
        if (sortName) {
            if (sortName === 'recentlyAdded') {
                sort = { ...sort, createdAt: -1 };
            } else if (sortName === 'asc') {
                sort = { 'name.first.value': 1 };
            } else if (sortName === 'des') {
                sort = { 'name.first.value': -1 };
            }
        }
        if (sortId) {
            if (sortId === 'recentlyAdded') {
                sort = { ...sort, createdAt: -1 };
            } else if (sortId === 'asc') {
                sort = { 'user_id.value': 1 };
            } else if (sortId === 'des') {
                sort = { 'user_id.value': -1 };
            }
        }
        const invalidUser = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
            user_type: userType,
            $and: [{ status: { $ne: constant.VALID } }, { status: { $ne: constant.COMPLETED } }],
        };
        const inactiveUser = {
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userType,
            isActive: false,
            isDeleted: false,
        };
        const registeredUser = {
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userType,
            isActive: true,
            isDeleted: false,
            $or: [{ status: constant.VALID }, { status: constant.COMPLETED }],
        };
        const invalidUserCount = await userModel.find(invalidUser).countDocuments();
        const inactiveUserCount = await userModel.find(inactiveUser).countDocuments();
        const registeredUserCount = await userModel.find(registeredUser).countDocuments();
        const subTabCount = await userModel.find(dbQuery).countDocuments();
        const userProject = {
            email: 1,
            'name.first.value': 1,
            'name.middle.value': 1,
            'name.family.value': 1,
            'name.last.value': 1,
            'name.first.flagged': 1,
            'name.middle.flagged': 1,
            'name.family.flagged': 1,
            'name.last.flagged': 1,
            'name.first.edited': 1,
            'name.middle.edited': 1,
            'name.family.edited': 1,
            'name.last.edited': 1,
            'name.first.userEdited': 1,
            'name.middle.userEdited': 1,
            'name.family.userEdited': 1,
            'name.last.userEdited': 1,
            'name.first.status': 1,
            'name.middle.status': 1,
            'name.family.status': 1,
            'name.last.status': 1,
            gender: 1,
            status: 1,
            isRegistered: 1,
            user_id: 1,
            'profileDetails.nationalityId': 1,
            'profileDetails._nationality_id': 1,
            _role_id: 1,
            program: 1,
            batch: 1,
            enrollmentYear: 1,
            isGlobalMandatoryStatus: 1,
        };
        const users = await userModel
            .find(dbQuery, userProject, { sort })
            .skip(skip)
            .limit(limit)
            .populate({ path: '_role_id', match: { isActive: true, isDeleted: false } })
            .lean();
        const totalDocs = await userModel.countDocuments(dbQuery);
        return {
            statusCode: 200,
            data: {
                users,
                currentPage: pageNo,
                totalPages: Math.ceil(totalDocs / limit),
                subTabCount,
                invalidUserCount,
                inactiveUserCount,
                registeredUserCount,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listUsersWithOutPaginate = async ({ headers = {}, query = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const { tab, subTab, userType, gender, program, batch, yearFrom, yearTo } = query;
        const userModel = getModel(tenantURL, USER, userSchema);
        let dbQuery = {
            isActive: true,
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userType,
        };
        const sort = { createdAt: -1 };
        if (gender) {
            dbQuery = { ...dbQuery, 'gender.value': gender };
        }
        if (program) {
            dbQuery = { ...dbQuery, 'program.value': { $in: program } };
        }
        if (batch) {
            dbQuery = { ...dbQuery, 'batch.value': batch };
        }
        if (yearFrom && yearTo) {
            dbQuery = {
                ...dbQuery,
                'enrollmentYear.value': {
                    $gte: new Date(yearFrom),
                    $lte: new Date(yearTo),
                },
            };
        }
        if (yearFrom && !yearTo) {
            dbQuery = {
                ...dbQuery,
                'enrollmentYear.value': {
                    $gte: new Date(yearFrom),
                },
            };
        }
        if (tab === constant.USER_STATUS.REGISTERED) {
            dbQuery = {
                ...dbQuery,
                $or: [{ status: constant.VALID }, { status: constant.COMPLETED }],
            };
        } else if (tab === constant.USER_STATUS.INACTIVE) {
            dbQuery = {
                _institution_id: convertToMongoObjectId(_institution_id),
                user_type: userType,
                isActive: false,
                isDeleted: false,
            };
        } else if (tab === constant.USER_STATUS.REGISTRATION_PENDING) {
            if (subTab === constant.PENDING) {
                dbQuery = {
                    ...dbQuery,
                    $or: [{ status: constant.PROFILE_UPDATED }, { status: constant.RESUBMITTED }],
                };
            } else if (subTab === constant.INVALID) {
                dbQuery = { ...dbQuery, status: constant.INVALID };
            } else if (subTab === constant.USER_STATUS.REQUEST_SENT) {
                dbQuery = {
                    ...dbQuery,
                    $or: [{ status: constant.IMPORTED }, { status: constant.PASSWORD_CONFIRMED }],
                };
            } else if (subTab === constant.EXPIRED) {
                dbQuery = {
                    ...dbQuery,
                    'verification.email': { $ne: true },
                    'verification.password': { $ne: true },
                    status: constant.EXPIRED,
                };
            } else {
                dbQuery = {
                    ...dbQuery,
                    isActive: true,
                    isDeleted: false,
                    status: 'invited',
                    $or: [
                        { 'verification.data': constant.PENDING },
                        { 'verification.data': constant.CORRECT },
                        { 'verification.data': constant.CORRECTION_REQUIRED },
                        { 'verification.email': false },
                        { 'verification.mobile': false },
                    ],
                    $expr: {
                        $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72],
                    },
                };
            }
        }
        const userProject = {
            email: 1,
            'name.first.value': 1,
            'name.middle.value': 1,
            'name.family.value': 1,
            'name.last.value': 1,
            'profileDetails.nationalityId': 1,
            'profileDetails._nationality_id': 1,
            status: 1,
            isRegistered: 1,
            gender: 1,
            user_id: 1,
            _role_id: 1,
            program: 1,
            batch: 1,
            enrollmentYear: 1,
        };
        const users = await userModel
            .find(dbQuery, userProject, { sort })
            .populate({ path: '_role_id', match: { isActive: true, isDeleted: false } })
            .lean();
        const totalDocs = users.length;
        return {
            statusCode: 200,
            data: {
                users,
                totalPages: Math.ceil(totalDocs),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const searchUsers = async ({ headers = {}, query = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const { searchKey, userType } = query;
        const userModel = getModel(tenantURL, USER, userSchema);
        let dbQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userType,
        };
        const sort = { createdAt: -1 };
        if (searchKey && searchKey.length) {
            const word = searchKey.split(/\s+/);
            if (word.length < 2) {
                dbQuery = {
                    ...dbQuery,
                    $or: [
                        { 'name.first.value': { $regex: searchKey, $options: 'i' } },
                        { 'name.middle.value': { $regex: searchKey, $options: 'i' } },
                        { 'name.last.value': { $regex: searchKey, $options: 'i' } },
                        { 'name.family.value': { $regex: searchKey, $options: 'i' } },
                        { email: { $regex: searchKey, $options: 'i' } },
                        { 'user_id.value': { $regex: searchKey, $options: 'i' } },
                        { 'gender.value': { $regex: searchKey, $options: 'i' } },
                    ],
                };
            } else if (word.length === 2) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        {
                            $or: [
                                { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                { 'name.middle.value': { $regex: word[0], $options: 'i' } },
                            ],
                        },
                        {
                            $or: [
                                { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                { 'name.last.value': { $regex: word[1], $options: 'i' } },
                                { 'name.family.value': { $regex: word[1], $options: 'i' } },
                            ],
                        },
                    ],
                };
            } else if (word.length === 3) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { 'name.first.value': { $regex: word[0], $options: 'i' } },
                        { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                        {
                            $or: [
                                { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                { 'name.family.value': { $regex: word[2], $options: 'i' } },
                            ],
                        },
                    ],
                };
            } else if (word.length === 4) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        { 'name.first.value': { $regex: word[0], $options: 'i' } },
                        { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                        { 'name.last.value': { $regex: word[2], $options: 'i' } },
                        {
                            $or: [{ 'name.family.value': { $regex: word[3], $options: 'i' } }],
                        },
                    ],
                };
            }
        }
        const userProject = {
            email: 1,
            'name.first.value': 1,
            'name.middle.value': 1,
            'name.family.value': 1,
            'name.last.value': 1,
            'name.first.flagged': 1,
            'name.middle.flagged': 1,
            'name.family.flagged': 1,
            'name.last.flagged': 1,
            'name.first.edited': 1,
            'name.middle.edited': 1,
            'name.family.edited': 1,
            'name.last.edited': 1,
            'name.first.status': 1,
            'name.middle.status': 1,
            'name.family.status': 1,
            'name.last.status': 1,
            gender: 1,
            status: 1,
            isRegistered: 1,
            user_id: 1,
            'profileDetails.nationalityId': 1,
            'profileDetails._nationality_id': 1,
            _role_id: 1,
            program: 1,
            batch: 1,
            enrollmentYear: 1,
            isActive: 1,
            isDelete: 1,
        };
        const users = await userModel
            .find(dbQuery, userProject, { sort })
            .limit(10)
            .populate({ path: '_role_id', match: { isActive: true, isDeleted: false } })
            .lean();
        if (!users) {
            return {
                statusCode: 400,
                message: 'DS_GET_FAILED',
            };
        }
        users.forEach((user) => {
            if (user.status === constant.VALID || user.status === constant.COMPLETED) {
                user.userStatus = constant.USER_STATUS.REGISTERED;
            } else if (
                user.status === constant.PROFILE_UPDATED ||
                user.status === constant.RESUBMITTED
            ) {
                user.userStatus =
                    constant.USER_STATUS.REGISTRATION_PENDING + ' ' + constant.PENDING;
            } else if (user.isActive === false && user.isActive === false) {
                user.userStatus = constant.USER_STATUS.INACTIVE;
            } else if (user.status === constant.INVALID) {
                user.userStatus =
                    constant.USER_STATUS.REGISTRATION_PENDING + ' ' + constant.INVALID;
            } else if (
                user.status === constant.IMPORTED ||
                user.status === constant.PASSWORD_CONFIRMED
            ) {
                user.userStatus =
                    constant.USER_STATUS.REGISTRATION_PENDING +
                    ' ' +
                    constant.USER_STATUS.REQUEST_SENT;
            } else if (user.status === constant.EXPIRED) {
                user.userStatus =
                    constant.USER_STATUS.REGISTRATION_PENDING + ' ' + constant.EXPIRED;
            }
        });
        return {
            statusCode: 200,
            data: {
                users,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStaffDesignation = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { _institution_id } = params;

        const settings = await settingsModel
            .findOne(
                {
                    _institution_id,
                },
                {
                    'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        if (!settings.globalConfiguration.staffUserManagement.designationConfiguration.designations)
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: settings.globalConfiguration.staffUserManagement.designationConfiguration
                .designations,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getParticularStaffDesignation = async ({ query = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { designation, sortName, sortId, searchKey } = query;
        let dbQuery = {
            isActive: true,
            isDeleted: false,
            designation,
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        const { limit, pageNo, skip } = getPaginationValues(query);
        let sort = { createdAt: -1 };
        if (searchKey && searchKey.length) {
            const word = searchKey.split(/\s+/);
            if (word.length < 2) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        {
                            $or: [
                                { 'name.first.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.middle.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.last.value': { $regex: searchKey, $options: 'i' } },
                                { 'name.family.value': { $regex: searchKey, $options: 'i' } },
                                { email: { $regex: searchKey, $options: 'i' } },
                                { 'user_id.value': { $regex: searchKey, $options: 'i' } },
                                { 'gender.value': { $regex: searchKey, $options: 'i' } },
                            ],
                        },
                    ],
                };
            } else if (word.length === 2) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        {
                            $and: [
                                {
                                    $or: [
                                        { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                        { 'name.middle.value': { $regex: word[0], $options: 'i' } },
                                    ],
                                },
                                {
                                    $or: [
                                        { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                        { 'name.last.value': { $regex: word[1], $options: 'i' } },
                                        { 'name.family.value': { $regex: word[1], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
            } else if (word.length === 3) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        {
                            $and: [
                                { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                {
                                    $or: [
                                        { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                        { 'name.family.value': { $regex: word[2], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
            } else if (word.length === 4) {
                dbQuery = {
                    ...dbQuery,
                    $and: [
                        {
                            $and: [
                                { 'name.first.value': { $regex: word[0], $options: 'i' } },
                                { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                                { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                {
                                    $or: [
                                        { 'name.family.value': { $regex: word[3], $options: 'i' } },
                                    ],
                                },
                            ],
                        },
                    ],
                };
            }
        }
        if (sortName) {
            if (sortName === 'recentlyAdded') {
                sort = { ...sort, createdAt: -1 };
            } else if (sortName === 'asc') {
                sort = { 'name.first.value': 1 };
            } else if (sortName === 'des') {
                sort = { 'name.first.value': -1 };
            }
        }
        if (sortId) {
            if (sortId === 'recentlyAdded') {
                sort = { ...sort, createdAt: -1 };
            } else if (sortId === 'asc') {
                sort = { 'user_id.value': 1 };
            } else if (sortId === 'des') {
                sort = { 'user_id.value': -1 };
            }
        }
        const userDoc = await userModel
            .find(
                dbQuery,
                { name: 1, user_id: 1, email: 1, designation: 1, _designation_id: 1 },
                { sort },
            )
            .skip(skip)
            .limit(limit)
            .lean();
        const totalDocs = await userModel.countDocuments(dbQuery);
        if (!userDoc.length)
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: {
                userDoc,
                currentPage: pageNo,
                totalPages: Math.ceil(totalDocs / limit),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDesignation = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { updateDesignation, designation, previousDesignation, type } = body;
        if (type === 'all') {
            const userUpdate = await userModel.updateMany(
                {
                    isDeleted: false,
                    designation: previousDesignation,
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    $set: { designation },
                },
            );
        } else {
            const bulkUpdate = [];
            updateDesignation.forEach((val) => {
                bulkUpdate.push({
                    updateMany: {
                        filter: { _id: convertToMongoObjectId(val.id) },
                        update: { $set: { designation: val.designation } },
                    },
                });
            });
            if (bulkUpdate.length) {
                await userModel.bulkWrite(bulkUpdate);
            }
        }
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateUserAcademicAllocation = async ({ params = {}, headers = {}, body = {} }) => {
    // Checking is Staff Subject is Scheduled or Not
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;
        const { designation, _designation_id, academicAllocation } = body;

        const userUpdate = await userModel.findByIdAndUpdate(
            { _id: id },
            { $set: { designation, _designation_id, academic_allocation: academicAllocation } },
        );
        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const activeInActiveUser = async ({ params = {}, headers = {}, body = {} }) => {
    // Checking is Staff Subject is Scheduled or Not
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { id } = params;
        const { isActive } = body;

        const userDocument = await userModel.findOne(
            { _id: id },
            { name: 1, user_type: 1, verification: 1, email: 1 },
        );

        const collegeName = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                { name: 1 },
            )
            .lean();

        const settingDoc = await settingModel
            .findOne(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                {
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                },
            )
            .lean();
        let labelBody;
        let labelName;
        if (isActive) {
            labelBody =
                settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[5].labelBody;
            labelName =
                settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[5].labelName;
        } else {
            labelBody =
                settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[4].labelBody;
            labelName =
                settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[4].labelName;
        }

        if (!userDocument)
            return {
                statusCode: 400,
                message: 'NO_DATA_FOUND',
            };

        const userUpdate = await userModel.findByIdAndUpdate(
            { _id: id },
            { $set: { isActive } },
            { new: true },
        );

        const userImportsCopy = [];
        userImportsCopy.push({
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userDocument.user_type,
            firstName: userDocument.name.first.value ? userDocument.name.first.value : '',
            middleName: userDocument.name.middle.value ? userDocument.name.middle.value : '',
            lastName: userDocument.name.last.value ? userDocument.name.last.value : '',
            familyName: userDocument.name.family.value ? userDocument.name.family.value : '',
            email: userDocument.email.trim().toLowerCase(),
            invitedAt: userDocument.verification.invitedAt,
        });

        sendStaffMail(userImportsCopy, settingDoc, collegeName, origin, labelBody, labelName);

        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userDefaultInstitution = async ({ params = {}, headers = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { email } = params;
        const { isDefault } = body;
        const userExists = await userModel.findOne({
            email,
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        if (!userExists) {
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        }
        const userUpdate = await userModel.findByIdAndUpdate(
            { _id: userExists._id },
            { $set: { isDefault } },
            { new: true },
        );

        if (!userUpdate)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE',
            };
        const userDocs = await userModel.find({
            email,
            _institution_id: { $ne: convertToMongoObjectId(_institution_id) },
        });
        await updateRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                bool: {
                    should: [{ match: { email } }],
                },
            },
            script: `ctx._source.isDefault = false`,
        });
        const bulkWrite = [];
        userDocs.forEach((doc) => {
            bulkWrite.push({
                updateMany: {
                    filter: {
                        _id: convertToMongoObjectId(doc._id),
                    },
                    update: {
                        isDefault: false,
                    },
                },
            });
        });
        if (bulkWrite.length > 0) {
            await userModel.bulkWrite(bulkWrite);
        }

        await updateRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                bool: {
                    should: [{ match: { email } }],
                    must: [{ term: { _institution_id } }],
                },
            },
            script: `ctx._source.isDefault = '${isDefault}'`,
        });
        return { statusCode: 200, message: 'DS_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserAcademicAllocation = async ({ params = {}, headers = {} }) => {
    // Checking is Staff Subject is Scheduled or Not
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;

        const userDocs = await userModel.findById(id, { academic_allocation: 1, designation: 1 });
        if (!userDocs)
            return {
                statusCode: 400,
                message: 'DS_NO_DATA_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: userDocs,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateEmploymentSchedule = async ({ params = {}, headers = {}, body = {} }) => {
    const { tenantURL } = headers;
    const userModel = getModel(tenantURL, USER, userSchema);
    const { id } = params;
    const {
        user_employment_type,
        staffModeType,
        daysTimeType,
        scheduleType,
        schedule,
        scheduleByDate,
    } = body;
    let query = {
        'employment.user_employment_type': user_employment_type,
        'employment.user_schedule_type': scheduleType,
    };
    if (user_employment_type === constant.FULL_TIME) {
        const full_time = {
            'employment.schedule_times.full_time': schedule,
            'employment.schedule_times.by_day': [],
            'employment.schedule_times.by_date': [],
            'employment.staffModeType': staffModeType,
            'employment.daysTimeType': daysTimeType,
        };
        query = { ...query, ...full_time };
    } else {
        if (scheduleType === constant.BY_DAY) {
            const part_time = {
                'employment.schedule_times.by_day': schedule,
                'employment.schedule_times.full_time': [],
                'employment.schedule_times.by_date': [],
                'employment.staffModeType': staffModeType,
                'employment.daysTimeType': daysTimeType,
            };
            query = { ...query, ...part_time };
        } else {
            const by_date = {
                'employment.schedule_times.by_date': scheduleByDate,
                'employment.schedule_times.by_day': [],
                'employment.schedule_times.full_time': [],
            };
            query = { ...query, ...by_date };
        }
    }
    const userUpdate = await userModel.findByIdAndUpdate(
        { _id: id },
        {
            $set: query,
        },
    );
    if (!userUpdate)
        return {
            statusCode: 400,
            message: 'FAILED_TO_UPDATE',
        };
    return { statusCode: 200, message: 'DS_UPDATED' };
};

const getEmploymentSchedule = async ({ params = {}, headers = {}, body = {} }) => {
    const { tenantURL } = headers;
    const userModel = getModel(tenantURL, USER, userSchema);
    const { _user_id } = params;

    const userDocs = await userModel.findById(
        { _id: convertToMongoObjectId(_user_id) },
        { employment: 1 },
    );
    if (!userDocs)
        return {
            statusCode: 400,
            message: 'FAILED_TO_GET',
        };
    return { statusCode: 200, message: 'DATA_RETRIEVED', data: userDocs.employment };
};

//get all profile details by using user id
const getProfileDetails = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, USER, userSchema);
        const { _user_id } = params;
        const userQuery = {
            _id: convertToMongoObjectId(_user_id),
            isDeleted: false,
        };
        const userProjection = {
            biometricData: 1,
            uploadedDocuments: 1,
            vaccineConfiguration: 1,
            profileDetails: 1,
            mobile: 1,
            gender: 1,
            name: 1,
            email: 1,
            user_id: 1,
            activityLog: 1,
            statusHistory: 1,
            status: 1,
            isRegistered: 1,
            changedFields: 1,
        };
        const users = await userModel.findOne(userQuery, userProjection).lean();
        if (!users)
            return {
                statusCode: 400,
                message: 'DS_GET_FAILED',
            };
        const biometricUploaded = [];
        for (biometricFace of users.biometricData.face.value) {
            biometricUploaded.push({
                url: biometricFace,
                signedUrl: await getSignedURL(biometricFace),
            });
        }
        users.biometricData = biometricUploaded;
        const documentUploaded = [];
        for (documentCategory of users.uploadedDocuments) {
            const signedDocuments = [];
            for (documents of documentCategory.document) {
                const urlLists = [];
                for (Url of documents.url) {
                    urlLists.push({
                        url: Url.value,
                        signedUrl: await getSignedURL(Url.value),
                        flagged: Url.flagged,
                    });
                }
                signedDocuments.push({ _document_id: documents._document_id, urls: urlLists });
            }
            documentUploaded.push({
                _category_id: documentCategory._category_id,
                document: signedDocuments,
            });
        }
        users.uploadedDocuments = documentUploaded;
        return {
            statusCode: 200,
            message: 'DS_DATA_RETRIEVED',
            data: users,
        };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userMailPush = async ({ headers = {}, body = {}, params = {}, query = {} }) => {
    const { tenantURL, _institution_id } = headers;
    const { tab } = query;
    const { id, message } = body;
    const userModel = getModel(tenantURL, USER, userSchema);
    let dbQuery = { isActive: true, isDeleted: false };
    let userLists;
    if (tab === ALL) {
        dbQuery = {
            ...dbQuery,
            $or: [
                { 'verification.document': false },
                { 'verification.face': false },
                { 'verification.email': false },
                { 'verification.password': false },
            ],
        };
        userLists = await userModel.find(dbQuery);
    } else if (tab === SELECTED) {
        userLists = await userModel.find({ _id: { $in: id } });
    }
    const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
    const settingDoc = await settingModel
        .findOne(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            {
                'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
            },
        )
        .lean();
    if (userLists) {
        await userLists.forEach(async (userData, index) => {
            let token = crypto.randomBytes(16).toString('hex');
            token = bcrypt.hashSync(`DigiScheduler${userData.verification.invitedAt}`, 16);
            let expiryDate = userData.verification.invitedAt;
            expiryDate = moment(expiryDate).add(
                settingDoc.globalConfiguration.staffUserManagement.mailSettingsConfiguration
                    .mailExpiration,
                'days',
            );
            const data = {
                name: nameFormatter(userData.name),
                link: `${util_key.STAFF_SIGN_UP_URL}`,
                password: userData.password,
                dateExpired: expiryDate,
            };
            const subject = `Staff Registration`;
            let mailBody = `
                <h3>DigiScheduler</h3>;
                <p><b>Dear ${data.name}</b>, <b>kindly register your account ${util_key.SIGN_UP_URL},Your password: ${data.password} ,will be expired at ${data.dateExpired}</b></p>;
                `;
            if (message) {
                mailBody = message;
            }
            const mailSendStatus = await sendStaffEmail({
                displayName:
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.displayName,
                emailId: settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.email,
                userName: settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.userName,
                password: settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.password,
                smtpClient:
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient,
                portNumber:
                    settingDoc.globalConfiguration.basicDetails.emailIdConfiguration.portNumber,
                toEmail: userData.email,
                subject,
                text: mailBody,
            });
        });
        return {
            statusCode: 200,
            message: 'MAIL_SEND',
        };
    }
    return { statusCode: 404, message: 'MAIL_NOT_SEND' };
};

const flagUnflagUser = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const { field, _category_id, _document_id, _url_id, isFlagged } = query;
        const { userId } = body;
        const findQuery = { _id: convertToMongoObjectId(userId) };
        let dbQuery = {};
        let arrayFilters = {};
        const isFieldFlagged = isFlagged === 'true';
        switch (field) {
            case 'name_first':
                dbQuery = {
                    $set: {
                        'name.first.flagged': isFieldFlagged,
                        'name.first.status': '',
                    },
                    ...(isFieldFlagged && { $inc: { 'name.first.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'name.first.flagCount': -1 } }),
                };
                break;
            case 'name_last':
                dbQuery = {
                    $set: { 'name.last.flagged': isFieldFlagged, 'name.last.status': '' },
                    ...(isFieldFlagged && { $inc: { 'name.last.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'name.last.flagCount': -1 } }),
                };
                break;
            case 'name_middle':
                dbQuery = {
                    $set: { 'name.middle.flagged': isFieldFlagged, 'name.middle.status': '' },
                    ...(isFieldFlagged && { $inc: { 'name.middle.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'name.middle.flagCount': -1 } }),
                };
                break;
            case 'name_family':
                dbQuery = {
                    $set: { 'name.family.flagged': isFieldFlagged, 'name.family.status': '' },
                    ...(isFieldFlagged && { $inc: { 'name.family.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'name.family.flagCount': -1 } }),
                };
                break;
            case 'gender':
                dbQuery = {
                    $set: { 'gender.flagged': isFieldFlagged, 'gender.status': '' },
                    ...(isFieldFlagged && { $inc: { 'gender.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'gender.flagCount': -1 } }),
                };
                break;
            case 'program':
                dbQuery = {
                    $set: { 'program.flagged': isFieldFlagged, 'program.status': '' },
                    ...(isFieldFlagged && { $inc: { 'program.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'program.flagCount': -1 } }),
                };
                break;
            case 'batch':
                dbQuery = {
                    $set: { 'batch.flagged': isFieldFlagged, 'batch.status': '' },
                    ...(isFieldFlagged && { $inc: { 'batch.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'batch.flagCount': -1 } }),
                };
                break;
            case 'enrollmentYear':
                dbQuery = {
                    $set: { 'enrollmentYear.flagged': isFieldFlagged, 'enrollmentYear.status': '' },
                    ...(isFieldFlagged && { $inc: { 'enrollmentYear.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'enrollmentYear.flagCount': -1 } }),
                };
                break;
            case 'user_id':
                dbQuery = {
                    $set: { 'user_id.flagged': isFieldFlagged, 'user_id.status': '' },
                    ...(isFieldFlagged && { $inc: { 'user_id.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'user_id.flagCount': -1 } }),
                };
                break;
            case 'contact':
                dbQuery = {
                    $set: { 'mobile.flagged': isFieldFlagged, 'mobile.status': '' },
                    ...(isFieldFlagged && { $inc: { 'mobile.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'mobile.flagCount': -1 } }),
                };
                break;
            case 'passport_no':
                dbQuery = {
                    $set: {
                        'profileDetails.passportNo.flagged': isFieldFlagged,
                        'profileDetails.passportNo.status': '',
                    },
                    ...(isFieldFlagged && { $inc: { 'profileDetails.passportNo.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'profileDetails.passportNo.flagCount': -1 } }),
                };
                break;
            case 'dob':
                dbQuery = {
                    $set: {
                        'profileDetails.dob.flagged': isFieldFlagged,
                        'profileDetails.dob.status': '',
                    },
                    ...(isFieldFlagged && { $inc: { 'profileDetails.dob.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'profileDetails.dob.flagCount': -1 } }),
                };
                break;
            case 'nationalityId':
                dbQuery = {
                    $set: {
                        'profileDetails.nationalityId.flagged': isFieldFlagged,
                        'profileDetails.nationalityId.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.nationalityId.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.nationalityId.flagCount': -1 },
                    }),
                };
                break;
            case '_nationality_id':
                dbQuery = {
                    $set: {
                        'profileDetails._nationality_id.flagged': isFieldFlagged,
                        'profileDetails._nationality_id.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails._nationality_id.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails._nationality_id.flagCount': -1 },
                    }),
                };
                break;
            case 'office_no':
                dbQuery = {
                    $set: {
                        'profileDetails.office.officeRoomNo.flagged': isFieldFlagged,
                        'profileDetails.office.officeRoomNo.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.office.officeRoomNo.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.office.officeRoomNo.flagCount': -1 },
                    }),
                };
                break;
            case 'office_extension':
                dbQuery = {
                    $set: {
                        'profileDetails.office.officeExtension.flagged': isFieldFlagged,
                        'profileDetails.office.officeExtension.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.office.officeExtension.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.office.officeExtension.flagCount': -1 },
                    }),
                };
                break;
            case 'buildingStreetName':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.buildingStreetName.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.buildingStreetName.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.buildingStreetName.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.buildingStreetName.flagCount': -1 },
                    }),
                };
                break;
            case 'floorNo':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.floorNo.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.floorNo.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.floorNo.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.floorNo.flagCount': -1 },
                    }),
                };
                break;
            case 'country':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.country.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.country.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.country.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.country.flagCount': -1 },
                    }),
                };
                break;
            case 'district':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.district.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.district.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.district.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.district.flagCount': -1 },
                    }),
                };
                break;
            case 'city':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.city.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.city.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.city.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.city.flagCount': -1 },
                    }),
                };
                break;
            case 'zipCode':
                dbQuery = {
                    $set: {
                        'profileDetails.addressDetails.zipCode.flagged': isFieldFlagged,
                        'profileDetails.addressDetails.zipCode.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.zipCode.flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'profileDetails.addressDetails.zipCode.flagCount': -1 },
                    }),
                };
                break;
            case 'parentGuardianPhoneNo':
                dbQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagged':
                            isFieldFlagged,
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount': 1,
                        },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount':
                                -1,
                        },
                    }),
                };
                break;
            case 'parentGuardianEmailId':
                dbQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.parentGuardianEmailId.flagged':
                            isFieldFlagged,
                        'profileDetails.otherContactDetails.parentGuardianEmailId.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount': 1,
                        },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount':
                                -1,
                        },
                    }),
                };
                break;
            case 'spouseGuardianPhoneNo':
                dbQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagged':
                            isFieldFlagged,
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount': 1,
                        },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: {
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount':
                                -1,
                        },
                    }),
                };
                break;
            case 'vaccine_config':
                dbQuery = {
                    $set: {
                        'vaccineConfiguration.$[i].flagged': isFieldFlagged,
                        'vaccineConfiguration.$[i].status': '',
                    },
                    ...(isFieldFlagged && { $inc: { 'vaccineConfiguration.$[i].flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'vaccineConfiguration.$[i].flagCount': -1 } }),
                };
                arrayFilters = {
                    arrayFilters: [
                        {
                            'i._id': convertToMongoObjectId(_category_id),
                        },
                    ],
                };
                break;
            case 'doc':
                dbQuery = {
                    $set: {
                        'uploadedDocuments.$[i].document.$[j].url.$[k].flagged': isFieldFlagged,
                        'uploadedDocuments.$[i].document.$[j].url.$[k].status': '',
                    },
                    ...(isFieldFlagged && {
                        $inc: { 'uploadedDocuments.$[i].document.$[j].url.$[k].flagCount': 1 },
                    }),
                    ...(!isFieldFlagged && {
                        $inc: { 'uploadedDocuments.$[i].document.$[j].url.$[k].flagCount': -1 },
                    }),
                };
                arrayFilters = {
                    arrayFilters: [
                        {
                            'i._category_id': convertToMongoObjectId(_category_id),
                        },
                        { 'j._document_id': convertToMongoObjectId(_document_id) },
                        { 'k._id': convertToMongoObjectId(_url_id) },
                    ],
                };
                break;
            case 'bio':
                dbQuery = {
                    $set: {
                        'biometricData.face.flagged': isFieldFlagged,
                        'biometricData.face.status': '',
                    },
                    ...(isFieldFlagged && { $inc: { 'biometricData.face.flagCount': 1 } }),
                    ...(!isFieldFlagged && { $inc: { 'biometricData.face.flagCount': -1 } }),
                };
                break;
            default:
        }

        if (field === 'vaccine_config' || field === 'doc') {
            await userModel.updateOne(findQuery, dbQuery, arrayFilters);
        } else {
            await userModel.findOneAndUpdate(findQuery, dbQuery);
        }
        return { statusCode: 200, message: constants.DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const approvalRejectUser = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const { type, field, _category_id, _document_id, _url_id, isFlagged, status } = query;
        const {
            userId,
            globalMandatoryEdited,
            globalMandatory,
            isGlobalMandatoryStatus,
            isRejectHandlingStatus,
        } = body;
        const findQuery = { _id: convertToMongoObjectId(userId) };
        const userDoc = await userModel.findOne(findQuery);
        let dbQuery = {};
        let arrayFilters = {};
        if (type === 'single') {
            const isFieldFlagged = isFlagged === 'true';
            switch (field) {
                case 'name_first':
                    dbQuery = {
                        $set: {
                            'name.first.flagged': isFieldFlagged,
                            'name.first.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'name.first.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'name.first.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'name.first.flagCount': -1 } }),
                    };
                    break;
                case 'name_last':
                    dbQuery = {
                        $set: {
                            'name.last.flagged': isFieldFlagged,
                            'name.last.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'name.last.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'name.last.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'name.last.flagCount': -1 } }),
                    };
                    break;
                case 'name_middle':
                    dbQuery = {
                        $set: {
                            'name.middle.flagged': isFieldFlagged,
                            'name.middle.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'name.middle.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'name.middle.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'name.middle.flagCount': -1 } }),
                    };
                    break;
                case 'name_family':
                    dbQuery = {
                        $set: {
                            'name.family.flagged': isFieldFlagged,
                            'name.family.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'name.family.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'name.family.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'name.family.flagCount': -1 } }),
                    };
                    break;
                case 'gender':
                    dbQuery = {
                        $set: {
                            'gender.flagged': isFieldFlagged,
                            'gender.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'gender.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'gender.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'gender.flagCount': -1 } }),
                    };
                    break;
                case 'program':
                    dbQuery = {
                        $set: { 'program.flagged': isFieldFlagged, 'program.status': status },
                        ...(isFieldFlagged && { $inc: { 'program.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'program.flagCount': -1 } }),
                    };
                    break;
                case 'batch':
                    dbQuery = {
                        $set: {
                            'batch.flagged': isFieldFlagged,
                            'batch.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'batch.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'batch.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'batch.flagCount': -1 } }),
                    };
                    break;
                case 'enrollmentYear':
                    dbQuery = {
                        $set: {
                            'enrollmentYear.flagged': isFieldFlagged,
                            'enrollmentYear.status': status,
                        },
                        ...(isFieldFlagged && { $inc: { 'enrollmentYear.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'enrollmentYear.flagCount': -1 } }),
                    };
                    break;
                case 'user_id':
                    dbQuery = {
                        $set: {
                            'user_id.flagged': isFieldFlagged,
                            'user_id.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'user_id.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'user_id.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'user_id.flagCount': -1 } }),
                    };
                    break;
                case 'contact':
                    dbQuery = {
                        $set: {
                            'mobile.flagged': isFieldFlagged,
                            'mobile.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'mobile.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'mobile.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'mobile.flagCount': -1 } }),
                    };
                    break;
                case 'passport_no':
                    dbQuery = {
                        $set: {
                            'profileDetails.passportNo.flagged': isFieldFlagged,
                            'profileDetails.passportNo.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.passportNo.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.passportNo.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.passportNo.flagCount': -1 },
                        }),
                    };
                    break;
                case 'dob':
                    dbQuery = {
                        $set: {
                            'profileDetails.dob.flagged': isFieldFlagged,
                            'profileDetails.dob.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.dob.globalMandatoryEdited': globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && { $inc: { 'profileDetails.dob.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'profileDetails.dob.flagCount': -1 } }),
                    };
                    break;
                case 'nationalityId':
                    dbQuery = {
                        $set: {
                            'profileDetails.nationalityId.flagged': isFieldFlagged,
                            'profileDetails.nationalityId.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.nationalityId.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.nationalityId.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.nationalityId.flagCount': -1 },
                        }),
                    };
                    break;
                case '_nationality_id':
                    dbQuery = {
                        $set: {
                            'profileDetails._nationality_id.flagged': isFieldFlagged,
                            'profileDetails._nationality_id.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails._nationality_id.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails._nationality_id.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails._nationality_id.flagCount': -1 },
                        }),
                    };
                    break;
                case 'office_no':
                    dbQuery = {
                        $set: {
                            'profileDetails.office.officeRoomNo.flagged': isFieldFlagged,
                            'profileDetails.office.officeRoomNo.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.office.officeRoomNo.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.office.officeRoomNo.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.office.officeRoomNo.flagCount': -1 },
                        }),
                    };
                    break;
                case 'office_extension':
                    dbQuery = {
                        $set: {
                            'profileDetails.office.officeExtension.flagged': isFieldFlagged,
                            'profileDetails.office.officeExtension.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.office.officeExtension.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.office.officeExtension.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.office.officeExtension.flagCount': -1 },
                        }),
                    };
                    break;
                case 'buildingStreetName':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.buildingStreetName.flagged':
                                isFieldFlagged,
                            'profileDetails.addressDetails.buildingStreetName.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.buildingStreetName.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: {
                                'profileDetails.addressDetails.buildingStreetName.flagCount': 1,
                            },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: {
                                'profileDetails.addressDetails.buildingStreetName.flagCount': -1,
                            },
                        }),
                    };
                    break;
                case 'floorNo':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.floorNo.flagged': isFieldFlagged,
                            'profileDetails.addressDetails.floorNo.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.floorNo.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.floorNo.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.floorNo.flagCount': -1 },
                        }),
                    };
                    break;
                case 'country':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.country.flagged': isFieldFlagged,
                            'profileDetails.addressDetails.country.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.country.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.country.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.country.flagCount': -1 },
                        }),
                    };
                    break;
                case 'district':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.district.flagged': isFieldFlagged,
                            'profileDetails.addressDetails.district.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.district.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.district.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.district.flagCount': -1 },
                        }),
                    };
                    break;
                case 'city':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.city.flagged': isFieldFlagged,
                            'profileDetails.addressDetails.city.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.city.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.city.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.city.flagCount': -1 },
                        }),
                    };
                    break;
                case 'zipCode':
                    dbQuery = {
                        $set: {
                            'profileDetails.addressDetails.zipCode.flagged': isFieldFlagged,
                            'profileDetails.addressDetails.zipCode.status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.addressDetails.zipCode.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.zipCode.flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'profileDetails.addressDetails.zipCode.flagCount': -1 },
                        }),
                    };
                    break;
                case 'parentGuardianPhoneNo':
                    dbQuery = {
                        $set: {
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagged':
                                isFieldFlagged,
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.status':
                                status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount': 1,
                            },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount':
                                    -1,
                            },
                        }),
                    };
                    break;
                case 'parentGuardianEmailId':
                    dbQuery = {
                        $set: {
                            'profileDetails.otherContactDetails.parentGuardianEmailId.flagged':
                                isFieldFlagged,
                            'profileDetails.otherContactDetails.parentGuardianEmailId.status':
                                status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount': 1,
                            },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount':
                                    -1,
                            },
                        }),
                    };
                    break;
                case 'spouseGuardianPhoneNo':
                    dbQuery = {
                        $set: {
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagged':
                                isFieldFlagged,
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.status':
                                status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount': 1,
                            },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: {
                                'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount':
                                    -1,
                            },
                        }),
                    };
                    break;
                case 'vaccine_config':
                    dbQuery = {
                        $set: {
                            'vaccineConfiguration.$[i].flagged': isFieldFlagged,
                            'vaccineConfiguration.$[i].status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'vaccineConfiguration.$[i].globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'vaccineConfiguration.$[i].flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'vaccineConfiguration.$[i].flagCount': -1 },
                        }),
                    };
                    arrayFilters = {
                        arrayFilters: [
                            {
                                'i._id': convertToMongoObjectId(_category_id),
                            },
                        ],
                    };
                    break;
                case 'doc':
                    dbQuery = {
                        $set: {
                            'uploadedDocuments.$[i].document.$[j].url.$[k].flagged': isFieldFlagged,
                            'uploadedDocuments.$[i].document.$[j].url.$[k].status': status,
                            ...((globalMandatoryEdited === false ||
                                globalMandatoryEdited === true) && {
                                'uploadedDocuments.$[i].document.$[j].url.$[k].globalMandatoryEdited':
                                    globalMandatoryEdited,
                            }),
                        },
                        ...(isFieldFlagged && {
                            $inc: { 'uploadedDocuments.$[i].document.$[j].url.$[k].flagCount': 1 },
                        }),
                        ...(!isFieldFlagged && {
                            $inc: { 'uploadedDocuments.$[i].document.$[j].url.$[k].flagCount': -1 },
                        }),
                    };
                    arrayFilters = {
                        arrayFilters: [
                            {
                                'i._category_id': convertToMongoObjectId(_category_id),
                            },
                            { 'j._document_id': convertToMongoObjectId(_document_id) },
                            { 'k._id': convertToMongoObjectId(_url_id) },
                        ],
                    };
                    break;
                case 'bio':
                    dbQuery = {
                        $set: {
                            'biometricData.face.flagged': isFieldFlagged,
                            'biometricData.face.status': status,
                            'biometricData.face.globalMandatoryEdited': globalMandatoryEdited,
                        },
                        ...(isFieldFlagged && { $inc: { 'biometricData.face.flagCount': 1 } }),
                        ...(!isFieldFlagged && { $inc: { 'biometricData.face.flagCount': -1 } }),
                    };
                    break;
                default:
            }

            if (field === 'vaccine_config' || field === 'doc') {
                await userModel.findByIdAndUpdate(
                    { _id: convertToMongoObjectId(userId) },
                    dbQuery,
                    arrayFilters,
                );
            } else {
                await userModel.findByIdAndUpdate({ _id: convertToMongoObjectId(userId) }, dbQuery);
            }
        } else {
            const isFieldFlagged = isFlagged === 'true';
            let userType;
            if (userDoc.user_type === 'staff') {
                userType = 'staff';
            } else {
                userType = 'student';
            }
            const dbUpdateQuery = [];
            for (documentLists of userDoc.uploadedDocuments) {
                for (documentList of documentLists.document) {
                    if (documentList.globalMandatory === true) {
                        if (!documentList.url.length) {
                            dbUpdateQuery.push({
                                updateMany: {
                                    filter: findQuery,
                                    update: {
                                        $set: {
                                            'uploadedDocuments.$[categoryId].document.$[documentId].globalMandatory': true,
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'categoryId._category_id': convertToMongoObjectId(
                                                documentLists._category_id,
                                            ),
                                        },
                                        {
                                            'documentId._document_id': convertToMongoObjectId(
                                                documentList._document_id,
                                            ),
                                        },
                                    ],
                                },
                            });
                        } else {
                            dbUpdateQuery.push({
                                updateMany: {
                                    filter: findQuery,
                                    update: {
                                        $set: {
                                            'uploadedDocuments.$[categoryId].document.$[documentId].globalMandatory':
                                                globalMandatory,
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'categoryId._category_id': convertToMongoObjectId(
                                                documentLists._category_id,
                                            ),
                                        },
                                        {
                                            'documentId._document_id': convertToMongoObjectId(
                                                documentList._document_id,
                                            ),
                                        },
                                    ],
                                },
                            });
                        }
                    }
                }
            }
            if (dbUpdateQuery.length > 0) {
                await userModel.bulkWrite(dbUpdateQuery).catch((error) => {
                    throw new Error(error);
                });
            }

            dbQuery = {
                $set: {
                    'name.first.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'name.first.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.name.first.value !== '' &&
                        userDoc.name.first.globalMandatory === true && {
                            'name.first.globalMandatory': globalMandatory,
                        }),
                    'name.last.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'name.last.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.name.last.value !== '' &&
                        userDoc.name.last.globalMandatory === true && {
                            'name.last.globalMandatory': globalMandatory,
                        }),
                    'name.middle.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'name.middle.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.name.middle.value !== '' &&
                        userDoc.name.middle.globalMandatory === true && {
                            'name.middle.globalMandatory': globalMandatory,
                        }),
                    'name.family.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'name.family.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.name.family.value !== '' &&
                        userDoc.name.family.globalMandatory === true && {
                            'name.family.globalMandatory': globalMandatory,
                        }),
                    'gender.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'gender.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.gender.value !== '' &&
                        userDoc.gender.globalMandatory === true && {
                            'gender.globalMandatory': globalMandatory,
                        }),
                    ...(userType === 'student' && { 'program.flagged': isFieldFlagged }),
                    ...(userType === 'student' && { 'enrollmentYear.flagged': isFieldFlagged }),
                    ...(userType === 'student' &&
                        (globalMandatory === false || globalMandatory === true) && {
                            'enrollmentYear.globalMandatoryEdited': globalMandatoryEdited,
                        }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.enrollmentYear.value !== '' &&
                        userDoc.enrollmentYear.globalMandatory === true && {
                            'enrollmentYear.globalMandatory': globalMandatory,
                        }),
                    ...(userType === 'student' && { 'batch.flagged': isFieldFlagged }),
                    ...(userType === 'student' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'batch.globalMandatoryEdited': globalMandatoryEdited,
                        }),
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'batch.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...(userType === 'student' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'program.globalMandatoryEdited': globalMandatoryEdited,
                        }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.batch.value !== '' &&
                        userDoc.batch.globalMandatory === true && {
                            'batch.globalMandatory': globalMandatory,
                        }),
                    'mobile.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'mobile.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.mobile.value !== '' &&
                        userDoc.mobile.globalMandatory === true && {
                            'mobile.globalMandatory': globalMandatory,
                        }),
                    'user_id.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'user_id.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.user_id.value !== '' &&
                        userDoc.user_id.globalMandatory === true && {
                            'user_id.globalMandatory': globalMandatory,
                        }),
                    'profileDetails.passportNo.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.passportNo.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.passportNo.value !== '' &&
                        userDoc.profileDetails.passportNo.globalMandatory === true && {
                            'profileDetails.passportNo.globalMandatory': globalMandatory,
                        }),
                    'profileDetails.dob.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.dob.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.dob.value !== '' &&
                        userDoc.profileDetails.dob.globalMandatory === true && {
                            'profileDetails.dob.globalMandatory': globalMandatory,
                        }),
                    'profileDetails.nationalityId.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.nationalityId.globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.nationalityId.value !== '' &&
                        userDoc.profileDetails.nationalityId.globalMandatory === true && {
                            'profileDetails.nationalityId.globalMandatory': globalMandatory,
                        }),
                    'profileDetails._nationality_id.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails._nationality_id.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...(userType === 'staff' && {
                        'profileDetails.office.officeRoomNo.flagged': isFieldFlagged,
                    }),
                    ...(userType === 'staff' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'profileDetails.office.officeRoomNo.globalMandatoryEdited':
                                globalMandatoryEdited,
                        }),
                    ...(userType === 'staff' && {
                        'profileDetails.office.officeExtension.flagged': isFieldFlagged,
                    }),
                    ...(userType === 'staff' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'profileDetails.office.officeExtension.globalMandatoryEdited':
                                globalMandatoryEdited,
                        }),
                    'profileDetails.addressDetails.buildingStreetName.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.buildingStreetName.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.buildingStreetName.value !== '' &&
                        userDoc.profileDetails.addressDetails.buildingStreetName.globalMandatory ===
                            true && {
                            'profileDetails.addressDetails.buildingStreetName.globalMandatory':
                                globalMandatory,
                        }),
                    'profileDetails.addressDetails.floorNo.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.floorNo.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.floorNo.value !== '' &&
                        userDoc.profileDetails.addressDetails.floorNo.globalMandatory === true && {
                            'profileDetails.addressDetails.floorNo.globalMandatory':
                                globalMandatory,
                        }),
                    'profileDetails.addressDetails.country.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.country.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.country.value !== '' &&
                        userDoc.profileDetails.addressDetails.country.globalMandatory === true && {
                            'profileDetails.addressDetails.country.globalMandatory':
                                globalMandatory,
                        }),
                    'profileDetails.addressDetails.district.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.district.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.district.value !== '' &&
                        userDoc.profileDetails.addressDetails.district.globalMandatory === true && {
                            'profileDetails.addressDetails.district.globalMandatory':
                                globalMandatory,
                        }),
                    'profileDetails.addressDetails.city.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.city.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.unit.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.city.value !== '' &&
                        userDoc.profileDetails.addressDetails.city.globalMandatory === true && {
                            'profileDetails.addressDetails.city.globalMandatory': globalMandatory,
                        }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.unit.value !== '' &&
                        userDoc.profileDetails.addressDetails.unit.globalMandatory === true && {
                            'profileDetails.addressDetails.unit.globalMandatory': globalMandatory,
                        }),
                    'profileDetails.addressDetails.zipCode.flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'profileDetails.addressDetails.zipCode.globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.addressDetails.zipCode.value !== '' &&
                        userDoc.profileDetails.addressDetails.zipCode.globalMandatory === true && {
                            'profileDetails.addressDetails.zipCode.globalMandatory':
                                globalMandatory,
                        }),
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagged':
                            isFieldFlagged,
                    }),
                    ...(userType === 'student' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatoryEdited':
                                globalMandatoryEdited,
                        }),
                    ...(userType === 'student' &&
                        (globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.otherContactDetails.parentGuardianPhoneNo.value !==
                            '' &&
                        userDoc.profileDetails.otherContactDetails.parentGuardianPhoneNo
                            .globalMandatory === true && {
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatory':
                                globalMandatory,
                        }),
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.parentGuardianEmailId.flagged':
                            isFieldFlagged,
                    }),
                    ...(userType === 'student' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatoryEdited':
                                globalMandatoryEdited,
                        }),
                    ...(userType === 'student' &&
                        (globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.otherContactDetails.parentGuardianEmailId.value !==
                            '' &&
                        userDoc.profileDetails.otherContactDetails.parentGuardianEmailId
                            .globalMandatory === true && {
                            'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatory':
                                globalMandatory,
                        }),
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagged':
                            isFieldFlagged,
                    }),
                    ...(userType === 'student' &&
                        (globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatoryEdited':
                                globalMandatoryEdited,
                        }),
                    ...(userType === 'student' &&
                        (globalMandatory === false || globalMandatory === true) &&
                        userDoc.profileDetails.otherContactDetails.spouseGuardianPhoneNo.value !==
                            '' &&
                        userDoc.profileDetails.otherContactDetails.spouseGuardianPhoneNo
                            .globalMandatory === true && {
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatory':
                                globalMandatory,
                        }),
                    'vaccineConfiguration.$[].flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'vaccineConfiguration.$[].globalMandatoryEdited': globalMandatoryEdited,
                    }),
                    ...((globalMandatory === false || globalMandatory === true) && {
                        'vaccineConfiguration.$[].globalMandatory': globalMandatory,
                    }),
                    'uploadedDocuments.$[].document.$[].url.$[].flagged': isFieldFlagged,
                    ...((globalMandatoryEdited === false || globalMandatoryEdited === true) && {
                        'uploadedDocuments.$[].document.$[].url.$[].globalMandatoryEdited':
                            globalMandatoryEdited,
                    }),
                    'biometricData.face.globalMandatoryEdited': false,
                    'biometricData.face.flagged': isFieldFlagged,
                    'name.first.status': status,
                    'name.last.status': status,
                    'name.middle.status': status,
                    'name.family.status': status,
                    'user_id.status': status,
                    'gender.status': status,
                    'mobile.status': status,
                    ...(userType === 'student' && {
                        'program.status': status,
                    }),
                    ...(userType === 'student' && {
                        'enrollmentYear.status': status,
                    }),
                    ...(userType === 'student' && {
                        'batch.status': status,
                    }),
                    'profileDetails.passportNo.status': status,
                    'profileDetails.dob.status': status,
                    'profileDetails.nationalityId.status': status,
                    'profileDetails._nationality_id.status': status,
                    ...(userType === 'staff' && {
                        'profileDetails.office.officeRoomNo.status': status,
                    }),
                    ...(userType === 'staff' && {
                        'profileDetails.office.officeExtension.status': status,
                    }),
                    'profileDetails.addressDetails.buildingStreetName.status': status,
                    'profileDetails.addressDetails.floorNo.status': status,
                    'profileDetails.addressDetails.country.status': status,
                    'profileDetails.addressDetails.district.status': status,
                    'profileDetails.addressDetails.city.status': status,
                    'profileDetails.addressDetails.zipCode.status': status,
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.status': status,
                    }),
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.parentGuardianEmailId.status': status,
                    }),
                    ...(userType === 'student' && {
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.status': status,
                    }),
                    'vaccineConfiguration.$[].status': status,
                    'uploadedDocuments.$[].document.$[].url.$[].status': status,
                    'biometricData.face.status': status,
                    approvingTypeAll: true,
                    ...((isGlobalMandatoryStatus === false || isGlobalMandatoryStatus === true) && {
                        isGlobalMandatoryStatus,
                    }),
                    ...((isRejectHandlingStatus === false || isRejectHandlingStatus === true) && {
                        isRejectHandlingStatus,
                    }),
                },
                ...(isFieldFlagged && {
                    $inc: {
                        'name.first.flagCount': 1,
                        'name.last.flagCount': 1,
                        'name.middle.flagCount': 1,
                        'name.family.flagCount': 1,
                        'gender.flagCount': 1,
                        'mobile.flagCount': 1,
                        'user_id.flagCount': 1,
                        'profileDetails.passportNo.flagCount': 1,
                        'profileDetails.dob.flagCount': 1,
                        'profileDetails.nationalityId.flagCount': 1,
                        'profileDetails._nationality_id.flagCount': 1,
                        'profileDetails.addressDetails.buildingStreetName.flagCount': 1,
                        'profileDetails.addressDetails.floorNo.flagCount': 1,
                        'profileDetails.addressDetails.country.flagCount': 1,
                        'profileDetails.addressDetails.district.flagCount': 1,
                        'profileDetails.addressDetails.city.flagCount': 1,
                        'profileDetails.addressDetails.zipCode.flagCount': 1,
                        'uploadedDocuments.$[].document.$[].url.$[].flagCount': 1,
                        'biometricData.face.flagCount': 1,
                        'vaccineConfiguration.$[].flagCount': 1,
                    },
                }),
                ...(userType === 'student' &&
                    isFieldFlagged && {
                        $inc: {
                            'program.flagCount': 1,
                            'enrollmentYear.flagCount': 1,
                            'batch.flagCount': 1,
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount': 1,
                            'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount': 1,
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount': 1,
                        },
                    }),
                ...(userType === 'student' &&
                    !isFieldFlagged && {
                        $inc: {
                            'program.flagCount': -1,
                            'enrollmentYear.flagCount': -1,
                            'batch.flagCount': -1,
                            'profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount':
                                -1,
                            'profileDetails.otherContactDetails.parentGuardianEmailId.flagCount':
                                -1,
                            'profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount':
                                -1,
                        },
                    }),
                ...(userType === 'staff' &&
                    isFieldFlagged && {
                        $inc: {
                            'profileDetails.office.officeExtension.flagCount': 1,
                            'profileDetails.office.officeRoomNo.flagCount': 1,
                        },
                    }),
                ...(userType === 'staff' &&
                    !isFieldFlagged && {
                        $inc: {
                            'profileDetails.office.officeExtension.flagCount': -1,
                            'profileDetails.office.officeRoomNo.flagCount': -1,
                        },
                    }),
                ...(!isFieldFlagged && {
                    $inc: {
                        'name.first.flagCount': -1,
                        'name.last.flagCount': -1,
                        'name.middle.flagCount': -1,
                        'name.family.flagCount': -1,
                        'gender.flagCount': -1,
                        'mobile.flagCount': -1,
                        'user_id.flagCount': -1,
                        'profileDetails.passportNo.flagCount': -1,
                        'profileDetails.dob.flagCount': -1,
                        'profileDetails.nationalityId.flagCount': -1,
                        'profileDetails._nationality_id.flagCount': -1,
                        'profileDetails.addressDetails.buildingStreetName.flagCount': -1,
                        'profileDetails.addressDetails.floorNo.flagCount': -1,
                        'profileDetails.addressDetails.country.flagCount': -1,
                        'profileDetails.addressDetails.district.flagCount': -1,
                        'profileDetails.addressDetails.city.flagCount': -1,
                        'profileDetails.addressDetails.zipCode.flagCount': -1,
                        'biometricData.face.flagCount': -1,
                        'uploadedDocuments.$[].document.$[].url.$[].flagCount': -1,
                        'vaccineConfiguration.$[].flagCount': -1,
                    },
                }),
            };
            const findUser = await userModel.findByIdAndUpdate(
                { _id: convertToMongoObjectId(userId) },
                dbQuery,
                { new: true },
            );
            if (findUser) {
                const flaggedVaccineCheck = findUser.vaccineConfiguration.find(
                    (element) => element.globalMandatoryEdited === true,
                );
                const documentCheckList = [];
                for (getDocuments of findUser.uploadedDocuments) {
                    for (getDoc of getDocuments.document) {
                        const flagData = getDoc.url.find(
                            (elementEntry) => elementEntry.globalMandatoryEdited,
                        );
                        if (flagData) documentCheckList.push(flagData);
                    }
                }
                if (
                    !findUser.user_id.globalMandatoryEdited &&
                    !findUser.batch.globalMandatoryEdited &&
                    !findUser.name.first.globalMandatoryEdited &&
                    !findUser.name.middle.globalMandatoryEdited &&
                    !findUser.name.last.globalMandatoryEdited &&
                    !findUser.name.family.globalMandatoryEdited &&
                    !findUser.enrollmentYear.globalMandatoryEdited &&
                    !findUser.program.globalMandatoryEdited &&
                    !findUser.gender.globalMandatoryEdited &&
                    !findUser.mobile.globalMandatoryEdited &&
                    !findUser.profileDetails.passportNo.globalMandatoryEdited &&
                    !findUser.profileDetails.dob.globalMandatoryEdited &&
                    !findUser.profileDetails.nationalityId.globalMandatoryEdited &&
                    !findUser.profileDetails._nationality_id.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.buildingStreetName
                        .globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.floorNo.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.country.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.district.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.city.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.zipCode.globalMandatoryEdited &&
                    !findUser.profileDetails.addressDetails.unit.globalMandatoryEdited &&
                    !findUser.profileDetails.office.officeExtension.globalMandatoryEdited &&
                    !findUser.profileDetails.office.officeRoomNo.globalMandatoryEdited &&
                    !findUser.profileDetails.otherContactDetails.parentGuardianPhoneNo
                        .globalMandatoryEdited &&
                    !findUser.profileDetails.otherContactDetails.parentGuardianEmailId
                        .globalMandatoryEdited &&
                    !findUser.profileDetails.otherContactDetails.spouseGuardianPhoneNo
                        .globalMandatoryEdited &&
                    !flaggedVaccineCheck &&
                    !documentCheckList.length &&
                    !findUser.biometricData.face.globalMandatoryEdited
                ) {
                    await userModel.findByIdAndUpdate(
                        { _id: convertToMongoObjectId(userId) },
                        {
                            $set: { validationPendingStatus: false },
                        },
                    );
                }
            }
        }
        return { statusCode: 200, message: constants.DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const verifyingStatus = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { userId } = params;
        const { statusType, fields } = body;
        const settingDoc = await settingModel
            .findOne(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                {
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                    'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.studentUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.studentUserManagement.mailConfiguration': 1,
                    'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                },
            )
            .lean();
        if (!settingDoc.globalConfiguration.basicDetails.emailIdConfiguration) {
            return { statusCode: 400, message: 'SETTING_EMAIL_ID_CONFIG_NOT_EXIST' };
        }

        const collegeName = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                { name: 1 },
            )
            .lean();
        const userDocument = await userModel
            .findOne(
                { _id: convertToMongoObjectId(userId) },
                {
                    email: 1,
                    name: 1,
                    user_type: 1,
                    _institution_id: 1,
                    verification: 1,
                    uploadedDocuments: 1,
                    profileDetails: 1,
                    user_id: 1,
                    batch: 1,
                    enrollmentYear: 1,
                    program: 1,
                    gender: 1,
                    mobile: 1,
                    status: 1,
                },
            )
            .lean();
        if (!userDocument) {
            return { statusCode: 400, message: constants.DS_GET_FAILED };
        }
        let labelConfiguration;
        let documentConfiguration;
        if (userDocument.user_type === constants.DC_STAFF) {
            labelConfiguration =
                settingDoc.globalConfiguration.staffUserManagement.labelFieldConfiguration;
            labelConfiguration = labelConfiguration.find((element) => element.language === 'en');
            documentConfiguration =
                settingDoc.globalConfiguration.staffUserManagement.documentConfiguration;
        } else {
            labelConfiguration =
                settingDoc.globalConfiguration.studentUserManagement.labelFieldConfiguration;
            labelConfiguration = labelConfiguration.find((element) => element.language === 'en');
            documentConfiguration =
                settingDoc.globalConfiguration.studentUserManagement.documentConfiguration;
        }
        let mandatoryDocuments = '';
        const setModifierForGlobalMandatory = { update: {} };
        const setModifier = { update: {} };
        const documentUpdates = [];
        setModifier.update.status = statusType;
        let flaggedContents = '';
        setModifier.update['name.first.status'] = '';
        setModifier.update['name.middle.status'] = '';
        setModifier.update['name.last.status'] = '';
        setModifier.update['name.family.status'] = '';
        setModifier.update['gender.status'] = '';
        if (userDocument.user_type === 'student') {
            setModifier.update['program.status'] = '';
            setModifier.update['batch.status'] = '';
            setModifier.update['enrollmentYear.status'] = '';
            setModifier.update['profileDetails.otherContactDetails.parentGuardianPhoneNo.status'] =
                '';
            setModifier.update['profileDetails.otherContactDetails.parentGuardianEmailId.status'] =
                '';
            setModifier.update['profileDetails.otherContactDetails.spouseGuardianPhoneNo.status'] =
                '';
        }
        setModifier.update['user_id.status'] = '';
        setModifier.update['mobile.status'] = '';
        setModifier.update['profileDetails.passportNo.status'] = '';
        setModifier.update['profileDetails.dob.status'] = '';
        setModifier.update['profileDetails.nationalityId.status'] = '';
        setModifier.update['profileDetails._nationality_id.status'] = '';
        setModifier.update['profileDetails.addressDetails.buildingStreetName.status'] = '';
        setModifier.update['profileDetails.addressDetails.floorNo.status'] = '';
        setModifier.update['profileDetails.addressDetails.zipCode.status'] = '';
        setModifier.update['profileDetails.addressDetails.country.status'] = '';
        setModifier.update['profileDetails.addressDetails.district.status'] = '';
        setModifier.update['profileDetails.addressDetails.city.status'] = '';
        if (userDocument.user_type === 'staff') {
            setModifier.update['profileDetails.office.officeExtension.status'] = '';
            setModifier.update['profileDetails.office.officeRoomNo.status'] = '';
        }
        setModifier.update['biometricData.face.status'] = '';
        setModifier.update.approvingTypeAll = false;
        if (fields && fields.firstName) {
            setModifier.update['name.first.flagged'] = fields.firstName.flagged;
            setModifier.update['name.first.invalid'] = fields.firstName.invalid;
            if (fields.firstName.flagged) {
                flaggedContents += '<p>First Name:flagged</p>';
            }
        }
        if (fields && fields.middleName) {
            setModifier.update['name.middle.flagged'] = fields.middleName.flagged;
            setModifier.update['name.middle.invalid'] = fields.middleName.invalid;
            if (fields.middleName.flagged) {
                flaggedContents += '<p>Middle Name:flagged</p>';
            }
        }
        if (fields && fields.lastName) {
            setModifier.update['name.last.flagged'] = fields.lastName.flagged;
            setModifier.update['name.last.invalid'] = fields.lastName.invalid;
            if (fields.lastName.flagged) {
                flaggedContents += '<p>Last Name:flagged</p>';
            }
        }
        if (fields && fields.familyName) {
            setModifier.update['name.family.flagged'] = fields.familyName.flagged;
            setModifier.update['name.family.invalid'] = fields.familyName.invalid;
            if (fields.familyName.flagged) {
                flaggedContents += '<p>Family Name:flagged</p>';
            }
        }
        if (fields && fields.gender) {
            setModifier.update['gender.flagged'] = fields.gender.flagged;
            setModifier.update['gender.invalid'] = fields.gender.invalid;
            if (fields.gender.flagged) {
                flaggedContents += '<p>Gender:flagged</p>';
            }
        }
        if (fields && fields.program) {
            setModifier.update['program.flagged'] = fields.program.flagged;
            setModifier.update['program.invalid'] = fields.program.invalid;
            if (fields.program.flagged) {
                flaggedContents += '<p>program:flagged</p>';
            }
        }
        if (fields && fields.batch) {
            setModifier.update['batch.flagged'] = fields.batch.flagged;
            setModifier.update['batch.invalid'] = fields.batch.invalid;
            if (fields.batch.flagged) {
                flaggedContents += '<p>batch:flagged</p>';
            }
        }
        if (fields && fields.enrollmentYear) {
            setModifier.update['enrollmentYear.flagged'] = fields.enrollmentYear.flagged;
            setModifier.update['enrollmentYear.invalid'] = fields.enrollmentYear.invalid;
            if (fields.enrollmentYear.flagged) {
                flaggedContents += '<p>enrollmentYear:flagged</p>';
            }
        }
        if (fields && fields.user_id) {
            setModifier.update['user_id.flagged'] = fields.user_id.flagged;
            setModifier.update['user_id.invalid'] = fields.user_id.invalid;
            if (fields.user_id.flagged) {
                flaggedContents += '<p>user_id:flagged</p>';
            }
        }
        if (fields && fields.no) {
            setModifier.update['mobile.flagged'] = fields.no.flagged;
            setModifier.update['mobile.invalid'] = fields.no.invalid;
            if (fields.no.flagged) {
                flaggedContents += '<p>No:flagged</p>';
            }
        }
        if (fields && fields.passportNo) {
            setModifier.update['profileDetails.passportNo.flagged'] = fields.passportNo.flagged;
            setModifier.update['profileDetails.passportNo.invalid'] = fields.passportNo.invalid;
            if (fields.passportNo.flagged) {
                flaggedContents += '<p>Passport No:flagged</p>';
            }
        }
        if (fields && fields.dob) {
            setModifier.update['profileDetails.dob.flagged'] = fields.dob.flagged;
            setModifier.update['profileDetails.dob.invalid'] = fields.dob.invalid;
            if (fields.dob.flagged) {
                flaggedContents += '<p>DOB:flagged</p>';
            }
        }
        if (fields && fields.nationalityId) {
            setModifier.update['profileDetails.nationalityId.flagged'] =
                fields.nationalityId.flagged;
            setModifier.update['profileDetails.nationalityId.invalid'] =
                fields.nationalityId.invalid;
            if (fields.nationalityId.flagged) {
                flaggedContents += '<p>NationalityId :flagged</p>';
            }
        }
        if (fields && fields._nationality_id) {
            setModifier.update['profileDetails._nationality_id.flagged'] =
                fields._nationality_id.flagged;
            setModifier.update['profileDetails._nationality_id.invalid'] =
                fields._nationality_id.invalid;
        }
        if (fields && fields.buildingStreetName) {
            setModifier.update['profileDetails.addressDetails.buildingStreetName.flagged'] =
                fields.buildingStreetName.flagged;
            setModifier.update['profileDetails.addressDetails.buildingStreetName.invalid'] =
                fields.buildingStreetName.invalid;
            if (fields.buildingStreetName.flagged) {
                flaggedContents += '<p>Building Street Name :flagged</p>';
            }
        }
        if (fields && fields.floorNo) {
            setModifier.update['profileDetails.addressDetails.floorNo.flagged'] =
                fields.floorNo.flagged;
            setModifier.update['profileDetails.addressDetails.floorNo.invalid'] =
                fields.floorNo.invalid;
            if (fields.floorNo.flagged) {
                flaggedContents += '<p>FloorNo :flagged</p>';
            }
        }
        if (fields && fields.country) {
            setModifier.update['profileDetails.addressDetails.country.flagged'] =
                fields.country.flagged;
            setModifier.update['profileDetails.addressDetails.country.invalid'] =
                fields.country.invalid;
            if (fields.country.flagged) {
                flaggedContents += '<p>Country :flagged</p>';
            }
        }
        if (fields && fields.district) {
            setModifier.update['profileDetails.addressDetails.district.flagged'] =
                fields.district.flagged;
            setModifier.update['profileDetails.addressDetails.district.invalid'] =
                fields.district.invalid;
            if (fields.district.flagged) {
                flaggedContents += '<p>District :flagged</p>';
            }
        }
        if (fields && fields.city) {
            setModifier.update['profileDetails.addressDetails.city.flagged'] = fields.city.flagged;
            setModifier.update['profileDetails.addressDetails.city.invalid'] = fields.city.invalid;
            if (fields.city.flagged) {
                flaggedContents += '<p>City :flagged</p>';
            }
        }
        if (fields && fields.zipCode) {
            setModifier.update['profileDetails.addressDetails.zipCode.flagged'] =
                fields.zipCode.flagged;
            setModifier.update['profileDetails.addressDetails.zipCode.invalid'] =
                fields.zipCode.invalid;
            if (fields.zipCode.flagged) {
                flaggedContents += '<p>zipCode :flagged</p>';
            }
        }
        if (fields && fields.parentGuardianPhoneNo) {
            setModifier.update['profileDetails.otherContactDetails.parentGuardianPhoneNo.flagged'] =
                fields.parentGuardianPhoneNo.flagged;
            setModifier.update['profileDetails.otherContactDetails.parentGuardianPhoneNo.invalid'] =
                fields.parentGuardianPhoneNo.invalid;
            if (fields.parentGuardianPhoneNo.flagged) {
                flaggedContents += '<p>parentGuardianPhoneNo :flagged</p>';
            }
        }
        if (fields && fields.parentGuardianEmailId) {
            setModifier.update['profileDetails.otherContactDetails.parentGuardianEmailId.flagged'] =
                fields.parentGuardianEmailId.flagged;
            setModifier.update['profileDetails.otherContactDetails.parentGuardianEmailId.invalid'] =
                fields.parentGuardianEmailId.invalid;
            if (fields.parentGuardianEmailId.flagged) {
                flaggedContents += '<p>parentGuardianEmailId :flagged</p>';
            }
        }
        if (fields && fields.spouseGuardianPhoneNo) {
            setModifier.update['profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagged'] =
                fields.spouseGuardianPhoneNo.flagged;
            setModifier.update['profileDetails.otherContactDetails.spouseGuardianPhoneNo.invalid'] =
                fields.spouseGuardianPhoneNo.invalid;
            if (fields.spouseGuardianPhoneNo.flagged) {
                flaggedContents += '<p>spouseGuardianPhoneNo :flagged</p>';
            }
        }
        if (fields && fields.officeExtension) {
            setModifier.update['profileDetails.office.officeExtension.flagged'] =
                fields.officeExtension.flagged;
            setModifier.update['profileDetails.office.officeExtension.invalid'] =
                fields.officeExtension.invalid;
            if (fields.officeExtension.flagged) {
                flaggedContents += '<p>officeExtension :flagged</p>';
            }
        }
        if (fields && fields.officeRoomNo) {
            setModifier.update['profileDetails.office.officeRoomNo.flagged'] =
                fields.officeRoomNo.flagged;
            setModifier.update['profileDetails.office.officeRoomNo.invalid'] =
                fields.officeRoomNo.invalid;
            if (fields.officeRoomNo.flagged) {
                flaggedContents += '<p>officeRoomNo :flagged</p>';
            }
        }
        if (fields && fields.biometricData) {
            setModifier.update['biometricData.face.flagged'] = fields.biometricData.flagged;
            setModifier.update['biometricData.face.invalid'] = fields.biometricData.invalid;
            if (fields.biometricData.flagged) {
                flaggedContents += '<p>Biometric face :flagged</p>';
            }
        }

        setModifier.update.$push = {
            activityLog: {
                activity: 'Profile moved to ' + statusType,
                activityAt: new Date(),
            },
        };

        documentUpdates.push({
            updateMany: {
                filter: { _id: convertToMongoObjectId(userId) },
                update: setModifier.update,
            },
        });
        const passportNo = labelConfiguration.profileDetails.find(
            (element) => element.mappingKey === 'passportNo',
        );
        if (!userDocument.profileDetails.passportNo.value && passportNo.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (passportNo.translatedInput ? passportNo.translatedInput : passportNo.name) +
                '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.passportNo.globalMandatory'
            ] = true;
        }
        const familyName = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'familyName',
        );
        if (!userDocument.name.family.value && familyName.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (familyName.translatedInput ? familyName.translatedInput : familyName.name) +
                '</p>';
            setModifierForGlobalMandatory.update['name.family.globalMandatory'] = true;
        }
        const lastName = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'lastName',
        );
        if (!userDocument.name.last.value && lastName.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (lastName.translatedInput ? lastName.translatedInput : lastName.name) +
                '</p>';
            setModifierForGlobalMandatory.update['name.last.globalMandatory'] = true;
        }

        const middleName = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'middleName',
        );
        if (!userDocument.name.middle.value && middleName.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (middleName.translatedInput ? middleName.translatedInput : middleName.name) +
                '</p>';
            setModifierForGlobalMandatory.update['name.middle.globalMandatory'] = true;
        }
        const firstName = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'firstName',
        );
        if (!userDocument.name.first.value && firstName.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (firstName.translatedInput ? firstName.translatedInput : firstName.name) +
                '</p>';
            setModifierForGlobalMandatory.update['name.first.globalMandatory'] = true;
        }

        const dob = labelConfiguration.profileDetails.find(
            (element) => element.mappingKey === 'dob',
        );
        if (!userDocument.profileDetails.dob.value && dob.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (dob.translatedInput ? dob.translatedInput : dob.name) + '</p>';
            setModifierForGlobalMandatory.update['profileDetails.dob.globalMandatory'] = true;
        }
        const gender = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'gender',
        );
        if (!userDocument.gender.value && gender.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (gender.translatedInput ? dob.translatedInput : dob.name) + '</p>';
            setModifierForGlobalMandatory.update['gender.globalMandatory'] = true;
        }
        const no = labelConfiguration.basicDetails.find((element) => element.mappingKey === 'no');
        if (!userDocument.mobile.no && no.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (no.translatedInput ? no.translatedInput : no.name) + '</p>';
            setModifierForGlobalMandatory.update['mobile.no.globalMandatory'] = true;
        }

        const user_id = labelConfiguration.basicDetails.find(
            (element) => element.mappingKey === 'user_id',
        );
        if (!userDocument.user_id.value && user_id.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (user_id.translatedInput ? user_id.translatedInput : user_id.name) + '</p>';
            setModifierForGlobalMandatory.update['user_id.no.globalMandatory'] = true;
        }
        const _nationality_id = labelConfiguration.profileDetails.find(
            (element) => element.mappingKey === '_nationality_id',
        );
        if (!userDocument.profileDetails._nationality_id.value && _nationality_id.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (_nationality_id.translatedInput
                    ? _nationality_id.translatedInput
                    : _nationality_id.name) +
                '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails._nationality_id.globalMandatory'
            ] = true;
        }
        const nationalityId = labelConfiguration.profileDetails.find(
            (element) => element.mappingKey === 'nationalityId',
        );
        if (!userDocument.profileDetails.nationalityId.value && nationalityId.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (nationalityId.translatedInput
                    ? nationalityId.translatedInput
                    : nationalityId.name) +
                '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.nationalityId.globalMandatory'
            ] = true;
        }
        if (userDocument.user_type === constants.DC_STAFF) {
            const officeExtension = labelConfiguration.contactDetails.find(
                (element) => element.mappingKey === 'officeExtension',
            );
            if (
                !userDocument.profileDetails.office.officeExtension.value &&
                officeExtension.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (officeExtension.translatedInput
                        ? officeExtension.translatedInput
                        : officeExtension.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.office.officeExtension.globalMandatory'
                ] = true;
            }
            const officeRoomNo = labelConfiguration.contactDetails.find(
                (element) => element.mappingKey === 'officeRoomNo',
            );
            if (
                !userDocument.profileDetails.office.officeRoomNo.value &&
                officeRoomNo.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (officeRoomNo.translatedInput
                        ? officeRoomNo.translatedInput
                        : officeRoomNo.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.office.officeRoomNo.globalMandatory'
                ] = true;
            }
        }
        const buildingStreetName = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'buildingStreetName',
        );
        if (
            !userDocument.profileDetails.addressDetails.buildingStreetName.value &&
            buildingStreetName.isMandatory
        ) {
            mandatoryDocuments +=
                '<p>' +
                (buildingStreetName.translatedInput
                    ? buildingStreetName.translatedInput
                    : buildingStreetName.name) +
                '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.buildingStreetName.globalMandatory'
            ] = true;
        }
        const floorNo = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'floorNo',
        );
        if (!userDocument.profileDetails.addressDetails.floorNo.value && floorNo.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (floorNo.translatedInput ? floorNo.translatedInput : floorNo.name) + '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.floorNo.globalMandatory'
            ] = true;
        }

        const district = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'district',
        );
        if (!userDocument.profileDetails.addressDetails.district.value && district.isMandatory) {
            mandatoryDocuments +=
                '<p>' +
                (district.translatedInput ? district.translatedInput : district.name) +
                '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.district.globalMandatory'
            ] = true;
        }
        const city = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'city',
        );
        if (!userDocument.profileDetails.addressDetails.city.value && city.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (city.translatedInput ? city.translatedInput : city.name) + '</p>';

            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.city.globalMandatory'
            ] = true;
        }
        const country = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'country',
        );
        if (!userDocument.profileDetails.addressDetails.country.value && country.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (country.translatedInput ? country.translatedInput : country.name) + '</p>';
            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.country.globalMandatory'
            ] = true;
        }
        const zipCode = labelConfiguration.addressDetails.find(
            (element) => element.mappingKey === 'zipCode',
        );
        if (!userDocument.profileDetails.addressDetails.zipCode.value && zipCode.isMandatory) {
            mandatoryDocuments +=
                '<p>' + (zipCode.translatedInput ? zipCode.translatedInput : zipCode.name) + '</p>';

            setModifierForGlobalMandatory.update[
                'profileDetails.addressDetails.zipCode.globalMandatory'
            ] = true;
        }
        if (userDocument.user_type === 'student') {
            const parentGuardianPhoneNo = labelConfiguration.otherContactDetails.find(
                (element) => element.mappingKey === 'parentGuardianPhoneNo',
            );
            if (
                !userDocument.profileDetails.otherContactDetails.parentGuardianPhoneNo.value &&
                parentGuardianPhoneNo.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (parentGuardianPhoneNo.translatedInput
                        ? parentGuardianPhoneNo.translatedInput
                        : parentGuardianPhoneNo.name) +
                    '</p>';

                setModifierForGlobalMandatory.update[
                    'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatory'
                ] = true;
            }
            const parentGuardianEmailId = labelConfiguration.otherContactDetails.find(
                (element) => element.mappingKey === 'parentGuardianEmailId',
            );
            if (
                !userDocument.profileDetails.otherContactDetails.parentGuardianEmailId.value &&
                parentGuardianEmailId.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (parentGuardianEmailId.translatedInput
                        ? parentGuardianEmailId.translatedInput
                        : parentGuardianEmailId.name) +
                    '</p>';

                setModifierForGlobalMandatory.update[
                    'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatory'
                ] = true;
            }
            const spouseGuardianPhoneNo = labelConfiguration.otherContactDetails.find(
                (element) => element.mappingKey === 'spouseGuardianPhoneNo',
            );
            if (
                !userDocument.profileDetails.otherContactDetails.spouseGuardianPhoneNo.value &&
                spouseGuardianPhoneNo.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (spouseGuardianPhoneNo.translatedInput
                        ? spouseGuardianPhoneNo.translatedInput
                        : spouseGuardianPhoneNo.name) +
                    '</p>';

                setModifierForGlobalMandatory.update[
                    'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatory'
                ] = true;
            }
            const enrollment_year = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'enrollment_year',
            );
            if (!userDocument.enrollmentYear.value && enrollment_year.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (enrollment_year.translatedInput
                        ? enrollment_year.translatedInput
                        : enrollment_year.name) +
                    '</p>';

                setModifierForGlobalMandatory.update['enrollmentYear.globalMandatory'] = true;
            }
            const program_no = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'program_no',
            );
            if (!userDocument.program.value && program_no.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (program_no.translatedInput ? program_no.translatedInput : program_no.name) +
                    '</p>';

                setModifierForGlobalMandatory.update['program.globalMandatory'] = true;
            }
            const batch = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'batch',
            );
            if (!userDocument.batch.value && batch.isMandatory) {
                mandatoryDocuments +=
                    '<p>' + (batch.translatedInput ? batch.translatedInput : batch.name) + '</p>';

                setModifierForGlobalMandatory.update['batch.globalMandatory'] = true;
            }
        }

        if (statusType === constant.VALID) {
            let mandatoryCategoryDocuments;
            if (userDocument.user_type === constants.DC_STAFF) {
                mandatoryCategoryDocuments =
                    settingDoc.globalConfiguration.staffUserManagement.documentConfiguration
                        .chooseDocuments;
            } else {
                mandatoryCategoryDocuments =
                    settingDoc.globalConfiguration.studentUserManagement.documentConfiguration
                        .chooseDocuments;
            }

            for (categoryDocumentsLists of mandatoryCategoryDocuments) {
                for (documentLists of categoryDocumentsLists.document) {
                    if (documentLists.isMandatory) {
                        const document = {};
                        document._category_id = categoryDocumentsLists._id;
                        document._id = documentLists._id;
                        document.isMandatory = documentLists.isMandatory;
                        document.userId = userDocument._id;
                        let chkDocuments = false;

                        if (userDocument.uploadedDocuments.length) {
                            const categoryFound = userDocument.uploadedDocuments.find(
                                (element) =>
                                    element._category_id.toString() ===
                                    categoryDocumentsLists._id.toString(),
                            );
                            if (categoryFound && categoryFound.document.length) {
                                const documentFound = categoryFound.document.find(
                                    (element) =>
                                        element._document_id.toString() ===
                                        documentLists._id.toString(),
                                );
                                if (documentFound && documentFound.url.length) {
                                    chkDocuments = true;
                                }
                            }
                        }
                        if (!chkDocuments) {
                            mandatoryDocuments += '<p>' + documentLists.labelName + '</p>';
                        }
                        await DocumentMissingCheck(document, tenantURL);
                    }
                }
            }
        }

        if (userDocument.uploadedDocuments && userDocument.uploadedDocuments.length) {
            for (documents of userDocument.uploadedDocuments) {
                const setModifier2 = { update: {} };
                if (documents.document && documents.document.length) {
                    for (document of documents.document) {
                        if (document.urls && document.urls.length) {
                            for (documentLists of document.urls) {
                                setModifier2.update[
                                    'uploadedDocuments.$[categoryId].document.$[documentId].url.$[urlId].status'
                                ] = '';
                                documentUpdates.push({
                                    updateMany: {
                                        filter: { _id: convertToMongoObjectId(userId) },
                                        update: setModifier2.update,
                                        arrayFilters: [
                                            {
                                                'categoryId._category_id': convertToMongoObjectId(
                                                    documents._category_id,
                                                ),
                                            },
                                            {
                                                'documentId._document_id': convertToMongoObjectId(
                                                    document._document_id,
                                                ),
                                            },
                                            {
                                                'urlId._id': convertToMongoObjectId(
                                                    documentLists._url_id,
                                                ),
                                            },
                                        ],
                                    },
                                });
                            }
                        }
                    }
                }
            }
        }
        if (fields && fields.documents && fields.documents.length > 0) {
            const documentSettingCategory =
                settingDoc.globalConfiguration.staffUserManagement.documentConfiguration
                    .chooseDocuments;
            for (document of fields.documents) {
                const categoryName = documentSettingCategory.find(
                    (element) => element._id.toString() === document._category_id.toString(),
                );
                const setModifier2 = { update: {} };
                for (documentLists of document.urls) {
                    if (documentLists.flagged) {
                        const subCategoryName = categoryName.document.find(
                            (element) =>
                                element._id.toString() === document._document_id.toString(),
                        );
                        flaggedContents +=
                            '<p>Document ' + subCategoryName.labelName + ': flagged</p>';
                    }
                    setModifier2.update[
                        'uploadedDocuments.$[categoryId].document.$[documentId].url.$[urlId].flagged'
                    ] = documentLists.flagged;

                    setModifier2.update[
                        'uploadedDocuments.$[categoryId].document.$[documentId].url.$[urlId].invalid'
                    ] = documentLists.invalid;
                    documentUpdates.push({
                        updateMany: {
                            filter: { _id: convertToMongoObjectId(userId) },
                            update: setModifier2.update,
                            arrayFilters: [
                                {
                                    'categoryId._category_id': convertToMongoObjectId(
                                        document._category_id,
                                    ),
                                },
                                {
                                    'documentId._document_id': convertToMongoObjectId(
                                        document._document_id,
                                    ),
                                },
                                {
                                    'urlId._id': convertToMongoObjectId(documentLists._url_id),
                                },
                            ],
                        },
                    });
                }
            }
        }

        let flaggedDocuments = '';
        if (flaggedContents.length > 0) {
            flaggedDocuments += 'Please find the below flagged contents';
            flaggedDocuments += flaggedContents;
        }
        if (mandatoryDocuments.length > 0 && statusType === constant.VALID) {
            flaggedDocuments += 'Please find the below mandatory documents';
            flaggedDocuments += mandatoryDocuments;
        }
        let labelBody;
        let labelName;
        if (statusType === constant.INVALID) {
            if (userDocument.user_type === constants.DC_STAFF) {
                labelBody =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[1]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[1]
                        .labelName;
            } else {
                labelBody =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[1]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[1]
                        .labelName;
            }
        } else {
            if (userDocument.user_type === constants.DC_STAFF) {
                labelBody =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[2]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[2]
                        .labelName;
            } else {
                labelBody =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[2]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[2]
                        .labelName;
            }
        }
        const userImportsCopy = [];
        userImportsCopy.push({
            _institution_id: convertToMongoObjectId(_institution_id),
            user_type: userDocument.user_type,
            firstName: userDocument.name.first.value ? userDocument.name.first.value : '',
            middleName: userDocument.name.middle.value ? userDocument.name.middle.value : '',
            lastName: userDocument.name.last.value ? userDocument.name.last.value : '',
            familyName: userDocument.name.family.value ? userDocument.name.family.value : '',
            email: userDocument.email.trim().toLowerCase(),
            invitedAt: userDocument.verification.invitedAt,
            flaggedDocuments,
        });
        sendStaffMail(userImportsCopy, settingDoc, collegeName, origin, labelBody, labelName);
        if (statusType === constant.VALID) {
            documentUpdates.push({
                updateMany: {
                    filter: { _id: userDocument._id },
                    update: setModifierForGlobalMandatory.update,
                },
            });
        }
        await userModel.bulkWrite(documentUpdates).catch((error) => {
            throw new Error(error);
        });
        await updateRecords({
            indexName: USER_INSTITUTIONS,
            query: {
                bool: {
                    should: [{ match: { email: userDocument.email } }],
                    must: [{ term: { _institution_id: String(userDocument._institution_id) } }],
                },
            },
            script: `ctx._source.status = '${statusType}'`,
        });
        return { statusCode: 200, message: constants.DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const verificationPending = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const { userId } = params;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const userDocument = await userModel
            .findOne(
                { _id: convertToMongoObjectId(userId) },
                {
                    email: 1,
                    name: 1,
                },
            )
            .lean();
        if (!userDocument) {
            return { statusCode: 400, message: constants.DS_GET_FAILED };
        }
        const userUpdateObject = {};
        userUpdateObject.$addToSet = {
            activityLog: {
                activity: `Verification pending`,
                activityAt: new Date(),
            },
        };
        if (userUpdateObject !== {}) {
            const userUpdate = await userModel.updateOne(
                { _id: convertToMongoObjectId(userId) },
                userUpdateObject,
            );
            if (!userUpdate)
                return {
                    statusCode: 400,
                    message: 'FAILED_TO_UPDATE',
                };
        }
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const multipleStatusUpdating = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL, _institution_id, origin } = headers;
        const userModel = getModel(tenantURL, constant.USER, userSchema);
        const settingModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const institutionModel = getModel(tenantURL, constant.INSTITUTION, institutionSchema);
        const { statusType, id, userType } = body;
        const settingDoc = await settingModel
            .findOne(
                { _institution_id: convertToMongoObjectId(_institution_id) },
                {
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                    'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,

                    'globalConfiguration.studentUserManagement.mailSettingsConfiguration': 1,
                    'globalConfiguration.studentUserManagement.mailConfiguration': 1,
                    'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                },
            )
            .lean();
        const collegeName = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                { name: 1 },
            )
            .lean();
        if (!settingDoc.globalConfiguration.basicDetails.emailIdConfiguration) {
            return { statusCode: 400, message: 'SETTING_EMAIL_ID_CONFIG_NOT_EXIST' };
        }
        const idLists = id.map((element) => convertToMongoObjectId(element));
        const userDocument = await userModel
            .find(
                { _id: { $in: idLists } },
                {
                    _id: 1,
                    email: 1,
                    name: 1,
                    user_type: 1,
                    verification: 1,
                    uploadedDocuments: 1,
                    profileDetails: 1,
                    user_id: 1,
                    batch: 1,
                    enrollmentYear: 1,
                    program: 1,
                    gender: 1,
                    mobile: 1,
                    _institution_id: 1,
                },
            )
            .lean();
        if (!userDocument) {
            return { statusCode: 400, message: constants.DS_GET_FAILED };
        }
        const setModifier = { update: {} };
        const documentUpdates = [];
        setModifier.update.status = statusType;

        setModifier.update.$push = {
            activityLog: {
                activity: 'Profile moved to ' + statusType,
                activityAt: new Date(),
            },
        };

        documentUpdates.push({
            updateMany: {
                filter: { _id: { $in: idLists } },
                update: setModifier.update,
            },
        });
        const userImportsCopy = [];
        let labelConfiguration;
        let documentConfiguration;
        if (userType === constants.DC_STAFF) {
            labelConfiguration =
                settingDoc.globalConfiguration.staffUserManagement.labelFieldConfiguration;
            labelConfiguration = labelConfiguration.find((element) => element.language === 'en');
            documentConfiguration =
                settingDoc.globalConfiguration.staffUserManagement.documentConfiguration;
        } else {
            labelConfiguration =
                settingDoc.globalConfiguration.studentUserManagement.labelFieldConfiguration;
            labelConfiguration = labelConfiguration.find((element) => element.language === 'en');
            documentConfiguration =
                settingDoc.globalConfiguration.studentUserManagement.documentConfiguration;
        }
        for (userList of userDocument) {
            let flaggedDocuments = '';
            let mandatoryDocuments = '';
            const setModifierForGlobalMandatory = { update: {} };
            if (
                userList.name.first.flagged ||
                userList.name.middle.flagged ||
                userList.name.last.flagged ||
                userList.name.family.flagged
            ) {
                flaggedDocuments += '<b>Personal details</b><br/>';

                if (userList.name.first.flagged) {
                    flaggedDocuments += '<p>First Name:Flagged</p>';
                }
                if (userList.name.middle.flagged) {
                    flaggedDocuments += '<p>Middle Name:Flagged</p>';
                }
                if (userList.name.last.flagged) {
                    flaggedDocuments += '<p>Last Name:Flagged</p>';
                }
                if (userList.name.family.flagged) {
                    flaggedDocuments += '<p>Family Name:Flagged</p>';
                }
            }
            if (
                userList.profileDetails.passportNo.flagged ||
                userList.profileDetails.dob.flagged ||
                userList.profileDetails.nationalityId.flagged ||
                userList.profileDetails.office.officeExtension.flagged ||
                userList.profileDetails.office.officeRoomNo.flagged ||
                userList.profileDetails.addressDetails.buildingStreetName.flagged ||
                userList.profileDetails.addressDetails.floorNo.flagged ||
                userList.profileDetails.addressDetails.country.flagged ||
                userList.profileDetails.addressDetails.district.flagged ||
                userList.profileDetails.addressDetails.city.flagged ||
                userList.profileDetails.addressDetails.zipCode.flagged ||
                userList.profileDetails.addressDetails.unit.flagged ||
                (userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.parentGuardianPhoneNo &&
                    userList.profileDetails.otherContactDetails.parentGuardianPhoneNo.flagCount) ||
                (userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.parentGuardianEmailId &&
                    userList.profileDetails.otherContactDetails.parentGuardianEmailId.flagCount) ||
                (userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.spouseGuardianPhoneNo &&
                    userList.profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagCount)
            ) {
                flaggedDocuments += '<b>Profile details</b><br/>';

                if (userList.profileDetails.passportNo.flagged) {
                    flaggedDocuments += '<p>Passport No:Flagged</p>';
                }
                if (userList.profileDetails.dob.flagged) {
                    flaggedDocuments += '<p>DOB:Flagged</p>';
                }
                if (userList.profileDetails.nationalityId.flagged) {
                    flaggedDocuments += '<p>Nationality Id:Flagged</p>';
                }
                if (userList.profileDetails.office.officeExtension.flagged) {
                    flaggedDocuments += '<p>Office Extension:Flagged</p>';
                }

                if (userList.profileDetails.office.officeRoomNo.flagged) {
                    flaggedDocuments += '<p>Office Room No:Flagged</p>';
                }
                if (userList.profileDetails.addressDetails.buildingStreetName.flagged) {
                    flaggedDocuments += '<p>Building Street Name:Flagged</p>';
                }

                if (userList.profileDetails.addressDetails.floorNo.flagged) {
                    flaggedDocuments += '<p>Floor No:Flagged</p>';
                }

                if (userList.profileDetails.addressDetails.country.flagged) {
                    flaggedDocuments += '<p>Country:Flagged</p>';
                }

                if (userList.profileDetails.addressDetails.district.flagged) {
                    flaggedDocuments += '<p>District:Flagged</p>';
                }

                if (userList.profileDetails.addressDetails.city.flagged) {
                    flaggedDocuments += '<p>City:Flagged</p>';
                }

                if (userList.profileDetails.addressDetails.zipCode.flagged) {
                    flaggedDocuments += '<p>zipCode:Flagged</p>';
                }

                if (
                    userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.parentGuardianPhoneNo &&
                    userList.profileDetails.otherContactDetails.parentGuardianPhoneNo.flagged
                ) {
                    flaggedDocuments += '<p>parentGuardianPhoneNo:Flagged</p>';
                }
                if (
                    userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.parentGuardianEmailId &&
                    userList.profileDetails.otherContactDetails.parentGuardianEmailId.flagged
                ) {
                    flaggedDocuments += '<p>parentGuardianEmailId:Flagged</p>';
                }
                if (
                    userList.profileDetails.otherContactDetails &&
                    userList.profileDetails.otherContactDetails.spouseGuardianPhoneNo &&
                    userList.profileDetails.otherContactDetails.spouseGuardianPhoneNo.flagged
                ) {
                    flaggedDocuments += '<p>spouseGuardianPhoneNo:Flagged</p>';
                }
                if (userList.profileDetails.addressDetails.unit.flagged) {
                    flaggedDocuments += '<p>Unit:Flagged</p>';
                }
            }
            const passportNo = labelConfiguration.profileDetails.find(
                (element) => element.mappingKey === 'passportNo',
            );
            if (!userList.profileDetails.passportNo.value && passportNo.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (passportNo.translatedInput ? passportNo.translatedInput : passportNo.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.passportNo.globalMandatory'
                ] = true;
            }
            const familyName = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'familyName',
            );
            if (!userList.name.family.value && familyName.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (familyName.translatedInput ? familyName.translatedInput : familyName.name) +
                    '</p>';
                setModifierForGlobalMandatory.update['name.family.globalMandatory'] = true;
            }
            const lastName = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'lastName',
            );
            if (!userList.name.last.value && lastName.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (lastName.translatedInput ? lastName.translatedInput : lastName.name) +
                    '</p>';
                setModifierForGlobalMandatory.update['name.last.globalMandatory'] = true;
            }

            const middleName = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'middleName',
            );
            if (!userList.name.middle.value && middleName.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (middleName.translatedInput ? middleName.translatedInput : middleName.name) +
                    '</p>';
                setModifierForGlobalMandatory.update['name.middle.globalMandatory'] = true;
            }
            const firstName = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'firstName',
            );
            if (!userList.name.first.value && firstName.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (firstName.translatedInput ? firstName.translatedInput : firstName.name) +
                    '</p>';
                setModifierForGlobalMandatory.update['name.first.globalMandatory'] = true;
            }

            const dob = labelConfiguration.profileDetails.find(
                (element) => element.mappingKey === 'dob',
            );
            if (!userList.profileDetails.dob.value && dob.isMandatory) {
                mandatoryDocuments +=
                    '<p>' + (dob.translatedInput ? dob.translatedInput : dob.name) + '</p>';
                setModifierForGlobalMandatory.update['profileDetails.dob.globalMandatory'] = true;
            }
            const gender = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'gender',
            );
            if (!userList.gender.value && gender.isMandatory) {
                mandatoryDocuments +=
                    '<p>' + (gender.translatedInput ? dob.translatedInput : dob.name) + '</p>';
                setModifierForGlobalMandatory.update['gender.globalMandatory'] = true;
            }

            const no = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'no',
            );
            if (!userList.mobile.no && no.isMandatory) {
                mandatoryDocuments +=
                    '<p>' + (no.translatedInput ? no.translatedInput : no.name) + '</p>';
                setModifierForGlobalMandatory.update['mobile.no.globalMandatory'] = true;
            }

            const user_id = labelConfiguration.basicDetails.find(
                (element) => element.mappingKey === 'user_id',
            );
            if (!userList.user_id.value && user_id.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (user_id.translatedInput ? user_id.translatedInput : user_id.name) +
                    '</p>';
                setModifierForGlobalMandatory.update['user_id.no.globalMandatory'] = true;
            }
            const _nationality_id = labelConfiguration.profileDetails.find(
                (element) => element.mappingKey === '_nationality_id',
            );
            if (!userList.profileDetails._nationality_id.value && _nationality_id.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (_nationality_id.translatedInput
                        ? _nationality_id.translatedInput
                        : _nationality_id.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails._nationality_id.globalMandatory'
                ] = true;
            }

            const nationalityId = labelConfiguration.profileDetails.find(
                (element) => element.mappingKey === 'nationalityId',
            );
            if (!userList.profileDetails.nationalityId.value && nationalityId.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (nationalityId.translatedInput
                        ? nationalityId.translatedInput
                        : nationalityId.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.nationalityId.globalMandatory'
                ] = true;
            }
            const officeExtension = labelConfiguration.contactDetails.find(
                (element) => element.mappingKey === 'officeExtension',
            );
            if (
                !userList.profileDetails.office.officeExtension.value &&
                officeExtension.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (officeExtension.translatedInput
                        ? officeExtension.translatedInput
                        : officeExtension.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.office.officeExtension.globalMandatory'
                ] = true;
            }
            const officeRoomNo = labelConfiguration.contactDetails.find(
                (element) => element.mappingKey === 'officeRoomNo',
            );
            if (!userList.profileDetails.office.officeRoomNo.value && officeRoomNo.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (officeRoomNo.translatedInput
                        ? officeRoomNo.translatedInput
                        : officeRoomNo.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.office.officeRoomNo.globalMandatory'
                ] = true;
            }
            const buildingStreetName = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'buildingStreetName',
            );
            if (
                !userList.profileDetails.addressDetails.buildingStreetName.value &&
                buildingStreetName.isMandatory
            ) {
                mandatoryDocuments +=
                    '<p>' +
                    (buildingStreetName.translatedInput
                        ? buildingStreetName.translatedInput
                        : buildingStreetName.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.buildingStreetName.globalMandatory'
                ] = true;
            }
            const floorNo = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'floorNo',
            );
            if (!userList.profileDetails.addressDetails.floorNo.value && floorNo.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (floorNo.translatedInput ? floorNo.translatedInput : floorNo.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.floorNo.globalMandatory'
                ] = true;
            }

            const district = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'district',
            );
            if (!userList.profileDetails.addressDetails.district.value && district.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (district.translatedInput ? district.translatedInput : district.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.district.globalMandatory'
                ] = true;
            }
            const city = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'city',
            );
            if (!userList.profileDetails.addressDetails.city.value && city.isMandatory) {
                mandatoryDocuments +=
                    '<p>' + (city.translatedInput ? city.translatedInput : city.name) + '</p>';

                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.city.globalMandatory'
                ] = true;
            }
            const country = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'country',
            );
            if (!userList.profileDetails.addressDetails.country.value && country.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (country.translatedInput ? country.translatedInput : country.name) +
                    '</p>';
                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.country.globalMandatory'
                ] = true;
            }
            const zipCode = labelConfiguration.addressDetails.find(
                (element) => element.mappingKey === 'zipCode',
            );
            if (!userList.profileDetails.addressDetails.zipCode.value && zipCode.isMandatory) {
                mandatoryDocuments +=
                    '<p>' +
                    (zipCode.translatedInput ? zipCode.translatedInput : zipCode.name) +
                    '</p>';

                setModifierForGlobalMandatory.update[
                    'profileDetails.addressDetails.zipCode.globalMandatory'
                ] = true;
            }

            if (userList.user_type === 'student') {
                const parentGuardianPhoneNo = labelConfiguration.otherContactDetails.find(
                    (element) => element.mappingKey === 'parentGuardianPhoneNo',
                );
                if (
                    !userList.profileDetails.otherContactDetails.parentGuardianPhoneNo.value &&
                    parentGuardianPhoneNo.isMandatory
                ) {
                    mandatoryDocuments +=
                        '<p>' +
                        (parentGuardianPhoneNo.translatedInput
                            ? parentGuardianPhoneNo.translatedInput
                            : parentGuardianPhoneNo.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update[
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatory'
                    ] = true;
                }
                const parentGuardianEmailId = labelConfiguration.otherContactDetails.find(
                    (element) => element.mappingKey === 'parentGuardianEmailId',
                );
                if (
                    !userList.profileDetails.otherContactDetails.parentGuardianEmailId.value &&
                    parentGuardianEmailId.isMandatory
                ) {
                    mandatoryDocuments +=
                        '<p>' +
                        (parentGuardianEmailId.translatedInput
                            ? parentGuardianEmailId.translatedInput
                            : parentGuardianEmailId.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update[
                        'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatory'
                    ] = true;
                }
                const spouseGuardianPhoneNo = labelConfiguration.otherContactDetails.find(
                    (element) => element.mappingKey === 'spouseGuardianPhoneNo',
                );
                if (
                    !userList.profileDetails.otherContactDetails.spouseGuardianPhoneNo.value &&
                    spouseGuardianPhoneNo.isMandatory
                ) {
                    mandatoryDocuments +=
                        '<p>' +
                        (spouseGuardianPhoneNo.translatedInput
                            ? spouseGuardianPhoneNo.translatedInput
                            : spouseGuardianPhoneNo.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update[
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatory'
                    ] = true;
                }
                const enrollment_year = labelConfiguration.basicDetails.find(
                    (element) => element.mappingKey === 'enrollment_year',
                );
                if (!userList.enrollmentYear.value && enrollment_year.isMandatory) {
                    mandatoryDocuments +=
                        '<p>' +
                        (enrollment_year.translatedInput
                            ? enrollment_year.translatedInput
                            : enrollment_year.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update['enrollmentYear.globalMandatory'] = true;
                }
                const program_no = labelConfiguration.basicDetails.find(
                    (element) => element.mappingKey === 'program_no',
                );
                if (!userList.program.value && program_no.isMandatory) {
                    mandatoryDocuments +=
                        '<p>' +
                        (program_no.translatedInput
                            ? program_no.translatedInput
                            : program_no.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update['program.globalMandatory'] = true;
                }
                const batch = labelConfiguration.basicDetails.find(
                    (element) => element.mappingKey === 'batch',
                );
                if (!userList.batch.value && batch.isMandatory) {
                    mandatoryDocuments +=
                        '<p>' +
                        (batch.translatedInput ? batch.translatedInput : batch.name) +
                        '</p>';

                    setModifierForGlobalMandatory.update['batch.globalMandatory'] = true;
                }
            }

            if (userList.uploadedDocuments.length) {
                for (documentLists of userList.uploadedDocuments) {
                    let documentSettingCategory;
                    if (userType === constants.DC_STAFF) {
                        documentSettingCategory =
                            settingDoc.globalConfiguration.staffUserManagement.documentConfiguration
                                .chooseDocuments;
                    } else {
                        documentSettingCategory =
                            settingDoc.globalConfiguration.studentUserManagement
                                .documentConfiguration.chooseDocuments;
                    }
                    const categoryName = documentSettingCategory.find(
                        (element) =>
                            element._id.toString() === documentLists._category_id.toString(),
                    );
                    if (categoryName) {
                        for (document of documentLists.document) {
                            let checkFlag = [];
                            if (document.url.length > 0) {
                                checkFlag = document.url.filter((element) => element.flagged);
                            }
                            if (checkFlag.length > 0) {
                                const subCategoryName = categoryName.document.find(
                                    (element) =>
                                        element._id.toString() === document._document_id.toString(),
                                );

                                flaggedDocuments +=
                                    '<p><b>' + categoryName.documentCategory + '</b></p>';

                                flaggedDocuments += '<p>' + subCategoryName.labelName + '</p>';
                            }
                        }
                    }
                }
            }
            if (statusType === constant.VALID) {
                let mandatoryCategoryDocuments;
                if (userType === constants.DC_STAFF) {
                    mandatoryCategoryDocuments =
                        settingDoc.globalConfiguration.staffUserManagement.documentConfiguration
                            .chooseDocuments;
                } else {
                    mandatoryCategoryDocuments =
                        settingDoc.globalConfiguration.studentUserManagement.documentConfiguration
                            .chooseDocuments;
                }

                for (categoryDocumentsLists of mandatoryCategoryDocuments) {
                    for (documentLists of categoryDocumentsLists.document) {
                        if (documentLists.isMandatory) {
                            const document = {};
                            document._category_id = categoryDocumentsLists._id;
                            document._id = documentLists._id;
                            document.isMandatory = documentLists.isMandatory;
                            document.userId = userList._id;
                            let chkDocuments = false;

                            if (userList.uploadedDocuments.length) {
                                const categoryFound = userList.uploadedDocuments.find(
                                    (element) =>
                                        element._category_id.toString() ===
                                        categoryDocumentsLists._id.toString(),
                                );
                                if (categoryFound && categoryFound.document.length) {
                                    const documentFound = categoryFound.document.find(
                                        (element) =>
                                            element._document_id.toString() ===
                                            documentLists._id.toString(),
                                    );
                                    if (documentFound && documentFound.url.length) {
                                        chkDocuments = true;
                                    }
                                }
                            }
                            if (!chkDocuments) {
                                mandatoryDocuments += '<p>' + documentLists.labelName + '</p>';
                            }
                            await DocumentMissingCheck(document, tenantURL);
                        }
                    }
                }
            }

            let flaggedContents = '';
            if (flaggedDocuments.length > 0) {
                flaggedContents += 'Please find the below flagged contents';
                flaggedContents += flaggedDocuments;
            }
            if (mandatoryDocuments.length > 0 && statusType === constant.VALID) {
                flaggedContents += 'Please find the below mandatory documents';
                flaggedContents += mandatoryDocuments;
            }
            userImportsCopy.push({
                _institution_id: convertToMongoObjectId(_institution_id),
                user_type: userList.user_type,
                firstName: userList.name.first.value ? userList.name.first.value : '',
                middleName: userList.name.middle.value ? userList.name.middle.value : '',
                lastName: userList.name.last.value ? userList.name.last.value : '',
                familyName: userList.name.family.value ? userList.name.family.value : '',
                email: userList.email.trim().toLowerCase(),
                invitedAt: userList.verification.invitedAt,
                flaggedDocuments: flaggedContents,
            });

            if (statusType === constant.VALID) {
                documentUpdates.push({
                    updateMany: {
                        filter: { _id: userList._id },
                        update: setModifierForGlobalMandatory.update,
                    },
                });
            }
        }
        let labelBody;
        let labelName;
        if (statusType === constant.INVALID) {
            if (userType === constants.DC_STAFF) {
                labelBody =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[1]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[1]
                        .labelName;
            } else {
                labelBody =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[1]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[1]
                        .labelName;
            }
        } else {
            if (userType === constants.DC_STAFF) {
                labelBody =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[2]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.staffUserManagement.mailConfiguration[2]
                        .labelName;
            } else {
                labelBody =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[2]
                        .labelBody;
                labelName =
                    settingDoc.globalConfiguration.studentUserManagement.mailConfiguration[2]
                        .labelName;
            }
        }
        sendStaffMail(userImportsCopy, settingDoc, collegeName, origin, labelBody, labelName);

        await userModel.bulkWrite(documentUpdates).catch((error) => {
            throw new Error(error);
        });
        userDocument.forEach((getUser) => {
            updateRecords({
                indexName: USER_INSTITUTIONS,
                query: {
                    bool: {
                        should: [{ match: { email: getUser.email } }],
                        must: [{ term: { _institution_id: String(getUser._institution_id) } }],
                    },
                },
                script: `ctx._source.status = '${statusType}'`,
            });
        });

        return { statusCode: 200, message: constants.DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    loggedInService,
    authUserInstitutions,
    loginService,
    loginServiceForWeb,
    userStaffStudentImport,
    userStaffStudentImportTemplates,
    // exportUsers,
    deleteUser,
    updateUser,
    getUser,
    userDetails,
    userSignUp,
    userRegistration,
    userReSet,
    getDocumentConfiguration,
    documentUpload,
    usersDocumentUpload,
    getUsersDocumentUpload,
    verifyUserData,
    listUsers,
    listUsersWithOutPaginate,
    searchUsers,
    getUsersBiometricConfiguration,
    usersBiometricUpload,
    getUsersBiometricUpload,
    getStaffDesignation,
    updateUserAcademicAllocation,
    getUserAcademicAllocation,
    getProfileDetails,
    userMailPush,
    flagUnflagUser,
    approvalRejectUser,
    verifyingStatus,
    verifyStudentData,
    updateEmploymentSchedule,
    getEmploymentSchedule,
    userSingleDocumentUpload,
    verificationPending,
    multipleStatusUpdating,
    sendRequest,
    activeInActiveUser,
    userRegistrationSkip,
    forgotPassword,
    getParticularStaffDesignation,
    updateDesignation,
    userDefaultInstitution,
    listInstitution,
    userSignUpForWeb,
};
