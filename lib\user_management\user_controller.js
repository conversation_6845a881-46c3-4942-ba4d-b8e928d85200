const constant = require('../utility/constants');
const user = require('mongoose').model(constant.USER);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const user_history = require('mongoose').model(constant.USER_HISTORY);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const digi_university = require('mongoose').model(constant.DIGI_UNIVERSITY);
const department_subject = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const course_schedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const base_control = require('../base/base_controller');
const parentUserSchema = require('mongoose').model(constant.PARENT_USER);
const faceRegisterSchema = require('../models/faceRegister');
const globalSettingSchema = require('../global_session_settings/global_session_settings_model');
const common_files = require('../utility/common');
const user_formate = require('./user_formate');
const ObjectId = common_files.convertToMongoObjectId;
const common_fun = require('../utility/common_functions');
const util_key = require('../utility/util_keys');
const crypto = require('crypto');
const moment = require('moment');
const bcrypt = require('bcryptjs');
const tokenUtil = require('../utility/token.util');
const { getAppCodeByDomain, getSignedURL } = require('../utility/common_functions');
const Excel = require('exceljs');
const file_upload = require('../utility/file_upload');
const multer = require('multer');
const { digiAuthService } = require('../../digi-auth');
const { changeUserName } = require('../lmsWarning/lmsWarning.controller');

const { response_function, convertToMongoObjectId, clone } = require('../utility/common');
const { get_list, get, get_list_populate } = require('../base/base_controller');
const { logger, DEPLOY_TO, SERVICES, AUTH_PASSWORD_SYNC } = require('../utility/util_keys');
const { updateStudentGroupFlatCacheData } = require('../student_group/student_group_services');
const { encryptedRoutes } = require('../../service/endpoint.util');
const { defaultPolicy } = require('../../middleware/policy.middleware');

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false } },
        { $sort: { updatedAt: -1 } },
        {
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'program._program_id',
                foreignField: '_id',
                as: 'programs',
            },
        },
        { $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } },
        { $addFields: { program_name: '$programs.name' } },
        // { $project: { _id: 1, username: 1, employee_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, status: 1 } },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(user, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data[0] */ user_formate.user_list_array(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('USER_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* user_formate.user(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    //try {
    const id = ObjectId(req.params.id);
    const aggre = { _id: id, isDeleted: false, isActive: true };
    // let doc = await base_control.get(user, aggre, {});
    const populate = { path: 'address._nationality_id', select: { _id: 1, name: 1 } };
    // let doc = await base_control.get_list_populate(user, aggre, {}, populate);
    const doc = await user
        .findOne(aggre, {})
        .populate(populate)
        .populate({
            path: 'program._program_id',
            model: constant.DIGI_PROGRAM,
            select: { _id: 1, name: 1 },
        });
    if (doc) {
        // Encrypting URLs
        if (doc.address._nationality_id_doc)
            doc.address._nationality_id_doc = await getSignedURL(doc.address._nationality_id_doc);
        if (doc.address._address_doc)
            doc.address._address_doc = await getSignedURL(doc.address._address_doc);
        if (doc.id_doc._college_id_doc)
            doc.id_doc._college_id_doc = await getSignedURL(doc.id_doc._college_id_doc);
        if (doc.student_docs._school_certificate_doc)
            doc.student_docs._school_certificate_doc = await getSignedURL(
                doc.student_docs._school_certificate_doc,
            );
        if (doc.student_docs._entrance_exam_certificate_doc)
            doc.student_docs._entrance_exam_certificate_doc = await getSignedURL(
                doc.student_docs._entrance_exam_certificate_doc,
            );
        if (doc.enrollment._admission_order_doc)
            doc.enrollment._admission_order_doc = await getSignedURL(
                doc.enrollment._admission_order_doc,
            );

        if (doc.id_doc._employee_id_doc)
            doc.id_doc._employee_id_doc = await getSignedURL(doc.id_doc._employee_id_doc);
        if (doc.enrollment._appointment_order_doc)
            doc.enrollment._appointment_order_doc = await getSignedURL(
                doc.enrollment._appointment_order_doc,
            );
        if (
            doc.qualifications.degree.length !== 0 &&
            doc.qualifications.degree[0] &&
            doc.qualifications.degree[0]._degree_doc
        )
            doc.qualifications.degree[0]._degree_doc = await getSignedURL(
                doc.qualifications.degree[0]._degree_doc,
            );

        common_files.sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('USER_DETAILS'),
            /* doc.data[0] */ user_formate.user_ID(doc),
        );
        // common_files.com_response(res, 200, true, "user details", doc.data[0]/* user_formate.user_ID(doc.data[0]) */);
    } else {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_FOUND_OR_DATA_MISMATCH'),
            doc,
        );
    }
    // } catch (error) {
    //     console.log('Internal Server Error ', error);
    //     common_files.com_response(res, 500, false, "Internal Server Error : Catch : ", error);
    // }
};

exports.insert = async (req, res) => {
    // let objs = {};
    console.log('Files ', req.files);
    const user_upload = file_upload.uploadfile2.fields([
        { name: '_img_doc[0]', maxCount: 1 },
        // { name: '_img_doc[1]', maxCount: 1 }
    ]);

    await user_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            // return res.status(500).send('Multer Error in uploading');
            console.log('Multer Error in uploading');
        }
        if (err) {
            // return res.status(500).send('Unknown Error occurred uploading');
            console.log('Unknown Error occurred uploading', err);
        }
    });
    console.log('Return ', user_upload);
    const doc = { status: true, data: null };
    console.log(req.body['_img_doc[0]']);
    // console.log(Array.isArray(req.body._img_doc));
    // console.log((req.body._img_doc).length);
    // doc = await base_control.insert(user, objs);
    if (doc.status) {
        common_files.com_response(res, 201, true, req.t('USER_ADDED_SUCCESSFULLY'), req.body);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.update = async (req, res) => {
    const user_check = await base_control.get(user, { _id: req.params.id, isDeleted: false }, {});
    if (user_check.status) {
        let project;
        let query;
        const emails = [req.body.email, req.body.email.toUpperCase(), req.body.email.toLowerCase()];
        if (user_check.data.user_type == constant.EVENT_WHOM.STAFF) {
            const ids = [
                req.body.employee_id,
                req.body.employee_id.toUpperCase(),
                req.body.employee_id.toLowerCase(),
            ];
            query = {
                _id: { $ne: ObjectId(req.params.id) },
                $or: [
                    { user_id: { $in: ids } },
                    { email: { $in: emails } },
                    // { 'address.nationality_id': req.body.nationality_id.trim() },
                ],
                isDeleted: false,
            };
            if (util_key.SERVICES.NATIONALITY_ID === 'true' && req.body.nationality_id)
                query.$or.push({ 'address.nationality_id': req.body.nationality_id.trim() });
            // let query = { $or: [{ user_id: req.body.employee_id }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false };
            project = { user_id: 1, email: 1, name: 1, user_type: 1, 'address.nationality_id': 1 };
            // data_check = await base_control.get_list(user, query, project);
        } else if (user_check.data.user_type == constant.EVENT_WHOM.STUDENT) {
            const ids = [
                req.body.academic_no,
                req.body.academic_no.toUpperCase(),
                req.body.academic_no.toLowerCase(),
            ];
            query = {
                _id: { $ne: ObjectId(req.params.id) },
                $or: [
                    { user_id: { $in: ids } },
                    { email: { $in: emails } },
                    // { 'address.nationality_id': req.body.nationality_id.trim() },
                ],
                isDeleted: false,
            };
            if (util_key.SERVICES.NATIONALITY_ID === 'true' && req.body.nationality_id)
                query.$or.push({ 'address.nationality_id': req.body.nationality_id.trim() }); // let query = { $or: [{ user_id: req.body.academic_no }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false };
            project = { user_id: 1, email: 1, name: 1, user_type: 1, 'address.nationality_id': 1 };
            // data_check = await base_control.get_list(user, query, project);
        }
        const data_check = await base_control.get_list(user, query, project);
        // if (user_check.data.user_type == constant.EVENT_WHOM.STAFF) {
        //     let query = { _id: { $ne: ObjectId(req.params.id) }, $or: [{ user_id: req.body.employee_id }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false, isActive: true };
        //     let project = { user_id: 1, email: 1, name: 1, user_type: 1, 'address.nationality_id': 1 };
        //     data_check = await base_control.get_list(user, query, project);
        // } else if (user_check.data.user_type == constant.EVENT_WHOM.STUDENT) {
        //     let query = { _id: { $ne: ObjectId(req.params.id) }, $or: [{ user_id: req.body.academic_no }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false, isActive: true };
        //     let project = { user_id: 1, email: 1, name: 1, user_type: 1, 'address.nationality_id': 1 };
        //     data_check = await base_control.get_list(user, query, project);
        // }
        if (!data_check.status) {
            const obj = {};
            if (user_check.data.user_type == 'staff') {
                if (req.body.employee_id != undefined && req.body.employee_id.length != 0) {
                    Object.assign(obj, { user_id: req.body.employee_id });
                }
                if (req.body.email != undefined && req.body.email.length != 0) {
                    Object.assign(obj, { email: req.body.email.toLowerCase() });
                }
                if (req.body.first_name != undefined && req.body.first_name.length != 0) {
                    Object.assign(obj, { 'name.first': req.body.first_name });
                }
                if (req.body.last_name != undefined && req.body.last_name.length != 0) {
                    Object.assign(obj, { 'name.last': req.body.last_name });
                }
                if (req.body.middle_name != undefined) {
                    Object.assign(obj, { 'name.middle': req.body.middle_name });
                }
                if (req.body.family != undefined) {
                    Object.assign(obj, { 'name.family': req.body.family });
                }
                if (req.body.gender != undefined && req.body.gender.length != 0) {
                    Object.assign(obj, { gender: req.body.gender });
                }
                if (req.body.nationality_id != undefined && req.body.nationality_id.length != 0) {
                    Object.assign(obj, {
                        'address.nationality_id': req.body.nationality_id.trim(),
                    });
                }
                if (req.body.enrollment_year != undefined && req.body.enrollment_year.length != 0) {
                    Object.assign(obj, { enrollment_year: req.body.enrollment_year });
                }
            } else {
                if (req.body.academic_no != undefined && req.body.academic_no.length != 0) {
                    Object.assign(obj, { user_id: req.body.academic_no });
                }
                if (req.body.email != undefined && req.body.email.length != 0) {
                    Object.assign(obj, { email: req.body.email.toLowerCase() });
                }
                if (req.body.first_name != undefined && req.body.first_name.length != 0) {
                    Object.assign(obj, { 'name.first': req.body.first_name });
                }
                if (req.body.last_name != undefined && req.body.last_name.length != 0) {
                    Object.assign(obj, { 'name.last': req.body.last_name });
                }
                if (req.body.middle_name != undefined) {
                    Object.assign(obj, { 'name.middle': req.body.middle_name });
                }
                if (req.body.family != undefined) {
                    Object.assign(obj, { 'name.family': req.body.family });
                }
                if (req.body.gender != undefined && req.body.gender.length != 0) {
                    Object.assign(obj, { gender: req.body.gender });
                }
                if (req.body.nationality_id != undefined && req.body.nationality_id.length != 0) {
                    Object.assign(obj, {
                        'address.nationality_id': req.body.nationality_id.trim(),
                    });
                }
                if (req.body.program_no != undefined && req.body.program_no.length != 0) {
                    Object.assign(obj, { 'program.program_no': req.body.program_no });
                    const query = { code: req.body.program_no, isDeleted: false, isActive: true };
                    const project = { _id: 1, name: 1 };
                    const program_data = await base_control.get(program, query, project);
                    Object.assign(obj, { 'program._program_id': program_data.data._id });
                }
                if (req.body.enrollment_year != undefined && req.body.enrollment_year.length != 0) {
                    Object.assign(obj, { enrollment_year: req.body.enrollment_year });
                }
                if (req.body.batch != undefined && req.body.batch.length != 0) {
                    Object.assign(obj, { batch: req.body.batch });
                }
            }
            Object.assign(obj, {
                status: 'imported',
                verification: {
                    email: false,
                    mobile: false,
                    data: 'pending',
                    face: false,
                    finger: false,
                },
            });
            // console.log('User Update : ', obj);
            // let doc = { status: false, data: obj }
            const doc = await base_control.update(user, ObjectId(req.params.id), obj);
            if (doc.status) {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('USER_UPDATE_SUCCESSFULLY'),
                    doc.data,
                );
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('ERROR_USER_DATA_UNABLE_TO_UPDATE'),
                    doc.data,
                );
            }
        } else {
            let response = req.t('FOUND_DUPLICATE_DATA');
            if (user_check.data.user_type == constant.EVENT_WHOM.STAFF) {
                if (
                    data_check.data.findIndex(
                        (i) =>
                            i.user_id == req.body.employee_id ||
                            i.user_id == req.body.employee_id.toLowerCase() ||
                            i.user_id == req.body.employee_id.toUpperCase(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_EMPLOYEE_ID');
                } else if (
                    data_check.data.findIndex(
                        (i) =>
                            i.email == req.body.email ||
                            i.email == req.body.email.toUpperCase() ||
                            i.email == req.body.email.toLowerCase(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_EMAIL');
                } else if (
                    util_key.SERVICES.NATIONALITY_ID === 'true' &&
                    data_check.data.findIndex(
                        (i) => i.address.nationality_id == req.body.nationality_id.trim(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_NATIONALITY_ID');
                }
            } else if (user_check.data.user_type == constant.EVENT_WHOM.STUDENT) {
                if (
                    data_check.data.findIndex(
                        (i) =>
                            i.user_id == req.body.academic_no ||
                            i.user_id == req.body.academic_no.toUpperCase() ||
                            i.user_id == req.body.academic_no.toLowerCase(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_ACADEMIC_NO');
                } else if (
                    data_check.data.findIndex(
                        (i) =>
                            i.email == req.body.email ||
                            i.email == req.body.email.toUpperCase() ||
                            i.email == req.body.email.toLowerCase(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_EMAIL');
                } else if (
                    util_key.SERVICES.NATIONALITY_ID === 'true' &&
                    data_check.data.findIndex(
                        (i) => i.address.nationality_id == req.body.nationality_id.trim(),
                    ) != -1
                ) {
                    response = req.t('FOUND_DUPLICATE_NATIONALITY_ID');
                }
            }
            common_files.comResponseWithRequest(
                req,
                res,
                409,
                false,
                req.t('FOUND_DUPLICATE_DATA'),
                response,
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_USER_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_USER_ID'),
        );
    }
};

exports.delete = async (req, res) => {
    const object_id = req.params.id;
    const doc = await base_control.hard_delete(user, { _id: object_id });
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('USER_DELETED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(req, res, 404, false, req.t('ERROR'), doc.data);
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false, isActive: true };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }

        const doc = await base_control.get_list(user, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('USER_LIST'),
                user_formate.user_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
        );
    }
};

exports.signup = async (req, res) => {
    const cond = { email: req.body.email.toLowerCase(), isDeleted: false, isActive: true };
    const proj = { _id: 1, email: 1, password: 1, user_type: 1, user_id: 1, status: 1 };
    const doc = await base_control.get(user, cond, proj);
    const response = {
        _id: doc.data._id,
        email: doc.data.email,
        user_type: doc.data.user_type,
        services: SERVICES,
    };
    // console.log(doc.data[0].password);
    if (doc.status) {
        if (doc.data.password == req.body.password) {
            await base_control.update(user, doc.data._id, { 'verification.email': true });
            digiAuthService
                .verifyUser({
                    employeeOrAcademicId: doc.data.user_id,
                    type: 'email',
                    verify: doc.data.email,
                    isVerified: true,
                    verifiedBy: doc.data.user_id,
                })
                .then((updatedUser) => {
                    console.log('userController -> signup -> updatedUser:', updatedUser);
                })
                .catch((err) => {
                    console.log('userController -> signup -> err:', err);
                });
            response.tokens = await tokenUtil.generateAuthTokens({
                userId: doc.data.user_id,
                _id: doc.data._id,
                userType: defaultPolicy.SIGNUP,
                userRoleData: [],
            });
            common_files.sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('USER_LOGIN_SUCCESSFULLY'),
                response /* user_formate.user_ID(doc.data[0]) */,
            );
        } else {
            if (
                doc.data.status != 'verified' &&
                doc.data.status != 'submitted' &&
                doc.data.status != 'completed'
            ) {
                if (!bcrypt.compareSync(req.body.password, doc.data.password)) {
                    common_files.sendResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('PASSWORD_NOT_MATCH'),
                        req.t('PASSWORD_NOT_MATCH'),
                    );
                } else {
                    response.tokens = await tokenUtil.generateAuthTokens({
                        userId: doc.data.user_id,
                        _id: doc.data._id,
                        userType: defaultPolicy.SIGNUP,
                        userRoleData: [],
                    });
                    common_files.sendResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('USER_LOGIN_SUCCESSFULLY'),
                        response /* user_formate.user_ID(doc.data[0]) */,
                    );
                }
            } else {
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('SIGNUP_PROCESS_IS_OVER_CONTACT_ADMIN'),
                    req.t('SIGNUP_PROCESS_IS_OVER_CONTACT_ADMIN'),
                );
            }
        }
    } else {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.set_password = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    const proj = { _id: 1, password: 1, user_id: 1 };
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        if (
            doc.data.password == req.body.old_password ||
            bcrypt.compareSync(req.body.old_password, doc.data.password)
        ) {
            const update_pass = await base_control.update(user, doc.data._id, {
                password: bcrypt.hashSync(req.body.new_password, 10),
            });
            if (update_pass.status) {
                if (AUTH_PASSWORD_SYNC === 'true') {
                    digiAuthService
                        .syncUser({
                            employeeOrAcademicId: doc.data.user_id,
                            userId: doc.data._id,
                            password: req.body.new_password,
                        })
                        .then((updatedUser) => {
                            console.log(
                                'userController -> set_password -> updatedUser:',
                                updatedUser,
                            );
                        })
                        .catch((err) => {
                            console.log('userController -> set_password -> err:', err);
                        });
                }
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('PASSWORD_SUCCESSFULLY_CHANGED'),
                    req.t('PASSWORD_SUCCESSFULLY_CHANGED') /* user_formate.user_ID(doc.data) */,
                );
            } else {
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('UNABLE_TO_CHANGE_PASSWORD'),
                    req.t('UNABLE_TO_CHANGE_PASSWORD'),
                );
            }
        } else {
            common_files.sendResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('PASSWORD_NOT_MATCH'),
                req.t('PASSWORD_NOT_MATCH'),
            );
        }
    } else {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.register_mobile = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    const proj = { _id: 1, user_id: 1 };
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        // if (DEPLOY_TO && DEPLOY_TO === 'india') {
        if (SERVICES.MOBILE === 'false') {
            const update_pass = await base_control.update(user, doc.data._id, {
                mobile: req.body.mobile,
                'verification.mobile': true,
            });
            let server_response = {
                status_code: 200,
                status: true,
                message: '',
                data: '',
            };
            if (update_pass.status)
                server_response = {
                    status_code: 200,
                    status: true,
                    message: req.t('MOBILE_NUMBER_CHANGED_SUCCESSFULLY'),
                    data: req.t('MOBILE_NUMBER_CHANGED_SUCCESSFULLY'),
                };
            else
                server_response = {
                    status_code: 500,
                    status: false,
                    message: req.t('DUPLICATE_MOBILE_NUMBER'),
                    data: req.t('DUPLICATE_MOBILE_NUMBER'),
                };
            return common_files.sendResponseWithRequest(
                req,
                res,
                server_response.status_code,
                server_response.status,
                server_response.message,
                server_response.data,
            );
        }
        const otp = common_fun.getFourDigitOTP();
        const sms_messages = `${otp} ` + req.t('IS_YOUR_DIGISCHEDULER_PORTAL_REGISTRATION_CODE');
        const update_pass = await base_control.update(user, doc.data._id, {
            mobile: req.body.mobile,
            'otp.no': otp,
            'otp.expiry_date': common_fun.timestampNow(),
        });
        if (update_pass.status) {
            // function sentMessageCallback(error, response, body) {
            //     if (!error && common_fun.checkSMSResponse(response, body)) {
            //         console.log('sms sent :' + req.body.mobile);
            //     } else {
            //         console.log('sms error : ', error);
            //     }
            // }
            // common_fun.send_message(req.body.mobile, sms_messages, sentMessageCallback);
            common_fun.send_sms(req.body.mobile, sms_messages);

            const slack_chat_data = `Mobile Register -> Mobile : ${req.body.mobile} - OTP : ${otp}`;
            common_fun.send_otp_to_slack(slack_chat_data);

            // digiAuthService
            //     .syncUser({
            //         employeeOrAcademicId: doc.data.user_id,
            //         mobile: req.body.mobile,
            //     })
            //     .then((updatedUser) => {
            //         console.log('userController -> register_mobile -> updatedUser:', updatedUser);
            //     })
            //     .catch((err) => {
            //         console.log('userController -> register_mobile -> err:', err);
            //     });

            common_files.sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('MOBILE_REGISTERED_AND_OTP_SEND'),
                req.t('MOBILE_REGISTERED_AND_OTP_SEND') /* user_formate.user_ID(doc.data[0]) */,
            );
        } else {
            common_files.sendResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('THIS_MOBILE_NUMBER_ALREADY_EXIST'),
                req.t('THIS_MOBILE_NUMBER_ALREADY_EXIST'),
            );
        }
    } else {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.otp_verify = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    // let proj = { _id: 1, name: 1, user_type: 1, user_id: 1, gender: 1, email: 1, dob: 1, mobile: 1 };
    const proj = {};
    const doc = await base_control.get(user, cond, proj);
    // return res.send(doc);
    if (doc.status) {
        let roles;
        let report_to;
        if (doc.data._role_id) {
            const populate = { path: 'roles._role_id' /* , select: { '_id': 1, 'name': 1 } */ };
            const roles_data = await base_control.get_list_populate(
                role_assign,
                { _id: ObjectId(doc.data._role_id) },
                {},
                populate,
            );
            const role_details = [];
            roles_data.data[0].roles.forEach((element) => {
                if (element._role_id.isActive) {
                    element.role_name = element._role_id.name;
                    role_details.push(element);
                }
            });
            // return res.send(roles_data.data[0].roles);
            roles = role_details;
            report_to = roles_data.data[0].report_to;
        }
        if (doc.data.otp.no == req.body.otp) {
            const secondsDifference = common_fun.getSecondsDifference(
                doc.data.otp.expiry_date,
                common_fun.timestampNow(),
            );
            if (secondsDifference <= util_key.OTP_EXPIRY_DURATION_IN_SECS) {
                let update_pass = { status: true };
                if (!doc.data.verification.mobile) {
                    update_pass = await base_control.update(user, doc.data._id, {
                        'verification.mobile': true,
                    });
                }
                if (update_pass.status) {
                    const tokens = await tokenUtil.generateAuthTokens(doc.data);
                    const respData = { ...doc.data.toObject(), tokens, roles, report_to };
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('OTP_VERIFIED_SUCCESSFULLY'),
                        respData /* user_formate.user_ID(doc.data) */,
                    );
                } else {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('UNABLE_TO_VERIFY_OTP'),
                        req.t('UNABLE_TO_VERIFY_OTP'),
                    );
                }
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('OTP_EXPIRES'),
                    req.t('OTP_EXPIRES'),
                );
            }
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('INVALID_OTP'),
                req.t('INVALID_OTP'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.get_mobile_mail = async (req, res) => {
    // let match = req.params.match;
    const aggre = [
        { $match: { isDeleted: false, isActive: true } },
        { $project: { contact: 1 } },
        { $addFields: { parent_mobile: '$contact.mobile' } },
        { $addFields: { check: { $in: [req.params.value, '$parent_mobile'] } } },
        // { $addFields: { check1: '$check' } },
        { $project: { check: 1 } },
    ];
    const doc = await base_control.get_aggregate(user, aggre);
    // console.log(doc.data);
    if (doc.status) {
        // console.log(doc.data.indexOf({ "check": false }));
        const checks =
            doc.data[
                doc.data
                    .map(function (item) {
                        return item.check;
                    })
                    .indexOf(true)
            ];
        console.log(checks);
        if (checks == undefined) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('MOBILE_NO_NOT_MATCH'),
                req.t('MOBILE_NO_NOT_MATCH') /* user_formate.user_ID(doc.data[0]) */,
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('DUPLICATE_MOBILE_NUMBER'),
                req.t('DUPLICATE_MOBILE_NUMBER'),
            );
        }
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.user_login = async (req, res) => {
    const cond = { email: req.body.email.toLowerCase(), isDeleted: false, isActive: true };
    const proj = { _id: 1, email: 1, password: 1, mobile: 1, verification: 1, status: 1 };
    let messages = '';
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        // console.log(doc.data.status);
        if (doc.data.status == 'completed') {
            if (doc.data.verification.email && doc.data.verification.mobile) {
                // console.log(bcrypt.hashSync(req.body.password, 10));
                // console.log(bcrypt.compareSync(req.body.password, doc.data.password));
                if (
                    req.body.password != doc.data.password &&
                    bcrypt.compareSync(req.body.password, doc.data.password)
                ) {
                    const otp = common_fun.getFourDigitOTP();
                    await base_control.update(user, doc.data._id, {
                        'otp.no': otp,
                        'otp.expiry_date': common_fun.timestampNow(),
                    });
                    messages =
                        '<p>' +
                        req.t('DEAR_DIGISCHEDULER_USER') +
                        '<br>' +
                        otp +
                        ' ' +
                        req.t(
                            'IS_SECRET_OTP_FOR_YOU_ACCOUNT_LOGIN_THIS_VALID_FOR_3MIN_PLS_DO_NOT_SHARE_OTP_WITH_ANYONE',
                        ) +
                        '</p>';
                    if (req.body.otp_mode == 'email') {
                        common_fun.send_email(
                            doc.data.email,
                            req.t('DIGISCHEDULER_ALERT'),
                            messages,
                        );
                    } else {
                        const sms_messages = `${otp} ` + req.t('IS_YOUR_DIGISCHEDULER_LOGIN_OTP');
                        if (doc.data.mobile) {
                            common_fun.send_sms(doc.data.mobile, sms_messages);
                        }
                    }
                    // send OTP to slack channel
                    const slack_chat_data = `Login -> Email : ${doc.data.email} - OTP : ${otp}`;
                    common_fun.send_otp_to_slack(slack_chat_data);
                    const response = { _id: doc.data._id, email: doc.data.email };
                    common_files.com_response(
                        res,
                        200,
                        true,
                        req.t('OTP_SEND'),
                        response /* user_formate.user_ID(doc.data[0]) */,
                    );
                } else {
                    common_files.com_response(
                        res,
                        500,
                        false,
                        req.t('PASSWORD_NOT_MATCH'),
                        req.t('PASSWORD_NOT_MATCH'),
                    );
                }
            } else {
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('YOU_NEED_TO_SIGNUP_FIRST'),
                    req.t('YOU_NEED_TO_SIGNUP_FIRST'),
                );
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('YOUR_PROFILE_IS_NOT_COMPLETED_ASK_ADMIN'),
                req.t('YOUR_PROFILE_IS_NOT_COMPLETED_ASK_ADMIN'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};
exports.university_user_login = async (req, res) => {
    try {
        const cond = { email: req.body.email.toLowerCase(), isDeleted: false, isActive: true };
        const proj = { _id: 1, email: 1, name: 1, password: 1, role: 1, user_id: 1 };
        const user_doc = await base_control.get(user, cond, proj);
        if (!user_doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_ADMIN_LOGIN'),
                        req.t('UNABLE_TO_FIND_ADMIN_LOGIN'),
                    ),
                );
        if (req.body.password != user_doc.data.password)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('PASSWORD_NOT_MATCH'),
                        req.t('PASSWORD_NOT_MATCH'),
                    ),
                );

        const univ_query = {
            _user_id: ObjectId(user_doc.data._id),
            isDeleted: false,
            isActive: true,
        };
        const university_data = await base_control.get(digi_university, univ_query, { _id: 1 });
        let university = {};
        if (university_data.status) university = { status: true };
        else university = { status: false };
        delete user_doc.data.password;
        const tokens = await tokenUtil.generateAuthTokens(user_doc.data);
        const respData = {
            ...user_doc.data.toObject(),
            tokens,
            /* roles, report_to, */ university,
        };
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('USER_LOGGED_IN_SUCCESSFULLY'),
                    respData,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
exports.profile_update = async (req, res) => {
    try {
        let objs = {};
        const contact = [];
        const cor = [];
        const user_check = await base_control.get(
            user,
            { _id: req.body.id, isDeleted: false, isActive: true },
            {},
        );
        if (user_check.status) {
            if (user_check.data.user_type == constant.EVENT_WHOM.STUDENT) {
                objs = {
                    dob: req.body.dob,
                    'address._nationality_id': req.body._nationality_id,
                    'address.building': req.body.building,
                    'address.city': req.body.city,
                    'address.district': req.body.district,
                    'address.zip_code': req.body.zip_code,
                    'address.unit': req.body.unit,
                    // 'address.passport_no': req.body.passport_no,
                    // 'office.office_extension': req.body.office_extension,
                    // 'office.office_room_no': req.body.office_room_no,
                    // status: 'submitted'
                };
                if (req.body.passport_no != undefined && req.body.passport_no.length != 0) {
                    Object.assign(objs, { 'address.passport_no': req.body.passport_no });
                }
                if (req.body.family_name != undefined && req.body.family_name.length != 0) {
                    Object.assign(objs, { 'name.family_name': req.body.family_name });
                }
                if (req.body.contact.type != undefined) {
                    if (req.body.contact.type == constant.RELATION.PARENT) {
                        const contact_father_obj = {};
                        const contact_mother_obj = {};
                        Object.assign(contact_father_obj, {
                            relation_type: constant.RELATION.FATHER,
                        });
                        Object.assign(contact_mother_obj, {
                            relation_type: constant.RELATION.MOTHER,
                        });

                        if (
                            req.body.contact.father_name != undefined &&
                            req.body.contact.father_name.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                name: req.body.contact.father_name,
                            });
                        }
                        if (
                            req.body.contact.father_mobile != undefined &&
                            req.body.contact.father_mobile.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                mobile: req.body.contact.father_mobile,
                            });
                        }
                        if (
                            req.body.contact.father_email != undefined &&
                            req.body.contact.father_email.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                email: req.body.contact.father_email,
                            });
                        }
                        if (
                            req.body.contact.mother_name != undefined &&
                            req.body.contact.mother_name.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                name: req.body.contact.mother_name,
                            });
                        }
                        if (
                            req.body.contact.mother_mobile != undefined &&
                            req.body.contact.mother_mobile.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                mobile: req.body.contact.mother_mobile,
                            });
                        }
                        if (
                            req.body.contact.mother_email != undefined &&
                            req.body.contact.mother_email.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                email: req.body.contact.mother_email,
                            });
                        }
                        if (contact_father_obj != {}) contact.push(contact_father_obj);
                        if (contact_mother_obj != {}) contact.push(contact_mother_obj);
                    } else if (req.body.contact.type == constant.RELATION.GUARDIAN) {
                        const contact_obj = {};
                        Object.assign(contact_obj, { relation_type: constant.RELATION.GUARDIAN });

                        if (
                            req.body.contact.guardian_name != undefined &&
                            req.body.contact.guardian_name.length != 0
                        ) {
                            Object.assign(contact_obj, { name: req.body.contact.guardian_name });
                        }
                        if (
                            req.body.contact.guardian_mobile != undefined &&
                            req.body.contact.guardian_mobile.length != 0
                        ) {
                            Object.assign(contact_obj, {
                                mobile: req.body.contact.guardian_mobile,
                            });
                        }
                        if (
                            req.body.contact.guardian_email != undefined &&
                            req.body.contact.guardian_email.length != 0
                        ) {
                            Object.assign(contact_obj, { email: req.body.contact.guardian_email });
                        }
                        if (contact_obj != {}) contact.push(contact_obj);
                    } else if (req.body.contact.type == constant.RELATION.SPOUSE) {
                        const contact_obj = {};
                        Object.assign(contact_obj, { relation_type: constant.RELATION.SPOUSE });

                        if (
                            req.body.contact.spouse_name != undefined &&
                            req.body.contact.spouse_name.length != 0
                        ) {
                            Object.assign(contact_obj, { name: req.body.contact.spouse_name });
                        }
                        if (
                            req.body.contact.spouse_mobile != undefined &&
                            req.body.contact.spouse_mobile.length != 0
                        ) {
                            Object.assign(contact_obj, { mobile: req.body.contact.spouse_mobile });
                        }
                        if (
                            req.body.contact.spouse_email != undefined &&
                            req.body.contact.spouse_email.length != 0
                        ) {
                            Object.assign(contact_obj, { email: req.body.contact.spouse_email });
                        }
                        if (contact_obj != {}) contact.push(contact_obj);
                    }
                }
                Object.assign(objs, { contact });
                if (req.body.academic_no != undefined && !req.body.academic_no) {
                    cor.push('academic_no');
                }
                if (req.body.enrollment_year != undefined && !req.body.enrollment_year) {
                    cor.push('enrollment_year');
                }
                if (req.body.program_no != undefined && !req.body.program_no) {
                    cor.push('program_no');
                }
            } else {
                objs = {
                    dob: req.body.dob,
                    enrollment_year: req.body.enrollment_year,
                    'address._nationality_id': req.body._nationality_id,
                    'address.building': req.body.building,
                    'address.city': req.body.city,
                    'address.district': req.body.district,
                    'address.zip_code': req.body.zip_code,
                    'address.unit': req.body.unit,
                    'office.office_extension': req.body.office_extension,
                    'office.office_room_no': req.body.office_room_no,
                    // status: 'submitted'
                };
                if (req.body.family_name != undefined && req.body.family_name.length != 0) {
                    Object.assign(objs, { 'name.family_name': req.body.family_name });
                }
                if (req.body.passport_no != undefined && req.body.passport_no.length != 0) {
                    Object.assign(objs, { 'address.passport_no': req.body.passport_no });
                }
                if (req.body.employee_id != undefined && !req.body.employee_id) {
                    cor.push('employee_id');
                }
            }
            if (req.body.first_name != undefined && !req.body.first_name) {
                cor.push('first_name');
            }
            if (req.body.last_name != undefined && !req.body.last_name) {
                cor.push('last_name');
            }
            if (req.body.middle_name != undefined && !req.body.middle_name) {
                cor.push('middle_name');
            }
            if (req.body.gender != undefined && !req.body.gender) {
                cor.push('gender');
            }
            if (req.body.nationality_id != undefined && !req.body.nationality_id) {
                cor.push('nationality_id');
            }
            objs.correction = cor;
            if (cor.length != 0) {
                Object.assign(objs, { 'verification.data': constant.CORRECTION_REQUIRED });
            } else {
                Object.assign(objs, { 'verification.data': constant.CORRECT });
            }
            // let doc = { status: true };
            // console.log(objs);
            if (util_key.SERVICES.DOCUMENT_SEC_NEED === 'false') objs.status = 'submitted';
            const doc = await base_control.update(user, req.body.id, objs);
            if (doc.status) {
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    201,
                    true,
                    req.t('USER_PROFILE_UPDATED_SUCCESSFULLY'),
                    doc.data,
                );
            } else {
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('ERROR_UNABLE_TO_UPDATE_PROFILE_PLS_VALIDATE_EACH_AND_EVERY_FIELDS'),
                    req.t('ERROR_UNABLE_TO_UPDATE_PROFILE_PLS_VALIDATE_EACH_AND_EVERY_FIELDS'),
                );
            }
        } else {
            common_files.sendResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('ERROR_IN_USER_ID_IS_NOT_MATCH_IN_DATABASE'),
                req.t('USER_ID_IS_NOT_MATCH_IN_DATABASE'),
            );
        }
    } catch (error) {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR'),
            req.t('UNABLE_TO_UPDATE_PROFILE_PLS_VALIDATE_EACH_AND_EVERY_FIELDS') + error,
        );
    }
};

exports.profile_document_update = async (req, res) => {
    const objs = {
        'student_docs._school_certificate_doc': req.body._school_certificate_doc
            ? req.body._school_certificate_doc
            : '',
        'student_docs._entrance_exam_certificate_doc': req.body._entrance_exam_certificate_doc
            ? req.body._entrance_exam_certificate_doc
            : '',
        'enrollment._admission_order_doc': req.body._admission_order_doc
            ? req.body._admission_order_doc
            : '',
        'id_doc._college_id_doc': req.body._college_id_doc ? req.body._college_id_doc : '',
        'address._nationality_id_doc': req.body._nationality_id_doc
            ? req.body._nationality_id_doc
            : '',
        'address._address_doc': req.body._address_doc ? req.body._address_doc : '',
        status: 'submitted',
    };
    const doc = await base_control.update(user, ObjectId(req.body.id), objs);
    if (doc.status) {
        //Email,SMS Push to user
        const user_data = await base_control.get(
            user,
            { _id: ObjectId(req.body.id) },
            { name: 1, mobile: 1, email: 1 },
        );
        const name = user_data.data.name.middle
            ? user_data.data.name.first +
              ' ' +
              user_data.data.name.middle +
              ' ' +
              user_data.data.name.last
            : user_data.data.name.first + ' ' + user_data.data.name.last;
        const sms_message = req.t(
            'YOUR_SIGNUP_PROCESS_IN_DIGI_CLASS_IS_COMPLETED_PLEASE_VISIT_ADMIN_OFFICE_TO_COMPLETE_YOUR_BIOMETRIC_PROCESS_AND_REGISTRATION',
        );
        const email_message =
            '<p>' +
            req.t('DEAR') +
            ' ' +
            name +
            ',' +
            common_fun.emailGreetingContent() +
            req.t('PROFILE_DOC_UPDATE_MSG') +
            common_fun.emailRegardsContent() +
            '</p>';
        common_fun.send_email(user_data.data.email, req.t('DIGISCHEDULER_ALERT'), email_message);
        if (user_data.data.mobile) common_fun.send_sms(user_data.data.mobile, sms_message);

        common_files.sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('USER_DOCUMENTS_UPDATE_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.sendResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.profile_staff_document_update = async (req, res) => {
    const objs = {
        'id_doc._employee_id_doc': req.body._employee_id_doc ? req.body._employee_id_doc : '',
        'address._nationality_id_doc': req.body._nationality_id_doc
            ? req.body._nationality_id_doc
            : '',
        'address._address_doc': req.body._address_doc ? req.body._address_doc : '',
        'qualifications.degree': [
            {
                // degree_name: req.body.degree_name,
                _degree_doc: req.body._degree_doc ? req.body._degree_doc : '',
            },
        ],
        'enrollment._appointment_order_doc': req.body._appointment_order_doc
            ? req.body._appointment_order_doc
            : '',
        status: 'submitted',
    };

    const doc = await base_control.update(user, req.body.id, objs);
    if (doc.status) {
        //Email,SMS Push to user
        const user_data = await base_control.get(
            user,
            { _id: ObjectId(req.body.id) },
            { name: 1, mobile: 1, email: 1 },
        );
        const name = user_data.data.name.middle
            ? user_data.data.name.first +
              ' ' +
              user_data.data.name.middle +
              ' ' +
              user_data.data.name.last
            : user_data.data.name.first + ' ' + user_data.data.name.last;
        const sms_message = req.t('PROFILE_STAFF_DOC_UPDATE_MSG');
        const email_message =
            '<p>' +
            req.t('DEAR') +
            ' ' +
            name +
            ',' +
            common_fun.emailGreetingContent() +
            req.t('PROFILE_STAFF_DOC_UPDATE_EMAIL_MSG') +
            common_fun.emailRegardsContent() +
            '</p>';
        common_fun.send_email(user_data.data.email, req.t('DIGISCHEDULER_ALERT'), email_message);
        if (user_data.data.mobile) common_fun.send_sms(user_data.data.mobile, sms_message);

        common_files.com_response(
            res,
            201,
            true,
            req.t('USER_DOCUMENTS_UPDATE_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.staff_doc_edit = async (req, res) => {
    const objs = {};
    if (req.body._employee_id_doc != undefined && req.body._employee_id_doc.length != 0) {
        Object.assign(objs, { 'id_doc._employee_id_doc': req.body._employee_id_doc });
    }
    if (req.body._nationality_id_doc != undefined && req.body._nationality_id_doc.length != 0) {
        Object.assign(objs, { 'address._nationality_id_doc': req.body._nationality_id_doc });
    }
    if (req.body._address_doc != undefined && req.body._address_doc.length != 0) {
        Object.assign(objs, { 'address._address_doc': req.body._address_doc });
    }
    if (req.body._degree_doc != undefined && req.body._degree_doc.length != 0) {
        Object.assign(objs, { 'qualifications.degree': [{ _degree_doc: req.body._degree_doc }] });
    }
    if (
        req.body._appointment_order_doc != undefined &&
        req.body._appointment_order_doc.length != 0
    ) {
        Object.assign(objs, {
            'enrollment._appointment_order_doc': req.body._appointment_order_doc,
        });
    }

    //Student Datas
    if (
        req.body._school_certificate_doc != undefined &&
        req.body._school_certificate_doc.length != 0
    ) {
        Object.assign(objs, {
            'student_docs._school_certificate_doc': req.body._school_certificate_doc,
        });
    }
    if (
        req.body._entrance_exam_certificate_doc != undefined &&
        req.body._entrance_exam_certificate_doc.length != 0
    ) {
        Object.assign(objs, {
            'student_docs._entrance_exam_certificate_doc': req.body._entrance_exam_certificate_doc,
        });
    }
    if (req.body._admission_order_doc != undefined && req.body._admission_order_doc.length != 0) {
        Object.assign(objs, { 'enrollment._admission_order_doc': req.body._admission_order_doc });
    }
    if (req.body._college_id_doc != undefined && req.body._college_id_doc.length != 0) {
        Object.assign(objs, { 'id_doc._college_id_doc': req.body._college_id_doc });
    }

    const doc = await base_control.update(user, req.body.id, objs);
    // let doc = { status: false, data: req.body }
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            req.t('USER_DOCUMENTS_UPDATED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.user_list = async (req, res) => {
    const filter = req.params.filter;
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [{ $match: { isDeleted: false, isActive: true } }];
    let aggre_len = { isDeleted: false, isActive: true };

    if (filter == 'submitted') {
        aggre.push({ $match: { status: 'submitted' } });
        aggre_len.status = 'submitted';
    } else if (filter == constant.VALID) {
        aggre.push({ $match: { status: constant.VALID } });
        aggre_len.status = constant.VALID;
    } else if (filter == constant.INVALID) {
        aggre.push({ $match: { status: constant.INVALID } });
        aggre_len.status = constant.INVALID;
    } else if (filter == 'mismatch') {
        aggre.push({ $match: { status: 'submitted' } });
        aggre.push({ $match: { 'verification.data': constant.CORRECTION_REQUIRED } });
        aggre_len = {
            isDeleted: false,
            isActive: true,
            status: 'submitted',
            'verification.data': constant.CORRECTION_REQUIRED,
        };
    }

    aggre.push({ $sort: { updatedAt: -1 } }, { $skip: skips }, { $limit: limits });
    const doc = await base_control.get_aggregate(user, aggre);
    if (doc.status) {
        // const response = {
        //     count: {
        //         allCount: totalPages,
        //         validCount: data_valid_count,
        //         pendingCount: data_pending_count,
        //         pendingAllCount: data_pending_all_count,
        //         pendingDataCount: data_pending_data_count,
        //         pendingBioCount: data_pending_bio_count,
        //     },
        //     data: doc.data
        // };

        const totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('USER_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* user_formate.user(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.user_mail_push = async (req, res) => {
    let messages = '';
    const user_lists = await base_control.get_list(user, { _id: req.body.id }, {});
    if (user_lists.status) {
        await user_lists.data.forEach(async (user_datas, index) => {
            token = crypto.randomBytes(16).toString('hex');
            let date_f = new Date();
            date_f = moment(date_f).add(3, 'days').format('D MMM YYYY');
            if (req.body.type == 'signup') {
                const temp_pass = common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
                // if (!user_datas.verification.email) {
                await base_control.update(user, user_datas._id, {
                    password: temp_pass,
                    status: 'invited',
                });
                // if (AUTH_PASSWORD_SYNC === 'true') {
                //     digiAuthService
                //         .syncUser({
                //             employeeOrAcademicId: user_datas.user_id,
                //             userId: user_datas._id,
                //             password: temp_pass,
                //         })
                //         .then((updatedUser) => {
                //             console.log(
                //                 'userController -> set_password -> updatedUser:',
                //                 updatedUser,
                //             );
                //         })
                //         .catch((err) => {
                //             console.log('userController -> set_password -> err:', err);
                //         });
                // }
                const name = user_datas.name.middle
                    ? user_datas.name.first +
                      ' ' +
                      user_datas.name.middle +
                      ' ' +
                      user_datas.name.last
                    : user_datas.name.first + ' ' + user_datas.name.last;
                const links =
                    user_datas.user_type != 'staff'
                        ? util_key.SIGN_UP_URL
                        : util_key.STAFF_SIGN_UP_URL;
                const replace_data = {
                    v_name: name,
                    // v_link: `https://digischeduler.netlify.com/signup?token=${token}`,
                    v_link: `${util_key.FE_URL}/signup`,
                    v_password: temp_pass,
                    v_date_expired: `${date_f}`, // Date.toString('d M Y'),
                    v_admin_sign: 'Academic Affairs',
                    video_link: `${links}`,
                };
                const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                messages = req.body.message.replace(re, (matched) => replace_data[matched]);
            } else if (req.body.type == 'valid') {
                const name = user_datas.name.middle
                    ? user_datas.name.first +
                      ' ' +
                      user_datas.name.middle +
                      ' ' +
                      user_datas.name.last
                    : user_datas.name.first + ' ' + user_datas.name.last;
                const replace_data = {
                    User: name,
                };
                const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                messages = req.body.message.replace(re, (matched) => replace_data[matched]);
                // messages = req.body.message
            } else if (req.body.type == 'invalid') {
                const name = user_datas.name.middle
                    ? user_datas.name.first +
                      ' ' +
                      user_datas.name.middle +
                      ' ' +
                      user_datas.name.last
                    : user_datas.name.first + ' ' + user_datas.name.last;
                const replace_data = {
                    User: name,
                };
                const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                messages = req.body.message.replace(re, (matched) => replace_data[matched]);
                // messages = req.body.message
            }
            // const mailOptions = {
            //     from: util_key.EMAIL_SENDER_ADDRESS, // sender address
            //     to: user_datas.email, // emails, // receiver
            //     subject: 'Digi Scheduler Account', // Subject line
            //     html: messages,
            // };
            // await common_fun.send_mail(mailOptions, (error) => {
            //     if (!error) {
            //         console.log('mail sent : ' + user_datas.email);
            //     } else {
            //         console.log('Mail Error : ', error);
            //     }
            // });
            common_fun.send_email(user_datas.email, req.t('DIGISCHEDULER_ALERT'), messages);
            if (req.body.id.length == index + 1) {
                common_files.sendResponseWithRequest(
                    req,
                    res,
                    201,
                    true,
                    req.t('MAIL_SEND_SUCCESSFULLY'),
                    req.t('MAIL_SEND_SUCCESSFULLY'),
                );
            }
        });
    } else {
        common_files.sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_IN_USER_DATA_IS_NOT_MATCH_DATABASE'),
            user_lists.data,
        );
    }
};

exports.user_edit = async (req, res) => {
    let docs = { status: false };
    const obj = {};
    const contact = [];
    let courseScheduleQuery = {};
    let courseScheduleUpdate = {};
    let studentGroupUpdate = {};
    const nameObject = {};
    // console.log(req.body.id);
    const user_check = await base_control.get(
        user,
        { _id: req.body.id, isDeleted: false, isActive: true },
        {},
    );
    if (user_check.status) {
        if (req.body.email != undefined && req.body.email.length != 0) {
            Object.assign(obj, { email: req.body.email.toLowerCase() });
        }
        if (req.body.first_name != undefined && req.body.first_name.length != 0) {
            Object.assign(obj, { 'name.first': req.body.first_name });
            nameObject.first = req.body.first_name;
        }
        if (req.body.last_name != undefined && req.body.last_name.length != 0) {
            Object.assign(obj, { 'name.last': req.body.last_name });
            nameObject.last = req.body.last_name;
        }
        if (req.body.middle_name != undefined) {
            Object.assign(obj, { 'name.middle': req.body.middle_name });
            if (req.body.middle_name.length !== 0) nameObject.middle = req.body.middle_name;
        }
        if (req.body.family_name != undefined) {
            Object.assign(obj, { 'name.family': req.body.family_name });
            if (req.body.family_name.length !== 0) nameObject.family = req.body.family_name;
        }
        if (req.body.gender != undefined && req.body.gender.length != 0) {
            Object.assign(obj, { gender: req.body.gender });
        }
        if (req.body.nationality_id != undefined && req.body.nationality_id.length != 0) {
            if (util_key.SERVICES.NATIONALITY_ID === 'true') {
                const user_check = await base_control.get(
                    user,
                    {
                        _id: { $ne: ObjectId(req.body.id) },
                        'address.nationality_id': req.body.nationality_id.trim(),
                        isDeleted: false,
                    },
                    { _id: 1 },
                );
                if (user_check.status)
                    return res
                        .status(409)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                409,
                                false,
                                req.t('DUPLICATE_NATIONALITY_ID_FOUND'),
                                req.t('DUPLICATE_NATIONALITY_ID_FOUND'),
                            ),
                        );
            }
            Object.assign(obj, { 'address.nationality_id': req.body.nationality_id.trim() });
        }
        if (req.body.building != undefined && req.body.building.length != 0) {
            Object.assign(obj, { 'address.building': req.body.building });
        }
        if (req.body.city != undefined && req.body.city.length != 0) {
            Object.assign(obj, { 'address.city': req.body.city });
        }
        if (req.body.district != undefined && req.body.district.length != 0) {
            Object.assign(obj, { 'address.district': req.body.district });
        }
        if (req.body.zip_code != undefined && req.body.zip_code.length != 0) {
            Object.assign(obj, { 'address.zip_code': req.body.zip_code });
        }
        if (req.body.unit != undefined && req.body.unit.length != 0) {
            Object.assign(obj, { 'address.unit': req.body.unit });
        }
        if (req.body.passport_no != undefined && req.body.passport_no.length != 0) {
            Object.assign(obj, { 'address.passport_no': req.body.passport_no });
        }
        if (req.body.dob != undefined && req.body.dob.length != 0) {
            Object.assign(obj, { dob: req.body.dob });
        }
        if (req.body._nationality_id != undefined && req.body._nationality_id.length != 0) {
            Object.assign(obj, { 'address._nationality_id': req.body._nationality_id });
        }
        if (
            // DEPLOY_TO &&
            // DEPLOY_TO === 'india' &&
            SERVICES.MOBILE === 'false' &&
            req.body.mobile != undefined &&
            req.body.mobile.length != 0
        ) {
            Object.assign(obj, { mobile: req.body.mobile });
        }

        if (user_check.data.user_type == constant.EVENT_WHOM.STAFF) {
            if (req.body.employee_id != undefined && req.body.employee_id.length != 0) {
                const query = {
                    user_type: {
                        $in: [
                            req.body.employee_id,
                            req.body.employee_id.toUpperCase(),
                            req.body.employee_id.toLowerCase(),
                        ],
                    },
                    isDeleted: false,
                };
                const project = { user_id: 1, email: 1, name: 1, user_type: 1 };
                const user_doc_validation = await base_control.get_list(user, query, project);
                if (user_doc_validation.status)
                    return res.status(409).send({
                        status_code: 409,
                        status: false,
                        message: req.t('DUPLICATE_EMPLOYEE_ID'),
                        data: req.t('DUPLICATE_EMPLOYEE_ID'),
                    });
                Object.assign(obj, { user_id: req.body.employee_id });
            }
            if (req.body.office_room_no != undefined && req.body.office_room_no.length != 0) {
                Object.assign(obj, { 'office.office_room_no': req.body.office_room_no });
            }
            if (req.body.office_extension != undefined && req.body.office_extension.length != 0) {
                Object.assign(obj, { 'office.office_extension': req.body.office_extension });
            }
            // Updating Name In Course Schedule
            if (nameObject !== {}) {
                courseScheduleQuery = {
                    isDeleted: false,
                    'staffs._staff_id': convertToMongoObjectId(req.body.id),
                };
                courseScheduleUpdate = {
                    $set: {
                        'staffs.$.staff_name': nameObject,
                    },
                };
            }
        } else {
            if (req.body.academic_no != undefined && req.body.academic_no.length != 0) {
                const query = {
                    user_type: {
                        $in: [
                            req.body.academic_no,
                            req.body.academic_no.toUpperCase(),
                            req.body.academic_no.toLowerCase(),
                        ],
                    },
                    isDeleted: false,
                };
                const project = { user_id: 1, email: 1, name: 1, user_type: 1 };
                const user_doc_validation = await base_control.get_list(user, query, project);
                if (user_doc_validation.status)
                    return res
                        .status(409)
                        .send(
                            common_files.comResponseWithRequest(
                                req,
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_ACADEMIC_NO'),
                                req.t('DUPLICATE_ACADEMIC_NO'),
                            ),
                        );
                Object.assign(obj, { user_id: req.body.academic_no });
            }
            if (req.body.program_no != undefined && req.body.program_no.length != 0) {
                Object.assign(obj, { 'program.program_no': req.body.program_no });
                const query = { code: req.body.program_no, isDeleted: false, isActive: true };
                const project = { _id: 1, name: 1 };
                const program_data = await base_control.get(program, query, project);
                Object.assign(obj, { 'program._program_id': program_data.data._id });
            }
            if (req.body.enrollment_year != undefined && req.body.enrollment_year.length != 0) {
                Object.assign(obj, { enrollment_year: req.body.enrollment_year });
            }
            if (req.body.batch != undefined && req.body.batch.length != 0) {
                Object.assign(obj, { batch: req.body.batch });
            }
            if (req.body.contact != undefined) {
                if (req.body.contact.type != undefined) {
                    if (req.body.contact.type == constant.RELATION.PARENT) {
                        const contact_father_obj = {};
                        const contact_mother_obj = {};
                        if (
                            req.body.contact.father_name != undefined &&
                            req.body.contact.father_name.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                relation_type: constant.RELATION.FATHER,
                            });
                            Object.assign(contact_father_obj, {
                                name: req.body.contact.father_name,
                            });
                        }
                        if (
                            req.body.contact.father_mobile != undefined &&
                            req.body.contact.father_mobile.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                mobile: req.body.contact.father_mobile,
                            });
                        }
                        if (
                            req.body.contact.father_email != undefined &&
                            req.body.contact.father_email.length != 0
                        ) {
                            Object.assign(contact_father_obj, {
                                email: req.body.contact.father_email,
                            });
                        }
                        if (
                            req.body.contact.mother_name != undefined &&
                            req.body.contact.mother_name.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                relation_type: constant.RELATION.MOTHER,
                            });
                            Object.assign(contact_mother_obj, {
                                name: req.body.contact.mother_name,
                            });
                        }
                        if (
                            req.body.contact.mother_mobile != undefined &&
                            req.body.contact.mother_mobile.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                mobile: req.body.contact.mother_mobile,
                            });
                        }
                        if (
                            req.body.contact.mother_email != undefined &&
                            req.body.contact.mother_email.length != 0
                        ) {
                            Object.assign(contact_mother_obj, {
                                email: req.body.contact.mother_email,
                            });
                        }
                        if (contact_father_obj != {}) contact.push(contact_father_obj);
                        if (contact_mother_obj != {}) contact.push(contact_mother_obj);
                    } else if (req.body.contact.type == constant.RELATION.GUARDIAN) {
                        const contact_obj = {};
                        if (
                            req.body.contact.guardian_name != undefined &&
                            req.body.contact.guardian_name.length != 0
                        ) {
                            Object.assign(contact_obj, {
                                relation_type: constant.RELATION.GUARDIAN,
                            });
                            Object.assign(contact_obj, { name: req.body.contact.guardian_name });
                        }
                        if (
                            req.body.contact.guardian_mobile != undefined &&
                            req.body.contact.guardian_mobile.length != 0
                        ) {
                            Object.assign(contact_obj, {
                                mobile: req.body.contact.guardian_mobile,
                            });
                        }
                        if (
                            req.body.contact.guardian_email != undefined &&
                            req.body.contact.guardian_email.length != 0
                        ) {
                            Object.assign(contact_obj, { email: req.body.contact.guardian_email });
                        }
                        if (contact_obj != {}) contact.push(contact_obj);
                    } else if (req.body.contact.type == constant.RELATION.SPOUSE) {
                        const contact_obj = {};
                        if (
                            req.body.contact.spouse_name != undefined &&
                            req.body.contact.spouse_name.length != 0
                        ) {
                            Object.assign(contact_obj, { relation_type: constant.RELATION.SPOUSE });
                            Object.assign(contact_obj, { name: req.body.contact.spouse_name });
                        }
                        if (
                            req.body.contact.spouse_mobile != undefined &&
                            req.body.contact.spouse_mobile.length != 0
                        ) {
                            Object.assign(contact_obj, { mobile: req.body.contact.spouse_mobile });
                        }
                        if (
                            req.body.contact.spouse_email != undefined &&
                            req.body.contact.spouse_email.length != 0
                        ) {
                            Object.assign(contact_obj, { email: req.body.contact.spouse_email });
                        }
                        if (contact_obj != {}) contact.push(contact_obj);
                    }
                }
                Object.assign(obj, { contact });
                // Parent App Flow
                if (process.env.PARENT_APP && process.env.PARENT_APP === 'true') {
                    const parentQuery = [];
                    const parentOperationCondition = [];
                    for (userContactElement of user_check.data.contact) {
                        if (userContactElement.email && userContactElement.mobile) {
                            parentQuery.push({
                                email: userContactElement.email,
                                mobile: userContactElement.mobile,
                            });
                            if (
                                !contact.find(
                                    (contactElement) =>
                                        contactElement.email === userContactElement.email &&
                                        contactElement.mobile === userContactElement.mobile,
                                )
                            ) {
                                parentOperationCondition.push({
                                    updateOne: {
                                        filter: {
                                            email: userContactElement.email,
                                            mobile: userContactElement.mobile,
                                        },
                                        update: {
                                            $pull: {
                                                childIds: convertToMongoObjectId(
                                                    user_check.data._id,
                                                ),
                                            },
                                        },
                                    },
                                });
                            }
                        }
                    }
                    for (contactElement of contact) {
                        if (contactElement.email && contactElement.mobile) {
                            parentQuery.push({
                                email: contactElement.email,
                                mobile:
                                    contactElement.mobile.length ===
                                    SERVICES.REACT_APP_COUNTRY_CODE_LENGTH
                                        ? contactElement.mobile
                                        : {
                                              $in: [
                                                  contactElement.mobile,
                                                  `${SERVICES.REACT_APP_COUNTRY_CODE}${contactElement.mobile}`,
                                              ],
                                          },
                            });
                        }
                    }
                    if (parentQuery.length) {
                        const parentCheck = await parentUserSchema.find(
                            { $or: parentQuery },
                            { email: 1, mobile: 1, childIds: 1 },
                        );
                        for (contactElement of contact) {
                            if (contactElement.email && contactElement.mobile) {
                                const parentIds = parentCheck
                                    .filter((userContactElement) =>
                                        contactElement.email === userContactElement.email &&
                                        (contactElement.mobile === contactElement.mobile.length) ===
                                            SERVICES.REACT_APP_COUNTRY_CODE_LENGTH
                                            ? userContactElement.mobile
                                            : `${SERVICES.REACT_APP_COUNTRY_CODE}${contactElement.mobile}` &&
                                              !userContactElement.childIds.find(
                                                  (childIdElement) =>
                                                      childIdElement.toString() ===
                                                      user_check.data._id.toString(),
                                              ),
                                    )
                                    .map((userContactElement) => userContactElement._id);
                                if (parentIds.length) {
                                    const updateParentOperation = {
                                        updateOne: {
                                            filter: {
                                                _id: { $in: parentIds },
                                            },
                                            update: {
                                                $push: {
                                                    childIds: convertToMongoObjectId(
                                                        user_check.data._id,
                                                    ),
                                                },
                                            },
                                        },
                                    };
                                    const isDuplicate = parentOperationCondition.some(
                                        (parentElement) => {
                                            return (
                                                JSON.stringify(parentElement) ===
                                                JSON.stringify(updateParentOperation)
                                            );
                                        },
                                    );
                                    if (!isDuplicate) {
                                        parentOperationCondition.push(updateParentOperation);
                                    }
                                }
                            }
                        }
                        if (parentOperationCondition.length)
                            await parentUserSchema.bulkWrite(parentOperationCondition);
                    }
                }
            }
            // Updating Name In Course Schedule
            if (nameObject !== {}) {
                courseScheduleQuery = {
                    isDeleted: false,
                    'students._id': convertToMongoObjectId(req.body.id),
                };
                courseScheduleUpdate = {
                    $set: {
                        'students.$.name': nameObject,
                    },
                };
                studentGroupUpdate =
                    req.body.academic_no !== undefined && req.body.academic_no.length !== 0
                        ? {
                              'groups.$[i].students.$[j].academic_no': req.body.academic_no,
                              'groups.$[i].students.$[j].name': nameObject,
                          }
                        : {
                              'groups.$[i].students.$[j].name': nameObject,
                          };
                await changeUserName({ userId: req.body.id, name: nameObject });
            }
        }
        obj.correction = [];
        // Object.assign(obj, { 'verification.data': constant.DONE });
        docs = await base_control.update(user, req.body.id, obj);
        if (docs.status) {
            if (courseScheduleUpdate !== {} && courseScheduleQuery !== {})
                console.log(
                    await base_control.update_push_pull_many(
                        course_schedule,
                        courseScheduleQuery,
                        courseScheduleUpdate,
                    ),
                );

            // Student Group Data update
            if (studentGroupUpdate !== {}) {
                const studentGroupData = await base_control.get_list(
                    student_group,
                    { 'groups.students._student_id': ObjectId(req.body.id) },
                    {},
                );
                if (studentGroupData.status) {
                    const bulk_data = [];
                    for (sgElement of studentGroupData.data) {
                        for (groupsElement of sgElement.groups) {
                            if (
                                groupsElement.students.find(
                                    (studentElement) =>
                                        studentElement._student_id.toString() ===
                                        req.body.id.toString(),
                                )
                            )
                                bulk_data.push({
                                    updateOne: {
                                        filter: {
                                            _id: ObjectId(sgElement._id),
                                        },
                                        update: {
                                            $set: studentGroupUpdate,
                                        },
                                        arrayFilters: [
                                            { 'i._id': ObjectId(groupsElement._id) },
                                            { 'j._student_id': ObjectId(req.body.id) },
                                        ],
                                    },
                                });
                        }
                    }
                    const changes = await base_control.bulk_write(student_group, bulk_data);
                    updateStudentGroupFlatCacheData();
                    if (!changes.status)
                        console.log('Unable to change course name in student group');
                    console.log('Course name changed in student group');
                }
            }
            await common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('USER_DATAS_MODIFIED_SUCCESSFULLY'),
                req.t('USER_DATAS_MODIFIED_SUCCESSFULLY'),
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), docs.data);
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_USER_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_USER_ID'),
        );
    }
};

exports.profile_status_update = async (req, res) => {
    const objs = {
        status: 'verified',
        'verification.data': req.body.data,
    };
    const doc = await base_control.update(user, req.body.id, objs);
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            req.t('USER_DOCUMENTS_UPDATE_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.user_face_bio_register = async (req, res) => {
    const objs = {};
    if (req.body.face) {
        Object.assign(objs, { 'verification.face': true });
    }
    if (req.body.finger) {
        Object.assign(objs, { 'verification.finger': true });
    }
    Object.assign(objs, { status: 'completed' });
    // objs = {
    //     'verification.face': true,
    //     'verification.finger': true
    // };
    const doc = await base_control.update(user, req.body.id, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('USER_FACE_FINGER_SUCCESSFULLY'), doc.data);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
        );
    }
};

exports.user_academic_allocation = async (req, res) => {
    // Checking is Staff Subject is Scheduled or Not
    const { status: cs_status, data: cs_data } = await base_control.get_list(
        course_schedule,
        {
            // _institution_id: ObjectId(_institution_id),
            // _institution_calendar_id: ObjectId(_institution_calendar_id),
            'staffs._staff_id': ObjectId(req.body.id),
            isDeleted: false,
            isActive: true,
        },
        {},
    );
    if (cs_status) {
        let subjects = [];
        let reqSubjects = [];
        for (element of cs_data) {
            subjects = [...subjects, ...element.subjects.map((ele) => ele._subject_id.toString())];
        }
        subjects = [...new Set(subjects)];
        for (subElement of req.body.data) {
            reqSubjects = [...reqSubjects, ...subElement._department_subject_id];
        }
        // console.log(subjects.filter((ele) => reqSubjects.includes(ele)));
        if (subjects.filter((ele) => reqSubjects.includes(ele)).length === 0)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('SCHEDULED_SUBJECTS_CANNOT_BE_REMOVED'),
                        req.t('SCHEDULED_SUBJECTS_CANNOT_BE_REMOVED'),
                    ),
                );
    }
    let doc = { status: true, data: '' };
    await base_control.update_condition(
        user,
        { _id: req.body.id },
        { $set: { academic_allocation: [] } },
    );
    if (req.body.data && req.body.data.length != 0) {
        req.body.data.forEach(async (element, index) => {
            let objs = {
                $push: {
                    academic_allocation: {
                        allocation_type: element.allocation_type,
                        _program_id: element._program_id,
                        _department_id: element._department_id,
                        // _department_division_id: element._department_division_id,
                        _department_subject_id: element._department_subject_id,
                    },
                },
            };
            if (
                element._department_division_id != undefined &&
                element._department_division_id.length == 24
            ) {
                objs = {
                    $push: {
                        academic_allocation: {
                            allocation_type: element.allocation_type,
                            _program_id: element._program_id,
                            _department_id: element._department_id,
                            _department_division_id: element._department_division_id,
                            _department_subject_id: element._department_subject_id,
                        },
                    },
                };
            }
            doc = await base_control.update_condition(user, { _id: req.body.id }, objs);
            if (index == req.body.data.length - 1) {
                if (req.body.user_type) {
                    objs = {
                        $set: {
                            institution_role: req.body.institution_role,
                            staff_employment_type: req.body.user_type,
                        },
                    };
                }
                doc = await base_control.update_condition(user, { _id: req.body.id }, objs);
                if (doc.status) {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        201,
                        true,
                        req.t('USER_ACADEMIC_IS_SUCCESSFULLY_ALLOCATED'),
                        doc.data,
                    );
                } else {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('ERROR'),
                        doc.data,
                    );
                }
            }
        });
    } else {
        if (req.body.user_type) {
            const objs = {
                $set: {
                    institution_role: req.body.institution_role,
                    staff_employment_type: req.body.user_type,
                },
            };
            doc = await base_control.update_condition(user, { _id: req.body.id }, objs);
        }
        if (doc.status) {
            common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('USER_ACADEMIC_IS_SUCCESSFULLY_ALLOCATED'),
                doc.data,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    }
};

exports.user_employment = async (req, res) => {
    let doc = { status: false, data: '' };
    let objs = {};
    if (req.body.user_employment_type == constant.FULL_TIME) {
        objs = {
            employment: {
                // user_type: req.body.user_type,
                // institution_role: req.body.institution_role,
                user_employment_type: req.body.user_employment_type,
                schedule_times: {
                    full_time: req.body.schedule_times,
                },
            },
        };
    } else if (req.body.user_employment_type == constant.PART_TIME) {
        if (req.body.user_schedule_type == constant.BY_DATE) {
            objs = {
                employment: {
                    // user_type: req.body.user_type,
                    // institution_role: req.body.institution_role,
                    user_employment_type: req.body.user_employment_type,
                    schedule_times: {
                        by_date: req.body.schedule_times,
                    },
                },
            };
        } else if (req.body.user_schedule_type == constant.BY_DAY) {
            // for (let i = 0; i < req.body.schedule_times.length; i++) {
            //     let f_start = req.body.schedule_times[i].start_time;
            //     let f_end = req.body.schedule_times[i].end_time;
            //     for (let j = i; j < req.body.schedule_times.length; j++) {
            //         if (req.body.schedule_times[i].days == req.body.schedule_times[j].days) {
            //             let s_start = req.body.schedule_times[j].start_time;
            //             let s_end = req.body.schedule_times[j].end_time;
            //             console.log(i, j, 'FS SS', f_start < s_start);
            //             console.log(i, j, 'FS SE', f_start > s_end);
            //             console.log(i, j, 'FE SE', f_start < s_start);
            //             console.log(i, j, 'FE SS', f_start > s_end);
            //             // console.log(i, j, 'FS FS', f_start > s_start);
            //             if (f_start < s_start && f_start > s_end) {
            //                 console.log('First ');
            //             }
            //         }
            //     }
            // }
            objs = {
                employment: {
                    user_type: req.body.user_type,
                    institution_role: req.body.institution_role,
                    user_employment_type: req.body.user_employment_type,
                    schedule_times: {
                        by_day: req.body.schedule_times,
                    },
                },
            };
        }
    }
    // if staff facial false not mandatory
    if (req.body?.staffFacial === false) {
        objs = {
            ...objs,
            'verification.face': true,
            'verification.finger': true,
            'verification.data': constant.DONE,
            status: constant.COMPLETED,
        };
    }
    // console.log(objs);
    doc = await base_control.update(user, req.body.id, objs);
    // console.log(doc);
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            201,
            true,
            req.t('USER_ACADEMIC_IS_SUCCESSFULLY_ALLOCATED'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_filter = async (req, res) => {
    // let skips = Number(req.query.limit * (req.query.pageNo - 1));
    // let limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false, isActive: true } },
        // { $skip: skips }, { $limit: limits }
    ];

    console.log('program', req.params.program);
    console.log('time', req.params.time);
    console.log('type', req.params.type);
    aggre.push({
        $project: {
            _id: 1,
            username: 1,
            employee_id: 1,
            email: 1,
            name: 1,
            gender: 1,
            'address.nationality_id': 1,
            status: 1,
        },
    });
    aggre.push({ $sort: { updatedAt: -1 } });
    // console.log(aggre);
    // let doc = await base_control.get_aggregate(user, aggre);
    if (doc.status) {
        // let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* user_formate.user(doc.data) */);
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('USER_LIST'),
            doc.data /* user_formate.user(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.user_dashboard = async (req, res) => {
    const cond = { _id: req.query.id, isDeleted: false, isActive: true };
    const proj = { _id: 1, email: 1, password: 1, name: 1 };
    // let messages = '';
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        if (doc.data.name.first == 'Bharath') {
            const response = { _id: doc.data._id, email: doc.data.email, role: 'dean' };
            common_files.com_response(
                res,
                200,
                true,
                'Dashboard',
                response /* user_formate.user_ID(doc.data[0]) */,
            );
        } else {
            const response = { _id: doc.data._id, email: doc.data.email, role: 'vice-dean' };
            common_files.com_response(
                res,
                200,
                true,
                'Dashboard',
                response /* user_formate.user_ID(doc.data[0]) */,
            );
        }
    } else {
        common_files.com_response(res, 500, false, req.t('ID_NOT_FOUND'), req.t('ID_NOT_FOUND'));
    }
};

exports.list_all = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    let query;

    if (req.params.tab == 'all') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: { $not: { $eq: 'completed' } },
            // status: { $not: 'imported' },
        };
    } else if (req.params.tab == 'inactive') {
        query = {
            isActive: false,
            isDeleted: false,
            user_type: req.params.user_type,
        };
    } else if (req.params.tab == 'imported') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'imported',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
        };
    } else if (req.params.tab == 'submitted') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECT },
                // { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'mismatch') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'valid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
        };
    } else if (req.params.tab == 'invalid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [
                { 'verification.data': constant.INVALID },
                { 'verification.data': constant.DONE },
            ],
        };
    } else if (req.params.tab == 'invited') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (req.params.tab == 'expired') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (req.params.tab == 'completed') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'completed',
            $or: [
                { 'verification.data': constant.DONE },
                { 'verification.face': true },
                { 'verification.finger': true },
            ],
        };
    }

    const all_user = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: { $not: { $eq: 'completed' } },
    };
    const inactive_user = {
        isActive: false,
        isDeleted: false,
        user_type: req.params.user_type,
    };
    const imported = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'imported',
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const submitted = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'submitted',
        $and: [
            { 'verification.data': constant.CORRECT },
            // { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.face': false },
            { 'verification.finger': false },
        ],
    };
    const mismatch = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'submitted',
        $and: [
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.face': false },
            { 'verification.finger': false },
        ],
    };
    const valid = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'verified',
        $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
    };
    const invalid = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'verified',
        $or: [{ 'verification.data': constant.INVALID }, { 'verification.data': constant.DONE }],
    };
    const invited = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'invited',
        $expr: { $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.data': constant.CORRECT },
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const expired = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'invited',
        $expr: { $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        $or: [
            { 'verification.data': constant.PENDING },
            { 'verification.data': constant.CORRECT },
            { 'verification.data': constant.CORRECTION_REQUIRED },
            { 'verification.email': false },
            { 'verification.mobile': false },
        ],
    };
    const completed = {
        isActive: true,
        isDeleted: false,
        user_type: req.params.user_type,
        status: 'completed',
        $or: [
            { 'verification.data': constant.DONE },
            { 'verification.face': true },
            { 'verification.finger': true },
        ],
    };

    const all_user_count = (await base_control.get_count(user, all_user)).data;
    const inactive_user_count = (await base_control.get_count(user, inactive_user)).data;
    const imported_count = (await base_control.get_count(user, imported)).data;
    const submitted_count = (await base_control.get_count(user, submitted)).data;
    const mismatch_count = (await base_control.get_count(user, mismatch)).data;
    const valid_count = (await base_control.get_count(user, valid)).data;
    const invalid_count = (await base_control.get_count(user, invalid)).data;
    const invited_count = (await base_control.get_count(user, invited)).data;
    const expired_count = (await base_control.get_count(user, expired)).data;
    const completed_count = (await base_control.get_count(user, completed)).data;
    let faceReRegisterCount = 0;
    if (SERVICES.FACE_RE_REGISTER === 'true' && req.params.tab == 'all') {
        faceReRegisterCount = (
            await faceRegisterSchema.distinct('userId', {
                isDeleted: false,
                userType: req.params.user_type,
                status: constant.PENDING,
            })
        ).length;
    }

    const aggre = [{ $match: query }];
    if (req.params.user_type == 'staff') {
        if (req.params.tab == 'completed') {
            aggre.push({ $addFields: { program_ids: '$academic_allocation._program_id' } });
            aggre.push(
                {
                    $lookup: {
                        from: 'role_assigns',
                        localField: '_role_id',
                        foreignField: '_id',
                        as: 'role_data',
                    },
                },
                { $unwind: { path: '$role_data', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role_check: {
                            $filter: {
                                input: '$role_data.roles',
                                as: 'item',
                                cond: { $gte: ['$$item.isDefault', true] },
                            },
                        },
                    },
                },
                { $unwind: { path: '$role_check', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role: {
                            $cond: {
                                if: { $ne: ['$role_check', null] },
                                then: '$role_check.role_name',
                                else: '',
                            },
                        },
                    },
                },
            );
        }
    } else {
        aggre.push({
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'program._program_id',
                foreignField: '_id',
                as: 'programs',
            },
        });
        aggre.push({ $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } });
        aggre.push({ $addFields: { program_name: '$programs.name' } });
    }
    if (req.params.tab == 'all') {
        aggre.push({
            $addFields: {
                user_state: {
                    $switch: {
                        branches: [
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'imported'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Imported',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'correct'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "correct"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Submitted',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'error'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "error"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Mismatch',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'valid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Valid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'invalid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invalid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $lte: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invited',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $gt: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Expired',
                            },
                        ],
                        default: 'Unknown',
                    },
                },
            },
        });
    }
    // else if (req.params.tab == 'invited') {
    //     aggre.push(
    //         { $addFields: { hours: { $divide: [{ $subtract: ["$$NOW", "$updatedAt"] }, 3600000] } } },
    //         { $match: { 'hours': { $lte: 72 } } }
    //     );
    // } else if (req.params.tab == 'expired') {
    //     aggre.push(
    //         { $addFields: { hours: { $divide: [{ $subtract: ["$$NOW", "$updatedAt"] }, 3600000] } } },
    //         { $match: { 'hours': { $gt: 72 } } }
    //     );
    // }
    aggre.push({ $sort: { updatedAt: -1 } }, { $skip: skips }, { $limit: limits });
    const doc = await base_control.get_aggregate_with_count(user, aggre, query);
    const data_res = Array.isArray(doc.data) ? doc.data : [];
    const totalPages = Math.ceil(doc.totalDoc / limits);
    const response = {
        status_code: 200,
        status: true,
        message: req.t('USER_LIST'),
        totalDoc: doc.totalDoc,
        totalPages,
        currentPage: Number(req.query.pageNo),
        count: {
            all_active_user_count: all_user_count,
            inactive_user_count,
            imported_count,
            invited_count,
            submitted_count,
            mismatch_count,
            valid_count,
            invalid_count,
            expired_count,
            completed_count,
            faceReRegisterCount,
        },
        data: user_formate.user_list_formate(data_res),
    };
    common_files.listUserResponse(req, res, 200, response);
};

exports.list_all_user = async (req, res) => {
    let dbQuery = {
        isActive: true,
        isDeleted: false,
        user_type: req.query.user_type,
        status: 'completed',
        $or: [
            { 'verification.data': constant.DONE },
            { 'verification.face': true },
            { 'verification.finger': true },
        ],
    };
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const searchKey = req.query.searchKey;
    if (searchKey && searchKey.length) {
        const word = searchKey.split(/\s+/);
        if (word.length < 2) {
            dbQuery = {
                ...dbQuery,
                $and: [
                    { $or: [...dbQuery.$or] },
                    {
                        $or: [
                            { 'name.first': { $regex: searchKey, $options: 'i' } },
                            { 'name.middle': { $regex: searchKey, $options: 'i' } },
                            { 'name.last': { $regex: searchKey, $options: 'i' } },
                            { 'name.family': { $regex: searchKey, $options: 'i' } },
                            { email: { $regex: searchKey, $options: 'i' } },
                            { user_id: { $regex: searchKey, $options: 'i' } },
                        ],
                    },
                ],
            };
            delete dbQuery.$or;
        } else if (word.length === 2) {
            dbQuery = {
                ...dbQuery,
                $and: [
                    { $or: [...dbQuery.$or] },
                    {
                        $and: [
                            {
                                $or: [
                                    { 'name.first': { $regex: word[0], $options: 'i' } },
                                    { 'name.middle': { $regex: word[0], $options: 'i' } },
                                ],
                            },
                            {
                                $or: [
                                    { 'name.middle': { $regex: word[1], $options: 'i' } },
                                    { 'name.last': { $regex: word[1], $options: 'i' } },
                                    { 'name.family': { $regex: word[1], $options: 'i' } },
                                ],
                            },
                        ],
                    },
                ],
            };
            delete dbQuery.$or;
        } else if (word.length === 3) {
            dbQuery = {
                ...dbQuery,
                $and: [
                    { $or: [...dbQuery.$or] },
                    {
                        $and: [
                            { 'name.first.value': { $regex: word[0], $options: 'i' } },
                            { 'name.middle.value': { $regex: word[1], $options: 'i' } },
                            {
                                $or: [
                                    { 'name.last.value': { $regex: word[2], $options: 'i' } },
                                    { 'name.family.value': { $regex: word[2], $options: 'i' } },
                                ],
                            },
                        ],
                    },
                ],
            };
            delete dbQuery.$or;
        } else if (word.length === 4) {
            dbQuery = {
                ...dbQuery,
                $and: [
                    { $or: [...dbQuery.$or] },
                    {
                        $and: [
                            { 'name.first': { $regex: word[0], $options: 'i' } },
                            { 'name.middle': { $regex: word[1], $options: 'i' } },
                            { 'name.last': { $regex: word[2], $options: 'i' } },
                            {
                                $or: [{ 'name.family': { $regex: word[3], $options: 'i' } }],
                            },
                        ],
                    },
                ],
            };
            delete dbQuery.$or;
        }
    }
    const project = {
        name: 1,
    };
    const data = await user.find(dbQuery, project).skip(skips).limit(limits);
    const response = {
        status_code: 200,
        status: true,
        message: req.t('USER_LIST'),
        data,
    };
    common_files.listUserResponse(req, res, 200, response);
};

exports.exportUsers = async (req, res) => {
    try {
        let query;
        const { tab, user_type } = req.params;
        const { fields } = req.query;
        if (tab == 'all') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: { $not: { $eq: 'completed' } },
                // status: { $not: 'imported' },
            };
        } else if (tab == 'inactive') {
            query = {
                isActive: false,
                isDeleted: false,
                user_type,
            };
        } else if (tab == 'imported') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'imported',
                $or: [
                    { 'verification.data': constant.PENDING },
                    { 'verification.email': false },
                    { 'verification.mobile': false },
                ],
            };
        } else if (tab == 'submitted') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'submitted',
                $and: [
                    { 'verification.data': constant.CORRECT },
                    // { 'verification.data': constant.CORRECTION_REQUIRED },
                    { 'verification.face': false },
                    { 'verification.finger': false },
                ],
            };
        } else if (tab == 'mismatch') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'submitted',
                $and: [
                    { 'verification.data': constant.CORRECTION_REQUIRED },
                    { 'verification.face': false },
                    { 'verification.finger': false },
                ],
            };
        } else if (tab == 'valid') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'verified',
                $or: [
                    { 'verification.data': constant.VALID },
                    { 'verification.data': constant.DONE },
                ],
            };
        } else if (tab == 'invalid') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'verified',
                $or: [
                    { 'verification.data': constant.INVALID },
                    { 'verification.data': constant.DONE },
                ],
            };
        } else if (tab == 'invited') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'invited',
                $or: [
                    { 'verification.data': constant.PENDING },
                    { 'verification.data': constant.CORRECT },
                    { 'verification.data': constant.CORRECTION_REQUIRED },
                    { 'verification.email': false },
                    { 'verification.mobile': false },
                ],
                $expr: {
                    $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72],
                },
            };
        } else if (tab == 'expired') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'invited',
                $or: [
                    { 'verification.data': constant.PENDING },
                    { 'verification.data': constant.CORRECT },
                    { 'verification.data': constant.CORRECTION_REQUIRED },
                    { 'verification.email': false },
                    { 'verification.mobile': false },
                ],
                $expr: {
                    $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72],
                },
            };
        } else if (tab == 'completed') {
            query = {
                isActive: true,
                isDeleted: false,
                user_type,
                status: 'completed',
                $or: [
                    { 'verification.data': constant.DONE },
                    { 'verification.face': true },
                    { 'verification.finger': true },
                ],
            };
        }

        const aggre = [{ $match: query }];
        const customFields = JSON.parse(fields);
        const academicLoc = customFields.findIndex((fieldElement) => fieldElement === 'academic');
        customFields[academicLoc] = 'user_id';
        const projection = customFields.reduce((a, v) => ({ ...a, [v]: 1 }), {});
        if (user_type == 'staff') {
            if (tab == 'completed') {
                aggre.push({ $addFields: { program_ids: '$academic_allocation._program_id' } });
                aggre.push(
                    {
                        $lookup: {
                            from: 'role_assigns',
                            localField: '_role_id',
                            foreignField: '_id',
                            as: 'role_data',
                        },
                    },
                    { $unwind: { path: '$role_data', preserveNullAndEmptyArrays: true } },
                    {
                        $addFields: {
                            role_check: {
                                $filter: {
                                    input: '$role_data.roles',
                                    as: 'item',
                                    cond: { $gte: ['$$item.isDefault', true] },
                                },
                            },
                        },
                    },
                    { $unwind: { path: '$role_check', preserveNullAndEmptyArrays: true } },
                    {
                        $addFields: {
                            role: {
                                $cond: {
                                    if: { $ne: ['$role_check', null] },
                                    then: '$role_check.role_name',
                                    else: '',
                                },
                            },
                        },
                    },
                );
            }
        } else {
            aggre.push({
                $lookup: {
                    from: constant.DIGI_PROGRAM,
                    localField: 'program._program_id',
                    foreignField: '_id',
                    as: 'programs',
                },
            });
            aggre.push({ $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } });
            aggre.push({ $addFields: { program_name: '$programs.name' } });
        }
        if (tab == 'all') {
            aggre.push({
                $addFields: {
                    user_state: {
                        $switch: {
                            branches: [
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'imported'] },
                                            {
                                                $or: [
                                                    { $eq: ['$verification.data', 'pending'] },
                                                    { $eq: ['$verification.email', false] },
                                                    { $eq: ['$verification.mobile', false] },
                                                ],
                                            },
                                        ],
                                    },
                                    then: 'Imported',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'submitted'] },
                                            { $eq: ['$verification.data', 'correct'] },
                                            // {
                                            //     $or: [
                                            //         { $eq: ["$verification.data", "correct"] },
                                            //         { $eq: ["$verification.face", false] },
                                            //         { $eq: ["$verification.finger", false] }
                                            //     ]
                                            // }
                                        ],
                                    },
                                    then: 'Submitted',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'submitted'] },
                                            { $eq: ['$verification.data', 'error'] },
                                            // {
                                            //     $or: [
                                            //         { $eq: ["$verification.data", "error"] },
                                            //         { $eq: ["$verification.face", false] },
                                            //         { $eq: ["$verification.finger", false] }
                                            //     ]
                                            // }
                                        ],
                                    },
                                    then: 'Mismatch',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'verified'] },
                                            {
                                                $or: [
                                                    { $eq: ['$verification.data', 'valid'] },
                                                    { $eq: ['$verification.data', 'success'] },
                                                ],
                                            },
                                        ],
                                    },
                                    then: 'Valid',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'verified'] },
                                            {
                                                $or: [
                                                    { $eq: ['$verification.data', 'invalid'] },
                                                    { $eq: ['$verification.data', 'success'] },
                                                ],
                                            },
                                        ],
                                    },
                                    then: 'Invalid',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'invited'] },
                                            {
                                                $or: [
                                                    { $eq: ['$verification.data', 'pending'] },
                                                    { $eq: ['$verification.data', 'correct'] },
                                                    { $eq: ['$verification.data', 'error'] },
                                                    { $eq: ['$verification.email', false] },
                                                    { $eq: ['$verification.mobile', false] },
                                                ],
                                            },
                                            {
                                                $lte: [
                                                    {
                                                        $divide: [
                                                            { $subtract: ['$$NOW', '$updatedAt'] },
                                                            3600000,
                                                        ],
                                                    },
                                                    72,
                                                ],
                                            },
                                        ],
                                    },
                                    then: 'Invited',
                                },
                                {
                                    case: {
                                        $and: [
                                            { $eq: ['$status', 'invited'] },
                                            {
                                                $or: [
                                                    { $eq: ['$verification.data', 'pending'] },
                                                    { $eq: ['$verification.data', 'correct'] },
                                                    { $eq: ['$verification.data', 'error'] },
                                                    { $eq: ['$verification.email', false] },
                                                    { $eq: ['$verification.mobile', false] },
                                                ],
                                            },
                                            {
                                                $gt: [
                                                    {
                                                        $divide: [
                                                            { $subtract: ['$$NOW', '$updatedAt'] },
                                                            3600000,
                                                        ],
                                                    },
                                                    72,
                                                ],
                                            },
                                        ],
                                    },
                                    then: 'Expired',
                                },
                            ],
                            default: 'Unknown',
                        },
                    },
                },
            });
        }
        aggre.push({ $sort: { updatedAt: -1 } });
        aggre.push({ $project: projection });
        const doc = await base_control.get_aggregate_with_count(user, aggre, query);
        const data_res = Array.isArray(doc.data) ? doc.data : [];
        const response = {
            data: user_formate.user_list_formate(data_res),
        };
        const wb = new Excel.Workbook();
        wb.creator = 'DigiScheduler';
        wb.created = new Date();
        wb.modified = new Date();
        wb.lastPrinted = new Date();

        const worksheet = wb.addWorksheet('User Details', {
            properties: { showGridLines: false },
            views: [{ state: 'frozen', ySplit: 1 }],
        });

        // add column headers
        const columns = [];
        for (const field of customFields) {
            if (field === 'name') {
                columns.push({ header: 'first_name', key: 'first' });
                columns.push({ header: 'middle_name', key: 'middle' });
                columns.push({ header: 'last_name', key: 'last' });
            } else {
                columns.push({ header: field, key: field });
            }
        }
        worksheet.columns = columns;

        response.data.forEach((val) => {
            worksheet.addRow({
                first: val.name.first,
                middle: val.name.middle,
                last: val.name.last,
                family_name: val.name.family,
                user_type: val.user_type,
                user_id: val.user_type == 'student' ? val.academic : val.employee_id,
                gender: val.gender,
                email: val.email,
                batch: val.batch,
                enrollment_year: val.enrollment_year,
                dob: val.dob,
                nationality_id: val.address.nationality_id,
                user_state: val.user_state,
                status: val.status,
            });
        });
        const fileName = `${tab}_${user_type}.xlsx`;
        return {
            statusCode: 200,
            data: {
                wb,
                fileName,
            },
        };
    } catch (e) {
        console.error(e);
        if (e instanceof Error) {
            throw e;
        } else {
            throw new Error(e);
        }
    }
};

exports.table_get_id = async (req, res) => {
    //try {
    let aggre = [];
    if (req.params.user_type == 'staff') {
        aggre = [
            { $match: { isDeleted: false, isActive: true } },
            { $match: { user_type: 'staff' } },
            { $sort: { updatedAt: -1 } },
        ];
    } else {
        aggre = [
            { $match: { isDeleted: false, isActive: true } },
            { $match: { user_type: 'student' } },
            { $sort: { updatedAt: -1 } },
            {
                $lookup: {
                    from: constant.DIGI_PROGRAM,
                    localField: 'program._program_id',
                    foreignField: '_id',
                    as: 'programs',
                },
            },
            { $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } },
            { $addFields: { program_name: '$programs.name' } },
        ];
    }

    const doc = await base_control.get_aggregate(user, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('USER_DETAILS'),
            /* doc.data[0] */ user_formate.user_id_formate(doc.data[0]),
        );
        // common_files.com_response(res, 200, true, "user details", doc.data[0]/* user_formate.user_ID(doc.data[0]) */);
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_FOUND_OR_DATA_MISMATCH'),
            doc.data,
        );
    }
    // } catch (error) {
    //     console.log('Internal Server Error ', error);
    //     common_files.com_response(res, 500, false, "Internal Server Error : Catch : ", error);
    // }
};

exports.send_mobile_otp = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    const proj = { _id: 1 };
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        const otp = common_fun.getFourDigitOTP();
        const sms_messages = `${otp} ` + req.t('IS_YOUR_DIGISCHEDULER_PORTAL_OTP_CODE');
        const update_pass = await base_control.update(user, doc.data._id, {
            'otp.no': otp,
            'otp.expiry_date': common_fun.timestampNow(),
        });
        if (update_pass.status) {
            // function sentMessageCallback(error, response, body) {
            //     if (common_fun.checkSMSResponse(response, body)) {
            //         console.log('sms sent :' + req.body.mobile);
            //     } else {
            //         console.log('sms error : ', error);
            //     }
            // }
            // common_fun.send_message(req.body.mobile, sms_messages, sentMessageCallback);
            common_fun.send_sms(req.body.mobile, sms_messages);

            const slack_chat_data = `Mobile Register -> Mobile : ${req.body.mobile} - OTP : ${otp}`;
            common_fun.send_otp_to_slack(slack_chat_data);
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('MOBILE_OTP_SEND'),
                req.t('MOBILE_OTP_SEND') /* user_formate.user_ID(doc.data[0]) */,
            );
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('UNABLE_TO_SEND_OTP_CHECK_MOBILE_NO'),
                req.t('UNABLE_TO_SEND_OTP_CHECK_MOBILE_NO'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.change_mobile = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    const proj = {};
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        if (doc.data.otp.no == req.body.otp) {
            const secondsDifference = common_fun.getSecondsDifference(
                doc.data.otp.expiry_date,
                common_fun.timestampNow(),
            );
            if (secondsDifference <= util_key.OTP_EXPIRY_DURATION_IN_SECS) {
                let update_pass = { status: true };
                const objs = {
                    user_id: doc.data._id,
                    user_type: doc.data.user_type,
                    section: 'Profile',
                    title: 'Mobile Number',
                    before_update: doc.data.mobile,
                    after_update: req.body.mobile,
                    editor: req.body.admin_id,
                    reason: req.body.reason,
                };
                update_pass = await base_control.update(user, doc.data._id, {
                    mobile: req.body.mobile,
                });
                if (update_pass.status) {
                    await base_control.insert(user_history, objs);

                    // digiAuthService
                    //     .syncUser({
                    //         employeeOrAcademicId: doc.data.user_id,
                    //         mobile: req.body.mobile,
                    //     })
                    //     .then((updatedUser) => {
                    //         console.log(
                    //             'userController -> change_mobile -> updatedUser:',
                    //             updatedUser,
                    //         );
                    //     })
                    //     .catch((err) => {
                    //         console.log('userController -> change_mobile -> err:', err);
                    //     });

                    common_files.comResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('MOBILE_NUMBER_CHANGED_SUCCESSFULLY'),
                        req.t('MOBILE_NUMBER_CHANGED_SUCCESSFULLY'),
                    );
                } else {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('UNABLE_TO_VERIFY_OTP'),
                        req.t('UNABLE_TO_VERIFY_OTP'),
                    );
                }
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('OTP_EXPIRES'),
                    req.t('OTP_EXPIRES'),
                );
            }
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('INVALID_OTP'),
                req.t('INVALID_OTP'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.user_active_inactive = async (req, res) => {
    const objs = {};
    if (req.body.status) {
        Object.assign(objs, { isActive: true });
    } else {
        const institutionCalendarData = await institution_calendar
            .findOne({ isDeleted: false, status: 'published' }, { _id: 1 })
            .sort({ _id: -1 });
        // Checking is Staff is assigned for Schedule
        const { status: cs_status } = await base_control.get(
            course_schedule,
            {
                // _institution_id: ObjectId(_institution_id),
                _institution_calendar_id:
                    institutionCalendarData && institutionCalendarData._id
                        ? ObjectId(institutionCalendarData._id)
                        : undefined,
                'staffs._staff_id': ObjectId(req.body.id),
                status: 'pending',
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (cs_status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('CAN_NOT_IN_ACTIVE_SCHEDULED_STAFF'),
                        req.t('CAN_NOT_IN_ACTIVE_SCHEDULED_STAFF'),
                    ),
                );

        Object.assign(objs, { isActive: false });
    }
    const doc = await base_control.update(user, req.body.id, objs);
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('USER_ACTIVE_STATUS_IS_CHANGED'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
        );
    }
};

exports.user_finger_registration = async (req, res) => {
    let objs = {};
    const user_get = await base_control.get(user, { _id: req.body._id }, { verification: 1 });
    if (user_get.status) {
        const ars = [];
        req.body.data.forEach((element) => {
            ars.push({
                name: element.name,
                image: element.image,
                isotemplate: element.IsoTemplate,
                active: element.active,
            });
        });
        objs = {
            'biometric_data.finger': ars,
        };
        Object.assign(objs, { 'verification.finger': true });
        if (user_get.data.verification.face) {
            Object.assign(objs, { status: 'completed' });
        }
        // console.log(objs);
        const doc = await base_control.update(user, user_get.data._id, objs);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('USER_FINGER_SUCCESSFULLY_STORED_IN_DB'),
                req.t('USER_FINGER_SUCCESSFULLY_STORED_IN_DB'),
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('UNABLE_TO_INSERT_USER_THUMP_PLEASE_RETRY'),
                req.t('UNABLE_TO_INSERT_USER_THUMP_PLEASE_RETRY'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
        );
    }
};

exports.user_face_registration = async (req, res) => {
    let objs = {};
    const user_get = await base_control.get(
        user,
        { _id: req.body._id },
        { verification: 1, name: 1, mobile: 1, email: 1, user_id: 1, status: 1 },
    );
    if (user_get.status) {
        const ars = [req.body.center, req.body.left, req.body.right, req.body.up];

        objs = {
            'biometric_data.face': ars,
        };
        Object.assign(objs, {
            'verification.face': true,
            'verification.finger': true,
            status: constant.COMPLETED,
        });
        Object.assign(objs, { 'verification.data': constant.DONE });
        /* Object.assign(objs, { 'verification.face': true });
        if (user_get.data.verification.finger) {
            Object.assign(objs, { 'status': 'completed' });
        } */
        const doc = await base_control.update(user, user_get.data._id, objs);
        if (doc.status) {
            //Email,SMS Push to user
            if (user_get.data.status !== constant.COMPLETED) {
                const name = user_get.data.name.middle
                    ? user_get.data.name.first +
                      ' ' +
                      user_get.data.name.middle +
                      ' ' +
                      user_get.data.name.last
                    : user_get.data.name.first + ' ' + user_get.data.name.last;
                const sms_message = req.t('USER_FACE_REG_SMS');
                const email_message =
                    '<p>' +
                    req.t('DEAR') +
                    ' ' +
                    name +
                    ',' +
                    common_fun.emailGreetingContent() +
                    req.t('USER_FACE_REG_EMAIL') +
                    common_fun.emailRegardsContent() +
                    '</p>';
                common_fun.send_email(user_get.data.email, 'DigiClass Alert', email_message);
                if (user_get.data.mobile) common_fun.send_sms(user_get.data.mobile, sms_message);
            }
            // Need to send Image to DigiAuth & Train Face for verification
            digiAuthService
                .syncUser({
                    employeeOrAcademicId: user_get.data.user_id,
                    facial: ars,
                    userId: user_get.data._id,
                })
                .then((updatedUser) => {
                    logger.info({ updatedUser }, 'userService -> uploadFacialData -> updatedUser:');
                    return digiAuthService.faceTrain({
                        employeeOrAcademicId: user_get.data.user_id,
                    });
                })
                .then((trainFace) => {
                    logger.info({ trainFace }, 'userService -> Face Train:');
                })
                .catch((err) => {
                    logger.error({ err }, 'userService -> uploadFacialData -> err:');
                });

            common_files.com_response(
                res,
                200,
                true,
                req.t('USER_FACE_SUCCESSFULLY_STORED_IN_DB'),
                req.t('USER_FACE_SUCCESSFULLY_STORED_IN_DB'),
            );
            const { sendAnnouncement } = require('../announcement/announcement_service');
            await sendAnnouncement(user_get.data._id, 'newUser');
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('UNABLE_TO_REGISTER_YOUR_FACE_PLEASE_RETRY'),
                req.t('UNABLE_TO_REGISTER_YOUR_FACE_PLEASE_RETRY'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
            req.t('ERROR_USER_ID_IS_NOT_FOUND'),
        );
    }
};

exports.user_search = async (req, res) => {
    let query = { isDeleted: false, isActive: true };
    let regex = '';
    const project = {
        user_type: 1,
        user_id: 1,
        email: 1,
        name: 1,
        gender: 1,
        role: 1,
        status: 1,
        batch: 1,
        address: 1,
        program: 1,
        username: 1,
        dob: 1,
        mobile: 1,
        isActive: 1,
        isDeleted: 1,
    };
    const search_data = req.params.text;
    if (search_data.length != 0) {
        regex = new RegExp(search_data, 'i');
    }

    if (req.params.tab == 'all') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: { $not: { $eq: 'completed' } },
            // status: { $not: 'imported' },
        };
    } else if (req.params.tab == 'inactive') {
        query = {
            isActive: false,
            isDeleted: false,
            user_type: req.params.user_type,
        };
    } else if (req.params.tab == 'imported') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'imported',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
        };
    } else if (req.params.tab == 'submitted') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECT },
                // { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'mismatch') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'valid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
        };
    } else if (req.params.tab == 'invalid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [
                { 'verification.data': constant.INVALID },
                { 'verification.data': constant.DONE },
            ],
        };
    } else if (req.params.tab == 'invited') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                // { 'verification.email': false },
                // { 'verification.mobile': false }
            ],
        };
    } else if (req.params.tab == 'expired') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
        };
    } else if (req.params.tab == 'completed') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'completed',
            $or: [
                { 'verification.data': constant.DONE },
                { 'verification.face': true },
                { 'verification.finger': true },
            ],
        };
    }
    query.$or = [
        { 'name.first': regex },
        { 'name.middle': regex },
        { 'name.last': regex },
        { email: regex },
        { user_id: regex },
        { batch: regex },
        { 'program.program_no': regex },
        { 'academic_allocation._department_id.department_title': regex },
    ];

    const populate = {
        path: 'academic_allocation._department_subject_id',
        select: { title: 1 },
    };
    const populate2 = {
        path: 'academic_allocation._department_id',
        model: constant.DEPARTMENT,
        select: { department_title: 1 },
    };

    const doc = await user.find(query, project).populate(populate).populate(populate2);
    common_files.com_response(res, 200, true, 'Data ', doc);
    // let doc = await base_control.get_list_populate(user, query, project, populate);
    // let doc = await base_control.get_list_populate(user, query, {}, populate);
    // if (doc.status) {
    //     common_files.com_response(res, 200, true, "User list", doc.data);
    //     // common_files.com_response(res, 200, true, "User Image GET list", /* doc.data */ user_formate.user_list_formate(doc.data));
    // } else {
    //     common_files.com_response(res, 500, false, "Error", doc);
    // }
};

exports.user_search_aggregate = async (req, res) => {
    let query = { isDeleted: false, isActive: true };
    let regex = '';
    // const project = {
    //     user_type: 1,
    //     user_id: 1,
    //     email: 1,
    //     name: 1,
    //     gender: 1,
    //     role: 1,
    //     status: 1,
    //     batch: 1,
    //     address: 1,
    //     program: 1,
    //     username: 1,
    //     dob: 1,
    //     mobile: 1,
    //     isActive: 1,
    //     isDeleted: 1,
    // };
    const search_data = req.params.text;
    if (search_data.length != 0) {
        regex = new RegExp(search_data, 'i');
    }
    if (req.params.tab == 'all') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: { $not: { $eq: 'completed' } },
            // status: { $not: 'imported' },
        };
    } else if (req.params.tab == 'inactive') {
        query = {
            isActive: false,
            isDeleted: false,
            user_type: req.params.user_type,
        };
    } else if (req.params.tab == 'imported') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'imported',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
        };
    } else if (req.params.tab == 'submitted') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECT },
                // { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'mismatch') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'submitted',
            $and: [
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.face': false },
                { 'verification.finger': false },
            ],
        };
    } else if (req.params.tab == 'valid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [{ 'verification.data': constant.VALID }, { 'verification.data': constant.DONE }],
        };
    } else if (req.params.tab == 'invalid') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'verified',
            $or: [
                { 'verification.data': constant.INVALID },
                { 'verification.data': constant.DONE },
            ],
        };
    } else if (req.params.tab == 'invited') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $lte: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (req.params.tab == 'expired') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'invited',
            $or: [
                { 'verification.data': constant.PENDING },
                { 'verification.data': constant.CORRECT },
                { 'verification.data': constant.CORRECTION_REQUIRED },
                { 'verification.email': false },
                { 'verification.mobile': false },
            ],
            $expr: { $gt: [{ $divide: [{ $subtract: ['$$NOW', '$updatedAt'] }, 3600000] }, 72] },
        };
    } else if (req.params.tab == 'completed') {
        query = {
            isActive: true,
            isDeleted: false,
            user_type: req.params.user_type,
            status: 'completed',
            $or: [
                { 'verification.data': constant.DONE },
                { 'verification.face': true },
                { 'verification.finger': true },
            ],
        };
    }
    const agg = [{ $match: query }];
    if (req.params.user_type == 'staff') {
        if (req.params.tab == 'completed') {
            agg.push(
                {
                    $lookup: {
                        from: constant.DIGI_PROGRAM,
                        localField: 'academic_allocation._program_id',
                        foreignField: '_id',
                        as: 'program',
                    },
                },
                {
                    $lookup: {
                        from: constant.DEPARTMENT,
                        localField: 'academic_allocation._department_id',
                        foreignField: '_id',
                        as: 'department',
                    },
                },
                {
                    $lookup: {
                        from: constant.DEPARTMENT_SUBJECT,
                        localField: 'academic_allocation._department_subject_id',
                        foreignField: '_id',
                        as: 'subject',
                    },
                },
            );
            agg.push({ $addFields: { program_ids: '$academic_allocation._program_id' } });
            agg.push(
                {
                    $lookup: {
                        from: 'role_assigns',
                        localField: '_role_id',
                        foreignField: '_id',
                        as: 'role_data',
                    },
                },
                { $unwind: { path: '$role_data', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role_check: {
                            $filter: {
                                input: '$role_data.roles',
                                as: 'item',
                                cond: { $gte: ['$$item.isDefault', true] },
                            },
                        },
                    },
                },
                { $unwind: { path: '$role_check', preserveNullAndEmptyArrays: true } },
                {
                    $addFields: {
                        role: {
                            $cond: {
                                if: { $ne: ['$role_check', null] },
                                then: '$role_check.role_name',
                                else: '',
                            },
                        },
                    },
                },
            );
        }
    } else {
        // agg.push({
        //     $lookup: {
        //         from: constant.DIGI_PROGRAM,
        //         localField: 'program.program_no',
        //         foreignField: 'no',
        //         as: 'student_program',
        //     },
        // });
        agg.push({
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'program._program_id',
                foreignField: '_id',
                as: 'programs',
            },
        });
        agg.push({ $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } });
        agg.push({ $addFields: { program_name: '$programs.name' } });
    }
    if (req.params.tab == 'all') {
        agg.push({
            $addFields: {
                user_state: {
                    $switch: {
                        branches: [
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'imported'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Imported',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'correct'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "correct"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Submitted',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'submitted'] },
                                        { $eq: ['$verification.data', 'error'] },
                                        // {
                                        //     $or: [
                                        //         { $eq: ["$verification.data", "error"] },
                                        //         { $eq: ["$verification.face", false] },
                                        //         { $eq: ["$verification.finger", false] }
                                        //     ]
                                        // }
                                    ],
                                },
                                then: 'Mismatch',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'valid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Valid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'verified'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'invalid'] },
                                                { $eq: ['$verification.data', 'success'] },
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invalid',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $lte: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Invited',
                            },
                            {
                                case: {
                                    $and: [
                                        { $eq: ['$status', 'invited'] },
                                        {
                                            $or: [
                                                { $eq: ['$verification.data', 'pending'] },
                                                { $eq: ['$verification.data', 'correct'] },
                                                { $eq: ['$verification.data', 'error'] },
                                                { $eq: ['$verification.email', false] },
                                                { $eq: ['$verification.mobile', false] },
                                            ],
                                        },
                                        {
                                            $gt: [
                                                {
                                                    $divide: [
                                                        { $subtract: ['$$NOW', '$updatedAt'] },
                                                        3600000,
                                                    ],
                                                },
                                                72,
                                            ],
                                        },
                                    ],
                                },
                                then: 'Expired',
                            },
                        ],
                        default: 'Unknown',
                    },
                },
            },
        });
    }
    agg.push({
        $match: {
            $or: [
                { 'name.first': regex },
                { 'name.middle': regex },
                { 'name.last': regex },
                { email: regex },
                { user_id: regex },
                { batch: regex },
                { 'program.program_no': regex },
                { 'department.department_title': regex },
                { 'subject.title': regex },
                { 'employment.user_type': regex },
                { 'employment.user_employment_type': regex },
                { 'program.name': regex },
                { gender: regex },
                { mobile: regex },
                { 'address.nationality_id': regex },
                { program_name: regex },
            ],
        },
    });
    agg.push({ $sort: { gender: -1 } });
    if (req.query.limit && req.query.pageNo) {
        const skips = Number(req.query.limit * (req.query.pageNo - 1));
        const limits = Number(req.query.limit) || 0;
        agg.push({
            $facet: {
                totalCount: [{ $count: 'value' }],
                pipelineResults: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            user_type: 1,
                            academic: 1,
                            gender: 1,
                            email: 1,
                            batch: 1,
                            enrollment_year: 1,
                            dob: 1,
                            mobile: 1,
                            'address.nationality_id': 1,
                            role: 1,
                            user_state: 1,
                            program_name: 1,
                            status: 1,
                            isActive: 1,
                            isDeleted: 1,
                            user_id: 1,
                            username: 1,
                            program_ids: 1,
                            pageInfo: 1,
                            count: 1,
                        },
                    },
                ],
            },
        });
        agg.push(
            { $unwind: '$pipelineResults' },
            { $unwind: '$totalCount' },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: ['$pipelineResults', { totalCount: '$totalCount.value' }],
                    },
                },
            },
        );
        agg.push({ $skip: skips }, { $limit: limits });
    }
    const doc =
        req.query.limit && req.query.pageNo
            ? await base_control.get_aggregate(user, agg)
            : await base_control.get_aggregate_with_count(user, agg, query);
    if (doc.status) {
        if (req.query.limit && req.query.pageNo) {
            const totalPages =
                doc.data && doc.data[0] && doc.data[0].totalCount
                    ? Math.ceil(doc.data[0].totalCount / Number(req.query.limit) || 0)
                    : 0;
            common_files.listAllResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('USER_SEARCH_GET_LIST'),
                doc.totalDoc,
                totalPages,
                Number(req.query.pageNo),
                user_formate.user_list_formate(doc.data),
            );
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('USER_SEARCH_GET_LIST'),
                /* doc.data */ user_formate.user_list_formate(doc.data),
            );
        }
    } else {
        common_files.comResponseWithRequest(req, res, 404, false, req.t('NOT_FOUND'), doc);
    }
};

exports.user_img_get = async (req, res) => {
    const query = { _id: ObjectId(req.params.id) };
    const project = { _id: 1, name: 1, email: 1, 'biometric_data.face': 1 };
    const doc = await base_control.get(user, query, project);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "user list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ user_formate.user_list_array(doc.data));
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('USER_IMAGE_GET_LIST'),
            doc.data /* user_formate.user(doc.data) */,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            false,
            req.t('ERROR_NO_IMAGE_FOUND'),
            doc.data,
        );
    }
};

exports.user_staff_student_import = async (req, res) => {
    const existing_data = [];
    const user_imports = [];
    const duplicates = [];
    const imp_email = [];
    const imp_user_id = [];
    const imp_nationality = [];
    if (req.body.user_type == constant.EVENT_WHOM.STAFF) {
        let staff_emp_id = req.body.data.map((staff) => staff.employee_id.trim());
        let staff_mail = req.body.data.map((staff) => staff.email.trim());
        let staff_nationality = req.body.data
            .filter((staffElement) => staffElement.nationality_id)
            .map((staff) => staff.nationality_id.trim());
        staff_emp_id = staff_emp_id.concat(
            req.body.data.map((staff) => staff.employee_id.trim().toUpperCase()),
        );
        staff_emp_id = staff_emp_id.concat(
            req.body.data.map((staff) => staff.employee_id.trim().toLowerCase()),
        );
        staff_mail = staff_mail.concat(
            req.body.data.map((staff) => staff.email.trim().toUpperCase()),
        );
        staff_mail = staff_mail.concat(
            req.body.data.map((staff) => staff.email.trim().toLowerCase()),
        );
        staff_nationality = staff_nationality.concat(
            req.body.data
                .filter((staffElement) => staffElement.nationality_id)
                .map((staff) => staff.nationality_id.trim().toUpperCase()),
        );
        staff_nationality = staff_nationality.concat(
            req.body.data
                .filter((staffElement) => staffElement.nationality_id)
                .map((staff) => staff.nationality_id.trim().toLowerCase()),
        );
        const query = {
            $or: [
                { user_id: { $in: staff_emp_id } },
                { email: { $in: staff_mail } },
                // { 'address.nationality_id': { $in: staff_nationality } },
            ],
            isDeleted: false,
        };
        if (util_key.SERVICES.NATIONALITY_ID === 'true' && staff_nationality.length)
            query.$or.push({ 'address.nationality_id': { $in: staff_nationality } });
        const project = {
            user_id: 1,
            email: 1,
            name: 1,
            user_type: 1,
            'address.nationality_id': 1,
        };
        const staff_doc = await base_control.get_list(user, query, project);
        req.body.data.forEach((doc) => {
            if (
                staff_doc.status &&
                staff_doc.data.findIndex(
                    (i) =>
                        i.user_id == doc.employee_id.trim() ||
                        i.user_id == doc.employee_id.trim().toUpperCase() ||
                        i.user_id == doc.employee_id.trim().toLowerCase() ||
                        i.email == doc.email.trim() ||
                        i.email == doc.email.trim().toLowerCase() ||
                        i.email == doc.email.trim().toUpperCase() ||
                        (util_key.SERVICES.NATIONALITY_ID === 'true'
                            ? i.address.nationality_id == doc.nationality_id.trim() ||
                              i.address.nationality_id == doc.nationality_id.trim().toLowerCase() ||
                              i.address.nationality_id == doc.nationality_id.trim().toUpperCase()
                            : false),
                ) != -1
            ) {
                existing_data.push(doc);
            } else {
                if (
                    imp_email.indexOf(doc.email.trim().toLowerCase()) == -1 &&
                    imp_user_id.indexOf(doc.employee_id.trim().toLowerCase()) == -1 &&
                    (util_key.SERVICES.NATIONALITY_ID === 'true'
                        ? imp_nationality.indexOf(doc.nationality_id.trim().toLowerCase()) == -1
                        : true)
                ) {
                    const objs = {
                        insertOne: {
                            document: {
                                user_type: req.body.user_type,
                                user_id: doc.employee_id.trim(),
                                email: doc.email.trim().toLowerCase(),
                                name: {
                                    first: doc.first_name,
                                    last: doc.last_name,
                                    middle: doc.middle_name,
                                    family: doc.family_name,
                                },
                                gender: doc.gender.toLowerCase().trim(),
                                password: common_fun.generateRandomNumber(
                                    util_key.RANDOM_PASSWORD_LENGTH,
                                ), // generate random password
                                address: {
                                    nationality_id: doc.nationality_id.trim(),
                                },
                                enrollment_year: doc.enrollment_year,
                                role: 'Staff',
                                status: 'imported',
                            },
                        },
                    };
                    if (doc.middle_name != undefined && doc.middle_name.length == 0) {
                        delete objs.insertOne.document.name.middle;
                    }
                    if (doc.family_name != undefined && doc.family_name.length == 0) {
                        delete objs.insertOne.document.name.family;
                    }
                    user_imports.push(objs);
                    imp_email.push(doc.email.trim().toLowerCase());
                    imp_user_id.push(doc.employee_id.trim().toLowerCase());
                    imp_nationality.push(doc.nationality_id.trim().toLowerCase());
                } else {
                    duplicates.push(doc);
                }
            }
        });
    } else if (req.body.user_type == constant.EVENT_WHOM.STUDENT) {
        let student_acd_no = req.body.data.map((student) => student.academic_no.trim());
        let student_mail = req.body.data.map((student) => student.email.trim());
        let student_nationality = req.body.data.map((student) => student.nationality_id.trim());
        student_acd_no = student_acd_no.concat(
            req.body.data.map((student) => student.academic_no.trim().toUpperCase()),
        );
        student_acd_no = student_acd_no.concat(
            req.body.data.map((student) => student.academic_no.trim().toLowerCase()),
        );
        student_mail = student_mail.concat(
            req.body.data.map((student) => student.email.trim().toUpperCase()),
        );
        student_mail = student_mail.concat(
            req.body.data.map((student) => student.email.trim().toLowerCase()),
        );
        student_nationality = student_nationality.concat(
            req.body.data.map((student) => student.nationality_id.trim().toUpperCase()),
        );
        student_nationality = student_nationality.concat(
            req.body.data.map((student) => student.nationality_id.trim().toLowerCase()),
        );
        const query = {
            $or: [
                { user_id: { $in: student_acd_no } },
                { email: { $in: student_mail } },
                // { 'address.nationality_id': { $in: student_nationality } },
            ],
            isDeleted: false,
        };
        if (util_key.SERVICES.NATIONALITY_ID === 'true' && student_nationality.length)
            query.$or.push({ 'address.nationality_id': { $in: student_nationality } });
        const project = {
            user_id: 1,
            email: 1,
            name: 1,
            user_type: 1,
            'address.nationality_id': 1,
        };
        const std_doc = await base_control.get_list(user, query, project);
        const query1 = { isDeleted: false, isActive: true };
        const project1 = { _id: 1, name: 1, code: 1 };
        const program_data = await base_control.get_list(program, query1, project1);
        req.body.data.forEach((doc) => {
            if (
                std_doc.status &&
                std_doc.data.findIndex(
                    (i) =>
                        i.user_id == doc.academic_no.trim() ||
                        i.user_id == doc.academic_no.trim().toUpperCase() ||
                        i.user_id == doc.academic_no.trim().toLowerCase() ||
                        i.email == doc.email.trim() ||
                        i.email == doc.email.trim().toLowerCase() ||
                        i.email == doc.email.trim().toUpperCase() ||
                        (util_key.SERVICES.NATIONALITY_ID === 'true'
                            ? i.address.nationality_id == doc.nationality_id.trim() ||
                              i.address.nationality_id == doc.nationality_id.trim().toLowerCase() ||
                              i.address.nationality_id == doc.nationality_id.trim().toUpperCase()
                            : false),
                ) != -1
            ) {
                existing_data.push(doc);
            } else {
                if (
                    imp_email.indexOf(doc.email.trim().toLowerCase()) == -1 &&
                    imp_user_id.indexOf(doc.academic_no.trim().toLowerCase()) == -1 &&
                    (util_key.SERVICES.NATIONALITY_ID === 'true'
                        ? imp_nationality.indexOf(doc.nationality_id.trim().toLowerCase()) == -1
                        : true)
                ) {
                    const objs = {
                        insertOne: {
                            document: {
                                user_type: req.body.user_type,
                                user_id: doc.academic_no.trim(),
                                email: doc.email.trim().toLowerCase(),
                                name: {
                                    first: doc.first_name,
                                    last: doc.last_name,
                                    middle: doc.middle_name,
                                    family: doc.family_name,
                                },
                                batch: doc.batch.trim(),
                                gender: doc.gender.toLowerCase().trim(),
                                password: common_fun.generateRandomNumber(
                                    util_key.RANDOM_PASSWORD_LENGTH,
                                ), // generate random password
                                address: {
                                    nationality_id: doc.nationality_id.trim(),
                                },
                                program: {
                                    program_no: doc.program_no.trim(),
                                    _program_id:
                                        program_data.data.findIndex(
                                            (i) =>
                                                i.code.toString() ===
                                                doc.program_no.trim().toString(),
                                        ) != -1
                                            ? program_data.data[
                                                  program_data.data.findIndex(
                                                      (i) =>
                                                          i.code.toString() ===
                                                          doc.program_no.trim().toString(),
                                                  )
                                              ]._id
                                            : null,
                                },
                                enrollment_year: doc.enrollment_year,
                                role: 'Student',
                                status: 'imported',
                            },
                        },
                    };
                    if (
                        program_data.data.findIndex(
                            (i) => i.code.toString() === doc.program_no.trim().toString(),
                        ) == -1
                    ) {
                        delete objs.insertOne.document.program._program_id;
                    }
                    if (doc.middle_name != undefined && doc.middle_name.length == 0) {
                        delete objs.insertOne.document.name.middle;
                    }
                    if (doc.family_name != undefined && doc.family_name.length == 0) {
                        delete objs.insertOne.document.name.family;
                    }
                    user_imports.push(objs);
                    imp_email.push(doc.email.trim().toLowerCase());
                    imp_user_id.push(doc.academic_no.trim().toLowerCase());
                    imp_nationality.push(doc.nationality_id.trim().toLowerCase());
                } else {
                    duplicates.push(doc);
                }
            }
        });
    }
    if (user_imports.length != 0) {
        const data_create = await base_control.bulk_write(user, user_imports);
        if (data_create.status) {
            /* let aggre = [
                { $match: { 'isDeleted': false, isActive: true } },
                { $lookup: { from: constant.DIGI_PROGRAM, localField: 'program._program_id', foreignField: '_id', as: 'programs' } },
                { $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } },
                { $addFields: { program_name: '$programs.name' } },
                { $sort: { updatedAt: -1 } },
                { $project: { _id: 1, user_id: 1, email: 1, name: 1, gender: 1, 'address.nationality_id': 1, program_name: 1, 'program': 1, enrollment_year: 1, status: 1 } }
            ];
            let user_data = await base_control.get_aggregate(user, aggre); */
            const auth = [];
            for (let i = 0; i < user_imports.length; i++) {
                const push_objs = {
                    _id: data_create.responses.insertedIds[i],
                    userType: user_imports[i].insertOne.document.user_type.toUpperCase(),
                    user_id: user_imports[i].insertOne.document.user_id,
                    email: user_imports[i].insertOne.document.email,
                    name: user_imports[i].insertOne.document.name,
                    gender: user_imports[i].insertOne.document.gender,
                    // password: user_imports[i].insertOne.document.password,
                };
                push_objs.name.last =
                    push_objs.name.last !== undefined &&
                    push_objs.name.last !== '' &&
                    push_objs.name.last !== ' '
                        ? push_objs.name.last
                        : ' ';
                auth.push(push_objs);
            }
            console.info({ auth }, 'Auth Import Users ');
            const appCode = getAppCodeByDomain(req.headers);
            digiAuthService
                .importUser({ users: auth, appCode })
                .then((importedUsers) => {
                    logger.info(
                        { importedUsers },
                        'userController -> exports.user_import -> importedUsers : %s',
                    );
                })
                .catch((err) => {
                    logger.error({ err }, 'userController -> exports.user_import -> err');
                });
            const response_data = {
                imported_records_count: auth.length,
                invalid_records_count: existing_data.length + duplicates.length,
                imported_records: auth,
                invalid_records: existing_data,
                duplicate_records_count: duplicates.length,
                duplicate_records: duplicates,
            };
            if (existing_data.length != 0) {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('USER_DATAS_IMPORTED_SUCCESSFULLY'),
                    response_data,
                );
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('USER_DATAS_IMPORTED_SUCCESSFULLY_SOME_DATA_ARE_NOT'),
                    response_data,
                );
            }
            // common_files.com_response(res, 201, true, "user datas imported successfully", user_formate.user_list_array(user_data.data));
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                404,
                false,
                req.t('ERROR_UNABLE_TO_IMPORT_USER_DETAILS'),
                req.t('ERROR_UNABLE_TO_IMPORT_USER_DETAILS') + ': ' + data_create.data,
            );
        }
    } else {
        const response_data = {
            imported_records_count: user_imports.length,
            invalid_records_count: existing_data.length + duplicates.length,
            imported_records: user_imports,
            invalid_records: existing_data,
            duplicate_records_count: duplicates.length,
            duplicate_records: duplicates,
        };
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            false,
            req.t('ERROR_FOUND_DUPLICATE_INVALID_DATA_IN_UPLOAD_FILE'),
            response_data,
        );
    }
};

exports.single_insert = async (req, res) => {
    let user_imports;
    let data_check;
    const emails = [req.body.email, req.body.email.toUpperCase(), req.body.email.toLowerCase()];
    if (req.body.user_type == constant.EVENT_WHOM.STAFF) {
        const ids = [
            req.body.employee_id,
            req.body.employee_id.toUpperCase(),
            req.body.employee_id.toLowerCase(),
        ];
        const query = {
            $or: [
                { user_id: { $in: ids } },
                { email: { $in: emails } },
                // { 'address.nationality_id': req.body.nationality_id.trim() },
            ],
            isDeleted: false,
        };
        if (util_key.SERVICES.NATIONALITY_ID === 'true' && req.body.nationality_id)
            query.$or.push({ 'address.nationality_id': req.body.nationality_id.trim() });
        // let query = { $or: [{ user_id: req.body.employee_id }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false };
        const project = {
            user_id: 1,
            email: 1,
            name: 1,
            user_type: 1,
            'address.nationality_id': 1,
        };
        data_check = await base_control.get_list(user, query, project);
    } else if (req.body.user_type == constant.EVENT_WHOM.STUDENT) {
        const ids = [
            req.body.academic_no,
            req.body.academic_no.toUpperCase(),
            req.body.academic_no.toLowerCase(),
        ];
        const query = {
            $or: [
                { user_id: { $in: ids } },
                { email: { $in: emails } },
                // { 'address.nationality_id': req.body.nationality_id.trim() },
            ],
            isDeleted: false,
        };
        if (util_key.SERVICES.NATIONALITY_ID === 'true' && req.body.nationality_id)
            query.$or.push({ 'address.nationality_id': req.body.nationality_id.trim() });
        // let query = { $or: [{ user_id: req.body.academic_no }, { email: req.body.email }, { 'address.nationality_id': req.body.nationality_id }], isDeleted: false };
        const project = {
            user_id: 1,
            email: 1,
            name: 1,
            user_type: 1,
            'address.nationality_id': 1,
        };
        data_check = await base_control.get_list(user, query, project);
    }
    if (!data_check.status) {
        if (req.body.user_type == constant.EVENT_WHOM.STAFF) {
            const objs = {
                user_type: req.body.user_type,
                user_id: req.body.employee_id.trim(),
                email: req.body.email.trim().toLowerCase(),
                name: {
                    first: req.body.first_name,
                    last: req.body.last_name,
                    // middle: req.body.middle_name,
                    // family: req.body.family_name
                },
                gender: req.body.gender,
                password: common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH), // generate random password
                address: {
                    nationality_id: req.body.nationality_id ? req.body.nationality_id.trim() : '',
                },
                role: 'Staff',
                status: 'imported',
            };
            if (req.body.enrollment_year != undefined && req.body.enrollment_year.length != 0) {
                Object.assign(objs, { enrollment_year: req.body.enrollment_year });
            }
            if (req.body.middle_name != undefined && req.body.middle_name.length != 0) {
                Object.assign(objs, { 'name.middle': req.body.middle_name });
            }
            if (req.body.family_name != undefined && req.body.family_name.length != 0) {
                Object.assign(objs, { 'name.family': req.body.family_name });
            }
            user_imports = objs;
        } else if (req.body.user_type == constant.EVENT_WHOM.STUDENT) {
            const query = { code: req.body.program_no, isDeleted: false, isActive: true };
            const project = { _id: 1, name: 1 };
            const program_data = await base_control.get(program, query, project);
            const objs = {
                user_type: req.body.user_type,
                user_id: req.body.academic_no.trim(),
                email: req.body.email.trim().toLowerCase(),
                name: {
                    first: req.body.first_name,
                    last: req.body.last_name,
                    // middle: req.body.middle_name
                },
                batch: req.body.batch.trim(),
                gender: req.body.gender.trim(),
                password: common_fun.generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH), // generate random password
                address: {
                    nationality_id: req.body.nationality_id ? req.body.nationality_id.trim() : '',
                },
                program: {
                    program_no: req.body.program_no,
                    _program_id: common_fun.check_value_or_null(program_data, 'data._id'),
                },
                role: 'Student',
                status: 'imported',
            };
            if (req.body.enrollment_year != undefined && req.body.enrollment_year.length != 0) {
                Object.assign(objs, { enrollment_year: req.body.enrollment_year });
            }
            if (req.body.middle_name != undefined && req.body.middle_name.length != 0) {
                Object.assign(objs, { 'name.middle': req.body.middle_name });
            }
            if (req.body.family_name != undefined && req.body.family_name.length != 0) {
                Object.assign(objs, { 'name.family': req.body.family_name });
            }
            user_imports = objs;
        }
        const data_create = await base_control.insert(user, user_imports);
        if (data_create.status) {
            const auth = [];
            auth.push({
                _id: data_create.responses._id,
                userType: user_imports.user_type.toUpperCase(),
                user_id: user_imports.user_id,
                email: user_imports.email,
                name: user_imports.name,
                gender: user_imports.gender,
            });
            const appCode = getAppCodeByDomain(req.headers);
            digiAuthService
                .importUser({ users: auth, appCode })
                .then((importedUsers) => {
                    logger.info(
                        { importedUsers },
                        'userController -> exports.user_import -> importedUsers',
                    );
                })
                .catch((err) => {
                    logger.error({ err }, 'userController -> exports.user_import -> err');
                });
            common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('USER_DATAS_IMPORTED_SUCCESSFULLY'),
            );
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('ERROR_UNABLE_TO_IMPORT_USER_DETAILS'),
                req.t('ERROR_UNABLE_TO_IMPORT_USER_DETAILS') + ' : ' + data_create.data,
            );
        }
    } else {
        let response = req.t('FOUND_DUPLICATE_DATA');
        if (req.body.user_type == constant.EVENT_WHOM.STAFF) {
            if (
                data_check.data.findIndex(
                    (i) =>
                        i.user_id == req.body.employee_id ||
                        i.user_id == req.body.employee_id.toLowerCase() ||
                        i.user_id == req.body.employee_id.toUpperCase(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_EMPLOYEE_ID');
            } else if (
                data_check.data.findIndex(
                    (i) =>
                        i.email == req.body.email ||
                        i.email == req.body.email.toUpperCase() ||
                        i.email == req.body.email.toLowerCase(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_EMAIL');
            } else if (
                util_key.SERVICES.NATIONALITY_ID === 'true' &&
                data_check.data.findIndex(
                    (i) => i.address.nationality_id == req.body.nationality_id.trim(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_NATIONALITY_ID');
            }
        } else if (req.body.user_type == constant.EVENT_WHOM.STUDENT) {
            if (
                data_check.data.findIndex(
                    (i) =>
                        i.user_id == req.body.academic_no ||
                        i.user_id == req.body.academic_no.toUpperCase() ||
                        i.user_id == req.body.academic_no.toLowerCase(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_ACADEMIC_NO');
            } else if (
                data_check.data.findIndex(
                    (i) =>
                        i.email == req.body.email ||
                        i.email == req.body.email.toUpperCase() ||
                        i.email == req.body.email.toLowerCase(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_EMAIL');
            } else if (
                util_key.SERVICES.NATIONALITY_ID === 'true' &&
                data_check.data.findIndex(
                    (i) => i.address.nationality_id == req.body.nationality_id.trim(),
                ) != -1
            ) {
                response = req.t('FOUND_DUPLICATE_NATIONALITY_ID');
            }
        }
        common_files.comResponseWithRequest(
            req,
            res,
            409,
            false,
            req.t('FOUND_DUPLICATE_DATA'),
            response,
        );
    }
};

exports.staff_list = async (req, res) => {
    const aggre = [
        { $match: { isDeleted: false } },
        { $sort: { updatedAt: -1 } },
        { $match: { status: 'completed' } },
        { $unwind: { path: '$academic_allocation', preserveNullAndEmptyArrays: true } },
        { $match: { 'academic_allocation.allocation_type': 'primary' } },
        {
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'academic_allocation._program_id',
                foreignField: '_id',
                as: 'program',
            },
        },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: 'academic_allocation._department_id',
                foreignField: '_id',
                as: 'department',
            },
        },
        {
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'academic_allocation._department_subject_id',
                foreignField: '_id',
                as: 'subject',
            },
        },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$department', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                program_name: '$program.name',
                department: '$department.department_title',
                subject: '$subject.name',
            },
        },
        { $project: { email: 1, name: 1, role: 1, program_name: 1, department: 1, subject: 1 } },
    ];
    const doc = await base_control.get_aggregate(user, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('STAFF_LIST'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.dashboard = async (req, res) => {
    const cond = [
        { $match: { _id: ObjectId(req.params.id) } },
        { $unwind: { path: '$academic_allocation', preserveNullAndEmptyArrays: true } },
        { $match: { 'academic_allocation.allocation_type': 'primary' } },
        {
            $lookup: {
                from: constant.DIGI_PROGRAM,
                localField: 'academic_allocation._program_id',
                foreignField: '_id',
                as: 'program',
            },
        },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: 'academic_allocation._department_id',
                foreignField: '_id',
                as: 'department',
            },
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: 'academic_allocation._department_subject_id',
                foreignField: '_id',
                as: 'subject',
            },
        },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$department', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$subject', preserveNullAndEmptyArrays: true } },
        {
            $project: {
                user_type: 1,
                user_id: 1,
                email: 1,
                name: 1,
                dob: 1,
                enrollment_year: 1,
                gender: 1,
                mobile: 1,
                address: 1,
                contact: 1,
                office: 1,
                role: 1,
                employment: 1,
                biometric_data: 1,
                status: 1,
                program: 1,
                department: 1,
                subject: 1,
                programs: 1,
                sub_role: 1,
            },
        },
    ];

    const user_details = await base_control.get_aggregate(user, cond);
    if (user_details.status) {
        // console.log(user_details.data[0].programs);
        const ins_cal = await base_control.get_sort_limt(
            institution_calendar,
            { isDeleted: false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED },
            { calendar_name: 1 },
            { updatedAt: -1 },
            1,
        );
        const pro_cal = await base_control.get_sort_limt(
            program_calendar,
            { isDeleted: false, _program_id: { $in: user_details.data[0].program._id } },
            { _program_id: 1, _institution_calendar_id: 1, status: 1 },
            { updatedAt: -1 },
            1,
        );
        const response = {
            user_details: user_details.data[0],
            institution_calendar: ins_cal.data,
            program_calendar: pro_cal.data,
        };
        common_files.com_response(res, 200, true, 'Dashboard', response);
    } else {
        common_files.com_response(res, 500, false, req.t('ID_NOT_FOUND'), req.t('ID_NOT_FOUND'));
    }
};

exports.user_details = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { id },
        } = req;

        //User Gets
        const populate = { path: 'address._nationality_id', select: { _id: 1, name: 1 } };
        const { status: userStatus, data: userDatas } = await get_list_populate(
            user,
            {
                _id: convertToMongoObjectId(id),
                // isActive: true,
                isDeleted: false,
            },
            {
                name: 1,
                user_id: 1,
                email: 1,
                batch: 1,
                gender: 1,
                mobile: 1,
                dob: 1,
                address: 1,
                program: 1,
                user_type: 1,
                // 'address.nationality_id': 1,
                // 'address._nationality_id': 1,
                // 'address.passport_no': 1,
                // 'program.program_no': 1,
                // 'program._program_id': 1,
                office: 1,
                employment: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
                institution_role: 1,
                createdAt: 1,
            },
            populate,
        );
        if (!userStatus)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STAFF_NOT_FOUND'),
                        req.t('STAFF_NOT_FOUND'),
                    ),
                );
        const userData = userDatas[0];
        const response = {
            _id: userData._id,
            name: userData.name,
            email: userData.email,
            user_id: userData.user_id,
            gender: userData.gender,
            dob: userData.dob,
            mobile_no: userData.mobile || '',
            createdAt: userData.createdAt,
            address: userData.address,
            nationality_id: userData.address.nationality_id,
            nationality:
                (userData.address._nationality_id && userData.address._nationality_id.name) || '',
            passport_no: userData.address.passport_no,
            institution_role: userData.institution_role ? userData.institution_role : '',
        };
        if (userData.user_type === constant.EVENT_WHOM.STUDENT) {
            if (userData.program && userData.program.program_no) {
                const { status: programStatus, data: programData } = await get(
                    program,
                    {
                        code: userData.program.program_no,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isActive: true,
                        isDeleted: false,
                    },
                    { name: 1 },
                );
                response.program_name = programStatus ? programData.name : '';
            }
        } else {
            // Department Subjects
            const departmentSubject = await get_list(
                department_subject,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {},
            );
            Object.assign(response, {
                office: userData.office,
                staff_employment_type: userData.staff_employment_type,
                employment_type: userData.employment.user_employment_type,
                employment: userData.employment,
            });

            const subjectDatas = departmentSubject.data
                .map((ele) =>
                    ele.subject.map((ele2) => {
                        return { _id: ele2._id, subject_name: ele2.subject_name };
                    }),
                )
                .flat();

            const academicAllocation = [];
            const staffSubjects = [];
            for (academicElement of userData.academic_allocation) {
                const subjectNames = [];
                for (subjectElement of academicElement._department_subject_id) {
                    const sub = subjectDatas.find(
                        (ele) => ele._id.toString() === subjectElement._id.toString(),
                    );
                    if (sub) {
                        subjectNames.push({
                            _subject_id: sub._id,
                            subject_name: sub.subject_name,
                        });
                        staffSubjects.push(sub._id.toString());
                    }
                }
                academicAllocation.push({
                    allocation_type: academicElement.allocation_type,
                    _program_id: academicElement._program_id,
                    program_name:
                        departmentSubject.data.find(
                            (ele) =>
                                ele.program_id.toString() ===
                                academicElement._program_id.toString(),
                        ).program_name || '',
                    _department_id: academicElement._department_id,
                    department_name: departmentSubject.data.find(
                        (ele) => ele._id.toString() === academicElement._department_id.toString(),
                    ).department_name,
                    subjects: subjectNames,
                });
            }
            response.academic_allocation = academicAllocation;
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STAFF_DETAILS'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.staff_profile_details = async (req, res) => {
    const query = { _id: ObjectId(req.params.id) };
    let project = {};
    project = {
        user_type: 1,
        user_id: 1,
        email: 1,
        name: 1,
        academic_allocation: 1,
        employment: 1,
        institution_role: 1,
        staff_employment_type: 1,
    };
    // let user_details = await base_control.get(user, query, project);
    const pop1 = { path: 'academic_allocation._program_id', select: { name: 1 } };
    const pop2 = {
        path: 'academic_allocation._department_id' /* , select: { department_title: 1 } */,
    };
    const pop3 = { path: 'academic_allocation._department_division_id', select: { title: 1 } };
    // const pop4 = { path: 'academic_allocation._department_subject_id', select: { title: 1 } };
    const user_details = await user
        .findOne(query, project)
        .populate(pop1)
        .populate(pop2)
        .populate(pop3);
    // .populate(pop4);
    if (user_details) {
        const response = {};
        if (user_details.academic_allocation.length != 0 || user_details.staff_employment_type) {
            Object.assign(response, { academic_check: true });
            const { data: department_data } = await base_control.get_list(
                department_subject,
                { isDeleted: false },
                {},
            );
            const shared_subject = [];
            for (let j = 0; j < department_data.length; j++) {
                const subject_data = department_data[j].subject.filter(
                    (ele) => ele.isDeleted == false,
                );
                for (element of subject_data) {
                    if (element.shared_with && element.shared_with.length != 0) {
                        shared_subject.push({
                            program_id: department_data[j].program_id,
                            program_name: department_data[j].program_name,
                            department_id: department_data[j]._id,
                            department_name: department_data[j].department_name,
                            subject_id: element._id,
                            subject_name: element.subject_name,
                            shared_with: element.shared_with,
                            isActive: element.isActive,
                            isDeleted: element.isDeleted,
                        });
                    }
                }
            }
            for (let j = 0; j < user_details.academic_allocation.length; j++) {
                for (element of shared_subject) {
                    const datas = user_details.academic_allocation[j]._department_id;
                    if (
                        element.shared_with.findIndex(
                            (i) =>
                                i.program_id.toString() == datas?.program_id.toString() &&
                                i.department_id.toString() == datas._id.toString(),
                        ) != -1
                    )
                        user_details.academic_allocation[j]._department_id.subject.push({
                            _id: element.subject_id,
                            subject_name: element.subject_name,
                            isActive: element.isActive,
                            isDeleted: element.isDeleted,
                            shared: [],
                        });
                }
            }
        } else {
            Object.assign(response, { academic_check: false });
        }
        if (user_details.employment.user_employment_type) {
            Object.assign(response, { employment_check: true });
        } else {
            Object.assign(response, { employment_check: false });
        }
        Object.assign(response, {
            user_type: user_details.user_type,
            user_id: user_details.user_id,
            email: user_details.email,
            name: user_details.name,
            academic_allocation: user_details.academic_allocation,
            employment: user_details.employment,
            institution_role: user_details.institution_role || '',
            staff_employment_type: user_details.staff_employment_type || '',
        });
        common_files.sendResponseWithRequest(req, res, 200, true, req.t('USER_DETAILS'), response);
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            404,
            false,
            req.t('USER_NOT_FOUND'),
            req.t('USER_NOT_FOUND'),
        );
    }
};

exports.user_program_adding = async (req, res) => {
    const program_check = await base_control.get(
        program,
        { _id: ObjectId(req.body._program_id) },
        { _id: 1, name: 1, no: 1 },
    );
    if (program_check.status) {
        const objs = {
            $push: {
                programs: {
                    name: program_check.data.name.substring(0, program_check.data.name.length - 4),
                    _program_id: ObjectId(program_check.data._id),
                    no: program_check.data.no,
                    interim: program_check.data.interim,
                },
            },
        };
        const docs = await base_control.update_condition(
            user,
            { _id: ObjectId(req.body._id) },
            objs,
        );
        if (docs.status) {
            await common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_SUCCESSFULLY_PUSHED_INTO_USER'),
                req.t('PROGRAM_SUCCESSFULLY_PUSHED_INTO_USER'),
            );
        } else {
            common_files.com_response(
                res,
                400,
                false,
                req.t('UNABLE_TO_PUSH_PROGRAM'),
                req.t('UNABLE_TO_PUSH_PROGRAM'),
            );
        }
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('PROGRAM_ID_IS_WRONG'),
            req.t('PROGRAM_ID_IS_WRONG'),
        );
    }
};

exports.student_search = async (req, res) => {
    const populate = { path: 'program._program_id', select: { _id: 1, name: 1, no: 1 } };
    const query = {
        user_id: req.params.academic_no,
        user_type: constant.EVENT_WHOM.STUDENT,
        isDeleted: false,
        isActive: true,
    };
    const doc = await base_control.get_list_populate(
        user,
        query,
        { _id: 1, name: 1, gender: 1, 'program.__program_id': 1 },
        populate,
    );
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('USER_DETAILS'),
            doc.data /* user_formate.user_ID(doc.data) */,
        );
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ACADEMIC_NO_NOT_FOUND_OR_DATA_MISMATCH'),
            doc.data,
        );
    }
};

//Forget Password
exports.forget_send_otp = async (req, res) => {
    const cond = { email: req.body.email.toLowerCase(), isDeleted: false, isActive: true };
    const proj = { _id: 1, email: 1, password: 1, mobile: 1, verification: 1, status: 1 };
    let messages = '';
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        if (doc.data.status == 'completed') {
            if (doc.data.verification.email && doc.data.verification.mobile) {
                const otp = common_fun.getFourDigitOTP();
                await base_control.update(user, doc.data._id, {
                    'otp.no': otp,
                    'otp.expiry_date': common_fun.timestampNow(),
                });
                messages =
                    '<p>' +
                    req.t('DEAR_DIGISCHEDULER_USER') +
                    ',<br>' +
                    otp +
                    ' ' +
                    req.t(
                        'IS_SECRET_OTP_FOR_YOU_ACCOUNT_LOGIN_THIS_VALID_FOR_3MIN_PLS_DO_NOT_SHARE_OTP_WITH_ANYONE',
                    ) +
                    '</p>';
                if (req.body.otp_mode == 'email') {
                    common_fun.send_email(doc.data.email, req.t('DIGISCHEDULER_ALERT'), messages);
                } else {
                    const sms_messages = `${otp} ` + req.t('IS_YOUR_DIGISCHEDULER_LOGIN_OTP');
                    if (doc.data.mobile) {
                        common_fun.send_sms(doc.data.mobile, sms_messages);
                    }
                }
                // send OTP to slack channel
                const slack_chat_data = `Login -> Email : ${doc.data.email} - OTP : ${otp}`;
                common_fun.send_otp_to_slack(slack_chat_data);
                const response = {
                    _id: doc.data._id,
                    email: doc.data.email,
                    mobile: doc.data.mobile,
                    token: await tokenUtil.generateAuthTokens({
                        userId: doc.data._id,
                        _id: doc.data._id,
                        userType: defaultPolicy.FORGET,
                    }),
                };
                common_files.comResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('OTP_SEND'),
                    response /* user_formate.user_ID(doc.data[0]) */,
                );
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    401,
                    false,
                    req.t('YOU_NEED_TO_SIGNUP_FIRST'),
                    req.t('YOU_NEED_TO_SIGNUP_FIRST'),
                );
            }
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                401,
                false,
                req.t('YOUR_PROFILE_IS_NOT_COMPLETED_ASK_ADMIN'),
                req.t('YOUR_PROFILE_IS_NOT_COMPLETED_ASK_ADMIN'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            404,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.forget_set_password = async (req, res) => {
    const cond = { _id: req.body.id, isDeleted: false, isActive: true };
    const proj = { _id: 1, password: 1, user_id: 1 };
    const doc = await base_control.get(user, cond, proj);
    if (doc.status) {
        const update_pass = await base_control.update(user, doc.data._id, {
            password: bcrypt.hashSync(req.body.new_password, 10),
        });
        if (update_pass.status) {
            if (AUTH_PASSWORD_SYNC === 'true') {
                digiAuthService
                    .syncUser({
                        employeeOrAcademicId: doc.data.user_id,
                        userId: doc.data._id,
                        password: req.body.new_password,
                    })
                    .then((updatedUser) => {
                        console.log('userController -> set_password -> updatedUser:', updatedUser);
                    })
                    .catch((err) => {
                        console.log('userController -> set_password -> err:', err);
                    });
            }
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('PASSWORD_SUCCESSFULLY_CHANGED'),
                req.t('PASSWORD_SUCCESSFULLY_CHANGED') /* user_formate.user_ID(doc.data) */,
            );
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                304,
                false,
                req.t('UNABLE_TO_CHANGE_PASSWORD'),
                req.t('UNABLE_TO_CHANGE_PASSWORD'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            404,
            false,
            req.t('EMAIL_ID_NOT_FOUND'),
            req.t('EMAIL_ID_NOT_FOUND'),
        );
    }
};

exports.getDocumentById = async (req, res) => {
    try {
        const { userId, documentKey } = req.params;
        const doc = await user.findOne(
            { _id: ObjectId(userId), isDeleted: false, isActive: true },
            {},
        );
        let url = '';
        if (doc) {
            switch (documentKey.toString()) {
                case '_nationality_id_doc':
                    if (doc.address._nationality_id_doc)
                        url = await getSignedURL(doc.address._nationality_id_doc);
                    break;
                case '_address_doc':
                    if (doc.address._address_doc)
                        url = await getSignedURL(doc.address._address_doc);
                    break;
                case '_college_id_doc':
                    if (doc.id_doc._college_id_doc)
                        url = await getSignedURL(doc.id_doc._college_id_doc);
                    break;
                case '_school_certificate_doc':
                    if (doc.student_docs._school_certificate_doc)
                        url = await getSignedURL(doc.student_docs._school_certificate_doc);
                    break;
                case '_entrance_exam_certificate_doc':
                    if (doc.student_docs._entrance_exam_certificate_doc)
                        url = await getSignedURL(doc.student_docs._entrance_exam_certificate_doc);
                    break;
                case '_admission_order_doc':
                    if (doc.enrollment._admission_order_doc)
                        url = await getSignedURL(doc.enrollment._admission_order_doc);
                    break;
                case '_employee_id_doc':
                    if (doc.id_doc._employee_id_doc)
                        url = await getSignedURL(doc.id_doc._employee_id_doc);
                    break;
                case '_appointment_order_doc':
                    if (doc.enrollment._appointment_order_doc)
                        url = await getSignedURL(doc.enrollment._appointment_order_doc);
                    break;
                case '_degree_doc':
                    if (
                        doc.qualifications.degree.length !== 0 &&
                        doc.qualifications.degree[0] &&
                        doc.qualifications.degree[0]._degree_doc
                    )
                        url = await getSignedURL(doc.qualifications.degree[0]._degree_doc);
                    break;
                case 'face':
                    if (
                        doc.biometric_data.length !== 0 &&
                        doc.biometric_data.face[0] &&
                        doc.biometric_data.face[0].length !== 0
                    )
                        url = await getSignedURL(doc.biometric_data.face[0]);
                    break;
                default:
                    url = '';
                    break;
            }
            common_files.com_response(res, 200, true, req.t('USER_DOCUMENT_SIGNED_URL'), { url });
        } else {
            common_files.com_response(
                res,
                404,
                false,
                req.t('ERROR_ID_NOT_FOUND_OR_DATA_MISMATCH'),
                doc,
            );
        }
    } catch (error) {
        console.log('Internal Server Error ', error);
        common_files.com_response(res, 500, false, req.t('ERROR'), error);
    }
};
exports.automation_get_otp = async (req, res) => {
    try {
        /*secure this cron keys only valid for testing and prod automation purpose to dsiable this on prod data centric services */
        if (req.headers.otp_key === util_key.DSCRONKEY) {
            const cond = { email: req.query.email };
            const proj = {};
            const doc = await base_control.get(user, cond, proj);
            common_files.com_response(res, 200, true, req.t('GET_OTP'), {
                password: doc.data.password,
                otp: doc.data.otp,
            });
        } else {
            common_files.com_response(res, 404, false, req.t('ERROR'));
        }
    } catch (error) {
        common_files.com_response(res, 500, false, req.t('ERROR'), error);
    }
};

exports.loggedInUserDetails = async (req, res) => {
    try {
        const { userId } = req.params;
        const cond = { _id: userId, isDeleted: false, isActive: true };
        const proj = {};
        const doc = await base_control.get(user, cond, proj);
        if (doc.status) {
            let roles;
            let report_to;
            if (doc.data._role_id) {
                const populate = { path: 'roles._role_id' };
                const roles_data = await base_control.get_list_populate(
                    role_assign,
                    { _id: ObjectId(doc.data._role_id) },
                    {},
                    populate,
                );
                const role_details = [];
                roles_data.data[0].roles.forEach((element) => {
                    if (element._role_id.isActive) {
                        element.role_name = element._role_id.name;
                        role_details.push(element);
                    }
                });
                roles = role_details;
                report_to = roles_data.data[0].report_to;
            }

            if (!doc.data.verification.mobile) {
                await base_control.update(user, doc.data._id, {
                    'verification.mobile': true,
                });
            }
            const sessionGlobalAttendanceSetting = await globalSettingSchema
                .findOne(
                    {},
                    {
                        warningMode: 1,
                        staffFacial: '$basicDetails.staffFacial',
                        studentFacial: '$basicDetails.studentFacial',
                        selfRegistrationDocument: '$basicDetails.selfRegistrationDocument',
                    },
                )
                .lean();
            // const tokens = await tokenUtil.generateAuthTokens(doc.data);
            const respData = {
                ...doc.data.toObject(),
                /* tokens, */ roles,
                report_to,
                warningMode: sessionGlobalAttendanceSetting?.warningMode || 'course',
                staffFacial: sessionGlobalAttendanceSetting?.staffFacial ?? true,
                studentFacial: sessionGlobalAttendanceSetting?.studentFacial ?? true,
                selfRegistrationDocument:
                    sessionGlobalAttendanceSetting?.selfRegistrationDocument ?? true,
                encryptedRoutes: encryptedRoutes(),
            };
            common_files.sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('USER_LOGGED_IN_SUCCESSFULLY'),
                respData,
            );
        } else {
            common_files.sendResponseWithRequest(
                req,
                500,
                false,
                req.t('EMAIL_ID_NOT_FOUND'),
                req.t('EMAIL_ID_NOT_FOUND'),
            );
        }
    } catch (error) {
        common_files.sendResponseWithRequest(req, res, 500, false, req.t('ERROR'), error);
    }
};

exports.IntegrationUserSync = async (req, res) => {
    try {
        const { userId, selectedUserIds } = req.body;
        const query = {
            // isActive: true,
            isDeleted: false,
            status: 'completed',
            ...(userId && userId.length && { user_id: { $nin: userId } }),
            ...(selectedUserIds && selectedUserIds.length && { user_id: { $in: selectedUserIds } }),
        };
        const userDocs = await user
            .find(query, {
                email: 1,
                dob: 1,
                verification: 1,
                address: 1,
                name: 1,
                gender: 1,
                user_id: 1,
                user_type: 1,
                'biometric_data.face': 1,
                institution_role: 1,
                id_doc: 1,
                mobile: 1,
                status: 1,
                password: 1,
            })
            .lean();
        common_files.com_response(res, 200, true, req.t('USER_LIST'), userDocs);
    } catch (error) {
        common_files.com_response(res, 500, false, req.t('ERROR'), error);
    }
};

exports.userBiometricRegister = async (req) => {
    try {
        const { body = {} } = req;
        const { userId, center, left, right, up, faceDescriptors } = body;
        const userFilter = { _id: convertToMongoObjectId(userId) };
        const userData = await user
            .findOne(userFilter, {
                status: 1,
                name: 1,
                email: 1,
                user_id: 1,
                mobile: 1,
            })
            .lean();
        if (!userData) return { statusCode: 404, message: req.t('ERROR_USER_ID_IS_NOT_FOUND') };
        const facialTrained = JSON.parse(faceDescriptors).map((faceDescriptorElement) => {
            let faceUrl;
            switch (faceDescriptorElement.face) {
                case 'center':
                    faceUrl = center;
                    break;
                case 'left':
                    faceUrl = left;
                    break;
                case 'right':
                    faceUrl = right;
                    break;
                case 'up':
                    faceUrl = up;
                    break;
                default:
                    break;
            }
            // const formattedDescriptor = faceDescriptorElement.descriptor.replace(/,\s*]$/, ']');
            return {
                bucketUrl: faceUrl,
                labeledFaceDescriptors: {
                    label: faceUrl.split('/').pop(),
                    descriptors: [faceDescriptorElement.descriptor],
                },
                trainedAt: new Date(),
            };
        });

        const userObject = {
            'biometric_data.face': [center, left, right, up],
            ...(userData.status !== constant.COMPLETED &&
                userData.name && {
                    'verification.face': true,
                    'verification.finger': true,
                    'verification.data': constant.DONE,
                    status: constant.COMPLETED,
                }),
            'biometric_data.facialTrained': facialTrained,
        };
        await user.updateOne(userFilter, userObject);
        if (userData.name && userData.status !== constant.COMPLETED) {
            const emailContent = `<p>${req.t('DEAR')} ${common_fun.nameFormatter(
                userData.name,
            )},<br>${common_fun.emailGreetingContent()}<br><br>${req.t(
                'USER_FACE_REG_EMAIL',
            )}<br><br>${common_fun.emailRegardsContent()}</p>`;
            common_fun.send_email(userData.email, 'DigiClass Alert', emailContent);
            if (userData.mobile) common_fun.send_sms(userData.mobile, req.t('USER_FACE_REG_SMS'));
        }
        // Push Face URL & Trained Face Descriptor to DigiAuth
        if (userData.name) {
            digiAuthService
                .syncUser({
                    employeeOrAcademicId: userData.user_id,
                    facial: [center, left, right, up],
                    userId: userData._id,
                    facialTrained,
                })
                .then((updatedUser) => {
                    console.info(updatedUser, 'Auth User Facial Updated');
                })
                .catch((err) => {
                    console.error(err, ' Error User Facial Update');
                });
            if (SERVICES.ANNOUNCEMENT_V2 === 'true') {
                const { sendAnnouncement } = require('../announcement/announcement_service');
                await sendAnnouncement(userId, 'newUser');
            }
        }
        return {
            statusCode: 200,
            message: 'USER_FACE_SUCCESSFULLY_STORED_IN_DB',
        };
    } catch (error) {
        console.error(error);
        return new Error();
    }
};
