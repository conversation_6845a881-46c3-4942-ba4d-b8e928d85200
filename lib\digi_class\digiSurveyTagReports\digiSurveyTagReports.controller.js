const digiSurveyResponseSchema = require('../digiSurveyResponse/digiSurveyResponse.model');
const digiSurveyTagReportSchema = require('./digiSurveyTagReports.model');
const surveySchema = require('../../models/digiSurvey');
const digiSurveyExternalUserSchema = require('./digiSurveyExternalUsers.model');
const userSchema = require('../../models/user');
const { convertToMongoObjectId, paginator } = require('../../utility/common');
const {
    COMPLETED,
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    DS_LEVEL_KEY,
    USER_MODULE_PERMISSION_CATEGORY_KEYS: { TAGS, GROUPS, FAMILIES },
    SURVEY_QUESTION_TYPE: { RATING },
    GENDER: { MALE, FEMALE },
    ALL,
    TAG_REPORT_REFRESH_STATE: { REGULAR },
    DC_STAFF,
    DS_UPDATE,
    DS_DELETE,
    TAG_REPORT_DEFAULT_RATING_VALUE,
} = require('../../utility/constants');
const {
    getExcludedSurveyList,
    analyzeTagReportByView,
    getTagReportResponseRateAnalysisFromCache,
    getTotalDocument,
    convertRatingValuesToFive,
    getTotalAnsweredUserCount,
} = require('./digiSurveyTagReports.service');
const { getPaginationValues } = require('../../utility/pagination');

exports.getExistingTagReport = async ({ query = {} }) => {
    try {
        const { reportId, createdBy } = query;
        if (reportId) {
            const existingTagReportDetails = await digiSurveyTagReportSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(reportId),
                    },
                    {
                        tagReportName: 1,
                        selectedTags: 1,
                        selectedGroups: 1,
                        selectedFamilies: 1,
                        noOfTags: 1,
                        targetRangeDetails: 1,
                    },
                )
                .lean();
            return {
                statusCode: 200,
                message: 'TAG_REPORT_LIST',
                data: existingTagReportDetails,
            };
        }
        const createdTagReport = await digiSurveyTagReportSchema.create({
            createdBy,
        });
        return {
            statusCode: 200,
            message: 'TAG_REPORT_CREATED_SUCCESSFULLY',
            data: { _id: createdTagReport._id },
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

exports.getTagBasedQuestions = async ({ body = {} }) => {
    try {
        const {
            tagId,
            institutionCalendarId,
            reportId,
            pageNo,
            limits,
            surveyLevel = [],
            programId = [],
            courseId = [],
            year = [],
            level = [],
            term = [],
        } = body;
        const surveyDetails = await surveySchema
            .find(
                {
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    status: COMPLETED,
                    isDeleted: false,
                    ...(surveyLevel && surveyLevel.length && { surveyLevel: { $in: surveyLevel } }),
                    ...(year && year.length && { yearNo: { $in: year } }),
                    ...(level && level.length && { levelNo: { $in: level } }),
                    ...(term && term.length && { term: { $in: term } }),
                    ...(programId &&
                        programId.length && {
                            programId: {
                                $in: programId.map((programIdElement) =>
                                    convertToMongoObjectId(programIdElement),
                                ),
                            },
                        }),
                    ...(courseId &&
                        courseId.length && {
                            courseId: {
                                $in: courseId.map((programIdElement) =>
                                    convertToMongoObjectId(programIdElement),
                                ),
                            },
                        }),
                    'mappedTagDetails.tagId': convertToMongoObjectId(tagId),
                },
                {
                    'questions.pages.elements': 1,
                    mappedTagDetails: 1,
                    'outComeDetails.prefixSentence': 1,
                },
            )
            .lean();
        if (!surveyDetails.length) {
            return {
                statusCode: 200,
                message: 'NO_DATA',
                data: [],
            };
        }
        // Preprocess to find matching questions
        const questionDetails = [];
        const surveyStudentResponseFindQuery = [];
        surveyDetails.forEach((surveyElement) => {
            const {
                _id: surveyId,
                mappedTagDetails,
                questions: { pages = [] },
                outComeDetails: { prefixSentence = '' },
            } = surveyElement;
            const constructedTaggedQuestion = new Map();
            mappedTagDetails
                .filter(({ tagId: mappedTagId }) => String(mappedTagId) === String(tagId))
                .forEach(({ questionId: mappedQuestionId, isOutcomeQuestion }) => {
                    if (mappedQuestionId) {
                        constructedTaggedQuestion.set(String(mappedQuestionId), {
                            isOutcomeQuestion,
                        });
                    }
                });
            pages.forEach(({ elements = [] }) => {
                const matchedRatingQuestion = elements.filter(
                    ({ uniqueId, type }) =>
                        constructedTaggedQuestion.has(String(uniqueId)) && type === RATING,
                );
                matchedRatingQuestion.forEach((matchedQuestionElement) => {
                    const {
                        uniqueId,
                        title: questionTitle,
                        name = '',
                        rateValues = [],
                    } = matchedQuestionElement;
                    const getConstructedTaggedQuestion = constructedTaggedQuestion.get(
                        String(uniqueId),
                    );
                    const maxRatingPoints =
                        rateValues?.[rateValues.length - 1]?.value ??
                        TAG_REPORT_DEFAULT_RATING_VALUE;
                    questionDetails.push({
                        surveyId,
                        questionTitle: questionTitle || name,
                        questionId: uniqueId,
                        maxRatingPoints,
                        isOutcomeQuestion: getConstructedTaggedQuestion?.isOutcomeQuestion ?? false,
                        prefixSentence,
                    });
                    surveyStudentResponseFindQuery.push({
                        surveyId: convertToMongoObjectId(surveyId),
                        questionType: RATING,
                        questionId: uniqueId,
                    });
                });
            });
        });
        if (!surveyStudentResponseFindQuery.length) {
            return {
                statusCode: 200,
                message: 'NO_DATA',
                data: [],
            };
        }
        // Fetch user survey responses for the relevant questions
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                { $or: surveyStudentResponseFindQuery },
                { questionId: 1, answer: 1, gender: 1, surveyId: 1 },
            )
            .lean();
        //  check if any surveyExclude for the particular question in the report
        const excludedSurvey = await getExcludedSurveyList({ reportId, tagId });
        const checkIsSurveyExcluded = ({ currentSurveyId, currentQuestionId, currentTagId }) => {
            return (
                excludedSurvey &&
                excludedSurvey.excludedSurveyIds.some(
                    (excludedSurveyElement) =>
                        String(excludedSurveyElement.tagId) === String(currentTagId) &&
                        String(excludedSurveyElement.questionId) === String(currentQuestionId) &&
                        String(excludedSurveyElement.surveyIds).includes(String(currentSurveyId)),
                )
            );
        };
        // Group responses by questionId
        const genderBasedQuestionResponse = new Map();
        userSurveyResponses.forEach(({ surveyId, questionId, answer, gender }) => {
            const constructedQuestionKey = `${surveyId}_${questionId}`;
            if (!genderBasedQuestionResponse.has(constructedQuestionKey)) {
                genderBasedQuestionResponse.set(constructedQuestionKey, {
                    maleResponse: [],
                    femaleResponse: [],
                    publicResponse: [],
                });
            }
            if (
                !checkIsSurveyExcluded({
                    currentSurveyId: surveyId,
                    currentQuestionId: questionId,
                    currentTagId: tagId,
                })
            ) {
                const existingGenderBasedResponse =
                    genderBasedQuestionResponse.get(constructedQuestionKey);
                if (gender === MALE) existingGenderBasedResponse.maleResponse.push(answer);
                else if (gender === FEMALE) existingGenderBasedResponse.femaleResponse.push(answer);
                else existingGenderBasedResponse.publicResponse.push(answer);
            }
        });
        // Build the final result by mapping question details with responses
        const questionBasedResponse = new Map();
        questionDetails.forEach(
            ({
                surveyId,
                questionTitle,
                questionId,
                maxRatingPoints = TAG_REPORT_DEFAULT_RATING_VALUE,
                prefixSentence,
                isOutcomeQuestion,
            }) => {
                const questionBasedResponseKey = `${surveyId}_${questionId}`;
                const responseGroup = genderBasedQuestionResponse.get(questionBasedResponseKey) || {
                    maleResponse: [],
                    femaleResponse: [],
                    publicResponse: [],
                };
                let constructedQuestionBasedKey = `${questionId}-${isOutcomeQuestion}`;
                if (isOutcomeQuestion) {
                    constructedQuestionBasedKey = `${constructedQuestionBasedKey}-${prefixSentence}`;
                }
                // Initialize the question-based entry if it doesn't exist
                if (!questionBasedResponse.has(constructedQuestionBasedKey)) {
                    questionBasedResponse.set(constructedQuestionBasedKey, {
                        questionId,
                        questionTitle,
                        isOutcomeQuestion,
                        prefixSentence: isOutcomeQuestion ? prefixSentence : '',
                        meanValues: {
                            male: [],
                            female: [],
                            public: [],
                        },
                    });
                }
                const questionEntry = questionBasedResponse.get(
                    constructedQuestionBasedKey,
                ).meanValues;
                questionEntry.male.push(
                    ...convertRatingValuesToFive({
                        values: responseGroup.maleResponse,
                        maxRatingPoints,
                    }),
                );
                questionEntry.female.push(
                    ...convertRatingValuesToFive({
                        values: responseGroup.femaleResponse,
                        maxRatingPoints,
                    }),
                );
                questionEntry.public.push(
                    ...convertRatingValuesToFive({
                        values: responseGroup.publicResponse,
                        maxRatingPoints,
                    }),
                );
            },
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: paginator([...questionBasedResponse.values()], pageNo, limits),
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

exports.getQuestionBasedSurveyDetails = async ({ query = {} }) => {
    try {
        const {
            questionId,
            tagId,
            institutionCalendarId,
            reportId,
            pageNo,
            limits,
            isOutcomeQuestion,
            prefixSentence,
        } = query;
        const surveyDetails = await surveySchema
            .find(
                {
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    status: COMPLETED,
                    isDeleted: false,
                    mappedTagDetails: {
                        $elemMatch: {
                            tagId: convertToMongoObjectId(tagId),
                            questionId,
                        },
                    },
                    ...(isOutcomeQuestion === 'true' && {
                        'outComeDetails.prefixSentence': prefixSentence,
                    }),
                },
                {
                    surveyName: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    surveyVersionName: 1,
                    'multiSelectedProgramIds.programId': 1,
                    'multiSelectedProgramIds.term': 1,
                    'multiSelectedProgramIds.yearNo': 1,
                    'multiSelectedProgramIds.levelNo': 1,
                    'multiSelectedProgramIds.rotationCount': 1,
                    'multiSelectedProgramIds.courseId': 1,
                },
            )
            .populate([
                { path: 'multiSelectedProgramIds.programId', select: { name: 1 } },
                { path: 'multiSelectedProgramIds.courseId', select: { course_name: 1 } },
                { path: 'surveyBankId', select: { cloneName: 1 } },
            ])
            .lean();
        const excludedSurvey = await getExcludedSurveyList({
            reportId,
            tagId,
            questionId,
            prefixSentence,
            isOutcomeQuestion,
        });
        const paginatedSurveyDetails = paginator(surveyDetails, pageNo, limits);
        const excludedSurveyDetails =
            excludedSurvey &&
            excludedSurvey.excludedSurveyIds.find(
                (excludedSurveyIdElement) =>
                    String(excludedSurveyIdElement.tagId) === String(tagId) &&
                    String(excludedSurveyIdElement.questionId) === String(questionId) &&
                    (isOutcomeQuestion === 'false' ||
                        excludedSurveyIdElement.prefixSentence === prefixSentence),
            );
        paginatedSurveyDetails.data.forEach((paginatedSurveyDetailElement) => {
            paginatedSurveyDetailElement.isExclude =
                (excludedSurveyDetails &&
                    String(excludedSurveyDetails.surveyIds).includes(
                        String(paginatedSurveyDetailElement._id),
                    )) ||
                false;
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: paginatedSurveyDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSingleSurveyDetail = async ({ query = {} }) => {
    try {
        const { surveyId } = query;
        const surveyDetails = await surveySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    programId: 1,
                    surveyUsers: 1,
                    'questions.pages': 1,
                    courseId: 1,
                    createdBy: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .populate({ path: 'programId', select: { code: 1, name: 1 } })
            .populate({ path: 'courseId', select: { course_code: 1, course_name: 1 } })
            .lean();
        const totalAnsweredUserCount = await getTotalAnsweredUserCount({
            questions: surveyDetails.questions.pages,
            surveyId: surveyDetails._id,
        });
        const surveyParticipantCount = {
            programId: '',
            courseId: '',
            createdBy: '',
            participantCount: 0,
            totalAnsweredUserCount,
        };
        if (surveyDetails) {
            surveyParticipantCount.programId = surveyDetails.programId;
            surveyParticipantCount.participantCount = surveyDetails.surveyUsers.length;
            surveyParticipantCount.courseId = surveyDetails.courseId || '';
            surveyParticipantCount.createdBy = surveyDetails.createdBy || '';
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: surveyParticipantCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.excludeSurvey = async ({ body = {} }) => {
    try {
        const {
            reportId,
            tagId,
            questionId,
            surveyIds = [],
            isOutcomeQuestion,
            prefixSentence,
        } = body;
        const isAlreadyExcluded = await digiSurveyTagReportSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(reportId),
                    'excludedSurveyIds.tagId': convertToMongoObjectId(tagId),
                    'excludedSurveyIds.questionId': questionId,
                    ...(isOutcomeQuestion && {
                        'excludedSurveyIds.prefixSentence': prefixSentence,
                    }),
                },
                { _id: 1 },
            )
            .lean();
        const updateQuery = isAlreadyExcluded
            ? {
                  $set: {
                      'excludedSurveyIds.$[excludedSurveyElement].surveyIds': surveyIds,
                  },
              }
            : {
                  $push: {
                      excludedSurveyIds: {
                          tagId: convertToMongoObjectId(tagId),
                          questionId,
                          isOutcomeQuestion,
                          prefixSentence,
                          surveyIds,
                      },
                  },
              };
        const arrayFilter = isAlreadyExcluded
            ? {
                  arrayFilters: [
                      {
                          'excludedSurveyElement.tagId': convertToMongoObjectId(tagId),
                          'excludedSurveyElement.questionId': questionId,
                          ...(isOutcomeQuestion && {
                              'excludedSurveyElement.prefixSentence': prefixSentence,
                          }),
                      },
                  ],
              }
            : {};
        await digiSurveyTagReportSchema.updateOne(
            {
                _id: convertToMongoObjectId(reportId),
            },
            updateQuery,
            arrayFilter,
        );
        return {
            statusCode: 200,
            message: 'TAG_REPORT_UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createTagReport = async ({ body = {}, headers = {} }) => {
    try {
        const { institutionId } = headers;
        const {
            createdBy,
            tagReportName,
            viewBy,
            noOfTags,
            targetRangeDetails,
            selectedTags,
            selectedGroups,
            selectedFamilies,
            reportId,
        } = body;
        const updatedSurveyTagReport = await digiSurveyTagReportSchema.updateOne(
            {
                _id: convertToMongoObjectId(reportId),
            },
            {
                $set: {
                    institutionId,
                    createdBy,
                    tagReportName,
                    viewBy,
                    noOfTags,
                    targetRangeDetails,
                    selectedTags,
                    selectedGroups,
                    selectedFamilies,
                },
            },
            { upsert: true },
        );
        return {
            statusCode:
                updatedSurveyTagReport.upsertedCount || updatedSurveyTagReport.modifiedCount
                    ? 200
                    : 400,
            message: updatedSurveyTagReport.upsertedCount
                ? 'TAG_REPORT_CREATED_SUCCESSFULLY'
                : updatedSurveyTagReport.modifiedCount
                ? 'TAG_REPORT_UPDATED_SUCCESSFULLY'
                : 'UNABLE_TO_CREATE_OR_UPDATE_TAG_REPORT',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTagReportList = async ({ query = {} }) => {
    try {
        const { createdBy } = query;
        const tagReportList = await digiSurveyTagReportSchema
            .find(
                {
                    createdBy: convertToMongoObjectId(createdBy),
                    isDeleted: false,
                },
                {
                    tagReportName: 1,
                    viewBy: 1,
                    noOfTags: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        return {
            statusCode: 200,
            message: 'TAG_REPORT_LIST',
            data: tagReportList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTagReportHeaderFilters = async ({ body = {} }) => {
    try {
        const {
            reportId,
            institutionCalendarId,
            surveyLevel = [],
            programId = [],
            courseId = [],
            year = [],
            level = [],
            term = [],
            requiredField = '',
        } = body;
        const {
            viewBy = '',
            selectedTags: { tags = [] },
            selectedGroups: { groups = [] },
            selectedFamilies: { families = [] },
        } = await digiSurveyTagReportSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(reportId),
                },
                {
                    'selectedTags.tags._id': 1,
                    'selectedTags.tags.code': 1,
                    'selectedTags.tags.description': 1,
                    'selectedTags.tags.name': 1,
                    'selectedGroups.groups._id': 1,
                    'selectedGroups.groups.code': 1,
                    'selectedGroups.groups.description': 1,
                    'selectedGroups.groups.name': 1,
                    'selectedGroups.groups.tags': 1,
                    'selectedFamilies.families._id': 1,
                    'selectedFamilies.families.code': 1,
                    'selectedFamilies.families.description': 1,
                    'selectedFamilies.families.name': 1,
                    'selectedFamilies.families.groups': 1,
                    viewBy: 1,
                },
            )
            .lean();
        const surveyDetails = await surveySchema
            .find(
                {
                    status: COMPLETED,
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    isTemplate: true,
                    isDeleted: false,
                    ...(surveyLevel !== ALL &&
                        surveyLevel.length && { surveyLevel: { $in: surveyLevel } }),
                    ...(programId !== ALL && programId.length && { programId: { $in: programId } }),
                    ...(courseId !== ALL && courseId.length && { courseId: { $in: courseId } }),
                    ...(year !== ALL && year.length && { yearNo: { $in: year } }),
                    ...(level !== ALL && level.length && { levelNo: { $in: level } }),
                    ...(term !== ALL && term.length && { term: { $in: term } }),
                    ...(tags.length && {
                        'mappedTagDetails.tagId': {
                            $in: tags.map((tagElement) => tagElement._id),
                        },
                    }),
                },
                {
                    programId: 1,
                    term: 1,
                    yearNo: 1,
                    levelNo: 1,
                    courseId: 1,
                    surveyLevel: 1,
                    mappedTagDetails: 1,
                },
            )
            .populate({ path: 'programId', select: { code: 1, name: 1 } })
            .populate({ path: 'courseId', select: { course_code: 1, course_name: 1 } })
            .lean();
        const { totalSurveyLevels, totalProgramIds, totalCourseIds, uniqueTagIds } =
            surveyDetails.reduce(
                (
                    acc,
                    { programId, term, yearNo, levelNo, courseId, surveyLevel, mappedTagDetails },
                ) => {
                    if (
                        requiredField === DS_LEVEL_KEY &&
                        !acc.totalSurveyLevels.includes(surveyLevel)
                    ) {
                        acc.totalSurveyLevels.push(surveyLevel);
                    }
                    if (requiredField === DS_PROGRAM_KEY) {
                        if (
                            surveyLevel === DS_PROGRAM_KEY &&
                            !acc.uniqueProgramIds.has(String(programId._id))
                        ) {
                            acc.totalProgramIds.push(programId);
                            acc.uniqueProgramIds.add(String(programId._id));
                        }
                        if (surveyLevel === DS_COURSE_KEY) {
                            const courseKey = `${term}-${yearNo}-${levelNo}-${courseId._id}`;
                            if (!acc.uniqueCourseIds.has(courseKey)) {
                                acc.totalCourseIds.push({ term, yearNo, levelNo, courseId });
                                acc.uniqueCourseIds.add(courseKey);
                            }
                        }
                    }
                    acc.uniqueTagIds = new Set([
                        ...acc.uniqueTagIds,
                        ...mappedTagDetails.map((mappedTagDetailElement) =>
                            String(mappedTagDetailElement.tagId),
                        ),
                    ]);
                    return acc;
                },
                {
                    totalSurveyLevels: [],
                    totalProgramIds: [],
                    totalCourseIds: [],
                    uniqueProgramIds: new Set(),
                    uniqueCourseIds: new Set(),
                    uniqueTagIds: new Set(),
                },
            );
        const totalTagDetails =
            requiredField === TAGS
                ? tags.filter((uniqueTagElement) => uniqueTagIds.has(String(uniqueTagElement._id)))
                : [];
        const totalGroupDetails =
            requiredField === GROUPS
                ? groups.filter((uniqueGroupElement) =>
                      uniqueGroupElement.tags.some((uniqueMappedTagElement) =>
                          uniqueTagIds.has(String(uniqueMappedTagElement)),
                      ),
                  )
                : [];
        const totalFamilies = [];
        const uniqueFamilyIds = new Set();
        if (requiredField === FAMILIES) {
            families.forEach((uniqueFamilyElement) => {
                const hasMatchingGroup = uniqueFamilyElement.groups.some((groupIdElement) => {
                    const group = groups.find(
                        (groupElement) => String(groupElement._id) === String(groupIdElement),
                    );
                    return group?.tags?.some((tagElement) => uniqueTagIds.has(String(tagElement)));
                });
                if (hasMatchingGroup && !uniqueFamilyIds.has(String(uniqueFamilyElement._id))) {
                    uniqueFamilyIds.add(uniqueFamilyElement._id);
                    delete uniqueFamilyElement.groups;
                    totalFamilies.push({ ...uniqueFamilyElement, groups: undefined });
                }
            });
        }
        return {
            statusCode: 200,
            message: 'TAG_REPORT_HEADER_FILTERS',
            data: {
                ...(requiredField === DS_LEVEL_KEY && { totalSurveyLevels }),
                ...(requiredField === DS_PROGRAM_KEY && { totalProgramIds, totalCourseIds }),
                ...(requiredField === TAGS && { totalTagDetails }),
                ...(requiredField === GROUPS && { totalGroupDetails }),
                ...(requiredField === FAMILIES && { totalFamilies }),
                viewBy,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTagReportResponseRateAnalysis = async ({ body = {} }) => {
    try {
        const {
            reportId,
            institutionCalendarId,
            surveyLevel = [],
            programId = [],
            courseId = [],
            year = [],
            level = [],
            term = [],
            selectedFamilyIds = [],
            selectedGroupsIds = [],
            selectedTagIds = [],
            viewBy = TAGS,
            familyId,
            groupId,
            state = REGULAR,
        } = body;
        const {
            questionDetails = [],
            studentResponses = [],
            tagReportDetail = {},
            refreshedTime = new Date(),
        } = await getTagReportResponseRateAnalysisFromCache({ reportId, state });
        const filteredSurveyIds = [];
        const filteredTagQuestionDetails = questionDetails.filter((questionDetailElement) => {
            const isSurveyPresent =
                String(institutionCalendarId) ===
                    String(questionDetailElement.institutionCalendarId) &&
                (!surveyLevel.length || surveyLevel.includes(questionDetailElement.surveyLevel)) &&
                (!programId.length ||
                    programId.includes(String(questionDetailElement.programId))) &&
                (!courseId.length || courseId.includes(String(questionDetailElement.courseId))) &&
                (!year.length || year.includes(questionDetailElement.yearNo)) &&
                (!level.length || level.includes(questionDetailElement.levelNo)) &&
                (!term.length || term.includes(questionDetailElement.term));
            if (isSurveyPresent) {
                filteredSurveyIds.push(String(questionDetailElement.surveyId));
                return true;
            }
            return false;
        });
        const filteredStudentResponses = studentResponses.filter((studentResponseELement) =>
            filteredSurveyIds.includes(String(studentResponseELement.surveyId)),
        );
        const constructedTagBasedReports = new Map();
        filteredTagQuestionDetails.forEach((questionDetailElement) => {
            const questionBasedResponse = filteredStudentResponses.filter(
                (userSurveyResponseElement) =>
                    String(userSurveyResponseElement.surveyId) ===
                        String(questionDetailElement.surveyId) &&
                    String(userSurveyResponseElement.questionId) ===
                        String(questionDetailElement.questionId),
            );
            if (!constructedTagBasedReports.has(String(questionDetailElement.mappedTagId))) {
                constructedTagBasedReports.set(String(questionDetailElement.mappedTagId), {
                    tagId: String(questionDetailElement.mappedTagId),
                    totalQuestions: 0,
                    studentAnswers: [],
                });
            }
            const existingConstructedTagBasedReport = constructedTagBasedReports.get(
                String(questionDetailElement.mappedTagId),
            );
            existingConstructedTagBasedReport.totalQuestions += 1;
            existingConstructedTagBasedReport.studentAnswers.push(
                ...questionBasedResponse.map(
                    (questionBasedResponseElement) =>
                        (questionBasedResponseElement.answer /
                            questionDetailElement.maxRatingPoints) *
                        TAG_REPORT_DEFAULT_RATING_VALUE, // student responses converted for 5-based rating values
                ),
            );
        });
        const tagReportResponseRateAnalysis = analyzeTagReportByView({
            viewBy,
            tagReportDetail,
            constructedTagBasedReports,
            familyId,
            groupId,
            selectedTagIds,
            selectedGroupsIds,
            selectedFamilyIds,
        });
        return {
            statusCode: 200,
            message: 'TAG_REPORT_LIST',
            data: { refreshedTime, tagReportResponseRateAnalysis },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.deleteTagReport = async ({ body = {} }) => {
    try {
        const { reportId } = body;
        const deletedTagReport = await digiSurveyTagReportSchema.updateOne(
            {
                _id: convertToMongoObjectId(reportId),
            },
            {
                $set: { isDeleted: true },
            },
        );
        let response = { statusCode: 200, message: 'TAG_REPORT_DELETED_SUCCESSFULLY' };
        if (!deletedTagReport.modifiedCount) {
            response = { statusCode: 400, message: 'UNABLE_TO_DELETE_TAG_REPORT' };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getExternalUsers = async ({ query = {} }) => {
    try {
        const { searchKey = '', isTotalCountNeeded = 'false' } = query;
        const externalUserFindQuery = {
            isDeleted: false,
            ...(searchKey &&
                searchKey.trim() !== '' && {
                    $or: [
                        { name: { $regex: searchKey, $options: 'i' } },
                        { email: { $regex: searchKey, $options: 'i' } },
                    ],
                }),
        };
        const { skip, limit } = getPaginationValues(query);
        const externalUsers = await digiSurveyExternalUserSchema
            .find(externalUserFindQuery, { name: 1, email: 1 })
            .sort({ name: 1 })
            .skip(skip)
            .limit(limit);
        const totalDocument =
            isTotalCountNeeded === 'true'
                ? await getTotalDocument({
                      model: digiSurveyExternalUserSchema,
                      findQuery: externalUserFindQuery,
                  })
                : 0;
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { externalUsers, totalDocument },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTotalStaff = async ({ query = {} }) => {
    try {
        const { searchKey = '', isTotalCountNeeded = 'false' } = query;
        const staffFindQuery = {
            isDeleted: false,
            isActive: true,
            user_type: DC_STAFF,
            status: COMPLETED,
            ...(searchKey &&
                searchKey.trim() !== '' && {
                    $or: [
                        { 'name.first': { $regex: searchKey, $options: 'i' } },
                        { 'name.last': { $regex: searchKey, $options: 'i' } },
                        { 'name.family': { $regex: searchKey, $options: 'i' } },
                        { 'name.middle': { $regex: searchKey, $options: 'i' } },
                        { user_id: { $regex: searchKey, $options: 'i' } },
                    ],
                }),
        };
        const { skip, limit } = getPaginationValues(query);
        const totalStaffList = await userSchema
            .find(staffFindQuery, { user_type: 1, user_id: 1, name: 1, email: 1, gender: 1 })
            .sort({ 'name.first': 1 })
            .skip(skip)
            .limit(limit);
        const totalDocument =
            isTotalCountNeeded === 'true'
                ? await getTotalDocument({
                      model: userSchema,
                      findQuery: staffFindQuery,
                  })
                : 0;
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { totalStaffList, totalDocument },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateExternalUserList = async ({ body = {} }) => {
    try {
        const { updateId, newUserList, updateType } = body;
        switch (updateType) {
            case DS_DELETE:
                await digiSurveyExternalUserSchema.deleteOne({
                    _id: convertToMongoObjectId(updateId),
                });
                break;
            case DS_UPDATE:
                await digiSurveyExternalUserSchema.updateOne(
                    { _id: convertToMongoObjectId(updateId) },
                    { $set: newUserList },
                );
                break;
            default:
                await digiSurveyExternalUserSchema.create(newUserList);
        }
        return {
            statusCode: 201,
            message: 'EXTERNAL_USERS_UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.checkIsEmailDuplicate = async ({ query = {} }) => {
    try {
        const { email } = query;
        const isDuplicateEmail = await digiSurveyExternalUserSchema
            .findOne(
                {
                    email: email.trim(),
                },
                { _id: 1 },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: !!isDuplicateEmail,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
