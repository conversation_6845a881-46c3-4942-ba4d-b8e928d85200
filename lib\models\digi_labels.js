const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const labelSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        college_program_type: {
            type: String,
            trim: true,
        },
        theory: {
            type: String,
        },
        practical: {
            type: String,
        },
        clinical: {
            type: String,
        },
        t: { type: String },
        p: { type: String },
        c: { type: String },
        onsite: {
            type: String,
        },
        plo: {
            type: String,
        },
        clo: {
            type: String,
        },
        slo: {
            type: String,
        },
        level: {
            type: String,
        },
        term: {
            type: String,
        },
        standard: {
            type: String,
        },
        selective: {
            type: String,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        sg: {
            type: String,
        },
        mg: {
            type: String,
        },
        fg: {
            type: String,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_LABEL, labelSchema);
