const UserModel = require('../../../lib/models/user');
const PortfolioModel = require('../portfolio/portfolio.model');
const StudentResponseModel = require('../student-response/student-response.model');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const PortfolioAssignEvaluatorModel = require('./evaluation.model');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const FormModel = require('../form/form.model');

const { BadRequestError } = require('../../common/utils/api_error_util');
const {
    convertToMongoObjectId,
    isIDEquals,
    isNumber,
    roundToTwoDecimalPlaces,
} = require('../../common/utils/common.util');
const {
    LB,
    NOT_STARTED,
    PUBLISHED,
    REJECTED,
    APPROVED,
    COMPLETED,
    ON_GOING,
} = require('../../common/utils/enums');
const {
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const { getFacultyListByCourse } = require('../course/course.service');
const {
    calculateStudentAchievedPoints,
    calculateGlobalRubricPoint,
} = require('../rubric/rubric.helper');
const { calculateStudentTotalMarks } = require('./evaluation.helper');
const {
    ABSENT,
    PRESENT,
    REGULAR,
    ON_DUTY,
    LEAVE,
    PERMISSION,
} = require('../../common/utils/constants');

// get evaluator list by course
const getEvaluatorListByCourse = async ({
    programId,
    courseId,
    institutionCalendarId,
    childrenId,
}) => {
    const evaluators = await getFacultyListByCourse({
        programId,
        courseId,
        institutionCalendarId,
        childrenId,
    });

    const assignments = await PortfolioAssignEvaluatorModel.find(
        {
            programId,
            courseId,
            institutionCalendarId,
            childrenId,
        },
        { role: 1, 'evaluators.userId': 1 },
    ).lean();

    return evaluators.map((evaluator) => {
        const matchingAssignments = assignments.filter((assignment) =>
            assignment.evaluators.some((e) => isIDEquals(e.userId, evaluator.userId)),
        );

        return {
            ...evaluator,
            isAssigned: matchingAssignments.length > 0,
            roles: matchingAssignments.map(({ role }) => ({ ...role })),
        };
    });
};

// create or update evaluator assignment
const createOrUpdateEvaluatorAssignment = async ({
    programId,
    courseId,
    institutionCalendarId,
    portfolioId,
    componentId,
    childrenId,
    userId,
    role,
    evaluators,
    deliveryTypes,
}) => {
    await PortfolioAssignEvaluatorModel.updateOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            portfolioId,
            componentId,
            childrenId,
            ...(role?._id ? { 'role._id': role?._id } : { canApproveEvaluation: true }),
        },
        {
            $set: {
                createdBy: userId,
                ...(role?._id ? { role } : { canApproveEvaluation: true }),
                evaluators,
                deliveryTypes,
            },
            $setOnInsert: {
                programId,
                courseId,
                institutionCalendarId,
                portfolioId,
                componentId,
                childrenId,
            },
        },
        { upsert: true },
    );
};

// get student for evaluator
const getStudentForEvaluator = async ({
    programId,
    courseId,
    institutionCalendarId,
    portfolioId,
    componentId,
    childrenId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    userId,
}) => {
    const isEvaluatorAssigned = await PortfolioAssignEvaluatorModel.exists({
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        componentId,
        childrenId,
        'evaluators.userId': userId,
    });

    if (!isEvaluatorAssigned) {
        return { students: [], count: { total: 0, male: 0, female: 0 } };
    }

    const studentGroup = await getStudentListFromStudentGroup({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
    });

    const { sgStudentList: students = [] } = studentGroup;

    if (!students.length) {
        return {
            students: [],
            count: { total: 0, male: 0, female: 0 },
        };
    }

    const studentIds = students.map((student) => convertToMongoObjectId(student._student_id));

    const studentResponses = await StudentResponseModel.find(
        {
            componentId,
            childrenId,
            'student._id': { $in: studentIds },
        },
        {
            'student._id': 1,
            scheduleId: 1,
            evaluationStatus: 1,
            status: 1,
            formTimestamps: 1,
            componentId: 1,
            portfolioId: 1,
            childrenId: 1,
            formId: 1,
            totalMarks: 1,
            awardedMarks: 1,
            approvalStatus: 1,
            evaluators: 1,
            evaluations: 1,
            globalRubricTotalPoints: 1,
            globalRubricAwardedPoints: 1,
        },
    ).lean();

    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
        },
        {
            'components._id': 1,
            'components.code': 1,
            'components.deliveryTypes': 1,
            'components.children._id': 1,
            'components.children.awardedMarks': 1,
            'components.children.marks': 1,
            'components.children.formId': 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
        },
    ).lean();
    const component = portfolio?.components?.find((componentEntry) =>
        isIDEquals(componentEntry._id, componentId),
    );

    const children = component?.children?.find((child) => isIDEquals(child._id, childrenId));

    const activeStudents = await UserModel.find(
        { _id: { $in: studentIds }, isActive: true },
        { _id: 1, email: 1, name: 1, academicNo: '$user_id', gender: 1 },
    ).lean();

    const studentPortfolios = await StudentPortfolioModel.find(
        {
            portfolioId: portfolio._id,
            'components.children.isNewEntry': true,
        },
        {
            'components._id': 1,
            'components.children': 1,
            'student._id': 1,
            portfolioId: 1,
            componentId: 1,
            childrenId: 1,
        },
    ).lean();

    const assignedEvaluator = await PortfolioAssignEvaluatorModel.findOne(
        {
            childrenId,
            componentId,
            'evaluators.userId': convertToMongoObjectId(userId),
            'role._id': { $exists: true },
        },
        { 'role._id': 1, _id: 0 },
    ).lean();

    const form = await FormModel.findOne(
        {
            _id: children?.formId,
        },
        { 'evaluations.roleId': 1, 'evaluations.marks': 1, _id: 0 },
    ).lean();

    const matchedEvaluation = form?.evaluations?.find((e) =>
        isIDEquals(e.roleId, assignedEvaluator?.role?._id),
    );

    const formattedStudents = activeStudents.map(({ _id, name, academicNo, email, gender }) => ({
        name,
        studentId: _id,
        academicNo,
        email,
        gender: gender === 'male' ? 'M' : 'F',
    }));

    const schedules = [];

    if (component?.code === LB && component?.deliveryTypes?.length) {
        const courseSchedules = await CourseScheduleModel.find(
            {
                _institution_calendar_id: institutionCalendarId,
                _program_id: programId,
                _course_id: courseId,
                'students._id': { $in: studentIds },
                type: REGULAR,
                isDeleted: false,
                isActive: true,
                term: portfolio?.term,
                year_no: portfolio?.year,
                level_no: portfolio?.level,
                ...(portfolio?.rotation && { rotation: portfolio?.rotation }),
                ...(portfolio?.rotationCount && { rotationCount: portfolio?.rotationCount }),
            },
            {
                scheduleDate: '$schedule_date',
                session: 1,
                start: 1,
                end: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                'students._id': 1,
            },
        ).lean();
        schedules.push(...courseSchedules);

        formattedStudents.forEach((student) => {
            const matchedSchedules = courseSchedules.filter((schedule) => {
                return (
                    component.deliveryTypes.some(
                        (deliveryType) =>
                            deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                    ) && schedule.students.some((s) => isIDEquals(s._id, student.studentId))
                );
            });

            const formattedSchedules = [];
            let globalRubricTotalPoints = 0;
            let globalRubricAwardedPoints = 0;
            let globalRubricLength = 0;
            let awardedMarks = 0;
            matchedSchedules.forEach((schedule) => {
                const response = studentResponses.find(
                    (response) =>
                        isIDEquals(response.scheduleId, schedule._id) &&
                        isIDEquals(response.student._id, student.studentId) &&
                        isIDEquals(response.componentId, componentId) &&
                        isIDEquals(response.childrenId, childrenId),
                );
                let evaluator = {};

                const matchedEvaluator = response?.evaluators?.find((e) =>
                    isIDEquals(e.userId, userId),
                );
                if (matchedEvaluator) {
                    evaluator = {
                        awardedMarks: matchedEvaluator?.awardedMarks || 0,
                        marks: matchedEvaluator?.marks || 0,
                        globalRubricTotalPoints: matchedEvaluator?.globalRubricTotalPoints || 0,
                        globalRubricAwardedPoints: matchedEvaluator?.globalRubricAwardedPoints || 0,
                    };
                    awardedMarks += matchedEvaluator?.awardedMarks || 0;
                } else {
                    evaluator = {
                        marks: matchedEvaluation?.marks || 0,
                    };
                }

                if (evaluator?.globalRubricTotalPoints) {
                    globalRubricTotalPoints = evaluator?.globalRubricTotalPoints || 0;
                    globalRubricAwardedPoints += evaluator?.globalRubricAwardedPoints || 0;
                    globalRubricLength += 1;
                }

                formattedSchedules.push({
                    scheduleDate: schedule?.scheduleDate,
                    session: schedule?.session,
                    start: schedule?.start,
                    end: schedule?.end,
                    scheduleStartDateAndTime: schedule?.scheduleStartDateAndTime,
                    scheduleEndDateAndTime: schedule?.scheduleEndDateAndTime,
                    status: response?.status || NOT_STARTED,
                    evaluationStatus: response?.evaluationStatus || NOT_STARTED,
                    evaluatedBy: response?.evaluatedBy || null,
                    formTimestamps: response?.formTimestamps || {},
                    responseId: response?._id || '',
                    totalMarks: children?.marks || 0,
                    awardedMarks: response?.awardedMarks || 0,
                    _id: schedule?._id || '',
                    approvalStatus: response?.approvalStatus || null,
                    evaluator,
                });
            });

            const studentPortfolio = studentPortfolios.find(
                (studentPortfolio) =>
                    isIDEquals(studentPortfolio.portfolioId, portfolio._id) &&
                    isIDEquals(studentPortfolio?.student?._id, student.studentId),
            );
            if (studentPortfolio?.components?.length) {
                const studentComponent = studentPortfolio.components.find((componentEntry) =>
                    isIDEquals(componentEntry._id, component._id),
                );

                const studentEntries = studentComponent?.children?.filter(
                    (child) => child.isNewEntry,
                );
                student.entries = studentEntries;
            }
            const averageGlobalRubricAwardedPoints =
                globalRubricLength > 0 ? globalRubricAwardedPoints / globalRubricLength : 0;

            student.globalRubricAwardedPoints = averageGlobalRubricAwardedPoints;
            student.globalRubricTotalPoints = globalRubricTotalPoints;
            student.awardedMarks =
                formattedSchedules.length > 0
                    ? roundToTwoDecimalPlaces(awardedMarks / formattedSchedules.length)
                    : 0;

            student.schedules = formattedSchedules;
        });
    } else {
        formattedStudents.forEach((student) => {
            const response = studentResponses.find(
                (response) =>
                    isIDEquals(response.student._id, student.studentId) &&
                    isIDEquals(response.componentId, componentId) &&
                    isIDEquals(response.childrenId, childrenId),
            );

            const matchedEvaluator = response?.evaluators?.find((e) =>
                isIDEquals(e.userId, userId),
            );
            if (matchedEvaluator) {
                student.evaluator = {
                    awardedMarks: matchedEvaluator?.awardedMarks || 0,
                    marks: matchedEvaluator?.marks || 0,
                    globalRubricTotalPoints: matchedEvaluator?.globalRubricTotalPoints || 0,
                    globalRubricAwardedPoints: matchedEvaluator?.globalRubricAwardedPoints || 0,
                };
            } else {
                student.evaluator = {
                    marks: matchedEvaluation?.marks || 0,
                };
            }

            const studentPortfolio = studentPortfolios.find(
                (studentPortfolio) =>
                    isIDEquals(studentPortfolio.portfolioId, portfolio._id) &&
                    isIDEquals(studentPortfolio?.student?._id, student.studentId),
            );
            if (studentPortfolio?.components?.length) {
                const studentComponent = studentPortfolio.components.find((componentEntry) =>
                    isIDEquals(componentEntry._id, componentId),
                );

                const studentEntries = studentComponent?.children?.find((child) =>
                    isIDEquals(child._id, childrenId),
                );

                student.assignedDate = {
                    startDate: studentEntries?.startDate,
                    endDate: studentEntries?.endDate,
                };
            }

            student.status = response?.status || NOT_STARTED;
            student.evaluationStatus = response?.evaluationStatus || NOT_STARTED;
            student.evaluatedBy = response?.evaluatedBy || null;
            student.formTimestamps = response?.formTimestamps || {};
            student.responseId = response?._id || '';
            student.totalMarks = children?.marks || 0;
            student.awardedMarks = children?.awardedMarks || 0;
            student.approvalStatus = response?.approvalStatus || null;
        });
    }

    const count = formattedStudents.reduce(
        (acc, { gender }) => {
            acc.total++;
            if (gender === 'M') acc.male++;
            if (gender === 'F') acc.female++;
            return acc;
        },
        { total: 0, male: 0, female: 0 },
    );

    return { students: formattedStudents, count, schedules };
};

// get student response for evaluator
const getStudentResponseForEvaluator = async ({ responseId, isPages = false, userId }) => {
    const projection = {
        reviews: 1,
        portfolioId: 1,
        componentId: 1,
        childrenId: 1,
        totalMarks: 1,
        awardedMarks: 1,
        formId: 1,
        student: 1,
        type: 1,
        evaluators: 1,
        evaluations: 1,
        status: 1,
        scheduleId: 1,
        evaluationStatus: 1,
        approvalStatus: 1,
        formTimestamps: 1,
        title: 1,
    };

    if (isPages) projection.pages = 1;

    const response = await StudentResponseModel.findOne({ _id: responseId }, projection).lean();

    let evaluator = response?.evaluators?.find((e) => isIDEquals(e.userId, userId));

    if (!evaluator?.rubrics?.length) {
        const evaluationDoc = await PortfolioAssignEvaluatorModel.findOne(
            {
                componentId: response.componentId,
                childrenId: response.childrenId,
                'evaluators.userId': userId,
            },
            { role: 1 },
        ).lean();

        const matched = response?.evaluations?.find((e) => e.name === evaluationDoc?.role?.role);

        evaluator = {
            ...(evaluator || { userId }),
            rubrics: matched?.rubrics?.length ? matched.rubrics : [],
            marks: matched?.marks || 0,
        };
    }

    const { evaluators, evaluations, ...rest } = response || {};

    const portfolio = await StudentPortfolioModel.findOne(
        { _id: response?.portfolioId, isDeleted: false },
        { programId: 1, courseId: 1, institutionCalendarId: 1, components: 1 },
    ).lean();
    if (!portfolio) {
        throw new NotFoundError('PORTFOLIO_NOT_FOUND');
    }

    const program = await ProgramModel.findOne(
        { _id: portfolio?.programId, isDeleted: false },
        { name: 1, code: 1 },
    ).lean();

    const course = await CourseModel.findOne(
        { _id: portfolio?.courseId, isDeleted: false },
        { name: '$course_name', code: '$course_code' },
    );

    const component = portfolio?.components?.find((component) =>
        isIDEquals(component._id, response?.componentId),
    );

    const children = component?.children?.find((child) =>
        isIDEquals(child._id, response?.childrenId),
    );

    return {
        evaluator: {
            ...evaluator,
            ...(!evaluator?.globalRubrics?.length &&
                component?.hasGlobalRubric && {
                    globalRubrics: children?.rubrics,
                }),
        },
        ...rest,
        program,
        course,
        component,
        children: { name: children?.name, code: children?.code },
        institutionCalendarId: portfolio?.institutionCalendarId,
    };
};

// generate evaluation bulk updates for student response
const generateEvaluationBulkUpdatesForStudentResponse = async ({
    componentId,
    childrenId,
    studentIds,
    scheduleId,
    userId,
    marks,
    rubrics = [],
    globalRubrics = [],
    role = {},
    evaluatedBy = {},
}) => {
    const studentResponses = await StudentResponseModel.find(
        {
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
            ...(studentIds.length && {
                'student._id': {
                    $in: studentIds.map((studentId) => convertToMongoObjectId(studentId)),
                },
            }),
        },
        { evaluations: 1, evaluators: 1, totalMarks: 1 },
    ).lean();

    const students = [];
    let errorMessage = '';

    const bulkUpdates = studentResponses
        .map((response) => {
            const { evaluations = [], evaluators = [] } = response;
            const evaluator = evaluators.find((e) => isIDEquals(e.userId, userId));
            let globalAchievedPoints = {};
            if (globalRubrics.length) {
                globalAchievedPoints = calculateGlobalRubricPoint({
                    rubrics: globalRubrics,
                });
            }
            if (evaluator) {
                let studentAchievedPoints = {};
                if (rubrics.length) {
                    studentAchievedPoints = calculateStudentAchievedPoints({
                        rubrics,
                        totalMarks: evaluator.marks,
                    });
                }

                evaluator.rubrics = rubrics;
                evaluator.awardedMarks = isNumber(studentAchievedPoints?.awardedMarks)
                    ? studentAchievedPoints.awardedMarks
                    : marks;

                if (globalRubrics.length) {
                    evaluator.globalRubrics = globalRubrics;
                    evaluator.globalRubricTotalPoints = globalAchievedPoints?.totalRubricsPoint;
                    evaluator.globalRubricAwardedPoints = globalAchievedPoints?.awardedMarks;
                }

                if (evaluator?.marks < evaluator.awardedMarks) {
                    errorMessage = 'MARKS_CANNOT_BE_LESS_THAN_AWARDED_MARKS';
                    students.push(response?.student?._id);
                    return null;
                }
            } else {
                const evaluation = evaluations.find((e) => isIDEquals(e.roleId, role._id));
                let studentAchievedPoints = {};
                if (rubrics.length) {
                    studentAchievedPoints = calculateStudentAchievedPoints({
                        rubrics,
                        totalMarks: evaluation.marks,
                    });
                }

                const awardedMarks = isNumber(studentAchievedPoints?.awardedMarks)
                    ? studentAchievedPoints.awardedMarks
                    : marks;

                if (evaluation?.marks < awardedMarks) {
                    errorMessage = 'MARKS_CANNOT_BE_LESS_THAN_AWARDED_MARKS';
                    students.push(response?.student?._id);
                    return null;
                }

                evaluators.push({
                    rubrics,
                    awardedMarks,
                    ...evaluatedBy,
                    marks: evaluation.marks || 0,
                    ...(globalRubrics.length && {
                        globalRubricTotalPoints: globalAchievedPoints?.totalRubricsPoint,
                        globalRubricAwardedPoints: globalAchievedPoints?.awardedMarks,
                    }),
                    globalRubrics: globalRubrics.length ? globalRubrics : [],
                });
            }

            const globalRubricAwardedPoints = evaluators.reduce(
                (sum, e) => sum + (globalRubrics.length ? e.globalRubricAwardedPoints : 0),
                0,
            );

            return {
                updateOne: {
                    filter: { _id: response._id },
                    update: {
                        $set: {
                            awardedMarks: calculateStudentTotalMarks({
                                evaluators,
                                totalMarks: response.totalMarks,
                            }),
                            evaluators,
                            evaluationStatus: studentIds.length ? ON_GOING : COMPLETED,
                            ...(globalRubrics.length && {
                                globalRubricTotalPoints: globalAchievedPoints?.totalRubricsPoint,
                                globalRubricAwardedPoints:
                                    globalRubricAwardedPoints / evaluators.length,
                            }),
                        },
                    },
                },
            };
        })
        .filter(Boolean);

    return { bulkUpdates, errorMessage, students };
};

// update student marks
const updateStudentMarks = async ({
    programId,
    courseId,
    institutionCalendarId,
    componentId,
    childrenId,
    formId,
    scheduleId,
    studentIds,
    marks,
    userId,
    rubrics = [],
    globalRubrics = [],
}) => {
    const assignedEvaluator = await PortfolioAssignEvaluatorModel.findOne(
        {
            componentId,
            childrenId,
            'evaluators.userId': convertToMongoObjectId(userId),
            'role._id': { $exists: true },
        },
        { role: 1 },
    ).lean();

    if (!assignedEvaluator) {
        throw new BadRequestError('EVALUATOR_NOT_FOUND');
    }

    const evaluatedBy = await UserModel.findOne(
        { _id: userId },
        { name: 1, email: 1, employeeId: '$user_id', userId: '$_id' },
    ).lean();

    const {
        bulkUpdates: studentResponseBulkUpdates,
        errorMessage,
        students,
    } = await generateEvaluationBulkUpdatesForStudentResponse({
        componentId,
        childrenId,
        scheduleId,
        studentIds,
        evaluatedBy,
        marks,
        rubrics,
        globalRubrics,
        userId,
        role: assignedEvaluator?.role,
    });

    if (errorMessage) {
        return { message: errorMessage, students, isError: true };
    }

    await StudentResponseModel.bulkWrite(studentResponseBulkUpdates).catch(() => {
        throw new BadRequestError('ERROR_IN_UPDATING_MARKS');
    });

    return { message: 'MARKS_UPDATED_SUCCESSFULLY', isError: false };
};

// get components by evaluator
const getComponentsByEvaluator = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    rotation,
    rotationCount,
    userId,
}) => {
    const evaluations = await PortfolioAssignEvaluatorModel.find(
        {
            programId,
            courseId,
            institutionCalendarId,
            'evaluators.userId': userId,
        },
        {
            portfolioId: 1,
            componentId: 1,
            childrenId: 1,
            deliveryTypes: 1,
            role: 1,
            canApproveEvaluation: 1,
        },
    ).lean();

    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            status: PUBLISHED,
            term,
            year,
            level,
            ...(rotation && { rotation_no: rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
        },
        { components: 1, totalMarks: 1 },
    ).lean();

    if (!evaluations.some((evaluation) => isIDEquals(evaluation.portfolioId, portfolio?._id))) {
        return { components: [] };
    }

    const components = portfolio?.components?.flatMap((component) => {
        const matchedEvaluation = evaluations.find((evaluation) =>
            isIDEquals(evaluation.componentId, component._id),
        );

        if (!matchedEvaluation) return [];

        if (component.code === LB && component?.deliveryTypes?.length) {
            const hasMatchingDeliveryType = matchedEvaluation.deliveryTypes.some((deliveryType) =>
                component.deliveryTypes.some((deliveryTypeEntity) =>
                    isIDEquals(
                        deliveryTypeEntity.deliveryTypeSymbol,
                        deliveryType.deliveryTypeSymbol,
                    ),
                ),
            );

            if (!hasMatchingDeliveryType) return [];
        }

        const filteredChildren =
            component.children?.reduce((acc, child) => {
                const matchedEvaluations = evaluations.filter((evaluation) =>
                    isIDEquals(evaluation.childrenId, child._id),
                );

                if (matchedEvaluation) {
                    acc.push({
                        ...child,
                        canApproveEvaluation:
                            matchedEvaluations?.some(
                                (evaluation) => evaluation?.canApproveEvaluation,
                            ) || false,
                        canEvaluate: matchedEvaluations?.some(
                            (evaluation) => !!evaluation?.role?._id,
                        ),
                    });
                }

                return acc;
            }, []) || [];

        return {
            ...component,
            children: filteredChildren,
        };
    });

    return { components, totalMarks: portfolio?.totalMarks || 0, _id: portfolio?._id || '' };
};

// reject student submission
const rejectStudentSubmission = async ({
    componentId,
    childrenId,
    scheduleIds,
    studentIds,
    isApproved,
    userId,
    reason,
}) => {
    await StudentResponseModel.updateMany(
        {
            componentId: convertToMongoObjectId(componentId),
            childrenId: convertToMongoObjectId(childrenId),
            ...(scheduleIds.length && {
                scheduleId: {
                    $in: scheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId)),
                },
            }),
            ...(studentIds.length && {
                'student._id': {
                    $in: studentIds.map((studentId) => convertToMongoObjectId(studentId)),
                },
            }),
        },
        {
            $set: isApproved
                ? { approvalStatus: APPROVED, reason: reason || '' }
                : { evaluationStatus: REJECTED, reason: reason || '' },
            $push: {
                statusHistory: {
                    status: isApproved ? APPROVED : REJECTED,
                    reason: reason || '',
                    createdAt: new Date(),
                    userId: convertToMongoObjectId(userId),
                },
            },
        },
    );
};

const completeEvaluation = async ({ componentId, childrenId, scheduleId, studentIds }) => {
    await StudentResponseModel.updateMany(
        {
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
            ...(studentIds.length && { 'student._id': { $in: studentIds } }),
        },
        { $set: { evaluationStatus: COMPLETED } },
    );
};

const getRubricsOrMarksForEvaluation = async ({
    portfolioId,
    componentId,
    childrenId,
    formId,
    userId,
}) => {
    const form = await FormModel.findOne({ _id: formId }, { evaluations: 1 }).lean();

    const evaluator = await PortfolioAssignEvaluatorModel.findOne(
        { 'evaluators.userId': convertToMongoObjectId(userId), componentId, childrenId },
        { role: 1 },
    ).lean();
    if (!evaluator) {
        return { rubrics: [], marks: 0 };
    }

    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.hasGlobalRubric': 1,
            'components.children._id': 1,
            'components.children.rubrics': 1,
        },
    ).lean();

    const component = portfolio?.components?.find((component) =>
        isIDEquals(component._id, componentId),
    );

    const children = component?.children?.find((child) => isIDEquals(child._id, childrenId));

    const evaluation = form?.evaluations.find((e) => isIDEquals(e?.roleId, evaluator?.role?._id));

    return {
        rubrics: evaluation?.rubrics || [],
        marks: evaluation?.marks || 0,
        hasGlobalRubric: component?.hasGlobalRubric || false,
        globalRubrics: children?.rubrics || [],
    };
};

const getEvaluatorsForCourseAdmin = async ({ portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        { 'components._id': 1, 'components.children._id': 1, 'components.children.formId': 1 },
    ).lean();

    const component = portfolio?.components?.find((component) =>
        isIDEquals(component._id, componentId),
    );

    const children = component?.children?.find((child) => isIDEquals(child._id, childrenId));

    if (!children || !children.formId) return [];

    const form = await FormModel.findOne({ _id: children.formId }, { evaluations: 1 }).lean();

    const evaluators = await PortfolioAssignEvaluatorModel.find(
        {
            componentId,
            childrenId,
            'role._id': { $in: form.evaluations.map((e) => e.roleId) },
        },
        { role: 1, evaluators: 1 },
    ).lean();
    const evaluatorList = evaluators.flatMap((e) => e.evaluators);

    return evaluatorList;
};

const getPortfolioInsights = async ({ portfolioId, componentId, childrenId, studentId }) => {
    const portfolio = await PortfolioModel.findOne(
        {
            _id: portfolioId,
            components: {
                $elemMatch: {
                    _id: componentId,
                    code: LB,
                },
            },
        },
        {
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            term: 1,
            year: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
        },
    ).lean();

    if (!portfolio) return [];

    const courseSchedules = await CourseScheduleModel.find(
        {
            _institution_calendar_id: portfolio.institutionCalendarId,
            _program_id: portfolio.programId,
            _course_id: portfolio.courseId,
            year_no: portfolio.year,
            term: portfolio.term,
            level_no: portfolio.level,
            ...(portfolio.rotation && { rotation_no: portfolio.rotation }),
            ...(portfolio.rotationCount && { rotation_count: portfolio.rotationCount }),
            'students._id': convertToMongoObjectId(studentId),
            type: REGULAR,
            isDeleted: false,
            isActive: true,
        },
        {
            'students.$': 1,
        },
    ).lean();

    const studentResponses = await StudentResponseModel.find(
        {
            componentId,
            childrenId,
            'student._id': studentId,
        },
        { status: 1, statusHistory: 1, approvalStatus: 1, evaluationStatus: 1, scheduleId: 1 },
    ).lean();

    const count = {
        totalEntries: studentResponses.length,
        acceptedEntries: 0,
        reAcceptedEntries: 0,
        missingEntries: 0,
        rejectedEntries: 0,
        absent: 0,
        present: 0,
        onDuty: 0,
        leave: 0,
        permission: 0,
    };

    courseSchedules.forEach((schedule) => {
        const student = schedule.students.find((s) => isIDEquals(s._id, studentId));
        if (!student?.status) return;

        switch (student.status) {
            case ABSENT:
                count.absent += 1;
                break;
            case PRESENT:
                count.present += 1;
                break;
            case ON_DUTY:
                count.onDuty += 1;
                break;
            case LEAVE:
                count.leave += 1;
                break;
            case PERMISSION:
                count.permission += 1;
                break;
            default:
                break;
        }
    });

    studentResponses.forEach((response) => {
        if (response.evaluationStatus === COMPLETED) count.acceptedEntries += 1;
        const reAccepted = response.statusHistory.filter((h) => h.status === COMPLETED).length - 1;
        if (reAccepted > 0) count.reAcceptedEntries += reAccepted;
        if (response.status === NOT_STARTED) count.missingEntries += 1;
        if (response.evaluationStatus === REJECTED) count.rejectedEntries += 1;
    });

    return count;
};

const getEvaluationCount = async ({ componentId, childrenId, scheduleId, studentId }) => {
    const studentResponses = await StudentResponseModel.find(
        {
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
            ...(studentId && { 'student._id': studentId }),
        },
        {
            status: 1,
            statusHistory: 1,
            approvalStatus: 1,
            evaluationStatus: 1,
            scheduleId: 1,
        },
    ).lean();

    const count = {
        totalEntries: studentResponses.length,
        acceptedEntries: 0,
        reAcceptedEntries: 0,
        onGoingEntries: 0,
        pendingEntries: 0,
    };

    studentResponses.forEach((response) => {
        if (response.evaluationStatus === COMPLETED) count.acceptedEntries += 1;
        const reAccepted = response.statusHistory.filter((h) => h.status === COMPLETED).length - 1;
        if (reAccepted > 0) count.reAcceptedEntries += reAccepted;
        if (response.evaluationStatus === ON_GOING) count.onGoingEntries += 1;
        if (response.evaluationStatus === NOT_STARTED) count.pendingEntries += 1;
    });

    return count;
};

module.exports = {
    getEvaluatorListByCourse,
    createOrUpdateEvaluatorAssignment,
    getStudentForEvaluator,
    getStudentResponseForEvaluator,
    rejectStudentSubmission,
    updateStudentMarks,
    getComponentsByEvaluator,
    completeEvaluation,
    getRubricsOrMarksForEvaluation,
    getEvaluatorsForCourseAdmin,
    getPortfolioInsights,
    getEvaluationCount,
};
