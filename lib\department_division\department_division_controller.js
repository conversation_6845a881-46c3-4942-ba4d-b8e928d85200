let constant = require('../utility/constants');
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
var department = require('mongoose').model(constant.DEPARTMENT);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const department_division_formate = require('./department_division_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_department_id', foreignField: '_id', as: 'department' } },
        { $unwind: '$department' },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(department_division, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "department_division list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ department_division_formate.department_division(doc.data));
        // common_files.list_all_response(res, 200, true, "department_division list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* department_division_formate.department_division(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_department_id', foreignField: '_id', as: 'department' } },
        { $unwind: '$department' },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },

    ];
    let doc = await base_control.get_aggregate(department_division, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "department_division details", /* doc.data */department_division_formate.department_division_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let department_ids = [], title_name = [];
    let status, datas, docs;
    let doc_update = { status: true };
    await req.body.data.forEach(element => {
        if (element.id == '' && element.id.length == 0) {
            title_name.push(element.title);
        }
        if (department_ids.indexOf(element._department_id) == -1) {
            department_ids.push(element._department_id);
        }
    });
    let checks = { status: true };
    checks = await base_control.check_id(department, { _id: { $in: department_ids }, 'isDeleted': false });
    if (checks.status) {
        let query = { title: { $in: title_name }, _department_id: { $in: department_ids }, isDeleted: false };
        let docs1 = await base_control.get_list(department_division, query, {});
        console.log(docs1);
        if (!docs1.status) {
            await req.body.data.forEach(async (doc, index) => {
                let objects = {
                    title: doc.title,
                    _department_id: doc._department_id
                };
                if (doc.id == '' && doc.id.length == 0) {
                    docs = await base_control.insert(department_division, objects);
                    doc_update = await base_control.update_push_pull(department, doc._department_id, { $push: { _division_id: docs.responses._id } });
                    if (docs.status && doc_update.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                } else {
                    docs = await base_control.update(department_division, doc.id, objects);

                    if (docs.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                }
                if (req.body.data.length == index + 1) {
                    if (status) {
                        let aggre = [
                            { $match: { '_id': ObjectId(doc._department_id) } },
                            { $match: { 'isDeleted': false } },
                            { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
                            { $project: { 'division._id': 1, 'division.title': 1 } }
                        ];
                        let division_ids_get = await base_control.get_aggregate(department, aggre);

                        common_files.com_response(res, 201, true, "department_division Added successfully", division_ids_get);
                    } else {
                        common_files.com_response(res, 500, false, "Error", docs.data);
                    }
                }
            });
        } else {
            common_files.com_response(res, 500, false, "Error duplicate values found ", "This content already present in DB");
        }
    }
    else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID : ', checks);
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    //  Check if department_id is present we have unlink old department and link to new department 
    if (req.body._department_id != undefined) {
        checks = await base_control.check_id(department, { _id: { $in: req.body._department_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(department_division, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "department_division update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(object_id) } }
    ];
    let docs = await base_control.get_aggregate(department_division, aggre);
    await base_control.update_push_pull(department, docs.data[0]._department_id, { $pull: { _division_id: object_id } });

    let doc = await base_control.delete(department_division, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "department_division deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(department_division, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "department_division List", department_division_formate.department_division_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_department_division_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_department_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },

        // { $lookup: { from: constant.DEPARTMENT, localField: '_department_id', foreignField: '_id', as: 'department' } },
        // { $unwind: '$department' },
        // { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },

    ];
    let doc = await base_control.get_aggregate(department_division, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "department_division details", /* doc.data */department_division_formate.department_division_ID_Array_Only(doc.data));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_subject = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        // { $lookup: { from: constant.DEPARTMENT, localField: '_department_id', foreignField: '_id', as: 'department' } },
        // { $unwind: '$department' },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        { $project: { _id: 1, 'subject._id': 1, 'subject.title': 1 } }
    ];
    let doc = await base_control.get_aggregate(department_division, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "department_division details", /* doc.data */department_division_formate.department_division_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "department_division details", doc.data/* department_division_formate.department_division_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};