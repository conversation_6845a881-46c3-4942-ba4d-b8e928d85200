const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
// const program = require('mongoose').model(constant.PROGRAM);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
// const course = require('mongoose').model(constant.COURSE);
const course = require('mongoose').model(constant.DIGI_COURSE);
// const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
// const session_order = require('mongoose').model(constant.SESSION_ORDER);
// const session_type = require('mongoose').model(constant.SESSION_TYPE);
const session_type = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const institution = require('mongoose').model(constant.INSTITUTION);
const { updateStudentGroupFlatCacheData } = require('./student_group_services');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
exports.course_group_create = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_list = req.body.students.map((i) => i.academic_no);
        const user_checks = await base_control.get_list(
            user,
            { $or: [{ _id: req.body._user_id }, { user_id: student_list }], isDeleted: false },
            {
                _id: 1,
                user_id: 1,
                gender: 1,
                name: 1,
            },
        );
        const staff_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._user_id.toString(),
        );
        if (!user_checks.status && staff_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const users = [];
        const user_ids = [];
        const already_exist = [];
        const duplicate = [];
        const not_found = [];
        const imported = [];
        const male_user_ids = [];
        const female_user_ids = [];
        let objs;
        let obj;
        const course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const setting =
            student_group_check.data.groups[student_group_row].courses[course_loc].setting;
        let existing_student_ids = [];
        if (setting.length !== 0) {
            setting.forEach((element) => {
                existing_student_ids = existing_student_ids.concat(element.ungrouped);
                element.session_setting.forEach((sub_element) => {
                    sub_element.groups.forEach((delivery_element) => {
                        existing_student_ids = existing_student_ids.concat(
                            delivery_element._student_ids,
                        );
                    });
                });
            });
        }
        const course_status = [];
        const mark = [];
        const courses = student_group_check.data.groups[student_group_row].courses.map(
            (i) => i._course_id,
        );
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }

        await req.body.students.forEach((element) => {
            const pos = user_checks.data.findIndex((i) => i.user_id === element.academic_no); //Here we are checking in user data(users collection)
            if (pos !== -1) {
                //Here we are checking in student group
                const dub = existing_student_ids.findIndex(
                    (i) => i.toString() === user_checks.data[pos]._id.toString(),
                );
                if (dub === -1) {
                    //Here we are checking in user data(users collection)
                    if (user_ids.indexOf(user_checks.data[pos]._id) === -1) {
                        if (parseFloat(element.mark) < 0 || parseFloat(element.mark) > 100) {
                            mark.push(element);
                        } else {
                            if (user_checks.data[pos].gender === constant.GENDER.MALE)
                                male_user_ids.push(user_checks.data[pos]._id);
                            else female_user_ids.push(user_checks.data[pos]._id);
                            if (
                                student_group_check.data.groups[
                                    student_group_row
                                ].students.findIndex(
                                    (i) => i.academic_no === element.academic_no,
                                ) === -1
                            ) {
                                users.push({
                                    _student_id: user_checks.data[pos]._id,
                                    academic_no: user_checks.data[pos].user_id,
                                    name: user_checks.data[pos].name,
                                    gender: user_checks.data[pos].gender,
                                    mark: element.mark,
                                    imported_on: common_fun.timestampNow(),
                                    _imported_by: req.body._user_id,
                                    imported_by: user_checks.data[staff_loc].name,
                                    course_group_status: course_status,
                                });
                            }
                        }
                        imported.push(element);
                        user_ids.push(user_checks.data[pos]._id);
                    } else {
                        duplicate.push(element);
                    }
                } else {
                    already_exist.push(element);
                }
            } else {
                not_found.push(element);
            }
        });
        if (setting.length !== 0) {
            if (req.query.mode === constant.GENDER.BOTH) {
                objs = {
                    $push: {
                        'groups.$[i].courses.$[l].setting.$[j].ungrouped': [
                            ...male_user_ids,
                            ...female_user_ids,
                        ],
                        'groups.$[i].students': users,
                    },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'l._course_id': ObjectId(req.body._course_id) },
                        { 'j.gender': constant.GENDER.BOTH },
                    ],
                };
            } else {
                objs = {
                    $push: {
                        'groups.$[i].courses.$[l].setting.$[j].ungrouped': male_user_ids,
                        'groups.$[i].courses.$[l].setting.$[k].ungrouped': female_user_ids,
                        'groups.$[i].students': users,
                    },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'l._course_id': ObjectId(req.body._course_id) },
                        { 'j.gender': constant.GENDER.MALE },
                        { 'k.gender': constant.GENDER.FEMALE },
                    ],
                };
            }
            obj = await base_control.update_condition_array_filter(
                student_group,
                { _id: ObjectId(student_group_check.data._id) },
                objs,
                filter,
            );
        } else {
            if (req.query.mode === constant.GENDER.BOTH) {
                objs = {
                    $set: {
                        'groups.$[i].courses.$[k].setting': [
                            {
                                ungrouped: [...male_user_ids, ...female_user_ids],
                                gender: constant.GENDER.BOTH,
                            },
                        ],
                    },
                    $push: { 'groups.$[i].students': users },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'k._course_id': ObjectId(req.body._course_id) },
                    ],
                };
            } else {
                objs = {
                    $set: {
                        'groups.$[i].courses.$[k].setting': [
                            { ungrouped: male_user_ids, gender: constant.GENDER.MALE },
                            { ungrouped: female_user_ids, gender: constant.GENDER.FEMALE },
                        ],
                    },
                    $push: { 'groups.$[i].students': users },
                };
                filter = {
                    arrayFilters: [
                        {
                            'i.level': req.body.level,
                            'i.term': req.body.batch,
                        },
                        { 'k._course_id': ObjectId(req.body._course_id) },
                    ],
                };
            }
            obj = await base_control.update_condition_array_filter(
                student_group,
                { _id: ObjectId(student_group_check.data._id) },
                objs,
                filter,
            );
        }
        const response = {
            male_count: male_user_ids.length,
            female_count: female_user_ids.length,
            not_found,
            duplicate,
            already_exist,
            imported,
            mark,
        };
        // let doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        if (!obj.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_ADD_STUDENT'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SUCCESSFULLY_STUDENTS_ADDED'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'ERROR_CATCH', error.toString()));
    }
};

exports.individual_course_setting_get = async (req, res) => {
    try {
        const query = {
            _id: ObjectId(req.params.id),
            'master.term': req.params.batch,
            isDeleted: false,
        };
        const project = {};
        const doc = await base_control.get(student_group, query, project);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const ic_get = await base_control.get(
            institution_calendar,
            { _id: ObjectId(doc.data._institution_calendar_id) },
            { calendar_name: 1 },
        );
        const p_get = await base_control.get(
            program,
            { _id: ObjectId(doc.data.master._program_id) },
            { name: 1 },
        );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params.course) },
            { courses_name: 1, courses_number: 1 },
        );
        const course_session_order = await base_control.get_list(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params.course) },
            {},
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {
                /* _program_id: ObjectId(doc.data.master._program_id) */
            },
            {},
        );

        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        // const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];
        const group_name =
            ic_get.data.calendar_name +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P' +
            '-' +
            (req.params.batch ? 'RT' : 'IT') +
            '-' +
            doc.data.master.year +
            'Y' +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P:' +
            doc.data.master.curriculum +
            '-' +
            doc.data.master.level +
            'L-' +
            course_get.data.courses_number;

        const delivery = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        // symbol.forEach((element) => {
        //     const delivery_data =
        //         delivery_type.data[
        //             delivery_type.data.findIndex((i) => i.delivery_symbol === element)
        //         ];
        //     delivery.push({
        //         delivery_type: delivery_data.session_type,
        //         delivery_name: delivery_data.delivery_type,
        //         delivery_symbol: delivery_data.delivery_symbol,
        //     });
        // });
        // delivery.forEach((element) => {
        //     if (element.delivery_type === 'Theory')
        //         theory.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Practical')
        //         practical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Clinical')
        //         clinical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        // });
        // const session_dev_type = {
        //     Theory: theory,
        //     Practical: practical,
        //     Clinical: clinical,
        // };
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            session_names.push(delivery_data.session_name);
            delivery.push({
                session_type: delivery_data.session_name,
                delivery: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        const session_delivery = [];
        session_names = [...new Set(session_names)];
        session_names.forEach((element) => {
            const del = [];
            delivery.forEach((sub_element) => {
                if (sub_element.session_type === element)
                    del.push({
                        delivery_name: sub_element.delivery,
                        symbol: sub_element.symbol,
                    });
            });
            session_delivery.push({ session_type: element, delivery: del });
        });
        let male_count = 0;
        let female_count = 0;
        const objs = {
            _id: doc.data._id,
            master: doc.data.master,
            group_name,
            session_type: session_delivery,
            course: course_get.data,
            setting:
                doc.data.courses[
                    doc.data.courses.findIndex(
                        (i) => i._course_id.toString() === req.params.course.toString(),
                    )
                ].setting,
        };
        doc.data.courses[
            doc.data.courses.findIndex(
                (i) => i._course_id.toString() === req.params.course.toString(),
            )
        ].setting.forEach((element) => {
            element.ungrouped.forEach((sub_element) => {
                const std_data =
                    doc.data.students[
                        doc.data.students.findIndex(
                            (i) => i._student_id.toString() === sub_element.toString(),
                        )
                    ];
                if (std_data.gender === constant.GENDER.MALE) {
                    male_count++;
                } else {
                    female_count++;
                }
            });
        });
        Object.assign(objs, { male_count, female_count });
        // Object.assign(objs, { 'rotation_count': doc.data.rotation_count })
        common_files.com_response(res, 200, true, req.t(req.t('STUDENT_GROUP_SETTING_PAGE')), objs);
    } catch (error) {
        common_files.com_response(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.individual_course_group_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_COURSE'),
                    ),
                );

        let ind_group_setting = [];
        const groups_setting = [];
        let g_type = [];
        const student_groups =
            student_group_check.data.groups[student_group_row].courses[course_loc];
        req.body.groups.forEach((element) => {
            const name =
                student_group_check.data.groups[student_group_row].group_name +
                '-' +
                student_group_check.data.groups[student_group_row].courses[course_loc].course_no +
                '-G';
            let gs_obj = {};
            let student_ids = [];
            if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
                for (rot_data of student_group_check.data.groups[student_group_row]
                    .rotation_group_setting) {
                    if (rot_data.gender === element.gender) {
                        student_ids = student_ids.concat(rot_data._student_ids);
                    }
                }
                gs_obj = {
                    _group_no: element._group_no,
                    gender: element.gender,
                    ungrouped: student_ids,
                };
            } else {
                gs_obj = {
                    _group_no: element._group_no,
                    gender: element.gender,
                    ungrouped:
                        student_groups.setting[
                            student_groups.setting.findIndex((i) => i.gender === element.gender)
                        ] &&
                        student_groups.setting[
                            student_groups.setting.findIndex((i) => i.gender === element.gender)
                        ].ungrouped
                            ? student_groups.setting[
                                  student_groups.setting.findIndex(
                                      (i) => i.gender === element.gender,
                                  )
                              ].ungrouped
                            : [],
                };
            }
            g_type = [];
            element.delivery_type_group.forEach((sub_element) => {
                ind_group_setting = [];
                for (let i = 1; i <= sub_element.no_of_group; i++) {
                    const igs_obj = {
                        group_no: i,
                        group_name: name + '-' + sub_element.delivery + '-' + i.toString(),
                    };
                    ind_group_setting.push(igs_obj);
                }
                const ses_setting = {
                    group_name: name + '-' + sub_element.delivery,
                    delivery_type: sub_element.delivery_type,
                    session_type: sub_element.delivery,
                    no_of_group: sub_element.no_of_group,
                    no_of_student: sub_element.no_of_students,
                    groups: ind_group_setting,
                };
                g_type.push(ses_setting);
            });
            Object.assign(gs_obj, { session_setting: g_type });
            groups_setting.push(gs_obj);
        });
        objs = { $set: { 'groups.$[i].courses.$[j].setting': groups_setting } };
        filter = {
            arrayFilters: [
                {
                    'i.level': req.body.level,
                    'i.term': req.body.batch,
                },
                { 'j._course_id': req.body._course_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_SET_COURSE_GROUP_SETTING'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.individual_course_grouped_list_filter = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            'groups.level': req.params.level,
            'groups.term': req.params.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.params.course.toString(),
        );
        if (course_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_COURSE'),
                    ),
                );
        const objs = {
            _id: student_group_check.data._id,
            _institution_calendar_id: student_group_check.data._institution_calendar_id,
            group_name: student_group_check.data.group_name,
            master: student_group_check.data.master,
            course_excess_count:
                student_group_check.data.groups[student_group_row].course_excess_count,
        };
        let all_student_id = [];
        let gender_groups;
        const course_datas = student_group_check.data.groups[student_group_row].courses[course_loc];
        if (course_datas.setting) {
            course_datas.setting.forEach((element) => {
                if (
                    req.query.mode
                        ? req.query.mode === constant.GENDER.BOTH
                        : element.gender === req.params.gender
                ) {
                    gender_groups = [
                        {
                            gender: req.query.mode ? req.query.mode : element.gender,
                            ungrouped: [],
                            grouped: [],
                        },
                    ];
                    const session_setting = element.session_setting;
                    all_student_id = [];
                    session_setting.forEach((ses_element) => {
                        ses_element.groups.forEach((group_element) => {
                            all_student_id = all_student_id.concat(group_element._student_ids);
                        });
                    });
                    const temps = [];
                    all_student_id.forEach((sub_element) => {
                        if (temps.indexOf(sub_element.toString()) === -1) {
                            temps.push(sub_element.toString());
                        }
                    });
                    all_student_id = temps;
                    const session_types = session_setting.map((i) => i.session_type);
                    let student = [];
                    element.ungrouped.forEach((ungroup_element) => {
                        const temp =
                            student_group_check.data.groups[student_group_row].students[
                                student_group_check.data.groups[
                                    student_group_row
                                ].students.findIndex(
                                    (i) => i._student_id.toString() === ungroup_element.toString(),
                                )
                            ];
                        if (temp) {
                            const data = {};
                            session_types.forEach((session_element) => {
                                data[session_element.toString()] = '-';
                            });
                            data._student_id = temp._student_id;
                            data.name = temp.name;
                            data.academic_no = temp.academic_no;
                            data.gender = temp.gender;
                            data.mark = temp.mark;
                            data.imported_on = temp.imported_on;
                            data.imported_by = temp.imported_by;
                            student.push(data);
                        }
                    });
                    const group_ind = gender_groups.findIndex((i) => i.gender === element.gender);
                    gender_groups[group_ind].ungrouped = student;
                    student = [];
                    all_student_id = all_student_id.filter(
                        (item) => !element.ungrouped.includes(item.toString()),
                    );
                    all_student_id.forEach((master_group_element) => {
                        const temp =
                            student_group_check.data.groups[student_group_row].students[
                                student_group_check.data.groups[
                                    student_group_row
                                ].students.findIndex(
                                    (i) =>
                                        i._student_id.toString() ===
                                        master_group_element.toString(),
                                )
                            ];
                        if (temp) {
                            const data = {};
                            let course_session_index = -1;
                            session_setting.forEach((session_element) => {
                                session_element.groups.forEach((group_element) => {
                                    if (
                                        group_element._student_ids.findIndex(
                                            (i) => i.toString() === master_group_element.toString(),
                                        ) !== -1
                                    ) {
                                        course_session_index = group_element.group_no;
                                    }
                                });
                                data[session_element.session_type.toString()] =
                                    course_session_index !== -1 ? course_session_index : '-';
                            });
                            data._student_id = temp._student_id;
                            data.name = temp.name;
                            data.academic_no = temp.academic_no;
                            data.gender = temp.gender;
                            data.mark = temp.mark;
                            data.imported_on = temp.imported_on;
                            data.imported_by = temp.imported_by;
                            student.push(data);
                        }
                    });
                    gender_groups[group_ind].grouped = student;
                }
            });
        }
        Object.assign(objs, {
            course: {
                _course_id: course_datas._course_id,
                course_name: course_datas.course_name,
                course_no: course_datas.course_no,
                versionNo: course_datas.versionNo || 1,
                versioned: course_datas.versioned || false,
                versionName: course_datas.versionName || '',
                versionedFrom: course_datas.versionedFrom || null,
            },
        });
        Object.assign(objs, { groups: gender_groups });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('COURSE_STUDENT_GROUP_DATA'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.individual_courses_list = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            'groups.level': req.params.level,
            'groups.term': req.params.batch,
            isDeleted: false,
        };
        // const student_group_check = await base_control.get(student_group, query, {});
        const student_group_check = await student_group
            .findOne(query)
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        if (!student_group_check)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const objs = {
            _id: student_group_check._id,
            _institution_calendar_id: student_group_check._institution_calendar_id,
            group_name: student_group_check.group_name,
            master: student_group_check.master,
            course_excess_count: student_group_check.groups[student_group_row].course_excess_count,
        };
        let all_student_id = [];
        let gender_groups;
        const courses = [];
        for (course_element of student_group_check.groups[student_group_row].courses) {
            const course_loc = student_group_check.groups[student_group_row].courses.findIndex(
                (i) => i._course_id._id.toString() === course_element._course_id._id.toString(),
            );
            const course_datas = student_group_check.groups[student_group_row].courses[course_loc];
            gender_groups = [
                { gender: req.query.mode ? req.query.mode : req.params.gender, grouped: [] },
            ];
            if (course_datas.setting) {
                course_datas.setting.forEach((element) => {
                    if (
                        req.query.mode
                            ? req.query.mode === element.gender
                            : element.gender === req.params.gender
                    ) {
                        const session_setting = element.session_setting;
                        all_student_id = [];
                        session_setting.forEach((ses_element) => {
                            ses_element.groups.forEach((group_element) => {
                                all_student_id = all_student_id.concat(group_element._student_ids);
                            });
                        });
                        const temps = [];
                        all_student_id.forEach((sub_element) => {
                            if (temps.indexOf(sub_element.toString()) === -1) {
                                temps.push(sub_element.toString());
                            }
                        });
                        all_student_id = temps;
                        const student = [];
                        const group_ind = gender_groups.findIndex(
                            (i) => i.gender === element.gender,
                        );
                        all_student_id = all_student_id.filter(
                            (item) => !element.ungrouped.includes(item.toString()),
                        );
                        all_student_id.forEach((master_group_element) => {
                            const temp =
                                student_group_check.groups[student_group_row].students[
                                    student_group_check.groups[
                                        student_group_row
                                    ].students.findIndex(
                                        (i) =>
                                            i._student_id.toString() ===
                                            master_group_element.toString(),
                                    )
                                ];
                            if (temp) {
                                const data = {};
                                let course_session_index = -1;
                                session_setting.forEach((session_element) => {
                                    session_element.groups.forEach((group_element) => {
                                        if (
                                            group_element._student_ids.findIndex(
                                                (i) =>
                                                    i.toString() ===
                                                    master_group_element.toString(),
                                            ) !== -1
                                        ) {
                                            course_session_index = group_element.group_no;
                                        }
                                    });
                                    data[session_element.session_type.toString()] =
                                        course_session_index !== -1 ? course_session_index : '-';
                                });
                                data._student_id = temp._student_id;
                                data.name = temp.name;
                                data.academic_no = temp.academic_no;
                                data.gender = temp.gender;
                                data.mark = temp.mark;
                                data.imported_on = temp.imported_on;
                                data.imported_by = temp.imported_by;
                                student.push(data);
                            }
                        });
                        gender_groups[group_ind].grouped = student;
                    }
                });
            }
            courses.push({
                _course_id: course_datas._course_id?._id,
                versionedCourseIds: course_datas._course_id?.versionedCourseIds || [],
                course_name: course_datas.course_name,
                course_no: course_datas.course_no,
                versionNo: course_datas._course_id?.versionNo || 1,
                versioned: course_datas._course_id?.versioned || false,
                versionName: course_datas._course_id?.versionName || '',
                versionedFrom: course_datas._course_id?.versionedFrom || null,
                groups: gender_groups,
            });
        }
        Object.assign(objs, { courses });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('COURSE_STUDENT_GROUP_DATA'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.individual_course_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc = { status: true, data: [] };
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const master_group_ind = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.body.master_group.toString(),
        );
        const course_session_setting =
            student_group_data.data.courses[course_ind].setting[master_group_ind].session_setting;

        req.body.delivery_group.forEach(async (element, index) => {
            const session =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session.groups.forEach((sub_element) => {
                if (sub_element._student_ids.findIndex((i) => i === req.body._student_id) !== -1) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': {
                            $in: req.body._student_id,
                        },
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        { 'j.gender': req.body.gender },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j.gender': req.body.gender },
                    { 'k.session_type': element.session_type },
                    { 'l.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (doc.status) {
                    updateStudentGroupFlatCacheData();
                    await updateStudentGroupRedisKey({
                        courseId: req.body._course_id,
                        level: req.body.level,
                        batch: req.body.batch,
                    });
                    common_files.com_response(
                        res,
                        200,
                        true,
                        req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                        doc.data,
                    );
                } else {
                    common_files.com_response(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.individual_course_grouping_data = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const group_list = [];
        const course_ind = doc.data.courses.findIndex(
            (i) => i._course_id.toString() === req.params.course.toString(),
        );
        const master_group_ind = doc.data.courses[course_ind].setting.findIndex(
            (i) => i.gender === req.params.gender,
        );
        const course_session_setting =
            doc.data.courses[course_ind].setting[master_group_ind].session_setting;
        course_session_setting.forEach((element) => {
            const session = [];
            let obj = {};
            element.groups.forEach((sub_element) => {
                const split = sub_element.group_name.split('-');
                obj = {
                    group_no: sub_element.group_no,
                    group_name: sub_element.group_name,
                    group: split[split.length - 2] + '-' + split[split.length - 1],
                    total: element.no_of_student,
                    occupied: sub_element._student_ids.length,
                };
                session.push(obj);
            });
            group_list.push({
                group_name: element.group_name,
                session_type: element.session_type,
                group: session,
            });
        });
        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_SETTING_PAGE'), group_list);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.individual_course_list = async (req, res) => {
    const program_check = await base_control.get_list(program, { isDeleted: false }, {});
    if (program_check.status) {
        const program_cri = [];
        const program_ids = [];
        let program_no = null;
        program_check.data.forEach((element) => {
            if (element._id === req.params.id) {
                program_no = element.no;
            }
        });
        program_check.data.forEach((element) => {
            if (program_no === element.no) {
                program_ids.push(element._id);
                const ver = element.name.substring(element.name.length - 3, element.name.length);
                program_cri.push({
                    _id: element._id,
                    name: element.name,
                    no: element.no,
                    version: ver,
                });
            }
        });
        const course_list = await base_control.get_list(
            course,
            {
                _program_id: ObjectId(
                    program_cri[program_cri.findIndex((i) => i.version === req.params.curriculum)]
                        ._id,
                ),
                study_level: req.params.level,
                isDeleted: false,
            },
            { courses_name: 1, model: 1 },
        );
        common_files.com_response(
            res,
            200,
            true,
            req.t('STUDENT_GROUP_SETTING_PAGE'),
            course_list.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.elective_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc = { status: true, data: [] };
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const master_group_ind = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i.gender === req.body.gender,
        );
        const course_session_setting =
            student_group_data.data.courses[course_ind].setting[master_group_ind].session_setting;

        course_session_setting.forEach(async (element, index) => {
            objs = {
                $pull: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': {
                        $in: req.body._student_id,
                    },
                },
                $push: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[m]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j.gender': req.body.gender },
                    { 'k.session_type': element.session_type },
                    { 'l.group_no': req.body.old_group_no },
                    { 'm.group_no': req.body.new_group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (course_session_setting.length === index + 1) {
                if (doc.status) {
                    updateStudentGroupFlatCacheData();
                    await updateStudentGroupRedisKey({
                        courseId: req.body._course_id,
                        level: req.body.level,
                        batch: req.body.batch,
                    });
                    common_files.com_response(
                        res,
                        200,
                        true,
                        req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                        doc.data,
                    );
                } else {
                    common_files.com_response(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.individual_student_list_dashboard = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const obj = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_level === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.params._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        const courses_data =
            student_group_data.data.groups[student_group_level].courses[student_group_course];
        Object.assign(obj, {
            course: {
                _course_id: courses_data._course_id,
                course_name: courses_data.course_name,
                course_no: courses_data.course_no,
            },
        });
        let male_student_id = [];
        let female_student_id = [];
        if (courses_data.setting) {
            const male_set = courses_data.setting.filter((i) => i.gender === constant.GENDER.MALE);
            const female_set = courses_data.setting.filter(
                (i) => i.gender === constant.GENDER.FEMALE,
            );
            male_set.forEach((elements) => {
                const male_session_setting = elements.session_setting;
                male_session_setting[0].groups.forEach((group_element) => {
                    male_student_id = male_student_id.concat(group_element._student_ids);
                });
            });
            female_set.forEach((elements) => {
                const female_session_setting = elements.session_setting;
                female_session_setting[0].groups.forEach((group_element) => {
                    female_student_id = female_student_id.concat(group_element._student_ids);
                });
            });
        }
        const male_student = [];
        const female_student = [];
        male_student_id.forEach((std_element) => {
            const temp =
                student_group_data.data.groups[student_group_level].students[
                    student_group_data.data.groups[student_group_level].students.findIndex(
                        (i) => i._student_id.toString() === std_element.toString(),
                    )
                ];
            if (temp) {
                const data = {};
                data._student_id = temp._student_id;
                data.name = temp.name;
                data.academic_no = temp.academic_no;
                data.gender = temp.gender;
                male_student.push(data);
            }
        });
        female_student_id.forEach((std_element) => {
            const temp =
                student_group_data.data.groups[student_group_level].students[
                    student_group_data.data.groups[student_group_level].students.findIndex(
                        (i) => i._student_id.toString() === std_element.toString(),
                    )
                ];
            if (temp) {
                const data = {};
                data._student_id = temp._student_id;
                data.name = temp.name;
                data.academic_no = temp.academic_no;
                data.gender = temp.gender;
                female_student.push(data);
            }
        });
        const male_sort = male_student.sort(function (a, b) {
            return b.academic_no - a.academic_no;
        });
        const female_sort = female_student.sort(function (a, b) {
            return b.academic_no - a.academic_no;
        });
        Object.assign(obj, { male_students: male_sort });
        Object.assign(obj, { female_students: female_sort });
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('COURSE_STUDENT_LIST'), obj),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
