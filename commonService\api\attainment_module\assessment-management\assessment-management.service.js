const { convertToMongoObjectId, query } = require('../../../utility/common');
const {
    LOCAL,
    DIGI_PROGRAM,
    ROLE_ASSIGNS,
    DIGI_COURSE,
    COURSE_SCHEDULE,
    COURSE_COORDINATOR,
} = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const {
    programFormatting,
    userProgramIdFormatting,
} = require('../../serviceAdapter/adapter.formatter');
const programSchema = require('mongoose').model(DIGI_PROGRAM);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGNS);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);

const getProgramList = async ({ _institution_id, userRoleProgram }) => {
    try {
        let programList;
        if (BASIC_DATA_FROM === LOCAL) {
            programList = await programSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: { $in: userRoleProgram },
                    },
                    { name: 1, code: 1, type: 1, degree: 1, program_type: 1 },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        return programFormatting({ programList, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserRoleProgramList = async ({
    _institution_id,
    user_id,
    role_id,
    institutionCalendarId,
}) => {
    try {
        let roleAssignData;
        let courseCoordinatorData;
        let courseScheduleData;
        let isCourseAdmin = false;
        let isProgramAdmin = false;
        if (BASIC_DATA_FROM === LOCAL) {
            roleAssignData = await roleAssignSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _user_id: convertToMongoObjectId(user_id),
                    },
                    {
                        'roles._id': 1,
                        'roles._role_id': 1,
                        'roles.role_name': 1,
                        'roles.isAdmin': 1,
                        'roles.program._program_id': 1,
                    },
                )
                .lean();
            if (roleAssignData && roleAssignData.roles) {
                const userRoleData = roleAssignData.roles.find(
                    (roleElement) => roleElement._role_id.toString() === role_id,
                );
                isProgramAdmin = userRoleData && userRoleData.isAdmin;
                isCourseAdmin = userRoleData && userRoleData.role_name === COURSE_COORDINATOR;
            }
            if (isCourseAdmin) {
                courseCoordinatorData = await courseSchema
                    .find(
                        {
                            ...query,
                            _institution_id: convertToMongoObjectId(_institution_id),
                            'coordinators._institution_calendar_id':
                                convertToMongoObjectId(institutionCalendarId),
                            'coordinators._user_id': convertToMongoObjectId(user_id),
                        },
                        {
                            _program_id: 1,
                        },
                    )
                    .lean();
                roleAssignData = undefined;
            }
            if (!isCourseAdmin && !isProgramAdmin)
                courseScheduleData = await courseScheduleSchema
                    .distinct('_program_id', {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        type: 'regular',
                        'staffs._staff_id': convertToMongoObjectId(user_id),
                    })
                    .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        return userProgramIdFormatting({
            roleAssignData,
            role_id,
            courseCoordinatorData,
            courseScheduleData,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getProgramList,
    getUserRoleProgramList,
};
