const RubricService = require('./rubric.service');

const createRubric = async ({
    body: {
        name = '',
        desc = '',
        scoring = '',
        parameters = [],
        isTemplate = false,
        isGlobal = false,
    },
    headers: { user_id: userId } = {},
}) => {
    const rubric = await RubricService.createRubric({
        name,
        desc,
        scoring,
        parameters,
        isTemplate,
        isGlobal,
        userId,
    });

    return { statusCode: 200, message: 'CREATED_SUCCESSFULLY', data: { _id: rubric._id } };
};

const getRubrics = async ({ query: { rubricId, isTemplate, isGlobal } }) => {
    const rubrics = await RubricService.getRubrics({ rubricId, isTemplate, isGlobal });

    return { statusCode: 200, message: 'SUCCESS', data: rubrics };
};

const updateRubric = async ({
    query: { rubricId },
    body: {
        name = '',
        desc = '',
        scoring = '',
        parameters = [],
        createdBy = {},
        isTemplate,
        isGlobal,
    },
}) => {
    await RubricService.updateRubric({
        rubricId,
        name,
        desc,
        scoring,
        parameters,
        createdBy,
        isTemplate,
        isGlobal,
    });

    return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
};

const deleteRubric = async ({ query: { rubricId } }) => {
    await RubricService.deleteRubric({ rubricId });

    return { statusCode: 200, message: 'DELETED_SUCCESSFULLY' };
};

const createGlobalRubric = async ({
    body: { name = '', desc = '', scoring = '', parameters = [] },
    headers: { user_id: userId } = {},
}) => {
    const rubric = await RubricService.createGlobalRubric({
        name,
        desc,
        scoring,
        parameters,
        userId,
    });

    return { statusCode: 200, message: 'CREATED_SUCCESSFULLY', data: { _id: rubric._id } };
};

module.exports = { createRubric, getRubrics, updateRubric, deleteRubric, createGlobalRubric };
