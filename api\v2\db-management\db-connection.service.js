const { ADMIN_DB_URI, ENABLE_SAAS, MONGO_DB_V2 } = require('../utility/util_keys');
const { CLIENT_URI } = require('../utility/constants');
const { initAdminDbConnection } = require('./admin/admin-db.service');
const { initTenantDbConnection } = require('./tenant/tenant-db.service');
const { getAllTenants } = require('./tenant/tenant.util');
const { redisClient } = require('../../../config/redis-connection');

let adminDbConnection;
let connectionMap = {};

const subscribeNewTenant = () => {
    if (redisClient && redisClient.Subscriber) {
        redisClient.Subscriber.on('message', (channel, tenant) => {
            const { tenantSubdomain, tenantURI } = JSON.parse(tenant);
            if (!connectionMap[[tenantSubdomain]]) {
                connectionMap[[tenantSubdomain]] = initTenantDbConnection(tenantURI);
            }
        });
        redisClient.Subscriber.subscribe('new-tenant');
    }
};

const publishnewTenant = (tenant) => {
    redisClient.Client.publish('new-tenant', tenant);
};

const connectAllTenants = async (adminDBURI) => {
    let tenants = [];
    try {
        tenants = await getAllTenants(adminDBURI);
    } catch (e) {
        return;
    }
    if (tenants.length) {
        connectionMap = tenants
            .map((tenant) => {
                return {
                    [tenant.subdomain]: initTenantDbConnection(tenant.dbURI),
                };
            })
            .reduce((prev, next) => {
                return { ...prev, ...next };
            }, {});
    }
    connectionMap.admin = adminDBURI;
    // console.log('connectAllDb connectionMap', connectionMap);
};

const connectAllDb = async () => {
    if (ENABLE_SAAS == 'true') {
        adminDbConnection = await initAdminDbConnection(ADMIN_DB_URI);
        await connectAllTenants(adminDbConnection);
    } else {
        const tenantURI = await initTenantDbConnection(MONGO_DB_V2);
        connectionMap[[CLIENT_URI]] = tenantURI;
    }
};

const connectNewTenant = async (tenantSubdomain, tenantURI) => {
    try {
        const tenant = initTenantDbConnection(tenantURI);
        connectionMap[[tenantSubdomain]] = tenant;
        publishnewTenant(JSON.stringify({ tenantSubdomain, tenantURI }));
    } catch (error) {
        console.log('error connecting new tenant', tenantURI);
    }
};

const getConnectionByTenant = (tenantSubdomain) => {
    if (connectionMap) {
        return connectionMap[tenantSubdomain];
    }
};

const getAdminConnection = () => {
    if (adminDbConnection) {
        console.log('Getting adminDbConnection');
        return adminDbConnection;
    }
};

module.exports = {
    connectAllDb,
    getAdminConnection,
    getConnectionByTenant,
    connectAllTenants,
    connectNewTenant,
    subscribeNewTenant,
};
