const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const infrastructure = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        location: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            enum: [constant.BUILDING_TYPE.COLLEGE, constant.BUILDING_TYPE.HOSPITAL],
            required: true,
        },
        floors: [
            {
                floor_name: {
                    type: String,
                    required: true,
                },
                category: {
                    type: String,
                    enum: [constant.FLOOR_CATEGORY.LEVEL, constant.FLOOR_CATEGORY.BASEMENT],
                    required: true,
                },
            },
        ],
        zones: [
            {
                type: String,
                required: true,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        outsideCampus: { type: Boolean, default: false },
        radius: { type: Number },
        latitude: { type: String },
        longitude: { type: String },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.INFRASTRUCTURE, infrastructure);
