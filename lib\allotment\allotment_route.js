const express = require('express');
const route = express.Router();
const allotment = require('./allotment_controller');
const validater = require('./allotment_validator');
// route.get('/complete/:id', validater.allotment_id, allotment.allotment_complete);
route.post('/list', allotment.list_values);
route.get('/:id', validater.allotment_id, allotment.list_id);
route.get('/', allotment.list);
route.post('/', validater.allotment, allotment.insert);
// route.put('/:id', validater.allotment_id, validater.allotment, allotment.update);
// route.delete('/:id', validater.allotment_id, allotment.delete);
route.get('/program/:id', validater.allotment_id, allotment.list_program);
// route.get('/subject/:id', validater.allotment_id, allotment.list_subjects);
// route.get('/program/:id/:level', validater.allotment_id_level, allotment.list_allotment_program_level);
route.get('/program/:id/:year_level/:input', validater.allotment_year_level, allotment.list_course_year_level);
// route.get('/department/program/:id', validater.allotment_id, allotment.list_program);
route.get('/department_list_added/program/:id', validater.allotment_id, allotment.department_list_added);
module.exports = route;