const constant = require('../utility/constants');
const digi_college = require('mongoose').model(constant.DIGI_COLLEGE);
const digi_university = require('mongoose').model(constant.DIGI_UNIVERSITY);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = require('mongodb').ObjectID;

async function insert(req, res) {
    try {
        let query = { isDeleted: false };
        let uni_check = await base_control.get(digi_university, query, {});
        if (uni_check.status) {
            let query = { university_id: ObjectId(uni_check.data._id), 'isDeleted': false };
            let ins = await base_control.get_list(digi_college, query, {});
            if (ins.status) {
                // console.log(uni_check.data.no_of_college, ' ', ins.data.length)
                // console.log(uni_check.data.no_of_college < ins.data.length)
                if (uni_check.data.no_of_college <= ins.data.length) return res.status(410).send(common_files.response_function(res, 410, false, "Institution Count is Exceeding", 'Institution Count is Exceeding'));
            }
        }
        let obj = {
            university_id: req.body.university_id,
            university_name: req.body.university_name,
            college_name: req.body.college_name,
            logo: req.body.logo,
            address_details: {
                address: req.body.address_details.address,
                country: req.body.address_details.country,
                state: req.body.address_details.state,
                district: req.body.address_details.district,
                city: req.body.address_details.city,
                zipcode: req.body.address_details.zipcode,
            }
        };
        let doc = await base_control.insert(digi_college, obj);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, "College added successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, "Unable to add College", []));
    }
    catch (error) {
        console.log(error)
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
async function update(req, res) {
    try {

        /* let institution_check = await base_control.get(institution, { _id: req.headers._institution_id, 'isDeleted': false });
        if (!institution_check.status) return res.status(410).send(common_files.response_function(res, 500, false, "Institution not found", 'Institution not found')); */
        // let obj = {
        //     college_name: req.body.college_name,
        //     // logo: req.body.logo,
        //     address_details: {
        //         address: req.body.address_details.address,
        //         country: req.body.address_details.country,
        //         state: req.body.address_details.state,
        //         district: req.body.address_details.district,
        //         city: req.body.address_details.city,
        //         zipcode: req.body.address_details.zipcode,
        //     }
        // };
        // if (req.body.logo) obj[logo] = req.body.logo;
        let query = { _id: ObjectId(req.params.id) }
        let doc = await base_control.update(digi_college, query, req.body);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, "College updated successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, "Unable to update college", []));
    }
    catch (error) {
        console.log(error)
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
async function list_id(req, res) {
    try {
        let query = { _id: ObjectId(req.params.id) };
        let doc = await base_control.get(digi_college, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        return res.status(200).send(common_files.response_function(res, 200, true, "College Details", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
}
async function list(req, res) {
    try {
        let query = { university_id: ObjectId(req.params.university_id), isActive: true };
        let doc = await base_control.get_list(digi_college, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        return res.status(200).send(common_files.response_function(res, 200, true, "College List", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
}
async function archived_college_list(req, res) {
    try {
        let query = { university_id: ObjectId(req.params.university_id), isActive: false };
        let doc = await base_control.get_list(digi_college, query, {});
        if (!doc.status) return res.status(200).send(common_files.response_function(res, 200, false, "Error", []));
        return res.status(200).send(common_files.response_function(res, 200, true, "Archived College List", doc.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
}
async function delete_college(req, res) {
    try {

        /* let institution_check = await base_control.get(institution, { _id: req.headers._institution_id, 'isDeleted': false });
        if (!institution_check.status) return res.status(410).send(common_files.response_function(res, 500, false, "Institution not found", 'Institution not found')); */
        let obj = { isDeleted: true };
        let query = { _id: ObjectId(req.params.id) }
        let doc = await base_control.update(digi_college, query, obj);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, "College deleted successfully", doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, "Unable to deleted college", []));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};
async function archive_college(req, res) {
    try {

        /* let institution_check = await base_control.get(institution, { _id: req.headers._institution_id, 'isDeleted': false });
        if (!institution_check.status) return res.status(410).send(common_files.response_function(res, 500, false, "Institution not found", 'Institution not found')); */
        let obj = { isActive: req.body.isActive };
        let query = { _id: ObjectId(req.params.id) }
        let success_message = "";
        let error_message = "";
        if (req.body.isActive == false) {
            success_message = "College archived successfully"
            error_message = "Unable to archive college";
        }
        else {
            success_message = "College unarchive successfully"
            error_message = "Unable to unarchive college";
        }

        let doc = await base_control.update(digi_college, query, obj);
        if (doc.status) return res.status(201).send(common_files.response_function(res, 201, true, success_message, doc.data));
        return res.status(410).send(common_files.response_function(res, 200, false, error_message, []));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};


module.exports = {
    insert,
    update,
    list_id,
    list,
    delete_college,
    archive_college,
    archived_college_list
}