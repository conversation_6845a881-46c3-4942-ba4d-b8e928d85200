const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.course_management_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('PROGRAM_ID_REQUIRED');
                        }),
                    session_type: Joi.string()
                        .valid(
                            constant.COURSE_MANAGEMENT_SESSION_TYPE.EXTRA_CURRICULAR,
                            constant.COURSE_MANAGEMENT_SESSION_TYPE.BREAK,
                        )
                        .required()
                        .error((error) => {
                            return req.t('SESSION_TYPE_MUST_BE_BREAK_OR_EXTRA_CURRICULAR');
                        }),
                    title: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('TITLE_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE, constant.GENDER.BOTH)
                        .required()
                        .error((error) => {
                            return req.t('MUST_BE_MALE_FEMALE_BOTH');
                        }),
                    start_time: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('START_TIME_REQUIRED');
                        }),
                    end_time: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('END_TIME_REQUIRED');
                        }),
                    mode: Joi.string()
                        .valid(constant.DAYS_MODE.WEEKLY, constant.DAYS_MODE.DAILY)
                        .required()
                        .error((error) => {
                            return req.t('MODE_MUST_BE_WEEKLY_OR_DAILY');
                        }),
                    selected_days: Joi.array().when('mode', {
                        is: constant.DAYS_MODE.WEEKLY,
                        then: Joi.array()
                            .items(
                                Joi.string()
                                    .valid(
                                        constant.DAYS.SUNDAY,
                                        constant.DAYS.MONDAY,
                                        constant.DAYS.TUESDAY,
                                        constant.DAYS.WEDNESDAY,
                                        constant.DAYS.THURSDAY,
                                        constant.DAYS.FRIDAY,
                                        constant.DAYS.SATURDAY,
                                    )
                                    .required(),
                            )
                            .required()
                            .error((error) => {
                                return req.t('SELECTED_DAYS');
                            }),
                    }),
                    start_date: Joi.date().error((error) => {
                        return req.t('START_DATE_REQUIRED');
                    }),
                    end_date: Joi.date().error((error) => {
                        return req.t('END_DATE_REQUIRED');
                    }),
                    /* _user_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }) */
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.course_management_setting_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.program_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('PROGRAM_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.course_management_setting_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
