const router = require('express').Router();
const { courseDetails } = require('../courseReport/courseReport.controller');
const catchAsync = require('../utility/catch-async');
const { daInputDataMigrateDCInputs } = require('../utility/externalSync.service');
const {
    getInstitutionCalendars,
    studentGroupingList,
    individualStudentGroupNameList,
    encryptDecryptData,
} = require('./dcExternalSync.controller');
const { studentGroupingValidation } = require('./dcExternalSync.validator');
router.get('/getInstitutionCalendars', catchAsync(getInstitutionCalendars));
router.post(
    '/studentGroupingList',
    daInputDataMigrateDCInputs,
    studentGroupingValidation,
    catchAsync(studentGroupingList),
);
router.post(
    '/individualStudentGroupNameList',
    daInputDataMigrateDCInputs,
    studentGroupingValidation,
    catchAsync(individualStudentGroupNameList),
);

router.post(
    '/courseReportDetails',
    daInputDataMigrateDCInputs,
    studentGroupingValidation,
    catchAsync(courseDetails),
);

router.post('/encryptDecryptData', catchAsync(encryptDecryptData));

module.exports = router;
