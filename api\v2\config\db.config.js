/*
 *  Module dependencies
 */

const { createConnection, connection, disconnect } = require('mongoose');
const { MONGO_DB_V2, logger } = require('../utility/util_keys');

let connectionString;

const options = {
    useFindAndModify: false,
    useNewUrlParser: true,
    useCreateIndex: true,
    useUnifiedTopology: true,
};

connection.on('connected', () => {
    logger.info('Mongoose connection established.');
});

connection.on('error', (err) => {
    logger.error({ err }, 'Mongoose connection error:');
});

connection.on('disconnected', () => {
    logger.warn('Mongoose disconnected.');
});

connection.on('reconnected', () => {
    logger.warn('Mongoose reconnected.');
});

connection.on('close', () => {
    logger.info('Mongoose connection closed.');
});

const doConnect = () => {
    logger.info('doConnect mongodb.');
    connectionString = createConnection(MONGO_DB_V2, options);
    return connectionString;
};

const doDisconnect = () => {
    logger.warn('doDisconnect mongodb.');
    return connectionString.disconnect();
};

const getConnectionString = (schemaName, modelName) => {
    if (!connectionString) {
        connectionString = doConnect();
    }

    return connectionString.model(schemaName, modelName);
};

module.exports = {
    doConnect,
    getConnectionString,
    doDisconnect,
};
