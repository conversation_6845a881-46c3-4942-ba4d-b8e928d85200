const programCalendarModel = require('../../../lib/models/program_calendar');
const programModel = require('../../../lib/models/digi_programs');
const { convertToMongoObjectId } = require('../../common/utils/common.util');
const calendarProgramList = async ({ institutionCalendarId }) => {
    const calendarProgramIds = await programCalendarModel
        .find(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            },
            { _program_id: 1 },
        )
        .lean();
    const programIds = calendarProgramIds.flatMap(({ _program_id }) => _program_id);
    const programDetails = await programModel
        .find(
            {
                _id: { $in: programIds },
            },
            { name: 1, code: 1 },
        )
        .lean();

    return programDetails;
};

const formatProgramCourses = (programCalendarData) => {
    if (!programCalendarData || !programCalendarData.level) {
        return [];
    }

    const formattedCourses = [];

    programCalendarData.level.forEach((level) => {
        // Process normal courses
        level?.course?.forEach((course) => {
            formattedCourses.push({
                term: level.term,
                year: level.year,
                levelNo: level.level_no,
                courseType: 'normal',
                courseId: course._course_id,
                courseName: course.courses_name,
                courseNumber: course.courses_number,
            });
        });

        // Process rotation courses
        level?.rotation_course?.forEach((rotation) => {
            rotation?.course?.forEach((course) => {
                formattedCourses.push({
                    term: level.term,
                    year: level.year,
                    levelNo: level.level_no,
                    rotationNo: rotation.rotation_count,
                    courseType: 'rotation',
                    courseId: course._course_id,
                    courseName: course.courses_name,
                    courseNumber: course.courses_number,
                });
            });
        });
    });

    return formattedCourses;
};

const programCourseList = async ({ institutionCalendarId, programId }) => {
    const programCalendarCourses = await programCalendarModel
        .findOne(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
            },
            {
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.rotation': 1,
                'level.rotation_count': 1,
                'level.course._course_id': 1,
                'level.course.courses_name': 1,
                'level.course.courses_number': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.courses_name': 1,
                'level.rotation_course.course.courses_number': 1,
            },
        )
        .lean();

    const formattedCourses = formatProgramCourses(programCalendarCourses);
    return formattedCourses;
};

module.exports = {
    calendarProgramList,
    programCourseList,
    formatProgramCourses,
};
