const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    assignmentDetails,
    createAssignmentReport,
    assignmentAllReportData,
    assignmentTypesData,
    assignmentTypesAllData,
    updatePublishedReportAndStudentData,
    unPublishReportData,
    listAssignmentReportStudentData,
    publishIndividualAssignmentReport,
    deleteExistingReportData,
    getFrameworkDetails,
    getOutcomeBasedAssignmentDetails,
    getRubricReportAssignmentsList,
    getRubricReportPromptList,
    getRubricReportData,
} = require('./assignment.report.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

// header course data
router.get(
    '/assignment-details',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(assignmentDetails),
);

// with pagination
router.get(
    '/assignment-types',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(assignmentTypesData),
);

// without pagination
router.get(
    '/assignment-type-list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(assignmentTypesAllData),
);

// publish assignment report
router.post(
    '/create',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(createAssignmentReport),
);

// update published data
router.put(
    '/update-publishedData',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updatePublishedReportAndStudentData),
);

// publish specific or all student
router.put(
    '/Publish-individual',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(publishIndividualAssignmentReport),
);

// published report retrieve
router.get(
    '/report-data',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(assignmentAllReportData),
);

// unPublish report data
router.put(
    '/unPublish',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(unPublishReportData),
);

// student gradeFinalized report data
router.get(
    '/list-student-report',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(listAssignmentReportStudentData),
);

// delete report and student data
router.delete(
    '/delete-report',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(deleteExistingReportData),
);

// frameWork details
router.get(
    '/framework-details',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getFrameworkDetails),
);

// outcome based assignment details
router.get(
    '/outcome-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getOutcomeBasedAssignmentDetails),
);

//Rubric Report

//assignment list
router.get(
    '/rubric-report/assignments-list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getRubricReportAssignmentsList),
);

//Prompt list
router.get(
    '/rubric-report/prompts-list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getRubricReportPromptList),
);

//Student list
router.get(
    '/rubric-report/all-students',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getRubricReportData),
);

module.exports = router;
