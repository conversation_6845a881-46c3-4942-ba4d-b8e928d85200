const passport = require('passport');
const { APP_STATE } = require('../utility/util_keys');
const jwtDecode = require('jwt-decode');
const jwt = require('jsonwebtoken');
const verifyCallback = (req, resolve, reject) => async (err, user, info) => {
    if (APP_STATE !== 'local') console.log('err', err);
    console.log('info', info);
    console.log('user', user);
    if (err || info || !user) {
        return reject({
            status_code: 401,
            status: false,
            message: 'Please authenticate',
            data: 'Authentication code not matching',
        });
    }
    req.user = user;
    resolve();
};
//
const authentication = async (req, res, next) => {
    if (req.header('authorization')) {
        const authorizationHeader = req.header('authorization');
        const Token = authorizationHeader.replace('Bearer ', '');
        req.headers.payload = await jwtDecode(Token);
    }
    return new Promise((resolve, reject) => {
        passport.authenticate('jwt', { session: false }, verifyCallback(req, resolve, reject))(
            req,
            res,
            next,
        );
    })
        .then(() => next())
        .catch((err) => res.status(401).send(err));
    // .catch((err) => next(err));
};

const authenticationForSignUp = async (req, res, next) => {
    try {
        const token = req.headers.authorization.split(' ')[1]; // Authorization: 'Bearer TOKEN'
        if (!token) {
            throw new Error({
                status_code: 401,
                status: false,
                message: 'Please authenticate',
                data: 'Authentication code missing',
            });
        }
        if (req.header('authorization')) {
            const authorizationHeader = req.header('authorization');
            const Token = authorizationHeader.replace('Bearer ', '');
            req.headers.payload = await jwtDecode(Token);
        }
        jwt.verify(token, process.env.JWT_SECRET_FOR_SIGNUP);

        next();
    } catch (err) {
        res.status(401).send({
            status_code: 401,
            status: false,
            message: 'Please authenticate',
            data: 'Authentication code not matching',
        });
    }
};
module.exports = { authentication, authenticationForSignUp };
