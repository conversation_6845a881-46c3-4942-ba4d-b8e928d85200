const userActivityLogSchema = require('./userActivity.model');
const { logger, USER_ACTIVITY_LOG } = require('../utility/util_keys');

const logActivity = async ({
    userId,
    role,
    method,
    action,
    host,
    url,
    message,
    data = {},
    canInsert = false,
}) => {
    if (canInsert) {
        userActivityLogSchema.create({
            userId,
            role,
            method,
            action,
            host,
            url,
            data,
            message,
        });
    }

    try {
        if (USER_ACTIVITY_LOG) {
            logger.info(
                'insertLog: %s-%s-%s-%s-%s-%s-%s-%o',
                userId,
                role,
                method,
                action,
                host,
                url,
                data,
            );
        }
    } catch (err) {
        logger.error({ err }, 'error in adding access logs');
    }
};

const getUserLog = async ({ query = {} }) => {
    try {
        const { userId } = query;
        const userLog = await userActivityLogSchema
            .find({ userId }, { _id: 0, _institution_id: 0, __v: 0 })
            .sort({ _id: -1 })
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: userLog,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    logActivity,
    getUserLog,
};
