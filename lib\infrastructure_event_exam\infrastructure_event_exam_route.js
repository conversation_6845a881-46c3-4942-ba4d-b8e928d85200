const express = require('express');
const route = express.Router();
const infra_event_exam = require('./infrastructure_event_exam_controller');
const validator = require('./infrastructure_event_exam_validator');

route.post('/select_event_exam' ,validator.infra_event_exam,infra_event_exam.create);
route.get('/get_event_exam' ,infra_event_exam.get);
route.get('/get_event_exam_id/:id' ,validator.infra_event_exam_id,infra_event_exam.get_id);
route.put('/update_event_exam/:id' ,validator.infra_event_exam_id,validator.infra_event_exam,infra_event_exam.update);
route.put('/update_event_exam_only/:id' ,validator.infra_event_exam_id,validator.event_exam,infra_event_exam.update_event_exam);
route.delete('/delete_event_exam/:id' ,validator.infra_event_exam_id,infra_event_exam.delete);



module.exports = route;