const { ACADEMIC, ADMIN } = require('../../utility/constants');

const getDepartmentStats = (departments) => {
    let subjectCount = 0;
    let adminCount = 0;
    let academicCount = 0;
    const programHashMap = {};
    departments.forEach((department) => {
        if (department.subject) subjectCount += department.subject.length;
        if (department.type === ACADEMIC) academicCount++;
        if (department.type === ADMIN) {
            if (!programHashMap[[department.type]])
                programHashMap[[department.type]] = {
                    departments: 1,
                    subjects: department.subject.length,
                };
            else {
                programHashMap[[department.type]].departments++;
                programHashMap[[department.type]].subjects += department.subject.length;
            }
            adminCount++;
        }
        if (department._program_id) {
            if (!programHashMap[[department._program_id]])
                programHashMap[[department._program_id]] = {
                    programName: department.programName,
                    departments: 1,
                    subjects: department.subject.length,
                };
            else {
                programHashMap[[department._program_id]].departments++;
                programHashMap[[department._program_id]].subjects += department.subject.length;
            }
        }
    });
    const programDistribution = Object.values(programHashMap);
    return { subjectCount, academicCount, adminCount, programDistribution };
};

const filterDepartmentSubjects = (departments) => {
    let subjects;
    if (Array.isArray(departments)) {
        departments.forEach((department) => {
            subjects = department.subject;
            subjects = subjects.filter(
                (subject) => subject.isDeleted === false && subject.isActive === true,
            );
            department.subject = subjects;
        });
    } else {
        subjects = departments.subject;
        subjects = subjects.filter(
            (subject) => subject.isDeleted === false && subject.isActive === true,
        );
        departments.subject = subjects;
    }
    return departments;
};

const filterSharedSubjects = (departments, programId) => {
    const sharedDepartmentSubjects = [];
    departments.forEach((department) => {
        const subjects = department.subject
            .filter(
                (subjectEntry) =>
                    subjectEntry.sharedWith.filter(
                        (sharedEntry) => sharedEntry._program_id.toString() === programId,
                    ).length !== 0,
            )
            .map((sub) => {
                return { subjectName: sub.subjectName, _id: sub._id, sharedWith: sub.sharedWith };
            });
        sharedDepartmentSubjects.push({
            _program_id: department._program_id,
            programName: department.programName,
            departmentName: department.departmentName,
            subject: subjects,
            _id: department._id,
        });
    });
    return sharedDepartmentSubjects;
};

const populateInstituteDepartments = ({
    departments = [],
    allDepartments = [],
    programIds = [],
    type,
}) => {
    const allHashedDepartments = {};
    const instituteDepartments = [];
    departments.forEach((department) => {
        instituteDepartments.push({ ...department, sharedSubjects: [] });
    });

    allDepartments.forEach((department) => {
        if (!allHashedDepartments[[department._id]]) {
            allHashedDepartments[[department._id]] = { ...department, sharedSubjects: [] };
        }
    });

    allDepartments.forEach((department) => {
        const subjects = department.subject;
        subjects.forEach((subject) => {
            const sharedWith = subject.sharedWith;
            sharedWith.forEach((sharedTo) => {
                const hashedDepartment = instituteDepartments.find((dept) => {
                    return (
                        dept &&
                        dept._id &&
                        dept._id.toString() === sharedTo._department_id.toString()
                    );
                });
                if (hashedDepartment) {
                    hashedDepartment.sharedSubjects.push({
                        subjectName: subject.subjectName,
                        _id: subject._id,
                        departmentName: department.departmentName,
                        _department_id: department._id,
                        programName: department.programName,
                        _program_id: department._program_id,
                    });
                }
            });
        });

        if (department.type == ADMIN && type === ACADEMIC) {
            const sharedWith = department.sharedWith;
            if (sharedWith.length) {
                sharedWith.forEach((sharedTo) => {
                    if (programIds.includes(sharedTo._program_id.toString())) {
                        const departmentInfo = {
                            ...filterDepartmentSubjects(department),
                        };
                        delete departmentInfo._id;
                        delete departmentInfo.sharedWith;
                        delete departmentInfo._parent_id;
                        delete departmentInfo.programName;
                        delete departmentInfo._program_id;
                        departmentInfo.isShared = true;
                        departmentInfo.sharedTo = {
                            _program_id: sharedTo._program_id,
                            programName: sharedTo.programName,
                        };
                        instituteDepartments.push(departmentInfo);
                    }
                });
            }
        }
    });

    departments.forEach((department) => {
        const sharedWith = department.sharedWith;
        if (sharedWith && sharedWith.length && type === ACADEMIC) {
            sharedWith.forEach((sharedTo) => {
                if (programIds.includes(sharedTo._program_id.toString())) {
                    const departmentInfo = {
                        ...filterDepartmentSubjects(department),
                        _program_id: sharedTo._program_id,
                        programName: sharedTo.programName,
                    };
                    delete departmentInfo._id;
                    delete departmentInfo.sharedWith;
                    delete departmentInfo._parent_id;
                    departmentInfo.isShared = true;
                    departmentInfo.sharedTo = {
                        _program_id: sharedTo._program_id,
                        programName: sharedTo.programName,
                    };
                    departmentInfo.sharedFromProgramId = department._program_id;
                    departmentInfo.sharedFromProgramName = department.programName;
                    instituteDepartments.push(departmentInfo);
                }
            });
        }
    });

    return instituteDepartments;
};

module.exports = {
    getDepartmentStats,
    filterDepartmentSubjects,
    filterSharedSubjects,
    populateInstituteDepartments,
};
