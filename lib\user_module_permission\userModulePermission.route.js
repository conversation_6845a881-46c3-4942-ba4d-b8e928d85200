const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    getModuleUsers,
    getUserPermissionModuleList,
    getUserModuleExistingPermissions,
    createUserModulePermissions,
    getSingleProgramCalendarDetails,
    getSingleProgramCourseDetails,
} = require('./userModulePermission.controller');
const {
    getModuleListValidator,
    getModuleUsersValidator,
    getUserModuleExistingPermissionValidator,
} = require('./userModulePermission.validator');

router.get(
    '/getUserPermissionModuleList',
    getModuleListValidator,
    catchAsync(getUserPermissionModuleList),
);
router.get('/getModuleUsers', getModuleUsersValidator, catchAsync(getModuleUsers));
router.get(
    '/getUserModuleExistingPermissions',
    getUserModuleExistingPermissionValidator,
    catchAsync(getUserModuleExistingPermissions),
);
router.post('/createUserModulePermissions', catchAsync(createUserModulePermissions));
router.get(
    '/getSingleProgramCalendarDetails/:programId',
    catchAsync(getSingleProgramCalendarDetails),
);
router.get('/getSingleProgramCourseDetails', catchAsync(getSingleProgramCourseDetails));
module.exports = router;
