const { Schema, model } = require('mongoose');
const { VERIFICATION } = require('../../common/utils/constants');
const { VERIFICATION_EXPIRY_SECONDS } = require('../../../lib/utility/util_keys');

const verificationSchema = new Schema(
    {
        email: { type: String, required: true },
        code: { type: Number, required: true },
        roleId: { type: Schema.Types.ObjectId, required: true },
        studentId: { type: Schema.Types.ObjectId, required: true },
        componentId: { type: Schema.Types.ObjectId, required: true },
        childrenId: { type: Schema.Types.ObjectId, required: true },
        deliveryTypeId: { type: Schema.Types.ObjectId },
        expiresAt: {
            type: Date,
            expires: 0,
        },
    },
    { timestamps: true },
);

module.exports = model(VERIFICATION, verificationSchema);
