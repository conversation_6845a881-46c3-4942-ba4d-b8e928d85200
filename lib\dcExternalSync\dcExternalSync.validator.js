const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
exports.studentGroupingValidation = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            institutionCalendarId: objectId.required().error((error) => {
                return error;
            }),
            programId: objectId.required().error((error) => {
                return 'Program Code Invalid';
            }),
            term: Joi.string()
                .required()
                .error((error) => {
                    return 'Term Code Invalid';
                }),
            year: Joi.string()
                .required()
                .error((error) => {
                    return 'Year Code Invalid';
                }),
            level: Joi.string()
                .required()
                .error((error) => {
                    return 'Level Code Invalid';
                }),
            courseId: objectId.required().error((error) => {
                return 'Course Code Invalid';
            }),
            studentId: objectId.optional().error((error) => {
                return 'Student Code Invalid';
            }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(422).send({
            status_code: 422,
            status: false,
            message: req.t('VALIDATION_ERROR'),
            data: errors,
        });
    }
    next();
};
