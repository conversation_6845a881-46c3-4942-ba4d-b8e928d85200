const formSettingSchema = require('../categoryForm/formSetting.model');
const qapcFormInitiatorSchema = require('../formInitiator/qapcFormInitiator.model');
const { convertToMongoObjectId } = require('../../utility/common');
const qapcPermissionSchema = require('../rolePermission/qapcPermission.model');
const qapcActionSchema = require('../rolePermission/action.model');
const {
    PUBLISHED,
    PENDING,
    FORM_APPROVER,
    USER,
    QAPC_ROLE,
    APPROVED,
    NOT_INITIATED,
    NOT_APPLICABLE,
    REVOKED,
    QAPC_FORM_COURSES_GROUPS,
    QAPC_FORM_CATEGORY,
    REJECTED,
    SKIPPED,
    IN_REVIEW,
    SUBMITTED,
    RE_SUBMIT,
    RESUBMISSION,
    DELAYED,
    ALL,
    GUEST_USER,
    FORM_INITIATOR,
    FORM_INITIATOR_LABEL,
} = require('../../utility/constants');
const { send_email } = require('../../utility/common_functions');
const formCourseGroupSchema = require('../categoryForm/formCourseGroups.module');

const getFormApproverLevel = async ({ uniqueFormIds }) => {
    try {
        return await formSettingSchema
            .find(
                {
                    _id: {
                        $in: uniqueFormIds.map((formElement) =>
                            convertToMongoObjectId(formElement),
                        ),
                    },
                    status: PUBLISHED,
                    isDeleted: false,
                    isActive: true,
                    archive: false,
                },
                {
                    'approvalLevel.approvalStatus': 1,
                    'approvalLevel.requireAll': 1,
                    'approvalLevel.requireMinium': 1,
                    'approvalLevel.turnAroundTime': 1,
                    'approvalLevel.escalateRequest': 1,
                    'approvalLevel.minimum_user': 1,
                    'approvalLevel.allowToSkipping': 1,
                    'approvalLevel.allowToOverwrite': 1,
                    'approvalLevel.levelNumber': 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatedWiseSort = ({ formList }) => {
    try {
        return formList.sort((a, b) => {
            return new Date(b.updatedAt) - new Date(a.updatedAt);
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getFormInitiatedIds = async ({ assuranceProcessQuery }) => {
    try {
        return await qapcFormInitiatorSchema
            .find(assuranceProcessQuery, {
                categoryFormId: 1,
                categoryFormCourseId: 1,
                categoryFormGroupId: 1,
                'mergedFormId.formInitiatorId': 1,
                mergeStatus: 1,
                'approverDetails.currentLevel': 1,
                'approverDetails.levelStatus': 1,
                'approverDetails.isHistory': 1,
                programId: 1,
                approverList: 1,
                'formCalenderIds.institutionCalenderId.$': 1,
                updatedAt: 1,
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getFormInitiatorList = async ({ formInitiatorIds }) => {
    try {
        return await qapcFormInitiatorSchema
            .find(
                {
                    _id: {
                        $in: formInitiatorIds.map((initiatorElement) =>
                            convertToMongoObjectId(initiatorElement),
                        ),
                    },
                    submissionStatus: { $in: [SUBMITTED, RESUBMISSION] },
                    isActive: true,
                    isDeleted: false,
                    archive: false,
                },
                {
                    'approverDetails.currentLevel': 1,
                    'approverDetails.levelStatus': 1,
                    formAttachment: 1,
                    selectedGroupName: 1,
                    categoryId: 1,
                    categoryName: 1,
                    formName: 1,
                    categoryFormId: 1,
                    categoryFormCourseId: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseName: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    courseId: 1,
                    categoryFormGroupId: 1,
                    'mergedFormId.formInitiatorId': 1,
                    submissionStatus: 1,
                    submissionDate: 1,
                    approverList: 1,
                    level: 1,
                    'resubmissionLog.level': 1,
                    'resubmissionLog.roleId': 1,
                    'resubmissionLog.userId': 1,
                    'resubmissionLog.reason': 1,
                    'resubmissionLog.resubmissionDate': 1,
                    updatedAt: 1,
                },
            )
            .populate({ path: 'resubmissionLog.userId', select: { name: 1 } })
            .populate({ path: 'resubmissionLog.roleId', select: { roleName: 1 } })
            .populate({
                path: 'categoryFormId',
                select: {
                    categoryFormType: 1,
                    formType: 1,
                    'approvalLevel.levelNumber': 1,
                    'approvalLevel.specificSections': 1,
                },
            })
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const checkApproverLevel = async ({ formList, formApproverLevel }) => {
    try {
        formList.forEach((initiatedElement) => {
            const matchingApprover = formApproverLevel.find(
                (levelElement) =>
                    levelElement._id.toString() === initiatedElement.categoryFormId.toString(),
            );
            if (matchingApprover) {
                initiatedElement.isLastLevel =
                    initiatedElement.approverDetails.currentLevel + 1 ===
                    matchingApprover.approvalLevel.length;
            }
        });
        return { formList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getApproverUserList = async ({ categoryFormGroupId }) => {
    try {
        const qapcRoleUserData = await qapcPermissionSchema.aggregate([
            {
                $match: {
                    'permissions.cfpc.subModuleName': FORM_APPROVER,
                    $expr: {
                        $cond: [
                            {
                                $gte: [
                                    {
                                        $size: {
                                            $ifNull: [
                                                '$categoryFormCourseId.cfpc.categoryFormGroupId',
                                                [],
                                            ],
                                        },
                                    },
                                    1,
                                ],
                            },
                            {
                                $eq: [
                                    '$categoryFormCourseId.cfpc.categoryFormGroupId',
                                    convertToMongoObjectId(categoryFormGroupId),
                                ],
                            },
                            true,
                        ],
                    },
                },
            },
            {
                $project: {
                    userId: 1,
                    permissions: {
                        cfpc: {
                            $filter: {
                                input: '$permissions.cfpc',
                                as: 'cfpc',
                                cond: {
                                    $and: [{ $eq: ['$$cfpc.subModuleName', FORM_APPROVER] }],
                                },
                            },
                        },
                    },
                },
            },
            {
                $project: {
                    userId: 1,
                    'permissions.cfpc.qapcRoleId': 1,
                    'permissions.cfpc.approverLevelIndex': 1,
                },
            },
        ]);
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: qapcRoleUserData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getFormInitiatorApprover = async ({ formInitiatorId }) => {
    try {
        return await qapcFormInitiatorSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(formInitiatorId),
                },
                {
                    approverList: 1,
                    'approverDetails.currentLevel': 1,
                    'approverDetails.levelStatus': 1,
                    levelStartTime: 1,
                    mergedFormId: 1,
                    rejectionDetails: 1,
                    resubmissionLog: 1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const splitLevelBasedUser = async ({ formInitiators, allUserLists }) => {
    try {
        formInitiators.forEach((initiatorsElement) => {
            const approverUser = allUserLists.qapcRoleUserData.flatMap((roleUser) =>
                roleUser.permissions.cfpc
                    .filter((cfpcElement) =>
                        cfpcElement.formCourseGroupId
                            .toString()
                            .includes(initiatorsElement.categoryFormGroupId.toString()),
                    )
                    .map((filteredCfpcElement) => ({
                        userId: roleUser.userId,
                        approverLevelIndex: filteredCfpcElement.approverLevelIndex,
                        qapcRoleId: filteredCfpcElement.qapcRoleId,
                    })),
            );
            const groupedApproverUser = new Map();
            approverUser.forEach(
                ({ userId, userName, approverLevelIndex, qapcRoleId, qapcRoleName }) => {
                    if (!groupedApproverUser.has(approverLevelIndex)) {
                        groupedApproverUser.set(approverLevelIndex, {
                            approverLevelIndex,
                            roles: new Map(),
                        });
                    }
                    const level = groupedApproverUser.get(approverLevelIndex);
                    if (!level.roles.has(qapcRoleId.toString())) {
                        level.roles.set(qapcRoleId.toString(), {
                            qapcRoleId,
                            qapcRoleName,
                            userIds: [],
                        });
                    }
                    const role = level.roles.get(qapcRoleId);
                    const userExists = role.userIds.some(
                        (user) => user.userId.toString() === userId.toString(),
                    );
                    if (!userExists) {
                        role.userIds.push({
                            userId,
                            userName,
                        });
                    }
                },
            );
            const approverRoleUser = Array.from(groupedApproverUser.values()).map(
                (levelElement) => ({
                    approverLevelIndex: levelElement.approverLevelIndex,
                    roles: Array.from(levelElement.roles.values()),
                }),
            );
            initiatorsElement.approverRoleUser = approverRoleUser;
        });
        return { formInitiators };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLevelUserList = ({ categoryFormGroupId, qapcRoleUserData }) => {
    try {
        const approverUser = qapcRoleUserData.flatMap((roleUserElement) =>
            roleUserElement.cfpcPermissions
                .filter((cfpcElement) =>
                    cfpcElement.formCourseGroupId
                        .toString()
                        .includes(categoryFormGroupId.toString()),
                )
                .map((filteredCfpcElement) => ({
                    userId: roleUserElement.userId,
                    userName: roleUserElement.userName,
                    approverLevelIndex: filteredCfpcElement.approverLevelIndex,
                    qapcRoleId: filteredCfpcElement.qapcRoleId,
                    qapcRoleName: filteredCfpcElement.qapcRoleName,
                })),
        );
        const approverRole = [];
        approverUser.forEach((userElement) => {
            const levelIndex = userElement.approverLevelIndex;
            const roleId = userElement.qapcRoleId;
            let levelEntry = approverRole.find(
                (approverElement) => approverElement.approverLevelIndex === levelIndex,
            );
            if (!levelEntry) {
                levelEntry = { approverLevelIndex: levelIndex, roleIds: [] };
                approverRole.push(levelEntry);
            }
            let roleEntry = levelEntry.roleIds.find(
                (approverElement) => approverElement.qapcRoleId.toString() === roleId.toString(),
            );
            if (!roleEntry) {
                roleEntry = { qapcRoleId: roleId, roleName: userElement.qapcRoleName, userIds: [] };
                levelEntry.roleIds.push(roleEntry);
            }
            roleEntry.userIds.push({
                userId: userElement.userId || userElement.guestUserId,
                userName: userElement.userName || userElement.guestUserName,
                status: PENDING,
            });
        });
        return { approverRole };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLevelWiseSplitUser = ({ roleUserList }) => {
    try {
        const uniqueCategoryGroupId = [];
        roleUserList.forEach((roleUserElement) => {
            roleUserElement.cfpcPermissions.forEach((permissionElement) => {
                if (permissionElement?.formCourseGroupId?.length) {
                    permissionElement.formCourseGroupId.forEach((courseGroupElement) => {
                        let matchingCategory = uniqueCategoryGroupId.find(
                            (groupElement) =>
                                groupElement.categoryGroupId.toString() ===
                                courseGroupElement.toString(),
                        );
                        if (!matchingCategory) {
                            matchingCategory = {
                                categoryGroupId: courseGroupElement,
                                approverLevels: [],
                            };
                            uniqueCategoryGroupId.push(matchingCategory);
                        }
                        let matchingLevel = matchingCategory.approverLevels.find(
                            (levelElement) =>
                                levelElement.approverLevelIndex ===
                                permissionElement.approverLevelIndex,
                        );
                        if (!matchingLevel) {
                            matchingLevel = {
                                approverLevelIndex: permissionElement.approverLevelIndex,
                                roleIds: [],
                            };
                            matchingCategory.approverLevels.push(matchingLevel);
                        }
                        let matchingRole = matchingLevel.roleIds.find(
                            (roleElement) =>
                                roleElement.qapcRoleId.toString() ===
                                permissionElement.qapcRoleId.toString(),
                        );
                        if (!matchingRole) {
                            matchingRole = {
                                qapcRoleId: permissionElement.qapcRoleId,
                                userIds: [],
                            };
                            matchingLevel.roleIds.push(matchingRole);
                        }
                        matchingRole.userIds.push({
                            userId: roleUserElement.userId,
                        });
                    });
                }
            });
        });
        return { uniqueCategoryGroupId };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getApproverRoleUserList = ({ qapcLevels, userId, roleId }) => {
    try {
        const matchingCategoryGroupIds = [];
        qapcLevels.forEach((levelElement) => {
            levelElement.approverLevels.forEach((approverLevel) => {
                approverLevel.roleIds.forEach((roleElement) => {
                    if (roleElement.qapcRoleId.toString() === roleId.toString()) {
                        const isUserMatching = roleElement.userIds.some(
                            (userElement) => userElement.userId.toString() === userId.toString(),
                        );
                        if (isUserMatching) {
                            const alreadyExists = matchingCategoryGroupIds.some(
                                (matchingGroupElement) =>
                                    matchingGroupElement.categoryGroupId.toString() ===
                                        levelElement.categoryGroupId.toString() &&
                                    matchingGroupElement.approverLevelIndex ===
                                        approverLevel.approverLevelIndex,
                            );
                            if (!alreadyExists) {
                                matchingCategoryGroupIds.push({
                                    categoryGroupId: levelElement.categoryGroupId,
                                    approverLevelIndex: approverLevel.approverLevelIndex,
                                });
                            }
                        }
                    }
                });
            });
        });
        return { matchingCategoryGroupIds };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getApprovedUserFormList = ({ userActionsList, formInitiators }) => {
    try {
        const groupMatchingForms = [];
        const uniqueFormInitiatedIds = new Set();
        const uniqueFormIds = [];
        const uniqueCourseGroupIds = [];
        const uniqueCategoryFormCourseIds = [];
        userActionsList.forEach((qapcLevelElement) => {
            formInitiators.forEach((initiatorElement) => {
                if (
                    initiatorElement.categoryFormGroupId.toString() ===
                        qapcLevelElement.categoryGroupId.toString() &&
                    !uniqueFormInitiatedIds.has(initiatorElement._id)
                ) {
                    uniqueFormInitiatedIds.add(initiatorElement._id);
                    groupMatchingForms.push(initiatorElement);
                    uniqueFormIds.push(String(initiatorElement.categoryFormId));
                    uniqueCourseGroupIds.push(String(initiatorElement.categoryFormGroupId));
                    uniqueCategoryFormCourseIds.push(String(initiatorElement.categoryFormCourseId));
                }
            });
        });
        return {
            groupMatchingForms,
            uniqueFormIds: [...new Set(uniqueFormIds)],
            uniqueCourseGroupIds: [...new Set(uniqueCourseGroupIds)],
            uniqueCategoryFormCourseIds: [...new Set(uniqueCategoryFormCourseIds)],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDaysDifference = ({ currentDate, startDate }) => {
    try {
        const timeDifference = currentDate - new Date(startDate);
        const daysDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
        return daysDifference;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateLevelWiseStatus = ({ formInitiatorApprover, formApproverLevel, qapcLevelUserData }) => {
    try {
        const approvedStatus = [];
        const currentLevel = formInitiatorApprover?.approverDetails?.currentLevel;
        formApproverLevel.approvalLevel.forEach((approvalLevelElement) => {
            const matchingLevelUserData = qapcLevelUserData.find(
                (levelUserElement) =>
                    levelUserElement.approverLevelIndex === approvalLevelElement.levelNumber,
            );
            if (matchingLevelUserData) {
                Object.assign(matchingLevelUserData, {
                    requireAll: approvalLevelElement.requireAll,
                    requireMinium: approvalLevelElement.requireMinium,
                    minimum_user: approvalLevelElement.minimum_user,
                    turnAroundTime: approvalLevelElement.turnAroundTime,
                });
                // Find matching level from formInitiatorApprover
                const matchingLevel = formInitiatorApprover?.approverList.find(
                    (approverElement) =>
                        approverElement.level === matchingLevelUserData.approverLevelIndex,
                );
                // Update role and user statuses based on the approval level
                matchingLevelUserData.roleIds.forEach((roleIdElement) => {
                    if (approvalLevelElement.levelNumber === currentLevel) {
                        roleIdElement.userIds.forEach((userElement) => {
                            const matchingRole = matchingLevel?.roleUser.find(
                                (roleUserElement) =>
                                    roleUserElement.roleId.toString() ===
                                    roleIdElement.qapcRoleId.toString(),
                            );
                            const matchingUser = matchingRole?.userIds.find(
                                (approverUserElement) =>
                                    approverUserElement.userId.toString() ===
                                    userElement.userId.toString(),
                            );
                            if (matchingUser) {
                                userElement.status = matchingUser.status;
                                userElement.reason = matchingUser?.reason;
                            } else {
                                userElement.status = [REJECTED, RE_SUBMIT].includes(
                                    matchingLevel?.levelStatus,
                                )
                                    ? NOT_APPLICABLE
                                    : PENDING;
                            }
                        });
                    } else if (matchingLevel) {
                        roleIdElement.userIds.forEach((userElement) => {
                            const matchingRole = matchingLevel.roleUser.find(
                                (roleUserElement) =>
                                    roleUserElement.roleId.toString() ===
                                    roleIdElement.qapcRoleId.toString(),
                            );

                            if (matchingRole) {
                                const matchingUser = matchingRole.userIds.find(
                                    (approverUserElement) =>
                                        approverUserElement.userId.toString() ===
                                        userElement.userId.toString(),
                                );
                                userElement.status = matchingUser ? matchingUser.status : PENDING;
                                userElement.reason = matchingUser?.reason;
                            }
                        });
                    } else if (currentLevel > approvalLevelElement.levelNumber) {
                        roleIdElement.userIds.forEach((userElement) => {
                            userElement.status = NOT_APPLICABLE;
                        });
                    } else {
                        roleIdElement.userIds.forEach((userElement) => {
                            userElement.status = NOT_INITIATED;
                        });
                    }
                });
                approvedStatus.push({
                    levelStatus: matchingLevel?.levelStatus || PENDING,
                    isSkipped: matchingLevel?.isSkipped || false,
                    ...matchingLevelUserData,
                });
            }
        });
        // Update previous level statuses if necessary
        approvedStatus.forEach((statusElement) => {
            if (statusElement.approverLevelIndex < currentLevel) {
                statusElement.roleIds.forEach((roleIdElement) => {
                    roleIdElement.userIds.forEach((userElement) => {
                        if (userElement.status === (PENDING || NOT_INITIATED)) {
                            userElement.status = NOT_APPLICABLE;
                        }
                    });
                });
            } else if (
                statusElement.approverLevelIndex === currentLevel &&
                formInitiatorApprover?.approverDetails?.levelStatus === APPROVED
            ) {
                statusElement.roleIds.forEach((roleIdElement) => {
                    roleIdElement.userIds.forEach((userElement) => {
                        if (userElement.status === (PENDING || NOT_INITIATED)) {
                            userElement.status = NOT_APPLICABLE;
                        }
                    });
                });
            }
        });
        //check isSkipping status
        approvedStatus.forEach((currentLevel, i) => {
            if (currentLevel.isSkipped) {
                approvedStatus.slice(0, i).forEach((previousLevel) => {
                    previousLevel.roleIds.forEach((roleId) => {
                        roleId.userIds.forEach((userElement) => {
                            if (userElement.status === (NOT_APPLICABLE || NOT_INITIATED)) {
                                userElement.status = SKIPPED;
                            }
                        });
                    });
                });
            }
        });
        //check the delayed
        approvedStatus.forEach((approverElement) => {
            const levelStartDate = formInitiatorApprover?.levelStartTime?.find(
                (levelStartElement) =>
                    levelStartElement.level === approverElement.approverLevelIndex,
            );
            const currentDate = new Date();
            if (levelStartDate) {
                let startDate = new Date(levelStartDate?.startDate);
                const tatEndDate = approverElement.turnAroundTime;
                startDate = startDate.setDate(startDate.getDate() + tatEndDate);
                if (currentLevel === approverElement.approverLevelIndex) {
                    const daysDifference = getDaysDifference({
                        currentDate,
                        startDate,
                    });
                    if (daysDifference > 0) {
                        let allPending = true;
                        approverElement.roleIds.forEach((role) => {
                            role.userIds.forEach((userElement) => {
                                if (userElement.status === PENDING) {
                                    userElement.status = DELAYED;
                                } else {
                                    allPending = false;
                                }
                            });
                        });
                        if (allPending) {
                            approverElement.levelStatus = DELAYED;
                        }
                    }
                }
                //delayed time check
                const nextLevel = formInitiatorApprover?.levelStartTime?.find(
                    (levelStartElement) => levelStartElement.level === levelStartDate.level + 1,
                );
                startDate = new Date(startDate);
                if (!nextLevel && currentDate >= startDate) {
                    const daysDifference = getDaysDifference({
                        currentDate,
                        startDate,
                    });
                    if (daysDifference > 0) {
                        approverElement.delayedDate = daysDifference;
                    }
                } else {
                    const nextLevelStartDate = new Date(nextLevel?.startDate);
                    if (nextLevelStartDate && nextLevelStartDate >= startDate) {
                        const daysDifference = getDaysDifference({
                            currentDate: nextLevelStartDate,
                            startDate,
                        });
                        if (daysDifference > 0) {
                            approverElement.delayedDate = daysDifference;
                        }
                    }
                }
            }
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: approvedStatus };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const formApproverStatus = ({
    alreadyApprovedUser,
    categoryFormGroupId,
    currentLevel,
    allLevelUser,
    matchingFormApprover,
    userId,
}) => {
    try {
        let isLastUser = false;
        const matchingGroupData = allLevelUser?.find(
            (levelUserElement) =>
                levelUserElement.categoryGroupId.toString() === categoryFormGroupId.toString(),
        );
        if (matchingGroupData) {
            const currentLevelData = matchingGroupData?.approverLevels.find(
                (levelElement) => levelElement.approverLevelIndex === currentLevel,
            );
            const hasNextLevel = matchingFormApprover.approvalLevel.find(
                (levelElement) => levelElement.levelNumber === currentLevel,
            );
            let numberOfRole = 0;
            if (currentLevelData) {
                if (hasNextLevel.requireAll) {
                    const levelUserIds = currentLevelData.roleIds.flatMap((roleElement) =>
                        roleElement.userIds.map((userElement) => userElement.userId),
                    );
                    const alreadyApprovedUserData = alreadyApprovedUser?.find(
                        (approvedLevel) => approvedLevel.level === currentLevel,
                    );
                    if (
                        levelUserIds?.length === 1 &&
                        levelUserIds.toString().includes(String(userId))
                    ) {
                        isLastUser = true;
                    } else if (alreadyApprovedUserData) {
                        const approvedUserIds = alreadyApprovedUserData.roleUser.flatMap(
                            (roleElement) =>
                                roleElement.userIds.map((userElement) =>
                                    String(userElement.userId),
                                ),
                        );
                        const remainingUsers = levelUserIds.filter(
                            (userIdElement) => !approvedUserIds.includes(String(userIdElement)),
                        );

                        if (
                            remainingUsers.length === 1 &&
                            String(remainingUsers[0]) === String(userId)
                        ) {
                            isLastUser = true;
                        }
                    }
                } else if (hasNextLevel.requireMinium) {
                    currentLevelData.roleIds.forEach((roleElement) => {
                        const approvedLevelData = alreadyApprovedUser?.find(
                            (approvedElement) => approvedElement.level === currentLevel,
                        );
                        const roleApprovedUsers = approvedLevelData?.roleUser.find(
                            (roleUserElement) =>
                                String(roleUserElement.roleId) === String(roleElement.qapcRoleId),
                        );
                        const rpUserIdsCount = roleApprovedUsers?.userIds.length || 0;
                        const approverUserIdsCount = roleElement.userIds.length;
                        const minimumUser = hasNextLevel.minimum_user || 0;
                        if (rpUserIdsCount === 0 && minimumUser === 1) {
                            numberOfRole += 1;
                        } else if (
                            rpUserIdsCount < minimumUser &&
                            rpUserIdsCount > approverUserIdsCount
                        ) {
                            numberOfRole += 1;
                        } else if (
                            approverUserIdsCount < minimumUser &&
                            rpUserIdsCount > 0 &&
                            rpUserIdsCount < minimumUser &&
                            rpUserIdsCount <= approverUserIdsCount
                        ) {
                            numberOfRole += 1;
                        } else if (
                            rpUserIdsCount <= minimumUser &&
                            (rpUserIdsCount === 0 || rpUserIdsCount > 0) &&
                            (approverUserIdsCount - 1 === rpUserIdsCount ||
                                rpUserIdsCount === minimumUser)
                        ) {
                            numberOfRole += 1;
                        }
                    });
                    if (numberOfRole === currentLevelData?.roleIds?.length) {
                        isLastUser = true;
                    }
                }
            }
        }
        return isLastUser;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const approvedStatusEdit = ({
    userId,
    roleId,
    matchingFormApprover,
    currentLevel,
    alreadyApprovedUser,
}) => {
    try {
        let isEdited = false;
        alreadyApprovedUser.forEach((approverElement) => {
            if (approverElement.level > currentLevel) {
                isEdited = false;
            } else if (approverElement.level === currentLevel) {
                const matchingRole = approverElement.roleUser.find(
                    (roleElement) => String(roleElement.roleId) === String(roleId),
                );
                if (matchingRole) {
                    const matchingUser = matchingRole.userIds.find(
                        (userElement) => String(userElement.userId) === String(userId),
                    );
                    if (matchingUser) {
                        userUpdateDate = matchingUser.statusDate;
                        const currentApproverData = matchingFormApprover?.approvalLevel.find(
                            (formApprovedElement) =>
                                formApprovedElement.levelNumber === currentLevel,
                        );
                        if (currentApproverData) {
                            const userUpdateDate = new Date(matchingUser.statusDate);
                            const updatedUserDate = new Date(userUpdateDate);
                            updatedUserDate.setDate(
                                updatedUserDate.getDate() +
                                    (currentApproverData?.turnAroundTime || 0),
                            );
                            const currentDate = new Date();
                            if (updatedUserDate > currentDate) {
                                isEdited = true;
                            }
                        }
                    }
                }
            }
        });
        return isEdited;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getApproverCategory = async ({ userId, roleId, subModuleType, institutionCalenderId }) => {
    try {
        const formRolePermission = [
            { $eq: ['$$item.subModuleName', subModuleType] },
            { $eq: ['$$item.qapcRoleId', convertToMongoObjectId(roleId)] },
            { $eq: ['$$item.selectedAcademic', true] },
            {
                $in: [
                    convertToMongoObjectId(institutionCalenderId),
                    '$$item.institutionCalendarIds',
                ],
            },
        ];
        const qapcPermissionList = await qapcPermissionSchema.aggregate([
            {
                $match: {
                    $and: [
                        {
                            $or: [
                                { userId: convertToMongoObjectId(userId) },
                                { guestUserId: convertToMongoObjectId(userId) },
                            ],
                        },
                        {
                            qapcRoleIds: { $in: [convertToMongoObjectId(roleId)] },
                        },
                    ],
                },
            },
            {
                $project: {
                    'permissions.cfpc.formCourseGroupId': 1,
                    'permissions.cfpc.subModuleName': 1,
                    'permissions.cfpc.qapcRoleId': 1,
                    'permissions.cfpc.selectedAcademic': 1,
                    'permissions.cfpc.institutionCalendarIds': 1,
                },
            },
            {
                $addFields: {
                    'permissions.cfpc': {
                        $filter: {
                            input: '$permissions.cfpc',
                            as: 'item',
                            cond: {
                                $and: formRolePermission,
                            },
                        },
                    },
                },
            },
            {
                $unwind: '$permissions.cfpc',
            },
            {
                $group: {
                    _id: null,
                    formCourseGroupIds: {
                        $addToSet: '$permissions.cfpc.formCourseGroupId',
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    formCourseGroupIds: {
                        $reduce: {
                            input: '$formCourseGroupIds',
                            initialValue: [],
                            in: { $concatArrays: ['$$value', '$$this'] },
                        },
                    },
                },
            },
            {
                $lookup: {
                    from: QAPC_FORM_COURSES_GROUPS,
                    localField: 'formCourseGroupIds',
                    foreignField: '_id',
                    as: 'categoryGroupData',
                },
            },
            {
                $project: {
                    'categoryGroupData.categoryId': 1,
                },
            },
            {
                $unwind: '$categoryGroupData',
            },
            {
                $group: {
                    _id: null,
                    categoryIds: { $addToSet: '$categoryGroupData.categoryId' },
                },
            },
            {
                $project: {
                    _id: 0,
                    categoryIds: 1,
                },
            },
            {
                $lookup: {
                    from: QAPC_FORM_CATEGORY,
                    localField: 'categoryIds',
                    foreignField: '_id',
                    as: 'categoryData',
                },
            },
            {
                $project: {
                    'categoryData.categoryName': 1,
                    'categoryData.level': 1,
                    'categoryData._id': 1,
                },
            },
        ]);
        return {
            status: 200,
            message: 'DATA_RECEIVED',
            data: qapcPermissionList?.[0]?.categoryData || [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const approverFormUserStatus = ({ approverList, formCurrentLevel, roleId, userId }) => {
    try {
        let userStatus = PENDING;
        approverList.forEach((approverElement) => {
            if (approverElement.level === formCurrentLevel) {
                approverElement.roleUser.forEach((roleUserElement) => {
                    if (roleUserElement.roleId?.toString() === roleId.toString()) {
                        roleUserElement.userIds?.forEach((userElement) => {
                            if (userElement.userId.toString() === userId.toString()) {
                                userStatus = userElement.status;
                            }
                        });
                    }
                });
            }
        });
        return userStatus;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAllUserApproverList = async ({ uniqueCourseGroupIds, institutionCalenderId }) => {
    try {
        uniqueCourseGroupIds = uniqueCourseGroupIds.map((groupElement) =>
            convertToMongoObjectId(groupElement),
        );
        const qapcRoleUserData = await qapcPermissionSchema.aggregate([
            {
                $match: {
                    'permissions.cfpc.subModuleName': FORM_APPROVER,
                },
            },
            {
                $project: {
                    userId: 1,
                    guestUserId: 1, // Include guestUserId in projection
                    permissions: {
                        cfpc: {
                            $filter: {
                                input: '$permissions.cfpc',
                                as: 'cfpcElement',
                                cond: {
                                    $and: [
                                        { $eq: ['$$cfpcElement.subModuleName', FORM_APPROVER] },
                                        {
                                            $setIsSubset: [
                                                '$$cfpcElement.formCourseGroupId',
                                                uniqueCourseGroupIds,
                                            ],
                                        },
                                        {
                                            $or: [
                                                {
                                                    $and: [
                                                        { $not: '$$cfpcElement.selectedAcademic' },
                                                        {
                                                            $eq: [
                                                                '$$cfpcElement.academicYear',
                                                                ALL,
                                                            ],
                                                        },
                                                    ],
                                                },
                                                {
                                                    $and: [
                                                        {
                                                            $eq: [
                                                                '$$cfpcElement.selectedAcademic',
                                                                true,
                                                            ],
                                                        },
                                                        {
                                                            $gt: [
                                                                {
                                                                    $size: {
                                                                        $ifNull: [
                                                                            '$$cfpcElement.institutionCalendarIds',
                                                                            [],
                                                                        ],
                                                                    },
                                                                },
                                                                0,
                                                            ],
                                                        },
                                                        {
                                                            $in: [
                                                                convertToMongoObjectId(
                                                                    institutionCalenderId,
                                                                ),
                                                                '$$cfpcElement.institutionCalendarIds',
                                                            ],
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                    },
                },
            },
            {
                $addFields: {
                    'permissions.cfpc': {
                        $reduce: {
                            input: '$permissions.cfpc',
                            initialValue: [],
                            in: {
                                $cond: {
                                    if: {
                                        $in: [
                                            {
                                                $concat: [
                                                    {
                                                        $reduce: {
                                                            input: '$$this.formCourseGroupId',
                                                            initialValue: '',
                                                            in: {
                                                                $concat: [
                                                                    '$$value',
                                                                    { $toString: '$$this' },
                                                                    ',',
                                                                ],
                                                            },
                                                        },
                                                    },
                                                    { $toString: '$$this.subModuleName' },
                                                    { $toString: '$$this.approverLevelIndex' },
                                                    { $toString: '$$this.qapcRoleId' },
                                                ],
                                            },
                                            {
                                                $map: {
                                                    input: '$$value',
                                                    as: 'existing',
                                                    in: {
                                                        $concat: [
                                                            {
                                                                $reduce: {
                                                                    input: '$$existing.formCourseGroupId',
                                                                    initialValue: '',
                                                                    in: {
                                                                        $concat: [
                                                                            '$$value',
                                                                            { $toString: '$$this' },
                                                                            ',',
                                                                        ],
                                                                    },
                                                                },
                                                            },
                                                            {
                                                                $toString:
                                                                    '$$existing.subModuleName',
                                                            },
                                                            {
                                                                $toString:
                                                                    '$$existing.approverLevelIndex',
                                                            },
                                                            { $toString: '$$existing.qapcRoleId' },
                                                        ],
                                                    },
                                                },
                                            },
                                        ],
                                    },
                                    then: '$$value',
                                    else: { $concatArrays: ['$$value', ['$$this']] },
                                },
                            },
                        },
                    },
                },
            },
            {
                $match: {
                    'permissions.cfpc': { $ne: [] },
                },
            },
            {
                $project: {
                    userId: 1,
                    guestUserId: 1, // for guestUser
                    cfpcPermissions: '$permissions.cfpc',
                },
            },
            {
                $lookup: {
                    from: USER,
                    localField: 'userId',
                    foreignField: '_id',
                    as: 'userData',
                },
            },
            {
                $lookup: {
                    from: GUEST_USER, // Add lookup for guest users
                    localField: 'guestUserId',
                    foreignField: '_id',
                    as: 'guestUserData',
                },
            },
            // Combine results from both lookups
            {
                $addFields: {
                    userData: {
                        $cond: {
                            if: { $gt: [{ $size: '$userData' }, 0] },
                            then: { $arrayElemAt: ['$userData', 0] },
                            else: { $arrayElemAt: ['$guestUserData', 0] },
                        },
                    },
                },
            },
            // Filter out documents without valid user data
            {
                $match: {
                    userData: { $ne: null },
                },
            },
            // Unwind permissions
            {
                $unwind: '$cfpcPermissions',
            },
            {
                $addFields: {
                    'cfpcPermissions.qapcRoleId': { $toObjectId: '$cfpcPermissions.qapcRoleId' },
                },
            },
            // QAPC role lookup remains unchanged
            {
                $lookup: {
                    from: QAPC_ROLE,
                    localField: 'cfpcPermissions.qapcRoleId',
                    foreignField: '_id',
                    as: 'qapcRoleData',
                },
            },
            {
                $unwind: {
                    path: '$qapcRoleData',
                    preserveNullAndEmptyArrays: true,
                },
            },
            // Grouping stage updated to handle new userData structure
            {
                $group: {
                    _id: '$_id',
                    userData: { $first: '$userData' },
                    cfpcPermissions: {
                        $push: {
                            qapcRoleId: '$cfpcPermissions.qapcRoleId',
                            approverLevelIndex: '$cfpcPermissions.approverLevelIndex',
                            formCourseGroupId: '$cfpcPermissions.formCourseGroupId',
                            qapcRoleName: '$qapcRoleData.roleName',
                        },
                    },
                },
            },
            // Final projection handles both user types
            {
                $project: {
                    userName: '$userData.name',
                    userId: '$userData._id',
                    cfpcPermissions: '$cfpcPermissions',
                },
            },
        ]);

        return { qapcRoleUserData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const splitQualityAssurance = ({
    formApproverLevel,
    formInitiators,
    userActionsList,
    userId,
    roleId,
    roleUserList,
    viewActionId,
}) => {
    try {
        let pendingWithYou = [];
        let pendingWithOthers = [];
        let history = [];
        const uniqueInitiatorId = new Set();
        const uniqueHistory = new Set();
        formInitiators.forEach((initiatorElement) => {
            const formCurrentLevel = initiatorElement.approverDetails.currentLevel;
            const isReSubmit = initiatorElement?.approverDetails?.levelStatus === RE_SUBMIT;
            const isHistory = initiatorElement.approverDetails?.isHistory || false;
            const RPLevelIndex = userActionsList.filter(
                (actionElement) =>
                    String(actionElement.categoryGroupId) ===
                        String(initiatorElement.categoryFormGroupId) &&
                    actionElement?.actionIds?.toString().includes(String(viewActionId)),
            );
            const matchingFormApprover = formApproverLevel.find(
                (approverElement) =>
                    String(approverElement._id) === String(initiatorElement.categoryFormId),
            );
            const totalNumberOfApprover = matchingFormApprover.approvalLevel?.length;
            if (RPLevelIndex?.length) {
                RPLevelIndex.forEach((roleActionElement) => {
                    const approverLevel = roleActionElement.levelIndex;
                    const uniqueFormLevel = `${initiatorElement._id}+${approverLevel}`;
                    const formInitiatorId = initiatorElement._id;
                    const initiatorData = {
                        formInitiatorId,
                        categoryGroupId: initiatorElement.categoryFormGroupId,
                        actionIds: roleActionElement.actionIds,
                        userLevel: approverLevel,
                        updateAt: initiatorElement.updatedAt,
                    };
                    const isNotInUniqueSets =
                        !uniqueInitiatorId.has(uniqueFormLevel) &&
                        !uniqueHistory.has(formInitiatorId);
                    if (isHistory && isNotInUniqueSets) {
                        const isEditStatus = approvedStatusEdit({
                            alreadyApprovedUser: initiatorElement.approverList,
                            userId,
                            roleId,
                            matchingFormApprover,
                            currentLevel: approverLevel,
                        });
                        const userStatus = approverFormUserStatus({
                            approverList: initiatorElement.approverList,
                            formCurrentLevel:
                                initiatorElement.approverDetails?.userLevel || approverLevel,
                            categoryGroupId: initiatorElement.categoryFormGroupId,
                            roleId,
                            userId,
                        });
                        initiatorData.isEditStatus = isEditStatus;
                        initiatorData.currentUserStatus = userStatus;
                        initiatorData.historyStatus =
                            initiatorElement?.approverDetails?.levelStatus;
                        history.push(initiatorData);
                        uniqueInitiatorId.add(uniqueFormLevel);
                        uniqueHistory.add(formInitiatorId);
                    } else if (approverLevel === formCurrentLevel) {
                        const userStatus = approverFormUserStatus({
                            approverList: initiatorElement.approverList,
                            formCurrentLevel,
                            roleId,
                            userId,
                            categoryGroupId: initiatorElement.categoryFormGroupId,
                        });
                        if (
                            userStatus === PENDING &&
                            formCurrentLevel === totalNumberOfApprover &&
                            isNotInUniqueSets
                        ) {
                            const levelWiseSplitUser = getLevelWiseSplitUser({
                                roleUserList,
                            });
                            const userApproverList = formApproverStatus({
                                alreadyApprovedUser: initiatorElement.approverList,
                                currentLevel: approverLevel,
                                allLevelUser: levelWiseSplitUser.uniqueCategoryGroupId,
                                userId,
                                matchingFormApprover,
                                categoryFormGroupId: initiatorElement.categoryFormGroupId,
                            });
                            initiatorData.isLastUser = userApproverList;
                            initiatorData.currentUserStatus = userStatus;
                            pendingWithYou.push(initiatorData);
                            uniqueInitiatorId.add(uniqueFormLevel);
                        } else if (userStatus === PENDING && isNotInUniqueSets) {
                            if (isReSubmit) {
                                initiatorData.currentUserStatus = NOT_APPLICABLE;
                                pendingWithOthers.push(initiatorData);
                            } else {
                                initiatorData.currentUserStatus = userStatus;
                                pendingWithYou.push(initiatorData);
                            }
                            uniqueInitiatorId.add(uniqueFormLevel);
                        } else if (userStatus != PENDING && isNotInUniqueSets) {
                            const isEditStatus = approvedStatusEdit({
                                alreadyApprovedUser: initiatorElement.approverList,
                                userId,
                                roleId,
                                matchingFormApprover,
                                currentLevel: approverLevel,
                            });
                            initiatorData.currentUserStatus = userStatus;
                            initiatorData.isEditStatus = isEditStatus;
                            if (userStatus === RE_SUBMIT) {
                                pendingWithYou.push(initiatorData);
                            } else {
                                pendingWithOthers.push(initiatorData);
                            }
                            uniqueInitiatorId.add(uniqueFormLevel);
                        }
                    } else if (approverLevel != formCurrentLevel && isNotInUniqueSets) {
                        matchingFormApprover?.approvalLevel.forEach((approvedFormElement) => {
                            //check the last level last user
                            if (
                                isNotInUniqueSets &&
                                approvedFormElement.levelNumber === approverLevel
                            ) {
                                const userStatus = approverFormUserStatus({
                                    approverList: initiatorElement.approverList,
                                    formCurrentLevel: approverLevel,
                                    categoryGroupId: initiatorElement.categoryFormGroupId,
                                    roleId,
                                    userId,
                                });
                                //check currentLevel not equal to approver level
                                if (
                                    userStatus === PENDING &&
                                    (approvedFormElement.allowToSkipping ||
                                        approvedFormElement.allowToOverwrite)
                                ) {
                                    initiatorData.currentUserStatus = userStatus;
                                    if (
                                        approverLevel === matchingFormApprover.approvalLevel?.length
                                    ) {
                                        const levelWiseSplitUser = getLevelWiseSplitUser({
                                            roleUserList,
                                        });
                                        const userApproverList = formApproverStatus({
                                            alreadyApprovedUser: initiatorElement.approverList,
                                            currentLevel: approverLevel,
                                            allLevelUser: levelWiseSplitUser.uniqueCategoryGroupId,
                                            userId,
                                            matchingFormApprover,
                                            categoryFormGroupId:
                                                initiatorElement.categoryFormGroupId,
                                        });
                                        initiatorData.isLastUser = userApproverList;
                                        initiatorData.allowToSkipping =
                                            approvedFormElement.allowToSkipping;
                                        initiatorData.allowToOverwrite =
                                            approvedFormElement.allowToOverwrite;
                                        if (isReSubmit) {
                                            initiatorData.currentUserStatus = NOT_APPLICABLE;
                                            pendingWithOthers.push(initiatorData);
                                        } else {
                                            pendingWithOthers.push(initiatorData);
                                            uniqueInitiatorId.add(uniqueFormLevel);
                                        }
                                    }
                                    //first level reject move to last level
                                    else if (formCurrentLevel >= approverLevel) {
                                        initiatorData.currentUserStatus = NOT_APPLICABLE;
                                        pendingWithOthers.push(initiatorData);
                                        uniqueInitiatorId.add(uniqueFormLevel);
                                    } else if (!isReSubmit) {
                                        initiatorData.allowToSkipping =
                                            approvedFormElement.allowToSkipping;
                                        initiatorData.allowToOverwrite =
                                            approvedFormElement.allowToOverwrite;
                                        pendingWithOthers.push(initiatorData);
                                        uniqueInitiatorId.add(uniqueFormLevel);
                                    }
                                } else {
                                    if (userStatus != PENDING) {
                                        const isEditStatus = approvedStatusEdit({
                                            alreadyApprovedUser: initiatorElement.approverList,
                                            userId,
                                            roleId,
                                            matchingFormApprover,
                                            currentLevel: approverLevel,
                                        });
                                        initiatorData.currentUserStatus = userStatus;
                                        initiatorData.isEditStatus = isEditStatus;
                                        pendingWithOthers.push(initiatorData);
                                    } else {
                                        initiatorData.currentUserStatus = SKIPPED;
                                        pendingWithOthers.push(initiatorData);
                                    }
                                    uniqueInitiatorId.add(uniqueFormLevel);
                                }
                            }
                        });
                    }
                });
            }
        });
        if (pendingWithYou?.length) {
            pendingWithYou = updatedWiseSort({ formList: pendingWithYou });
        }
        if (pendingWithOthers?.length) {
            pendingWithOthers = updatedWiseSort({ formList: pendingWithOthers });
        }
        if (history?.length) {
            history = updatedWiseSort({ formList: history });
        }
        return {
            status: 200,
            message: 'DATA_RECEIVED',
            data: {
                pendingWithYou,
                pendingWithOthers,
                history,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const allUserUpdateStatus = ({ approverList, RPLevelUserList, currentLevel }) => {
    try {
        const matchingApproverList = approverList.find(
            (approverElement) => approverElement.level === currentLevel,
        );
        const matchingRPLevelUserList = RPLevelUserList.find(
            (levelUserList) => levelUserList.approverLevelIndex === currentLevel,
        );
        let moveToNextLevel = false;
        //new all flow check
        const shouldMoveToNextLevel = matchingRPLevelUserList?.roleIds.some((roleIdElement) => {
            const matchingRoleUser = matchingApproverList?.roleUser.find(
                (roleUserElement) =>
                    String(roleUserElement.roleId) === String(roleIdElement.qapcRoleId),
            );
            if (!matchingRoleUser) {
                moveToNextLevel = false;
                return true;
            }
            const allUserIdsMatch = roleIdElement.userIds.every((rpUserElement) =>
                matchingRoleUser.userIds.some(
                    (approverUser) => String(rpUserElement.userId) === String(approverUser.userId),
                ),
            );
            if (!allUserIdsMatch) {
                moveToNextLevel = false;
                return true;
            }
            moveToNextLevel = true;
            return false;
        });

        if (!shouldMoveToNextLevel) {
            moveToNextLevel = true;
        }
        return moveToNextLevel;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const minimumUserUpdateStatus = ({
    approverList,
    RPLevelUserList,
    currentLevel,
    minimumUser,
    roleId,
}) => {
    try {
        const matchingApproverList = approverList.find(
            (approverElement) => approverElement.level === currentLevel,
        );
        const matchingRPLevelUserList = RPLevelUserList.find(
            (levelUserList) => levelUserList.approverLevelIndex === currentLevel,
        );
        let moveToNextLevel = false;
        let numberOfRole = 0;
        matchingRPLevelUserList?.roleIds.forEach((roleIdElement) => {
            const matchingRoleUser = matchingApproverList?.roleUser.find(
                (roleUserElement) =>
                    String(roleUserElement.roleId) === String(roleIdElement.qapcRoleId),
            );
            const rpUserIdsCount = roleIdElement?.userIds.length;
            const approverUserIdsCount = matchingRoleUser?.userIds?.length || 0;
            if (
                rpUserIdsCount < minimumUser &&
                rpUserIdsCount - approverUserIdsCount &&
                String(roleIdElement.qapcRoleId) === String(roleId)
            ) {
                numberOfRole += 1;
            } else if (approverUserIdsCount >= minimumUser && rpUserIdsCount >= minimumUser) {
                numberOfRole += 1;
            } else if (rpUserIdsCount <= minimumUser && rpUserIdsCount === approverUserIdsCount) {
                numberOfRole += 1;
            }
        });
        if (numberOfRole === matchingRPLevelUserList?.roleIds?.length) {
            moveToNextLevel = true;
        }
        return moveToNextLevel;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const rejectedFormMovedNextLevel = ({ currentLevel, formApproverLevel }) => {
    try {
        let isHistory = true;
        if (currentLevel < formApproverLevel.length) {
            for (let i = currentLevel; i < formApproverLevel.length; i++) {
                if (formApproverLevel[i].allowToOverwrite) {
                    currentLevel = formApproverLevel[i].levelNumber;
                    isHistory = false;
                    break;
                }
            }
        }
        return { currentLevel, isHistory };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateFormApproverDetail = async ({
    formInitiatorApprover,
    formApproverLevel,
    formInitiatorIds,
    userLevel,
    status,
    _user_id,
    role_id,
    RPLevelUserList,
    reason,
    isSkipped,
}) => {
    try {
        const approverList = formInitiatorApprover.approverList?.[0]?.level
            ? formInitiatorApprover.approverList
            : [];
        const rejectionDetails = formInitiatorApprover?.rejectionDetails || [];
        const lastApproverStatus = formInitiatorApprover?.approverDetails?.levelStatus;
        const resubmissionLog = formInitiatorApprover?.resubmissionLog || [];
        const approverDetails = {
            currentLevel: formInitiatorApprover?.approverDetails?.currentLevel,
            levelStatus: status,
            startDate: new Date(),
            ...(isSkipped && { isSkipped }),
        };
        let levelStartTime = formInitiatorApprover?.levelStartTime;
        let formInitiatedStatus = IN_REVIEW;
        if (
            userLevel >= formInitiatorApprover?.approverDetails?.currentLevel ||
            status === REVOKED
        ) {
            let levelExists = false;
            if (approverList?.length) {
                approverList.forEach((approverElement, approverIndex) => {
                    if (approverElement?.level === userLevel) {
                        levelExists = true;
                        let roleUserExists = false;
                        approverElement.levelStatus = status;
                        approverElement.isSkipped =
                            isSkipped || approverElement?.isSkipped || false;
                        approverElement.roleUser.forEach((roleUserElement, roleUserIndex) => {
                            if (roleUserElement?.roleId.toString() === role_id.toString()) {
                                roleUserExists = true;
                                if (status === REVOKED) {
                                    roleUserElement.userIds = roleUserElement.userIds.filter(
                                        (userElement) =>
                                            userElement.userId.toString() !== _user_id.toString(),
                                    );
                                    if (roleUserElement.userIds.length === 0) {
                                        approverElement.roleUser.splice(roleUserIndex, 1);
                                    }
                                } else {
                                    const existingUserIndex = roleUserElement.userIds.findIndex(
                                        (userElement) =>
                                            userElement.userId.toString() === _user_id.toString(),
                                    );
                                    if (existingUserIndex !== -1) {
                                        roleUserElement.userIds[existingUserIndex].status = status;
                                        roleUserElement.userIds[existingUserIndex].statusDate =
                                            new Date();
                                        if (reason) {
                                            roleUserElement.userIds[existingUserIndex].reason =
                                                reason;
                                        }
                                    } else {
                                        roleUserElement.userIds.push({
                                            userId: convertToMongoObjectId(_user_id),
                                            status,
                                            ...(reason && { reason }),
                                            statusDate: new Date(),
                                        });
                                    }
                                }
                            }
                        });
                        if (!roleUserExists && status !== REVOKED) {
                            approverElement.roleUser.push({
                                roleId: convertToMongoObjectId(role_id),
                                userIds: [
                                    {
                                        userId: convertToMongoObjectId(_user_id),
                                        status,
                                        ...(reason && { reason }),
                                        statusDate: new Date(),
                                    },
                                ],
                            });
                        }
                        if (approverElement.roleUser.length === 0) {
                            approverList.splice(approverIndex, 1);
                        }
                    }
                });
            }
            if (!levelExists && status !== REVOKED) {
                approverList.push({
                    level: userLevel,
                    levelStatus: status,
                    ...(isSkipped && { isSkipped }),
                    updateLevelDate: new Date(),
                    roleUser: [
                        {
                            roleId: convertToMongoObjectId(role_id),
                            userIds: [
                                {
                                    userId: convertToMongoObjectId(_user_id),
                                    status,
                                    ...(reason && { reason }),
                                    statusDate: new Date(),
                                },
                            ],
                        },
                    ],
                });
            }
        }
        //update the current level
        const userFormApproverLevel = formApproverLevel?.[0]?.approvalLevel.find(
            (approverLevelElement) => approverLevelElement.levelNumber === userLevel,
        );
        if (userFormApproverLevel && status != REVOKED) {
            //check all user
            let updatedUserStatus;
            if (userFormApproverLevel.requireAll) {
                updatedUserStatus = allUserUpdateStatus({
                    approverList,
                    RPLevelUserList,
                    currentLevel: userLevel,
                });
            } else if (userFormApproverLevel.requireMinium) {
                updatedUserStatus = minimumUserUpdateStatus({
                    approverList,
                    RPLevelUserList,
                    currentLevel: userLevel,
                    minimumUser: userFormApproverLevel?.minimum_user,
                    roleId: role_id,
                });
            }
            //update current level status
            if (!updatedUserStatus) {
                approverDetails.currentLevel =
                    approverDetails.currentLevel <= userLevel
                        ? userLevel
                        : approverDetails.currentLevel;
                approverDetails.levelStatus = status;
            } else {
                const isLastLevel =
                    userFormApproverLevel.levelNumber >= formApproverLevel[0].approvalLevel.length;
                approverDetails.currentLevel = isLastLevel
                    ? userFormApproverLevel.levelNumber
                    : userFormApproverLevel.levelNumber + 1;
                approverDetails.levelStatus = isLastLevel ? status : PENDING;
                approverDetails.isHistory =
                    isLastLevel && [APPROVED, REJECTED, PUBLISHED].includes(status);
                if (
                    levelStartTime?.length &&
                    !levelStartTime.some(
                        (levelElement) =>
                            levelElement.level === userFormApproverLevel.levelNumber + 1,
                    )
                ) {
                    levelStartTime.push({
                        level: userFormApproverLevel.levelNumber + 1,
                        startDate: new Date(),
                    });
                }
            }
        }
        switch (status) {
            case RE_SUBMIT: {
                approverDetails.currentLevel = userLevel;
                approverDetails.levelStatus = status;
                formInitiatedStatus = RE_SUBMIT;
                resubmissionLog.push({
                    level: userLevel,
                    roleId: convertToMongoObjectId(role_id),
                    userId: convertToMongoObjectId(_user_id),
                    reason,
                    resubmissionDate: new Date(),
                });
                break;
            }
            case REJECTED: {
                rejectionDetails.push({
                    level: userLevel,
                    roleId: convertToMongoObjectId(role_id),
                    userId: convertToMongoObjectId(_user_id),
                    reason,
                });
                isMoveNextLevel = rejectedFormMovedNextLevel({
                    currentLevel: userLevel,
                    formApproverLevel: formApproverLevel?.[0]?.approvalLevel,
                });
                approverDetails.isHistory = isMoveNextLevel.isHistory;
                approverDetails.currentLevel = isMoveNextLevel.currentLevel;
                approverDetails.isSkipped = isSkipped || approverDetails?.isSkipped || false;
                break;
            }
            case REVOKED: {
                const lastApprover = approverList[approverList.length - 1];
                const currentUserLevel = lastApprover?.level || 1;

                if (lastApproverStatus === RE_SUBMIT) {
                    resubmissionLog.pop();
                }
                if (currentUserLevel === 1 && !approverList?.length) {
                    approverDetails.currentLevel = currentUserLevel;
                    approverDetails.levelStatus = PENDING;
                } else if (currentUserLevel === userLevel) {
                    approverDetails.currentLevel = userLevel;
                } else {
                    let updateRevokeStatus;
                    if (userFormApproverLevel.requireAll) {
                        updateRevokeStatus = allUserUpdateStatus({
                            approverList,
                            RPLevelUserList,
                            currentLevel: currentUserLevel,
                        });
                        if (!updateRevokeStatus && lastApprover?.levelStatus === REJECTED) {
                            approverDetails.currentLevel = userLevel;
                            approverDetails.levelStatus = PENDING;
                        } else {
                            approverDetails.currentLevel = updateRevokeStatus
                                ? currentUserLevel + 1
                                : currentUserLevel;
                            approverDetails.levelStatus = updateRevokeStatus ? PENDING : status;
                            if (
                                updateRevokeStatus &&
                                !levelStartTime.some(
                                    (levelElement) =>
                                        String(levelElement.level) ===
                                        String(userFormApproverLevel.levelNumber + 1),
                                )
                            ) {
                                levelStartTime.push({
                                    level: userFormApproverLevel.levelNumber + 1,
                                    startDate: new Date(),
                                });
                            }
                        }
                    } else if (userFormApproverLevel.requireMinium) {
                        updateRevokeStatus = minimumUserUpdateStatus({
                            approverList,
                            RPLevelUserList,
                            currentLevel: currentUserLevel,
                            minimumUser: userFormApproverLevel?.minimum_user,
                            roleId: role_id,
                        });
                    }
                    if (!updateRevokeStatus && lastApprover?.levelStatus === REJECTED) {
                        approverDetails.currentLevel = userLevel;
                        approverDetails.levelStatus = PENDING;
                    } else {
                        approverDetails.currentLevel = updateRevokeStatus
                            ? currentUserLevel + 1
                            : currentUserLevel;
                        approverDetails.levelStatus = updateRevokeStatus ? PENDING : status;
                        if (
                            updateRevokeStatus &&
                            !levelStartTime.some(
                                (levelElement) =>
                                    String(levelElement.level) ===
                                    String(userFormApproverLevel.levelNumber + 1),
                            )
                        ) {
                            levelStartTime.push({
                                level: userFormApproverLevel.levelNumber + 1,
                                startDate: new Date(),
                            });
                        }
                    }
                }
                approverDetails.isHistory = false;
                formInitiatedStatus = IN_REVIEW;
                break;
            }
            default:
                break;
        }
        //update formInitiator status
        if (approverDetails?.isHistory) {
            formInitiatedStatus = status;
            approverDetails.userLevel = userLevel;
        }
        //level time status
        levelStartTime = levelStartTime?.filter(
            (levelElement) => levelElement.level <= approverDetails.currentLevel,
        );
        //update approver List
        await qapcFormInitiatorSchema.updateOne(
            {
                _id: {
                    $in: formInitiatorIds.map((formIdElement) =>
                        convertToMongoObjectId(formIdElement),
                    ),
                },
            },
            {
                $set: {
                    approverList,
                    approverDetails,
                    rejectionDetails,
                    ...(formInitiatedStatus && { status: formInitiatedStatus }),
                    resubmissionLog,
                    levelStartTime,
                },
            },
        );
        return {
            status: 200,
            message: 'UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getViewActionId = async ({ actionName }) => {
    try {
        return await qapcActionSchema
            .find(
                {
                    actionName,
                },
                {
                    _id: -1,
                },
            )
            .lean();
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUniqueGroupIdsAndFormDetails = (formData) => {
    const groupIds = [];
    const formNames = [];
    const formIds = [];
    const institutionCalenderIds = [];
    formData.forEach((formElement) => {
        if (formElement.categoryFormGroupId) groupIds.push(formElement.categoryFormGroupId);
        if (formElement.formName) formNames.push(formElement.formName);
        if (formElement._id) formIds.push(formElement._id);
        if (formElement.formCalenderIds && formElement.formCalenderIds?.length) {
            formElement.formCalenderIds.forEach((fc) => {
                if (fc.institutionCalenderId) institutionCalenderIds.push(fc.institutionCalenderId);
            });
        }
    });

    return {
        groupIds: [...new Set(groupIds.map((groupIdElement) => groupIdElement.toString()))],
        formNames,
        formIds,
        institutionCalenderIds,
    };
};

const getPermissionEmails = async (groupIds, subModules, qapcRoleId) => {
    const qapcPermissionData = await qapcPermissionSchema
        .find(
            {
                'permissions.cfpc': {
                    $elemMatch: {
                        formCourseGroupId: {
                            $in: groupIds.map((id) => convertToMongoObjectId(id)),
                        },
                        subModuleName: Array.isArray(subModules) ? { $in: subModules } : subModules,
                        isDeleted: false,
                        ...(qapcRoleId && { qapcRoleId: convertToMongoObjectId(qapcRoleId) }),
                    },
                },
            },
            { userId: 1, guestUserId: 1 },
        )
        .populate({ path: 'userId', select: { email: 1, name: 1 } })
        .populate({ path: 'guestUserId', model: GUEST_USER, select: { email: 1, name: 1 } })
        .lean();
    let emailList = qapcPermissionData
        .map((userElement) => {
            if (userElement.userId && userElement.userId.email) return userElement.userId.email;
            if (userElement.guestUserId && userElement.guestUserId.email)
                return userElement.guestUserId.email;
            return null;
        })
        .filter(Boolean);
    emailList = [...new Set(emailList)];
    return { users: qapcPermissionData, emails: emailList };
};

const removeCurrentUserEmail = (emailList, users, currentUserId) => {
    const user = users.find(
        (userElement) =>
            (userElement.userId && String(userElement.userId._id) === String(currentUserId)) ||
            (userElement.guestUserId &&
                String(userElement.guestUserId._id) === String(currentUserId)),
    );
    if (!user) return emailList;
    const emailToRemove = user.userId?.email || user.guestUserId?.email;
    return emailList.filter((email) => email !== emailToRemove);
};

const sendFormStatusUpdateEmail = async ({ formInitiatorIds, status, _user_id }) => {
    const formData = await qapcFormInitiatorSchema
        .find(
            {
                _id: { $in: formInitiatorIds.map((id) => convertToMongoObjectId(id)) },
            },
            {
                categoryFormGroupId: 1,
                formName: 1,
                _id: 1,
                formCalenderIds: 1,
                submissionDate: 1,
            },
        )
        .lean();
    const { groupIds, formNames } = getUniqueGroupIdsAndFormDetails(formData);
    const { users, emails } = await getPermissionEmails(groupIds, [
        FORM_APPROVER,
        FORM_INITIATOR,
        'QA Dashboard',
    ]);
    const emailList = removeCurrentUserEmail(emails, users, _user_id);
    if (emailList.length) {
        const form = formData[0] || {};
        const toEmails = emailList.join(', ');
        const initiatorDetail = users.find(
            (userElement) =>
                (userElement.userId && String(userElement.userId._id) === String(_user_id)) ||
                (userElement.guestUserId &&
                    String(userElement.guestUserId._id) === String(_user_id)),
        );
        const approverName = initiatorDetail?.userId?.email || initiatorDetail?.guestUserId?.email;
        const subject = `Action Taken on Submitted Form - ${formNames.join(', ')}`;
        const body = `<p>Dear ${toEmails},</p>
         <p>The form you submitted has been <b>${status} by ${
            approverName || 'the approver'
        }</b></p>
         <h4>Form Details:</h4>
         <ul>
            <li><b>Form Title:</b> ${form.formName}</li>
            <li><b>Action Taken:</b> ${status}</li>
            <li><b>Action By:</b> ${approverName}</li>
            <li><b>Date & Time:</b> ${form.submissionDate}</li>
         </ul>
        Feel free to contact the approver directly if clarification is needed.`;
        await send_email(emailList, subject, body);
    }
};

const getUsersByCategoryId = async ({ categoryId, qapcRoleId, subModuleName }) => {
    try {
        const formCourseGroups = await formCourseGroupSchema
            .find(
                {
                    categoryId: convertToMongoObjectId(categoryId),
                    isActive: true,
                    isDeleted: false,
                },
                { _id: 1 },
            )
            .lean();
        if (!formCourseGroups.length) {
            return { status: 404, message: 'NO_FORM_COURSE_GROUPS_FOUND' };
        }
        const groupIds = formCourseGroups.map((groupElement) => groupElement._id);
        const qapcPermissionData = await qapcPermissionSchema
            .find(
                {
                    'permissions.cfpc': {
                        $elemMatch: {
                            formCourseGroupId: {
                                $in: groupIds.map((groupElement) =>
                                    convertToMongoObjectId(groupElement),
                                ),
                            },
                            subModuleName:
                                subModuleName === FORM_INITIATOR_LABEL
                                    ? FORM_INITIATOR
                                    : FORM_APPROVER,
                            isDeleted: false,
                            qapcRoleId: convertToMongoObjectId(qapcRoleId),
                        },
                    },
                },
                { userId: 1 },
            )
            .populate({ path: 'userId', select: { email: 1, name: 1, _id: 1 } })
            .lean();
        const users = qapcPermissionData
            .map((userElement) => {
                if (userElement.userId) {
                    return {
                        _id: userElement.userId._id,
                        email: userElement.userId.email,
                        name: userElement.userId.name,
                    };
                }
                return null;
            })
            .filter(Boolean);

        return {
            status: 200,
            message: 'USERS_RETRIEVED',
            data: {
                users,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getFormApproverLevel,
    getFormInitiatedIds,
    splitQualityAssurance,
    getFormInitiatorList,
    checkApproverLevel,
    getApproverUserList,
    getFormInitiatorApprover,
    updateFormApproverDetail,
    getAllUserApproverList,
    splitLevelBasedUser,
    getLevelUserList,
    getLevelWiseSplitUser,
    getApproverRoleUserList,
    getApprovedUserFormList,
    updateLevelWiseStatus,
    getApproverCategory,
    getViewActionId,
    updatedWiseSort,
    sendFormStatusUpdateEmail,
    getUniqueGroupIdsAndFormDetails,
    getPermissionEmails,
    removeCurrentUserEmail,
    getUsersByCategoryId,
};
