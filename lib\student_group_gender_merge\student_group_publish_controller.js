const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const { INSTITUTION_NAME } = require('../utility/util_keys');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const institution = require('mongoose').model(constant.INSTITUTION);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
const course = require('mongoose').model(constant.DIGI_COURSE);
const userRole = require('mongoose').model(constant.ROLE_ASSIGN);
const { updateStudentGroupFlatCacheData } = require('./student_group_services');
const { usersCoursesRedisCacheRemove } = require('../../service/redisCache.service');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
exports.group_publish = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );

        const institution_calendar_data = await base_control.get(
            institution_calendar,
            { _id: ObjectId(student_group_check.data._institution_calendar_id), isDeleted: false },
            { calendar_name: 1 },
        );
        if (!institution_calendar_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_INSTITUTION_CALENDAR'),
                        req.t('UNABLE_TO_FIND_INSTITUTION_CALENDAR'),
                    ),
                );
        const student_details = [];
        let ids = [];
        const student_datas = [];
        if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
            for (setting of student_group_check.data.groups[student_group_row]
                .rotation_group_setting) {
                if (
                    (req.query.mode
                        ? req.query.mode === setting.gender
                        : setting.gender === req.body.gender) &&
                    setting._student_ids.length !== 0
                ) {
                    student_details.push({
                        group_no: setting.group_no,
                        gender: setting.gender,
                        student_ids: setting._student_ids,
                    });
                    ids = ids.concat(setting._student_ids);
                }
            }
        } else {
            for (setting of student_group_check.data.groups[student_group_row].group_setting) {
                for (setting_group of setting.groups) {
                    if (
                        (req.query.mode
                            ? req.query.mode === setting.gender
                            : setting.gender === req.body.gender) &&
                        setting_group._student_ids.length !== 0
                    ) {
                        student_details.push({
                            group_no: setting_group.group_no,
                            gender: setting.gender,
                            student_ids: setting_group._student_ids,
                        });
                        ids = ids.concat(setting_group._student_ids);
                    }
                }
            }
        }
        const student_status = student_group_check.data.groups[student_group_row].students;
        const level_name =
            student_group_check.data.groups[student_group_row].rotation === 'yes'
                ? 'Rotation'
                : 'Foundation';
        const staffsIds = [];
        // Getting Program Vice Dean, Department Chairman
        const userRoleData = await base_control.get_list(
            userRole,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
                'roles.program._program_id': ObjectId(student_group_check.data.master._program_id),
                'roles.isAdmin': true,
            },
            { _user_id: 1 },
        );
        if (!userRoleData.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('VICE_DEAN_COURSE_COORDINATOR_NOT_FOUND'),
                        req.t('VICE_DEAN_COURSE_COORDINATOR_NOT_FOUND'),
                    ),
                );
        for (roleUser of userRoleData.data) {
            ids.push(roleUser._user_id);
            staffsIds.push(roleUser._user_id);
        }
        const courseIds = student_group_check.data.groups[student_group_row].courses.map(
            (ele) => ele._course_id,
        );
        //Course Details
        const courseDetails = await base_control.get_list(
            course,
            {
                _id: { $in: courseIds },
                'coordinators.term': req.body.batch,
                isDeleted: false,
            },
            {
                course_name: 1,
                course_code: 1,
                coordinators: 1,
            },
        );
        if (courseDetails.status) {
            for (courseDatas of courseDetails.data) {
                const staffData = courseDatas.coordinators.find(
                    (ele) => ele.term === req.body.batch,
                );
                if (staffData) {
                    ids.push(staffData._user_id);
                    staffsIds.push(staffData._user_id);
                }
            }
        }
        // return res.send({ courseIds, userRoleData, ids });
        const user_data = await base_control.get_list(
            user,
            { _id: ids, /* user_type: constant.EVENT_WHOM.STUDENT, */ isDeleted: false },
            { name: 1, user_id: 1, email: 1, mobile: 1 },
        );
        for (student of student_details) {
            student.student_ids.forEach((element) => {
                const pub_status_loc = student_status.findIndex(
                    (i) => i._student_id.toString() === element.toString(),
                );
                if (
                    pub_status_loc !== -1 &&
                    student_status[pub_status_loc].master_group_status === 'pending'
                ) {
                    const data =
                        user_data.data[
                            user_data.data.findIndex((i) => i._id.toString() === element.toString())
                        ];
                    student_datas.push({
                        group_no: student.group_no,
                        _id: element,
                        name: data.name,
                        user_id: data.user_id,
                        gender: student.gender,
                        email: data.email,
                        mobile: data.mobile || '',
                    });
                }
            });
        }
        const email_message =
            '<p>Dear v_name,<br>' +
            common_fun.emailGreetingContent() +
            '<br>' +
            'This email is to inform  that your ' +
            level_name +
            ' year group for the academic year <b>v_academic</b> has been allocated as <b>' +
            level_name +
            ' group group_no</b>. For more details, kindly visit the Academic Affairs Office.<br><br>' +
            'Thank you, <br>' +
            common_fun.emailRegardsContent() +
            '</p>';
        const sms_message =
            'Your ' +
            level_name +
            ' group is allocated. Please check your registered email for details. For any query, visit Academic Affairs Office - ' +
            INSTITUTION_NAME +
            '';
        const bulkUpdateObj = [];
        for (std of student_datas) {
            const name = std.name.middle
                ? std.name.first + ' ' + std.name.middle + ' ' + std.name.last
                : std.name.first + ' ' + std.name.last;
            const replace_data = {
                v_name: name,
                v_academic: institution_calendar_data.data.calendar_name,
                group_no: std.group_no,
            };
            const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
            const message = email_message.replace(re, (matched) => replace_data[matched]);
            if (req.body.notify_via.indexOf('email') !== -1) {
                if (std.email !== undefined && std.email !== '')
                    await common_fun.send_email(
                        std.email,
                        'DigiClass Alert - Your ' + level_name + ' Groups Allocation',
                        message,
                    );
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const message_data = 'DigiClass - ' + sms_message;
                if (std.mobile !== undefined && std.mobile !== '')
                    await common_fun.send_sms(std.mobile, message_data);
            }
            if (req.body.notify_via.indexOf('digischeduler') !== -1) {
                //Send Notification to DigiScheduler FCM
            }
            if (req.body.notify_via.indexOf('digiclass') !== -1) {
                //Send Notification to DigiScheduler FCM
            }
            // const objs = {
            //     $set: {
            //         'groups.$[i].students.$[j].master_group_status': 'published',
            //     },
            // };
            // const filter = {
            //     arrayFilters: [
            //         { 'i.term': req.body.batch, 'i.level': req.body.level },
            //         { 'j._student_id': std._id },
            //     ],
            // };
            // await base_control.update_condition_array_filter(student_group, query, objs, filter);
            bulkUpdateObj.push({
                updateOne: {
                    filter: query,
                    update: {
                        $set: {
                            'groups.$[i].students.$[j].master_group_status': 'published',
                        },
                    },
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._student_id': ObjectId(std._id) },
                    ],
                },
            });
        }
        if (bulkUpdateObj.length !== 0)
            console.log(await base_control.bulk_write(student_group, bulkUpdateObj));
        // Pushing Notification to Staff
        if (staffsIds.length) {
            const users = [];
            for (userElement of staffsIds) {
                const userData = user_data.data.find(
                    (i) => i._id.toString() === userElement.toString(),
                );
                if (userData) users.push(userData);
            }
            const term = req.body.batch.charAt(0).toUpperCase() + req.body.batch.slice(1);
            const email_message =
                '<p>Dear,' +
                '<br>' +
                common_fun.emailGreetingContent() +
                '<br>' +
                'This email is to inform that your ' +
                level_name +
                ' year group for the academic year <b>' +
                institution_calendar_data.data.calendar_name +
                '</b> has been allocated as <b>' +
                level_name +
                ' - ' +
                req.body.level +
                ' - (' +
                term +
                ') Student Group<br><br>Thank You' +
                common_fun.emailRegardsContent() +
                '</p>';
            const sms_message =
                'DigiClass - You have published ' +
                level_name +
                ' year group for the academic year <b>' +
                institution_calendar_data.data.calendar_name +
                '</b> has been allocated as <b>' +
                level_name +
                ' - ' +
                req.body.level +
                ' - (' +
                term +
                ' Student Group - ' +
                INSTITUTION_NAME +
                '';
            if (req.body.notify_via.indexOf('email') !== -1 && users.length) {
                await common_fun.send_email(
                    users.map((ele) => ele.email),
                    'DigiClass Alert',
                    email_message,
                );
            }
            if (req.body.notify_via.indexOf('sms') !== -1 && users.length) {
                await common_fun.send_sms(
                    users.map((ele) => ele.mobile),
                    sms_message,
                );
            }
        }
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    '' + level_name + ' Group Successfully Published',
                    ' ' + level_name + ' Group Successfully Published',
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.course_publish = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_ind = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        let master_group_ind = -1;
        if (student_group_check.data.groups[student_group_row].group_mode === 'course') {
            master_group_ind = student_group_check.data.groups[student_group_row].courses[
                course_ind
            ].setting.findIndex((i) =>
                req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
            );
        } else {
            master_group_ind = student_group_check.data.groups[student_group_row].courses[
                course_ind
            ].setting.findIndex(
                (i) =>
                    (req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender) &&
                    i._group_no.toString() === req.body.group_no.toString(),
            );
        }
        if (master_group_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_GROUP'),
                        req.t('UNABLE_TO_FIND_GROUP'),
                    ),
                );
        const course_session_setting =
            student_group_check.data.groups[student_group_row].courses[course_ind].setting[
                master_group_ind
            ].session_setting;
        let ids = [];
        const student_datas = [];
        for (setting of course_session_setting) {
            for (setting_group of setting.groups) {
                if (setting_group._student_ids.length !== 0) {
                    ids = ids.concat(setting_group._student_ids);
                }
            }
        }
        const session_setting = course_session_setting;
        const temps = [];
        const student = [];
        ids.forEach((element) => {
            if (temps.indexOf(element.toString()) === -1) {
                temps.push(element.toString());
            }
        });
        ids = temps;
        ids.forEach((master_group_element) => {
            const temp =
                student_group_check.data.groups[student_group_row].students[
                    student_group_check.data.groups[student_group_row].students.findIndex(
                        (i) => i._student_id.toString() === master_group_element.toString(),
                    )
                ];
            if (temp) {
                const course_status = temp.course_group_status.findIndex(
                    (i) => i._course_id.toString() === req.body._course_id.toString(),
                );
                if (
                    course_status !== -1 &&
                    temp.course_group_status[course_status].status === 'pending'
                ) {
                    const data = {};
                    let course_session_index = -1;
                    const session_del = [];
                    session_setting.forEach((session_element) => {
                        session_element.groups.forEach((group_element) => {
                            if (
                                group_element._student_ids.findIndex(
                                    (i) => i.toString() === master_group_element.toString(),
                                ) !== -1
                            ) {
                                course_session_index = group_element.group_no;
                            }
                        });
                        session_del.push({
                            delivery: session_element.session_type.toString(),
                            group: course_session_index !== -1 ? course_session_index : '-',
                        });
                    });
                    data.delivery = session_del;
                    data.course_name =
                        student_group_check.data.groups[student_group_row].courses[
                            course_ind
                        ].course_name;
                    data.course_no =
                        student_group_check.data.groups[student_group_row].courses[
                            course_ind
                        ].course_no;
                    data._student_id = temp._student_id;
                    data.gender = temp.gender;
                    student.push(data);
                }
            }
        });
        //Course Details
        const courseDetails = await base_control.get(
            course,
            {
                _id: ObjectId(req.body._course_id),
                isDeleted: false,
            },
            {
                course_name: 1,
                course_code: 1,
                coordinators: 1,
            },
        );
        const staffData = courseDetails.data.coordinators.find(
            (ele) => ele.term === req.body.batch,
        );
        if (staffData) ids.push(staffData._user_id);
        // return res.send(courseDetails);
        const user_data = await base_control.get_list(
            user,
            { _id: ids, /* user_type: constant.EVENT_WHOM.STUDENT, */ isDeleted: false },
            { name: 1, user_id: 1, email: 1, mobile: 1 },
        );
        for (std of student) {
            const data =
                user_data.data[
                    user_data.data.findIndex((i) => i._id.toString() === std._student_id.toString())
                ];
            student_datas.push({
                delivery: std.delivery,
                _id: std._student_id,
                name: data.name,
                user_id: data.user_id,
                gender: std.gender,
                email: data.email,
                mobile: data.mobile || '',
                course_name: std.course_name,
                course_no: std.course_no,
            });
        }
        const email_message =
            '<p>Dear v_name,<br>' +
            common_fun.emailGreetingContent() +
            '<br>' +
            'This email is to inform that your <b>course_code</b> course groups have been allocated as follows:<br>' +
            'course_delivery<br>' +
            'For more details, kindly visit the Academic Affairs Office.<br><br>Thank you, <br>' +
            common_fun.emailRegardsContent() +
            '</p>';
        const sms_message =
            'Your course group for ' +
            student_group_check.data.groups[student_group_row].courses[course_ind].course_name +
            ' - ' +
            student_group_check.data.groups[student_group_row].courses[course_ind].course_no +
            ' is allocated. Please check your registered email for details. For any query, contact course coordinator- ' +
            INSTITUTION_NAME +
            '';
        const bulkUpdateObj = [];
        for (std of student_datas) {
            let delivery_tags = '';
            for (delivery_data of std.delivery) {
                delivery_tags =
                    delivery_tags +
                    'Delivery : ' +
                    delivery_data.delivery +
                    ' - ' +
                    ' Group : ' +
                    delivery_data.group +
                    '<br>';
            }
            const name = std.name.middle
                ? std.name.first + ' ' + std.name.middle + ' ' + std.name.last
                : std.name.first + ' ' + std.name.last;
            const replace_data = {
                v_name: name,
                course_code: std.course_name + ' - ' + std.course_no,
                course_delivery: delivery_tags,
            };
            const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
            const message = email_message.replace(re, (matched) => replace_data[matched]);
            if (req.body.notify_via.indexOf('email') !== -1) {
                if (std.email !== undefined && std.email !== '')
                    await common_fun.send_email(
                        std.email,
                        'DigiClass Alert - Your Course ' +
                            student_group_check.data.groups[student_group_row].courses[course_ind]
                                .course_name +
                            ' - ' +
                            student_group_check.data.groups[student_group_row].courses[course_ind]
                                .course_no +
                            ' Groups Allocation',
                        message,
                    );
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const message_data = 'DigiClass - ' + sms_message;
                if (std.mobile !== undefined && std.mobile !== '')
                    await common_fun.send_sms(std.mobile, message_data);
            }
            if (req.body.notify_via.indexOf('digischeduler') !== -1) {
                //Send Notification to DigiScheduler FCM
            }
            if (req.body.notify_via.indexOf('digiclass') !== -1) {
                //Send Notification to DigiScheduler FCM
            }
            // const objs = {
            //     $set: {
            //         'groups.$[i].students.$[j].course_group_status.$[k].status': 'published',
            //     },
            // };
            // const filter = {
            //     arrayFilters: [
            //         { 'i.term': req.body.batch, 'i.level': req.body.level },
            //         { 'j._student_id': std._id },
            //         { 'k._course_id': req.body._course_id },
            //     ],
            // };
            // await base_control.update_condition_array_filter(student_group, query, objs, filter);
            bulkUpdateObj.push({
                updateOne: {
                    filter: query,
                    update: {
                        $set: {
                            'groups.$[i].students.$[j].course_group_status.$[k].status':
                                'published',
                        },
                    },
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._student_id': ObjectId(std._id) },
                        { 'k._course_id': ObjectId(req.body._course_id) },
                    ],
                },
            });
        }
        if (bulkUpdateObj.length !== 0)
            console.log(await base_control.bulk_write(student_group, bulkUpdateObj));
        // Pushing Notification to Staff
        if (staffData) {
            const data = user_data.data.find(
                (i) => i._id.toString() === staffData._user_id.toString(),
            );
            if (data) {
                const name = data.name.middle
                    ? data.name.first + ' ' + data.name.middle + ' ' + data.name.last
                    : data.name.first + ' ' + data.name.last;
                const term = req.body.batch.charAt(0).toUpperCase() + req.body.batch.slice(1);
                const email_message =
                    '<p>Dear ' +
                    name +
                    ',<br>' +
                    common_fun.emailGreetingContent() +
                    '<br>' +
                    'This email is to inform that you have published ' +
                    courseDetails.data.course_name +
                    ' - ' +
                    courseDetails.data.course_code +
                    ' (' +
                    term +
                    ') Course Student Group<br><br>' +
                    common_fun.emailRegardsContent() +
                    '</p>';
                const sms_message =
                    'DigiClass - You have published ' +
                    courseDetails.data.course_name +
                    ' - ' +
                    courseDetails.data.course_code +
                    ' Course Student Group - ' +
                    INSTITUTION_NAME +
                    '';
                if (req.body.notify_via.indexOf('email') !== -1) {
                    if (data.email !== undefined && data.email !== '')
                        await common_fun.send_email(data.email, 'DigiClass Alert', email_message);
                }
                if (req.body.notify_via.indexOf('sms') !== -1) {
                    if (data.mobile !== undefined && data.mobile !== '')
                        await common_fun.send_sms(data.mobile, sms_message);
                }
            }
        }
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (student_datas && student_datas.length)
            await usersCoursesRedisCacheRemove({
                userIds: student_datas.map((studentElement) => studentElement._id.toString()),
                institutionCalendarId: student_group_check.data._institution_calendar_id,
            });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_GROUP_SUCCESSFULLY_PUBLISHED'),
                    req.t('GROUP_SUCCESSFULLY_PUBLISHED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
