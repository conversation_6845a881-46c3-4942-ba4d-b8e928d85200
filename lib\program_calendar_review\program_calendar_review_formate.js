let institution_formate = require('../institution_calendar/institution_calendar_formate');
let course_formate = require('../course/course_formate');

module.exports = {
    program_calendar: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                course: course_formate.course_ID_Only(element.course),
                start_date: element.start_date,
                end_date: element.end_date,
                institution: institution_formate.institution_ID_Only(element.institution),
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    program_calendar_ID: (doc) => {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            course: course_formate.course_ID_Only(doc.course),
            start_date: doc.start_date,
            end_date: doc.end_date,
            institution: institution_formate.institution_ID_Only(doc.institution),
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            course: doc._course_id,
            start_date: doc.start_date,
            end_date: doc.end_date,
            institution: doc._institution_id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                course: element._course_id,
                start_date: element.start_date,
                end_date: element.end_date,
                institution: element._institution_id,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}