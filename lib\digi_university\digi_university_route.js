const express = require('express');
const route = express.Router();
const digi_university = require('./digi_university_controller');
const validator = require('./digi_university_validator');

const file_upload = require('../utility/file_upload');
const multer = require('multer');

const logo_upload = file_upload.uploadfile2.fields([
    { name: 'logo', maxCount: 1 }
]);


route.post('/', (req, res, next) => {
    logo_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    })
}, validator.insert, digi_university.insert);
route.get('/country_state_city_list', digi_university.country_state_city_list);
route.get('/:id', validator.id, digi_university.list_id);
route.get('/get_university_details_by_user/:user_id', validator.user_id, digi_university.get_university_details_by_user_ID);
route.put('/:id', (req, res, next) => {
    logo_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    })
}, validator.id, validator.update, digi_university.update);


module.exports = route;