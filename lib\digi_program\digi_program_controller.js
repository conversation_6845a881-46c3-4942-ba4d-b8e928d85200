const constant = require('../utility/constants');
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const digi_curriculum = require('mongoose').model(constant.DIGI_CURRICULUM);
const department_subject = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const digi_session_delivery_types = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const Framework = require('mongoose').model(constant.FRAMEWORK);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
// var institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const labelSchema = require('mongoose').model(constant.DIGI_LABEL);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const lmsSettingSchemas = require('../lmsStudentSetting/lmsStudentSetting.model');
const {
    clearItem,
    allProgramDatas,
    allDepartmentSubjectList,
} = require('../../service/cache.service');
const { convertToMongoObjectId } = require('../utility/common');
const ObjectId = convertToMongoObjectId;
const { LMS_VERSION } = require('../utility/util_keys');
// const { PROGRAM_CALENDAR_COURSE } = require('../utility/constants');
const { changeProgramName } = require('../lmsWarning/lmsWarning.controller');

// Updating Program Flat Caching Data
const updateProgramFlatCacheData = async () => {
    clearItem('allProgram');
    await allProgramDatas();
};

// Updating Department Subject Flat Caching Data
const updateDepartmentSubjectFlatCacheData = async () => {
    clearItem('allDepartmentSubjects');
    await allDepartmentSubjectList();
};

/**
 * Returning Program list based on Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Program_List>}
 */
async function list(req, res) {
    const program_list = await base_control.get_list(
        program,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    if (!program_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []),
            );

    const department_list = await base_control.get_list(
        department_subject,
        { /* _institution_id: ObjectId(req.headers._institution_id), */ isDeleted: false },
        {},
    );
    const department_data = [];
    const responses = [];
    if (department_list.status)
        for (element of department_list.data) {
            if (element.program_id) {
                department_data.push(element);
            }
        }
    const query_c = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
        isActive: true,
    };
    const curriculum_data = await base_control.get_list(digi_curriculum, query_c, {
        curriculum_name: 1,
        _program_id: 1,
    });
    const query_s = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
        isActive: true,
    };
    const session_data = await base_control.get_list(digi_session_delivery_types, query_s, {
        session_name: 1,
        session_symbol: 1,
        _program_id: 1,
    });
    // return res.send(session_data);
    program_list.data.forEach((element) => {
        let department_count = 0;
        let subject_count = 0;
        let departments = [];
        let curriculum_list = [];
        let session_type_list = [];
        if (department_list.status) {
            departments = department_data.filter(
                (item) => item.program_id.toString() == element._id.toString(),
            );
            for (department_element of departments) {
                const active_subject = department_element.subject.filter(
                    (i) => i.isDeleted == false,
                );
                subject_count += active_subject.length;
            }
            department_count = departments.length;
        }
        if (curriculum_data.status) {
            curriculum_list = curriculum_data.data.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        }
        if (session_data.status) {
            session_type_list = session_data.data.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        }
        responses.push({
            ...element.toObject(),
            department: department_count,
            subject: subject_count,
            curriculum: curriculum_list,
            session_type: session_type_list,
        });
    });
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('PROGRAM_LIST'),
                responses,
            ),
        );
}

/**
 * Returning Program list based on _id
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Single Program Get>}
 */
async function list_id(req, res) {
    const program_list = await base_control.get(
        program,
        {
            _id: ObjectId(req.params.id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        },
        {},
    );
    if (program_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_LIST'),
                    program_list.data,
                ),
            );
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                410,
                false,
                req.t('PROGRAM_NOT_FOUND'),
                {},
            ),
        );
}

/**
 * Adding Program based on Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Add Program>}
 */
async function insert(req, res) {
    const institution_check = await base_control.get(institution, {
        _id: req.headers._institution_id,
        isDeleted: false,
    });
    if (!institution_check.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );

    //Duplicate check
    // let curriculum_data = await base_control.get(digi_curriculum, { _id: { $ne: ObjectId(req.params.id) }, curriculum_name: req.body.curriculum_name.toLowerCase(), _program_id: ObjectId(req.body._program_id), isDeleted: false }, { _id: 1 });
    const program_name = [req.body.name, req.body.name.toUpperCase(), req.body.name.toLowerCase()];
    const program_code = [req.body.code, req.body.code.toUpperCase(), req.body.code.toLowerCase()];
    const duplicate_check = {
        $or: [{ name: program_name }, { code: program_code }],
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_list = await base_control.get(program, duplicate_check, {});
    if (program_list.status)
        return res
            .status(409)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    409,
                    false,
                    req.t('DUPLICATE_PROGRAM'),
                    [],
                ),
            );

    req.body._institution_id = req.headers._institution_id;
    const doc = await base_control.insert(program, req.body);
    updateProgramFlatCacheData();
    if (doc.status) {
        if (LMS_VERSION === 'V2') {
            const studentSettingData = await lmsSettingSchemas.updateMany(
                {
                    _institution_id: convertToMongoObjectId(req.body._institution_id),
                },
                {
                    $addToSet: { 'levelApprover.$[i].programIds': doc.responses._id },
                },
                {
                    arrayFilters: [
                        {
                            'i.isExceptionalProgram': false,
                        },
                    ],
                },
            );
        }
        return res
            .status(201)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    201,
                    true,
                    req.t('PROGRAM_CREATED'),
                    doc,
                ),
            );
    }
    return res
        .status(410)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                410,
                false,
                req.t('UNABLE_TO_CREATE_PROGRAM'),
                doc.data,
            ),
        );
}

/**
 * Update Program based on _id
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Update Program>}
 */
async function update(req, res) {
    try {
        //Duplicate check
        let program_name = [];
        let program_code = [];
        if (req.body.name)
            program_name = [
                req.body.name,
                req.body.name.toUpperCase(),
                req.body.name.toLowerCase(),
            ];
        if (req.body.code)
            program_code = [
                req.body.code,
                req.body.code.toUpperCase(),
                req.body.code.toLowerCase(),
            ];
        if (program_name.length != 0 || program_code.length != 0) {
            const duplicate_check = {
                _id: { $ne: ObjectId(req.params.id) },
                $or: [{ name: program_name }, { code: program_code }],
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
            const program_list = await base_control.get(program, duplicate_check, {});
            if (program_list.status)
                return res
                    .status(409)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            409,
                            true,
                            req.t('DUPLICATE_PROGRAM'),
                            [],
                        ),
                    );
        }
        const doc = await base_control.update(program, ObjectId(req.params.id), req.body);

        if (doc.status) {
            //Lms Setting Changes
            if (LMS_VERSION === 'V2') {
                if (typeof req.body.isActive === 'boolean') {
                    if (req.body.isActive) {
                        const updateLms = await lmsSettingSchemas.updateMany(
                            {
                                _institution_id: convertToMongoObjectId(
                                    req.headers._institution_id,
                                ),
                            },
                            {
                                $push: {
                                    'levelApprover.$[i].programIds': convertToMongoObjectId(
                                        req.params.id,
                                    ),
                                },
                            },
                            {
                                arrayFilters: [
                                    {
                                        'i.isExceptionalProgram': false,
                                    },
                                ],
                            },
                        );
                    } else {
                        const updateLms = await lmsSettingSchemas.updateMany(
                            {
                                _institution_id: convertToMongoObjectId(
                                    req.headers._institution_id,
                                ),
                            },
                            {
                                $pull: {
                                    'levelApprover.$[i].programIds': convertToMongoObjectId(
                                        req.params.id,
                                    ),
                                },
                            },
                            {
                                arrayFilters: [
                                    {
                                        'i.programIds': convertToMongoObjectId(req.params.id),
                                    },
                                ],
                            },
                        );
                    }
                }
            }
            //Program name change
            const obj_dept = {
                $set: {
                    program_name: req.body.name,
                },
            };
            const query_cond = {
                program_id: ObjectId(req.params.id),
            };
            await base_control.update_many_with_array_filter(
                department_subject,
                query_cond,
                obj_dept,
            );
            //Get Depts and subjects
            const dept_subj_list = await base_control.get_list(
                department_subject,
                {
                    program_id: { $ne: ObjectId(req.params.id) },
                },
                { _id: 1, shared_with: 1, subject: 1 },
            );
            //return res.send(dept_subj_list);
            if (dept_subj_list.status) {
                const bulk = [];
                const bulkSubj = [];
                for (const dept_subj of dept_subj_list.data) {
                    //subj shared with
                    for (const subj of dept_subj.subject) {
                        if (subj == [] || subj.shared_with == []) continue;

                        const filteredSubj = subj.shared_with.filter(
                            (ele) =>
                                ele.program_id.toString() == ObjectId(req.params.id).toString(),
                        );
                        if (filteredSubj == []) continue;
                        for (const subjF of filteredSubj) {
                            bulkSubj.push({
                                updateOne: {
                                    filter: {
                                        //program_id: { $ne: ObjectId(req.params.id) },
                                        //'subject._id': ObjectId(subj._id),
                                        'subject.shared_with._id': ObjectId(subjF._id),
                                    },
                                    update: {
                                        $set: {
                                            'subject.$[subj].shared_with.$[sharedID].program_name':
                                                req.body.name,
                                        },
                                    },
                                    arrayFilters: [
                                        { 'subj._id': ObjectId(subj._id) },
                                        { 'sharedID._id': ObjectId(subjF._id) },
                                    ],
                                },
                            });
                        }
                    }

                    //Department shared with
                    if (dept_subj.shared_with == []) continue;
                    const filteredDeptSubj = dept_subj.shared_with.filter(
                        (ele) => ele.program_id.toString() == ObjectId(req.params.id).toString(),
                    );
                    if (filteredDeptSubj == []) continue;
                    for (sharedDeptSubj of filteredDeptSubj) {
                        bulk.push({
                            updateOne: {
                                filter: {
                                    'shared_with._id': ObjectId(sharedDeptSubj._id),
                                },
                                update: {
                                    $set: {
                                        'shared_with.$.program_name': req.body.name,
                                    },
                                },
                            },
                        });
                    }
                }
                //return res.send(bulkSubj);
                let docU = '';
                if (bulk.length > 0) docU = await department_subject.bulkWrite(bulk);
                if (bulkSubj.length > 0) docU = await department_subject.bulkWrite(bulkSubj);
                //console.log(docU);
            }

            // Changing Program in Role Assign Area
            if (req.body.name && req.body.name.length != 0) {
                const role_based_list = await base_control.get_list(
                    role_assign,
                    { 'roles.program._program_id': ObjectId(req.params.id) },
                    {},
                );
                if (role_based_list.status) {
                    const bulk_data = [];
                    for (role_assign_element of role_based_list.data) {
                        for (role_element of role_assign_element.roles) {
                            for (program_element of role_element.program)
                                if (
                                    program_element._program_id.toString() ===
                                    req.params.id.toString()
                                )
                                    bulk_data.push({
                                        updateOne: {
                                            filter: {
                                                _id: ObjectId(role_assign_element._id),
                                            },
                                            update: {
                                                $set: {
                                                    'roles.$[i].program.$[j].program_name':
                                                        req.body.name,
                                                },
                                            },
                                            arrayFilters: [
                                                { 'i._id': role_element._id },
                                                { 'j._id': program_element._id },
                                            ],
                                        },
                                    });
                        }
                    }
                    const role_program_name_change = await base_control.bulk_write(
                        role_assign,
                        bulk_data,
                    );
                    if (!role_program_name_change.status)
                        console.log(req.t('UNABLE_TO_CHANGE_PROGRAM_NAME_ROLE_ASSIGN'));
                    console.log(req.t('PROGRAM_NAME_CHANGED_IN_ROLE_ASSIGN'));
                }
                await changeProgramName({
                    programId: ObjectId(req.params.id),
                    name: req.body.name,
                });
            }
            updateProgramFlatCacheData();
            updateDepartmentSubjectFlatCacheData();
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('PROGRAM_UPDATED'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_UPDATE_PROGRAM'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error ',
                    error.toString(),
                ),
            );
    }
}

/**
 * Remove Program based on _id
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Add Program>}
 */
async function remove(req, res) {
    const doc = await base_control.delete(program, ObjectId(req.params.id));
    updateProgramFlatCacheData();
    if (doc.status)
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('PROGRAM_REMOVED'), doc.data),
            );
    return res
        .status(410)
        .send(
            common_files.response_function(
                res,
                410,
                false,
                req.t('UNABLE_TO_REMOVED_PROGRAM'),
                doc.data,
            ),
        );
}

/**
 * Returning Program list based on Type / Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Program_List>}
 */
async function type_list(req, res) {
    const program_list = await base_control.get_list(
        program,
        {
            _institution_id: ObjectId(req.headers._institution_id),
            program_type: req.params.type,
            isDeleted: false,
        },
        {},
    );
    if (!program_list.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('PROGRAM_LIST'), []));

    const department_list = await base_control.get_list(
        department_subject,
        { /* _institution_id: ObjectId(req.headers._institution_id), */ isDeleted: false },
        {},
    );
    const department_data = [];
    const responses = [];
    if (department_list.status)
        for (element of department_list.data) {
            if (element.program_id) {
                department_data.push(element);
            }
        }
    const query_c = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
        isActive: true,
    };
    const curriculum_data = await base_control.get_list(digi_curriculum, query_c, {
        curriculum_name: 1,
        _program_id: 1,
    });
    const query_s = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
        isActive: true,
    };
    const session_data = await base_control.get_list(digi_session_delivery_types, query_s, {
        session_name: 1,
        session_symbol: 1,
        _program_id: 1,
    });

    program_list.data.forEach((element) => {
        let department_count = 0;
        let subject_count = 0;
        let departments = [];
        let curriculum_list = [];
        let session_type_list = [];
        if (department_list.status) {
            departments = department_data.filter(
                (item) => item.program_id.toString() == element._id.toString(),
            );
            for (department_element of departments) {
                const active_subject = department_element.subject.filter(
                    (i) => i.isDeleted == false,
                );
                subject_count += active_subject.length;
            }
            department_count = departments.length;
        }
        if (curriculum_data.status) {
            curriculum_list = curriculum_data.data.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        }
        if (session_data.status) {
            session_type_list = session_data.data.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        }
        responses.push({
            ...element.toObject(),
            department: department_count,
            subject: subject_count,
            curriculum: curriculum_list,
            session_type: session_type_list,
        });
    });
    return res
        .status(200)
        .send(common_files.response_function(res, 200, true, req.t('PROGRAM_LIST'), responses));
}

async function program_department_list(req, res) {
    try {
        const aggre = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            {
                $lookup: {
                    from: 'digi_department_subjects',
                    localField: '_id',
                    foreignField: 'program_id',
                    as: 'department_data',
                },
            },
            {
                $lookup: {
                    from: 'digi_session_delivery_types',
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'session_delivery_data',
                },
            },
        ];
        const doc = await base_control.get_aggregate(program, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, 'Error', []));
        const program_dept_list = [];
        const shared_subject = [];
        for (let i = 0; i < doc.data.length; i++) {
            const session_delivery_data = doc.data[i].session_delivery_data.filter(
                (ele) => ele.isDeleted == false,
            );
            const new_delivery_type_data = [];
            for (let j = 0; j < session_delivery_data.length; j++) {
                const delivery_types_data = session_delivery_data[j].delivery_types.filter(
                    (ele) => ele.isDeleted == false,
                );
                for (element of delivery_types_data) {
                    new_delivery_type_data.push({
                        _session_id: session_delivery_data[j]._id,
                        program_id: session_delivery_data[j]._program_id,
                        program_name: session_delivery_data[j].program_name,
                        session_name: session_delivery_data[j].session_name,
                        session_symbol: session_delivery_data[j].session_symbol,
                        _delivery_type_id: element._id,
                        delivery_name: element.delivery_name,
                        delivery_symbol: element.delivery_symbol,
                        delivery_duration: element.delivery_duration,
                    });
                }
            }
            const department_data = doc.data[i].department_data.filter(
                (ele) => ele.isDeleted == false,
            );
            const new_dept_data = [];
            for (let j = 0; j < department_data.length; j++) {
                const subject_data = department_data[j].subject.filter(
                    (ele) => ele.isDeleted == false,
                );
                for (element of subject_data) {
                    if (element.shared_with && element.shared_with.length != 0) {
                        shared_subject.push({
                            program_id: department_data[j].program_id,
                            program_name: department_data[j].program_name,
                            department_id: department_data[j]._id,
                            department_name: department_data[j].department_name,
                            subject_id: element._id,
                            subject_name: element.subject_name,
                            shared_with: element.shared_with,
                        });
                    }
                }
                new_dept_data.push({
                    _id: department_data[j]._id,
                    isActive: department_data[j].isActive,
                    isDeleted: department_data[j].isDeleted,
                    program_id: department_data[j].program_id,
                    program_name: department_data[j].program_name,
                    department_name: department_data[j].department_name,
                    subject: subject_data,
                    shared: [],
                });
            }
            program_dept_list.push({
                _id: doc.data[i]._id,
                isDeleted: doc.data[i].isDeleted,
                isActive: doc.data[i].isActive,
                program_type: doc.data[i].program_type,
                name: doc.data[i].name,
                code: doc.data[i].code,
                level: doc.data[i].level,
                type: doc.data[i].type,
                degree: doc.data[i].degree,
                no_term: doc.data[i].no_term,
                _institution_id: doc.data[i]._institution_id,
                createdAt: doc.data[i].createdAt,
                updatedAt: doc.data[i].updatedAt,
                department_data: new_dept_data,
                delivery_types: new_delivery_type_data,
            });
        }
        for (let i = 0; i < program_dept_list.length; i++) {
            for (let j = 0; j < program_dept_list[i].department_data.length; j++) {
                const shared = [];
                for (element of shared_subject) {
                    const datas = program_dept_list[i].department_data[j];
                    if (
                        element.shared_with.findIndex(
                            (i) =>
                                i.program_id.toString() == datas.program_id.toString() &&
                                i.department_id.toString() == datas._id.toString(),
                        ) != -1
                    )
                        shared.push({
                            program_id: element.program_id,
                            program_name: element.program_name,
                            department_id: element.department_id,
                            department_name: element.department_name,
                            subject_id: element.subject_id,
                            subject_name: element.subject_name,
                        });
                }
                Object.assign(program_dept_list[i].department_data[j], { shared });
            }
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_DEPARTMENT_LIST'),
                    program_dept_list,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch ',
                    error.toString(),
                ),
            );
    }
}

async function program_input_sidebar(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //Curriculum, dept, session types
        const sidebar = [];
        let new_curriculum_data = '';
        let new_department = '';
        let new_shared_with = '';
        // let new_session_type_data = '';
        //curriculum by program id
        const query_p = { _program_id: ObjectId(req.params.id), isDeleted: false };
        const curriculum_data = await base_control.get_list(digi_curriculum, query_p, {});
        if (curriculum_data.status) {
            sidebar.push({ curriculum: curriculum_data.data });
            new_curriculum_data = curriculum_data.data;
        } else {
            sidebar.push({ curriculum: [] });
            new_curriculum_data = [];
        }
        //Department List
        const query_d = { /* program_id: ObjectId(req.params.id), */ isDeleted: false };
        const dept_data = await base_control.get_list(department_subject, query_d, {});

        const shared_with = [];
        const new_department_data = [];
        const shared_subject = [];
        if (dept_data.status) {
            for (let i = 0; i < dept_data.data.length; i++) {
                if (
                    dept_data.data[i].program_id &&
                    dept_data.data[i].program_id.toString() !== req.params.id.toString()
                ) {
                    const subject_data = dept_data.data[i].subject.filter(
                        (ele) => ele.isDeleted == false,
                    );
                    for (element of subject_data) {
                        if (element.shared_with && element.shared_with.length != 0) {
                            shared_subject.push({
                                program_id: dept_data.data[i].program_id,
                                program_name: dept_data.data[i].program_name,
                                department_id: dept_data.data[i]._id,
                                department_name: dept_data.data[i].department_name,
                                subject_id: element._id,
                                subject_name: element.subject_name,
                                shared_with: element.shared_with,
                            });
                        }
                    }
                }
            }
            // return res.send(shared_subject);
            for (let i = 0; i < dept_data.data.length; i++) {
                if (
                    dept_data.data[i].program_id &&
                    dept_data.data[i].program_id.toString() === req.params.id.toString()
                ) {
                    new_department_data.push({
                        isActive: dept_data.data[i].isActive,
                        isDeleted: dept_data.data[i].isDeleted,
                        _id: dept_data.data[i]._id,
                        program_id: dept_data.data[i].program_id,
                        program_name: dept_data.data[i].program_name,
                        department_name: dept_data.data[i].department_name,
                        shared_with: dept_data.data[i].shared_with,
                        subject: dept_data.data[i].subject,
                        shared: [],
                        createdAt: dept_data.data[i].createdAt,
                        updatedAt: dept_data.data[i].updatedAt,
                    });
                }
                if (
                    dept_data.data[i].shared_with.length != 0 &&
                    dept_data.data[i].shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() ==
                            req.params.id.toString() /*  && (i.department_id).toString() == (dept_data.data[i]._id).toString() */,
                    ) != -1
                ) {
                    shared_with.push({
                        program_id: dept_data.data[i].program_id,
                        program_name: dept_data.data[i].program_name,
                        _id: dept_data.data[i]._id,
                        department_name: dept_data.data[i].department_name,
                        subject: dept_data.data[i].subject,
                        shared_with: dept_data.data[i].shared_with,
                    });
                }
                // new_department_data.push(obj);
                // shared_with.push(obj_shared_with)
            }
            sidebar.push({ department: new_department_data, shared_with });
            new_department = new_department_data;
            new_shared_with = shared_with;
        } else {
            sidebar.push({ department: [], shared_with: [] });
            new_department = [];
            new_shared_with = [];
        }

        for (let j = 0; j < new_department.length; j++) {
            const shared = [];
            for (element of shared_subject) {
                const datas = new_department[j];
                if (
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                )
                    shared.push({
                        program_id: element.program_id,
                        program_name: element.program_name,
                        department_id: element.department_id,
                        department_name: element.department_name,
                        subject_id: element.subject_id,
                        subject_name: element.subject_name,
                    });
            }
            Object.assign(new_department[j], { shared });
        }

        //session types by program id
        const query_s = { _program_id: ObjectId(req.params.id), isDeleted: false };
        const session_data = await base_control.get_list(digi_session_delivery_types, query_s, {});
        if (session_data.status) {
            sidebar.push({ session_types: session_data.data });
            new_session_type_data = session_data.data;
        } else {
            sidebar.push({ session_types: [] });
            session_data.data = [];
        }

        //Framework data
        const query_f = { isDeleted: false };
        const framework_data = await base_control.get_list(Framework, query_f, {});

        return res.status(200).send(
            common_files.responseFunctionWithRequest(req, 200, true, req.t('SIDE_BAR'), {
                curriculum: new_curriculum_data,
                department: { department: new_department, shared_with: new_shared_with },
                session_types: session_data,
                framework: framework_data,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const checkDuplicate = function (program_excel_data, program_code) {
    const program_code_arr = program_excel_data.map((ele) => ele.Program_Code);
    let flag = 0;
    const first_ind = program_code_arr.indexOf(program_code);
    const last_ind = program_code_arr.lastIndexOf(program_code);
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + req.t('IS_REQUIRED'));
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
};
async function validate_data_check_program(req, res, data, collection_datas, duplicate_in_excel) {
    const message = [];
    const obj = {};
    const { program_data } = collection_datas;

    //Excel Duplicate Check
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) => ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase(),
    );
    const program_code_ind = program_data.data.findIndex(
        (ele) => ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase(),
    );

    if (program_name_ind != -1) message.push(req.t('PROGRAM_NAME_ALREADY_EXIST'));
    if (program_code_ind != -1) message.push(req.t('PROGRAM_CODE_ALREADY_EXIST'));

    //Type check(Program/Prerequisite)
    const pg_typ_arr = [constant.PROGRAM_TYPE.PROGRAM, constant.PROGRAM_TYPE.PREREQUISITE];
    if (data.Type != '') {
        const check_type = pg_typ_arr.includes(data.Type.trim().toLowerCase());
        if (!check_type)
            message.push(
                req.t('TYPE_MUST_BE') +
                    constant.PROGRAM_TYPE.PROGRAM +
                    '/' +
                    constant.PROGRAM_TYPE.PREREQUISITE,
            );
        else {
            obj.type = data.Type.trim().toLowerCase();
            if (data.Type.trim().toLowerCase() == constant.PROGRAM_TYPE.PROGRAM) {
                //Program Type check(UG/PG)
                const graduate_type_arr = [constant.GRADUATE_TYPE.UG, constant.GRADUATE_TYPE.PG];
                if (data.Program_Type != '') {
                    const check_program_type = graduate_type_arr.includes(
                        data.Program_Type.trim().toLowerCase(),
                    );
                    if (!check_program_type)
                        message.push(
                            req.t('PROGRAM_TYPE_MUST_BE') +
                                constant.GRADUATE_TYPE.UG +
                                '/' +
                                constant.GRADUATE_TYPE.PG,
                        );
                    else {
                        if (data.Program_Type.trim().toLowerCase() == constant.GRADUATE_TYPE.UG)
                            obj.program_type = constant.UG;
                        else obj.program_type = constant.PG;
                    }
                } else message.push(req.t('CHECK_PROGRAM_TYPE_IS_EMPTY'));

                //Program Level Check
                if (data.Program_Level == '') message.push(req.t('CHECK_PROGRAM_LEVEL_IS_EMPTY'));

                //Degree_Name Check
                if (data.Degree_Name == '') message.push(req.t('CHECK_DEGREE_NAME_IS_EMPTY'));
            }
        }
    } else message.push(req.t('CHECK_TYPE_IS_EMPTY'));

    if (Number.isInteger(data.No_of_Terms)) {
        if (
            (data.No_of_Terms == 0 && data.Terms.length == 0) ||
            data.No_of_Terms != data.Terms.length
        )
            message.push(req.t('CHECK_TERMS_AND_NO_OF_TERMS'));
    } else message.push(req.t('NO_OF_TERMS_MUST_BE_INTEGER'));

    return { message, data: obj, other_datas: {} }; //Message for data check api   //Remaining datas for Import api
}
async function data_check_program(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = { isDeleted: false };
        const program_data = await base_control.get_list(program, query, {});
        if (!program_data.status) program_data.data = [];

        const collection_datas = { program_data };

        //Empty Validation
        const optional_field = [];
        //Optional fields
        for (data of req.body.programs) {
            if (data.Type.trim().toLowerCase() == constant.PROGRAM_TYPE.PREREQUISITE) {
                optional_field.push('Program_Type');
                optional_field.push('Program_Level');
                optional_field.push('Degree_Name');
            } else if (data.Type.trim().toLowerCase() != constant.PROGRAM_TYPE.PREREQUISITE) {
                optional_field.push('Program_Type');
                optional_field.push('Program_Level');
                optional_field.push('Degree_Name');
            }
        }
        const empty_validation_check = field_empty_validation(req.body.programs, optional_field);
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.programs) {
            const excel_dup_status = checkDuplicate(req.body.programs, data.Program_Code);
            const program_duplicate_status = excel_dup_status == 1;
            if (program_duplicate_status) {
                //Duplicate in Excel
                validation = await validate_data_check_program(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_program(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }

            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const localDataCheckProgram = async (req, res) => {
    const query = { isDeleted: false };
    const program_data = await base_control.get_list(program, query, {});
    if (!program_data.status) program_data.data = [];

    const collection_datas = { program_data };
    const collections_error_message = [];
    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    for (data of req.body.programs) {
        const excel_dup_status = checkDuplicate(req.body.programs, data.Program_Code);
        const program_duplicate_status = excel_dup_status == 1;
        if (program_duplicate_status) {
            //Duplicate in Excel
            validation = await validate_data_check_program(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validation = await validate_data_check_program(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }

        if (validation.message.length > 0) invalid_data.push({ data, message: validation.message });
        else valid_data.push({ data, message: validation.message });

        import_data.push(validation.data);
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: {},
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_program(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const data_check = await localDataCheckProgram(req, res); //Data check validation
        //return res.send(data_check);
        if (!data_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                    ),
                );

        const import_data = data_check.data.import_data;
        const program_arr = [];
        let loc = 0;
        for (pgm of req.body.programs) {
            const term_arr = [];
            for (let i = 0; i < pgm.No_of_Terms; i++)
                term_arr.push({ term_no: i + 1, term_name: pgm.Terms[i].name });

            if (pgm.Type.trim().toLowerCase() === constant.PROGRAM_TYPE.PROGRAM) {
                program_arr.push({
                    insertOne: {
                        document: {
                            _institution_id: req.headers._institution_id,
                            name: pgm.Program_Name.trim(),
                            code: pgm.Program_Code.trim(),
                            program_type: import_data[loc].type, //Interchange program_type with type, type with program_type as requested
                            type: import_data[loc].program_type,
                            level: pgm.Program_Level.trim(),
                            degree: pgm.Degree_Name.trim(),
                            no_term: pgm.No_of_Terms,
                            term: term_arr,
                        },
                    },
                });
            } else {
                program_arr.push({
                    insertOne: {
                        document: {
                            _institution_id: req.headers._institution_id,
                            name: pgm.Program_Name.trim(),
                            code: pgm.Program_Code.trim(),
                            program_type: pgm.Type.trim(), //Interchange program_type with type, type with program_type as requested
                            no_term: pgm.No_of_Terms,
                            term: term_arr,
                        },
                    },
                });
            }
            loc++;
        }
        // return res.send(program_arr);
        const doc = await base_control.bulk_write(program, program_arr);
        updateProgramFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('PROGRAM_IMPORTED_SUCCESSFULLY'),
                        program_arr,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_IMPORT_PROGRAM'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function program_department_list_along_share(req, res) {
    try {
        const aggre = [
            {
                $match: {
                    _institution_id: convertToMongoObjectId(req.headers._institution_id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            {
                $lookup: {
                    from: 'digi_department_subjects',
                    localField: '_id',
                    foreignField: 'program_id',
                    as: 'department_data',
                },
            },
            {
                $lookup: {
                    from: 'digi_session_delivery_types',
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'session_delivery_data',
                },
            },
        ];
        const doc = await base_control.get_aggregate(program, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, true, 'Error', []));
        const shared_subject = [];
        const shared_depart = [];
        let DeptSubs = [];
        doc.data.forEach((eleDept) => {
            DeptSubs = JSON.parse(JSON.stringify(eleDept.department_data));
            DeptSubs.forEach((deptShare) => {
                deptShare.shared = false;
                const subject_data = deptShare.subject.filter((ele) => ele.isDeleted == false);
                for (element of subject_data) {
                    if (element.shared_with && element.shared_with.length != 0) {
                        shared_subject.push({
                            program_id: deptShare.program_id,
                            program_name: deptShare.program_name,
                            department_id: deptShare._id,
                            department_name: deptShare.department_name,
                            subject_id: element._id,
                            subject_name: element.subject_name,
                            shared_with: element.shared_with,
                            shared: false,
                            isDeleted: element.isDeleted,
                        });
                    }
                }
                deptShare.subject.forEach((ele2) => {
                    ele2.shared = false;
                });
                deptShare.shared_with.forEach((ele3) => {
                    shared_depart.push({
                        _id: deptShare._id,
                        isActive: deptShare.isActive,
                        isDeleted: deptShare.isDeleted,
                        program_id: ele3.program_id,
                        program_name: ele3.program_name,
                        shared_from_program_id: deptShare.program_id,
                        shared_from_program_name: deptShare.program_name,
                        department_name: deptShare.department_name,
                        subject: deptShare.subject,
                        shared_with: deptShare.shared_with,
                        shared: true,
                    });
                });
            });
        });

        doc.data.forEach((eleDept) => {
            DeptSubs = JSON.parse(JSON.stringify(eleDept.department_data));
            DeptSubs = [
                ...DeptSubs,
                ...shared_depart.filter(
                    (ele) => ele.program_id.toString() === eleDept._id.toString(),
                ),
            ];
            eleDept.department_data = DeptSubs;
        });
        doc.data.forEach((eleDept) => {
            eleDept.department_data.forEach((deptShare) => {
                for (element of shared_subject) {
                    const datas = deptShare;
                    if (
                        element.shared_with.findIndex(
                            (i) =>
                                i.program_id.toString() == datas.program_id.toString() &&
                                i.department_id.toString() == datas._id.toString(),
                        ) != -1
                    )
                        deptShare.subject.push({
                            _id: element.subject_id,
                            subject_name: element.subject_name,
                            subject_shared_from_program_id: element.program_id,
                            subject_shared_from_program_name: element.program_name,
                            isDeleted: element.isDeleted,
                            shared: true,
                        });
                }
            });
        });
        const program_dept_list = [];
        for (let i = 0; i < doc.data.length; i++) {
            const session_delivery_data = doc.data[i].session_delivery_data.filter(
                (ele) => ele.isDeleted == false,
            );
            const new_delivery_type_data = [];
            for (let j = 0; j < session_delivery_data.length; j++) {
                const delivery_types_data = session_delivery_data[j].delivery_types.filter(
                    (ele) => ele.isDeleted == false,
                );
                for (element of delivery_types_data) {
                    new_delivery_type_data.push({
                        _session_id: session_delivery_data[j]._id,
                        program_id: session_delivery_data[j]._program_id,
                        program_name: session_delivery_data[j].program_name,
                        session_name: session_delivery_data[j].session_name,
                        session_symbol: session_delivery_data[j].session_symbol,
                        _delivery_type_id: element._id,
                        delivery_name: element.delivery_name,
                        delivery_symbol: element.delivery_symbol,
                        delivery_duration: element.delivery_duration,
                    });
                }
            }
            const department_data = doc.data[i].department_data.filter(
                (ele) => ele.isDeleted == false,
            );
            const new_dept_data = [];
            for (let j = 0; j < department_data.length; j++) {
                const subject_data = department_data[j].subject.filter(
                    (ele) => ele.isDeleted == false,
                );
                for (element of subject_data) {
                    if (element.shared_with && element.shared_with.length != 0) {
                        shared_subject.push({
                            program_id: department_data[j].program_id,
                            program_name: department_data[j].program_name,
                            department_id: department_data[j]._id,
                            department_name: department_data[j].department_name,
                            subject_id: element._id,
                            subject_name: element.subject_name,
                            shared_with: element.shared_with,
                        });
                    }
                }
                new_dept_data.push({
                    _id: department_data[j]._id,
                    isActive: department_data[j].isActive,
                    isDeleted: department_data[j].isDeleted,
                    program_id: department_data[j].program_id,
                    program_name: department_data[j].program_name,
                    department_name: department_data[j].department_name,
                    subject: subject_data,
                    shared: [],
                });
            }
            program_dept_list.push({
                _id: doc.data[i]._id,
                isDeleted: doc.data[i].isDeleted,
                isActive: doc.data[i].isActive,
                program_type: doc.data[i].program_type,
                name: doc.data[i].name,
                code: doc.data[i].code,
                level: doc.data[i].level,
                type: doc.data[i].type,
                degree: doc.data[i].degree,
                no_term: doc.data[i].no_term,
                _institution_id: doc.data[i]._institution_id,
                createdAt: doc.data[i].createdAt,
                updatedAt: doc.data[i].updatedAt,
                department_data: new_dept_data,
                delivery_types: new_delivery_type_data,
            });
        }
        for (let i = 0; i < program_dept_list.length; i++) {
            for (let j = 0; j < program_dept_list[i].department_data.length; j++) {
                const shared = [];
                for (element of shared_subject) {
                    const datas = program_dept_list[i].department_data[j];
                    if (
                        element.shared_with.findIndex(
                            (i) =>
                                i.program_id.toString() == datas.program_id.toString() &&
                                i.department_id.toString() == datas._id.toString(),
                        ) != -1
                    )
                        shared.push({
                            program_id: element.program_id,
                            program_name: element.program_name,
                            department_id: element.department_id,
                            department_name: element.department_name,
                            subject_id: element.subject_id,
                            subject_name: element.subject_name,
                        });
                }
                Object.assign(program_dept_list[i].department_data[j], { shared });
            }
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_DEPARTMENT_LIST'),
                    program_dept_list,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch ',
                    error.toString(),
                ),
            );
    }
}

/**
 * Returning Program list based on Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Program_List>}
 */
async function programListWithType(req, res) {
    const program_list = await base_control.get_list(
        program,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        { name: 1, college_program_type: 1 },
    );
    if (!program_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []),
            );
    const labelList = await base_control.get_list(
        labelSchema,
        {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        },
        {
            isDeleted: 0,
            isActive: 0,
            _institution_id: 0,
            createdAt: 0,
            updatedAt: 0,
            __v: 0,
        },
    );
    return res.status(200).send(
        common_files.responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), {
            programDatas: program_list.data,
            labels: labelList.status ? labelList.data : [],
        }),
    );
}

/**
 * Adding Label based on Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Add Labels>}
 */
async function addLabels(req, res) {
    req.body._institution_id = req.headers._institution_id;
    const doc = await base_control.insert(labelSchema, req.body);
    if (doc.status)
        return res
            .status(201)
            .send(common_files.response_function(res, 201, true, 'Label Added', doc));
    return res
        .status(410)
        .send(common_files.response_function(res, 410, false, 'Unable To add Label', doc.data));
}

/**
 * Update Label based on _id
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Update Label>}
 */
async function updateLabel(req, res) {
    try {
        const doc = await base_control.update(labelSchema, ObjectId(req.params.id), req.body);
        if (doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, 'Label Updated', doc.data));
        return res
            .status(410)
            .send(common_files.response_function(res, 410, false, 'Label Updated', doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error ', error.toString()));
    }
}

module.exports = {
    list,
    type_list,
    list_id,
    insert,
    update,
    remove,
    program_department_list,
    program_input_sidebar,
    import_program,
    data_check_program,
    program_department_list_along_share,
    programListWithType,
    addLabels,
    updateLabel,
};
