const { convertToMongoObjectId } = require('../../utility/common');
const { searchKeyFunction } = require('../../utility/common_functions');
const qapcPermissionSchema = require('../rolePermission/qapcPermission.model');
const qapcGuideResourceSchema = require('../formInitiator/formGuideResources.model');
const {
    QAPCUserPermissionList,
    filterMatchingGroup,
    getAttachmentDocument,
    incorporateSectionList,
    matchingFormAttachment,
} = require('../formInitiator/formInitiator.service');
const {
    getFormApproverLevel,
    getFormInitiatedIds,
    splitQualityAssurance,
    getFormInitiatorList,
    getApproverUserList,
    getFormInitiatorApprover,
    updateFormApproverDetail,
    getAllUserApproverList,
    getLevelUserList,
    getLevelWiseSplitUser,
    getApprovedUserFormList,
    updateLevelWiseStatus,
    getApproverCategory,
    getViewActionId,
    updatedWiseSort,
} = require('./formApprover.service');
const {
    SUBMITTED,
    FORM_APPROVER,
    VIEW,
    RESUBMISSION,
    FORM_INITIATOR,
    EDIT,
    ALL,
} = require('../../utility/constants');
const { send_email } = require('../../utility/common_functions');

exports.approverCategoryList = async ({ headers = {}, query = {} }) => {
    try {
        const { _user_id, role_id } = headers;
        const { institutionCalenderId, subModuleType } = query;
        return getApproverCategory({
            userId: _user_id,
            roleId: role_id,
            institutionCalenderId,
            subModuleType,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qualityAssuranceApproverList = async ({ headers = {}, query = {} }) => {
    try {
        const { _user_id, role_id, _institution_id } = headers;
        const { institutionCalenderId, categoryId, searchKey } = query;
        const assuranceProcessQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            formCalenderIds: {
                $elemMatch: {
                    institutionCalenderId: convertToMongoObjectId(institutionCalenderId),
                    isDeleted: false,
                },
            },
            submissionStatus: { $in: [SUBMITTED, RESUBMISSION] },
            isActive: true,
            isDeleted: false,
            archive: false,
            ...(searchKey && {
                $or: [
                    { programName: searchKeyFunction({ searchKey }) },
                    { courseName: searchKeyFunction({ searchKey }) },
                ],
            }),
            categoryId: convertToMongoObjectId(categoryId),
        };
        const userList = await QAPCUserPermissionList({
            userId: _user_id,
            roleId: role_id,
            subModuleType: FORM_APPROVER,
        });
        if (!userList?.qapcPermissionList) {
            return { status: 200, message: 'NO_DATA_FOUND' };
        }
        //  user category form group Ids
        const uniqueCategoryGroupIds = new Set();
        const userActionsList = [];
        userList?.qapcPermissionList?.categoryFormGroupData.forEach(
            ({
                formGroupId,
                selectedAcademicTrue,
                calendarIds,
                actionIds,
                levelIndex,
                academicYear,
            }) => {
                //  unique key formGroupId and levelIndex
                const uniqueFormGroupId = `${formGroupId}_${levelIndex}`;
                if (
                    !uniqueCategoryGroupIds.has(uniqueFormGroupId) &&
                    ((!selectedAcademicTrue && academicYear === ALL) ||
                        (selectedAcademicTrue &&
                            calendarIds?.length &&
                            calendarIds.toString().includes(String(institutionCalenderId))))
                ) {
                    if (actionIds?.length) {
                        userActionsList.push({
                            actionIds,
                            categoryGroupId: formGroupId,
                            levelIndex,
                        });
                        uniqueCategoryGroupIds.add(uniqueFormGroupId);
                    }
                }
            },
        );
        if (!userActionsList?.length) {
            return { status: 200, message: 'NO_DATA_FOUND' };
        }
        const formInitiatorData = await getFormInitiatedIds({ assuranceProcessQuery });
        if (!formInitiatorData?.length) {
            return { status: 200, message: 'NO_DATA_FOUND' };
        }
        const filterMatchingGroupData = filterMatchingGroup({ formInitiatorData });
        if (!filterMatchingGroupData?.filterMatchingForm?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND' };
        }
        const createdFormData = getApprovedUserFormList({
            userActionsList,
            formInitiators: filterMatchingGroupData.filterMatchingForm,
        });
        if (!createdFormData?.groupMatchingForms?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND' };
        }
        //get form approver Level
        const formApproverLevel = await getFormApproverLevel({
            uniqueFormIds: createdFormData.uniqueFormIds,
        });
        const allUserLists = await getAllUserApproverList({
            uniqueCourseGroupIds: createdFormData?.uniqueCourseGroupIds,
            institutionCalenderId,
        });
        const viewActionId = await getViewActionId({ actionName: VIEW });
        return splitQualityAssurance({
            formApproverLevel,
            formInitiators: createdFormData.groupMatchingForms,
            userActionsList,
            userId: _user_id,
            roleId: role_id,
            roleUserList: allUserLists.qapcRoleUserData,
            viewActionId: viewActionId?.[0]?._id,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formApplicationList = async ({ query = {} }) => {
    try {
        const { formInitiatorIds } = query;
        const initiatorIds = formInitiatorIds.map((initiatorElement) =>
            convertToMongoObjectId(initiatorElement),
        );
        let formInitiatedData = await getFormInitiatorList({ formInitiatorIds });
        const formAttachmentData = await getAttachmentDocument({
            initiatorIds,
        });
        const matchingFormAttachmentData = await matchingFormAttachment({
            formInitiatorList: formInitiatedData,
            formAttachmentData,
            subModuleType: FORM_APPROVER,
        });
        formInitiatedData = matchingFormAttachmentData
            ? matchingFormAttachmentData.formInitiatorList
            : formInitiatedData;
        const incorporateData = await incorporateSectionList({ formList: formInitiatedData });
        //update wise sort form
        const sortedForm = updatedWiseSort({ formList: incorporateData.formList });
        return {
            status: 200,
            message: 'DATA_RETRIEVED',
            data: { filterMatchingFormData: sortedForm },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.approverUserList = async ({ query = {} }) => {
    try {
        const { formInitiatorId, categoryFormId, categoryFormGroupId, categoryFormCourseId } =
            query;
        return getApproverUserList({
            formInitiatorId,
            categoryFormId,
            categoryFormGroupId,
            categoryFormCourseId,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateApproverBy = async ({ headers = {}, body = {} }) => {
    try {
        const {
            formInitiatorId,
            userLevel,
            status,
            categoryFormId,
            categoryFormGroupId,
            reason,
            isSkipped,
            institutionCalenderId,
        } = body;
        const { _user_id, role_id } = headers;
        const formInitiatorApprover = await getFormInitiatorApprover({ formInitiatorId });
        const formApproverLevel = await getFormApproverLevel({ uniqueFormIds: [categoryFormId] });
        const allUserLists = await getAllUserApproverList({
            uniqueCourseGroupIds: [categoryFormGroupId],
            institutionCalenderId,
        });
        const levelWiseSplitUser = getLevelWiseSplitUser({
            roleUserList: allUserLists.qapcRoleUserData,
        });
        const mergeFormIds = formInitiatorApprover?.mergedFormId.map(
            (formIdElement) => formIdElement.formInitiatorId,
        );
        return updateFormApproverDetail({
            formInitiatorApprover,
            formApproverLevel,
            formInitiatorIds: [formInitiatorId, ...mergeFormIds],
            userLevel,
            status,
            _user_id,
            role_id,
            reason,
            isSkipped,
            RPLevelUserList: levelWiseSplitUser?.uniqueCategoryGroupId?.[0]?.approverLevels || [],
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.levelRoleUserList = async ({ query = {} }) => {
    try {
        const { categoryFormGroupId, categoryFormId, formInitiatorId, institutionCalenderId } =
            query;
        const formInitiatorApprover = await getFormInitiatorApprover({ formInitiatorId });
        const formApproverLevelData = await getFormApproverLevel({
            uniqueFormIds: [categoryFormId],
        });
        const allUserLists = await getAllUserApproverList({
            uniqueCourseGroupIds: [categoryFormGroupId],
            institutionCalenderId,
        });
        const qapcLevelUserData = getLevelUserList({
            categoryFormGroupId,
            qapcRoleUserData: allUserLists.qapcRoleUserData,
        });
        return updateLevelWiseStatus({
            formInitiatorApprover,
            formApproverLevel: formApproverLevelData[0],
            qapcLevelUserData: qapcLevelUserData?.approverRole,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formInitiatorUserList = async ({ query = {}, headers = {} }) => {
    try {
        const { categoryFormGroupId } = query;
        const getFormUserDate = await getViewActionId({ actionName: [VIEW, EDIT] });
        let categoryFormUserList = await qapcPermissionSchema
            .find(
                {
                    'permissions.cfpc': {
                        $elemMatch: {
                            subModuleName: FORM_INITIATOR,
                            formCourseGroupId: convertToMongoObjectId(categoryFormGroupId),
                            ...(getFormUserDate?.length && {
                                actionId: {
                                    $in: getFormUserDate.map((actionElement) =>
                                        convertToMongoObjectId(actionElement._id),
                                    ),
                                },
                            }),
                        },
                    },
                },
                {
                    _id: 0,
                    userId: 1,
                },
            )
            .populate({ path: 'userId', select: { email: 1 } })
            .lean();
        categoryFormUserList = categoryFormUserList?.map((userElement) => {
            return { _id: userElement.userId._id, email: userElement.userId.email };
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: categoryFormUserList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.sendEmailToFormInitiator = async ({ body = {} }) => {
    try {
        const { to, mailSubject, mailContent } = body;
        send_email(to, mailSubject, mailContent);
        return { statusCode: 200, message: 'MAIL_SEND_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.addComment = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formGuideResourcesId, addComment } = body;
        await qapcGuideResourceSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(formGuideResourcesId),
            },
            {
                $push: {
                    addComment,
                },
            },
        );
        return {
            statusCode: 200,
            message: 'SAVED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
