const express = require('express');
const route = express.Router();
const role_management = require('./role_management_controller');
const validater = require('./role_management_validator');
// route.post('/list', role_management.list_values);
// route.get('/rotation/:calendar/:rotation_no/:course', validater.rotation_course_get, role_management.rotation_course_get);
// route.get('/year_course/:calendar/:year_no', validater.course_list_year_get, role_management.course_list_year_get);
// route.get('/level_course/:calendar/:level_no', validater.course_list_level_get, role_management.course_list_level_get);
// route.get('/:calendar/:term/:course', validater.course_get, role_management.course_get);

// route.post('/', validater.role_management, role_management.insert);
// route.put('/', validater.role_management_update, role_management.update);
// route.delete('/', validater.role_management_delete, role_management.delete);
// route.delete('/event', validater.role_management_delete_event, role_management.delete_event);

route.get('/role_get/:role_id', role_management.role_get);

route.post('/add_role'/* , validater.role_management */, role_management.add_role);
route.post('/add_permission'/* , validater.role_management */, role_management.add_permission);
route.post('/add_privilege'/* , validater.role_management */, role_management.add_privilege);
route.post('/add_permission_set'/* , validater.role_management */, role_management.add_permisssion_set);
route.post('/add_role_set'/* , validater.role_management */, role_management.add_role_set);

route.post('/assign_role'/* , validater.role_management */, role_management.assign_role);

module.exports = route;