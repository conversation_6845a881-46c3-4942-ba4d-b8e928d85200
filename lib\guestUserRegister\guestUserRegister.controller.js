const guestUserSchema = require('../models/guestUser');
const bcrypt = require('bcryptjs');
const { generateAuthTokens } = require('../utility/token.util');
const { send_email, generateRandomNumber } = require('../utility/common_functions');
const { getPaginationValues } = require('../utility/pagination');
const util_key = require('../utility/util_keys');
const { GUEST } = require('../utility/constants');

exports.registerGuestUser = async ({ body = {} }) => {
    try {
        const { name, email, mobile, password, module, userType } = body;
        const existingUser = await guestUserSchema
            .findOne({ email: email.toLowerCase() }, { _id: 1 })
            .lean();
        if (existingUser) {
            return { status: 409, message: 'Email already exists' };
        }
        const guestData = {
            name,
            email: email.toLowerCase(),
            mobile,
            isActive: true,
            password: bcrypt.hashSync(password, 10),
            module,
            userType,
            isDeleted: false,
        };
        await guestUserSchema.create(guestData);
        return {
            status: 201,
            message: 'Guest user registered successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAllGuestUsers = async ({ query = {} }) => {
    try {
        const { searchKey = '' } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        const searchQuery = {
            isDeleted: false,
            ...(searchKey && {
                $or: [
                    { 'name.first': { $regex: searchKey, $options: 'i' } },
                    { 'name.last': { $regex: searchKey, $options: 'i' } },
                    { email: { $regex: searchKey, $options: 'i' } },
                ],
            }),
        };
        const users = await guestUserSchema
            .find(searchQuery, {
                name: 1,
                email: 1,
                mobile: 1,
                isActive: 1,
                module: 1,
                userType: 1,
            })
            .skip(skip)
            .limit(limit)
            .lean();
        const total = await guestUserSchema.find(searchQuery).countDocuments().lean();
        return {
            status: 200,
            message: 'All guest users fetched successfully',
            data: {
                users,
                pageNo,
                limit,
                totalPages: Math.ceil(total / limit),
                totalDocs: total,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getGuestUserById = async ({ query = {} }) => {
    try {
        const { guestUserId } = query;
        const userData = await guestUserSchema
            .findById(guestUserId, {
                name: 1,
                email: 1,
                mobile: 1,
                isActive: 1,
                module: 1,
                userType: 1,
            })
            .lean();
        if (!userData) {
            return { status: 404, message: 'Guest user not found' };
        }
        return {
            status: 200,
            message: 'Guest user fetched successfully',
            data: userData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateGuestUserById = async ({ query = {}, body = {} }) => {
    try {
        const { guestUserId } = query;
        const allowedFields = ['name', 'email', 'mobile', 'module', 'userType', 'isActive'];
        const updateData = {};
        for (const key of allowedFields) {
            if (body.hasOwnProperty(key)) {
                if (key === 'email') {
                    updateData[key] = body[key].toLowerCase();
                } else {
                    updateData[key] = body[key];
                }
            }
        }
        const updatedUser = await guestUserSchema
            .findByIdAndUpdate(guestUserId, updateData, {
                new: true,
            })
            .lean();
        if (!updatedUser) {
            return { status: 404, message: 'Guest user not found' };
        }
        return {
            status: 200,
            message: 'Guest user updated successfully',
            data: updatedUser,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.deleteGuestUserById = async ({ query = {} }) => {
    try {
        const { guestUserId } = query;
        const deletedUser = await guestUserSchema
            .findByIdAndUpdate(guestUserId, { isDeleted: true })
            .lean();
        if (!deletedUser) {
            return { status: 404, message: 'Guest user not found' };
        }
        return {
            status: 200,
            message: 'Guest user deleted successfully',
            data: deletedUser,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.loginGuestUser = async ({ body = {} }) => {
    try {
        const { email, password } = body;
        if (!email || !password) {
            return { status: 400, message: 'Email and password are required' };
        }
        const user = await guestUserSchema
            .findOne(
                { email: email.toLowerCase() },
                {
                    name: 1,
                    email: 1,
                    password: 1,
                    _id: 1,
                    module: 1,
                    userType: 1,
                },
            )
            .lean();
        if (!user || user.email !== email.toLowerCase()) {
            return { status: 400, message: 'Invalid email or password' };
        }
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return { status: 400, message: 'Invalid email or password' };
        }
        const GUEST_SERVICES = {
            Q360: util_key.SERVICES.Q360,
            LABEL_CHANGE: util_key.SERVICES.LABEL_CHANGE,
            TRANSLATION: util_key.SERVICES.TRANSLATION,
            REACT_APP_ENVIRONMENT: util_key.SERVICES.REACT_APP_ENVIRONMENT,
            REACT_APP_COLLEGE_NAME: util_key.SERVICES.REACT_APP_COLLEGE_NAME,
            REACT_APP_COUNTRY_CODE: util_key.SERVICES.REACT_APP_COUNTRY_CODE,
            REACT_APP_COUNTRY_CODE_LENGTH: util_key.SERVICES.REACT_APP_COUNTRY_CODE_LENGTH,
            REACT_APP_ROTATION_LEVEL: util_key.SERVICES.REACT_APP_ROTATION_LEVEL,
            REACT_APP_ACTIVE_VERSION: util_key.SERVICES.REACT_APP_ACTIVE_VERSION,
            REACT_APP_INSTITUTION_ID: util_key.SERVICES.REACT_APP_INSTITUTION_ID,
            REACT_APP_PARENT_DETAILS_MANDATORY:
                util_key.SERVICES.REACT_APP_PARENT_DETAILS_MANDATORY,
            DC_ADMIN_URL: util_key.SERVICES.DC_ADMIN_URL,
            DC_URL: util_key.SERVICES.DC_URL,
            DA_URL: util_key.SERVICES.DA_URL,
            DA_URL_1: util_key.SERVICES.DA_URL_1,
            SIS_SYNC: util_key.SERVICES.SIS_SYNC,
            ENCRYPT_PAYLOAD: util_key.SERVICES.ENCRYPT_PAYLOAD,
        };
        const tokens = await generateAuthTokens({
            userId: user.email,
            _id: user._id,
            userType: user.userType,
            userRoleData: [GUEST],
        });
        return {
            status: 200,
            message: 'Login successful',
            data: {
                name: user.name,
                _id: user._id,
                module: user.module,
                tokens,
                services: GUEST_SERVICES,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.sendEmailToGuestUser = async ({ body = {} }) => {
    try {
        const { to, mailSubject, mailContent } = body;
        const temporaryPassword = generateRandomNumber(util_key.RANDOM_PASSWORD_LENGTH);
        const personalizedContent = mailContent.replace(
            /(<li><strong>Temporary Password:<\/strong>)(\s*<\/li>)/,
            `$1 ${temporaryPassword}$2`,
        );
        await send_email(to, mailSubject, personalizedContent);
        await guestUserSchema
            .updateMany(
                { email: { $in: to } },
                { $set: { password: bcrypt.hashSync(temporaryPassword, 10) } },
            )
            .lean();
        return { status: 200, message: 'MAIL SEND SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
