const { Schema } = require('mongoose');
const constant = require('../../utility/constants');

const subjectSchema = new Schema({
    _subject_id: {
        type: Schema.Types.ObjectId,
    },
    subjectName: {
        type: String,
    },
});

const sessionOrderSchema = new Schema(
    {
        _session_id: {
            type: Schema.Types.ObjectId,
        },
        sNo: Number,
        deliveryType: String,
        _deliveryType_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DEPARTMENT_SUBJECT,
        },
        deliverySymbol: {
            type: String,
        },
        deliveryNumber: {
            type: Number,
        },
        deliveryTopic: {
            type: String,
        },
        subjects: [subjectSchema],
        duration: Number,
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: constant.COURSE,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        mode: {
            type: String,
        },
        week: [
            {
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                levelNo: String,
                weekNo: Number,
            },
        ],
        theme: {
            type: String,
        },
    },
    { timestamps: true },
);
module.exports = sessionOrderSchema;
