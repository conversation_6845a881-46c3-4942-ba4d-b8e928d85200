const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const digi_session_delivery_types = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
        },
        program_name: String,
        session_name: {
            type: String,
            //required: true
        },
        session_symbol: {
            type: String,
            //required: true
        },
        contact_hour_per_credit_hour: {
            type: String,
            //required: true
        },
        duration_per_contact_hour: {
            type: Number,
            // required: true,
        },
        session_duration: {
            type: Number,
            //required: true
        },
        delivery_types: [
            {
                delivery_name: {
                    type: String,
                    //required: true
                },
                delivery_symbol: {
                    type: String,
                    //required: true
                },
                delivery_duration: {
                    type: Number,
                    //required: true
                },
                isDeleted: {
                    type: Boolean,
                    default: false,
                },
                isActive: {
                    type: Boolean,
                    default: true,
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_SESSION_DELIVERY_TYPES, digi_session_delivery_types);
