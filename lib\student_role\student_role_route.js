const express = require("express");
const route = express.Router();
const student_role_controller = require('../student_role/student_role_controller')

route.post("/", student_role_controller.create);
route.get("/", student_role_controller.read);
route.get("/:_id", student_role_controller.read);
route.put("/:_id", student_role_controller.update);
route.delete("/:_id", student_role_controller.delete);

module.exports = route;
