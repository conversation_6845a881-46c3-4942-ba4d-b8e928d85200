const program_formate = require('../program/program_formate');

// exports.credit_hours_individual_ID_Only = (doc) => {
//     //console.log(doc);
//     let obj = {
//         _id: doc._id,
//         credit_hours_individual_title: doc.credit_hours_individual_title,
//         division: doc._division_id,
//         subject: doc._subject_id,
//         program: doc._program_id,
//         isActive: doc.isActive,
//         isDeleted: doc.isDeleted
//     }
//     return obj;
// }

module.exports = {
    credit_hours_individual: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                theory_credit_hours: element.theory_credit_hours,
                pratical_credit_hours: element.pratical_credit_hours,
                clinical_credit_hours: element.clinical_credit_hours,
                theory_total_contact_hours: element.theory_total_contact_hours,
                pratical_total_contact_hours: element.pratical_total_contact_hours,
                clinical_total_contact_hours: element.clinical_total_contact_hours,
                total_credit_hours: element.total_credit_hours,
                total_contact_hours: element.total_contact_hours,
                theory_contact_hours: element.theory_contact_hours,
                pratical_contact_hours: element.pratical_contact_hours,
                clinical_contact_hours: element.clinical_contact_hours,
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    credit_hours_individual_ID: (doc) => {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            theory_credit_hours: doc.theory_credit_hours,
            pratical_credit_hours: doc.pratical_credit_hours,
            clinical_credit_hours: doc.clinical_credit_hours,
            theory_total_contact_hours: doc.theory_total_contact_hours,
            pratical_total_contact_hours: doc.pratical_total_contact_hours,
            clinical_total_contact_hours: doc.clinical_total_contact_hours,
            total_credit_hours: doc.total_credit_hours,
            total_contact_hours: doc.total_contact_hours,
            theory_contact_hours: doc.theory_contact_hours,
            pratical_contact_hours: doc.pratical_contact_hours,
            clinical_contact_hours: doc.clinical_contact_hours,
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_individual_ID_Only: function (doc) {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            theory_credit_hours: doc.theory_credit_hours,
            pratical_credit_hours: doc.pratical_credit_hours,
            clinical_credit_hours: doc.clinical_credit_hours,
            theory_total_contact_hours: doc.theory_total_contact_hours,
            pratical_total_contact_hours: doc.pratical_total_contact_hours,
            clinical_total_contact_hours: doc.clinical_total_contact_hours,
            total_credit_hours: doc.total_credit_hours,
            total_contact_hours: doc.total_contact_hours,
            theory_contact_hours: doc.theory_contact_hours,
            pratical_contact_hours: doc.pratical_contact_hours,
            clinical_contact_hours: doc.clinical_contact_hours,
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_individual_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                theory_credit_hours: element.theory_credit_hours,
                pratical_credit_hours: element.pratical_credit_hours,
                clinical_credit_hours: element.clinical_credit_hours,
                theory_total_contact_hours: element.theory_total_contact_hours,
                pratical_total_contact_hours: element.pratical_total_contact_hours,
                clinical_total_contact_hours: element.clinical_total_contact_hours,
                total_credit_hours: element.total_credit_hours,
                total_contact_hours: element.total_contact_hours,
                theory_contact_hours: element.theory_contact_hours,
                pratical_contact_hours: element.pratical_contact_hours,
                clinical_contact_hours: element.clinical_contact_hours,
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}