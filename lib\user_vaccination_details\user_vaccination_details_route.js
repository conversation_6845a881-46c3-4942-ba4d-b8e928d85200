const express = require('express');
const route = express.Router();
const file_upload = require('../utility/file_upload');
const multer = require('multer');

const user_document = file_upload.uploadfile3.fields([
    { name: '_user_document', maxCount: 1 },
    { name: '_vaccination_document', maxCount: 1 },
]);

// controller;
const {
    insert,
    update,
    list,
    listByUserID,
    deleteUserVaccinationDetails,
    documentUpload,
} = require('./user_vaccination_details_controller');

const { validate } = require('../../middleware/validation');
const {
    getAddUserVaccinationDetailsValidate: { body: getAddUserVaccinationDetailsValidateSchema },
} = require('./user_vaccination_details_validator');
const { logger } = require('../utility/util_keys');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.post(
    '/document_upload',
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    (req, res, next) => {
        user_document(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                logger.error({ err }, 'Vaccination Log Error');
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    documentUpload,
);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    validate([{ schema: getAddUserVaccinationDetailsValidateSchema, property: 'body' }]),
    insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    validate([{ schema: getAddUserVaccinationDetailsValidateSchema, property: 'body' }]),
    update,
);
route.delete('/:id', [userPolicyAuthentication([])], deleteUserVaccinationDetails);
route.get(
    '/',
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:profile_view',
            'user_management:student_management:registration_pending:submitted:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:invalid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
        ]),
    ],
    list,
);
route.get(
    '/:user_id',
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:profile_view',
            'user_management:student_management:registration_pending:submitted:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:invalid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
        ]),
    ],
    listByUserID,
);

module.exports = route;
