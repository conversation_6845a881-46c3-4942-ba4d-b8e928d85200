const express = require('express');
const route = express.Router();
const roles_offices = require('./roles_offices_controller');
const validator = require('./roles_offices_validator');

route.post('/create_list',validator.list,roles_offices.create);
route.get('/get_list', roles_offices.get);
route.put('/update_list/:id',validator.list,validator.list_id,roles_offices.update);
route.delete('/delete_list/:id',validator.list_id, roles_offices.delete);
route.get('/get_list_id/:id',validator.list_id,roles_offices.get_id);
route.get('/get_roles',roles_offices.getroles);
route.get('/get_offices',roles_offices.getoffices);
route.get('/get_office_list_roles/:staff_id',roles_offices.getofficelistandroles);


module.exports = route;