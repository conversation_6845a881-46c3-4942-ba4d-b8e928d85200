const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const document_manager = new Schema(
    {
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: constant.USER,
            require: true,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_COURSE,
        },
        sessionOrScheduleIds: [
            {
                type: {
                    type: String,
                    trim: true,
                },
                _id: {
                    type: Schema.Types.ObjectId,
                },
            },
        ],
        starred: [
            {
                type: Schema.Types.ObjectId,
            },
        ],
        type: String,
        name: String,
        title: String,
        url: {
            type: String,
            require: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        courseAdmin: {
            type: Boolean,
            default: false,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
        },
        year_no: {
            type: String,
        },
        level_no: {
            type: String,
        },
        term: {
            type: String,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
        },
        remote: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DOCUMENT_MANAGER, document_manager);
