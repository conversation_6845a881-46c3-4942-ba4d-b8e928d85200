const express = require('express');
const route = express.Router();
const multer = require('multer');
const { validate } = require('../../../middleware/validation');
const {
    loginSchema: { body: loginBodySchema },
    authLoginSchema: { body: authLoginBodySchema },
    authMSTeamSSOLoginSchema: { body: authMSTeamSSOLoginBodySchema },
    authLoggedInSchema: { body: authLoggedInBodySchema },
    authSSOSchema: { body: authSSOBodySchema },
    tokenSchema: { body: tokenBodySchema },
    otpSchema: { body: otpBodySchema },
    setPasswordSchema: { body: setPasswordBodySchema },
    logoutSchema: { body: logoutBodySchema },
    fcmUpdateBySchema: { params: fcmUpdateByParamSchema, body: fcmUpdateByBodySchema },
    authUserDeviceRegistrationSchema: { body: authUserDeviceRegistrationBodySchema },
} = require('./user_validate_schema');
const {
    login,
    token,
    forgetPassword,
    otpVerify,
    updatePassword,
    faceVerify,
    logout,
    fcmUpdate,
    loginService,
    loggedInService,
    parentAuthSignup,
    parentOTPVerify,
    parentSetPassword,
    parentAuthLogin,
    parentAuthLoggedInService,
    getSessions,
    viewCourseStaffs,
    parentAuthForget,
    getScheduleCount,
    recentAssessment,
    courseAssignmentLog,
    singleCourseAssignmentLogin,
    childEmergencyDetails,
    cancelledEmergencyAlert,
    emergencyAlertStartTime,
    viewNotificationInParent,
    outComeDetails,
    ssoLoginService,
    ssoService,
    mobileDeviceLogs,
    authUserDeviceRegistration,
} = require('./user_controller');
const { uploadfile4 } = require('../../utility/file_upload');
const { authMiddleware } = require('../../../middleware');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
const { loginGuestUser } = require('../../guestUserRegister/guestUserRegister.controller');
const catchAsync = require('../../utility/catch-async');
const { loginGuestUserValidate } = require('../../guestUserRegister/guestUserRegister.validate');

const face_verify = uploadfile4.fields([{ name: 'face', maxCount: 1 }]);
route.post(
    '/:id/fcm-update',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([
        { schema: fcmUpdateByParamSchema, property: 'params' },
        { schema: fcmUpdateByBodySchema, property: 'body' },
    ]),
    fcmUpdate,
);
route.post(
    '/forget',
    (req, res, next) => {
        face_verify(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    forgetPassword,
);
route.post(
    '/login',
    [userPolicyAuthentication([])],
    validate([{ schema: loginBodySchema, property: 'body' }]),
    login,
);
route.post(
    '/token',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: tokenBodySchema, property: 'body' }]),
    token,
);

route.post(
    '/otp_verify',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP, defaultPolicy.FORGET])],
    validate([{ schema: otpBodySchema, property: 'body' }]),
    otpVerify,
);
route.post(
    '/set_password',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP, defaultPolicy.FORGET])],
    validate([{ schema: setPasswordBodySchema, property: 'body' }]),
    updatePassword,
);

route.post(
    '/face_verify',
    authMiddleware,
    [userPolicyAuthentication([])],
    (req, res, next) => {
        face_verify(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    faceVerify,
);

route.post(
    '/logout',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: logoutBodySchema, property: 'body' }]),
    logout,
);

// New Login Service
route.post(
    '/authLogin',
    validate([{ schema: authLoginBodySchema, property: 'body' }]),
    loginService,
);
route.post(
    '/authLoggedIn',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: authLoggedInBodySchema, property: 'body' }]),
    loggedInService,
);

route.post(
    '/authUserDeviceRegistration',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: authUserDeviceRegistrationBodySchema, property: 'body' }]),
    authUserDeviceRegistration,
);

// SSO Login
route.post('/authSSO', validate([{ schema: authSSOBodySchema, property: 'body' }]), ssoService);

// MSTeam SSO Login
route.post(
    '/authSSOLogin',
    validate([{ schema: authMSTeamSSOLoginBodySchema, property: 'body' }]),
    ssoLoginService,
);

// Parent App
route.post(
    '/parent/authSignup',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentAuthSignup,
);
route.post(
    '/parent/authOTPVerify',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentOTPVerify,
);
route.put(
    '/parent/authSetPassword',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentSetPassword,
);
route.post(
    '/parent/authLogin',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentAuthLogin,
);
route.post(
    '/parent/authLoggedIn',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentAuthLoggedInService,
);
route.get(
    '/parent/getSessions',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    getSessions,
);
route.get(
    '/parent/viewStaff',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    viewCourseStaffs,
);
route.post(
    '/parent/forget',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    parentAuthForget,
);
route.get(
    '/parent/getScheduleCount',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    getScheduleCount,
);

route.get(
    '/parentApp/recentAssessment',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    recentAssessment,
);
route.get(
    '/parentApp/courseAssignmentLog',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    courseAssignmentLog,
);
route.get(
    '/parentApp/singleCourseAssignmentLogin',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    singleCourseAssignmentLogin,
);
route.get(
    '/parentApp/outComeDetails',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    outComeDetails,
);
//child Emergency
route.post(
    '/child/childEmergencyDetails',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    childEmergencyDetails,
);
route.put(
    '/child/cancelledEmergencyAlert',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    cancelledEmergencyAlert,
);
route.get(
    '/child/emergencyAlertStartTime',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    emergencyAlertStartTime,
);
route.get(
    '/child/viewNotificationInParent',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    viewNotificationInParent,
);
route.post('/guestUser/login', validate(loginGuestUserValidate), catchAsync(loginGuestUser));

//
route.get('/mobileDeviceLogs', authMiddleware, [userPolicyAuthentication([])], mobileDeviceLogs);
module.exports = route;
