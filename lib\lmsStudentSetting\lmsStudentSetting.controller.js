const lmsStudentSettingSchema = require('./lmsStudentSetting.model');
const globleSessionSettingSchema = require('../global_session_settings/global_session_settings_model');
const lmsStudentSettingCalendarSchema = require('./lmsStudentSettingCalendar.model');
const { convertToMongoObjectId } = require('../utility/common');
const lmsStudentSchema = require('../lmsStudent/lmsStudent.model');
const { MALE, FEMALE, BOTH } = require('../utility/enums');
const {
    DIGI_PROGRAM,
    GENERAL,
    MEDICAL,
    LEAVE,
    USER_BASED,
    ROLE_BASED,
    DC_STUDENT,
    PUBLISHED,
    WARNING_MAIL,
    LMS_STUDENT_SETTING_CALENDAR,
    WARNING_CONFIG_TYPE,
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
} = require('../utility/constants');
const programInput = require('mongoose').model(DIGI_PROGRAM);
const courseSchedule = require('../models/course_schedule');
const institutionCalendarSchema = require('../models/institution_calendar');
const { getSizeOfUrl, getSignedUrl } = require('./lmsStudentSetting.service');
const { getSignedURL } = require('../utility/common_functions');
const roleAssignSchema = require('../models/role_assign');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const { logger } = require('../utility/util_keys');
const studentGroupSchema = require('../models/student_group');
const institutionCalendar = require('../models/institution_calendar');
const { redisClient } = require('../../config/redis-connection');
const warningMailSchema = require('mongoose').model(WARNING_MAIL);
const getStudentLMSSetting = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { classificationType, leaveApplicationCriteria } = query;
        let studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType,
                    isActive: true,
                },
                {
                    generalConfig: 1,
                    warningConfig: 1,
                    classificationType: 1,
                    catagories: 1,
                    termsAndCondition: 1,
                    leaveCalculation: 1,
                    levelApprover: 1,
                    leavePolicy: 1,
                    warningMode: 1,
                    warningConfigBased: 1,
                    comprehensiveWarningConfig: 1,
                    leaveApplicationCriteria: 1,
                },
            )
            .populate({ path: 'warningConfig.notificationRoleIds', select: { name: 1 } })
            .populate({ path: 'warningConfig.meetingRoleIds', select: { name: 1 } })
            .populate({
                path: 'warningConfig.escalationLevels.escalatingRoleIds',
                select: { name: 1 },
            })
            .populate({
                path: 'levelApprover.level.roleIds',
                select: { name: 1 },
            })
            .populate({
                path: 'levelApprover.level.userIds',
                select: { name: 1 },
            })
            .populate({
                path: 'levelApprover.programIds',
                select: { name: 1 },
            })
            .populate({
                path: 'warningConfig.denialManagement.roleIds',
                select: { name: 1 },
            })
            .populate({
                path: 'warningConfig.denialManagement.userIds',
                select: { name: 1 },
            })
            .populate({
                path: 'warningConfig.notificationToStudent.sendNotificationAuthority',
                select: { name: 1 },
            })
            // comprehensiveWarningConfig
            .populate({
                path: 'comprehensiveWarningConfig.notificationRoleIds',
                select: { name: 1 },
            })
            .populate({
                path: 'comprehensiveWarningConfig.denialManagement.roleIds',
                select: { name: 1 },
            })
            .populate({
                path: 'comprehensiveWarningConfig.denialManagement.userIds',
                select: { name: 1 },
            })
            .populate({
                path: 'comprehensiveWarningConfig.notificationToStudent.sendNotificationAuthority',
                select: { name: 1 },
            })
            .lean();
        let programInputs;
        if (!studentSettingData) {
            programInputs = await programInput
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isDeleted: false,
                        isActive: true,
                    },
                    { _id: 1 },
                )
                .sort({ name: 1 });
            programInputs = programInputs.map((i) => convertToMongoObjectId(i._id));
            studentSettingData = await lmsStudentSettingSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType,
                leaveCalculation: 'sessions',
                leaveApplicationCriteria: leaveApplicationCriteria || DAY,
                levelApprover: [
                    {
                        programIds: programInputs,
                        isExceptionalProgram: false,
                    },
                ],
            });
        }
        if (
            studentSettingData.levelApprover === null ||
            (studentSettingData.levelApprover && studentSettingData.levelApprover.length === 0)
        ) {
            programInputs = await programInput.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            programInputs = programInputs.map((i) => convertToMongoObjectId(i._id));
            studentSettingData = await lmsStudentSettingSchema
                .findByIdAndUpdate(
                    { _id: studentSettingData._id },
                    {
                        levelApprover: [
                            {
                                programIds: programInputs,
                                isExceptionalProgram: false,
                            },
                        ],
                    },
                    { new: true },
                )
                .populate({ path: 'warningConfig.notificationRoleIds', select: { name: 1 } })
                .populate({ path: 'warningConfig.meetingRoleIds', select: { name: 1 } })
                .populate({
                    path: 'warningConfig.escalationLevels.escalatingRoleIds',
                    select: { name: 1 },
                })
                .populate({
                    path: 'levelApprover.level.roleIds',
                    select: { name: 1 },
                })
                .populate({
                    path: 'levelApprover.level.userIds',
                    select: { name: 1 },
                })
                .populate({
                    path: 'levelApprover.programIds',
                    select: { name: 1 },
                })
                .populate({
                    path: 'warningConfig.notificationToStudent.sendNotificationAuthority',
                    select: { name: 1 },
                })
                .lean();
        }
        if (classificationType === LEAVE) {
            if (
                studentSettingData.categories === null ||
                (studentSettingData.catagories && studentSettingData.catagories.length === 0)
            ) {
                studentSettingData = await lmsStudentSettingSchema
                    .findByIdAndUpdate(
                        {
                            _id: convertToMongoObjectId(studentSettingData._id),
                        },
                        {
                            catagories: [
                                {
                                    categoryName: GENERAL,
                                    isDefault: true,
                                    isActive: false,
                                },
                                {
                                    categoryName: MEDICAL,
                                    isDefault: true,
                                    isActive: false,
                                },
                            ],
                        },
                        {
                            new: true,
                        },
                    )
                    .populate({ path: 'warningConfig.notificationRoleIds', select: { name: 1 } })
                    .populate({ path: 'warningConfig.meetingRoleIds', select: { name: 1 } })
                    .populate({
                        path: 'warningConfig.escalationLevels.escalatingRoleIds',
                        select: { name: 1 },
                    })
                    .populate({
                        path: 'levelApprover.level.roleIds',
                        select: { name: 1 },
                    })
                    .populate({
                        path: 'levelApprover.level.userIds',
                        select: { name: 1 },
                    })
                    .populate({
                        path: 'levelApprover.programIds',
                        select: { name: 1 },
                    })
                    .populate({
                        path: 'warningConfig.notificationToStudent.sendNotificationAuthority',
                        select: { name: 1 },
                    })
                    .lean();
            }
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: studentSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateGeneraConfig = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId } = params;
        const { parentApplyPermission, parentAcknowledgePermission } = body;

        const studentSettingData = await lmsStudentSettingSchema
            .updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        ...(typeof parentApplyPermission === 'boolean' && {
                            'generalConfig.parentApplyPermission': parentApplyPermission,
                        }),
                        ...(typeof parentAcknowledgePermission === 'boolean' && {
                            'generalConfig.parentAcknowledgePermission':
                                parentAcknowledgePermission,
                        }),
                    },
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const comprehensiveWarningCheck = (studentSettingData) => {
    return !!(
        studentSettingData &&
        studentSettingData.warningMode &&
        studentSettingData.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
    );
};

const createCatagories = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, categoryName, percentage } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $push: {
                        catagories: {
                            categoryName,
                            percentage,
                        },
                    },
                },
                {
                    new: true,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD' };
        const classificationType = studentSettingData.classificationType;
        const isComprehensiveWarning = comprehensiveWarningCheck(studentSettingData);
        if (classificationType === LEAVE) {
            const denialWarning = (
                isComprehensiveWarning
                    ? studentSettingData.comprehensiveWarningConfig
                    : studentSettingData.warningConfig
            ).sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            const denialWarningId = convertToMongoObjectId(denialWarning[0]._id);
            const newCategory = studentSettingData.catagories.find(
                (category) => category.categoryName === categoryName,
            );
            newCategory.categoryId = convertToMongoObjectId(newCategory._id);
            const studentSettingWarningUpdateData = await lmsStudentSettingSchema
                .findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(settingId),
                    },
                    {
                        $push: isComprehensiveWarning
                            ? {
                                  'comprehensiveWarningConfig.$[i].categoryWisePercentage':
                                      newCategory,
                              }
                            : { 'warningConfig.$[i].categoryWisePercentage': newCategory },
                    },
                    {
                        arrayFilters: [
                            {
                                'i._id': denialWarningId,
                            },
                        ],
                    },
                )
                .lean();
        }
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteCatagories = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, categoryId } = body;
        const studentSettingData = await lmsStudentSettingSchema.findByIdAndUpdate(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            {
                $pull: {
                    catagories: { _id: convertToMongoObjectId(categoryId) },
                },
            },
        );
        if (studentSettingData.classificationType === 'leave') {
            const isComprehensiveWarning = comprehensiveWarningCheck(studentSettingData);
            const removeWarningCategory = await lmsStudentSettingSchema.updateOne(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $pull: isComprehensiveWarning
                        ? {
                              'comprehensiveWarningConfig.$[].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          }
                        : {
                              'warningConfig.$[].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          },
                },
            );
            const categoryName = studentSettingData.catagories.find(
                (element) => element._id.toString() === categoryId.toString(),
            ).categoryName;
            const denialWarning = (
                isComprehensiveWarning
                    ? studentSettingData.comprehensiveWarningConfig
                    : studentSettingData.warningConfig
            ).sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            if (
                categoryName.toLowerCase() ===
                denialWarning[0].unappliedLeaveConsideredAs.toLowerCase()
            ) {
                const studentSettingData = await lmsStudentSettingSchema.updateOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(settingId),
                    },
                    {
                        $set: isComprehensiveWarning
                            ? {
                                  'comprehensiveWarningConfig.$[warning].unappliedLeaveConsideredAs':
                                      null,
                              }
                            : {
                                  'warningConfig.$[warning].unappliedLeaveConsideredAs': null,
                              },
                    },
                    {
                        arrayFilters: [
                            {
                                'warning._id': convertToMongoObjectId(denialWarning[0]._id),
                            },
                        ],
                    },
                );
                if (!studentSettingData)
                    return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
                return { statusCode: 200, message: 'DATA_UPDATED' };
            }
        }
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DELETE' };
        return { statusCode: 200, message: 'DATA_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editCategoryName = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId, settingId, categoryName, percentage, isActive, isDefault } = body;
        const getSetting = await lmsStudentSettingSchema
            .findOne(
                { _id: convertToMongoObjectId(settingId) },
                { 'catagories.categoryName': 1, 'catagories._id': 1 },
            )
            .lean();
        const currentCategory = getSetting.catagories.find(
            (categoryElement) => categoryElement._id.toString() === categoryId.toString(),
        );
        const studentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        ...(categoryName && {
                            'catagories.$[categoryId].categoryName': categoryName,
                        }),
                        ...(typeof percentage === 'number' && {
                            'catagories.$[categoryId].percentage': percentage,
                        }),
                        ...(typeof isActive === 'boolean' && {
                            'catagories.$[categoryId].isActive': isActive,
                        }),
                        ...(typeof isDefault === 'boolean' && {
                            'catagories.$[categoryId].isDefault': isDefault,
                        }),
                    },
                },
                {
                    arrayFilters: [
                        {
                            'categoryId._id': convertToMongoObjectId(categoryId),
                        },
                    ],
                    new: true,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        const classificationType = studentSettingData.classificationType;
        if (classificationType === LEAVE) {
            const isComprehensiveWarning = comprehensiveWarningCheck(studentSettingData);
            const denialWarning = (
                isComprehensiveWarning
                    ? studentSettingData.comprehensiveWarningConfig
                    : studentSettingData.warningConfig
            ).sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            let isChangeUnappliedLeave = false;
            let denialWarningId;
            if (denialWarning.length) {
                if (
                    denialWarning[0].unappliedLeaveConsideredAs != null &&
                    denialWarning[0].unappliedLeaveConsideredAs.toLowerCase() ===
                        currentCategory.categoryName.toLowerCase()
                ) {
                    isChangeUnappliedLeave = true;
                }
                denialWarningId = denialWarning[0]._id;
            }
            const query = {
                _id: convertToMongoObjectId(settingId),
            };
            const updateObj = {};
            if (
                isChangeUnappliedLeave ||
                categoryName ||
                percentage ||
                typeof isActive === 'boolean'
            ) {
                if (isChangeUnappliedLeave) {
                    updateObj[
                        isComprehensiveWarning
                            ? 'comprehensiveWarningConfig.$[i].categoryWisePercentage.$[j].percentage'
                            : 'warningConfig.$[i].unappliedLeaveConsideredAs'
                    ] = categoryName;
                }

                if (categoryName) {
                    updateObj[
                        isComprehensiveWarning
                            ? 'comprehensiveWarningConfig.$[i].categoryWisePercentage.$[j].percentage'
                            : 'warningConfig.$[i].categoryWisePercentage.$[j].categoryName'
                    ] = categoryName;
                }

                if (typeof percentage === 'number') {
                    updateObj[
                        isComprehensiveWarning
                            ? 'comprehensiveWarningConfig.$[i].categoryWisePercentage.$[j].percentage'
                            : 'warningConfig.$[i].categoryWisePercentage.$[j].percentage'
                    ] = percentage;
                }

                if (typeof isActive === 'boolean' && !isActive) {
                    updateObj.$pull = isComprehensiveWarning
                        ? {
                              'comprehensiveWarningConfig.$[i].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          }
                        : {
                              'warningConfig.$[i].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          };
                }
            }
            const studentSettingWarningUpdateData = await lmsStudentSettingSchema.findByIdAndUpdate(
                query,
                { $set: updateObj },
                {
                    arrayFilters: [
                        { 'i._id': convertToMongoObjectId(denialWarningId) },
                        { 'j.categoryId': convertToMongoObjectId(categoryId) },
                    ],
                    new: true,
                },
            );
        }
        if (categoryName) {
            const lmsStudentData = await lmsStudentSchema.updateMany(
                {
                    categoryId: convertToMongoObjectId(categoryId),
                },
                {
                    categoryName,
                },
            );
            await lmsDenialSchema.updateMany(
                {
                    'categoryWisePercentage.categoryId': convertToMongoObjectId(categoryId),
                },
                {
                    ...(categoryName && {
                        'categoryWisePercentage.$[j].categoryName': categoryName,
                    }),
                },
                {
                    arrayFilters: [{ 'j.categoryId': convertToMongoObjectId(categoryId) }],
                },
            );
        }
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createCatagoriesTypes = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, categoryId, types, classificationType } = body;
        let studentSettingData;
        const updateTypeName = async ({ studentSettingData, types }) => {
            const bulkWrites = [];
            const studentSettingCategoryData = studentSettingData.catagories.find(
                (category) => category._id.toString() === categoryId.toString(),
            );
            for (const type of studentSettingCategoryData.types) {
                for (const userType of types) {
                    if (
                        userType._id &&
                        type._id.toString() === userType._id.toString() &&
                        type.typeName != userType.typeName
                    ) {
                        bulkWrites.push({
                            updateMany: {
                                filter: { typeId: userType._id },
                                update: { $set: { typeName: userType.typeName } },
                            },
                        });
                    }
                }
            }
            if (bulkWrites.length) {
                await lmsStudentSchema.bulkWrite(bulkWrites);
            }
        };
        if (classificationType === 'leave') {
            studentSettingData = await lmsStudentSettingSchema.findByIdAndUpdate(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $push: { 'catagories.$[categoryId].types': types },
                },
                {
                    arrayFilters: [
                        {
                            'categoryId._id': convertToMongoObjectId(categoryId),
                        },
                    ],
                },
            );
            if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD_TYPE' };
            updateTypeName({ studentSettingData, types });
            return { statusCode: 200, message: 'DATA_ADDED' };
        }
        if (classificationType === 'on_duty') {
            for (const type of types) {
                type.noHours =
                    type.frequency.frequencyBy === 'month'
                        ? type.frequency.setFrequency * 12
                        : type.frequency.setFrequency * 1;
            }
            studentSettingData = await lmsStudentSettingSchema
                .findByIdAndUpdate(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(settingId),
                    },
                    {
                        $set: { 'catagories.$[categoryId].types': types },
                    },
                    {
                        arrayFilters: [
                            {
                                'categoryId._id': convertToMongoObjectId(categoryId),
                            },
                        ],
                    },
                )
                .lean();
            if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD_TYPE' };
            updateTypeName({ studentSettingData, types });
            return { statusCode: 200, message: 'DATA_ADDED' };
        }
        studentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: { 'catagories.$[categoryId].types': types },
                },
                {
                    arrayFilters: [
                        {
                            'categoryId._id': convertToMongoObjectId(categoryId),
                        },
                    ],
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD_TYPE' };
        updateTypeName({ studentSettingData, types });
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteCatagoriesTypes = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, categoryId, typesId } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $pull: {
                        'catagories.$[categoryId].types': { _id: convertToMongoObjectId(typesId) },
                    },
                },
                {
                    arrayFilters: [
                        {
                            'categoryId._id': convertToMongoObjectId(categoryId),
                        },
                    ],
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DELETE_TYPE' };
        return { statusCode: 200, message: 'DATA_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCategoryTypes = async ({ body = {} }) => {
    try {
        const {
            settingId,
            typesId,
            typeName,
            typeDescription,
            typeColor,
            noHours,
            attachmentMandatory,
            allowedDuringExam,
            attendanceStatus,
            setFrequency,
            frequencyBy,
            frequencyByMonth,
            isActive,
            percentage,
            isReasonFromApplicant,
            isProofToBeAttached,
            excludePresent,
            attendanceType,
        } = body;
        let studentSettingData;
        studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    classificationType: 1,
                    'catagories.types._id': 1,
                    'catagories.types.frequency': 1,
                },
            )
            .lean();
        if (studentSettingData && studentSettingData.classificationType === 'on_duty') {
            let onDutyNoHours;
            if (setFrequency) {
                for (const category of studentSettingData.catagories) {
                    for (const type of category.types) {
                        if (type._id.toString() === typesId.toString()) {
                            onDutyNoHours = frequencyBy
                                ? frequencyBy === 'month'
                                    ? setFrequency * 12
                                    : setFrequency * 1
                                : type.frequency.frequencyBy === 'month'
                                ? setFrequency * 12
                                : setFrequency * 1;
                        }
                    }
                }
            } else if (frequencyBy) {
                for (const category of studentSettingData.catagories) {
                    for (const type of category.types) {
                        if (type._id.toString() === typesId.toString()) {
                            onDutyNoHours =
                                frequencyBy === 'month'
                                    ? type.frequency.setFrequency * 12
                                    : type.frequency.setFrequency * 1;
                        }
                    }
                }
            }
            studentSettingData = await lmsStudentSettingSchema.updateOne(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    ...(typeName && { 'catagories.$[].types.$[typeId].typeName': typeName }),
                    ...(typeDescription && {
                        'catagories.$[].types.$[typeId].typeDescription': typeDescription,
                    }),
                    ...(typeColor && { 'catagories.$[].types.$[typeId].typeColor': typeColor }),

                    ...((setFrequency || frequencyBy) && {
                        'catagories.$[].types.$[typeId].noHours': onDutyNoHours,
                    }),

                    ...(typeof attachmentMandatory === 'boolean' && {
                        'catagories.$[].types.$[typeId].attachmentMandatory': attachmentMandatory,
                    }),
                    ...(typeof allowedDuringExam === 'boolean' && {
                        'catagories.$[].types.$[typeId].allowedDuringExam': allowedDuringExam,
                    }),
                    ...(typeof attendanceStatus === 'boolean' && {
                        'catagories.$[].types.$[typeId].attendanceStatus': attendanceStatus,
                    }),
                    ...(setFrequency && {
                        'catagories.$[].types.$[typeId].frequency.setFrequency': setFrequency,
                    }),
                    ...(frequencyBy && {
                        'catagories.$[].types.$[typeId].frequency.frequencyBy': frequencyBy,
                    }),
                    ...(frequencyByMonth && {
                        'catagories.$[].types.$[typeId].frequency.frequencyByMonth':
                            frequencyByMonth,
                    }),
                    ...(typeof isActive === 'boolean' && {
                        'catagories.$[].types.$[typeId].isActive': isActive,
                    }),
                    ...(typeof percentage === 'number' && {
                        'catagories.$[].types.$[typeId].percentage': percentage,
                    }),
                    ...(typeof isReasonFromApplicant === 'boolean' && {
                        'catagories.$[].types.$[typeId].isReasonFromApplicant':
                            isReasonFromApplicant,
                    }),
                    ...(typeof isProofToBeAttached === 'boolean' && {
                        'catagories.$[].types.$[typeId].isProofToBeAttached': isProofToBeAttached,
                    }),
                    ...(typeof excludePresent === 'boolean' && {
                        'catagories.$[].types.$[typeId].excludePresent': excludePresent,
                    }),
                    ...(attendanceType && {
                        'catagories.$[].types.$[typeId].attendanceType': attendanceType,
                    }),
                },
                {
                    arrayFilters: [
                        {
                            'typeId._id': convertToMongoObjectId(typesId),
                        },
                    ],
                },
            );
            if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_TYPE' };
            return { statusCode: 200, message: 'DATA_UPDATED' };
        }
        studentSettingData = await lmsStudentSettingSchema.updateOne(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                ...(typeName && { 'catagories.$[].types.$[typeId].typeName': typeName }),
                ...(typeDescription && {
                    'catagories.$[].types.$[typeId].typeDescription': typeDescription,
                }),
                ...(typeColor && { 'catagories.$[].types.$[typeId].typeColor': typeColor }),
                ...(noHours && { 'catagories.$[].types.$[typeId].noHours': noHours }),
                ...(typeof attachmentMandatory === 'boolean' && {
                    'catagories.$[].types.$[typeId].attachmentMandatory': attachmentMandatory,
                }),
                ...(typeof allowedDuringExam === 'boolean' && {
                    'catagories.$[].types.$[typeId].allowedDuringExam': allowedDuringExam,
                }),
                ...(typeof attendanceStatus === 'boolean' && {
                    'catagories.$[].types.$[typeId].attendanceStatus': attendanceStatus,
                }),
                ...(setFrequency && {
                    'catagories.$[].types.$[typeId].frequency.setFrequency': setFrequency,
                }),
                ...(frequencyBy && {
                    'catagories.$[].types.$[typeId].frequency.frequencyBy': frequencyBy,
                }),
                ...(frequencyByMonth && {
                    'catagories.$[].types.$[typeId].frequency.frequencyByMonth': frequencyByMonth,
                }),
                ...(typeof isActive === 'boolean' && {
                    'catagories.$[].types.$[typeId].isActive': isActive,
                }),
                ...(typeof percentage === 'number' && {
                    'catagories.$[].types.$[typeId].percentage': percentage,
                }),
                ...(typeof isReasonFromApplicant === 'boolean' && {
                    'catagories.$[].types.$[typeId].isReasonFromApplicant': isReasonFromApplicant,
                }),
                ...(typeof isProofToBeAttached === 'boolean' && {
                    'catagories.$[].types.$[typeId].isProofToBeAttached': isProofToBeAttached,
                }),
                ...(typeof excludePresent === 'boolean' && {
                    'catagories.$[].types.$[typeId].excludePresent': excludePresent,
                }),
                ...(attendanceType && {
                    'catagories.$[].types.$[typeId].attendanceType': attendanceType,
                }),
            },
            {
                arrayFilters: [
                    {
                        'typeId._id': convertToMongoObjectId(typesId),
                    },
                ],
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_TYPE' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateTermsAndCondition = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, termsAndCondition, leaveCalculation, leaveApplicationCriteria } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        termsAndCondition,
                        leaveCalculation,
                        leaveApplicationCriteria,
                    },
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const addProgramLevel = async ({ body = {} }) => {
    try {
        const { settingId, isExceptionalProgram, programIds } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    levelApprover: 1,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'NO_DATA' };
        if (isExceptionalProgram) {
            const mainProgram = studentSettingData.levelApprover.find((level) => {
                return !level.isExceptionalProgram;
            });
            if (mainProgram.programIds.length) {
                let mainProgramIds = mainProgram.programIds.map((id) => id.toString());
                mainProgramIds = mainProgramIds
                    .filter((element) => !programIds.includes(element))
                    .map((i) => {
                        return convertToMongoObjectId(i);
                    });
                const studentSettingUpdateData = await lmsStudentSettingSchema.findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(studentSettingData._id),
                    },
                    {
                        'levelApprover.$[approver].programIds': mainProgramIds,
                    },
                    {
                        arrayFilters: [
                            {
                                'approver._id': convertToMongoObjectId(mainProgram._id),
                            },
                        ],
                    },
                );
            }
        }
        const programObjectIds = programIds.map((i) => {
            return convertToMongoObjectId(i);
        });
        const studentSettingUpdateProgram = await lmsStudentSettingSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(studentSettingData._id),
            },
            {
                $push: {
                    levelApprover: { programIds: programObjectIds, isExceptionalProgram },
                },
            },
        );
        if (!studentSettingUpdateProgram) return { statusCode: 410, message: 'UNABLE_TO_UPDATE' };
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const editProgramLevel = async ({ body = {} }) => {
    try {
        const {
            isExceptionalProgram,
            programIds,
            levelApproverId,
            removedProgramIds,
            addedProgramIds,
            settingId,
        } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    levelApprover: 1,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'NO_DATA' };
        if (isExceptionalProgram) {
            const mainProgram = studentSettingData.levelApprover.find((level) => {
                return level.isExceptionalProgram === false;
            });
            let mainProgramIds = mainProgram.programIds.map((id) => id.toString());
            if (removedProgramIds && removedProgramIds.length) {
                mainProgramIds = [...mainProgramIds, ...removedProgramIds];
            }
            if (addedProgramIds && addedProgramIds.length) {
                mainProgramIds = mainProgramIds
                    .filter((element) => !addedProgramIds.includes(element))
                    .map((i) => {
                        return convertToMongoObjectId(i);
                    });
            }
            const studentSettingUpdateData = await lmsStudentSettingSchema.findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(studentSettingData._id),
                },
                {
                    'levelApprover.$[approver].programIds': mainProgramIds,
                },
                {
                    arrayFilters: [
                        {
                            'approver._id': convertToMongoObjectId(mainProgram._id),
                        },
                    ],
                },
            );
        }
        const programObjectIds = programIds.map((i) => {
            return convertToMongoObjectId(i);
        });
        let studentSettingUpdateProgram;
        if (programIds.length !== 0) {
            studentSettingUpdateProgram = await lmsStudentSettingSchema.findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(studentSettingData._id),
                },
                {
                    'levelApprover.$[approver].programIds': programObjectIds,
                },
                {
                    new: true,
                    arrayFilters: [
                        {
                            'approver._id': convertToMongoObjectId(levelApproverId),
                        },
                    ],
                },
            );
        } else {
            studentSettingUpdateProgram = await lmsStudentSettingSchema.findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(studentSettingData._id),
                },
                {
                    $pull: {
                        levelApprover: {
                            _id: convertToMongoObjectId(levelApproverId),
                        },
                    },
                },
                {
                    new: true,
                },
            );
        }
        if (!studentSettingUpdateProgram) return { statusCode: 410, message: 'UNABLE_TO_UPDATE' };
        return { statusCode: 200, message: 'DATA_ADDED', data: studentSettingUpdateProgram };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const divideGenderSegregation = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { genderSegregation, classificationType, levelApproverId } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType,
                    isActive: true,
                },
                {
                    levelApprover: 1,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'NO_DATA' };
        const levelApprover = studentSettingData.levelApprover.find(
            (ele) => ele._id.toString() === levelApproverId.toString(),
        );
        const { programIds, isExceptionalProgram, level } = levelApprover;
        let bothLevel;
        if (genderSegregation) {
            const maleLevel = level
                .map(({ _id, ...rest }) => rest)
                .map((x) => {
                    x.gender = MALE;
                    return x;
                });
            const femaleLevel = level
                .map(({ _id, ...rest }) => rest)
                .map((x) => {
                    x.gender = FEMALE;
                    return x;
                });
            bothLevel = [...maleLevel, ...femaleLevel];
        } else {
            bothLevel = level
                .map(({ _id, ...rest }) => rest)
                .filter((x) => x.gender === MALE)
                .map((x) => {
                    x.gender = BOTH;
                    return x;
                });
        }
        const studentSettingDataUpdate = await lmsStudentSettingSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(studentSettingData._id),
            },
            {
                'levelApprover.$[levelApprover].level': bothLevel,
                ...(typeof genderSegregation === 'boolean' && {
                    'levelApprover.$[levelApprover].genderSegregation': genderSegregation,
                }),
            },
            {
                new: true,
                arrayFilters: [
                    {
                        'levelApprover._id': convertToMongoObjectId(levelApproverId),
                    },
                ],
            },
        );
        if (!studentSettingDataUpdate) return { statusCode: 410, message: 'UNABLE_TO_UPDATE' };
        return { statusCode: 200, message: 'DATA_ADDED', data: studentSettingDataUpdate };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addLeaveApprovalLevel = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, levelApproverId, level } = body;
        let studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    levelApprover: 1,
                },
            )
            .populate({ path: 'levelApprover.level.userIds', select: { name: 1, user_id: 1 } })
            .populate({ path: 'levelApprover.level.roleIds', select: { name: 1 } })
            .lean();
        const levelApproverData = studentSettingData.levelApprover.find(
            (levelApproverElement) =>
                levelApproverElement._id.toString() === levelApproverId.toString(),
        );
        const matchingName = [];
        let message;
        if (levelApproverData && levelApproverData.level.length) {
            for (const levelElement of levelApproverData.level) {
                if (
                    level.categoryBased === ROLE_BASED &&
                    levelElement.categoryBased === ROLE_BASED
                ) {
                    levelElement.roleIds.filter((roleElement) => {
                        if (levelApproverData.genderSegregation === false) {
                            if (
                                level.roleIds.some(
                                    (levelRoleElement) =>
                                        levelRoleElement.toString() === roleElement._id.toString(),
                                )
                            ) {
                                matchingName.push(roleElement);
                            }
                        } else {
                            if (levelApproverData.genderSegregation === true) {
                                if (
                                    level.roleIds.some(
                                        (levelRoleElement) =>
                                            levelRoleElement.toString() ===
                                                roleElement._id.toString() &&
                                            level.gender.toLowerCase() ===
                                                levelElement.gender.toLowerCase(),
                                    )
                                ) {
                                    matchingName.push(roleElement);
                                }
                            }
                        }
                    });
                    message = 'Role already existed';
                }
                if (
                    level.categoryBased === USER_BASED &&
                    levelElement.categoryBased === USER_BASED
                ) {
                    levelElement.userIds.filter((userElement) => {
                        if (
                            level.userIds.some(
                                (levelUserElement) =>
                                    levelUserElement.toString() === userElement._id.toString(),
                            )
                        ) {
                            matchingName.push(userElement);
                        }
                    });
                    message = 'User already existed';
                }
            }
        }

        if (matchingName && matchingName.length) {
            return { statusCode: 200, message, data: matchingName };
        }
        const userRoleMatching = [];
        if (level.categoryBased === USER_BASED) {
            const roleAssignData = await roleAssignSchema
                .find(
                    {
                        _user_id: {
                            $in: level.userIds.map((userIdElement) =>
                                convertToMongoObjectId(userIdElement),
                            ),
                        },
                    },
                    {
                        'roles._role_id': 1,
                    },
                )
                .populate({ path: '_user_id', select: { name: 1, _user_id: 1 } })
                .populate({ path: 'roles._role_id', select: { name: 1 } });
            if (levelApproverData && levelApproverData.level.length) {
                for (const levelElement of levelApproverData.level) {
                    if (levelElement.categoryBased === ROLE_BASED) {
                        for (const roleAssign of roleAssignData) {
                            for (const roleAssignElement of roleAssign.roles) {
                                for (const roleElement of levelElement.roleIds) {
                                    for (const levelUserElement of level.userIds) {
                                        if (
                                            roleAssignElement._role_id &&
                                            roleAssign._user_id &&
                                            roleElement._id.toString() ===
                                                roleAssignElement._role_id._id.toString() &&
                                            levelUserElement.toString() ===
                                                roleAssign._user_id._id.toString()
                                        ) {
                                            userRoleMatching.push({
                                                roleName: roleElement,
                                                userName: roleAssign._user_id,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (userRoleMatching && userRoleMatching.length) {
            return {
                statusCode: 200,
                message: 'User already exited in role',
                data: userRoleMatching,
            };
        }
        const roleUserMatching = [];
        if (level.categoryBased === ROLE_BASED) {
            const roleAssignData = await roleAssignSchema
                .find(
                    {
                        'roles._role_id': {
                            $in: level.roleIds.map((roleElement) =>
                                convertToMongoObjectId(roleElement),
                            ),
                        },
                    },
                    {
                        _user_id: 1,
                        'roles._role_id.$': 1,
                    },
                )
                .populate({ path: '_user_id', select: { name: 1 } })
                .populate({ path: 'roles._role_id', select: { name: 1 } })
                .lean();
            if (levelApproverData && levelApproverData.level.length) {
                for (const levelElement of levelApproverData.level) {
                    if (levelElement.categoryBased === USER_BASED) {
                        for (const userAssign of roleAssignData) {
                            for (const userElement of levelElement.userIds) {
                                for (const levelRoleElement of level.roleIds) {
                                    for (const roleElement of userAssign.roles) {
                                        if (
                                            userAssign._user_id &&
                                            roleElement._role_id &&
                                            userElement._id.toString() ===
                                                userAssign._user_id._id.toString() &&
                                            levelRoleElement.toString() ===
                                                roleElement._role_id._id.toString()
                                        ) {
                                            roleUserMatching.push({
                                                roleName: roleElement._role_id,
                                                userName: userAssign._user_id,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (roleUserMatching && roleUserMatching.length) {
            return {
                statusCode: 200,
                message: 'role already exited in user',
                data: roleUserMatching,
            };
        }
        studentSettingData = await lmsStudentSettingSchema
            .updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $push: {
                        'levelApprover.$[levelApprover].level': level,
                    },
                },
                {
                    arrayFilters: [
                        {
                            'levelApprover._id': convertToMongoObjectId(levelApproverId),
                        },
                    ],
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD' };
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteLevel = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, levelApproverId, levelId } = body;
        const studentSettingData = await lmsStudentSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            {
                $pull: {
                    'levelApprover.$[levelApprover].level': {
                        _id: convertToMongoObjectId(levelId),
                    },
                },
            },
            {
                arrayFilters: [
                    {
                        'levelApprover._id': convertToMongoObjectId(levelApproverId),
                    },
                ],
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DELETE' };
        return { statusCode: 200, message: 'DATA_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editLevels = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            settingId,
            levelId,
            levelName,
            roleIds,
            approvalConfig,
            categoryBased,
            userIds,
            turnAroundTime,
            escalateRequest,
            skipPreviousLevelApproval,
            gender,
            overwritePreviousLevelApproval,
        } = body;
        let studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                    'levelApprover.level._id': convertToMongoObjectId(levelId),
                },
                {
                    'levelApprover.level.$': 1,
                    'levelApprover.genderSegregation': 1,
                },
            )
            .populate({ path: 'levelApprover.level.userIds', select: { name: 1, user_id: 1 } })
            .populate({ path: 'levelApprover.level.roleIds', select: { name: 1 } });
        const matchingName = [];
        let message;
        for (const levelApproverElement of studentSettingData.levelApprover) {
            for (const levelElement of levelApproverElement.level) {
                if (levelApproverElement.genderSegregation === false) {
                    if (
                        levelElement.categoryBased === ROLE_BASED &&
                        categoryBased === ROLE_BASED &&
                        !(levelElement._id.toString() === levelId.toString())
                    ) {
                        levelElement.roleIds.filter((roleElement) => {
                            if (
                                roleIds.some(
                                    (levelRoleElement) =>
                                        levelRoleElement.toString() === roleElement._id.toString(),
                                )
                            ) {
                                matchingName.push(roleElement);
                            }
                        });
                        message = 'Role already existed';
                    } else if (
                        categoryBased === USER_BASED &&
                        levelElement.categoryBased === USER_BASED &&
                        !(levelElement._id.toString() === levelId.toString())
                    ) {
                        levelElement.userIds.filter((userElement) => {
                            if (
                                userIds.some(
                                    (levelUserElement) =>
                                        levelUserElement.toString() === userElement._id.toString(),
                                )
                            ) {
                                matchingName.push(userElement);
                            }
                        });
                        message = 'User already existed';
                    }
                } else if (levelApproverElement.genderSegregation === true) {
                    if (
                        levelElement.categoryBased === ROLE_BASED &&
                        categoryBased === ROLE_BASED &&
                        !(levelElement._id.toString() === levelId.toString())
                    ) {
                        levelElement.roleIds.filter((roleElement) => {
                            if (
                                roleIds.some(
                                    (levelRoleElement) =>
                                        levelRoleElement.toString() ===
                                            roleElement._id.toString() &&
                                        levelElement.gender.toLowerCase() === gender.toLowerCase(),
                                )
                            ) {
                                matchingName.push(roleElement);
                            }
                        });
                        message = 'Role already existed';
                    }
                }
            }
        }
        if (matchingName && matchingName.length) {
            return { statusCode: 200, message, data: matchingName };
        }
        const userRoleMatching = [];
        if (categoryBased === USER_BASED) {
            const roleAssignData = await roleAssignSchema
                .find(
                    {
                        _user_id: {
                            $in: userIds.map((userIdElement) =>
                                convertToMongoObjectId(userIdElement),
                            ),
                        },
                    },
                    {
                        'roles._role_id': 1,
                    },
                )
                .populate({ path: '_user_id', select: { name: 1, _user_id: 1 } })
                .populate({ path: 'roles._role_id', select: { name: 1 } });
            for (const levelApproverElement of studentSettingData.levelApprover) {
                for (const levelElement of levelApproverElement.level) {
                    if (
                        levelElement.categoryBased === ROLE_BASED &&
                        !(levelElement._id.toString() === levelId.toString())
                    ) {
                        for (const roleAssign of roleAssignData) {
                            for (const roleAssignElement of roleAssign.roles) {
                                for (const roleElement of levelElement.roleIds) {
                                    for (const levelUserElement of userIds) {
                                        if (
                                            roleAssignElement._role_id &&
                                            roleAssign._user_id &&
                                            roleElement._id.toString() ===
                                                roleAssignElement._role_id._id.toString() &&
                                            levelUserElement.toString() ===
                                                roleAssign._user_id._id.toString()
                                        ) {
                                            userRoleMatching.push({
                                                roleName: roleElement,
                                                userName: roleAssign._user_id,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (userRoleMatching && userRoleMatching.length) {
            return {
                statusCode: 200,
                message: 'User already exited in role',
                data: userRoleMatching,
            };
        }
        const roleUserMatching = [];
        if (categoryBased === ROLE_BASED) {
            const roleAssignData = await roleAssignSchema
                .find(
                    {
                        'roles._role_id': {
                            $in: roleIds.map((roleElement) => convertToMongoObjectId(roleElement)),
                        },
                    },
                    {
                        _user_id: 1,
                        'roles._role_id.$': 1,
                    },
                )
                .populate({ path: '_user_id', select: { name: 1 } })
                .populate({ path: 'roles._role_id', select: { name: 1 } });
            for (const levelApproverElement of studentSettingData.levelApprover) {
                for (const levelElement of levelApproverElement.level) {
                    if (
                        levelElement.categoryBased === USER_BASED &&
                        !(levelElement._id.toString() === levelId.toString())
                    ) {
                        for (const userAssign of roleAssignData) {
                            for (const userElement of levelElement.userIds) {
                                for (const levelRoleElement of roleIds) {
                                    for (const userRoleElement of userAssign.roles) {
                                        if (
                                            userAssign._user_id &&
                                            userRoleElement._role_id &&
                                            userElement._id.toString() ===
                                                userAssign._user_id._id.toString() &&
                                            userRoleElement._role_id._id.toString() ===
                                                levelRoleElement.toString()
                                        ) {
                                            roleUserMatching.push({
                                                roleName: userRoleElement._role_id,
                                                userName: userAssign._user_id,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (roleUserMatching && roleUserMatching.length) {
            return {
                statusCode: 200,
                message: 'role already exited in user',
                data: roleUserMatching,
            };
        }
        studentSettingData = await lmsStudentSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            {
                ...(levelName && { 'levelApprover.$[].level.$[level].levelName': levelName }),
                ...(gender && { 'levelApprover.$[].level.$[level].gender': gender }),
                ...(approvalConfig && {
                    'levelApprover.$[].level.$[level].approvalConfig': approvalConfig,
                }),
                ...(categoryBased && {
                    'levelApprover.$[].level.$[level].categoryBased': categoryBased,
                }),
                ...(turnAroundTime && {
                    'levelApprover.$[].level.$[level].turnAroundTime': turnAroundTime,
                }),
                ...(typeof escalateRequest === 'boolean' && {
                    'levelApprover.$[].level.$[level].escalateRequest': escalateRequest,
                }),
                ...(typeof skipPreviousLevelApproval === 'boolean' && {
                    'levelApprover.$[].level.$[level].skipPreviousLevelApproval':
                        skipPreviousLevelApproval,
                }),
                ...(roleIds && {
                    $set: {
                        'levelApprover.$[].level.$[level].roleIds': roleIds,
                        'levelApprover.$[].level.$[level].userIds': [],
                    },
                }),
                ...(userIds && {
                    $set: {
                        'levelApprover.$[].level.$[level].userIds': userIds,
                        'levelApprover.$[].level.$[level].roleIds': [],
                    },
                }),
                ...(typeof overwritePreviousLevelApproval === 'boolean' && {
                    'levelApprover.$[].level.$[level].overwritePreviousLevelApproval':
                        overwritePreviousLevelApproval,
                }),
            },
            {
                arrayFilters: [
                    {
                        'level._id': convertToMongoObjectId(levelId),
                    },
                ],
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addWarningDenialConfiguration = async ({ headers = {}, body = {} }) => {
    try {
        const { warningConfig, comprehensiveWarningConfig, settingId } = body;
        const studentSettingData = await lmsStudentSettingSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(settingId) },
            {
                $set: {
                    ...(warningConfig && { warningConfig }),
                    ...(comprehensiveWarningConfig && { comprehensiveWarningConfig }),
                },
            },
        );
        if (!studentSettingData)
            return { statusCode: 410, message: 'UNABLE_TO_ADD_WARNING_CONFIGURATION' };
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() - 1);
        const institutionCalendarIdData = await institutionCalendarSchema
            .find(
                {
                    $or: [{ isActive: false }, { isActive: true, end_date: { $lt: currentDate } }],
                },
                { _id: 1 },
            )
            .lean();
        if (institutionCalendarIdData.length) {
            const institutionCalendarId = institutionCalendarIdData.map(({ _id }) => _id);
            const lmsSettingCalendar = await lmsStudentSettingCalendarSchema.find(
                {
                    _institution_calendar_id: { $in: institutionCalendarId },
                },
                { _institution_calendar_id: 1 },
            );
            let updateNewCalendarSetting;
            if (lmsSettingCalendar.length) {
                const lmsSettingCalendarId = lmsSettingCalendar.map(
                    ({ _institution_calendar_id }) => _institution_calendar_id.toString(),
                );
                updateNewCalendarSetting = institutionCalendarId
                    .filter((id) => !lmsSettingCalendarId.includes(id.toString()))
                    .map(({ _id }) => ({
                        _institution_calendar_id: convertToMongoObjectId(_id),
                        warningConfig: studentSettingData.warningConfig,
                        comprehensiveWarningConfig: studentSettingData.comprehensiveWarningConfig,
                    }));
            } else {
                updateNewCalendarSetting = institutionCalendarId.map(({ _id }) => ({
                    _institution_calendar_id: convertToMongoObjectId(_id),
                    warningConfig: studentSettingData.warningConfig,
                    comprehensiveWarningConfig: studentSettingData.comprehensiveWarningConfig,
                }));
            }
            const updateCompletedInActiveCalendarSetting =
                await lmsStudentSettingCalendarSchema.insertMany(updateNewCalendarSetting);
        }
        // remove the settings cache consumed in lmsNewSettingWithOptions func
        const pattern = `${LMS_STUDENT_SETTING_CALENDAR}-${headers._institution_id?.toString()}-*`;
        const keys = await redisClient.Client.keys(pattern);
        for (const key of keys) {
            await redisClient.Client.del(key);
        }
        return { statusCode: 200, message: 'WARNING_CONFIGURATION_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCategoryStatus = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId, settingId, isActive } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        'catagories.$[categoryId].isActive': isActive,
                    },
                },
                {
                    arrayFilters: [
                        {
                            'categoryId._id': convertToMongoObjectId(categoryId),
                        },
                    ],
                },
            )
            .lean();
        if (studentSettingData.classificationType === 'leave' && isActive === false) {
            const isComprehensiveWarning = comprehensiveWarningCheck(studentSettingData);
            const categoryName = studentSettingData.catagories.find(
                (element) => element._id.toString() === categoryId.toString(),
            ).categoryName;
            const denialWarning = (
                isComprehensiveWarning
                    ? studentSettingData.comprehensiveWarningConfig
                    : studentSettingData.warningConfig
            ).sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            if (
                categoryName.toLowerCase() ===
                denialWarning[0].unappliedLeaveConsideredAs.toLowerCase()
            ) {
                const studentSettingData = await lmsStudentSettingSchema.updateOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(settingId),
                    },
                    {
                        $set: isComprehensiveWarning
                            ? {
                                  'comprehensiveWarningConfig.$[warning].unappliedLeaveConsideredAs':
                                      null,
                              }
                            : {
                                  'warningConfig.$[warning].unappliedLeaveConsideredAs': null,
                              },
                        $pull: isComprehensiveWarning
                            ? {
                                  'comprehensiveWarningConfig.$[warning].categoryWisePercentage': {
                                      categoryId: convertToMongoObjectId(categoryId),
                                  },
                              }
                            : {
                                  'warningConfig.$[warning].categoryWisePercentage': {
                                      categoryId: convertToMongoObjectId(categoryId),
                                  },
                              },
                    },
                    {
                        arrayFilters: [
                            {
                                'warning._id': convertToMongoObjectId(denialWarning[0]._id),
                            },
                        ],
                    },
                );
                if (!studentSettingData)
                    return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
                return { statusCode: 200, message: 'DATA_UPDATED' };
            }
            await lmsStudentSettingSchema.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $pull: isComprehensiveWarning
                        ? {
                              'comprehensiveWarningConfig.$[warning].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          }
                        : {
                              'warningConfig.$[warning].categoryWisePercentage': {
                                  categoryId: convertToMongoObjectId(categoryId),
                              },
                          },
                },
                {
                    arrayFilters: [
                        {
                            'warning._id': convertToMongoObjectId(denialWarning[0]._id),
                        },
                    ],
                },
            );
        }
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addLeavePolicy = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            settingId,
            documentTitle,
            description,
            attachmentDocument,
            isDocumentValidity,
            documentValidity,
            noExpiryDate,
            allowStudentVisibilityStatus,
        } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $push: {
                        leavePolicy: {
                            documentTitle,
                            description,
                            attachmentDocument,
                            isDocumentValidity,
                            documentValidity,
                            noExpiryDate,
                            allowStudentVisibilityStatus,
                            updatedAt: new Date(),
                        },
                    },
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_ADD' };
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteProgramLevel = async ({ body = {} }) => {
    try {
        const { settingId, levelApproverId } = body;
        const studentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $pull: { levelApprover: { _id: convertToMongoObjectId(levelApproverId) } },
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DELETE_TYPE' };
        const mainProgram = studentSettingData.levelApprover.find((level) => {
            return level.isExceptionalProgram === false;
        });
        let mainProgramIds = mainProgram.programIds.map((id) => id.toString());
        const removedProgramIds = studentSettingData.levelApprover
            .find(
                (levelApproverElement) =>
                    levelApproverElement._id.toString() === levelApproverId.toString(),
            )
            .programIds.map((i) => i.toString());
        mainProgramIds = [...mainProgramIds, ...removedProgramIds];
        const studentSettingUpdateData = await lmsStudentSettingSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(studentSettingData._id),
            },
            {
                'levelApprover.$[approver].programIds': mainProgramIds,
            },
            {
                arrayFilters: [
                    {
                        'approver._id': convertToMongoObjectId(mainProgram._id),
                    },
                ],
            },
        );
        return { statusCode: 200, message: 'DATA_DELETED', data: studentSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateWarningConfig = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, warningConfigId, isActive } = body;
        const studentSettingData = await lmsStudentSettingSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                'warningConfig.$[warningConfigId].isActive': isActive,
                'comprehensiveWarningConfig.$[warningConfigId].isActive': isActive,
            },
            {
                arrayFilters: [
                    {
                        'warningConfigId._id': convertToMongoObjectId(warningConfigId),
                    },
                ],
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() - 1);
        const institutionCalendarIdData = await institutionCalendarSchema
            .find(
                {
                    $or: [{ isActive: false }, { isActive: true, end_date: { $lt: currentDate } }],
                },
                { _id: 1 },
            )
            .lean();
        if (institutionCalendarIdData.length) {
            const institutionCalendarId = institutionCalendarIdData.map(({ _id }) => _id);
            const lmsSettingCalendar = await lmsStudentSettingCalendarSchema.find(
                {
                    _institution_calendar_id: { $in: institutionCalendarId },
                },
                { _institution_calendar_id: 1 },
            );
            let updateNewCalendarSetting;
            if (lmsSettingCalendar.length) {
                const lmsSettingCalendarId = lmsSettingCalendar.map(
                    ({ _institution_calendar_id }) => _institution_calendar_id.toString(),
                );
                updateNewCalendarSetting = institutionCalendarId
                    .filter((id) => !lmsSettingCalendarId.includes(id.toString()))
                    .map(({ _id }) => ({
                        _institution_calendar_id: convertToMongoObjectId(_id),
                        warningConfig: studentSettingData.warningConfig,
                        comprehensiveWarningConfig: studentSettingData.comprehensiveWarningConfig,
                    }));
            } else {
                updateNewCalendarSetting = institutionCalendarId.map(({ _id }) => ({
                    _institution_calendar_id: convertToMongoObjectId(_id),
                    warningConfig: studentSettingData.warningConfig,
                    comprehensiveWarningConfig: studentSettingData.comprehensiveWarningConfig,
                }));
            }
            const updateCompletedInActiveCalendarSetting =
                await lmsStudentSettingCalendarSchema.insertMany(updateNewCalendarSetting);
        }
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteLeavePolicy = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, leavePolicyId } = body;
        const studentSettingData = await lmsStudentSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            {
                $pull: { leavePolicy: { _id: convertToMongoObjectId(leavePolicyId) } },
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DELETE' };
        return { statusCode: 200, message: 'DATA_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editLeavePolicy = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            settingId,
            leavePolicyId,
            documentTitle,
            description,
            attachmentDocument,
            isDocumentValidity,
            documentValidity,
            noExpiryDate,
            allowStudentVisibilityStatus,
        } = body;
        const studentSettingData = await lmsStudentSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            {
                ...(documentTitle && {
                    'leavePolicy.$[leavePolicyId].documentTitle': documentTitle,
                }),
                ...(typeof description === 'string' && {
                    'leavePolicy.$[leavePolicyId].description': description,
                }),
                ...(attachmentDocument && {
                    'leavePolicy.$[leavePolicyId].attachmentDocument': attachmentDocument,
                }),
                ...(documentValidity && {
                    'leavePolicy.$[leavePolicyId].documentValidity': documentValidity,
                }),
                ...(typeof isDocumentValidity === 'boolean' && {
                    'leavePolicy.$[leavePolicyId].isDocumentValidity': isDocumentValidity,
                }),
                ...(typeof noExpiryDate === 'boolean' && {
                    'leavePolicy.$[leavePolicyId].noExpiryDate': noExpiryDate,
                }),
                ...(typeof allowStudentVisibilityStatus === 'boolean' && {
                    'leavePolicy.$[leavePolicyId].allowStudentVisibilityStatus':
                        allowStudentVisibilityStatus,
                }),
                updatedAt: new Date(),
            },
            {
                arrayFilters: [
                    {
                        'leavePolicyId._id': convertToMongoObjectId(leavePolicyId),
                    },
                ],
            },
        );
        if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listProgram = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const studentSettingData = await programInput
            .find({
                _institution_id: convertToMongoObjectId(_institution_id),
                isActive: true,
                isDeleted: false,
            })
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'NO_DATA' };
        return { statusCode: 200, message: 'LIST_DATA', data: studentSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLeavePoliceForStudent = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType: LEAVE,
                    isActive: true,
                },
                {
                    leavePolicy: 1,
                    updatedAt: 1,
                },
            )
            .lean();
        if (!studentSettingData) return { statusCode: 410, message: 'NO_DATA' };
        const studentVisibilityStatus = studentSettingData.leavePolicy.filter(
            (leavePolicyStatus) => leavePolicyStatus.allowStudentVisibilityStatus,
        );
        for (const student of studentVisibilityStatus) {
            for (const document of student.attachmentDocument) {
                document.signedUrl = await getSignedURL(document.url);
            }
        }
        if (!studentVisibilityStatus.length) return { statusCode: 200, message: 'NO_DATA' };
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: studentVisibilityStatus,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const upload_Document = async ({ body = {} }) => {
    try {
        const { file } = body;
        const name = file.split('/').pop();
        return {
            statusCode: 200,
            data: {
                url: file,
                signedUrl: await getSignedUrl(file),
                size: await getSizeOfUrl(file),
                name,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const generateUrl = async ({ query = {} }) => {
    try {
        const { url } = query;
        return {
            statusCode: 200,
            data: {
                signedUrl: await getSignedUrl(url),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteWarningConfig = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { settingId, warningConfigId } = query;
        const studentSettingData = await lmsStudentSettingSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(settingId),
            },
            { warningConfig: 1, warningMode: 1, comprehensiveWarningConfig: 1 },
        );
        const isComprehensiveWarning = comprehensiveWarningCheck(studentSettingData);
        const filterWarningArray = (
            isComprehensiveWarning
                ? studentSettingData.comprehensiveWarningConfig.filter(
                      (item) => item._id.toString() !== warningConfigId,
                  )
                : studentSettingData.warningConfig.filter(
                      (item) => item._id.toString() !== warningConfigId,
                  )
        ).map((item, index) => {
            if (item.typeName === 1) {
                return item;
            }
            item.typeName = index + 1;
            return item;
        });
        const updateStudentSettingData = await lmsStudentSettingSchema
            .findByIdAndUpdate(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: isComprehensiveWarning
                        ? { comprehensiveWarningConfig: filterWarningArray }
                        : { warningConfig: filterWarningArray },
                },
                { new: true },
            )
            .lean();
        return {
            statusCode: 200,
            data: updateStudentSettingData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserCalendarList = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, userType, isWarning } = params;
        logger.info('LMS -> lmsStudentSettingController - getUserCalendarList -> start ');
        let institutionCalendarIds = [];
        if (userType === DC_STUDENT) {
            const studentGroupData = await studentGroupSchema.distinct('_institution_calendar_id', {
                'groups.courses.setting.session_setting.groups._student_ids':
                    convertToMongoObjectId(userId),
            });
            if (studentGroupData && studentGroupData.length) {
                institutionCalendarIds = studentGroupData;
            } else {
                return { statusCode: 400, message: 'Calendar Not Found' };
            }
        }
        let calendarQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            status: PUBLISHED,
        };
        if (userType === DC_STUDENT) {
            calendarQuery._id = {
                $in: institutionCalendarIds,
            };
        }

        if (isWarning && isWarning !== 'false') {
            const institutionCalendarId = await warningMailSchema.distinct('institutionCalendarId');
            calendarQuery = {
                ...calendarQuery,
                _id: { $in: institutionCalendarId },
            };
        }
        const institutionCalenders = await institutionCalendar
            .find(calendarQuery, {
                calendar_name: 1,
                start_date: 1,
                end_date: 1,
                isActive: 1,
                isDeleted: 1,
            })
            .sort({ _id: -1 })
            .lean();
        if (!institutionCalenders.length) {
            return { statusCode: 400, message: 'Calendar Not Found' };
        }
        return { statusCode: 200, message: 'Calendar List', data: institutionCalenders };
    } catch (error) {
        logger.error(
            { error },
            'LMS ->  lmsStudentSettingController - getUserCalendarList -> error',
        );
        if (error instanceof error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteLmsSettingCalendar = async ({ id }) => {
    await lmsStudentSettingCalendarSchema.deleteMany({
        _institution_calendar_id: convertToMongoObjectId(id),
    });
};

const getWarningSettingBasedCalendar = async ({
    _institution_calendar_id,
    lmsSettingQuery = { warningConfig: 1, comprehensiveWarningConfig: 1 },
}) => {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() - 1);
    const institutionCalendarIdData = await institutionCalendarSchema
        .find(
            {
                $or: [{ isActive: false }, { isActive: true, end_date: { $lt: currentDate } }],
            },
            { _id: 1 },
        )
        .lean();
    if (
        institutionCalendarIdData.length &&
        institutionCalendarIdData
            .map((calendarElement) => calendarElement._id.toString())
            .includes(_institution_calendar_id.toString())
    ) {
        const calendarSetting = await lmsStudentSettingCalendarSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
                lmsSettingQuery,
            )
            .lean();
        if (
            calendarSetting &&
            calendarSetting.warningConfig &&
            calendarSetting.warningConfig.length
        ) {
            return calendarSetting.warningConfig;
        }
        if (
            calendarSetting &&
            calendarSetting.comprehensiveWarningConfig &&
            calendarSetting.comprehensiveWarningConfig.length
        ) {
            return calendarSetting.comprehensiveWarningConfig;
        }
    }
    return [];
};

const updateCourseOrComprehensive = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { warningModeValue, settingId } = body;
        const workingSystemUpdate = await lmsStudentSettingSchema.updateOne(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    warningMode: warningModeValue,
                },
            },
        );
        const globalSessionUpdate = await globleSessionSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: {
                    warningMode: warningModeValue,
                },
            },
        );
        if (!workingSystemUpdate.modifiedCount && !globalSessionUpdate.modifiedCount)
            return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//currently not in use in future anything need we can reuse
const deleteLmsSettingData = async ({ headers = {}, query = {} }) => {
    try {
        const { classificationType } = query;
        const { _institution_id } = headers;

        const deleteLmsSetting = await lmsStudentSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType,
                isDeleted: false,
            },
            {
                $set: { isDeleted: true },
            },
        );
        if (!deleteLmsSetting.modifiedCount) {
            return { statusCode: 200, message: 'Not Updated' };
        }
        return { statusCode: 200, message: 'Deleted Successfully' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getStudentLMSSetting,
    updateGeneraConfig,
    createCatagories,
    deleteCatagories,
    createCatagoriesTypes,
    deleteCatagoriesTypes,
    updateCategoryTypes,
    updateTermsAndCondition,
    addLeaveApprovalLevel,
    deleteLevel,
    editLevels,
    addWarningDenialConfiguration,
    updateCategoryStatus,
    listProgram,
    addProgramLevel,
    editProgramLevel,
    deleteProgramLevel,
    divideGenderSegregation,
    updateWarningConfig,
    addLeavePolicy,
    deleteLeavePolicy,
    editLeavePolicy,
    editCategoryName,
    getLeavePoliceForStudent,
    deleteWarningConfig,
    upload_Document,
    generateUrl,
    getUserCalendarList,
    deleteLmsSettingCalendar,
    getWarningSettingBasedCalendar,
    updateCourseOrComprehensive,
    deleteLmsSettingData,
};
