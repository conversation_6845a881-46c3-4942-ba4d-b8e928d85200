const constant = require('../utility/constants');
var infra_delivery_medium = require('mongoose').model(constant.INFRASTRUCTURE_DELIVERY_MEDIUM);
var session_type = require('mongoose').model(constant.SESSION_TYPE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');


exports.create = async(req,res) => {
    let objs = {
        name: req.body.name,
        remote: req.body.remote,
        onsite: req.body.onsite
    }
    let doc = await base_control.insert(infra_delivery_medium, objs);
    if(doc.status) {
        common_files.com_response(res, 200, true, "selected remote or online for delivery type" ,doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in selecting medium for delivery type", "Error in selecting medium for delivery type")
    }
}

exports.get = async(req,res) => {
    let doc_data = [];
    let doc = await base_control.get_list(infra_delivery_medium, {'isDeleted': false})
    if(doc.status) {
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,name:element.name,remote:element.remote,onsite:element.onsite})
        });
        common_files.com_response(res, 200, true, "Got all seleted delivery medium", doc_data)
    } else {
        common_files.com_response(res, 404, false, "Error in getting selected delivery medium ", "Error in getting selected delivery medium")
    }
}

exports.get_id = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_delivery_medium, {_id:{$in: req.params.id} ,'isDeleted': false});
    if(checks.status) {
    let doc_data = [];
    let doc = await base_control.get_list(infra_delivery_medium, {_id: req.params.id})
    if(doc.status) {
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,name:element.name,remote:element.remote,onsite:element.onsite})
        });
        common_files.com_response(res, 200, true, "Got seleted delivery medium by id", doc_data)
    } else {
        common_files.com_response(res, 404, false, "Error in getting selected delivery medium by id" ,"Error in getting selected delivery medium by id")
    }    
} else {
  common_files.com_response(res, 404, false, "Error id not match", "Check parsind reference ID")
    }
}

exports.update = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_delivery_medium,{_id:{$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
        let objs = {
            name: req.body.name,
            remote: req.body.remote,
            onsite: req.body.onsite
        }
    let doc = await base_control.update(infra_delivery_medium,{_id: req.params.id},objs)    
    if(doc.status) {
        common_files.com_response(res, 200, true, "updated selected delivery medium", doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in updating delivery medium", "Error in updating delivery medium")
    }    
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.update_remote_onsite = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_delivery_medium,{_id:{$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
        let objs = {};
        if(req.body.remote != undefined && req.body.onsite != undefined){
            Object.assign(objs,{'remote' :req.body.remote},{'onsite' :req.body.onsite})
        } else {
            if(req.body.remote != undefined && req.body.onsite == undefined){
                Object.assign(objs,{'remote' :req.body.remote})
            }
            if(req.body.remote == undefined && req.body.onsite != undefined){
                Object.assign(objs,{'onsite' :req.body.onsite})
            }
        }
    let doc = await base_control.update(infra_delivery_medium,{_id: req.params.id},objs)    
    if(doc.status) {
        common_files.com_response(res, 200, true, "updated selected delivery medium", doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in updating delivery medium", "Error in updating delivery medium")
    }    
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.delete = async(req,res) => {
    let checks = { status: true};
    checks = await base_control.check_id(infra_delivery_medium, {_id:{$in: req.params.id}, 'isDeleted':false});
    if(checks.status) {
        let doc = await base_control.delete(infra_delivery_medium,{_id: req.params.id})
        if(doc.status) {
            common_files.com_response(res, 200, true, "Deleted delivery medium", doc.data)
        } else {
            common_files.com_response(res, 404, false, "Error in deleting delivery medium", "Error in deleting delivery medium")
        }
    } else {
        common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
    }
}

exports.get_symbols = async(req,res) => {
    let aggre = [
        { $match  :{'isDeleted': false}},
        { $group :{ _id:'$delivery_symbol'}},
        { $project :{_id:0,delivery_symbol :'$_id'}}
    ];
    let doc = await base_control.get_aggregate(session_type, aggre)
    if(doc.status) {
        common_files.com_response(res, 200, true, "Got distinct delivery symbols", doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in getting distinct delivery symbols", "Error in getting distinct delivery symbols" )
    }
}