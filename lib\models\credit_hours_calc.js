let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let credit_hours_calc = new Schema({
    session_type: {
        type: String,
        required: true
    },
    credit_hours: {
        type: Number,
        required: true
    },
    contact_hours: {
        type: Number,
        required: true
    },
    per_session: {
        type: Number,
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.CREDIT_HOURS_CALC, credit_hours_calc);