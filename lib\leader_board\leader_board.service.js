const leaderBoardSchema = require('./leader_board.model');
const { convertToMongoObjectId } = require('../utility/common');
const {
    STAFF_ATTENDANCE,
    ENGAGER,
    COMPLETED,
    SCHEDULE_TYPES: { REGULAR, SURPRISE_QUIZ, SUPPORT_SESSION },
    LEADER_BOARD,
} = require('../utility/constants');
const { QUIZ } = require('../utility/enums');
const scheduledAttendanceSchema = require('../models/schedule_attendance');
const activitySchema = require('../models/activities');
const userSchema = require('../models/user');
const { redisClient } = require('../../config/redis-connection');
const programSchema = require('../models/digi_programs');

const topPerformanceFilter = async ({ leaderData }) => {
    try {
        //user details
        const userData = await userSchema
            .find(
                {
                    _id: {
                        $in: leaderData.performanceData.map((leaderElement) =>
                            convertToMongoObjectId(leaderElement._staff_id),
                        ),
                    },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                    'employment.user_employment_type': 1,
                    gender: 1,
                    user_id: 1,
                },
            )
            .lean();
        //get programData
        const programData = await programSchema
            .find(
                {
                    _id: {
                        $in: leaderData.performanceData.map((leaderElement) =>
                            convertToMongoObjectId(leaderElement._program_id),
                        ),
                    },
                },
                {
                    name: 1,
                },
            )
            .lean();
        let gender = new Set();
        let employeeType = new Set();
        let rankData = new Set();
        for (const userElement of userData) {
            if (!employeeType.has(userElement.employment.user_employment_type))
                employeeType.add(userElement.employment.user_employment_type);
            if (!gender.has(userElement.gender)) gender.add(userElement.gender);
        }
        //rank filter
        for (const performanceElement of leaderData.performanceData) {
            if (!rankData.has(performanceElement.rank)) {
                rankData.add(performanceElement.rank);
            }
        }
        rankData = Array.from(rankData);
        gender = Array.from(gender);
        employeeType = Array.from(employeeType);
        return { userData, programData, gender, employeeType, rankData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const sortedPerformance = ({ performanceData, topFilter = false }) => {
    try {
        performanceData.sort((a, b) => b.weight - a.weight);
        //given the rank
        let rank = 0;
        let prevOverall = -1;
        for (const performanceElement of performanceData) {
            if (performanceElement.weight !== prevOverall) {
                rank++;
                prevOverall = performanceElement.weight;
            }
            performanceElement.rank = rank;
        }
        return { performanceData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const allCriteriaScore = ({ performanceData }) => {
    try {
        const criteriaScoreData = new Map();
        for (const performanceElement of performanceData) {
            for (const sessionElement of performanceElement.sessions) {
                const criteriaKey = `${sessionElement.criteriaName}`;
                if (!criteriaScoreData.has(criteriaKey)) {
                    criteriaScoreData.set(criteriaKey, {
                        criteriaName: sessionElement.criteriaName,
                        staffScore: 0,
                        sumOfCriteria: 0,
                    });
                }
                const existingData = criteriaScoreData.get(criteriaKey);
                existingData.staffScore += sessionElement.staffScore;
                existingData.sumOfCriteria += 1;
                criteriaScoreData.set(criteriaKey, existingData);
            }
        }
        let criteriaAverageScore = Array.from(criteriaScoreData.values());
        criteriaAverageScore = criteriaAverageScore.map((criteriaElement) => ({
            criteriaName: criteriaElement.criteriaName,
            criteriaScore: (criteriaElement.staffScore / criteriaElement.sumOfCriteria).toFixed(2),
        }));
        criteriaAverageScore = criteriaAverageScore.sort(
            (a, b) => a.criteriaScore - b.criteriaScore,
        );
        return { criteriaAverageScore };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const allProgramAverageScore = ({ performanceData, totalWeight }) => {
    try {
        const allProgramAverage = new Map();
        for (const performanceElement of performanceData) {
            for (const sessionElement of performanceElement.sessions) {
                const parameterKey = `${sessionElement.parameterName}`;
                if (!allProgramAverage.has(parameterKey)) {
                    allProgramAverage.set(parameterKey, {
                        parameterName: sessionElement.parameterName,
                        parameterWeight: 0,
                        parameterCount: 0,
                    });
                }
                const existingParameter = allProgramAverage.get(parameterKey);
                existingParameter.parameterCount++;
                existingParameter.parameterWeight += sessionElement.sessionWeight;
            }
        }
        let programAverageData = Array.from(allProgramAverage.values());
        programAverageData = programAverageData.map((parameterElement) => ({
            parameterName: parameterElement.parameterName,
            parameterWeight: Math.round(
                parameterElement.parameterWeight / parameterElement.parameterCount,
            ),
        }));
        const overAllProgramScore =
            (programAverageData.reduce(
                (sumOfScore, performanceElement) => sumOfScore + performanceElement.parameterWeight,
                0,
            ) /
                totalWeight) *
            100;
        return { programAverageData, overAllProgramScore };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const programBasedFilter = ({ performanceData, programIds, topFilter = false }) => {
    try {
        const filterProgram =
            performanceData.length &&
            performanceData.filter((performanceElement) =>
                programIds.toString().includes(performanceElement._program_id.toString()),
            );
        const sortedData = sortedPerformance({
            performanceData: filterProgram,
            topFilter,
        });
        return { performanceData: sortedData.performanceData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getCurrentUserDetails = ({ userId, leaderData }) => {
    try {
        leaderData.performanceData = leaderData.performanceData.filter(
            (performanceElement) => performanceElement._staff_id.toString() === userId.toString(),
        );
        const sortData = sortedPerformance({ performanceData: leaderData.performanceData });
        const criteriaScoreData = allCriteriaScore({ performanceData: sortData.performanceData });
        const programAverageScore = allProgramAverageScore({
            performanceData: leaderData.performanceData,
            totalWeight: leaderData.totalWeight,
        });
        leaderData = {
            performanceData: sortData.performanceData,
            criteriaAverageScore: criteriaScoreData.criteriaAverageScore,
            programAverageData: programAverageScore.programAverageData,
            overAllProgramScore: programAverageScore.overAllProgramScore,
            badges: leaderData.badges,
            badgeStyle: leaderData.badgeStyle,
            weightTooltip: leaderData.weightTooltip,
            totalScore: leaderData.totalScore,
            totalWeight: leaderData.totalWeight,
            lastUpdated: leaderData.lastUpdated,
        };
        return { leaderData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const leaderBoardCalculation = ({ sessionData, totalWeight }) => {
    try {
        const sessionDataMap = new Map();
        for (const sessionElement of sessionData) {
            //check the criteria allowance
            if (sessionElement.isActiveAllowance) {
                const allowance =
                    (sessionElement.totalCompletedSession / 100) * sessionElement.allowance;
                sessionElement.totalCompletedSession -= allowance;
                sessionElement.totalCompletedSession = Math.round(
                    sessionElement.totalCompletedSession,
                );
            }
            const selfAttendance =
                (sessionElement.sessionOnTime / sessionElement.totalCompletedSession) * 100;
            sessionElement.selfAttendance = selfAttendance >= 100 ? 100 : selfAttendance;
            // check the criteria isConsideration
            if (
                (!sessionElement.isActiveConsideration && sessionElement.isActiveScore) ||
                (sessionElement.isActiveScore &&
                    sessionElement.isActiveConsideration &&
                    sessionElement.consideration >= selfAttendance)
            ) {
                sessionElement.staffScore =
                    (sessionElement.selfAttendance * sessionElement.criteriaScore) / 100;
                sessionElement.staffScore = parseFloat(sessionElement.staffScore.toFixed(2));
            } else {
                sessionElement.staffScore = sessionElement.criteriaScore
                    ? sessionElement.criteriaScore
                    : 0;
            }
            if (
                !sessionElement.isActiveAllowance &&
                !sessionElement.isActiveConsideration &&
                !sessionElement.isActiveScore
            ) {
                sessionElement.staffScore = selfAttendance;
            }
            sessionElement.sessionWeight =
                (sessionElement.staffScore * sessionElement.criteriaWeight) / 100;
            if (sessionElement.isActiveScore) {
                sessionElement.sessionWeight =
                    (sessionElement.staffScore / sessionElement.criteriaScore) *
                    sessionElement.criteriaWeight;
            }
            sessionElement.sessionWeight = Math.round(sessionElement.sessionWeight);
            sessionElement.staffScore = Math.round(sessionElement.staffScore);
            //over all score--> staffId and programId based to add all criteria score
            const staff_program_key = `${sessionElement._staff_id}+${sessionElement._program_id}`;
            if (!sessionDataMap.has(staff_program_key)) {
                sessionDataMap.set(staff_program_key, {
                    _staff_id: sessionElement._staff_id,
                    _program_id: sessionElement._program_id,
                    staffScore: 0,
                    weight: 0,
                    sessions: [],
                });
            }
            // overAllWeight
            const existingData = sessionDataMap.get(staff_program_key);
            existingData.staffScore += sessionElement.staffScore;
            existingData.weight += sessionElement.sessionWeight;
            existingData.sessions.push(sessionElement);
            sessionDataMap.set(staff_program_key, existingData);
        }
        const performanceData = Array.from(sessionDataMap.values());
        const programAverageScore = allProgramAverageScore({ performanceData, totalWeight });
        const criteriaAverageData = allCriteriaScore({ performanceData });
        //sorted data
        const sortedData = sortedPerformance({ performanceData });
        return {
            performanceData: sortedData.performanceData,
            criteriaAverageScore: criteriaAverageData.criteriaAverageScore,
            overAllProgramScore: programAverageScore.overAllProgramScore,
            programAverageData: programAverageScore.programAverageData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const addMinutes = ({ startDateTime, minutes }) => {
    try {
        let startDate = new Date(startDateTime);
        startDate.setMinutes(startDate.getMinutes() + minutes);
        startDate = startDate.toISOString().slice(0, 16);
        return startDate;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getLeaderBoard = async ({
    scheduledData,
    boardType,
    _institution_id,
    _institution_calendar_id,
    from,
    to,
    currentUserId,
    filterType = false,
    userId,
}) => {
    try {
        let leaderBoardData = await leaderBoardSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    leaderBoard: 1,
                    anomalyBoard: 1,
                },
            )
            .lean();
        leaderBoardData = leaderBoardData && leaderBoardData[boardType + 'Board'];
        const badges = leaderBoardData.badgeName;
        const badgeStyle = leaderBoardData.badgeStyle;
        const weightTooltip = leaderBoardData.parameters.map((parameterElement) => ({
            parameterName: parameterElement.name,
            parameterWeight: parameterElement.weight,
        }));
        const scheduledDetails = [];
        const staffDetails = [];
        //staff wise split the scheduled
        for (const scheduledElement of scheduledData) {
            if (userId) {
                scheduledElement.staffs = scheduledElement.staffs.filter(
                    (staffElement) => staffElement._staff_id.toString() === userId.toString(),
                );
            }
            for (const staffElement of scheduledElement.staffs) {
                const sessionOnTime =
                    scheduledElement.sessionDetail &&
                    scheduledElement.sessionDetail.startBy &&
                    scheduledElement.sessionDetail.startBy.toString() ===
                        staffElement._staff_id.toString()
                        ? scheduledElement.sessionDetail.start_time
                        : staffElement.time
                        ? staffElement.time
                        : 0;
                const existingStaffData = staffDetails.find(
                    (staffIdElement) =>
                        staffIdElement._staff_id.toString() === staffElement._staff_id.toString() &&
                        staffIdElement._program_id.toString() ===
                            scheduledElement._program_id.toString() &&
                        scheduledElement.term &&
                        staffIdElement.term.toLowerCase() === scheduledElement.term.toLowerCase() &&
                        scheduledElement.year_no &&
                        staffIdElement.year_no.toLowerCase() ===
                            scheduledElement.year_no.toLowerCase() &&
                        scheduledElement.level_no &&
                        staffIdElement.level_no.toLowerCase() ===
                            scheduledElement.level_no.toLowerCase() &&
                        scheduledElement.rotation &&
                        staffIdElement.rotation === scheduledElement.rotation &&
                        scheduledElement.rotation_count &&
                        staffIdElement.rotation_count === scheduledElement.rotation_count,
                );
                const existingScheduleData =
                    existingStaffData &&
                    existingStaffData.scheduledDetails.some(
                        (existingScheduleElement) =>
                            existingScheduleElement._id.toString() ===
                            scheduledElement._id.toString(),
                    );
                if (!existingStaffData) {
                    staffDetails.push({
                        _staff_id: staffElement._staff_id,
                        _program_id: scheduledElement._program_id,
                        _course_id: scheduledElement._course_id,
                        term: scheduledElement.term,
                        year_no: scheduledElement.year_no,
                        level_no: scheduledElement.level_no,
                        rotation: scheduledElement.rotation,
                        rotation_count: scheduledElement.rotation_count
                            ? scheduledElement.rotation_count
                            : 0,
                        scheduledDetails: [
                            {
                                scheduleStartDateAndTime: scheduledElement.scheduleStartDateAndTime,
                                _id: scheduledElement._id,
                                type: scheduledElement.type,
                                sessionDetail: scheduledElement.sessionDetail,
                                sessionOnTime,
                                students: scheduledElement.students,
                            },
                        ],
                    });
                } else if (!existingScheduleData) {
                    existingStaffData.scheduledDetails.push({
                        scheduleStartDateAndTime: scheduledElement.scheduleStartDateAndTime,
                        _id: scheduledElement._id,
                        type: scheduledElement.type,
                        sessionDetail: scheduledElement.sessionDetail,
                        sessionOnTime,
                        students: scheduledElement.students,
                    });
                }
            }
        }
        //getting engager data;
        const scheduledAttendanceData = await scheduledAttendanceSchema
            .find(
                {
                    scheduleId: {
                        $in: scheduledData.map((scheduledElement) =>
                            convertToMongoObjectId(scheduledElement._id),
                        ),
                    },
                    status: COMPLETED,
                    mode: SURPRISE_QUIZ,
                },
                { 'students._id': 1, 'scheduleId.$': 1 },
            )
            .lean();
        // get all activity data
        const activityData = await activitySchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    courseId: {
                        $in: scheduledData.map((scheduledElement) =>
                            convertToMongoObjectId(scheduledElement._course_id),
                        ),
                    },
                    status: COMPLETED,
                    quizType: QUIZ,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    courseId: 1,
                    createdBy: 1,
                },
            )
            .lean();
        const student = [];
        const sessionData = [];
        let scheduleStartDate;
        let sessionStartDate;
        let criteriaWeight;
        let totalScore = 0;
        let totalWeight = 0;
        const activeParameterWeight = new Set();
        if (boardType === 'leader') {
            for (const parameterElement of leaderBoardData.parameters) {
                criteriaWeight =
                    parameterElement.weight /
                    parameterElement.criteria.filter(
                        (parameter_criteriaElement) => parameter_criteriaElement.isActive === true,
                    ).length;
                const parameterId = `${parameterElement._id}`;
                for (const criteriaElement of parameterElement.criteria) {
                    if (criteriaElement.isActive) {
                        totalScore =
                            totalScore <= criteriaElement.score.noOfScore
                                ? criteriaElement.score.noOfScore
                                : totalScore;
                        if (!activeParameterWeight.has(parameterId)) {
                            totalWeight += parameterElement.weight;
                            activeParameterWeight.add(parameterId);
                        }
                        for (const staffElement of staffDetails) {
                            let completedSession = 0;
                            let sessionOnTime = 0;
                            for (const scheduledElement of staffElement.scheduledDetails) {
                                if (scheduledElement.type === REGULAR) {
                                    if (criteriaElement.criteriaName === STAFF_ATTENDANCE) {
                                        scheduleStartDate = new Date(
                                            scheduledElement.scheduleStartDateAndTime,
                                        )
                                            .toISOString()
                                            .slice(0, 16);
                                        sessionStartDate = scheduledElement.sessionOnTime
                                            ? new Date(scheduledElement.sessionOnTime)
                                                  .toISOString()
                                                  .slice(0, 16)
                                            : 0;
                                        if (criteriaElement.noOfSession) {
                                            completedSession++;
                                            //check the criterial bufferTime
                                            if (criteriaElement.bufferTime.isActive) {
                                                scheduleStartDate = addMinutes({
                                                    startDateTime:
                                                        scheduledElement.scheduleStartDateAndTime,
                                                    minutes: criteriaElement.bufferTime.noOfBuffer,
                                                });
                                                if (
                                                    scheduleStartDate.toString() >=
                                                    sessionStartDate.toString()
                                                ) {
                                                    sessionOnTime++;
                                                }
                                            } else if (
                                                scheduleStartDate.toString() ===
                                                sessionStartDate.toString()
                                            ) {
                                                sessionOnTime++;
                                            }
                                        } else {
                                            completedSession++;
                                            let bufferTime = 0;
                                            //buffer isActive add the minsOfStart + noOfBuffer
                                            if (criteriaElement.bufferTime.isActive) {
                                                bufferTime =
                                                    criteriaElement.sessionStarted.minsOfStart +
                                                    criteriaElement.bufferTime.noOfBuffer;
                                            }
                                            scheduleStartDate = addMinutes({
                                                startDateTime:
                                                    scheduledElement.scheduleStartDateAndTime,
                                                minutes:
                                                    criteriaElement.sessionStarted.minsOfStart +
                                                    bufferTime,
                                            });
                                            let studentOnTime = 1;
                                            for (const studentElement of scheduledElement.students) {
                                                const studentPrimaryTime =
                                                    studentElement.primaryTime
                                                        ? new Date(studentElement.primaryTime)
                                                              .toISOString()
                                                              .slice(0, 16)
                                                        : 0;
                                                if (
                                                    studentPrimaryTime &&
                                                    scheduleStartDate.toString() >=
                                                        studentPrimaryTime.toString() &&
                                                    criteriaElement.sessionStarted.noOfStudent >=
                                                        studentOnTime
                                                ) {
                                                    sessionOnTime++;
                                                    studentOnTime++;
                                                }
                                            }
                                        }
                                    } else if (criteriaElement.criteriaName === ENGAGER) {
                                        const scheduledEngagerData =
                                            scheduledAttendanceData &&
                                            scheduledAttendanceData.length &&
                                            scheduledAttendanceData.find(
                                                (attendanceElement) =>
                                                    scheduledElement._id.toString() ===
                                                    attendanceElement.scheduleId[0].toString(),
                                            );
                                        completedSession++;
                                        if (criteriaElement.noOfEngager && scheduledEngagerData) {
                                            sessionOnTime++;
                                        } else if (
                                            !criteriaElement.noOfEngager &&
                                            scheduledEngagerData &&
                                            scheduledEngagerData.students.length
                                        ) {
                                            sessionOnTime++;
                                        }
                                    }
                                }
                                if (
                                    (scheduledElement.type === REGULAR ||
                                        scheduledElement.type === SUPPORT_SESSION) &&
                                    criteriaElement.criteriaName === 'quiz'
                                ) {
                                    completedSession++;
                                    sessionOnTime = activityData.filter(
                                        (activityElement) =>
                                            activityElement.courseId.toString() ===
                                                staffElement._course_id.toString() &&
                                            activityElement.createdBy.toString() ===
                                                staffElement._staff_id._id.toString(),
                                    ).length;
                                }
                            }
                            if (
                                !sessionData.some(
                                    (sessionElement) =>
                                        sessionElement._staff_id.toString() ===
                                            staffElement._staff_id._id.toString() &&
                                        sessionElement.criteriaId.toString() ===
                                            criteriaElement._id.toString() &&
                                        sessionElement._program_id.toString() ===
                                            staffElement._program_id.toString() &&
                                        sessionElement.year_no.toLowerCase() ===
                                            staffElement.year_no.toLowerCase() &&
                                        sessionElement.term.toLowerCase() ===
                                            staffElement.term.toLowerCase() &&
                                        sessionElement.level_no.toLowerCase() ===
                                            staffElement.level_no.toLowerCase() &&
                                        sessionElement.rotation == staffElement.rotation &&
                                        sessionElement.rotation_count ===
                                            staffElement.rotation_count,
                                )
                            ) {
                                sessionData.push({
                                    totalCompletedSession: completedSession,
                                    totalScheduled: completedSession,
                                    sessionOnTime,
                                    _staff_id: staffElement._staff_id._id,
                                    _program_id: staffElement._program_id,
                                    _course_id: staffElement._course_id,
                                    year_no: staffElement.year_no,
                                    term: staffElement.term,
                                    level_no: staffElement.level_no,
                                    rotation_count: staffElement.rotation_count,
                                    rotation: staffElement.rotation,
                                    parameterId: parameterElement._id,
                                    parameterName: parameterElement.name,
                                    parameterWeight: parameterElement.weight,
                                    criteriaId: criteriaElement._id,
                                    criteriaName: criteriaElement.criteriaName,
                                    criteriaWeight,
                                    isActive: criteriaElement.isActive,
                                    isActiveScore: criteriaElement.score.isActive,
                                    criteriaScore: criteriaElement.score.noOfScore,
                                    isActiveConsideration: criteriaElement.consideration.isActive,
                                    consideration: criteriaElement.consideration.noOfConsideration,
                                    isActiveAllowance: criteriaElement.allowance.isActive,
                                    allowance: criteriaElement.allowance.noOfAllowance,
                                });
                            }
                        }
                    }
                }
            }
        }
        let leaderData = leaderBoardCalculation({ sessionData, totalWeight });
        const filterData = await topPerformanceFilter({ leaderData });
        leaderData.badges = badges;
        leaderData.badgeStyle = badgeStyle;
        leaderData.weightTooltip = weightTooltip;
        leaderData.totalScore = totalScore;
        leaderData.totalWeight = totalWeight;
        if (filterType) {
            const filterKey = `${LEADER_BOARD}-filter:${_institution_calendar_id}-${from}-${to}`;
            await redisClient.Client.set(filterKey, JSON.stringify(filterData));
            if (!currentUserId) return filterData;
        }
        if (leaderData) {
            const lastUpdated = new Date();
            leaderData.lastUpdated = lastUpdated;
            const leaderBoardRedisKey = `${LEADER_BOARD}:${_institution_calendar_id}-${from}-${to}`;
            await redisClient.Client.set(leaderBoardRedisKey, JSON.stringify(leaderData));
        }
        if (currentUserId) {
            const currentData = getCurrentUserDetails({ userId: currentUserId, leaderData });
            leaderData = currentData.leaderData;
            if (filterType) {
                const filterData = await topPerformanceFilter({ leaderData });
                return filterData;
            }
        }
        return { leaderData, filterData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const leaderBoardCatchData = async ({
    scheduledData,
    boardType,
    _institution_id,
    _institution_calendar_id,
    from,
    to,
    currentUserId,
    programIds,
}) => {
    try {
        const leaderBoardRedisKey = `${LEADER_BOARD}:${_institution_calendar_id}-${from}-${to}`;
        const leaderCatchData = await redisClient.Client.get(leaderBoardRedisKey);
        let leaderData;
        if (leaderCatchData) {
            leaderData = JSON.parse(leaderCatchData);
            if (!programIds.length && !currentUserId) return { leaderData };
        }
        if (!leaderCatchData || !leaderBoardRedisKey) {
            const catchLeaderBoardData = await getLeaderBoard({
                scheduledData,
                boardType,
                _institution_id,
                _institution_calendar_id,
                from,
                to,
            });
            leaderData = catchLeaderBoardData.leaderData;
            if (!programIds.length && !currentUserId) return { leaderData };
        }
        if (currentUserId) {
            const currentData = getCurrentUserDetails({ userId: currentUserId, leaderData });
            leaderData = currentData.leaderData;
            if (!programIds.length) return { leaderData };
        }
        if (programIds.length) {
            const programFilter = programBasedFilter({
                performanceData: leaderData.performanceData,
                programIds,
            });
            const programAverageScore = allProgramAverageScore({
                performanceData: programFilter.performanceData,
                totalWeight: leaderData.totalWeight,
            });
            const criteriaAverageData = allCriteriaScore({
                performanceData: programFilter.performanceData,
            });
            leaderData = {
                performanceData: programFilter.performanceData,
                criteriaAverageScore: criteriaAverageData.criteriaAverageScore,
                programAverageData: programAverageScore.programAverageData,
                overAllProgramScore: programAverageScore.overAllProgramScore,
                badges: leaderData.badges,
                badgeStyle: leaderData.badgeStyle,
                weightTooltip: leaderData.weightTooltip,
                totalScore: leaderData.totalScore,
                totalWeight: leaderData.totalWeight,
                lastUpdated: leaderData.lastUpdated,
            };
            return { leaderData };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const leaderBoardCatchFilter = async ({
    scheduledData,
    boardType,
    _institution_id,
    _institution_calendar_id,
    state,
    from,
    to,
    programIds,
    currentUserId,
}) => {
    try {
        const filterKey = `${LEADER_BOARD}-filter:${_institution_calendar_id}-${from}-${to}`;
        const filterCatchData = await redisClient.Client.get(filterKey);
        if (state === 'filter' && filterCatchData) {
            return JSON.parse(filterCatchData);
        }
        if (filterCatchData && state === 'regular' && !programIds.length && !currentUserId) {
            return JSON.parse(filterCatchData);
        }
        if (!filterCatchData || state === 'fresh') {
            const filterData = await getLeaderBoard({
                scheduledData,
                boardType,
                _institution_id,
                _institution_calendar_id,
                from,
                to,
                currentUserId,
                filterType: true,
            });
            if (!programIds.length) return filterData;
        }
        //current user filter
        const leaderBoardRedisKey = `${LEADER_BOARD}:${_institution_calendar_id}-${from}-${to}`;
        const leaderCatchData = await redisClient.Client.get(leaderBoardRedisKey);
        const leaderBoardData = JSON.parse(leaderCatchData);
        if (currentUserId && state === 'regular' && leaderBoardData) {
            const currentData = getCurrentUserDetails({
                userId: currentUserId,
                leaderData: leaderBoardData,
            });
            leaderData = currentData.leaderData;
            const filterData = await topPerformanceFilter({ leaderData });
            if (!programIds.length) return filterData;
            leaderBoardData.performanceData = leaderData.performanceData;
        }
        if (programIds.length) {
            // const leaderBoardRedisKey = `${LEADER_BOARD}:${_institution_calendar_id}-${from}-${to}`;
            // const leaderCatchData = await redisClient.Client.get(leaderBoardRedisKey);
            // const leaderBoardData = JSON.parse(leaderCatchData);
            const programFilter = programBasedFilter({
                performanceData: leaderBoardData.performanceData,
                programIds,
            });
            const leaderData = {
                performanceData: programFilter.performanceData,
            };
            const filterData = await topPerformanceFilter({ leaderData });
            return filterData;
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    getLeaderBoard,
    leaderBoardCatchData,
    leaderBoardCatchFilter,
};
