const lmsLateConfigSettingSchema = require('./lmsAttendanceConfig.model');
const lateConfigManagementSchema = require('./lateConfigManagement.model');
const institutionSchema = require('../models/institution');
const courseScheduleSchema = require('../models/course_schedule');
const { convertToMongoObjectId, sendResponse } = require('../utility/common');
const {
    ATTENDANCE_CONFIG,
    LATE_CONFIG,
    COURSE_WISE,
    STUDENT_WISE,
} = require('../utility/constants');
exports.attendanceAddRow = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { lateConfig, attendanceMarkConfig } = body;
        const deleteQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            $or: [{ 'lateConfig.lateType': lateConfig.lateType }, { mode: ATTENDANCE_CONFIG }],
        };
        if (lateConfig?.range?.length) {
            const lateConfigIds = lateConfig.range
                .filter((item) => item._id)
                .map((item) => convertToMongoObjectId(item._id));
            if (lateConfigIds.length) {
                deleteQuery.$and = [{ _id: { $nin: lateConfigIds } }];
            }
        }
        const deleteConfigurations = await lmsLateConfigSettingSchema.deleteMany(deleteQuery);
        const bulkUpdateOperations = [];
        for (const lateConfigItem of lateConfig.range) {
            const rangeDocument = {
                _institution_id: convertToMongoObjectId(_institution_id),
                lateConfig: {
                    lateType: lateConfig.lateType,
                    range: lateConfigItem,
                },
                mode: LATE_CONFIG,
            };
            if (lateConfigItem._id) {
                const updateOperation = {
                    updateOne: {
                        filter: { _id: convertToMongoObjectId(lateConfigItem._id) },
                        update: rangeDocument,
                    },
                };
                bulkUpdateOperations.push(updateOperation);
            } else {
                const insertOperation = {
                    insertOne: {
                        document: rangeDocument,
                    },
                };
                bulkUpdateOperations.push(insertOperation);
            }
        }
        if (attendanceMarkConfig.hasOwnProperty('range')) {
            for (const attendanceMarkConfigItem of attendanceMarkConfig.range) {
                const rangeDocument = {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    attendanceMarkConfig: {
                        basedGrandMarks: attendanceMarkConfig.basedGrandMarks,
                        range: attendanceMarkConfigItem,
                        equalGrandMarks: attendanceMarkConfig.equalGrandMarks,
                    },
                    mode: ATTENDANCE_CONFIG,
                };
                if (attendanceMarkConfigItem._id) {
                    rangeDocument._id = attendanceMarkConfigItem._id;
                }
                const insertOperation = {
                    insertOne: {
                        document: rangeDocument,
                    },
                };
                bulkUpdateOperations.push(insertOperation);
            }
        }
        const result = await lmsLateConfigSettingSchema.bulkWrite(bulkUpdateOperations);
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getAttendanceConfig = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        let globalLateConfig;
        let getAttendanceData = await lmsLateConfigSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    mode: 1,
                    lateConfig: 1,
                    attendanceMarkConfig: 1,
                },
            )
            .sort({ createdAt: -1 })
            .lean();
        if (getAttendanceData) {
            getAttendanceData = getAttendanceData.reduce(
                ({ getLateResult, getAttendanceResult }, getAttendanceElement) => {
                    if (getAttendanceElement.mode === LATE_CONFIG) {
                        const key = getAttendanceElement.lateConfig.lateType;
                        if (!getLateResult[key]) {
                            getLateResult[key] = {
                                lateType: getAttendanceElement.lateConfig.lateType,
                                range: [
                                    {
                                        _id: getAttendanceElement._id,
                                        ...getAttendanceElement.lateConfig.range,
                                    },
                                ],
                            };
                        } else {
                            getLateResult[key].range.unshift({
                                _id: getAttendanceElement._id,
                                ...getAttendanceElement.lateConfig.range,
                            });
                        }
                    }
                    if (getAttendanceElement.mode === ATTENDANCE_CONFIG) {
                        getAttendanceResult.equalGrandMarks =
                            getAttendanceElement.attendanceMarkConfig.equalGrandMarks;
                        getAttendanceResult.basedGrandMarks =
                            getAttendanceElement.attendanceMarkConfig.basedGrandMarks;
                        const rangeKey = 'range';
                        if (!getAttendanceResult.hasOwnProperty(rangeKey)) {
                            getAttendanceResult[rangeKey] = [];
                        }
                        getAttendanceResult[rangeKey].unshift({
                            _id: getAttendanceElement._id,
                            ...getAttendanceElement.attendanceMarkConfig.range,
                        });
                    }
                    return { getLateResult, getAttendanceResult };
                },
                { getLateResult: {}, getAttendanceResult: {} },
            );
            getAttendanceData = {
                lateConfig: Object.values(getAttendanceData.getLateResult),
                attendanceConfig: getAttendanceData.getAttendanceResult,
            };
            globalLateConfig = await institutionSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(_institution_id),
                    },
                    {
                        lateConfig: 1,
                    },
                )
                .lean();
        }
        if (!getAttendanceData) return { statusCode: 200, message: 'NO_DATA' };
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: { getAttendanceData, globalLateConfig },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.attendanceConfig = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
        } = req;
        const getAttendanceData = await lmsLateConfigSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    mode: LATE_CONFIG,
                },
                {
                    'lateConfig.range._id': 1,
                    'lateConfig.range.lateLabel': 1,
                    'lateConfig.range.short_code': 1,
                },
            )
            .lean();
        if (!getAttendanceData) return sendResponse(res, 200, false, 'NO_DATA', null);
        const rangeData = [];
        if (getAttendanceData && getAttendanceData.length) {
            for (const getAttendanceDataElement of getAttendanceData) {
                rangeData.push({
                    _id: getAttendanceDataElement._id,
                    lateLabel: getAttendanceDataElement.lateConfig.range.lateLabel,
                    short_code: getAttendanceDataElement.lateConfig.range.short_code,
                });
            }
        }
        return sendResponse(res, 200, true, 'LIST_DATA', rangeData);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.updateLateExclude = async ({ body = {} }) => {
    try {
        const {
            _id,
            programId,
            yearNo,
            levelNo,
            term,
            courseId,
            rotation,
            rotationCount,
            studentId,
            typeWiseUpdate,
            excludeStatus,
            institutionCalendarId,
        } = body;
        let { sessionIds } = body;
        if (_id) {
            await lmsLateConfigSettingSchema.updateOne(
                { _id: convertToMongoObjectId(_id) },
                { $set: { excludeStatus } },
            );
            return { statusCode: 200, message: 'DATA_UPDATED' };
        }
        let queryData = {
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            programId: convertToMongoObjectId(programId),
            levelNo,
            term,
            courseId: convertToMongoObjectId(courseId),
            typeWiseUpdate,
            ...(rotationCount && { rotationCount }),
            ...(studentId && { studentId: convertToMongoObjectId(studentId) }),
            isActive: true,
            isDeleted: false,
        };
        if (sessionIds && sessionIds.length) {
            const courseScheduleData = await courseScheduleSchema
                .find(
                    {
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        level_no: levelNo,
                        term,
                        merge_status: true,
                        _course_id: convertToMongoObjectId(courseId),
                        'session._session_id': { $in: sessionIds.map(convertToMongoObjectId) },
                    },
                    {
                        'merge_with.session_id': 1,
                    },
                )
                .lean();
            for (const scheduleData of courseScheduleData) {
                for (const mergeElement of scheduleData.merge_with) {
                    if (!sessionIds.includes(mergeElement.session_id.toString())) {
                        sessionIds = [...sessionIds, mergeElement.session_id.toString()];
                    }
                }
            }
            await lateConfigManagementSchema.updateMany(queryData, {
                $set: { excludeStatus: false },
            });
            const bulkOperations = sessionIds.map((sessionId) => ({
                updateOne: {
                    filter: {
                        ...queryData,
                        sessionId: convertToMongoObjectId(sessionId),
                    },
                    update: { $set: { excludeStatus } },
                    upsert: true,
                },
            }));
            await lateConfigManagementSchema.bulkWrite(bulkOperations);
            if (typeWiseUpdate === 'session_wise' && excludeStatus === true) {
                await lateConfigManagementSchema.updateMany(
                    {
                        ...queryData,
                        typeWiseUpdate: { $ne: 'session_wise' },
                    },
                    { $set: { excludeStatus: false } },
                );
            }
        } else {
            let lateSchema = await lateConfigManagementSchema.findOne(queryData, { _id: 1 }).lean();
            if (!lateSchema) {
                queryData = { ...queryData, excludeStatus };
                lateSchema = await lateConfigManagementSchema.create(queryData);
            } else {
                await lateConfigManagementSchema.updateOne(queryData, { $set: { excludeStatus } });
            }
            if (typeWiseUpdate === 'course_wise' && excludeStatus === true) {
                await lateConfigManagementSchema.updateMany(
                    {
                        isActive: true,
                        isDeleted: false,
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        programId: convertToMongoObjectId(programId),
                        levelNo,
                        term,
                        courseId: convertToMongoObjectId(courseId),
                        typeWiseUpdate: { $ne: 'course_wise' },
                        ...(rotationCount && { rotationCount }),
                    },
                    { $set: { excludeStatus: false } },
                );
            } else if (typeWiseUpdate === 'student_wise') {
                await lateConfigManagementSchema.updateMany(
                    {
                        isActive: true,
                        isDeleted: false,
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        programId: convertToMongoObjectId(programId),
                        levelNo,
                        term,
                        courseId: convertToMongoObjectId(courseId),
                        studentId: convertToMongoObjectId(studentId),
                        typeWiseUpdate: { $ne: 'student_wise' },
                        ...(rotationCount && { rotationCount }),
                    },
                    { $set: { excludeStatus: false } },
                );
            }
        }
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};
exports.getLateExcludeStatus = async ({ query = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            courseId,
            studentId,
            rotation,
            rotationCount,
            typeWiseUpdate,
        } = query;
        let lateSchema = await lateConfigManagementSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    programId: convertToMongoObjectId(programId),
                    levelNo,
                    term,
                    courseId: convertToMongoObjectId(courseId),
                    typeWiseUpdate,
                    ...(rotationCount && { rotationCount }),
                    ...(studentId && { studentId: convertToMongoObjectId(studentId) }),
                    isActive: true,
                    isDeleted: false,
                },
                { excludeStatus: 1 },
            )
            .sort({ updatedAt: -1 });
        if (!lateSchema) return { statusCode: 200, message: 'NO_DATA', data: false };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: lateSchema.excludeStatus,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
