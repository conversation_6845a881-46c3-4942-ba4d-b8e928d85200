const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const {
    USER,
    DISCUSSION_TOPICS,
    INSTITUTION,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
    DISCUSSION_UNREAD,
} = require('../../utility/constants');

const discussionTopics = new Schema(
    {
        title: {
            type: String,
            required: true,
            minlength: 3,
            maxlength: 100,
        },
        topics: {
            type: String,
            required: true,
            minlength: 3,
            maxlength: 100,
        },
        description: {
            type: String,
            minlength: 3,
            maxlength: 1024,
            required: true,
        },
        author: {
            type: ObjectId,
            ref: USER,
            required: true,
        },
        channel_id: {
            type: String,
            required: true,
        },
        institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        course_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        year: {
            type: String,
            required: true,
        },
        level_no: {
            type: String,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        rotation: String,
        rotation_count: Number,
        main_group_name: {
            type: String,
            required: true,
        },
        group_name: {
            type: String,
            required: true,
        },
        admin_course: {
            type: Boolean,
            required: true,
        },
        merge_status: {
            type: Boolean,
            required: true,
        },
        likes: {
            type: [ObjectId],
            ref: USER,
            default: [],
        },
        attachments: [
            {
                url: String,
                name: String,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

const discussionUnRead = new Schema(
    {
        channelId: { type: String },
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        userIds: [{ userId: { type: ObjectId, ref: USER }, unReadCount: Number }],
    },
    { timestamps: true },
);

// mongoose.set('useFindAndModify', false);

const discussionTopicsSchema = mongoose.model(DISCUSSION_TOPICS, discussionTopics);
const discussionUnReadSchema = mongoose.model(DISCUSSION_UNREAD, discussionUnRead);

module.exports = { discussionTopicsSchema, discussionUnReadSchema };
