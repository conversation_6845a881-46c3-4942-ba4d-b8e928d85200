const {
    response_function,
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
} = require('../utility/common');
const { get_list, get, get_list_populate } = require('../base/base_controller');
const {
    INSTITUTION,
    // PROGRAM_CALENDAR,
    // DIGI_COURSE,
    // USER,
    // ROL<PERSON>,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    USER,
    PART_TIME,
    FULL_TIME,
    PROGRAM_CALENDAR,
    STUDENT_GROUP,
    DIGI_SESSION_ORDER,
    DIGI_SESSION_DELIVERY_TYPES,
    DIGI_DEPARTMENT_SUBJECT,
    D<PERSON>I_COURSE,
    <PERSON>ADEM<PERSON>,
    ADMINISTRATION,
    BOTH,
    COURSE_SCHEDULE,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    GENDER: { MALE, FEMALE /*  BOTH */ },
    TIME_GROUP_BOOKING_TYPE: { ONSITE, REMOTE },
    COMPLETED,
    PENDING,
    ABSENT,
    PRESENT,
    LEAVE,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    SCHEDULE_TYPES: { REGULAR, SUPPORT_SESSION },
    EXCLUDE,
} = require('../utility/constants');
const {
    staffSplit,
    scheduleDateFormateChange,
    courseSessionOrderFilterBasedSchedule,
} = require('../utility/common_functions');
const { SCHEDULE_SESSION_BASED_REPORT } = require('../utility/util_keys');
const { getStaffRatings } = require('./reports_analytics_services');
const {
    allCourseList,
    allStudentGroupYesterday,
    allSessionOrderDatas,
    allProgramCalendarDatas,
    allSessionDeliveryTypesDatas,
} = require('../../service/cache.service');
const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
// const course = require('mongoose').model(DIGI_COURSE);
const user = require('mongoose').model(USER);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
const digi_curriculum = require('mongoose').model(DIGI_CURRICULUM);
const program = require('mongoose').model(DIGI_PROGRAM);
const student_group = require('mongoose').model(STUDENT_GROUP);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const session_delivery_type = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const course = require('mongoose').model(DIGI_COURSE);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const {
    getLateAutoAndManualRange,
    getLateLabelForSchedule,
    getConfigAndStudentLateAbsentForSingleStudent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    getLateConfigAndStudentLateAbsent,
} = require('../utility/utility.service');
const compare = function (a, b) {
    if (a.s_no < b.s_no) {
        return -1;
    }
    if (a.s_no > b.s_no) {
        return 1;
    }
    return 0;
};
const studentGroupStudentFilter = async (sgCourseData, gender, groupNo) => {
    const genderGroup = sgCourseData.setting.find((ele) =>
        ele && groupNo && groupNo !== 0
            ? ele.gender === gender && ele._group_no.toString() === groupNo.toString()
            : ele.gender === gender,
    );
    const deliveryGroup =
        genderGroup &&
        genderGroup.session_setting &&
        genderGroup.session_setting.length &&
        genderGroup.session_setting[0].groups
            ? genderGroup.session_setting[0].groups
            : [];
    const studentIds = deliveryGroup.map((ele) => ele._student_ids).flat();
    return studentIds;
};

const studentGroupStudentFilterWithDelivery = async (
    sgCourseData,
    session_type,
    gender,
    session_group_id,
    groupNo,
) => {
    const genderGroup = sgCourseData.setting.find((ele) =>
        ele && groupNo && groupNo !== 0
            ? ele.gender === gender && ele._group_no.toString() === groupNo.toString()
            : ele.gender === gender,
    );
    let deliveryDatas = [];
    if (genderGroup && genderGroup.session_setting) {
        const sessionElement = genderGroup.session_setting.find(
            (ele) => ele.session_type === session_type,
        );
        const groupElement = sessionElement
            ? sessionElement.groups.find((ele) => ele._id.toString() === session_group_id)
            : [];
        deliveryDatas = groupElement ? groupElement._student_ids : [];
    }
    return deliveryDatas;
};

const studentsAttendanceAlongSG = async (
    scheduleData,
    sgCourseData,
    gender,
    group_name,
    group_no,
) => {
    const groupSchedule = clone(
        scheduleData
            .filter((ele) =>
                ele.student_groups.find(
                    (ele2) => ele2.gender === gender && ele2.group_name === group_name,
                ),
            )
            .map((ele2) => {
                return {
                    _id: ele2._id,
                    session: ele2.session,
                    status: ele2.status,
                    student_groups: ele2.student_groups,
                    students: ele2.students,
                    merge_status: ele2.merge_status,
                    merge_with: ele2.merge_with,
                    merge_sessions: [],
                    isActive: ele2.isActive,
                    staffs: ele2.staffs,
                };
            }),
    );
    const mergedSchedules = clone(groupSchedule.filter((ele) => ele.merge_status === true));
    for (schedules of mergedSchedules) {
        for (mergeSchedule of schedules.merge_with) {
            const mergedLoc = mergedSchedules.findIndex(
                (ele) => ele._id.toString() === mergeSchedule.schedule_id.toString(),
            );
            if (mergedLoc !== -1) {
                schedules.merge_sessions.push(mergedSchedules[mergedLoc].session);
                schedules.student_groups = [
                    ...schedules.student_groups,
                    ...mergedSchedules[mergedLoc].student_groups,
                ];
                mergedSchedules.splice(mergedLoc, 1);
            }
        }
    }
    const normalSchedules = clone([
        ...clone(groupSchedule.filter((ele) => ele.merge_status === false)),
        ...mergedSchedules,
    ]);
    for (scheduleElement of normalSchedules) {
        for (sgSchedule of scheduleElement.student_groups) {
            const session_group = [];
            for (sessionSGSchedule of sgSchedule.session_group) {
                let studentSGIds = await studentGroupStudentFilterWithDelivery(
                    sgCourseData,
                    scheduleElement.session.delivery_symbol,
                    sgSchedule.gender,
                    sessionSGSchedule.session_group_id
                        ? sessionSGSchedule.session_group_id.toString()
                        : '',
                    group_no,
                );
                if (scheduleElement.merge_status === true) {
                    for (mergedSessionElement of scheduleElement.merge_sessions) {
                        studentSGIds = [
                            ...studentSGIds,
                            ...(await studentGroupStudentFilterWithDelivery(
                                sgCourseData,
                                mergedSessionElement.delivery_symbol,
                                sgSchedule.gender,
                                sessionSGSchedule.session_group_id
                                    ? sessionSGSchedule.session_group_id.toString()
                                    : '',
                                group_no,
                            )),
                        ];
                    }
                }
                const studentDatas = scheduleElement.students.filter((ele) =>
                    studentSGIds.find(
                        (ele2) => ele._id.toString() === ele2.toString() && ele.status !== EXCLUDE,
                    ),
                );
                session_group.push({
                    group_no: sessionSGSchedule.group_no,
                    group_name: sessionSGSchedule.group_name,
                    student_count: studentDatas.length,
                    present_count: studentDatas.filter(
                        (ele) =>
                            scheduleElement.status === COMPLETED &&
                            (ele.status === PRESENT ||
                                ele.status === ONDUTY ||
                                ele.status === PERMISSION),
                    ).length,
                });
            }
            sgSchedule.session_group = session_group;
            delete sgSchedule.students;
        }
        scheduleElement.student_count = scheduleElement.students.filter(
            (studentsElement) => studentsElement.status !== EXCLUDE,
        ).length;
        if (scheduleElement.status === COMPLETED)
            scheduleElement.present_count = scheduleElement.students.filter(
                (ele) =>
                    ele.status === PRESENT || ele.status === ONDUTY || ele.status === PERMISSION,
            ).length;
        delete scheduleElement.students;
    }
    const response = {
        total_session: groupSchedule.length,
        complete_session: groupSchedule.filter(
            (ele) => ele.isActive === true && ele.status === COMPLETED,
        ).length,
        pending_session: groupSchedule.filter(
            (ele) => ele.isActive === true && ele.status === PENDING,
        ).length,
        merged_session: groupSchedule.filter(
            (ele) => ele.isActive === true && ele.merge_status === true,
        ).length,
        missed_session: groupSchedule.filter(
            (ele) => ele.isActive === true && ele.status === 'missed',
        ).length,
        canceled_session: groupSchedule.filter((ele) => ele.isActive === false).length,
        schedule: normalSchedules,
    };
    return response;
};

const studentsSupportSessionAttendanceAlongSG = async (
    scheduleData,
    gender,
    group_name,
    programSessionDelivery,
) => {
    const groupSchedule = clone(
        scheduleData
            .filter(
                (ele) =>
                    ele.student_groups.find(
                        (ele2) => ele2.gender === gender && ele2.group_name === group_name,
                    ) && ele.isActive === true,
            )
            .map((ele2) => {
                return {
                    _id: ele2._id,
                    title: ele2.title,
                    sub_type: ele2.sub_type,
                    status: ele2.status,
                    isActive: ele2.isActive,
                };
            }),
    );
    const deliveryDatas = [];
    const sessionDeliveryNames = ['Counselling', 'Academic Advisor', 'Feedback', 'Training'];
    for (sessionDeliveryNameElement of sessionDeliveryNames) {
        deliveryDatas.push({
            type: sessionDeliveryNameElement,
            total_session: groupSchedule.filter(
                (ele) => ele.sub_type.toLowerCase() === sessionDeliveryNameElement.toLowerCase(),
            ).length,
            complete_session: groupSchedule.filter(
                (ele) =>
                    ele.sub_type.toLowerCase() === sessionDeliveryNameElement.toLowerCase() &&
                    ele.status === COMPLETED,
            ).length,
        });
    }
    for (programSessionDeliveryElement of programSessionDelivery) {
        let total_session = 0;
        let complete_session = 0;
        for (deliveryTypeElement of programSessionDeliveryElement.delivery_types) {
            total_session += groupSchedule.filter(
                (ele) =>
                    ele.sub_type.toLowerCase() === deliveryTypeElement.delivery_name.toLowerCase(),
            ).length;
            complete_session += groupSchedule.filter(
                (ele) =>
                    ele.sub_type.toLowerCase() ===
                        deliveryTypeElement.delivery_name.toLowerCase() && ele.status === COMPLETED,
            ).length;
        }
        deliveryDatas.push({
            type: programSessionDeliveryElement.session_name,
            total_session,
            complete_session,
        });
    }
    const response = {
        total_session: groupSchedule.length,
        complete_session: groupSchedule.filter(
            (ele) => ele.isActive === true && ele.status === COMPLETED,
        ).length,
        delivery_data: deliveryDatas,
    };
    return response;
};

const courseCreditContactHours = ({
    sessionDeliveryTypesData,
    courseScheduled,
    sessionOrderData,
    credit_hours,
    gender,
    group_name,
}) => {
    const courseScheduleData = clone(
        courseScheduled.filter((ele) =>
            ele.student_groups.find(
                (ele2) => ele2.gender === gender && ele2.group_name === group_name,
            ),
        ),
    );
    const credit_hoursData = [];
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduleData
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        let sessionCompletedHours = 0;
        let endScheduleCount = 0;
        let scheduleSessionCount = 0;
        let endSchedule = [];
        if (deliveryTypeData.length) {
            // Altering Based on Scheduled Session SG wise
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    endScheduleCount++;
                    sessionCompletedHours += sessionOrderElement.duration;
                }
            }
            endSchedule = deliveryTypeData.filter((ele) => ele.status === COMPLETED);
        }
        const courseDeliveryWiseDurationPerContactHours =
            deliveryCredit &&
            deliveryCredit.duration_per_contact_hour &&
            parseInt(deliveryCredit.duration_per_contact_hour)
                ? parseInt(deliveryCredit.duration_per_contact_hour)
                : 60;
        const sessionCompletedCredit =
            sessionCompletedHours !== 0
                ? sessionCompletedHours / courseDeliveryWiseDurationPerContactHours
                : 0;

        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours:
                deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
            completed_credit_hours:
                sessionCompletedCredit /
                    (deliveryCredit && deliveryCredit.contact_hours
                        ? parseInt(deliveryCredit.contact_hours)
                        : parseInt(sessionTypeElement.contact_hour_per_credit_hour)) || 0,
            // completed_contact_hours:
            //     (endScheduleCount * parseInt(sessionTypeElement.session_duration)) / 60 || 0,
            // completed_credit_hours:
            //     sessionCompletedCredit /
            //         parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0,
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours:
                parseInt(
                    deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
                ) *
                (deliveryCredit && deliveryCredit.contact_hours
                    ? parseInt(deliveryCredit.contact_hours)
                    : parseInt(sessionTypeElement.contact_hour_per_credit_hour)),
            no_of_sessionsSchedule: scheduleSessionCount,
            completed_sessionsSchedule: endScheduleCount,
            // no_of_sessions: deliveryTypeData.length,
            no_of_sessions: sessionOrderData.filter(
                (sessionOrderElement) =>
                    sessionOrderElement._session_id.toString() ===
                    sessionTypeElement._id.toString(),
            ).length,
            completed_sessions: endSchedule.length,
        });
    }
    return credit_hoursData;
};

const AttendanceLearningOutcome = async (
    scheduleData,
    sgCourseData,
    gender,
    group_name,
    courseSessionFlow,
    sessionDelivery,
    courseSols,
    groupNo,
) => {
    const groupSchedule = clone(
        scheduleData
            .filter(
                (ele) =>
                    ele.isActive &&
                    ele.status === COMPLETED &&
                    ele.student_groups.find(
                        (ele2) => ele2.gender === gender && ele2.group_name === group_name,
                    ),
            )
            .map((ele2) => {
                return {
                    _id: ele2._id,
                    session: ele2.session,
                    status: ele2.status,
                    student_groups: ele2.student_groups,
                };
            }),
    );
    const genderGroup = sgCourseData.setting.find((ele) =>
        ele && groupNo && groupNo !== 0
            ? ele.gender === gender && ele._group_no.toString() === groupNo.toString()
            : ele.gender === gender,
    );
    for (const sessionDeliveryElement of sessionDelivery) {
        let sessionIds = [];

        if (genderGroup && genderGroup.session_setting) {
            const sessionElement = genderGroup.session_setting.find(
                (ele) => ele.session_type === sessionDeliveryElement.delivery_symbol,
            );
            sessionIds = sessionElement
                ? sessionElement.groups.map((ele) => ele._id.toString())
                : [];
        }
        const sessionSessionFLow = courseSessionFlow.filter(
            (ele) => ele.delivery_symbol === sessionDeliveryElement.delivery_symbol,
        );
        for (deliverySessionElement of sessionSessionFLow) {
            if (groupSchedule.length) {
                const schedules = groupSchedule.filter(
                    (ele) =>
                        ele.session.delivery_symbol === sessionDeliveryElement.delivery_symbol &&
                        ele.session.delivery_no === deliverySessionElement.delivery_no,
                );
                const scheduleGroupSessionId = schedules
                    .map((ele) =>
                        ele.student_groups
                            .map((ele2) =>
                                ele2.session_group.map((ele3) => ele3.session_group_id.toString()),
                            )
                            .flat(),
                    )
                    .flat();
                const deliveryScheduleSG = sessionIds.filter(
                    (ele) => !scheduleGroupSessionId.includes(ele.toString()),
                );
                if (deliveryScheduleSG.length === 0) {
                    const courseDeliverySlo = courseSols.filter(
                        (ele) =>
                            ele.delivery_symbol === sessionDeliveryElement.delivery_symbol &&
                            ele.delivery_no === deliverySessionElement.delivery_no,
                    );
                    sessionDeliveryElement.executed_slo_count += courseDeliverySlo.length;
                }
            }
        }
    }
    return sessionDelivery;
};

const courseDomains = async (
    courseData,
    // To At
    scheduleData,
    sgCourseData,
    gender,
    group_name,
    courseSessionFlow,
    sessionDelivery,
    groupNo,
) => {
    const courseDomainData = [];
    if (courseData.framework && courseData.framework.domains)
        for (domainElement of courseData.framework.domains) {
            let courseSols = [];
            const deliveryMappedSlo = clone(sessionDelivery);
            for (cloElement of domainElement.clo) {
                for (sessionElement of deliveryMappedSlo) {
                    sessionElement.mapped_slo_count += cloElement.slos.filter(
                        (ele) => ele.delivery_symbol === sessionElement.delivery_symbol,
                    ).length;
                    courseSols = [
                        ...courseSols,
                        ...cloElement.slos.filter(
                            (ele) => ele.delivery_symbol === sessionElement.delivery_symbol,
                        ),
                    ];
                }
            }
            const attendance_learning_outcome = await AttendanceLearningOutcome(
                scheduleData,
                sgCourseData,
                gender,
                group_name,
                courseSessionFlow,
                clone(deliveryMappedSlo),
                courseSols,
                groupNo,
            );
            courseDomainData.push({
                _id: domainElement._id,
                name: domainElement.name,
                no: domainElement.no,
                session_delivery: attendance_learning_outcome,
            });
        }
    return courseDomainData;
};

const studentScheduleCountWithSessionType = async (
    sgCourseData,
    scheduleData,
    gender,
    group_name,
    groupNo,
) => {
    const deliveryTypeSchedules = [];
    for (courseSessionTypes of sgCourseData) {
        const sessionTypeDatas = scheduleData.filter(
            (ele) =>
                /* ele.isActive && */
                ele.session.delivery_symbol === courseSessionTypes.symbol &&
                ele.student_groups.find((ele2) =>
                    ele2 && groupNo && groupNo !== 0
                        ? ele2.gender === gender &&
                          ele2.group_no.toString() === groupNo.toString() &&
                          ele2.group_name === group_name
                        : ele2.gender === gender && ele2.group_name === group_name,
                ),
        );
        const deliveryTypeScheduleIndex = deliveryTypeSchedules.findIndex(
            (ele) => ele && ele.session_type === courseSessionTypes.session_type,
        );
        if (deliveryTypeScheduleIndex === -1)
            deliveryTypeSchedules.push({
                session_type: courseSessionTypes.session_type,
                schedule_count: sessionTypeDatas.length,
                schedule_completed_count: sessionTypeDatas.filter((ele) => ele.status === COMPLETED)
                    .length,
            });
        else {
            deliveryTypeSchedules[deliveryTypeScheduleIndex].schedule_count +=
                sessionTypeDatas.length;
            deliveryTypeSchedules[deliveryTypeScheduleIndex].schedule_completed_count +=
                sessionTypeDatas.filter((ele) => ele.status === COMPLETED).length;
        }
    }
    return deliveryTypeSchedules;
};

const scheduleSubjectStaff = async (
    scheduleSubjects,
    scheduleData,
    sgCourseData,
    gender,
    group_name,
    groupNo,
) => {
    const subjectsData = [];
    for (scheduleSub of scheduleSubjects) {
        const subjectSchedules = scheduleData.filter(
            (ele) =>
                ele.student_groups.find((ele2) =>
                    ele2 && groupNo && groupNo !== 0
                        ? ele2.gender === gender &&
                          ele2.group_no.toString() === groupNo.toString() &&
                          ele2.group_name === group_name
                        : ele2.gender === gender && ele2.group_name === group_name,
                ) &&
                ele.subjects.find(
                    (ele2) => ele2._subject_id.toString() === scheduleSub._subject_id.toString(),
                ),
        );
        let subjectStaffs = subjectSchedules.map((ele) => ele.staffs).flat();
        subjectStaffs = subjectStaffs.filter(
            (ele, index) =>
                subjectStaffs.findIndex(
                    (ele2) => ele2._staff_id.toString() === ele._staff_id.toString(),
                ) === index,
        );
        const staffs = [];
        for (staffElement of subjectStaffs) {
            const staffSchedule = subjectSchedules.filter((ele) =>
                ele.staffs.find(
                    (ele2) => ele2._staff_id.toString() === staffElement._staff_id.toString(),
                ),
            );
            const staffDeliveryTypeSchedules = await studentScheduleCountWithSessionType(
                sgCourseData.session_types,
                staffSchedule,
                gender,
                group_name,
                groupNo,
            );
            staffs.push({
                staff_name: staffElement.staff_name,
                _staff_id: staffElement._staff_id,
                session_type_schedule: staffDeliveryTypeSchedules,
            });
        }
        subjectsData.push({
            _subject_id: scheduleSub._subject_id,
            subject_name: scheduleSub.subject_name,
            staff_count: subjectStaffs.length,
            staffs,
        });
    }
    return subjectsData;
};

// Get Course -> Along with Student Groups
exports.course_overview = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term },
            query: { rotation_count },
        } = req;

        let courseData = await allCourseList();
        courseData = courseData.find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );
        const coordinator = courseData.coordinators
            ? courseData.coordinators.find(
                  (ele) =>
                      ele &&
                      ele._institution_calendar_id.toString() ===
                          institutionCalendarId.toString() &&
                      ele.term === term,
              )
            : '';
        const shared_with = courseData.course_assigned_details.find(
            (ele) =>
                ele._program_id.toString() === programId.toString() && ele.level_no === levelNo,
        );
        const courseResponse = {
            administration: courseData.administration,
            participating: courseData.participating,
            coordinators: coordinator ? coordinator.user_name : '',
            shared_with: shared_with ? shared_with.course_shared_with : [],
            versionNo: courseData.versionNo || 1,
            versioned: courseData.versioned || false,
            versionName: courseData.versionName || '',
            versionedFrom: courseData.versionedFrom || null,
            versionedCourseIds: courseData.versionedCourseIds || [],
        };
        let pcData = await allProgramCalendarDatas();
        pcData = pcData.filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.course.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                        levelElement.rotation_course.some((rotationElement) =>
                            rotationElement.course.some(
                                (courseElement) =>
                                    courseElement._course_id.toString() === courseId.toString(),
                            ),
                        ),
                ),
        );
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.term === term &&
                    pcLevel.rotation === 'no' &&
                    pcLevel.level_no.toString() === levelNo
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        courseResponse.program_name =
                            shared_with && shared_with.program_name ? shared_with.program_name : '';
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    pcLevel.term === term &&
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.level_no.toString() === levelNo
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );
                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) => ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            courseResponse.program_name =
                                shared_with && shared_with.program_name
                                    ? shared_with.program_name
                                    : '';
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }

        let studentGroupData = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        studentGroupData = studentGroupData.find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.some(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level.toString() === levelNo.toString() &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const master_group = [];
        const sgLevel = studentGroupData.groups.find(
            (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
        );
        if (!sgLevel)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('LEVEL_NOT_FOUND_IN_STUDENT_GROUP'),
                        req.t('LEVEL_NOT_FOUND_IN_STUDENT_GROUP'),
                    ),
                );
        const sgCourseData = sgLevel.courses.find((ele) =>
            rotation_count && rotation_count !== 0
                ? /* ele.rotation_count.toString() === rotation_count.toString() && */
                  ele._course_id.toString() === courseId &&
                  ele.setting.find(
                      (ele2) => ele2._group_no.toString() === rotation_count.toString(),
                  )
                : ele._course_id.toString() === courseId,
        );
        if (!sgCourseData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        console.log(sgCourseData);
        console.time('courseSchedule');
        // Course Schedule for Course list
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                // type: 'regular',
                // status: COMPLETED,
                term,
                level_no: levelNo,
                // isActive: true,
                isDeleted: false,
            },
            {
                session: 1,
                status: 1,
                merge_status: 1,
                merge_with: 1,
                student_groups: 1,
                mode: 1,
                subjects: 1,
                staffs: 1,
                students: 1,
                isActive: 1,
                type: 1,
                title: 1,
                sub_type: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        console.timeEnd('courseSchedule');
        const scheduleSupportData = scheduleData.data.filter(
            (scheduleElement) =>
                scheduleElement.type === SUPPORT_SESSION && scheduleElement.sub_type,
        );
        scheduleData.data = clone(
            scheduleData.data.filter((scheduleElement) => scheduleElement.type === REGULAR),
        );
        const programSessionDelivery = (await allSessionDeliveryTypesDatas()).filter(
            (sessionOrderElement) =>
                sessionOrderElement._program_id.toString() === programId.toString(),
        );
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) =>
                sessionOrderElement._course_id.toString() === courseId.toString(),
        );

        // * Session Schedule Based Report Course Session Order filter based on Schedule
        let sessionFlowData = courseSessionOrder
            ? clone(courseSessionOrder)
            : { session_flow_data: [] };
        if (SCHEDULE_SESSION_BASED_REPORT && SCHEDULE_SESSION_BASED_REPORT === 'true') {
            sessionFlowData = courseSessionOrderFilterBasedSchedule({
                courseSessionFlow: courseSessionOrder,
                courseSchedule: scheduleData.data,
            });
        }
        const courseSessionFlow = sessionFlowData
            ? sessionFlowData.session_flow_data.map((ele) => {
                  return {
                      _session_id: ele._session_id,
                      delivery_type: ele.delivery_type,
                      _delivery_id: ele._delivery_id,
                      delivery_symbol: ele.delivery_symbol,
                      delivery_no: ele.delivery_no,
                  };
              })
            : [];

        let sessionDelivery = sessionFlowData
            ? sessionFlowData.session_flow_data.map((ele) => {
                  return {
                      _session_id: ele._session_id,
                      delivery_type: ele.delivery_type,
                      _delivery_id: ele._delivery_id,
                      delivery_symbol: ele.delivery_symbol,
                      mapped_slo_count: 0,
                      executed_slo_count: 0,
                  };
              })
            : [];
        sessionDelivery = sessionDelivery.filter(
            (ele, index) =>
                sessionDelivery.findIndex(
                    (ele2) => ele2._delivery_id.toString() === ele._delivery_id.toString(),
                ) === index,
        );
        let scheduleSubjects = scheduleData.data.map((ele) => ele.subjects).flat();
        scheduleSubjects = scheduleSubjects.filter(
            (ele, index) =>
                scheduleSubjects.findIndex(
                    (ele2) => ele2._subject_id.toString() === ele._subject_id.toString(),
                ) === index,
        );
        let maleStudentCount = 0;
        let femaleStudentCount = 0;
        switch (sgLevel.group_mode.toString()) {
            case FYD.toString():
                for (const foundationGroupSetting of sgLevel.group_setting) {
                    for (const foundationGroup of foundationGroupSetting.groups) {
                        const studentIds = await studentGroupStudentFilter(
                            sgCourseData,
                            foundationGroupSetting.gender,
                            foundationGroup.group_no.toString(),
                        );
                        if (foundationGroupSetting.gender === BOTH) {
                            maleStudentCount += studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === MALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            ).length;
                            femaleStudentCount += studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === FEMALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            ).length;
                        } else if (foundationGroupSetting.gender === MALE)
                            maleStudentCount += studentIds.length;
                        else femaleStudentCount += studentIds.length;
                        const deliveryTypeSchedules = await studentScheduleCountWithSessionType(
                            sgCourseData.session_types,
                            scheduleData.data,
                            foundationGroupSetting.gender,
                            foundationGroup.group_name,
                            foundationGroup.group_no.toString(),
                        );
                        const subjectsData = await scheduleSubjectStaff(
                            scheduleSubjects,
                            scheduleData.data,
                            sgCourseData,
                            foundationGroupSetting.gender,
                            foundationGroup.group_name,
                            foundationGroup.group_no.toString(),
                        );
                        const student_attendance = await studentsAttendanceAlongSG(
                            scheduleData.data,
                            sgCourseData,
                            foundationGroupSetting.gender,
                            foundationGroup.group_name,
                            foundationGroup.group_no.toString(),
                        );
                        const student_support_session =
                            await studentsSupportSessionAttendanceAlongSG(
                                scheduleSupportData,
                                foundationGroupSetting.gender,
                                foundationGroup.group_name,
                                programSessionDelivery,
                            );
                        // Credit Hours Data Calculation
                        const credit_hours = courseCreditContactHours({
                            sessionDeliveryTypesData: programSessionDelivery,
                            courseScheduled: scheduleData.data,
                            sessionOrderData: sessionFlowData
                                ? sessionFlowData.session_flow_data
                                : [],
                            credit_hours: courseData.credit_hours
                                ? courseData.credit_hours
                                : courseResponse.credit_hours,
                            gender: foundationGroupSetting.gender,
                            group_name: foundationGroup.group_name,
                        });
                        const courseDomainData = await courseDomains(
                            courseData,
                            scheduleData.data,
                            sgCourseData,
                            foundationGroupSetting.gender,
                            foundationGroup.group_name,
                            courseSessionFlow,
                            sessionDelivery,
                            foundationGroup.group_no.toString(),
                        );
                        master_group.push({
                            _id: foundationGroup._id,
                            gender: foundationGroupSetting.gender,
                            group_no: foundationGroup.group_no,
                            group_name: foundationGroup.group_name,
                            student_count: studentIds.length,
                            session_type_schedule: deliveryTypeSchedules,
                            subject_session_status: subjectsData,
                            student_attendance,
                            domains: courseDomainData,
                            student_support_session,
                            credit_hours,
                        });
                    }
                }
                break;
            case ROTATION.toString():
                for (const rotationGroupSetting of sgLevel.rotation_group_setting) {
                    if (
                        rotation_count &&
                        rotation_count.toString() === rotationGroupSetting.group_no.toString()
                    ) {
                        const studentIds = await studentGroupStudentFilter(
                            sgCourseData,
                            rotationGroupSetting.gender,
                            rotationGroupSetting.group_no.toString(),
                        );
                        if (rotationGroupSetting.gender === BOTH) {
                            maleStudentCount += studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === MALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            ).length;
                            femaleStudentCount += studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === FEMALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            ).length;
                        } else if (rotationGroupSetting.gender === MALE)
                            maleStudentCount += studentIds.length;
                        else femaleStudentCount += studentIds.length;
                        const deliveryTypeSchedules = await studentScheduleCountWithSessionType(
                            sgCourseData.session_types,
                            scheduleData.data,
                            rotationGroupSetting.gender,
                            rotationGroupSetting.group_name,
                            rotationGroupSetting.group_no.toString(),
                        );
                        const subjectsData = await scheduleSubjectStaff(
                            scheduleSubjects,
                            scheduleData.data,
                            sgCourseData,
                            rotationGroupSetting.gender,
                            rotationGroupSetting.group_name,
                            rotationGroupSetting.group_no.toString(),
                        );
                        const student_attendance = await studentsAttendanceAlongSG(
                            scheduleData.data,
                            sgCourseData,
                            rotationGroupSetting.gender,
                            rotationGroupSetting.group_name,
                            rotationGroupSetting.group_no.toString(),
                        );
                        const student_support_session =
                            await studentsSupportSessionAttendanceAlongSG(
                                scheduleSupportData,
                                rotationGroupSetting.gender,
                                rotationGroupSetting.group_name,
                                programSessionDelivery,
                            );
                        // Credit Hours Data Calculation
                        const credit_hours = courseCreditContactHours({
                            sessionDeliveryTypesData: programSessionDelivery,
                            courseScheduled: scheduleData.data,
                            sessionOrderData: sessionFlowData
                                ? sessionFlowData.session_flow_data
                                : [],
                            credit_hours: courseData.credit_hours
                                ? courseData.credit_hours
                                : courseResponse.credit_hours,
                            gender: rotationGroupSetting.gender,
                            group_name: rotationGroupSetting.group_name,
                        });
                        const courseDomainData = await courseDomains(
                            courseData,
                            scheduleData.data,
                            sgCourseData,
                            rotationGroupSetting.gender,
                            rotationGroupSetting.group_name,
                            courseSessionFlow,
                            sessionDelivery,
                            rotationGroupSetting.group_no.toString(),
                        );
                        master_group.push({
                            _id: rotationGroupSetting._id,
                            gender: rotationGroupSetting.gender,
                            rotation: 'yes',
                            rotation_count: rotationGroupSetting.group_no,
                            group_no: rotationGroupSetting.group_no,
                            group_name: rotationGroupSetting.group_name,
                            student_count: studentIds.length,
                            session_type_schedule: deliveryTypeSchedules,
                            subject_session_status: subjectsData,
                            student_attendance,
                            domains: courseDomainData,
                            student_support_session,
                            credit_hours,
                        });
                    }
                }
                break;
            case COURSE.toString():
                // for (sgCourse of sgLevel.courses) {
                if (sgCourseData) {
                    const sgCourse = sgCourseData;
                    if (master_group.length === 0 && sgCourse.setting.length !== 0)
                        for (const setting of sgCourse.setting) {
                            const group_name =
                                setting.gender === BOTH
                                    ? 'SG1'
                                    : setting.gender === MALE
                                    ? 'MG-1'
                                    : 'FG-1';
                            const studentIds = await studentGroupStudentFilter(
                                sgCourseData,
                                setting.gender,
                            );
                            if (setting.gender === BOTH) {
                                maleStudentCount += studentIds.filter((studentIdElement) =>
                                    sgLevel.students.find(
                                        (studentElement) =>
                                            studentElement.gender === MALE &&
                                            studentElement._student_id.toString() ===
                                                studentIdElement.toString(),
                                    ),
                                ).length;
                                femaleStudentCount += studentIds.filter((studentIdElement) =>
                                    sgLevel.students.find(
                                        (studentElement) =>
                                            studentElement.gender === FEMALE &&
                                            studentElement._student_id.toString() ===
                                                studentIdElement.toString(),
                                    ),
                                ).length;
                            } else if (setting.gender === MALE)
                                maleStudentCount += studentIds.length;
                            else femaleStudentCount += studentIds.length;
                            const deliveryTypeSchedules = await studentScheduleCountWithSessionType(
                                sgCourseData.session_types,
                                scheduleData.data,
                                setting.gender,
                                group_name,
                            );
                            const subjectsData = await scheduleSubjectStaff(
                                scheduleSubjects,
                                scheduleData.data,
                                sgCourseData,
                                setting.gender,
                                group_name,
                            );
                            const student_attendance = await studentsAttendanceAlongSG(
                                scheduleData.data,
                                sgCourseData,
                                setting.gender,
                                group_name,
                            );
                            const student_support_session =
                                await studentsSupportSessionAttendanceAlongSG(
                                    scheduleSupportData,
                                    setting.gender,
                                    group_name,
                                    programSessionDelivery,
                                );
                            // Credit Hours Data Calculation
                            const credit_hours = courseCreditContactHours({
                                sessionDeliveryTypesData: programSessionDelivery,
                                courseScheduled: scheduleData.data,
                                sessionOrderData: sessionFlowData
                                    ? sessionFlowData.session_flow_data
                                    : [],
                                credit_hours: courseData.credit_hours
                                    ? courseData.credit_hours
                                    : courseResponse.credit_hours,
                                gender: setting.gender,
                                group_name,
                            });
                            const courseDomainData = await courseDomains(
                                courseData,
                                scheduleData.data,
                                sgCourseData,
                                setting.gender,
                                group_name,
                                courseSessionFlow,
                                sessionDelivery,
                            );
                            master_group.push({
                                _id: setting._id,
                                gender: setting.gender,
                                group_no: 1,
                                group_name,
                                student_count: studentIds.length,
                                session_type_schedule: deliveryTypeSchedules,
                                subject_session_status: subjectsData,
                                student_attendance,
                                domains: courseDomainData,
                                student_support_session,
                                credit_hours,
                            });
                        }
                }
                break;
            default:
                break;
        }

        for (sgGroup of master_group) {
            sgGroup.onsite_count = scheduleData.data.filter(
                (ele) =>
                    ele.mode === ONSITE &&
                    ele.student_groups.find(
                        (ele2) =>
                            ele2.group_name === sgGroup.group_name &&
                            ele2.group_no === sgGroup.group_no &&
                            ele2.gender === sgGroup.gender,
                    ),
            ).length;
            sgGroup.remote_count = scheduleData.data.filter(
                (ele) =>
                    ele.mode === REMOTE &&
                    ele.student_groups.find(
                        (ele2) =>
                            ele2.group_name === sgGroup.group_name &&
                            ele2.group_no === sgGroup.group_no &&
                            ele2.gender === sgGroup.gender,
                    ),
            ).length;
        }

        //Staff Ratings

        const subjects = [];
        courseData.participating.forEach((elePSUB) => {
            subjects.push({
                _subject_id: elePSUB._subject_id,
                subject_name: elePSUB.subject_name,
            });
        });
        const ind = subjects.findIndex(
            (ele) =>
                ele._subject_id.toString() === courseData.administration._subject_id.toString(),
        );
        if (ind == -1)
            subjects.push({
                /* _program_id: courseData.administration._program_id,
                program_name: courseData.administration.program_name,
                _department_id: courseData.administration._department_id,
                department_name: courseData.administration.department_name, */
                _subject_id: courseData.administration._subject_id,
                subject_name: courseData.administration.subject_name,
            });

        // const staffRatings = await getStaffRatings(subjects, scheduleData.data);
        // return res.send({ courseData, scheduleData, master_group, studentGroupData });
        const responses = {
            male_count: maleStudentCount,
            female_count: femaleStudentCount,
            course_details: courseResponse,
            subject_list: scheduleSubjects,
            group_data: master_group,
            // staff_ratings: staffRatings,
        };
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('COURSE_OVERVIEW'), responses));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Course Learning Outcome with SG Group wise
exports.courseLearningOutCome = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term },
            query: { rotation_count },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // Course Data
        const courseData = await get(
            course,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(courseId),
                isActive: true,
                isDeleted: false,
            },
            {},
        );
        if (!courseData.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );

        // Session Delivery Type Data
        // const { data: deliveryTypeData } = await get_list(
        //     session_delivery_type,
        //     {
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _program_id: convertToMongoObjectId(programId),
        //         isActive: true,
        //         isDeleted: false,
        //     },
        //     {},
        // );

        // Session Flow Data
        const { data: sessionFlowData } = await get(
            session_order,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _course_id: convertToMongoObjectId(courseId),
                isActive: true,
                isDeleted: false,
            },
            {},
        );
        const courseSessionFlow = sessionFlowData.session_flow_data.map((ele) => {
            return {
                _session_id: ele._session_id,
                delivery_type: ele.delivery_type,
                _delivery_id: ele._delivery_id,
                delivery_symbol: ele.delivery_symbol,
                delivery_no: ele.delivery_no,
            };
        });
        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                term,
                type: 'regular',
                // status: COMPLETED,
                isActive: true,
                isDeleted: false,
            },
            {
                session: 1,
                status: 1,
                student_groups: 1,
                rotation: 1,
                rotation_count: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        // return res.send({ courseSessionFlow, scheduleData });
        let sessionDelivery = sessionFlowData.session_flow_data.map((ele) => {
            return {
                _session_id: ele._session_id,
                delivery_type: ele.delivery_type,
                _delivery_id: ele._delivery_id,
                delivery_symbol: ele.delivery_symbol,
                mapped_slo_count: 0,
                executed_slo_count: 0,
            };
        });
        sessionDelivery = sessionDelivery.filter(
            (ele, index) =>
                sessionDelivery.findIndex(
                    (ele2) => ele2._delivery_id.toString() === ele._delivery_id.toString(),
                ) === index,
        );
        const courseDomain = clone(courseData.data);
        const courseDomainData = [];
        let courseSols = [];
        if (courseDomain.framework && courseDomain.framework.domains)
            for (domainElement of courseDomain.framework.domains) {
                const deliveryMappedSlo = clone(sessionDelivery);
                for (cloElement of domainElement.clo) {
                    for (sessionElement of deliveryMappedSlo) {
                        sessionElement.mapped_slo_count += cloElement.slos.filter(
                            (ele) => ele.delivery_symbol === sessionElement.delivery_symbol,
                        ).length;
                        courseSols = [
                            ...courseSols,
                            ...cloElement.slos.filter(
                                (ele) => ele.delivery_symbol === sessionElement.delivery_symbol,
                            ),
                        ];
                    }
                }
                courseDomainData.push({
                    _id: domainElement._id,
                    name: domainElement.name,
                    no: domainElement.no,
                    session_delivery: deliveryMappedSlo,
                });
            }
        // return res.send({ courseDomainData, sessionDelivery, courseDomain, courseSols });
        const responses = {
            course_domains: courseDomainData,
        };
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('COURSE_OVERVIEW'), responses));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Staff Course wise Schedule Attendance Status
exports.courseStaffAttendanceReport = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term, userId, userType },
            query: { rotation_count },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const { data: cData } = await get(
            course,
            { _id: convertToMongoObjectId(courseId) },
            {
                _program_id: 1,
                'course_assigned_details._program_id': 1,
                'course_assigned_details.program_name': 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );
        //Staff Gets
        const staffData = await get(
            user,
            {
                _id: convertToMongoObjectId(userId),
                status: 'completed',
                user_type: userType,
                isActive: true,
                isDeleted: false,
            },
            { name: 1, user_id: 1, email: 1, gender: 1 },
        );
        staffData.data = staffData.status ? staffData.data : [];

        //Program Calendar
        const pc_query = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            'level.level_no': levelNo,
            'level.term': term,
            $or: [
                {
                    'level.course._course_id': convertToMongoObjectId(courseId),
                },
                {
                    'level.rotation_course.course._course_id': convertToMongoObjectId(courseId),
                },
            ],
        };
        pc_project = {
            _id: 1,
            _program_id: 1,
            isActive: 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.course._course_id': 1,
            'level.course.courses_name': 1,
            'level.course.start_date': 1,
            'level.course.end_date': 1,
            'level.course.courses_number': 1,
            'level.course.model': 1,
            'level.course.credit_hours': 1,
            'level._id': 1,
            'level.curriculum': 1,
            'level.term': 1,
            'level.year': 1,
            'level.level_no': 1,
            'level._program_id': 1,
            'level.rotation_course.rotation_count': 1,
            'level.rotation_course.course._course_id': 1,
            'level.rotation_course.course.courses_name': 1,
            'level.rotation_course.course.start_date': 1,
            'level.rotation_course.course.end_date': 1,
            'level.rotation_course.course.courses_number': 1,
            'level.rotation_course.course.model': 1,
            'level.rotation_course.course.credit_hours': 1,
            'level.rotation': 1,
        };
        const { status: pcStatus, data: pcData } = await get_list(
            program_calendar,
            pc_query,
            pc_project,
        );
        if (!pcStatus)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
            versionNo: cData.versionNo || 1,
            versioned: cData.versioned || false,
            versionName: cData.versionName || '',
            versionedFrom: cData.versionedFrom || null,
            versionedCourseIds: cData.versionedCourseIds || [],
        };
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount: rotation_count,
        });

        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.term === term &&
                    pcLevel.rotation === 'no' &&
                    pcLevel.level_no.toString() === levelNo
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        // courseResponse.program_name = shared_with.program_name;
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    pcLevel.term === term &&
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.level_no.toString() === levelNo
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );

                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) =>
                                ele._course_id && ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }
        const csQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            // 'staffs._staff_id': convertToMongoObjectId(userId),
            type: 'regular',
            term,
            level_no: levelNo,
            //isActive: true,
            isDeleted: false,
            rotation_count,
        };
        const csProject = {
            session: 1,
            status: 1,
            merge_status: 1,
            merge_with: 1,
            scheduleStartDateAndTime: 1,
            scheduleStartFrom: 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            mode: 1,
            subjects: 1,
            'sessionDetail.start_time': 1,
            // staffs: 1,
            isActive: 1,
            classModeType: 1,
        };
        if (userType === 'staff') {
            csQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            csProject.staffs = 1;
        } else {
            csQuery['students._id'] = convertToMongoObjectId(userId);
            csProject.students = 1;
        }
        // Course Schedule for Course list
        const scheduleData = await get_list(course_schedule, csQuery, csProject);
        if (!scheduleData.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Schedule Not found',
                        'Schedule Not found',
                    ),
                );
        scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
        //return res.send(scheduleData.data);

        //Merge Schedule handle
        const mergedSchedules = clone(scheduleData.data.filter((ele) => ele.merge_status === true));

        //Course Schedule for Merge schedule lookup
        const csDQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),

            type: 'regular',
            term,
            level_no: levelNo,
            isDeleted: false,
        };
        const csDProject = {
            session: 1,
            status: 1,
            merge_status: 1,
            merge_with: 1,
            mode: 1,
            subjects: 1,
            staffs: 1,
            students: 1,
            isActive: 1,
            student_groups: 1,
        };

        const csData = await get_list(course_schedule, csDQuery, csDProject);
        if (!csData.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSE_SCHEDULE_NOT_FOUND'),
                        req.t('COURSE_SCHEDULE_NOT_FOUND'),
                    ),
                );

        mergedSchedules.forEach((eleMS, indexMS) => {
            eleMS.merge_with.forEach((eleMW, indexMW) => {
                const mergedLoc = mergedSchedules.findIndex(
                    (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                );
                if (mergedLoc !== -1) {
                    if (!mergedSchedules[indexMS].merge_sessions)
                        mergedSchedules[indexMS].merge_sessions = [];
                    mergedSchedules[indexMS].merge_sessions.push({
                        session: mergedSchedules[mergedLoc].session,
                        student_groups: mergedSchedules[mergedLoc].student_groups,
                    });
                    mergedSchedules.splice(mergedLoc, 1);
                } else {
                    // Student Data wil not be available for both So where failed, So we have to find merge data in Course schedule data
                    const csInd = csData.data.findIndex(
                        (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                    );
                    //console.log(csData.data[csInd]);
                    if (csInd != -1) {
                        if (!mergedSchedules[indexMS].merge_sessions)
                            mergedSchedules[indexMS].merge_sessions = [];
                        mergedSchedules[indexMS].merge_sessions.push({
                            session: csData.data[csInd].session,
                            student_groups: csData.data[csInd].student_groups,
                        });
                    }
                }
            });
        });

        let normalSchedules = clone([
            ...clone(scheduleData.data.filter((ele) => ele.merge_status === false)),
            ...mergedSchedules,
        ]);
        //

        //Combine Merge session
        normalSchedules.forEach((eleNS, indexNS) => {
            let delivery_type = eleNS.session.delivery_symbol + eleNS.session.delivery_no;
            let mergeConcatSession = '';
            if (eleNS.merge_status === true) {
                eleNS.merge_with.forEach((eleMW, indexMW) => {
                    const csInd = scheduleData.data.findIndex(
                        (eleCourseS) => eleCourseS._id.toString() === eleMW.schedule_id.toString(),
                    );
                    if (csInd != -1) {
                        if (eleNS.merge_with.length == 1) {
                            mergeConcatSession +=
                                scheduleData.data[csInd].session.delivery_symbol +
                                scheduleData.data[csInd].session.delivery_no;
                        } else {
                            if (eleNS.merge_with.length - 1 === indexMW) {
                                mergeConcatSession +=
                                    scheduleData.data[csInd].session.delivery_symbol +
                                    scheduleData.data[csInd].session.delivery_no;
                            } else {
                                mergeConcatSession +=
                                    scheduleData.data[csInd].session.delivery_symbol +
                                    scheduleData.data[csInd].session.delivery_no +
                                    ',';
                            }
                        }
                    }
                });
            } else delivery_type = eleNS.session.delivery_symbol + eleNS.session.delivery_no;
            normalSchedules[indexNS].s_no = eleNS.session.s_no;
            if (mergeConcatSession != '')
                normalSchedules[indexNS].delivery_type = delivery_type + ',' + mergeConcatSession;
            else
                normalSchedules[indexNS].delivery_type =
                    delivery_type + ' ' + eleNS.session.session_topic;
        });

        if (userType === 'staff') {
            for (scheduleElement of normalSchedules) {
                const staffData = scheduleElement.staffs.find(
                    (ele) => ele._staff_id.toString() === userId,
                );
                scheduleElement.staffs = staffData;
            }
        } else {
            for (scheduleElement of normalSchedules) {
                const studentData = scheduleElement.students.find(
                    (ele) => ele && ele._id.toString() === userId,
                );
                scheduleElement.students = studentData;
            }
        }
        const sortedNormalSchedule = normalSchedules.sort(compare);

        //Merge session should not consider as 1, consider as many
        let session_completed_count = 0;
        let pending_count = 0;
        let present_count = 0;
        let absent_count = 0;
        let leave_count = 0;
        let onduty_count = 0;
        let permission_count = 0;
        let total_session = 0;
        //return res.send(normalSchedules);
        //Exclude schedules are removed for the students
        if (userType === 'student') {
            normalSchedules = normalSchedules.filter(
                (normalScheduleElement) =>
                    normalScheduleElement.students &&
                    normalScheduleElement.students.status !== EXCLUDE,
            );
        }
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount: rotation_count,
            lateExcludeManagement,
        });
        for (const eleNormalSchedules of normalSchedules) {
            const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: institutionCalendarId,
                courseId,
                programId,
                levelNo,
                term,
                rotationCount: rotation_count,
                lateExcludeManagement,
                studentId: eleNormalSchedules?.students?._id,
            }).lateExclude;
            let lateLabel = null;
            if (!lateExcludeForStudent && !lateExclude) {
                const { lateLabel: updatedLateLabel } = getLateLabelForSchedule({
                    lateDurationRange,
                    manualLateRange,
                    manualLateData,
                    schedule: eleNormalSchedules,
                    student_data: eleNormalSchedules?.students,
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo,
                    term,
                    rotationCount: rotation_count,
                    lateExcludeManagement,
                });
                lateLabel = updatedLateLabel;
            }
            eleNormalSchedules.lateLabel = lateLabel ? lateLabel.lateLabel : null;
            if (eleNormalSchedules.merge_status === true) {
                /* console.log({
                    schedule_id: eleNormalSchedules._id,
                    merge_status: eleNormalSchedules.merge_status,
                }); */
                // let merge_count = 1;
                // if (userType === 'student') {
                // for (mergeWithElement of eleNormalSchedules.merge_with) {
                //     const mergeScheduleElement = normalSchedules.find(
                //         (scheduleElement) =>
                //             scheduleElement._id.toString() ===
                //                 mergeWithElement.schedule_id.toString() &&
                //             scheduleElement.session._session_id.toString() !==
                //                 mergeWithElement.session_id.toString(),
                //     );
                //     if (mergeScheduleElement) merge_count++;
                // }
                // merge_count += eleNormalSchedules.merge_with.length;
                // }
                const merge_count = eleNormalSchedules.merge_with.length + 1;
                //Total Session
                total_session += merge_count;
                //Completed count
                if (eleNormalSchedules.status === COMPLETED) session_completed_count += merge_count;

                //Pending Count
                if (userType === 'staff' && eleNormalSchedules.staffs.status === PENDING)
                    pending_count += merge_count;
                if (userType === 'student' && eleNormalSchedules.students.status === PENDING)
                    pending_count += merge_count;

                //Present Count
                if (
                    userType === 'staff' &&
                    (eleNormalSchedules.staffs.status === PRESENT ||
                        eleNormalSchedules.staffs.status === ONDUTY) &&
                    eleNormalSchedules.status === COMPLETED
                )
                    present_count += merge_count;

                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === PRESENT /*  ||
                        eleNormalSchedules.students.status === ONDUTY */ &&
                    eleNormalSchedules.status === COMPLETED
                )
                    present_count += merge_count;

                // Onduty
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === ONDUTY &&
                    eleNormalSchedules.status === COMPLETED
                )
                    onduty_count += merge_count;
                //Absent Count
                if (userType === 'staff' && eleNormalSchedules.staffs.status === ABSENT)
                    absent_count += merge_count;
                if (userType === 'student' && eleNormalSchedules.students.status === ABSENT)
                    absent_count += merge_count;

                //Leave Count
                if (
                    userType === 'staff' &&
                    (eleNormalSchedules.staffs.status === LEAVE ||
                        eleNormalSchedules.staffs.status === PERMISSION) &&
                    eleNormalSchedules.status === COMPLETED
                )
                    leave_count += merge_count;
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === LEAVE /* ||
                        eleNormalSchedules.students.status === PERMISSION */ &&
                    eleNormalSchedules.status === COMPLETED
                )
                    leave_count += merge_count;
                // Permission
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === PERMISSION &&
                    eleNormalSchedules.status === COMPLETED
                )
                    permission_count += merge_count;
            } else {
                // console.log({
                //     schedule_id: eleNormalSchedules._id,
                //     merge_status: eleNormalSchedules.merge_status,
                // });
                total_session++;
                if (eleNormalSchedules.status === COMPLETED) session_completed_count++;

                //Pending Count
                if (userType === 'staff' && eleNormalSchedules.staffs.status === PENDING)
                    pending_count++;
                if (userType === 'student' && eleNormalSchedules.students.status === PENDING)
                    pending_count++;

                //Present Count
                if (
                    userType === 'staff' &&
                    (eleNormalSchedules.staffs.status === PRESENT ||
                        eleNormalSchedules.staffs.status === ONDUTY) &&
                    eleNormalSchedules.status === COMPLETED
                )
                    present_count++;

                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === PRESENT /* ||
                        eleNormalSchedules.students.status === ONDUTY */ &&
                    eleNormalSchedules.status === COMPLETED
                )
                    present_count++;
                // Onduty
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === ONDUTY &&
                    eleNormalSchedules.status === COMPLETED
                )
                    onduty_count++;
                //Absent Count
                if (userType === 'staff' && eleNormalSchedules.staffs.status === ABSENT)
                    absent_count++;
                if (userType === 'student' && eleNormalSchedules.students.status === ABSENT)
                    absent_count++;

                //Leave Count
                if (
                    userType === 'staff' &&
                    (eleNormalSchedules.staffs.status === LEAVE ||
                        eleNormalSchedules.staffs.status === PERMISSION) &&
                    eleNormalSchedules.status === COMPLETED
                )
                    leave_count++;
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === LEAVE /* ||
                        eleNormalSchedules.students.status === PERMISSION */ &&
                    eleNormalSchedules.status === COMPLETED
                )
                    leave_count++;
                // Permission
                if (
                    userType === 'student' &&
                    eleNormalSchedules.students.status === PERMISSION &&
                    eleNormalSchedules.status === COMPLETED
                )
                    permission_count++;
            }
        }
        const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount: rotation_count,
            lateExcludeManagement,
            studentId: normalSchedules[0]?.students?._id,
        }).lateExclude;
        let studentLateAbsent = 0;
        if (!lateExcludeForStudent && !lateExclude) {
            const { studentLateAbsent: updatedStudentLateAbsent } =
                getConfigAndStudentLateAbsentForSingleStudent({
                    lateDurationRange,
                    manualLateRange,
                    manualLateData,
                    sortedStudentSessions: normalSchedules,
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo,
                    term,
                    rotationCount: rotation_count,
                    lateExcludeManagement,
                });
            studentLateAbsent = updatedStudentLateAbsent;
        }
        const response = {
            user_details: staffData.data,
            course_details: courseResponse,
            schedule: sortedNormalSchedule,
            summary: {
                total_session,
                session_completed_count,
                pending_count,
                present_count,
                absent_count,
                // Need to Get from LMS
                leave_count,
                onduty_count,
                permission_count,
                studentLateAbsent,
            },
        };

        /* const response = {
            user_details: staffData.data,
            course_details: courseResponse,
            schedule: sortedNormalSchedule,
            summary: {
                total_session: normalSchedules.length,
                session_completed_count: normalSchedules.filter((ele) => ele.status === COMPLETED)
                    .length,
                pending_count: normalSchedules.filter((ele) =>
                    userType === 'staff'
                        ? ele.staffs.status === PENDING
                        : ele.students.status === PENDING,
                ).length,
                present_count: normalSchedules.filter((ele) =>
                    userType === 'staff'
                        ? (ele.staffs.status === PRESENT || ele.staffs.status === ONDUTY) &&
                          ele.status === COMPLETED
                        : (ele.students.status === PRESENT || ele.students.status === ONDUTY) &&
                          ele.status === COMPLETED,
                ).length,
                absent_count: normalSchedules.filter((ele) =>
                    userType === 'staff'
                        ? ele.staffs.status === ABSENT
                        : ele.students.status === ABSENT,
                ).length,
                // Need to Get from LMS
                leave_count: normalSchedules.filter((ele) =>
                    userType === 'staff'
                        ? (ele.staffs.status === LEAVE || ele.staffs.status === PERMISSION) &&
                          ele.status === COMPLETED
                        : (ele.students.status === LEAVE || ele.students.status === PERMISSION) &&
                          ele.status === COMPLETED,
                ).length,
            },
        }; */
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_ATTENDANCE_REPORT'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
