const FormModel = require('./form.model');
const StudentResponseModel = require('../student-response/student-response.model');
const UserModel = require('../../../lib/models/user');
const PortfolioRoleModel = require('../role/role.model');

const { PUBLISHED, NOT_STARTED, DRAFT } = require('../../common/utils/enums');
const {
    UpdateFailedError,
    NotFoundError,
    BadRequestError,
} = require('../../common/utils/api_error_util');
const { getAssignedUserSections, buildFormQuery } = require('./form.helper');
const { isIDEquals } = require('../../common/utils/common.util');
const { STUDENT, STAFF } = require('../../common/utils/constants');

const formProject = {
    title: 1,
    pages: 1,
    type: 1,
    publishedDate: 1,
    createdBy: 1,
    status: 1,
};

const updateForm = async ({ formId, title, type, pages, userId, status = DRAFT }) => {
    if (!formId) {
        const user = await UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean();

        const form = await FormModel.create({
            title,
            type,
            pages,
            status,
            isTemplate: true,
            createdBy: { id: userId, name: user?.name || {} },
        });

        return form._id;
    }

    await FormModel.updateOne(
        { _id: formId },
        {
            $set: {
                ...(title && { title }),
                ...(type && { type }),
                ...(pages && { pages }),
                status,
            },
        },
    );
};

const getForms = async ({ formId, isPages, type, userId, status }) => {
    const project = {
        title: 1,
        type: 1,
        publishedDate: 1,
        createdBy: 1,
        status: 1,
        pages: 1,
        updatedAt: 1,
    };

    if (formId) {
        const form = await FormModel.findOne(
            { _id: formId, isDeleted: false },
            isPages ? formProject : project,
        ).lean();

        return form || null;
    }

    const forms = await FormModel.find(
        {
            isDeleted: false,
            isTemplate: true,
            ...(type && { 'type.code': type }),
            ...(status && { status }),
            ...(userId && { 'createdBy.id': userId }),
        },
        project,
    ).lean();

    return forms || [];
};

const deleteForm = async ({ formId }) => {
    const form = await FormModel.updateOne({ _id: formId }, { $set: { isDeleted: true } });
    if (!form.modifiedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    return form;
};

const getFormsByUser = async ({ email, type, formId, isPages }) => {
    const forms = await FormModel.find(buildFormQuery({ email, formId }), {
        title: 1,
        type: 1,
        description: 1,
        ...(isPages && { pages: 1 }),
        status: 1,
        createdBy: 1,
        publishedDate: 1,
        marks: 1,
    }).lean();

    if (!type === STUDENT)
        return isPages ? forms.map((form) => getAssignedUserSections({ form, email })) : forms;

    const studentResponses = await StudentResponseModel.find(
        { 'student.email': email },
        {
            ...(isPages && { pages: 1 }),
            status: 1,
            formId: 1,
            totalMarks: 1,
            awardedMarks: 1,
            evaluationStatus: 1,
        },
    ).lean();

    const targetForms = isPages
        ? forms.map((form) => getAssignedUserSections({ form, email }))
        : forms;

    return targetForms.map((form) => {
        const response = studentResponses.find((response) => isIDEquals(response.formId, form._id));
        return {
            ...form,
            ...(response && response),
            status: response?.status || NOT_STARTED,
        };
    });
};

const validateForm = async ({ formId }) => {
    const form = await FormModel.findOne(
        { _id: formId, isDeleted: false },
        { pages: 1, status: 1 },
    );

    if (!form) return 'FORM_NOT_FOUND';

    if (form.status === PUBLISHED) return 'FORM_ALREADY_PUBLISHED';

    if (!form.pages?.length) return 'FORM_HAS_NO_ELEMENTS';

    const isEvaluatorExists = form.pages.every((page) => {
        return page.elements.every((element) => {
            const isEvaluateByRole = element.roles?.some((role) => role.evaluate);
            const isEvaluateByUser = element.users?.some((user) => user.evaluate);

            return isEvaluateByRole || isEvaluateByUser;
        });
    });

    if (!isEvaluatorExists) return 'PLEASE_ASSIGN_EVALUATOR_FOR_EVERY_SECTION';

    return null;
};

const publishForm = async ({ formId }) => {
    const result = await FormModel.updateOne(
        { _id: formId },
        { $set: { status: PUBLISHED, publishedDate: new Date() } },
    );
    if (!result.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }
};

const getAssignedRolesInForm = async ({ formId }) => {
    const form = await FormModel.findOne(
        { _id: formId, status: PUBLISHED },
        { 'pages.elements.roles': 1, title: 1, 'type.name': 1 },
    ).lean();
    if (!form) {
        throw new NotFoundError('FORM_NOT_FOUND');
    }

    const roles = form.pages.flatMap((page) => page.elements.flatMap((element) => element.roles));

    const filteredRoles = roles.filter((role) => role.evaluate);

    const assignedRoles = await PortfolioRoleModel.find(
        { _id: { $in: filteredRoles.map(({ roleId }) => roleId) }, type: STAFF },
        { name: 1, roleId: '$_id', _id: 0 },
    ).lean();

    return { roles: assignedRoles, name: form.title, type: form.type.name };
};

module.exports = {
    getForms,
    updateForm,
    deleteForm,
    getFormsByUser,
    publishForm,
    validateForm,
    getAssignedRolesInForm,
};
