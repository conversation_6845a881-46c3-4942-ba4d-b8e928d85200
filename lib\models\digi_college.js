let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let college = new Schema({
    university_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    university_name: String,
    college_name: String,
    logo: String,
    address_details: {
        address: String,
        country: String,
        state: String,
        district: String,
        city: String,
        zipcode: String,
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DIGI_COLLEGE, college);