const mongoose = require('mongoose');
const { Schema } = mongoose;

const { INSTITUTION } = require('../../../utility/constants');

const actionSchema = {
    name: {
        type: String,
        required: true,
        trim: true,
    },
};

const pageSchema = {
    // Faculty list page
    name: {
        type: String,
        required: true,
        trim: true,
    },
    url: {
        type: String,
    }, // /student or /faculty (particular page)
    actions: [actionSchema],
    tabs: [
        {
            // All students, validated, pending validation
            name: {
                type: String,
                required: true,
                trim: true,
            },
            url: {
                type: String,
            }, // /student or /faculty (particular page)
            actions: [actionSchema],
            subTabs: [
                {
                    // verification pending, profile verification, biometric verification
                    name: {
                        type: String,
                        required: true,
                        trim: true,
                    },
                    url: {
                        type: String,
                    }, // /student or /faculty (particular page)
                    actions: [actionSchema],
                },
            ],
        },
    ],
};

const moduleSchema = new Schema({
    // User management
    // _institution_id: {
    //     type: Schema.Types.ObjectId,
    //     ref: INSTITUTION,
    // },
    // _parent_id: {
    //     type: Schema.Types.ObjectId,
    // },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    url: {
        type: String,
    }, // /user (includes both student and faculty)
    pages: [pageSchema],
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true,
    },
});

module.exports = {
    moduleSchema,
};
