const express = require("express");
const route = express.Router();
const infrastructure_event_controller = require('./infrastructure_event_controller');
const validator = require('./infrastructure_event_validator')

route.post("/", validator.commonEventCreate ,infrastructure_event_controller.create);
route.post("/maintenance", validator.maintenanceEventCreate, infrastructure_event_controller.createEventForMaintenance);
route.get("/", validator.getAll ,infrastructure_event_controller.read);
route.get("/participants", infrastructure_event_controller.readEventsOfPaticipants);
route.get("/:_id", infrastructure_event_controller.read);
route.get("/infrastuctures", infrastructure_event_controller.readEventsOfInfrastuctures);
route.post('/check-available-timings',validator.checkAvailabilityTimings,infrastructure_event_controller.checkAvailableTimings);
route.patch("/approve/:_id", infrastructure_event_controller.approveEventsOfInfrastuctures);
route.patch("/cancel/:_id", infrastructure_event_controller.approveEventsOfInfrastuctures);
route.put("/:_id", validator.update, infrastructure_event_controller.update);
route.patch("/:_id", infrastructure_event_controller.softDelete);
route.patch("/recovery/:_id", infrastructure_event_controller.recovery);
route.delete("/hard-delete/:_id", infrastructure_event_controller.delete);

module.exports = route;