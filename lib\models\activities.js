const mongoose = require('mongoose');
const Schema = mongoose.Schema;
// constants
const {
    ACTIVITIES,
    USER,
    DIGI_COURSE,
    NOT_STARTED,
    STARTED,
    COMPLETED,
    DRAFT,
    PUBLISHED,
    QUESTION,
    SCHEDULE,
    COURSE_SCHEDULE,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
} = require('../utility/constants');

// enums
const {
    TIME,
    ONE_BY_ONE,
    QUIZ,
    POLL,
    SURVEY,
    OLD,
    NEW,
    MANUAL,
    SYSTEM,
} = require('../utility/enums');
const activitiesSchema = new Schema(
    {
        scheduleIds: [
            {
                type: Schema.Types.ObjectId,
                ref: COURSE_SCHEDULE,
            },
        ],
        sessionId: {
            type: Schema.Types.ObjectId,
        },
        courseId: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        sessionFlowIds: [
            {
                _id: {
                    type: Schema.Types.ObjectId,
                },
                type: {
                    type: String,
                },
            },
        ],
        name: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            enum: [SCHEDULE],
        },
        quizType: {
            type: String,
            required: true,
            enum: [QUIZ, POLL, SURVEY],
        },
        status: {
            type: String,
            enum: [DRAFT, COMPLETED, STARTED, NOT_STARTED, PUBLISHED],
            default: DRAFT,
        },
        startTime: {
            type: Date,
        },
        endTime: {
            type: Date,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },

        quizStartedBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        quizStopBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        socketEventStaffId: {
            type: String,
        },
        socketEventStudentId: {
            type: String,
        },
        staffStartWithExam: {
            type: String,
            enum: [TIME, ONE_BY_ONE],
        },
        setQuizTime: {
            type: Number,
        },
        students: [
            {
                _studentId: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                questions: [
                    {
                        _questionId: {
                            type: Schema.Types.ObjectId,
                            ref: QUESTION,
                        },
                        _optionId: {
                            type: Schema.Types.ObjectId,
                        },
                        answer: {
                            type: Boolean,
                        },
                        textAnswer: {
                            type: String,
                        },
                        _optionIdArray: {
                            type: [Schema.Types.ObjectId],
                            default: [],
                        },
                        matchingOptions: {
                            type: [String],
                            default: [],
                        },
                        marks: {
                            type: Number,
                        },
                        staffCorrectedAnswer: {
                            type: String,
                        },
                    },
                ],
                status: {
                    type: String,
                },
            },
        ],
        studentCompletedQuiz: [],
        setting: {},
        questions: [
            {
                _id: {
                    type: Schema.Types.ObjectId,
                    ref: QUESTION,
                },
                type: {
                    type: String,
                    enum: [OLD, NEW],
                },
                order: {
                    type: Number,
                },
                questionMoved: {
                    type: Boolean,
                },
                acceptQuestionResponse: {
                    type: Boolean,
                    default: true,
                },
            },
        ],
        schedule: {
            startDateAndTime: {
                type: Date,
            },
            endDateAndTime: {
                type: Date,
            },
        },
        courseAdmin: {
            type: Boolean,
            default: false,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
        },
        year_no: {
            type: String,
        },
        level_no: {
            type: String,
        },
        term: {
            type: String,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        seconds: {
            type: Number,
            default: 0,
        },
        correctionType: {
            type: String,
            enum: [MANUAL, SYSTEM],
        },
        shuffleQuestionOrder: {
            type: Boolean,
            default: false,
        },
        description: {
            type: String,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ACTIVITIES, activitiesSchema);
