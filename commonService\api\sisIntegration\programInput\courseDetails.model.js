const mongoose = require('mongoose');
const { COURSE_DETAILS, DIGI_COURSE, INSTITUTION_CALENDAR } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const termCalendarSchemas = new Schema(
    {
        isActive: {
            type: Boolean,
            default: true,
        },
        courseId: { type: String },
        courseOfferNo: { type: String },
        courseTerm: { type: String },
        sessionNo: { type: String },
        section: { type: String },
        collegeCode: { type: String },
        academic: { type: String },
        subject: { type: String },
        catalogNo: { type: String },
        courseCode: { type: String },
        academicPlan: { type: String },
        deliveryType: { type: String },
        associatedCode: { type: String },
        academicOrg: { type: String },
        campus: { type: String },
        location: { type: String },
        startDate: { type: String },
        endDate: { type: String },
        courseName: { type: String },
        courseDescription: { type: String },
        courseDescriptionLong: { type: String },
        _course_id: { type: ObjectId, ref: DIGI_COURSE },
        institutionCalendarIds: [{ type: ObjectId, ref: INSTITUTION_CALENDAR }],
    },
    { timestamps: true },
);

module.exports = mongoose.model(COURSE_DETAILS, termCalendarSchemas);
