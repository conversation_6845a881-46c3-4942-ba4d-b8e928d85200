let institution_formate = require('../institution/institution_formate');
let common_fun = require('../utility/common_functions');

module.exports = {
    institution: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: common_fun.check_value_or_null(element, '_id') || '',
                calendar_name: common_fun.check_value_or_null(element, 'calendar_name') || '',
                batch: common_fun.check_value_or_null(element, 'batch') || '',
                start_date: common_fun.check_value_or_null(element, 'start_date') || '',
                end_date: common_fun.check_value_or_null(element, 'end_date') || '',
                _creater_id: common_fun.check_value_or_null(element, '_creater_id') || '',
                _institution_id: common_fun.check_value_or_null(element, '_institution_id') || '',
                status: common_fun.check_value_or_null(element, 'status') || '',
                institution: institution_formate.institution_ID_Only(element.institution),
                isDeleted: common_fun.check_value_or_null(element, 'isDeleted') || '',
                isActive: common_fun.check_value_or_null(element, 'isActive') || ''
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    institution_ID: (doc) => {
        let obj = {
            _id: doc._id,
            batch: doc.batch,
            institution: institution_formate.institution_ID_Only(doc.institution),
            start_date: doc.start_date,
            end_date: doc.end_date,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    institution_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            batch: doc.batch,
            institution: doc._institution_id,
            start_date: doc.start_date,
            end_date: doc.end_date,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    institution_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                batch: element.batch,
                institution: element._institution_id,
                start_date: element.start_date,
                end_date: element.end_date,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}