let mongoose = require('mongoose');
let Schemas = mongoose.Schema;
let constant = require('../../utility/constants');

const resetTokenSchema = new Schemas(
    {
        _userId: {
            type: Schema.Types.ObjectId,
            required: true,
        },
        type: {
            type: String,
            enum: [constant.STUDENTS, constant.FACULTY],
            required: true,
        },
        token: {
            type: String,
            required: true,
        },
        createdAt: {
            type: Date,
            required: true,
            default: Date.now,
            expires: 86400,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(constant.RESET_TOKEN, resetTokenSchema);
