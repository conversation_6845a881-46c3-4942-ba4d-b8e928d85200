const mongoose = require('mongoose');
const { INSTITUTION, GLOBAL_SESSION_TARDIS } = require('../utility/constants');
const ObjectId = mongoose.Schema.Types.ObjectId;

const tardisSchema = new mongoose.Schema(
    {
        _institution_id: {
            type: ObjectId,
            required: true,
            ref: INSTITUTION,
        },
        name: {
            type: String,
            required: true,
        },
        short_code: {
            type: String,
            required: true,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timeStamps: true,
    },
);

const tardisModel = mongoose.model(GLOBAL_SESSION_TARDIS, tardisSchema);

module.exports = tardisModel;
