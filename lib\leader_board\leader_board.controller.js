const leaderBoardSchema = require('./leader_board.model');
const { convertToMongoObjectId } = require('../utility/common');
const institutionCalendarSchema = require('../models/institution_calendar');
const courseScheduledSchema = require('../models/course_schedule');
const {
    COMPLETED,
    LEADER_BOARD,
    SCHEDULE_TYPES: { REGULAR },
} = require('../utility/constants');
const {
    getLeaderBoard,
    leaderBoardCatchData,
    leaderBoardCatchFilter,
} = require('./leader_board.service');
const { getPaginationValues } = require('../../commonService/utility/pagination');
const { redisClient } = require('../../config/redis-connection');

exports.createParameter = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { parameters } = body;
        const leaderBoardCount = await leaderBoardSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        if (!leaderBoardCount) {
            const leaderBoardData = await leaderBoardSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
            });
        }
        const updateLeaderBoard = await leaderBoardSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $push: {
                    ...(parameters && { 'leaderBoard.parameters': parameters }),
                },
            },
        );
        if (updateLeaderBoard.nModified) {
            return { statusCode: 200, message: 'Created_Successfully' };
        }
        return { statusCode: 200, message: 'Not_Created' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getParameters = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { boardType } = query;
        const defaultParameters = [
            {
                name: 'Punctuality',
                weight: 50,
                criteria: [
                    {
                        supportSession: { regular: false, activity: false },
                        criteriaName: 'staff_attendance',
                        sessionStarted: { noOfStudent: '1', minsOfStart: '10' },
                        bufferTime: { isActive: true, noOfBuffer: '15' },
                        isActive: true,
                        score: { isActive: true, noOfScore: '5' },
                        allowance: { isActive: true, noOfAllowance: '25' },
                        noOfEngager: true,
                        consideration: { isActive: true, noOfConsideration: '75' },
                        noOfSession: false,
                    },
                ],
            },
            {
                name: 'Engagement',
                weight: 50,
                criteria: [
                    {
                        studentParticipated: '1',
                        supportSession: { regular: false, activity: false },
                        criteriaName: 'engager',
                        bufferTime: { isActive: false },
                        isActive: true,
                        score: { isActive: true, noOfScore: '5' },
                        allowance: { isActive: true, noOfAllowance: '25' },
                        noOfEngager: false,
                        consideration: { isActive: true, noOfConsideration: '75' },
                        noOfSession: true,
                    },
                ],
            },
        ];
        if (boardType === 'leader') {
            const updateLeaderBoard = await leaderBoardSchema.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    $set: {
                        'leaderBoard.parameters': defaultParameters,
                    },
                },
            );
        }
        let leaderBoardData = await leaderBoardSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    leaderBoard: 1,
                    anomalyBoard: 1,
                },
            )
            .lean();
        leaderBoardData = leaderBoardData && leaderBoardData[boardType + 'Board'];
        if (!leaderBoardData) return { statusCode: 200, message: 'No_Data' };
        return { statusCode: 200, message: 'List_Data', data: leaderBoardData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.editParameters = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { parameterId, name, weight, criteria } = body;
        const leaderBoardData = await leaderBoardSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                ...(parameterId && {
                    'leaderBoard.parameters._id': convertToMongoObjectId(parameterId),
                }),
            },
            {
                $set: {
                    ...(name && { 'leaderBoard.parameters.$.name': name }),
                    ...(weight && { 'leaderBoard.parameters.$.weight': weight }),
                    ...(criteria && {
                        'leaderBoard.parameters.$.criteria': criteria,
                    }),
                },
            },
        );
        if (!leaderBoardData.nModified) return { statusCode: 200, message: 'Not_Updated' };
        return { statusCode: 200, message: 'Updated_Successfully' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.saveLeaderBoard = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { leaderBoard, anomalyBoard } = body;
        const leaderBoardCount = await leaderBoardSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        if (!leaderBoardCount) {
            const leaderBoardData = await leaderBoardSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
            });
        }
        const updateLeaderBoard = await leaderBoardSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: {
                    ...(leaderBoard && { leaderBoard }),
                    ...(anomalyBoard && { anomalyBoard }),
                },
            },
        );
        //delete leader data in redis
        const leaderBoardKey = `${LEADER_BOARD}*`;
        const leaderBoardData = await redisClient.Client.keys(leaderBoardKey);
        if (leaderBoardData.length) await redisClient.Client.del(leaderBoardData);

        if (updateLeaderBoard.nModified) return { statusCode: 200, message: 'Saved successfully' };
        return { statusCode: 200, message: 'Not saved successfully' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteParameters = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { parameterId } = query;
        const leaderBoardData = await leaderBoardSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $pull: { 'leaderBoard.parameters': { _id: convertToMongoObjectId(parameterId) } },
            },
        );
        if (leaderBoardData.nModified) return { statusCode: 200, message: 'Deleted successfully' };
        return { statusCode: 200, message: 'Not Deleted successfully' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.leaderDashboard = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _institution_calendar_id,
            board,
            from,
            to,
            state,
            performanceProgramIds,
            userIds,
            gender,
            employeeType,
            ranks,
            programIds,
            currentUserId,
        } = query;
        let getLeaderBoardData;
        const courseScheduleData = await courseScheduledSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    status: COMPLETED,
                    isActive: true,
                    isDeleted: false,
                    // $or: [
                    //     { outsideCampus: { $exists: true, $eq: false } },
                    //     { outsideCampus: { $exists: false } },
                    // ],
                    schedule_date: {
                        $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                        $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                    },
                },
                {
                    'staffs.status': 1,
                    'staffs._staff_id': 1,
                    'staffs.time': 1,
                    'staffs.mode': 1,
                    scheduleStartDateAndTime: 1,
                    'sessionDetail.start_time': 1,
                    'sessionDetail.startBy': 1,
                    'students.primaryTime': 1,
                    'students._id': 1,
                    _program_id: 1,
                    type: 1,
                    _course_id: 1,
                    year_no: 1,
                    term: 1,
                    level_no: 1,
                    rotation: 1,
                    rotation_count: 1,
                },
            )
            .lean();
        // return { statusCode: 200, message: 'No', data: courseScheduleData };
        if (!courseScheduleData.length)
            return { statusCode: 200, message: 'No_Data', data: { lastUpdated: new Date() } };

        if (state === 'regular') {
            getLeaderBoardData = await leaderBoardCatchData({
                scheduledData: courseScheduleData,
                boardType: board,
                _institution_id,
                _institution_calendar_id,
                from,
                to,
                currentUserId,
                programIds: programIds && programIds.length ? programIds : [],
            });
        }
        if (state === 'fresh') {
            getLeaderBoardData = await getLeaderBoard({
                scheduledData: courseScheduleData,
                boardType: board,
                _institution_id,
                _institution_calendar_id,
                programIds: programIds && programIds.length ? programIds : [],
                from,
                to,
                currentUserId,
            });
        }
        if (
            currentUserId &&
            getLeaderBoardData &&
            getLeaderBoardData.leaderData &&
            !getLeaderBoardData.leaderData.performanceData.length
        ) {
            return { statusCode: 200, message: 'No_Data', data: { lastUpdated: new Date() } };
        }
        //filter data
        let filterData;
        if (
            (gender && gender.length) ||
            (employeeType && employeeType.length) ||
            (userIds && userIds.length) ||
            (performanceProgramIds && performanceProgramIds.length)
        ) {
            filterData = await leaderBoardCatchFilter({
                scheduledData: courseScheduleData,
                boardType: board,
                _institution_id,
                _institution_calendar_id,
                programIds: programIds && programIds.length ? programIds : [],
                from,
                to,
                currentUserId,
                state: 'filter',
            });
        }
        let genderFilter = [];
        let employeeTypeFilter = [];
        if (gender && gender.length) {
            genderFilter = filterData.userData
                .filter((userElement) => gender.includes(userElement.gender))
                .map((userElement) => userElement._id);
        }
        if (employeeType && employeeType.length) {
            employeeTypeFilter = filterData.userData
                .filter((userElement) =>
                    employeeType.includes(userElement.employment.user_employment_type),
                )
                .map((userElement) => userElement._id);
        }
        if (
            (genderFilter && genderFilter.length) ||
            (employeeTypeFilter && employeeTypeFilter.length) ||
            (gender && gender.length) ||
            (employeeType && employeeType.length) ||
            (userIds && userIds.length) ||
            (ranks && ranks.length) ||
            (performanceProgramIds && performanceProgramIds.length)
        ) {
            getLeaderBoardData.leaderData.performanceData =
                getLeaderBoardData.leaderData.performanceData.filter((performanceElement) => {
                    const programMatch =
                        !performanceProgramIds ||
                        performanceProgramIds.length === 0 ||
                        performanceProgramIds.includes(performanceElement._program_id.toString());
                    const userMatch =
                        !userIds ||
                        userIds.length === 0 ||
                        userIds.includes(performanceElement._staff_id.toString());
                    const genderMatch =
                        !genderFilter ||
                        genderFilter.length === 0 ||
                        genderFilter.toString().includes(performanceElement._staff_id.toString());
                    const typeMatch =
                        !employeeTypeFilter ||
                        employeeTypeFilter.length === 0 ||
                        employeeTypeFilter
                            .toString()
                            .includes(performanceElement._staff_id.toString());
                    const rankMatch =
                        !ranks ||
                        ranks.length === 0 ||
                        ranks.toString().includes(performanceElement.rank.toString());
                    return programMatch && userMatch && genderMatch && typeMatch && rankMatch;
                });
        }
        const pagination = getPaginationValues(query);
        if (
            getLeaderBoardData &&
            getLeaderBoardData.leaderData &&
            getLeaderBoardData.leaderData.performanceData &&
            getLeaderBoardData.leaderData.performanceData.length
        ) {
            getLeaderBoardData.leaderData.performanceData =
                getLeaderBoardData.leaderData.performanceData.map((boardElement) => ({
                    ...boardElement,
                    staffScore: boardElement.staffScore / boardElement.sessions.length,
                    weight: boardElement.weight / boardElement.sessions.length,
                    sessions: boardElement.sessions.map((sessionElement) => ({
                        criteriaName: sessionElement.criteriaName,
                        staffScore: sessionElement.staffScore,
                        parameterName: sessionElement.parameterName,
                        sessionWeight: sessionElement.sessionWeight,
                        criteriaScore: sessionElement.criteriaScore,
                        sessionElementWeight: sessionElement.sessionElementWeight,
                        parameterWeight: sessionElement.parameterWeight,
                    })),
                }));
        }
        return {
            statusCode: 200,
            message: 'ListData',
            data: {
                totalCount: getLeaderBoardData.leaderData.performanceData.length,
                getLeaderBoardData: getLeaderBoardData.leaderData.performanceData.slice(
                    pagination.skip,
                    pagination.skip + pagination.limit,
                ),
                criteriaAverageScore: getLeaderBoardData.leaderData.criteriaAverageScore,
                overAllProgramScore: getLeaderBoardData.leaderData.overAllProgramScore,
                programAverageData: getLeaderBoardData.leaderData.programAverageData,
                totalScore: getLeaderBoardData.leaderData.totalScore,
                totalWeight: getLeaderBoardData.leaderData.totalWeight,
                badges: getLeaderBoardData.leaderData.badges,
                badgeStyle: getLeaderBoardData.leaderData.badgeStyle,
                weightTooltip: getLeaderBoardData.leaderData.weightTooltip,
                lastUpdated: getLeaderBoardData.leaderData.lastUpdated,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.leaderDashboardFilter = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _institution_calendar_id, board, from, to, state, programIds, currentUserId } =
            query;
        const courseScheduleData = await courseScheduledSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    status: COMPLETED,
                    isActive: true,
                    isDeleted: false,
                    // $or: [
                    //     { outsideCampus: { $exists: true, $eq: false } },
                    //     { outsideCampus: { $exists: false } },
                    // ],
                    schedule_date: {
                        $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                        $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                    },
                },
                {
                    'staffs.status': 1,
                    'staffs._staff_id': 1,
                    'staffs.time': 1,
                    'staffs.mode': 1,
                    scheduleStartDateAndTime: 1,
                    'sessionDetail.start_time': 1,
                    'sessionDetail.startBy': 1,
                    'students.primaryTime': 1,
                    'students._id': 1,
                    _program_id: 1,
                    program_name: 1,
                    type: 1,
                    _course_id: 1,
                    year_no: 1,
                    term: 1,
                    level_no: 1,
                    rotation: 1,
                    rotation_count: 1,
                },
            )
            .lean();
        const filterData = await leaderBoardCatchFilter({
            scheduledData: courseScheduleData,
            boardType: board,
            _institution_id,
            _institution_calendar_id,
            state,
            from,
            to,
            currentUserId,
            programIds: programIds && programIds.length ? programIds : [],
        });
        if (!filterData) return { statusCode: 200, message: 'No_data' };
        return { statusCode: 200, message: 'List_Data', data: filterData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleUserDashboard = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_calendar_id, userId, programId, from, to, board } = query;
        const { _institution_id } = headers;
        const courseScheduleData = await courseScheduledSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'staffs._staff_id': convertToMongoObjectId(userId),
                    _program_id: convertToMongoObjectId(programId),
                    isActive: true,
                    isDeleted: false,
                    schedule_date: {
                        $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                        $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                    },
                    status: COMPLETED,
                },
                {
                    'staffs.status': 1,
                    'staffs._staff_id': 1,
                    'staffs.time': 1,
                    'staffs.mode': 1,
                    scheduleStartDateAndTime: 1,
                    'sessionDetail.start_time': 1,
                    'sessionDetail.startBy': 1,
                    'students.primaryTime': 1,
                    'students._id': 1,
                    _program_id: 1,
                    type: 1,
                    _course_id: 1,
                    year_no: 1,
                    term: 1,
                    level_no: 1,
                    rotation: 1,
                    rotation_count: 1,
                },
            )
            .lean();
        const getLeaderBoardData = await getLeaderBoard({
            scheduledData: courseScheduleData,
            boardType: board,
            _institution_id,
            _institution_calendar_id,
            programIds: [],
            from,
            to,
            userId,
        });
        let sessionData = [];
        sessionData = getLeaderBoardData.leaderData.performanceData[0].sessions;
        const userScheduledData = await courseScheduledSchema.find(
            {
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'staffs._staff_id': convertToMongoObjectId(userId),
                _program_id: convertToMongoObjectId(programId),
                isActive: true,
                isDeleted: false,
                schedule_date: {
                    $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                    $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                },
            },
            {
                year_no: 1,
                term: 1,
                level_no: 1,
                rotation: 1,
                rotation_count: 1,
                _course_id: 1,
                course_name: 1,
                course_code: 1,
                status: 1,
            },
        );
        const performanceData = [];
        if (sessionData.length && userScheduledData && userScheduledData.length) {
            const parameterCheck = new Set();
            const weightData = [];
            for (const sessionElement of sessionData) {
                matchingScheduledCount = userScheduledData.filter(
                    (scheduledElement) =>
                        (scheduledElement.year_no &&
                            scheduledElement.year_no.toLowerCase() ===
                                sessionElement.year_no.toLowerCase() &&
                            scheduledElement.term &&
                            scheduledElement.term.toLowerCase() ===
                                sessionElement.term.toLowerCase() &&
                            scheduledElement.level_no &&
                            scheduledElement.level_no.toLowerCase() ===
                                sessionElement.level_no.toLowerCase() &&
                            scheduledElement._course_id &&
                            scheduledElement._course_id.toString() ===
                                sessionElement._course_id.toString() &&
                            scheduledElement.rotation &&
                            scheduledElement.rotation.toLowerCase() ===
                                sessionElement.rotation.toLowerCase()) ||
                        (scheduledElement.rotation_count &&
                            scheduledElement.rotation_count.toLowerCase() ===
                                sessionElement.rotation_count.toLowerCase()),
                );
                const parameterKey = `${sessionElement._course_id}+${sessionElement.year_no}+${sessionElement.term}+${sessionElement.level_no}+${sessionElement.rotation}+${sessionElement.rotation_count}+${sessionElement.parameterName}`;
                if (!parameterCheck.has(parameterKey)) {
                    weightData.push({
                        parameterName: sessionElement.parameterName,
                        parameterWeight: sessionElement.parameterWeight,
                        singleWeight: sessionElement.sessionWeight,
                    });
                    parameterCheck.add(parameterKey);
                }
                if (matchingScheduledCount && matchingScheduledCount.length) {
                    performanceData.push({
                        _course_id: sessionElement._course_id,
                        course_code: sessionElement.course_code,
                        course_name: sessionElement.course_name,
                        year_no: sessionElement.year_no,
                        term: sessionElement.term,
                        level_no: sessionElement.level_no,
                        rotation: sessionElement.rotation,
                        rotation_count: sessionElement.rotation_count,
                        scheduledCount: matchingScheduledCount.length,
                        completedScheduled: sessionElement.totalScheduled,
                    });
                }
            }
        }
        if (!courseScheduleData) return { statusCode: 200, message: 'No_data' };
        return { statusCode: 200, message: 'List_Data', data: performanceData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
