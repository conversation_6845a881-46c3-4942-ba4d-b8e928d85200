const digiSurveyResponseSchema = require('./digiSurveyResponse.model');
const surveySchemas = require('../../models/digiSurvey');
const { convertToMongoObjectId } = require('../../utility/common');

exports.saveSurveyResponse = async ({ headers = {}, body = {} }) => {
    try {
        const { institutionCalendarId, surveyId, userId, userType, answers, gender } = body;
        const surveyResponseBulkWrite = answers.map(
            ({ name = '', sectionNo = '', value = '', questionType = '', questionId = '' }) => {
                return {
                    updateOne: {
                        filter: {
                            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                            surveyId: convertToMongoObjectId(surveyId),
                            userId: convertToMongoObjectId(userId),
                            userType,
                            sectionNo,
                            gender,
                            questionType,
                            questionNo: name,
                            questionId,
                        },
                        update: {
                            $set: {
                                answer: value,
                            },
                        },
                        upsert: true,
                    },
                };
            },
        );
        await digiSurveyResponseSchema.bulkWrite(surveyResponseBulkWrite);
        return { statusCode: 201, message: 'SURVEY_RESPONSE_SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSurveyQuestion = async ({ query = {} }) => {
    try {
        const { surveyId, userId } = query;
        const surveyQuestions = await surveySchemas.findOne(
            {
                _id: convertToMongoObjectId(surveyId),
            },
            { questions: 1 },
        );
        const existingSurveyResponse = await digiSurveyResponseSchema.find(
            {
                surveyId: convertToMongoObjectId(surveyId),
                userId: convertToMongoObjectId(userId),
            },
            { sectionNo: 1, questionNo: 1, answer: 1 },
        );
        return {
            statusCode: 200,
            message: 'SURVEY_QUESTIONS',
            data: {
                surveyQuestions: surveyQuestions || {},
                existingSurveyResponse,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.checkIsSurveyClosed = async ({ query = {} }) => {
    try {
        const { surveyId } = query;
        const surveyStatus = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                { status: 1, _id: 0, 'closedSurveyReason.reason': 1 },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'SURVEY_STATUS',
            data: surveyStatus,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
