// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.program_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.assign_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    course_assigned_details: Joi.object()
                        .keys({
                            _program_id: Joi.string().alphanum().length(24),
                            program_name: Joi.string().min(1).max(50),
                            _curriculum_id: Joi.string().alphanum().length(24),
                            curriculum_name: Joi.string().min(1).max(50),
                            _year_id: Joi.string().alphanum().length(24),
                            year: Joi.string().min(1).max(50),
                            _level_id: Joi.string().alphanum().length(24),
                            level_no: Joi.string().min(1).max(50),
                            course_duration: Joi.object().keys({
                                start_week: Joi.number(),
                                end_week: Joi.number(),
                                total: Joi.number(),
                            }),
                            course_shared_with: Joi.array().items(
                                Joi.object({
                                    _program_id: Joi.string().alphanum().length(24),
                                    program_name: Joi.string().min(1).max(50),
                                    _curriculum_id: Joi.string().alphanum().length(24),
                                    curriculum_name: Joi.string().min(1).max(50),
                                    _year_id: Joi.string().alphanum().length(24),
                                    year: Joi.string().min(1).max(50),
                                    _level_id: Joi.string().alphanum().length(24),
                                    level_no: Joi.string().min(1).max(50),
                                }),
                            ),
                        })
                        .unknown(true),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.assign_course_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _program_id: Joi.string().alphanum().length(24),
                    program_name: Joi.string().min(1).max(50),
                    _curriculum_id: Joi.string().alphanum().length(24),
                    curriculum_name: Joi.string().min(1).max(50),
                    _year_id: Joi.string().alphanum().length(24),
                    year: Joi.string().min(1).max(50),
                    _level_id: Joi.string().alphanum().length(24),
                    level_no: Joi.string().min(1).max(50),
                    course_duration: Joi.object().keys({
                        start_week: Joi.number(),
                        end_week: Joi.number(),
                        total: Joi.number(),
                    }),
                    course_shared_with: Joi.array().items(
                        Joi.object({
                            _program_id: Joi.string().alphanum().length(24),
                            program_name: Joi.string().min(1).max(50),
                            _curriculum_id: Joi.string().alphanum().length(24),
                            curriculum_name: Joi.string().min(1).max(50),
                            _year_id: Joi.string().alphanum().length(24),
                            year: Joi.string().min(1).max(50),
                            _level_id: Joi.string().alphanum().length(24),
                            level_no: Joi.string().min(1).max(50),
                        }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.course_import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    course: Joi.array().items(
                        Joi.object({
                            Program_Name: Joi.string().min(2).max(250).required(),
                            Program_Code: Joi.string().min(2).max(50).required(),
                            Curriculum_Name: Joi.string().min(2).max(250).required(),
                            Year: Joi.string().min(2).max(250).required(),
                            Level: Joi.string().min(1).max(250).required(),
                            Course_Type: Joi.string()
                                .valid(
                                    constant.COURSE_TYPE.STANDARD,
                                    constant.COURSE_TYPE.SELECTIVE,
                                )
                                .required(),
                            Course_Recurring_at: Joi.string().min(1).max(250).required(),
                            Course_Code: Joi.string().min(2).max(250).required(),
                            Course_Name: Joi.string().min(2).max(250).required(),
                            Duration_in_weeks: Joi.string().min(2).max(250).required(),
                            Delivering_Subjects: Joi.string().min(2).max(250).required(),
                            Administrating_Subject_Dept_Prog: Joi.string()
                                .min(2)
                                .max(250)
                                .required(),
                            Theory_Credit_Hours: Joi.number().required(),
                            Theory_Contact_hours_per_credit_hour: Joi.number().required(),
                            Theory_duration_in_minutes: Joi.number().required(),
                            Practical_Credit_Hours: Joi.number().required(),
                            Practical_Contact_hours_per_credit_hour: Joi.number().required(),
                            Practical_duration_in_minutes: Joi.number().required(),
                            Clinical_Credit_Hours: Joi.number().required(),
                            Clinical_Contact_hours_per_credit_hour: Joi.number().required(),
                            Clinical_duration_in_minutes: Joi.number().required(),
                            Allow_to_edit_credit_hours_while_scheduling: Joi.bool().required(),
                            Should_Achieve_target_credit_hours: Joi.bool().required(),
                        }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
