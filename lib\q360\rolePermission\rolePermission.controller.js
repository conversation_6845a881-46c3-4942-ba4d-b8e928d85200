const { convertToMongoObjectId } = require('../../utility/common');
const qapcRoleSchema = require('./qapcRole.model');
const formSettingSchema = require('../categoryForm/formSetting.model');
const formCourseGroupSchema = require('../categoryForm/formCourseGroups.module');
const qapcSubModuleSchema = require('./subModule.model');
const formSettingCourseSchema = require('../categoryForm/formSettingCourses.model');
const {
    QAPC_GROUPED_BY_CATEGORY,
    EVENT_WHOM: { STAFF },
    COMPLETED,
} = require('../../utility/constants');
const institutionCalenderSchema = require('../../models/institution_calendar');
const userSchemas = require('../../models/user');
const qapcActionSchema = require('./action.model');
const {
    updateCFPCPermission,
    updatePCCFPermission,
    getPermissionData,
    pullCFPCPermission,
    getGroupedCategoryForms,
    getGroupedProgramForms,
} = require('./rolePermission.service');
const qapcPermissionSchema = require('./qapcPermission.model');

exports.createNewRole = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { roleName } = body;
        const createNewRole = await qapcRoleSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            roleName,
        });
        if (!createNewRole) return { statusCode: 400, message: 'ROLE_NAME_NOT_CREATED', data: {} };
        return {
            statusCode: 200,
            message: 'CREATED_NAME_SUCCESSFULLY',
            data: { roleName: createNewRole.roleName, _id: createNewRole._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qapcRoleList = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const qapcRoleList = await qapcRoleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    roleName: 1,
                    levels: 1,
                    hierarchy: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        if (!qapcRoleList.length)
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: qapcRoleList };
        return { start: 200, message: 'DATA_RETRIEVED', data: qapcRoleList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateRoleName = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { roleName, qapcRoleId } = body;
        const updateRoleName = await qapcRoleSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(qapcRoleId),
            },
            {
                $set: {
                    roleName,
                },
            },
        );
        if (!updateRoleName.modifiedCount) {
            return { statusCode: 400, message: 'UPDATE_ERROR' };
        }
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getLevelCategory = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { hierarchy, level } = query;
        if (hierarchy === QAPC_GROUPED_BY_CATEGORY) {
            return getGroupedCategoryForms({ _institution_id, level });
        }
        return getGroupedProgramForms({ _institution_id, level });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getFormCourseAndGroup = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormId } = query;
        const categoryFormCourseData = await formSettingCourseSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    isConfigure: true,
                    isEnable: true,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    categoryId: 1,
                    categoryFormId: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                },
            )
            .lean();
        const categoryGroupData = await formCourseGroupSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    categoryFormCourseId: 1,
                    term: 1,
                    attemptTypeName: 1,
                    academicYear: 1,
                    group: 1,
                    groupName: 1,
                },
            )
            .lean();
        //sort update or created list
        if (!categoryFormCourseData.length) {
            return {
                statusCode: 400,
                message: 'NO_DATA_FOUND',
                data: { categoryFormCourseData, categoryGroupData },
            };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { categoryFormCourseData, categoryGroupData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getProgramGroup = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormCourseIds } = query;
        const formGroupData = await formCourseGroupSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                    categoryFormCourseId: {
                        $in: categoryFormCourseIds.map((categoryCourseElement) =>
                            convertToMongoObjectId(categoryCourseElement),
                        ),
                    },
                },
                {
                    attemptTypeName: 1,
                    academicYear: 1,
                    groupName: 1,
                    term: 1,
                    categoryFormId: 1,
                    categoryFormCourseId: 1,
                },
            )
            .lean();
        if (!formGroupData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formGroupData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qapcSubModules = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const subModuleData = await qapcSubModuleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                { moduleName: 1, actionsIds: 1 },
            )
            .lean();
        if (!subModuleData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: subModuleData };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: subModuleData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formApproverLevel = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormIds } = query;
        const formApprovalLevelData = await formSettingSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: {
                        $in: categoryFormIds.map((categoryElement) =>
                            convertToMongoObjectId(categoryElement),
                        ),
                    },
                    isActive: true,
                    archive: false,
                    isDeleted: false,
                },
                {
                    'approvalLevel.category': 1,
                    'approvalLevel.specificSections': 1,
                    _id: 0,
                },
            )
            .lean();
        if (!formApprovalLevelData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: [] };
        }
        //check high approval level
        const ListApprovalLevel = formApprovalLevelData.reduce(
            (preApprovalElement, approvalElement) => {
                if (!preApprovalElement.approvalLevel) {
                    return approvalElement;
                }
                return approvalElement.approvalLevel.length >
                    preApprovalElement.approvalLevel.length
                    ? approvalElement
                    : preApprovalElement;
            },
            {},
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: ListApprovalLevel.approvalLevel,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, categoryFormIds, assignedInstitutionId } = query;
        const formCourseSettingData = await formSettingCourseSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(assignedInstitutionId && {
                        assignedInstitutionId: convertToMongoObjectId(assignedInstitutionId),
                    }),
                    ...(programId && { programId: convertToMongoObjectId(programId) }),
                    ...(categoryFormIds &&
                        categoryFormIds.length && {
                            categoryFormId: {
                                $in: categoryFormIds.map((categoryElement) =>
                                    convertToMongoObjectId(categoryElement),
                                ),
                            },
                        }),

                    isEnable: true,
                    isActive: true,
                    isConfigure: true,
                    isDeleted: false,
                },
                {
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    categoryId: 1,
                    categoryFormId: 1,
                },
            )
            .lean();
        if (!formCourseSettingData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: formCourseSettingData };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formCourseSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAcademicYear = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const institutionCalenderData = await institutionCalenderSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    end_date: { $gt: new Date() },
                    isActive: true,
                    isDeleted: false,
                    status: { $exists: false },
                },
                {
                    calendar_name: 1,
                    start_date: 1,
                    end_date: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        if (!institutionCalenderData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: institutionCalenderData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.saveRolePermission = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { cfpcPermissions, pccfPermissions, uniqueId } = body;
        if (cfpcPermissions) {
            if (uniqueId.length) {
                await pullCFPCPermission({ uniqueId });
            }
            return updateCFPCPermission({ _institution_id, cfpcPermissions });
        }
        if (pccfPermissions && pccfPermissions.length) {
            return updatePCCFPermission({ _institution_id, pccfPermissions });
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.assignedUserList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { qapcRoleId, hierarchy, levelName } = query;
        return getPermissionData({
            _institution_id,
            qapcRoleId,
            levelName,
            hierarchy,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qapcAction = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const actionsData = await qapcActionSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                { actionName: 1 },
            )
            .lean();
        if (!actionsData.length) {
            return { statusCode: 400, message: 'NO_DATA_FOUND', data: actionsData };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: actionsData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateRoleLevel = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { roleLevels } = body;
        const roleBulkUpdate = [];
        for (roleElement of roleLevels) {
            if (roleElement._id) {
                roleBulkUpdate.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(roleElement._id),
                            _institution_id: convertToMongoObjectId(_institution_id),
                        },
                        update: {
                            $set: {
                                levels: roleElement.levels,
                            },
                        },
                    },
                });
            }
        }
        if (roleBulkUpdate) {
            await qapcRoleSchema.bulkWrite(roleBulkUpdate);
        }
        return {
            statusCode: 200,
            message: 'UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserQAPCRole = async ({ headers = {} }) => {
    try {
        const { _user_id } = headers;
        const qapcPermissionData = await qapcPermissionSchema
            .find(
                {
                    $or: [
                        { userId: convertToMongoObjectId(_user_id) },
                        { guestUserId: convertToMongoObjectId(_user_id) },
                    ],
                },
                {
                    _id: 0,
                    qapcRoleIds: 1,
                },
            )
            .populate({ path: 'qapcRoleIds', select: { roleName: 1 } })
            .lean();
        if (!qapcPermissionData.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: { qapcRoleIds: [] } };
        }
        return { start: 200, message: 'DATA_RETRIEVED', data: qapcPermissionData[0] };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.userList = async () => {
    try {
        const userData = await userSchemas
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    user_type: STAFF,
                    status: COMPLETED,
                },
                {
                    user_id: 1,
                    name: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: userData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
