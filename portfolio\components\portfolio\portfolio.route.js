const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const PortfolioController = require('./portfolio.controller');
const FormController = require('../form/form.controller');
const {
    getPortfolioSchema,
    updatePortfolioSchema,
    deletePortfolioSchema,
    publishPortfolioSchema,
    getComponentFormSchema,
    updateComponentFormSchema,
    assignStudentSchema,
    getStudentsByCourseSchema,
    updateStudentReviewSchema,
    getPortfolioForAssignSchema,
} = require('./portfolio.validation');

router.get('/', validate(getPortfolioSchema), catchAsync(PortfolioController.getPortfolio));

router.put('/', validate(updatePortfolioSchema), catchAsync(PortfolioController.updatePortfolio));

router.delete(
    '/',
    validate(deletePortfolioSchema),
    catchAsync(PortfolioController.deletePortfolio),
);

router.put(
    '/publish',
    validate(publishPortfolioSchema),
    catchAsync(PortfolioController.publishPortfolio),
);

router.get(
    '/form',
    validate(getComponentFormSchema),
    catchAsync(PortfolioController.getComponentForm),
);

router.put(
    '/form',
    validate(updateComponentFormSchema),
    catchAsync(PortfolioController.updateComponentForm),
);

router.get('/list', catchAsync(PortfolioController.getPortfolioList));

router.delete('/detach', catchAsync(PortfolioController.detachPortfolioFrom));

router.put(
    '/assign',
    validate(assignStudentSchema),
    catchAsync(PortfolioController.assignStudentToPortfolio),
);

router.get(
    '/student',
    validate(getStudentsByCourseSchema),
    catchAsync(PortfolioController.getStudentsByCourse),
);

router.put(
    '/review',
    validate(updateStudentReviewSchema),
    catchAsync(PortfolioController.updateStudentReview),
);

router.get(
    '/assign',
    validate(getPortfolioForAssignSchema),
    catchAsync(PortfolioController.getPortfolioForAssignEvaluator),
);

router.get('/form-list', catchAsync(PortfolioController.getForms));

module.exports = router;
