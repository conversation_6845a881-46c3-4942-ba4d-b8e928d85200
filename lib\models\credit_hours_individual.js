let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let credit_hours_individual = new Schema({
    year: {
        type: Number,
        required: true
    },
    level: {
        type: Number,
        required: true
    },
    theory_credit_hours: {
        type: Number,
        required: true
    },
    pratical_credit_hours: {
        type: Number,
        required: true
    },
    clinical_credit_hours: {
        type: Number,
        required: true
    },
    rotation: {
        type: String,
        enum: ['yes', 'no'],
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.CREDIT_HOURS_INDIVIDUAL, credit_hours_individual);