// let university_formate = require('../university/university_formate');
let country_formate = require('../country/country_formate');

module.exports = {
    institution: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                type: element.type,
                name: element.name,
                code: element.code,
                location: element.location,
                country: country_formate.country_ID(element.country),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    institution_ID: (doc) => {
        let obj = {
            _id: doc._id,
            type: doc.type,
            name: doc.name,
            code: doc.code,
            location: doc.location,
            country: country_formate.country_ID(doc.country),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    institution_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            type: doc.type,
            name: doc.name,
            code: doc.code,
            location: doc.location,
            country: doc._country_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    institution_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                type: element.type,
                name: element.name,
                code: element.code,
                location: element.location,
                country: element._country_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },
}