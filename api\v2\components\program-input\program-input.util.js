const multer = require('multer');
const { uploadProgramMedia } = require('../../utility/file-upload');
const keys = require('../../utility/util_keys');
const { getS3SignedUrl } = require('../../services/aws.service');
const mediaUpload = uploadProgramMedia.fields([{ name: 'media', maxCount: 1 }]);
const { response_function } = require('../../utility/common');

const uploadProgramMediaFile = (req, res, next) => {
    mediaUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getPresignedUrlsForPrograms = async (doc, instituteId) => {
    let document = { ...doc };
    if (doc._doc) {
        document = { ...doc._doc };
    }
    if (document.goals && document.goals.mediaURL) {
        const fileName = document.goals.mediaURL.split('/').pop();
        const url = await getS3SignedUrl({
            bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
            key: `institute_${instituteId}/` + fileName,
        });
        document.goals.presignedMediaURL = url;
    }
    if (document.objectives && document.objectives.mediaURL) {
        const fileName = document.objectives.mediaURL.split('/').pop();
        const url = await getS3SignedUrl({
            bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
            key: `institute_${instituteId}/` + fileName,
        });
        document.objectives.presignedMediaURL = url;
    }
    if (document.portfolio && document.portfolio.length) {
        const promises = document.portfolio.map(async (portfolio) => {
            if (portfolio.mediaURL) {
                const fileName = portfolio.mediaURL.split('/').pop();
                const url = await getS3SignedUrl({
                    bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
                    key: `institute_${instituteId}/` + fileName,
                });
                return url;
            }
        });

        const presignedURLS = await Promise.all(promises);
        document.portfolio = document.portfolio.map((portfolio, index) => {
            if (presignedURLS[index]) {
                if (portfolio._doc) {
                    portfolio = { ...portfolio._doc, presignedMediaURL: presignedURLS[index] };
                } else {
                    portfolio = { ...portfolio, presignedMediaURL: presignedURLS[index] };
                }
            }
            return portfolio;
        });
    }
    return document;
};

//helper
const populateCurricula = (curriculumDocs, courseDocs) => {
    const curriculumDatas = [];
    let isConfigured = true;
    let isPreRequisite = false;
    let certificateCount = 0;
    curriculumDocs.forEach((curriculumDoc) => {
        let levelData = [];
        const yearData = [];
        curriculumDoc.yearLevel.forEach((yearElement) => {
            yearElement.levels.forEach((levelElement) => {
                let course_count = 0;
                courseDocs.forEach((courseDoc) => {
                    courseDoc.courseAssignedDetails.forEach((courseAssignedDetail) => {
                        if (
                            String(courseAssignedDetail._level_id) === String(levelElement._id) &&
                            String(courseAssignedDetail._year_id) === String(yearElement._id)
                        ) {
                            course_count += 1;
                        }
                    });
                });
                levelData.push({
                    _id: levelElement._id,
                    level_name: levelElement.levelName,
                    start_week: levelElement.startWeek,
                    end_week: levelElement.endWeek,
                    course_count,
                });
                if (course_count === 0) {
                    isConfigured = false;
                }
            });
            if (yearElement.certificate) {
                certificateCount += 1;
            }
            if (yearElement._preRequisite_id) {
                isPreRequisite = true;
            }
            const refactorCurriculumDoc = { ...yearElement };
            refactorCurriculumDoc.levels = levelData;
            levelData = [];
            yearData.push(refactorCurriculumDoc);
        });
        const refactorCurriculumDoc = {
            ...curriculumDoc,
            isConfigured,
            isPreRequisite,
            certificateCount,
        };
        refactorCurriculumDoc.yearLevel = yearData;
        curriculumDatas.push(refactorCurriculumDoc);
    });
    return curriculumDatas;
};

const populateSessionCurriculumDeptSubjects = (
    programs = [],
    departmentSubjects = [],
    curricula = [],
    sessionTypes = [],
    courseDocs = [],
    forSingleProgram = false,
) => {
    const populatedPrograms = programs.map((program) => {
        let programCurricula = curricula.filter(
            (curriculum) => curriculum._program_id.toString() === program._id.toString(),
        );
        if (forSingleProgram) {
            programCurricula = populateCurricula(programCurricula, courseDocs);
        }
        let programSessions = sessionTypes.filter(
            (sessionType) => sessionType._program_id.toString() === program._id.toString(),
        );
        const sessionTypesCount = programSessions.length;
        const deliveryTypesCount = programSessions
            .filter((session) => session.deliveryTypes && session.deliveryTypes.length)
            .reduce((prev, curr) => {
                return prev + curr.deliveryTypes.filter((type) => type.isDeleted === false).length;
            }, 0);
        programSessions = programSessions.map((sessionType) => {
            if (sessionType) {
                return {
                    sessionSymbol: sessionType.sessionSymbol,
                    sessionName: sessionType.sessionName,
                    _id: sessionType._id,
                };
            }
        });
        let departments = departmentSubjects.map((departmentSubject) => {
            if (departmentSubject._program_id.toString() === program._id.toString())
                return departmentSubject;
        });
        departments = departments.filter((department) => !!department);
        const departmentCount = departments.length;
        const subjectCount = departments.reduce(
            (prev, curr) =>
                prev +
                curr.subject.filter(
                    (subjectEntry) =>
                        subjectEntry &&
                        subjectEntry.isDeleted === false &&
                        subjectEntry.isActive === true,
                ).length,
            0,
        );
        if (!program._doc)
            return {
                ...program,
                curriculum: programCurricula,
                sessionTypes: programSessions,
                departmentSubject: { departmentCount, subjectCount },
                sessionDeliverTypes: { sessionTypesCount, deliveryTypesCount },
            };
        return {
            ...program._doc,
            curriculum: programCurricula,
            sessionTypes: programSessions,
            departmentSubject: { departmentCount, subjectCount },
            sessionDeliveryTypes: { sessionTypesCount, deliveryTypesCount },
        };
    });
    return populatedPrograms;
};

module.exports = {
    getPresignedUrlsForPrograms,
    uploadProgramMediaFile,
    populateSessionCurriculumDeptSubjects,
};
