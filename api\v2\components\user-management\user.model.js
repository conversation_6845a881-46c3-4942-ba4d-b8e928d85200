const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const constant = require('../../utility/constants');

const userSchema = new Schemas(
    {
        _institution_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _parent_id: {
            type: Schemas.Types.ObjectId,
            default: null,
        },
        user_type: {
            type: String,
            required: true,
        },
        user_id: {
            value: {
                type: String,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            // this is used for after enabling mandatory in settings for registered users
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        ioToken: {
            type: String,
            default: null,
            trim: true,
        },
        batch: {
            value: {
                type: String,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        email: {
            type: String,
            required: true,
            // unique: true,
            sparse: true,
        },
        name: {
            first: {
                value: {
                    type: String,
                    trim: true,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                userEdited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: false,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            middle: {
                value: {
                    type: String,
                    trim: true,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                userEdited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            last: {
                value: {
                    type: String,
                    trim: true,
                },
                flagged: {
                    type: Boolean,
                    default: 0,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                userEdited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            family: {
                value: {
                    type: String,
                    trim: true,
                },
                flagged: {
                    type: Boolean,
                    default: 0,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                userEdited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
        },
        password: String,
        enrollmentYear: {
            value: {
                type: Date,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            // this is used for after enabling mandatory in settings for registered users
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        program: {
            value: {
                type: String,
                trim: true,
            },
            _program_id: {
                type: Schemas.Types.ObjectId,
                ref: constant.PROGRAM,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            // this is used for after enabling mandatory in settings for registered users
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        gender: {
            value: {
                type: String,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            // this is used for after enabling mandatory in settings for registered users
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        mobile: {
            code: { type: String },
            no: {
                type: Number,
                // unique: true,
                sparse: true,
            },
            flagged: {
                type: Boolean,
                default: false,
            },
            status: {
                type: String,
            },
            edited: {
                type: Boolean,
                default: false,
            },
            userEdited: {
                type: Boolean,
                default: false,
            },
            flagCount: {
                type: Number,
                default: 0,
            },
            invalid: { type: Boolean, default: false },
            // this is used for after enabling mandatory in settings for registered users
            globalMandatory: {
                type: Boolean,
                default: false,
            },
            globalMandatoryEdited: {
                type: Boolean,
                default: false,
            },
        },
        profileDetails: {
            passportNo: {
                value: {
                    type: String,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            dob: {
                value: {
                    type: Date,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            nationalityId: {
                value: {
                    type: String,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            _nationality_id: {
                value: {
                    type: String,
                },
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
            addressDetails: {
                buildingStreetName: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                floorNo: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                country: {
                    value: {
                        name: String,
                        countryId: Number,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                countryId: {
                    type: String,
                },
                districtId: {
                    type: String,
                },
                district: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                city: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                zipCode: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                unit: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
            },
            office: {
                officeExtension: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                officeRoomNo: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
            },
            otherContactDetails: {
                parentGuardianPhoneNo: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                parentGuardianEmailId: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
                spouseGuardianPhoneNo: {
                    value: {
                        type: String,
                    },
                    flagged: {
                        type: Boolean,
                        default: false,
                    },
                    status: {
                        type: String,
                    },
                    edited: {
                        type: Boolean,
                        default: false,
                    },
                    flagCount: {
                        type: Number,
                        default: 0,
                    },
                    invalid: { type: Boolean, default: false },
                    // this is used for after enabling mandatory in settings for registered users
                    globalMandatory: {
                        type: Boolean,
                        default: false,
                    },
                    globalMandatoryEdited: {
                        type: Boolean,
                        default: false,
                    },
                },
            },
        },
        vaccineConfiguration: [
            {
                _category_id: { type: Schemas.Types.ObjectId },
                vaccineDetails: [
                    {
                        _vaccine_id: { type: Schemas.Types.ObjectId },
                        dosageTaken: [{ type: String }],
                        boosterTaken: [{ type: String }],
                        universalNumber: { type: String },
                        vaccineCentre: { type: String },
                        country: { type: String },
                        city: { type: String },
                        doses: [
                            {
                                name: { type: String },
                                date: { type: String },
                                batchNo: { type: String },
                            },
                        ],
                        booster: [
                            {
                                name: { type: String },
                                date: { type: String },
                                batchNo: { type: String },
                            },
                        ],
                    },
                ],
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                // this is used for after enabling mandatory in settings for registered users
                globalMandatory: {
                    type: Boolean,
                    default: false,
                },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        contact: [
            {
                relationType: {
                    type: String,
                },
                name: String,
                email: String,
                mobile: Number,
            },
        ],
        _role_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.ROLE_ASSIGN,
        },
        designation: {
            type: String,
        },
        _designation_id: {
            type: Schemas.Types.ObjectId,
        },
        academic_allocation: [
            {
                allocation_type: {
                    type: String,
                    enum: [constant.PRIMARY, constant.AUXILIARY],
                },
                departmentType: {
                    type: String,
                    enum: [constant.ACADEMIC, constant.ADMIN],
                },
                _program_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.PROGRAM,
                },
                programName: { type: String },
                _department_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DEPARTMENT_SUBJECT,
                },
                departmentName: { type: String },
                departmentSubject: [
                    {
                        _department_subject_id: {
                            type: Schemas.Types.ObjectId,
                            ref: constant.DEPARTMENT_SUBJECT,
                        },
                        subjectName: { type: String },
                    },
                ],
            },
        ],
        staff_employment_type: {
            type: String,
            enum: [constant.ACADEMIC, constant.ADMINISTRATION, constant.BOTH],
        },
        employment: {
            user_type: {
                type: String,
                enum: [constant.ACADEMIC, constant.ADMINISTRATION, constant.BOTH],
            },
            user_employment_type: {
                type: String,
                enum: [constant.FULL_TIME, constant.PART_TIME],
            },
            user_schedule_type: {
                type: String,
                enum: [constant.BY_DATE, constant.BY_DAY],
            },
            staffModeType: {
                type: String,
                enum: [constant.COMMON, constant.INDEPENDENT],
            },
            daysTimeType: {
                type: String,
                enum: [constant.COMMON, constant.INDEPENDENT],
            },
            academic_year: String,
            schedule_times: {
                full_time: [
                    {
                        mode: {
                            type: [String],
                            enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                        },
                        days: [
                            {
                                type: String,
                                enum: [
                                    constant.DAYS.SUNDAY,
                                    constant.DAYS.MONDAY,
                                    constant.DAYS.TUESDAY,
                                    constant.DAYS.WEDNESDAY,
                                    constant.DAYS.THURSDAY,
                                    constant.DAYS.FRIDAY,
                                    constant.DAYS.SATURDAY,
                                ],
                            },
                        ],
                        startTime: String,
                        endTime: String,
                    },
                ],
                by_date: [
                    {
                        startDate: String,
                        endDate: String,
                        staffModeType: {
                            type: String,
                            enum: [constant.COMMON, constant.INDEPENDENT],
                        },
                        daysTimeType: {
                            type: String,
                            enum: [constant.COMMON, constant.INDEPENDENT],
                        },
                        schedule: [
                            {
                                mode: {
                                    type: [String],
                                    enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                                },
                                days: [
                                    {
                                        type: String,
                                        enum: [
                                            constant.DAYS.SUNDAY,
                                            constant.DAYS.MONDAY,
                                            constant.DAYS.TUESDAY,
                                            constant.DAYS.WEDNESDAY,
                                            constant.DAYS.THURSDAY,
                                            constant.DAYS.FRIDAY,
                                            constant.DAYS.SATURDAY,
                                        ],
                                    },
                                ],
                                startTime: String,
                                endTime: String,
                            },
                        ],
                    },
                ],
                by_day: [
                    {
                        mode: {
                            type: [String],
                            enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                        },
                        days: [
                            {
                                type: String,
                                enum: [
                                    constant.DAYS.SUNDAY,
                                    constant.DAYS.MONDAY,
                                    constant.DAYS.TUESDAY,
                                    constant.DAYS.WEDNESDAY,
                                    constant.DAYS.THURSDAY,
                                    constant.DAYS.FRIDAY,
                                    constant.DAYS.SATURDAY,
                                ],
                            },
                        ],
                        startTime: String,
                        endTime: String,
                    },
                ],
            },
        },
        verification: {
            invitedAt: { type: Date },
            email: { type: Boolean, default: false },
            password: { type: Boolean, default: false },
            mobile: {
                type: Boolean,
                default: true,
            },
            profile: {
                basicDetails: { type: Boolean, default: false },
                profileDetails: { type: Boolean, default: false },
                vaccinationDetails: { type: Boolean, default: false },
            },
            document: { type: Boolean, default: false },
            face: { type: Boolean, default: false },
            skip: { type: Boolean, default: false },
        },
        face: [{ type: String }],
        status: {
            type: String,
            default: constant.IMPORTED,
            enum: [
                constant.IMPORTED,
                'signed',
                constant.PASSWORD_CONFIRMED,
                constant.PROFILE_UPDATED,
                constant.PROFILE_UPDATING,
                constant.RESUBMITTED,
                constant.VERIFICATION_DONE,
                constant.DONE,
                constant.ACTIVE,
                constant.INACTIVE,
                constant.BLOCKED,
                constant.INVALID,
                constant.VALID,
                constant.EXPIRED,
            ],
        },
        validationPendingStatus: { type: Boolean, default: false },
        isRegistered: { type: Boolean, default: false },
        device_type: String,
        last_login_device_type: String,
        web_fcm_token: {
            type: String,
        },
        fcm_token: {
            type: String,
        },
        otp: {
            no: {
                type: Number,
                default: 0,
            },
            issuedDateTime: {
                type: Date,
                default: Date.now(),
            },
        },
        activityLog: [
            {
                activity: { type: String },
                activityAt: { type: Date },
            },
        ],
        lastLogin: {
            type: Date,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isDefault: {
            type: Boolean,
            default: false,
        },
        socketEventId: {
            dashboardEventId: { type: String },
            activityEventId: { type: String },
            chatEventId: { type: String },
            sessionEventId: { type: String },
            courseEventId: { type: String },
        },
        uploadedDocuments: [
            {
                _category_id: {
                    type: Schemas.Types.ObjectId,
                },
                document: [
                    {
                        _document_id: {
                            type: Schemas.Types.ObjectId,
                        },
                        // this is used for after enabling mandatory in settings for registered users
                        globalMandatory: {
                            type: Boolean,
                            default: false,
                        },
                        url: [
                            {
                                value: { type: String },
                                flagged: { type: Boolean, default: false },
                                status: {
                                    type: String,
                                },
                                edited: {
                                    type: Boolean,
                                    default: false,
                                },
                                flagCount: { type: Number, default: 0 },
                                invalid: { type: Boolean, default: false },
                                globalMandatoryEdited: {
                                    type: Boolean,
                                    default: false,
                                },
                            },
                        ],
                    },
                ],
            },
        ],
        biometricData: {
            face: {
                value: [
                    {
                        type: String,
                    },
                ],
                flagged: {
                    type: Boolean,
                    default: false,
                },
                status: {
                    type: String,
                },
                edited: {
                    type: Boolean,
                    default: false,
                },
                flagCount: {
                    type: Number,
                    default: 0,
                },
                invalid: { type: Boolean, default: false },
                globalMandatoryEdited: {
                    type: Boolean,
                    default: false,
                },
            },
        },
        statusHistory: [
            {
                status: {
                    type: String,
                },
                updatedBy: {
                    type: Schemas.Types.ObjectId,
                },
                updatedDateTime: {
                    type: Date,
                },
            },
        ],
        logoutTime: { type: Date },
        changedFields: { type: [String], default: [] },
        isGlobalMandatoryStatus: { type: Boolean, default: false },
        approvingTypeAll: { type: Boolean, default: false },
        isRejectHandlingStatus: { type: Boolean, default: false },
    },
    { timestamps: true },
);
module.exports = userSchema;
