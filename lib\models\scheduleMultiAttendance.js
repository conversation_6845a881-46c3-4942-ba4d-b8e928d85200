const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const {
    INSTITUTION,
    COURSE_SCHEDULE,
    USER,
    RUNNING,
    COMPLETED,
    SCHEDULE_MULTI_ATTENDANCE,
} = require('../utility/constants');

const scheduleMultiAttendance = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        primaryScheduleId: { type: ObjectId, ref: COURSE_SCHEDULE },
        startByStaffId: { type: ObjectId, ref: USER },
        mergeScheduleDetails: [
            {
                scheduleId: { type: ObjectId, ref: COURSE_SCHEDULE },
                // includeStaffId: [{ type: ObjectId, ref: USER }],
                excludeStaffId: [{ type: ObjectId, ref: USER }],
            },
        ],
        excludeMeOther: { type: Boolean, default: true },
        otherScheduleIds: [{ type: ObjectId, ref: COURSE_SCHEDULE }],
        status: { type: String, enum: [RUNNING, COMPLETED] },
        isDeleted: { type: Boolean, default: false },
        isActive: { type: Boolean, default: true },
    },
    { timestamps: true },
);
module.exports = model(SCHEDULE_MULTI_ATTENDANCE, scheduleMultiAttendance);
