const mongoose = require('mongoose');
const { DIGI_SURVEY, PLO, CLO, DIGI_SURVEY_QUESTION } = require('../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const digiSurveyQuestionSchemas = new Schema(
    {
        surveyId: {
            type: ObjectId,
            ref: DIGI_SURVEY,
        },
        questionNo: { type: String },
        questionType: { type: String },
        question: { type: String },
        questionDescription: { type: String },
        ratingValues: [{ position: { type: Number }, values: { type: String } }],
        ratingType: { type: Number },
        outCome: [
            {
                outComeId: { type: ObjectId },
                outComeName: { type: String },
            },
        ],
        outComeType: { type: String, enum: [CLO, PLO] },
        tagId: { type: ObjectId },
        tagName: { type: String },
        kpi: { type: String },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(DIGI_SURVEY_QUESTION, digiSurveyQuestionSchemas);
