let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let department_division = new Schema({
    title: {
        type: String
    },
    _department_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT,
        required: true
    },
    _subject_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
    }],
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DEPARTMENT_DIVISIONS, department_division);