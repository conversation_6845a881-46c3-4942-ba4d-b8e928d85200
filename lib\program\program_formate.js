let institution_formate = require('../institution/institution_formate');

module.exports = {
    program: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                level: element.level,
                level_no: element.level_no,
                degree: element.degree,
                no: element.no,
                theory_credit: element.theory_credit,
                practicals_credit: element.practicals_credit,
                clinic_credit: element.clinic_credit,
                total_credit: element.total_credit,
                abbreviation: element.abbreviation,
                interim: doc.interim,
                // abbreviation: {
                //     theory: element.abbreviation.theory,
                //     practical: element.abbreviation.practical,
                //     clinic: element.abbreviation.clinic,
                //     credit_hours: element.abbreviation.credit_hours,
                //     contact_hours: element.abbreviation.contact_hours
                // },
                institution: institution_formate.institution_ID_Only(element.institution),
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    program_ID: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            level: doc.level,
            level_no: doc.level_no,
            degree: doc.degree,
            no: doc.no,
            theory_credit: doc.theory_credit,
            practicals_credit: doc.practicals_credit,
            clinic_credit: doc.clinic_credit,
            total_credit: doc.total_credit,
            abbreviation: doc.abbreviation,
            interim: doc.interim,
            // abbreviation: {
            //     theory: doc.abbreviation.theory,
            //     practical: doc.abbreviation.practical,
            //     clinic: doc.abbreviation.clinic,
            //     credit_hours: doc.abbreviation.credit_hours,
            //     contact_hours: doc.abbreviation.contact_hours
            // },
            institution: institution_formate.institution_ID_Only(doc.institution),
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            level: doc.level,
            level_no: doc.level_no,
            degree: doc.degree,
            no: doc.no,
            theory_credit: doc.theory_credit,
            practicals_credit: doc.practicals_credit,
            clinic_credit: doc.clinic_credit,
            total_credit: doc.total_credit,
            abbreviation: doc.abbreviation,
            interim: doc.interim,
            // abbreviation: {
            //     theory: doc.abbreviation.theory,
            //     practical: doc.abbreviation.practical,
            //     clinic: doc.abbreviation.clinic,
            //     credit_hours: doc.abbreviation.credit_hours,
            //     contact_hours: doc.abbreviation.contact_hours
            // },
            institution: doc._institution_id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                level: element.level,
                level_no: element.level_no,
                degree: element.degree,
                no: element.no,
                theory_credit: element.theory_credit,
                practicals_credit: element.practicals_credit,
                clinic_credit: element.clinic_credit,
                total_credit: element.total_credit,
                abbreviation: element.abbreviation,
                interim: doc.interim,
                // abbreviation: {
                //     theory: element.abbreviation.theory,
                //     practical: element.abbreviation.practical,
                //     clinic: element.abbreviation.clinic,
                //     credit_hours: element.abbreviation.credit_hours,
                //     contact_hours: element.abbreviation.contact_hours
                // },
                institution: element._institution_id,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}