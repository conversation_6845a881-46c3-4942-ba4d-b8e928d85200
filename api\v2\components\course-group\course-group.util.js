const { COURSE_TYPE } = require('../../utility/constants');

const getGroupedCourses = (groups) => {
    const courseIds = [];
    groups.forEach((group) => {
        courseIds.push(...group._course_ids);
    });
    return courseIds;
};

const courseSharedWithDetail = ({
    courseDatas,
    programId,
    curriculumId,
    yearId,
    levelId,
    isPhaseFlowWithOutLevel,
}) => {
    const courses = [];
    for (courseElement of courseDatas) {
        const courseShared = { courseShared: false, courseSharedFrom: '' };
        let sharedWith;
        for (courseAssignedElement of courseElement.courseAssignedDetails) {
            if (isPhaseFlowWithOutLevel === true) {
                sharedWith = courseAssignedElement.courseSharedWith.find(
                    (sharedElement) => sharedElement._year_id.toString() === yearId,
                );
            } else {
                sharedWith = courseAssignedElement.courseSharedWith.find(
                    (sharedElement) =>
                        sharedElement._year_id.toString() === yearId &&
                        sharedElement._level_id.toString() === levelId,
                );
            }
            if (sharedWith) {
                courseShared.courseShared = true;
                courseShared.courseSharedFrom = courseAssignedElement.programName;
                if (!courseAssignedElement.programName) {
                    courseShared.courseSharedFrom = COURSE_TYPE.INDEPENDENT;
                }
            }
        }
        courses.push({
            ...courseElement,
            ...courseShared,
        });
    }
    return courses;
};

const populateCourseDetails = (
    groups,
    groupedCourses,
    programId,
    curriculumId,
    yearId,
    levelId,
    isPreRequisite,
    isPhaseFlowWithOutLevel,
) => {
    const hashMap = {};
    const courseGroups = [];
    const populatedGroupedCourses = courseSharedWithDetail({
        courseDatas: groupedCourses,
        programId,
        curriculumId,
        yearId,
        levelId,
        isPhaseFlowWithOutLevel,
    });
    groups.forEach((group) => {
        const courseIds = group._course_ids;
        if (!hashMap[[group._id]]) hashMap[[group._id]] = [];
        courseIds.forEach((courseId) => {
            const course = populatedGroupedCourses.find((courseEntry) => {
                return courseEntry._id.toString() === courseId.toString();
            });
            course.groupName = group.groupName;
            hashMap[[group._id]].push(course);
        });
    });
    const keys = Object.keys(hashMap);
    keys.forEach((key) => {
        courseGroups.push({
            groupId: key,
            groupName: hashMap[key][0].groupName,
            courses: hashMap[key],
        });
    });
    return courseGroups;
};

module.exports = { getGroupedCourses, populateCourseDetails, courseSharedWithDetail };
