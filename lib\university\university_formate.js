let country_formate = require('../country/country_formate');
module.exports = {
    university: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                university_code: element.university_code,
                location: element.location,
                country: country_formate.country_ID(element.country),
                isActive: element.isDeleted,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    university_ID: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            university_code: doc.university_code,
            location: doc.location,
            country: country_formate.country_ID(doc.country),
            isActive: doc.isDeleted,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    university_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            university_code: doc.university_code,
            location: doc.location,
            country: doc._country_id,
            isActive: doc.isDeleted,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    university_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                university_code: element.university_code,
                location: element.location,
                country: element._country_id,
                isActive: element.isDeleted,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },
}