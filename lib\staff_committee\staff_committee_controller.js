const constant = require('../utility/constants');
var staff_committee = require('mongoose').model(constant.STAFF_COMMITTEE);
var program = require('mongoose').model(constant.PROGRAM);
var user = require('mongoose').model(constant.USER);
const base_control = require('../base/base_controller');
const ObjectID = require('mongodb').ObjectID;
const common_files = require('../utility/common');


exports.create_committee = async(req,res) =>{
    let objs = {
        committee_name:req.body.committee_name,
        committee_description:req.body.committee_description,
        committee_admin: req.body.committee_admin
    }

    let doc = await base_control.insert(staff_committee, objs);
    if(doc.status){
        common_files.com_response(res, 200, true, "Staff Committee created", doc.responses)
    }else{
        common_files.com_response(res, 404, false, "Error in creating committee", "Error in creating committee")
    }
    
}


exports.get_committee = async(req,res) => {
    let doc_data = [];
    let doc = await base_control.get_list(staff_committee,{'isDeleted':false})
    if (doc.status){
        doc.data.forEach(element => {
        doc_data.push({_id: element._id,committee_admin:element.committee_admin,committee_name:element.committee_name,committee_description:element.committee_description,_member_id:element._member_id});
        });
        common_files.com_response(res, 200, true, "Got all committee data", doc_data)
    }else{
        common_files.com_response(res, 404, false, "Error in getting committee data", "Error in getting committee data")
    }
}


exports.get_committee_id = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(staff_committee, { _id: { $in: req.params.id }, 'isDeleted': false }); 
    if(checks.status){
        let id = req.params.id
    let aggre = [
        { $match: { '_id': ObjectID(id) } },
        { $lookup: { from: constant.USER, localField: '_member_id', foreignField: '_id', as: 'member' } },
        { $lookup: { from: constant.USER, localField: 'committee_admin', foreignField: '_id', as: 'committee_admin' } },
        { $match: { 'isDeleted': false } },
        { $project:{
            '_id':1,
            'committee_name':1,
            'committee_description':1,
            '_member_id':1,
            'member._id':1,
            'member.name':1,
            'member.user_id':1,
            'member.role':1,
            'committee_admin._id':1,
            'committee_admin.name':1,
            'committee_admin.user_id':1,
            'committee_admin.role':1
        }}
    ];
    let doc = await base_control.get_aggregate(staff_committee,aggre)
    if(doc.status){
        common_files.com_response(res, 200, true, "Get committee by id" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in getting committee by id", "Error in getitng committee by id")
    }
} else {
        common_files.com_response(res ,404 ,false, "Error id not match" ,'Check parsing reference ID')
}
}


exports.update_committee = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(staff_committee, { _id: { $in: req.params.id }, 'isDeleted': false });
    if(checks.status){
    let objs = {
        committee_name:req.body.committee_name,
        committee_description:req.body.committee_description,
        committee_admin:req.body.committee_admin
    }
    let doc = await base_control.update(staff_committee,{_id:req.params.id},objs)
    if(doc.status){
        common_files.com_response(res, 200, true, "Updated the committee" ,doc.responses)
    }else{
        common_files.com_response(res, 404, false, "Error in updating committee", "Error in updating committee")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", 'Check parsing reference ID')
}
}


exports.add_members = async(req,res) =>{
    let checks = {status : true}
    checks = await base_control.check_id(staff_committee,{_id:{$in:req.body.id},'isDeleted':false})
    if(checks.status){
    let objs ={
        $push:{
                _member_id:req.body._member_id
        }
    }
    let doc = await base_control.update_push_pull_many(staff_committee,{_id:req.body.id},objs)
    if(doc.status){
        common_files.com_response(res, 200, true, "Added members to the committee" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in adding members", "Error in adding members")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", 'Check parsing reference ID')
}
}


exports.delete_members = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(staff_committee,{_id:{$in:req.params.id},'isDeleted':false})
    if(checks.status){
    let objs = {
        $pull:{
              _member_id:req.params.memberid
        }
     }
    let doc = await base_control.update_push_pull(staff_committee,{_id:req.params.id},objs) 
    if(doc.status){
        common_files.com_response(res, 200, true, "Get all members by committee" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in deleting members" ,"Error in deleting members")
    }
} else {
    common_files.com_response(res, 404, false, "Error committee id not match", 'Check parsing refernce ID')
}
}


exports.delete_committee = async(req,res) =>{
    let checks = {status:true}
    checks = await base_control.check_id(staff_committee,{_id:{$in:req.params.id},'isDeleted':false})
    if(checks.status){
    let objectid = ObjectID(req.params.id)
    let doc = await base_control.delete(staff_committee,objectid);
    if(doc.status){
        common_files.com_response(res, 200, true, "Deleted committee" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in deleting committee" ,doc.data)
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", 'Check parsing Reference ID')
}
}


exports.get_admin = async(req,res) =>{
    let id = req.params.id
    let aggre = [
        { $match: { 'committee_admin': ObjectID(id) } },
        { $lookup: { from: constant.USER, localField: '_member_id', foreignField: '_id', as: 'member' } },
        { $lookup: { from: constant.USER, localField: 'committee_admin', foreignField: '_id', as: 'committee_admin' } },
        { $match: { 'isDeleted': false } },
        { $project:{
            '_id':1,
            'committee_name':1,
            'committee_description':1,
            '_member_id':1,
            'member._id':1,
            'member.name':1,
            'member.user_id':1,
            'member.role':1,
            'committee_admin._id':1,
            'committee_admin.name':1,
            'committee_admin.user_id':1,
            'committee_admin.role':1
        }}
    ];
    let doc =await base_control.get_aggregate(staff_committee, aggre)
    if(doc.status){
        common_files.com_response(res, 200, true, "committee list based on admin" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in getting committee details" ,doc.data)
    }
}


exports.getmembersbystaff = async(req,res) => {
    let aggre = [
        {$match:{$or:[
            {'academic_allocation.allocation_type':'primary'},
            {'employment.user_employment_type':req.params.user_employment_type},
            {'employment.user_type':req.params.user_type},
            {'role':req.params.role}
        ]}},
        { $lookup: { from: constant.PROGRAM, localField: 'academic_allocation._program_id', foreignField: '_id', as: 'program' } },
        {$match:{'isDeleted': false}},
        { $project:{
            '_id':1,
            'name':1,
            'role':1,
            'user_id':1,
            'academic_allocation':1,
            'program._id':1,
            'program.name':1,
            'program.level':1,
            'program.level_no':1,
            'program.degree':1,
            'program.no':1
        }}
    ];
    let doc = await base_control.get_aggregate(user ,aggre);
    if(doc.status){
        common_files.com_response(res, 200, true, "staff list based on filters" ,doc.data)
    }else{
        common_files.com_response(res, 404, false, "Error in getting staff list" ,doc.data)
    }

}


