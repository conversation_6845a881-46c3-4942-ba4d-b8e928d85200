/* eslint-disable guard-for-in */
const constant = require('../utility/constants');
const lms = require('mongoose').model(constant.LMS);
const institution = require('mongoose').model(constant.INSTITUTION);
const role = require('mongoose').model(constant.ROLE);
const user = require('mongoose').model(constant.USER);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const { lms_data } = require('../utility/common_datas');
const ObjectId = common_files.convertToMongoObjectId;

exports.list_category_id = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const aggr = [
        { $unwind: '$category' },
        { $match: { 'category._id': ObjectId(req.params.id) } },
    ];
    const doc = await base_control.get_aggregate(lms, aggr);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('CATEGORY_LIST_BY_ID'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.list_category = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
    const doc = await base_control.get(lms, query, {});
    if (doc.status) {
        // doc.data.category = doc.data.category.filter(ele => ele.isDeleted == false);
        const res_data = [];
        let leave_types = [];
        const student_warning_absence_calculation = [];
        doc.data.category.forEach((element) => {
            if (element.isDeleted == false) {
                leave_types = [];
                element.leave_type.forEach((sub_element) => {
                    if (sub_element.isDeleted == false) {
                        leave_types.push(sub_element);
                    }
                });
                res_data.push({
                    isActive: element.isActive,
                    isDeleted: element.isDeleted,
                    _id: element._id,
                    category_to: element.category_to,
                    category_name: element.category_name,
                    category_type: element.category_type,
                    leave_type: leave_types,
                });
            }
        });

        doc.data.student_warning_absence_calculation.forEach((element) => {
            if (element.isDeleted == false) student_warning_absence_calculation.push(element);
        });
        doc.data.student_warning_absence_calculation = student_warning_absence_calculation;
        doc.data.category = res_data;
        common_files.responseFunctionWithRequest(
            req,
            200,
            true,
            req.t('LEAVE_MANAGEMENT_LIST'),
            doc.data,
        );
    } else {
        common_files.responseFunctionWithRequest(
            req,
            200,
            false,
            req.t('LEAVE_MANAGEMENT_LIST'),
            {},
        );
    }
};

exports.single_category = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _institution_id: ObjectId(req.headers._institution_id) };
    const project = { category: { $elemMatch: { _id: ObjectId(req.params.id) } } };
    const doc = await base_control.get(lms, query, project);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('LEAVE_MANAGEMENT_CATEGORY'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.update_status = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const { category_id, leave_type_id, isActive } = req.body;
    let query;
    const setObj = {};
    const arr_filter = { arrayFilters: [] };
    if (category_id && leave_type_id) {
        query = {
            category: { $elemMatch: { _id: category_id, 'leave_type._id': leave_type_id } },
        };
        setObj['category.$[outer].leave_type.$[inner].isActive'] = isActive;
        arr_filter.arrayFilters.push({ 'outer._id': ObjectId(category_id) });
        arr_filter.arrayFilters.push({ 'inner._id': ObjectId(leave_type_id) });
    } else {
        query = {
            'category._id': category_id,
        };
        setObj['category.$.isActive'] = isActive;
        arr_filter.arrayFilters.push({ 'outer._id': ObjectId(category_id) });
    }
    const doc = await base_control.update_condition_array_filter(
        lms,
        query,
        {
            $set: setObj,
        },
        arr_filter,
    );
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            `${
                category_id && leave_type_id ? 'Leave Type' : 'Category'
            } status updated successfully. `,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.delete_category = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { 'category._id': ObjectId(req.params.id) };
    const doc = await base_control.update_condition(lms, query, {
        $set: {
            'category.$.isDeleted': true,
            'category.$.isActive': false,
        },
    });
    if (doc.status) {
        common_files.com_response(res, 201, true, req.t('CATEGORY_DELETED_SUCCESSFULLY'));
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.delete_leave_type = async (req, res) => {
    const category_id = req.params.id;
    // const leave_type_id = req.params.typeid;

    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        category: { $elemMatch: { _id: category_id, 'leave_type._id': req.params.typeid } },
    };
    const arr_filter = {
        arrayFilters: [
            { 'outer._id': ObjectId(req.params.id) },
            { 'inner._id': ObjectId(req.params.typeid) },
        ],
    };
    const doc = await base_control.update_condition_array_filter(
        lms,
        query,
        {
            $set: {
                'category.$[outer].leave_type.$[inner].isDeleted': true,
                'category.$[outer].leave_type.$[inner].isActive': false,
            },
        },
        arr_filter,
    );
    console.log({ query, doc });
    if (doc.status) {
        common_files.com_response(res, 201, true, req.t('LEAVE_TYPE_DELETED_SUCCESSFULLY'));
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.list_leave_type = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );

    /*
    let query = { "category._id": ObjectId(category_id), "category.isDeleted": false };
    let proj = { "category.leave_type": 1 } */
    const category_id = req.params.id;
    const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
    const doc = await base_control.get_list(lms, query, {});
    if (doc.status) {
        const cat_ind = doc.data[0].category.findIndex((ele) => ele._id == category_id);
        if (cat_ind == -1)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CATEGORY_NOT_FOUND'),
                        req.t('CATEGORY_NOT_FOUND'),
                    ),
                );
        const cat_data = doc.data[0].category[cat_ind];
        const leave_type_data = cat_data.leave_type.filter((ele) => ele.isDeleted == false);
        doc.data = leave_type_data;
        common_files.com_response(res, 200, true, req.t('LEAVE_TYPE_LIST'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};

exports.insert_category = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    let doc;
    if (lms_check.status) {
        //check duplicate category
        const cat_ind = lms_check.data.category.findIndex(
            (ele) =>
                ele &&
                ele.category_name &&
                ele.category_name.toLowerCase() === req.body.category_name.toLowerCase() &&
                ele.category_type == req.body.category_type &&
                ele.category_to == req.body.to &&
                ele.isDeleted == false,
        );
        if (cat_ind != -1)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('DUPLICATE_FOUND_NOT_ABLE_TO_INSERT'),
                        req.t('DUPLICATE_FOUND_NOT_ABLE_TO_INSERT'),
                    ),
                );

        const query = {
            _id: ObjectId(lms_check.data._id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const objs = {
            $push: {
                category: {
                    category_to: req.body.to,
                    category_name: req.body.category_name,
                    category_type: req.body.category_type,
                },
            },
        };
        doc = await base_control.update_condition(lms, query, objs);
    } else {
        const objs = {
            _institution_id: ObjectId(req.headers._institution_id),
            category: {
                category_to: req.body.to,
                category_name: req.body.category_name,
                category_type: req.body.category_type,
            },
        };
        doc = await base_control.insert(lms, objs);
    }
    if (doc.status)
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('ADDED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    return res
        .status(404)
        .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
};
exports.update_category = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    if (!lms_check.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('FAILED_TO_UPDATE'), []));
    //check duplicate category
    const cat_ind = lms_check.data.category.findIndex(
        (ele) =>
            ele &&
            ele.category_name &&
            ele.category_name.toLowerCase() == req.body.category_name.toLowerCase() &&
            ele.category_type == req.body.category_type &&
            ele.category_to == req.body.to &&
            ele.isDeleted == false,
    );
    if (cat_ind != -1)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('DUPLICATE_FOUND_NOT_ABLE_TO_INSERT'),
                    req.t('DUPLICATE_FOUND_NOT_ABLE_TO_INSERT'),
                ),
            );

    const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
    const objs = {
        $set: {
            'category.$[i].category_to': req.body.to,
            'category.$[i].category_name': req.body.category_name,
            'category.$[i].category_type': req.body.category_type,
        },
    };
    const filter = {
        arrayFilters: [
            {
                'i._id': req.params.id,
            },
        ],
    };
    const doc = await base_control.update_condition_array_filter(lms, query, objs, filter);
    if (!doc.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('FAILED_TO_UPDATE'),
                    doc.data,
                ),
            );
    return res
        .status(200)
        .send(
            common_files.response_function(res, 200, true, req.t('UPDATED_SUCCESSFULLY'), doc.data),
        );
};

exports.insert_leave_type = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const { category_id, ...reqBody } = req.body;
    const checkQuery = {
        'category._id': ObjectId(category_id),
        category: {
            $elemMatch: {
                _id: ObjectId(category_id),
                'leave_type.type_name': reqBody.type_name,
                isDeleted: false,
            },
        },
        isDeleted: false,
    };
    const checkDoc = await base_control.get(lms, checkQuery);
    if (checkDoc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('DUPLICATE_LEAVE_TYPE'),
                    req.t('DUPLICATE_LEAVE_TYPE'),
                ),
            );

    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    if (!lms_check.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('NO_DATA_FOUND'), []));

    const cat_ind = lms_check.data.category.findIndex((ele) =>
        ele._id.equals(req.body.category_id),
    );
    if (cat_ind == -1)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('CATEGORY_NOT_FOUND'), []));

    const leave_type_ind = lms_check.data.category[cat_ind].leave_type.findIndex(
        (ele) =>
            ele &&
            ele.type_name &&
            ele.type_name.toLowerCase() == req.body.type_name.toLowerCase() &&
            ele.isDeleted == false,
    );
    if (leave_type_ind != -1)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    true,
                    req.t('DUPLICATE_FOUND'),
                    req.t('DUPLICATE_FOUND'),
                ),
            );

    const query = { 'category._id': ObjectId(category_id) };
    const doc = await base_control.update_condition(lms, query, {
        $addToSet: {
            'category.$.leave_type': [reqBody],
        },
    });
    if (doc.status)
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('LEAVE_TYPE_ADDED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    return res
        .status(410)
        .send(common_files.response_function(res, 410, false, req.t('SERVER_ERROR'), doc.data));
};
exports.update_leave_type = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const lms_check = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            {},
        );
        if (!lms_check.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(res, 200, true, req.t('FAILED_TO_UPDATE'), []),
                );
        // return res.send(lms_check);
        const cat_ind = lms_check.data.category.findIndex((ele) =>
            ele._id.equals(req.params.category_id),
        );
        const leave_type_ind = lms_check.data.category[cat_ind].leave_type.findIndex(
            (ele) =>
                ele &&
                ele.type_name &&
                ele.type_name.toLowerCase() == req.body.type_name.toLowerCase() &&
                ele.isDeleted == false,
        );
        const leave_type_loc = lms_check.data.category[cat_ind].leave_type.findIndex(
            (ele) => ele._id.toString() == req.params.leave_type_id.toString(),
        );
        if (leave_type_ind != -1 && leave_type_loc != leave_type_ind)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('DUPLICATE_FOUND'),
                        req.t('DUPLICATE_FOUND'),
                    ),
                );

        // //Leave Type Duplicate validation
        // // let { ...reqBody } = req.body;
        // let checkQuery = {
        //     // { $not: { $eq: ObjectId(req.params.id) } }
        //     "category._id": ObjectId(req.params.category_id),
        //     // 'category': { $elemMatch: { 'leave_type.type_name': reqBody.type_name, isDeleted: false } }, isDeleted: false,
        //     // 'category': { $not: { $elemMatch: { 'leave_type._id': ObjectId(req.params.leave_type_id) } } }
        // }
        // let checkDoc = await base_control.get(lms, checkQuery);
        // return res.send(checkDoc);
        // if (checkDoc.status) {
        //     let category_loc = checkDoc.data.category.findIndex(i => (i._id).toString() == (req.params.category_id).toString())
        //     if (category_loc != -1)
        //         checkDoc.data.category[category_loc].leave_type.forEach(element => {
        //             // console.log
        //             if (element.type_name == req.body.type_name && (element._id).toString() != (req.params.leave_type_id).toString())
        //                 return res.status(200).send(common_files.response_function(res, 200, true, "Duplicate Leave type", req.t('DUPLICATE_LEAVE_TYPE')));
        //         });
        // }
        // console.log('check');
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const obj = {
            $set: {
                'category.$[i].leave_type.$[j].type_name': req.body.type_name,
                'category.$[i].leave_type.$[j].desc': req.body.desc,
                'category.$[i].leave_type.$[j].gender': req.body.gender,
                'category.$[i].leave_type.$[j].payment': req.body.payment,
                'category.$[i].leave_type.$[j].entitlement': req.body.entitlement,
                'category.$[i].leave_type.$[j].no_of_days': req.body.no_of_days,
                'category.$[i].leave_type.$[j].per_month': req.body.per_month,
                'category.$[i].leave_type.$[j].weekend_consideration':
                    req.body.weekend_consideration,
                'category.$[i].leave_type.$[j].is_reason_required': req.body.is_reason_required,
                'category.$[i].leave_type.$[j].is_attachment_required':
                    req.body.is_attachment_required,
            },
        };
        const filter = {
            arrayFilters: [
                { 'i._id': req.params.category_id },
                { 'j._id': req.params.leave_type_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(lms, query, obj, filter);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('FAILED_TO_UPDATE'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.activate_deactivate_leave_type = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const obj = {
            $set: {
                'category.$[i].leave_type.$[j].isActive': req.body.isActive,
            },
        };
        const filter = {
            arrayFilters: [
                { 'i._id': req.params.category_id },
                { 'j._id': req.params.leave_type_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(lms, query, obj, filter);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('FAILED_TO_UPDATE'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update_settings = async (req, res) => {
    const settings_id = req.params.id;
    const checkQuery = { _id: ObjectId(settings_id) };
    const checkDoc = await base_control.get(lms, checkQuery);
    if (checkDoc.status) {
        const query = { _id: ObjectId(settings_id) };
        const doc = await base_control.update(lms, query, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, req.t('UPDATED_SUCCESSFULLY'));
        } else {
            common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
        }
    } else {
        res.status(404).send(
            common_files.response_function(
                res,
                404,
                false,
                req.t('LEAVE_SETTINGS_NOT_FOUND'),
                req.t('LEAVE_SETTINGS_NOT_FOUND'),
            ),
        );
    }
};

exports.update = async (req, res) => {
    const category_id = req.params.id;
    const leave_type_id = req.params.typeid;
    const body = req.body;
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const checkQuery = {
        category: { $elemMatch: { _id: category_id, 'leave_type._id': leave_type_id } },
    };
    const checkDoc = await base_control.get(lms, checkQuery);
    if (checkDoc.status) {
        const query = {
            category: { $elemMatch: { _id: category_id, 'leave_type._id': req.params.typeid } },
        };
        const arr_filter = {
            arrayFilters: [
                { 'outer._id': ObjectId(req.params.id) },
                { 'inner._id': ObjectId(req.params.typeid) },
            ],
        };
        const { leave_type, ...category_body } = body;
        const setObj = {};
        for (const key in leave_type) {
            setObj[`category.$[outer].leave_type.$[inner].${key}`] = leave_type[key];
        }
        for (const key in category_body) {
            setObj[`category.$[outer].${key}`] = category_body[key];
        }
        const doc = await base_control.update_condition_array_filter(
            lms,
            query,
            {
                $set: setObj,
            },
            arr_filter,
        );
        if (doc.status) {
            common_files.com_response(res, 201, true, req.t('UPDATED_SUCCESSFULLY'));
        } else {
            common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
        }
    } else {
        res.status(404).send(
            common_files.response_function(
                res,
                404,
                false,
                req.t('CATEGORY_LEVEL_TYPE_NOT_FOUND'),
                req.t('CATEGORY_LEVEL_TYPE_NOT_FOUND'),
            ),
        );
    }
};

exports.set_permissions = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    let doc;
    if (lms_check.status) {
        // console.log(lms_check.data.category.findIndex(i => i.category_type == req.body.category_type));
        // return res.send(lms_check);
        // console.log(lms_check.data.category.findIndex(i => (i.category_type == req.body.category_type) && (i.category_to == req.body.to)));
        if (
            lms_check.data.category.findIndex(
                (i) => i.category_type == req.body.category_type && i.category_to == req.body.to,
            ) != -1
        ) {
            const query = {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
            const objs = {
                $set: {
                    'category.$[i].category_type': req.body.category_type,
                    'category.$[i].permission': {
                        permission_hours: req.body.permission_hours,
                        permission_frequency: req.body.permission_frequency,
                        permission_frequency_by: req.body.permission_frequency_by,
                        is_scheduled: req.body.is_scheduled,
                        is_permission_attachment_required:
                            req.body.is_permission_attachment_required,
                    },
                },
            };
            const filter = {
                arrayFilters: [
                    { 'i.category_to': req.body.to, 'i.category_type': req.body.category_type },
                ],
            };
            doc = await base_control.update_condition_array_filter(lms, query, objs, filter);
        } else {
            const query = {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
            const objs = {
                $push: {
                    category: {
                        category_to: req.body.to,
                        category_type: req.body.category_type,
                        permission: {
                            permission_hours: req.body.permission_hours,
                            permission_frequency: req.body.permission_frequency,
                            permission_frequency_by: req.body.permission_frequency_by,
                            is_scheduled: req.body.is_scheduled,
                            is_permission_attachment_required:
                                req.body.is_permission_attachment_required,
                        },
                    },
                },
            };
            doc = await base_control.update_condition(lms, query, objs);
        }
    } else {
        const objs = {
            _institution_id: ObjectId(req.headers._institution_id),
            category: [
                {
                    category_to: req.body.to,
                    category_type: req.body.category_type,
                    permission: {
                        permission_hours: req.body.permission_hours,
                        permission_frequency: req.body.permission_frequency,
                        permission_frequency_by: req.body.permission_frequency_by,
                        is_scheduled: req.body.is_scheduled,
                        is_permission_attachment_required:
                            req.body.is_permission_attachment_required,
                    },
                },
            ],
        };
        doc = await base_control.insert(lms, objs);
    }
    if (doc.status) {
        res.status(200).send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('PERMISSION_UPDATED'),
                doc.data,
            ),
        );
    } else {
        res.status(410).send(
            common_files.responseFunctionWithRequest(
                req,
                410,
                false,
                req.t('SERVER_ERROR'),
                doc.data,
            ),
        );
    }
};

exports.list_permissions = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    const doc = [];
    if (lms_check.status) {
        const user_loc = lms_check.data.category.findIndex(
            (i) => i.category_to == req.params.to && i.category_type == 'permission',
        );
        if (user_loc != -1) {
            const objs = {
                ...lms_check.data.category[user_loc].permission.toObject(),
                report_absence_document: lms_check.data.report_absence_document,
            };
            doc.push(objs);
        }
    }
    res.status(200).send(
        common_files.responseFunctionWithRequest(req, 200, true, req.t('PERMISSION_LIST'), doc),
    );
};

exports.set_hr = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    let doc;
    if (lms_check.status) {
        const query = {
            _id: ObjectId(lms_check.data._id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const objs = {
            $set: { hr_contact: req.body.hr_contact },
        };
        doc = await base_control.update_condition(lms, query, objs);
    } else {
        const objs = {
            _institution_id: ObjectId(req.headers._institution_id),
            hr_contact: req.body.hr_contact,
        };
        doc = await base_control.insert(lms, objs);
    }
    if (doc.status) {
        res.status(200).send(
            common_files.response_function(res, 200, true, req.t('HR_CONTACT_UPDATED'), doc.data),
        );
    } else {
        res.status(404).send(
            common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), doc.data),
        );
    }
};

exports.list_hr = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const lms_check = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    const doc = [];
    if (lms_check.status) {
        if (lms_check.data.hr_contact) doc.push(lms_check.data.hr_contact);
    }
    res.status(200).send(common_files.response_function(res, 200, true, req.t('HR_CONTACT'), doc));
};

exports.insert_student_warning_absence_calculation = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const lms_check = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            { student_warning_absence_calculation: 1 },
        );
        let doc;
        if (lms_check.status) {
            // Duplicate Check
            const dupCheck = lms_check.data.student_warning_absence_calculation
                ? lms_check.data.student_warning_absence_calculation.find(
                      (ele) =>
                          ele &&
                          ele.warning &&
                          ele.warning === req.body.warning &&
                          ele.isDeleted === false,
                  )
                : false;
            if (dupCheck)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('DUPLICATE_WARNING'),
                            req.t('DUPLICATE_WARNING'),
                        ),
                    );

            const query = {
                _id: ObjectId(lms_check.data._id),
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
            const objs = {
                $push: {
                    student_warning_absence_calculation: {
                        warning: req.body.warning,
                        absence_percentage: req.body.absence_percentage,
                        // conducted_session_percentage: req.body.conducted_session_percentage,
                        warning_message: req.body.warning_message,
                    },
                },
            };
            doc = await base_control.update_condition(lms, query, objs);
        } else {
            const objs = {
                _institution_id: ObjectId(req.headers._institution_id),
                student_warning_absence_calculation: {
                    warning: req.body.warning,
                    absence_percentage: req.body.absence_percentage,
                    // conducted_session_percentage: req.body.conducted_session_percentage,
                    warning_message: req.body.warning_message,
                },
            };
            doc = await base_control.insert(lms, objs);
        }
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('ADDED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.update_student_warning_absence_calculation = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // Duplicate Check
        const { status: dupCheckStatus, data: lmsData } = await base_control.get(
            lms,
            {},
            { student_warning_absence_calculation: 1 },
        );
        if (dupCheckStatus) {
            const dupCheck = lmsData.student_warning_absence_calculation
                ? lmsData.student_warning_absence_calculation.find(
                      (ele) =>
                          ele &&
                          ele.warning &&
                          ele.warning === req.body.warning &&
                          ele.isDeleted === false &&
                          ele._id.toString() !== req.params.sub_id.toString(),
                  )
                : false;
            if (dupCheck)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('DUPLICATE_WARNING'),
                            req.t('DUPLICATE_WARNING'),
                        ),
                    );
        }
        const query = {
            _id: ObjectId(req.params.master_id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const objs = {
            $set: {
                'student_warning_absence_calculation.$[i].warning': req.body.warning,
                'student_warning_absence_calculation.$[i].absence_percentage':
                    req.body.absence_percentage,
                // 'student_warning_absence_calculation.$[i].conducted_session_percentage':
                //     req.body.conducted_session_percentage,
                'student_warning_absence_calculation.$[i].warning_message':
                    req.body.warning_message,
            },
        };
        const filter = {
            arrayFilters: [{ 'i._id': ObjectId(req.params.sub_id) }],
        };
        doc = await base_control.update_condition_array_filter(lms, query, objs, filter);

        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.get_student_warning_absence_calculation = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await base_control.get(lms, { _id: ObjectId(req.params.id), isDeleted: false });
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, req.t('NO_RECORDS'), []));

        const new_array = [];
        doc.data.student_warning_absence_calculation.forEach((element) => {
            if (element.isDeleted == false) new_array.push(element);
        });
        if (new_array.length == 0)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, req.t('NO_RECORDS'), []));
        doc.data.student_warning_absence_calculation = new_array;
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENT_WARNING_ANDABSENCE_CALCULATION_LIST'),
                    doc.data.student_warning_absence_calculation,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.delete_student_warning_absence_calculation = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        let doc = await base_control.get(lms, {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        });
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, req.t('NO_RECORDS'), []));

        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const objs = {
            $set: {
                'student_warning_absence_calculation.$[i].isDeleted': true,
            },
        };
        const filter = {
            arrayFilters: [{ 'i._id': ObjectId(req.params.id) }],
        };
        doc = await base_control.update_condition_array_filter(lms, query, objs, filter);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_DELETE'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('DELETED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.insert_lms_roles = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const lms_check = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            {},
        );
        let doc;
        let objs = {};
        if (lms_check.status) {
            const query = {
                _id: ObjectId(lms_check.data._id),
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
            if (req.params.role_type == constant.LMS_ROLE_TYPE.REVIEWER) {
                if (req.body.roles_data.length == 2) {
                    objs = {
                        $push: {
                            reviewer: {
                                $each: [
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[0].dean,
                                            vice_dean: req.body.roles_data[0].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[0].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[0].reporting_manager,
                                            custom: req.body.roles_data[0].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[1].dean,
                                            vice_dean: req.body.roles_data[1].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[1].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[1].reporting_manager,
                                            custom: req.body.roles_data[1].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                ],
                            },
                        },
                    };
                } else {
                    objs = {
                        $push: {
                            reviewer: {
                                staff_type: req.body.staff_type,
                                roles: {
                                    dean: req.body.roles_data[0].dean,
                                    vice_dean: req.body.roles_data[0].vice_dean,
                                    department_chairman: req.body.roles_data[0].department_chairman,
                                    reporting_manager: req.body.roles_data[0].reporting_manager,
                                    custom: req.body.roles_data[0].custom,
                                },
                                user_ids: req.body.user_ids,
                            },
                        },
                    };
                }
            }
            if (req.params.role_type == constant.LMS_ROLE_TYPE.FORWARDER) {
                if (req.body.roles_data.length == 2) {
                    objs = {
                        $push: {
                            forwarder: {
                                $each: [
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[0].dean,
                                            vice_dean: req.body.roles_data[0].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[0].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[0].reporting_manager,
                                            custom: req.body.roles_data[0].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[1].dean,
                                            vice_dean: req.body.roles_data[1].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[1].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[1].reporting_manager,
                                            custom: req.body.roles_data[1].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                ],
                            },
                        },
                    };
                } else {
                    objs = {
                        $push: {
                            forwarder: {
                                staff_type: req.body.staff_type,
                                roles: {
                                    dean: req.body.roles_data[0].dean,
                                    vice_dean: req.body.roles_data[0].vice_dean,
                                    department_chairman: req.body.roles_data[0].department_chairman,
                                    reporting_manager: req.body.roles_data[0].reporting_manager,
                                    custom: req.body.roles_data[0].custom,
                                },
                                user_ids: req.body.user_ids,
                            },
                        },
                    };
                }
            }
            if (req.params.role_type == constant.LMS_ROLE_TYPE.APPROVER) {
                if (req.body.roles_data.length == 2) {
                    objs = {
                        $push: {
                            approver: {
                                $each: [
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[0].dean,
                                            vice_dean: req.body.roles_data[0].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[0].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[0].reporting_manager,
                                            custom: req.body.roles_data[0].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[1].dean,
                                            vice_dean: req.body.roles_data[1].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[1].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[1].reporting_manager,
                                            custom: req.body.roles_data[1].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                ],
                            },
                        },
                    };
                } else {
                    objs = {
                        $push: {
                            approver: {
                                staff_type: req.body.staff_type,
                                roles: {
                                    dean: req.body.roles_data[0].dean,
                                    vice_dean: req.body.roles_data[0].vice_dean,
                                    department_chairman: req.body.roles_data[0].department_chairman,
                                    reporting_manager: req.body.roles_data[0].reporting_manager,
                                    custom: req.body.roles_data[0].custom,
                                },
                                user_ids: req.body.user_ids,
                            },
                        },
                    };
                }
            }
            if (req.params.role_type == constant.LMS_ROLE_TYPE.REPORT_ABSENCE) {
                if (req.body.roles_data.length == 2) {
                    objs = {
                        $push: {
                            report_absence: {
                                $each: [
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[0].dean,
                                            vice_dean: req.body.roles_data[0].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[0].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[0].reporting_manager,
                                            custom: req.body.roles_data[0].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                    {
                                        staff_type: req.body.staff_type,
                                        roles: {
                                            dean: req.body.roles_data[1].dean,
                                            vice_dean: req.body.roles_data[1].vice_dean,
                                            department_chairman:
                                                req.body.roles_data[1].department_chairman,
                                            reporting_manager:
                                                req.body.roles_data[1].reporting_manager,
                                            custom: req.body.roles_data[1].custom,
                                        },
                                        user_ids: req.body.user_ids,
                                    },
                                ],
                            },
                        },
                    };
                } else {
                    objs = {
                        $push: {
                            report_absence: {
                                staff_type: req.body.staff_type,
                                roles: {
                                    dean: req.body.roles_data[0].dean,
                                    vice_dean: req.body.roles_data[0].vice_dean,
                                    department_chairman: req.body.roles_data[0].department_chairman,
                                    reporting_manager: req.body.roles_data[0].reporting_manager,
                                    custom: req.body.roles_data[0].custom,
                                },
                                user_ids: req.body.user_ids,
                            },
                        },
                    };
                }
            }
            doc = await base_control.update_condition(lms, query, objs);
        } else {
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_DATA_FOUND'),
                        req.t('NO_DATA_FOUND'),
                    ),
                );
        }
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('ADDED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.lms_roles_set = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
    const obj = { $push: { reviewer_forwarder_approver: [req.body] } };
    const doc = await base_control.update_condition(lms, query, obj);
    return res.send(doc);
};

exports.lms_roles_assign = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
    const lms_data = await base_control.get(lms, query, {});

    req.body.data.forEach(async (element, index) => {
        const role_loc = lms_data.data.reviewer_forwarder_approver.findIndex(
            (i) => i._id.toString() == element._id.toString(),
        );
        const roles = lms_data.data.reviewer_forwarder_approver[role_loc].roles;
        const role_data = [];
        let user_datas = [];
        roles.forEach(async (sub_element) => {
            if (element.isActive && sub_element._id.toString() == element._role_id.toString()) {
                role_data.push({
                    _id: sub_element._id,
                    role: sub_element.role,
                    status: true,
                });
                if (element.user_id) user_datas = element.user_id;
            } else {
                role_data.push({
                    _id: sub_element._id,
                    role: sub_element.role,
                    status: false,
                });
            }
        });
        const filter = { arrayFilters: [{ 'i._id': element._id }] };
        let obj;
        if (element.isActive) {
            obj = {
                $set: {
                    'reviewer_forwarder_approver.$[i].isActive': true,
                    'reviewer_forwarder_approver.$[i].user_ids': user_datas,
                    'reviewer_forwarder_approver.$[i].roles': role_data,
                },
            };
        } else {
            obj = {
                $set: {
                    'reviewer_forwarder_approver.$[i].isActive': false,
                    'reviewer_forwarder_approver.$[i].roles': role_data,
                    'reviewer_forwarder_approver.$[i].user_ids': user_datas,
                },
            };
        }
        const doc = await base_control.update_condition_array_filter(lms, query, obj, filter);
        if (req.body.data.length == index + 1) {
            if (doc.status)
                return res
                    .status(200)
                    .send(
                        common_files.response_function(
                            res,
                            200,
                            true,
                            req.t('STAFF_LMS_ROLES_ARE_ASSIGNED'),
                            doc.data,
                        ),
                    );
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_SET_STAFF_LMS_ROLES'),
                        doc.data,
                    ),
                );
        }
    });
};

exports.lms_approver_list = async (req, res) => {
    try {
        // let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
        // if (!institution_check.status) return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", req.t('INSTITUTION_NOT_FOUND')));
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const project = { leave_approval: 1 };
        // let doc = await base_control.get(lms, query, project);
        const populate = { path: 'leave_approval.role._roles_id', select: { name: 1 } };
        const doc = await base_control.get_list_populate(lms, query, project, populate);

        if (doc.status) {
            const approvers = [];
            doc.data[0].leave_approval.forEach((element) => {
                if (element.approver_association != 'Others') {
                    const role = {
                        _roles_id: element.role._roles_id._id,
                        role_name: element.role._roles_id.name,
                    };
                    // console.log(element.role)
                    element.role = role;
                }
                approvers.push(element);
            });
            doc.data[0].leave_approval = approvers;
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(req, 200, true, 'List', doc.data[0]),
                );
            // return res.status(200).send(common_files.response_function(res, 200, true, "List", doc.data));
        }
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('SERVER_ERROR'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.lms_approver_add = async (req, res) => {
    try {
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const project = { leave_approval: 1 };
        const lms_datas = await base_control.get(lms, query, project);
        if (!lms_datas.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_SETTINGS'),
                        req.t('UNABLE_TO_FIND_SETTINGS'),
                    ),
                );
        let level_no = 0;

        if (req.body.staff_type.toString() == 'both') {
            for (element of lms_datas.data.leave_approval) {
                if (element.staff_type.toString() != req.body.staff_type.toString())
                    return res
                        .status(410)
                        .send(
                            common_files.response_function(
                                res,
                                410,
                                false,
                                req.t('STAFF_TYPE_HAS_TO_BE_UNIQUE_IN_APPROVERS'),
                                req.t('STAFF_TYPE_HAS_TO_BE_UNIQUE_IN_APPROVERS'),
                            ),
                        );
            }
        }

        if (req.body.approver_association != 'Others' && req.body._roles_id) {
            for (element of lms_datas.data.leave_approval) {
                if (
                    element.staff_type.toString() == req.body.staff_type.toString() &&
                    element.role._roles_id &&
                    element.role._roles_id.toString() == req.body._roles_id.toString()
                ) {
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_ROLE_IN_STAFF_TYPE'),
                                req.t('DUPLICATE_ROLE_IN_STAFF_TYPE'),
                            ),
                        );
                }
            }
            // let check = lms_datas.data.leave_approval.findIndex(i => (i.staff_type).toString() == (req.body.staff_type).toString() && (i.role._roles_id).toString() == (req.body._roles_id).toString())
            // if (check != -1) return res.status(409).send(common_files.response_function(res, 409, false, "Duplicate Role in Staff type", req.t('DUPLICATE_ROLE_IN_STAFF_TYPE')));
        } else if (req.body._staff_id && req.body._staff_id.length != 0) {
            for (element of lms_datas.data.leave_approval) {
                if (
                    element.staff_type.toString() == req.body.staff_type.toString() &&
                    element._staff_id &&
                    element._staff_id.toString() == req.body._staff_id.toString()
                ) {
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_STAFF_IN_STAFF_TYPE'),
                                req.t('DUPLICATE_STAFF_IN_STAFF_TYPE'),
                            ),
                        );
                }
            }
        }

        lms_datas.data.leave_approval.forEach((element) => {
            if (element.staff_type.toString() == req.body.staff_type.toString()) {
                level_no = element.level_no > level_no ? element.level_no : level_no;
            }
        });
        const data = {
            staff_type: req.body.staff_type,
            // _staff_id: req.body._staff_id,
            staff_name: req.body.staff_name,
            level_no: ++level_no,
            approver_level_name: req.body.approver_level_name,
            approver_association: req.body.approver_association,
            // role: {
            //     _roles_id: req.body._roles_id,
            //     role_name: req.body.role_name
            // }
        };
        if (req.body.approver_association != 'Others' && req.body._roles_id) {
            //Getting Role Modules data and assigning Leave Module to That role
            const role_data = await base_control.get(
                role,
                { _id: ObjectId(req.body._roles_id) },
                {},
            );
            if (role_data.status) {
                const module_loc = role_data.data.modules.findIndex(
                    (i) => i.name == 'Leave Management',
                );
                if (module_loc != -1) {
                    const page_loc = role_data.data.modules[module_loc].pages.findIndex(
                        (i) => i.name == 'Approve Leave',
                    );
                    if (page_loc == -1) {
                        role_data.data.modules[module_loc].pages = [
                            ...role_data.data.modules[module_loc].pages,
                            ...lms_data().pages,
                        ];
                        // role_data.data.modules[module_loc].pages.push(lms_data().pages[0]);
                    } else role_data.data.modules[module_loc].pages[page_loc] = lms_data().pages[0];
                } else {
                    role_data.data.modules.push(lms_data());
                }
                const objs = { $set: { modules: role_data.data.modules } };
                const role_modules = await base_control.update_condition(
                    role,
                    { _id: ObjectId(req.body._roles_id) },
                    objs,
                );
                console.log(role_modules);
            }
            data.role = {
                _roles_id: req.body._roles_id,
                role_name: req.body.role_name,
            };
        } else {
            //Add Leave Approver Role to User
            data._staff_id = req.body._staff_id;
            //Creating Role and assigning to User
            const users_check = await base_control.get(
                user,
                {
                    _id: ObjectId(req.body._staff_id),
                    isDeleted: false,
                    isActive: true,
                },
                { name: 1 },
            );
            if (!users_check.status)
                return res
                    .status(410)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('USER_NOT_FOUND'),
                            req.t('USER_NOT_FOUND'),
                        ),
                    );

            const regex = new RegExp(['^Leave Approver$'].join(''), 'i');
            const role_data = await base_control.get(
                role,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    name: regex,
                    isDeleted: false,
                    isActive: true,
                },
                {},
            );
            // return res.send(role_data);
            let roleAssignData = {};
            const role_assign_list = await base_control.get(
                role_assign,
                {
                    _user_id: ObjectId(req.body._staff_id),
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
                {},
            );
            if (!role_data.status) {
                //Create a new Role and add Leave Management is Only module
                const approverRole = {
                    _institution_id: req.headers._institution_id,
                    name: 'Leave Approver',
                    modules: lms_data(),
                };
                const { status: roleCreateStatus, responses: roleCreateData } =
                    await base_control.insert(role, approverRole);
                if (!roleCreateStatus)
                    return res
                        .status(410)
                        .send(
                            common_files.response_function(
                                res,
                                410,
                                false,
                                req.t('ERROR_UNABLE_TO_ADD_STAFF_APPROVER'),
                                req.t('ERROR_UNABLE_TO_ADD_STAFF_APPROVER'),
                            ),
                        );
                roleAssignData = {
                    _role_id: roleCreateData._id,
                    role_name: roleCreateData.name,
                };
            } else {
                roleAssignData = {
                    _role_id: role_data.data._id,
                    role_name: role_data.data.name,
                };
            }
            const objs = {
                _institution_id: req.headers._institution_id,
                _user_id: req.body._staff_id,
                user_name: users_check.data.name,
                roles: [
                    {
                        _role_id: roleAssignData._role_id,
                        role_name: roleAssignData.role_name,
                        program: [],
                        department: [],
                        isDefault: false,
                        isActive: true,
                    },
                ],
            };
            // return res.send({ roleAssignData, objs });
            let doc;
            if (!role_assign_list.status) {
                doc = await base_control.insert(role_assign, objs);
                console.log(doc);
                await base_control.update(user, ObjectId(req.body._staff_id), {
                    _role_id: ObjectId(doc.responses._id),
                });
            } else {
                role_assign_list.data.roles.push({
                    _role_id: ObjectId(roleAssignData._role_id),
                    role_name: roleAssignData.role_name,
                    program: [],
                    department: [],
                });
                console.log(role_assign_list);
                doc = await base_control.update_condition(
                    role_assign,
                    { _user_id: ObjectId(req.body._staff_id) },
                    { $set: role_assign_list.data },
                );
            }
            if (!doc.status)
                return res
                    .status(410)
                    .send(
                        common_files.response_function(
                            res,
                            410,
                            false,
                            req.t('UNABLE_TO_ASSIGN_ROLE_TO_USER'),
                            doc.data,
                        ),
                    );
        }
        // console.log(data);
        const obj = { $push: { leave_approval: data } };
        const doc = await base_control.update_condition(lms, query, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('STAFF_APPROVER_ADDED'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_ADD_STAFF_APPROVER'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.lms_approver_edit = async (req, res) => {
    try {
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const project = { leave_approval: 1 };
        const lms_datas = await base_control.get(lms, query, project);

        if (req.body.staff_type.toString() == 'both') {
            for (element of lms_datas.data.leave_approval) {
                if (element.staff_type.toString() != req.body.staff_type.toString())
                    return res
                        .status(410)
                        .send(
                            common_files.response_function(
                                res,
                                410,
                                false,
                                req.t('STAFF_TYPE_HAS_TO_BE_UNIQUE_IN_APPROVERS'),
                                req.t('STAFF_TYPE_HAS_TO_BE_UNIQUE_IN_APPROVERS'),
                            ),
                        );
            }
        }

        if (req.body.approver_association != 'Others' && req.body._roles_id) {
            for (element of lms_datas.data.leave_approval) {
                if (
                    element._id.toString() != req.params.id.toString() &&
                    element.staff_type.toString() == req.body.staff_type.toString() &&
                    element.role._roles_id &&
                    element.role._roles_id.toString() == req.body._roles_id.toString()
                ) {
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_ROLE_IN_STAFF_TYPE'),
                                req.t('DUPLICATE_ROLE_IN_STAFF_TYPE'),
                            ),
                        );
                }
            }
        } else if (req.body._staff_id && req.body._staff_id.length != 0) {
            for (element of lms_datas.data.leave_approval) {
                if (
                    element._id.toString() != req.params.id.toString() &&
                    element.staff_type.toString() == req.body.staff_type.toString() &&
                    element._staff_id &&
                    element._staff_id.toString() == req.body._staff_id.toString()
                ) {
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_STAFF_IN_STAFF_TYPE'),
                                req.t('DUPLICATE_STAFF_IN_STAFF_TYPE'),
                            ),
                        );
                }
            }
        }
        // Remove Leave Approve Permission for Role Or User
        // const lmsQuery = {
        //     _institution_id: ObjectId(req.headers._institution_id),
        //     isDeleted: false,
        // };
        // const lmsProject = { leave_approval: 1 };
        // const lms_datas = await base_control.get(lms, lmsQuery, lmsProject);
        // if (!lms_datas.status)
        //     return res
        //         .status(404)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 404,
        //                 false,
        //                 'Unable to Find LMS Settings',
        //                 'Unable to Find LMS Settings',
        //             ),
        //         );
        const approver_loc = lms_datas.data.leave_approval.findIndex(
            (i) => i._id.toString() == req.params.id.toString(),
        );
        if (approver_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_APPROVER'),
                        req.t('UNABLE_TO_FIND_APPROVER'),
                    ),
                );
        //Removing Leave Approver Permission from the Role
        const approver_data = lms_datas.data.leave_approval[approver_loc];
        if (approver_data.approver_association !== 'Others' && approver_data.role._roles_id) {
            //Getting Role Modules data and assigning Leave Module to That role
            const role_data = await base_control.get(
                role,
                { _id: ObjectId(approver_data.role._roles_id) },
                {},
            );
            if (role_data.status) {
                const module_loc = role_data.data.modules.findIndex(
                    (i) => i.name == 'Leave Management',
                );
                if (module_loc != -1) {
                    const page_loc = role_data.data.modules[module_loc].pages.findIndex(
                        (i) => i.name == 'Approve Leave',
                    );
                    if (page_loc != -1) role_data.data.modules[module_loc].pages.splice(page_loc);
                    if (role_data.data.modules[module_loc].pages.length == 0)
                        role_data.data.modules.splice(module_loc);
                }
                const objs = { $set: { modules: role_data.data.modules } };
                /* const role_modules =  */ await base_control.update_condition(
                    role,
                    { _id: ObjectId(approver_data.role._roles_id) },
                    objs,
                );
                // console.log(role_modules);
            }
        } else {
            //Remove User Leave Approver Role
            const role_assign_list = await base_control.get(
                role_assign,
                {
                    _user_id: ObjectId(approver_data._staff_id),
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
                {},
            );
            if (!role_assign_list.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('USER_ROLE_NOT_FOUND'),
                            req.t('USER_ROLE_NOT_FOUND'),
                        ),
                    );
            const loc = role_assign_list.data.roles.findIndex(
                (ele) => ele.role_name === 'Leave Approver',
            );
            if (loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('USERS_LEAVE_ROLE_NOT_FOUND'),
                            req.t('USERS_LEAVE_ROLE_NOT_FOUND'),
                        ),
                    );
            role_assign_list.data.roles.splice(loc, 1);
            /* const roleRemove = */ await base_control.update_condition(
                role_assign,
                {
                    _user_id: ObjectId(approver_data._staff_id),
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
                { $set: { roles: role_assign_list.data.roles } },
            );
            // console.log(roleRemove);
        }
        const data = {
            $set: {
                'leave_approval.$[i].staff_type': req.body.staff_type,
                'leave_approval.$[i].staff_name': req.body.staff_name,
                'leave_approval.$[i]._staff_id': req.body._staff_id,
                'leave_approval.$[i].approver_level_name': req.body.approver_level_name,
                'leave_approval.$[i].approver_association': req.body.approver_association,
                'leave_approval.$[i].role._roles_id': req.body._roles_id,
                'leave_approval.$[i].role.role_name': req.body.role_name,
            },
        };
        const filter = { arrayFilters: [{ 'i._id': req.params.id }] };
        const doc = await base_control.update_condition_array_filter(lms, query, data, filter);
        if (doc.status) {
            let level_no = 0;
            // Adding Approver Permission to Role Or User
            lms_datas.data.leave_approval.forEach((element) => {
                if (element.staff_type.toString() == req.body.staff_type.toString()) {
                    level_no = element.level_no > level_no ? element.level_no : level_no;
                }
            });
            if (req.body.approver_association != 'Others' && req.body._roles_id) {
                //Getting Role Modules data and assigning Leave Module to That role
                const role_data = await base_control.get(
                    role,
                    { _id: ObjectId(req.body._roles_id) },
                    {},
                );
                if (role_data.status) {
                    const module_loc = role_data.data.modules.findIndex(
                        (i) => i.name == 'Leave Management',
                    );
                    if (module_loc != -1) {
                        const page_loc = role_data.data.modules[module_loc].pages.findIndex(
                            (i) => i.name == 'Approve Leave',
                        );
                        if (page_loc == -1) {
                            role_data.data.modules[module_loc].pages = [
                                ...role_data.data.modules[module_loc].pages,
                                ...lms_data().pages,
                            ];
                            // role_data.data.modules[module_loc].pages.push(lms_data().pages[0]);
                        } else
                            role_data.data.modules[module_loc].pages[page_loc] =
                                lms_data().pages[0];
                    } else {
                        role_data.data.modules.push(lms_data());
                    }
                    const objs = { $set: { modules: role_data.data.modules } };
                    const role_modules = await base_control.update_condition(
                        role,
                        { _id: ObjectId(req.body._roles_id) },
                        objs,
                    );
                    console.log(role_modules);
                }
                data.role = {
                    _roles_id: req.body._roles_id,
                    role_name: req.body.role_name,
                };
            } else {
                //Add Leave Approver Role to User
                data._staff_id = req.body._staff_id;
                //Creating Role and assigning to User
                const users_check = await base_control.get(
                    user,
                    {
                        _id: ObjectId(req.body._staff_id),
                        isDeleted: false,
                        isActive: true,
                    },
                    { name: 1 },
                );
                if (!users_check.status)
                    return res
                        .status(410)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('USER_NOT_FOUND'),
                                req.t('USER_NOT_FOUND'),
                            ),
                        );

                const regex = new RegExp(['^Leave Approver$'].join(''), 'i');
                const role_data = await base_control.get(
                    role,
                    {
                        _institution_id: ObjectId(req.headers._institution_id),
                        name: regex,
                        isDeleted: false,
                        isActive: true,
                    },
                    {},
                );
                // return res.send(role_data);
                let roleAssignData = {};
                const role_assign_list = await base_control.get(
                    role_assign,
                    {
                        _user_id: ObjectId(req.body._staff_id),
                        _institution_id: ObjectId(req.headers._institution_id),
                        isDeleted: false,
                    },
                    {},
                );
                if (!role_data.status) {
                    //Create a new Role and add Leave Management is Only module
                    const approverRole = {
                        _institution_id: req.headers._institution_id,
                        name: 'Leave Approver',
                        modules: lms_data(),
                    };
                    const { status: roleCreateStatus, responses: roleCreateData } =
                        await base_control.insert(role, approverRole);
                    if (!roleCreateStatus)
                        return res
                            .status(410)
                            .send(
                                common_files.response_function(
                                    res,
                                    410,
                                    false,
                                    req.t('ERROR_UNABLE_TO_ADD_STAFF_APPROVER'),
                                    req.t('ERROR_UNABLE_TO_ADD_STAFF_APPROVER'),
                                ),
                            );
                    roleAssignData = {
                        _role_id: roleCreateData._id,
                        role_name: roleCreateData.name,
                    };
                } else {
                    roleAssignData = {
                        _role_id: role_data.data._id,
                        role_name: role_data.data.name,
                    };
                }
                const objs = {
                    _institution_id: req.headers._institution_id,
                    _user_id: req.body._staff_id,
                    user_name: users_check.data.name,
                    roles: [
                        {
                            _role_id: roleAssignData._role_id,
                            role_name: roleAssignData.role_name,
                            program: [],
                            department: [],
                            isDefault: false,
                            isActive: true,
                        },
                    ],
                };
                // return res.send({ roleAssignData, objs });
                let doc;
                if (!role_assign_list.status) {
                    doc = await base_control.insert(role_assign, objs);
                    console.log(doc);
                    await base_control.update(user, ObjectId(req.body._staff_id), {
                        _role_id: ObjectId(doc.responses._id),
                    });
                } else {
                    role_assign_list.data.roles.push({
                        _role_id: ObjectId(roleAssignData._role_id),
                        role_name: roleAssignData.role_name,
                        program: [],
                        department: [],
                    });
                    console.log(role_assign_list);
                    doc = await base_control.update_condition(
                        role_assign,
                        { _user_id: ObjectId(req.body._staff_id) },
                        { $set: role_assign_list.data },
                    );
                }
                console.log(doc);
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('STAFF_APPROVER_EDITED'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_EDIT_STAFF_APPROVED'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(410)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.lms_approver_remove = async (req, res) => {
    try {
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        const project = { leave_approval: 1 };
        const lms_data = await base_control.get(lms, query, project);
        if (!lms_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LMS_SETTINGS'),
                        req.t('UNABLE_TO_FIND_LMS_SETTINGS'),
                    ),
                );
        const approver_loc = lms_data.data.leave_approval.findIndex(
            (i) => i._id.toString() == req.params.id.toString(),
        );
        if (approver_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_APPROVER'),
                        req.t('UNABLE_TO_FIND_APPROVER'),
                    ),
                );

        //Removing Leave Approver Permission from the Role
        const approver_data = lms_data.data.leave_approval[approver_loc];
        if (approver_data.approver_association !== 'Others' && approver_data.role._roles_id) {
            //Getting Role Modules data and assigning Leave Module to That role
            const role_data = await base_control.get(
                role,
                { _id: ObjectId(approver_data.role._roles_id) },
                {},
            );
            if (role_data.status) {
                const module_loc = role_data.data.modules.findIndex(
                    (i) => i.name == 'Leave Management',
                );
                if (module_loc != -1) {
                    const page_loc = role_data.data.modules[module_loc].pages.findIndex(
                        (i) => i.name == 'Approve Leave',
                    );
                    if (page_loc != -1) role_data.data.modules[module_loc].pages.splice(page_loc);
                    if (role_data.data.modules[module_loc].pages.length == 0)
                        role_data.data.modules.splice(module_loc);
                }
                const objs = { $set: { modules: role_data.data.modules } };
                /* const role_modules =  */ await base_control.update_condition(
                    role,
                    { _id: ObjectId(approver_data.role._roles_id) },
                    objs,
                );
                // console.log(role_modules);
            }
        } else {
            //Remove User Leave Approver Role
            const role_assign_list = await base_control.get(
                role_assign,
                {
                    _user_id: ObjectId(approver_data._staff_id),
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
                {},
            );
            if (!role_assign_list.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('USER_ROLE_NOT_FOUND'),
                            req.t('USER_ROLE_NOT_FOUND'),
                        ),
                    );
            const loc = role_assign_list.data.roles.findIndex(
                (ele) => ele.role_name === 'Leave Approver',
            );
            if (loc === -1)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            'Users Leave Role not found',
                            'Users Leave Role not found',
                        ),
                    );
            role_assign_list.data.roles.splice(loc, 1);
            /* const roleRemove = */ await base_control.update_condition(
                role_assign,
                {
                    _user_id: ObjectId(approver_data._staff_id),
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
                { $set: { roles: role_assign_list.data.roles } },
            );
            // console.log(roleRemove);
        }

        const other_approvers = [];
        const type_approvers = [];
        const staff_type =
            lms_data.data.leave_approval[
                lms_data.data.leave_approval.findIndex(
                    (i) => i._id.toString() == req.params.id.toString(),
                )
            ].staff_type;
        lms_data.data.leave_approval.forEach((element) => {
            if (element.staff_type.toString() == staff_type.toString()) {
                if (element._id.toString() != req.params.id.toString())
                    type_approvers.push(element);
            } else {
                other_approvers.push(element);
            }
        });
        const sorted = type_approvers.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.level_no) > parseInt(b.level_no)) {
                comparison = 1;
            } else if (parseInt(a.level_no) < parseInt(b.level_no)) {
                comparison = -1;
            }
            return comparison;
        });
        sorted.forEach((element, index) => {
            sorted[index].level_no = index + 1;
        });
        const data = {
            $set: { leave_approval: other_approvers.concat(sorted) },
        };
        const doc = await base_control.update_condition(lms, query, data);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('STAFF_APPROVER_REMOVED'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_REMOVE_STAFF_APPROVER'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(410)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.report_absence = async (req, res) => {
    try {
        console.log(req.body.report_absence_document);
        const lms_update = await base_control.update_condition(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            { $set: { report_absence_document: req.body.report_absence_document } },
        );
        if (lms_update.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('REPORT_ABSENCE_UPDATED'),
                        req.t('REPORT_ABSENCE_UPDATED'),
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_UPDATE_STAFF_REPORT_ABSENCE'),
                    lms_update.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
