const constant = require('../utility/constants');
const institution_calendar_event = require('mongoose').model(constant.CALENDAR_EVENT);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const staff = require('mongoose').model(constant.USER);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const institution_formate = require('./calendar_event_formate');
const ObjectId = common_files.convertToMongoObjectId;
const common_fun = require('../utility/common_functions');
const notification = require('../notification_manager/notification_manager_controller');
const notification_manager = require('../models/notification_manager');
const keys = require('../utility/util_keys');
const { removeAllOldChatChannels } = require('../digi_class/digi_chat/chat_service');

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false, isActive: true } },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_calendar_id',
                foreignField: '_id',
                as: 'institution_calendar',
            },
        },
        { $unwind: '$institution_calendar' },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(institution_calendar_event, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.institution_calendar_event(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_calendar_id',
                foreignField: '_id',
                as: 'institution_calendar',
            },
        },
        { $unwind: '$institution_calendar' },
    ];
    const doc = await base_control.get_aggregate(institution_calendar_event, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "institution_calendar_event details", /* doc.data */institution_formate.institution_calendar_event_ID(doc.data[0]));
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_DETAILS'),
            doc.data[0] /* institution_formate.institution_calendar_event_ID(doc.data[0]) */,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EVENT_NOT_FOUND'),
            doc.data,
        );
    }
};

exports.insert = async (req, res) => {
    //Check before adding same date is already present date
    const checks = await base_control.check_id(institution_calendar, {
        _id: { $in: req.body._calendar_id },
        isDeleted: false,
    });
    if (checks.status) {
        let objs = {};
        // if (req.body.event_name.second_language != undefined && req.body.event_description.second_language != undefined) {
        objs = {
            event_calendar: req.body.event_calendar,
            event_type: req.body.event_type,
            // event_whom: req.body.event_whom,
            event_date: req.body.event_date,
            start_time: req.body.start_time,
            end_time: req.body.end_time,
            end_date: req.body.end_date,
            _calendar_id: req.body._calendar_id,
            status: 'pending',
            isActive: false,
        };

        if (req.body.event_whom != undefined) {
            Object.assign(objs, { event_whom: req.body.event_whom });
        }
        if (req.body.event_gender != undefined) {
            Object.assign(objs, { event_gender: req.body.event_gender });
        }

        if (req.body.event_name != undefined) {
            if (req.body.event_name.first_language != undefined) {
                Object.assign(objs, {
                    'event_name.first_language': req.body.event_name.first_language,
                });
            }
            if (req.body.event_name.second_language != undefined) {
                Object.assign(objs, {
                    'event_name.second_language': req.body.event_name.second_language,
                });
            }
        }
        if (req.body.event_description != undefined) {
            if (req.body.event_description.first_language != undefined) {
                Object.assign(objs, {
                    'event_description.first_language': req.body.event_description.first_language,
                });
            }
            if (req.body.event_description.second_language != undefined) {
                Object.assign(objs, {
                    'event_description.second_language': req.body.event_description.second_language,
                });
            }
        }

        if (req.body._infrastructure_id != undefined && req.body._infrastructure_id.length == 24) {
            objs._infrastructure_id = req.body._infrastructure_id;
        }
        // console.log(objs);
        // let doc = { status: false, data: '' }
        const list = await base_control.get(
            institution_calendar_event,
            {
                _calendar_id: req.body._calendar_id,
                event_calendar: req.body.event_calendar,
                isDeleted: false,
                event_date: req.body.event_date,
            },
            { _id: 1 },
        );
        if (!list.status) {
            const doc = await base_control.insert(institution_calendar_event, objs);
            if (doc.status) {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    201,
                    true,
                    req.t('INSTITUTION_CALENDAR_EVENT_ADDED_SUCCESSFULLY'),
                    doc.data,
                );
            } else {
                common_files.comResponseWithRequest(
                    req,
                    res,
                    500,
                    false,
                    req.t('UNABLE_TO_ADD_EVENT'),
                    doc.data,
                );
            }
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('THERE_IS_AN_EVENT_IN_SAME_DATE'),
                req.t('THERE_IS_AN_EVENT_IN_SAME_DATE'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.update = async (req, res) => {
    let checks = { status: true };
    if (req.body._calendar_id != undefined) {
        checks = await base_control.check_id(institution_calendar, {
            _id: { $in: req.body._calendar_id },
            isDeleted: false,
        });
    }
    if (checks.status) {
        const object_id = req.params.id;
        const objs = {};
        if (req.body.event_type != undefined) {
            Object.assign(objs, { event_type: req.body.event_type });
        }
        if (req.body.event_whom != undefined) {
            Object.assign(objs, { event_whom: req.body.event_whom });
        }
        if (req.body.event_name != undefined) {
            if (req.body.event_name.first_language != undefined) {
                Object.assign(objs, {
                    'event_name.first_language': req.body.event_name.first_language,
                });
            }
            if (req.body.event_name.second_language != undefined) {
                Object.assign(objs, {
                    'event_name.second_language': req.body.event_name.second_language,
                });
            }
        }
        if (req.body.event_description != undefined) {
            if (req.body.event_description.first_language != undefined) {
                Object.assign(objs, {
                    'event_description.first_language': req.body.event_description.first_language,
                });
            }
            if (req.body.event_description.second_language != undefined) {
                Object.assign(objs, {
                    'event_description.second_language': req.body.event_description.second_language,
                });
            }
        }
        if (req.body.event_date != undefined) {
            Object.assign(objs, { event_date: req.body.event_date });
        }
        if (req.body.start_time != undefined) {
            Object.assign(objs, { start_time: req.body.start_time });
        }
        if (req.body.end_time != undefined) {
            Object.assign(objs, { end_time: req.body.end_time });
        }
        if (req.body.end_date != undefined) {
            Object.assign(objs, { end_date: req.body.end_date });
        }
        if (req.body._infrastructure_id != undefined && req.body._infrastructure_id.length == 24) {
            Object.assign(objs, { _infrastructure_id: req.body._infrastructure_id });
        }
        // console.log(object_id, objs);
        const doc = await base_control.update(institution_calendar_event, object_id, objs);
        // console.log(doc);
        if (doc.status) {
            common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('INSTITUTION_CALENDAR_EVENT_UPDATE_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.delete = async (req, res) => {
    const object_id = req.params.id;
    const doc = await base_control.delete(institution_calendar_event, object_id);
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            201,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_DELETED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }

        const doc = await base_control.get_list(institution_calendar_event, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
                institution_formate.institution_calendar_event_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
        );
    }
};

exports.list_calendar_event = async (req, res) => {
    let aggre = [];
    let skips;
    let limits;
    let id = req.params.id;
    if (req.query.limit != undefined || req.query.pageNo != undefined) {
        skips = Number(req.query.limit * (req.query.pageNo - 1));
        limits = Number(req.query.limit) || 0;
        id = req.params.id;
        aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            { $match: { event_calendar: 'institution' } },
            { $match: { isDeleted: false /* isActive: true */ } },
            { $sort: { event_date: 1 } },
            { $skip: skips },
            { $limit: limits },
        ];
    } else {
        aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            { $match: { event_calendar: 'institution' } },
            { $match: { isDeleted: false /* isActive: true */ } },
            { $sort: { event_date: 1 } },
        ];
    }
    const query = {
        _calendar_id: ObjectId(id),
        event_calendar: 'institution',
        isDeleted: false,
        isActive: true,
    };
    const doc = await base_control.get_aggregate_with_id_match(
        institution_calendar_event,
        aggre,
        query,
    );
    if (doc.status) {
        let totalPages;
        if (req.query.limit != undefined || req.query.pageNo != undefined) {
            totalPages = Math.ceil(doc.totalDoc / limits);
        } else {
            totalPages = 1;
        }
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.institution_calendar_event(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('THERE_IS_NO_EVENT_DATA_FOUND'), doc.data);
    }
};

exports.list_event = async (req, res) => {
    let aggre = [];
    let skips;
    let limits;
    let id = req.params.id;
    if (req.query.limit != undefined || req.query.pageNo != undefined) {
        skips = Number(req.query.limit * (req.query.pageNo - 1));
        limits = Number(req.query.limit) || 0;
        id = req.params.id;
        aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            { $match: { event_calendar: 'institution' } },
            { $match: { isDeleted: false } },
            { $sort: { event_date: 1 } },
            { $skip: skips },
            { $limit: limits },
        ];
    } else {
        aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            { $match: { event_calendar: 'institution' } },
            { $match: { isDeleted: false } },
            { $sort: { event_date: 1 } },
        ];
    }
    const query = { _calendar_id: ObjectId(id), isDeleted: false };
    const doc = await base_control.get_aggregate_with_id_match(
        institution_calendar_event,
        aggre,
        query,
    );
    if (doc.status) {
        let totalPages;
        if (req.query.limit != undefined || req.query.pageNo != undefined) {
            totalPages = Math.ceil(doc.totalDoc / limits);
        } else {
            totalPages = 1;
        }
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.institution_calendar_event(doc.data));
        common_files.listAllResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('THERE_IS_NO_EVENT_DATA_FOUND'),
            doc.data,
        );
    }
};

/*
exports.add_reviewer = async (req, res) => {
    let checks = { status: true }
    let status, datas;
    let staff_id = [];
    let object_id = req.params.id;
    await req.body.data.forEach(element => {
        if (staff_id.indexOf(element._reviewer_id) == -1) {
            staff_id.push(element._reviewer_id);
        }
    });
    // console.log(req.body.notify_via);
    checks = await base_control.check_id(staff, { _id: { $in: staff_id }, 'isDeleted': false });
    if (checks.status) {
        await req.body.data.forEach(async (element, index) => {
            let objs = {
                review: {
                    _reviewer_ids: element._reviewer_id,
                    expire: {
                        expire_date: element.expire_date,
                        expire_time: element.expire_time
                    }
                }
            };
            // console.log(index, objs);
            let doc = await base_control.update_push_pull_many(institution_calendar_event, { _calendar_id: object_id }, { $push: objs });
            // console.log(doc);
            if (doc.status) {
                status = true;
                datas = doc;
            } else {
                datas = doc;
                status = false;
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    common_files.com_response(res, 200, true, "institution_calendar_event update successfully", doc.data);
                } else {
                    common_files.com_response(res, 500, false, "Error", doc.data);
                }
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error Reviewer id not match", 'Check Parshing reference ID');
    }
}; */

exports.add_reviewer = async (req, res) => {
    let checks = { status: true };
    let checks_dup = { status: true };
    let status;
    let datas;
    const staff_id = [];
    const object_id = req.params.id;
    await req.body._reviewer_id.forEach((element) => {
        if (staff_id.indexOf(element) == -1) {
            staff_id.push(element);
        }
    });
    // console.log(req.body.notify_via);
    checks = await base_control.check_id(staff, { _id: { $in: staff_id }, isDeleted: false });
    // console.log(checks, staff_id);
    checks_dup = await base_control.get_list(
        institution_calendar_event,
        { _calendar_id: object_id, 'review._reviewer_ids': { $in: staff_id }, isDeleted: false },
        { _id: 1 },
    );
    // console.log(staff_id, 'dup', checks_dup);
    if (checks.status) {
        if (!checks_dup.status) {
            await req.body._reviewer_id.forEach(async (element, index) => {
                // console.log(checks, staff_id);
                const objs = {
                    review: {
                        _reviewer_ids: element,
                        status: 'pending',
                    },
                };
                const doc = await base_control.update_push_pull_many(
                    institution_calendar_event,
                    { _calendar_id: object_id },
                    { $push: objs },
                );
                await base_control.update_condition(
                    staff,
                    //{ _id: { $in: staff_id } },
                    { _id: ObjectId(element) },
                    {
                        /* $set: { role: 'Institution_Calendar_Reviewer' }, */ $push: {
                            sub_role: 'Institution_Calendar_Reviewer',
                        },
                    },
                );
                // await base_control.update_condition(staff, { _id: { $in: staff_id } }, { $set: { role: 'Institution_Calendar_Reviewer' } });
                // await base_control.update_condition(staff, { _id: { $in: staff_id } }, { $set: { role: 'Vice_Dean' } });
                if (doc.status) {
                    status = true;
                    datas = doc;
                } else {
                    datas = doc;
                    status = false;
                }
                if (req.body._reviewer_id.length == index + 1) {
                    if (status) {
                        common_files.comResponseWithRequest(
                            req,
                            res,
                            200,
                            true,
                            req.t('INSTITUTION_CALENDAR_EVENT_UPDATE_SUCCESSFULLY'),
                            doc.data,
                        );
                    } else {
                        common_files.comResponseWithRequest(
                            req,
                            res,
                            500,
                            false,
                            req.t('ERROR'),
                            doc.data,
                        );
                    }
                }
            });
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                500,
                false,
                req.t('REVIEWER_ALREADY_PRESENT'),
                req.t('CHECK_PARSING_REFERENCE_ID'),
            );
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('REVIEWER_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.remove_reviewer = async (req, res) => {
    const object_id = req.params.id;
    const pull_id = ObjectId(req.params.reviewer);
    await base_control.update_condition(
        staff,
        { _id: pull_id },
        { /* $set: { role: 'Staff' }, */ $pull: { sub_role: 'Institution_Calendar_Reviewer' } },
    );
    const doc = await base_control.update_push_pull_many(
        institution_calendar_event,
        { _calendar_id: object_id, isDeleted: false },
        { $pull: { review: { _reviewer_ids: pull_id } } },
    );
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('REVIEWER_REMOVED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('REVIEWER_ID_OR_CALENDAR_ID_NOT_MATCH'),
            doc.data,
        );
    }
};

exports.add_reviewer_review = async (req, res) => {
    // let checks = { status: true }
    // if (req.body._calendar_id != undefined) {
    //     checks = await base_control.check_id(institution_calendar, { _id: { $in: req.body._calendar_id }, 'isDeleted': false });
    // }
    // if (checks.status) {
    const object_id = {
        _id: req.body._event_id,
        isDeleted: false,
        'review._reviewer_ids': req.body._reviewer_id,
    };
    const objs = {};
    if (req.body.reviewer_type == 'reviewer') {
        if (req.body.review != undefined) {
            Object.assign(objs, { 'review.$.reviews': req.body.review });
        }
        if (req.body.review_comment != undefined && req.body.review_comment.length != 0) {
            Object.assign(objs, { 'review.$.reviewer_comment': req.body.review_comment });
        }
        Object.assign(objs, { 'review.$.status': 'Done' });
    } else {
        if (req.body.review != undefined) {
            Object.assign(objs, { 'review.$.dean_feedback': req.body.review });
        }
        if (req.body.review_comment != undefined && req.body.review_comment.length != 0) {
            Object.assign(objs, { 'review.$.dean_comment': req.body.review_comment });
        }
        Object.assign(objs, { status: 'Done' });
    }
    // console.log(object_id, objs);
    const doc = await base_control.update_condition(institution_calendar_event, object_id, objs);
    // console.log(doc);
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            req.t('REVIEW_ADDED_SUCCESSFULLY'),
            req.t('REVIEW_ADDED_SUCCESSFULLY'),
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
    // } else {
    //     common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    // }
};

exports.list_calendar_event_date_filter = async (req, res) => {
    let aggre = [];
    const id = req.params.id;
    let start_date;
    let end_date;
    if (req.query.start_date != undefined && req.query.end_date != undefined) {
        start_date = req.query.start_date;
        end_date = req.query.end_date;
        const sd = new Date(start_date);
        const ed = new Date(end_date);
        // console.log(sd, ' : ', ed);
        // console.log(sd.toUTCString(), ' : ', ed.toUTCString());
        aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            // { $match: { 'isDeleted': false } },
            { $match: { event_date: { $gte: sd }, end_date: { $lte: ed } } },
            { $sort: { event_date: 1 } },
        ];
    }
    const doc = await base_control.get_aggregate(institution_calendar_event, aggre);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* institution_formate.institution_calendar_event(doc.data) */);
        common_files.com_response(
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST') + doc.data.length,
            doc.data /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('THERE_IS_NO_EVENT_DATA_FOUND'), doc.data);
    }
};

exports.list_calendar_event_reviewer = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _calendar_id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        { $limit: 1 },
        {
            $lookup: {
                from: constant.USER,
                localField: 'review._reviewer_ids',
                foreignField: '_id',
                as: 'staff_reviewer',
            },
        },
        {
            $project: {
                'staff_reviewer._id': 1,
                'staff_reviewer.employee_id': 1,
                'staff_reviewer.name': 1,
            },
        },
        { $project: { _id: 0 } },
        // { $sort: { updatedAt: -1 } }
    ];

    const doc = await base_control.get_aggregate(institution_calendar_event, aggre);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.institution_calendar_event(doc.data));
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
            doc.data[0]
                .staff_reviewer /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('THERE_IS_NO_EVENT_DATA_FOUND'),
            doc.data,
        );
    }
};

exports.send_reviewer_notification = async (req, res) => {
    let checks = { status: true };
    let status;
    let datas;
    const staff_id = [];
    const object_id = req.params.id;
    await req.body.data.forEach((element) => {
        if (staff_id.indexOf(element._reviewer_id) == -1) {
            staff_id.push(element._reviewer_id);
            // console.log(element.notify_via);
        }
    });
    checks = await base_control.check_id(staff, { _id: { $in: staff_id }, isDeleted: false });
    // console.log(checks, staff_id);
    if (checks.status) {
        const noti_data = {
            notification_type: 'Event',
            notification_priority: 'medium',
            title: 'Added as Institution event Reviewer',
            start_date: common_fun.timestampNow,
            start_time: common_fun.timestampNow,
            end_date: req.body.expire_date,
            end_time: req.body.expire_time,
            _master_id: req.params.id,
            web_link: 'https://digischeduler.netlify.app/calender',
            _notify_to_id: staff_id,
        };
        const doc_not = await base_control.insert(notification_manager, noti_data);
        console.log(doc_not);

        await req.body.data.forEach(async (element, index) => {
            const objs = {
                'review.$.expire.expire_date': req.body.expire_date,
                'review.$.expire.expire_time': req.body.expire_time,
            };
            // console.log(req.body.expire_date, req.body.expire_time);
            // console.log(element.notify_via);
            const staff_data = await base_control.get(staff, { _id: element._reviewer_id }, {});
            const doc = await base_control.update_push_pull_many(
                institution_calendar_event,
                { _calendar_id: object_id, 'review._reviewer_ids': element._reviewer_id },
                { $set: objs },
            );
            // console.log(doc);
            // console.log(staff_data);
            const name = staff_data.data.name.middle
                ? staff_data.data.name.first +
                  ' ' +
                  staff_data.data.name.middle +
                  ' ' +
                  staff_data.data.name.last
                : staff_data.data.name.first + ' ' + staff_data.data.name.last;
            const messages =
                '<p> ' +
                req.t('DEAR') +
                ' ' +
                name +
                ',<br>' +
                common_fun.emailGreetingContent() +
                req.t('SEND_REVIEWER_NOTIFICATION_MSG') +
                common_fun.emailRegardsContent() +
                '</p>';
            if (doc.status) {
                status = true;
                datas = doc;
                // console.log(element.notify_via.indexOf('email'));
                if (element.notify_via.indexOf('email') != -1) {
                    // const mailOptions = {
                    //     to: staff_data.data.email,
                    //     subject: 'Digi Scheduler Institution calendar Review',
                    //     messages: messages,
                    // };
                    // await common_fun.sending_mail(mailOptions, (error) => {
                    //     if (error) {
                    //         console.log('err', error);
                    //     }
                    //     if (!error) {
                    //         console.log('mail sent : ' + staff_data.data.email);
                    //     } else {
                    //         console.log('Mail Error : ', error);
                    //     }
                    // });
                    common_fun.send_email(
                        staff_data.data.email,
                        req.t('DIGISCHEDULER_ALERT'),
                        messages,
                    );
                }
                if (element.notify_via.indexOf('sms') != -1) {
                    const sms_messages =
                        req.t('DEAR') + ' ' + name + ', ' + req.t('SEND_REVIEWER_NOTIFICATION_MSG');
                    // function sentMessageCallback(error, response, body) {
                    //     if (!error && common_fun.checkSMSResponse(response, body)) {
                    //         console.log('sms sent :' + staff_data.data.mobile);
                    //         // common_files.com_response(res, 200, true, "SMS Send");
                    //     } else {
                    //         console.log('sms error : ', error);
                    //         // common_files.com_response(res, 503, false, 'Error in sending message');
                    //     }
                    // }
                    // common_fun.send_message(staff_data.data.mobile, sms_messages, sentMessageCallback);
                    common_fun.send_sms(staff_data.data.mobile, sms_messages);
                }
                if (element.notify_via.indexOf('digischeduler') != -1) {
                    //Send Notification to DigiScheduler FCM
                }
                if (element.notify_via.indexOf('digiclass') != -1) {
                    //Send Notification to DigiScheduler FCMF
                }
            } else {
                datas = doc;
                status = false;
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('SENDING_REVIEW_REQUEST'),
                        req.t('SENDING_REVIEW_REQUEST'),
                    );
                } else {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('ERROR'),
                        doc.data,
                    );
                }
            }
        });
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('REVIEWER_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.list_id_reviewer = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.USER,
                localField: 'review._reviewer_ids',
                foreignField: '_id',
                as: 'reviewers',
            },
        },
        {
            $project: {
                _id: 1,
                event_type: 1,
                event_name: 1,
                event_description: 1,
                event_date: 1,
                start_time: 1,
                end_time: 1,
                end_date: 1,
                review: 1,
                'reviewers._id': 1,
                'reviewers.name': 1,
            },
        },
        { $unwind: { path: '$review', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                'review.reviewer_name': {
                    $filter: {
                        input: '$reviewers',
                        as: 'rev',
                        cond: {
                            $eq: ['$review._reviewer_ids', '$$rev._id'],
                        },
                    },
                },
            },
        },
        { $unwind: { path: '$review.reviewer_name', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                event_type: {
                    $first: '$event_type',
                },
                event_name: {
                    $first: '$event_name',
                },
                event_description: {
                    $first: '$event_description',
                },
                event_date: {
                    $first: '$event_date',
                },
                start_time: {
                    $first: '$start_time',
                },
                end_time: {
                    $first: '$end_time',
                },
                end_date: {
                    $first: '$end_date',
                },
                review: {
                    $push: '$review',
                },
            },
        },
    ];
    const doc = await base_control.get_aggregate(institution_calendar_event, aggre);
    if (doc.status) {
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_DETAILS'),
            /* doc.data */ institution_formate.institution_calendar_event_ID(doc.data[0]),
        );
        // common_files.com_response(res, 200, true, "institution_calendar_event details", doc.data[0]/* institution_formate.institution_calendar_event_ID(doc.data[0]) */);
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('EVENT_NOT_FOUND'),
            doc.data,
        );
    }
};

exports.send_dean_reviewer_notification = async (req, res) => {
    let documents;
    let doc;
    if (req.body.to == 'dean') {
        const id = req.body._calendar_id;
        const aggre = [
            { $match: { _id: ObjectId(id) } },
            { $match: { isDeleted: false } },
            {
                $lookup: {
                    from: constant.USER,
                    localField: '_creater_id',
                    foreignField: '_id',
                    as: 'staffs',
                },
            },
        ];
        documents = await base_control.get_aggregate(institution_calendar, aggre);

        // console.log(documents);
        const stf = [];
        const stf_mail = documents.data[0].staffs[0].email;
        const stf_mobile = documents.data[0].staffs[0].mobile;
        stf.push(documents.data[0].staffs[0]._id);
        // console.log(stf, stf_mail, stf_mobile);
        const noti_data = {
            notification_type: 'Event',
            notification_priority: 'low',
            title: req.body.message,
            web_link: 'https://digischeduler.netlify.app/calender',
            _notify_to_id: stf,
        };
        doc = await base_control.insert(notification_manager, noti_data);
        // doc = await notification.insert(noti_data);
        // console.log(doc);
        const messages = '<p>' + req.body.message + '</p>';
        if (stf_mail && req.body.notify_via.indexOf('email') != -1) {
            // const mailOptions = {
            //     to: stf_mail,
            //     subject: 'Digi Scheduler Institution calendar Review',
            //     messages: messages,
            // };
            // await common_fun.sending_mail(mailOptions, (error) => {
            //     if (error) {
            //         console.log('err', error);
            //     }
            //     if (!error) {
            //         console.log('mail sent : ' + stf_mail);
            //     } else {
            //         console.log('Mail Error : ', error);
            //     }
            // });
            common_fun.send_email(stf_mail, 'DigiClass Alert', messages);
        }
        if (stf_mobile && req.body.notify_via.indexOf('sms') != -1) {
            const sms_messages = req.body.message;
            // function sentMessageCallback(error, response, body) {
            //     if (!error && common_fun.checkSMSResponse(response, body)) {
            //         console.log('sms sent :' + stf_mobile);
            //     } else {
            //         console.log('sms error : ', error);
            //     }
            // }
            // common_fun.send_message(stf_mobile, sms_messages, sentMessageCallback);
            common_fun.send_sms(stf_mobile, sms_messages);
        }
        if (req.body.notify_via.indexOf('digischeduler') != -1) {
            //Send Notification to DigiScheduler FCM
        }
        if (req.body.notify_via.indexOf('digiclass') != -1) {
            //Send Notification to DigiScheduler FCMF
        }
        if (doc.status) {
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('YOUR_REVIEW_SENT_TO_DEAN_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    } else if (req.body.to == 'reviewer') {
        const id = req.body._calendar_id;
        const aggre = [
            { $match: { _calendar_id: ObjectId(id) } },
            { $match: { isDeleted: false } },
            { $limit: 1 },
            {
                $lookup: {
                    from: constant.USER,
                    localField: 'review._reviewer_ids',
                    foreignField: '_id',
                    as: 'staffs',
                },
            },
            { $addFields: { staff_ids: '$staffs._id' } },
        ];
        documents = await base_control.get_aggregate(institution_calendar_event, aggre);
        // console.log(documents);
        // doc = documents;
        documents.data[0].staffs.forEach(async (element, index) => {
            // console.log(element)
            const stf_mail = element.email;
            const stf_mobile = element.mobile;
            // stf.push(documents.data[0].staffs[0]._id);
            // console.log(documents.data[0].staff_ids, stf_mail, stf_mobile);
            const noti_data = {
                notification_type: 'Event',
                notification_priority: 'low',
                title: req.body.message,
                web_link: 'https://digischeduler.netlify.app/calender',
                _notify_to_id: documents.data[0].staff_ids,
            };
            doc = await base_control.insert(notification_manager, noti_data);
            // doc = await notification.insert(noti_data);
            // console.log(doc);
            const messages = '<p>' + req.body.message + '</p>';
            if (stf_mail && req.body.notify_via.indexOf('email') != -1) {
                // const mailOptions = {
                //     to: stf_mail,
                //     subject: 'Digi Scheduler Institution calendar Review',
                //     messages: messages,
                // };
                // await common_fun.sending_mail(mailOptions, (error) => {
                //     if (error) {
                //         console.log('err', error);
                //     }
                //     if (!error) {
                //         console.log('mail sent : ' + stf_mail);
                //     } else {
                //         console.log('Mail Error : ', error);
                //     }
                // });
                common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (stf_mobile && req.body.notify_via.indexOf('sms') != -1) {
                const sms_messages = req.t('DIGISCHEDULER') + req.body.message;
                // function sentMessageCallback(error, response, body) {
                //     if (!error && common_fun.checkSMSResponse(response, body)) {
                //         console.log('sms sent :' + stf_mobile);
                //     } else {
                //         console.log('sms error : ', error);
                //     }
                // }
                // common_fun.send_message(stf_mobile, sms_messages, sentMessageCallback);
                common_fun.send_sms(stf_mobile, sms_messages);
            }
            if (req.body.notify_via.indexOf('digischeduler') != -1) {
                //Send Notification to DigiScheduler FCM
            }
            if (req.body.notify_via.indexOf('digiclass') != -1) {
                //Send Notification to DigiScheduler FCMF
            }
            if (documents.data[0].staffs.length == index + 1) {
                if (doc.status) {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('YOUR_REVIEW_SENT_TO_DEAN_SUCCESSFULLY'),
                        doc.data,
                    );
                } else {
                    common_files.comResponseWithRequest(
                        req,
                        res,
                        500,
                        false,
                        req.t('ERROR'),
                        doc.data,
                    );
                }
            }
        });
    } else if (req.body.to == 'publish') {
        const id = req.body._calendar_id;
        const query =
            req.body.type == 'both'
                ? { isDeleted: false, isActive: true, status: 'completed' }
                : {
                      isDeleted: false,
                      isActive: true,
                      status: 'completed',
                      user_type: req.body.type,
                  };
        await base_control.update_condition(
            staff,
            { isDeleted: false },
            { $pull: { sub_role: 'Institution_Calendar_Reviewer' } },
        );
        documents = await base_control.get_list(staff, query, {
            _id: 1,
            name: 1,
            email: 1,
            mobile: 1,
        });
        await base_control.update_push_pull_many(
            institution_calendar_event,
            { _calendar_id: id },
            { isActive: true },
        );
        await base_control.update(institution_calendar, id, { status: 'published' });
        // old chat remove changes
        const getAllInstitutionCalendars = await institution_calendar.find(
            {
                isDeleted: false,
                status: 'published',
                _id: { $ne: id },
            },
            { _id: 1 },
        );
        const getAllInstitutionCalendarIds = getAllInstitutionCalendars.map(
            (getAllInstitutionCalendar) => getAllInstitutionCalendar._id.toString(),
        );
        if (keys.SERVICES.CHAT_ENABLED && keys.SERVICES.CHAT_ENABLED === 'true')
            await removeAllOldChatChannels(getAllInstitutionCalendarIds);

        // const staff_ids = [];
        if (documents.data != 'Not found') {
            const stf_mail = documents.data
                .filter((userElement) => userElement.email !== null)
                .map((item) => item.email);
            const stf_mobile = documents.data
                .filter((userElement) => userElement.mobile && userElement.mobile !== null)
                .map((item) => item.mobile);
            const messages = '<p>' + req.body.message + '</p>';
            if (req.body.notify_via.indexOf('email') !== -1) {
                common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const sms_messages = req.t('DIGISCHEDULER') + ' ' + req.body.message;
                common_fun.send_sms(stf_mobile, sms_messages);
            }
            const noti_data = {
                notification_type: 'Event',
                notification_priority: 'low',
                title: req.body.message,
                web_link: keys.FE_URL + '/calender',
                _notify_to_id: documents.data.map((item) => item._id),
            };
            await base_control.insert(notification_manager, noti_data);
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('INSTITUTION_CALENDAR_IS_SUCCESSFULLY_PUBLISHED'),
                req.t('INSTITUTION_CALENDAR_IS_SUCCESSFULLY_PUBLISHED'),
            );
            // documents.data.forEach(async (element, index) => {
            //     staff_ids.push(element._id);
            //     const stf_mail = element.email;
            //     const stf_mobile = element.mobile;
            //     const messages = '<p>' + req.body.message + '</p>';
            //     if (req.body.notify_via.indexOf('email') != -1) {
            //         // const mailOptions = {
            //         //     to: stf_mail,
            //         //     subject: 'Digi Scheduler Institution calendar',
            //         //     messages: messages,
            //         // };
            //         // await common_fun.sending_mail(mailOptions, (error) => {
            //         //     if (error) {
            //         //         console.log('Mail err', error);
            //         //     }
            //         //     if (!error) {
            //         //         console.log('mail sent : ' + stf_mail);
            //         //     } else {
            //         //         console.log('Mail Error : ', error);
            //         //     }
            //         // });
            //         common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            //     }
            //     if (req.body.notify_via.indexOf('sms') != -1) {
            //         const sms_messages = req.body.message;
            //         // function sentMessageCallback(error, response, body) {
            //         //     if (!error && common_fun.checkSMSResponse(response, body)) {
            //         //         console.log('sms sent :' + stf_mobile);
            //         //     } else {
            //         //         console.log('sms error : ', error);
            //         //     }
            //         // }
            //         // common_fun.send_message(stf_mobile, sms_messages, sentMessageCallback);
            //         common_fun.send_sms(stf_mobile, sms_messages);
            //     }
            //     if (req.body.notify_via.indexOf('digischeduler') != -1) {
            //         //Send Notification to DigiScheduler FCM
            //     }
            //     if (req.body.notify_via.indexOf('digiclass') != -1) {
            //         //Send Notification to DigiScheduler FCMF
            //     }
            //     if (documents.data.length == index + 1) {
            //         console.log('Over');
            //         const noti_data = {
            //             notification_type: 'Event',
            //             notification_priority: 'low',
            //             title: req.body.message,
            //             web_link: keys.FE_URL + '/calender',
            //             _notify_to_id: staff_ids,
            //         };
            //         await base_control.insert(notification_manager, noti_data);
            //         common_files.com_response(
            //             res,
            //             200,
            //             true,
            //             req.t('INSTITUTION_CALENDAR_IS_SUCCESSFULLY_PUBLISHED'),
            //             req.t('INSTITUTION_CALENDAR_IS_SUCCESSFULLY_PUBLISHED'),
            //         );
            //     }
            // });
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                404,
                true,
                req.t('UNABLE_TO_PUSH_NOTIFICATION_TO_USER'),
                req.t('UNABLE_TO_PUSH_NOTIFICATION_TO_USER'),
            );
        }
    }
};
