let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

const schema = new Schema({
    _office_id:{
        type: String,
        required: true,
        trim: true,
    },
    office_name:{
        type: String,
        required: true,
        trim: true,
    },
    _role_id:{
        type: String,
        required: true,
        trim: true,
    },
    role_name:{
        type: String,
        required: true,
        trim: true,
    },
    _program_id:{
        type: String,
        trim: true,
    },
    program_name:{
        type: String,
        trim: true,
    },
    _dept_id:{
        type: String,
        trim: true,
    },
    dept_name:{
        type: String,
        trim: true,
    },
    _subject_id:{
        type: String,
        trim: true,
    },
    subject_name:{
        type: String,
        trim: true,
    },
    _course_id:{
        type: String,
        trim: true,
    },
    course_name:{
        type: String,
        trim: true,
    },
    _infra_id:{
        type: String,
        trim: true,
    },
    infra_name:{
        type: String,
        trim: true,
    },
    _staff_id:{
        type: String,
        required: true,
        trim: true,
    },
    staff_name:{
        type: String,
        required: true,
        trim: true,
    },
    acting_staff_status: {
        type: Boolean,
        default: false
    },
    _acting_staff_id:{
        type: String,
        trim: true,
    },
    acting_staff_name:{
        type: String,
        trim: true,
    }, 
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }  
},
{ timestamps: true });
module.exports = mongoose.model(constant.ROLE_ASSIGN_TO_STAFF, schema);

