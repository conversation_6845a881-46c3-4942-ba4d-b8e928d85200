const courseScheduleSchemas = require('../models/course_schedule');
const courseSchema = require('../models/digi_course');
const studentGroupSchema = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');
const {
    SCHEDULE_TYPES: { REGULAR },
    GENDER: { MALE, FEMALE },
    MISSED,
    PENDING,
    STUDENT_GROUP_MODE: { FYD },
} = require('../utility/constants');
const institutionCalendarsSchema = require('../models/institution_calendar');
const roleAssignSchema = require('../models/role_assign');
const courseSessionOrderSchema = require('../models/digi_session_order');
const constant = require('../utility/constants');
const { getLevelsFromRedis, courseTopicNotCoveredList } = require('./courseReport.service');
const { getUserRoleProgramList } = require('../utility/utility.service');
const programCalendarSchema = require('../models/program_calendar');
const roleSchema = require('../models/role');
const userSchema = require('../models/user');
const { userWarningDenialStatus } = require('../digi_class/course_session/course_session_service');

const courseCompleteReport = async ({ query = {}, headers = {} }) => {
    try {
        const { term, levelNo, courseCode } = query;
        const { _institution_calendar_id } = headers;
        console.time('courseDetails');
        const courseDetails = await courseSchema
            .findOne(
                { course_code: courseCode },
                {
                    course_name: 1,
                    course_code: 1,
                    'administration.department_name': 1,
                    _program_id: 1,
                    'coordinators._institution_calendar_id': 1,
                    'coordinators.term': 1,
                    'coordinators.level_no': 1,
                    'coordinators.user_name': 1,
                },
            )
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();
        // console.timeEnd('courseDetails');
        // let institutionCalendarData = null;
        // console.time('institutionCalendarData');
        // institutionCalendarData = await institutionCalendarSchemas
        //     .findOne(
        //         {
        //             isActive: true,
        //             isDeleted: false,
        //             ...(!_institution_calendar_id
        //                 ? { status: PUBLISHED }
        //                 : { _id: convertToMongoObjectId(_institution_calendar_id) }),
        //         },
        //         { calendar_name: 1 },
        //     )
        //     .sort({ _id: -1 })
        //     .lean();
        // console.timeEnd('institutionCalendarData');
        console.time('courseScheduleDatas');
        const courseScheduleDatas = await courseScheduleSchemas
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    type: REGULAR,
                    ...(term && {
                        term: { $regex: '^' + term.toLowerCase() + '$', $options: 'i' },
                    }),
                    ...(levelNo && { level_no: levelNo }),
                    _course_id: convertToMongoObjectId(courseDetails._id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
                { 'staffs._staff_id': 1, 'staffs.staff_name': 1 },
            )
            .sort({ _id: -1 })
            .lean();
        console.timeEnd('courseScheduleDatas');
        const courseStaffs = [];
        courseScheduleDatas.forEach((scheduleElement) => {
            scheduleElement.staffs.forEach((staffElement) => {
                if (
                    !courseStaffs.find(
                        (courseStaffElement) =>
                            courseStaffElement._staff_id.toString() ===
                            staffElement._staff_id.toString(),
                    )
                ) {
                    courseStaffs.push(staffElement);
                }
            });
        });
        const studentGroupData = await studentGroupSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    isDeleted: false,
                    isActive: true,
                    'master._program_id': convertToMongoObjectId(courseDetails._program_id._id),
                    'groups.level': levelNo,
                    'groups.term': { $regex: '^' + term.toLowerCase() + '$', $options: 'i' },
                    'groups.courses._course_id': convertToMongoObjectId(courseDetails._id),
                },
                {
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.no_of_group': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.students._student_id': 1,
                    'groups.students.gender': 1,
                },
            )
            .lean();
        let studentIds = [];
        const studentGroupDetails = [];
        let levelStudents = [];
        studentGroupData.groups.forEach((groupElement) => {
            if (
                groupElement.term.toLowerCase() === term.toLowerCase() &&
                groupElement.level === levelNo
            )
                groupElement.courses.forEach((courseElement) => {
                    if (courseElement._course_id.toString() === courseDetails._id.toString()) {
                        courseElement.setting.forEach((settingElement) => {
                            settingElement.session_setting[0].groups.forEach((sessionElement) => {
                                studentIds = [
                                    ...studentIds,
                                    ...sessionElement._student_ids.map((studentIdElement) =>
                                        studentIdElement.toString(),
                                    ),
                                ];
                            });
                            const studentGroupIndex = studentGroupDetails.findIndex(
                                (studentGroupDetailElement) =>
                                    studentGroupDetailElement.gender === settingElement.gender,
                            );
                            if (studentGroupIndex === -1) {
                                studentGroupDetails.push({
                                    gender: settingElement.gender,
                                    groupCount: settingElement.session_setting[0].no_of_group,
                                });
                            } /* else {
                        } */
                        });
                        levelStudents = [...levelStudents, ...groupElement.students];
                    }
                });
        });
        studentIds = [...new Set(studentIds)];
        levelStudents = levelStudents.filter((studentElement) =>
            studentIds.find(
                (studentIdElement) =>
                    studentIdElement.toString() === studentElement._student_id.toString(),
            ),
        );
        const studentCount = [
            {
                gender: MALE,
                count: levelStudents.filter(
                    (levelStudentsElement) => levelStudentsElement.gender === MALE,
                ).length,
            },
            {
                gender: FEMALE,
                count: levelStudents.filter(
                    (levelStudentsElement) => levelStudentsElement.gender === FEMALE,
                ).length,
            },
        ];
        courseDetails.coordinators = courseDetails.coordinators.find(
            (coordinatorElement) =>
                coordinatorElement.term.toLowerCase() === term.toLowerCase() &&
                coordinatorElement._institution_calendar_id.toString() ===
                    _institution_calendar_id.toString() &&
                coordinatorElement.level_no === levelNo,
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                courseDetails,
                courseInstructors: courseStaffs,
                // institutionCalendarData,
                studentGroupDetails,
                studentCount,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getCalendarList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id, _role_id } = headers;
        let calendar = [];
        const roleAssignDocs = await roleAssignSchema
            .findOne({
                _user_id: convertToMongoObjectId(_user_id),
                isDeleted: false,
                isActive: true,
                'roles._role_id': convertToMongoObjectId(_role_id),
            })
            .lean();
        if (roleAssignDocs) {
            calendar = await institutionCalendarsSchema
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        status: constant.PUBLISHED,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        calendar_name: 1,
                        start_date: 1,
                        end_date: 1,
                    },
                )
                .lean();
        }
        if (
            roleAssignDocs &&
            roleAssignDocs.roles &&
            roleAssignDocs.roles.length &&
            roleAssignDocs.roles.find((roleElement) => roleElement.isAdmin)
        ) {
            return {
                statusCode: 200,
                message: 'SUCCESS',
                data: calendar,
            };
        }
        if (
            roleAssignDocs &&
            roleAssignDocs.roles.find(
                (roleElement) => roleElement.role_name === constant.COURSE_COORDINATOR,
            )
        ) {
            let coordinatorsCalendarIds = await courseSchema
                .distinct('coordinators._institution_calendar_id', {
                    isActive: true,
                    isDeleted: false,
                    coordinators: {
                        $elemMatch: {
                            _user_id: convertToMongoObjectId(_user_id),
                        },
                    },
                })
                .lean();
            coordinatorsCalendarIds = coordinatorsCalendarIds.map(
                (coordinatorsCalendarIdsElement) => coordinatorsCalendarIdsElement._id.toString(),
            );
            if (coordinatorsCalendarIds.length) {
                calendar = calendar.filter((calendarElement) =>
                    coordinatorsCalendarIds.includes(calendarElement._id.toString()),
                );
                return {
                    statusCode: 200,
                    message: 'SUCCESS',
                    data: calendar,
                };
            }
        } else {
            return {
                statusCode: 200,
                message: 'SUCCESS',
                data: [],
            };
        }
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const getProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id, _role_id, _institution_calendar_id } = headers;
        const roleAssignDocs = await roleAssignSchema
            .findOne({
                _user_id: convertToMongoObjectId(_user_id),
                isDeleted: false,
                isActive: true,
                'roles._role_id': convertToMongoObjectId(_role_id),
            })
            .populate({
                path: 'roles.program._program_id',
                model: constant.DIGI_PROGRAM,
                select: { term: 1 },
            })
            .lean();
        if (
            roleAssignDocs &&
            roleAssignDocs.roles &&
            roleAssignDocs.roles.length &&
            roleAssignDocs.roles.find((roleElement) => roleElement.isAdmin)
        ) {
            let programsData = roleAssignDocs.roles
                .filter(
                    (roleElement) =>
                        roleElement.isAdmin && roleElement.program && roleElement.program.length,
                )
                .map((roleElement) => {
                    const validPrograms = roleElement.program.map((programElement) => {
                        if (programElement._program_id) {
                            return {
                                programId: programElement._program_id._id.toString(),
                                programName: programElement.program_name,
                                term: programElement._program_id.term,
                            };
                        }
                        return null;
                    });
                    return validPrograms;
                })
                .flat()
                .filter((validProgramElement) => validProgramElement !== null);
            const programIds = programsData.map((programElement) =>
                convertToMongoObjectId(programElement.programId),
            );
            let existingProgramIds = await programCalendarSchema.distinct('_program_id', {
                _program_id: { $in: programIds },
                isDeleted: false,
                isActive: true,
                status: 'published',
            });
            existingProgramIds = existingProgramIds.map((existingProgramId) =>
                existingProgramId.toString(),
            );
            programsData = programsData.reduce((acc, current) => {
                const isDuplicate = acc.some(
                    (accElement) =>
                        accElement.programId.toString() === current.programId.toString(),
                );
                if (!isDuplicate && existingProgramIds.includes(current.programId)) {
                    acc.push(current);
                }
                return acc;
            }, []);
            return {
                statusCode: 200,
                message: 'SUCCESS',
                data: programsData,
            };
        }
        if (
            roleAssignDocs &&
            roleAssignDocs.roles.find(
                (roleElement) => roleElement.role_name === constant.COURSE_COORDINATOR,
            )
        ) {
            const courseDocs = await courseSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        'coordinators._user_id': convertToMongoObjectId(_user_id),
                        'coordinators._institution_calendar_id':
                            convertToMongoObjectId(_institution_calendar_id),
                    },
                    {
                        _program_id: 1,
                    },
                )
                .populate({
                    path: '_program_id',
                    select: { name: 1, term: 1 },
                })
                .lean();
            if (courseDocs.length) {
                let programsData = courseDocs.map((courseElement) => {
                    const program = courseElement._program_id;
                    return {
                        programId: program._id.toString(),
                        programName: program.name,
                        term: program.term,
                    };
                });
                const programIds = programsData.map((programElement) =>
                    convertToMongoObjectId(programElement.programId),
                );
                let existingProgramIds = await programCalendarSchema
                    .distinct('_program_id', {
                        _program_id: { $in: programIds },
                        isDeleted: false,
                        isActive: true,
                        status: 'published',
                    })
                    .lean();
                existingProgramIds = existingProgramIds.map((existingProgramElement) =>
                    existingProgramElement.toString(),
                );
                programsData = programsData.reduce((acc, current) => {
                    const isDuplicate = acc.some(
                        (accElement) =>
                            accElement.programId.toString() === current.programId.toString(),
                    );
                    if (!isDuplicate && existingProgramIds.includes(current.programId)) {
                        acc.push(current);
                    }
                    return acc;
                }, []);
                return {
                    statusCode: 200,
                    message: 'SUCCESS',
                    data: programsData,
                };
            }
        }
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: [],
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const getCourseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id, _institution_calendar_id, _role_id } = headers;
        const { programId } = query;
        let programLevels = await getLevelsFromRedis({ programId, _institution_calendar_id });
        if (!programLevels) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        const { userCourseIds } = await getUserRoleProgramList({
            user_id: _user_id,
            _institution_id,
            role_id: _role_id,
            institutionCalendarId: [_institution_calendar_id],
        });
        programLevels = programLevels.filter((levelElement) => {
            const { level_no, term, rotation, course, rotation_course } = levelElement;
            if (userCourseIds.length) {
                if (rotation === 'yes' && rotation_course && rotation_course.length) {
                    levelElement.rotation_course = rotation_course
                        .filter((rotationCourseElement) => {
                            const validRotationCourses = rotationCourseElement.course.filter(
                                (rCourseElement) =>
                                    userCourseIds.some(
                                        (userCourseElement) =>
                                            rCourseElement._course_id.toString() ===
                                                userCourseElement._course_id.toString() &&
                                            term === userCourseElement.term &&
                                            level_no === userCourseElement.level_no,
                                    ),
                            );
                            return validRotationCourses.length > 0;
                        })
                        .map((rotationCourseElement) => {
                            const validRotationCourses = rotationCourseElement.course.filter(
                                (rCourseElement) =>
                                    userCourseIds.some(
                                        (userCourseElement) =>
                                            rCourseElement._course_id.toString() ===
                                                userCourseElement._course_id.toString() &&
                                            term === userCourseElement.term &&
                                            level_no === userCourseElement.level_no,
                                    ),
                            );
                            return {
                                rotation_count: rotationCourseElement.rotation_count,
                                course: validRotationCourses,
                            };
                        });
                    return levelElement.rotation_course.length > 0;
                }
                const validCourses = course.filter((courseElement) =>
                    userCourseIds.some(
                        (userCourseElement) =>
                            courseElement._course_id.toString() ===
                                userCourseElement._course_id.toString() &&
                            term === userCourseElement.term &&
                            level_no === userCourseElement.level_no,
                    ),
                );
                levelElement.course = validCourses;
                return validCourses.length > 0;
            }
            return true;
        });
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: programLevels,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const getCourseScheduleList = async ({ headers = {}, query = {} }) => {
    try {
        const { term, levelNo, courseCode, courseId, type } = query;
        const { _institution_calendar_id } = headers;

        console.time('courseSessionOrderData');
        const courseSessionOrderData = await courseSessionOrderSchema
            .findOne(
                {
                    _course_id: convertToMongoObjectId(courseId),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'session_flow_data._id': 1,
                    'session_flow_data.s_no': 1,
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_no': 1,
                    'session_flow_data.delivery_topic': 1,
                },
            )
            .lean();
        console.timeEnd('courseSessionOrderData');
        console.time('courseScheduleDatas');
        const courseScheduleDatas = await courseScheduleSchemas
            .find(
                {
                    isDeleted: false,
                    type: REGULAR,
                    ...(term && {
                        term: { $regex: '^' + term.toLowerCase() + '$', $options: 'i' },
                    }),
                    ...(levelNo && { level_no: levelNo }),
                    ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
                    ...(courseCode && { course_code: courseCode }),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
                { 'session._session_id': 1, status: 1, isActive: 1 },
            )
            .sort({ _id: -1 })
            .lean();
        console.timeEnd('courseScheduleDatas');

        const typeBasedScheduleList = [];
        for (sessionFlowElement of courseSessionOrderData.session_flow_data) {
            const sessionBasedSchedule = courseScheduleDatas.filter(
                (scheduleElement) =>
                    scheduleElement.session._session_id.toString() ===
                    sessionFlowElement._id.toString(),
            );
            const typeBasedSchedules = sessionBasedSchedule.filter((sessionBasedScheduleElement) =>
                type === MISSED
                    ? sessionBasedScheduleElement.status === MISSED
                    : sessionBasedScheduleElement.status === PENDING,
            );
            if (typeBasedSchedules.length === sessionBasedSchedule.length)
                typeBasedScheduleList.push({
                    ...sessionFlowElement,
                    scheduleCount: sessionBasedSchedule.length,
                    typeBasedCount: typeBasedSchedules.length,
                });
        }

        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: typeBasedScheduleList,
            // data: { typeBasedScheduleList, courseSessionOrderData, courseScheduleDatas },
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const courseCompleteReportWithDetails = async ({ body = {} }) => {
    try {
        const { terms, levelNos, courseCode } = body;
        // const termRegex = terms.map((termElement) => {
        //     return { $regex: '^' + termElement + '$', $options: 'i' };
        // });
        const termRegex = { $regex: terms.join('|'), $options: 'i' };
        console.info(terms, termRegex);
        console.time('institutionCalendar');
        const institutionCalendar = await institutionCalendarsSchema
            .findOne(
                {
                    status: constant.PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        console.timeEnd('institutionCalendar');
        console.time('courseDetails');
        const courseDetails = await courseSchema
            .findOne(
                { isDeleted: false, course_code: { $in: courseCode } },
                {
                    course_name: 1,
                    course_code: 1,
                    'administration.department_name': 1,
                    _program_id: 1,
                    'coordinators._institution_calendar_id': 1,
                    'coordinators.term': 1,
                    'coordinators.level_no': 1,
                    'coordinators.user_name': 1,
                },
            )
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();
        console.timeEnd('courseDetails');

        if (!courseDetails)
            return {
                statusCode: 404,
                message: 'Course Not Found',
            };

        console.time('courseScheduleDatas');
        const courseScheduleDatas = await courseScheduleSchemas
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    type: REGULAR,
                    ...(terms && {
                        // term: { $regex: '^' + term.toLowerCase() + '$', $options: 'i' },
                        term: termRegex,
                    }),
                    ...(levelNos && { level_no: { $in: levelNos } }),
                    _course_id: convertToMongoObjectId(courseDetails._id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendar._id),
                },
                { 'staffs._staff_id': 1, 'staffs.staff_name': 1 },
            )
            .sort({ _id: -1 })
            .lean();
        console.timeEnd('courseScheduleDatas');
        const courseStaffs = [];
        courseScheduleDatas.forEach((scheduleElement) => {
            scheduleElement.staffs.forEach((staffElement) => {
                if (
                    !courseStaffs.find(
                        (courseStaffElement) =>
                            courseStaffElement._staff_id.toString() ===
                            staffElement._staff_id.toString(),
                    )
                ) {
                    courseStaffs.push(staffElement);
                }
            });
        });

        const studentGroupData = await studentGroupSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendar._id),
                    isDeleted: false,
                    isActive: true,
                    'master._program_id': convertToMongoObjectId(courseDetails._program_id._id),
                    'groups.level': { $in: levelNos },
                    // 'groups.term': termRegex,
                    // 'groups.term': { $regex: '^' + term.toLowerCase() + '$', $options: 'i' },
                    'groups.courses._course_id': convertToMongoObjectId(courseDetails._id),
                },
                {
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.no_of_group': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.students._student_id': 1,
                    'groups.students.gender': 1,
                },
            )
            .lean();
        if (!studentGroupData)
            return {
                statusCode: 404,
                message: 'Student Group Not Found',
            };
        let studentIds = [];
        const studentGroupDetails = [];
        let levelStudents = [];
        studentGroupData.groups.forEach((groupElement) => {
            if (
                terms.find(
                    (termElement) => termElement.toLowerCase() === groupElement.term.toLowerCase(),
                ) &&
                levelNos.find(
                    (levelElement) =>
                        levelElement.toLowerCase() === groupElement.level.toLowerCase(),
                )
            )
                groupElement.courses.forEach((courseElement) => {
                    if (courseElement._course_id.toString() === courseDetails._id.toString()) {
                        courseElement.setting.forEach((settingElement) => {
                            settingElement.session_setting[0].groups.forEach((sessionElement) => {
                                studentIds = [
                                    ...studentIds,
                                    ...sessionElement._student_ids.map((studentIdElement) =>
                                        studentIdElement.toString(),
                                    ),
                                ];
                            });
                            const studentGroupIndex = studentGroupDetails.findIndex(
                                (studentGroupDetailElement) =>
                                    studentGroupDetailElement.gender === settingElement.gender,
                            );
                            if (studentGroupIndex === -1) {
                                studentGroupDetails.push({
                                    gender: settingElement.gender,
                                    groupCount: settingElement.session_setting[0].no_of_group,
                                });
                            } /* else {
                        } */
                        });
                        levelStudents = [...levelStudents, ...groupElement.students];
                    }
                });
        });
        studentIds = [...new Set(studentIds)];
        levelStudents = levelStudents.filter((studentElement) =>
            studentIds.find(
                (studentIdElement) =>
                    studentIdElement.toString() === studentElement._student_id.toString(),
            ),
        );
        const studentCount = [
            {
                gender: MALE,
                count: levelStudents.filter(
                    (levelStudentsElement) => levelStudentsElement.gender === MALE,
                ).length,
            },
            {
                gender: FEMALE,
                count: levelStudents.filter(
                    (levelStudentsElement) => levelStudentsElement.gender === FEMALE,
                ).length,
            },
        ];
        courseDetails.coordinators = courseDetails.coordinators.find(
            (coordinatorElement) =>
                terms.find(
                    (termElement) =>
                        termElement.toLowerCase() === coordinatorElement.term.toLowerCase(),
                ) &&
                coordinatorElement._institution_calendar_id.toString() ===
                    institutionCalendar._id.toString() &&
                levelNos.find(
                    (levelElement) =>
                        levelElement.toLowerCase() === coordinatorElement.level.toLowerCase(),
                ),
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                courseDetails,
                courseInstructors: courseStaffs,
                studentGroupDetails,
                studentCount,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseDetails = async ({ body = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            courseId,
            year,
            level,
            term,
            rotationNo,
            includeTopicNotCovered,
            groupIds,
        } = body;
        if (includeTopicNotCovered) {
            const topicDetails = await courseTopicNotCoveredList({
                institutionCalendarId,
                programId,
                courseId,
                year,
                level,
                term,
                rotationNo,
                groupIds,
            });
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: {
                    topicDetails,
                },
            };
        }
        console.time('courseDetails');
        const courseDetails = await courseSchema
            .findOne(
                { _id: convertToMongoObjectId(courseId) },
                {
                    course_name: 1,
                    course_code: 1,
                    'administration._department_id': 1,
                    'administration.department_name': 1,
                    'administration.subject_name': 1,
                    _program_id: 1,
                    'coordinators._institution_calendar_id': 1,
                    'coordinators.term': 1,
                    'coordinators.level_no': 1,
                    'coordinators.user_name': 1,
                },
            )
            .populate({ path: '_program_id', select: { name: 1 } })
            .lean();
        console.timeEnd('courseDetails');
        console.time('courseScheduleDatas');
        const courseScheduleDatas = await courseScheduleSchemas
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    type: REGULAR,
                    term,
                    level_no: level,
                    _course_id: convertToMongoObjectId(courseId),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    ...(rotationNo && { rotation_count: rotationNo }),
                    ...(groupIds &&
                        groupIds.length && {
                            'student_groups.session_group.session_group_id': {
                                $in: groupIds.map((groupIdElement) =>
                                    convertToMongoObjectId(groupIdElement),
                                ),
                            },
                        }),
                },
                { 'staffs._staff_id': 1, 'staffs.staff_name': 1 },
            )
            .sort({ _id: -1 })
            .lean();
        console.timeEnd('courseScheduleDatas');
        const courseStaffs = [];
        courseScheduleDatas.forEach((scheduleElement) => {
            scheduleElement.staffs.forEach((staffElement) => {
                if (
                    !courseStaffs.find(
                        (courseStaffElement) =>
                            String(courseStaffElement._staff_id) === String(staffElement._staff_id),
                    )
                ) {
                    courseStaffs.push(staffElement);
                }
            });
        });
        const studentGroupData = await studentGroupSchema
            .findOne(
                {
                    isDeleted: false,
                    isActive: true,
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'master.year': year,
                    'groups.level': level,
                    'groups.term': term,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                },
                {
                    _institution_id: 1,
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.group_mode': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.no_of_group': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.students._student_id': 1,
                    'groups.students.gender': 1,
                },
            )
            .lean();
        let studentIds = [];
        const studentGroupDetails = [];
        let levelStudents = [];
        studentGroupData.groups.forEach((groupElement) => {
            if (
                groupElement.term.toLowerCase() === term.toLowerCase() &&
                groupElement.level === level
            )
                groupElement.courses.forEach((courseElement) => {
                    if (String(courseElement._course_id) === courseId) {
                        courseElement.setting.forEach((settingElement) => {
                            if (
                                rotationNo
                                    ? parseInt(settingElement._group_no) === parseInt(rotationNo)
                                    : true
                            ) {
                                settingElement.session_setting.forEach((sessionSettingElement) => {
                                    sessionSettingElement.groups.forEach((sessionElement) => {
                                        if (
                                            !groupIds ||
                                            !groupIds.length ||
                                            groupIds.some(
                                                (groupId) =>
                                                    String(groupId) === String(sessionElement._id),
                                            )
                                        )
                                            studentIds = [
                                                ...studentIds,
                                                ...sessionElement._student_ids.map(
                                                    (studentIdElement) => String(studentIdElement),
                                                ),
                                            ];
                                    });
                                });
                                const studentGroupIndex = studentGroupDetails.findIndex(
                                    (studentGroupDetailElement) =>
                                        studentGroupDetailElement.gender === settingElement.gender,
                                );
                                if (studentGroupIndex === -1) {
                                    if (groupElement.group_mode === FYD) {
                                        studentGroupDetails.push({
                                            gender: settingElement.gender,
                                            groupCount:
                                                settingElement.session_setting[0].no_of_group,
                                        });
                                    } else {
                                        studentGroupDetails.push({
                                            gender: settingElement.gender,
                                            deliveryGroups: settingElement.session_setting.map(
                                                (sessionSetting) => {
                                                    return {
                                                        deliverySymbol: sessionSetting.session_type,
                                                        groupCount: sessionSetting.no_of_group,
                                                    };
                                                },
                                            ),
                                        });
                                    }
                                } else if (!rotationNo) {
                                    if (groupElement.group_mode === FYD) {
                                        studentGroupDetails[studentGroupIndex].groupCount +=
                                            settingElement.session_setting[0].no_of_group;
                                    } else {
                                        studentGroupDetails[studentGroupIndex].deliveryGroups.push({
                                            groupCount: settingElement.session_setting.map(
                                                (sessionSetting) => {
                                                    return {
                                                        deliverySymbol: sessionSetting.session_type,
                                                        groupCount: sessionSetting.no_of_group,
                                                    };
                                                },
                                            ),
                                        });
                                    }
                                }
                            }
                        });
                        levelStudents = [...levelStudents, ...groupElement.students];
                    }
                });
        });
        studentIds = [...new Set(studentIds)];
        levelStudents = levelStudents.filter((studentElement) =>
            studentIds.find(
                (studentIdElement) =>
                    String(studentIdElement) === String(studentElement._student_id),
            ),
        );
        levelStudents = Array.from(
            new Map(levelStudents.map((item) => [String(item._student_id), item])).values(),
        );
        courseDetails.coordinators = courseDetails.coordinators.find(
            (coordinatorElement) =>
                coordinatorElement.term.toLowerCase() === term.toLowerCase() &&
                String(coordinatorElement._institution_calendar_id) === institutionCalendarId &&
                coordinatorElement.level_no === level,
        );
        console.time('userWarningDenialStatus');
        const studentDenialStatus = (
            await userWarningDenialStatus({
                studentIds: studentIds.map((studentIdElement) => {
                    return {
                        _student_id: studentIdElement,
                        isDenial: false,
                    };
                }),
                courseId,
                level,
                term,
                institutionCalendarId,
                _institution_id: studentGroupData._institution_id,
            })
        ).filter((studentElement) => studentElement.isDenial === true);
        console.timeEnd('userWarningDenialStatus');
        const programAdminRole = await roleSchema
            .find(
                {
                    name: 'ViceDean',
                    'modules.name': 'Program Calendar',
                    'modules.pages': {
                        $elemMatch: {
                            name: 'Dashboard',
                            $and: [
                                { 'actions.name': 'Calendar Settings' },
                                { 'actions.name': 'Add Course' },
                                { 'actions.name': 'Review Calendar' },
                            ],
                            tabs: {
                                $elemMatch: {
                                    name: 'Course',
                                    $and: [
                                        { 'actions.name': 'Edit' },
                                        { 'actions.name': 'Delete' },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
                    _id: 1,
                    name: 1,
                },
            )
            .lean();
        const programAdminRoleUsers = await roleAssignSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    'roles._role_id': {
                        $in: programAdminRole.map((roleElement) =>
                            convertToMongoObjectId(roleElement._id),
                        ),
                    },
                    'roles.program._program_id': convertToMongoObjectId(
                        courseDetails._program_id._id,
                    ),
                },
                { _user_id: 1 },
            )
            .lean();
        const departmentAdminRoleUsers = await roleAssignSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,

                    'roles.program._program_id': convertToMongoObjectId(
                        courseDetails._program_id._id,
                    ),
                    'roles.department._department_id': convertToMongoObjectId(
                        courseDetails.administration._department_id,
                    ),
                },
                { _user_id: 1 },
            )
            .lean();
        const adminUsers = await userSchema
            .find(
                {
                    _id: {
                        $in: [
                            ...programAdminRoleUsers.map((adminRoleUserElement) =>
                                convertToMongoObjectId(adminRoleUserElement._user_id),
                            ),
                            ...departmentAdminRoleUsers.map((adminRoleUserElement) =>
                                convertToMongoObjectId(adminRoleUserElement._user_id),
                            ),
                        ],
                    },
                    email: /@ibnsina.edu.sa/,
                },
                { name: 1, _id: 1 },
            )
            .lean();
        const programAdminUsers = adminUsers.filter((adminUserElement) =>
            programAdminRoleUsers.find(
                (programAdminRoleUserElement) =>
                    String(programAdminRoleUserElement._user_id) === String(adminUserElement._id),
            ),
        );
        const departmentAdminUsers = adminUsers.filter((adminUserElement) =>
            departmentAdminRoleUsers.find(
                (programAdminRoleUserElement) =>
                    String(programAdminRoleUserElement._user_id) === String(adminUserElement._id),
            ),
        );
        const studentDetails = {
            maleStudents: levelStudents.filter(
                (levelStudentsElement) => levelStudentsElement.gender === MALE,
            ),
            femaleStudents: levelStudents.filter(
                (levelStudentsElement) => levelStudentsElement.gender === FEMALE,
            ),
        };
        const studentCount = [
            {
                gender: MALE,
                count: studentDetails.maleStudents.length,
            },
            {
                gender: FEMALE,
                count: studentDetails.femaleStudents.length,
            },
        ];
        const denialStudentCount = [
            {
                gender: MALE,
                count: studentDenialStatus.filter((studentElement) =>
                    studentDetails.maleStudents.find(
                        (genderStudentElement) =>
                            String(genderStudentElement._student_id) ===
                            String(studentElement._student_id),
                    ),
                ).length,
            },
            {
                gender: FEMALE,
                count: studentDenialStatus.filter((studentElement) =>
                    studentDetails.femaleStudents.find(
                        (genderStudentElement) =>
                            String(genderStudentElement._student_id) ===
                            String(studentElement._student_id),
                    ),
                ).length,
            },
        ];
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                courseDetails,
                courseInstructors: courseStaffs,
                studentGroupDetails,
                studentCount,
                denialStudentCount,
                programAdmins: programAdminUsers,
                departmentAdmin: departmentAdminUsers,
                //! Currently I Don't have it need to Add a feature flow in SAAM Denial Module
                withdrawnStudentCount: [
                    {
                        gender: MALE,
                        count: 0,
                    },
                    {
                        gender: FEMALE,
                        count: 0,
                    },
                ],
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    courseCompleteReport,
    getCalendarList,
    getCourseList,
    getProgramList,
    getCourseScheduleList,
    courseCompleteReportWithDetails,
    courseDetails,
};
