const { dsGetAllWithSortAsJSON, get } = require('../../base/base_controller');
const CourseSchedule = require('../../models/course_schedule');
const Course = require('../../models/digi_course');
const Activity = require('../../models/activities');
const Question = require('../../models/question');
const User = require('../../models/user');
const Document = require('../../models/document_manager');
const Program = require('../../models/digi_programs');
const InstitutionCalendar = require('../../models/institution_calendar');
const CourseScheduleSetting = require('../../models/course_schedule_setting');
const ProgramCalendar = require('../../models/program_calendar');
const { deniedStudentPercent } = require('../../../config/index');

const {
    convertToMongoObjectId,
    convertToUtcFormat,
    convertToUtcEndFormat,
    convertToUtcTimeFormat,
    sendResponse,
} = require('../../utility/common');
const {
    COMPLETED,
    PENDING,
    PROGRAM_CALENDAR,
    PRESENT,
    ABSENT,
    LEAVE,
    DC_STAFF,
    SCHEDULE_TYPES: { EVENT, SUPPORT_SESSION, REGULAR },
    DAYS,
    COURSE_SCHEDULE,
    TIME_GROUP_BOOKING_TYPE,
    DS_CLO_KEY,
    DS_SLO_KEY,
    DRAFT,
    SCHEDULE,
    PUBLISHED,
} = require('../../utility/constants');
const { convertingRiyadhToUTC, dateTimeBasedConverter } = require('../../utility/common_functions');
const { PM } = require('../../utility/enums');
const { encryptData, decryptData } = require('../../utility/encrypt_decrypt.util');
const query = { isDeleted: false, isActive: true };
const cs = (str) => str.toString();
const clone = (object) => JSON.parse(JSON.stringify(object));
const getJSON = dsGetAllWithSortAsJSON;
const getInfraById = async (infraIds) => {
    try {
        csQuery = { 'programs.remoteScheduling._id': { $in: infraIds } };
        csProject = { 'programs.remoteScheduling': 1 };

        return await CourseScheduleSetting.find(csQuery, csProject);
    } catch (error) {
        throw new Error(error);
    }
};

const getStaffCompletedSession = async (query) => {
    const courseScheduleQuery = {
        isDeleted: query.isDeleted,
        status: COMPLETED,
        _course_id: query._course_id,
        _institution_calendar_id: query._institution_calendar_id,
        _program_id: query._program_id,
        year_no: query.year_no,
        level_no: query.level_no,
        term: query.term,
        rotation: query.rotation,
    };
    if (query.rotation_count) {
        courseScheduleQuery.rotation_count = query.rotation_count;
    }
    const csProject = { merge_status: 1, merge_with: 1 };
    let courseSchedules = await CourseSchedule.find(courseScheduleQuery, csProject).lean();
    let staffCompletedSession = 0;
    courseSchedules = courseSchedules.map((courseSchedule) => {
        staffCompletedSession++;
        if (courseSchedule.merge_status) {
            staffCompletedSession += courseSchedule.merge_with.length;
        }
    });
    return staffCompletedSession;
};
const getCourseIds = async (userId, type) => {
    try {
        let csQuery;
        if (type === DC_STAFF) csQuery = { 'staffs._staff_id': convertToMongoObjectId(userId) };
        else csQuery = { 'students._id': convertToMongoObjectId(userId) };
        const csProject = { _course_id: 1, _id: 0 };
        const courseSchedule = await CourseSchedule.find(csQuery, csProject);
        return [...new Set(courseSchedule.map((schedule) => schedule._course_id))];
    } catch (error) {
        throw new Error(error);
    }
};
const getRatingByCourses = async (
    institutionCalendarId,
    courses,
    courseIds,
    staffId,
    year,
    level,
    term,
    rotation,
    rotationCount,
) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        let courseScheduleQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            termQuery = [term];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
        } else {
            courseQuery = courseIds;
            yearQuery = year;
            levelQuery = level;
            rotationQuery = rotation;
            termQuery = term;
            rotationCountQuery = rotationCount;
        }
        courseScheduleQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            term: { $in: termQuery },
            rotation: { $in: rotationQuery },
            'staffs.status': PRESENT,
            students: { $exists: true, $type: 'array', $ne: [] },
            isDeleted: false,
            isActive: true,
            type: REGULAR,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };
        rotationCountQuery = rotationCountQuery.filter((rotationCount) => rotationCount);
        if (rotationCountQuery) {
            courseScheduleQuery.rotation_count = { $in: rotationCountQuery };
        }

        if (courses && courses.length) {
            const courseQuery = courses.map((course) => {
                return {
                    _course_id: course._course_id,
                    year_no: course.year_no,
                    level_no: course.level_no,
                    term: course.term,
                    rotation: course.rotation,
                    rotation_count: course.rotation_count,
                };
            });
            courseScheduleQuery = {
                'staffs.status': PRESENT,
                students: { $exists: true, $type: 'array', $ne: [] },
                isDeleted: false,
                isActive: true,
                type: REGULAR,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            };
            if (courseQuery && courseQuery.length) {
                courseScheduleQuery.$or = courseQuery;
            }
        }
        const Project = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            rotation_count: 1,
            term: 1,
            rotation: 1,
            session: 1,
            status: 1,
            merge_status: 1,
            merge_with: 1,
        };
        let courseSchedules = await CourseSchedule.find(courseScheduleQuery, Project).lean();
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    courseSchedule.session &&
                    courseSchedule.session._session_id &&
                    staff.status === PRESENT,
            ),
        );
        const feedBacks = [];
        if (courses && courses.length) {
            let i = 0;
            courses.forEach((course) => {
                const courseDetails = courseSchedules.filter((courseSchedule) =>
                    courseSchedule.rotation_count && course.rotation_count
                        ? courseSchedule._course_id.toString() === course._course_id.toString() &&
                          courseSchedule.year_no.toString() === course.year_no.toString() &&
                          courseSchedule.level_no.toString() === course.level_no.toString() &&
                          courseSchedule.term.toString() === course.term.toString() &&
                          courseSchedule.rotation_count === course.rotation_count
                        : courseSchedule._course_id.toString() === course._course_id.toString() &&
                          courseSchedule.year_no.toString() === course.year_no.toString() &&
                          courseSchedule.level_no.toString() === course.level_no.toString() &&
                          courseSchedule.term.toString() === course.term.toString(),
                );
                let sumOfRatings = 0;
                let count = 0;
                let levelOfCourseId;
                let yearOfCourseId;
                const schedulePush = [];
                let rotation_count;
                let rotation;
                courseDetails.map((courseDetail) => {
                    const {
                        students,
                        level_no,
                        year_no,
                        _id,
                        status,
                        merge_status,
                        merge_with,
                        term,
                    } = courseDetail;
                    rotation_count = courseDetail.rotation_count ? courseDetail.rotation_count : '';
                    rotation = courseDetail.rotation ? courseDetail.rotation : '';
                    termOfCourseId = term;
                    levelOfCourseId = level_no;
                    yearOfCourseId = year_no;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);
                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                    students.map((student) => {
                        if (student.feedBack && student.feedBack.rating) {
                            schedulePush.push(_id);
                            if (merge_status) {
                                merge_with.forEach((mergeSchedule) => {
                                    schedulePush.push(mergeSchedule.schedule_id);
                                });
                            }
                        }
                    });
                });
                let uniqueSchedules = schedulePush.map((schedule) => schedule.toString());
                uniqueSchedules = [...new Set(uniqueSchedules)];
                if (count) {
                    feedBacks.push({
                        _course_id: course._course_id,
                        level_no: levelOfCourseId,
                        year_no: yearOfCourseId,
                        term: termOfCourseId,
                        totalFeedback: count,
                        avgRating: count ? (sumOfRatings / count).toFixed(1) : 0,
                        sessionCount: uniqueSchedules.length,
                        rotation,
                        rotationCount: rotation_count,
                    });
                }
                i++;
            });
        }
        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};
const getCourseRatingForStaffs = async (
    _institution_calendar_id,
    courseIds,
    year,
    level,
    rotation,
    rotationCount,
    term,
) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        let termQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
            termQuery = [term];
        } else {
            courseQuery = courseIds;
            yearQuery = year;
            levelQuery = level;
            rotationQuery = rotation;
            rotationCountQuery = rotationCount;
            termQuery = term;
        }
        const fbQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            term: { $in: termQuery },
            'staffs.status': PRESENT,
            students: { $exists: true, $type: 'array', $ne: [] },
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        };
        const fbProject = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            rotation_count: 1,
            rotation: 1,
            session: 1,
            status: 1,
            subjects: 1,
            merge_status: 1,
            merge_with: 1,
        };
        const courseSchedules = await CourseSchedule.find(fbQuery, fbProject).lean();
        const courseSchedulesStaffs = courseSchedules.map((staffEntry) =>
            staffEntry.staffs.filter((staff) => staff._staff_id),
        );
        const mergeStaffs = [].concat(...courseSchedulesStaffs);

        const distinctstaffs = mergeStaffs.filter(
            (elem, index) =>
                mergeStaffs.findIndex(
                    (obj) => obj._staff_id.toString() === elem._staff_id.toString(),
                ) === index,
        );
        const staffIds = distinctstaffs.map((staffEntry) => staffEntry._staff_id);
        const staffAcademicIds = await User.find(
            { _id: { $in: staffIds } },
            { _id: 1, user_id: 1 },
        ).lean();
        const staffFeedBacks = [];
        for (const staffs of distinctstaffs) {
            const staffCourseSchedules = courseSchedules.filter((courseSchedule) =>
                courseSchedule.staffs.find(
                    (staff) =>
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        staff._staff_id.toString() === staffs._staff_id.toString() &&
                        staff.status === PRESENT,
                ),
            );

            if (courseQuery && courseQuery.length) {
                let i = 0;
                courseQuery.map((courseId) => {
                    const courseDetails = staffCourseSchedules.filter((courseSchedule) => {
                        if (courseSchedule._course_id.toString() === courseId.toString()) {
                            return true;
                        }
                        return false;
                    });
                    let sumOfRatings = 0;
                    let count = 0;
                    let levelOfCourseId;
                    let yearOfCourseId;
                    const schedulePush = [];
                    let rotation_count;
                    let rotation;
                    let completedCount = 0;
                    const staffSubjects = [];
                    courseDetails.map((courseDetail) => {
                        const {
                            students,
                            level_no,
                            year_no,
                            _id,
                            status,
                            subjects,
                            merge_status,
                            merge_with,
                        } = courseDetail;
                        if (status === COMPLETED) {
                            completedCount++;
                        }
                        staffSubjects.push(subjects);
                        rotation_count = courseDetail.rotation_count
                            ? courseDetail.rotation_count
                            : '';
                        rotation = courseDetail.rotation ? courseDetail.rotation : '';

                        levelOfCourseId = level_no;
                        yearOfCourseId = year_no;
                        sumOfRatings += students
                            .filter((student) => student.feedBack && student.feedBack.rating)
                            .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                            .reduce((a, b) => a + b, 0);
                        count += students.filter(
                            (student) => student.feedBack && student.feedBack.rating,
                        ).length;
                        students.map((student) => {
                            if (student.feedBack && student.feedBack.rating) {
                                schedulePush.push(_id);
                            }
                        });
                        if (merge_status) {
                            merge_with.forEach((mergeSchedule) => {
                                schedulePush.push(mergeSchedule.schedule_id);
                            });
                        }
                    });
                    const academicId = staffAcademicIds.find(
                        (academicEntry) =>
                            academicEntry._id.toString() === staffs._staff_id.toString(),
                    );
                    //getting unique subjects
                    let subjects = [];
                    if (staffSubjects) {
                        subjects = [
                            ...new Map(
                                staffSubjects.flat().map((item) => [item.subject_name, item]),
                            ).values(),
                        ];
                    }
                    if (count) {
                        let staffSchedules = schedulePush.map((schedule) => schedule.toString());
                        staffSchedules = [...new Set(staffSchedules)];
                        staffFeedBacks.push({
                            _course_id: courseId,
                            level_no: levelOfCourseId,
                            year_no: yearOfCourseId,
                            totalFeedback: count,
                            avgRating: count ? (sumOfRatings / count).toFixed(1) : 0,
                            sessionCount: staffSchedules.length,
                            sessionCompletedCount: completedCount,
                            rotation,
                            rotationCount: rotation_count,
                            subjects,
                            staffs: {
                                _staff_id: staffs._staff_id,
                                staff_name: staffs.staff_name,
                                status: staffs.status,
                                academicId: academicId.user_id,
                            },
                        });
                    }
                    i++;
                });
            }
        }
        return staffFeedBacks;
    } catch (error) {
        throw new Error(error);
    }
};
const getCourseAdminParams = async (staffId, institutionCalendarId) => {
    try {
        const courseFindQuery = {
            'coordinators._user_id': convertToMongoObjectId(staffId),
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
        };
        const courses = await Course.find(courseFindQuery, {
            coordinators: 1,
            _program_id: 1,
            course_name: 1,
            course_code: 1,
        })
            .populate({
                path: '_program_id',
                select: { name: 1 },
            })
            .lean();
        const courseParams = [];
        if (courses.length) {
            for (course of courses) {
                for (courseParam of course.coordinators) {
                    if (
                        courseParam._user_id.toString() === staffId.toString() &&
                        courseParam._institution_calendar_id.toString() ===
                            institutionCalendarId.toString() &&
                        courseParam.status
                    ) {
                        courseParams.push({
                            term: courseParam.term,
                            level_no: courseParam.level_no,
                            year_no: courseParam.year,
                            _institution_calendar_id: courseParam._institution_calendar_id,
                            _program_id: course._program_id._id,
                            program_name: course._program_id.name,
                            _course_id: course._id,
                            course_name: course.course_name,
                            course_code: course.course_code,
                        });
                    }
                }
            }
        }
        return courseParams;
    } catch (error) {
        throw new Error(error);
    }
};
const getCourses = async (staffId) => {
    try {
        const courseScheduleQuery = { schedule_date: { $exists: true }, isDeleted: false };
        const courseParams = await getCourseAdminParams(staffId);

        let coursesLists = [];
        if (courseParams.length > 0) {
            const courseParamsMapped = courseParams.map((courseParam) => {
                return {
                    term: courseParam.term,
                    level_no: courseParam.level_no,
                    year_no: courseParam.year_no,
                    _course_id: courseParam._course_id,
                };
            });
            courseScheduleQuery.$or = courseParamsMapped;

            const coursesList = await CourseSchedule.find(courseScheduleQuery, {
                course_name: 1,
                course_code: 1,
                _program_id: 1,
                _institution_calendar_id: 1,
                year_no: 1,
                program_name: 1,
                level_no: 1,
                rotation: 1,
                rotation_count: 1,
                term: 1,
                _course_id: 1,
            })
                .populate({
                    path: '_course_id',
                    select: {
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                })
                .lean();
            coursesList.filter(function (coursesListsEntry) {
                const chkCoursesList = coursesLists.findIndex(
                    (coursesListEntry) =>
                        coursesListEntry._program_id.toString() ===
                            coursesListsEntry._program_id.toString() &&
                        coursesListEntry._institution_calendar_id.toString() ===
                            coursesListsEntry._institution_calendar_id.toString() &&
                        coursesListEntry.year_no === coursesListsEntry.year_no &&
                        coursesListEntry.level_no === coursesListsEntry.level_no &&
                        coursesListEntry.rotation === coursesListsEntry.rotation &&
                        coursesListEntry.rotation_count === coursesListsEntry.rotation_count &&
                        coursesListEntry.term == coursesListsEntry.term &&
                        coursesListEntry._course_id._id.toString() ===
                            coursesListsEntry._course_id._id.toString(),
                );
                if (chkCoursesList <= -1) {
                    coursesListsEntry._id = coursesListsEntry._course_id._id;
                    coursesLists.push(coursesListsEntry);
                }
                return null;
            });
            coursesLists = coursesLists.filter(
                (coursesList) => coursesList.rotation_count !== null,
            );
        }
        return coursesLists;
    } catch (error) {
        throw new Error(error);
    }
};
// get course admin
const getCourseAdmin = async (userId, institutionCalendarId) => {
    let courseAdmin = false;
    let courses = await getCourses(userId);
    courses = courses.filter(
        (course) => course._institution_calendar_id.toString() === institutionCalendarId.toString(),
    );
    courseQuery = courses.map((course) => {
        return {
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
            _id: convertToMongoObjectId(course._id),
            'coordinators.term': course.term,
            'coordinators.year': course.year_no,
            'coordinators.level_no': course.level_no,
        };
    });
    const courseFindQuery = {
        'coordinators._user_id': convertToMongoObjectId(userId),
        'coordinators.status': true,
    };
    if (courseQuery.length > 0) courseFindQuery.$or = courseQuery;
    const coordinators = await Course.find(courseFindQuery, { _id: 1 }).lean();
    if (coordinators.length) {
        courseAdmin = true;
    }
    return courseAdmin;
};
const getStudentsDeniedCourses = async (courseIds, year, level, rotation, rotationCount) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
        }
        const fbQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            students: { $exists: true, $type: 'array', $ne: [] },
        };

        const fbProject = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            rotation_count: 1,
            rotation: 1,
            session: 1,
        };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject).lean();
        const courseSchedulesStudents = courseSchedules.map((studentEntry) =>
            studentEntry.students.filter((student) => student._id),
        );
        const mergeStudents = [].concat(...courseSchedulesStudents);

        const distinctStudents = mergeStudents.filter(
            (elem, index) =>
                mergeStudents.findIndex((obj) => obj._id.toString() === elem._id.toString()) ===
                index,
        );
        const deniedStudents = [];
        for (const students of distinctStudents) {
            courseSchedules = courseSchedules.filter((courseSchedule) =>
                courseSchedule.students.find(
                    (student) => student._id.toString() === students._id.toString(),
                ),
            );

            const studentCount = courseSchedules.length;

            if (courseSchedules) {
                const absentStudents = courseSchedules.filter((courseSchedule) =>
                    courseSchedule.students.find(
                        (student) =>
                            (student._id.toString() === students._id.toString() &&
                                student.status == PENDING) ||
                            student.status == ABSENT,
                    ),
                );

                const statusPercentage = (absentStudents.length / studentCount) * 100;

                if (deniedStudentPercent < statusPercentage) {
                    students.deniedPercent = statusPercentage;
                    students.absentCount = absentStudents.length;
                    students.overallCount = studentCount;
                    deniedStudents.push(students);
                }
            }
        }
        return deniedStudents;
    } catch (error) {
        throw new Error(error);
    }
};
const getRatingBySessions = async (sessionIds, staffId) => {
    try {
        let courseSchedules;
        if (sessionIds && staffId) {
            courseSchedules = await CourseSchedule.find(
                {
                    _id: { $in: sessionIds },
                    $or: [
                        {
                            'staffs._staff_id': convertToMongoObjectId(staffId),
                            'sessionDetail.startBy': convertToMongoObjectId(staffId),
                        },
                        {
                            'staffs._staff_id': convertToMongoObjectId(staffId),
                            'staffs.status': PRESENT,
                        },
                    ],
                    students: { $exists: true, $type: 'array', $ne: [] },
                },
                { _id: 1, students: 1 },
            );
        } else {
            courseSchedules = await CourseSchedule.find(
                {
                    _id: { $in: sessionIds },
                    students: { $exists: true, $type: 'array', $ne: [] },
                },
                { _id: 1, students: 1 },
            );
        }

        const feedBacks = [];

        if (sessionIds) {
            const sessionIdFilters = sessionIds.filter((sessionId) => sessionId);
            sessionIdFilters.map((sessionIdss) => {
                const courseDetails = courseSchedules.filter((courseSchedule) => {
                    if (courseSchedule._id.toString() === sessionIdss && sessionIdss.toString()) {
                        return true;
                    }
                    return false;
                });
                let sumOfRatings = 0;
                let count = 0;

                courseDetails.map((courseDetail) => {
                    const { students } = courseDetail;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);

                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                });
                if (count !== 0) {
                    feedBacks.push({
                        _session_id: sessionIdss,
                        totalFeedback: count,
                        avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                    });
                }
            });
        }

        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentCourseIds = async (studentId) => {
    try {
        const csQuery = { 'students._id': studentId };
        const csProject = { _course_id: 1, _id: 0 };
        const courseSchedule = await CourseSchedule.find(csQuery, csProject);
        return [...new Set(courseSchedule.map((schedule) => schedule._course_id))];
    } catch (error) {
        throw new Error(error);
    }
};

const isCourseScheduleExist = (
    courseParam,
    scheduleCourseIds,
    terms,
    level_nos,
    year_nos,
    rotationCounts,
) => {
    const { _course_id, term, level_no, year_no, rotation_count } = courseParam;
    const rotationCountEntry = rotationCounts
        .filter((rotationCount) => rotationCount)
        .map((rotationCount) => rotationCount.toString());
    if (
        (!scheduleCourseIds.includes(_course_id.toString()) &&
            !terms.includes(term) &&
            !level_nos.includes(level_no) &&
            !year_nos.includes(year_no)) ||
        (scheduleCourseIds.includes(_course_id.toString()) &&
            !terms.includes(term) &&
            level_nos.includes(level_no) &&
            year_nos.includes(year_no)) ||
        (rotation_count &&
            scheduleCourseIds.includes(_course_id.toString()) &&
            terms.includes(term) &&
            level_nos.includes(level_no) &&
            year_nos.includes(year_no) &&
            !rotationCountEntry.includes(rotation_count.toString()))
    ) {
        return true;
    }
    return false;
};

const getCourseIdsWithSessions = async (
    staffId,
    type,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
) => {
    try {
        const orQuery = [];
        const csQuery = {
            schedule_date: { $exists: true },
            isDeleted: false,
        };
        let courseParams;
        if (_institution_calendar_id) {
            courseParams = await getCourseAdminParams(staffId, _institution_calendar_id);
        } else {
            const institutionCalendarId = await InstitutionCalendar.findOne(
                { status: PUBLISHED },
                { _id: 1 },
            ).sort({
                _id: -1,
            });
            courseParams = await getCourseAdminParams(staffId, institutionCalendarId._id);
        }
        if (courseParams.length) {
            let courseParamsMapped = [];
            if (!_course_id) {
                courseParamsMapped = courseParams.map((courseParam) => {
                    return {
                        term: courseParam.term,
                        level_no: courseParam.level_no,
                        year_no: courseParam.year_no,
                        _course_id: courseParam._course_id,
                        _institution_calendar_id: courseParam._institution_calendar_id,
                    };
                });
            }
            if (type) {
                if (type !== 'all' && type !== 'today') {
                    courseParamsMapped.push(
                        {
                            'session.session_type': type,
                        },
                        {
                            type,
                        },
                    );
                } else if (type === 'today') {
                    const todayDate = convertToUtcFormat(new Date());
                    csQuery.schedule_date = new Date(todayDate);
                }
            }
            if (courseParamsMapped.length) csQuery.$or = courseParamsMapped;
        } else {
            return {
                courseIds: [],
                programIds: [],
                year: [],
                courseSchedule: [],
                level: [],
                rotation: [],
                rotationCount: [],
                courses: [],
            };
        }

        if (_course_id) {
            csQuery._course_id = convertToMongoObjectId(_course_id);
            csQuery._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
            csQuery._program_id = convertToMongoObjectId(_program_id);
            csQuery.year_no = year_no;
            csQuery.level_no = level_no;
            csQuery.term = term;
            if (rotation) {
                csQuery.rotation = rotation;
            }
            if (rotation_count) {
                csQuery.rotation_count = rotation_count;
            }
        }
        if (orQuery.length) {
            csQuery.$or = orQuery;
        }
        const csProject = {
            _course_id: 1,
            session: 1,
            student_groups: 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            end: 1,
            start: 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            _infra_id: 1,
            infra_id: '$_infra_id',
            infra_name: 1,
            staffs: 1,
            status: 1,
            sessionDetail: 1,
            students: 1,
            uuid: 1,
            socket_port: 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            isActive: 1,
            isDeleted: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            zoomDetail: 1,
            classModeType: 1,
        };
        let courseSchedule = await CourseSchedule.find(csQuery, csProject)
            .sort({ 'sessions.delivery_symbol': 1, 'sessions.delivery_no': 1 })
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .lean();
        let mergedSchedules = courseSchedule.filter(
            (courseScheduleEntry) => courseScheduleEntry.merge_status,
        );
        mergedSchedules = mergedSchedules.map((mergedSchedule) => mergedSchedule.merge_with);
        // eslint-disable-next-line no-sequences
        mergedSchedules = mergedSchedules.reduce((r, e) => (r.push(...e), r), []);
        const mergedScheduleIds = mergedSchedules.map((mergedSchedule) =>
            convertToMongoObjectId(mergedSchedule.schedule_id),
        );
        mergedSchedules = await CourseSchedule.find({ _id: { $in: mergedScheduleIds } }, csProject);

        courseSchedule = courseSchedule.map((schedule) => {
            let sumOfRatings = 0;
            let count = 0;
            let mergedStudents = [];
            const { students, _infra_id, staffs, merge_status, merge_with } = schedule;
            const startBy = staffs.find(
                (staff) => staff._staff_id.toString() === staffId.toString(),
            );
            mergedStudents = mergedStudents.concat(students);

            if (merge_status) {
                const mergedWithSchedules = merge_with.map((mergeWith) => mergeWith.schedule_id);
                mergedWithSchedules.forEach((mergedWithSchedule) => {
                    const mergedWithScheduleStudents = mergedSchedules.find(
                        (mergedSchedule) =>
                            mergedSchedule._id.toString() === mergedWithSchedule.toString(),
                    );
                    if (mergedWithScheduleStudents) {
                        mergedStudents = mergedStudents.concat(mergedWithScheduleStudents.students);
                    }
                });
            }
            if (mergedStudents && mergedStudents.length) {
                sumOfRatings += mergedStudents
                    .filter((student) => student.feedBack && student.feedBack.rating)
                    .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                    .reduce((a, b) => a + b, 0);

                count += mergedStudents.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
            }

            if (count) {
                schedule.feedBack = {
                    _session_id: schedule._id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                };
            }
            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                schedule.infra_name = infraName;
            }
            return schedule;
        });

        const courseIds = [...new Set(courseSchedule.map((schedule) => cs(schedule._course_id)))];
        // Getting Course
        const programCalendarData = await ProgramCalendar.find(
            {},
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.level_no': 1,
                'level.term': 1,
                'level.year': 1,
                'level.end_date': 1,
                'level.start_date': 1,
                'level.course._course_id': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );

        const splittedCourses = [];
        for (const courseId of courseIds) {
            const filterCourseSchedule = courseSchedule.filter(
                (courseScheduleEntry) =>
                    cs(courseScheduleEntry._course_id) === cs(courseId) &&
                    courseScheduleEntry._institution_calendar_id.toString() ===
                        _institution_calendar_id.toString(),
            );
            // different calendar and year and level based courses
            const courses = filterCourseSchedule.reduce((acc, current) => {
                const x = acc.find(
                    (item) =>
                        item._institution_calendar_id.toString() ===
                            current._institution_calendar_id.toString() &&
                        item._program_id.toString() === current._program_id.toString() &&
                        item.year_no === current.year_no &&
                        item.level_no === current.level_no &&
                        item.term === current.term &&
                        ((item.rotation.toString() === 'yes' &&
                            item.rotation_count &&
                            current.rotation_count &&
                            item.rotation_count.toString() === current.rotation_count.toString()) ||
                            item.rotation.toString() === 'no'),
                );

                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            if (courses.length) {
                for (const course of courses) {
                    const {
                        _institution_calendar_id,
                        _program_id,
                        year_no,
                        level_no,
                        rotation,
                        rotation_count,
                        _course_id,
                        term,
                        course_name,
                        course_code,
                        program_name,
                    } = course;
                    courseParams.push({
                        _institution_calendar_id,
                        _program_id,
                        year_no,
                        level_no,
                        rotation,
                        rotation_count,
                        _course_id,
                        term,
                        course_name,
                        course_code,
                        program_name,
                    });
                    if (programCalendarData) {
                        const { level } = programCalendarData.find(
                            (calendarElement) =>
                                calendarElement._institution_calendar_id.toString() ===
                                    _institution_calendar_id.toString() &&
                                calendarElement._program_id.toString() === _program_id.toString(),
                        );
                        const levelsEntry = level.find(
                            (levelEntry) =>
                                levelEntry.year === year_no &&
                                levelEntry.level_no === level_no &&
                                levelEntry.term === term,
                        );
                        const { course: coursesEntry, rotation_course } = levelsEntry;
                        let dates;
                        if (rotation === 'yes') {
                            if (rotation_course.length) {
                                const rotationCourses = rotation_course.filter(
                                    (rotationCourse) =>
                                        rotationCourse.rotation_count.toString() ===
                                        rotation_count.toString(),
                                );
                                if (rotationCourses.length) {
                                    rotationCourses.forEach((rotationCourse) => {
                                        courseParams.push({
                                            _institution_calendar_id,
                                            _program_id,
                                            year_no,
                                            level_no,
                                            rotation,
                                            rotation_count: rotationCourse.rotation_count,
                                            _course_id,
                                            term,
                                            course_name,
                                            course_code,
                                            program_name,
                                        });
                                    });
                                }
                            }
                            dates = rotation_course
                                .filter(
                                    (rotationCourseEntry) =>
                                        rotationCourseEntry.rotation_count === rotation_count,
                                )
                                .map((rCourse) => rCourse.course)
                                .flat()
                                .find(
                                    (rotationCourse) =>
                                        rotationCourse._course_id.toString() ===
                                        _course_id.toString(),
                                );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                            course.rotation_count = rotation_count;
                        } else {
                            dates = coursesEntry.find(
                                (courseEntry) =>
                                    courseEntry._course_id.toString() === _course_id.toString(),
                            );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                        }
                    }
                    splittedCourses.push(course);
                }
            }
        }
        //start checking term
        if (term) {
            courseParams = courseParams.filter(
                (termEntry) => termEntry.term.toString() === term.toString(),
            );
        }
        //end checking term
        if (courseParams.length) {
            courseParams.forEach((courseParam) => {
                const {
                    _course_id,
                    term,
                    level_no,
                    year_no,
                    course_name,
                    course_code,
                    _institution_calendar_id,
                    _program_id,
                    program_name,
                } = courseParam;
                if (!courseIds.length || splittedCourses.length) {
                    const scheduleCourseIds = splittedCourses.map((splittedCourse) => {
                        return splittedCourse._course_id.toString();
                    });
                    const terms = splittedCourses
                        .filter(
                            (splittedCourse) =>
                                splittedCourse._course_id.toString() === _course_id.toString(),
                        )
                        .map((splittedCourse) => splittedCourse.term);
                    const level_nos = splittedCourses
                        .filter(
                            (splittedCourse) =>
                                splittedCourse._course_id.toString() === _course_id.toString(),
                        )
                        .map((splittedCourse) => splittedCourse.level_no);
                    const year_nos = splittedCourses
                        .filter(
                            (splittedCourse) =>
                                splittedCourse._course_id.toString() === _course_id.toString(),
                        )
                        .map((splittedCourse) => splittedCourse.year_no);
                    const rotationCounts = splittedCourses
                        .filter(
                            (splittedCourse) =>
                                splittedCourse._course_id.toString() === _course_id.toString(),
                        )
                        .map((splittedCourse) => splittedCourse.rotation_count);
                    if (
                        isCourseScheduleExist(
                            courseParam,
                            scheduleCourseIds,
                            terms,
                            level_nos,
                            year_nos,
                            rotationCounts,
                        )
                    ) {
                        if (programCalendarData) {
                            const { level } = programCalendarData.find(
                                (calendarElement) =>
                                    calendarElement._institution_calendar_id.toString() ===
                                        _institution_calendar_id.toString() &&
                                    calendarElement._program_id.toString() ===
                                        _program_id.toString(),
                            );
                            const levelsEntry = level.find(
                                (levelEntry) =>
                                    levelEntry.year === year_no &&
                                    levelEntry.level_no === level_no &&
                                    levelEntry.term === term,
                            );
                            if (levelsEntry) {
                                const { course: coursesEntry, rotation_course } = levelsEntry;
                                let dates;
                                let end_date;
                                let start_date;
                                if (rotation_course.length) {
                                    const rotationCourses = rotation_course.filter((rCourses) =>
                                        rCourses.course.find(
                                            (rotationCourse) =>
                                                rotationCourse._course_id.toString() ===
                                                _course_id.toString(),
                                        ),
                                    );
                                    if (rotationCourses.length) {
                                        rotationCourses.forEach((rotationCourse) => {
                                            const { rotation_count, course } = rotationCourse;
                                            const filterCourses = course.filter(
                                                (courseEntry) =>
                                                    courseEntry._course_id.toString() ===
                                                    _course_id.toString(),
                                            );
                                            if (filterCourses.length) {
                                                filterCourses.forEach((filterCourse) => {
                                                    splittedCourses.push({
                                                        _institution_calendar_id,
                                                        _program_id,
                                                        program_name,
                                                        term,
                                                        year_no,
                                                        level_no,
                                                        _course_id,
                                                        course_name,
                                                        course_code,
                                                        rotation: 'yes',
                                                        end_date: filterCourse.end_date,
                                                        start_date: filterCourse.start_date,
                                                        rotation_count,
                                                    });
                                                });
                                            }
                                        });
                                    }
                                } else {
                                    dates = coursesEntry.find(
                                        (courseEntry) =>
                                            courseEntry._course_id.toString() ===
                                            _course_id.toString(),
                                    );
                                    if (dates) {
                                        end_date = dates.end_date;
                                        start_date = dates.start_date;
                                    }
                                    splittedCourses.push({
                                        _institution_calendar_id,
                                        _program_id,
                                        program_name,
                                        term,
                                        year_no,
                                        level_no,
                                        _course_id,
                                        course_name,
                                        course_code,
                                        rotation: 'no',
                                        end_date,
                                        start_date,
                                    });
                                }
                            }
                        }
                    }
                }
            });
        }
        const filteredSchedule = splittedCourses.reduce((splittedCourse, splittedCourseEntry) => {
            const x = splittedCourse.find(
                (course) =>
                    course._course_id.toString() === splittedCourseEntry._course_id.toString() &&
                    course.term.toString() === splittedCourseEntry.term.toString() &&
                    course.level_no.toString() === splittedCourseEntry.level_no.toString() &&
                    ((course.rotation &&
                        course.rotation.toString() === 'yes' &&
                        course.rotation_count.toString() ===
                            splittedCourseEntry.rotation_count.toString()) ||
                        (course.rotation && course.rotation.toString() === 'no')),
            );
            if (!x) {
                return splittedCourse.concat([splittedCourseEntry]);
            }
            return splittedCourse;
        }, []);
        // eslint-disable-next-line prefer-spread
        const mergedCourses = [].concat.apply([], filteredSchedule);

        const programIds = [...new Set(courseSchedule.map((schedule) => schedule._program_id))];
        const year = [...new Set(courseSchedule.map((schedule) => schedule.year_no))];
        const level = [...new Set(courseSchedule.map((schedule) => schedule.level_no))];
        const courseTerm = [...new Set(courseSchedule.map((schedule) => schedule.term))];
        const rotations = [...new Set(courseSchedule.map((schedule) => schedule.rotation))];
        const rotationCount = [
            ...new Set(courseSchedule.map((schedule) => schedule.rotation_count)),
        ];
        // session Completed count
        const staffCompletedSessions = await getStaffCompletedSession(csQuery);
        return {
            courseIds,
            programIds,
            year,
            courseSchedule,
            level,
            rotation: rotations,
            rotationCount,
            courses: mergedCourses,
            term: courseTerm,
            courseParams,
            staffCompletedSessions,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getPrograms = async (programIds) => {
    try {
        const pQuery = { ...query, _id: { $in: programIds } };
        const pProject = { name: 1 };
        return (await getJSON(Program, pQuery, pProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getProgramName = (programs, programId) => {
    let programName;
    programs.forEach((program) => {
        if (cs(program._id) === cs(programId)) {
            programName = program.name;
        }
    });
    return programName;
};

const getStudentAttendedSession = (courseSchedules, studentId) => {
    let attended = 0;
    courseSchedules.forEach((cSchedule) => {
        if (!cSchedule.students) return;
        if (
            cSchedule.type === REGULAR &&
            cSchedule.students.find(
                (student) =>
                    student.status === 'present' &&
                    student._id &&
                    studentId &&
                    cs(student._id) === cs(studentId),
            )
        ) {
            attended++;
        }
    });
    return attended;
};

const getSessions = (
    courseSchedules,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    userId,
    rotation,
    rotation_count,
    getSchedule,
) => {
    try {
        let courseSessions = [];
        const courseSchedulesEntry =
            rotation === 'yes'
                ? courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation &&
                          rotation_count &&
                          rotation_count === courseSchedule.rotation_count,
                  )
                : courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation,
                  );

        const isExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter((courseSession) => {
                if (
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    courseSession._session_id &&
                    sessionIdOrType &&
                    courseSession._session_id.toString() === sessionIdOrType.toString()
                ) {
                    if (
                        mergeStatus &&
                        scheduleId.toString() === courseSession.scheduleId.toString()
                    ) {
                        return true;
                    }
                    if (!mergeStatus) {
                        return true;
                    }
                }
                return false;
            }).length;

        const isSessionTypeExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter(
                (courseSession) =>
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    !courseSession._session_id &&
                    courseSession.session_topic === sessionIdOrType &&
                    courseSession.scheduleId &&
                    scheduleId.toString() === courseSession.scheduleId.toString(),
            ).length;

        courseSchedulesEntry.forEach((courseSchedule) => {
            courseSchedule.student_groups = courseSchedule.student_groups.map((studentGroup) => {
                studentGroup.students = [];
                return studentGroup;
            });
            if (
                courseSchedule.session &&
                !isExist(
                    courseSchedule.merge_status,
                    courseSchedule.session._session_id,
                    courseSchedule._id,
                )
            ) {
                const { _id, session, merge_status, merge_with, student_groups, status } =
                    courseSchedule;
                session.merge_status = merge_status;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (session && !merge_status) {
                    session.status = status;
                    session.student_groups = student_groups;
                    session.scheduleId = _id;
                    courseSessions.push(session);
                } else {
                    const mergedSessions = [];
                    const mergedStudents = [];
                    merge_with.forEach((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                    mergeWith.schedule_id.toString() &&
                                courseScheduleEntry.session &&
                                courseScheduleEntry.session._session_id.toString() ===
                                    mergeWith.session_id.toString(),
                        );
                        if (sessionDetails) {
                            mergeWith.session = {
                                _session_id: sessionDetails.session._session_id,
                                s_no: sessionDetails.session.s_no,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                                topic: sessionDetails.topic,
                            };
                            if (sessionDetails.students && sessionDetails.students.length) {
                                mergedStudents.push(sessionDetails.students);
                            }
                        }
                        mergedSessions.push(mergeWith);
                    });
                    const mergedScheduleIds = merge_with.map((mergeWith) =>
                        mergeWith.schedule_id.toString(),
                    );
                    let sessionMerged = merge_with.filter((mergeWith) => mergeWith.session_id);
                    sessionMerged = sessionMerged.map((mergeWith) =>
                        mergeWith.session_id.toString(),
                    );
                    courseSchedule.merge_with = mergedSessions;
                    if (mergedStudents.length) {
                        if (courseSchedule.students && courseSchedule.students.length)
                            mergedStudents.push(courseSchedule.students);
                        // eslint-disable-next-line prefer-spread
                        const concatMergedStudents = [].concat.apply([], mergedStudents);
                        //Sort the result to show beedback first then only we can identify using find if merge is scheduled
                        const sortFeedbackBasedStudents = concatMergedStudents.sort(function (
                            a,
                            b,
                        ) {
                            return b.feedBack ? 1 : -1;
                        });
                        // duplicate student removed
                        const students = sortFeedbackBasedStudents.reduce((acc, current) => {
                            const x = acc.find(
                                (item) => item._id.toString() === current._id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                        courseSchedule.students = students;
                    }
                    const duplicateMergedSession = courseSessions.find(
                        (courseSession) =>
                            courseSession._session_id &&
                            sessionMerged.includes(courseSession._session_id.toString()) &&
                            courseSession.merge_status.toString() ===
                                courseSchedule.merge_status.toString() &&
                            mergedScheduleIds.includes(courseSession.scheduleId.toString()),
                    );

                    const mergedSessionSchedules = courseSchedulesEntry.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    session.scheduleId = _id;
                    if (!duplicateMergedSession) courseSessions.push(session);
                }
            }
            // splitted to session type based
            const sessionTypes = [EVENT, SUPPORT_SESSION];
            if (
                courseSchedule.type &&
                sessionTypes.includes(courseSchedule.type) &&
                !isSessionTypeExist(
                    courseSchedule.merge_status,
                    courseSchedule.title,
                    courseSchedule._id,
                )
            ) {
                const { type, merge_status, student_groups, merge_with } = courseSchedule;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (type) {
                    courseSessions.push({
                        scheduleId: courseSchedule._id,
                        session_type: courseSchedule.type,
                        session_topic: courseSchedule.title,
                        merge_status,
                        student_groups,
                    });
                }
                if (merge_status) {
                    const mergedSessions = merge_with.map((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString(),
                        );
                        mergeWith.session = {
                            session_type: sessionDetails.type,
                            session_topic: sessionDetails.title,
                        };
                        return mergeWith;
                    });
                    courseSchedule.merge_with = mergedSessions;
                    courseSessions.push(session);
                }
            }
        });

        let totalSessions = 0;
        let completedSessions = 0;
        let staffCompletedSessions = 0;
        let warningCount = 0;
        let presentCount = 0;
        let leaveCount = 0;
        let absentCount = 0;
        // sort by session
        let courseSessionsRegular = courseSessions.filter(
            (courseSession) => courseSession._session_id,
        );
        const courseSessionsOthers = courseSessions.filter(
            (courseSession) => !courseSession._session_id,
        );
        courseSessionsRegular = courseSessionsRegular.sort((a, b) =>
            a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
        );
        courseSessions = courseSessionsRegular.concat(courseSessionsOthers);
        const courseScheduleStringify = clone(courseSchedulesEntry);
        courseSessions.forEach((courseSession) => {
            const { merge_status: courseSessionMergeStatus, scheduleId } = courseSession;
            // courseSession.documentCount = 0; // To Do
            // courseSession.activityCount = 0; // To Do
            const schedules = courseSession._session_id
                ? courseScheduleStringify.filter((courseSchedule) => {
                      if (
                          courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          if (
                              courseSessionMergeStatus &&
                              scheduleId.toString() === courseSchedule._id.toString()
                          ) {
                              return true;
                          }
                      } else if (
                          !courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          return true;
                      }
                  })
                : courseScheduleStringify.filter(
                      (courseSchedule) =>
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSession.session_topic === courseSchedule.title &&
                          courseSchedule._id &&
                          courseSchedule._id.toString() === scheduleId.toString(),
                  );
            courseSession.schedules = schedules;
        });
        courseSessions.forEach((courseSession) => {
            const { schedules } = courseSession;
            const courseSchedules = [];
            schedules.forEach((schedule) => {
                const {
                    start: { hour: startHour, minute: startMinute, format: startFormat },
                    end: { hour: endHour, minute: endMinute, format: endFormat },
                    schedule_date,
                } = schedule;

                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    startHours,
                    startMinute,
                    0,
                );
                const endDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    endHours,
                    endMinute,
                    0,
                );
                schedule.start_date = startDateAndTime;
                schedule.end_date = endDateAndTime;
                courseSchedules.push(schedule);
            });
            courseSession.schedules = courseSchedules;
        });
        courseSessions.forEach((courseSession) => {
            const { schedules } = courseSession;
            courseSession.absentCount = 0;
            courseSession.leaveCount = 0;
            courseSession.warningCount = 0;
            courseSession.presentCount = 0;
            courseSession.totalSchedules = 0;
            schedules.forEach((cSchedule) => {
                if (
                    cSchedule.session &&
                    cSchedule.session._session_id &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students &&
                    cSchedule.students.length
                ) {
                    cSchedule.students.forEach((student) => {
                        if (cs(student._id) === cs(userId)) {
                            if (student.status === 'absent' || student.status === 'pending')
                                courseSession.absentCount++;
                            if (student.status === 'present') {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (
                                student.status === 'leave' ||
                                student.status === 'on_duty' ||
                                student.status === 'permission'
                            )
                                courseSession.leaveCount++;
                            if (student.status === 'leave') courseSession.warningCount++; //To Do
                        }
                    });
                }
                cSchedule.staffs.forEach((staff) => {
                    if (cSchedule.status === COMPLETED && cs(staff._staff_id) === cs(userId)) {
                        if (staff.status === 'absent' || staff.status === 'pending')
                            courseSession.absentCount++;
                        if (staff.status === 'present') {
                            courseSession.presentCount++;
                            //form adding merge sessions count
                            if (cSchedule.merge_status) {
                                courseSession.presentCount += cSchedule.merge_with.length;
                            }
                        }
                        if (
                            staff.status === 'leave' ||
                            staff.status === 'on_duty' ||
                            staff.status === 'permission'
                        )
                            courseSession.leaveCount++;
                        if (staff.status === 'leave') courseSession.warningCount++;
                    }
                });
                if (cSchedule.isActive === true) courseSession.totalSchedules++;

                if (!getSchedule) {
                    cSchedule.studentsCount = cSchedule.students.length;
                    cSchedule.students = [];
                }
            });
            warningCount += courseSession.warningCount;
            presentCount += courseSession.presentCount;
            leaveCount += courseSession.leaveCount;
            absentCount += courseSession.absentCount;
            courseSession.schedules = schedules;
        });

        totalSessions = courseSessions.filter(
            (courseSession) =>
                courseSession._session_id &&
                courseSession.schedules.find((schedule) => schedule.isActive),
        ).length;
        //form adding merge sessions count
        const mergedSessions = courseSessions.filter((courseSession) => courseSession.merge_status);
        if (mergedSessions) {
            for (mergedSessionsEntry of mergedSessions) {
                totalSessions += mergedSessionsEntry.merge_with.length;
            }
        }
        const groupNames = [];

        for (const courseSession of courseSessions) {
            if (courseSession.student_groups) {
                for (const studentGroup of courseSession.student_groups) {
                    if (
                        studentGroup.group_name &&
                        !groupNames.find((groupName) => groupName === studentGroup.group_name)
                    ) {
                        groupNames.push(studentGroup.group_name);
                    }
                }
            }
        }

        const uniqueSessionTypes = [
            ...new Set(
                courseSessions
                    .map((type) => {
                        if (type._session_id) return type.session_type;
                    })
                    .filter((item) => item),
            ),
        ];
        const sessDetails = courseSessions.map((courseSession) => {
            const groupNamesArray = [];

            if (courseSession.student_groups) {
                courseSession.student_groups.forEach((groupName) => {
                    groupNamesArray.push(groupName.group_name);
                });
            }
            courseSession.groupNames = groupNamesArray;
            return courseSession;
        });
        const courseSessionDetails = groupNames.map((groupName) => {
            const totalGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );
            const completedGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === COMPLETED &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const pendingGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === PENDING &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const courseSessions = uniqueSessionTypes.map((uniqueSessionType) => {
                const completedCount = completedGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === COMPLETED &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                const pendingCount = pendingGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === PENDING &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;
                const totalCount = totalGroup.filter(
                    (sessionData) =>
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                return {
                    deliveryName: uniqueSessionType,
                    totalCount,
                    completedCount,
                    pendingCount,
                };
            });
            const totalGroups = totalGroup.length;
            return {
                studentGroupName: groupName.split('-').splice(-2).join('-'),
                totalSessions: totalGroups,
                completedSessions: completedGroup.length,
                pendingSessions: pendingGroup.length,
                courseSessions,
            };
        });
        courseSessions.forEach((courseSession) => {
            if (courseSession._session_id) {
                const courseSchedules = courseSession.schedules.filter(
                    (courseSchedule) =>
                        courseSchedule.isActive === true &&
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        courseSession._session_id &&
                        courseSchedule.session._session_id.toString() ===
                            courseSession._session_id.toString(),
                );
                const sessionCompleted = courseSchedules.filter(
                    (courseSchedule) => courseSchedule.status === COMPLETED,
                );
                if (
                    sessionCompleted.length &&
                    courseSchedules.length &&
                    sessionCompleted.length === courseSchedules.length
                ) {
                    completedSessions++;
                    //form adding merge sessions count
                    const mergedCompletedSessions = sessionCompleted.filter(
                        (courseSession) => courseSession.merge_status,
                    );
                    if (mergedCompletedSessions) {
                        for (mergedCompletedSessionsEntry of mergedCompletedSessions) {
                            completedSessions += mergedCompletedSessionsEntry.merge_with.length;
                        }
                    }
                }
            }
        });
        courseSessions.forEach((courseSession) => {
            if (courseSession._session_id) {
                const courseSchedules = courseSession.schedules.filter(
                    (courseSchedule) =>
                        courseSchedule.isActive === true &&
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        courseSession._session_id &&
                        courseSchedule.session._session_id.toString() ===
                            courseSession._session_id.toString(),
                );
                const sessionCompleted = courseSchedules.filter(
                    (courseSchedule) => courseSchedule.status === COMPLETED,
                );
                if (sessionCompleted.length) {
                    staffCompletedSessions += sessionCompleted.length;
                    //form adding merge sessions count
                    const mergedCompletedSessions = sessionCompleted.filter(
                        (courseSession) => courseSession.merge_status,
                    );
                    if (mergedCompletedSessions) {
                        for (mergedCompletedSessionsEntry of mergedCompletedSessions) {
                            staffCompletedSessions +=
                                mergedCompletedSessionsEntry.merge_with.length;
                        }
                    }
                }
            }
        });
        const attendedSessions = getStudentAttendedSession(courseSchedulesEntry, userId);
        return {
            courseSessionDetails,
            warningCount,
            presentCount,
            totalSessions,
            completedSessions,
            staffCompletedSessions,
            courseSessions,
            attendedSessions,
            leaveCount,
            absentCount,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getAllCourses = async (staffId, institutionCalendarId) => {
    try {
        const {
            courseIds,
            courseSchedule,
            courses,
            year,
            level,
            rotation,
            term,
            rotationCount,
            staffCompletedSessions,
        } = await getCourseIdsWithSessions(staffId, '', '', institutionCalendarId);
        const feedBackData = await getRatingByCourses(
            institutionCalendarId,
            courses,
            courseIds,
            staffId,
            year,
            level,
            term,
            rotation,
            rotationCount,
        );
        const courseIdsResults = courses.map((courseId) => {
            return courseId._course_id;
        });

        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIdsResults },
            },
            {
                _id: 1,
                course_type: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        ).lean();
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                level_no,
                year_no,
                isActive,
                subjects,
                rotation,
                end_date,
                start_date,
                rotation_count,
                term: courseTerm,
            } = course;
            const result = getSessions(
                courseSchedule,
                courseId,
                _institution_calendar_id,
                _program_id,
                year_no,
                level_no,
                courseTerm,
                staffId,
                rotation,
                rotation_count,
            );
            const {
                courseSessions,
                courseSessionDetails,
                totalSessions,
                completedSessions,
                attendedSessions,
                presentCount,
                warningCount,
                leaveCount,
                absentCount,
            } = result;
            let feedback;
            let rotationCount;
            if (!course.rotation_count) {
                rotationCount = '';
            } else {
                rotationCount = course.rotation_count;
            }

            const feedBacks = rotationCount
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          feedBackDetail.rotationCount === rotationCount &&
                          feedBackDetail.term === courseTerm,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          feedBackDetail.term === courseTerm,
                  );
            if (feedBacks) feedback = feedBacks;

            let course_type;
            let versionNo = 1;
            let versioned = false;
            let versionName = '';
            let versionedFrom = null;
            let versionedCourseIds = [];
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) {
                course_type = courseTypes.course_type;
                versionNo = courseTypes.versionNo || versionNo;
                versioned = courseTypes.versioned || versioned;
                versionName = courseTypes.versionName || versionName;
                versionedFrom = courseTypes.versionedFrom || versionedFrom;
                versionedCourseIds = courseTypes.versionedCourseIds || versionedCourseIds;
            }
            return {
                _id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                courseSessionDetails,
                totalSessions,
                completedSessions,
                staffCompletedSessions,
                attendedSessions,
                term: courseTerm,
                leaveCount,
                absentCount,
                warningCount,
                presentCount,
                subjects,
                year: year_no,
                level: level_no,
                isActive,
                feedback,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
                versionNo,
                versioned,
                versionName,
                versionedFrom,
                versionedCourseIds,
            };
        });
        return coursesList;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourse = async (
    staffId,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    type,
    getSchedule,
) => {
    try {
        let result = await getCourseIdsWithSessions(
            staffId,
            type,
            _course_id,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
        );
        const { courseSchedule, courses: sessionCourses, staffCompletedSessions } = result;
        let courses;
        if (_course_id) {
            courses = sessionCourses.filter(
                (sessionCourse) => sessionCourse._course_id.toString() === _course_id.toString(),
            );
        } else {
            courses = sessionCourses;
        }
        const courseIds = courses.map((courseId) => {
            return courseId._course_id;
        });
        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIds },
            },
            { _id: 1, course_type: 1 },
        );
        let allScheduleIds = courseSchedule.map((schedule) => {
            return schedule.session
                ? schedule.session._session_id.toString()
                : schedule._id.toString();
        });
        allScheduleIds = [...new Set(allScheduleIds)];
        allScheduleIds = allScheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));
        //todo work for single get schedule add document ,activities count changes
        // let activities = [];
        // let documents = [];
        // if (getSchedule) {
        //     const activityQuery = {
        //         $or: [
        //             { scheduleIds: { $in: allScheduleIds } },
        //             { 'sessionFlowIds._id': { $in: allScheduleIds } },
        //         ],
        //         isDeleted: false,
        //         _program_id: convertToMongoObjectId(_program_id),
        //         year_no,
        //         level_no,
        //         term,
        //         rotation,
        //         _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        //     };
        //     if (rotation_count) {
        //         activityQuery.rotation_count = rotation_count;
        //     }
        //     activities = (
        //         await getJSON(Activity, activityQuery, {
        //             sessionFlowIds: 1,
        //             scheduleIds: 1,
        //             _program_id: 1,
        //             courseId: 1,
        //             year_no: 1,
        //             level_no: 1,
        //             term: 1,
        //             rotation_count: 1,
        //             rotation: 1,
        //             _institution_calendar_id: 1,
        //         })
        //     ).data;
        //     documents = (
        //         await getJSON(
        //             Document,
        //             {
        //                 'sessionOrScheduleIds._id': { $in: allScheduleIds },
        //                 isDeleted: false,
        //                 isActive: true,
        //             },
        //             {},
        //         )
        //     ).data;
        // }
        const feedBackData = await getRatingByCourses(
            _institution_calendar_id,
            sessionCourses,
            _course_id,
            staffId,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
        );
        const staffFeedBacks = await getCourseRatingForStaffs(
            _institution_calendar_id,
            _course_id,
            year_no,
            level_no,
            rotation,
            rotation_count,
            term,
        );
        const deniedStudents = [];
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id: calendarId,
                _program_id: programId,
                program_name,
                course_name,
                course_code,
                level_no: levelNo,
                year_no: yearNo,
                term: courseTerm,
                isActive,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;
            result = getSessions(
                courseSchedule,
                courseId,
                calendarId,
                programId,
                yearNo,
                levelNo,
                courseTerm,
                staffId,
                rotation,
                rotation_count,
                getSchedule,
            );
            let feedback;
            const feedBacks = rotation_count
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.term === courseTerm &&
                          feedBackDetail.rotation === rotation &&
                          rotation_count &&
                          feedBackDetail.rotationCount === rotation_count,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.term === courseTerm &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) feedback = feedBacks;
            const {
                courseSessionDetails,
                absentCount,
                leaveCount,
                warningCount,
                presentCount,
                totalSessions,
                completedSessions,
                courseSessions,
                attendedSessions,
            } = result;

            let course_type;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) course_type = courseTypes.course_type;

            return {
                _id: courseId,
                _program_id: programId,
                courseSessionDetails,
                warningCount,
                presentCount,
                absentCount,
                leaveCount,
                program_name,
                course_name,
                course_code,
                totalSessions,
                completedSessions,
                staffCompletedSessions,
                attendedSessions,
                year: yearNo,
                level: levelNo,
                isActive,
                feedback,
                staffFeedBacks,
                sessions: courseSessions,
                _institution_calendar_id: calendarId,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
                deniedStudents,
                term: courseTerm,
            };
        });
        for (const course of coursesList) {
            // todo count changes
            // if (getSchedule) {
            //     for (const session of course.sessions) {
            //         session.documentCount = 0;
            //         session.activityCount = 0;
            //         for (const schedule of session.schedules) {
            //             const {
            //                 _program_id,
            //                 _course_id: courseId,
            //                 year_no,
            //                 level_no,
            //                 term,
            //                 rotation_count,
            //                 rotation,
            //                 _institution_calendar_id,
            //             } = schedule;
            //             schedule.documentCount = 0;
            //             schedule.activityCount = 0;
            //             if (schedule.session && schedule.session._session_id) {
            //                 const docs = documents.filter((doc) =>
            //                     doc.sessionOrScheduleIds
            //                         .map((id) => id._id.toString())
            //                         .includes(schedule.session._session_id.toString()),
            //                 );

            //                 schedule.documentCount = docs.length;
            //                 const filteredActivities = activities.filter((activity) => {
            //                     if (
            //                         activity._program_id.toString() === _program_id.toString() &&
            //                         activity.courseId.toString() === courseId.toString() &&
            //                         activity.year_no === year_no &&
            //                         activity.level_no === level_no &&
            //                         activity.term === term &&
            //                         activity.rotation === rotation &&
            //                         activity._institution_calendar_id.toString() ===
            //                             _institution_calendar_id.toString() &&
            //                         activity.sessionFlowIds.find(
            //                             (sessionFlowIdElement) =>
            //                                 sessionFlowIdElement._id.toString() ===
            //                                 schedule.session._session_id.toString(),
            //                         )
            //                     ) {
            //                         return true;
            //                     }
            //                     return false;
            //                 });
            //                 schedule.activityCount += filteredActivities.length;
            //             } else {
            //                 const docs = documents.filter((doc) =>
            //                     doc.sessionOrScheduleIds
            //                         .map((id) => id._id.toString())
            //                         .includes(schedule._id.toString()),
            //                 );
            //                 schedule.documentCount += docs.length;
            //                 const filteredActivities = activities.filter(
            //                     (activity) =>
            //                         activity.scheduleIds &&
            //                         activity.scheduleIds.find(
            //                             (scheduleElement) =>
            //                                 scheduleElement.toString() === schedule._id.toString(),
            //                         ),
            //                 );
            //                 schedule.activityCount += filteredActivities.length;
            //             }
            //             session.documentCount = schedule.documentCount;
            //             session.activityCount = schedule.activityCount;
            //         }
            //     }
            // }
            const feedBacks = rotation_count
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          feedBackDetail.term === term &&
                          rotation_count &&
                          feedBackDetail.rotationCount === rotation_count,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.term === term &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) course.feedback = feedBacks;
        }
        return coursesList;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseDocuments = async (courseId, sessionId) => {
    try {
        const dQuery = { ...query };
        if (courseId) dQuery._course_id = convertToMongoObjectId(courseId);
        if (sessionId) dQuery._session_flow_id = convertToMongoObjectId(sessionId);
        const dProject = {
            type: 1,
            name: 1,
            url: 1,
            _course_id: 1,
            _session_flow_id: 1,
            shared: 1,
        };
        return (await getJSON(Document, dQuery, dProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getScheduleById = async (scheduleId) => {
    try {
        csQuery = { ...query, _id: convertToMongoObjectId(scheduleId) };
        csProject = {};
        return (await getJSON(CourseSchedule, csQuery, csProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

// get question
const formatQuestions = async (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentCorrectAnswered = 0;
    for (const activityQuestion of activityQuestions) {
        const { _id, options } = activityQuestion;
        if (options && options.length > 0) {
            for (const option of options) {
                const { _id: optionId, answer: optionAnswer } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = optionAnswer;
                    studentAnsweredOptionId = optionId;
                }
            }
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }
    }
    return {
        questions,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

const getScheduleByDate = async (userId, date, timeZone) => {
    try {
        csQuery = {
            $or: [
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
                { 'students._id': convertToMongoObjectId(userId) },
            ],
            ...query,
        };
        const timeZonedStartDate = dateTimeBasedConverter(date, timeZone);
        const timeZonedBeginDate = timeZonedStartDate.clone().startOf('day');
        const timeZonedEndDate = timeZonedStartDate.clone().endOf('day');
        csQuery.scheduleStartDateAndTime = {
            $gte: new Date(timeZonedBeginDate),
            $lte: new Date(timeZonedEndDate),
        };
        const start = new Date(timeZonedStartDate);
        const startTimestamp = new Date(timeZonedStartDate).getTime();
        csProject = {
            isDeleted: 0,
            createdAt: 0,
            updatedAt: 0,
        };
        let courseSchedules = await CourseSchedule.find(csQuery, csProject)
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .populate({
                path: '_course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        // if merged session exists
        courseSchedules = courseSchedules.map((courseSchedule) => {
            const {
                start: { hour: startHour, minute: startMinute, format: startFormat },
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
                _infra_id,
            } = courseSchedule;

            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                courseSchedule.infra_name = infraName;
            }

            if (
                !courseSchedule.scheduleStartDateAndTime &&
                !courseSchedule.scheduleEndDateAndTime
            ) {
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertingRiyadhToUTC(
                    schedule_date,
                    startHours,
                    startMinute,
                );
                const endDateAndTime = convertingRiyadhToUTC(schedule_date, endHours, endMinute);
                courseSchedule.scheduleStartDateAndTime = startDateAndTime;
                courseSchedule.scheduleEndDateAndTime = endDateAndTime;
            }

            if (courseSchedule.merge_status) {
                const mergedSessions = courseSchedule.merge_with.map((mergeWith) => {
                    const sessionDetails = courseSchedules.find(
                        (courseScheduleEntry) =>
                            courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString() &&
                            courseScheduleEntry.session &&
                            courseScheduleEntry.session._session_id.toString() ===
                                mergeWith.session_id.toString(),
                    );
                    if (sessionDetails) {
                        mergeWith.session = {
                            _session_id: sessionDetails.session._session_id,
                            s_no: sessionDetails.session.s_no,
                            delivery_symbol: sessionDetails.session.delivery_symbol,
                            delivery_no: sessionDetails.session.delivery_no,
                        };
                    }
                    return mergeWith;
                });
                courseSchedule.merge_with = mergedSessions;
            }
            courseSchedule.versionNo = courseSchedule._course_id.versionNo || 1;
            courseSchedule.versioned = courseSchedule._course_id.versioned || false;
            courseSchedule.versionName = courseSchedule._course_id.versionName || '';
            courseSchedule.versionedFrom = courseSchedule._course_id.versionedFrom || null;
            courseSchedule.versionedCourseIds = courseSchedule._course_id.versionedCourseIds || [];
            courseSchedule._course_id = courseSchedule._course_id._id;
            return courseSchedule;
        });

        // session duplicate removed
        const mergedCourseSchedules = [];
        courseSchedules.forEach((courseSchedule) => {
            const { merge_status, merge_with, student_groups } = courseSchedule;
            if (merge_status) {
                const mergedScheduleIds = merge_with.map((mergedWith) => mergedWith.schedule_id);
                const duplicateSessions = mergedScheduleIds.filter((mergedScheduleId) =>
                    mergedCourseSchedules.find(
                        (mergedCourseSchedule) =>
                            mergedCourseSchedule._id.toString() === mergedScheduleId.toString(),
                    ),
                );
                if (duplicateSessions.length === 0) {
                    const mergedSessionSchedules = courseSchedules.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    mergedCourseSchedules.push(courseSchedule);
                }
            } else {
                mergedCourseSchedules.push(courseSchedule);
            }
        });
        courseSchedules = mergedCourseSchedules;
        const programId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._program_id)),
        ][0];
        const courseId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._course_id._id)),
        ][0];
        const institutionCalenderId = [
            ...new Set(
                courseSchedules.map((courseSchedule) => courseSchedule._institution_calendar_id),
            ),
        ][0];
        const institutionId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._institution_id)),
        ][0];
        const term = [...new Set(courseSchedules.map((courseSchedule) => courseSchedule.term))][0];
        const levelNo = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule.level_no)),
        ][0];
        const { status: programCalendarStatus, data: programCalendars } = await get(
            ProgramCalendar,
            {
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalenderId),
                'level.term': term,
                'level.level_no': levelNo,
            },
            {},
        );
        if (programCalendarStatus && programCalendars) {
            const programCalenderLevels = programCalendars.level.find(
                (programCalendar) =>
                    programCalendar.level_no === levelNo && programCalendar.term === term,
            );
            let programCalenderCourseEvents = programCalenderLevels.course.find(
                (courseEntry) => courseEntry._course_id.toString() === courseId.toString(),
            );
            if (programCalenderCourseEvents && programCalenderCourseEvents.courses_events) {
                programCalenderCourseEvents = programCalenderCourseEvents.courses_events;
            } else {
                programCalenderCourseEvents = [];
            }
            programCalenderCourseEvents = programCalenderCourseEvents.filter(
                (programCalenderCourseEvent) =>
                    new Date(programCalenderCourseEvent.event_date).getTime() === startTimestamp,
            );
            const programCalenderEvents = programCalenderLevels.events.filter(
                (programCalenderLevel) =>
                    new Date(programCalenderLevel.event_date).getTime() === startTimestamp,
            );
            if (programCalenderCourseEvents.length) {
                programCalenderCourseEvents = programCalenderCourseEvents.map(
                    (programCalenderEvent) => {
                        let startDate = programCalenderEvent.start_time.setHours(
                            programCalenderEvent.start_time.getHours() + 5,
                        );
                        startDate += 30 * 60000;
                        let endDate = programCalenderEvent.end_time.setHours(
                            programCalenderEvent.end_time.getHours() + 5,
                        );
                        endDate += 30 * 60000;
                        return {
                            _id: programCalenderEvent._id,
                            _event_id: programCalenderEvent._event_id,
                            event_type: programCalenderEvent.event_type,
                            event_name: programCalenderEvent.event_name,
                            start_date: new Date(startDate),
                            end_date: new Date(endDate),
                            type: 'calender_event',
                        };
                    },
                );
                courseSchedules = courseSchedules.concat(programCalenderCourseEvents);
                courseSchedules = courseSchedules.reduce((acc, current) => {
                    const x = acc.find(
                        (item) =>
                            item._event_id &&
                            item._event_id.toString() === current._event_id.toString(),
                    );
                    if (!x) {
                        return acc.concat([current]);
                    }
                    return acc;
                }, []);
            }
            if (programCalenderEvents.length) {
                programCalenderEvent = programCalenderEvents.map((programCalenderEvent) => {
                    let startDate = programCalenderEvent.start_time.setHours(
                        programCalenderEvent.start_time.getHours() + 5,
                    );
                    startDate += 30 * 60000;
                    let endDate = programCalenderEvent.end_time.setHours(
                        programCalenderEvent.end_time.getHours() + 5,
                    );
                    endDate += 30 * 60000;
                    return {
                        _id: programCalenderEvent._id,
                        _event_id: programCalenderEvent._event_id,
                        event_type: programCalenderEvent.event_type,
                        event_name: programCalenderEvent.event_name,
                        start_date: new Date(startDate),
                        end_date: new Date(endDate),
                        type: 'calender_event',
                    };
                });
                courseSchedules = courseSchedules.concat(programCalenderEvents);
                courseSchedules = courseSchedules.reduce((acc, current) => {
                    const x = acc.find(
                        (item) =>
                            item._event_id &&
                            item._event_id.toString() === current._event_id.toString(),
                    );
                    if (!x) {
                        return acc.concat([current]);
                    }
                    return acc;
                }, []);
            }
        }
        //Remote/Extra Schedule Get
        const { status: courseScheduleSettingStatus, data: courseScheduleSettings } = await get(
            CourseScheduleSetting,
            {
                _institution_id: convertToMongoObjectId(institutionId),
                isActive: true,
                isDeleted: false,
            },
            {},
        );

        if (courseScheduleSettingStatus && courseScheduleSettings) {
            const programEvents = courseScheduleSettings.programs.find(
                (program) => program._program_id.toString() === programId.toString(),
            );
            const extraCurricularAndBreakTiming = programEvents.extraCurricularAndBreakTiming;
            let breakTimings = extraCurricularAndBreakTiming.filter((breakTiming) => {
                const { days, isDeleted } = breakTiming;
                const constantDays = Object.values(DAYS);
                let currentDay = new Date(start).getDay();
                currentDay = constantDays[currentDay];
                if (!isDeleted && days.includes(currentDay)) {
                    return true;
                }
                return false;
            });

            if (breakTimings.length) {
                breakTimings = breakTimings.map((breakTiming) => {
                    const {
                        startTime: { hour: startHour, minute: startMinute, format: startFormat },
                        endTime: { hour: endHour, minute: endMinute, format: endFormat },
                    } = breakTiming;

                    const startHours =
                        startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                    const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                    const startDateAndTime = new Date(
                        convertToUtcTimeFormat(start, startHours, startMinute, 0),
                    );
                    const endDateAndTime = new Date(
                        convertToUtcTimeFormat(start, endHours, endMinute, 0),
                    );
                    return {
                        days: breakTiming.days,
                        startTime: breakTiming.startTime,
                        endTime: breakTiming.endTime,
                        allowCourseCoordinatesToEdit: breakTiming.allowCourseCoordinatesToEdit,
                        isDeleted: breakTiming.isDeleted,
                        _id: breakTiming._id,
                        _institution_calendar_id: breakTiming._institution_calendar_id,
                        title: breakTiming.title,
                        gender: breakTiming.gender,
                        mode: breakTiming.mode,
                        type: breakTiming.type,
                        sessionType: 'other',
                        start_date: startDateAndTime,
                        end_date: endDateAndTime,
                    };
                });
                courseSchedules = courseSchedules.concat(breakTimings);
            }
        }

        // today activity

        const end = new Date(timeZonedStartDate).getTime();
        // end = new Date(end).getTime();

        const activities = await Activity.find({
            'schedule.startDateAndTime': { $lte: new Date(end), $gte: new Date(start) },
            status: { $ne: DRAFT },
            type: SCHEDULE,
            isDeleted: false,
        })
            .populate({ path: 'courseId', select: { course_code: 1, course_name: 1 } })
            .lean();
        if (activities.length) {
            // Gathering Datas from certain collections
            // Questions gathering
            let activityQuestionIds = [];
            let activitySessionIds = [];
            let activityCourseScheduleIds = [];
            for (const activityElement of activities) {
                activityQuestionIds = [
                    ...activityQuestionIds,
                    ...activityElement.questions.map((question) =>
                        convertToMongoObjectId(question._id),
                    ),
                ];
                activitySessionIds = [
                    ...activitySessionIds,
                    ...activityElement.sessionFlowIds.map((sessionFlowId) =>
                        convertToMongoObjectId(sessionFlowId._id),
                    ),
                ];
                activityCourseScheduleIds = [
                    ...activityCourseScheduleIds,
                    ...activityElement.scheduleIds.map((scheduleId) =>
                        convertToMongoObjectId(scheduleId),
                    ),
                ];
            }
            const activityQuestionDatas = await Question.find({
                _id: { $in: activityQuestionIds },
                isDeleted: false,
            });
            // Course Schedule Data gathering
            const courseScheduleList = await CourseSchedule.find(
                {
                    $or: [
                        {
                            'session._session_id': { $in: activitySessionIds },
                        },
                        {
                            _id: { $in: activityCourseScheduleIds },
                        },
                    ],
                },
                {},
            );
            // Merged Session gathering
            let mergedScheduleIds = [];
            for (scheduleElement of courseScheduleList) {
                if (scheduleElement.merge_status) {
                    mergedScheduleIds = [
                        ...mergedScheduleIds,
                        ...scheduleElement.merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        ),
                    ];
                }
            }
            let mergedCourseScheduleData = [];
            if (mergedScheduleIds.length)
                mergedCourseScheduleData = await CourseSchedule.find({
                    _id: { $in: mergedScheduleIds },
                });

            const activityDetails = [];

            for (const activity of activities) {
                const { scheduleIds, sessionId, sessionFlowIds, questions, students } = activity;

                const questionDetails = [];
                if (questions.length) {
                    let questionIds;
                    questionIds = questions.sort((a, b) => {
                        return a.order - b.order;
                    });
                    questionIds = questions.map((question) => question._id.toString());
                    for (questionIdElement of questionIds) {
                        const questionDataElement = activityQuestionDatas.find(
                            (ele) => ele._id.toString() === questionIdElement.toString(),
                        );
                        if (questionDataElement) questionDetails.push(questionDataElement);
                    }
                }
                const courseSchedules = courseScheduleList.filter((ele) =>
                    scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                );
                let studentGroupsName = '';
                for (const scheduleId of courseSchedules) {
                    const { merge_status, merge_with } = scheduleId;

                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const mergedSchedules = mergedCourseScheduleData.filter((ele) =>
                                scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                            );

                            mergedSchedules.push(scheduleId);
                            mergedSchedules.forEach((courseSchedule) => {
                                const { student_groups, _id } = courseSchedule;
                                if (student_groups && student_groups.length) {
                                    let studentGroups = student_groups.map((student_group) => {
                                        const { group_name, session_group } = student_group;
                                        let groupName = group_name.split('-').slice(-2);
                                        groupName = groupName[1]
                                            ? groupName[0] + '-' + groupName[1]
                                            : groupName[0];
                                        if (session_group && session_group.length) {
                                            let sessionGroup = session_group.map(
                                                (groupNameEntry) => {
                                                    let groupNames = groupNameEntry.group_name
                                                        .split('-')
                                                        .slice(-2);
                                                    groupNames = groupNames[1]
                                                        ? groupNames[0] + '-' + groupNames[1]
                                                        : groupNames[0];
                                                    return groupNames;
                                                },
                                            );
                                            sessionGroup = sessionGroup.toString();
                                            groupName += '(' + sessionGroup + ')';
                                        }
                                        return groupName;
                                    });
                                    studentGroups = studentGroups.toString();
                                    studentGroupsName += studentGroups;
                                }
                            });
                        }
                    } else {
                        const { student_groups } = scheduleId;
                        if (student_groups && student_groups.length) {
                            let studentGroups = student_groups.map((student_group) => {
                                const { group_name, session_group } = student_group;
                                let groupName = group_name.split('-').slice(-2);
                                groupName = groupName[1]
                                    ? groupName[0] + '-' + groupName[1]
                                    : groupName[0];
                                if (session_group && session_group.length) {
                                    let sessionGroup = session_group.map((groupNameEntry) => {
                                        let groupNames = groupNameEntry.group_name
                                            .split('-')
                                            .slice(-2);
                                        groupNames = groupNames[1]
                                            ? groupNames[0] + '-' + groupNames[1]
                                            : groupNames[0];
                                        return groupNames;
                                    });
                                    sessionGroup = sessionGroup.toString();
                                    groupName += '(' + sessionGroup + ')';
                                }
                                return groupName;
                            });
                            studentGroups = studentGroups.toString();
                            studentGroupsName += studentGroups;
                        }
                    }
                }
                const sessionIds = sessionFlowIds.map((sessionFlowId) =>
                    sessionFlowId._id.toString(),
                );
                const sessions = courseScheduleList
                    .filter(
                        (ele) =>
                            ele.session &&
                            ele.session._session_id &&
                            sessionIds.find(
                                (ele2) => ele2.toString() === ele.session._session_id.toString(),
                            ),
                    )
                    .map((ele3) => {
                        return {
                            _id: ele3._id,
                            title: ele3.title,
                            type: ele3.type,
                            session: ele3.session,
                        };
                    });
                const sessionDetails = sessionFlowIds.map((sessionId) => {
                    const sessionDetail = sessions.find(
                        (sessionEntry) =>
                            (sessionEntry.session &&
                                sessionEntry.session._session_id.toString() ===
                                    sessionId._id.toString()) ||
                            (sessionEntry.type === sessionId.type &&
                                sessionEntry._id.toString() === sessionId._id.toString()),
                    );
                    if (sessionDetail) {
                        const { session, _id, title, type } = sessionDetail;
                        if (session) {
                            const { _session_id } = session;
                            if (
                                session &&
                                _session_id &&
                                sessionIds.includes(_session_id.toString())
                            ) {
                                return {
                                    _id: session._session_id,
                                    s_no: session.s_no,
                                    delivery_symbol: session.delivery_symbol,
                                    delivery_no: session.delivery_no,
                                    session_type: session.session_type,
                                    session_topic: session.session_topic,
                                    type: REGULAR,
                                };
                            }
                        }
                        if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                            return { _id, title, type };
                        }
                    }
                });

                let answeredQuestions;
                answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
                if (answeredStudent) answeredQuestions = answeredStudent.questions;

                let activityQuestions;
                if (questionDetails && questionDetails.length) {
                    activityQuestions = questionDetails.map((selectedQuestion) => {
                        const { _id } = selectedQuestion;
                        const questionType = questions.find(
                            (q) => q._id.toString() === _id.toString(),
                        );
                        if (questionType) {
                            selectedQuestion.type = questionType.type;
                            selectedQuestion.order = questionType.order;
                        }
                        return selectedQuestion;
                    });
                    activityQuestions = await formatQuestions(activityQuestions, answeredQuestions);
                }
                const schedule = courseSchedules.find(
                    (scheduleId) =>
                        scheduleId.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleId.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );

                activityDetails.push({
                    activityId: activity._id,
                    status: activity.status,
                    name: activity.name,
                    quizType: activity.quizType,
                    schedule: activity.schedule,
                    questions: activity.questions,
                    socketEventStaffId: activity.socketEventStaffId,
                    staffStartWithExam: activity.staffStartWithExam,
                    socketEventStudentId: activity.socketEventStudentId,
                    totalStudentAnsweredCount: students ? students.length : 0,
                    totalStudentCount: schedule && schedule.students ? schedule.students.length : 0,
                    createdBy: activity.createdBy,
                    setQuizTime: activity.setQuizTime,
                    courseId: activity.courseId._id,
                    course_name: activity.courseId.course_name,
                    type: 'activity',
                    studentGroupName: studentGroupsName,
                    sessionId,
                    sessionType: schedule && schedule.type ? schedule.type : undefined,
                    _institution_calendar_id:
                        schedule && schedule._institution_calendar_id
                            ? schedule._institution_calendar_id
                            : undefined,
                    year_no: schedule && schedule.year_no ? schedule.year_no : undefined,
                    level_no: schedule && schedule.level_no ? schedule.level_no : undefined,
                    _program_id:
                        schedule && schedule._program_id ? schedule._program_id : undefined,
                    _course_id: schedule && schedule._course_id ? schedule._course_id : undefined,
                    merge_status: schedule ? schedule.merge_status : undefined,
                    _id: schedule && schedule._id ? schedule._id : undefined,
                    sessionFlowIds: sessionDetails,
                    rotation: schedule && schedule.rotation ? schedule.rotation : undefined,
                    rotation_count:
                        schedule && schedule.rotation_count ? schedule.rotation_count : undefined,
                });
            }
            courseSchedules = courseSchedules.concat(activityDetails);
        }
        return courseSchedules;
    } catch (error) {
        throw new Error(error);
    }
};

const getCoursesByStaffId = async (staffId) => {
    try {
        const courseIds = await getCourseIds(staffId, DC_STAFF);
        const cQuery = { ...query, _id: { $in: courseIds } };
        const cProject = {
            course_name: 1,
            course_code: 1,
            _program_id: 1,
            program_name: 1,
            term: 1,
            year_no: 1,
            level_no: 1,
            rotation: 1,
            rotation_count: 1,
            'course_assigned_details.year': 1,
        };
        const courses = (await getJSON(Course, cQuery, cProject)).data;
        const courseSchedules = (
            await getJSON(
                CourseSchedule,
                { _course_id: { $in: courseIds } },
                { _id: 1, _course_id: 1, year_no: 1, level_no: 1 },
            )
        ).data;

        const year = [];
        const level = [];
        const courseId = [];
        courseSchedules.map((courseDatas) => {
            courseId.push(courseDatas._course_id);
            year.push(courseDatas.year_no);
            level.push(courseDatas.level_no);
            rotation.push(courseDatas.rotation);
            rotationCount.push(courseDatas.rotation_count);
        });
        const feedBackData = await getRatingByCourses(
            courses,
            courseId,
            staffId,
            year,
            level,
            rotation,
            rotationCount,
        );

        courses.forEach((course) => {
            course.totalSessions = courseSchedules.filter(
                (cSchedules) => cs(cSchedules._course_id) === cs(course._id),
            ).length;
            course.completedSessions = courseSchedules.filter(
                (cSchedules) =>
                    cs(cSchedules._course_id) === cs(course._id) &&
                    cSchedules.status === 'completed',
            ).length;
            course.feedBack = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._course_id.toString() === course._id.toString() &&
                    feedBackDetail.level_no === course.level_no &&
                    feedBackDetail.year_no === course.year_no &&
                    feedBackDetail.rotation === course.rotation &&
                    course.rotation_count &&
                    feedBackDetail.rotation_count === course.rotation_count,
            );
        });
        return courses;
    } catch (error) {
        throw new Error(error);
    }
};

const getCoursesByStudentId = async (studentId) => {
    try {
        const csQuery = { 'students._id': studentId, isDeleted: false, isActive: true };
        const csProject = {};
        const courseSchedules = await CourseSchedule.find(csQuery, csProject);
        const courseIds = [...new Set(courseSchedules.map((schedule) => schedule._course_id))];
        const cQuery = { ...query, _id: { $in: courseIds } };
        const cProject = { course_name: 1, course_code: 1, _program_id: 1 };
        const courses = (await getJSON(Course, cQuery, cProject)).data;
        const getSessionCount = (_course_id, flag) => {
            if (flag === 'total') {
                return courseSchedules.filter(
                    (cSchedule) => cs(cSchedule._course_id) === cs(_course_id),
                ).length;
            }
            if (flag === COMPLETED) {
                return courseSchedules.filter(
                    (cSchedule) =>
                        cs(cSchedule._course_id) === cs(_course_id) &&
                        cSchedule.status === COMPLETED,
                ).length;
            }
            return courseSchedules.filter(
                (cSchedule) =>
                    cs(cSchedule._course_id) === cs(_course_id) &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students.find(
                        (student) => cs(student._id) === cs(studentId) && student.status === flag,
                    ),
            ).length;
        };
        courses.forEach((course) => {
            course.totalSessions = getSessionCount(course._id, 'total');
            course.completedSessions = getSessionCount(course._id, COMPLETED);
            course.attendedSessions = getSessionCount(course._id, PRESENT);
            course.absentSessions = getSessionCount(course._id, ABSENT);
            course.leaveSessions = getSessionCount(course._id, LEAVE);
            course.warningOfSessions = getSessionCount(course._id, ABSENT); // To Do
            course.startDate = new Date(); // To Do
            course.endDate = new Date(); // To Do
            course.level = 3;
        });
        return courses;
    } catch (error) {
        throw new Error(error);
    }
};

const getSloBySessionIdAndCourseId = async (courseId, sessionId) => {
    try {
        const query = {
            _id: convertToMongoObjectId(courseId),
            isDeleted: false,
            isActive: true,
        };
        const courseResult = await Course.findOne(query);
        const sessionSlos = [];
        if (courseResult) {
            const domains = courseResult.framework.domains;
            domains.forEach((domainItem) => {
                if (!domainItem.clo) return;
                domainItem.clo.forEach((cloItem) => {
                    if (cloItem.slos && cloItem.slos.length) {
                        cloItem.slos.forEach((sloItem) => {
                            if (
                                sloItem.delivery_type_id &&
                                sloItem.delivery_type_id.toString() === sessionId.toString() &&
                                sloItem.mapped_value === 'TRUE'
                            ) {
                                sessionSlos.push({
                                    _id: sloItem.slo_id,
                                    name: sloItem.name,
                                    no: sloItem.no,
                                    type: DS_SLO_KEY,
                                });
                            }
                        });
                    }
                });
            });
            if (!sessionSlos.length) {
                domains.forEach((domainItem) => {
                    if (!domainItem.clo) return;
                    domainItem.clo.forEach((cloItem) => {
                        sessionSlos.push({
                            _id: cloItem._id,
                            name: cloItem.name,
                            no: cloItem.no,
                            type: DS_CLO_KEY,
                        });
                    });
                });
            }
        }
        return sessionSlos;
    } catch (error) {
        throw new Error(error);
    }
};
const getFeedbackByStaff = async (staffId) => {
    try {
        const fbQuery = {
            $or: [
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'sessionDetail.startBy': convertToMongoObjectId(staffId),
                },
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'staffs.status': PRESENT,
                },
            ],
            students: { $exists: true, $type: 'array', $ne: [] },
            isDeleted: false,
            isActive: true,
        };
        const fbProject = { students: 1, session: 1, staffs: 1, _id: 1 };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject);
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    staff._staff_id.toString() === staffId.toString() && staff.status === PRESENT,
            ),
        );
        let sumOfRatings = 0;
        let count = 0;
        let sessionCount = 0;
        let sessions = courseSchedules.filter(
            (courseSchedule) => courseSchedule.session && courseSchedule.session._session_id,
        );
        if (sessions && sessions.length) {
            sessions = sessions.map((sessionEntry) => sessionEntry.session._session_id.toString());
            sessions = [...new Set(sessions)];
            sessionCount += sessions.length;
        }
        const schedulePush = [];
        courseSchedules = courseSchedules.filter(
            (courseSchedule) => courseSchedule.session && courseSchedule.session._session_id,
        );
        courseSchedules.map((courseDetail) => {
            const { students, _id } = courseDetail;

            sumOfRatings += students
                .filter(
                    (student) =>
                        student.status === PRESENT && student.feedBack && student.feedBack.rating,
                )
                .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                .reduce((a, b) => a + b, 0);
            if (students) {
                count += students.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
                students.map((student) => {
                    if (student.feedBack && student.feedBack.rating) {
                        schedulePush.push(_id);
                    }
                });
            }
        });
        return {
            staff_id: staffId,
            totalFeedback: count,
            avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
            sessionCount: schedulePush.length,
        };
    } catch (error) {
        throw new Error(error);
    }
};
module.exports = {
    getAllCourses,
    getCourse,
    getCourseIds,
    getCourseDocuments,
    getScheduleById,
    getScheduleByDate,
    getCoursesByStaffId,
    getCoursesByStudentId,
    getSloBySessionIdAndCourseId,
    getStudentCourseIds,
    getFeedbackByStaff,
    getRatingBySessions,
    getInfraById,
    getCourseRatingForStaffs,
    getCourses,
    getCourseAdminParams,
};
