const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    getModuleUsers,
    getUserPermissionModuleList,
    getUserModuleExistingPermissions,
    createUserModulePermissions,
    getSingleProgramCalendarDetails,
    getSingleProgramCourseDetails,
} = require('./userModulePermission.controller');
const {
    getModuleListValidator,
    getModuleUsersValidator,
    getUserModuleExistingPermissionValidator,
} = require('./userModulePermission.validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.get(
    '/getUserPermissionModuleList',
    [
        userPolicyAuthentication([
            'user_module_permission:survey:view',
            'user_module_permission:announcement:view',
        ]),
    ],
    getModuleListValidator,
    catchAsync(getUserPermissionModuleList),
);
router.get(
    '/getModuleUsers',
    [
        userPolicyAuthentication([
            'user_module_permission:survey:view',
            'user_module_permission:announcement:view',
        ]),
    ],
    getModuleUsersValidator,
    catchAsync(getModuleUsers),
);
router.get(
    '/getUserModuleExistingPermissions',
    [
        userPolicyAuthentication([
            'user_module_permission:survey:view',
            'user_module_permission:announcement:view',
        ]),
    ],
    getUserModuleExistingPermissionValidator,
    catchAsync(getUserModuleExistingPermissions),
);
router.post(
    '/createUserModulePermissions',
    [
        userPolicyAuthentication([
            'user_module_permission:survey:view',
            'user_module_permission:announcement:view',
        ]),
    ],
    catchAsync(createUserModulePermissions),
);
router.get(
    '/getSingleProgramCalendarDetails/:programId',
    [
        userPolicyAuthentication([
            'user_module_permission:survey:view',
            'user_module_permission:announcement:view',
        ]),
    ],
    catchAsync(getSingleProgramCalendarDetails),
);
router.get('/getSingleProgramCourseDetails', catchAsync(getSingleProgramCourseDetails));
module.exports = router;
