const { check_value_or_null } = require('../../utility/common');

module.exports = {
    user_list_formate: (element) => {
        const formate_obj = [];
        let obj = {};
        element.forEach((doc) => {
            if (doc.user_type === 'staff') {
                obj = {
                    _id: check_value_or_null(doc, '_id') || '',
                    name: {
                        first: check_value_or_null(doc, 'name.first') || '',
                        middle: check_value_or_null(doc, 'name.middle') || '',
                        last: check_value_or_null(doc, 'name.last') || '',
                        family: check_value_or_null(doc, 'name.family') || '',
                    },
                    user_type: doc.user_type,
                    employee_id: check_value_or_null(doc, 'user_id') || '',
                    program: {
                        program_no: check_value_or_null(doc, 'program.program_no') || '',
                        _program_id: check_value_or_null(doc, 'program._program_id') || '',
                    },
                    batch: check_value_or_null(doc, 'batch') || '',
                    gender: check_value_or_null(doc, 'gender') || '',
                    email: check_value_or_null(doc, 'email') || '',
                    username: check_value_or_null(doc, 'username') || '',
                    dob: check_value_or_null(doc, 'dob') || '',
                    mobile: check_value_or_null(doc, 'mobile') || '',
                    address: {
                        nationality_id: check_value_or_null(doc, 'address.nationality_id') || '',
                        building: check_value_or_null(doc, 'address.building') || '',
                        city: check_value_or_null(doc, 'address.city') || '',
                        district: check_value_or_null(doc, 'address.district') || '',
                        zip_code: check_value_or_null(doc, 'address.zip_code') || '',
                        unit: check_value_or_null(doc, 'address.unit') || '',
                        street_no: check_value_or_null(doc, 'address.street_no') || '',
                        passport_no: check_value_or_null(doc, 'address.passport_no') || '',
                    },
                    office: {
                        office_extension: check_value_or_null(doc, 'office.office_extension') || '',
                        office_room_no: check_value_or_null(doc, 'office.office_room_no') || '',
                    },
                    contact: {
                        mobile: check_value_or_null(doc, 'contact.mobile') || '',
                    },
                    role: check_value_or_null(doc, 'role') || '',
                    user_state: check_value_or_null(doc, 'user_state') || '',
                    status: check_value_or_null(doc, 'status') || '',
                    isActive: check_value_or_null(doc, 'isActive'),
                    isDeleted: check_value_or_null(doc, 'isDeleted'),
                };
            } else if (doc.user_type === 'student') {
                obj = {
                    _id: check_value_or_null(doc, '_id') || '',
                    name: {
                        first: check_value_or_null(doc, 'name.first') || '',
                        middle: check_value_or_null(doc, 'name.middle') || '',
                        last: check_value_or_null(doc, 'name.last') || '',
                        family: check_value_or_null(doc, 'name.family') || '',
                    },
                    user_type: doc.user_type,
                    academic: check_value_or_null(doc, 'user_id') || '',
                    gender: check_value_or_null(doc, 'gender') || '',
                    email: check_value_or_null(doc, 'email') || '',
                    batch: check_value_or_null(doc, 'batch') || '',
                    enrollment_year: check_value_or_null(doc, 'enrollment_year') || '',
                    dob: check_value_or_null(doc, 'dob') || '',
                    mobile: check_value_or_null(doc, 'mobile') || '',
                    address: {
                        nationality_id: check_value_or_null(doc, 'address.nationality_id') || '',
                    },
                    role: check_value_or_null(doc, 'role') || '',
                    user_state: check_value_or_null(doc, 'user_state') || '',
                    program_no: check_value_or_null(doc, 'program_name') || '',
                    status: check_value_or_null(doc, 'status') || '',
                    isActive: check_value_or_null(doc, 'isActive'),
                    isDeleted: check_value_or_null(doc, 'isDeleted'),
                };
            }
            formate_obj.push(obj);
            // return obj;
        });
        return formate_obj;
    },
};
