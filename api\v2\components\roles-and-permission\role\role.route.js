const route = require('express').Router();
const { validate } = require('../../../utility/input-validation');

const { getAllRoleList, createRole, updateRole, deleteRole } = require('./role.controller');
const catchAsync = require('../../../utility/catch-async');
const { roleIdValidation, roleValidation } = require('./role.validator');

route.get('/', catchAsync(getAllRoleList));

route.post('/', validate(roleValidation), catchAsync(createRole));

route.put('/:id', validate(roleIdValidation), validate(roleValidation), catchAsync(updateRole));

route.delete('/:id', validate(roleIdValidation), catchAsync(deleteRole));

module.exports = route;
