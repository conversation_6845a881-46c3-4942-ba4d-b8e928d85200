const assignmentSchema = require('./assignment.model');
const assignmentAnswerSchema = require('../assignment_answer/assignment_answer.model');
const assignmentGroupSchema = require('../assignment_group/assignment_group.model');
const assignmentSettings = require('../assignment-settings/assignment-settings.model');
const assignmentPromptSchema = require('../assignment-prompt/assignment-prompt.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const {
    getSessionLists,
    getInstructorLists,
    getStudentGroupLists,
    getStudentCourseGroupLists,
    getAssignmentList,
    assignmentQuery,
    promptsProject,
    updateDateBasedOnSession,
    updateDateBasedOnCourse,
    updateCommentsAndDate,
    constructAssignment,
    createNewAssignmentPrompts,
} = require('./assignment.service');
const assignmentPromptAnswerSchema = require('../assignment_prompt_answer/assignment_prompt_answer.model');
const {
    SUBMITTED,
    COMPLETED,
    PROGRAM_CALENDAR,
    PENDING,
    DURATION,
    RELEASED,
    IN_REVIEW,
    GRADED,
    TO_GRADE,
    COURSE_SCHEDULE,
    IN_GRADING,
    RESUBMIT,
    USER,
} = require('../../../utility/constants');
const { getSignedUrl } = require('../assignment-settings/assignment-settings.util');
const { getPaginationValues } = require('../../../utility/pagination');
const ProgramCalendar = require('mongoose').model(PROGRAM_CALENDAR);
const { getUserRoleProgramList } = require('../../../../lib/utility/utility.service');
const { allProgramCalendarDatas } = require('../../../../service/cache.service');
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const userSchema = require('mongoose').model(USER);
const { APP_NOTIFICATIONS } = require('../../../../lib/utility/constants');
const notificationSchema = require('mongoose').model(APP_NOTIFICATIONS);
const assignmentReportSchema = require('../assignment_report/assignment.report.student.model');
const { sendNotificationPush } = require('../../../../service/pushNotification.service');
const assignmentCommentsSchema = require('../assignment_comments/assignment_comments.model');

const createStudentsForAssignments = async ({ isGroupProject, assignTo, defaultData }) => {
    const assignmentStudentData =
        assignTo.length >= 2 ? (isGroupProject ? assignTo[1] : assignTo[0]) : {};
    if (Object.keys(assignmentStudentData).length === 0) return;
    let studentIds = [];
    let groupData = [];
    const bulkWrite = [];
    const removedStudentIds = [];
    const allStudentIds = [];
    const addedStudent = [];
    if (isGroupProject) {
        groupData = await assignmentGroupSchema
            .find(
                {
                    _id: {
                        $in: assignmentStudentData.groups.map((group) =>
                            convertToMongoObjectId(group.groupId),
                        ),
                    },
                },
                {
                    groups: 1,
                },
            )
            .lean();
        studentIds = groupData
            .map((IndividualGroups) =>
                IndividualGroups.groups
                    .map((groupElement) =>
                        groupElement.students.map((student) => {
                            student.groupId = IndividualGroups._id;
                            return student;
                        }),
                    )
                    .flat(),
            )
            .flat();
    } else studentIds = assignmentStudentData.studentIds;

    let previouslyAddedStudents = await assignmentAnswerSchema.aggregate([
        {
            $match: {
                isDeleted: false,
                assignmentId: convertToMongoObjectId(defaultData.assignmentId),
                _institution_id: convertToMongoObjectId(defaultData._institution_id),
                courseId: convertToMongoObjectId(defaultData.courseId),
                programId: convertToMongoObjectId(defaultData.programId),
            },
        },
        { $group: { _id: null, ids: { $push: { $toString: '$studentId' } } } },
    ]);
    if (previouslyAddedStudents.length > 0) {
        previouslyAddedStudents = previouslyAddedStudents[0].ids;
        for (const StudentElement of studentIds) {
            const studentId = StudentElement.studentId.toString();
            allStudentIds.push(studentId);
            if (!previouslyAddedStudents.includes(studentId)) {
                addedStudent.push(StudentElement);
            }
        }
        for (const StudentElement of previouslyAddedStudents) {
            if (!allStudentIds.includes(StudentElement)) {
                removedStudentIds.push(StudentElement);
            }
        }
    }
    if (previouslyAddedStudents.length) {
        studentIds = addedStudent;
    }

    if (removedStudentIds.length) {
        bulkWrite.push({
            updateMany: {
                filter: {
                    studentId: { $in: removedStudentIds },
                    assignmentId: defaultData.assignmentId,
                },
                update: { $set: { isDeleted: true } },
            },
        });
    }

    if (studentIds.length) {
        for (const studentElement of studentIds) {
            bulkWrite.push({
                insertOne: {
                    document: {
                        studentId: studentElement.studentId,
                        ...(studentElement.groupId && { groupId: studentElement.groupId }),
                        ...defaultData,
                    },
                },
            });
        }
    }

    await assignmentAnswerSchema.bulkWrite(bulkWrite);
};
const addAssignment = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            courseId,
            course_code,
            courseName,
            programName,
            subject,
            isDraft,
            basic,
            submission,
            evaluation,
            reflection,
            programId,
            contributor,
            term,
            isActive,
            rotation_count,
            level_no,
            year_no,
            createdBy,
        } = body;
        const assignment = await assignmentSchema.create({
            _institution_id,
            _institution_calendar_id,
            _course_id: courseId,
            course_code,
            courseName,
            programName,
            subject,
            _program_id: programId,
            contributor,
            term,
            isDraft,
            basic,
            submission,
            evaluation,
            reflection,
            isActive,
            rotation_count,
            level_no,
            year_no,
            createdBy,
        });
        if (!assignment) return { statusCode: 410, message: 'ASSESSMENT NOT ADDED' };
        return {
            statusCode: 200,
            message: 'ASSESSMENT ADDED',
            data: { assignmentId: assignment._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateAssignment = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { id } = params;
        const {
            courseId,
            course_code,
            programId,
            courseName,
            programName,
            contributor,
            term,
            subject,
            isDraft,
            isActive,
            basic,
            submission,
            evaluation,
            reflection,
            pause,
            hide,
            publishedAt,
            generalSettings,
            rotation_count,
            level_no,
            year_no,
            createdBy,
            notificationStatus,
        } = body;
        const {
            assignTo,
            type,
            due,
            start,
            end,
            remindStudents,
            recurring,
            includePreviousDiscussions,
            recurWhenThisCourseIsActive,
        } = submission;
        const assignment = await assignmentSchema.updateOne(
            { _id: convertToMongoObjectId(id) },
            {
                ...(courseId && { _course_id: courseId }),
                ...(course_code && { course_code }),
                ...(subject && { subject }),
                ...(courseName && { courseName }),
                ...(programName && { programName }),
                ...(programId && { _program_id: programId }),
                ...(contributor && { contributor }),
                ...(createdBy && { createdBy }),
                ...(term && { term }),
                ...(level_no && { level_no }),
                ...(rotation_count && { rotation_count }),
                ...(year_no && { year_no }),
                ...((isActive === true || isActive === false) && { isActive }),
                ...((isDraft === true || isDraft === false) && { isDraft }),
                ...(basic && { basic }),

                ...(assignTo && { 'submission.assignTo': assignTo }),
                ...(type && { 'submission.type': type }),
                ...(due && { 'submission.due': due }),
                ...(start && { 'submission.start': start }),
                ...(end && { 'submission.end': end }),
                ...(remindStudents && { 'submission.remindStudents': remindStudents }),
                ...(recurring && { 'submission.recurring': recurring }),
                ...(includePreviousDiscussions && {
                    'submission.includePreviousDiscussions': includePreviousDiscussions,
                }),
                ...(recurWhenThisCourseIsActive && {
                    'submission.recurWhenThisCourseIsActive': recurWhenThisCourseIsActive,
                }),

                ...(evaluation && { evaluation }),
                ...(reflection && { reflection }),
                ...(_institution_id && { _institution_id }),
                ...(_institution_calendar_id && { _institution_calendar_id }),
                ...(publishedAt && { publishedAt }),
                ...(generalSettings && { generalSettings }),
                ...((pause === true || pause === false) && { pause }),
                ...((hide === true || hide === false) && { hide }),
                ...(isActive === true && notificationStatus && { notificationStatus }),
            },
        );
        if (!assignment) return { statusCode: 410, message: 'ASSIGNMENT NOT UPDATED' };
        if (isActive) {
            // create student
            await createStudentsForAssignments({
                isGroupProject: basic.isGroupProject,
                assignTo: submission.assignTo,
                defaultData: {
                    assignmentId: convertToMongoObjectId(id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    courseId: convertToMongoObjectId(courseId),
                    programId: convertToMongoObjectId(programId),
                    status: PENDING,
                },
            });

            // send notification to students
            const notificationTitle =
                notificationStatus === 'Create Assignment'
                    ? 'New Assignment Created'
                    : 'Assignment Updated';
            const notificationDesc =
                `digivalitsolutions.com` +
                `\n` +
                basic.title +
                `\n |` +
                ` ${courseName} - ${year_no} - ${level_no} - ${term}`;

            const notificationData = {
                title: notificationTitle,
                Title: notificationTitle,
                description: notificationDesc,
                Body: notificationDesc,
                notificationType: 'assignment',
                assignmentId: id,
                courseId,
                programId,
                ...(start && { startDate: start.dateAndTime }),
                ...(due && { dueDate: due.dateAndTime }),
                ...(end && { endDate: end.dateAndTime }),
                buttonAction: 'assignment',
                ClickAction: 'assignment',
            };

            const studentsId = assignTo[0].studentIds.map((studentElement) =>
                convertToMongoObjectId(studentElement.studentId),
            );
            const allStudentData = await userSchema
                .find(
                    { _id: { $in: studentsId } },
                    { web_fcm_token: 1, fcm_token: 1, device_type: 1 },
                )
                .lean();

            for (const studentElement of allStudentData) {
                notificationData.studentId = studentElement._id;
                if (studentElement.fcm_token) {
                    sendNotificationPush(
                        studentElement.fcm_token,
                        notificationData,
                        studentElement.device_type,
                    );
                }
                if (studentElement.web_fcm_token) {
                    sendNotificationPush(studentElement.web_fcm_token, notificationData, 'web');
                }
            }
            delete notificationData.Title;
            delete notificationData.Body;
            notificationData.assignmentId = convertToMongoObjectId(id);
            notificationData.institutionCalendarId = _institution_calendar_id;
            notificationData.yearNo = year_no;
            notificationData.levelNo = level_no;
            notificationData.term = term;
            notificationData.rotation_count = rotation_count;
            notificationData.users = allStudentData.map((studentElement) => {
                return { _id: studentElement._id, isViewed: false };
            });
            notificationData.title =
                notificationStatus === 'Create Assignment'
                    ? 'New Assignment Created'
                    : 'Assignment Updated';
            notificationData.assignmentData = {
                ...(start && { startDate: start.dateAndTime }),
                ...(due && { dueDate: due.dateAndTime }),
                ...(end && { endDate: end.dateAndTime }),
            };

            if (notificationStatus === 'Create Assignment') {
                const notificationCreate = await notificationSchema.create(notificationData);
                if (!notificationCreate) return { statusCode: 410, message: 'Failed Notification' };
            } else {
                const notificationUpdate = await notificationSchema.updateOne(
                    { assignmentId: convertToMongoObjectId(id) },
                    { $set: notificationData },
                );
                if (!notificationUpdate) return { statusCode: 410, message: 'Failed Notification' };
            }
        }

        return { statusCode: 200, message: 'ASSIGNMENT UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateSubmissionDue = async ({ body = {}, params = {} }) => {
    try {
        const { id } = params;
        const { submission } = body;
        const {
            due,
            start,
            end,
            remindStudents,
            recurring,
            includePreviousDiscussions,
            recurWhenThisCourseIsActive,
        } = submission;
        const assignment = await assignmentSchema.updateOne(
            { _id: convertToMongoObjectId(id) },
            {
                ...(due && { 'submission.due': due }),
                ...(start && { 'submission.start': start }),
                ...(end && { 'submission.end': end }),
                ...(remindStudents && { 'submission.remindStudents': remindStudents }),
                ...(recurring && { 'submission.recurring': recurring }),
                ...(includePreviousDiscussions && {
                    'submission.includePreviousDiscussions': includePreviousDiscussions,
                }),
                ...(recurWhenThisCourseIsActive && {
                    'submission.recurWhenThisCourseIsActive': recurWhenThisCourseIsActive,
                }),
            },
        );
        if (!assignment)
            return { statusCode: 410, message: 'ASSIGNMENT SUBMISSION DUE NOT UPDATED' };
        return { statusCode: 200, message: 'ASSIGNMENT SUBMISSION DUE UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAssignmentDuration = async ({
    _institution_id,
    _program_id,
    _course_id,
    term,
    _id,
    submission,
    startStatus,
    endStatus,
}) => {
    const programCalendar = await ProgramCalendar.findOne(
        {
            _institution_id,
            _program_id,
            isActive: true,
            isDeleted: false,
            level: { $elemMatch: { term, _program_id, 'course._courseId_': _course_id } },
        },
        { 'level.course.start_date': 1, 'level.course.end_date': 1 },
    ).lean();
    if (programCalendar) {
        const { level } = programCalendar;
        const { startType, endType } = submission;
        const {
            course: { start_date, end_date },
        } = level;
        if (
            startStatus === true &&
            submission.startSelect === DURATION &&
            submission.startTypeSelect === 'After Course Starts'
        ) {
            const startDate = new Date(
                start_date.setDate(start_date.getDate() + Number(startType)),
            );
            return startDate;
        }
        if (
            endStatus === true &&
            submission.endSelect === DURATION &&
            submission.endTypeSelect === 'Before Course Ends'
        ) {
            const endDate = new Date(end_date.setDate(end_date.getDate() - Number(endType)));
            return endDate;
        }
    }
};
const getAssignment = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { id, _course_id } = params;
        const { studentId, studentData, staffData, settingsData, staffId } = query;
        const isStudentDataNeeded = studentData === 'true';
        const isStaffDataNeeded = staffData === 'true';
        const assignment = await assignmentSchema
            .findById({ _id: convertToMongoObjectId(id) })
            .populate({ path: 'basic.evaluation.rubricsId', select: { name: 1 } })
            .lean();
        if (!assignment) return { statusCode: 410, message: 'ASSIGNMENT NOT FOUND' };
        if (assignment.basic.instructionAttachment) {
            for (const instructionAttachment of assignment.basic.instructionAttachment) {
                instructionAttachment.signedUrl = await getSignedUrl(instructionAttachment.url);
            }
        }
        const { sessionIds, courseIds } = await updateCommentsAndDate({
            assignmentList: [assignment],
            _institution_id,
            allStudentData: [],
            studentId,
            getAllSubmissionData: true,
        });
        if (Object.keys(sessionIds).length) {
            await updateDateBasedOnSession({
                sessionIds,
                assignmentList: [assignment],
                _institution_id,
                _institution_calendar_id,
            });
        }
        if (Object.keys(courseIds).length) {
            await updateDateBasedOnCourse({
                courseIds,
                assignmentList: [assignment],
                _institution_calendar_id,
            });
        }
        if (studentId) {
            const assignmentAnswerData = await assignmentAnswerSchema
                .findOne({
                    isDeleted: false,
                    assignmentId: convertToMongoObjectId(id),
                    studentId: convertToMongoObjectId(studentId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                })
                .populate({ path: 'studentId', select: { name: 1, user_id: 1 } })
                .lean();
            if (isStaffDataNeeded) {
                if (staffId || assignmentAnswerData.staffEvaluation.releasedStaffId) {
                    const _staffId =
                        staffId || assignmentAnswerData.staffEvaluation.releasedStaffId.toString();
                    const selectedStaff = assignmentAnswerData.staffEvaluation.allStaffData.find(
                        (allStaffElement) => allStaffElement.staffId.toString() === _staffId,
                    );
                    if (selectedStaff)
                        assignment.staffData = {
                            rubrics: selectedStaff.rubrics,
                            staffStatus: selectedStaff.individualStaffStatus,
                            totalMark: selectedStaff.totalMark,
                            learningOutcome: selectedStaff.learningOutcome,
                        };
                }
            }
            if (isStudentDataNeeded) {
                delete assignmentAnswerData.staffEvaluation;
                assignment.studentData = assignmentAnswerData;
            }
        }
        if (settingsData) {
            const settingsData = await assignmentSettings
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        $or: [
                            {
                                'programSettings.programId': convertToMongoObjectId(
                                    assignment._program_id,
                                ),
                            },
                            {
                                'courseSettings.courseId': convertToMongoObjectId(
                                    assignment._course_id,
                                ),
                                'courseSettings.programId': convertToMongoObjectId(
                                    assignment._program_id,
                                ),
                            },
                            {
                                'subjectSettings.courseId': convertToMongoObjectId(
                                    assignment._course_id,
                                ),
                                'subjectSettings.programId': convertToMongoObjectId(
                                    assignment._program_id,
                                ),
                            },
                        ],
                    },
                    {
                        programSettings: 1,
                        courseSettings: 1,
                        subjectSettings: 1,
                    },
                )
                .lean();
            if (settingsData) {
                const assignmentSettings = {
                    subjectSettings: [],
                };
                for (const settingsElement of settingsData) {
                    const { programSettings, courseSettings, subjectSettings } = settingsElement;
                    if (programSettings.programId)
                        assignmentSettings.programSettings = programSettings;
                    if (courseSettings.programId && courseSettings.courseId)
                        assignmentSettings.courseSettings = courseSettings;
                    if (
                        subjectSettings.programId &&
                        subjectSettings.courseId &&
                        subjectSettings.subjectId
                    )
                        assignmentSettings.subjectSettings.push(subjectSettings);
                }
                assignment.assignmentSettings = assignmentSettings;
            }
        }
        assignment.todaysDate = new Date();
        return { statusCode: 200, message: 'ASSIGNMENT DATA', data: assignment };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getProgramCourseSubjectIds = async ({
    _institution_id,
    _institution_calendar_id,
    staffId,
    roleId,
}) => {
    const programAndCourseIds = await getUserRoleProgramList({
        _institution_id,
        user_id: staffId,
        role_id: roleId,
        institutionCalendarId: [_institution_calendar_id],
    });
    if (programAndCourseIds.userProgramIds.length) {
        programAndCourseIds.userProgramIds = programAndCourseIds.userProgramIds.map(
            (userProgramId) => convertToMongoObjectId(userProgramId),
        );
    }
    if (programAndCourseIds.userCourseIds.length) {
        programAndCourseIds.userCourseIds = programAndCourseIds.userCourseIds.map(
            (userCourseIdElement) => userCourseIdElement._course_id,
        );
    }
    if (!programAndCourseIds.userProgramIds.length && !programAndCourseIds.userCourseIds.length) {
        const courseSchedules = await courseScheduleSchema.aggregate([
            {
                $match: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    isDeleted: false,
                },
            },
            {
                $group: {
                    _id: null,
                    subjectIds: { $push: '$subjects._subject_id' },
                },
            },
            {
                $project: {
                    _id: 0,
                    subjectIds: 1,
                },
            },
        ]);
        if (courseSchedules.length)
            programAndCourseIds.subjectIds = courseSchedules[0].subjectIds.flat();
    }

    return programAndCourseIds;
};
const listAssignment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { staffId, roleId } = query;
        const programAndCourseIds = await getProgramCourseSubjectIds({
            _institution_id,
            _institution_calendar_id,
            staffId,
            roleId,
        });
        const assignmentList = await getAssignmentList({
            _institution_id,
            staffId,
            _institution_calendar_id,
            programAndCourseIds,
        });
        assignmentList.isAdmin = programAndCourseIds.isProgramAdmin;
        return { statusCode: 200, message: 'ASSIGNMENT DATA', data: assignmentList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteAssignment = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id, deletedReason } = body;
        const updateData = { isDeleted: true };
        const deleteAssignment = await assignmentSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            {
                ...updateData,
                deletedReason,
            },
        );
        if (!deleteAssignment) return { statusCode: 410, message: 'ASSIGNMENT DATA DELETE FAILED' };
        const deleteAssignmentStudentAnswer = await assignmentAnswerSchema.updateMany(
            {
                assignmentId: convertToMongoObjectId(id),
            },
            updateData,
        );
        return { statusCode: 200, message: 'ASSIGNMENT DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listStudentCourse = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { studentId } = query;
        const dbQuery = {
            'submission.assignTo.studentIds': {
                $elemMatch: { studentId },
            },
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        };

        const assignments = await assignmentSchema.aggregate([
            { $match: dbQuery },
            {
                $group: {
                    _id: '$courseName',
                    _course_id: { $first: '$_course_id' },
                    courseName: { $first: '$courseName' },
                    originalId: { $first: '$_id' },
                },
            },
            {
                $project: {
                    _id: '$originalId',
                    _course_id: 1,
                    courseName: 1,
                },
            },
        ]);

        if (!assignments) {
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        }

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { assignments },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listStudentAssignments = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { studentId, status, searchKey, courseId } = query;
        const { limit, pageNo, skip } = getPaginationValues(query);
        if (!studentId) {
            return { statusCode: 400, message: 'VALID_STUDENT_ID_REQUIRED' };
        }
        if (!status) {
            return { statusCode: 400, message: 'ASSIGNMENT_STATUS_REQUIRED' };
        }
        const studentAssignment = await assignmentAnswerSchema.aggregate([
            {
                $match: {
                    isDeleted: false,
                    studentId: convertToMongoObjectId(studentId),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
            },
            {
                $group: {
                    _id: '$status',
                    assignments: { $push: '$$ROOT' },
                },
            },
        ]);
        let pendingIndex = -1;
        let completedIndex = -1;
        let submittedIndex = -1;
        let draftCount = 0;
        let totalDocs = 0;
        let assignmentIds = [];
        const allStudentAnswers = {};
        const isProgressAssignmentIds = [];
        const isStatusMatched = (groupedStatus, matchStatus, index) => {
            if (groupedStatus._id === matchStatus) {
                if (matchStatus === PENDING) pendingIndex = index;
                if (matchStatus === RELEASED) completedIndex = index;
                if (matchStatus === SUBMITTED) submittedIndex = index;
            }
            if (status === matchStatus && groupedStatus._id === matchStatus) {
                assignmentIds = groupedStatus.assignments.map((assignmentElement) => {
                    allStudentAnswers[assignmentElement.assignmentId] = assignmentElement;
                    return convertToMongoObjectId(assignmentElement.assignmentId);
                });
                totalDocs += groupedStatus.assignments.length;
            }
        };
        studentAssignment.forEach((groupedStatus, statusIndex) => {
            isStatusMatched(groupedStatus, PENDING, statusIndex);
            isStatusMatched(groupedStatus, RELEASED, statusIndex);
            isStatusMatched(groupedStatus, SUBMITTED, statusIndex);

            for (const assignment of groupedStatus.assignments) {
                if (assignment.isDraft) {
                    draftCount++;
                    isProgressAssignmentIds.push(assignment.assignmentId.toString());
                }
            }
        });
        let dbQuery = {
            isDeleted: false,
            isDraft: false,
            _institution_id,
            _id: { $in: assignmentIds },
        };
        if (searchKey && searchKey.length != 0) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    { 'basic.title': { $regex: searchKey, $options: 'i' } },
                    { 'basic.category': { $regex: searchKey, $options: 'i' } },
                    { courseName: { $regex: searchKey, $options: 'i' } },
                ],
            };
        }
        if (courseId) {
            dbQuery = {
                ...dbQuery,
                _course_id: convertToMongoObjectId(courseId),
            };
        }
        const assignmentList = await assignmentSchema
            .find(dbQuery, assignmentQuery, { createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean();
        const { sessionIds, courseIds } = await updateCommentsAndDate({
            assignmentList,
            _institution_id,
            allStudentData: [],
            allStudentAnswers,
            studentId,
        });
        if (Object.keys(sessionIds).length) {
            await updateDateBasedOnSession({
                sessionIds,
                assignmentList,
                _institution_id,
                _institution_calendar_id,
            });
        }
        if (Object.keys(courseIds).length) {
            await updateDateBasedOnCourse({ courseIds, assignmentList, _institution_calendar_id });
        }
        const calculateCount = (index) => {
            if (index === -1) return 0;
            return studentAssignment[index].assignments.length;
        };
        // let inProgressCount = 0;
        // for (const assignment of assignmentList) {
        //     const assignmentStringId = assignment._id.toString();
        //     if (isProgressAssignmentIds.includes(assignmentStringId)) {
        //         const { endDate } = assignment.submission;
        //         const inputEndDate = new Date(endDate);
        //         if (!endDate || inputEndDate === 'Invalid Date') {
        //             inProgressCount++;
        //         } else {
        //             const currentDate = new Date();
        //             const dbDate = new Date(endDate);
        //             if (currentDate > dbDate) {
        //                 inProgressCount++;
        //             }
        //         }
        //     }
        // }
        const totalReportStudentDataCount = await assignmentReportSchema.countDocuments({
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            studentId: convertToMongoObjectId(studentId),
            status: 'publish',
        });

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                assignments: assignmentList,
                currentPage: pageNo,
                totalPages: Math.ceil(totalDocs / limit),
                pendingAssignments: calculateCount(pendingIndex),
                completedAssignments: calculateCount(completedIndex),
                submittedAssignments: calculateCount(submittedIndex),
                // draftCount: inProgressCount !== 0 ? inProgressCount : draftCount,
                draftCount,
                todayDate: new Date(),
                reportStudentDataCount: totalReportStudentDataCount,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listSession = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id, _institution_calendar_id } = headers;
        const { _course_id } = params;
        const { isCoordinator } = query;
        const getSession = await getSessionLists({
            _institution_id,
            _course_id,
            _user_id,
            isCoordinator,
            _institution_calendar_id,
        });
        if (!getSession)
            return {
                statusCode: 404,
                message: 'NO_SESSION_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getSession,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listInstructor = async ({ query = {} }) => {
    try {
        const { _program_id, subjectIds, searchKey } = query;
        const getInstructor = await getInstructorLists({
            _program_id,
            subjectIds,
            searchKey,
        });
        if (!getInstructor)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getInstructor,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getStudentGroups = async ({ params = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { _program_id, _course_id, term } = params;
        const getStudents = await getStudentGroupLists({
            _institution_id,
            _institution_calendar_id,
            _program_id,
            _course_id,
            term,
        });
        if (!getStudents)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getStudents,
        };
    } catch (error) {
        console.error(error);
        return { status: false };
    }
};
const getStudentCourseGroups = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _institution_calendar_id, _program_id, _course_id, term, deliverySymbol } = query;
        const getStudents = await getStudentCourseGroupLists({
            _institution_id,
            _institution_calendar_id,
            _program_id,
            _course_id,
            term,
            deliverySymbol,
        });
        if (!getStudents)
            return {
                statusCode: 404,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getStudents,
        };
    } catch (error) {
        console.error(error);
        return { status: false };
    }
};
const updateStatus = async ({ query = {} }) => {
    const { assignmentId, studentId, status, staffStatus } = query;
    try {
        const assignmentUpdateStudent = await assignmentSchema.updateOne(
            { _id: assignmentId },
            {
                'submission.assignTo.$[].studentIds.$[j].status': status,
                'submission.assignTo.$[].studentIds.$[j].staffStatus': staffStatus,
            },
            {
                arrayFilters: [
                    {
                        'j.studentId': convertToMongoObjectId(studentId),
                    },
                ],
            },
        );
        if (!assignmentUpdateStudent)
            return { statusCode: 410, message: 'ASSIGNMENT STATUS NOT UPDATED' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT STATUS UPDATED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const extendDue = async ({ query = {} }) => {
    const { assignmentId, studentId, dueDate, description } = query;
    try {
        const assignmentUpdateStudent = await assignmentSchema.findByIdAndUpdate(
            { _id: assignmentId },
            {
                $addToSet: {
                    'submission.assignTo.$[].studentIds.$[j].specialDue': { dueDate, description },
                },
            },
            {
                arrayFilters: [
                    {
                        'j.studentId': convertToMongoObjectId(studentId),
                    },
                ],
            },
        );
        if (!assignmentUpdateStudent)
            return { statusCode: 410, message: 'ASSIGNMENT DUE NOT EXTENDED' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT DUE EXTENDED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const listStaffAssignments = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { staffId } = query;
        const assignmentList = await assignmentSchema
            .find(
                {
                    'evaluation.evaluators.evaluatorId': convertToMongoObjectId(staffId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                assignmentQuery,
            )
            .lean();
        return constructAssignment({
            _institution_id,
            staffId,
            _institution_calendar_id,
            assignmentList,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const studentListForAssignment = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId } = params;
        const { isGroupProject } = query;
        let studentList = await assignmentAnswerSchema
            .find(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    assignmentId: convertToMongoObjectId(assignmentId),
                },
                {
                    studentId: 1,
                    staffEvaluation: 1,
                    noOfAttempt: 1,
                    status: 1,
                    assignmentId: 1,
                    specialDue: 1,
                    submittedAt: 1,
                    groupId: 1,
                },
            )
            .populate({ path: 'studentId', select: { name: 1, user_id: 1 } })
            .lean();
        let groupList = [];
        if (isGroupProject === 'true') {
            groupList = await assignmentGroupSchema
                .find({
                    _id: studentList.map((studentElement) =>
                        convertToMongoObjectId(studentElement.groupId),
                    ),
                })
                .lean();
            for (const { groups } of groupList) {
                for (const { students } of groups) {
                    students.forEach((student, index) => {
                        const studentIndex = studentList.findIndex(
                            (totalStudent) =>
                                totalStudent.studentId._id.toString() ===
                                student.studentId.toString(),
                        );
                        students[index] = { ...(studentIndex !== -1 && studentList[studentIndex]) };
                    });
                }
            }
            studentList = groupList;
        }
        if (!studentList) return { statusCode: 410, message: 'NO STUDENTS IN ASSIGNMENT' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT_DATA',
            data: studentList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getStudentStatus = ({ isReleased, needToResubmit }) => {
    if (needToResubmit) return PENDING;
    if (isReleased) return RELEASED;
    return SUBMITTED;
};
const getFinalStatus = ({ isReleased, isGraded, isGrading, multiStaff }) => {
    if (isReleased) return RELEASED;
    const isSingleStaffGraded = !multiStaff && isGraded;
    if (isSingleStaffGraded) return GRADED;
    const isSingleStaffGrading = !multiStaff && isGrading;
    if (isSingleStaffGrading) return IN_GRADING;
    const isAnyMultiStaffGraded = multiStaff && isGraded;
    if (isAnyMultiStaffGraded) return IN_REVIEW;
    return TO_GRADE;
};

function processSubPrompts({
    prompt,
    staffObjectId,
    staffEvalForPrompt = [],
    assignmentPromptStudentQuery,
}) {
    if (prompt.length === 0) {
        return staffEvalForPrompt;
    }

    for (const { promptId, score, learningOutcome, rubrics, subPrompts } of prompt) {
        if (subPrompts) {
            processSubPrompts({
                prompt: subPrompts,
                staffObjectId,
                staffEvalForPrompt,
                assignmentPromptStudentQuery,
            });
        }
        const objectPromptId = convertToMongoObjectId(promptId);
        const staffEvaluationData = {
            score,
            rubrics,
            learningOutcome,
            promptId: objectPromptId,
            staffId: staffObjectId,
        };

        staffEvalForPrompt.push(
            {
                updateOne: {
                    filter: {
                        ...assignmentPromptStudentQuery,
                        assignmentPromptId: objectPromptId,
                        'staffEvaluation.staffId': staffObjectId,
                    },
                    update: {
                        $set: {
                            'staffEvaluation.$[staffObject].score': score,
                            ...(rubrics && {
                                'staffEvaluation.$[staffObject].rubrics': rubrics,
                            }),
                            ...(learningOutcome && {
                                'staffEvaluation.$[staffObject].learningOutcome': learningOutcome,
                            }),
                        },
                    },
                    arrayFilters: [
                        {
                            'staffObject.staffId': staffObjectId,
                        },
                    ],
                },
            },
            {
                updateOne: {
                    filter: {
                        ...assignmentPromptStudentQuery,
                        assignmentPromptId: objectPromptId,
                        'staffEvaluation.staffId': { $ne: staffObjectId },
                    },
                    update: {
                        $push: { staffEvaluation: staffEvaluationData },
                    },
                },
            },
        );
    }
}

const updateStudentData = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId, staffEvaluation, multiStaff } = body;
        const { individualStaffStatus, staffId, totalMark } = staffEvaluation;
        const isReleased = individualStaffStatus === RELEASED;
        const isGraded = individualStaffStatus === GRADED;
        const isGrading = individualStaffStatus === IN_GRADING;
        const needToResubmit = individualStaffStatus === RESUBMIT;
        const isMultiStaff = multiStaff === 'true';
        const studentStatus = getStudentStatus({ isReleased, needToResubmit });
        const finalStatus = getFinalStatus({
            isReleased,
            isGraded,
            isGrading,
            multiStaff: isMultiStaff,
        });
        //const staffStatus
        const assignmentStudentQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            studentId: convertToMongoObjectId(studentId),
            assignmentId: convertToMongoObjectId(assignmentId),
        };
        const allStaffData = {
            ...(!needToResubmit && { 'staffEvaluation.allStaffData.$': staffEvaluation }),
        };
        const evaluationData = {
            'staffEvaluation.staffStatus': finalStatus,
            status: studentStatus,
            ...(!isMultiStaff &&
                individualStaffStatus === GRADED && { 'staffEvaluation.totalMark': totalMark }),
            ...(studentStatus === RELEASED && {
                'staffEvaluation.releasedStaffId': staffId,
                'staffEvaluation.totalMark': totalMark,
            }),
            ...(needToResubmit && { assignmentAnswer: [] }),
        };
        const staffObjectId = convertToMongoObjectId(staffId);
        const staffEvaluationBulkUpdate = [
            {
                updateOne: {
                    filter: {
                        ...assignmentStudentQuery,
                        'staffEvaluation.allStaffData.staffId': staffObjectId,
                    },
                    update: {
                        $set: { ...allStaffData, ...evaluationData },
                    },
                },
            },
            {
                updateOne: {
                    filter: {
                        ...assignmentStudentQuery,
                        'staffEvaluation.allStaffData.staffId': { $ne: staffObjectId },
                    },
                    update: {
                        ...(!needToResubmit && {
                            $push: { 'staffEvaluation.allStaffData': staffEvaluation },
                        }),
                        $set: evaluationData,
                    },
                },
            },
        ];
        const assignmentAnswer = await assignmentAnswerSchema.bulkWrite(staffEvaluationBulkUpdate);
        if (!assignmentAnswer)
            return {
                statusCode: 401,
                message: ' Error In Updating ',
            };
        const { prompt } = staffEvaluation;
        if (!needToResubmit && prompt && prompt.length) {
            const staffEvalForPrompt = [];
            const assignmentPromptStudentQuery = {
                studentId: convertToMongoObjectId(studentId),
                assignmentId: convertToMongoObjectId(assignmentId),
                _institution_id: convertToMongoObjectId(_institution_id),
            };

            processSubPrompts({
                prompt,
                staffObjectId,
                staffEvalForPrompt,
                assignmentPromptStudentQuery,
            });

            const updatePromptScore = await assignmentPromptAnswerSchema.bulkWrite(
                staffEvalForPrompt,
            );
            if (!updatePromptScore)
                return {
                    statusCode: 401,
                    message: ' Error In Updating ',
                };
        }
        if (needToResubmit) {
            const deleteStudentPrompt = await assignmentPromptAnswerSchema.deleteMany({
                _institution_id: convertToMongoObjectId(_institution_id),
                assignmentId: convertToMongoObjectId(assignmentId),
                studentId: convertToMongoObjectId(studentId),
            });
        }
        return {
            statusCode: 200,
            message: 'Updated Successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateMultipleStudentData = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentsIds = [], staffEvaluation, multiStaff } = body;
        const { individualStaffStatus, staffId, totalMark, prompt } = staffEvaluation;
        const isReleased = individualStaffStatus === RELEASED;
        const isGraded = individualStaffStatus === GRADED;
        const isGrading = individualStaffStatus === IN_GRADING;
        const needToResubmit = individualStaffStatus === RESUBMIT;
        const isMultiStaff = multiStaff === true;
        const bulkOperations = [];
        const staffObjectId = convertToMongoObjectId(staffId);
        const assignmentQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            assignmentId: convertToMongoObjectId(assignmentId),
            isDeleted: false,
        };

        studentsIds.forEach((studentId) => {
            const studentStatus = getStudentStatus({ isReleased, needToResubmit });
            const finalStatus = getFinalStatus({
                isReleased,
                isGraded,
                isGrading,
                multiStaff: isMultiStaff,
            });
            const assignmentStudentQuery = {
                ...assignmentQuery,
                studentId: convertToMongoObjectId(studentId),
            };
            const allStaffData = {
                ...(!needToResubmit && { 'staffEvaluation.allStaffData.$': staffEvaluation }),
            };
            const evaluationData = {
                'staffEvaluation.staffStatus': finalStatus,
                status: studentStatus,
                ...(!isMultiStaff &&
                    individualStaffStatus === GRADED && { 'staffEvaluation.totalMark': totalMark }),
                ...(studentStatus === RELEASED && {
                    'staffEvaluation.releasedStaffId': staffId,
                    'staffEvaluation.totalMark': totalMark,
                }),
                ...(needToResubmit && { assignmentAnswer: [] }),
            };

            bulkOperations.push(
                {
                    updateOne: {
                        filter: {
                            ...assignmentStudentQuery,
                            'staffEvaluation.allStaffData.staffId': staffObjectId,
                        },
                        update: {
                            $set: {
                                ...allStaffData,
                                ...evaluationData,
                                'staffEvaluation.allStaffData.$': staffEvaluation,
                            },
                        },
                    },
                },
                {
                    updateOne: {
                        filter: {
                            ...assignmentStudentQuery,
                            'staffEvaluation.allStaffData.staffId': { $ne: staffObjectId },
                        },
                        update: {
                            ...(!needToResubmit && {
                                $push: { 'staffEvaluation.allStaffData': staffEvaluation },
                            }),
                            $set: evaluationData,
                        },
                    },
                },
            );
            if (needToResubmit) {
                bulkOperations.push({
                    deleteMany: {
                        filter: {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            assignmentId: convertToMongoObjectId(assignmentId),
                            studentId: convertToMongoObjectId(studentId),
                        },
                    },
                });
            }
        });

        const assignmentAnswer = await assignmentAnswerSchema.bulkWrite(bulkOperations);
        if (!assignmentAnswer)
            return {
                statusCode: 401,
                message: 'Error In Updating',
            };

        if (!needToResubmit && prompt && prompt.length) {
            const staffEvalForPrompt = [];

            studentsIds.forEach((studentId) => {
                const assignmentPromptStudentQuery = {
                    ...assignmentQuery,
                    studentId: convertToMongoObjectId(studentId),
                };
                processSubPrompts({
                    prompt,
                    staffObjectId,
                    staffEvalForPrompt,
                    assignmentPromptStudentQuery,
                });
            });

            const updatePromptScore = await assignmentPromptAnswerSchema.bulkWrite(
                staffEvalForPrompt,
            );
            if (!updatePromptScore)
                return {
                    statusCode: 401,
                    message: 'Error In Updating',
                };
        }
        return {
            statusCode: 200,
            message: 'Updated Successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const studentAssignmentMarkUpdate = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId, releasedStaffId, score } = query;
        if (!score)
            return {
                statusCode: 401,
                message: 'Score Required',
            };
        const studentAssignmentQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            assignmentId: convertToMongoObjectId(assignmentId),
            studentId: convertToMongoObjectId(studentId),
        };
        const updateStudentMark = await assignmentAnswerSchema.updateOne(studentAssignmentQuery, {
            'staffEvaluation.totalMark': score,
            'staffEvaluation.staffStatus': RELEASED,
            status: RELEASED,
            'staffEvaluation.releasedStaffId': releasedStaffId,
        });
        if (!updateStudentMark)
            return {
                statusCode: 401,
                message: ' Error In Updating ',
            };
        const studentList = await assignmentAnswerSchema
            .find(studentAssignmentQuery, {
                studentId: 1,
                staffEvaluation: 1,
                noOfAttempt: 1,
                status: 1,
                assignmentId: 1,
                specialDue: 1,
                submittedAt: 1,
                groupId: 1,
            })
            .populate({ path: 'studentId', select: { name: 1 } })
            .lean();
        return {
            statusCode: 200,
            message: 'Updated Successfully',
            data: studentList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCalenderData = async ({ query = {} }) => {
    try {
        const { courseId } = query;
        const cachedProgramCalenderData = await allProgramCalendarDatas();
        const programCalenderData = [];
        cachedProgramCalenderData.forEach((cachedProgramCalenderElement) => {
            const matchingLevels = cachedProgramCalenderElement.level.filter((levelElement) =>
                levelElement.course.some(
                    (courseElement) => courseElement._course_id.toString() === courseId,
                ),
            );

            if (matchingLevels.length) {
                matchingLevels.forEach((matchingLevelElement) => {
                    programCalenderData.push({
                        year: matchingLevelElement.year,
                        level_no: matchingLevelElement.level_no,
                        term: matchingLevelElement.term,
                        level_id: matchingLevelElement._id,
                        _institution_calendar_id:
                            cachedProgramCalenderElement._institution_calendar_id,
                    });
                });
            }
        });
        if (!programCalenderData)
            return {
                statusCode: 200,
                message: 'DATA_NOT_FOUND',
            };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: programCalenderData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createdStudentCheckExistAssignment = async ({
    batch,
    level,
    courseId,
    studentIds,
    _institution_id,
    institutionCalendarId,
}) => {
    try {
        const studentDetails = await userSchema
            .find({ _id: { $in: studentIds } }, { name: 1 })
            .lean();
        if (!studentDetails.length) return true;

        const assignmentQuery = {
            term: batch,
            isActive: true,
            isDeleted: false,
            level_no: level,
            'submission.assignTo.isAllStudent': true,
            _course_id: convertToMongoObjectId(courseId),
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };

        const assignmentProject = {
            _id: 1,
            submission: 1,
            _program_id: 1,
        };

        const assignmentData = await assignmentSchema
            .find(assignmentQuery, assignmentProject)
            .lean();
        if (!assignmentData.length) return true;

        const assignmentBulkUpdate = [];
        const assignmentAnswerBulkUpdate = [];
        const programId = assignmentData[0]._program_id;

        for (const assignmentElement of assignmentData) {
            const { _id, submission } = assignmentElement;
            for (const studentElement of studentDetails) {
                const studentId = studentElement._id;
                const studentName = `${studentElement.name.first} ${studentElement.name.last}`;
                const existCheckStudentId = submission.assignTo[0].studentIds.some(
                    (elem) => elem.studentId == studentId,
                );
                if (!existCheckStudentId) {
                    assignmentBulkUpdate.push({
                        updateOne: {
                            filter: { _id },
                            update: {
                                $addToSet: {
                                    'submission.assignTo.0.studentIds': {
                                        studentId,
                                        name: studentName,
                                    },
                                    'evaluation.evaluators.$[].studentIds': studentId,
                                },
                            },
                        },
                    });

                    assignmentAnswerBulkUpdate.push({
                        insertOne: {
                            document: {
                                programId,
                                studentId,
                                status: PENDING,
                                assignmentId: convertToMongoObjectId(_id),
                                courseId: convertToMongoObjectId(courseId),
                                _institution_id: convertToMongoObjectId(_institution_id),
                                _institution_calendar_id:
                                    convertToMongoObjectId(institutionCalendarId),
                            },
                        },
                    });
                }
            }
        }

        const createMissingAssignments = await assignmentSchema.bulkWrite(assignmentBulkUpdate);

        if (!createMissingAssignments) return false;

        const createMissingAssignmentsAnswer = await assignmentAnswerSchema.bulkWrite(
            assignmentAnswerBulkUpdate,
        );

        if (!createMissingAssignmentsAnswer) return false;

        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removedStudentCheckExistAssignment = async ({
    _institution_id,
    institutionCalendarId,
    term,
    level_no,
    _course_id,
    studentIds,
}) => {
    try {
        const assignmentQuery = {
            term,
            level_no,
            isActive: true,
            isDeleted: false,
            'submission.assignTo.studentIds.studentId': { $in: studentIds },
            _course_id: convertToMongoObjectId(_course_id),
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };

        const assignmentData = await assignmentSchema.find(assignmentQuery, { _id: 1 }).lean();
        if (!assignmentData.length) return false;

        const assignmentIds = assignmentData.map(({ _id }) => _id);

        const removeStudentFromAssignment = await assignmentSchema.updateMany(
            { _id: { $in: assignmentIds } },
            { $pull: { 'submission.assignTo.$[].studentIds': { studentId: { $in: studentIds } } } },
        );
        if (!removeStudentFromAssignment) return false;

        const updateData = { isDeleted: true };
        const removeAssignmentAnswer = await assignmentAnswerSchema.updateMany(
            { assignmentId: { $in: assignmentIds }, studentId: { $in: studentIds } },
            updateData,
        );
        if (!removeAssignmentAnswer) return false;

        const removeAssignmentPromptAnswer = await assignmentPromptAnswerSchema.updateMany(
            { assignmentId: { $in: assignmentIds }, studentId: { $in: studentIds } },
            updateData,
        );

        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDuplicateAssignments = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { staffId, roleId } = query;
        const {
            courseId,
            courseCode,
            courseName,
            rotationCount,
            assignmentIds,
            duplicateTo = '',
        } = body;
        if (!assignmentIds && !assignmentIds.length) {
            return { statusCode: 400, message: 'AssignmentIds Required' };
        }
        const assignmentData = await assignmentSchema
            .find(
                {
                    isDeleted: false,
                    _id: {
                        $in: assignmentIds.map((assignmentIdElement) =>
                            convertToMongoObjectId(assignmentIdElement),
                        ),
                    },
                },
                assignmentQuery,
            )
            .lean();
        if (!assignmentData.length) {
            return { statusCode: 404, message: 'Assignment Data Not Found' };
        }
        const assignmentPrompts = await assignmentPromptSchema
            .find(
                {
                    isDeleted: false,
                    assignmentId: { $in: assignmentIds.map((id) => convertToMongoObjectId(id)) },
                },
                promptsProject,
            )
            .lean();
        const assignmentPromptBulkCreate = [];
        const newAssignments = [];
        for (const assignmentElement of assignmentData) {
            const assignmentPrompt = assignmentPrompts.filter(
                (promptElement) =>
                    promptElement.assignmentId.toString() === assignmentElement._id.toString(),
            );
            const newAssignmentId = convertToMongoObjectId();
            const { basic } = assignmentElement;

            if (basic.prompts.length && !assignmentPrompt.length) {
                return { statusCode: 404, message: 'Assignment Prompt Data Not Found' };
            }
            const { assignmentParentPromptId } = createNewAssignmentPrompts({
                assignmentPromptBulkCreate,
                assignmentPrompt,
                newAssignmentId,
                isDuplicateKey: true,
            });
            assignmentElement._institution_id = _institution_id;
            assignmentElement._institution_calendar_id = _institution_calendar_id;
            assignmentElement.isActive = false;
            assignmentElement.isDraft = true;
            delete assignmentElement.createdAt;
            delete assignmentElement.updatedAt;
            delete assignmentElement.publishedAt;
            basic.prompts = assignmentParentPromptId;
            assignmentElement.isCopyFrom = assignmentElement._id;
            assignmentElement._id = newAssignmentId;
            assignmentElement.isDuplicate = true;
            assignmentElement.basic.title = `${basic.title} - duplicate`;
            if (basic.type !== 'Summative Assignment') {
                assignmentElement.submission.assignTo[0].studentIds = [];
                assignmentElement.submission.assignTo[1].studentIds = [];
                assignmentElement.submission.assignTo[0].isAllStudent = false;
            }
            if (duplicateTo === 'otherCourse') {
                assignmentElement._course_id = courseId;
                assignmentElement.course_code = courseCode;
                assignmentElement.courseName = courseName;
                assignmentElement.rotation_count = rotationCount;
            }
            newAssignments.push(assignmentElement);
        }
        if (newAssignments.length) {
            const createDuplicateAssignments = await assignmentSchema.insertMany(newAssignments);
            if (!createDuplicateAssignments) {
                return { statusCode: 410, message: 'Duplicate Assignment Failed' };
            }
        }
        if (assignmentPromptBulkCreate.length) {
            const assignmentPromptCreate = await assignmentPromptSchema.bulkWrite(
                assignmentPromptBulkCreate,
            );

            if (!assignmentPromptCreate) {
                return { statusCode: 410, message: 'Error In Creating Prompt Duplicate Data' };
            }
        }
        return {
            statusCode: 200,
            message: 'Duplicate Assignment Created',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getResetAssignments = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId } = query;
        if (!assignmentId) return { statusCode: 400, message: 'AssignmentId Required' };
        const assignment = await assignmentSchema
            .findOne(
                {
                    isDeleted: false,
                    _id: convertToMongoObjectId(assignmentId),
                },
                { basic: 1 },
            )
            .lean();
        if (!assignment) return { statusCode: 404, message: 'Assignment Data Not Found' };
        const assignmentPromptBulkCreate = [];
        const updateData = { isDeleted: true };
        const { basic } = assignment;
        const assignmentPrompt = await assignmentPromptSchema
            .find(
                { assignmentId: convertToMongoObjectId(assignmentId), isDeleted: false },
                promptsProject,
            )
            .lean();
        if (basic.prompts.length && !assignmentPrompt.length)
            return { statusCode: 404, message: 'Assignment Prompt Data Not Found' };
        const { assignmentParentPromptId } = createNewAssignmentPrompts({
            assignmentPromptBulkCreate,
            assignmentPrompt,
            newAssignmentId: assignmentId,
            isReset: true,
        });
        const commonField = {
            isDeleted: false,
            assignmentId: convertToMongoObjectId(assignmentId),
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        const assignmentComments = await assignmentCommentsSchema
            .find(commonField, { studentId: 1 })
            .lean();
        if (assignmentComments.length) {
            const assignmentCommentBulkCreate = [];
            for (const commentElement of assignmentComments) {
                const { studentId } = commentElement;

                assignmentCommentBulkCreate.push(
                    {
                        insertOne: {
                            document: {
                                studentId,
                                comments: [],
                                ...commonField,
                            },
                        },
                    },
                    {
                        updateOne: {
                            filter: { ...commonField, studentId },
                            update: updateData,
                        },
                    },
                );
            }
            const createComments = await assignmentCommentsSchema.bulkWrite(
                assignmentCommentBulkCreate,
            );
            if (!createComments)
                return {
                    statusCode: 410,
                    message: 'Failed Reset',
                };
        }
        const assignmentStudents = await assignmentAnswerSchema
            .find({ assignmentId: convertToMongoObjectId(assignmentId), isDeleted: false })
            .lean();
        if (assignmentStudents.length) {
            const studentDataBulkUpdate = [];
            for (const studentElement of assignmentStudents) {
                const staffEvaluation = {
                    allStaffData: [],
                };
                delete studentElement._id;
                studentElement.status = PENDING;
                studentElement.isReset = true;
                studentElement.isDeleted = false;
                delete studentElement.createdAt;
                delete studentElement.updatedAt;
                delete studentElement.submittedAt;
                studentElement.staffEvaluation = staffEvaluation;
                studentElement.promptsAnswer = [];
                studentElement.assignmentAnswer = [];
                studentElement.noOfAttempt = 0;
                studentDataBulkUpdate.push({
                    insertOne: {
                        document: studentElement,
                    },
                });
            }
            const deleteAssignmentStudent = await assignmentAnswerSchema.updateMany(
                {
                    assignmentId: convertToMongoObjectId(assignmentId),
                },
                updateData,
            );
            if (!deleteAssignmentStudent)
                return {
                    statusCode: 410,
                    message: 'Error In Reset Assignment Student Data',
                };
            const createNewStudent = await assignmentAnswerSchema.bulkWrite(studentDataBulkUpdate);
            if (!createNewStudent)
                return {
                    statusCode: 410,
                    message: 'Error In Reset Assignment Student Data',
                };
        }
        if (assignmentParentPromptId.length) {
            const deletePrompt = await assignmentPromptSchema.updateMany(
                { assignmentId: convertToMongoObjectId(assignmentId) },
                updateData,
            );
            if (!deletePrompt)
                return {
                    statusCode: 410,
                    message: 'Error In Reset Assignment Prompt Data',
                };
            const createNewPrompt = await assignmentPromptSchema.bulkWrite(
                assignmentPromptBulkCreate,
            );
            if (!createNewPrompt)
                return {
                    statusCode: 410,
                    message: 'Error In Reset Assignment Prompt Data',
                };
        }
        const AssignmentPromptStudentAnswer = await assignmentPromptAnswerSchema
            .find({ assignmentId: convertToMongoObjectId(assignmentId), isDeleted: false })
            .lean();
        if (AssignmentPromptStudentAnswer.length) {
            const deletePromptAnswer = await assignmentPromptAnswerSchema.updateMany(
                { assignmentId: convertToMongoObjectId(assignmentId) },
                updateData,
            );
            if (!deletePromptAnswer)
                return {
                    statusCode: 410,
                    message: 'Error In Reset Assignment Prompt Answer Data',
                };
        }
        const updateAssignment = await assignmentSchema.updateOne(
            { _id: convertToMongoObjectId(assignmentId) },
            {
                'basic.prompts': assignmentParentPromptId,
                'evaluation.evaluators.$[].prompts': assignmentParentPromptId,
            },
        );
        if (updateAssignment.modifiedCount === 0)
            return { statusCode: 410, message: 'Error In Reset Assignment' };

        return { statusCode: 200, message: 'Reset Completed' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const extendIndividualAssignmentDueDate = async ({ body = {} }) => {
    try {
        const { assignmentId, startDate, endDate, dueDate } = body;

        if (!assignmentId) return { statusCode: 400, message: 'Assignment Id Required' };

        if (!startDate && !endDate && !dueDate)
            return { statusCode: 400, message: 'Required Field Empty' };

        const commonField = {
            duration: { type: '', value: '' },
            courseOrSession: { after: '', type: '' },
        };

        const updatedData = {};

        if (startDate) {
            updatedData['submission.start'] = {
                type: 'Date',
                previousDate: startDate.previousDate,
                dateAndTime: startDate.dateAndTime,
                ...commonField,
            };
        }

        if (endDate) {
            updatedData['submission.end'] = {
                type: 'Date',
                previousDate: endDate.previousDate,
                dateAndTime: endDate.dateAndTime,
                ...commonField,
            };
        }

        if (dueDate) {
            updatedData['submission.due'] = {
                type: 'Date',
                previousDate: dueDate.previousDate,
                dateAndTime: dueDate.dateAndTime,
                ...commonField,
            };
        }

        const extendAssignmentDueDate = await assignmentSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(assignmentId) },
            updatedData,
        );

        if (!extendAssignmentDueDate)
            return { statusCode: 410, message: 'Assignment Due Date Extended Failed' };

        return { statusCode: 200, message: 'Assignment Due Date Extended' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const extendIndividualAssignmentStudentDueDate = async ({ body = {} }) => {
    try {
        const { assignmentId, startDate, endDate, dueDate, studentId } = body;

        if (!assignmentId) return { statusCode: 400, message: 'Assignment Id Required' };
        if (!studentId) return { statusCode: 400, message: 'StudentId Id Required' };

        if (!startDate && !endDate && !dueDate)
            return { statusCode: 400, message: 'Required Field Empty' };

        const updateData = {};
        if (startDate) {
            updateData['specialDue.startDate'] = startDate;
        }

        if (endDate) {
            updateData['specialDue.endDate'] = endDate;
        }

        if (dueDate) {
            updateData['specialDue.dueDate'] = dueDate;
        }

        const extendAssignmentDueDate = await assignmentAnswerSchema.updateOne(
            {
                isDeleted: false,
                studentId: convertToMongoObjectId(studentId),
                assignmentId: convertToMongoObjectId(assignmentId),
            },
            updateData,
        );

        if (!extendAssignmentDueDate)
            return { statusCode: 410, message: 'Assignment Due Date Extended Failed' };

        return { statusCode: 200, message: 'Assignment Due Date Extended' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const revertReleasedGrade = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { assignmentId, studentId, staffId, isMultiStaff } = body;
        const studentAssignmentQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            assignmentId: convertToMongoObjectId(assignmentId),
            studentId: convertToMongoObjectId(studentId),
        };
        const studentAssignment = await assignmentAnswerSchema
            .findOne(studentAssignmentQuery, {
                'staffEvaluation.releasedStaffId': 1,
                'staffEvaluation.totalMark': 1,
                'staffEvaluation.staffStatus': 1,
                'staffEvaluation.allStaffData.staffId': 1,
                'staffEvaluation.allStaffData.totalMark': 1,
                'staffEvaluation.allStaffData.individualStaffStatus': 1,
            })
            .lean();
        if (!studentAssignment) {
            return { statusCode: 404, message: 'Student data not found' };
        }
        delete studentAssignment.staffEvaluation.releasedStaffId;
        const updatedStaffEvaluation = {
            ...studentAssignment.staffEvaluation,
            staffStatus: isMultiStaff ? IN_REVIEW : GRADED,
            totalMark: studentAssignment.staffEvaluation.totalMark,
        };
        if (updatedStaffEvaluation.allStaffData.length) {
            updatedStaffEvaluation.allStaffData = updatedStaffEvaluation.allStaffData.map(
                (staffElement) => {
                    if (staffElement.staffId.toString() === staffId) {
                        return { ...staffElement, individualStaffStatus: GRADED };
                    }
                    return staffElement;
                },
            );
        }
        const updateResult = await assignmentAnswerSchema.updateOne(studentAssignmentQuery, {
            $set: {
                staffEvaluation: updatedStaffEvaluation,
                status: SUBMITTED,
            },
        });
        if (updateResult.modifiedCount === 0) {
            return { statusCode: 410, message: 'Cannot revert released grade' };
        }
        return { statusCode: 200, message: 'Successfully reverted released grade' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    updateAssignment,
    addAssignment,
    getAssignment,
    listAssignment,
    deleteAssignment,
    listSession,
    listInstructor,
    getStudentGroups,
    updateSubmissionDue,
    getStudentCourseGroups,
    listStudentAssignments,
    listStudentCourse,
    updateStatus,
    listStaffAssignments,
    extendDue,
    getAssignmentDuration,
    studentListForAssignment,
    updateStudentData,
    updateMultipleStudentData,
    studentAssignmentMarkUpdate,
    getProgramCourseSubjectIds,
    getProgramCalenderData,
    getDuplicateAssignments,
    getResetAssignments,
    createdStudentCheckExistAssignment,
    removedStudentCheckExistAssignment,
    extendIndividualAssignmentDueDate,
    extendIndividualAssignmentStudentDueDate,
    revertReleasedGrade,
};
