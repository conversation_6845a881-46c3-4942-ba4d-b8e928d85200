/* eslint-disable no-useless-catch */
const constant = require('../utility/constants');
const Course = require('mongoose').model(constant.DIGI_COURSE);

exports.courseUpdate = async (_id, language, field, label) => {
    try {
        let course = await Course.findOne({ _id });
        if (!course) return { status: false };
        const data = {};
        data[field + '_labels'] = { language, label };
        const labels = course[field + '_labels'];
        const existLable = labels.find((label) => label.language === language);
        if (!existLable) await Course.updateOne({ _id }, { $addToSet: data });
        else {
            const query = { _id };
            query[field + '_labels.language'] = language;
            const data = {};
            data[field + '_labels.$.label'] = label;
            console.log(data);
            await Course.updateOne(query, { $set: data });
        }
        course = await Course.findOne({ _id });
        return { status: true, course };
    } catch (err) {
        throw err;
    }
};
