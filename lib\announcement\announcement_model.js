const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;
const {
    ANNOUNCEMENT,
    DRAFT,
    SCHEDULED,
    PUBLISHED,
    REVOKED,
    USER,
    EXPIRED,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../utility/constants');
const announcementSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        announcementTitle: { type: String },
        message: { type: String },
        announcementType: {
            name: { type: String },
            typeId: { type: ObjectId },
        },
        priorityType: {
            name: { type: String },
            typeId: { type: ObjectId },
            colorCode: { type: String },
        },
        attachments: [
            {
                url: String,
                name: String,
            },
        ],
        publishDate: { type: Date },
        expiryDate: { type: Date },
        selectUserGroup: [{ type: String }],
        status: { type: String, enums: [DRAFT, PUBLISHED, SCHEDULED, REVOKED, EXPIRED] },
        createdBy: { type: ObjectId, ref: USER },
        userViewList: [
            {
                userViewId: { type: ObjectId },
                view: { type: Boolean, default: false },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isParent: {
            type: Boolean,
        },
        isStudent: { type: Boolean },
        digiClassConnect: { type: String },
        selectedUserList: [{ _id: { type: ObjectId } }],
        digiClassrelation: [{ type: String }],
        staffType: { type: String },
        selectedAdminTime: [{ type: String }],
        selectedProgram: [
            {
                _institution_calendar_id: { type: ObjectId, ref: INSTITUTION_CALENDAR },
                calendar_name: { type: String },
                programList: [
                    {
                        _program_id: { type: ObjectId, ref: DIGI_PROGRAM },
                        program_name: { type: String },
                        program_code: { type: String },
                        level: [
                            {
                                term: {
                                    type: String,
                                },
                                year: { type: String },
                                level_no: { type: String },
                                rotation: {
                                    type: String,
                                    enum: ['yes', 'no'],
                                },
                                curriculum: {
                                    type: String,
                                },
                                course: [
                                    {
                                        _course_id: {
                                            type: Schema.Types.ObjectId,
                                            ref: DIGI_COURSE,
                                        },
                                        courses_name: {
                                            type: String,
                                        },
                                        courses_number: {
                                            type: String,
                                        },
                                        selectedAll: { type: Boolean },
                                        selectedCourse: [{ type: String }],
                                    },
                                ],
                                rotation_course: [
                                    {
                                        rotation_count: { type: Number },
                                        course: [
                                            {
                                                _course_id: {
                                                    type: Schema.Types.ObjectId,
                                                    ref: DIGI_COURSE,
                                                },
                                                courses_name: {
                                                    type: String,
                                                },
                                                courses_number: {
                                                    type: String,
                                                },
                                                selectedAll: { type: Boolean },
                                                selectedCourse: [{ type: String }],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
        selectedChildUser: [{ _id: { type: ObjectId } }],
        childIds: [{ type: ObjectId }],
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(ANNOUNCEMENT, announcementSchema);
