const Joi = require('joi');
const {
    SCHEDULE_TYPES: { /* SCHEDULE, REGULAR, */ EVENT, SUPPORT_SESSION },
    GENDER: { MALE, FEMALE, BOTH },
    AM,
    PM,
    EVENT_TYPE: { GENERAL, EXAM, TRAINING },
    REMOTE_PLATFORM: { ZOOM, TEAMS },
} = require('../utility/constants');

function getCourseCourseCoordinatorsValidate() {
    const schema = {
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().min(3).required(),
            _user_id: Joi.string().allow('').required(),
            _institution_calendar_id: Joi.string().alphanum().length(24),
        }),
    };
    return schema;
}
function getManageTopicsValidator() {
    const schema = {
        body: Joi.object().keys({
            _institution_calendar_id: Joi.string().alphanum().length(24).required(),
            course_id: Joi.string().alphanum().length(24).required(),
            level_no: Joi.string().required(),
            program_id: Joi.string().alphanum().length(24).required(),
            delivery_type_details: Joi.array()
                .optional()
                .items(
                    Joi.object({
                        session_id: Joi.string().alphanum().length(24).required(),
                        delivery_type_id: Joi.string().alphanum().length(24).required(),
                        delivery_type: Joi.string().required(),
                        delivery_symbol: Joi.string().required(),
                    }),
                ),
            topics: Joi.array()
                .optional()
                .items(
                    Joi.object({
                        title: Joi.string().required(),
                    }),
                ),
        }),
    };
    return schema;
}

function getUpdateManageTopicsValidator() {
    const schema = {
        body: Joi.object().keys({
            delivery_type_details: Joi.array()
                .optional()
                .items(
                    Joi.object({
                        session_id: Joi.string().alphanum().length(24).required(),
                        delivery_type_id: Joi.string().alphanum().length(24).required(),
                        delivery_type: Joi.string().required(),
                        delivery_symbol: Joi.string().required(),
                    }),
                ),
            topics: Joi.array()
                .optional()
                .items(
                    Joi.object({
                        _id: Joi.string().alphanum().length(24).optional().allow(''),
                        title: Joi.string().required(),
                    }),
                ),
        }),
    };
    return schema;
}

function getLectureSettingValidator() {
    const schema = {
        body: Joi.object().keys({
            _institution_calendar_id: Joi.string().alphanum().length(24).required(),
            _program_id: Joi.string().alphanum().length(24).required(),
            remotePlatform: Joi.string().valid(TEAMS, ZOOM),
            /* .required() */
            program_name: Joi.string().min(3).required(),
            _course_id: Joi.string().alphanum().length(24).required(),
            _session_type_id: Joi.string().alphanum().length(24).required(),
            _delivery_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().required(),
            year_no: Joi.string().required(),
            level_no: Joi.string().required(),
            subject: Joi.array().items(
                Joi.object({
                    _subject_id: Joi.string().alphanum().length(24).required(),
                    subject_name: Joi.string().required(),
                }),
            ),
            mode: Joi.string().required(),
            _infra_id: Joi.string().alphanum().length(24).optional(),
            infra_name: Joi.string().optional(),
            staff: Joi.array().items(
                Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    staff_name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }),
            ),
            attendanceTakingStaff: Joi.array().items(
                Joi.object({
                    staffId: Joi.string().alphanum().length(24).required(),
                    staffName: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }),
            ),
            session_details: Joi.array().items(
                Joi.object({
                    session_id: Joi.string().alphanum().length(24).required(),
                    student_groups: Joi.array().items(
                        Joi.object({
                            group_id: Joi.string().alphanum().length(24).required(),
                            gender: Joi.string().required(),
                            group_no: Joi.number().required(),
                            group_name: Joi.string().required(),
                            session_group: Joi.array().items(
                                Joi.object({
                                    session_group_id: Joi.string().alphanum().length(24).required(),
                                    group_no: Joi.number().required(),
                                    group_name: Joi.string().required(),
                                }),
                            ),
                        }),
                    ),
                }),
            ),
            topic: Joi.string().optional(),
            _topic_id: Joi.string().alphanum().length(24).optional(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            outsideCampus: Joi.boolean().optional(),
            // campusDetails: Joi.object()
            //     .keys({
            //         email: Joi.string().optional().allow(''),
            //         mobile: Joi.number().optional().allow(''),
            //         byDevice: Joi.string().optional(),
            //     })
            //     .optional(),
            classLeaders: Joi.array().items(Joi.string().alphanum().length(24)).optional(),
            externalStaffs: Joi.array().items(Joi.string().alphanum().length(24)).optional(),
            selfAttendance: Joi.boolean().optional(),
        }),
    };
    return schema;
}
function getAddSupportSessionValidator() {
    const schema = {
        body: Joi.object().keys({
            _institution_calendar_id: Joi.string().alphanum().length(24).required(),
            _program_id: Joi.string().alphanum().length(24).required(),
            program_name: Joi.string().min(3).required(),
            _course_id: Joi.string().alphanum().length(24).required(),
            course_code: Joi.string().required(),
            course_name: Joi.string().required(),
            term: Joi.string().required(),
            title: Joi.string().required(),
            year_no: Joi.string().required(),
            level_no: Joi.string().required(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            type: Joi.string().valid(SUPPORT_SESSION).required(),
            student_groups: Joi.array().items(
                Joi.object({
                    group_id: Joi.string().alphanum().length(24).required(),
                    delivery_symbol: Joi.string().optional(),
                    gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                    group_no: Joi.number().required(),
                    group_name: Joi.string().min(3).required(),
                    session_group: Joi.array().items(
                        Joi.object({
                            session_group_id: Joi.string().optional(),
                            group_no: Joi.string().optional(),
                            group_name: Joi.string().optional(),
                        }),
                    ),
                    students: Joi.array().items(
                        Joi.object({
                            _student_id: Joi.string().alphanum().length(24).required(),
                            academic_no: Joi.string().required(),
                            name: Joi.object({
                                first: Joi.string().required(),
                                middle: Joi.string().optional().allow(''),
                                family: Joi.string().optional().allow(''),
                                last: Joi.string().optional().allow(''),
                            }),
                            gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                        }),
                    ),
                    total_students: Joi.number().required(),
                    selected_students: Joi.number().required(),
                }),
            ),
        }),
    };
    return schema;
}
function getEditSupportSessionValidator() {
    const schema = {
        body: Joi.object().keys({
            _program_id: Joi.string().alphanum().length(24).required(),
            program_name: Joi.string().min(3).required(),
            _course_id: Joi.string().alphanum().length(24).required(),
            course_code: Joi.string().required(),
            course_name: Joi.string().required(),
            term: Joi.string().required(),
            title: Joi.string().required(),
            year_no: Joi.string().required(),
            level_no: Joi.string().required(),
            type: Joi.string().valid(SUPPORT_SESSION).required(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            student_groups: Joi.array().items(
                Joi.object({
                    group_id: Joi.string().alphanum().length(24).required(),
                    delivery_symbol: Joi.string().optional(),
                    gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                    group_no: Joi.number().required(),
                    group_name: Joi.string().min(3).required(),
                    session_group: Joi.array().items(
                        Joi.object({
                            session_group_id: Joi.string().optional(),
                            group_no: Joi.string().optional(),
                            group_name: Joi.string().optional(),
                        }),
                    ),
                    students: Joi.array().items(
                        Joi.object({
                            _student_id: Joi.string().alphanum().length(24).required(),
                            academic_no: Joi.string().required(),
                            name: Joi.object({
                                first: Joi.string().required(),
                                middle: Joi.string().optional().allow(''),
                                family: Joi.string().optional().allow(''),
                                last: Joi.string().optional().allow(''),
                            }),
                            gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                        }),
                    ),
                    total_students: Joi.number().required(),
                    selected_students: Joi.number().required(),
                }),
            ),
        }),
    };
    return schema;
}
function getAddEventValidator() {
    const schema = {
        body: Joi.object().keys({
            _institution_calendar_id: Joi.string().alphanum().length(24).required(),
            _program_id: Joi.string().alphanum().length(24).required(),
            program_name: Joi.string().min(3).required(),
            _course_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().required(),
            course_code: Joi.string().required(),
            course_name: Joi.string().required(),
            title: Joi.string().required(),
            year_no: Joi.string().required(),
            level_no: Joi.string().required(),
            type: Joi.string().valid(EVENT).required(),
            sub_type: Joi.string().required(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            student_groups: Joi.array().items(
                Joi.object({
                    group_id: Joi.string().alphanum().length(24).required(),
                    gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                    group_no: Joi.number().required(),
                    group_name: Joi.string().min(3).required(),
                    delivery_symbol: Joi.string().optional(),
                    session_group: Joi.array().items(
                        Joi.object({
                            session_group_id: Joi.string().optional(),
                            group_no: Joi.string().optional(),
                            group_name: Joi.string().optional(),
                        }),
                    ),
                    students: Joi.array().items(
                        Joi.object({
                            _student_id: Joi.string().alphanum().length(24).required(),
                            academic_no: Joi.string().required(),
                            name: Joi.object({
                                first: Joi.string().required(),
                                middle: Joi.string().optional().allow(''),
                                family: Joi.string().optional().allow(''),
                                last: Joi.string().optional().allow(''),
                            }),
                            gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                        }),
                    ),
                    total_students: Joi.number().required(),
                    selected_students: Joi.number().required(),
                }),
            ),
        }),
    };
    return schema;
}
function getEditEventValidator() {
    const schema = {
        body: Joi.object().keys({
            _program_id: Joi.string().alphanum().length(24).required(),
            program_name: Joi.string().min(3).required(),
            _course_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().required(),
            course_code: Joi.string().required(),
            course_name: Joi.string().required(),
            title: Joi.string().required(),
            year_no: Joi.string().required(),
            level_no: Joi.string().required(),
            type: Joi.string().valid(EVENT).required(),
            sub_type: Joi.string().valid(GENERAL, EXAM, TRAINING).required(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            student_groups: Joi.array().items(
                Joi.object({
                    group_id: Joi.string().alphanum().length(24).required(),
                    gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                    group_no: Joi.number().required(),
                    group_name: Joi.string().min(3).required(),
                    delivery_symbol: Joi.string().optional(),
                    session_group: Joi.array().items(
                        Joi.object({
                            session_group_id: Joi.string().optional(),
                            group_no: Joi.string().optional(),
                            group_name: Joi.string().optional(),
                        }),
                    ),
                    students: Joi.array().items(
                        Joi.object({
                            _student_id: Joi.string().alphanum().length(24).required(),
                            academic_no: Joi.string().required(),
                            name: Joi.object({
                                first: Joi.string().required(),
                                middle: Joi.string().optional().allow(''),
                                family: Joi.string().optional().allow(''),
                                last: Joi.string().optional().allow(''),
                            }),
                            gender: Joi.string().valid(MALE, FEMALE, BOTH).required(),
                        }),
                    ),
                    total_students: Joi.number().required(),
                    selected_students: Joi.number().required(),
                }),
            ),
        }),
    };
    return schema;
}
function getCreateScheduleSupportSession() {
    const schema = {
        body: Joi.object().keys({
            _institution_calendar_id: Joi.string().alphanum().length(24).required(),
            sub_type: Joi.string().required(),
            // remotePlatform: Joi.string().valid(TEAMS, ZOOM) /* .required() */,
            schedule_date: Joi.date().required(),
            remotePlatform: Joi.string().valid(TEAMS, ZOOM),
            scheduleStartDateAndTime: Joi.date().required(),
            scheduleEndDateAndTime: Joi.date().required(),
            start: Joi.object().keys({
                hour: Joi.number().required(),
                minute: Joi.number().required(),
                format: Joi.string().valid(AM, PM).required(),
            }),
            end: Joi.object().keys({
                hour: Joi.number().required(),
                minute: Joi.number().required(),
                format: Joi.string().valid(AM, PM).required(),
            }),
            mode: Joi.string().required(),
            subjects: Joi.array().items(
                Joi.object({
                    _subject_id: Joi.string().alphanum().length(24).required(),
                    subject_name: Joi.string().required(),
                }),
            ),
            staffs: Joi.array().items(
                Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    staff_name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }),
            ),
            attendanceTakingStaff: Joi.array().items(
                Joi.object({
                    staffId: Joi.string().alphanum().length(24).required(),
                    staffName: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }),
            ),
            _infra_id: Joi.string().optional().allow('').alphanum().length(24),
            infra_name: Joi.string().optional().allow(''),
            isActive: Joi.boolean(),
            outsideCampus: Joi.boolean().optional(),
            // campusDetails: Joi.object()
            //     .keys({
            //         email: Joi.string().optional().allow(''),
            //         mobile: Joi.number().optional().allow(''),
            //         byDevice: Joi.string().optional(),
            //     })
            //     .optional(),
            classLeaders: Joi.array().items(Joi.string().alphanum().length(24)).optional(),
            externalStaffs: Joi.array().items(Joi.string().alphanum().length(24)).optional(),
            selfAttendance: Joi.boolean().optional(),
        }),
    };
    return schema;
}
function updateAdvancedSettingMissedScheduleValidator() {
    const schema = {
        body: Joi.object().keys({
            isMissedToSchedule: Joi.boolean(),
        }),
    };
    return schema;
}
module.exports = {
    getCourseCourseCoordinatorsValidate: getCourseCourseCoordinatorsValidate(),
    getManageTopicsValidator: getManageTopicsValidator(),
    getUpdateManageTopicsValidator: getUpdateManageTopicsValidator(),
    getLectureSettingValidator: getLectureSettingValidator(),
    getAddSupportSessionValidator: getAddSupportSessionValidator(),
    getAddEventValidator: getAddEventValidator(),
    getEditEventValidator: getEditEventValidator(),
    getCreateScheduleSupportSession: getCreateScheduleSupportSession(),
    getEditSupportSessionValidator: getEditSupportSessionValidator(),
    updateAdvancedSettingMissedScheduleValidator: updateAdvancedSettingMissedScheduleValidator(),
};
