// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.institution_calendar = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    calendar_name: Joi.string().required(),
                    calendar_type: Joi.string()
                        .valid(constant.PRIMARY, constant.SECONDARY)
                        .required(),
                    _primary_calendar_id: Joi.string().alphanum().length(24),
                    _creater_id: Joi.string().alphanum().length(24).required(),
                    _institution_id: Joi.string().alphanum().length(24).required(),
                    primary_calendar: Joi.string()
                        .valid(constant.GREGORIAN, constant.HIJRI)
                        .required(),
                    batch: Joi.string().min(1).max(10).trim().required(),
                    start_date: Joi.date().required(),
                    end_date: Joi.date().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_calendar_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                id: Joi.string().alphanum().length(24).required(),
            }),
            body: Joi.object()
                .keys({
                    calendar_name: Joi.string(),
                    calendar_type: Joi.string().valid(constant.PRIMARY, constant.SECONDARY),
                    _primary_calendar_id: Joi.string().alphanum().length(24),
                    _creater_id: Joi.string().alphanum().length(24),
                    _institution_id: Joi.string().alphanum().length(24),
                    primary_calendar: Joi.string().valid(constant.GREGORIAN, constant.HIJRI),
                    batch: Joi.string().min(1).max(10).trim(),
                    start_date: Joi.date(),
                    end_date: Joi.date(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_calendar_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            re,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
