const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    INSTITUTION,
    COURSE_SCHEDULE,
    USER,
    FACE_REGISTER,
    LEAVE_FLOW_TYPE_STATUS: { PENDING, APPROVED, REJECTED },
    INSTITUTION_CALENDAR,
    DC_STUDENT,
    DC_STAFF,
} = require('../utility/constants');

const faceRegister = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        scheduleId: {
            type: Schema.Types.ObjectId,
            ref: COURSE_SCHEDULE,
        },
        userId: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        userType: { type: String, enum: [DC_STUDENT, DC_STAFF] },
        faceURL: { type: String },
        faceURLs: [{ type: String }],
        descriptor: [],
        descriptors: [{ face: { type: String }, descriptor: [] }],
        status: { type: String, default: PENDING, enum: [PENDING, APPROVED, REJECTED] },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        reasonToReject: { type: String },
    },
    { timestamps: true },
);
module.exports = mongoose.model(FACE_REGISTER, faceRegister);
