const { objectIdRQSchema, stringSchema } = require('../utility/validationSchemas');

// Validation schemas for userDevice module
const getUserDevicesSchema = {
    userId: objectIdRQSchema,
};

const deactivateUserDeviceSchema = {
    userId: objectIdRQSchema,
    dbRecordId: objectIdRQSchema,
    unregisteredReason: stringSchema,
};

module.exports = {
    getUserDevicesSchema,
    deactivateUserDeviceSchema,
};
