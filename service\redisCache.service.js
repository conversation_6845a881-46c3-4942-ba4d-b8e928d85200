const { redisClient } = require('../config/redis-connection');
const { logger } = require('../lib/utility/util_keys');

const {
    REDIS_FOLDERS: { USER_COURSES, USER_INSTITUTION_CALENDAR },
} = require('../lib/utility/constants');

// Multi Get Flow
// const redisMultiKeys = await redisClient.Client.mget([
//     userCourseRedisKey,
//     `${USER_COURSES}:60a4e86750e71442d1c9cb1b-${institutionCalendarId}`,
//     `${USER_COURSES}:60a4e86750e71442d1c9cb6b-${institutionCalendarId}`,
// ]);
// const usersCourses = redisMultiKeys.map((redisCourseElement) => JSON.parse(redisCourseElement));
// const usersCourses = [];
// for (redisCourseElement of redisMultiKeys) {
//     usersCourses.push(JSON.parse(redisCourseElement));
// }
// return usersCourses;

const usersCoursesRedisCacheRemove = async ({ userIds, institutionCalendarId }) => {
    try {
        logger.info(
            { userIds, institutionCalendarId },
            'courseSessionController -> usersCoursesRedisCacheRemove -> User Course Cache Remove start',
        );
        if (userIds?.length && institutionCalendarId) {
            let usersCacheFiles = userIds.map((userIdElement) => {
                return `${USER_COURSES}:${userIdElement}-${institutionCalendarId}`;
            });
            usersCacheFiles = [
                ...usersCacheFiles,
                ...userIds.map((userIdElement) => {
                    return `${USER_INSTITUTION_CALENDAR}:${userIdElement}`;
                }),
            ];
            await redisClient.Client.del(usersCacheFiles);
        }
        logger.info(
            { userIds, institutionCalendarId },
            'redisCacheService -> usersCoursesRedisCacheRemove ->  User Course Cache Remove end',
        );
    } catch (error) {
        throw new Error(error);
    }
};

const luaScript = `
local patterns = cjson.decode(ARGV[1])
for i, pattern in ipairs(patterns) do
  local keys = redis.call('KEYS', pattern)
  for j, key in ipairs(keys) do
    redis.call('DEL', key)
  end
end
return true`;

module.exports = { usersCoursesRedisCacheRemove, luaScript };
