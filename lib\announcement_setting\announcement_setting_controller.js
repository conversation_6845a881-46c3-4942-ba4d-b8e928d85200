const announcementSettingSchema = require('./announcement_setting_model');
const { convertToMongoObjectId } = require('../utility/common');
exports.addTypes = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { announcementType, priorityType, tagAnnouncementType, priorityAnnouncement } = body;
        let announcementSettingData;
        announcementSettingData = await announcementSettingSchema.findOne({
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        if (!announcementSettingData) {
            announcementSettingData = await announcementSettingSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
            });
        }
        if (announcementSettingData) {
            const addAnnouncementType = await announcementSettingSchema
                .updateOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    {
                        $set: {
                            ...(typeof tagAnnouncementType === 'boolean' && {
                                tagAnnouncementType,
                            }),
                            ...(typeof priorityAnnouncement === 'boolean' && {
                                priorityAnnouncement,
                            }),
                            ...(announcementType && { announcementType }),
                            ...(priorityType && {
                                priorityType,
                            }),
                        },
                    },
                )
                .lean();
            if (!addAnnouncementType) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        }
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.typeList = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const announcementSettingData = await announcementSettingSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                announcementType: 1,
                priorityType: 1,
                priorityAnnouncement: 1,
                tagAnnouncementType: 1,
            },
        );
        if (!announcementSettingData) return { statusCode: 200, message: 'NO_Data' };
        return { statusCode: 200, message: 'List_Data', data: announcementSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteType = async ({ body = {} }) => {
    try {
        const { announcementTypeId, priorityTypeId, announcementSettingId } = body;
        const announcementSettingData = await announcementSettingSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementSettingId),
            },
            {
                $pull: {
                    ...(announcementTypeId && {
                        announcementType: { _id: convertToMongoObjectId(announcementTypeId) },
                    }),
                    ...(priorityTypeId && {
                        priorityType: { _id: convertToMongoObjectId(priorityTypeId) },
                    }),
                },
            },
        );
        if (!announcementSettingData) return { statusCode: 410, message: 'Unable_To_Delete' };
        return { statusCode: 200, message: 'Delete_Data' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
