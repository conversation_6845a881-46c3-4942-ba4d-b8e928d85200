const constant = require('./constants');
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const { convertToMongoObjectId } = require('./common');
const InstitutionCalendars = async ({ _institution_id }) => {
    const institutionCalenders = await institution_calendar
        .find(
            {
                // _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                isActive: true,
                status: 'published',
                end_date: { $gte: new Date() },
            },
            { _id: 1 },
        )
        .sort({ _id: -1 })
        .lean();
    const institutionCalendarIds = institutionCalenders.map((calendar_id) => {
        return calendar_id._id;
    });
    return { institutionCalendarIds };
};
module.exports = InstitutionCalendars;
