const Joi = require('joi');
const common_files = require('../utility/common');

exports.role = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    staff_type: Joi.string()
                        .allow(' ')
                        .min(3)
                        .max(20)
                        .trim()
                        .error((error) => {
                            return req.t('STAFFTYPE_REQUIRED');
                        }),
                    position_type: Joi.string()
                        .allow(' ')
                        .min(3)
                        .max(20)
                        .trim()
                        .error((error) => {
                            return req.t('POSITIONTYPE_REQUIRED');
                        }),
                    role: Joi.string()
                        .allow(' ')
                        .min(3)
                        .max(20)
                        .trim()
                        .error((error) => {
                            return req.t('ROLE_REQUIRED');
                        }),
                    _institution_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('INSTITUTION_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.role_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
