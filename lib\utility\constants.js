const { EMQ, MQ, CQ, EQ, MCQ, TF, SAQ, SCQ, IN_REVIEW } = require('./enums');
const { APP_STATE } = require('./util_keys');
const constants = Object.freeze({
    //external staff
    EXTERNAL_STAFF: 'external_staff',
    LEADER_BOARD: 'leader_board',
    STAFF_ATTENDANCE: 'staff_attendance',
    QUIZ: 'quiz',
    SUPPORT_SESSION: 'support_session',
    ENGAGER: 'engager',
    OVERALL_MANUAL: 'overall_manual',
    MANUALLY_CONDUCTED: 'manually_conducted',
    SAVE_AS_DRAFT: 'save_as_draft',
    REVIEW: 'review',
    RE_SUBMIT: 're_submit',
    RESUBMISSION: 'resubmission',
    SUBMITTED: 'submitted',
    QAPC_FORM: 'qapc_form',
    QAPC_INCORPORATE_SECTIONS: 'qapc_incorporate_sections',
    QAPC_FORM_SETTING: 'qapc_form_settings',
    FORM_APPROVER: 'Form Approver',
    FORM_INITIATOR: 'Form Initiator',
    QAPC_FORM_INITIATOR: 'qapc_form_initiator',
    UNPUBLISHED: 'unpublished',
    QAPC_INITIATOR_GUIDE_RESOURCES: 'qapc_initiator_guide_resources',
    QAPC_FORM_COURSES_GROUPS: 'qapc_form_courses_groups',
    QAPC_USER_DASHBOARD_SETTING: 'qapc_user_dashboard_settings',
    CATEGORY_SELECTED: { PUBLISH: 'publish', COMMENT: 'comment', EDIT: 'edit' },
    ENTIRE: 'entire',
    SPECIFIC: 'specific',
    SAQR_INSTITUTION_TYPE: {
        UNIVERSITY: 'university',
    },
    EVERY: 'every',
    ARCHIVE: 'archive',
    CATEGORY_FORM: 'category_form',
    QAPC_FORM_SETTING_COURSES: 'qapc_form_setting_courses',
    VIEW: 'View',
    EDIT: 'Edit',
    QAPC_SETTING: 'qapc_setting',
    QAPU_CATEGORY: 'qapc_category',
    QAPC_ROLE: 'qapc_roles',
    QAPC_ACTION: 'qapc_action',
    QAPC_SUB_MODULE: 'qapc_sub_module',
    QAPC_PERMISSION: 'qapc_permission',
    QAPC_FORM_CATEGORY: 'qapc_form_categories',
    QAPC_GROUPED_BY_CATEGORY: 'cfpc',
    QAPC_GROUPED_BY_PROGRAM: 'pccf',
    ANNUAL: 'annual',
    PERIODIC: 'periodic',
    SECTION: 'section',
    COMPLETE: 'complete',
    TAG_LEVEL: {
        PROGRAM: 'program',
        COURSE: 'course',
        INSTITUTION: 'institution',
    },
    FORM: 'form',
    ROOM_TYPE: {
        LAB: 'lab',
        LECTURE_ROOM: 'lecture_room',
        CLINIC: 'clinic',
    },

    GENDER: {
        MALE: 'male',
        FEMALE: 'female',
        BOTH: 'both',
    },
    DEPARTMENT_MASTER: {
        DEPARTMENT: 'department',
        DIVISION: 'division',
    },
    MODEL: {
        COURSE: 'course',
        MODULE: 'module',
        ELECTIVE: 'elective',
    },
    QAPC_SETTING_ATTEMPT_TYPE: 'qapc_setting_attempt_type',
    QAPC_SETTING_TAG: 'qapc_setting_tag',
    LEVEL_HIERARCHY: 'Level 1 Hierarchy',
    LMS_ATTENDANCE_CONFIG: 'lms_attendance_config',
    PRODUCTION_ENV: 'production',
    LOCAL_ENV: 'local',
    DEV_ENV: 'development',
    COLLEGE: 'colleges',
    COUNTRY: 'countries',
    COUNTRY_STATE_CITY: 'country_state_cities',
    CHILD_EMERGENCY: 'child_emergency',
    LMS: 'lms',
    LMS_REVIEW: 'lms_review',
    LMS_ROLES: 'lms_roles',
    LMS_STUDENT_ABSENCE_WARNING_CALCULATION: 'lms_student_absence_warning_calculation',
    LMS_CATEGORYSCHM: 'lms_categories',
    LMS_CATEGRORY_LEAVE_TYPE: 'lms_leave_type',
    LMS_STAFF_LEAVE: 'staff_leave',
    LMS_STAFF_LEAVE_SETTINGS: 'staff_leave_settings',
    LMS_STUDENT_SETTING: 'lms_student_setting',
    LMS_STUDENT_SETTING_CALENDAR: 'lms_student_setting_calendar',
    GENERAL: 'general',
    MEDICAL: 'medical',
    LMS_STUDENT: 'lms_student',
    LMS_DENIAL: 'lms_denial',
    LMS_DENIAL_LOG: 'lms_denial_log',
    COURSE: 'courses',
    DEPARTMENT: 'departments',
    INFRASTRUCTURE: 'infrastructures',
    INFRASTRUCTUREEVENTS: 'infrastructure_events',
    INFRASTRUCTURE_DELIVERY_TYPE: 'infrastructure_delivery_types',
    INFRASTRUCTURE_MANAGEMENT: 'infrastructure_managements',
    INFRASTRUCTURE_EVENTS: 'infrastructure_events',
    DAY_GROUP: 'day_groups',
    TIME_GROUP: 'time_groups',
    COLLEGE_BUILDING: 'college_buildings',
    LECTURE_ROOM: 'lecture_rooms',
    HOSPITAL: 'hospitals',
    MODULES: 'modules',
    POSITION: 'positions',
    PROGRAM: 'programs',
    ROLE: 'roles',
    OVERVIEW: 'overview',
    HISTORY: 'history',
    CANCELLED: 'Cancelled',
    OVERWRITE_CANCELLED: 'overwrite cancelled',
    APPROVED: 'Approved',
    FORWARD: 'Forwarded',
    REJECT: 'Rejected',
    DELAYED: 'Delayed',
    SKIPPED: 'Skipped',
    WITHDRAWN: 'Withdrawn',
    PERMISSION: 'permissions',
    ON_DUTY: 'on_duty',
    NOT_INITIATED: 'Not Initiated',
    NOT_APPLICABLE: 'Not Applicable',
    ALL_USERS: 'Approval Required From All Users',
    ANY_ONE_USER: 'Approval Required From Any One User',
    ANY_ONE_IN_EACH_ROLE: 'Approval Required From Any One In Each Role',
    ANY_ONE_IN_ANY_ROLE: 'Approval Required From Any One In Any Role',
    PRIVILEGE: 'privileges',
    ROLE_SET: 'role_sets',
    PERMISSION_SET: 'permission_sets',
    SEMESTER: 'semesters',
    STAFF: 'staffs',
    STUDENT: 'students',
    TOPIC: 'topics',
    UNIVERSITY: 'universities',
    STUDENT_GROUP: 'student_groups',
    INSTITUTION_CALENDAR: 'institution_calendars',
    PROGRAM_CALENDAR: 'program_calendars',
    CALENDAR_EVENT: 'calendar_events',
    PROGRAM_CALENDAR_EVENT: 'program_calendar_events',
    PROGRAM_CALENDAR_COURSE: 'program_calendar_courses',
    INSTITUTION: 'institutions',
    DEPARTMENT_DIVISIONS: 'department_divisions',
    DEPARTMENT_SUBJECT: 'department_subjects',
    CREDIT_HOURS_MASTER: 'credit_hours_masters',
    CREDIT_HOURS_CALC: 'credit_hours_calcs',
    CREDIT_HOURS_INDIVIDUAL: 'credit_hours_individuals',
    SESSION_TYPE: 'session_types',
    SESSION_ORDER: 'session_orders',
    RESET_TOKEN: 'reset_tokens',
    INSTITUTION_CALENDAR_EVENT_REVIEW: 'institution_calendar_event_reviews',
    NOTIFICATIONS: 'notifications',
    USER: 'users',
    GUEST_USER: 'guestusers',
    GUEST: 'guest',
    PARENT_USER: 'parent_users',
    DIGI_CHAT: 'digi_chat',
    USER_HISTORY: 'user_historys',
    STUDENT_LEAVE_REGISTER: 'student_leave_registers',
    STAFF_COMMITTEE: 'staff_committees',
    ROLES_OFFICES_LIST: 'roles_offices_lists',
    ROLES_PERMISSION: 'roles_permissions',
    ROLE_ASSIGN_TO_STAFF: 'role_assign_to_staff',
    INFRASTRUCTURE_EVENT_EXAM: 'infrastructure_event_exams',
    INFRASTRUCTURE_DELIVERY_MEDIUM: 'infrastructure_delivery_mediums',
    COURSE_STAFF_ALLOCATION: 'course_staff_allocation',
    COURSE_SCHEDULE: 'course_schedules',
    COURSE_SCHEDULE_SETTING: 'course_schedule_setting',
    COURSE_SCHEDULE_DELIVERY_SETTINGS: 'course_schedule_delivery_settings',
    COURSE_MANAGEMENT_SETTING: 'course_management_setting',
    SESSION: 'sessions',
    DOCUMENT_MANAGER: 'document_managers',
    USER_VACCINATION_DETAILS: 'user_vaccination_details',
    VACCINATION: 'vaccination',
    APP_VERSION_CONTROL: 'app_version_control',
    STUDENT_CRITERIA_MANIPULATION: 'student_criteria_manipulation',
    USER_BASED: 'User Based',
    ROLE_BASED: 'Role Based',
    OFFICE_LIST: 'office',
    ROLE_LIST: 'role',
    REVOKED: 'revoked',
    AUTOMATIC: 'automatic',
    MANUAL: 'manual',
    CUMULATIVE: 'cumulative',
    INDIVIDUAL: 'individual',
    WARNING_CONFIG_TYPE: { COURSE_BASED: 'course', COMPREHENSIVE: 'comprehensive' },
    WARNING_CONFIG_BASED: { HOUR: 'hour', SESSION: 'session' },

    ACTIVE: 'active',
    INACTIVE: 'inactive',
    BLOCKED: 'blocked',

    IMPORTED: 'imported',
    PASSWORD_CONFIRMED: 'password_confirmed',
    PROFILE_UPDATED: 'profile_updated',
    VERIFICATION_DONE: 'verification_done',
    PENDING: 'pending',
    LMS_PENDING: 'Pending',
    CORRECT: 'correct',
    CORRECTION_REQUIRED: 'error',
    VALID: 'valid',
    INVALID: 'invalid',
    DONE: 'success',
    PUBLISHED: 'published',
    SCHEDULED: 'scheduled',
    RESUBMIT: 'resubmit',
    IN_REVIEW: 'In Review',
    YET_TO_START: 'Yet to Start',
    REJECTED: 'Rejected',
    PENDING_WITH_OTHERS: 'pending with others',
    PENDING_WITH_YOU: 'pending with you',
    TILL_THE_END_OF_LEVEL: 'Till the end of level',
    START_OF_THE_NEXT_LEVEL: 'Start of the next level',
    END_OF_THE_COURSES: 'End of the courses',
    START_OF_THE_NEXT_ACADEMIC_YEAR: 'Start of the next academic year',
    END_OF_THE_ACADEMIC_YEAR: 'End of the academic year',
    STUDENTS: 'student',
    FACULTY: 'faculty',

    PRIMARY: 'primary',
    AUXILIARY: 'auxiliary',
    SECONDARY: 'secondary',

    ACADEMIC: 'academic',
    ADMINISTRATION: 'administration',
    BOTH: 'both',
    ONLINE: 'online',
    ON_SITE: 'on_site',
    FULL_TIME: 'full_time',
    PART_TIME: 'part_time',
    BY_DATE: 'by_date',
    BY_DAY: 'by_day',
    DAYS: {
        SUNDAY: 'sunday',
        MONDAY: 'monday',
        TUESDAY: 'tuesday',
        WEDNESDAY: 'wednesday',
        THURSDAY: 'thursday',
        FRIDAY: 'friday',
        SATURDAY: 'saturday',
    },
    WEEKDAYS: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],
    GREGORIAN: 'gregorian',
    HIJRI: 'hijri',
    EVENT_TYPE: {
        EXAM: 'exam',
        HOLIDAY: 'holiday',
        TRAINING: 'training',
        ORIENTATION: 'orientation',
        GENERAL: 'general',
    },
    NOTIFY_VIA: {
        ALL: 'all',
        MOBILE: 'mobile',
        EMAIL: 'email',
        DIGISCHEDULER: 'digischeduler',
        DIGICLASS: 'digiclass',
    },
    DEAN: 'dean',
    REVIEWER: 'reviewer',
    FORWARDER: 'forwarder',
    APPROVER: 'approver',
    EVENT_WHOM: {
        STUDENT: 'student',
        STAFF: 'staff',
        BOTH: 'both',
    },
    PRIVATE: 'private',
    ALL_CHAT: 'allChat',
    COURSE_CHAT: 'courseChat',
    PARTICIPANTS: 'participants',
    UNREAD: 'unread',
    EVENT_MODE: {
        ONLINE: 'online',
        OFFLINE: 'offline',
    },
    LIVE: 'live',

    NOTIFICATION_PRIORITY: {
        LOW: 'low',
        MEDIUM: 'medium',
        HIGH: 'high',
    },

    RELATION: {
        PARENT: 'parent',
        GUARDIAN: 'guardian',
        FATHER: 'father',
        MOTHER: 'mother',
        SIBLING: 'sibling',
        GRANDPARENT: 'grandparent',
        AUNT: 'aunt',
        UNCLE: 'uncle',
        SPOUSE: 'spouse',
    },

    BATCH: {
        REGULAR: 'regular',
        INTERIM: 'interim',
        BOTH: 'both',
    },

    CALENDAR: {
        INSTITUTION: 'institution',
        PROGRAM: 'program',
    },

    YEAR_LEVEL: {
        YEAR: 'year',
        LEVEL: 'level',
    },

    STUDENT_GROUP_MODE: {
        FYD: 'foundation',
        COURSE: 'course',
        ROTATION: 'rotation',
    },

    DIRECTION: {
        UP: 'up',
        DOWN: 'down',
    },

    BY_DATE_WEEK: {
        DATE: 'date',
        WEEK: 'week',
    },

    DAYS_MODE: {
        DAILY: 'daily',
        WEEKLY: 'weekly',
        YEAR: 'year',
        MONTH: 'month',
    },
    CUSTOM: 'custom',
    ALL: 'all',
    ANY: 'any',
    BY_USER: 'user',
    COURSE_MANAGEMENT_SESSION_TYPE: {
        EXTRA_CURRICULAR: 'extra_curricular',
        BREAK: 'break',
    },
    TIME_GROUP_BOOKING_TYPE: {
        ONSITE: 'onsite',
        REMOTE: 'remote',
    },

    FLOOR_CATEGORY: {
        LEVEL: 'level',
        BASEMENT: 'basement',
    },

    BUILDING_TYPE: {
        HOSPITAL: 'hospital',
        COLLEGE: 'college',
    },

    LEAVE_FLOW_TYPE: {
        REVIEW: 'review',
        FORWARD: 'forward',
        APPROVE: 'approve',
    },
    PAYMENT_STATUS: {
        PAID: 'paid',
        UNPAID: 'unpaid',
    },
    LEAVE_FLOW_TYPE_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved',
        REJECTED: 'rejected',
    },
    LMS_STAFF_TYPE: {
        ACADEMIC_AND_ADMINISTRATIVE: 'academic_and_administrative_staff',
        ADMINISTRATIVE: 'administrative',
    },
    LMS_ROLE_TYPE: {
        REVIEWER: 'reviewer',
        FORWARDER: 'forwarder',
        APPROVER: 'approver',
        REPORT_ABSENCE: 'report_absence',
    },
    LEAVE_TYPE: {
        ONDUTY: 'on_duty',
        LEAVE: 'leave',
        PERMISSION: 'permission',
        REPORT_ABSENCE: 'report_absence',
    },
    COURSE_WISE: 'course_wise',
    STUDENT_WISE: 'student_wise',
    SESSION_WISE: 'session_wise',
    GLOBAL_WISE: 'global_wise',
    ROLE_ASSIGN: 'role_assigns',
    DIGI_LABEL: 'digi_labels',
    DIGI_INSTITUTE: 'digi_institute',
    DIGI_PROGRAM: 'digi_programs',
    DIGI_DEPARTMENT_SUBJECT: 'digi_department_subject',
    DIGI_SESSION_DELIVERY_TYPES: 'digi_session_delivery_types',
    DIGI_CURRICULUM: 'digi_curriculums',
    DIGI_UNIVERSITY: 'digi_university',
    DIGI_COLLEGE: 'digi_college',
    DIGI_COURSE: 'digi_course',
    DIGI_SESSION_ORDER: 'digi_session_order',
    DIGI_SESSION_ORDER_MODULE: 'digi_session_order_module',
    DIGI_COURSE_ASSIGN: 'digi_course_assign',
    DIGI_COURSE_GROUP: 'digi_course_group',
    STUDENT_SESSION_SURVEY: 'student_session_survey',
    INSTITUTION_SETTINGS: 'institution_settings',
    UNIVERSITY_INSTITUTIONS: 'university_institutions',
    DIGI_SURVEY: 'digi_survey',
    DIGI_SURVEY_SECTION: 'digi_survey_section',
    DIGI_SURVEY_QUESTION: 'digi_survey_question',
    DIGI_SURVEY_STUDENT_SUBMISSION: 'digi_survey_student_submission',
    // COURSE_SCHEDULE_SETTING: "course_schedule_setting",
    HOURS_AS: {
        TOTAL: 'total',
        SPLIT_UP: 'split_up',
    },
    SET_VALUE_AS: {
        FIXED_VALUE: 'fixed_value',
        RANGE_OF_VALUES: 'range_of_values',
    },
    INSTITUTION_TYPE: {
        INDIVIDUAL: 'individual',
        GROUP: 'group',
    },
    COURSE_TYPE: {
        STANDARD: 'standard',
        SELECTIVE: 'selective',
    },
    FRAMEWORK: 'frameworks',
    MAPPING_TYPES: {
        IMPACT: 'impact_mapping_types',
        CONTENT: 'content_mapping_types',
    },
    MAPPING_TYPE_LIST: {
        IMPACT: 'impact',
        ALIGNMENT: 'alignment',
    },
    CONTENT_MAPPING_TYPE: {
        REQUIRED: 'required',
        OPTIONAL: 'optional',
    },
    PROGRAM_TYPE: {
        PROGRAM: 'program',
        PREREQUISITE: 'pre-requisite',
    },
    GRADUATE_TYPE: {
        UG: 'ug',
        PG: 'pg',
    },
    UG: 'undergraduate',
    PG: 'postgraduate',
    DASHBOARD_SETTING: 'dashboard_settings',

    /* DEPARTMENT_TYPE: {
      STUDY: 'study',
      TRAINING: 'training',
      RESEARCH: 'research'
  }, */
    // from DA
    DS_NONE: 'none',
    // collection names
    DS_USER: 'User',
    DS_STUDENT: 'Student',
    DS_FACULTY: 'Faculty',
    DS_COUNTRY: 'Country',
    DS_TEST_CENTER: 'Test_center',
    DS_ROLE: 'Role',
    DS_INSTITUTION: 'Institution',
    DS_MODULE: 'Module',
    DS_PERMISSION: 'Permission',
    DS_UI_ELEMENT: 'ui_element',
    DS_TOKEN: 'token',
    DS_ITEM_TYPE: 'Item_type',
    DS_ITEM: 'Item',
    DS_EXAM_TYPE: 'Exam_type',
    DS_FRAMEWORK: 'Framework',
    DS_ASSESSMENT: 'assessment',
    DS_ASSESSMENT_ANSWER: 'assessmentAnswer',
    DS_TAXONOMY: 'Taxonomy',
    DS_MAPPING: 'Mapping',
    DS_PROGRAM: 'Program',
    DS_CLO_MAPPING: 'Clo_mapping',
    DS_ACTIVITY_LOG: 'activity_log',
    DS_ASSESSMENT_TYPE: 'assessment_type',
    DS_ATTEMPT_TYPE: 'attempt_type',
    DS_EXAM_TIME: 'exam_time',
    DS_EXAM_DATE: 'exam_date',
    DS_EXAM: 'exam',
    DS_EXAM_COURSE_GROUP: 'exam_course_group',
    DS_COURSE_GROUP: 'course_group',
    DS_TIME_TABLE: 'time_table',
    DS_UPLOAD_GROUP: 'upload_group',
    DS_ACADEMIC_YEAR: 'academic_year',

    DS_TYPE_STUDENT: 'student',
    DS_TYPE_FACULTY: 'faculty',

    DS_FORMATIVE: 'formative',
    DS_SUMMATIVE: 'summative',

    DS_PRODUCTION_ENV: 'production',
    DS_LOCAL_ENV: 'local',
    DS_DEV_ENV: 'development',
    DS_TEST_ENV: 'test',

    DS_ACTIVE: 'active',
    DS_INACTIVE: 'inactive',
    DS_BLOCKED: 'blocked',

    DS_SURVEY: 'survey',
    DS_PENDING: 'pending',
    DS_CORRECTION_REQUIRED: 'error',
    DS_DONE: 'success',
    DS_SETTINGS: 'setting',
    DS_ROLE_STUDENT: 'student',
    DS_ROLE_FACULTY: 'faculty',
    DS_ROLE_SUPER_ADMIN: 'super_admin',
    DS_SUPERADMINUSERNAME: '<EMAIL>',
    DS_SETTING_ID: '5ea702ad8c016c25c890682b',
    DS_MONGODB_ID_LENGTH: 24,

    // Strings
    DS_NAME_ALREADY_EXISTS: 'Name already exists',
    DS_NAME_OR_CODE_ALREADY_EXISTS: 'Name or code already exists',
    DS_NAME_OR_ROOM_NUMBER_ALREADY_EXISTS: 'Name or room number already exists',

    DS_ALREADY_EXISTS: 'Already exists',
    DS_INTERNAL_SERVER_ERROR: 'Internal server error',
    DS_DATA_RETRIEVED: 'Data retrieved',
    DS_NOT_FOUND: 'Not found', // 404 - file not found
    DS_NO_DATA_FOUND: 'No data found', // 200 - empty array
    DS_ADDED: 'Added successfully',
    DS_UPDATED: 'Updated successfully',
    DS_DELETED: 'Deleted successfully',
    DS_ADD_FAILED: 'Failed to add',
    DS_UPDATE_FAILED: 'Failed to update',
    DS_DELETE_FAILED: 'Failed to delete',
    DS_DUPLICATE_FOUND: 'Duplicate found',
    DS_NOTHING_TO_UPDATE: 'Nothing to update',
    DS_INVALID_ID: 'Invalid Id',
    DS_INVALID_TYPE_FIELD: 'Exam Type must be string',

    DS_ALL: 'All',

    DS_IMPORT_ALERT_MSG:
        'Any conflicts with the existing data like mismatch, duplicates will be removed during import.',

    // Permissions strings
    DS_CREATE: 'create',
    DS_READ: 'view',
    DS_UPDATE: 'update',
    DS_DELETE: 'delete',
    DS_ALLOWED: 'allowed',

    DS_INVIGILATOR_TYPE_1: 'invigilator1',
    DS_INVIGILATOR_TYPE_2: 'invigilator2',
    DS_INVIGILATOR_TYPE_3: 'invigilator3',

    DS_INSTITUTION_KEY: 'institution',
    DS_PROGRAM_KEY: 'program',
    DS_TERM_KEY: 'term',
    DS_YEAR_KEY: 'year',
    DS_LEVEL_KEY: 'level',
    DS_CURRICULUM_KEY: 'curriculum',
    DS_ROTATION_GROUP_KEY: 'rotationGroup',
    DS_COURSE_KEY: 'course',
    DS_MODULE_KEY: 'module',
    DS_ELECTIVE_KEY: 'elective',
    DS_CLO_KEY: 'clo',
    DS_SUBJECT_KEY: 'subject',
    DS_TOPIC_KEY: 'topic',
    DS_SLO_KEY: 'slo',

    // testcenter
    DS_REPEAT_SESSION: 'repeat_session',

    // signature
    DS_ADMIN_SIGNATURE: 'Dean',

    DS_INVIGILATORS_UNASSIGNED: 'invigilators_unassigned',
    DS_UNPUBLISHED_ASSESSMENTS: 'unpublished_assessments',
    DS_STUDENTS_UNFILLED: 'students_unfilled',

    // export user types
    DS_EXPORT_ALL_INVIGILATORS: 'allInvigilators',
    DS_EXPORT_INDIVIDUAL_INVIGILATORS: 'individualInvigilator',
    DS_EXPORT_ALL_STUDENTS: 'allStudents',

    DS_NOTIFICATION_NOT_SENT: 'notificationNotSent',

    // assessment status
    DS_STATUS_NOT_STARTED: 'Not Started',
    DS_GENERIC_TYPE: 'Generic Type',
    DS_VERSION: 'version',

    // exams
    DS_ON_GOING_EXAM: 'onGoingExam',
    DS_PREVIOUS_EXAM: 'previousExam',

    DS_SUBJECT_EXPERT_REVIEWER: 'subjectExpertReviewer',
    DS_MEDICAL_EDUCATIONIST: 'medicalEducationist',

    // Question bank setting
    DS_IMPORT_FILE_FORMART: 'importFileFormats',
    DS_EXPORT_FILE_FORMART: 'exportFileFormats',
    DS_PERMISSION_QUESTION_BANK_SETTINGS: 'permissions',

    // Item type
    DS_ASSESSMENT_ITEM_TYPE: 'assessment',
    DS_SURVEY_ITEM_TYPE: 'survey',

    DS_MULTIPLE_QUESTIONS_ITEM_TYPE: [EMQ, MQ, CQ, EQ],
    DS_MANDATORY_MULTIPLE_QUESTIONS_ITEM_TYPE: [EMQ, MQ, CQ],
    DS_ASSESSMENT_ITEM_TYPES: [MCQ, TF, SAQ, EMQ, MQ, CQ, EQ],

    // courses
    DS_COURSES: 'courses',

    DS_SHORT_CODE: 'shortCode',

    // Topic types
    DS_EXTRACT_SINGLE_ITEM: 'extractSingleItem',
    DS_EXTRACT_GROUP_OF_ITEMS: 'extractGroupOfItems',

    DS_WITHOUT_SECTION: 'without_section',
    DS_WITH_SECTION: 'with_section',

    DS_DIGI_ASSESS_WEB: 'DA',
    DS_DIGI_ASSESS_DESKTOP: 'DD',

    DS_ASSESSMENT_AUTHOR: 'assessmentAuthor',
    DS_ITEM_AUTHOR: 'itemAuthor',

    // Surveys setting
    DS_PROGRAM_SURVEYS: 'programSurveys',
    DS_GENERAL_SURVEYS: 'generalSurveys',

    // allowed file types
    DS_ITEM_IMAGE_FILE_TYPES: ['jpeg', 'jpg', 'png'],
    DS_ITEM_AUDIO_FILE_TYPES: ['mp3', 'mpeg', 'wav'],
    DS_ITEM_VIDEO_FILE_TYPES: ['mp4'],
    DS_ITEM_APPLICATION_FILE_TYPES: ['pdf'],
    DS_IDENTITY: 'identity',
    DS_ALIGNMENT: 'ALIGNMENT',
    DS_IMPACT: 'IMPACT',
    DS_CONTENT_MAP: 'CONTENT_MAP',

    DS_OPTIONAL: 'OPTIONAL',
    DS_REQUIRED: 'REQUIRED',

    AM: 'AM',
    PM: 'PM',
    SCHEDULE_TYPES: {
        SCHEDULE: 'schedule',
        REGULAR: 'regular',
        EVENT: 'event',
        SUPPORT_SESSION: 'support_session',
    },
    SCHEDULE_EVENT_TYPES: {
        GENERAL: 'general',
        EXAM: 'exam',
        TRAINING: 'training',
        FEEDBACK: 'feedback',
        COUNSELLING: 'counselling',
        LECTURE: 'lecture',
        TUTORIAL: 'tutorial',
        SEMINAR: 'seminar',
        INTERACTIVE_LECTURE: 'interactive_lecture',
        HOSPITAL_SESSION: 'hospital_session',
    },
    //clinical attendance
    OUTSIDE_ATTENDANCE: 'outside_attendance',

    ANNOUNCEMENT_SETTING: 'announcement_setting',
    ANNOUNCEMENT: 'announcement',
    ANNOUNCEMENTUSERSETTING: 'announcement_user_setting',
    SCHEDULE: 'schedule',
    // activities
    ACTIVITIES: 'activity',
    EXPIRED: 'expired',
    // activities status
    DRAFT: 'draft',
    NOT_DRAFT: 'not_draft',
    COMPLETED: 'completed',
    MISSED: 'missed',
    STARTED: 'started',
    NOT_STARTED: 'not_started',
    SCHEDULE_STATUS: {
        NOT_SCHEDULED: 'not scheduled',
        CANCELLED: 'cancelled',
        MISSED_TO_COMPLETE: 'missed to complete',
    },
    QUESTION_TYPE: [MCQ, TF, SAQ, EMQ, MQ, CQ, EQ, SCQ],
    DC_STAFF: 'staff',
    DC_STUDENT: 'student',

    // Question
    QUESTION: 'question',

    // allowed file types
    ITEM_IMAGE_FILE_TYPES: ['jpeg', 'jpg', 'png', 'gif', 'heic'],
    ITEM_AUDIO_FILE_TYPES: ['mp3', 'mpeg', 'wav', 'm4a', 'x-m4a'],
    ITEM_VIDEO_FILE_TYPES: ['mp4', 'mov', 'quicktime'],
    ITEM_APPLICATION_FILE_TYPES: ['pdf'],

    // Taxonomy
    TAXONOMY: 'taxonomy',
    ONGOING: 'ongoing',
    PRESENT: 'present',
    ABSENT: 'absent',
    LEAVE: 'leave',
    EXCLUDE: 'exclude',
    SESSION_MODE: {
        START: 'start',
        STOP: 'stop',
        JOIN: 'join',
        CLOSE: 'close',
        MANUAL: 'manual',
        AUTO: 'auto',
    },
    IOS: 'ios',
    ANDROID: 'android',
    WEB: 'web',
    APP_NOTIFICATIONS: 'app_notifications',
    SUPPORT_EVENT: 'support_event',
    SSO_PROVIDER: {
        TEAMS: 'teams',
        GOOGLE: 'google',
    },

    SUMMARY: 'summary',
    SLO: 'slo',
    CLO: 'clo',
    PLO: 'plo',
    DASHBOARD: {
        ACTIVITIES: 'activities',
        SESSIONS: 'sessions',
        COURSES: 'courses',
        DOCUMENTS: 'documents',
        RATINGS: 'ratings',
    },

    REPORT_ANALYTICS: {
        PROGRAM_LIST: 'program_list',
        PLO: 'plo',
        CREDIT_HOURS: 'credit_hours',
        NO_STUDENT: 'no_student',
        NO_STAFF: 'no_staff',
    },
    PROGRAM_REPORT_SETTINGS: 'program_report_setting',
    WARNING_MAIL: 'warning_mail',
    V1_PROGRAM_CREDIT_HOURS: `${APP_STATE.toString()}-v1_program_credit_hours`,
    V1_PROGRAM_COURSE_CREDIT_HOURS: `${APP_STATE.toString()}-v1_program_course_credit_hours`,
    V1_PROGRAM_COURSE_SCHEDULE_STAFF_SUBJECT: `${APP_STATE.toString()}-v1_program_course_schedule_staff_subject`,
    V1_PROGRAM_LEVEL_STUDENT_COUNT: `${APP_STATE.toString()}-v1_program_level_student_count`,
    V1_PROGRAM_LEVEL_STUDENTS: `${APP_STATE.toString()}-v1_program_level_students`,
    V1_PROGRAM_LEVEL_STAFFS: `${APP_STATE.toString()}-v1_program_level_staffs`,
    REDIS_FOLDERS: {
        USER_COURSES: 'userCourses',
        USER_INSTITUTION_CALENDAR: 'userInstitutionCalendar',
        LMS_REPORT: 'lmsReport',
        LMS_REPORT_DENIAL: 'lmsReport_criterial_management',
    },
    COURSE_COORDINATOR: 'Course Coordinator',
    REMOTE_PLATFORM: {
        ZOOM: 'zoom',
        TEAMS: 'teams',
    },
    TEAMS_LOGIN_URL: 'https://login.microsoftonline.com/',
    TEAMS_MEETING_OPTIONS:
        'https://graph.microsoft.com/v1.0/users?$select=displayName,givenName,postalCode,mail,id&$search="mail:',
    TEAMS_GET_API_URL: 'https://graph.microsoft.com/beta/',
    TEAMS_V1_API_URL: 'https://graph.microsoft.com/v1.0/',
    COLLEGE_PROGRAM_TYPE: {
        MEDICAL: 'Medical',
        ENGINEERING: 'Engineering',
    },
    SCHEDULE_ATTENDANCE: 'schedule_attendance',
    SCHEDULE_ANOMALIES: 'schedule_anomalies',
    USER_DEVICE_DETAIL: 'user_device_detail',
    YEAR_LEVEL_AUTHOR: 'yearLevelAuthor',
    USER_REGISTERED_DETAIL: 'user_registered_detail',
    FACE_REGISTER: 'face_register',
    SCHEDULE_MULTI_DEVICE_ATTENDANCE: 'schedule_multi_device_attendance',
    SCHEDULE_MULTI_ATTENDANCE: 'schedule_multi_attendance',
    EXTERNAL_DATA_SYNC: 'externalDataSync',
    RETAKE: 'retake',
    RETAKE_ALL: 'retake_all',
    RETAKE_ABSENT: 'retake_absent',
    BUZZER: 'buzzer',
    SURPRISE_QUIZ: 'surprise_quiz',
    RUNNING: 'running',
    ATTENDANCE_DEFAULT_END_TIME: 5,
    SCHEDULE_ATTENDANCE_REASONS: 'schedule_attendance_reasons',
    EMAIL_CONTENT: 'email_content',
    GLOBAL_SESSION_SETTINGS: 'global_session_settings',
    GLOBAL_SESSION_TARDIS: 'global_session_tardis',
    DISCUSSION_TOPICS: 'discussion_topics',
    DISCUSSION_REPLIES: 'discussion_replies',
    DISCUSSION_UNREAD: 'discussion_unread',
    SESSION_STATUS_MANAGEMENT: 'session_status_management',
    LATE_CONFIG_MANAGEMENT: 'late_config_management',
    STUDENT_WARNING_RECORD: 'student_warning_records',
    LATE_CONFIG: 'late config',
    LATE_UPLOADED: 'late uploaded',
    NOT_UPLOADED: 'not uploaded',
    UPLOADED: 'uploaded',
    ATTENDANCE_CONFIG: 'attendance config',
    STUDENT_WARNING_REDIS: 'studentWarnings',
    USER_MODULE_PERMISSION: 'user_module_permission',
    TAG_MASTER: 'tag_master',
    GROUPS: 'groups',
    FAMILIES: 'families',
    DIGI_SURVEY_BANK: 'digi_survey_Bank',
    START: 'start',
    END: 'end',
    TEMPLATE: 'template',
    ALL_PROGRAM: 'all_program',
    ALL_COURSE: 'all_course',
    SPECIFIC_PROGRAM: 'specific_program',
    SPECIFIC_COURSE: 'specific_course',
    UPCOMING: 'upComing',
    INPROGRESS: 'inProgress',
    CLOSED: 'closed',
    EDIT_DURATION: 'editDuration',
    MALESTAFF: 'maleStaff',
    FEMALESTAFF: 'femaleStaff',
    MALESTUDENT: 'maleStudent',
    FEMALESTUDENT: 'femaleStudent',
    PUBLIC_USER: 'publicUser',
    INSTITUTION_CALENDAR_KEY: 'institutionCalendar',
    YEAR_LEVEL_KEY: 'yearLevel',
    ROTATION: {
        YES: 'yes',
        NO: 'no',
    },
    SURVEY_QUESTION_TYPE: {
        RATING: 'rating',
    },
    PUBLISH_TIMING: {
        LATER: 'later',
        NOW: 'now',
    },
    PUBLISH: 'publish',
    EXPIRE: 'expire',
    CHAT_GROUP: 'chat_group',
    CHAT_MESSAGE: 'chat_message',
    GROUP_TYPE: {
        STUDENT_GROUP: 'student_group',
        COURSE_GROUP: 'course_group',
    },
    COURSE_STUDENT: 'course_students',
    INITIATED: 'initiated',
    DISCIPLINARY_REMARKS: 'disciplinary_remarks',
    DISCIPLINARY_REMARKS_TYPE: {
        SCHEDULE_LEVEL: 'schedule_level',
        INSTITUTION_LEVEL: 'institution_level',
    },
    DIGI_SURVEY_RESPONSE: 'digiSurveyResponse',
    DC_STAFF_SURVEY: 'staffSurvey',
    PERSONAL: 'personal',
    FIRST: 'first',
    LAST: 'last',
    FAMILY: 'family',
    MIDDLE: 'middle',
    RATING_SCALE_LABEL: {
        STANDARD: 'standard',
        DYNAMIC: 'dynamic',
    },
    DIGI_USER_SURVEY_TYPE_LIST: 'digi_survey_type_list',
    MEDIA: 'media',
    DOCS: 'docs',
    LINKS: 'links',
    SURVEY_USERS: 'SURVEY_USERS',
    SURVEY_USER_LIST_EXPIRE_TIME: 5 * 24 * 60 * 60, //5 days
    SURVEY_TAG_REPORT_EXPIRE_TIME: 1 * 24 * 60 * 60, //1day
    SORTING_ORDER: {
        ASC: 'asc',
        DESC: 'desc',
    },
    SENTIMENT_ANALYSIS: {
        POSITIVE: 'positive',
        NEGATIVE: 'negative',
        NEUTRAL: 'neutral',
    },
    SURVEY_REPORT_SATISFACTION_LEVEL: {
        LOW_SATISFACTION: 33.34,
        MODERATE_SATISFACTION: 66.67,
        HIGH_SATISFACTION: 100,
    },
    SURVEY_SATISFACTION_LEVEL: {
        HIGH_SATISFACTION_KEY: 'highSatisfaction',
        LOW_SATISFACTION_KEY: 'lowSatisfaction',
        MODERATE_SATISFACTION_KEY: 'moderateSatisfaction',
    },
    DIGI_SURVEY_OUTCOME_REPORT_RESPONSE_SETTINGS: 'digiSurveyOutComeReportResponseSettings',
    USER_ACTIVITY_LOG: 'userActivityLog',
    CHANNEL: 'channel',
    SCHEDULE_STAFF_DEFAULT_ROLE: 'Schedule Staff',
    LEARNING_OUTCOME_SECTION: 'Learning outcome section',
    DIGI_SURVEY_TAG_REPORTS: 'digiSurveyTagReport',
    USER_MODULE_PERMISSION_CATEGORY_KEYS: {
        TAGS: 'tags',
        GROUPS: 'groups',
        FAMILIES: 'families',
    },
    TAG_REPORT_REFRESH_STATE: {
        FRESH: 'fresh',
        REGULAR: 'regular',
    },
    TAG_REPORTS: 'TAG_REPORTS',
    DIGI_SURVEY_EXTERNAL_USERS: 'digiSurveyExternalUsers',
    MAIL_DURATION: {
        DAILY: 'daily',
        WEEKLY: 'weekly',
        MONTHLY: 'monthly',
    },
    TAG_REPORT_DEFAULT_RATING_VALUE: 5,
    SURVEY_CREATOR_ROLE_NAME: 'Survey Template Creator',
    CORRELATION_MATRIX_FORMULA: {
        PEARSON_CORRELATION_COEFFICIENT: 'pcc',
        SPEARMAN_CORRELATION_COEFFICIENT: 'scc',
    },
    LEARNING_OUTCOME_LEVEL: {
        PLO: 'PLO',
        CLO: 'CLO',
    },
    Q360: {
        CATEGORY: 'category',
        DELETE_CATEGORY: 'deleteCategory',
        GRAPH: 'graph',
        PROGRAM: 'program',
        COURSE: 'course',
        QA_DASHBOARD: 'QA Dashboard',
        CATEGORY_LEVEL: 'Category Level',
        PROGRAM_LEVEL: 'Program Level',
        INSTITUTION: 'institution',
        INCORPORATED: 'Incorporated',
        INSTITUTION_LEVEL: 'Institution Level',
    },
    GET_ALL: 'getAll',
    // Encryption From handledBy
    SERVER: 'server',
    CLIENT_END: 'client_end',

    // push notification service
    HIGH: 'high',
    LEAVE_APPLICATION_CRITERIA: {
        SESSION: 'sessionWise',
        DAY: 'dayWise',
    },
    LMS_WARNING_CONFIG: 'lms_warning_config',

    YEAR_LEVEL_AUTHOR_TYPE_MODULE: {
        YEAR: 'year',
        LEVEL: 'level',
        STUDENT_GROUP: 'studentGroup',
        SCHEDULE: 'schedule',
    },
    YEAR_LEVEL_AUTHOR_ROLE: 'Year Level Author',
    LEVEL_BASED_ACCESS: 'levelBasedAccess',
    LEVEL_AUTHOR: 'Level Author',
    COURSE_HANDOUT: 'courseHandout',
    MERGED: 'merged',
    LATE: 'late',
    EARLY: 'early',
    NOTSTARTED: 'notStarted',
    FORM_INITIATOR_LABEL: 'formInitiator',
    Q360_LABEL: 'q360',
    SESSION_RATING: 'session_rating',
    COURSE_DELIVERY_GROUP_ADMIN: 'course_delivery_group_admin',
});

module.exports = constants;
