const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    courseCompleteReport,
    getCalendarList,
    getCourseList,
    getProgramList,
    getCourseScheduleList,
    courseCompleteReportWithDetails,
} = require('./courseReport.controller');

router.get('/courseCompleteReport', catchAsync(courseCompleteReport));
router.post('/courseCompleteReport', catchAsync(courseCompleteReportWithDetails));
//user course list
router.get('/calendarList', catchAsync(getCalendarList));
router.get('/programList', catchAsync(getProgramList));
router.get('/courseList', catchAsync(getCourseList));
router.get('/courseScheduleList', catchAsync(getCourseScheduleList));
module.exports = router;
