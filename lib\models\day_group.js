let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let day_group = new Schema({
    name: {
        type: String,
        required: true
    },
    days: {
        type: String,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });

// day_groupSchema.index({ name: 1, isDeleted: 1 }, { unique: true });
// module.exports = mongoose.model('conty', day_groupSchema);
module.exports = mongoose.model(constant.DAY_GROUP, day_group);