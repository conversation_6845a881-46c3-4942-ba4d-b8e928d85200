const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { USER, DIGI_CHAT } = require('../utility/constants');

const digiChatSchema = new Schema(
    {
        channelId: {
            type: String,
            required: true,
        },
        members: [
            {
                id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                isBlocked: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(DIGI_CHAT, digiChatSchema);
