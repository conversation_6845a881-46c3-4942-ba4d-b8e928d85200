const { formatHourMinuteOneDigitToTwoDigitNumber } = require('../../utility/time');
const { program } = require('../program-input/program-input.format');

function transformSession(session) {
    return formatHourMinuteOneDigitToTwoDigitNumber(session);
}

function transformWorkingDays(workingDays) {
    if (!Object.keys(workingDays).length) return [];

    return workingDays.map((workingDay) => {
        return {
            _id: workingDay._id,
            name: workingDay.name,
            session: transformSession(workingDay.session),
            isActive: workingDay.isActive,
        };
    });
}

function transformEmailConfig(configs) {
    if (!Object.keys(configs).length) return [];
    return configs.map((configEntry) => {
        return {
            _id: configEntry._id,
            displayName: configEntry.displayName,
            email: configEntry.email,
            smtpClient: configEntry.smtpClient,
            portNumber: configEntry.portNumber,
            ttl_ssl: configEntry.ttl_ssl,
            server: configEntry.server,
        };
    });
}

function transformBreaks(breaks) {
    if (!Object.keys(breaks).length) return [];

    return breaks.map((breakEntry) => {
        return {
            _id: breakEntry._id,
            name: breakEntry.name,
            session: transformSession(breakEntry.session),
            workingDays: breakEntry.workingDays,
        };
    });
}

function transformProgramSpecificBreaks(breaks) {
    if (!Object.keys(breaks).length) return [];

    return breaks.map((breakEntry) => {
        return {
            _id: breakEntry._id,
            name: breakEntry.name,
            session: transformSession(breakEntry.session),
            workingDays: breakEntry.workingDays,
            isParentConfig: breakEntry.isParentConfig,
        };
    });
}

function transformLanguage(languages) {
    if (!Object.keys(languages).length) return [];

    return languages.map((language) => {
        return {
            _id: language._id,
            name: language.name,
            code: language.code,
            isDefault: language.isDefault,
        };
    });
}

function transformGlobalConfiguration(setting) {
    if (!Object.keys(setting).length) return undefined;

    const {
        globalConfiguration: {
            basicDetails,
            departmentSubject,
            staffUserManagement,
            departmentManagement,
        },
    } = setting;
    const obj = {
        _id: setting._id,
        timeZone: basicDetails.timeZone,
        session: transformSession(basicDetails.session),
        isIndependentHours: basicDetails.isIndependentHours,
        workingDays: transformWorkingDays(basicDetails.workingDays),
        breaks: transformBreaks(basicDetails.breaks),
        emailIdConfiguration: basicDetails.emailIdConfiguration,
        eventType: basicDetails.eventType,
        language: transformLanguage(basicDetails.language),
        isGenderSegregation: basicDetails.isGenderSegregation,
        privacySettings: basicDetails.privacySettings,
        departmentSubject,
        vaccineConfiguration: basicDetails.vaccineConfiguration,
        departmentHierarchyStructure: basicDetails.departmentHierarchyStructure,
        departmentManagement,
    };
    return obj;
}

function transformGlobalConfigurationForProgram(setting) {
    if (!Object.keys(setting).length) return undefined;
    const transformBasicDetails = transformGlobalConfiguration(setting);
    setting.globalConfiguration.basicDetails = transformBasicDetails;
    return setting;
}

function transformLabels(labels) {
    if (!Object.keys(labels).length) return [];

    return labels.map((labelEntry) => {
        return {
            name: labelEntry.name,
            defaultInput: labelEntry.defaultInput,
            translatedInput: labelEntry.translatedInput,
        };
    });
}

function transformCreditHour(creditHours) {
    if (!Object.keys(creditHours).length) return [];

    return creditHours.map((creditHour) => {
        return {
            _id: creditHour._id,
            mode: creditHour.mode,
            isDefault: creditHour.isDefault,
        };
    });
}

function transformCurriculumNaming(curriculumNamings) {
    if (!Object.keys(curriculumNamings).length) return [];

    return curriculumNamings.map((curriculumNaming) => {
        return {
            _id: curriculumNaming._id,
            mode: curriculumNaming.mode,
            isDefault: curriculumNaming.isDefault,
        };
    });
}

function transformProgramType(ProgramTypes) {
    if (!Object.keys(ProgramTypes).length) return [];

    return ProgramTypes.map((ProgramType) => {
        return {
            _id: ProgramType._id,
            name: ProgramType.name,
            code: ProgramType.code,
        };
    });
}

function transformLabelConfiguration(labelConfiguration) {
    if (!Object.keys(labelConfiguration).length) return [];

    return labelConfiguration.map((labelEntry) => {
        return {
            language: labelEntry.language,
            labels: transformLabels(labelEntry.labels),
        };
    });
}

function transformProgramInput(setting) {
    if (!Object.keys(setting).length) return undefined;
    const {
        globalConfiguration: { programInput },
    } = setting;

    const obj = {
        _id: setting._id,
        labelConfiguration: transformLabelConfiguration(programInput.labelConfiguration),
        programType: transformProgramType(programInput.programType),
        curriculumNaming: transformCurriculumNaming(programInput.curriculumNaming),
        creditHours: transformCreditHour(programInput.creditHours),
    };

    return obj;
}

function transformProgramSpecificBasicDetails(programSettings) {
    const basicDetails = {
        timeZone: programSettings.settings.basicDetails.timeZone,
        isGenderSegregation: programSettings.settings.basicDetails.isGenderSegregation,
        breaks: transformProgramSpecificBreaks(programSettings.settings.basicDetails.breaks),
        eventType: programSettings.settings.basicDetails.eventType,
    };
    const transformedSettings = {
        _id: programSettings._id,
        settings: {
            basicDetails,
            programInput: programSettings.settings.programInput,
        },
    };
    return transformedSettings;
}

module.exports = {
    formatGlobalConfiguration: (setting) => transformGlobalConfiguration(setting),
    formatGlobalConfigurationForProgram: (setting) =>
        transformGlobalConfigurationForProgram(setting),
    formatProgramInput: (setting) => transformProgramInput(setting),
    transformProgramSpecificBasicDetails,
};
