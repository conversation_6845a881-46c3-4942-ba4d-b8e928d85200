const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { FORM, STUDENT_RESPONSE } = require('../../common/utils/constants');
const { NOT_STARTED } = require('../../common/utils/enums');
const { rubric } = require('../rubric/rubric.schema');
const { pagesSchema } = require('../form/form.schema');
const { roleSchema } = require('../portfolio/portfolio.schema');

const NameSchema = {
    first: String,
    middle: String,
    last: String,
    family: String,
};

const UserSchema = {
    _id: false,
    userId: ObjectId,
    name: NameSchema,
    email: String,
    employeeId: String,
    gender: String,
};

const PrepareAndPublishSchema = {
    ...UserSchema,
    formId: ObjectId,
    roleId: ObjectId,
    pages: pagesSchema,
};

const schema = new Schema(
    {
        formId: { type: ObjectId, ref: FORM },
        student: {
            _id: { type: ObjectId, index: true },
            email: { type: String, required: true },
            name: {
                first: { type: String, trim: true },
                middle: { type: String, trim: true },
                last: { type: String, trim: true },
            },
            academicNo: { type: String, trim: true },
        },
        title: { type: String, trim: true },
        type: {
            code: { type: String, trim: true },
            name: { type: String, trim: true },
            _id: { type: ObjectId },
        },
        pages: pagesSchema,
        isDeleted: { type: Boolean, default: false },
        status: { type: String, default: NOT_STARTED },
        evaluationStatus: { type: String, default: NOT_STARTED },
        totalMarks: { type: Number },
        awardedMarks: { type: Number },
        portfolioId: { type: ObjectId },
        componentId: { type: ObjectId },
        childrenId: { type: ObjectId },
        parentPortfolioId: { type: ObjectId },
        formTimestamps: {
            firstStartedAt: { type: Date },
            submittedAt: { type: Date },
            lastUpdated: { type: Date },
        },
        roles: [roleSchema],
        evaluators: [
            {
                name: {
                    first: { type: String },
                    middle: { type: String },
                    last: { type: String },
                },
                userId: { type: ObjectId },
                email: { type: String },
                employeeId: { type: String },
                marks: { type: Number },
                rubrics: [rubric],
                awardedMarks: { type: Number },
                globalRubrics: [rubric],
                globalRubricTotalPoints: { type: Number },
                globalRubricAwardedPoints: { type: Number },
            },
        ],
        scheduleId: { type: ObjectId },
        reviews: [
            {
                name: {
                    first: { type: String },
                    middle: { type: String },
                    last: { type: String },
                    family: { type: String },
                },
                userId: { type: ObjectId },
                text: { type: String },
                createdAt: { type: Date },
            },
        ],
        approvalStatus: { type: String },
        globalRubricTotalPoints: { type: Number },
        globalRubricAwardedPoints: { type: Number },
        statusHistory: [
            {
                status: { type: String },
                createdAt: { type: Date },
                userId: { type: ObjectId },
            },
        ],
        evaluatorImages: [
            {
                email: { type: String },
                attachment: {
                    key: { type: String, trim: true },
                    bucket: { type: String, trim: true },
                    type: { type: String, trim: true },
                },
            },
        ],
        prepareAndPublish: [PrepareAndPublishSchema],
    },
    { timestamps: true },
);

module.exports = model(STUDENT_RESPONSE, schema);
