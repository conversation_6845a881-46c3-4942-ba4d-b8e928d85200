let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let digi_session_delivery_types = new Schema({
    session_name: {
        type: String,
        required: true
    },
    session_symbol: {
        type: String,
        required: true
    },
    ct_hours_per_credit_hour: {
        type: String,
        required: true
    },
    session_duration: {
        type: Number,
        required: true
    },
    delivery_types: [{
        delivery_name: {
            type: String,
            required: true
        },
        delivery_symbol: {
            type: String,
            required: true
        },
        delivery_duration: {
            type: Number,
            required: true
        },
        isDeleted: {
            type: Boolean,
            default: false
        },
        isActive: {
            type: Boolean,
            default: true
        }
    }],
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }

}, { timestamps: true });
module.exports = mongoose.model(constant.DIGI_SESSION_DELIVERY_TYPES, digi_session_delivery_types);