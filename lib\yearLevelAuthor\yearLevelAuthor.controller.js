const { logger } = require('../utility/util_keys');
const {
    getYearLevelList,
    programList,
    programYearLevelList,
} = require('./yearLevelAuthor.service');
const { convertToMongoObjectId } = require('../utility/common');
const yearLevelAuthorSchema = require('../models/yearLevelAuthor');
const { YEAR_LEVEL_AUTHOR_TYPE_MODULE, YEAR_LEVEL_AUTHOR_ROLE } = require('../utility/constants');
const { assignRoleToUsers, getActiveInstitutionCalendars } = require('../utility/utility.service');
const { getUserRoleProgramListWithYearLevel } = require('../utility/utility.service');

const getProgramYearLevelList = async ({ query = {} }) => {
    try {
        const { programId } = query;
        const queryParsing = {
            programId,
        };
        logger.info(queryParsing, 'studentGroupMaster -> getProgramYearLevelList -> Start');
        const programCourseDatas = await getYearLevelList(queryParsing);
        logger.info(queryParsing, 'studentGroupMaster -> getProgramYearLevelList -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programCourseDatas };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'studentGroupMaster -> getProgramYearLevelList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const upsertYearLevelAuthor = async ({ body = {}, headers = {} }) => {
    try {
        const { programId, curriculumId, yearName, levelName, users, authorType /* termName */ } =
            body;
        const { _institution_id } = headers;
        logger.info(body, 'yearLevelAuthor -> upsertYearLevelAuthor -> Start');

        // Assign Year Level Author role to users
        await assignRoleToUsers({
            _institution_id,
            userIds: users.map((userElement) => userElement.userId),
            roleName: YEAR_LEVEL_AUTHOR_ROLE,
        });

        const existingRecord = await yearLevelAuthorSchema
            .findOne(
                {
                    programId: convertToMongoObjectId(programId),
                    curriculumId: convertToMongoObjectId(curriculumId),
                    yearName,
                    levelName:
                        authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.LEVEL ? levelName : null,
                    authorType,
                    // termName,
                },
                { users: 1 },
            )
            .lean();

        let result;
        if (existingRecord) {
            const updatedUsers = users.map((userElement) => {
                const existingUser = existingRecord.users.find(
                    (existingUserElement) =>
                        String(existingUserElement.userId) === String(userElement.userId),
                );
                if (existingUser) {
                    existingUser.modules = userElement.modules;
                    return existingUser;
                }
                return {
                    userId: convertToMongoObjectId(userElement.userId),
                    modules: userElement.modules,
                };
            });

            result = await yearLevelAuthorSchema.findByIdAndUpdate(
                existingRecord._id,
                { users: updatedUsers },
                { new: true },
            );
        } else {
            result = await yearLevelAuthorSchema.create({
                programId: convertToMongoObjectId(programId),
                curriculumId: convertToMongoObjectId(curriculumId),
                yearName,
                levelName: authorType === YEAR_LEVEL_AUTHOR_TYPE_MODULE.LEVEL ? levelName : null,
                authorType,
                // termName,
                users: users.map((user) => ({
                    userId: convertToMongoObjectId(user.userId),
                    modules: user.modules,
                })),
            });
        }

        logger.info({ result }, 'yearLevelAuthor -> upsertYearLevelAuthor -> End');
        return {
            statusCode: existingRecord ? 200 : 201,
            message: existingRecord ? 'UPDATED_SUCCESSFULLY' : 'CREATED_SUCCESSFULLY',
        };
    } catch (error) {
        logger.error({ error }, 'yearLevelAuthor -> upsertYearLevelAuthor -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userAuthorProgramList = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        let { institutionCalendarId } = query;
        const { module, withDetail = false } = query;

        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        // Convert withDetail to boolean
        const withDetailBoolean = withDetail === 'true' || withDetail === true;

        const queryParsing = {
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            module,
            withDetail: withDetailBoolean,
        };
        logger.info(queryParsing, 'yearLevelAuthor -> userAuthorProgramList -> Start');
        const { userProgramIds } = await getUserRoleProgramListWithYearLevel(queryParsing);
        logger.info(queryParsing, 'yearLevelAuthor -> userAuthorProgramList -> End');

        const programDatas = await programList({
            programIds: userProgramIds,
        });

        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programDatas };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'yearLevelAuthor -> userAuthorProgramList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userAuthorProgramYearLevelList = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { module, withDetail = true, institutionCalendarId, programId, term } = query;

        const queryParsing = {
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
            module,
            withDetail,
        };
        logger.info(queryParsing, 'yearLevelAuthor -> userAuthorProgramYearLevelList -> Start');
        // const { yearLevelAuthorDetails } = await getUserRoleProgramListWithYearLevel(queryParsing);
        const programYearLevelData = await programYearLevelList(queryParsing);
        logger.info(queryParsing, 'yearLevelAuthor -> userAuthorProgramYearLevelList -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programYearLevelData };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'yearLevelAuthor -> userAuthorProgramYearLevelList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getProgramYearLevelList,
    upsertYearLevelAuthor,
    userAuthorProgramList,
    userAuthorProgramYearLevelList,
};
