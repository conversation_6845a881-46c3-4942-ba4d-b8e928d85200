// const middleware = exports;
const Joi = require('joi');
const { comResponse } = require('../../utility/common');
const { CREDIT_HOURS_MODE } = require('../../utility/constants');

const sessionTypesAddSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                programName: Joi.string()
                    .min(2)
                    .max(100)
                    .required()
                    .error(() => {
                        return 'PROGRAM_NAME_REQUIRED';
                    }),
                sessionName: Joi.string()
                    .min(2)
                    .max(100)
                    .required()
                    .error(() => {
                        return 'SESSION_NAME_REQUIRED';
                    }),
                sessionSymbol: Joi.string()
                    .min(1)
                    .max(100)
                    .required()
                    .error(() => {
                        return 'SESSION_SYMBOL_REQUIRED';
                    }),
                contactHoursOrPeriodOfWeek: Joi.number().error(() => {
                    return 'CONTACT_HOURS_OR_PREIOD_OF_WEEK';
                }),
                sessionOrPeriodDuration: Joi.number()
                    .allow('')
                    .optional()
                    .error(() => {
                        return 'SESSIONOR_PERIOD_DURATION_REQUIRED';
                    }),
                creditHoursMode: Joi.string()
                    .valid(CREDIT_HOURS_MODE.STANDARD, CREDIT_HOURS_MODE.DYNAMIC)
                    .required()
                    .error(() => {
                        return 'CREDIT_HOURS_SHOULD_BE_STANDARD_DYNAMIC';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const addIndependentCourseSessionSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                sessionName: Joi.string()
                    .min(2)
                    .max(100)
                    .required()
                    .error(() => {
                        return 'SESSION_NAME_REQUIRED';
                    }),
                sessionSymbol: Joi.string()
                    .min(1)
                    .max(100)
                    .required()
                    .error(() => {
                        return 'SESSION_SYMBOL_REQUIRED';
                    }),
                contactHoursOrPeriodOfWeek: Joi.number().error(() => {
                    return 'CONTACT_HOURS_OR_PREIOD_OF_WEEK';
                }),
                sessionOrPeriodDuration: Joi.number()
                    .allow('')
                    .optional()
                    .error(() => {
                        return 'SESSIONOR_PERIOD_DURATION_REQUIRED';
                    }),
                creditHoursMode: Joi.string()
                    .valid(CREDIT_HOURS_MODE.STANDARD, CREDIT_HOURS_MODE.DYNAMIC)
                    .required()
                    .error(() => {
                        return 'CREDIT_HOURS_SHOULD_BE_STANDARD_DYNAMIC';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const deliveryTypesAddSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            deliveryName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_NAME_REQUIRED';
                }),
            deliverySymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_SYMBOL_REQUIRED';
                }),
            deliveryDuration: Joi.number().error(() => {
                return 'DELIVERY_DURATION_REQUIRED';
            }),
        }),

        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const independentCourseDeliveryTypesAddSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            deliveryName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_NAME_REQUIRED';
                }),
            deliverySymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_SYMBOL_REQUIRED';
                }),
            deliveryDuration: Joi.number().error(() => {
                return 'DELIVERY_DURATION_REQUIRED';
            }),
        }),

        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const sessionTypesUpdateSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            sessionName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'SESSION_NAME_REQUIRED';
                }),
            sessionSymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'SESSION_SYMBOL_REQUIRED';
                }),
            contactHoursOrPeriodOfWeek: Joi.number().error(() => {
                return 'CONTACT_HOURS_OR_PREIOD_OF_WEEK';
            }),
            sessionOrPeriodDuration: Joi.number()
                .allow('')
                .optional()
                .error(() => {
                    return SESSIONOR_PERIOD_DURATION_REQUIRED;
                }),
            creditHoursMode: Joi.string()
                .valid(CREDIT_HOURS_MODE.STANDARD, CREDIT_HOURS_MODE.DYNAMIC)
                .required()
                .error(() => {
                    return 'CREDIT_HOURS_SHOULD_BE_STANDARD_DYNAMIC';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const updateIndependentCourseSessionSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            sessionName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'SESSION_NAME_REQUIRED';
                }),
            sessionSymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'SESSION_SYMBOL_REQUIRED';
                }),
            contactHoursOrPeriodOfWeek: Joi.number().error(() => {
                return 'CONTACT_HOURS_OR_PREIOD_OF_WEEK';
            }),
            sessionOrPeriodDuration: Joi.number()
                .allow('')
                .optional()
                .error(() => {
                    return SESSIONOR_PERIOD_DURATION_REQUIRED;
                }),
            creditHoursMode: Joi.string()
                .valid(CREDIT_HOURS_MODE.STANDARD, CREDIT_HOURS_MODE.DYNAMIC)
                .required()
                .error(() => {
                    return 'CREDIT_HOURS_SHOULD_BE_STANDARD_DYNAMIC';
                }),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const deliveryTypesUpdateSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            deliveryName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_NAME_REQUIRED';
                }),
            deliverySymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_SYMBOL_REQUIRED';
                }),
            deliveryDuration: Joi.number().error(() => {
                return 'DELIVERY_DURATION_REQUIRED';
            }),
        }),

        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
                deliveryTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'DELIVERY_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const independentDeliveryTypesUpdateSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            deliveryName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_NAME_REQUIRED';
                }),
            deliverySymbol: Joi.string()
                .min(1)
                .max(100)
                .required()
                .error(() => {
                    return 'DELIVERY_SYMBOL_REQUIRED';
                }),
            deliveryDuration: Joi.number().error(() => {
                return 'DELIVERY_DURATION_REQUIRED';
            }),
        }),

        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
                deliveryTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'DELIVERY_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const sessionTypesDeleteSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);
const deliveryTypesDeleteSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
                deliveryTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'DELIVERY_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const sessionTypesGetSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const independentSessionTypesGetSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

const deleteIndependentSessionTypeValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                sessionTypeId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'SESSION_ID_REQUIRED';
                    }),
            })

            .unknown(true),
    })
    .unknown(true);

module.exports = {
    sessionTypesAddSchema,
    deliveryTypesAddSchema,
    sessionTypesUpdateSchema,
    deliveryTypesUpdateSchema,
    sessionTypesDeleteSchema,
    deliveryTypesDeleteSchema,
    sessionTypesGetSchema,
    addIndependentCourseSessionSchema,
    updateIndependentCourseSessionSchema,
    independentDeliveryTypesUpdateSchema,
    independentCourseDeliveryTypesAddSchema,
    independentSessionTypesGetSchema,
    deleteIndependentSessionTypeValidator,
};
