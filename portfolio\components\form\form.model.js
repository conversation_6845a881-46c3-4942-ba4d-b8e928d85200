const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { FORM } = require('../../common/utils/constants');
const constant = require('../../../lib/utility/constants');
const { pagesSchema } = require('./form.schema');

const schema = new Schema(
    {
        title: { type: String, trim: true },
        type: {
            code: { type: String, trim: true },
            name: { type: String, trim: true },
            _id: { type: ObjectId },
        },
        description: { type: String },
        pages: pagesSchema,
        isDeleted: { type: Boolean, default: false },
        status: { type: String },
        publishedDate: { type: Date },
        createdBy: {
            id: { type: ObjectId, ref: constant.BY_USER },
            name: {
                first: { type: String, trim: true },
                middle: { type: String, trim: true },
                last: { type: String, trim: true },
                family: { type: String, trim: true },
            },
        },
        isTemplate: { type: Boolean, default: false },
        isClone: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(FORM, schema);
