const StreamChat = require('stream-chat').StreamChat;
require('dotenv').config();
const { localLogger } = require('../../utility/locallogger');
const CourseSchedules = require('../../models/course_schedule');
const api_key = process.env.STREAM_API_KEY;
const api_secret = process.env.STREAM_API_SECRET;
const ioServerClient = StreamChat.getInstance(api_key, api_secret);
const { convertToMongoObjectId } = require('../../utility/common');
const DigiChat = require('../../models/digi_chat');
const User = require('../../models/user');

const createChannel = async (params, others = null) => {
    try {
        const result = { status: true, message: null, data: null };
        const { id, created_by_id, type, channelName, imageUrl, membersId, user_type } = params;
        if (!created_by_id || !type) {
            result.status = false;
            result.message = 'type & created_by_id required';
            return result;
        }
        const channel = await ioServerClient.channel(type, id, { created_by_id });
        await channel.create();
        localLogger.info(`await channel create() ${JSON.stringify(channel.data.name)}`);
        await channel.update({ name: channelName, image: imageUrl, ...others });
        if (membersId) await channel.addMembers(membersId);
        if (user_type === 'staff') await channel.addModerators([created_by_id]);
        result.message = 'successfully created';
        return result;
    } catch (error) {
        localLogger.info(`createChannel ${error.message}`);
        throw error;
    }
};

const addMembersToChannel = async (id, memberIds, channelName = null, others = null) => {
    try {
        const chat = await DigiChat.findOne({ channelId: id, 'members.id': memberIds[0] });
        if (chat) return;
        const filter = { type: 'team', id: { $in: [id] } };
        const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
        channelsRes.forEach(async (channel) => {
            await channel.addMembers(memberIds);
            localLogger.info(`await channel create() data ${JSON.stringify(channel.data)}`);
            localLogger.info(
                `await channel create() state ${JSON.stringify(channel.state.members)}`,
            );
        });

        if (channelName || others) {
            const channel = await ioServerClient.channel('team', id, {});
            await channel.update({
                name: channelName || channel.data.name,
                ...others,
            });
        }
    } catch (error) {
        localLogger.info(`addMemberToChannel catch ${error.message}`);
        throw error;
    }
};

const getMembersOfChannel = async (id) => {
    const filter = { type: 'team', id: { $in: [id] } };
    const channelsRes = await ioServerClient.queryChannels(filter, {}, { state: true });
    let members;
    channelsRes.forEach((channel) => {
        members = channel.state.members;
    });

    return members;
};
const getStudentSubjects = async (
    _institution_calendar_id,
    _course_id,
    year_no,
    level_no,
    term,
    _program_id,
    rotation,
    rotation_count,
    staffs,
    group_id,
) => {
    const staffIds = staffs.map((staff) => convertToMongoObjectId(staff));
    const query = {
        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        _course_id: convertToMongoObjectId(_course_id),
        year_no,
        level_no,
        term,
        _program_id: convertToMongoObjectId(_program_id),
        rotation,
        'student_groups.group_id': convertToMongoObjectId(group_id),
        'staffs._staff_id': { $in: staffIds },
    };
    if (rotation_count) query.rotation_count = rotation_count;

    const project = { subjects: 1, staffs: 1 };
    const courseSchedules = await CourseSchedules.find(query, project);
    const getSubjects = [];
    for (const staffId of staffs) {
        const csSubjects = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find((staff) => staff._staff_id.toString() === staffId),
        );
        const staffSubjects = [];
        for (const csSubject of csSubjects) {
            for (const subject of csSubject.subjects) {
                if (!staffSubjects.find((subjectEntry) => subjectEntry === subject.subject_name))
                    staffSubjects.push(subject.subject_name);
            }
        }
        getSubjects.push({ userId: staffId, subjects: staffSubjects });
    }
    return getSubjects;
};

const ioRegister = async (userId, role = 'user') => {
    let user = await User.findOne({ _id: userId });
    const { name, email, user_type, user_id, gender, ioToken } = user;
    if (ioToken) return user;
    const token = await ioServerClient.createToken(userId.toString());
    await ioServerClient.upsertUsers([
        {
            id: userId.toString(),
            name: name.first,
            fullName: `${name.first || ''} ${name.middle || ''} ${name.last || ''}`,
            role,
            email,
            user_type,
            user_id,
            gender,
        },
    ]);
    await User.updateMany({ _id: userId }, { $set: { ioToken: token } });
    user = await User.findOne({ _id: userId });
    return user;
};

const removeOldChatChannels = async (filter) => {
    const channels = await ioServerClient.queryChannels(filter, {}, { state: true });
    if (channels.length) {
        const channelCIds = channels.map((channel) => channel.cid);
        await ioServerClient.deleteChannels(channelCIds, { hard_delete: true });
    }
};
const removeAllOldChatChannels = async (institutionCalenderIds) => {
    try {
        const filter = { _institution_calendar_id: { $in: institutionCalenderIds } };
        const channels = await ioServerClient.queryChannels(
            filter,
            {},
            { limit: 100, state: true },
        );
        if (channels.length) {
            const channelIds = channels.map((channel) => channel.cid);
            // soft delete only
            await ioServerClient.deleteChannels(channelIds);
        }
    } catch (error) {
        return error;
    }
};

module.exports = {
    createChannel,
    addMembersToChannel,
    getMembersOfChannel,
    getStudentSubjects,
    ioRegister,
    removeOldChatChannels,
    removeAllOldChatChannels,
};
