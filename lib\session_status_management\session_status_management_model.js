const mongoose = require('mongoose');
const {
    INSTITUTION,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    DIGI_COURSE,
    ABSENT,
    PRESENT,
    EXCLUDE,
    SESSION_STATUS_MANAGEMENT,
} = require('../utility/constants');
const ObjectId = mongoose.Schema.Types.ObjectId;
const sessionStatusManagementSchema = new mongoose.Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: INSTITUTION,
            require: true,
        },
        _institution_calendar_id: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
            require: true,
        },
        _program_id: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
            require: true,
        },
        _curriculum_id: {
            type: ObjectId,
            ref: DIGI_CURRICULUM,
            require: true,
        },
        term: {
            type: String,
            require: true,
        },
        year: {
            type: String,
            require: true,
        },
        level: {
            type: String,
            require: true,
        },
        _course_id: {
            type: ObjectId,
            ref: DIGI_COURSE,
            require: true,
        },
        activeToInactive: {
            type: String,
            enum: [ABSENT, PRESENT, EXCLUDE],
            require: true,
        },
        inactiveToActive: {
            type: String,
            enum: [ABSENT, PRESENT, EXCLUDE],
            require: true,
        },
        isEnabled: {
            type: Boolean,
            default: false,
        },
    },
    { timeStamps: true },
);
const sessionStatusManagementModel = mongoose.model(
    SESSION_STATUS_MANAGEMENT,
    sessionStatusManagementSchema,
);

module.exports = sessionStatusManagementModel;
