const activitySchema = require('../../models/activities');
const {
    convertToMongoObjectId,
    sendResponse,
    list_all_response_function,
    sendResponseWithRequest,
    listAllResponseFunctionWithRequest,
} = require('../../utility/common');
const {
    DC_STAFF,
    DC_STUDENT,
    STARTED,
    COMPLETED,
    DS_SLO_KEY,
    DS_CLO_KEY,
    SCHEDULE_TYPES: { REGULAR },
    PUBLISHED,
    NOT_STARTED,
    DS_DATA_RETRIEVED,
    SCHEDULE,
} = require('../../utility/constants');
const {
    TIME,
    ONE_BY_ONE,
    OPEN_ENDED,
    OLD,
    NEW,
    QUIZ,
    MQ,
    SAQ,
    SCQ,
    MCQ,
    TF,
} = require('../../utility/enums');
const { timestampNow } = require('../../utility/common_functions');
const { cs } = require('../../utility/common');
const { getPaginationValues } = require('../../utility/pagination');
const { getUnsignedUrl, deleteAttachment } = require('../../utility/question_file_upload');
const questionSchema = require('../../models/question');
const courseScheduledSchema = require('../../models/course_schedule');
const userSchema = require('../../models/user');
const taxonomySchema = require('../../models/taxonomy');
const courseSchema = require('../../models/digi_course');
const { getAllActivity, studentGroupByGroupName } = require('./activities_v2_service');
const { sendNotificationPush } = require('../../../service/pushNotification.service');
const { saveDiscussionNotification } = require('../../utility/notification_push');
const { getClosBySloIds, getClos } = require('../learning_outcomes/learning_outcomes.service');
const { axiosCall } = require('../../../commonService/utility/socket.axios');
const { activityReportFromCloudFunction } = require('../../utility/utility.service');
const { getQuestionAnswered } = require('../../reports_analytics/reports_analytics_services');

// revamped started here
const shuffleQuestionsOrder = ({ questions }) => {
    for (let i = questions.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [questions[i], questions[j]] = [questions[j], questions[i]];
    }
    return questions;
};

const getActivityIdFromUrl = ({ link, _activityId }) => {
    const splitLink = link.split('/');
    const splitActivityId = splitLink.find((linkElement) => /^[0-9a-f]{24}$/.test(linkElement));
    return splitActivityId !== undefined ? splitActivityId : _activityId;
};

// get all activities
exports.getActivities = async (req, res) => {
    try {
        const {
            type,
            userId,
            courseId,
            sessionId,
            scheduleId,
            mergeStatus,
            limit,
            page,
            search,
            mode,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            courseAdmin,
            _institution_calendar_id: queryInstitutionCalendarId,
        } = req.query;
        const { _institution_calendar_id: headerInstitutionCalendarId } = req.headers;
        let institutionCalendarId = headerInstitutionCalendarId;
        if (
            (queryInstitutionCalendarId && queryInstitutionCalendarId.toString()) !==
            (headerInstitutionCalendarId && headerInstitutionCalendarId.toString())
        ) {
            institutionCalendarId = queryInstitutionCalendarId;
        }
        const queryData = {
            type,
            userId,
            limit,
            page,
            _institution_calendar_id: institutionCalendarId,
            courseAdmin,
            courseId,
            sessionId,
            scheduleId,
            mergeStatus,
            search,
            mode,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            headerInstitutionCalendarId: institutionCalendarId,
            rotation_count,
        };
        const { totalDoc, totalPages, currentPage, activities } = await getAllActivity(queryData);

        if (!activities || !activities.length) {
            return sendResponseWithRequest(req, res, 200, true, req.t('ACTIVITY_NOT_FOUND'), []);
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t(DS_DATA_RETRIEVED),
                        totalDoc,
                        totalPages,
                        currentPage,
                        activities,
                    ),
                );
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), activities);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.createActivities = async (req, res) => {
    try {
        const {
            name,
            quizType,
            createdBy,
            sessionFlowIds,
            courseId,
            _institution_calendar_id,
            _program_id,
            level_no,
            year_no,
            rotation,
            rotation_count,
            term,
            activityId,
            status,
            questions,
            courseAdmin,
            correctionType,
            description,
        } = req.body;
        let data;
        const bulkInserts = [];
        const questionIds = [];
        const orderPlace = [];
        let msg = '';

        if (activityId && questions && questions.length) {
            for (const questionElement of questions) {
                let questionOptions = [];
                let matchingOptions = [];
                if (!questionElement.questionId) {
                    if (questionElement.options && questionElement.options.length) {
                        questionOptions = questionElement.options.map((optionElement) => {
                            const questionOption = { answer: optionElement.answer };
                            if (optionElement.attachments)
                                questionOption.attachments = optionElement.attachments;
                            if (optionElement.name) questionOption.text = optionElement.name;
                            return questionOption;
                        });
                    }
                    if (questionElement.matchingOptions && questionElement.matchingOptions.length) {
                        matchingOptions = questionElement.matchingOptions.map(
                            (optionElement) => optionElement,
                        );
                    }
                    data = {
                        _activityId: convertToMongoObjectId(activityId),
                        text: questionElement.name,
                        questionType: questionElement.questionType,
                        options: questionOptions,
                        feedback: questionElement.feedback,
                    };

                    if (questionElement.sessionId) data.sessionId = questionElement.sessionId;
                    if (questionElement.sloIds)
                        data.sloIds = questionElement.sloIds.map((sloIdElement) =>
                            convertToMongoObjectId(sloIdElement),
                        );
                    if (questionElement.taxonomyIds)
                        data.taxonomyIds = questionElement.taxonomyIds.map((taxonomyIdElement) =>
                            convertToMongoObjectId(taxonomyIdElement),
                        );
                    if (questionElement.attachments) data.attachments = questionElement.attachments;
                    if (questionElement.mark) data.mark = questionElement.mark;
                    if (questionElement.mandatory) data.mandatory = questionElement.mandatory;
                    if (questionElement.descriptionEnable)
                        data.descriptionEnable = questionElement.descriptionEnable;
                    if (questionElement.shuffleOptionOrder)
                        data.shuffleOptionOrder = questionElement.shuffleOptionOrder;
                    if (questionElement.description) data.description = questionElement.description;

                    if (questionElement.characterLength)
                        data.characterLength = questionElement.characterLength;
                    if (questionElement.maxCharacterLimit)
                        data.maxCharacterLimit = questionElement.maxCharacterLimit;

                    if (
                        questionElement.matchingOptions &&
                        questionElement.matchingOptions.length > 0
                    )
                        data.matchingOptions = questionElement.matchingOptions;

                    if (questionElement.answerMatchingType)
                        data.answerMatchingType = questionElement.answerMatchingType;
                    if (questionElement.benchMark) data.benchMark = questionElement.benchMark;
                    if (
                        questionElement.answerTextVariant &&
                        questionElement.answerTextVariant.length > 0
                    )
                        data.answerTextVariant = questionElement.answerTextVariant;
                    data.isFromHeba = questionElement.isFromHeba;

                    orderPlace.push(questionElement.order);
                    // if (
                    //     !questionElement.options.length &&
                    //     questionElement.surveyQuestionType === OPEN_ENDED
                    // ) {
                    //     data.maxCharacterLimit = questionElement.maxCharacterLimit;
                    //     data.surveyQuestionType = questionElement.surveyQuestionType;
                    // }
                    // if (
                    //     questionElement.surveyQuestionType &&
                    //     questionElement.surveyQuestionType === LIKERTSCALE
                    // ) {
                    //     data.surveyQuestionType = questionElement.surveyQuestionType;
                    // }
                    // if (questionElement.questionViewType)
                    //     data.questionViewType = questionElement.questionViewType;
                    // if (questionElement.describeYourQuestion)
                    //     data.describeYourQuestion = questionElement.describeYourQuestion;
                    // if (questionElement.itemCategory)
                    //     data.itemCategory = questionElement.itemCategory;
                    // if (questionElement.aiNoOfOptions)
                    //     data.aiNoOfOptions = questionElement.aiNoOfOptions;
                    // if (typeof questionElement.generateFeedback === 'boolean')
                    //     data.generateFeedback = questionElement.generateFeedback;
                    bulkInserts.push({ insertOne: { document: data } });
                } else {
                    questionIds.push({
                        id: convertToMongoObjectId(questionElement.questionId),
                        type: OLD,
                        order: questionElement.order,
                    });
                }
            }

            if (bulkInserts.length) {
                await questionSchema
                    .bulkWrite(bulkInserts)
                    .then((result) => {
                        if (result) {
                            const { insertedIds } = result;
                            for (const insertedIdElement in insertedIds) {
                                if (insertedIds.hasOwnProperty(insertedIdElement)) {
                                    questionIds.push({
                                        id: insertedIds[insertedIdElement],
                                        type: NEW,
                                        order: orderPlace[insertedIdElement],
                                    });
                                }
                            }
                        }
                    })
                    .catch((err) => {
                        return sendResponseWithRequest(
                            req,
                            res,
                            200,
                            false,
                            req.t('FAILED_TO_ADD'),
                        );
                    });
            }
            if (status) {
                const updateData = {};
                updateData.status = status;
                if (status === PUBLISHED) updateData.status = NOT_STARTED;
                if (questionIds.length) {
                    const question = questionIds.map((questionIdElement) => {
                        return {
                            _id: questionIdElement.id,
                            type: questionIdElement.type,
                            order: questionIdElement.order,
                        };
                    });
                    updateData.questions = question;
                }
                const activityUpdatedData = await activitySchema.findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(activityId),
                    },
                    { $set: updateData },
                    { new: true },
                );
                const { status: activityStatus, quizType } = activityUpdatedData;
                msg = req.t(
                    `${quizType}_${
                        activityStatus === PUBLISHED ? 'PUBLISHED' : 'DRAFTED'
                    }_SUCCESSFULLY`,
                );
            }
        } else {
            const createActivityData = await activitySchema.create({
                name,
                courseId,
                sessionFlowIds,
                quizType,
                createdBy,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
                courseAdmin,
                correctionType,
                description,
            });

            msg = req.t(`${quizType}_ADDED_SUCCESSFULLY`);
            if (!createActivityData)
                return sendResponseWithRequest(req, res, 200, false, req.t('FAILED_TO_ADD'));
            return sendResponseWithRequest(req, res, 200, true, msg, createActivityData);
        }
        return sendResponseWithRequest(req, res, 200, true, msg);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

// if session changes in update screen remove old session based questions
const deleteSelectedQuestions = async (
    activitySessionFlowIds,
    activityQuestions,
    sessionFlowIds,
    sessionRemoved,
) => {
    const extractActivitySessionFlowIds = activitySessionFlowIds.map((sessionElement) =>
        sessionElement._id.toString(),
    );
    const extractSessionFlowIds = sessionFlowIds.map((sessionElement) => sessionElement._id);

    // check sessions flow mismatch
    const misMatchSessions = extractActivitySessionFlowIds.filter(
        (sessionElement) => !extractSessionFlowIds.includes(sessionElement),
    );
    const questionIds = activityQuestions.map((activityQuestion) =>
        convertToMongoObjectId(activityQuestion._id),
    );
    const questions = await questionSchema.find(
        {
            isDeleted: false,
            _id: {
                $in: questionIds,
            },
        },
        { sessionId: 1, attachments: 1, 'options.attachments': 1 },
    );

    // mismatched session questions
    if (misMatchSessions.length > 0 && !sessionRemoved) {
        let sessions = misMatchSessions.map((sessionQuestion) =>
            convertToMongoObjectId(sessionQuestion),
        );
        sessions = [...new Set(sessions)];
        const courseSchedules = await courseScheduledSchema.find(
            {
                isDeleted: false,
                'session._session_id': {
                    $in: sessions,
                },
            },
            { session: 1 },
        );
        const sessionDetail = [];
        if (courseSchedules) {
            sessions.forEach((session) => {
                const sessionDetails = courseSchedules.find(
                    (courseSchedule) =>
                        courseSchedule.session._session_id.toString() === session.toString(),
                );
                if (sessionDetails) {
                    sessionDetail.push(
                        sessionDetails.session.delivery_symbol +
                            '' +
                            sessionDetails.session.delivery_no,
                    );
                }
            });
        }
        if (sessionDetail.length) {
            return {
                isChanged: true,
                session: sessionDetail,
            };
        }
        return {
            isChanged: true,
            session: sessionDetail,
        };
    }

    const getOldOrNewQuestionType = ({ questionElement }) => {
        const { _id } = questionElement;
        const filterQuestion = activityQuestions.find(
            (questionElement) => questionElement._id.toString() === _id.toString(),
        );
        return filterQuestion && filterQuestion.type ? filterQuestion.type : 'NEW';
    };
    //delete attached links
    const removedQuestionIds = [];
    const removedIds = [];
    for (const questionElement of questions) {
        const questionType = getOldOrNewQuestionType({ questionElement });
        if (
            questionElement &&
            questionElement.sessionId &&
            misMatchSessions.includes(questionElement.sessionId.toString())
        ) {
            removedIds.push(questionElement._id.toString());
            if (questionType === 'NEW') {
                removedQuestionIds.push(convertToMongoObjectId(questionElement._id));
                for (const attachmentsElement of questionElement.options) {
                    for (const links of attachmentsElement.attachments) {
                        if (links.link !== '') await deleteAttachment(links.link);
                    }
                }
                for (const questionAttachmentElement of questionElement.attachments) {
                    if (questionAttachmentElement.link !== '')
                        await deleteAttachment(questionAttachmentElement.link);
                }
            }
        }
    }

    if (removedQuestionIds.length) {
        await questionSchema.deleteMany({ _id: { $in: removedQuestionIds } });
    }

    const updatedActivityQuestions = activityQuestions.filter((questionElement) => {
        return !removedIds.includes(questionElement._id.toString());
    });
    return {
        isChanged: false,
        questions: updatedActivityQuestions,
    };
};

// update activities
exports.updateActivities = async (req, res) => {
    try {
        const {
            body: {
                name,
                description,
                type,
                questions,
                status,
                schedule,
                sessionId,
                scheduleIds,
                sessionFlowIds,
                sessionRemoved,
                quizType,
                correctionType,
                shuffleQuestionOrder,
            },
            params: { id: activityId },
        } = req;
        let data;
        const questionIds = [];
        const bulkUpdates = [];
        const bulkInserts = [];
        const orderPlace = [];

        // get activity
        const activityData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(activityId),
                    isDeleted: false,
                },
                {
                    questions: 1,
                    sessionFlowIds: 1,
                    quizType: 1,
                },
            )
            .lean();
        if (!activityData)
            return sendResponseWithRequest(req, res, 200, false, req.t('ACTIVITY_NOT_FOUND'));

        const {
            questions: activityQuestions,
            sessionFlowIds: activitySessionFlowIds,
            quizType: activeQuizType,
        } = activityData;

        if (questions && questions.length) {
            const activityQuestionIds = activityQuestions.map(
                (activityQuestion) => activityQuestion._id,
            );
            const existingActivityQuestions = await questionSchema.find(
                {
                    _id: { $in: activityQuestionIds },
                },
                {
                    name: 1,
                    questionId: 1,
                    type: 1,
                    attachments: 1,
                    questionType: 1,
                    options: 1,
                    feedback: 1,
                    sessionId: 1,
                    sloIds: 1,
                    taxonomyIds: 1,
                    order: 1,
                    mark: 1,
                    mandatory: 1,
                    description: 1,
                    descriptionEnable: 1,
                    shuffleOptionOrder: 1,
                    characterLength: 1,
                    maxCharacterLimit: 1,
                    matchingOptions: 1,
                    answerMatchingType: 1,
                    benchMark: 1,
                    answerTextVariant: 1,
                    isFromHeba: 1,
                },
            );
            questions.forEach((questionElement) => {
                const {
                    name: questionName,
                    questionId,
                    type: activityQuestionType,
                    attachments,
                    questionType,
                    options,
                    feedback,
                    sessionId: questionSessionId,
                    sloIds,
                    taxonomyIds,
                    order,
                    // questionViewType,
                    // describeYourQuestion,
                    // itemCategory,
                    // aiNoOfOptions,
                    // generateFeedback,
                    // surveyQuestionType,
                    mark,
                    mandatory,
                    description,
                    descriptionEnable,
                    shuffleOptionOrder,
                    characterLength,
                    maxCharacterLimit,
                    matchingOptions,
                    answerMatchingType,
                    benchMark,
                    answerTextVariant,
                    isFromHeba,
                } = questionElement;

                const updateData = {
                    _activityId: activityId,
                    order,
                };
                if (questionName) {
                    updateData.text = questionName;
                }
                if (attachments) updateData.attachments = attachments;
                if (questionType) {
                    updateData.questionType = questionType;
                }
                if (questionSessionId) {
                    updateData.sessionId = questionSessionId;
                    updateData.sloIds = sloIds;
                    updateData.taxonomyIds = taxonomyIds;
                }
                if (feedback) updateData.feedback = feedback;
                if (mark) updateData.mark = mark;
                updateData.mandatory = mandatory;
                updateData.descriptionEnable = descriptionEnable;
                updateData.shuffleOptionOrder = shuffleOptionOrder;
                updateData.description = description;

                if (characterLength) updateData.characterLength = characterLength;
                if (maxCharacterLimit) updateData.maxCharacterLimit = maxCharacterLimit;

                if (matchingOptions && matchingOptions.length)
                    updateData.matchingOptions = matchingOptions;

                if (answerMatchingType) updateData.answerMatchingType = answerMatchingType;
                if (benchMark) updateData.benchMark = benchMark;
                if (answerTextVariant && answerTextVariant.length)
                    updateData.answerTextVariant = answerTextVariant;

                updateData.isFromHeba = isFromHeba;
                // if (surveyQuestionType) {
                //     updateData.surveyQuestionType = surveyQuestionType;
                // }
                // if (maxCharacterLimit) {
                //     updateData.maxCharacterLimit = maxCharacterLimit;
                // }
                // if (questionViewType) {
                //     updateData.questionViewType = questionViewType;
                // }
                // if (describeYourQuestion) {
                //     updateData.describeYourQuestion = describeYourQuestion;
                // }
                // if (itemCategory) {
                //     updateData.itemCategory = itemCategory;
                // }
                // if (aiNoOfOptions) {
                //     updateData.aiNoOfOptions = aiNoOfOptions;
                // }
                // if (typeof generateFeedback === 'boolean') {
                //     updateData.generateFeedback = generateFeedback;
                // }

                if (questionId) {
                    const hasQuestion = activityQuestions.find(
                        (activityQuestion) =>
                            activityQuestion._id.toString() === questionId.toString(),
                    );
                    if (hasQuestion) {
                        questionIds.push({
                            id: questionId,
                            type: activityQuestionType || 'NEW',
                            order,
                        });
                        if (activityQuestionType !== OLD) {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _activityId: convertToMongoObjectId(activityId),
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: updateData,
                                },
                            });
                        }
                    } else {
                        questionIds.push({ id: questionId, type: activityQuestionType, order });
                        bulkUpdates.push({
                            updateOne: {
                                filter: {
                                    _activityId: convertToMongoObjectId(activityId),
                                    _id: convertToMongoObjectId(questionId),
                                },
                                update: { isDeleted: true },
                            },
                        });
                    }
                } else {
                    if ([MQ, SAQ].includes(questionType)) {
                        updateData.options = [];
                    } else {
                        updateData.options = options.map((optionElement) => {
                            return {
                                text: optionElement.name,
                                answer: optionElement.answer,
                                attachments: optionElement.attachments,
                            };
                        });
                    }
                    orderPlace.push(order);
                    bulkInserts.push({ insertOne: { document: updateData } });
                }
                if (questionId && options && options.length) {
                    const oldActivityQuestion = existingActivityQuestions.find(
                        (activityQuestionEntry) =>
                            activityQuestionEntry._id.toString() === questionId.toString(),
                    );
                    let activityOptions;
                    if (oldActivityQuestion) activityOptions = oldActivityQuestion.options;

                    options.forEach((optionElement) => {
                        const {
                            _id,
                            name: text,
                            answer,
                            attachments: optionAttachments,
                        } = optionElement;
                        const hasOption = activityOptions?.find(
                            (activityOptionEntry) =>
                                _id && activityOptionEntry._id.toString() === _id.toString(),
                        );
                        if (activityOptions && hasOption) {
                            const optionUpdateData = {
                                'options.$[i].text': text,
                                'options.$[i].answer': answer,
                            };
                            if (optionAttachments) {
                                optionUpdateData['options.$[i].attachments'] = optionAttachments;
                            }

                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(questionId) },
                                    update: { $set: optionUpdateData },
                                    arrayFilters: [{ 'i._id': convertToMongoObjectId(_id) }],
                                },
                            });
                        } else if (questionId && _id) {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            options: { _id: convertToMongoObjectId(_id) },
                                        },
                                    },
                                },
                            });
                        }
                        if (!_id && (text || (optionAttachments && optionAttachments.length))) {
                            const optionUpdateData = { text, answer };
                            if (optionAttachments) {
                                optionUpdateData.attachments = optionAttachments;
                            }
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(questionId) },
                                    update: { $push: { options: optionUpdateData } },
                                },
                            });
                        }
                    });
                    const optionIds = options
                        .filter((optionElement) => optionElement._id)
                        .map((optionElement) => optionElement._id.toString());
                    let removeOptions = [];
                    if (activityOptions) {
                        removeOptions = activityOptions.filter(
                            (activityOption) => !optionIds.includes(activityOption._id.toString()),
                        );
                    }
                    if (removeOptions.length) {
                        const removeOptionIds = removeOptions.map(
                            (removeOption) => removeOption._id,
                        );
                        removeOptionIds.forEach((removeOptionId) => {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            options: {
                                                _id: convertToMongoObjectId(removeOptionId),
                                            },
                                        },
                                    },
                                },
                            });
                        });
                    }
                }

                if (questionId && matchingOptions && matchingOptions.length) {
                    const oldActivityQuestion = existingActivityQuestions.find(
                        (activityQuestionEntry) =>
                            activityQuestionEntry._id.toString() === questionId.toString(),
                    );
                    let activityMatchingOptions;
                    if (oldActivityQuestion)
                        activityMatchingOptions = oldActivityQuestion.matchingOptions;

                    matchingOptions.forEach((optionElement) => {
                        const { _id, rowText, columnText, answer, order } = optionElement;
                        const hasOption = activityMatchingOptions.find(
                            (activityOptionEntry) =>
                                _id && activityOptionEntry._id.toString() === _id.toString(),
                        );
                        if (activityMatchingOptions && hasOption) {
                            const optionUpdateData = {
                                'matchingOptions.$[i].rowText': rowText,
                                'matchingOptions.$[i].columnText': columnText,
                                'matchingOptions.$[i].answer': answer,
                                'matchingOptions.$[i].order': order,
                            };

                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(questionId) },
                                    update: { $set: optionUpdateData },
                                    arrayFilters: [{ 'i._id': convertToMongoObjectId(_id) }],
                                },
                            });
                        } else {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            matchingOptions: { _id: convertToMongoObjectId(_id) },
                                        },
                                    },
                                },
                            });
                        }
                    });
                    const optionIds = matchingOptions
                        .filter((optionElement) => optionElement._id)
                        .map((optionElement) => optionElement._id.toString());
                    let removeOptions = [];
                    if (activityMatchingOptions) {
                        removeOptions = activityMatchingOptions.filter(
                            (activityOption) => !optionIds.includes(activityOption._id.toString()),
                        );
                    }
                    if (removeOptions.length) {
                        const removeOptionIds = removeOptions.map(
                            (removeOption) => removeOption._id,
                        );
                        removeOptionIds.forEach((removeOptionId) => {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            matchingOptions: {
                                                _id: convertToMongoObjectId(removeOptionId),
                                            },
                                        },
                                    },
                                },
                            });
                        });
                    }
                }
            });

            if (bulkUpdates.length) {
                await questionSchema
                    .bulkWrite(bulkUpdates)
                    .then()
                    .catch((e) => {
                        return sendResponseWithRequest(
                            req,
                            res,
                            422,
                            false,
                            req.t('UPDATE_ERROR'),
                            e.message,
                        );
                    });
            }

            if (bulkInserts.length) {
                await questionSchema
                    .bulkWrite(bulkInserts)
                    .then((result) => {
                        if (result) {
                            const { insertedIds } = result;
                            for (const insertedId in insertedIds) {
                                if (insertedIds.hasOwnProperty(insertedId)) {
                                    questionIds.push({
                                        id: insertedIds[insertedId].toString(),
                                        type: NEW,
                                        order: orderPlace[insertedId],
                                    });
                                }
                            }
                        }
                    })
                    .catch((e) => {
                        return sendResponseWithRequest(
                            req,
                            res,
                            422,
                            false,
                            req.t('UPDATE_ERROR'),
                            e.message,
                        );
                    });
            }
        } else {
            if (type === SCHEDULE) {
                const getMergeSchedules = await courseScheduledSchema.find(
                    {
                        _id: { $in: scheduleIds },
                    },
                    {
                        merge_with: 1,
                        merge_status: 1,
                        _id: 1,
                    },
                );
                const mergedSessions = [];
                for (const getMergeSchedule of getMergeSchedules) {
                    const { merge_status, merge_with } = getMergeSchedule;
                    if (merge_status) {
                        merge_with.map((mergeWith) =>
                            mergedSessions.push(mergeWith.schedule_id.toString()),
                        );
                    }
                }
                const courseScheduleIds = [...scheduleIds, ...mergedSessions];
                data = {
                    $set: {
                        type,
                        schedule,
                        sessionId,
                        scheduleIds: courseScheduleIds,
                        description,
                        shuffleQuestionOrder,
                    },
                };
            } else {
                let sessionCheck;
                if (activeQuizType === QUIZ) {
                    // if session changes in update page
                    sessionCheck = await deleteSelectedQuestions(
                        activitySessionFlowIds,
                        activityQuestions,
                        sessionFlowIds,
                        sessionRemoved,
                    );
                    if (sessionCheck.isChanged) {
                        const msg = req.t('DO_YOU_WANT_TO_CHANGE_SESSION');
                        if (sessionCheck)
                            return sendResponseWithRequest(req, res, 200, true, msg, sessionCheck);
                    }
                }
                if (name && sessionFlowIds && sessionFlowIds.length) {
                    data = {
                        $set: {
                            name,
                            sessionFlowIds,
                            ...(correctionType && { correctionType }),
                            ...(sessionCheck &&
                                sessionCheck.questions && { questions: sessionCheck.questions }),
                        },
                    };
                }
            }
            if (data) {
                const activityUpdatedData = await activitySchema.findOneAndUpdate(
                    { _id: convertToMongoObjectId(activityId) },
                    data,
                );
                if (!activityUpdatedData)
                    return sendResponseWithRequest(req, res, 200, false, req.t('FAILED_TO_UPDATE'));
            }
        }
        if (status) {
            const updateData = {
                status,
                ...(quizType && { quizType }),
            };
            if (status === PUBLISHED) updateData.status = NOT_STARTED;
            if (questionIds.length) {
                const question = questionIds.map((questionElement) => {
                    return {
                        _id: questionElement.id,
                        type: questionElement.type,
                        order: questionElement.order,
                    };
                });
                updateData.questions = question;
            }

            await activitySchema.findOneAndUpdate(
                { _id: convertToMongoObjectId(activityId) },
                { $set: updateData },
            );
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

// delete activities
exports.deleteActivities = async (req, res) => {
    try {
        const {
            body: { type },
            params: { id },
        } = req;

        let activityData = {
            $set: {
                isDeleted: true,
            },
        };
        if (type === SCHEDULE) {
            activityData = {
                $unset: {
                    type: 1,
                    schedule: 1,
                    sessionId: 1,
                    scheduleIds: 1,
                    studentGroupId: 1,
                },
            };
        }
        await activitySchema.updateOne(
            {
                _id: convertToMongoObjectId(id),
            },
            activityData,
        );
        return sendResponseWithRequest(req, res, 200, true, req.t('DELETED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const formatQuestions = async ({ activityQuestions, answeredQuestions, conductArea = false }) => {
    const questions = [];
    let answeredCount = 0;
    let studentAnsweredOptionId;
    let studentAnsweredOptionIdArray = [];
    let studentTextAnswer;
    for (const activityQuestion of activityQuestions) {
        const { _id, _activityId, attachments, options, shuffleOptionOrder, matchingOptions } =
            activityQuestion;
        const questionAttachments = [];
        // get attachments unsigned url
        if (attachments && attachments.length > 0) {
            for (const attachment of attachments) {
                const { size, _id: attachmentId, link } = attachment;
                let unsignedUrl;
                if (link) {
                    const formattedActivityId = getActivityIdFromUrl({ link, _activityId });
                    unsignedUrl = await getUnsignedUrl(link, formattedActivityId);
                }
                questionAttachments.push({ size, _id: attachmentId, link: unsignedUrl });
            }
        }
        // get option attachments unsigned url
        const questionOptionWithAttachments = [];
        const shuffledOptions =
            conductArea && shuffleOptionOrder
                ? shuffleQuestionsOrder({ questions: options })
                : options;
        if (shuffledOptions && shuffledOptions.length > 0) {
            for (const option of shuffledOptions) {
                const {
                    _id: optionId,
                    attachments: optionAttachments,
                    text: optionText,
                    order: optionOrder,
                    answer,
                } = option;
                const questionOptionAttachments = [];
                for (const optionAttachment of optionAttachments) {
                    const {
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link,
                    } = optionAttachment;
                    let unsignedUrl;
                    if (link) {
                        const formattedActivityId = getActivityIdFromUrl({ link, _activityId });
                        unsignedUrl = await getUnsignedUrl(link, formattedActivityId);
                    }
                    questionOptionAttachments.push({
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link: unsignedUrl,
                    });
                }
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    studentAnsweredOptionId = optionId;
                    studentTextAnswer = optionText;
                    const findOptions = answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    );
                    studentAnsweredOptionIdArray = findOptions._optionIdArray;
                }

                questionOptionWithAttachments.push({
                    _id: optionId,
                    text: optionText,
                    attachments: questionOptionAttachments,
                    answer,
                    order: optionOrder,
                });
            }
        }
        const shuffledMatchingOptions =
            matchingOptions && matchingOptions.length
                ? conductArea && shuffleOptionOrder
                    ? shuffleQuestionsOrder({ questions: matchingOptions })
                    : matchingOptions
                : [];
        if (!shuffledOptions.length) {
            studentTextAnswer =
                answeredQuestions &&
                answeredQuestions.find(
                    (studentEntry) => studentEntry._questionId.toString() === _id.toString(),
                );
            studentTextAnswer = studentTextAnswer ? studentTextAnswer.textAnswer : null;
        }

        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }

        questions.push({
            ...activityQuestion,
            attachments: questionAttachments,
            options: questionOptionWithAttachments,
            questionAnswered,
            studentAnsweredOptionId,
            studentAnsweredOptionIdArray,
            studentTextAnswer,
            matchingOptions: shuffledMatchingOptions,
            mark: activityQuestion && activityQuestion.mark ? activityQuestion.mark : 1,
        });
    }
    const questionEntry = questions.sort((a, b) => {
        return a.order - b.order;
    });

    return {
        questions: questionEntry,
        answeredCount,
    };
};

exports.getActivity = async (req, res) => {
    try {
        const {
            params: { id },
            query: { studentId },
        } = req;
        const isStudent = studentId !== undefined;
        const activityData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(id),
                    isDeleted: false,
                },
                {
                    quizType: 1,
                    courseId: 1,
                    status: 1,
                    name: 1,
                    questions: 1,
                    schedule: 1,
                    setQuizTime: 1,
                    startTime: 1,
                    staffStartWithExam: 1,
                    _program_id: 1,
                    students: 1,
                    ...(isStudent
                        ? {
                              socketEventStudentId: 1,
                          }
                        : {
                              sessionId: 1,
                              socketEventStaffId: 1,
                              scheduleIds: 1,
                              sessionFlowIds: 1,
                          }),

                    type: 1,
                    createdBy: 1,
                    endTime: 1,
                    year_no: 1,
                    level_no: 1,
                    term: 1,
                    rotation: 1,
                    rotation_count: 1,
                    _institution_calendar_id: 1,
                    courseAdmin: 1,
                    correctionType: 1,
                    shuffleQuestionOrder: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: {
                    course_code: 1,
                    course_name: 1,
                    versionedCourseIds: 1,
                    versionedFrom: 1,
                    versionName: 1,
                    versioned: 1,
                    versionNo: 1,
                },
            })
            .lean();
        if (!activityData)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_DATA_FOUND'));

        const {
            students,
            sessionFlowIds,
            sessionId,
            courseId,
            questions,
            scheduleIds,
            shuffleQuestionOrder,
            staffStartWithExam,
            correctionType,
        } = activityData;

        let selectedQuestions = [];
        let answeredQuestions;
        let activityQuestions;
        if (isStudent) {
            const orderedQuestionsIds = questions
                .sort((a, b) => {
                    return a.order - b.order;
                })
                .map((questionElement) => questionElement._id);
            if (orderedQuestionsIds.length > 0) {
                selectedQuestions = await questionSchema
                    .find(
                        {
                            _id: { $in: orderedQuestionsIds },
                            isDeleted: false,
                        },
                        {
                            text: 1,
                            'options._id': 1,
                            'options.text': 1,
                            'options.order': 1,
                            'options.attachments': 1,
                            attachments: 1,
                            surveyQuestionType: 1,
                            maxCharacterLimit: 1,
                            _activityId: 1,
                            questionType: 1,
                            mandatory: 1,
                            description: 1,
                            shuffleOptionOrder: 1,
                            characterLength: 1,
                            mark: 1,
                            'matchingOptions._id': 1,
                            'matchingOptions.rowText': 1,
                            'matchingOptions.columnText': 1,
                            'matchingOptions.order': 1,
                        },
                    )
                    .lean();

                if (selectedQuestions.length > 0) {
                    selectedQuestions = selectedQuestions.map((selectedQuestion) => {
                        const { _id } = selectedQuestion;
                        const questionType = questions.find(
                            (q) => q._id.toString() === _id.toString(),
                        );
                        if (questionType) {
                            selectedQuestion.questionMoved = questionType.questionMoved
                                ? questionType.questionMoved
                                : false;
                            selectedQuestion.acceptQuestionResponse =
                                questionType.acceptQuestionResponse
                                    ? questionType.acceptQuestionResponse
                                    : false;
                            selectedQuestion.type = questionType.type;
                            selectedQuestion.order = questionType.order;
                        }
                        return selectedQuestion;
                    });
                }
            }

            if (studentId) {
                const answeredStudent = students.find((s) => cs(s._studentId) === cs(studentId));
                if (answeredStudent) answeredQuestions = answeredStudent.questions;
            }
            if (selectedQuestions.length) {
                activityQuestions = await formatQuestions({
                    activityQuestions: selectedQuestions,
                    answeredQuestions,
                    conductArea: true,
                });
                activityData.questions =
                    isStudent && staffStartWithExam === 'TIME' && shuffleQuestionOrder
                        ? shuffleQuestionsOrder({
                              questions: activityQuestions.questions,
                          })
                        : activityQuestions.questions;
            }
        }
        let totalStudents = 0;
        let studentGroupsName = '';
        let sessionDetails = [];
        if (!isStudent && scheduleIds && scheduleIds.length > 0) {
            const scheduleConvertIds = scheduleIds.map((scheduleIdElement) =>
                convertToMongoObjectId(scheduleIdElement),
            );

            const courseScheduleData = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleConvertIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'students._id': 1,
                    merge_status: 1,
                    merge_with: 1,
                    'student_groups.group_id': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                },
            );

            const mergedStudents = new Set();
            const mergedStudentGroups = new Set();
            for (const courseScheduleElement of courseScheduleData) {
                const { students, merge_status, merge_with, student_groups } =
                    courseScheduleElement;
                let scheduleStudents = students;
                let scheduleStudentGroups = student_groups;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWithElement) =>
                        convertToMongoObjectId(mergeWithElement.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const mergeScheduleData = await courseScheduledSchema.find(
                            {
                                _id: { $in: scheduleIds },
                                isDeleted: false,
                                isActive: true,
                            },
                            {
                                'students._id': 1,
                                'student_groups.group_id': 1,
                                'student_groups.group_name': 1,
                                'student_groups.session_group.group_name': 1,
                            },
                        );
                        scheduleStudents = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.students,
                        );
                        scheduleStudentGroups = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.student_groups,
                        );
                    }
                }
                scheduleStudents.forEach((studentElement) =>
                    mergedStudents.add(JSON.stringify(studentElement)),
                );
                scheduleStudentGroups.forEach((groupElement) =>
                    mergedStudentGroups.add(JSON.stringify(groupElement)),
                );
            }

            const uniqueStudents = [...mergedStudents].map((studentElement) =>
                JSON.parse(studentElement),
            );
            const uniqueStudentGroups = [...mergedStudentGroups].map((groupElement) =>
                JSON.parse(groupElement),
            );

            totalStudents = uniqueStudents.length;
            studentGroupsName = studentGroupByGroupName(uniqueStudentGroups)
                .map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                })
                .join(', ');
        }

        if (!isStudent) {
            let sessionFlows = sessionFlowIds.map((sessionFlowId) =>
                convertToMongoObjectId(sessionFlowId._id),
            );
            if (sessionId) {
                sessionFlows = [...sessionFlows, ...[sessionId]];
            }
            let sessions = await courseScheduledSchema
                .find(
                    {
                        _course_id: convertToMongoObjectId(courseId._id),
                        $or: [
                            {
                                'session._session_id': { $in: sessionFlows },
                            },
                            {
                                _id: { $in: sessionFlows },
                            },
                        ],
                    },
                    { session: 1, title: 1, type: 1, _id: 1 },
                )
                .lean();

            sessionFlows = sessionFlows.map((sessionFlow) => sessionFlow.toString());
            sessionDetails = sessionFlowIds.map((sessionId) => {
                const sessionDetail = sessions.find(
                    (sessionEntry) =>
                        (sessionId &&
                            sessionEntry.session &&
                            sessionEntry.session._session_id.toString() ===
                                sessionId._id.toString()) ||
                        (sessionEntry.type === sessionId.type &&
                            sessionEntry._id.toString() === sessionId._id.toString()),
                );
                if (sessionDetail) {
                    const { session, _id, title, type } = sessionDetail;
                    if (session) {
                        const { _session_id } = session;
                        if (
                            session &&
                            _session_id &&
                            sessionFlows.includes(_session_id.toString())
                        ) {
                            return {
                                _id: session._session_id,
                                s_no: session.s_no,
                                delivery_symbol: session.delivery_symbol,
                                delivery_no: session.delivery_no,
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                type: REGULAR,
                            };
                        }
                    }
                    if (title && type !== REGULAR && sessionFlows.includes(_id.toString())) {
                        return { _id, title, type };
                    }
                }
            });
            sessions = sessions
                .filter((sessionElement) => sessionElement.session)
                .map((sessionElement) => sessionElement.session);
        }

        activityData.sessionFlowIds = sessionDetails || [];
        activityData.studentGroupName = studentGroupsName;
        activityData.totalStudentCount = totalStudents;
        activityData.totalStudentAnsweredCount = students.length;
        activityData.totalQuestionCount = questions.length;
        activityData.answeredCount =
            activityQuestions && activityQuestions.answeredCount
                ? activityQuestions.answeredCount
                : 0;
        activityData.isNewActivity = !!(correctionType && correctionType !== '');

        return sendResponseWithRequest(req, res, 200, true, req.t('DATA_RETRIEVED'), activityData);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const getSloPlusCloBySessionIdAndCourseId = async (courseId, sessionId, type) => {
    try {
        const courseResult = await courseSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(courseId),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'framework.domains': 1,
                },
            )
            .lean();
        const sessionSlos = [];
        if (courseResult) {
            const domains = courseResult.framework.domains;
            domains.forEach((domainItem) => {
                if (!domainItem.clo) return;
                domainItem.clo.forEach((cloItem) => {
                    if (!cloItem.isDeleted) {
                        sessionSlos.push({
                            _id: cloItem._id,
                            name: cloItem.name,
                            no: cloItem.no,
                            type: DS_CLO_KEY,
                        });
                    }

                    if (cloItem.slos && cloItem.slos.length) {
                        cloItem.slos.forEach((sloItem) => {
                            if (
                                sloItem.delivery_type_id &&
                                sloItem.delivery_type_id.toString() === sessionId.toString() &&
                                sloItem.mapped_value === 'TRUE'
                            ) {
                                sessionSlos.push({
                                    _id: sloItem.slo_id,
                                    name: sloItem.name,
                                    no: sloItem.no,
                                    type: DS_SLO_KEY,
                                });
                            }
                        });
                    }
                });
            });
            if (!sessionSlos.length) {
                domains.forEach((domainItem) => {
                    if (!domainItem.clo) return;
                    domainItem.clo.forEach((cloItem) => {
                        if (!cloItem.isDeleted) {
                            sessionSlos.push({
                                _id: cloItem._id,
                                name: cloItem.name,
                                no: cloItem.no,
                                type: DS_CLO_KEY,
                            });
                        }
                    });
                });
            }
        }
        return sessionSlos;

        // const sloIds = sessionSlos.map((sessionSlo) => convertToMongoObjectId(sessionSlo._id));
        // const activities = await activitySchema
        //     .find(
        //         {
        //             status: COMPLETED,
        //             courseId: convertToMongoObjectId(courseId),
        //             'sessionFlowIds._id': convertToMongoObjectId(sessionId),
        //         },
        //         { questions: 1 },
        //     )
        //     .lean();
        // const activityQuestions = activities
        //     .filter((activity) => activity.questions && activity.questions.length)
        //     .map((activityEntry) => activityEntry.questions);

        // // eslint-disable-next-line prefer-spread
        // let questionIds = [].concat.apply([], activityQuestions);
        // questionIds = questionIds.map((questionId) => convertToMongoObjectId(questionId._id));

        // const questions = await questionSchema
        //     .find(
        //         {
        //             isDeleted: false,
        //             _id: { $in: questionIds },
        //             sloIds: { $in: sloIds },
        //             sessionId: convertToMongoObjectId(sessionId),
        //         },
        //         {
        //             _id: 1,
        //             sloIds: 1,
        //             sessionId: 1,
        //         },
        //     )
        //     .lean();
        // const questionSessions = sessionSlos.reduce((questionSession, currentQuestionSession) => {
        //     const existQuestionSession = questionSession.find(
        //         (questionSessionEntry) =>
        //             questionSessionEntry._id.toString() === currentQuestionSession._id.toString(),
        //     );
        //     if (!existQuestionSession) {
        //         return questionSession.concat([currentQuestionSession]);
        //     }
        //     return questionSession;
        // }, []);

        // if (type === 'question') {
        //     questionSessions = questionSessions.filter((sessionSlo) =>
        //         questions.find((question) => {
        //             const sloOrClos = question.sloIds.map((sloId) => sloId.toString());
        //             if (
        //                 sloOrClos.includes(sessionSlo._id.toString()) &&
        //                 sessionId.toString() === question.sessionId.toString()
        //             ) {
        //                 return true;
        //             }
        //             return false;
        //         }),
        //     );
        // }
        // return questionSessions;
    } catch (error) {
        throw new Error(error);
    }
};

exports.getActivityById = async (req, res) => {
    try {
        const {
            params: { id },
            query: { studentId },
        } = req;
        const isStudent = studentId !== undefined;
        const activityData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(id),
                    isDeleted: false,
                },
                {
                    quizType: 1,
                    courseId: 1,
                    status: 1,
                    name: 1,
                    questions: 1,
                    schedule: 1,
                    setQuizTime: 1,
                    startTime: 1,
                    staffStartWithExam: 1,
                    _program_id: 1,
                    students: 1,
                    ...(isStudent
                        ? {
                              socketEventStudentId: 1,
                          }
                        : {
                              sessionId: 1,
                              socketEventStaffId: 1,
                              scheduleIds: 1,
                              sessionFlowIds: 1,
                          }),

                    type: 1,
                    createdBy: 1,
                    endTime: 1,
                    year_no: 1,
                    level_no: 1,
                    term: 1,
                    rotation: 1,
                    rotation_count: 1,
                    _institution_calendar_id: 1,
                    courseAdmin: 1,
                    correctionType: 1,
                    shuffleQuestionOrder: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: {
                    course_code: 1,
                    course_name: 1,
                    versionedCourseIds: 1,
                    versionedFrom: 1,
                    versionName: 1,
                    versioned: 1,
                    versionNo: 1,
                },
            })
            .lean();
        if (!activityData)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_DATA_FOUND'));

        const {
            students,
            sessionFlowIds,
            sessionId,
            courseId,
            questions,
            scheduleIds,
            correctionType,
        } = activityData;

        let selectedQuestions = [];
        let answeredQuestions;
        let activityQuestions;
        let sessionIdDetail;
        const orderedQuestionsIds = questions
            .sort((a, b) => {
                return a.order - b.order;
            })
            .map((questionElement) => convertToMongoObjectId(questionElement._id));
        if (orderedQuestionsIds.length > 0) {
            selectedQuestions = await questionSchema
                .find(
                    {
                        _id: { $in: orderedQuestionsIds },
                        isDeleted: false,
                    },
                    {
                        text: 1,
                        'options._id': 1,
                        'options.text': 1,
                        'options.order': 1,
                        'options.attachments': 1,
                        'options.answer': 1,
                        attachments: 1,
                        surveyQuestionType: 1,
                        maxCharacterLimit: 1,
                        feedback: 1,
                        questionType: 1,
                        _activityId: 1,
                        generateFeedback: 1,
                        sessionId: 1,
                        taxonomyIds: 1,
                        sloIds: 1,
                        type: 1,
                        mark: 1,
                        mandatory: 1,
                        description: 1,
                        descriptionEnable: 1,
                        shuffleOptionOrder: 1,
                        characterLength: 1,
                        answerMatchingType: 1,
                        answerTextVariant: 1,
                        benchMark: 1,
                        matchingOptions: 1,
                    },
                )
                .lean();

            if (selectedQuestions.length > 0) {
                const taxonomyIdsArray = selectedQuestions.reduce((acc, questionElement) => {
                    return [...acc, ...questionElement.taxonomyIds];
                }, []);
                let taxonomyDBData = [];
                if (taxonomyIdsArray.length > 0) {
                    taxonomyDBData = await taxonomySchema.find(
                        { _id: { $in: taxonomyIdsArray } },
                        { name: 1 },
                    );
                }
                selectedQuestions = selectedQuestions.map((selectedQuestion) => {
                    const { _id } = selectedQuestion;
                    const filteredTaxonomy =
                        taxonomyDBData.length > 0
                            ? selectedQuestion.taxonomyIds.map((taxonomyElement) =>
                                  taxonomyDBData.find(
                                      (item) => item._id.toString() === taxonomyElement.toString(),
                                  ),
                              )
                            : [];

                    const questionType = questions.find((q) => q._id.toString() === _id.toString());
                    if (questionType) {
                        selectedQuestion.questionMoved = questionType.questionMoved
                            ? questionType.questionMoved
                            : false;
                        selectedQuestion.acceptQuestionResponse =
                            questionType.acceptQuestionResponse
                                ? questionType.acceptQuestionResponse
                                : true;
                        selectedQuestion.type = questionType.type;
                        selectedQuestion.order = questionType.order;
                    }
                    selectedQuestion.taxonomyIds = filteredTaxonomy;
                    if (
                        isStudent &&
                        selectedQuestion.matchingOptions &&
                        selectedQuestion.matchingOptions.length > 0
                    ) {
                        const columnTexts = selectedQuestion.matchingOptions.map(
                            (optionElement) => optionElement.columnText,
                        );
                        for (let i = columnTexts.length - 1; i > 0; i--) {
                            const j = Math.floor(Math.random() * (i + 1));
                            [columnTexts[i], columnTexts[j]] = [columnTexts[j], columnTexts[i]];
                        }
                        selectedQuestion.matchingOptions = selectQuestion.matchedQuestions.map(
                            (opt, index) => ({
                                ...opt,
                                columnText: columnTexts[index],
                            }),
                        );
                    }
                    return selectedQuestion;
                });
            }
        }

        if (studentId) {
            const answeredStudent = students.find((s) => cs(s._studentId) === cs(studentId));
            if (answeredStudent) answeredQuestions = answeredStudent.questions;
        }
        if (selectedQuestions.length) {
            activityQuestions = await formatQuestions({
                activityQuestions: selectedQuestions,
                answeredQuestions,
            });
            activityData.questions = activityQuestions.questions;
        } else {
            activityData.questions = [];
        }

        let totalStudents = 0;
        let studentGroupsName = '';
        if (!isStudent && scheduleIds && scheduleIds.length > 0) {
            const scheduleConvertIds = scheduleIds.map((scheduleIdElement) =>
                convertToMongoObjectId(scheduleIdElement),
            );

            const courseScheduleData = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleConvertIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'students._id': 1,
                    merge_status: 1,
                    merge_with: 1,
                    'student_groups.group_id': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                },
            );

            const mergedStudents = new Set();
            const mergedStudentGroups = new Set();
            for (const courseScheduleElement of courseScheduleData) {
                const { students, merge_status, merge_with, student_groups } =
                    courseScheduleElement;
                let scheduleStudents = students;
                let scheduleStudentGroups = student_groups;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWithElement) =>
                        convertToMongoObjectId(mergeWithElement.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const mergeScheduleData = await courseScheduledSchema.find(
                            {
                                _id: { $in: scheduleIds },
                                isDeleted: false,
                                isActive: true,
                            },
                            {
                                'students._id': 1,
                                'student_groups.group_id': 1,
                                'student_groups.group_name': 1,
                                'student_groups.session_group.group_name': 1,
                            },
                        );
                        scheduleStudents = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.students,
                        );
                        scheduleStudentGroups = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.student_groups,
                        );
                    }
                }
                scheduleStudents.forEach((studentElement) =>
                    mergedStudents.add(JSON.stringify(studentElement)),
                );
                scheduleStudentGroups.forEach((groupElement) =>
                    mergedStudentGroups.add(JSON.stringify(groupElement)),
                );
            }

            const uniqueStudents = [...mergedStudents].map((studentElement) =>
                JSON.parse(studentElement),
            );
            const uniqueStudentGroups = [...mergedStudentGroups].map((groupElement) =>
                JSON.parse(groupElement),
            );

            totalStudents = uniqueStudents.length;
            studentGroupsName = uniqueStudentGroups
                .map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                })
                .join(', ');
        }

        let sessionFlows = sessionFlowIds.map((sessionFlowId) =>
            convertToMongoObjectId(sessionFlowId._id),
        );
        if (sessionId) {
            sessionFlows = [...sessionFlows, ...[sessionId]];
        }

        let sessions = await courseScheduledSchema
            .find(
                {
                    _course_id: convertToMongoObjectId(courseId._id),
                    $or: [
                        {
                            'session._session_id': { $in: sessionFlows },
                        },
                        {
                            _id: { $in: sessionFlows },
                        },
                    ],
                },
                { session: 1, title: 1, type: 1, _id: 1 },
            )
            .lean();

        if (sessionId) {
            sessionIdDetail = sessions.find(
                (sessionEntry) =>
                    (sessionId &&
                        sessionEntry.session &&
                        sessionEntry.session._session_id.toString() === sessionId._id.toString()) ||
                    (sessionId && sessionEntry._id.toString() === sessionId._id.toString()),
            );
            if (sessionIdDetail.session) {
                sessionIdDetail.session.type = REGULAR;
            }

            sessionIdDetail =
                sessionIdDetail && sessionIdDetail.session
                    ? sessionIdDetail.session
                    : sessionIdDetail && sessionIdDetail.title
                    ? {
                          _id: sessionIdDetail._id,
                          title: sessionIdDetail.title,
                          type: sessionIdDetail.type,
                      }
                    : '';
        }
        sessionFlows = sessionFlows.map((sessionFlow) => sessionFlow.toString());
        const sessionDetails = sessionFlowIds.map((sessionId) => {
            const sessionDetail = sessions.find(
                (sessionEntry) =>
                    (sessionId &&
                        sessionEntry.session &&
                        sessionEntry.session._session_id.toString() === sessionId._id.toString()) ||
                    (sessionEntry.type === sessionId.type &&
                        sessionEntry._id.toString() === sessionId._id.toString()),
            );
            if (sessionDetail) {
                const { session, _id, title, type } = sessionDetail;
                if (session) {
                    const { _session_id } = session;
                    if (session && _session_id && sessionFlows.includes(_session_id.toString())) {
                        return {
                            _id: session._session_id,
                            s_no: session.s_no,
                            delivery_symbol: session.delivery_symbol,
                            delivery_no: session.delivery_no,
                            session_type: session.session_type,
                            session_topic: session.session_topic,
                            type: REGULAR,
                        };
                    }
                }
                if (title && type !== REGULAR && sessionFlows.includes(_id.toString())) {
                    return { _id, title, type };
                }
            }
        });
        sessions = sessions
            .filter((sessionElement) => sessionElement.session)
            .map((sessionElement) => sessionElement.session);

        const sessionFlowSlos = [];

        for (const sessionFlowId of sessionFlowIds) {
            const sessionDetail = sessions.find((s) => cs(s._session_id) === cs(sessionFlowId._id));
            if (sessionDetail) {
                let slos = await getSloPlusCloBySessionIdAndCourseId(
                    courseId._id,
                    sessionFlowId._id,
                );
                slos = slos.filter(
                    (value, index, array) =>
                        array.findIndex((sloIds) => sloIds._id === value._id) === index,
                );
                sessionFlowSlos.push({ session: sessionDetail, slos });
            }
        }

        if (
            activityData.questions.length > 0 &&
            sessionFlowSlos.length &&
            sessionFlowSlos[0].slos.length > 0
        ) {
            const sloUpdatedQuestions = activityData.questions.map((selectedQuestion) => {
                const { sloIds } = selectedQuestion;
                const sloArray = sessionFlowSlos
                    .map((sloElement) => sloElement.slos)
                    .flatMap((sloElement) => sloElement);
                const filteredSlos =
                    sloIds && sloIds.length > 0
                        ? sloIds
                              .map((sloElement) =>
                                  sloArray.find(
                                      (item) => item._id.toString() === sloElement.toString(),
                                  ),
                              )
                              .filter((sloElement) => sloElement !== undefined)
                        : [];

                selectedQuestion.sloIds = filteredSlos;
                return selectedQuestion;
            });
            activityData.questions = sloUpdatedQuestions;
        }

        activityData.sessionsAndSlos = sessionFlowSlos;
        activityData.sessionFlowIds = sessionDetails || [];
        activityData.sessionIdDetail = sessionIdDetail;
        activityData.studentGroupName = studentGroupsName;
        activityData.totalStudentCount = totalStudents;
        activityData.totalStudentAnsweredCount = students.length;
        activityData.totalQuestionCount = questions.length;
        activityData.answeredCount =
            activityQuestions && activityQuestions.answeredCount
                ? activityQuestions.answeredCount
                : 0;
        activityData.isNewActivity = !!(correctionType && correctionType !== '');

        return sendResponseWithRequest(req, res, 200, true, req.t('DATA_RETRIEVED'), activityData);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const sendActivityStartNotification = async ({ data, userId }) => {
    try {
        const {
            scheduleIds,
            name,
            quizType,
            _id,
            startTime,
            staffStartWithExam,
            questions,
            courseId,
            createdBy,
            courseAdmin,
            sessionFlowIds,
            level_no,
            _program_id,
            activityId,
            _institution_calendar_id,
            setQuizTime,
            correctionType,
            year_no,
        } = data;

        if (scheduleIds && scheduleIds.length > 0) {
            const scheduleConvertIds = scheduleIds.map((scheduleId) =>
                convertToMongoObjectId(scheduleId),
            );
            const courseScheduleData = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleConvertIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'students._id': 1,
                    'staffs._staff_id': 1,
                    merge_status: 1,
                    merge_with: 1,
                    'session._session_id': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.group_id': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                },
            );

            const mergedStudents = new Set();
            const mergedStaffs = new Set();
            const mergedSession = new Set();
            const mergedStudentGroups = new Set();
            for (const courseScheduleElement of courseScheduleData) {
                const { students, staffs, merge_status, merge_with, session, student_groups } =
                    courseScheduleElement;
                let scheduleStudents = students;
                let scheduleStaffs = staffs;
                let scheduleSession = session;
                let scheduleStudentGroups = student_groups;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const mergeScheduleData = await courseScheduledSchema.find(
                            {
                                _id: { $in: scheduleIds },
                                isDeleted: false,
                                isActive: true,
                            },
                            {
                                'students._id': 1,
                                'staffs._staff_id': 1,
                                'session._session_id': 1,
                                'session.delivery_symbol': 1,
                                'session.delivery_no': 1,
                                'student_groups.group_id': 1,
                                'student_groups.group_name': 1,
                                'student_groups.session_group.group_name': 1,
                            },
                        );
                        scheduleStudents = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.students,
                        );
                        scheduleStaffs = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.staffs,
                        );
                        scheduleSession = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.session,
                        );
                        scheduleStudentGroups = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.student_groups,
                        );
                    }
                } else {
                    scheduleSession = Array.isArray(scheduleSession)
                        ? scheduleSession
                        : [scheduleSession];
                }
                scheduleStudents.forEach((studentElement) =>
                    mergedStudents.add(JSON.stringify(studentElement)),
                );
                scheduleStaffs.forEach((staffElement) =>
                    mergedStaffs.add(JSON.stringify(staffElement)),
                );
                scheduleSession.forEach((sessionElement) =>
                    mergedSession.add(JSON.stringify(sessionElement)),
                );
                scheduleStudentGroups.forEach((groupsElement) =>
                    mergedStudentGroups.add(JSON.stringify(groupsElement)),
                );
            }

            const uniqueStudents = [...mergedStudents].map((studentElement) =>
                JSON.parse(studentElement),
            );
            const uniqueStaffs = [...mergedStaffs].map((staffElement) => JSON.parse(staffElement));
            const uniqueSessions = [...mergedSession]
                .map((sessionElement) => JSON.parse(sessionElement))
                .filter((sessionElement) => Object.keys(sessionElement).length !== 0);
            const uniqueStudentGroups = [...mergedStudentGroups].map((groupsElement) =>
                JSON.parse(groupsElement),
            );

            const sessionFlowIdsArray = sessionFlowIds
                .map((sessionElement) => {
                    const matchingSession = uniqueSessions.find(
                        (uniqueSession) =>
                            uniqueSession._session_id.toString() === sessionElement._id.toString(),
                    );
                    return matchingSession
                        ? { ...matchingSession, type: sessionElement.type }
                        : null;
                })
                .filter(Boolean);

            const studentGroupName = studentGroupByGroupName(uniqueStudentGroups)
                .map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                })
                .join(', ');

            const totalStudentIds = [
                ...uniqueStudents.map((studentElement) => studentElement._id),
                ...uniqueStaffs
                    .filter(
                        (staffElement) => staffElement._staff_id.toString() !== userId.toString(),
                    )
                    .map((staffElement) => staffElement._staff_id),
            ];

            const userDetails = await userSchema.find(
                {
                    _id: { $in: totalStudentIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    device_type: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    user_type: 1,
                },
            );
            const isNewActivity = !!(correctionType && correctionType !== '');
            const commonSocketData = {
                name,
                quizType,
                status: 'started',
                startTime,
                totalQuestionCount: questions.length,
                courseId,
                createdBy: {
                    name: createdBy.name,
                },
                _id,
                courseAdmin,
                _institution_calendar_id,
                staffStartWithExam,
                ...(staffStartWithExam === TIME && { setQuizTime }),
                type: 'normal',
                isNewActivity,
                correctionType,
            };
            // send socket to student
            const studentDetails = userDetails.filter(
                (studentElement) => studentElement.user_type === 'student',
            );
            if (studentDetails && studentDetails.length) {
                const studentSocketData = {
                    ...commonSocketData,
                };
                const sendSocketData = [];
                for (const user of studentDetails) {
                    const eventId = user._id;
                    sendSocketData.push({
                        eventId,
                        data: JSON.stringify({ notificationCount: 1, activity: studentSocketData }),
                    });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }

            // send socket to staff
            const staffDetails = userDetails.filter(
                (staffElement) => staffElement.user_type === 'staff',
            );
            if (staffDetails && staffDetails.length) {
                const staffSocketData = {
                    ...commonSocketData,
                    studentGroupName,
                    sessionFlowIds: sessionFlowIdsArray,
                    totalStudentAnsweredCount: 0,
                    totalStudentCount: studentDetails.length,
                };
                const sendSocketData = [];
                for (const user of staffDetails) {
                    const eventId = user._id;
                    sendSocketData.push({
                        eventId,
                        data: JSON.stringify({ notificationCount: 1, activity: staffSocketData }),
                    });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }

            // push notification
            if (userDetails.length) {
                const title = `${quizType} Started`;
                let message = name + '\n';
                message += staffStartWithExam === 'TIME' ? 'Standard' : 'One by one';
                message += ' ' + quizType;
                message +=
                    '\n' + _program_id.name + ' • ' + level_no + ' • ' + courseId.course_name;
                const pushData = {
                    Title: title,
                    Body: message,
                    title,
                    description: message,
                    ClickAction: 'activity_start',
                    buttonAction: 'activity_start',
                    //ScheduleId: scheduleIds[0],
                    //Session: schedule.session ? session._session_id : undefined,
                    programId: _program_id._id,
                    institutionCalendarId: _institution_calendar_id,
                    yearNo: year_no,
                    levelNo: level_no,
                    //term,
                    //mergeStatus: merge_status,
                    //mergeType: type,
                    CourseId: courseId._id,
                    activityId,
                    staffStartWithExam,
                    //_id: scheduleId,
                    courseId: courseId._id,
                    notificationType: 'activity_start',
                    // rotation,
                    // rotation_count,
                    type: 'activity',
                    isNewActivity,
                    correctionType,
                };
                userDetails.forEach((userElement) => {
                    if (userElement.fcm_token) {
                        sendNotificationPush(
                            userElement.fcm_token,
                            pushData,
                            userElement.device_type,
                        );
                    }
                    if (userElement.web_fcm_token) {
                        sendNotificationPush(userElement.web_fcm_token, pushData, 'web');
                    }
                });
                pushData.users = userDetails.map((studentElement) => {
                    return { _id: studentElement._id, isViewed: false };
                });
                saveDiscussionNotification(pushData);
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};

// staff move next question
const moveNextQuestion = async ({ socketEventStaffId, socketEventStudentId, questions }) => {
    const question = questions.find((questionElement) => !questionElement.questionMoved);

    const statusData = {
        examOnLive: true,
        movedToNextQuestion: true,
        acceptQuestionResponse: question.acceptQuestionResponse,
        nextQuestionId: question ? question._id : undefined,
    };
    const sendSocketData = [
        { eventId: socketEventStudentId, data: JSON.stringify(statusData) },
        { eventId: socketEventStaffId, data: JSON.stringify(statusData) },
    ];

    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    return questions;
};

exports.quizStartByStaff = async (req, res) => {
    try {
        const {
            activityId,
            sessionId,
            scheduleIds,
            staffStartWithExam,
            questionId,
            time,
            courseAdmin,
            shuffleQuestionOrder,
        } = req.body;
        const { id: userId } = req.params;
        let updateData = { activityId };
        // if check activity already exists
        const activityData = await activitySchema.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
                $or: [
                    {
                        scheduleIds: { $exists: true, $ne: null },
                        sessionId: { $exists: true, $ne: null },
                    },
                ],
            },
            {
                scheduleIds: 1,
            },
        );

        // Need to enable this
        if (
            activityData &&
            activityData.scheduleIds &&
            activityData.scheduleIds.length &&
            !questionId
        )
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ALREADY_THIS_QUIZ_SCHEDULED'),
                null,
            );

        if (questionId) {
            updateData = { 'questions.$[i].questionMoved': true };
            if (courseAdmin === true) {
                updateData.courseAdmin = true;
            }
            const data = { $set: updateData };
            const activityUpdatedData = await activitySchema
                .findOneAndUpdate({ _id: convertToMongoObjectId(activityId) }, data, {
                    arrayFilters: [{ 'i._id': convertToMongoObjectId(questionId) }],
                    new: true,
                })
                .select('_id socketEventStudentId socketEventStaffId questions');
            await moveNextQuestion({
                socketEventStaffId: activityUpdatedData.socketEventStaffId,
                socketEventStudentId: activityUpdatedData.socketEventStudentId,
                questions: activityUpdatedData.questions,
            });
            updateData = { questionMoved: true };
            if (!activityUpdatedData)
                sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));
        } else {
            const currentTime = timestampNow();
            const staffEventId = 'staff_' + activityId + '_' + currentTime;
            const studentEventId = 'student_' + activityId + '_' + currentTime;

            let endTime;
            let seconds;
            if (staffStartWithExam === TIME && time !== 0) {
                endTime = currentTime + parseInt(time);
                endTime = new Date(endTime);
                seconds = endTime.getSeconds();
                endTime = new Date(
                    endTime.getFullYear() +
                        '-' +
                        (endTime.getMonth() + 1) +
                        '-' +
                        endTime.getDate() +
                        ' ' +
                        endTime.getHours() +
                        ':' +
                        endTime.getMinutes(),
                );
                endTime = new Date(endTime);
            }

            const getMergeSchedules = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleIds },
                },
                {
                    merge_with: 1,
                    merge_status: 1,
                    _id: 1,
                },
            );
            const mergedSessions = [];
            for (const getMergeSchedule of getMergeSchedules) {
                const { merge_status, merge_with } = getMergeSchedule;
                if (merge_status) {
                    merge_with.map((mergeWith) =>
                        mergedSessions.push(mergeWith.schedule_id.toString()),
                    );
                }
            }
            const courseScheduleIds = [...scheduleIds, ...mergedSessions];
            updateData = {
                ...updateData,
                status: STARTED,
                socketEventStaffId: staffEventId,
                socketEventStudentId: studentEventId,
                quizStartedBy: convertToMongoObjectId(userId),
                staffStartWithExam,
                scheduleIds: courseScheduleIds,
                sessionId,
                startTime: currentTime,
                ...(courseAdmin && { courseAdmin: true }),
                // if staff started exam with time based
                ...(staffStartWithExam === TIME && { setQuizTime: time }),
                ...(staffStartWithExam === TIME && time !== 0 && { endTime, seconds }),
                ...(shuffleQuestionOrder && { shuffleQuestionOrder }),
            };

            const data = { $set: updateData };
            const activityUpdatedData = await activitySchema
                .findOneAndUpdate(
                    {
                        _id: convertToMongoObjectId(activityId),
                    },
                    data,
                    {
                        new: true,
                    },
                )
                .populate({
                    path: 'courseId',
                    select: { course_code: 1, course_name: 1 },
                })
                .populate({
                    path: 'createdBy',
                    select: { name: 1 },
                })
                .populate({
                    path: '_program_id',
                    select: { name: 1 },
                })
                .lean();

            if (!activityUpdatedData)
                return sendResponseWithRequest(req, res, 200, false, req.t('UPDATE_ERROR'));

            updateData.startTime = activityUpdatedData.startTime;
            const activityMergedData = {
                ...updateData,
                ...activityUpdatedData,
            };

            if (activityUpdatedData)
                sendActivityStartNotification({ data: activityMergedData, userId });
        }

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('UPDATED_SUCCESSFULLY'),
            updateData,
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const sendActivityEndNotification = async ({ data, userId }) => {
    try {
        const {
            scheduleIds,
            sessionFlowIds,
            _id,
            socketEventStudentId,
            socketEventStaffId,
            name,
            status,
            quizType,
            startTime,
            staffStartWithExam,
            questions,
            courseId,
            createdBy,
            courseAdmin,
            level_no,
            _program_id,
            _institution_calendar_id,
            students,
            correctionType,
            year_no,
        } = data;

        if (scheduleIds && scheduleIds.length > 0) {
            const scheduleConvertIds = scheduleIds.map((scheduleId) =>
                convertToMongoObjectId(scheduleId),
            );
            const courseScheduleData = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleConvertIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'students._id': 1,
                    'staffs._staff_id': 1,
                    merge_status: 1,
                    merge_with: 1,
                    'session._session_id': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.group_id': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                },
            );

            const mergedStudents = new Set();
            const mergedStaffs = new Set();
            const mergedSession = new Set();
            const mergedStudentGroups = new Set();
            for (const courseScheduleElement of courseScheduleData) {
                const { students, staffs, merge_status, merge_with, session, student_groups } =
                    courseScheduleElement;
                let scheduleStudents = students;
                let scheduleStaffs = staffs;
                let scheduleSession = session;
                let scheduleStudentGroups = student_groups;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const mergeScheduleData = await courseScheduledSchema.find(
                            {
                                _id: { $in: scheduleIds },
                                isDeleted: false,
                                isActive: true,
                            },
                            {
                                'students._id': 1,
                                'staffs._staff_id': 1,
                                'session._session_id': 1,
                                'session.delivery_symbol': 1,
                                'session.delivery_no': 1,
                                'student_groups.group_id': 1,
                                'student_groups.group_name': 1,
                                'student_groups.session_group.group_name': 1,
                            },
                        );
                        scheduleStudents = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.students,
                        );
                        scheduleStaffs = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.staffs,
                        );
                        scheduleSession = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.session,
                        );
                        scheduleStudentGroups = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.student_groups,
                        );
                    }
                } else {
                    scheduleSession = Array.isArray(scheduleSession)
                        ? scheduleSession
                        : [scheduleSession];
                }
                scheduleStudents.forEach((studentElement) =>
                    mergedStudents.add(JSON.stringify(studentElement)),
                );
                scheduleStaffs.forEach((staffElement) =>
                    mergedStaffs.add(JSON.stringify(staffElement)),
                );
                scheduleSession.forEach((sessionElement) =>
                    mergedSession.add(JSON.stringify(sessionElement)),
                );
                scheduleStudentGroups.forEach((groupsElement) =>
                    mergedStudentGroups.add(JSON.stringify(groupsElement)),
                );
            }

            const uniqueStudents = [...mergedStudents].map((studentElement) =>
                JSON.parse(studentElement),
            );
            const uniqueStaffs = [...mergedStaffs].map((staffElement) => JSON.parse(staffElement));
            const uniqueSessions = [...mergedSession]
                .map((sessionElement) => JSON.parse(sessionElement))
                .filter((sessionElement) => Object.keys(sessionElement).length !== 0);
            const uniqueStudentGroups = [...mergedStudentGroups].map((groupsElement) =>
                JSON.parse(groupsElement),
            );

            const sessionFlowIdsArray = sessionFlowIds
                .map((sessionElement) => {
                    const matchingSession = uniqueSessions.find(
                        (uniqueSession) =>
                            uniqueSession._session_id.toString() === sessionElement._id.toString(),
                    );
                    return matchingSession
                        ? { ...matchingSession, type: sessionElement.type }
                        : null;
                })
                .filter(Boolean);

            const studentGroupName = studentGroupByGroupName(uniqueStudentGroups)
                .map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                })
                .join(', ');

            const totalStudentIds = [
                ...uniqueStudents.map((studentElement) => studentElement._id),
                ...uniqueStaffs
                    .filter(
                        (staffElement) => staffElement._staff_id.toString() !== userId.toString(),
                    )
                    .map((staffElement) => staffElement._staff_id),
            ];

            const userDetails = await userSchema.find(
                {
                    _id: { $in: totalStudentIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    device_type: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    user_type: 1,
                },
            );

            // send socket to staff and student examOnLive false
            const sendSocketData = [];
            sendSocketData.push(
                {
                    eventId: socketEventStudentId,
                    data: JSON.stringify({ examOnLive: false }),
                },
                {
                    eventId: socketEventStaffId,
                    data: JSON.stringify({
                        examOnLive: false,
                    }),
                },
            );

            // Need to enable this
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);

            const isNewActivity = !!(correctionType && correctionType !== '');
            const commonSocketData = {
                name,
                quizType,
                status,
                startTime,
                totalQuestionCount: questions.length,
                courseId,
                createdBy: {
                    name: createdBy.name,
                },
                _id,
                courseAdmin,
                _institution_calendar_id,
                type: 'normal',
                isNewActivity,
                correctionType,
            };

            const getStudentAnsweredCount = (studentId) => {
                const studentAnsweredQuestions = students.find(
                    (studentElement) =>
                        studentElement._studentId.toString() === studentId.toString(),
                );
                return studentAnsweredQuestions !== undefined
                    ? studentAnsweredQuestions.questions.length
                    : 0;
            };
            // send socket to student
            const studentDetails = userDetails.filter(
                (studentElement) => studentElement.user_type === 'student',
            );
            if (studentDetails && studentDetails.length) {
                const studentSocketData = {
                    ...commonSocketData,
                    correctionStatus: isNewActivity ? 'NOT YET PUBLISHED' : '',
                };
                const sendSocketData = [];
                for (const user of studentDetails) {
                    const eventId = user._id;
                    sendSocketData.push({
                        eventId,
                        data: JSON.stringify({
                            examOnLive: false,
                            notificationCount: 1,
                            activity: {
                                ...studentSocketData,
                                answeredCount: getStudentAnsweredCount(eventId),
                            },
                        }),
                    });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }

            // send socket to staff
            const staffDetails = userDetails.filter(
                (staffElement) => staffElement.user_type === 'staff',
            );
            if (staffDetails && staffDetails.length) {
                const staffSocketData = {
                    ...commonSocketData,
                    studentGroupName,
                    sessionFlowIds: sessionFlowIdsArray,
                    totalStudentAnsweredCount: students.length,
                    totalStudentCount: studentDetails.length,
                    correctionStatus: isNewActivity ? 'MAKE CORRECTION' : '',
                };
                const sendSocketData = [];
                for (const user of staffDetails) {
                    const eventId = user._id;
                    sendSocketData.push({
                        eventId,
                        data: JSON.stringify({
                            examOnLive: false,
                            notificationCount: 1,
                            activity: staffSocketData,
                        }),
                    });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }

            // push notification
            if (userDetails.length) {
                const title = `${quizType} Ended`;
                let message = name + '\n';
                message += staffStartWithExam === 'TIME' ? 'Standard' : 'One by one';
                message += ' ' + quizType;
                message +=
                    '\n' + _program_id.name + ' • ' + level_no + ' • ' + courseId.course_name;
                const pushData = {
                    Title: title,
                    Body: message,
                    title,
                    description: message,
                    ClickAction: 'activity_end',
                    buttonAction: 'activity_end',
                    //ScheduleId: scheduleIds[0],
                    //Session: schedule.session ? session._session_id : undefined,
                    programId: _program_id._id,
                    institutionCalendarId: _institution_calendar_id,
                    yearNo: year_no,
                    levelNo: level_no,
                    //term,
                    //mergeStatus: merge_status,
                    //mergeType: type,
                    CourseId: courseId._id,
                    activityId: _id,
                    staffStartWithExam,
                    //_id: scheduleId,
                    courseId: courseId._id,
                    notificationType: 'activity_end',
                    // rotation,
                    // rotation_count,
                    type: 'activity',
                    isNewActivity,
                    correctionType,
                };
                userDetails.forEach((userElement) => {
                    if (userElement.fcm_token) {
                        sendNotificationPush(
                            userElement.fcm_token,
                            pushData,
                            userElement.device_type,
                        );
                    }
                    if (userElement.web_fcm_token) {
                        sendNotificationPush(userElement.web_fcm_token, pushData, 'web');
                    }
                });
                pushData.users = userDetails.map((studentElement) => {
                    return { _id: studentElement._id, isViewed: false };
                });
                saveDiscussionNotification(pushData);
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};

// quiz stop by staff
exports.quizStopByStaff = async (req, res) => {
    try {
        const {
            body: { activityId },
            params: { id: userId },
        } = req;

        const { status } = await activitySchema.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
            },
            {
                status: 1,
            },
        );

        // Need to enable this
        if (status === COMPLETED) {
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('QUIZ_ALREADY_STOPPED'),
                null,
            );
        }

        const updateData = {
            quizStopBy: convertToMongoObjectId(userId),
            endTime: timestampNow(),
            status: COMPLETED,
        };

        const data = { $set: updateData };
        const activityUpdatedData = await activitySchema
            .findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(activityId),
                },
                data,
                {
                    new: true,
                    projection: {
                        socketEventStudentId: 1,
                        socketEventStaffId: 1,
                        scheduleIds: 1,
                        status: 1,
                        _institution_calendar_id: 1,
                        sessionFlowIds: 1,
                        name: 1,
                        quizType: 1,
                        startTime: 1,
                        staffStartWithExam: 1,
                        questions: 1,
                        courseId: 1,
                        createdBy: 1,
                        courseAdmin: 1,
                        level_no: 1,
                        _program_id: 1,
                        students: 1,
                        correctionType: 1,
                        year_no: 1,
                        term: 1,
                        rotation_count: 1,
                    },
                },
            )
            .populate({
                path: 'courseId',
                select: { course_code: 1, course_name: 1 },
            })
            .populate({
                path: 'createdBy',
                select: { name: 1 },
            })
            .populate({
                path: '_program_id',
                select: { name: 1 },
            })
            .lean();

        if (!activityUpdatedData)
            return sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));

        const activityMergedData = {
            ...updateData,
            ...activityUpdatedData,
        };
        activityReportFromCloudFunction({
            institutionCalendarId: activityUpdatedData?._institution_calendar_id,
            programId: activityUpdatedData?._program_id?._id,
            courseId: activityUpdatedData?.courseId?._id,
            levelNo: activityUpdatedData?.level_no,
            term: activityUpdatedData?.term,
            rotation_count: activityUpdatedData?.rotation_count,
            isClear: true,
        });

        if (activityUpdatedData) sendActivityEndNotification({ data: activityMergedData, userId });

        // // update cache data
        // await clearItem('getAllCompletedActivities');
        // await allCompletedActivities(_institution_calendar_id);

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('UPDATED_SUCCESSFULLY'),
            updateData,
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const removeTags = (textAnswerValue) => {
    if (!textAnswerValue) return '';
    return textAnswerValue
        .toString()
        .replace(/(<([^>]+)>)/gi, ' ')
        .replace(/&nbsp;/g, ' ');
};

const checkSAQAnswer = ({ answerMatchingType, answerTextVariant, textAnswer }) => {
    const exactMatch = answerMatchingType === 'exactly';
    if (exactMatch) {
        const matchedAnswerVariantMark = answerTextVariant
            .filter((answerElement) => {
                const studentAnswer =
                    textAnswer !== null ? removeTags(textAnswer.toLowerCase()).split(' ') : '';
                const firstVariantAnswer =
                    answerElement &&
                    answerElement.firstVariant &&
                    answerElement.firstVariant !== null
                        ? answerElement.firstVariant.toLowerCase().split(' ')
                        : '';
                return firstVariantAnswer
                    ? firstVariantAnswer
                          .filter((variantElement) => variantElement !== '')
                          .every((variantElement) =>
                              studentAnswer
                                  .filter((variantElement) => variantElement !== '')
                                  .some(
                                      (studentAnswerElement) =>
                                          studentAnswerElement.toString() ===
                                          variantElement.toString(),
                                  ),
                          )
                    : false;
            })
            .reduce((totalMarks, currentElement) => totalMarks + currentElement.mark, 0);
        return matchedAnswerVariantMark;
    }
    const matchedAnswerVariantMark = answerTextVariant
        .filter((answerElement) => {
            const studentAnswer =
                textAnswer !== null
                    ? removeTags(textAnswer.toLowerCase())
                          .split(' ')
                          .filter((variantElement) => variantElement !== '')
                    : '';
            const firstVariantAnswer =
                answerElement && answerElement.firstVariant && answerElement.firstVariant !== null
                    ? answerElement.firstVariant
                          .toLowerCase()
                          .split(' ')
                          .filter((variantElement) => variantElement !== '')
                    : [];
            const secondVariantAnswer =
                answerElement && answerElement.secondVariant && answerElement.secondVariant !== null
                    ? answerElement.secondVariant
                          .toLowerCase()
                          .split(' ')
                          .filter((variantElement) => variantElement !== '')
                    : [];
            const checkVariant =
                (firstVariantAnswer
                    ? firstVariantAnswer.every((variantElement) =>
                          studentAnswer.some(
                              (studentAnswerElement) =>
                                  studentAnswerElement.toString() === variantElement.toString(),
                          ),
                      )
                    : false) ||
                (secondVariantAnswer
                    ? secondVariantAnswer.every((variantElement) =>
                          studentAnswer.some(
                              (studentAnswerElement) =>
                                  studentAnswerElement.toString() === variantElement.toString(),
                          ),
                      )
                    : false);
            return checkVariant;
        })
        .reduce((totalMarks, currentElement) => totalMarks + currentElement.mark, 0);
    return matchedAnswerVariantMark;
};

const getAnswerResponded = ({
    questionId,
    questionType,
    questionOptions,
    students,
    benchMark,
    correctionType,
    conduct,
    answerMatchingType = '',
    answerTextVariant = [],
    matchingOptions = [],
}) => {
    switch (questionType) {
        case 'SCQ':
        case 'TF':
        case 'MCQ': {
            const correctOptionsArray = questionOptions
                .filter((questionElement) => questionElement.answer === true)
                .map((questionElement) => questionElement._id.toString());

            const studentAttendedQuestion = students
                .map((studentElement) => {
                    const selectedQuestionData = studentElement.questions
                        .filter((questionElement) => {
                            return questionElement._questionId.toString() === questionId.toString();
                        })
                        .reduce((optionElement, currentStudentElement) => {
                            return {
                                options: [
                                    ...optionElement,
                                    ...currentStudentElement._optionIdArray,
                                ],
                                staffCorrectedAnswer: currentStudentElement.staffCorrectedAnswer,
                            };
                        }, []);
                    return {
                        _studentId: studentElement._studentId,
                        selectedQuestionOptions: selectedQuestionData.options,
                        staffCorrectedAnswer: selectedQuestionData.staffCorrectedAnswer,
                    };
                })
                .filter(
                    (optionElement) =>
                        optionElement &&
                        optionElement.selectedQuestionOptions &&
                        optionElement.selectedQuestionOptions.length > 0,
                );
            const checkStudentCorrectlyAnswered = studentAttendedQuestion.filter(
                (questionElement) => {
                    if (
                        questionElement &&
                        questionElement.staffCorrectedAnswer &&
                        questionElement.staffCorrectedAnswer !== ''
                    )
                        return questionElement.staffCorrectedAnswer === 'correct';

                    if (
                        questionType === 'MCQ' &&
                        correctOptionsArray.length < questionElement.selectedQuestionOptions.length
                    )
                        return false;
                    const selectedQuestion = questionElement.selectedQuestionOptions.map(
                        (optionElement) => optionElement.toString(),
                    );
                    return correctOptionsArray.every((correctOptionElement) =>
                        selectedQuestion.includes(correctOptionElement),
                    );
                },
            );
            return {
                correct: checkStudentCorrectlyAnswered.length,
                wrong: studentAttendedQuestion.length - checkStudentCorrectlyAnswered.length,
            };
        }
        case 'SAQ': {
            const studentAttendedQuestion = students.map((studentElement) => {
                const selectedQuestion = studentElement.questions.find((questionElement) => {
                    return questionElement._questionId.toString() === questionId.toString();
                });
                if (selectedQuestion) {
                    return {
                        _studentId: studentElement._studentId,
                        _questionId: selectedQuestion._questionId,
                        textAnswer: selectedQuestion.textAnswer,
                        marks: selectedQuestion.marks,
                        staffCorrectedAnswer: selectedQuestion.staffCorrectedAnswer,
                    };
                }
            });

            const checkStudentCorrectlyAnswered = studentAttendedQuestion.filter(
                (questionElement) => {
                    return correctionType === 'system' && questionElement && questionElement.marks
                        ? questionElement.marks >= benchMark
                        : correctionType === 'manual' && conduct
                        ? checkSAQAnswer({
                              answerMatchingType,
                              answerTextVariant,
                              textAnswer:
                                  questionElement && questionElement.textAnswer
                                      ? questionElement.textAnswer
                                      : '',
                          }) >= benchMark
                        : correctionType === 'manual' &&
                          !conduct &&
                          questionElement &&
                          questionElement.marks === undefined
                        ? checkSAQAnswer({
                              answerMatchingType,
                              answerTextVariant,
                              textAnswer:
                                  questionElement && questionElement.textAnswer
                                      ? questionElement.textAnswer
                                      : '',
                          }) >= benchMark
                        : questionElement && questionElement.staffCorrectedAnswer === 'correct';
                },
            );

            return {
                correct: checkStudentCorrectlyAnswered.length,
                wrong: studentAttendedQuestion.length - checkStudentCorrectlyAnswered.length,
            };
        }
        case 'MQ': {
            const studentAttendedQuestion = students
                .map((studentElement) => {
                    const selectedQuestion = studentElement.questions.find((questionElement) => {
                        return questionElement._questionId.toString() === questionId.toString();
                    });
                    if (selectedQuestion) {
                        return {
                            _studentId: studentElement._studentId,
                            _questionId: selectedQuestion._questionId,
                            matchingOptions: selectedQuestion.matchingOptions,
                            marks: selectedQuestion.marks,
                            staffCorrectedAnswer: selectedQuestion.staffCorrectedAnswer,
                        };
                    }
                })
                .filter(
                    (optionElement) =>
                        optionElement &&
                        optionElement.matchingOptions &&
                        optionElement.matchingOptions.length > 0,
                );
            const correctOptionsArray = matchingOptions.map((optionElement) =>
                optionElement.answer.toString(),
            );
            const checkStudentCorrectlyAnswered = studentAttendedQuestion.filter(
                (questionElement) => {
                    if (
                        questionElement &&
                        questionElement.staffCorrectedAnswer &&
                        questionElement.staffCorrectedAnswer !== ''
                    )
                        return questionElement.staffCorrectedAnswer === 'correct';

                    const matchingOptions = questionElement.matchingOptions;
                    return matchingOptions.every(
                        (correctOptionElement, optionIndex) =>
                            correctOptionElement ===
                            (correctOptionsArray && correctOptionsArray[optionIndex]
                                ? correctOptionsArray[optionIndex]
                                : ''),
                    );
                },
            );
            return {
                correct: checkStudentCorrectlyAnswered.length,
                wrong: studentAttendedQuestion.length - checkStudentCorrectlyAnswered.length,
            };
        }
        default:
            return {
                correct: 0,
                wrong: 0,
            };
    }
};

const getStudentMatchingData = ({ matchingOptions, students, questionId }) => {
    if (matchingOptions && matchingOptions.length) {
        const getStudentMatchingOptions = ({ matchingOptionElement, questionId }) => {
            const studentCount = students.reduce((count, student) => {
                const studentAnswer = student.questions
                    .filter(
                        (studentElement) =>
                            studentElement._questionId.toString() === questionId.toString(),
                    )
                    .find((studentElement) => {
                        return (
                            studentElement.matchingOptions &&
                            studentElement.matchingOptions.length &&
                            studentElement.matchingOptions[matchingOptionElement.order - 1] ===
                                matchingOptionElement.answer
                        );
                    });
                if (studentAnswer) {
                    return count + 1;
                }
                return count;
            }, 0);

            const percentage =
                ((studentCount / students.length) * 100).toFixed() !== 'NaN'
                    ? parseFloat((studentCount / students.length) * 100).toFixed(2)
                    : '0';
            return {
                studentCount,
                percentage,
            };
        };

        return matchingOptions.map((matchingOptionElement) => {
            const studentAnsweredOptions = getStudentMatchingOptions({
                matchingOptionElement,
                questionId,
            });
            return {
                ...matchingOptionElement,
                studentAnsweredCount: studentAnsweredOptions.studentCount,
                totalStudentAnswered: students.length,
                percentage: studentAnsweredOptions.percentage,
            };
        });
    }
    return [];
};

const getStudentTimeBasedResult = async ({ activityId, questionId }) => {
    const { students, questions, correctionType } = await activitySchema
        .findOne(
            { _id: convertToMongoObjectId(activityId) },
            { students: 1, questions: 1, correctionType: 1 },
        )
        .populate({ path: 'students._studentId', select: { name: 1, user_id: 1 } })
        .lean();

    const questionIds = questions
        .sort((a, b) => {
            return a.order - b.order;
        })
        .map((questionElement) => questionElement._id)
        .filter((questionElement) =>
            questionId !== null
                ? questionElement._id.toString() === questionId.toString()
                : questionElement,
        );
    const questionEntry = await questionSchema
        .find(
            {
                _id: { $in: questionIds },
            },
            {
                text: 1,
                'options._id': 1,
                'options.answer': 1,
                'options.text': 1,
                'options.optionText': 1,
                surveyQuestionType: 1,
                maxCharacterLimit: 1,
                questionType: 1,
                benchMark: 1,
                answerMatchingType: 1,
                answerTextVariant: 1,
                matchingOptions: 1,
            },
        )
        .lean();
    let correctlyResponded = 0;
    let wronglyResponded = 0;
    if (questionEntry && questionEntry.length > 0) {
        const studentQuestion = questionEntry.map((questionElement) => {
            const {
                _id,
                text,
                options,
                surveyQuestionType,
                maxCharacterLimit,
                questionType,
                benchMark,
                answerMatchingType,
                answerTextVariant,
                matchingOptions,
            } = questionElement;

            const studentOptions = options.map((optionElement) => {
                const { _id: optionId, text: optionText, answer } = optionElement;
                const studentCount = students.reduce((count, student) => {
                    const studentAnswer = student.questions.find((studentElement) => {
                        return (
                            studentElement._questionId.toString() === _id.toString() &&
                            (studentElement._optionIdArray && studentElement._optionIdArray.length
                                ? studentElement._optionIdArray
                                      .map((idElement) => idElement.toString())
                                      .includes(optionId.toString())
                                : (studentElement._optionId &&
                                      studentElement._optionId.toString() ===
                                          optionId.toString()) ||
                                  (surveyQuestionType === OPEN_ENDED &&
                                      !studentElement._optionId &&
                                      studentElement.textAnswer !== undefined))
                        );
                    });
                    if (studentAnswer) {
                        return count + 1;
                    }
                    return count;
                }, 0);

                const percentage =
                    ((studentCount / students.length) * 100).toFixed() !== 'NaN'
                        ? parseFloat((studentCount / students.length) * 100).toFixed(2)
                        : '0';

                return {
                    _id: optionId,
                    text: optionText,
                    answer,
                    studentAnsweredCount: studentCount,
                    totalStudentAnswered: students.length,
                    percentage,
                };
            });

            const studentMatchingOptions = getStudentMatchingData({
                matchingOptions,
                students,
                questionId: _id,
            });

            const studentTextAnswer = [];
            if (surveyQuestionType === OPEN_ENDED) {
                students.forEach((student) => {
                    const matchedQuestion = student.questions.find(
                        (q) =>
                            !q._optionId &&
                            q.textAnswer !== undefined &&
                            q._questionId.toString() === _id.toString(),
                    );
                    if (matchedQuestion) {
                        studentTextAnswer.push({
                            studentId: student._studentId,
                            _questionId: matchedQuestion._questionId,
                            textAnswer: matchedQuestion.textAnswer,
                        });
                    }
                });
            }

            const studentAttendQuestionCount = students.reduce((count, student) => {
                if (
                    student.questions.find(
                        (q) => q._questionId && q._questionId.toString() === _id.toString(),
                    )
                ) {
                    return count + 1;
                }
                return count;
            }, 0);

            if (correctionType) {
                const answerRespondedCount = getAnswerResponded({
                    questionId: _id,
                    questionType,
                    questionOptions: options,
                    students,
                    benchMark,
                    correctionType,
                    conduct: true,
                    answerMatchingType,
                    answerTextVariant,
                    matchingOptions,
                });
                correctlyResponded = answerRespondedCount.correct;
                wronglyResponded = answerRespondedCount.wrong;
            }

            return {
                _id,
                text,
                options: studentOptions,
                matchingOptions: studentMatchingOptions,
                textAnswers: studentTextAnswer,
                studentAnsweredCount: studentAttendQuestionCount,
                surveyQuestionType,
                maxCharacterLimit,
                correctlyResponded,
                wronglyResponded,
            };
        });
        return {
            activityId,
            totalAnswered: students.length,
            questions: studentQuestion,
        };
    }
    return {
        activityId,
        totalAnswered: 0,
        questions: [],
    };
};

const calculateStudentMarks = ({
    question,
    studentAnsweredOptions,
    textAnswer,
    studentMatchingOptions,
}) => {
    const { questionType, mark, options, answerMatchingType, answerTextVariant, matchingOptions } =
        question;
    if (!studentAnsweredOptions) return 0;
    switch (questionType) {
        case 'SCQ':
        case 'TF':
        case 'MCQ': {
            const actualOptionIds = options
                .filter((optionElement) => optionElement.answer)
                .map((optionElement) => optionElement._id.toString());
            const studentAnsweredOptionIds = studentAnsweredOptions.map((optionElement) =>
                optionElement.toString(),
            );
            if (questionType === 'MCQ' && actualOptionIds.length < studentAnsweredOptionIds.length)
                return 0;
            const optionPresent = actualOptionIds.every((actualOptionElement) =>
                studentAnsweredOptionIds.includes(actualOptionElement),
            );
            return optionPresent ? (mark === 0 ? 1 : mark) : 0;
        }
        case 'SAQ': {
            return checkSAQAnswer({ answerMatchingType, answerTextVariant, textAnswer });
        }
        case 'MQ': {
            const checkAnswer = matchingOptions.every((optionElement, optionElementIndex) => {
                return (
                    optionElement.answer ===
                    (studentMatchingOptions && studentMatchingOptions[optionElementIndex]
                        ? studentMatchingOptions[optionElementIndex]
                        : '')
                );
            });
            return checkAnswer ? mark : 0;
        }
        default:
            return 0;
    }
};

// question answer update by student
exports.questionAnsweredByStudent = async (req, res) => {
    try {
        const {
            body: { activityId, questions: studentQuestions },
            params: { id: userId },
        } = req;
        //to find duplicate answers
        let questions = studentQuestions.reduce((acc, current) => {
            const x = acc.find(
                (questionElement) =>
                    questionElement._questionId &&
                    questionElement._questionId.toString() === current._questionId.toString(),
            );
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);

        let updateData;
        let arrayFilters = {};
        const activity = await activitySchema.findOne(
            {
                _id: convertToMongoObjectId(activityId),
            },
            {
                students: 1,
                socketEventStaffId: 1,
                staffStartWithExam: 1,
                correctionType: 1,
            },
        );
        if (activity) {
            const { students, staffStartWithExam, socketEventStaffId, _id, correctionType } =
                activity;
            const isSystemCorrection = correctionType === 'system';
            if (isSystemCorrection) {
                const questionIds = questions.map((questionElement) => questionElement._questionId);
                const questionIdMarks = await questionSchema.find(
                    { _id: { $in: questionIds } },
                    {
                        mark: 1,
                        questionType: 1,
                        options: 1,
                        answerMatchingType: 1,
                        answerTextVariant: 1,
                        matchingOptions: 1,
                    },
                );
                function getStudentMarks({
                    questionId,
                    option,
                    textAnswer = '',
                    studentMatchingOptions,
                }) {
                    if (!questionId) return 0;
                    const filteredQuestion = questionIdMarks.find(
                        (markElement) => markElement._id.toString() === questionId.toString(),
                    );
                    if (filteredQuestion) {
                        return calculateStudentMarks({
                            question: filteredQuestion,
                            studentAnsweredOptions: option,
                            textAnswer,
                            studentMatchingOptions,
                        });
                    }
                    return 0;
                }

                questions = questions.map((questionElement) => {
                    return {
                        ...questionElement,
                        marks: getStudentMarks({
                            questionId: questionElement._questionId,
                            option:
                                questionElement._optionIdArray &&
                                questionElement._optionIdArray.length
                                    ? questionElement._optionIdArray
                                    : [],
                            textAnswer: questionElement.textAnswer && questionElement.textAnswer,
                            studentMatchingOptions:
                                questionElement.matchingOptions && questionElement.matchingOptions,
                        }),
                    };
                });
            }

            const hasStudentPresent = students.some(
                (studentElement) => studentElement._studentId.toString() === userId.toString(),
            );
            if (hasStudentPresent) {
                const studentAnsweredQuestions = students.find(
                    (activityStudent) =>
                        activityStudent._studentId.toString() === userId.toString(),
                );
                if (studentAnsweredQuestions) {
                    let answeredQuestions = studentAnsweredQuestions.questions;
                    answeredQuestions = answeredQuestions.map((answeredQuestion) =>
                        answeredQuestion._questionId.toString(),
                    );
                    questions = questions.filter(
                        (questionElement) =>
                            !answeredQuestions.includes(questionElement._questionId.toString()),
                    );
                }

                updateData = { 'students.$[j].questions': { $each: questions } };
                arrayFilters = [{ 'j._studentId': convertToMongoObjectId(userId) }];
                await activitySchema.updateOne(
                    { _id: convertToMongoObjectId(activityId) },
                    { $push: updateData },
                    { arrayFilters },
                );
            } else {
                updateData = {
                    students: {
                        _studentId: convertToMongoObjectId(userId),
                        questions,
                    },
                };
                await activitySchema.updateOne(
                    { _id: convertToMongoObjectId(activityId) },
                    { $push: updateData },
                );
            }

            const staffResult = {
                examOnLive: true,
                socketType:
                    staffStartWithExam === TIME
                        ? 'activity_answer_standard'
                        : 'activity_answer_one_by_one',
                data: await getStudentTimeBasedResult({
                    activityId: _id,
                    questionId:
                        staffStartWithExam === TIME
                            ? null
                            : staffStartWithExam === ONE_BY_ONE
                            ? studentQuestions &&
                              studentQuestions[0] &&
                              studentQuestions[0]._questionId
                                ? studentQuestions[0]._questionId
                                : null
                            : null,
                }),
            };
            const sendSocketData = [
                { eventId: socketEventStaffId, data: JSON.stringify(staffResult) },
            ];
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        }

        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

const acceptQuestionResponseOnOff = async ({ acceptQuestion, socketEventStudentId, questions }) => {
    const question = questions.find((questionEntry) => !questionEntry.questionMoved);
    const studentStatus = {
        examOnLive: true,
        movedToNextQuestion: false,
        acceptQuestionResponse: acceptQuestion,
        nextQuestionId: '',
    };
    if (question) {
        studentStatus.nextQuestionId = question._id;
    }
    const sendSocketData = [{ eventId: socketEventStudentId, data: JSON.stringify(studentStatus) }];
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
};

// question answer update by student
exports.acceptQuestionResponse = async (req, res) => {
    try {
        const {
            body: { activityId, questionId, acceptQuestion },
            params: { id: userId },
        } = req;
        let updateData;
        if (userId && questionId) {
            updateData = { 'questions.$[i].acceptQuestionResponse': acceptQuestion };
            updateData = { $set: updateData };
            const activityUpdatedData = await activitySchema
                .findByIdAndUpdate({ _id: convertToMongoObjectId(activityId) }, updateData, {
                    arrayFilters: [{ 'i._id': convertToMongoObjectId(questionId) }],
                })
                .select('_id  socketEventStudentId questions');
            await acceptQuestionResponseOnOff({
                acceptQuestion,
                socketEventStudentId: activityUpdatedData.socketEventStudentId,
                questions: activityUpdatedData.questions,
            });
            if (!activityUpdatedData)
                return sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));
        }
        const data = { acceptQuestionResponse: acceptQuestion };
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'), data);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

// format question with attachment link
const formatQuestionsWithAttachmentLink = async ({ questions, taxonomyIds = [], sloIds = [] }) => {
    const formatQuestions = [];
    // if (questions.length > 0) {
    let taxonomyArray = [];
    if (taxonomyIds && taxonomyIds.length) {
        taxonomyArray = await taxonomySchema.find(
            { _id: { $in: taxonomyIds } },
            { name: 1, _id: 1 },
        );
    }

    function getTaxonomyData(ids) {
        const updatedData =
            taxonomyArray.length > 0
                ? ids.map((taxonomyElement) =>
                      taxonomyArray.find(
                          (idElement) => idElement._id.toString() === taxonomyElement.toString(),
                      ),
                  )
                : [];
        return updatedData;
    }

    let sloDetailList = sloIds;
    if (sloIds && sloIds.length) {
        const sloDetails = await getClosBySloIds(sloIds);
        const cloDetails = await getClos(sloIds);
        sloDetailList = sloIds
            .map((sloIdElement) => {
                const sloList = sloDetails.find((sloDetailElement) =>
                    sloDetailElement.slos.find((slo) => slo.slo_id === sloIdElement.toString()),
                );
                let cloList;
                if (cloDetails.length) {
                    cloList = cloDetails.find(
                        (cloDetailElement) =>
                            cloDetailElement._id.toString() === sloIdElement.toString(),
                    );
                }
                let sloIdDetail;
                if (sloList) {
                    sloIdDetail = sloList.slos.find(
                        (sloElement) => sloElement.slo_id === sloIdElement.toString(),
                    );
                    sloIdDetail._id = sloIdDetail.slo_id;
                    sloIdDetail.type = DS_SLO_KEY;
                }
                if (!sloIdDetail && cloList) {
                    sloIdDetail = {
                        _id: cloList._id,
                        name: cloList.name,
                        type: DS_CLO_KEY,
                        no: cloList.no,
                    };
                }
                return sloIdDetail;
            })
            .filter((sloElement) => sloElement);
    }

    const getSloIdsData = (sloIds) => {
        return sloDetailList.length > 0
            ? sloIds.map((sloElement) =>
                  sloDetailList.find(
                      (detailElement) => detailElement._id.toString() === sloElement.toString(),
                  ),
              )
            : [];
    };

    for (const question of questions) {
        if (question) {
            const { attachments, options, sloIds, taxonomyIds, _activityId } = question;
            const questionAttachments = [];
            // get attachments unsigned url
            if (attachments && attachments.length > 0) {
                for (const attachment of attachments) {
                    const { size, _id: attachmentId, link } = attachment;
                    let unsignedUrl;
                    if (link) {
                        const formattedActivityId = getActivityIdFromUrl({ link, _activityId });
                        unsignedUrl = await getUnsignedUrl(link, formattedActivityId);
                    }
                    questionAttachments.push({ size, _id: attachmentId, link: unsignedUrl });
                }
                question.attachments = questionAttachments;
            }
            // get option attachments unsigned url
            const questionOptionWithAttachments = [];
            if (options && options.length > 0) {
                for (const option of options) {
                    const { attachments: optionAttachments } = option;
                    const questionOptionAttachments = [];
                    for (const optionAttachment of optionAttachments) {
                        const {
                            size: optionAttachmentSize,
                            _id: optionAttachmentId,
                            link,
                        } = optionAttachment;
                        let unsignedUrl;
                        if (link) {
                            const formattedActivityId = getActivityIdFromUrl({ link, _activityId });
                            unsignedUrl = await getUnsignedUrl(link, formattedActivityId);
                        }
                        questionOptionAttachments.push({
                            size: optionAttachmentSize,
                            _id: optionAttachmentId,
                            link: unsignedUrl,
                        });
                    }

                    option.attachments = questionOptionAttachments;
                    questionOptionWithAttachments.push(option);
                }
                question.options = questionOptionWithAttachments;
            }

            let sloDetailList = sloIds;
            if (sloIds && sloIds.length) {
                sloDetailList = getSloIdsData(sloIds);
            }
            let taxonomy = taxonomyIds;
            if (taxonomyIds && taxonomyIds.length) {
                // taxonomy
                const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
                taxonomy = getTaxonomyData(taxonomyIdEntry);
            }
            formatQuestions.push({
                ...question,
                sloIds: sloDetailList,
                taxonomyIds: taxonomy,
                attachments: questionAttachments,
                options: questionOptionWithAttachments,
            });
        }
    }
    return formatQuestions;
};

const getStaffTimeBasedResult = async ({ students, questions, correctionType }) => {
    const questionIds = questions
        .sort((a, b) => {
            return a.order - b.order;
        })
        .map((questionElement) => questionElement._id);
    const questionEntry = await questionSchema
        .find(
            {
                _id: { $in: questionIds },
            },
            {
                text: 1,
                options: 1,
                _activityId: 1,
                questionType: 1,
                attachments: 1,
                feedback: 1,
                sloIds: 1,
                taxonomyIds: 1,
                sessionId: 1,
                surveyQuestionType: 1,
                maxCharacterLimit: 1,
                mark: 1,
                mandatory: 1,
                description: 1,
                benchMark: 1,
                answerMatchingType: 1,
                answerTextVariant: 1,
                matchingOptions: 1,
            },
        )
        .lean();

    const sessionIds = questionEntry.map((questionElement) => questionElement.sessionId);
    const courseSchedules = await courseScheduledSchema.find(
        {
            'session._session_id': { $in: sessionIds },
            isDeleted: false,
            isActive: true,
        },
        {
            'session._session_id': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
        },
    );

    let taxonomyIdsArrayGroup = [];
    let sloIdsArrayGroup = [];
    let correctlyResponded = 0;
    let wronglyResponded = 0;
    if (questionEntry && questionEntry.length > 0) {
        let studentQuestion = questionEntry.map((questionElement) => {
            const activityQuestion = questions.find(
                (questionEntryElement) =>
                    questionEntryElement._id.toString() === questionElement._id.toString(),
            );
            const {
                _id,
                options,
                sloIds,
                taxonomyIds,
                sessionId,
                surveyQuestionType,
                questionType,
                benchMark,
                answerMatchingType,
                answerTextVariant,
                matchingOptions,
            } = questionElement;
            taxonomyIdsArrayGroup = [...taxonomyIdsArrayGroup, ...taxonomyIds];
            sloIdsArrayGroup = [...sloIdsArrayGroup, ...sloIds.map((sloElement) => cs(sloElement))];
            const studentOptions = options.map((optionElement) => {
                const {
                    _id: optionId,
                    text: optionText,
                    answer,
                    attachments: optionAttachments,
                } = optionElement;

                const studentCount = students.reduce((count, student) => {
                    const studentAnswer = student.questions.find((studentElement) => {
                        return (
                            studentElement._questionId.toString() === _id.toString() &&
                            (studentElement._optionIdArray && studentElement._optionIdArray.length
                                ? studentElement._optionIdArray
                                      .map((idElement) => idElement.toString())
                                      .includes(optionId.toString())
                                : (studentElement._optionId &&
                                      studentElement._optionId.toString() ===
                                          optionId.toString()) ||
                                  (surveyQuestionType === OPEN_ENDED &&
                                      !studentElement._optionId &&
                                      studentElement.textAnswer !== undefined))
                        );
                    });
                    if (studentAnswer) {
                        return count + 1;
                    }
                    return count;
                }, 0);

                const percentage =
                    ((studentCount / students.length) * 100).toFixed() !== 'NaN'
                        ? parseFloat((studentCount / students.length) * 100).toFixed(2)
                        : '0';

                return {
                    _id: optionId,
                    text: optionText,
                    attachments: optionAttachments,
                    answer,
                    studentAnsweredCount: studentCount,
                    totalStudentAnswered: students.length,
                    percentage,
                };
            });

            const studentMatchingOptions = getStudentMatchingData({
                matchingOptions,
                students,
                questionId: _id,
            });

            if (correctionType) {
                const answerRespondedCount = getAnswerResponded({
                    questionId: _id,
                    questionType,
                    questionOptions: options,
                    students,
                    benchMark,
                    correctionType,
                    conduct: false,
                    answerMatchingType,
                    answerTextVariant,
                    matchingOptions,
                });
                correctlyResponded = answerRespondedCount.correct;
                wronglyResponded = answerRespondedCount.wrong;
            }

            const studentTextAnswer = [];
            if (surveyQuestionType === OPEN_ENDED) {
                students.forEach((student) => {
                    const matchedQuestion = student.questions.find(
                        (q) =>
                            !q._optionId &&
                            q.textAnswer !== undefined &&
                            q._questionId.toString() === _id.toString(),
                    );
                    if (matchedQuestion) {
                        studentTextAnswer.push({
                            studentId: student._studentId,
                            _questionId: matchedQuestion._questionId,
                            textAnswer: matchedQuestion.textAnswer,
                        });
                    }
                });
            }

            const studentAttendQuestionCount = students.reduce((count, student) => {
                if (
                    student.questions.find(
                        (q) => q._questionId && q._questionId.toString() === _id.toString(),
                    )
                ) {
                    return count + 1;
                }
                return count;
            }, 0);

            let movedStatus = false;
            if (activityQuestion && activityQuestion.questionMoved) {
                movedStatus = true;
            }
            let acceptQuestionResponse = false;
            if (activityQuestion && activityQuestion.acceptQuestionResponse) {
                acceptQuestionResponse = activityQuestion.acceptQuestionResponse;
            }
            let sessionType;
            const sessionData = courseSchedules.find(
                (schedulesEntry) =>
                    sessionId &&
                    schedulesEntry.session &&
                    schedulesEntry.session._session_id &&
                    schedulesEntry.session._session_id.toString() === sessionId.toString(),
            );
            if (sessionData) {
                sessionType = sessionData.session;
            }
            return {
                ...questionElement,
                _id,
                options: studentOptions,
                matchingOptions: studentMatchingOptions,
                textAnswers: studentTextAnswer,
                studentAnsweredCount: studentAttendQuestionCount,
                questionMoved: movedStatus,
                acceptQuestionResponse,
                sessionType,
                correctlyResponded,
                wronglyResponded,
            };
        });
        studentQuestion = questionIds.map((questionIdElement) => {
            const questionDetails = studentQuestion.find(
                (questionElement) =>
                    questionElement._id.toString() === questionIdElement.toString(),
            );
            return questionDetails;
        });
        taxonomyIdsArrayGroup = [...new Set(taxonomyIdsArrayGroup)];
        sloIdsArrayGroup = [...new Set(sloIdsArrayGroup)];
        // format question
        const formatQuestion = formatQuestionsWithAttachmentLink({
            questions: studentQuestion,
            taxonomyIds: taxonomyIdsArrayGroup,
            sloIds: sloIdsArrayGroup,
        });
        //return formatQuestion;
        return formatQuestion;
    }

    return [];
};

// get student result (quiz)
const getStudentResult = async ({ students, questions, userId, correctionType }) => {
    const studentData = students.find(
        (studentEntry) => studentEntry._studentId._id.toString() === userId.toString(),
    );
    const questionIds = questions
        .sort((a, b) => {
            return a.order - b.order;
        })
        .map((questionElement) => questionElement._id);

    const questionEntry = await questionSchema
        .find(
            {
                _id: { $in: questionIds },
            },
            {
                text: 1,
                options: 1,
                _activityId: 1,
                questionType: 1,
                attachments: 1,
                feedback: 1,
                sloIds: 1,
                taxonomyIds: 1,
                sessionId: 1,
                surveyQuestionType: 1,
                maxCharacterLimit: 1,
                mark: 1,
                mandatory: 1,
                description: 1,
                benchMark: 1,
                answerMatchingType: 1,
                answerTextVariant: 1,
                matchingOptions: 1,
            },
        )
        .lean();

    const sessionIds = questionEntry.map((questionElement) => questionElement.sessionId);
    const courseSchedules = await courseScheduledSchema.find(
        {
            'session._session_id': { $in: sessionIds },
            isDeleted: false,
            isActive: true,
        },
        {
            'session._session_id': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
        },
    );

    function getStudentAttendedQuestion({ questionId }) {
        const studentQuestions = studentData.questions;
        return studentQuestions.some(
            (studentEntry) =>
                ((studentEntry &&
                    studentEntry._optionIdArray &&
                    studentEntry._optionIdArray.length > 0) ||
                    (studentEntry && studentEntry.textAnswer !== '') ||
                    (studentEntry && studentEntry._optionId)) &&
                studentEntry._questionId.toString() === questionId.toString(),
        );
    }

    function getStudentCorrectlyAnsweredQuestion({
        questionId,
        options,
        questionType,
        benchMark,
        answerMatchingType,
        answerTextVariant,
        matchingOptions,
    }) {
        const studentQuestions = studentData.questions;
        const filteredStudentQuestion = studentQuestions.find(
            (questionElement) => questionElement._questionId.toString() === questionId.toString(),
        );
        if (
            [SCQ, MCQ, TF].includes(questionType) &&
            filteredStudentQuestion &&
            filteredStudentQuestion._optionIdArray &&
            filteredStudentQuestion._optionIdArray.length
        ) {
            const filteredCorrectAnswerId = options
                .filter((optionElement) => optionElement.answer)
                .map((optionElement) => optionElement._id.toString());

            const filteredAnsweredOption =
                filteredStudentQuestion &&
                filteredStudentQuestion.staffCorrectedAnswer &&
                filteredStudentQuestion.staffCorrectedAnswer !== ''
                    ? filteredStudentQuestion.staffCorrectedAnswer === 'correct'
                    : filteredCorrectAnswerId.length ===
                      filteredStudentQuestion._optionIdArray.length
                    ? filteredCorrectAnswerId.every((studentOptionElement) =>
                          filteredStudentQuestion._optionIdArray
                              .map((optionElement) => optionElement.toString())
                              .includes(studentOptionElement.toString()),
                      )
                    : false;
            return {
                studentAnswered: filteredAnsweredOption,
                _optionId: filteredStudentQuestion._optionId,
                _optionIdArray: filteredStudentQuestion._optionIdArray,
                marks:
                    filteredStudentQuestion && filteredStudentQuestion.marks
                        ? filteredStudentQuestion.marks
                        : 0,
                staffCorrectedAnswer:
                    filteredStudentQuestion && filteredStudentQuestion.staffCorrectedAnswer
                        ? filteredStudentQuestion.staffCorrectedAnswer
                        : '',
            };
        }
        if (filteredStudentQuestion && filteredStudentQuestion._optionId) {
            const filteredAnsweredOption = options.find((optionElement) => {
                return (
                    optionElement._id.toString() === filteredStudentQuestion._optionId.toString() &&
                    optionElement.answer === true
                );
            });
            return {
                studentAnswered: !!filteredAnsweredOption,
                _optionId: filteredStudentQuestion._optionId,
                _optionIdArray: filteredStudentQuestion._optionIdArray,
                marks: 0,
                staffCorrectedAnswer: '',
            };
        }
        if (questionType === SAQ && filteredStudentQuestion && filteredStudentQuestion.textAnswer) {
            const marks =
                filteredStudentQuestion && filteredStudentQuestion.marks
                    ? filteredStudentQuestion.marks
                    : 0;
            return {
                studentAnswered:
                    correctionType === 'system'
                        ? marks >= benchMark
                        : correctionType === 'manual' &&
                          filteredStudentQuestion &&
                          filteredStudentQuestion.staffCorrectedAnswer
                        ? filteredStudentQuestion.staffCorrectedAnswer === 'correct'
                        : checkSAQAnswer({
                              answerMatchingType,
                              answerTextVariant,
                              textAnswer: filteredStudentQuestion.textAnswer,
                          }) >= benchMark,
                _optionId: '',
                _optionIdArray: [],
                textAnswer: filteredStudentQuestion.textAnswer,
                marks,
                staffCorrectedAnswer:
                    filteredStudentQuestion && filteredStudentQuestion.staffCorrectedAnswer
                        ? filteredStudentQuestion.staffCorrectedAnswer
                        : '',
            };
        }

        if (questionType === MQ && filteredStudentQuestion) {
            const marks =
                filteredStudentQuestion && filteredStudentQuestion.marks
                    ? filteredStudentQuestion.marks
                    : 0;
            const staffCorrectedAnswer =
                filteredStudentQuestion && filteredStudentQuestion.staffCorrectedAnswer
                    ? filteredStudentQuestion.staffCorrectedAnswer
                    : '';
            const studentAnswered =
                filteredStudentQuestion &&
                filteredStudentQuestion.staffCorrectedAnswer &&
                filteredStudentQuestion.staffCorrectedAnswer !== ''
                    ? filteredStudentQuestion.staffCorrectedAnswer === 'correct'
                    : matchingOptions && matchingOptions.length
                    ? matchingOptions
                          .map((optionElement) => optionElement.answer.toString())
                          .every(
                              (optionElement, optionIndex) =>
                                  optionElement ===
                                  (filteredStudentQuestion &&
                                      filteredStudentQuestion.matchingOptions &&
                                      filteredStudentQuestion.matchingOptions[optionIndex]),
                          )
                    : false;

            const studentAnsweredOptions =
                matchingOptions &&
                matchingOptions.length &&
                filteredStudentQuestion.matchingOptions &&
                filteredStudentQuestion.matchingOptions.length
                    ? matchingOptions.map((optionElement, optionElementIndex) => {
                          return {
                              ...optionElement,
                              columnText:
                                  filteredStudentQuestion.matchingOptions[optionElementIndex],
                              studentAnswered:
                                  filteredStudentQuestion.matchingOptions[optionElementIndex] ===
                                  null
                                      ? false
                                      : optionElement.answer.toString() ===
                                        filteredStudentQuestion.matchingOptions[
                                            optionElementIndex
                                        ].toString(),
                          };
                      })
                    : [];
            return {
                studentAnswered,
                _optionId: '',
                _optionIdArray: filteredStudentQuestion._optionIdArray,
                matchingOptions: studentAnsweredOptions,
                marks,
                staffCorrectedAnswer,
            };
        }

        return {
            studentAnswered: false,
            _optionId: filteredStudentQuestion._optionId || '',
            _optionIdArray: filteredStudentQuestion._optionIdArray || [],
            marks: filteredStudentQuestion.marks || 0,
            staffCorrectedAnswer: filteredStudentQuestion.staffCorrectedAnswer || '',
        };
    }

    let taxonomyIdsArrayGroup = [];
    let sloIdsArrayGroup = [];
    if (questionEntry && questionEntry.length > 0) {
        let studentQuestion = questionEntry.map((questionElement) => {
            const {
                _id,
                options,
                sloIds,
                taxonomyIds,
                sessionId,
                surveyQuestionType,
                questionType,
                benchMark,
                answerMatchingType,
                answerTextVariant,
                matchingOptions,
            } = questionElement;

            taxonomyIdsArrayGroup = [...taxonomyIdsArrayGroup, ...taxonomyIds];
            sloIdsArrayGroup = [...sloIdsArrayGroup, ...sloIds.map((sloElement) => cs(sloElement))];
            let studentAnswered = false;
            let studentAttended = false;
            let studentAnsweredOptionId = '';
            let studentMark = 0;
            let staffCorrectedAnswer = '';
            let studentTextAnswer;
            let studentAnsweredOptionIdArray = [];
            let studentMatchingOptions = [];
            if (studentData && studentData.questions) {
                const studentQuestions = studentData.questions;
                const hasStudentAttended = getStudentAttendedQuestion({ questionId: _id });
                if (hasStudentAttended) {
                    studentAttended = true;
                    if (surveyQuestionType === OPEN_ENDED) {
                        // need to verify
                        const matchedQuestions = studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._questionId.toString() === _id.toString(),
                        );
                        if (matchedQuestions) {
                            studentTextAnswer = matchedQuestions.textAnswer
                                ? matchedQuestions.textAnswer
                                : null;
                        }
                    } else {
                        const hasStudentAnswered = getStudentCorrectlyAnsweredQuestion({
                            questionId: _id,
                            options,
                            questionType,
                            benchMark,
                            answerMatchingType,
                            answerTextVariant,
                            matchingOptions,
                        });
                        if (hasStudentAnswered) {
                            studentAnswered = hasStudentAnswered.studentAnswered;
                            studentAnsweredOptionId = hasStudentAnswered._optionId;
                            studentAnsweredOptionIdArray = hasStudentAnswered._optionIdArray;
                            studentMark = hasStudentAnswered.marks;
                            staffCorrectedAnswer = hasStudentAnswered.staffCorrectedAnswer;
                            studentTextAnswer = hasStudentAnswered.textAnswer;
                            studentMatchingOptions =
                                hasStudentAnswered && hasStudentAnswered.matchingOptions
                                    ? hasStudentAnswered.matchingOptions
                                    : [];
                        }
                    }
                }
            }
            let sessionType;
            const sessionData = courseSchedules.find(
                (schedulesEntry) =>
                    schedulesEntry.session &&
                    sessionId &&
                    schedulesEntry.session._session_id &&
                    schedulesEntry.session._session_id.toString() === sessionId.toString(),
            );
            if (sessionData) {
                sessionType = sessionData.session;
            }

            return {
                ...questionElement,
                _id,
                options,
                studentAttended,
                studentAnswered,
                studentAnsweredOptionId,
                studentAnsweredOptionIdArray,
                sessionType,
                studentTextAnswer,
                studentMark,
                staffCorrectedAnswer,
                matchingOptions:
                    studentMatchingOptions && studentMatchingOptions.length
                        ? studentMatchingOptions
                        : matchingOptions,
                mark: questionElement && questionElement.mark ? questionElement.mark : 1,
            };
        });
        studentQuestion = questionIds.map((questionIdElement) => {
            const questionDetails = studentQuestion.find(
                (questionElement) =>
                    questionElement._id.toString() === questionIdElement.toString(),
            );
            return questionDetails;
        });
        taxonomyIdsArrayGroup = [...new Set(taxonomyIdsArrayGroup)];
        sloIdsArrayGroup = [...new Set(sloIdsArrayGroup)];
        // format question
        const formatQuestion = formatQuestionsWithAttachmentLink({
            questions: studentQuestion,
            taxonomyIds: taxonomyIdsArrayGroup,
            sloIds: sloIdsArrayGroup,
        });
        return formatQuestion;
    }
    return [];
};

exports.getResults = async (req, res) => {
    try {
        const {
            params: { id: activityId },
            query: { type, userId },
        } = req;
        let viewResults;
        const { students, questions, correctionType, quizType } = await activitySchema
            .findOne(
                { _id: convertToMongoObjectId(activityId) },
                { students: 1, questions: 1, correctionType: 1, quizType: 1 },
            )
            .populate({ path: 'students._studentId', select: { name: 1, user_id: 1 } })
            .lean();
        if (type === DC_STAFF)
            viewResults = await getStaffTimeBasedResult({ students, questions, correctionType });
        if (type === DC_STUDENT)
            viewResults = await getStudentResult({
                students,
                questions,
                userId,
                correctionType,
            });
        if (viewResults?.length) {
            viewResults.forEach((resultElement) => {
                resultElement.quizType = quizType;
            });
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('UPDATED_SUCCESSFULLY'),
            viewResults,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

const getAllSessionList = async ({
    courseId,
    userId,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    _institution_calendar_id,
    courseAdmin,
}) => {
    try {
        const courseScheduleData = await courseScheduledSchema
            .find(
                {
                    isDeleted: false,
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _course_id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(_program_id),
                    year_no,
                    level_no,
                    term,
                    rotation,
                    ...(rotation_count && { rotation_count }),
                    ...(courseAdmin !== 'true' && {
                        'staffs._staff_id': convertToMongoObjectId(userId),
                    }),
                },
                {
                    _id: 1,
                    'session._session_id': 1,
                    'session.s_no': 1,
                    title: 1,
                    type: 1,
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    'merge_with.session_id': 1,
                },
            )
            .lean();
        const sessionIds = [];
        const scheduleIds = [];
        let regularSessionList = [];
        const list = [];
        for (const coursedScheduleElement of courseScheduleData) {
            const {
                session = {},
                _id,
                title,
                type,
                merge_status,
                merge_with,
            } = coursedScheduleElement;
            const { _session_id, s_no } = session;
            if (
                session &&
                _session_id &&
                !sessionIds.some(
                    (sessionIdElement) =>
                        sessionIdElement.sessionId.toString() === _session_id.toString() &&
                        sessionIdElement.merge_status.toString() === merge_status.toString(),
                )
            ) {
                sessionIds.push({ sessionId: _session_id, merge_status });
                if (merge_status) {
                    const scheduleIds = merge_with.filter(
                        (mergeElement) => mergeElement.schedule_id,
                    );
                    const scheduleIdsToString = merge_with.map(
                        (mergeElement) => mergeElement.session_id,
                    );
                    if (
                        !regularSessionList.some((regularSessionElement) =>
                            scheduleIdsToString
                                .toString()
                                .includes(regularSessionElement._id.toString()),
                        )
                    ) {
                        let sessionTitle = session.delivery_symbol + session.delivery_no;
                        for (const scheduleIdElement of scheduleIds) {
                            sessionIds.push({
                                sessionId: scheduleIdElement.session_id,
                                merge_status,
                            });
                            const schedule = courseScheduleData.find(
                                (courseScheduleElement) =>
                                    courseScheduleElement._id.toString() ===
                                    scheduleIdElement.schedule_id.toString(),
                            );
                            sessionTitle +=
                                ',' +
                                schedule.session.delivery_symbol +
                                schedule.session.delivery_no;
                        }
                        regularSessionList.push({
                            _id,
                            title: sessionTitle,
                            type: REGULAR,
                            s_no,
                        });
                    }
                } else {
                    sessionIds.push({
                        sessionId: _session_id,
                        merge_status,
                    });
                    regularSessionList.push({
                        _id: _session_id,
                        title: session.delivery_symbol + session.delivery_no,
                        type: REGULAR,
                        s_no,
                    });
                }
            }
            regularSessionList = regularSessionList.sort((a, b) =>
                a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
            );
            if (title && !scheduleIds.includes(_id.toString())) {
                scheduleIds.push(_id.toString());
                list.push({
                    _id,
                    title,
                    type,
                });
            }
        }
        if (list.length) regularSessionList.push(...list);
        return regularSessionList;
    } catch (error) {
        throw new Error(error);
    }
};

exports.selectSessions = async (req, res) => {
    try {
        const {
            params: { id },
            query: {
                userId,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
                courseAdmin,
            },
        } = req;
        const sessionSupportList = await getAllSessionList({
            courseId: id,
            userId,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            courseAdmin,
        });
        if (!sessionSupportList.length)
            return sendResponseWithRequest(req, res, 200, false, 'No data found', []);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            sessionSupportList,
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getSessionSchedule = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const {
            userId,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            courseAdmin,
        } = req.query;
        const query = {
            _course_id: convertToMongoObjectId(courseId),
            $or: [
                { 'session._session_id': convertToMongoObjectId(sessionId), merge_status: false },
                { _id: convertToMongoObjectId(sessionId) },
            ],

            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            _program_id: convertToMongoObjectId(_program_id),
            year_no,
            level_no,
            term,
            rotation,
            isActive: true,
            isDeleted: false,
        };
        if (courseAdmin === 'true') {
            // check course admin verification
            const courses = await courseSchema.find(
                {
                    'coordinators._user_id': convertToMongoObjectId(userId),
                    'coordinators._institution_calendar_id':
                        convertToMongoObjectId(_institution_calendar_id),
                    'coordinators.term': term,
                    'coordinators.level_no': level_no,
                },
                {
                    _id: 1,
                },
            );
            if (courses.length) {
                const currentCourseIds = courses.map((course) => course._id);
                if (currentCourseIds) {
                    query._course_id = { $in: currentCourseIds };
                }
            } else {
                query['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
        } else {
            query['staffs._staff_id'] = convertToMongoObjectId(userId);
        }
        if (rotation_count) {
            query.rotation_count = rotation_count;
        }
        const project = {
            merge_status: 1,
            merge_with: 1,
            'student_groups.group_id': 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
        };
        const courseScheduleData = await courseScheduledSchema.find(query, project);
        if (!courseScheduleData.length)
            sendResponseWithRequest(req, res, 200, true, req.t('SCHEDULE_NOT_FOUND'), null);
        const sessions = [];
        for (const courseScheduleElement of courseScheduleData) {
            const { student_groups, _id, merge_status, merge_with } = courseScheduleElement;
            if (student_groups && student_groups.length && !merge_status) {
                let studentGroups = student_groups.map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                });
                studentGroups = [...new Set(studentGroups)];
                studentGroups = studentGroups.toString();
                sessions.push({ _id, studentGroupNames: studentGroups });
            }
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await courseScheduledSchema.find(
                        {
                            _id: { $in: scheduleIds },
                            isDeleted: false,
                            isActive: true,
                        },
                        {
                            'student_groups.group_name': 1,
                            'student_groups.session_group.group_name': 1,
                        },
                    );
                    scheduleStudents = schedules.map(
                        (scheduleElement) => scheduleElement.student_groups,
                    );
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                    if (scheduleStudents && scheduleStudents.length) {
                        scheduleStudents = scheduleStudents.concat(student_groups);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    }
                    let studentGroups = scheduleStudents.map((studentGroupElement) => {
                        const { group_name, session_group } = studentGroupElement;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((groupNameEntry) => {
                                let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    });
                    studentGroups = [...new Set(studentGroups)];
                    studentGroups = studentGroups.toString();
                    sessions.push({ _id, studentGroupNames: studentGroups });
                }
            }
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), sessions);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

// get all QuestionBank
exports.getQuestionBank = async (req, res) => {
    try {
        const {
            body: { courseId, sessions },
        } = req;

        if (sessions && sessions.length > 1) {
            const sessionIds = sessions.map((sessionElement) =>
                convertToMongoObjectId(sessionElement),
            );
            const courseScheduledQuery = {
                isDeleted: false,
                'session._session_id': { $in: sessionIds },
            };

            const courseSchedules = await courseScheduledSchema
                .find(courseScheduledQuery, {
                    session: 1,
                })
                .lean();

            const sessionDetails = [];
            for (const session of sessions) {
                let courseScheduleDetails = courseSchedules.filter((courseSchedule) => {
                    if (
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        courseSchedule.session._session_id.toString() === session.toString()
                    ) {
                        return true;
                    }
                    return false;
                });

                courseScheduleDetails = courseScheduleDetails.map(
                    (courseScheduleDetail) => courseScheduleDetail.session,
                );
                const sessionDetail = courseScheduleDetails.find(
                    (courseScheduleDetail) =>
                        courseScheduleDetail._session_id.toString() === session.toString(),
                );

                if (sessionDetail) {
                    sessionDetails.push(sessionDetail);
                }
            }
            const sessionDetailLists = sessionDetails.map((sessionDetail) => {
                sessionDetail._id = sessionDetail._session_id;
                return sessionDetail;
            });
            return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
                sessions: sessionDetailLists,
            });
        }
        const sessionId = sessions.toString();
        const slos = await getSloPlusCloBySessionIdAndCourseId(courseId, sessionId, 'question');
        let uniqueSloData = [];
        if (slos && slos.length) {
            uniqueSloData = Array.from(
                new Set(slos.map((sloElement) => sloElement._id.toString())),
            ).map((idElement) =>
                slos.find((sloElement) => sloElement._id.toString() === idElement),
            );
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            sessions: uniqueSloData,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

const formatQuestionsWithOutAttachmentLink = async ({ questions, taxonomyIds, sloIds }) => {
    let taxonomyArray = [];
    if (taxonomyIds.length) {
        taxonomyArray = await taxonomySchema.find(
            { _id: { $in: taxonomyIds } },
            { name: 1, _id: 1 },
        );
    }

    function getTaxonomyData(ids) {
        const updatedData =
            taxonomyArray.length > 0
                ? ids.map((taxonomyElement) =>
                      taxonomyArray.find(
                          (idElement) => idElement._id.toString() === taxonomyElement.toString(),
                      ),
                  )
                : [];
        return updatedData;
    }

    let sloDetailList = sloIds;
    if (sloIds && sloIds.length) {
        const sloDetails = await getClosBySloIds(sloIds);
        const cloDetails = await getClos(sloIds);
        sloDetailList = sloIds
            .map((sloIdElement) => {
                const sloList = sloDetails.find((sloDetailElement) =>
                    sloDetailElement.slos.find(
                        (sloElement) => sloElement.slo_id === sloIdElement.toString(),
                    ),
                );
                let cloList;
                if (cloDetails.length) {
                    cloList = cloDetails.find(
                        (cloDetailElement) =>
                            cloDetailElement._id.toString() === sloIdElement.toString(),
                    );
                }
                let sloIdDetail;
                if (sloList) {
                    sloIdDetail = sloList.slos.find(
                        (sloElement) => sloElement.slo_id === sloIdElement.toString(),
                    );
                    sloIdDetail._id = sloIdDetail.slo_id;
                    sloIdDetail.type = DS_SLO_KEY;
                }
                if (!sloIdDetail && cloList) {
                    sloIdDetail = {
                        _id: cloList._id,
                        name: cloList.name,
                        type: DS_CLO_KEY,
                        no: cloList.no,
                    };
                }
                return sloIdDetail;
            })
            .filter((sloElement) => sloElement);
    }

    const getSloIdsData = (sloIds) => {
        return sloDetailList.length > 0
            ? sloIds.map((sloElement) =>
                  sloDetailList.find(
                      (detailElement) => detailElement._id.toString() === sloElement.toString(),
                  ),
              )
            : [];
    };

    for (const questionElement of questions) {
        if (questionElement) {
            let sloDetailList = sloIds;
            if (questionElement.sloIds && questionElement.sloIds.length) {
                sloDetailList = getSloIdsData(questionElement.sloIds);
                questionElement.sloIds = sloDetailList;
            }

            if (questionElement.taxonomyIds && questionElement.taxonomyIds.length) {
                const taxonomyIdEntry = questionElement.taxonomyIds.map(
                    (taxonomyIdElement) => taxonomyIdElement,
                );
                questionElement.taxonomyIds = getTaxonomyData(taxonomyIdEntry);
            }
        }
    }
    return questions;
};

exports.getQuestionsBySession = async (req, res) => {
    try {
        const {
            body: { courseId, sessionId, sloOrCloId, _institution_calendar_id },
            query: { page, limit, search },
        } = req;
        const activityData = await activitySchema
            .find(
                {
                    status: COMPLETED,
                    courseId: convertToMongoObjectId(courseId),
                    'sessionFlowIds._id': convertToMongoObjectId(sessionId),
                    // _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                },
                {
                    'questions._id': 1,
                    students: 1,
                },
            )
            .lean();
        const questionIds = activityData
            .filter(
                (activityElement) => activityElement.questions && activityElement.questions.length,
            )
            .flatMap((activityQuestionElement) => activityQuestionElement.questions)
            .map((questionIdElement) => questionIdElement._id);
        const courseScheduleData = await courseScheduledSchema
            .findOne(
                {
                    isDeleted: false,
                    'session._session_id': convertToMongoObjectId(sessionId),
                },
                { session: 1 },
            )
            .lean();
        let currentPage = 0;
        let currentLimit = 0;
        currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        currentLimit = page && limit ? Number(limit) : 10;
        //search test question
        if (sessionId && !sloOrCloId) {
            const queryEntry = {
                isDeleted: false,
                _id: { $in: questionIds },
                sessionId: convertToMongoObjectId(sessionId),
                ...(search && { text: { $regex: search, $options: 'i' } }),
            };
            const questions = await questionSchema
                .find(queryEntry, {
                    isDeleted: 1,
                    acceptQuestionResponse: 1,
                    sloIds: 1,
                    taxonomyIds: 1,
                    text: 1,
                    options: 1,
                    feedback: 1,
                    attachments: 1,
                    sessionId: 1,
                    _activityId: 1,
                    questionType: 1,
                    matchingOptions: 1,
                    mark: 1,
                    maxCharacterLimit: 1,
                    descriptionEnable: 1,
                    shuffleOptionOrder: 1,
                    description: 1,
                    characterLength: 1,
                    answerMatchingType: 1,
                    benchMark: 1,
                    answerTextVariant: 1,
                })
                .skip(currentPage)
                .limit(currentLimit)
                .lean();
            const totalDoc = await questionSchema.countDocuments(queryEntry).exec();
            const questionDetails = questions.filter(
                (questionElement) =>
                    questionElement.sessionId &&
                    questionElement.sessionId.toString() === sessionId.toString(),
            );
            const questionList = [];
            // check question use and student correct answered
            let taxonomyIdsArrayGroup = [];
            let sloIdsArrayGroup = [];
            for (const questionDetail of questionDetails) {
                const correctOption = questionDetail.options.find(
                    (optionElement) => optionElement.answer,
                );
                const usedQuestions = activityData.filter((activityElement) =>
                    activityElement.questions.find(
                        (questionElement) =>
                            questionElement._id.toString() === questionDetail._id.toString(),
                    ),
                );
                let studentAnsweredQuestions = usedQuestions
                    .filter((activityElement) =>
                        activityElement.students.find((studentElement) =>
                            studentElement.questions.find(
                                (questionElement) =>
                                    questionElement._questionId.toString() ===
                                    questionDetail._id.toString(),
                            ),
                        ),
                    )
                    .map((activityElement) => activityElement.students);
                // eslint-disable-next-line prefer-spread
                studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                studentAnsweredQuestions = studentAnsweredQuestions.map((studentAnsweredQuestion) =>
                    studentAnsweredQuestion.questions.filter(
                        (questionElement) =>
                            questionElement._questionId.toString() ===
                            questionDetail._id.toString(),
                    ),
                );
                // eslint-disable-next-line prefer-spread
                studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                const totalStudents = studentAnsweredQuestions.length;
                const correctAnsweredStudents = studentAnsweredQuestions.filter(
                    (studentAnsweredQuestion) =>
                        studentAnsweredQuestion._optionId &&
                        correctOption &&
                        studentAnsweredQuestion._optionId.toString() ===
                            correctOption._id.toString(),
                ).length;
                const percentage =
                    (correctAnsweredStudents / totalStudents) * 100
                        ? ((correctAnsweredStudents / totalStudents) * 100).toFixed()
                        : '0';
                const usedCount = usedQuestions && usedQuestions.length ? usedQuestions.length : 0;

                let sessionType;
                if (courseScheduleData) {
                    sessionType = courseScheduleData.session;
                }
                questionList.push({
                    ...questionDetail,
                    usedCount,
                    percentage,
                    sessionType,
                    sessionDetails: sessionType,
                    mark: questionDetail && questionDetail.mark ? questionDetail.mark : 1,
                });
                taxonomyIdsArrayGroup = [...taxonomyIdsArrayGroup, ...questionDetail.taxonomyIds];
                sloIdsArrayGroup = [
                    ...sloIdsArrayGroup,
                    ...questionDetail.sloIds.map((sloElement) => cs(sloElement)),
                ];
            }
            const question = await formatQuestionsWithOutAttachmentLink({
                questions: questionList,
                taxonomyIds: taxonomyIdsArrayGroup,
                sloIds: sloIdsArrayGroup,
            });
            return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
                totalDoc,
                totalPages: Math.ceil(totalDoc / currentLimit),
                currentPage: parseInt(page),
                questions: question,
            });
        }
        const sloData = await getSloPlusCloBySessionIdAndCourseId(courseId, sessionId.toString());
        const sloIds = sloData
            .filter((sloElement) => sloElement._id.toString() === sloOrCloId.toString())
            .map((sloIdElement) => sloIdElement._id);
        const queryParams = {
            _id: {
                $in: questionIds.map((questionElement) => convertToMongoObjectId(questionElement)),
            },
            isDeleted: false,
            sloIds: {
                $elemMatch: { $in: sloIds.map((sloElement) => convertToMongoObjectId(sloElement)) },
            },
            sessionId: convertToMongoObjectId(sessionId),
            ...(search && { text: { $regex: search, $options: 'i' } }),
        };
        const questionQueryData = await questionSchema
            .find(queryParams, {
                isDeleted: 1,
                sloIds: 1,
                taxonomyIds: 1,
                text: 1,
                options: 1,
                feedback: 1,
                attachments: 1,
                sessionId: 1,
                _activityId: 1,
                questionType: 1,
                matchingOptions: 1,
                mark: 1,
                maxCharacterLimit: 1,
                descriptionEnable: 1,
                shuffleOptionOrder: 1,
                description: 1,
                characterLength: 1,
                answerMatchingType: 1,
                benchMark: 1,
                answerTextVariant: 1,
            })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        const totalDoc = await questionSchema.countDocuments(queryParams);
        const sloQuestion = [];
        let taxonomyIdsArrayGroup = [];
        let sloIdsArrayGroup = [];
        if (sloOrCloId) {
            for (const sloElement of sloIds) {
                const questionDetails = questionQueryData.filter((questionElement) => {
                    const questionSloIds = questionElement.sloIds.map((sloIdElement) =>
                        sloIdElement.toString(),
                    );
                    return (
                        questionElement.sessionId.toString() === sessionId.toString() &&
                        questionSloIds.includes(sloElement.toString())
                    );
                });
                const questionList = [];
                for (const questionDetailElement of questionDetails) {
                    const correctOption = questionDetailElement.options.find(
                        (optionElement) => optionElement.answer,
                    );
                    const userQuestions = activityData.filter((activityElement) =>
                        activityElement.questions.find(
                            (questionElement) =>
                                questionElement._id.toString() ===
                                questionDetailElement._id.toString(),
                        ),
                    );

                    let studentAnsweredQuestions = userQuestions
                        .filter((activityElement) =>
                            activityElement.students.some((studentElement) =>
                                studentElement.questions.some(
                                    (questionElement) =>
                                        questionElement._questionId.toString() ===
                                        questionDetailElement._id.toString(),
                                ),
                            ),
                        )
                        .flatMap((studentAnswerElement) => studentAnswerElement.students);

                    studentAnsweredQuestions = studentAnsweredQuestions
                        .map((studentAnswerElement) =>
                            studentAnswerElement.questions.filter(
                                (questionElement) =>
                                    questionElement._questionId.toString() ===
                                    questionDetailElement._id.toString(),
                            ),
                        )
                        .flatMap((studentAnswerElement) => studentAnswerElement);
                    const totalStudents = studentAnsweredQuestions.length;
                    const correctAnsweredStudents = studentAnsweredQuestions.filter(
                        (studentAnswerElement) =>
                            studentAnswerElement._optionId &&
                            correctOption &&
                            studentAnswerElement._optionId.toString() ===
                                correctOption._id.toString(),
                    ).length;
                    const percentage =
                        (correctAnsweredStudents / totalStudents) * 100
                            ? ((correctAnsweredStudents / totalStudents) * 100).toFixed()
                            : '0';
                    const usedCount =
                        userQuestions && userQuestions.length ? userQuestions.length : 0;
                    let sessionType;
                    if (courseScheduleData) sessionType = courseScheduleData.session;
                    questionList.push({
                        ...questionDetailElement,
                        usedCount,
                        percentage,
                        sessionType,
                        sessionDetails: sessionType,
                        mark:
                            questionDetailElement && questionDetailElement.mark
                                ? questionDetailElement.mark
                                : 1,
                    });
                    taxonomyIdsArrayGroup = [
                        ...taxonomyIdsArrayGroup,
                        ...questionDetailElement.taxonomyIds,
                    ];
                    sloIdsArrayGroup = [
                        ...sloIdsArrayGroup,
                        ...questionDetailElement.sloIds.map((sloElement) => cs(sloElement)),
                    ];
                }
                const question = await formatQuestionsWithOutAttachmentLink({
                    questions: questionList,
                    taxonomyIds: taxonomyIdsArrayGroup,
                    sloIds: sloIdsArrayGroup,
                });
                if (question && question.length) sloQuestion.push(question);
            }
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            totalDoc,
            totalPages: Math.ceil(totalDoc / currentLimit),
            currentPage: parseInt(page),
            questions: sloQuestion && sloQuestion[0] ? sloQuestion[0] : [],
        });
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getQuestions = async (req, res) => {
    try {
        const {
            body: { questions },
        } = req;
        const questionIds = questions.map((questionElement) =>
            convertToMongoObjectId(questionElement),
        );
        let questionEntry = await questionSchema
            .find(
                {
                    isDeleted: false,
                    _id: { $in: questionIds },
                },
                {
                    isDeleted: 1,
                    sloIds: 1,
                    taxonomyIds: 1,
                    text: 1,
                    options: 1,
                    feedback: 1,
                    attachments: 1,
                    sessionId: 1,
                    _activityId: 1,
                    questionType: 1,
                    matchingOptions: 1,
                },
            )
            .lean();
        if (questionEntry && questionEntry.length) {
            questionEntry = await formatQuestionsWithAttachmentLink({ questions: questionEntry });
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            questions: questionEntry,
        });
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getStudents = async (req, res) => {
    try {
        const {
            params: { id: activityId },
        } = req;

        const activityUpdatedData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(activityId),
                },
                {
                    scheduleIds: 1,
                    'students._studentId': 1,
                },
            )
            .lean();

        if (!activityUpdatedData)
            return sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));
        const { scheduleIds, students } = activityUpdatedData;
        let studentData = [];
        if (scheduleIds && scheduleIds.length > 0) {
            const scheduleConvertIds = scheduleIds.map((scheduleElement) =>
                convertToMongoObjectId(scheduleElement),
            );
            const courseScheduleData = await courseScheduledSchema.find(
                {
                    _id: { $in: scheduleConvertIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'students._id': 1,
                    merge_status: 1,
                    merge_with: 1,
                },
            );

            const mergedStudents = new Set();
            for (const courseScheduleElement of courseScheduleData) {
                const { students, merge_status, merge_with } = courseScheduleElement;
                let scheduleStudents = students;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWithElement) =>
                        convertToMongoObjectId(mergeWithElement.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const mergeScheduleData = await courseScheduledSchema.find(
                            {
                                _id: { $in: scheduleIds },
                                isDeleted: false,
                                isActive: true,
                            },
                            {
                                'students._id': 1,
                            },
                        );
                        scheduleStudents = mergeScheduleData.flatMap(
                            (scheduleElement) => scheduleElement.students,
                        );
                    }
                }
                scheduleStudents.forEach((studentElement) =>
                    mergedStudents.add(JSON.stringify(studentElement)),
                );
            }

            const uniqueStudents = [...mergedStudents].map((studentElement) =>
                JSON.parse(studentElement),
            );
            const activityStudentIds = students.map((activityStudent) =>
                activityStudent._studentId.toString(),
            );
            const studentIds = uniqueStudents
                .map((studentElement) => convertToMongoObjectId(studentElement._id))
                .filter((studentElement) => activityStudentIds.includes(studentElement.toString()));

            const userDetails = await userSchema.find(
                {
                    _id: { $in: studentIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    name: 1,
                    academicId: 1,
                    user_id: 1,
                },
            );
            studentData = userDetails.map((studentElement) => {
                return {
                    name: studentElement.name,
                    _id: studentElement._id,
                    academicId: studentElement.user_id,
                };
            });
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), studentData);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.updateStudentMarks = async (req, res) => {
    try {
        const {
            body: { questions },
            query: { activityId, studentId },
        } = req;

        const activityUpdatedData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(activityId),
                    'students._studentId': convertToMongoObjectId(studentId),
                },
                {
                    _id: 1,
                    students: 1,
                },
            )
            .lean();

        if (!activityUpdatedData)
            return sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));

        const { students } = activityUpdatedData;

        function getStudentMarks(questionId) {
            const filteredQuestion = questions.find(
                (questionElement) =>
                    questionElement._questionId.toString() === questionId.toString(),
            );
            return filteredQuestion ? filteredQuestion.marks : 0;
        }

        function getStaffCorrectedAnswer(questionId) {
            const filteredQuestion = questions.find(
                (questionElement) =>
                    questionElement._questionId.toString() === questionId.toString(),
            );
            return filteredQuestion ? filteredQuestion.staffCorrectedAnswer : '';
        }

        const updatedStudentData = students.map((studentElement) => {
            if (studentElement._studentId.toString() !== studentId.toString())
                return studentElement;
            const studentAnsweredQuestions = studentElement.questions.map((questionElement) => {
                return {
                    ...questionElement,
                    marks: getStudentMarks(questionElement._questionId),
                    staffCorrectedAnswer: getStaffCorrectedAnswer(questionElement._questionId),
                };
            });
            return { ...studentElement, questions: studentAnsweredQuestions, status: 'saved' };
        });

        await activitySchema.findOneAndUpdate(
            {
                _id: convertToMongoObjectId(activityId),
                'students._studentId': convertToMongoObjectId(studentId),
            },
            { $set: { students: updatedStudentData } },
        );
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.publishStudentMarks = async (req, res) => {
    try {
        const {
            query: { activityId },
        } = req;

        const activityUpdatedData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(activityId),
                },
                {
                    students: 1,
                },
            )
            .lean();

        if (!activityUpdatedData)
            return sendResponseWithRequest(req, res, 200, false, req.t('DS_UPDATE_FAILED'));

        const { students } = activityUpdatedData;

        const updatedStudentData = students.map((studentElement) => {
            return { ...studentElement, status: 'published' };
        });
        const updatedActivityData = await activitySchema
            .findByIdAndUpdate(
                { _id: convertToMongoObjectId(activityId) },
                { $set: { students: updatedStudentData } },
                {
                    new: true,
                    projection: {
                        _institution_calendar_id: 1,
                        _program_id: 1,
                        courseId: 1,
                        level_no: 1,
                        term: 1,
                        rotation_count: 1,
                    },
                },
            )
            .lean();
        activityReportFromCloudFunction({
            institutionCalendarId: updatedActivityData?._institution_calendar_id,
            programId: updatedActivityData?._program_id,
            courseId: updatedActivityData?.courseId,
            levelNo: updatedActivityData?.level_no,
            term: updatedActivityData?.term,
            rotation_count: updatedActivityData?.rotation_count,
            isClear: true,
        });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Students Marks Published Successfully',
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.exportActivity = async (req, res) => {
    try {
        const {
            params: { id },
        } = req;

        const activityData = await activitySchema
            .findOne(
                {
                    _id: convertToMongoObjectId(id),
                },
                {
                    questions: 1,
                    students: 1,
                    _id: 1,
                    name: 1,
                },
            )
            .lean();
        if (!activityData)
            return sendResponseWithRequest(req, res, 200, false, req.t('ACTIVITY_NOT_FOUND'));

        const { questions, students, _id, name } = activityData;

        const questionIds = questions
            .map((questionElement) => questionElement._id.toString())
            .flat();

        const studentIds = students.map((studentElement) => studentElement._studentId);

        const studentData = await userSchema
            .find(
                {
                    _id: { $in: studentIds },
                    isDeleted: false,
                },
                {
                    user_id: 1,
                    name: 1,
                    gender: 1,
                },
            )
            .lean();
        const questionList = await questionSchema
            .find(
                {
                    isDeleted: false,
                    _id: {
                        $in: questionIds,
                    },
                },
                { _id: 1, options: 1, questionType: 1, mark: 1, benchMark: 1 },
            )
            .lean();

        const questionWithWrongOption = [];
        for (questionElement of questions) {
            const questionData = questionList.find(
                (questionListElement) =>
                    questionListElement._id.toString() === questionElement._id.toString(),
            );
            if (!questionData) continue;

            const optionData = questionData.options.find(
                (answerElement) => answerElement.answer === true,
            );
            const wrongOptionData = questionData.options.find(
                (answerElement) => answerElement.answer === false,
            );
            if (questionData && optionData) {
                questionElement.correctOption = optionData._id;
                questionElement.questionData = questionData;
            }
            if (questionData && ['SAQ', 'MQ'].includes(questionData.questionType)) {
                questionElement.questionData = questionData;
            }
            questionWithWrongOption.push({
                _id: convertToMongoObjectId(),
                _questionId: convertToMongoObjectId(questionElement._id),
                _optionId: wrongOptionData && convertToMongoObjectId(wrongOptionData._id),
            });
        }
        for (activityStudentElement of students) {
            activityStudentElement.missingQuestionDatas = [];
            const missingQuestionDatas = questionWithWrongOption.filter(
                (wrongElement) =>
                    !activityStudentElement.questions.find(
                        (questionElement) =>
                            wrongElement._questionId.toString() ===
                            questionElement._questionId.toString(),
                    ),
            );
            if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                activityStudentElement.questions = [
                    ...activityStudentElement.questions,
                    ...missingQuestionDatas,
                ];
                activityStudentElement.missingQuestionDatas = missingQuestionDatas;
            }
        }
        let studentMark = 0;
        const studentActivities = [];
        for (studentElement of students) {
            let studentQuestionMarks = 0;
            let quizAnsweredCount = 0;
            for (questionElement of questions) {
                const questionData = studentElement.questions.find(
                    (studentQuestionElement) =>
                        studentQuestionElement._questionId.toString() ===
                        questionElement._id.toString(),
                );
                const missingQuestionData = studentElement.missingQuestionDatas.find(
                    (eleMissingQuestion) =>
                        eleMissingQuestion._questionId.toString() ===
                        questionElement._id.toString(),
                );
                if (questionData && !missingQuestionData) {
                    quizAnsweredCount++;
                    const questionAnswered = getQuestionAnswered({
                        questionData: questionElement,
                        questionElement: questionData,
                    });
                    if (questionAnswered) {
                        studentQuestionMarks++;
                    }
                }
            }
            studentMark += (studentQuestionMarks / questions.length) * 100;

            const studentDetails = studentData.find(
                (studentDataElement) =>
                    studentDataElement._id.toString() === studentElement._studentId.toString(),
            );
            studentActivities.push({
                _id,
                name,
                questionCount: questions.length,
                noQuizAnswered: quizAnsweredCount,
                answeredRight: studentQuestionMarks,
                mark: (studentQuestionMarks / questions.length) * 100,
                studentDetails,
            });
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            studentActivities,
        );
    } catch (error) {
        console.log(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getQuestionBankCreatedByItem = async ({ body = {} }) => {
    try {
        const { courseId } = body;
        const mongoCourseIds = courseId.map(convertToMongoObjectId);
        const activityData = await activitySchema
            .find({ courseId: { $in: mongoCourseIds } }, { createdBy: 1 })
            .populate({
                path: 'createdBy',
                select: { name: 1, gender: 1, user_id: 1 },
            })
            .lean();
        const uniqueStaff = [
            ...new Map(
                activityData
                    .filter((activityElement) => activityElement.createdBy)
                    .map((activityElement) => [
                        String(activityElement.createdBy._id),
                        activityElement.createdBy,
                    ]),
            ).values(),
        ];

        return { statusCode: 200, data: uniqueStaff };
    } catch (err) {
        throw err instanceof Error ? err : new Error(String(err));
    }
};

exports.getQuestionsByCourseList = async ({ body = {}, query = {} }) => {
    try {
        const { courseIds = [], questionTypes = [], userIds = [] } = body;
        const { search } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        const courseObjectIds = courseIds.map((courseId) => convertToMongoObjectId(courseId));
        const userObjectIds = userIds.map((userId) => convertToMongoObjectId(userId));
        const activityDatas = await activitySchema
            .find(
                {
                    isDeleted: false,
                    status: COMPLETED,
                    courseId: { $in: courseObjectIds },
                    ...(userObjectIds.length && { createdBy: { $in: userObjectIds } }),
                },
                {
                    'questions._id': 1,
                    'questions.questionType': 1,
                    createdBy: 1,
                    name: 1,
                    courseId: 1,
                },
            )
            .populate({ path: 'courseId', select: 'course_name' })
            .lean();

        const quetionIds = [];
        activityDatas.forEach(({ questions = [] }) => {
            questions.forEach(({ _id = '' }) => {
                quetionIds.push(_id.toString());
            });
        });

        const quetionORCondition = [];
        if (quetionIds.length) {
            quetionORCondition.push({ _id: { $in: quetionIds } });
        }
        if (courseObjectIds.length) {
            quetionORCondition.push({ courseId: { $in: courseObjectIds } });
        }
        if (userObjectIds.length) {
            quetionORCondition.push({ createdBy: { $in: userObjectIds } });
        }

        const quetionFilter = {
            isDeleted: false,
            ...(questionTypes.length && { questionType: { $in: questionTypes } }),
            ...(search && { text: { $regex: search, $options: 'i' } }),
            $or: quetionORCondition,
        };

        const [questions, totalDoc] = await Promise.all([
            questionSchema
                .find(quetionFilter, {
                    isDeleted: 1,
                    acceptQuestionResponse: 1,
                    sloIds: 1,
                    taxonomyIds: 1,
                    text: 1,
                    options: 1,
                    feedback: 1,
                    attachments: 1,
                    sessionId: 1,
                    _activityId: 1,
                    questionType: 1,
                    matchingOptions: 1,
                    mark: 1,
                    maxCharacterLimit: 1,
                    descriptionEnable: 1,
                    shuffleOptionOrder: 1,
                    description: 1,
                    characterLength: 1,
                    answerMatchingType: 1,
                    benchMark: 1,
                    answerTextVariant: 1,
                    courseId: 1,
                    createdBy: 1,
                })
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit)
                .populate({
                    path: 'courseId',
                    select: 'course_name',
                })
                .lean(),
            questionSchema.countDocuments(quetionFilter),
        ]);

        questions.forEach((quetionElement) => {
            if (!quetionElement?.courseId?.course_name) {
                const activityData = activityDatas.find(
                    (activityElement) =>
                        activityElement._id.toString() === quetionElement._activityId.toString(),
                );
                quetionElement.courseId = activityData?.courseId?._id || '';
                quetionElement.courseName = activityData?.courseId?.course_name || '';
                quetionElement.createdBy = activityData?.createdBy || '';
                quetionElement.quizName = activityData?.name || '';
            } else {
                quetionElement.courseName = quetionElement?.courseId?.course_name || '';
                quetionElement.courseId = quetionElement?.courseId?._id || '';
            }
            quetionElement.type = OLD;
        });

        const taxonomySet = new Set();
        const sloSet = new Set();
        const questionList = questions.map((questionElement) => {
            questionElement.taxonomyIds?.forEach((id) => taxonomySet.add(id));
            questionElement.sloIds?.forEach((id) => sloSet.add(cs(id)));
            return {
                ...questionElement,
                mark: questionElement.mark || 1,
            };
        });

        const formattedQuestions = await formatQuestionsWithOutAttachmentLink({
            questions: questionList,
            taxonomyIds: Array.from(taxonomySet),
            sloIds: Array.from(sloSet),
        });

        return {
            statusCode: 200,
            data: {
                totalDoc,
                totalPages: Math.ceil(totalDoc / +limit),
                currentPage: pageNo,
                questions: formattedQuestions,
            },
        };
    } catch (err) {
        throw err instanceof Error ? err : new Error(String(err));
    }
};
exports.createOrUpdateQuestion = async ({ headers = {}, body = {} }) => {
    try {
        const {
            questionId,
            name,
            questionType,
            options = [],
            matchingOptions = [],
            feedback,
            sloIds = [],
            taxonomyIds = [],
            attachments,
            mark,
            mandatory,
            descriptionEnable,
            shuffleOptionOrder,
            description,
            characterLength,
            maxCharacterLimit,
            answerMatchingType,
            benchMark,
            answerTextVariant,
            sessionId,
            courseId,
        } = body;

        const { _user_id } = headers;

        const questionData = {
            text: name,
            questionType,
            courseId: convertToMongoObjectId(courseId),
            options: options.map((optionElement) => ({
                answer: optionElement.answer,
                text: optionElement.name,
                attachments: optionElement.attachments,
            })),
            matchingOptions,
            feedback,
            sloIds: (sloIds || []).map(convertToMongoObjectId),
            taxonomyIds: (taxonomyIds || []).map(convertToMongoObjectId),
            attachments,
            mark,
            mandatory,
            descriptionEnable,
            shuffleOptionOrder,
            description,
            characterLength,
            maxCharacterLimit,
            answerMatchingType,
            benchMark,
            answerTextVariant,
            sessionId: sessionId ? convertToMongoObjectId(sessionId) : undefined,
            createdBy: convertToMongoObjectId(_user_id),
        };

        const updatedQuestion = await questionSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(questionId),
                createdBy: convertToMongoObjectId(_user_id),
            },
            { $set: questionData },
            { new: true, upsert: true },
        );
        if (updatedQuestion) {
            return { statusCode: 200, data: updatedQuestion };
        }
        return { statusCode: 422, message: 'Not authorized or question not found' };
    } catch (err) {
        throw err instanceof Error ? err : new Error(String(err));
    }
};

// revamped ends here
