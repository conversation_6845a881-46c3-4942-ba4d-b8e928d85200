const mongoose = require('mongoose');
const {
    ASSIGNMENT,
    S<PERSON><PERSON>ITTED,
    PENDING,
    COMPLETED,
    EXCUSED,
    TO_GRADE,
    IN_GRADING,
    IN_REVIEW,
    GRADED,
    RELEASED,
    RESUBMITTED,
    ASSIGNMENT_RUBRICS,
    USER,
    TAXONOMY,
    DIGI_COURSE,
    INSTITUTION_CALENDAR,
} = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const dateFields = {
    type: { type: String, default: 'Date' },
    dateAndTime: { type: Date, default: new Date() },
    duration: {
        value: { type: String },
        type: { type: String },
    },
    courseOrSession: {
        type: { type: String },
        after: { type: String },
    },
    previousDate: {
        date: {
            type: Date,
        },
        reason: {
            type: String,
        },
    },
};
const assignmentSchemas = new Schema(
    {
        _institution_id: {
            type: ObjectId,
        },
        _institution_calendar_id: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        _course_id: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        course_code: {
            type: String,
        },
        courseName: { type: String },
        programName: { type: String },
        _program_id: {
            type: ObjectId,
        },
        contributor: { type: ObjectId },
        year_no: {
            type: String,
        },
        level_no: {
            type: String,
        },
        rotation_count: {
            type: Number,
        },
        term: { type: String },
        createdBy: {
            type: ObjectId,
            ref: USER,
        },
        subject: [
            {
                subjectName: { type: String },
                subjectId: ObjectId,
            },
        ],
        isDraft: {
            type: Boolean,
            default: false,
        },
        pause: {
            type: Boolean,
            default: false,
        },
        hide: {
            type: Boolean,
            default: false,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        deletedReason: {
            type: String,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isRecurAssignment: {
            type: Boolean,
            default: false,
        },
        isDuplicate: {
            type: Boolean,
            default: false,
        },
        isCopyFrom: {
            type: ObjectId,
        },
        notificationStatus: {
            type: String,
        },
        reportCreated: {
            type: Boolean,
            default: false,
        },
        basic: {
            title: { type: String },
            description: { type: String },
            instructionAttachment: [
                {
                    url: { type: String },
                    signedUrl: { type: String },
                    sizeInKb: { type: Number },
                    name: { type: String },
                },
            ],
            type: { type: String },
            category: { name: { type: String }, _id: { type: ObjectId } },
            sessionUnit: [
                {
                    _session_id: { type: ObjectId },
                    session: String,
                    selectedSession: { type: ObjectId },
                },
            ],
            totalScore: { type: Number },
            scoringType: { type: String, default: 'Grade' },
            gradeAs: { type: String, default: 'Marks(Mk)' },
            totalAttempts: { type: Number, default: 0 },
            allowAssignmentsInFinalGrades: { type: Boolean, default: false },
            learningOutcome: {
                type: { type: String },
                lo: [{ type: ObjectId }],
            },
            taxonomy: [{ type: ObjectId, ref: TAXONOMY }],
            taxonomyScope: { type: String, default: 'None' },
            evaluationTools: { type: [String] },
            prompts: { type: [String] },
            isGroupProject: { type: Boolean, default: false },
            evaluation: {
                type: { type: String, default: 'None' },
                rubricsId: { type: ObjectId, ref: ASSIGNMENT_RUBRICS },
            },
            scoreUnit: { type: String, default: 'Mark' },
        },
        submission: {
            isGroupProject: { type: Boolean },
            assignTo: [
                {
                    isGroup: Boolean,
                    isAllStudent: Boolean,
                    studentIds: [
                        {
                            studentId: String,
                            name: String,
                            academicId: String,
                        },
                    ],
                    groups: [
                        {
                            groupId: ObjectId,
                            name: String,
                            subGroup: [
                                {
                                    subGroupId: ObjectId,
                                    subGroupName: String,
                                },
                            ],
                        },
                    ],
                    specialDue: [{ dueDate: Date, description: String }],
                },
            ],
            specialDue: [{ dueDate: Date, description: String }],
            isEnable: { type: Boolean, default: false },
            isDue: { type: Boolean, default: false },
            due: dateFields,
            start: dateFields,
            end: dateFields,
            remindStudents: {
                checked: { type: Boolean },
                duration: { type: String },
            },
            recurring: {
                checked: { type: Boolean },
                type: { type: String },
                at: { type: String },
                days: { type: [String] },
                time: { type: String },
            },
            includePreviousDiscussions: { type: Boolean },
            recurWhenThisCourseIsActive: { type: Boolean },
        },
        evaluation: {
            evaluationBy: { type: [String] },
            evaluators: [
                {
                    evaluatorId: {
                        type: ObjectId,
                        ref: USER,
                    },
                    evaluatorName: {
                        type: String,
                    },
                    assignmentGroupIds: { type: [String] },
                    studentIds: { type: [String] },
                    prompts: { type: [String] },
                    isReconciler: { type: Boolean, default: false },
                },
            ],
            gradingScore: { type: String },
            dueDays: { type: Number },
            setAnonymousStudent: { type: Boolean, default: false },
            setAnonymousStaff: { type: Boolean, default: false },
            checkPlagiarism: { type: Boolean, default: true },
            multiStaffMultiStudent: {
                type: Boolean,
                default: false,
            },
            isStudentsAnonymous: {
                type: Boolean,
                default: false,
            },
        },
        reflection: {
            addReflectionQuestions: { type: Boolean, default: false },
        },
        publishedAt: { type: Date },
        generalSettings: {
            extendAssignmentLimit: { type: Boolean },
            rubricsVisibleToStudent: { type: Boolean },
            randomisePrompt: { type: Boolean },
            studentSeeAfterAttempting: { type: String },
            allowStudentToUnSubmit: { type: Boolean },
            allowStudentToCheckPlagiarism: { type: Boolean },
            allowStudentToAttachFile: { type: Boolean },
            fileType: { type: [String] },
            fileSizeLimit: { type: String },
            addDiscussionThread: { type: Boolean },
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSIGNMENT, assignmentSchemas);
