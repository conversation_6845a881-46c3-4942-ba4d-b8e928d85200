const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const Types = mongoose.Types;
const constant = require('../../utility/constants');
const { UNIVERSITY, COLLEGE } = require('../../utility/enums');

const institutionSchema = new Schema(
    {
        type: {
            type: String,
            required: true,
            enum: [UNIVERSITY, COLLEGE],
        },
        name: {
            type: String,
            required: true,
        },
        code: {
            type: String,
            required: true,
        },
        logo: {
            type: String,
            required: true,
        },
        accreditation: [
            {
                accreditationAgencyName: String,
                accreditationType: String,
                accreditationNumber: Number,
                accreditationValue: String,
                validityStart: Date,
                validityEnd: Date,
                others: String,
            },
        ],
        parentInstitute: {
            type: Types.ObjectId,
            default: null,
        },
        noOfColleges: {
            type: Number,
            default: 0,
        },
        instituteDescription: {
            description: { type: String },
            mediaURL: String,
        },
        portfolio: [
            {
                title: String,
                description: String,
                mediaURL: String,
            },
        ],
        address: {
            address: String,
            country: {
                name: String,
                countryId: Number,
            },
            state: {
                name: String,
                stateId: Number,
            },
            district: String,
            city: {
                name: String,
            },
            zipCode: String,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isUniversity: {
            type: Boolean,
        },
        _created_by: {
            type: Types.ObjectId,
            default: null,
        },
    },
    { timestamps: true },
);

module.exports = institutionSchema;
