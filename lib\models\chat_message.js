const { USER, CHAT_GROUP, CHAT_MESSAGE } = require('../utility/constants');
const {
    Schema,
    Types: { ObjectId },
    model,
} = require('mongoose');

const chatV2Schema = Schema(
    {
        msg: { type: String },
        createdBy: {
            type: ObjectId,
            ref: USER,
            required: true,
        },
        fileName: { type: String },
        fileType: [{ type: String }],
        messageType: { type: String },
        url: { type: String },
        attachment: { type: String },
        members: [
            {
                type: ObjectId,
                ref: USER,
                required: true,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        channelId: {
            required: true,
            type: ObjectId,
            ref: CHAT_GROUP,
        },
        deletedFor: [{ type: ObjectId, ref: USER }],
        replyFor: {
            messageId: { type: ObjectId, ref: CHAT_MESSAGE },
            msg: { type: String },
            url: { type: String },
            createdBy: { type: String },
            fileType: { type: String },
            fileName: { type: String },
            attachment: { type: String },
        },
    },
    { timestamps: true },
);
module.exports = model(CHAT_MESSAGE, chatV2Schema);
