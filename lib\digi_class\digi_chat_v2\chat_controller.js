const courseScheduleSchema = require('../../models/course_schedule');
const studentGroupsSchema = require('../../models/student_group');
const chatGroupSchema = require('../../models/chat_group');
const messageSchema = require('../../models/chat_message');
const userSchema = require('../../models/user');
const courseSchema = require('../../models/digi_course');
const {
    DC_STAFF,
    SCHEDULE_TYPES: { REGULAR },
    ALL_CHAT,
    ALL,
    COURSE_CHAT,
    EVENT_WHOM: { STUDENT, STAFF },
    PRIVATE,
    UNREAD,
    COMPLETED,
    PARTICIPANTS,
    MEDIA,
    DOCS,
    LINKS,
    CHANNEL,
} = require('../../utility/constants');
const {
    SERVICES: { DIGI_CHAT },
    logger,
} = require('../../utility/util_keys');
const { convertToMongoObjectId, sendResponseWithRequest } = require('../../utility/common');
const {
    getUserChannelList,
    getScheduledActiveCalendars,
    getOverallGroup,
    checkAndCreateGroups,
    getChannelDetails,
} = require('./chat_service');
const { s3FileURL } = require('../../utility/file_upload');
const { BUCKET_DOCUMENT } = require('../../utility/util_keys');
const { getSignedURL } = require('../../utility/common_functions');
const dcCronAxios = require('../../utility/dcCron.axios');

exports.createCourseChannels = async (req, res) => {
    try {
        const { programId, userId, type } = req.body;
        if (DIGI_CHAT === 'true') {
            const { userCalendarList = [] } = await getScheduledActiveCalendars({
                // get  calendars which  has schedules for  current userId
                userId,
                userType: type,
                isCourseAdmin: false,
            });

            const studentGroupQuery = {
                _institution_calendar_id: { $in: userCalendarList },
                'groups.courses.setting.session_setting.groups._student_ids': { $exists: true },
            };
            let queryData;
            let schedulesForStaff;

            if (type === DC_STAFF) {
                schedulesForStaff = await courseScheduleSchema
                    .find(
                        {
                            _institution_calendar_id: { $in: userCalendarList },
                            'staffs._staff_id': convertToMongoObjectId(userId),
                            type: REGULAR,
                        },
                        {
                            term: 1,
                            _student_group_id: 1,
                            'student_groups.gender': 1,
                            'student_groups.group_no': 1,
                            'student_groups.group_id': 1,
                            'student_groups.session_group.session_group_id': 1,
                            year_no: 1,
                            level_no: 1,
                            rotation_count: 1,
                            _course_id: 1,
                            'staffs._staff_id': 1,
                            _program_id: 1,
                            _institution_calendar_id: 1,
                        },
                    )
                    .lean();
                queryData = schedulesForStaff.reduce(
                    (acc, curr) => {
                        acc._course_id.push(curr._course_id);
                        acc.term.push(curr.term);
                        acc.year_no.push(curr.year_no);
                        acc.level_no.push(curr.level_no);
                        acc.sGroupId.push(curr._student_group_id);
                        acc._program_id.push(curr._program_id);
                        return acc;
                    },
                    {
                        _course_id: [],
                        term: [],
                        sGroupId: [],
                        year_no: [],
                        level_no: [],
                        _program_id: [],
                    },
                );

                studentGroupQuery._id = { $in: queryData.sGroupId };
                studentGroupQuery['master._program_id'] = { $in: queryData._program_id };
                studentGroupQuery['master.year'] = { $in: queryData.year_no };
            } else {
                schedulesForStaff = await courseScheduleSchema
                    .find(
                        {
                            _institution_calendar_id: { $in: userCalendarList },
                            'students._id': convertToMongoObjectId(userId),
                            type: REGULAR,
                            // ...(programId &&
                            //     programId.length && {
                            //         _program_id: convertToMongoObjectId(programId),
                            //     }),
                        },
                        {
                            term: 1,
                            _student_group_id: 1,
                            'student_groups.gender': 1,
                            'student_groups.group_no': 1,
                            'student_groups.group_id': 1,
                            year_no: 1,
                            level_no: 1,
                            rotation_count: 1,
                            _course_id: 1,
                            'staffs._staff_id': 1,
                            _program_id: 1,
                            _institution_calendar_id: 1,
                        },
                    )
                    .lean();
                studentGroupQuery['groups.courses.setting.session_setting.groups._student_ids'] =
                    convertToMongoObjectId(userId);
            }

            const studentGroups = await studentGroupsSchema
                .find(studentGroupQuery, {
                    _institution_calendar_id: 1,
                    'master.year': 1,
                    'master._program_id': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.rotation': 1,
                    'groups.group_mode': 1,
                    'groups.rotation_count': 1,
                    'groups.courses.course_name': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting._id': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.courses.setting.session_setting.groups.group_no': 1,
                })
                .lean();
            if (!studentGroups.length) {
                return sendResponseWithRequest(
                    req,
                    res,
                    404,
                    false,
                    'no data found with this request',
                );
            }
            const { overAllGroup, queryForChatGroup } = getOverallGroup({
                userCalendarList,
                studentGroups,
                schedulesForStaff,
                DC_STAFF,
                type,
                userId,
            });
            if (overAllGroup.length) {
                const groupsFromDB = await chatGroupSchema
                    .find(
                        {
                            courseId: { $in: queryForChatGroup.courseId },
                            // programId: { $in: queryForChatGroup.programId },
                            year: { $in: queryForChatGroup.year },
                            level: { $in: queryForChatGroup.level },
                            term: { $in: queryForChatGroup.term },
                            isDeleted: false,
                        },
                        {
                            channelName: 1,
                            'members.userId': 1,
                            courseId: 1,
                            channelType: 1,
                            admin: 1,
                            year: 1,
                            level: 1,
                            term: 1,
                            gender: 1,
                            rotation: 1,
                            rotationCount: 1,
                            programId: 1,
                            _institution_calendar_id: 1,
                        },
                    )
                    .lean();

                const bulkOperation =
                    checkAndCreateGroups({
                        overAllGroup,
                        studentGroupsFromDB: groupsFromDB,
                        schedulesForStaff,
                        userId,
                    }) ?? [];
                if (bulkOperation.length) {
                    const bulkWrite = await chatGroupSchema.bulkWrite(bulkOperation);
                }
            }
        }
        return sendResponseWithRequest(req, res, 200, true, 'success');
    } catch (error) {
        logger.error(`${error.message} - ${error.stack}  `);
        return sendResponseWithRequest(req, res, 500, false, 'Request failed');
    }
};

exports.getUserChannelsList = async (req, res) => {
    try {
        const { institutionCalendarId, userId, courseId, rotationCount, term, level } = req.body;
        const userChannelsWithUnreadCount = await getUserChannelList({
            institutionCalendarId,
            userId,
            courseId,
            rotationCount,
            term,
            level,
        });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('success'),
            userChannelsWithUnreadCount,
        );
    } catch (error) {
        logger.error(`${error.message} in ${error.stack}`);
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};
exports.userChannelMessage = async (req, res) => {
    try {
        const { _user_id } = req.headers;
        const { channelId, page = 1, limit = 10 } = req.query;
        const skip = (page - 1) * limit;
        const chatMessageQuery = {
            channelId: convertToMongoObjectId(channelId),
            isDeleted: false,
            deletedFor: { $nin: [convertToMongoObjectId(_user_id)] },
        };
        const chennelDoc = await chatGroupSchema.findById(channelId, { members: 1 }).lean();
        if (chennelDoc) {
            let blockedDate = null;
            for (const memberElement of chennelDoc.members) {
                if (memberElement.userId.toString() === _user_id && memberElement.isBlocked)
                    blockedDate = memberElement.blockedDate;
            }
            if (blockedDate) {
                chatMessageQuery.createdAt = { $lte: blockedDate };
            }
        }
        const channelMessages = await messageSchema
            .find(chatMessageQuery, {
                msg: 1,
                createdBy: 1,
                attachment: 1,
                fileName: 1,
                createdAt: 1,
                replyFor: 1,
                fileType: 1,
                url: 1,
            })
            .sort({ _id: -1 })
            .skip(skip)
            .limit(limit)
            .populate({ path: 'createdBy', select: { name: 1 } })
            .lean();
        const totalMsgCount = await messageSchema.countDocuments(chatMessageQuery);
        sendResponseWithRequest(req, res, 200, true, req.t('success'), {
            channelId,
            messages: channelMessages,
            totalCount: totalMsgCount,
        });
    } catch (error) {
        logger.error(`${error.message} in ${error.stack}`);
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};
exports.groupInfo = async (req, res) => {
    try {
        const { channelId } = req.query;
        const channelData = await chatGroupSchema
            .findOne(
                { _id: convertToMongoObjectId(channelId) },
                {
                    'members.userId': 1,
                },
            )
            .populate({ path: 'members.userId', select: { name: 1, user_id: 1, role: 1 } })
            .lean();
        return sendResponseWithRequest(req, res, 200, true, req.t('success'), channelData);
    } catch (error) {
        logger.error(
            'digi_chat => chat_controller  => digiChatCreateCourseChannels' + error.message,
        );
        sendResponseWithRequest(req, res, 500, false, error.message);
    }
};

exports.createFileMessage = async (req, res) => {
    try {
        const { files } = req;
        const attachment = files.map((fileElement) => {
            const match = fileElement.location.match(/\/(\d+)-/);
            const timestamp = match ? match[1] : null;
            const fileName =
                timestamp + '-' + fileElement.originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            return s3FileURL({
                filePath: BUCKET_DOCUMENT,
                fileName,
            });
        });
        if (attachment.length) {
            sendResponseWithRequest(req, res, 200, true, 'documents inserted', attachment);
        } else {
            sendResponseWithRequest(req, res, 200, false, 'No documents inserted');
        }
    } catch (error) {
        logger.error('digi_chat => chat_controller  => SendMessages' + error.message);
        sendResponseWithRequest(req, res, 500, false, error.message);
    }
};

exports.generateUrl = async (req, res) => {
    try {
        const { url } = req.query;
        const signedUrl = await getSignedURL(url);
        sendResponseWithRequest(req, res, 200, true, 'signed url', signedUrl);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserChatCount = async (req, res) => {
    try {
        const { institutionCalendarId, userId } = req.query;
        const chatQuery = {
            $or: [
                { _institution_calendar_id: convertToMongoObjectId(institutionCalendarId) },
                { $and: [{ channelType: PRIVATE }, { 'members.userType': STAFF }] },
            ],
            'members.userId': convertToMongoObjectId(userId),
            isDeleted: false,
        };
        const userChannelList = await chatGroupSchema.find(chatQuery, { _id: 1 }).lean();
        if (!userChannelList.length)
            return sendResponseWithRequest(req, res, 200, true, 'messageCount', 0);
        const unreadChannelCountResult = await messageSchema.aggregate([
            {
                $match: {
                    channelId: { $in: userChannelList.map(({ _id }) => _id) },
                    isDeleted: false,
                    members: { $nin: [convertToMongoObjectId(userId)] },
                },
            },
            {
                $group: {
                    _id: '$channelId',
                },
            },
            {
                $count: 'unreadChannelCount',
            },
        ]);
        const unreadChannelCount =
            unreadChannelCountResult.length > 0
                ? unreadChannelCountResult[0].unreadChannelCount
                : 0;
        return sendResponseWithRequest(req, res, 200, true, 'messageCount', unreadChannelCount);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};
exports.getChannelsList = async (req, res) => {
    try {
        const {
            institutionCalendarId,
            userId,
            userName,
            tab,
            mode,
            searchKey,
            page = 1,
            limit = 10,
        } = req.query;
        const skip = (page - 1) * limit;
        const chatQuery = {
            $or: [
                { _institution_calendar_id: convertToMongoObjectId(institutionCalendarId) },
                { $and: [{ channelType: PRIVATE }, { 'members.userType': STAFF }] },
            ],
            'members.userId': convertToMongoObjectId(userId),
            isDeleted: false,
            ...(searchKey && {
                $or: [
                    { channelName: { $regex: new RegExp(searchKey, 'i') } },
                    { course_no: { $regex: new RegExp(searchKey, 'i') } },
                    { 'members.academicNo': { $regex: new RegExp(searchKey, 'i') } },
                ],
            }),
        };
        const commonFields = {
            _id: 1,
            channelName: 1,
            channelType: 1,
            rotation: 1,
            rotationCount: 1,
            term: 1,
            course_no: 1,
            course_name: 1,
            level: 1,
            year: 1,
            programId: 1,
            members: 1,
            admin: 1,
        };
        const programNamePopulate = { path: 'programId', select: { name: 1 } };
        if (tab === STUDENT || tab === STAFF) chatQuery.channelType = PRIVATE;
        else if (mode === COURSE_CHAT) chatQuery.channelType = { $ne: PRIVATE };
        const userChannelList = await chatGroupSchema
            .find(chatQuery, commonFields)
            .populate(programNamePopulate)
            .lean();
        let channelDetails = await getChannelDetails({
            availableChannels: userChannelList,
            userId,
        });
        if (tab === STUDENT || tab === STAFF) {
            channelDetails = channelDetails.filter((channelDetailElement) => {
                const firstUserId = channelDetailElement.members[0]?.userId?.toString();
                let allUserIdsSame = true;
                let someConditionMet = false;
                for (const memberElement of channelDetailElement.members) {
                    const memberUserId = memberElement?.userId?.toString();
                    if (memberUserId !== firstUserId) {
                        allUserIdsSame = false;
                    }
                    if (memberUserId !== userId && memberElement.userType === tab) {
                        someConditionMet = true;
                        break;
                    }
                }
                return someConditionMet || allUserIdsSame;
            });
        } else if (tab === UNREAD)
            channelDetails = channelDetails.filter((channelElement) => channelElement.unreadCount);
        if (mode !== COURSE_CHAT) {
            const privatChannelIds = [];
            for (const channelElement of channelDetails) {
                if (channelElement.channelType === PRIVATE) {
                    const [firstName, secondName] = channelElement.channelName.split('-');
                    channelElement.channelName = firstName === userName ? secondName : firstName;
                    privatChannelIds.push(channelElement._id);
                }
            }
            if (privatChannelIds.length) {
                const messageQuery = {
                    channelId: { $in: privatChannelIds },
                    isDeleted: false,
                    deletedFor: { $ne: convertToMongoObjectId(userId) },
                };
                const messageChannelIds = new Set(
                    (await messageSchema.distinct('channelId', messageQuery)).map((channelId) =>
                        channelId.toString(),
                    ),
                );
                channelDetails = channelDetails.filter((channelDetailsElement) => {
                    if (channelDetailsElement.channelType === PRIVATE) {
                        if (channelDetailsElement.currentUser) {
                            return (
                                (channelDetailsElement.userType === tab || tab === ALL) &&
                                messageChannelIds.has(channelDetailsElement._id.toString())
                            );
                        }
                        return messageChannelIds.has(channelDetailsElement._id.toString());
                    }
                    return true;
                });
            }
        }
        const totalPage = Math.ceil(channelDetails.length / Number(limit));
        channelDetails = channelDetails.slice(skip, skip + Number(limit));
        for (const channelElement of channelDetails)
            if (channelElement.channelType === PRIVATE)
                for (const memberElement of channelElement.members)
                    if (memberElement.userId.toString() !== userId && memberElement.academicNo)
                        channelElement.academicNo = memberElement.academicNo;
        return sendResponseWithRequest(req, res, 200, true, 'CHAT_LIST', {
            channelDetails,
            totalPage,
        });
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};
exports.createPrivateChannel = async (req, res) => {
    try {
        const { _institution_calendar_id, from, to } = req.body;
        const channelNames = [`${from.name}-${to.name}`, `${to.name}-${from.name}`];
        const chatGroup = await chatGroupSchema
            .findOne(
                {
                    channelName: { $in: channelNames },
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    channelType: PRIVATE,
                    isDeleted: false,
                },
                { _id: 1 },
            )
            .lean();
        if (!chatGroup) {
            const newChatGroup = await chatGroupSchema.create({
                _institution_calendar_id,
                channelName: `${from.name}-${to.name}`,
                channelType: PRIVATE,
                members: [
                    {
                        userId: from.userId,
                        userType: from.type,
                        academicNo: from.academicNo,
                    },
                    {
                        userId: to.userId,
                        userType: to.type,
                        academicNo: to.academicNo,
                    },
                ],
                admin: [from.userId],
                to: `${from.type}-${to.type}`,
            });
            return sendResponseWithRequest(req, res, 200, true, 'CHAT_CREATED', {
                channelId: newChatGroup._id,
            });
        }
        return sendResponseWithRequest(req, res, 200, true, 'ALREADY_CHAT_AVAILABLE', {
            channelId: chatGroup._id,
        });
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, error.message);
    }
};

exports.getUserList = async (req, res) => {
    try {
        const { _user_id } = req.headers;
        const { type, searchKey, institutionCalendarId } = req.query;
        let users = [];
        const userIds = new Set();
        const searchWords = searchKey.trim().split(/\s+/);
        const searchKeyLower = searchKey.toLowerCase();
        if (type === STAFF || !type) {
            const queryFilter = {
                $or: [
                    {
                        $and: [
                            { 'name.first': { $regex: new RegExp(searchWords[0], 'i') } },
                            { 'name.last': { $regex: new RegExp(searchWords[1], 'i') } },
                        ],
                    },
                    { 'name.first': { $regex: new RegExp(searchKey, 'i') } },
                    { 'name.last': { $regex: new RegExp(searchKey, 'i') } },
                    { user_id: { $regex: new RegExp(searchKey, 'i') } },
                ],
                status: COMPLETED,
                isDeleted: false,
                isActive: true,
                _id: { $ne: convertToMongoObjectId(_user_id) },
                ...(type && { user_type: STAFF }),
            };
            users = await userSchema
                .find(queryFilter, {
                    _id: 1,
                    'name.first': 1,
                    'name.last': 1,
                    user_id: 1,
                })
                .limit(10)
                .lean();
            users.forEach((userElement) => userIds.add(userElement._id.toString()));
        } else if (type === STUDENT) {
            const queryFilter = {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                $or: [
                    ...(searchWords.length === 2
                        ? [
                              {
                                  $and: [
                                      {
                                          'groups.students.name.first': {
                                              $regex: new RegExp(searchWords[0], 'i'),
                                          },
                                      },
                                      {
                                          'groups.students.name.last': {
                                              $regex: new RegExp(searchWords[1], 'i'),
                                          },
                                      },
                                  ],
                              },
                          ]
                        : []),
                    { 'groups.students.name.first': { $regex: searchKey } },
                    { 'groups.students.name.last': { $regex: searchKey } },
                    { 'groups.students.academic_no': { $regex: searchKey } },
                ],
            };
            const studentList = await studentGroupsSchema
                .find(queryFilter, {
                    'groups.students._student_id': 1,
                    'groups.students.name.first': 1,
                    'groups.students.name.last': 1,
                    'groups.students.academic_no': 1,
                })
                .lean();
            for (const studentGroupElement of studentList) {
                if (userIds.size > 10) break;
                for (const groupElement of studentGroupElement.groups) {
                    if (userIds.size > 10) break;
                    for (const studentElement of groupElement.students) {
                        if (userIds.size > 10) break;
                        const studentId = studentElement._student_id.toString();
                        const fullName =
                            `${studentElement.name.first} ${studentElement.name.last}`.toLowerCase();
                        if (
                            studentId != _user_id &&
                            !userIds.has(studentId) &&
                            (fullName.includes(searchKeyLower) ||
                                studentElement.academic_no.toLowerCase().includes(searchKeyLower))
                        ) {
                            userIds.add(studentId);
                            users.push({
                                _id: studentId,
                                academic_no: studentElement.academic_no,
                                name: studentElement.name,
                            });
                        }
                    }
                }
            }
        }
        const chatGroupData = await chatGroupSchema
            .find(
                {
                    $and: [
                        { 'members.userId': _user_id },
                        { 'members.userId': { $in: Array.from(userIds) } },
                    ],
                    channelType: PRIVATE,
                    isDeleted: false,
                },
                { _id: 1, members: 1 },
            )
            .lean();
        const chatMap = {};
        const channelIds = [];
        const blockedDates = {};
        for (const chatGroupElement of chatGroupData) {
            channelIds.push(chatGroupElement._id);
            for (const memberElement of chatGroupElement.members) {
                const userId = memberElement?.userId?.toString();
                if (userIds.has(userId) && !chatMap[userId] && userId !== _user_id) {
                    chatMap[userId] = {
                        channelId: chatGroupElement._id.toString(),
                        members: chatGroupElement.members,
                    };
                    if (memberElement?.isBlocked) {
                        chatMap[userId] = {
                            ...chatMap[userId],
                            isBlocked: memberElement.isBlocked,
                            blockedDate: memberElement.blockedDate,
                        };
                        blockedDates[chatGroupElement._id] = memberElement?.blockedDate;
                    }
                }
            }
        }
        const messageData = await messageSchema.aggregate([
            {
                $match: {
                    channelId: { $in: channelIds },
                    isDeleted: false,
                    deletedFor: { $nin: [convertToMongoObjectId(_user_id)] },
                },
            },
            {
                $addFields: {
                    blockedDate: {
                        $cond: {
                            if: {
                                $in: [
                                    '$channelId',
                                    Object.keys(blockedDates).map((blockedDateElement) =>
                                        convertToMongoObjectId(blockedDateElement),
                                    ),
                                ],
                            },
                            then: {
                                $let: {
                                    vars: {
                                        matchedItem: {
                                            $filter: {
                                                input: Object.entries(blockedDates).map(
                                                    ([blockedDateElement, date]) => ({
                                                        id: convertToMongoObjectId(
                                                            blockedDateElement,
                                                        ),
                                                        date,
                                                    }),
                                                ),
                                                cond: { $eq: ['$$this.id', '$channelId'] },
                                            },
                                        },
                                    },
                                    in: { $arrayElemAt: ['$$matchedItem.date', 0] },
                                },
                            },
                            else: null,
                        },
                    },
                },
            },
            {
                $match: {
                    $or: [
                        { blockedDate: { $eq: null } },
                        { $expr: { $lte: ['$createdAt', '$blockedDate'] } },
                    ],
                },
            },
            { $sort: { _id: -1 } },
            {
                $group: {
                    _id: '$channelId',
                    createdAt: { $first: '$createdAt' },
                    members: { $first: '$members' },
                    channelId: { $first: '$channelId' },
                },
            },
            {
                $project: {
                    _id: 0,
                    createdAt: 1,
                    members: 1,
                    channelId: 1,
                },
            },
        ]);
        const messageMap = {};
        const channelLastMessage = {};
        for (const messageElement of messageData) {
            channelLastMessage[messageElement.channelId.toString()] = messageElement.createdAt;
            for (const memberElement of messageElement.members) {
                if (!messageMap[memberElement.toString()])
                    messageMap[memberElement.toString()] = {
                        createdAt: messageElement.createdAt,
                        channelId: messageElement.channelId.toString(),
                    };
            }
        }
        for (const userElement of users) {
            if (userElement._id.toString() === _user_id) userElement.currentUser = true;
            if (type === STAFF) userElement.academic_no = userElement.user_id;
            delete userElement.user_id;
            if (messageData && messageMap[userElement._id.toString()]) {
                userElement.time = messageMap[userElement._id.toString()]?.createdAt || null;
                userElement.channelId = messageMap[userElement._id.toString()]?.channelId || null;
                userElement.members = chatMap[userElement._id.toString()]?.members || [];
            } else if (chatMap[userElement._id.toString()]) {
                userElement.channelId = chatMap[userElement._id.toString()]?.channelId || null;
                userElement.time =
                    channelLastMessage[chatMap[userElement._id.toString()]?.channelId] || null;
                userElement.members = chatMap[userElement._id.toString()]?.members || [];
            }
            userElement.isBlocked = chatMap[userElement._id.toString()]?.isBlocked || false;
        }
        return sendResponseWithRequest(req, res, 200, true, 'USER_LIST', users);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};

exports.getChatProfile = async (req, res) => {
    try {
        const { _user_id } = req.headers;
        const { channelId, page = 1, limit = 6, tab, type } = req.query;
        const pageNumber = parseInt(page);
        const pageLimit = parseInt(limit);
        if (tab === PARTICIPANTS) {
            const chatParticipants = await chatGroupSchema
                .findOne(
                    { _id: convertToMongoObjectId(channelId) },
                    {
                        _institution_calendar_id: 1,
                        term: 1,
                        year: 1,
                        level: 1,
                        rotation: 1,
                        rotationCount: 1,
                        courseId: 1,
                        programId: 1,
                        members: 1,
                    },
                )
                .lean();
            if (!chatParticipants)
                return sendResponseWithRequest(req, res, 404, false, req.t('No data found'));
            const courseDocs = await courseSchema
                .findOne(
                    {
                        _id: chatParticipants.courseId,
                        'coordinators._institution_calendar_id':
                            chatParticipants._institution_calendar_id,
                        'coordinators.term': chatParticipants.term,
                        'coordinators.year': chatParticipants.year,
                        'coordinators.level_no': chatParticipants.level,
                    },
                    {
                        'coordinators._institution_calendar_id': 1,
                        'coordinators.term': 1,
                        'coordinators.year': 1,
                        'coordinators.level_no': 1,
                        'coordinators._user_id': 1,
                    },
                )
                .lean();
            const courseAdmin = new Set();
            const userData = await userSchema
                .find(
                    {
                        _id: {
                            $in: chatParticipants.members.map(
                                (memberElement) => memberElement.userId,
                            ),
                        },
                    },
                    {
                        'name.first': 1,
                        'name.last': 1,
                        user_type: 1,
                        user_id: 1,
                    },
                )
                .lean();
            const userDetails = new Map(userData.map((user) => [user._id.toString(), user]));
            if (courseDocs) {
                for (const coordinatorElement of courseDocs.coordinators) {
                    if (
                        coordinatorElement._institution_calendar_id.toString() ===
                            chatParticipants._institution_calendar_id.toString() &&
                        coordinatorElement.term.toString() === chatParticipants.term.toString() &&
                        coordinatorElement.year.toString() === chatParticipants.year.toString() &&
                        coordinatorElement.level_no.toString() === chatParticipants.level.toString()
                    )
                        courseAdmin.add(coordinatorElement._user_id.toString());
                }
            }
            const userIds = [];
            chatParticipants.members = chatParticipants.members.map((memberElement) => {
                if (memberElement.userId.toString() !== _user_id)
                    userIds.push(memberElement.userId);
                const user = userDetails.get(memberElement.userId.toString());
                if (user) {
                    return {
                        ...memberElement,
                        name: user.name,
                        userType: user.user_type,
                        academicNo: user.user_id,
                        isCourseAdmin: courseAdmin.has(memberElement.userId.toString()),
                    };
                }
                return memberElement;
            });
            const privateChannel = await chatGroupSchema
                .find(
                    {
                        channelType: PRIVATE,
                        isDeleted: false,
                        $and: [
                            { 'members.userId': _user_id },
                            { 'members.userId': { $in: userIds } },
                        ],
                    },
                    { _id: 1, members: 1 },
                )
                .lean();
            const channel = new Map();
            for (const channelElement of privateChannel) {
                for (const memberElement of channelElement.members) {
                    if (memberElement.userId.toString() !== _user_id) {
                        channel.set(memberElement.userId.toString(), channelElement._id.toString());
                    }
                }
            }
            for (const memberElement of chatParticipants.members) {
                if (memberElement.userId.toString() !== _user_id) {
                    memberElement.channelId = channel.get(memberElement.userId.toString()) ?? null;
                }
            }
            return sendResponseWithRequest(req, res, 200, true, 'chat participants', {
                participants: chatParticipants.members,
            });
        }
        const chatMessages = await messageSchema.aggregate([
            {
                $match: {
                    channelId: convertToMongoObjectId(channelId),
                    isDeleted: false,
                    ...(type ? { fileType: type } : { fileType: { $ne: null } }),
                },
            },
            {
                $addFields: {
                    attachmentData: {
                        $cond: [
                            { $or: [{ $in: [DOCS, '$fileType'] }, { $in: [MEDIA, '$fileType'] }] },
                            { fileName: '$fileName', attachment: '$attachment' },
                            null,
                        ],
                    },
                    urlData: {
                        $cond: [{ $in: [LINKS, '$fileType'] }, '$url', null],
                    },
                },
            },
            {
                $facet: {
                    docs: [
                        { $match: { fileType: { $in: [DOCS] } } },
                        { $skip: (pageNumber - 1) * pageLimit },
                        { $limit: pageLimit },
                        {
                            $group: {
                                _id: DOCS,
                                attachments: { $push: '$attachmentData' },
                                urls: { $push: '$urlData' },
                            },
                        },
                    ],
                    media: [
                        { $match: { fileType: { $in: [MEDIA] } } },
                        { $skip: (pageNumber - 1) * pageLimit },
                        { $limit: pageLimit },
                        {
                            $group: {
                                _id: MEDIA,
                                attachments: { $push: '$attachmentData' },
                                urls: { $push: '$urlData' },
                            },
                        },
                    ],
                    links: [
                        { $match: { fileType: { $in: [LINKS] } } },
                        { $skip: (pageNumber - 1) * pageLimit },
                        { $limit: pageLimit },
                        {
                            $group: {
                                _id: LINKS,
                                attachments: { $push: '$attachmentData' },
                                urls: { $push: '$urlData' },
                            },
                        },
                    ],
                },
            },
            {
                $project: {
                    docs: {
                        $ifNull: [
                            { $arrayElemAt: ['$docs', 0] },
                            { _id: DOCS, attachments: [], urls: [] },
                        ],
                    },
                    media: {
                        $ifNull: [
                            { $arrayElemAt: ['$media', 0] },
                            { _id: MEDIA, attachments: [], urls: [] },
                        ],
                    },
                    links: {
                        $ifNull: [
                            { $arrayElemAt: ['$links', 0] },
                            { _id: LINKS, attachments: [], urls: [] },
                        ],
                    },
                },
            },
        ]);
        const filterNonNull = (attachment) => {
            return attachment.filter((attachmentElement) => attachmentElement !== null);
        };
        let chatResults = [MEDIA, DOCS, LINKS]
            .map((type) => ({
                type: chatMessages[0][type]?._id || type.toUpperCase(),
                attachments:
                    type !== LINKS ? filterNonNull(chatMessages[0][type]?.attachments || []) : [],
                urls: type === LINKS ? filterNonNull(chatMessages[0][type]?.urls || []) : [],
            }))
            .filter(
                (attachmentElement) =>
                    attachmentElement.attachments.length || attachmentElement.urls.length,
            );

        const totalCount = await messageSchema.countDocuments({
            channelId: convertToMongoObjectId(channelId),
            isDeleted: false,
            fileType: type || { $ne: null },
        });
        if (type) {
            chatResults = chatResults.filter((chatElement) => chatElement.type == type);
        }
        if (!type) {
            await Promise.all(
                chatResults.map(async (chatElement) => {
                    if (chatElement.type === MEDIA) {
                        chatElement.attachments = await Promise.all(
                            chatElement.attachments.map(async (attachmentElement) => {
                                attachmentElement.attachment = await getSignedURL(
                                    attachmentElement.attachment,
                                );
                                return attachmentElement;
                            }),
                        );
                    }
                    return chatElement;
                }),
            );
        }
        const totalPages = Math.ceil(totalCount / pageLimit);
        if (totalCount === 0) {
            return sendResponseWithRequest(req, res, 404, true, req.t('NO_DATA_FOUND'), {
                attachments: [],
            });
        }
        return sendResponseWithRequest(req, res, 200, true, 'chat media', {
            totalPages,
            attachments: chatResults,
        });
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};

exports.deleteChat = async (req, res) => {
    try {
        const { _user_id } = req.headers;
        const { channelId } = req.params;
        const messageData = await messageSchema.updateMany(
            { channelId: convertToMongoObjectId(channelId) },
            { $addToSet: { deletedFor: convertToMongoObjectId(_user_id) } },
        );
        if (!messageData.modifiedCount) {
            return sendResponseWithRequest(req, res, 404, false, 'Failed to delete');
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Messages successfully marked as deleted for the user',
        );
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t(error.message));
    }
};

exports.forwardMessage = async (req, res) => {
    try {
        const { _institution_calendar_id, from, toUsers, messageId } = req.body;
        const privateChannelNames = [];
        toUsers.forEach((toUserElement) => {
            const channelName = [
                `${from.name}-${toUserElement.name}`,
                `${toUserElement.name}-${from.name}`,
            ];
            privateChannelNames.push(...channelName);
        });
        const messageData = await messageSchema
            .findOne(
                { _id: convertToMongoObjectId(messageId) },
                {
                    msg: 1,
                    fileName: 1,
                    attachment: 1,
                    fileType: 1,
                    url: 1,
                },
            )
            .lean();
        const chatGroup = await chatGroupSchema
            .find(
                {
                    channelName: { $in: privateChannelNames },
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    channelType: PRIVATE,
                    isDeleted: false,
                },
                { _id: 1, channelName: 1, 'members.userId': 1 },
            )
            .lean();
        const fromUserData = await userSchema
            .findOne({ _id: convertToMongoObjectId(from.userId) }, { name: 1 })
            .lean();
        const chatMessageStruct = {
            msg: messageData.msg,
            createdBy: convertToMongoObjectId(from.userId),
            ...(messageData.fileName && { fileName: messageData.fileName }),
            ...(messageData.attachment && { attachment: messageData.attachment }),
            ...(messageData.fileType &&
                messageData.fileType.length && { fileType: messageData.fileType }),
            ...(messageData.url && { url: messageData.url }),
            createdAt: new Date(),
            // members: [convertToMongoObjectId(from.userId)],
        };
        const newChannelBulkCreate = [];
        const messageBulkCreate = [];
        const messageBulkSocket = [];
        const chatGroupMap = new Map(chatGroup.map((group) => [group.channelName, group]));
        toUsers.forEach((toUserElement) => {
            const toUserName = toUserElement.name;
            const channelName = `${from.name}-${toUserName}`;
            const reverseChannelName = `${toUserName}-${from.name}`;
            const newMessageId = { _id: convertToMongoObjectId() };
            const toUserChatGroup =
                chatGroupMap.get(channelName) || chatGroupMap.get(reverseChannelName);
            if (!toUserChatGroup) {
                if (toUserElement.type !== CHANNEL) {
                    const channelInsertData = {
                        _id: convertToMongoObjectId(),
                        _institution_calendar_id,
                        channelName: `${from.name}-${toUserElement.name}`,
                        channelType: PRIVATE,
                        members: [
                            {
                                userId: from.userId,
                                userType: from.type,
                                academicNo: from.academicNo,
                            },
                            {
                                userId: toUserElement.userId,
                                userType: toUserElement.type,
                                academicNo: toUserElement.academicNo,
                            },
                        ],
                        admin: [from.userId],
                        to: `${from.type}-${toUserElement.type}`,
                    };
                    newChannelBulkCreate.push(channelInsertData);
                    messageBulkCreate.push({
                        ...chatMessageStruct,
                        ...newMessageId,
                        channelId: channelInsertData._id,
                    });
                    messageBulkSocket.push({
                        event: 'msg_created',
                        data: {
                            msg: {
                                ...newMessageId,
                                ...chatMessageStruct,
                                createdBy: fromUserData,
                                channelId: channelInsertData._id,
                            },
                            roomId: channelInsertData._id,
                        },
                    });
                } else {
                    messageBulkCreate.push({
                        ...chatMessageStruct,
                        ...newMessageId,
                        channelId: toUserElement.channelId,
                    });
                    messageBulkSocket.push({
                        event: 'msg_created',
                        data: {
                            msg: {
                                ...chatMessageStruct,
                                ...newMessageId,
                                createdBy: fromUserData,
                                channelId: toUserElement.channelId,
                            },
                            roomId: toUserElement.channelId,
                        },
                    });
                }
            } else {
                messageBulkCreate.push({
                    ...chatMessageStruct,
                    ...newMessageId,
                    channelId: toUserChatGroup._id,
                });
                messageBulkSocket.push({
                    event: 'msg_created',
                    data: {
                        msg: {
                            ...chatMessageStruct,
                            ...newMessageId,
                            createdBy: fromUserData,
                            channelId: toUserChatGroup._id,
                        },
                        roomId: toUserChatGroup._id,
                    },
                });
            }
        });
        if (newChannelBulkCreate.length) await chatGroupSchema.create(newChannelBulkCreate);
        if (messageBulkCreate.length) await messageSchema.create(messageBulkCreate);
        await dcCronAxios({
            url: '/socketEmitService',
            method: 'POST',
            data: { data: messageBulkSocket },
        })
            .then((resp) => resp.data)
            .catch(function (error) {
                console.error(error);
            });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'CHAT_FORWARDED' /* , {
            messageData,
            chatGroup,
            newChannelBulkCreate,
            messageBulkCreate,
            messageBulkSocket,
        } */,
        );
    } catch (error) {
        console.error(error);
        return sendResponseWithRequest(req, res, 500, false, error.message);
    }
};
