const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_INITIATOR_GUIDE_RESOURCES,
    QAPC_FORM_INITIATOR,
    INSTITUTION,
    QAPC_SETTING_TAG,
    QAPC_FORM_SETTING,
    QAPC_INCORPORATE_SECTIONS,
    QAPC_ROLE,
    USER,
} = require('../../utility/constants');

const qapcGuideResourceSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        categoryFormId: { type: ObjectId, ref: QAPC_FORM_SETTING },
        formInitiatorIds: [{ type: ObjectId, ref: QAPC_FORM_INITIATOR }],
        formEvidenceAttachment: [
            {
                url: String,
                name: String,
            },
        ],
        sectionAttachments: [
            {
                sectionName: { type: String },
                description: { type: String },
                evidenceAttachment: [
                    {
                        url: String,
                        name: String,
                    },
                ],
            },
        ],
        tags: [
            {
                _id: { type: ObjectId, ref: QAPC_SETTING_TAG },
                name: { type: String },
                subTag: [{ type: String }],
                isDefault: { type: Boolean, default: false },
                isConfigured: { type: Boolean },
            },
        ],
        displayCapture: [
            {
                sectionName: { type: String },
                attachments: [
                    {
                        url: { type: String },
                        name: { type: String },
                        displayName: { type: String },
                    },
                ],
            },
        ],
        //incorporateFormLike
        incorporateFrom: [
            {
                incorporateId: { type: ObjectId, ref: QAPC_INCORPORATE_SECTIONS },
                isLike: { type: Boolean },
            },
        ],
        incorporateFromReason: { type: String },
        //form template
        formTemplate: { type: Object },
        addComment: [
            {
                level: { type: Number },
                roleId: { type: ObjectId, ref: QAPC_ROLE },
                userId: { type: ObjectId, ref: USER },
                commentDate: { type: Date },
                commentType: { type: String },
                selectedSections: [{ type: String }],
                attachments: [
                    {
                        url: { type: String },
                        name: { type: String },
                    },
                ],
                comment: { type: String },
            },
        ],
        //add pdf
        pdfAttachment: [
            {
                url: String,
                name: String,
            },
        ],
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_INITIATOR_GUIDE_RESOURCES, qapcGuideResourceSchema);
