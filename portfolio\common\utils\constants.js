const enums = require('./enums');

const constants = Object.freeze({
    DYNAMIC_OUTCOME_COMPONENT: 'portfolio-dynamic-outcome-component',
    MEASURABLE_TYPE: 'portfolio-measurable-type',
    ITEM_TYPES: [enums.MCQ, enums.SAQ, enums.MQ, enums.CQ, enums.EQ, enums.CSAQ, enums.MIT],
    FORM: 'portfolio-form',
    STUDENT_RESPONSE: 'portfolio-student-response',
    FORM_TYPES: ['Student Portfolio', 'Mini-Cex', 'Clinical-logbook'],
    RUBRIC: 'portfolio-rubric',
    STUDENT: 'student',
    STAFF: 'staff',
    PORTFOLIO: 'portfolio',
    FORM_TYPE: 'portfolio-form-type',
    PORTFOLIO_VARIABLE: 'portfolio-variable',
    PORTFOLIO_ASSIGN_EVALUATOR: 'portfolio-assign-evaluator',
    PORTFOLIO_STUDENT: 'portfolio-student',
    PORTFOLIO_ROLE: 'portfolio-role',
    PORTFOLIO_ASSIGN_STUDENT: 'portfolio-assign-student',
    WITH_IN_WEEK: 'with-in-week',
    WITH_IN_DAY: 'with-in-day',
    ALL_WAYS_OPEN: 'all-ways-open',
    MALE: 'M',
    FEMALE: 'F',
    MALE_LABEL: 'Male',
    FEMALE_LABEL: 'Female',
    PROGRAM: 'program',
    MAX_VALUE: 'maxValue',
    AWARDED_VALUE: 'awardedValue',
    ABSENT: 'absent',
    PRESENT: 'present',
    REGULAR: 'regular',
    ON_DUTY: 'on_duty',
    LEAVE: 'leave',
    PERMISSION: 'permission',
    COMPLETED_LABEL: 'completed',
});

module.exports = constants;
