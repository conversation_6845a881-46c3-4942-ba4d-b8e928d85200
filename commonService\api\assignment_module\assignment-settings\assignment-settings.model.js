const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../../utility/constants');
const totalAssignmentMarks = {
    isActive: {
        type: Boolean,
    },
    value: {
        type: Number,
    },
};
const copyAssignmentFromPrevious = {
    isCopy: {
        type: Boolean,
    },
    year: {
        type: String,
    },
    level: {
        type: String,
    },
    term: {
        type: String,
    },
    _institution_calendar_id: {
        type: Schema.Types.ObjectId,
    },
    _program_id: {
        type: Schema.Types.ObjectId,
    },
    _course_id: {
        type: Schema.Types.ObjectId,
    },
};
const markAndCopyAssignment = { totalAssignmentMarks, copyAssignmentFromPrevious };
const assignmentSettingsSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
        },
        programSettings: {
            programId: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
            },
            gradingSystem: {
                points: {
                    isActive: {
                        type: <PERSON>olean,
                    },
                    scalePoints: {
                        type: Number,
                    },
                },
                letterGrade: {
                    isActive: {
                        type: Boolean,
                    },
                    gradingMethod: [
                        {
                            percentage: {
                                from: {
                                    type: Number,
                                },
                                to: {
                                    type: Number,
                                },
                            },
                            grade: {
                                type: String,
                            },
                            description: {
                                type: String,
                            },
                        },
                    ],
                },
            },
            gamification: {
                isActive: {
                    type: Boolean,
                },
                levels: [
                    {
                        index: {
                            type: Number,
                        },
                        title: {
                            type: String,
                        },
                        creditsRequired: {
                            type: Number,
                        },
                    },
                ],
                creditPointsFor: {
                    submissionAssignmentCount: {
                        type: Number,
                    },
                    submissionOnTime: {
                        isActive: {
                            type: Boolean,
                        },
                        count: {
                            type: Number,
                        },
                    },
                    targetBenchmark: {
                        isActive: {
                            type: Boolean,
                        },
                        count: {
                            type: Number,
                        },
                    },
                    scoring: {
                        isActive: {
                            type: Boolean,
                        },
                        scores: [
                            {
                                startPercentage: {
                                    type: Number,
                                },
                                endPercentage: {
                                    type: Number,
                                },
                                creditPoints: {
                                    type: Number,
                                },
                            },
                        ],
                    },
                    leaderBoard: {
                        type: Boolean,
                    },
                    badges: {
                        isActive: {
                            type: Boolean,
                        },
                        badge: [
                            {
                                title: {
                                    type: String,
                                },
                                description: {
                                    type: String,
                                },
                                time: {
                                    type: Number,
                                },
                                badgeIcon: {
                                    type: String,
                                },
                            },
                        ],
                    },
                },
            },
        },
        courseSettings: {
            programId: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
            },
            courseId: {
                type: Schema.Types.ObjectId,
            },
            general: {
                groupLeaderCount: { type: Number },
                assignmentGuideLines: {
                    isActive: {
                        type: Boolean,
                    },
                    info: {
                        title: {
                            type: String,
                        },
                        description: {
                            type: String,
                        },
                        urls: [
                            {
                                url: {
                                    type: String,
                                },
                                name: {
                                    type: String,
                                },
                                sizeInKb: {
                                    type: Number,
                                },
                            },
                        ],
                        isDraft: {
                            type: Boolean,
                        },
                    },
                },
                allowMultiSubjectAssignment: {
                    type: Boolean,
                },
                allowCustomAssignmentFormats: {
                    isActive: {
                        type: Boolean,
                    },
                    isAdminOnly: {
                        type: Boolean,
                    },
                },
                summative: {
                    isActive: {
                        type: Boolean,
                    },
                    assignmentLimit: {
                        type: Number,
                    },
                    ...markAndCopyAssignment,
                    export: {
                        allowSubmittedAssignmentsExport: {
                            type: Boolean,
                        },
                        allowDiscussionExport: {
                            type: Boolean,
                        },
                        allowCompletedReportsAndAnalyticsExport: {
                            type: Boolean,
                        },
                        allowStudentCompletedAssignmentExport: {
                            type: Boolean,
                        },
                    },
                },
                formative: {
                    isActive: {
                        type: Boolean,
                    },
                    assignmentLimit: {
                        type: Number,
                    },
                    ...markAndCopyAssignment,
                    isAllowToExceedTheLimit: { type: Boolean },
                    export: {
                        allowSubmittedAssignmentsExport: {
                            type: Boolean,
                        },
                        allowDiscussionExport: {
                            type: Boolean,
                        },
                        allowCompletedReportsAndAnalyticsExport: {
                            type: Boolean,
                        },
                        allowStudentCompletedAssignmentExport: {
                            type: Boolean,
                        },
                    },
                },
            },
            evaluation: {
                rubericComponents: {
                    rowName: {
                        type: String,
                        default: 'Criteria',
                    },
                    columnName: {
                        type: String,
                        default: 'Dimension',
                    },
                },
                summative: {
                    scoring: {
                        assignmentPassMark: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        criticalCutOff: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        performanceRatingTitle: {
                            type: String,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    outcome: {
                        targetBenchmark: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        targetBenchmarkForEachOutcome: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        outcomeAttainmentLevel: {
                            type: Boolean,
                        },
                        outcomeAttainmentTitle: {
                            type: String,
                        },
                        outcomeCutOffValue: {
                            type: Number,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                        allowManualSelectionOutcomeAchievement: {
                            type: Boolean,
                        },
                    },
                    evaluators: {
                        allowMultipleEvaluators: {
                            type: Boolean,
                        },
                        allowPeerEvaluation: {
                            type: Boolean,
                        },
                        allowSelfEvaluation: {
                            type: Boolean,
                        },
                    },
                    feedBackAndDiscussion: {
                        allowReflection: {
                            type: Boolean,
                        },
                        allowManualReconcilation: {
                            type: Boolean,
                        },
                        allowRemediation: {
                            type: Boolean,
                        },
                        allowDiscussionForAllAssignment: {
                            type: Boolean,
                        },
                    },
                },
                formative: {
                    scoring: {
                        assignmentPassMark: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        criticalCutOff: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        performanceRatingTitle: {
                            type: String,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    outcome: {
                        targetBenchmark: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        targetBenchmarkForEachOutcome: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        outcomeAttainmentLevel: {
                            type: Boolean,
                        },
                        outcomeAttainmentTitle: {
                            type: String,
                        },
                        outcomeCutOffValue: {
                            type: Number,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                        allowManualSelectionOutcomeAchievement: {
                            type: Boolean,
                        },
                    },
                    evaluators: {
                        allowMultipleEvaluators: {
                            type: Boolean,
                        },
                        allowPeerEvaluation: {
                            type: Boolean,
                        },
                        allowSelfEvaluation: {
                            type: Boolean,
                        },
                    },
                    feedBackAndDiscussion: {
                        allowReflection: {
                            type: Boolean,
                        },
                        allowManualReconcilation: {
                            type: Boolean,
                        },
                        allowRemediation: {
                            type: Boolean,
                        },
                        allowDiscussionForAllAssignment: {
                            type: Boolean,
                        },
                    },
                },
            },
            plagiarismAndCollusion: {
                summative: {
                    plagiarismCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionOfSubmissionWhenPlagiarismIs: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        plagiarismLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    collusionCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionOfSubmissionWhenCollusionIs: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        collusionLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    studentPlagiarismCheckBeforeSubmission: {
                        type: Boolean,
                    },
                },
                formative: {
                    plagiarismCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionOfSubmissionWhenPlagiarismIs: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        plagiarismLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    collusionCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionOfSubmissionWhenPlagiarismIs: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        collusionLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    studentPlagiarismCheckBeforeSubmission: {
                        type: Boolean,
                    },
                },
            },
        },
        subjectSettings: {
            programId: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
            },
            courseId: {
                type: Schema.Types.ObjectId,
            },
            subjectId: {
                type: Schema.Types.ObjectId,
            },
            departmentId: {
                type: Schema.Types.ObjectId,
            },
            general: {
                assignmentGuideLines: {
                    isActive: {
                        type: Boolean,
                    },
                    info: {
                        title: { type: String },
                        description: { type: String },
                        urls: [
                            {
                                url: {
                                    type: String,
                                },
                                name: {
                                    type: String,
                                },
                                sizeInKb: {
                                    type: Number,
                                },
                            },
                        ],
                        isDraft: {
                            type: Boolean,
                        },
                    },
                },
                summative: {
                    isActive: {
                        type: Boolean,
                    },
                    assignmentLimit: {
                        type: Number,
                    },
                    totalAssignmentMarks,
                    export: {
                        allowSubmittedAssignmentsExport: {
                            type: Boolean,
                        },
                        allowDiscussionExport: {
                            type: Boolean,
                        },
                        allowCompletedReportsAndAnalyticsExport: {
                            type: Boolean,
                        },
                        allowStudentCompletedAssignmentExport: {
                            type: Boolean,
                        },
                    },
                },
                formative: {
                    isActive: {
                        type: Boolean,
                    },
                    assignmentLimit: {
                        type: Number,
                    },
                    totalAssignmentMarks,
                    isAllowToExceedTheLimit: {
                        type: Boolean,
                    },
                    export: {
                        allowSubmittedAssignmentsExport: {
                            type: Boolean,
                        },
                        allowDiscussionExport: {
                            type: Boolean,
                        },
                        allowCompletedReportsAndAnalyticsExport: {
                            type: Boolean,
                        },
                        allowStudentCompletedAssignmentExport: {
                            type: Boolean,
                        },
                    },
                },
            },
            evaluation: {
                rubericComponents: {
                    rowName: {
                        type: String,
                    },
                    columnName: {
                        type: String,
                    },
                },
                summative: {
                    scoring: {
                        assignmentPassMark: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        criticalCutOff: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        performanceRatingTitle: { type: String },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    outcome: {
                        targetBenchmark: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        targetBenchmarkForEachOutcome: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        outcomeAttainmentLevel: {
                            type: Boolean,
                        },
                        outcomeAttainmentTitle: {
                            type: String,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                        allowManualSelectionOutcomeAchievement: { type: Boolean },
                    },
                    evaluators: {
                        allowMultipleEvaluators: {
                            type: Boolean,
                        },
                        allowPeerEvaluation: {
                            type: Boolean,
                        },
                        allowSelfEvaluation: {
                            type: Boolean,
                        },
                    },
                    feedBackAndDiscussion: {
                        allowReflection: {
                            type: Boolean,
                        },
                        allowManualReconciliation: { type: Boolean },
                        allowRemediation: {
                            type: Boolean,
                        },
                        allowDiscussionForAllAssignment: {
                            type: Boolean,
                        },
                    },
                },
                formative: {
                    scoring: {
                        assignmentPassMark: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        criticalCutOff: {
                            condition: { type: String },
                            percentage: { type: Number },
                        },
                        performanceRatingTitle: {
                            type: String,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    outcome: {
                        targetBenchmark: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        targetBenchmarkForEachOutcome: {
                            condition: {
                                type: String,
                            },
                            percentage: {
                                type: Number,
                            },
                        },
                        outcomeAttainmentLevel: { type: Boolean },
                        outcomeAttainmentTitle: {
                            type: String,
                        },
                        performanceRatings: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                        allowManualSelectionOutcomeAchievement: { type: Boolean },
                    },
                    evaluators: {
                        allowMultipleEvaluators: {
                            type: Boolean,
                        },
                        allowPeerEvaluation: {
                            type: Boolean,
                        },
                        allowSelfEvaluation: {
                            type: Boolean,
                        },
                    },
                    feedBackAndDiscussion: {
                        allowReflection: {
                            type: Boolean,
                        },
                        allowManualReconciliation: {
                            type: Boolean,
                        },
                        allowRemediation: {
                            type: Boolean,
                        },
                        allowDiscussionForAllAssignment: {
                            type: Boolean,
                        },
                    },
                },
            },
            plagiarismAndCollusion: {
                summative: {
                    plagiarismCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        plagiarismLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    collusionCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        collusionLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                        studentPlagiarismCheckBeforeSubmission: {
                            type: Boolean,
                        },
                    },
                },
                formative: {
                    plagiarismCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        plagiarismLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    collusionCheck: {
                        isActive: {
                            type: Boolean,
                        },
                        autoRejectionCondition: {
                            type: String,
                        },
                        autoRejectionPercent: {
                            type: Number,
                        },
                        allowEvaluatorsToDecide: {
                            type: Boolean,
                        },
                        collusionLevels: [
                            {
                                rangeStart: {
                                    type: Number,
                                },
                                rangeEnd: {
                                    type: Number,
                                },
                                description: {
                                    type: String,
                                },
                            },
                        ],
                    },
                    studentPlagiarismCheckBeforeSubmission: {
                        type: Boolean,
                    },
                },
            },
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.ASSIGNMENT_SETTINGS, assignmentSettingsSchema);
