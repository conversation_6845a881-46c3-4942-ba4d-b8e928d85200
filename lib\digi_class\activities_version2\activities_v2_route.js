// const multer = require('multer');
const express = require('express');
const route = express.Router();
const {
    createActivities,
    selectSessions,
    updateActivities,
    deleteActivities,
    getQuestionBank,
    getQuestionsBySession,
    getSessionSchedule,
    getActivity,
    quizStartByStaff,
    quizStopByStaff,
    questionAnsweredByStudent,
    acceptQuestionResponse,
    getResults,
    getActivityById,
    getQuestions,
    getStudents,
    updateStudentMarks,
    getActivities,
    publishStudentMarks,
    exportActivity,
    getQuestionsByCourseList,
    getQuestionBankCreatedByItem,
    createOrUpdateQuestion,
} = require('./activities_v2_controller');
const catchAsync = require('../../utility/catch-async');
const {
    getActivitySchema,
    getResultSchema,
    quizStartByStaffSchema,
    acceptQuestionResponseSchema,
    quizStopByStaffSchema,
    questionAnsweredByStudentSchema,
    createActivitiesSchema,
    updateActivitiesSchema,
    deleteActivitiesSchema,
    selectSessionsSchema,
    getStudentParamSchema,
    saveStudentMarksSchema,
    getActivitiesSchema,
    publishStudentMarksSchema,
    questionBankStaffList,
    questionBankAllQuestion,
    createOrUpdateQuestionValidator,
} = require('./activities_v2_validate_schema');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

//activity start area
route.get(
    '/',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getActivitiesSchema,
    getActivities,
);
route.get(
    '/:id',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getActivitySchema,
    getActivity,
);
route.get(
    '/getById/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getActivitySchema,
    getActivityById,
);
route.get(
    '/:id/view-result',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getResultSchema,
    getResults,
);
route.put(
    '/:id/started',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    quizStartByStaffSchema,
    quizStartByStaff,
);
route.put(
    '/:id/stop',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    quizStopByStaffSchema,
    quizStopByStaff,
);
route.put(
    '/:id/answer',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    questionAnsweredByStudentSchema,
    questionAnsweredByStudent,
);
route.put(
    '/:id/accept-question-response',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    acceptQuestionResponseSchema,
    acceptQuestionResponse,
);

route.post(
    '/',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    createActivitiesSchema,
    createActivities,
);
route.put(
    '/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateActivitiesSchema,
    updateActivities,
);
route.delete(
    '/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    deleteActivitiesSchema,
    deleteActivities,
);
route.get(
    '/select-sessions/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    selectSessionsSchema,
    selectSessions,
);
route.get(
    '/get-session-schedule/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getSessionSchedule,
);
route.post('/question-bank', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], getQuestionBank);
route.post(
    '/question-bank/questions',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getQuestionsBySession,
);
route.post(
    '/get-questions',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getQuestions,
);
route.get(
    '/:id/get-students',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getStudentParamSchema,
    getStudents,
);
route.post(
    '/save-student-marks',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    saveStudentMarksSchema,
    updateStudentMarks,
);
route.post(
    '/publish-student-marks',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    publishStudentMarksSchema,
    publishStudentMarks,
);
route.get(
    '/exportActivity/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    exportActivity,
);
route.post(
    '/question-bank/AllQuestions',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    questionBankAllQuestion,
    catchAsync(getQuestionsByCourseList),
);
route.post(
    '/question-bank/get-staff-list',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    questionBankStaffList,
    catchAsync(getQuestionBankCreatedByItem),
);
route.post(
    '/createOrUpdateQuestion',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    createOrUpdateQuestionValidator,
    catchAsync(createOrUpdateQuestion),
);

module.exports = route;
