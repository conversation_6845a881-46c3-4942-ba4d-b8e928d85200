const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const FormModel = require('../form/form.model');
const StudentResponseModel = require('../student-response/student-response.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');

const { PUBLISHED, COMPLETED, ON_GOING, NOT_STARTED } = require('../../common/utils/enums');
const { getDocumentsCount } = require('../../base/base.helper');
const { isIDEquals, convertToMongoObjectId } = require('../../common/utils/common.util');
const { REGULAR } = require('../../common/utils/constants');

const getStudentPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    rotation,
    rotationCount,
    userId,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            'student._id': userId,
            status: PUBLISHED,
            term,
            year,
            level,
            ...(rotation && { rotation_no: rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
        },
        {
            components: 1,
            totalMarks: 1,
            status: 1,
            publishedDate: 1,
        },
    ).lean();
    if (!portfolio) {
        return {
            count: {
                upcomingCount: 0,
                totalCount: 0,
                completedCount: 0,
                studentResponseCount: 0,
                totalPoints: 0,
            },
            components: [],
        };
    }

    const courseSchedules = await CourseScheduleModel.find(
        {
            _institution_calendar_id: institutionCalendarId,
            _program_id: programId,
            _course_id: courseId,
            'students._id': userId,
            year_no: year,
            term,
            level_no: level,
            ...(rotation && { rotation_no: rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
            type: REGULAR,
            isDeleted: false,
            isActive: true,
        },
        {
            scheduleDate: '$schedule_date',
            session: 1,
            start: 1,
            end: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
        },
    ).lean();

    const formIds = portfolio.components
        .flatMap((component) => component.children.map((child) => child.formId))
        .filter((formId) => formId);

    const forms = await FormModel.find(
        {
            _id: { $in: formIds },
            $or: [
                { 'pages.elements.users.userId': userId },
                { 'pages.elements.roles.users.userId': userId },
            ],
        },
        {
            status: 1,
        },
    ).lean();

    const upcomingCount = forms.filter((form) => form.status === ON_GOING).length;

    const studentResponseCount = await getDocumentsCount({
        Model: StudentResponseModel,
        query: { status: COMPLETED, formId: { $in: formIds }, 'student._id': userId },
    });
    const studentResponses = await StudentResponseModel.find(
        {
            portfolioId: convertToMongoObjectId(portfolio._id),
            'student._id': userId,
        },
        {
            status: 1,
            formId: 1,
            _id: 0,
            scheduleId: 1,
            portfolioId: 1,
            childrenId: 1,
            componentId: 1,
            reviews: 1,
            evaluationStatus: 1,
            approvalStatus: 1,
        },
    ).lean();

    let totalPoints = 0;

    portfolio?.components?.forEach((component) => {
        totalPoints += component.marks || 0;
        const total = component.children?.length || 0;
        let completed = 0;

        if (component?.deliveryTypes?.length) {
            const schedules = courseSchedules.filter((schedule) => {
                return component.deliveryTypes.some(
                    (deliveryType) =>
                        deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                );
            });
            schedules.forEach((schedule) => {
                const children = component.children?.map((child) => {
                    const response = studentResponses.find(
                        (r) =>
                            isIDEquals(r.formId, child.formId) &&
                            isIDEquals(r.scheduleId, schedule._id) &&
                            isIDEquals(r.portfolioId, portfolio._id) &&
                            isIDEquals(r.componentId, component._id) &&
                            isIDEquals(r.childrenId, child._id),
                    );

                    if (response?.status === COMPLETED) completed += 1;

                    return {
                        ...child,
                        studentResponseStatus: response?.status || NOT_STARTED,
                        evaluationStatus: response?.evaluationStatus || NOT_STARTED,
                        reviews: response?.reviews || [],
                        approvalStatus: response?.approvalStatus || NOT_STARTED,
                    };
                });
                schedule.children = children;
            });
            component.schedules = schedules;
        } else {
            component.children?.forEach((child) => {
                const response = studentResponses.find(
                    (r) =>
                        isIDEquals(r.formId, child.formId) &&
                        isIDEquals(r.portfolioId, portfolio._id) &&
                        isIDEquals(r.componentId, component._id) &&
                        isIDEquals(r.childrenId, child._id),
                );

                child.studentResponseStatus = response?.status || NOT_STARTED;
                child.evaluationStatus = response?.evaluationStatus || NOT_STARTED;
                child.reviews = response?.reviews || [];
                child.approvalStatus = response?.approvalStatus || NOT_STARTED;
                if (response?.status === COMPLETED) completed += 1;
            });
        }

        component.completionPercentage = total ? Math.round((completed / total) * 100) : 0;
    });

    return {
        count: {
            upcomingCount,
            totalCount: forms?.length || 0,
            overallProgress: Math.round((studentResponseCount / forms.length) * 100) || 0,
            studentResponseCount,
            totalPoints,
        },
        ...portfolio,
    };
};

const updatePortfolio = async ({ portfolioId, componentId, component, userId }) => {
    const portfolio = await StudentPortfolioModel.findOneAndUpdate(
        {
            _id: portfolioId,
            'student._id': userId,
        },
        { $set: { 'components.$[i].children': component.children } },
        {
            arrayFilters: [{ 'i._id': convertToMongoObjectId(componentId) }],
            projection: { components: 1 },
            new: true,
        },
    );

    return portfolio;
};

const getGlobalRubric = async ({ portfolioId, componentId, childId, studentId }) => {
    const studentPortfolio = await StudentPortfolioModel.findOne({
        portfolioId,
        'student._id': studentId,
    }).lean();

    const component = studentPortfolio.components.find((component) =>
        isIDEquals(component?._id, componentId),
    );

    const child = component.children.find((child) => isIDEquals(child?._id, childId));
    const rubric = child.rubrics;

    return rubric;
};

module.exports = {
    getStudentPortfolio,
    updatePortfolio,
    getGlobalRubric,
};
