const express = require('express');
const route = express.Router();

// controller
const {
    getCourseCourseCoordinators,
    assignCourseCoordinator,
    publishCourseCoordinator,
    getUserProgram,
} = require('./course_coordinator_controller');

// validate schema
const {
    getCourseCourseCoordinatorsValidate: { body: getCourseCourseCoordinatorsBodySchema },
} = require('./course_coordinator_validate_schema');

// validate
const { validate } = require('../../middleware/validation');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get(
    '/coordinators/:institution_calendar/:program',
    [userPolicyAuthentication(['schedule_management:assign_course_coordinator:course_list:view'])],
    getCourseCourseCoordinators,
);
route.get(
    '/program_list/:userId/:roleId',
    [
        userPolicyAuthentication([
            'infrastructure_management:remote:view',
            'schedule_management:course_scheduling:list_view',
            'schedule_management:assign_course_coordinator:list_view',
            'schedule_management:extra_curricular_and_break:list_view',
            'schedule_management:course_scheduling:schedule:course_view',
        ]),
    ],
    getUserProgram,
);
route.post(
    '/',
    [userPolicyAuthentication(['schedule_management:assign_course_coordinator:course_list:edit'])],
    validate([{ schema: getCourseCourseCoordinatorsBodySchema, property: 'body' }]),
    assignCourseCoordinator,
);
route.post(
    '/publish',
    [
        userPolicyAuthentication([
            'schedule_management:assign_course_coordinator:course_list:publish',
        ]),
    ],
    // validate([{ schema: getCourseCourseCoordinatorsBodySchema, property: 'body' }]),
    publishCourseCoordinator,
);

module.exports = route;
