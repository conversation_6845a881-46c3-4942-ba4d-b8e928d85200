const express = require('express');
const route = express.Router();
const infrastructure_management = require('./infrastructure_management_controller');
const validator = require('./infrastructure_management_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get('/search', [userPolicyAuthentication([])], infrastructure_management.search);
route.get(
    '/:_id',
    [userPolicyAuthentication([])],
    validator.infrastructure_management_id,
    infrastructure_management.list_id,
);
route.get(
    '/',
    [userPolicyAuthentication(['infrastructure_management:onsite:view'])],
    infrastructure_management.list,
);
route.post(
    '/',
    [userPolicyAuthentication(['infrastructure_management:onsite:add'])],
    validator.infrastructure_management,
    infrastructure_management.insert,
);
route.put(
    '/:_id',
    [userPolicyAuthentication(['infrastructure_management:onsite:edit'])],
    validator.infrastructure_management_id,
    validator.infrastructure_management,
    infrastructure_management.update,
);
route.delete(
    '/:_id',
    [userPolicyAuthentication(['infrastructure_management:onsite:delete'])],
    validator.infrastructure_management_id,
    infrastructure_management.delete,
);
module.exports = route;
