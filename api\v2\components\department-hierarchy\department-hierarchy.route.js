const express = require('express');
const router = express.Router();
const {
    addHierarchy,
    getHierarchy,
    deleteHierarchy,
} = require('./department-hierarchy.controller');
const catchAsync = require('../../utility/catch-async');
const validator = require('./department-hierarchy.validator');
const { validate } = require('../../utility/input-validation');

router.post('/', validate(validator.addHierarchyValidator), catchAsync(addHierarchy));
router.get('/', validate(validator.getHierarchyValidator), catchAsync(getHierarchy));
router.delete('/', validate(validator.deleteHierarchyValidator), catchAsync(deleteHierarchy));

module.exports = router;
