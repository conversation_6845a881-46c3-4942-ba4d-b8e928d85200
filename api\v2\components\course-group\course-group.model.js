const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../utility/constants');

const courseGroupSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _curriculum_id: {
            type: Schema.Types.ObjectId,
            ref: constant.CURRICULUM,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
        },
        _year_id: {
            type: Schema.Types.ObjectId,
        },
        _level_id: {
            type: Schema.Types.ObjectId,
        },
        _course_ids: {
            type: [Schema.Types.ObjectId],
            ref: constant.COURSE,
        },
        groupName: {
            type: String,
        },
        isPhaseFlowWithOutLevel: {
            type: Boolean,
            default: false,
        },
    },

    { timestamps: true },
);

module.exports = courseGroupSchema;
