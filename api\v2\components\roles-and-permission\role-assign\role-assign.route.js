const route = require('express').Router();
const { validate } = require('../../../utility/input-validation');
const { getByListId, createRoleAssign, getDepartmentList } = require('./role-assign.controller');
const catchAsync = require('../../../utility/catch-async');
const { roleIdValidation, roleValidation } = require('./role-assign.validator');

route.get('/:id', validate(roleIdValidation), catchAsync(getByListId));

route.post('/', validate(roleValidation), catchAsync(createRoleAssign));

route.post('/department-list', catchAsync(getDepartmentList));

module.exports = route;
