const courseSchedules = require('../../models/course_schedule');
const { convertToMongoObjectId, clone, convertToUtcFormat } = require('../../utility/common');
const {
    TIME_GROUP_BOOKING_TYPE,
    PRESENT,
    DC_STAFF,
    ABSENT,
    LEAVE_TYPE: { PERMISSION },
    LEAVE,
} = require('../../utility/constants');
const isDeleteActive = {
    isDeleted: false,
    isActive: true,
};
const { getRemoteInfra } = require('../../utility/data.service');
const { logger } = require('../../utility/util_keys');

const getCoursesSessionList = async (userId, type, institutionCalendarId) => {
    try {
        let result = await getCourseIdsWithSessions(
            staffId,
            _course_id,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            rotation,
            rotation_count,
        );
        const { courseSchedule, courses } = result;

        const courseIds = courses.map((courseId) => {
            return courseId._course_id;
        });

        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIds },
            },
            { _id: 1, course_type: 1 },
        );

        let allScheduleIds = courseSchedule.map((schedule) => {
            return schedule.session
                ? schedule.session._session_id.toString()
                : schedule._id.toString();
        });
        allScheduleIds = [...new Set(allScheduleIds)];
        allScheduleIds = allScheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));
        const activities = (
            await getJSON(
                Activity,
                {
                    $or: [
                        { _scheduleId: { $in: allScheduleIds } },
                        { _sessionId: { $in: allScheduleIds } },
                    ],
                    isDeleted: false,
                },
                {},
            )
        ).data;
        const documents = (
            await getJSON(
                Document,
                {
                    'sessionOrScheduleIds._id': { $in: allScheduleIds },
                    isDeleted: false,
                    isActive: true,
                },
                {},
            )
        ).data;

        const feedBackData = await getRatingByCourses(
            _course_id,
            staffId,
            year_no,
            level_no,
            rotation,
            rotation_count,
        );
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id: calendarId,
                _program_id: programId,
                program_name,
                course_name,
                course_code,
                level_no: levelNo,
                year_no: yearNo,
                isActive,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;
            result = getSessions(
                courseSchedule,
                courseId,
                calendarId,
                programId,
                yearNo,
                levelNo,
                staffId,
                rotation,
                rotation_count,
            );
            const {
                courseSessionDetails,
                absentCount,
                leaveCount,
                warningCount,
                presentCount,
                totalSessions,
                completedSessions,
                courseSessions,
                attendedSessions,
            } = result;
            let feedback;
            const feedBacks = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._course_id.toString() === courseId.toString() &&
                    feedBackDetail.level_no === level_no &&
                    feedBackDetail.year_no === year_no &&
                    feedBackDetail.rotation === rotation &&
                    rotation_count &&
                    feedBackDetail.rotation_count === rotation_count,
            );
            if (feedBacks) feedback = feedBacks;
            let course_type;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) course_type = courseTypes.course_type;

            return {
                _id: courseId,
                _program_id: programId,
                courseSessionDetails,
                warningCount,
                presentCount,
                absentCount,
                leaveCount,
                program_name,
                course_name,
                course_code,
                totalSessions,
                completedSessions,
                attendedSessions,
                year: yearNo,
                level: levelNo,
                isActive,
                feedback,
                sessions: courseSessions,
                _institution_calendar_id: calendarId,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
            };
        });
        for (const course of coursesList) {
            for (const session of course.sessions) {
                session.documentCount = 0;
                session.activityCount = 0;
                for (const schedule of session.schedules) {
                    schedule.documentCount = 0;
                    schedule.activityCount = 0;
                    if (schedule.session && schedule.session._session_id) {
                        const docs = documents.filter((doc) =>
                            doc.sessionOrScheduleIds
                                .map((id) => id._id.toString())
                                .includes(schedule.session._session_id.toString()),
                        );
                        schedule.documentCount += docs.length;
                        const filteredActivities = activities.filter(
                            (activity) =>
                                activity._sessionId &&
                                activity._sessionId.toString() ===
                                    schedule.session._session_id.toString(),
                        );
                        schedule.activityCount += filteredActivities.length;
                    } else {
                        const docs = documents.filter((doc) =>
                            doc.sessionOrScheduleIds
                                .map((id) => id._id.toString())
                                .includes(schedule._id.toString()),
                        );
                        schedule.documentCount += docs.length;
                        const filteredActivities = activities.filter(
                            (activity) =>
                                activity._scheduleId &&
                                activity._scheduleId.toString() === schedule._id.toString(),
                        );
                        schedule.activityCount += filteredActivities.length;
                    }
                    session.documentCount += schedule.documentCount;
                    session.activityCount += schedule.activityCount;
                }
            }
            const feedBacks = rotation_count
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          rotation_count &&
                          feedBackDetail.rotationCount === rotation_count,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) course.feedback = feedBacks;
        }
        return coursesList;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getScheduleBySessionOrScheduleList = async (
    sessionOrScheduleId,
    institutionCalendarId,
    userId,
    type,
) => {
    try {
        const scheduleQuery = {
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            $or: [
                {
                    _id: convertToMongoObjectId(sessionOrScheduleId),
                },
                {
                    'session._session_id': convertToMongoObjectId(sessionOrScheduleId),
                },
            ],
            ...isDeleteActive,
        };
        const scheduleDatas = clone(
            await courseSchedules
                .find(scheduleQuery, {
                    _id: 1,
                    _program_id: 1,
                    _course_id: 1,
                    course_code: 1,
                    topic: 1,
                    mode: 1,
                    title: 1,
                    rotation: 1,
                    rotation_count: 1,
                    'subjects.subject_name': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    sessionDetails: 1,
                    staffs: 1,
                    infra_name: 1,
                    _infra_id: 1,
                    merge_status: 1,
                    merge_with: 1,
                    session: 1,
                    students: 1,
                    status: 1,
                })
                .populate({
                    path: 'merge_with.schedule_id',
                    select: { session: 1 },
                }),
        );
        // IndraData for remote
        const remoteInfra = await getRemoteInfra();
        for (scheduleElement of scheduleDatas) {
            if (scheduleElement.mode === TIME_GROUP_BOOKING_TYPE.REMOTE) {
                scheduleElement.infraDatas = remoteInfra.find(
                    (infraElement) =>
                        scheduleElement._infra_id &&
                        infraElement._id.toString() === scheduleElement._infra_id.toString(),
                );
            }

            if (type === DC_STAFF) {
                const studentElement = scheduleElement.students.filter(
                    (student) =>
                        student.status === PRESENT &&
                        student.feedBack &&
                        student.feedBack.rating &&
                        student.feedBack.rating !== 0,
                );
                scheduleElement.feedBack = {
                    totalFeedback: studentElement.length,
                    avgRating: studentElement
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0),
                };
                scheduleElement.studentCount = {
                    totalCount: scheduleElement.students.length,
                    absent: scheduleElement.students.filter(
                        (student) => student && student.status && student.status === ABSENT,
                    ).length,
                    present: scheduleElement.students.filter(
                        (student) => student.status === PRESENT,
                    ).length,
                    leave: scheduleElement.students.filter((student) => student.status === LEAVE)
                        .length,
                    on_duty: scheduleElement.students.filter(
                        (student) => student.status === 'on_duty',
                    ).length,
                    permission: scheduleElement.students.filter(
                        (student) => student.status === PERMISSION,
                    ).length,
                };
            } else
                scheduleElement.studentDetails = scheduleElement.students.find(
                    (studentElement) => studentElement._id.toString() === userId.toString(),
                );
            delete scheduleElement.students;
        }
        return scheduleDatas;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

module.exports = {
    getCoursesSessionList,
    getScheduleBySessionOrScheduleList,
};
