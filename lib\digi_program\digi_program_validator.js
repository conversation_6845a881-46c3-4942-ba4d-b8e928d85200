// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');
// const constant = require('../../../utility/constants');

exports.program = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    data: Joi.array().items(
                        Joi.object()
                            .keys({
                                _institution_id: Joi.string().alphanum().length(24),
                                name: Joi.string().min(2).max(50),
                                level: Joi.string().alphanum().min(2).max(50),
                                level_no: Joi.number().min(1).max(50),
                                degree: Joi.string().min(2).max(50),
                                no: Joi.string().alphanum().min(2).max(50),
                                theory_credit: Joi.number().min(0).max(1000),
                                practicals_credit: Joi.number().min(0).max(1000),
                                clinic_credit: Joi.number().min(0).max(1000),
                                total_credit: Joi.number().min(1).max(10000),
                                interim: Joi.boolean(),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
            // )
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.program_import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    programs: Joi.array().items(
                        Joi.object({
                            Program_Name: Joi.string().min(2).max(250).required(),
                            Program_Code: Joi.string().min(2).max(50).required(),
                            Program_Type: Joi.string()
                                .valid(
                                    constant.PROGRAM_TYPE.PROGRAM,
                                    constant.PROGRAM_TYPE.PREREQUISITE,
                                )
                                .required(),
                            Type: Joi.string().min(2).max(50).required(),
                            Program_Level: Joi.string().min(2).max(50).required(),
                            Degree_Name: Joi.string().min(2).max(250).required(),
                            No_of_Terms: Joi.number().required(),
                            Terms: Joi.array().items(
                                Joi.object({
                                    no: Joi.number().required(),
                                    name: Joi.string()
                                        // .valid(
                                        //     constant.BATCH.REGULAR,
                                        //     constant.BATCH.INTERIM,
                                        //     constant.BATCH.BOTH,
                                        // )
                                        .required(),
                                }),
                            ),
                        }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.program_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.abbreviation = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    abbreviation: Joi.object()
                        .keys({
                            theory: Joi.string().alphanum().min(1).max(50),
                            practical: Joi.string().alphanum().min(1).max(50),
                            clinic: Joi.string().alphanum().min(1).max(50),
                            credit_hours: Joi.string().min(1).max(50),
                            contact_hours: Joi.string().min(1).max(50),
                        })
                        .unknown(true),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
