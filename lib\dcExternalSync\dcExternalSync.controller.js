const userSchema = require('../models/user');
const institutionCalendarSchema = require('../models/institution_calendar');
const studentGroupsSchema = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');
const sessionDeliverySchema = require('../models/digi_session_delivery_types');
const courseScheduleSchema = require('../models/course_schedule');
const { userWarningDenialStatus } = require('../digi_class/course_session/course_session_service');
const {
    getUtcFormateTime,
    convertToLowerCase,
    formatCode,
    termCodeFormate,
} = require('../utility/common_functions');
const programCalenderSchema = require('../models/program_calendar');
const { PUBLISHED } = require('../utility/constants');
const { encryptData, decryptData } = require('../utility/encrypt_decrypt.util');
const programSchema = require('../models/digi_programs');
const {
    PRESENT,
    ABSENT,
    COMPLETED,
    SCHEDULE_TYPES: { REGULAR },
} = require('../utility/constants');
const { logger } = require('../utility/util_keys');
exports.getInstitutionCalendars = async ({ query = {} }) => {
    try {
        const { previousCalendar } = query;
        const institutionCalendersData = await institutionCalendarSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    status: 'published',
                    ...(previousCalendar !== 'true' && { end_date: { $gte: new Date() } }),
                },
                { _id: 1, calendar_name: 1, start_date: 1, end_date: 1 },
            )
            .sort({ _id: -1 })
            .lean();
        if (!institutionCalendersData.length) return { statusCode: 400, message: 'No_Data' };
        return { statusCode: 200, message: 'List_Data', data: institutionCalendersData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.studentGroupingList = async ({ body = {} }) => {
    try {
        const { institutionCalendarId, programId, courseId, year, level, term, rotationNo } = body;

        const studentGroupData = await studentGroupsSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'groups.term': term,
                    'master.year': year,
                    'groups.level': level,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                },
                {
                    _institution_id: 1,
                    'groups.rotation': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.group_name': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups': 1,
                    'groups.students._student_id': 1,
                },
            )
            .lean();
        if (!studentGroupData) return { status: 400, message: 'Groups not found' };
        const studentData =
            studentGroupData.groups.find(
                (groupElement) =>
                    groupElement.level.toLowerCase() === level.toLowerCase() &&
                    groupElement.term.toLowerCase() === term.toLowerCase(),
            ) || {};

        const courseData =
            studentData.courses?.find(
                (courseElement) => String(courseId) === String(courseElement._course_id),
            ) || {};

        const studentList = await userSchema
            .find(
                {
                    isDeleted: false,
                    _id: {
                        $in: studentData.students.map(
                            (studentElement) => studentElement._student_id,
                        ),
                    },
                },
                { user_id: 1, name: 1, gender: 1, isActive: 1 },
            )
            .lean();

        let students = new Map();
        const sessionType = new Set();
        if (courseData.setting) {
            if (rotationNo)
                courseData.setting = courseData.setting.filter(
                    (settingElement) => String(settingElement._group_no) === String(rotationNo),
                );
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    sessionType.add(sessionElement.session_type);
                    sessionElement.groups.forEach((groupElement) => {
                        groupElement._student_ids.forEach((groupStudentElement) => {
                            const matchingStudent = studentList.find(
                                (studentElement) =>
                                    String(studentElement._id) === String(groupStudentElement),
                            );
                            if (matchingStudent && !students.has(String(groupStudentElement))) {
                                students.set(String(groupStudentElement), {
                                    _student_id: groupStudentElement,
                                    academic_no: matchingStudent.user_id,
                                    name: matchingStudent.name,
                                    gender: matchingStudent.gender,
                                    activeStatus: matchingStudent.isActive,
                                    isDenial: false,
                                });
                            }
                        });
                    });
                });
            });
        }
        const sessionDeliveryData = await sessionDeliverySchema
            .find(
                {
                    _program_id: convertToMongoObjectId(programId),
                    'delivery_types.delivery_symbol': { $in: Array.from(sessionType) },
                },
                {
                    _id: 0,
                    session_name: 1,
                    session_symbol: 1,
                    'delivery_types.delivery_name': 1,
                    'delivery_types.delivery_symbol': 1,
                },
            )
            .lean();
        const deliveryTypeMap = new Map();
        sessionDeliveryData.forEach((deliveryElement) => {
            deliveryElement.delivery_types.forEach((type) => {
                deliveryTypeMap.set(type.delivery_symbol.toLowerCase(), {
                    deliveryName: type.delivery_name,
                    sessionTypeName: deliveryElement.session_name,
                    sessionTypeCode: deliveryElement.session_symbol,
                });
            });
        });
        if (courseData.setting) {
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    const sessionName = deliveryTypeMap.get(
                        sessionElement.session_type.toLowerCase(),
                    );
                    if (sessionName) {
                        sessionElement.deliveryTypeCode = sessionElement.session_type;
                        sessionElement.deliveryTypeName = sessionName.deliveryName;
                        sessionElement.sessionTypeName = sessionName.sessionTypeName;
                        sessionElement.sessionTypeCode = sessionName.sessionTypeCode;
                        delete sessionElement.session_type;
                    }
                });
            });
        }
        if (courseData) {
            const splitData = courseData.course_no.split(' ');
            courseData.subject = splitData[0];
            courseData.catalogNbr = splitData[1];
            courseData.strm = '2422';
        }
        students = Array.from(students.values());
        const studentDenialStatus = await userWarningDenialStatus({
            studentIds: students,
            courseId: courseData._course_id,
            level,
            term,
            institutionCalendarId,
            _institution_id: studentGroupData._institution_id,
        });
        const studentsGroupsDetails = {
            courseData,
            students: studentDenialStatus,
        };
        return { status: 200, message: 'Student List', data: studentsGroupsDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.individualStudentGroupNameList = async ({ body = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            courseId,
            year,
            level,
            term,
            rotationNo,
            studentIds,
        } = body;

        const studentList = await userSchema
            .find(
                {
                    isDeleted: false,
                    user_id: { $in: studentIds },
                },
                { user_id: 1, name: 1, gender: 1, isActive: 1 },
            )
            .lean();
        const studentIdList = studentList.map((studentElement) =>
            convertToMongoObjectId(studentElement._id),
        );
        const studentGroupData = await studentGroupsSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'groups.term': term,
                    'master.year': year,
                    'groups.level': level,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                    'groups.students._student_id': {
                        $in: studentIdList,
                    },
                },
                {
                    _institution_id: 1,
                    'groups.rotation': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.group_name': 1,
                    'groups.courses.setting.session_setting.groups': 1,
                },
            )
            .lean();
        if (!studentGroupData) return { status: 400, message: 'Groups not found' };
        const studentData =
            studentGroupData.groups.find(
                (groupElement) =>
                    groupElement.level.toLowerCase() === level.toLowerCase() &&
                    groupElement.term.toLowerCase() === term.toLowerCase(),
            ) || {};
        const courseData =
            studentData.courses?.find(
                (courseElement) => String(courseId) === String(courseElement._course_id),
            ) || {};
        const studentDetails = [];
        if (courseData.setting) {
            if (rotationNo)
                courseData.setting = courseData.setting.filter(
                    (settingElement) => String(settingElement._group_no) === String(rotationNo),
                );
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    sessionElement.groups.forEach((groupElement) => {
                        groupElement._student_ids.forEach((groupStudentElement) => {
                            const studentData = studentList.find(
                                (studentElement) =>
                                    String(studentElement._id) === String(groupStudentElement),
                            );
                            if (
                                studentIdList.find(
                                    (studentIdElement) =>
                                        String(studentIdElement) === String(groupStudentElement),
                                ) &&
                                studentData
                            ) {
                                const studentIndex = studentDetails.findIndex(
                                    (studentElement) =>
                                        studentElement._student_id === studentData._id,
                                );
                                if (studentIndex !== -1) {
                                    studentDetails[studentIndex].groups.push({
                                        groupId: groupElement._id,
                                        groupName: groupElement.group_name,
                                    });
                                } else {
                                    studentDetails.push({
                                        _student_id: studentData._id,
                                        academic_no: studentData.user_id,
                                        name: studentData.name,
                                        gender: studentData.gender,
                                        activeStatus: studentData.isActive,
                                        groups: [
                                            {
                                                groupId: groupElement._id,
                                                groupName: groupElement.group_name,
                                            },
                                        ],
                                    });
                                }
                            }
                        });
                    });
                });
            });
        }
        const studentDenialStatus = await userWarningDenialStatus({
            studentIds: studentDetails.map((studentElement) => ({
                _student_id: studentElement._student_id,
            })),
            courseId,
            level,
            term,
            institutionCalendarId,
            _institution_id: studentGroupData._institution_id,
        });
        studentDetails.forEach((studentElement) => {
            studentElement.isDenial =
                studentDenialStatus.find(
                    (denialElement) =>
                        String(denialElement._student_id) === String(studentElement._student_id),
                )?.isDenial || false;
        });
        return { status: 200, message: 'Student Group List', data: studentDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.classCourseDetails = async ({ body = {} }) => {
    try {
        const { courseDetails } = body;
        const institutionCalenderData = await institutionCalendarSchema
            .findOne(
                {
                    status: PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .sort({ createdAt: -1 })
            .lean();
        const studentGroupQuery = [];
        const matchingScheduleData = [];
        const courseCodes = courseDetails?.map((courseElement) =>
            `${courseElement.subject} ${courseElement.catalogNbr}`?.toUpperCase(),
        );
        const programCalenderData = await programCalenderSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalenderData?._id),
                    'level.course.courses_number': { $in: courseCodes },
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level._program_id': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.start_date': 1,
                    'level.course.end_date': 1,
                },
            )
            .lean();
        if (programCalenderData?.length) {
            courseDetails.forEach((courseDetailsElement) => {
                programCalenderData.forEach((calenderElement) => {
                    calenderElement?.level.forEach((levelElement) => {
                        levelElement.course.forEach((corseElement) => {
                            if (
                                convertToLowerCase(corseElement?.courses_number) ===
                                    convertToLowerCase(
                                        `${courseDetailsElement.subject} ${courseDetailsElement.catalogNbr}`,
                                    ) &&
                                new Date(corseElement.start_date).getTime() ===
                                    new Date(courseDetailsElement.startDate).getTime() &&
                                new Date(corseElement.end_date).getTime() ===
                                    new Date(courseDetailsElement.endDate).getTime()
                            ) {
                                studentGroupQuery.push({
                                    programId: levelElement?._program_id,
                                    courseId: corseElement?._course_id,
                                    year: levelElement?.year,
                                    level: levelElement?.level_no,
                                    term: levelElement?.term,
                                });
                            }
                        });
                    });
                });
            });
        }
        if (
            !institutionCalenderData ||
            !programCalenderData?.length ||
            !studentGroupQuery?.length
        ) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        const studentGroupData = await studentGroupsSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalenderData._id),
                    'master._program_id': {
                        $in: studentGroupQuery.map((studentGroupElement) =>
                            convertToMongoObjectId(studentGroupElement.programId),
                        ),
                    },
                    'master.year': {
                        $in: studentGroupQuery.map(
                            (studentGroupElement) => studentGroupElement.year,
                        ),
                    },
                    groups: {
                        $elemMatch: {
                            level: {
                                $in: studentGroupQuery.map(
                                    (studentGroupElement) => studentGroupElement.level,
                                ),
                            },
                            term: {
                                $in: studentGroupQuery.map(
                                    (studentGroupElement) => studentGroupElement.term,
                                ),
                            },
                            courses: {
                                $elemMatch: {
                                    _course_id: {
                                        $in: studentGroupQuery.map((studentGroupElement) =>
                                            convertToMongoObjectId(studentGroupElement.courseId),
                                        ),
                                    },
                                },
                            },
                        },
                    },
                },
                {
                    'master.program_no': 1,
                    'master.year': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses.setting.session_setting.groups.classNo': 1,
                },
            )
            .lean();
        if (!studentGroupData.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        studentGroupData.forEach((studentGroupDataElement) => {
            studentGroupDataElement.groups.forEach((groupElement) => {
                studentGroupQuery.forEach((studentGroupElement) => {
                    if (
                        String(groupElement.level) === String(studentGroupElement.level) &&
                        String(groupElement.term) === String(studentGroupElement.term)
                    ) {
                        groupElement.courses.forEach((courseElement) => {
                            if (
                                String(courseElement._course_id) ===
                                String(studentGroupElement.courseId)
                            ) {
                                courseElement.setting.forEach((settingElement) => {
                                    settingElement.session_setting.forEach((sessionElement) => {
                                        sessionElement.groups.forEach((groupElement) => {
                                            const matchingClassDetails = courseDetails?.find(
                                                (courseDetailsElement) =>
                                                    String(courseDetailsElement.classNbr) ===
                                                    String(groupElement?.classNo),
                                            );
                                            if (matchingClassDetails) {
                                                matchingScheduleData.push({
                                                    programCode:
                                                        studentGroupDataElement.master.program_no,
                                                    courseCode: courseElement.course_no,
                                                    yearCode: formatCode(
                                                        (codeFormate =
                                                            studentGroupDataElement.master.year),
                                                    ),
                                                    levelCode: formatCode(
                                                        (codeFormate = studentGroupElement.level),
                                                    ),
                                                    termCode: termCodeFormate(
                                                        (codeFormate = studentGroupElement.term),
                                                    ),
                                                    classNbr: matchingClassDetails?.classNbr,
                                                    subject: matchingClassDetails.subject,
                                                    catalogNbr: matchingClassDetails.catalogNbr,
                                                    strm: matchingClassDetails.strm,
                                                });
                                            }
                                        });
                                    });
                                });
                            }
                        });
                    }
                });
            });
        });
        if (matchingScheduleData?.length) {
            return {
                statusCode: 200,
                message: 'course details',
                data: matchingScheduleData,
            };
        }
        return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.encryptDecryptData = async ({ body = {} }) => {
    try {
        const { inputData, method, handledBy } = body;
        let processedData = [];
        switch (method) {
            case 'encrypt':
                processedData = encryptData({ content: inputData, handledBy });
                break;
            case 'decrypt':
                processedData = decryptData({ content: inputData, handledBy });
                break;
            default:
                processedData = inputData;
                break;
        }
        return { status: 200, message: 'Processed Data', data: processedData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.staffDurationBasedAvailability = async ({ body = {} }) => {
    try {
        const { staffIds, startDateAndTime, endDateAndTime } = body;
        const staffDetails = await userSchema
            .find(
                {
                    user_id: { $in: staffIds },
                },
                {
                    _id: 1,
                    user_id: 1,
                },
            )
            .lean();
        if (!staffDetails.length) return { status: 404, message: 'Staffs not found' };

        const staffScheduleDetails = await courseScheduleSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    scheduleStartDateAndTime: { $gte: new Date(startDateAndTime) },
                    scheduleEndDateAndTime: { $lte: new Date(endDateAndTime) },
                    'staffs._staff_id': {
                        $in: staffDetails.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                    },
                },
                {
                    _id: 1,
                    program_name: 1,
                    course_code: 1,
                    course_name: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_type': 1,
                    'staffs._staff_id': 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    status: 1,
                },
            )
            .lean();

        const groupedByStaff = {};

        staffScheduleDetails.forEach((scheduleElement) => {
            scheduleElement.staffs.forEach((staffElement) => {
                const staffId = String(staffElement._staff_id);
                if (!groupedByStaff[staffId]) {
                    const staffInfo = staffDetails.find(
                        (staffDetailElement) => String(staffDetailElement._id) === String(staffId),
                    );
                    if (staffInfo) {
                        groupedByStaff[staffId] = {
                            _staff_id: staffId,
                            user_id: staffInfo.user_id,
                            schedules: [],
                        };
                    }
                }
                if (groupedByStaff[staffId]) {
                    groupedByStaff[staffId].schedules.push({
                        _id: scheduleElement._id,
                        program_name: scheduleElement.program_name,
                        course_code: scheduleElement.course_code,
                        course_name: scheduleElement.course_name,
                        term: scheduleElement.term,
                        year_no: scheduleElement.year_no,
                        level_no: scheduleElement.level_no,
                        session: scheduleElement.session,
                        scheduleStartDateAndTime: scheduleElement.scheduleStartDateAndTime,
                        scheduleEndDateAndTime: scheduleElement.scheduleEndDateAndTime,
                        status: scheduleElement.status,
                    });
                }
            });
        });

        const groupedData = Object.values(groupedByStaff);

        return { status: 200, message: 'Staff Schedule Details', data: groupedData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.attendanceReportDetails = async ({ body = {} }) => {
    try {
        const { institutionCalendarCode, programCode, courseCode, yearNo, levelNo, term } = body;
        const queryParams = {
            institutionCalendarCode,
            programCode,
            courseCode,
            yearNo,
            levelNo,
            term,
        };
        logger.info(queryParams, 'DC External Sync -> attendanceReportDetails -> Start');

        const institutionCalendarData = await institutionCalendarSchema
            .findOne(
                {
                    calendar_name: institutionCalendarCode,
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        if (!institutionCalendarData) {
            logger.info(queryParams, 'DC External Sync -> attendanceReportDetails -> End');
            return {
                statusCode: 200,
                message: 'NO_DATA_FOUND',
                data: { students: [] },
            };
        }

        const programData = await programSchema
            .findOne(
                {
                    code: programCode,
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        if (!programData) {
            logger.info(queryParams, 'DC External Sync -> attendanceReportDetails -> End');
            return {
                statusCode: 200,
                message: 'NO_DATA_FOUND',
                data: { students: [] },
            };
        }

        const courseScheduleData = await courseScheduleSchema
            .find(
                {
                    _institution_calendar_id: institutionCalendarData._id,
                    _program_id: programData._id,
                    year_no: `year${yearNo}`,
                    level_no: `Level ${levelNo}`,
                    term,
                    type: REGULAR,
                    course_code: courseCode,
                    isDeleted: false,
                    isActive: true,
                    status: COMPLETED,
                },
                {
                    'students._id': 1,
                    'students.status': 1,
                    'students.user_id': 1,
                },
            )
            .lean();
        if (!courseScheduleData.length) {
            logger.info(queryParams, 'DC External Sync -> attendanceReportDetails -> End');
            return {
                statusCode: 200,
                message: 'NO_DATA_FOUND',
                data: { students: [] },
            };
        }

        const studentAttendanceStats = {};
        const studentIds = [];

        courseScheduleData.forEach((scheduleElement) => {
            scheduleElement.students.forEach((studentElement) => {
                const studentId = String(studentElement._id);
                studentIds.push(studentId);
                if (!studentAttendanceStats[studentId]) {
                    studentAttendanceStats[studentId] = {
                        studentId,
                        totalSchedules: 0,
                        attendedSchedule: 0,
                        absentCount: 0,
                        presentPercentage: 0,
                        absentPercentage: 0,
                    };
                }

                studentAttendanceStats[studentId].totalSchedules++;

                if (studentElement.status === PRESENT) {
                    studentAttendanceStats[studentId].attendedSchedule++;
                } else if (studentElement.status === ABSENT) {
                    studentAttendanceStats[studentId].absentCount++;
                }
            });
        });

        Object.values(studentAttendanceStats).forEach((student) => {
            if (student.totalSchedules > 0) {
                student.presentPercentage = Math.round(
                    (student.attendedSchedule / student.totalSchedules) * 100,
                );
                student.absentPercentage = Math.round(
                    (student.absentCount / student.totalSchedules) * 100,
                );
            }
        });

        const studentData = await userSchema
            .find(
                {
                    _id: { $in: studentIds },
                },
                { user_id: 1, syncKey: 1 },
            )
            .lean();

        logger.info(queryParams, 'DC External Sync -> attendanceReportDetails -> End');

        const studentAttendanceData = studentData.map((studentElement) => {
            const { studentId, ...attendanceStats } = studentAttendanceStats[studentElement._id];
            return {
                academicNo: studentElement.user_id,
                syncKey: studentElement.syncKey,
                ...attendanceStats,
            };
        });

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                students: studentAttendanceData,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
