const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.apple_leave_doc = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _leave_reason_doc: Joi.any().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.apply_leave = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    type: Joi.string().min(5).max(100).required(),
                    // from: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    // to: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    days: Joi.number(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.apply_leave_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.staff_report_absence_insert = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _institution_calendar_id: Joi.string().alphanum().length(24),
                    user_type: Joi.string().valid(
                        constant.EVENT_WHOM.STUDENT,
                        constant.EVENT_WHOM.STAFF,
                        constant.EVENT_WHOM.BOTH,
                    ),
                    staff_id: Joi.string().alphanum().length(24),
                    type: Joi.string().valid(
                        constant.LEAVE_TYPE.ONDUTY,
                        constant.LEAVE_TYPE.LEAVE,
                        constant.LEAVE_TYPE.PERMISSION,
                        constant.LEAVE_TYPE.REPORT_ABSENCE,
                    ),
                    // from: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    // to: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    days: Joi.number(),
                    is_noticed: Joi.bool().required(),
                    reason: Joi.string().min(3).max(100).required(),
                    _leave_reason_doc: Joi.string(),
                    created_by: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.student_report_absence_insert = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _institution_calendar_id: Joi.string().alphanum().length(24),
                    user_type: Joi.string().min(3).max(100).required(),
                    type: Joi.string().min(3).max(100).required(),
                    // from: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    // to: Joi.string().min(1).required().error(error => {
                    //     return error;
                    // }),
                    reason: Joi.string().min(3).max(100).required(),
                    application_date: Joi.date().required(),
                    approved_comments: Joi.string().min(3).max(100).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.reason_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    status: Joi.string().valid(
                        'onduty',
                        constant.LEAVE_TYPE.ONDUTY,
                        constant.LEAVE_TYPE.LEAVE,
                        constant.LEAVE_TYPE.PERMISSION,
                        constant.PRESENT,
                        constant.ABSENT,
                    ),
                    reason: Joi.string().required(),
                    updated_by: Joi.object()
                        .keys({
                            _staff_id: Joi.string().alphanum().length(24),
                            staff_name: Joi.object()
                                .keys({
                                    first: Joi.string().required(),
                                    middle: Joi.string().optional().allow(''),
                                    last: Joi.string().optional().allow(''),
                                    family: Joi.string().optional().allow(''),
                                })
                                .unknown(true),
                        })
                        .unknown(true),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.cancel_leave = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    comment: Joi.string().min(2).max(250).required(),
                    _person_id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
