// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.program_calendar_event = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(constant.CALENDAR.INSTITUTION, constant.CALENDAR.PROGRAM)
                        .required()
                        .error((error) => {
                            return req.t('EVENT_CALENDAR_REQUIRED');
                        }),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .required()
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    event_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('EVENT_DATE_REQUIRED');
                        }),
                    start_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('START_TIME_REQUIRED');
                        }),
                    end_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_TIME_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_batch_event = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(constant.CALENDAR.INSTITUTION, constant.CALENDAR.PROGRAM)
                        .required()
                        .error((error) => {
                            return req.t('EVENT_CALENDAR_REQUIRED');
                        }),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .required()
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM, constant.BATCH.BOTH)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    event_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('EVENT_DATE_REQUIRED');
                        }),
                    start_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('START_TIME_REQUIRED');
                        }),
                    end_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_TIME_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_event_remove = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_ID_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_event_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_event_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    rotation_count: Joi.number()
                        .min(1)
                        .max(5)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_COUNT_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_event_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.event_sync = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    _event_id: Joi.array().items(
                        Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error((error) => {
                                return req.t('_EVENT_ID_REQUIRED');
                            }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_course_event = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(
                            constant.CALENDAR.INSTITUTION,
                            constant.CALENDAR.PROGRAM,
                            constant.MODEL.COURSE,
                        )
                        .required()
                        .error((error) => {
                            return req.t('EVENT_CALENDAR_REQUIRED');
                        }),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .required()
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    event_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('EVENT_DATE_REQUIRED');
                        }),
                    start_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('START_TIME_REQUIRED');
                        }),
                    end_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_TIME_REQUIRED');
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_DATE_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_event_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(
                            constant.CALENDAR.INSTITUTION,
                            constant.CALENDAR.PROGRAM,
                            constant.MODEL.COURSE,
                        )
                        .error((error) => {
                            return req.t('EVENT_CALENDAR_REQUIRED');
                        }),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    event_date: Joi.date().error((error) => {
                        return req.t('EVENT_DATE_REQUIRED');
                    }),
                    start_time: Joi.date().error((error) => {
                        return req.t('START_TIME_REQUIRED');
                    }),
                    end_time: Joi.date().error((error) => {
                        return req.t('END_TIME_REQUIRED');
                    }),
                    end_date: Joi.date().error((error) => {
                        return req.t('END_DATE_REQUIRED');
                    }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_event_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(
                            constant.CALENDAR.INSTITUTION,
                            constant.CALENDAR.PROGRAM,
                            constant.MODEL.COURSE,
                        )
                        .error((error) => {
                            return req.t('EVENT_CALENDAR_REQUIRED');
                        }),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    event_date: Joi.date().error((error) => {
                        return req.t('EVENT_DATE_REQUIRED');
                    }),
                    start_time: Joi.date().error((error) => {
                        return req.t('START_TIME_REQUIRED');
                    }),
                    end_time: Joi.date().error((error) => {
                        return req.t('END_TIME_REQUIRED');
                    }),
                    end_date: Joi.date().error((error) => {
                        return req.t('END_DATE_REQUIRED');
                    }),
                    rotation_count: Joi.number()
                        .min(1)
                        .max(5)
                        .required()
                        .error((error) => {
                            return req.t('ROTATION_COUNT_REQUIRED');
                        }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.interim_event_sync = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM, constant.BATCH.BOTH)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    _event_id: Joi.array().items(
                        Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error((error) => {
                                return req.t('_EVENT_ID_REQUIRED');
                            }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_event_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .error((error) => {
                            return req.t('EVENT_TYPE_REQUIRED');
                        }),
                    event_name: Joi.object().keys({
                        first_language: Joi.string()
                            .min(1)
                            .max(2500)
                            .allow('')
                            .error((error) => {
                                return req.t('FIRST_LANGUAGE_REQUIRED');
                            }),
                    }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level_no: Joi.string()
                        .min(1)
                        .max(20)
                        .error((error) => {
                            return req.t('LEVEL_NO_REQUIRED');
                        }),
                    event_date: Joi.date().error((error) => {
                        return req.t('EVENT_DATE_REQUIRED');
                    }),
                    start_time: Joi.date().error((error) => {
                        return req.t('START_TIME_REQUIRED');
                    }),
                    end_time: Joi.date().error((error) => {
                        return req.t('END_TIME_REQUIRED');
                    }),
                    end_date: Joi.date().error((error) => {
                        return req.t('END_DATE_REQUIRED');
                    }),
                    _calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    _event_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('_EVENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
