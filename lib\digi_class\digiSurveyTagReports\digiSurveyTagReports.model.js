const {
    model,
    Schema,
    Types: { ObjectId },
} = require('mongoose');
const {
    DIGI_SURVEY_TAG_REPORTS,
    NOTIFICATION_PRIORITY: { LOW, MEDIUM, HIGH },
    USER,
    MAIL_DURATION: { DAILY, WEEKLY, MONTHLY },
} = require('../../utility/constants');

const targetRangeSchema = new Schema(
    {
        minimumRange: { type: Number },
        maximumRange: { type: Number },
    },
    { _id: false },
);

const basicDetailSchema = new Schema(
    {
        _id: { type: ObjectId },
        code: { type: String },
        name: { type: String },
        description: { type: String },
        targetRange: targetRangeSchema,
        notificationUsers: [
            {
                name: { type: String },
                email: { type: String },
                userType: { type: String },
            },
        ],
        targetRangeColorCode: { type: String },
    },
    { _id: false },
);

const tagSchema = new Schema({
    ...basicDetailSchema.obj,
});

const groupSchema = new Schema({
    ...basicDetailSchema.obj,
    tags: [{ type: ObjectId }],
});

const familySchema = new Schema({
    ...basicDetailSchema.obj,
    groups: [{ type: ObjectId }],
});

const digiSurveyTagReportSchema = new Schema(
    {
        institutionId: { type: ObjectId },
        createdBy: { type: ObjectId, ref: USER },
        tagReportName: { type: String },
        viewBy: { type: String },
        noOfTags: { type: Number },
        targetRangeDetails: {
            setCommonRangeForAllTags: { type: Boolean, default: true },
            targetRange: targetRangeSchema,
            mailDuration: { type: String, enum: [DAILY, WEEKLY, MONTHLY] },
            alertNotification: [
                {
                    isEnable: { type: Boolean, default: true },
                    performanceKey: { type: String, enum: [LOW, MEDIUM, HIGH] },
                    mailContent: { type: String },
                },
            ],
        },
        selectedTags: {
            isNeedToShow: { type: Boolean, default: true }, //to handle whether we need to show groups,families
            tags: [tagSchema],
        },
        selectedGroups: {
            isNeedToShow: { type: Boolean, default: false },
            groups: [groupSchema],
        },
        selectedFamilies: {
            isNeedToShow: { type: Boolean, default: false },
            families: [familySchema],
        },
        excludedSurveyIds: [
            // to check whether user off the particular question calculation for conducted survey
            {
                tagId: { type: ObjectId },
                questionId: { type: String },
                prefixSentence: { type: String },
                isOutcomeQuestion: { type: Boolean, default: false },
                surveyIds: [{ type: ObjectId }],
            },
        ],
        isDeleted: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(DIGI_SURVEY_TAG_REPORTS, digiSurveyTagReportSchema);
