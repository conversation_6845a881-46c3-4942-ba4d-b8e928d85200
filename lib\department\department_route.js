const express = require('express');
const route = express.Router();
const department = require('./department_controller');
const validater = require('./department_validator');
route.post('/list', department.list_values);
route.get('/program/:id', validater.department_id, department.list_program_department);
route.get('/program_no/:no', validater.program_no, department.list_department_program_no);
route.get('/division_subject/:id', validater.department_id, department.list_division_subject);
route.get('/:id', validater.department_id, department.list_id);
route.get('/', department.list);
route.post('/', validater.department, department.insert);
route.put('/:id', validater.department_id, validater.department_update, department.update);
route.delete('/:id', validater.department_id, department.delete);
route.post('/division/subject', validater.department_division_subject_get, department.list_depart_division_subject);

module.exports = route;