const Joi = require('joi');
const { com_response } = require('../utility/common');
const { EVENT_WHOM, GENDER, EVENT_MODE, NOTIFY_VIA } = require('../utility/constants');
const common_files = require('../utility/common');

exports.checkAvailabilityTimings = (req, res, next) => {
    ///^(1[012]|[1-9]):[0-5][0-9](\\s)?(?i)(AM|PM)$/
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    start_date: Joi.date()
                        .format('YYYY-MM-DD')
                        .raw()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_date: Joi.date()
                        .format('YYYY-MM-DD')
                        .raw()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    start_time: Joi.string()
                        .regex(/((1[0-2]|0?[1-9]):([0-5][0-9]) ?([AP][M]))/)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_time: Joi.string()
                        .min(3)
                        .max(20)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.commonEventCreate = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    infrastructure_id: Joi.string()
                        .alphanum()
                        .min(3)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    creator_id: Joi.string()
                        .alphanum()
                        .min(3)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    event_type: Joi.string()
                        .min(1)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    event_name: Joi.string()
                        .min(1)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    event_for: Joi.string()
                        .valid(EVENT_WHOM.STAFF, EVENT_WHOM.STUDENT, EVENT_WHOM.BOTH)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    agenta: Joi.array()
                        .items(Joi.string())
                        .min(1)
                        .max(25)
                        .error((error) => {
                            return error;
                        }),
                    gender: Joi.string()
                        .valid(GENDER.MALE, GENDER.FEMALE, GENDER.BOTH)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    no_of_attendees: Joi.number()
                        .integer()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    comittes: Joi.array()
                        .items(Joi.string())
                        .min(1)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    participants: Joi.array()
                        .items(Joi.string())
                        .min(1)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    start_date: Joi.date()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    start_time: Joi.string()
                        .regex(/\b((1[0-2]|0?[0-9]):([0-5][0-9]) ([AaPp][Mm]))/)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_time: Joi.string()
                        .regex(/\b((1[0-2]|0?[0-9]):([0-5][0-9]) ([AaPp][Mm]))/)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    event_mode: Joi.string()
                        .valid(EVENT_MODE.ONLINE, EVENT_MODE.OFFLINE)
                        .min(1)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    status: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    infra_approved: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_canceled: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_deleted: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_active: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    notify_via: Joi.string()
                        .valid(NOTIFY_VIA.EMAIL, NOTIFY_VIA.MOBILE)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    notify_message: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, (err, value) => {
        if (err) return com_response(res, 422, false, 'validation error', err.details[0].message);
        return next();
    });
};

exports.maintenanceEventCreate = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    infrastructure_id: Joi.string()
                        .alphanum()
                        .min(3)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    creator_id: Joi.string()
                        .alphanum()
                        .min(3)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    event_type: Joi.string()
                        .min(1)
                        .max(25)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    agenta: Joi.array()
                        .items(Joi.string())
                        .min(1)
                        .max(25)
                        .error((error) => {
                            return error;
                        }),
                    start_date: Joi.date()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_date: Joi.date()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    start_time: Joi.string()
                        .regex(/\b((1[0-2]|0?[0-9]):([0-5][0-9]) ([AaPp][Mm]))/)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    end_time: Joi.string()
                        .regex(/\b((1[0-2]|0?[0-9]):([0-5][0-9]) ([AaPp][Mm]))/)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    status: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    infra_approved: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_canceled: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_deleted: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    is_active: Joi.boolean()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    notify_via: Joi.string()
                        .valid(NOTIFY_VIA.EMAIL, NOTIFY_VIA.SMS)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    notify_message: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, (err, value) => {
        if (err) return com_response(res, 422, false, 'validation error', err.details[0].message);
        return next();
    });
};

exports.getAll = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object()
                .keys({
                    perPage: Joi.number()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    page: Joi.string()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, (err, value) => {
        if (err) return com_response(res, 422, false, 'validation error', err.details[0].message);
        return next();
    });
};

exports.update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.number()
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, (err, value) => {
        if (err) return com_response(res, 422, false, 'validation error', err.details[0].message);
        return next();
    });
};
