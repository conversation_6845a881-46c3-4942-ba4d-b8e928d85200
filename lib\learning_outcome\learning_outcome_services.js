const {
    allStudentGroupYesterday,
    allSessionOrderDatas,
    allCourseList,
    allCompletedActivities,
    allQuestions,
} = require('../../service/cache.service');
const { scheduleDateFormateChange } = require('../utility/common_functions');
const { convertToMongoObjectId } = require('../utility/common');
const { MALE } = require('../utility/constants');
const StudentSessionSurvey = require('../models/student_session_survey');
const { courseCLOLists } = require('../digi_class/survey/survey.service');
const { logger } = require('../utility/util_keys');

// Get Activity List based on Course wise
exports.studentGroupList = async (
    institutionCalendarId,
    programId,
    courseId,
    levelNo,
    term,
    rotationCount,
) => {
    try {
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.isDeleted === false &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === levelNo &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData) throw new Error('Student group not found');
        const sgLevel = studentGroupData.groups.find(
            (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
        );
        if (!sgLevel) throw new Error('Student Group Level not found');
        const sgCourseData = sgLevel.courses.find((ele) => ele._course_id.toString() === courseId);
        const studentWithGroupList = [];
        let studentWithOutGroupList = [];
        const sgCourseSettingData =
            rotationCount && parseInt(rotationCount) !== 0
                ? sgCourseData.setting.filter(
                      (ele) => parseInt(ele._group_no) === parseInt(rotationCount),
                  )
                : sgCourseData.setting;
        for (courseSetting of sgCourseSettingData) {
            const group_name =
                courseSetting._group_no !== undefined
                    ? courseSetting.gender === MALE
                        ? 'MG-' + courseSetting._group_no
                        : 'FG-' + courseSetting._group_no
                    : courseSetting.gender === MALE
                    ? 'MG-1'
                    : 'FG-1';
            // const group_name = courseSetting.gender === MALE ? 'MG-1' : 'FG-1';
            const deliveryGroup =
                courseSetting &&
                courseSetting.session_setting &&
                courseSetting.session_setting.length &&
                courseSetting.session_setting[0].groups
                    ? courseSetting.session_setting[0].groups
                    : [];
            const studentIds = deliveryGroup.map((ele) => ele._student_ids).flat();
            const groupStudentData = sgLevel.students
                .filter((ele) =>
                    studentIds.find((ele2) => ele2.toString() === ele._student_id.toString()),
                )
                .map((ele3) => {
                    return {
                        _student_id: ele3._student_id,
                        name: ele3.name,
                        academic_no: ele3.academic_no,
                        gender: ele3.gender,
                    };
                });
            studentWithGroupList.push({
                _id: courseSetting._id,
                gender: courseSetting.gender,
                group_name,
                students: groupStudentData,
            });
            studentWithOutGroupList = studentWithOutGroupList.concat(groupStudentData);
        }
        return {
            studentWithGroupList,
            studentWithOutGroupList,
        };
    } catch (error) {
        throw new Error(error);
    }
};

exports.selfEvaluationSurveySessionCloSlo = async (students, course) => {
    try {
        const { courseId, tab } = course;
        let reports;
        const studentGroups = [...new Set(students.map((student) => student.groupName))];

        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        const sessionOrderIds = courseSessionOrder.map((courseSessionOrderEntry) =>
            convertToMongoObjectId(courseSessionOrderEntry._id),
        );
        let rotationCount;
        if (course.rotationCount) {
            rotationCount = course.rotationCount;
        }
        const studentQuery = students.map((student) => {
            const studentQuery = {
                _institution_calendar_id: convertToMongoObjectId(course.institutionCalendarId),
                _program_id: convertToMongoObjectId(course.programId),
                _course_id: convertToMongoObjectId(course.courseId),
                _user_id: convertToMongoObjectId(student.studentId),
                // yearNo: convertToMongoObjectId(course.institutionCalendarId),
                levelNo: course.levelNo,
                term: course.term,
            };
            if (rotationCount) {
                studentQuery.rotationCount = rotationCount;
            }
            return studentQuery;
        });
        const surveyQuery = {
            isDeleted: false,
            isActive: true,
            slos: { $exists: true, $not: { $size: 0 } },
        };
        if (sessionOrderIds.length) {
            surveyQuery._session_order_id = { $in: sessionOrderIds };
        }
        if (studentQuery.length) {
            surveyQuery.$or = studentQuery;
        }
        const studentSurvey = await StudentSessionSurvey.find(surveyQuery, {
            slos: 1,
            _user_id: 1,
            _session_order_id: 1,
        });
        if (tab === 'session') {
            reports = courseSessionOrder.map((courseSession) => {
                const surveyStudentsWithSessionOrderId = studentSurvey.filter(
                    (student) =>
                        student._session_order_id.toString() === courseSession._id.toString(),
                );
                if (surveyStudentsWithSessionOrderId.length) {
                    surveyStudentsWithSessionOrderId.forEach((surveyStudent) => {
                        const studentGroupName = students.find(
                            (student) =>
                                student.studentId.toString() === surveyStudent._user_id.toString(),
                        );
                        if (studentGroupName) {
                            surveyStudent.studentGroupName = studentGroupName.groupName;
                        }
                    });
                }
                const studentSessionSlos = surveyStudentsWithSessionOrderId
                    .map((studentSessionSurveyEntry) => studentSessionSurveyEntry.slos)
                    .flat();
                const studentSession = studentSessionSlos
                    .map((studentSession) => studentSession.sloRating)
                    .filter((sessionSloRating) => sessionSloRating && sessionSloRating !== null);
                // in rating point format
                const pointRating =
                    studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        : 0;
                let ratings = [];
                ratings.push({
                    groupName: 'All Course Group',
                    pointRating,
                    totalStudents: students.length,
                    rattedStudents: surveyStudentsWithSessionOrderId.length,
                });
                const groupSurveys = studentGroups.map((studentGroup) => {
                    const groupSurvey = surveyStudentsWithSessionOrderId.filter(
                        (surveyStudent) => surveyStudent.studentGroupName === studentGroup,
                    );
                    const studentSessionSlos = groupSurvey
                        .map((studentSessionSurveyEntry) => studentSessionSurveyEntry.slos)
                        .flat();
                    const studentSession = studentSessionSlos
                        .map((studentSession) => studentSession.sloRating)
                        .filter(
                            (sessionSloRating) => sessionSloRating && sessionSloRating !== null,
                        );
                    // in rating point format
                    const pointRating =
                        studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            : 0;
                    return {
                        groupName: studentGroup,
                        pointRating,
                        totalStudents: students.filter(
                            (student) => student.groupName === studentGroup,
                        ).length,
                        rattedStudents: surveyStudentsWithSessionOrderId.length,
                    };
                });
                ratings = ratings.concat(groupSurveys);
                return {
                    _id: courseSession._id,
                    delivery_symbol: courseSession.delivery_symbol,
                    _session_id: courseSession._session_id,
                    delivery_topic: courseSession.delivery_topic,
                    delivery_type: courseSession.delivery_type,
                    delivery_no: courseSession.delivery_no,
                    s_no: courseSession.s_no,
                    ratings,
                };
            });
        }
        if (tab === 'slo') {
            const courseSessionSlos = courseSessionOrder
                .filter((courseSession) => courseSession.slo.length)
                .map((courseSessionEntry) => courseSessionEntry.slo)
                .flat();
            reports = courseSessionSlos.map((courseSessionSlo) => {
                const surveyStudentsWithSessionOrderId = studentSurvey.filter((student) =>
                    student.slos.find(
                        (slo) => slo._slo_id.toString() === courseSessionSlo._id.toString(),
                    ),
                );
                if (surveyStudentsWithSessionOrderId.length) {
                    surveyStudentsWithSessionOrderId.forEach((surveyStudent) => {
                        const studentGroupName = students.find(
                            (student) =>
                                student.studentId.toString() === surveyStudent._user_id.toString(),
                        );
                        if (studentGroupName) {
                            surveyStudent.studentGroupName = studentGroupName.groupName;
                        }
                    });
                }
                const studentSessionSlos = surveyStudentsWithSessionOrderId
                    .map((studentSessionSurveyEntry) =>
                        studentSessionSurveyEntry.slos.find(
                            (slo) => slo._slo_id.toString() === courseSessionSlo._id.toString(),
                        ),
                    )
                    .flat();
                const studentSession = studentSessionSlos
                    .map((studentSession) => studentSession.sloRating)
                    .filter((sessionSloRating) => sessionSloRating && sessionSloRating !== null);
                // in rating point format
                const pointRating =
                    studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        : 0;
                let ratings = [];
                ratings.push({
                    groupName: 'All Course Group',
                    pointRating,
                    totalStudents: students.length,
                    rattedStudents: surveyStudentsWithSessionOrderId.length,
                });
                const groupSurveys = studentGroups.map((studentGroup) => {
                    const groupSurvey = surveyStudentsWithSessionOrderId.filter(
                        (surveyStudent) => surveyStudent.studentGroupName === studentGroup,
                    );
                    const studentSessionSlos = groupSurvey
                        .map((studentSessionSurveyEntry) =>
                            studentSessionSurveyEntry.slos.find(
                                (slo) => slo._slo_id.toString() === courseSessionSlo._id.toString(),
                            ),
                        )
                        .flat();
                    const studentSession = studentSessionSlos
                        .map((studentSession) => studentSession.sloRating)
                        .filter(
                            (sessionSloRating) => sessionSloRating && sessionSloRating !== null,
                        );
                    // in rating point format
                    const pointRating =
                        studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            : 0;
                    return {
                        groupName: studentGroup,
                        pointRating,
                        totalStudents: students.filter(
                            (student) => student.groupName === studentGroup,
                        ).length,
                        rattedStudents: surveyStudentsWithSessionOrderId.length,
                    };
                });
                ratings = ratings.concat(groupSurveys);
                return {
                    _id: courseSessionSlo._id,
                    no: courseSessionSlo.no,
                    name: courseSessionSlo.name,
                    ratings,
                };
            });
        }
        if (tab === 'clo') {
            const courseData = (await allCourseList()).find(
                (courseElement) => courseElement._id.toString() === courseId,
            );
            let courseClos = await courseCLOLists({ courseData });
            courseClos = courseClos.map((clo) => clo.clos).flat();
            reports = courseClos.map((courseClo) => {
                const { slos } = courseClo;
                const sloIds = slos.map((slo) => slo._id.toString());
                const surveyStudentsWithSessionOrderId = studentSurvey.filter((student) =>
                    student.slos.find((slo) => sloIds.includes(slo._slo_id.toString())),
                );
                if (surveyStudentsWithSessionOrderId.length) {
                    surveyStudentsWithSessionOrderId.forEach((surveyStudent) => {
                        const studentGroupName = students.find(
                            (student) =>
                                student.studentId.toString() === surveyStudent._user_id.toString(),
                        );
                        if (studentGroupName) {
                            surveyStudent.studentGroupName = studentGroupName.groupName;
                        }
                    });
                }
                const studentSessionSlos = surveyStudentsWithSessionOrderId
                    .map((studentSessionSurveyEntry) =>
                        studentSessionSurveyEntry.slos.find((slo) =>
                            sloIds.includes(slo._slo_id.toString()),
                        ),
                    )
                    .flat();
                const studentSession = studentSessionSlos
                    .map((studentSession) => studentSession.sloRating)
                    .filter((sessionSloRating) => sessionSloRating && sessionSloRating !== null);
                // in rating point format
                const pointRating =
                    studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                        : 0;
                let ratings = [];
                ratings.push({
                    groupName: 'All Course Group',
                    pointRating,
                    totalStudents: students.length,
                    rattedStudents: surveyStudentsWithSessionOrderId.length,
                });
                const groupSurveys = studentGroups.map((studentGroup) => {
                    const groupSurvey = surveyStudentsWithSessionOrderId.filter(
                        (surveyStudent) => surveyStudent.studentGroupName === studentGroup,
                    );
                    const studentSessionSlos = groupSurvey
                        .map((studentSessionSurveyEntry) =>
                            studentSessionSurveyEntry.slos.find((slo) =>
                                sloIds.includes(slo._slo_id.toString()),
                            ),
                        )
                        .flat();
                    const studentSession = studentSessionSlos
                        .map((studentSession) => studentSession.sloRating)
                        .filter(
                            (sessionSloRating) => sessionSloRating && sessionSloRating !== null,
                        );
                    // in rating point format
                    const pointRating =
                        studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            ? studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length
                            : 0;
                    return {
                        groupName: studentGroup,
                        pointRating,
                        totalStudents: students.filter(
                            (student) => student.groupName === studentGroup,
                        ).length,
                        rattedStudents: surveyStudentsWithSessionOrderId.length,
                    };
                });
                ratings = ratings.concat(groupSurveys);
                return {
                    _id: courseClo._id,
                    no: courseClo.no,
                    name: courseClo.name,
                    ratings,
                };
            });
        }
        return { groups: studentGroups, reports };
    } catch (error) {
        throw new Error(error);
    }
};

// Get Activity List based on Course wise
exports.activityQuestionList = async ({ institutionCalendarId, courseId, term, rotationCount }) => {
    try {
        const allActivities = (await allCompletedActivities(institutionCalendarId)).filter(
            (activityElement) => activityElement.courseId.toString() === courseId.toString(),
        );
        const getAllQuestions = await allQuestions();
        for (activityElement of allActivities) {
            const questionWithWrongOption = [];
            for (questionElement of activityElement.questions) {
                const questionData = getAllQuestions.find(
                    (ele) => ele._id.toString() === questionElement._id.toString(),
                );
                if (!questionData) continue;
                const optionData = questionData.options.find((ele2) => ele2.answer);
                const wrongOptionData = questionData.options.find((ele2) => ele2.answer === false);
                if (questionData && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionData;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: convertToMongoObjectId(wrongOptionData._id),
                });
            }
            for (activityStudentElement of activityElement.students) {
                const missingQuestionDatas = questionWithWrongOption.filter(
                    (wrongElement) =>
                        !activityStudentElement.questions.find(
                            (questionElement) =>
                                wrongElement._questionId.toString() ===
                                questionElement._questionId.toString(),
                        ),
                );
                if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                    activityStudentElement.questions = [
                        ...activityStudentElement.questions,
                        ...missingQuestionDatas,
                    ];
                }
            }
        }
        return allActivities;
    } catch (error) {
        logger.error(error, 'Survey -> activityQuestionList -> Course Activity List Error');
        throw new Error(error);
    }
};

// Comparison Report Summary
exports.comparisonReportSummary = async ({
    studentWithGroupList,
    courseSessionOrder,
    userSurveyData,
    activityList,
    studentIds,
}) => {
    try {
        const sessionRating = [];
        const studentRatings = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_type: sessionOrderElement.delivery_type,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
                delivery_topic: sessionOrderElement.delivery_topic,
            };
            const studentDatas = [];
            for (studentIdElement of studentIds) {
                const studentSessionSurveyElement = userSurveyData.find(
                    (userSurveyElement) =>
                        userSurveyElement._session_order_id.toString() ===
                            sessionOrderElement._id.toString() &&
                        userSurveyElement._user_id.toString() === studentIdElement.toString(),
                );
                // for (studentSessionSurveyElement of userSessionSurvey) {
                const selfEvaluationRating = {
                    sloRating: null,
                    sloRatingPercentage: null,
                    slos: [],
                };
                if (
                    studentSessionSurveyElement &&
                    studentSessionSurveyElement.slos.length !== 0 &&
                    studentSessionSurveyElement.slos.length === sessionOrderElement.slo.length &&
                    !studentSessionSurveyElement.slos.find(
                        (sloElement) => sloElement.sloRating === null,
                    )
                ) {
                    selfEvaluationRating.sloRating = studentSessionSurveyElement.slos
                        .map((sloELement) => sloELement.sloRating)
                        .reduce((a, b) => (a !== null && b !== null ? a + b : 0));
                    if (selfEvaluationRating.sloRating !== null)
                        selfEvaluationRating.sloRating /= studentSessionSurveyElement.slos.length;
                    for (sloElement of studentSessionSurveyElement.slos) {
                        const sessionOrderSlo = sessionOrderElement.slo.find(
                            (slosElement) =>
                                slosElement._id.toString() === sloElement._slo_id.toString(),
                        );
                        selfEvaluationRating.slos.push({
                            // rating: sloElement.sloRating,
                            sloRating: sloElement.sloRating,
                            sloRatingPercentage: (sloElement.sloRating / 5) * 100,
                            sloNo: sessionOrderSlo.no,
                            deliverySymbol: sessionOrderElement.delivery_symbol,
                        });
                    }
                    selfEvaluationRating.sloRatingPercentage =
                        (selfEvaluationRating.sloRating / 5) * 100;
                }

                // Activity Report
                const QuizRating = {
                    sloRating: null,
                    sloRatingPercentage: null,
                    slos: [],
                };
                const quizSolsData = [];
                const sessionActivity = activityList.filter(
                    (activityElement) =>
                        activityElement.sessionFlowIds.find(
                            (sessionIdElement) =>
                                sessionIdElement._id.toString() ===
                                sessionOrderElement._id.toString(),
                        ) &&
                        activityElement.students.find(
                            (ele) => ele._studentId.toString() === studentIdElement.toString(),
                        ),
                );
                for (studentActivityElement of sessionActivity) {
                    const studentActivityReports = studentActivityElement.students.find(
                        (ele) => ele._studentId.toString() === studentIdElement.toString(),
                    );
                    for (activityQuestionElement of studentActivityElement.questions) {
                        if (
                            activityQuestionElement.questionData &&
                            activityQuestionElement.questionData.sloIds
                        )
                            for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                                const sloData = sessionOrderElement.slo.find(
                                    (ele2) => ele2._id.toString() === sloIdElement.toString(),
                                );
                                if (sloData) {
                                    const quizSolObject = {
                                        _id: sloData._id,
                                        sloNo: sloData.no,
                                        deliverySymbol: sessionOrderElement.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    };
                                    const loc = quizSolsData.findIndex(
                                        (ele) => ele._id.toString() === sloIdElement.toString(),
                                    );
                                    if (loc !== -1) quizSolsData[loc].count++;
                                    else quizSolsData.push(quizSolObject);
                                }
                            }
                    }
                    if (quizSolsData.length) {
                        let studentQuestionMarks = 0;
                        for (questionElement of studentActivityReports.questions) {
                            const questionData = studentActivityElement.questions.find(
                                (ele) =>
                                    ele._id.toString() === questionElement._questionId.toString(),
                            );
                            if (
                                questionData &&
                                questionElement._optionId &&
                                questionData.correctOption.toString() ===
                                    questionElement._optionId.toString()
                            ) {
                                studentQuestionMarks++;
                                for (sloElement of questionData.questionData.sloIds) {
                                    const sloLoc = quizSolsData.findIndex(
                                        (ele) => ele._id.toString() === sloElement.toString(),
                                    );
                                    if (sloLoc !== -1) {
                                        quizSolsData[sloLoc].mark++;
                                    }
                                }
                            }
                        }
                    }
                    for (quizSolsElement of quizSolsData) {
                        quizSolsElement.rating = quizSolsElement.mark / quizSolsElement.count;
                        quizSolsElement.sloRatingPercentage = quizSolsElement.rating * 100;
                        quizSolsElement.sloRating = quizSolsElement.rating * 5;
                    }
                }
                if (quizSolsData.length) {
                    const sessionSlos = quizSolsData.map((sloELement) => sloELement.rating);
                    if (sessionSlos.length)
                        QuizRating.sloRatingPercentage =
                            (sessionSlos.reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                                sessionSlos.length) *
                            100;
                    QuizRating.slos = quizSolsData.map((sloElement) => {
                        return {
                            // rating: sloElement.rating,
                            sloRating: sloElement.sloRating,
                            sloRatingPercentage: sloElement.sloRatingPercentage,
                            sloNo: sloElement.sloNo,
                            deliverySymbol: sloElement.deliverySymbol,
                        };
                    });
                    QuizRating.sloRating = (QuizRating.sloRatingPercentage / 100) * 5;
                }
                if (selfEvaluationRating.sloRating !== null || QuizRating.sloRating !== null)
                    studentDatas.push({
                        studentId: studentIdElement,
                        selfEvaluationRating,
                        QuizRating,
                    });
                // studentRatings
                const studentRatingIndex = studentRatings.findIndex(
                    (studentElement) =>
                        studentElement.studentId.toString() === studentIdElement.toString(),
                );
                if (studentRatingIndex === -1) {
                    const studentRatingObject = {
                        studentId: studentIdElement,
                        selfEvaluationRating: null,
                        selfEvaluationCount: 0,
                        QuizRating: null,
                        QuizCount: 0,
                    };
                    if (selfEvaluationRating.sloRating !== null) {
                        studentRatingObject.selfEvaluationRating =
                            studentRatingObject.selfEvaluationRating &&
                            studentRatingObject.selfEvaluationRating !== null
                                ? studentRatingObject.selfEvaluationRating +
                                  selfEvaluationRating.sloRating
                                : selfEvaluationRating.sloRating;
                        studentRatingObject.selfEvaluationCount++;
                    }
                    if (QuizRating.sloRating !== null) {
                        studentRatingObject.QuizRating =
                            studentRatingObject.QuizRating &&
                            studentRatingObject.QuizRating !== null
                                ? studentRatingObject.QuizRating + QuizRating.sloRating
                                : QuizRating.sloRating;
                        studentRatingObject.QuizCount++;
                    }
                    studentRatings.push(studentRatingObject);
                } else {
                    if (selfEvaluationRating.sloRating !== null) {
                        studentRatings[studentRatingIndex].selfEvaluationRating =
                            studentRatings[studentRatingIndex].selfEvaluationRating &&
                            studentRatings[studentRatingIndex].selfEvaluationRating !== null
                                ? studentRatings[studentRatingIndex].selfEvaluationRating +
                                  selfEvaluationRating.sloRating
                                : selfEvaluationRating.sloRating;
                        studentRatings[studentRatingIndex].selfEvaluationCount++;
                    }
                    if (QuizRating.sloRating !== null) {
                        studentRatings[studentRatingIndex].QuizRating =
                            studentRatings[studentRatingIndex].QuizRating &&
                            studentRatings[studentRatingIndex].QuizRating !== null
                                ? studentRatings[studentRatingIndex].QuizRating +
                                  QuizRating.sloRating
                                : QuizRating.sloRating;
                        studentRatings[studentRatingIndex].QuizCount++;
                    }
                }
                // }
            }
            const sessionSloSurvey = studentDatas
                .filter((studentElement) => studentElement.selfEvaluationRating.sloRating !== null)
                .map((studentElement) => studentElement.selfEvaluationRating.sloRating);
            const sessionSloQuiz = studentDatas
                .filter((studentElement) => studentElement.QuizRating.sloRating !== null)
                .map((studentElement) => studentElement.QuizRating.sloRating);
            const sessionSloSurveyRating =
                sessionSloSurvey.length !== 0
                    ? sessionSloSurvey.reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                      sessionSloSurvey.length
                    : null;
            const sessionSloQuizRating =
                sessionSloQuiz.length !== 0
                    ? sessionSloQuiz.reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                      sessionSloQuiz.length
                    : null;
            const selfEvaluationRating = {
                selfEvaluationRating: {
                    studentCount: sessionSloSurveyRating !== null ? sessionSloSurvey.length : null,
                    sloRating: sessionSloSurveyRating,
                    sloRatingPercentage:
                        sessionSloSurveyRating !== null ? (sessionSloSurveyRating / 5) * 100 : null,
                },
            };
            const quizRating = {
                QuizRating: {
                    studentCount: sessionSloQuizRating !== null ? sessionSloQuizRating.length : 0,
                    sloRating:
                        sessionSloQuizRating !== null ? (sessionSloQuizRating / 100) * 5 : null,
                    sloRatingPercentage: sessionSloQuizRating,
                },
            };
            sessionRating.push({
                ...objectData,
                ...selfEvaluationRating,
                ...quizRating,
                studentCount: studentDatas.length,
                studentDatas,
            });
        }
        // Student Group calculation
        const allCourseGroup = {
            selfEvaluationRatings: 0,
            selfEvaluationCount: 0,
            QuizRatings: 0,
            QuizCount: 0,
        };
        for (sgElement of studentWithGroupList) {
            sgElement.studentCount = sgElement.students.length;
            sgElement.selfEvaluationRatings = 0;
            sgElement.selfEvaluationCount = 0;
            sgElement.QuizRatings = 0;
            sgElement.QuizCount = 0;
            for (studentElement of sgElement.students) {
                const studentRating = studentRatings.find(
                    (studentRatingElement) =>
                        studentRatingElement.studentId.toString() ===
                        studentElement._student_id.toString(),
                );
                if (studentRating) {
                    const studentSurveyRating =
                        studentRating.selfEvaluationCount !== null &&
                        studentRating.selfEvaluationCount !== 0
                            ? studentRating.selfEvaluationRating / studentRating.selfEvaluationCount
                            : null;
                    const studentQuizRating =
                        studentRating.QuizCount !== null && studentRating.QuizCount !== 0
                            ? studentRating.QuizRating / studentRating.QuizCount
                            : null;
                    studentElement.selfEvaluationRating = {
                        sloRating: studentSurveyRating,
                        sloRatingPercentage:
                            studentSurveyRating !== null ? (studentSurveyRating / 5) * 100 : null,
                    };
                    studentElement.QuizRating = {
                        sloRating:
                            studentQuizRating !== null ? (studentQuizRating / 100) * 5 : null,
                        sloRatingPercentage: studentQuizRating,
                    };
                    if (studentSurveyRating !== null) {
                        sgElement.selfEvaluationRatings += studentSurveyRating;
                        sgElement.selfEvaluationCount++;
                    }
                    if (studentQuizRating !== null) {
                        sgElement.QuizRatings += studentQuizRating;
                        sgElement.QuizCount++;
                    }
                }
            }
            const groupSelfEvaluationRating =
                sgElement.selfEvaluationRatings / sgElement.selfEvaluationCount;
            const groupQuizRating = sgElement.QuizRatings / sgElement.QuizCount;
            sgElement.selfEvaluationRating = {
                sloRating: groupSelfEvaluationRating,
                sloRatingPercentage:
                    groupSelfEvaluationRating !== null
                        ? (groupSelfEvaluationRating / 5) * 100
                        : null,
            };
            sgElement.QuizRating = {
                sloRating: groupQuizRating !== null ? (groupQuizRating / 100) * 5 : null,
                sloRatingPercentage: groupQuizRating,
            };
            allCourseGroup.selfEvaluationRatings += sgElement.selfEvaluationRatings;
            allCourseGroup.selfEvaluationCount += sgElement.selfEvaluationCount;
            allCourseGroup.QuizRatings += sgElement.QuizRatings;
            allCourseGroup.QuizCount += sgElement.QuizCount;
        }
        const allCourseGroupSelfEvaluationRating =
            allCourseGroup.selfEvaluationRatings / allCourseGroup.selfEvaluationCount;
        const allCourseGroupQuizRating = allCourseGroup.QuizRatings / allCourseGroup.QuizCount;
        allCourseGroup.selfEvaluationRating = {
            sloRating: allCourseGroupSelfEvaluationRating,
            sloRatingPercentage: (allCourseGroupSelfEvaluationRating / 5) * 100,
        };
        allCourseGroup.QuizRating = {
            sloRating: allCourseGroupQuizRating,
            sloRatingPercentage: (allCourseGroupQuizRating / 100) * 5,
        };
        return { sessionRating, /* studentRatings, */ studentWithGroupList, allCourseGroup };
    } catch (error) {
        logger.error(
            error,
            'Survey -> comparisonReportSummary -> Course Comparison Report Summary Error',
        );
        throw new Error(error);
    }
};

// Self Evaluation Slo
exports.comparisonReportSlo = async ({
    studentWithGroupList,
    courseSessionOrder,
    userSurveyData,
    activityList,
    studentIds,
}) => {
    try {
        const ratingReport = [];
        const studentRatings = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            for (sessionSloElement of sessionOrderElement.slo) {
                const sloObject = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                };
                const studentDatas = [];
                for (studentIdElement of studentIds) {
                    const studentSessionSurveyElement = userSurveyData.find(
                        (userSurveyElement) =>
                            userSurveyElement._session_order_id.toString() ===
                                sessionOrderElement._id.toString() &&
                            userSurveyElement._user_id.toString() === studentIdElement.toString(),
                    );
                    const userRating =
                        studentSessionSurveyElement &&
                        studentSessionSurveyElement.slos &&
                        studentSessionSurveyElement.slos.length !== 0 &&
                        studentSessionSurveyElement.slos.find(
                            (sloElement) =>
                                sloElement._slo_id.toString() === sessionSloElement._id.toString(),
                        )
                            ? studentSessionSurveyElement.slos.find(
                                  (sloElement) =>
                                      sloElement._slo_id.toString() ===
                                      sessionSloElement._id.toString(),
                              ).sloRating
                            : null;

                    // Activity Report
                    const quizObject = {
                        mark: 0,
                        count: 0,
                        sloRatingPercentage: 0,
                    };
                    const sessionActivity = activityList.filter((activityElement) =>
                        activityElement.sessionFlowIds.find(
                            (sessionIdElement) =>
                                sessionIdElement._id.toString() ===
                                    sessionOrderElement._id.toString() &&
                                activityElement.students.find(
                                    (ele) =>
                                        ele._studentId.toString() === studentIdElement.toString(),
                                ),
                        ),
                    );
                    for (studentActivityElement of sessionActivity) {
                        const studentActivityReports = studentActivityElement.students.find(
                            (ele) => ele._studentId.toString() === studentIdElement.toString(),
                        );
                        for (activityQuestionElement of studentActivityElement.questions) {
                            if (
                                activityQuestionElement.questionData &&
                                activityQuestionElement.questionData.sloIds &&
                                activityQuestionElement.questionData.sloIds.find(
                                    (sloIdElement) =>
                                        sloIdElement.toString() ===
                                        sessionSloElement._id.toString(),
                                )
                            )
                                quizObject.count++;
                        }
                        if (quizObject.count) {
                            for (questionElement of studentActivityReports.questions) {
                                const questionData = studentActivityElement.questions.find(
                                    (ele) =>
                                        ele._id.toString() ===
                                        questionElement._questionId.toString(),
                                );
                                if (
                                    questionData &&
                                    questionElement._optionId &&
                                    questionData.correctOption.toString() ===
                                        questionElement._optionId.toString() &&
                                    questionData.questionData.sloIds.find(
                                        (sloIdElement) =>
                                            sloIdElement.toString() ===
                                            sessionSloElement._id.toString(),
                                    )
                                )
                                    quizObject.mark++;
                            }
                        }
                    }
                    quizObject.sloRatingPercentage =
                        quizObject.count !== null
                            ? (quizObject.mark / quizObject.count) * 100
                            : null;
                    const ratingObjects = {
                        selfEvaluationRating: {
                            sloRating: userRating,
                            sloRatingPercentage:
                                userRating !== null ? (userRating / 5) * 100 : null,
                        },
                        QuizRating: {
                            questionCount: quizObject.count,
                            sloRating:
                                quizObject.sloRatingPercentage !== null
                                    ? (quizObject.sloRatingPercentage / 100) * 5
                                    : null,
                            sloRatingPercentage: quizObject.sloRatingPercentage,
                        },
                    };
                    if (userRating !== null || quizObject.count !== 0)
                        studentDatas.push({
                            studentId: studentIdElement.toString(),
                            ...ratingObjects,
                        });

                    // studentRatings
                    const studentRatingIndex = studentRatings.findIndex(
                        (studentElement) =>
                            studentElement.studentId.toString() ===
                            studentIdElement.toString().toString(),
                    );
                    if (studentRatingIndex === -1) {
                        const studentRatingObject = {
                            studentId: studentIdElement.toString(),
                            selfEvaluationRating: null,
                            selfEvaluationCount: 0,
                            QuizRating: null,
                            QuizCount: 0,
                        };
                        if (ratingObjects.selfEvaluationRating.sloRating !== null) {
                            studentRatingObject.selfEvaluationRating =
                                studentRatingObject.selfEvaluationRating &&
                                studentRatingObject.selfEvaluationRating !== null
                                    ? studentRatingObject.selfEvaluationRating +
                                      ratingObjects.selfEvaluationRating.sloRating
                                    : ratingObjects.selfEvaluationRating.sloRating;
                            studentRatingObject.selfEvaluationCount++;
                        }
                        if (quizObject.count !== 0) {
                            studentRatingObject.QuizRating =
                                studentRatingObject.QuizRating &&
                                studentRatingObject.QuizRating !== null
                                    ? studentRatingObject.QuizRating +
                                      ratingObjects.QuizRating.sloRating
                                    : ratingObjects.QuizRating.sloRating;
                            studentRatingObject.QuizCount++;
                        }
                        if (
                            ratingObjects.selfEvaluationRating.sloRating !== null ||
                            quizObject.count !== 0
                        )
                            studentRatings.push(studentRatingObject);
                    } else {
                        if (userRating !== null) {
                            studentRatings[studentRatingIndex].selfEvaluationRating =
                                studentRatings[studentRatingIndex].selfEvaluationRating &&
                                studentRatings[studentRatingIndex].selfEvaluationRating !== null
                                    ? studentRatings[studentRatingIndex].selfEvaluationRating +
                                      ratingObjects.selfEvaluationRating.sloRating
                                    : ratingObjects.selfEvaluationRating.sloRating;
                            studentRatings[studentRatingIndex].selfEvaluationCount++;
                        }
                        if (quizObject.count !== 0) {
                            studentRatings[studentRatingIndex].QuizRating =
                                studentRatings[studentRatingIndex].QuizRating &&
                                studentRatings[studentRatingIndex].QuizRating !== null
                                    ? studentRatings[studentRatingIndex].QuizRating +
                                      ratingObjects.QuizRating.sloRating
                                    : ratingObjects.QuizRating.sloRating;
                            studentRatings[studentRatingIndex].QuizCount++;
                        }
                    }
                }
                const sessionSloSurvey = studentDatas
                    .filter(
                        (studentElement) => studentElement.selfEvaluationRating.sloRating !== null,
                    )
                    .map((studentElement) => studentElement.selfEvaluationRating.sloRating);
                const sessionSloQuiz = studentDatas
                    .filter((studentElement) => studentElement.QuizRating.sloRating !== null)
                    .map((studentElement) => studentElement.QuizRating.sloRating);
                const sessionSloSurveyRating =
                    sessionSloSurvey.length !== 0
                        ? sessionSloSurvey.reduce((a, b) =>
                              a !== null && b !== null ? a + b : 0,
                          ) / sessionSloSurvey.length
                        : null;
                const sessionSloQuizRating =
                    sessionSloQuiz.length !== 0
                        ? sessionSloQuiz.reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                          sessionSloQuiz.length
                        : null;
                const selfEvaluationRating = {
                    selfEvaluationRating: {
                        studentCount:
                            sessionSloSurveyRating !== null ? sessionSloSurvey.length : null,
                        sloRating: sessionSloSurveyRating,
                        sloRatingPercentage:
                            sessionSloSurveyRating !== null
                                ? (sessionSloSurveyRating / 5) * 100
                                : null,
                    },
                };
                const quizRating = {
                    QuizRating: {
                        studentCount:
                            sessionSloQuizRating !== null ? sessionSloQuizRating.length : 0,
                        sloRating:
                            sessionSloQuizRating !== null ? (sessionSloQuizRating / 100) * 5 : null,
                        sloRatingPercentage: sessionSloQuizRating,
                    },
                };

                ratingReport.push({
                    ...objectData,
                    ...sloObject,
                    ...selfEvaluationRating,
                    ...quizRating,
                    studentCount: studentDatas.length,
                    studentDatas,
                });
            }
        }
        // Student Group calculation
        const allCourseGroup = {
            selfEvaluationRatings: 0,
            selfEvaluationCount: 0,
            QuizRatings: 0,
            QuizCount: 0,
        };
        for (sgElement of studentWithGroupList) {
            sgElement.studentCount = sgElement.students.length;
            sgElement.selfEvaluationRatings = 0;
            sgElement.selfEvaluationCount = 0;
            sgElement.QuizRatings = 0;
            sgElement.QuizCount = 0;
            for (studentElement of sgElement.students) {
                const studentRating = studentRatings.find(
                    (studentRatingElement) =>
                        studentRatingElement.studentId.toString() ===
                        studentElement._student_id.toString(),
                );
                if (studentRating) {
                    const studentSurveyRating =
                        studentRating.selfEvaluationCount !== 0
                            ? studentRating.selfEvaluationRating / studentRating.selfEvaluationCount
                            : null;
                    const studentQuizRating =
                        studentRating.QuizCount !== null && studentRating.QuizCount !== 0
                            ? studentRating.QuizRating / studentRating.QuizCount
                            : null;
                    studentElement.selfEvaluationRating = {
                        sloRatingCount: studentRating.selfEvaluationCount,
                        sloRating: studentSurveyRating,
                        sloRatingPercentage:
                            studentSurveyRating !== null ? (studentSurveyRating / 5) * 100 : null,
                    };
                    studentElement.QuizRating = {
                        sloRating:
                            studentQuizRating !== null ? (studentQuizRating / 100) * 5 : null,
                        sloRatingPercentage: studentQuizRating,
                    };
                    if (studentSurveyRating !== null) {
                        sgElement.selfEvaluationRatings += studentSurveyRating;
                        sgElement.selfEvaluationCount++;
                    }
                    if (studentQuizRating !== null) {
                        sgElement.QuizRatings += studentQuizRating;
                        sgElement.QuizCount++;
                    }
                }
            }
            const groupSelfEvaluationRating =
                sgElement.selfEvaluationRatings / sgElement.selfEvaluationCount;
            const groupQuizRating = sgElement.QuizRatings / sgElement.QuizCount;
            sgElement.selfEvaluationRating = {
                sloRating: groupSelfEvaluationRating,
                sloRatingPercentage:
                    groupSelfEvaluationRating !== null
                        ? (groupSelfEvaluationRating / 5) * 100
                        : null,
            };
            sgElement.QuizRating = {
                sloRating: groupQuizRating !== null ? (groupQuizRating / 100) * 5 : null,
                sloRatingPercentage: groupQuizRating,
            };
            allCourseGroup.selfEvaluationRatings += sgElement.selfEvaluationRatings;
            allCourseGroup.selfEvaluationCount += sgElement.selfEvaluationCount;
            allCourseGroup.QuizRatings += sgElement.QuizRatings;
            allCourseGroup.QuizCount += sgElement.QuizCount;
        }
        const allCourseGroupSelfEvaluationRating =
            allCourseGroup.selfEvaluationRatings / allCourseGroup.selfEvaluationCount;
        const allCourseGroupQuizRating = allCourseGroup.QuizRatings / allCourseGroup.QuizCount;
        allCourseGroup.selfEvaluationRating = {
            sloRating: allCourseGroupSelfEvaluationRating,
            sloRatingPercentage: (allCourseGroupSelfEvaluationRating / 5) * 100,
        };
        allCourseGroup.QuizRating = {
            sloRating: allCourseGroupQuizRating,
            sloRatingPercentage: (allCourseGroupQuizRating / 100) * 5,
        };
        return { ratingReport, /* studentRatings, */ studentWithGroupList, allCourseGroup };
    } catch (error) {
        logger.error(error, 'Survey -> comparisonReportSlo -> Course Comparison Report SLO Error');
        throw new Error(error);
    }
};

// Self Evaluation Slo
exports.comparisonReportClo = async ({
    studentWithGroupList,
    courseSessionOrder,
    userSurveyData,
    activityList,
    studentIds,
    courseClo,
}) => {
    try {
        userSurveyData = userSurveyData
            .map((userSurveyElement) =>
                userSurveyElement.slos.map((sloElement) => {
                    return {
                        _user_id: userSurveyElement._user_id,
                        ...sloElement.toJSON(),
                    };
                }),
            )
            .flat();
        // Activity Report flow
        const activityFlow = ({ _id, studentId }) => {
            const quizObject = {
                mark: 0,
                count: 0,
            };
            for (studentActivityElement of activityList) {
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentId.toString(),
                );
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds &&
                        activityQuestionElement.questionData.sloIds.find(
                            (sloIdElement) => sloIdElement.toString() === _id.toString(),
                        )
                    )
                        quizObject.count++;
                }
                if (quizObject.count && studentActivityReports) {
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        if (
                            questionData &&
                            questionElement._optionId &&
                            questionData.correctOption.toString() ===
                                questionElement._optionId.toString() &&
                            questionData.questionData.sloIds.find(
                                (sloIdElement) => sloIdElement.toString() === _id.toString(),
                            )
                        )
                            quizObject.mark++;
                    }
                }
            }
            return quizObject;
        };

        for (courseDomainElement of courseClo) {
            for (cloElement of courseDomainElement.clos) {
                const studentDatas = [];
                for (studentIdElement of studentIds) {
                    const userSloData = userSurveyData.filter(
                        (userSurveySloElement) =>
                            userSurveySloElement.sloRating !== null &&
                            cloElement.slos.find(
                                (sloIdElement) =>
                                    sloIdElement._id.toString() ===
                                    userSurveySloElement._slo_id.toString(),
                            ),
                    );
                    const userSloRating =
                        userSloData && userSloData.length !== 0
                            ? userSloData
                                  .map((sloELement) => sloELement.sloRating)
                                  .reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                              userSloData.length
                            : null;
                    const userRating = {
                        // sloRating: userSloRating,
                        selfEvaluationRating: {
                            sloRating: userSloRating,
                            sloRatingPercentage:
                                userSloRating !== null ? (userSloRating / 5) * 100 : null,
                        },
                        QuizRating: {
                            sloRating: null,
                            sloRatingPercentage: null,
                            quizObject: {
                                mark: 0,
                                count: 0,
                            },
                        },
                        slos: [],
                    };
                    for (sloElement of cloElement.slos) {
                        const sessionOrderSlo = userSloData.find(
                            (slosElement) =>
                                slosElement._slo_id.toString() === sloElement._id.toString(),
                        );
                        const quizSlos = activityFlow({
                            _id: sloElement._id,
                            studentId: studentIdElement.toString(),
                        });
                        if (quizSlos.count && quizSlos.count !== 0) {
                            userRating.QuizRating.quizObject.mark +=
                                (quizSlos.mark / quizSlos.count) * 100;
                            userRating.QuizRating.quizObject.count++;
                        }
                        userRating.slos.push({
                            // sloRating: sessionOrderSlo ? sessionOrderSlo.sloRating : null,
                            sloNo: sloElement.no,
                            deliverySymbol: sloElement.delivery_symbol,
                            selfEvaluationRating: {
                                sloRating:
                                    sessionOrderSlo && sessionOrderSlo !== null
                                        ? sessionOrderSlo.sloRating
                                        : null,
                                sloRatingPercentage:
                                    sessionOrderSlo && sessionOrderSlo !== null
                                        ? (sessionOrderSlo.sloRating / 5) * 100
                                        : null,
                            },
                            // quizObject: quizSlos,
                            QuizRating: {
                                sloRating:
                                    quizSlos.count && quizSlos.count !== 0
                                        ? (quizSlos.mark / quizSlos.count) * 5
                                        : null,
                                sloRatingPercentage:
                                    quizSlos.count && quizSlos.count !== 0
                                        ? (quizSlos.mark / quizSlos.count) * 100
                                        : null,
                            },
                        });
                    }
                    const quizClo = activityFlow({
                        _id: cloElement._id,
                        studentId: studentIdElement.toString(),
                    });
                    if (quizClo.count && quizClo.count !== 0) {
                        userRating.QuizRating.quizObject.mark +=
                            (quizClo.mark / quizClo.count) * 100;
                        userRating.QuizRating.quizObject.count++;
                    }
                    if (userRating.QuizRating.quizObject.count !== 0) {
                        userRating.QuizRating.sloRatingPercentage =
                            userRating.QuizRating.quizObject.mark /
                            userRating.QuizRating.quizObject.count;
                        userRating.QuizRating.sloRating =
                            (userRating.QuizRating.sloRatingPercentage / 100) * 5;
                        delete userRating.QuizRating.quizObject;
                    }
                    if (
                        userRating.selfEvaluationRating.sloRating !== null ||
                        userRating.QuizRating.quizObject.count !== 0
                    )
                        studentDatas.push({
                            studentId: studentIdElement.toString(),
                            ...userRating,
                        });
                }
                cloElement.studentDatas = studentDatas;
                delete cloElement.slos;
            }
        }
        return courseClo;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// all SLO List
exports.allSLOList = async ({
    courseSessionOrder,
    userSurveyData,
    otherSurveyData,
    studentIds,
}) => {
    try {
        const ratingReport = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            // Class Survey Rating
            const otherSessionSurvey = otherSurveyData.filter(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            for (sessionSloElement of sessionOrderElement.slo) {
                const ratingObjects = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                    /*     userRating: { sloRating: userRating }, */
                    othersRating: {
                        /*                         sloRating: 0,
                        attendedCount: 0, */
                        noOfStudent: studentIds.length,
                    },
                };

                ratingReport.push({
                    ...objectData,
                    ...ratingObjects,
                });
            }
        }
        return ratingReport;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};
exports.mapSLORatingsWithCLO = async (courseClo, sloRatingsData) => {
    try {
        const clos = [];
        courseClo.forEach((eleCourseCLO) => {
            eleCourseCLO.clos.forEach((eleClos) => {
                let slos = [];
                eleClos.slos.forEach((eleSlos) => {
                    const sloRating = sloRatingsData.ratingReport.filter(
                        (ele) => ele._slo_id.toString() === eleSlos._id.toString(),
                    );
                    if (sloRating) {
                        //slos.push(sloRating);
                        slos = [...slos, ...sloRating];
                    }
                });
                clos.push({
                    _id: eleClos._id,
                    no: eleClos.no,
                    name: eleClos.name,
                    slos,
                });
            });
        });
        return clos;
    } catch (error) {
        throw new Error(error);
    }
};

exports.calculateMarks = async (cloData) => {
    try {
        cloData.forEach((eleClo) => {
            let mark = 0;
            let flag = 0;
            let ratingCount = 0;
            eleClo.slos.forEach((eleSlos) => {
                if (eleSlos.userRating != null) {
                    mark += eleSlos.userRating;
                    ratingCount++;
                    flag = 1;
                }
            });
            if (flag === 0) mark = null;
            eleClo.mark = mark;
            eleClo.average = mark / ratingCount;
            eleClo.sloCount = ratingCount;
        });
    } catch (error) {
        throw new Error(error);
    }
};
