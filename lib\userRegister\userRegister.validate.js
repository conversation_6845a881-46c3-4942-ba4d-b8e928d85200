const Joi = require('joi');

exports.verifyEmailValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            email: Joi.string()
                .required()
                .error(() => {
                    return 'EmailID_REQUIRED';
                }),
            forgotPassword: Joi.boolean().error(() => {
                return 'FORGOT_PASSWORD_REQUIRED';
            }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.verifyUserOTPValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'user_id_REQUIRED';
                }),
            otp: Joi.string()
                .required()
                .error(() => {
                    return 'OTP_Required';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.sendUserDataValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'user_id_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.updateUserDetailsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            face: Joi.string().error(() => {
                return 'Face_REQUIRED';
            }),
            document: Joi.string().error(() => {
                return 'Document_REQUIRED';
            }),
            term: Joi.string()
                .allow('')
                .error(() => {
                    return 'batch_REQUIRED';
                }),
            name: Joi.object({
                first: Joi.string()
                    .required()
                    .error(() => {
                        return 'first_REQUIRED';
                    }),
                last: Joi.string()
                    .required()
                    .error(() => {
                        return 'last_REQUIRED';
                    }),
                middle: Joi.string().allow('', null),
            })
                .required()
                .error(() => {
                    return 'name_REQUIRED';
                }),
            program: Joi.object({
                program_no: Joi.string()
                    .allow('')
                    .error(() => {
                        return 'program_no_REQUIRED';
                    }),
                _program_id: Joi.string()
                    .allow('')
                    .error(() => {
                        return '_program_id_REQUIRED';
                    }),
            }).error(() => {
                return 'name_REQUIRED';
            }),
            gender: Joi.string()
                .valid('male', 'female', 'other')
                .required()
                .error(() => {
                    return 'gender_REQUIRED';
                }),
            mobile: Joi.string()
                .required()
                .error(() => {
                    return 'mobile_REQUIRED';
                }),
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'userId_REQUIRED';
                }),
            user_id: Joi.string()
                .required()
                .error(() => {
                    return 'user_id_REQUIRED';
                }),
            password: Joi.string().required(),
            approvalStatus: Joi.string().valid('pending', 'approved', 'rejected').required(),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.checkUserPasswordValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'EmailID_REQUIRED';
                }),
            userPassword: Joi.string()
                .required()
                .error(() => {
                    return 'userPassword_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.dataForApprovalValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            limit: Joi.number()
                .required()
                .error(() => {
                    return 'limit_REQUIRED';
                }),
            pageNo: Joi.number()
                .required()
                .error(() => {
                    return 'pageNo_REQUIRED';
                }),
            userSearchKey: Joi.string()
                .allow('')
                .error(() => {
                    return 'userSearchKey';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.dataApprovalResultValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            Reason: Joi.string().error(() => {
                return 'Reason_REQUIRED';
            }),
            approvalStatus: Joi.string()
                .required()
                .error(() => {
                    return 'approvalStatus_REQUIRED';
                }),
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'Email_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.signedURLForBiometricDataValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            fileUrl: Joi.string()
                .required()
                .error(() => {
                    return 'fileUrl_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
    next();
};

exports.getDocumentValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return 'USERID_REQUIRED';
                }),
            passKey: Joi.string()
                .required()
                .error(() => {
                    return 'Password_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
    next();
};

exports.deleteUserValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            userId: Joi.string()
                .length(24)
                .required()
                .error(() => {
                    return 'userId_REQUIRED';
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
    next();
};

exports.updateUserDetailInApprovalFlowValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({
            term: Joi.string().error(() => {
                return 'batch_REQUIRED';
            }),
            name: Joi.object({
                first: Joi.string().error(() => {
                    return 'first_REQUIRED';
                }),
                last: Joi.string().error(() => {
                    return 'last_REQUIRED';
                }),
                middle: Joi.string().allow('', null),
            }).error(() => {
                return 'name_REQUIRED';
            }),
            gender: Joi.string()
                .valid('male', 'female', 'other')
                .error(() => {
                    return 'gender_REQUIRED';
                }),
            mobile: Joi.number().error(() => {
                return 'mobile_REQUIRED';
            }),
            userId: Joi.string().error(() => {
                return 'userId_REQUIRED';
            }),
            user_id: Joi.string().error(() => {
                return 'user_id_REQUIRED';
            }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};

exports.programNameValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }).unknown(true),
        body: Joi.object({}).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((d) => d.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
