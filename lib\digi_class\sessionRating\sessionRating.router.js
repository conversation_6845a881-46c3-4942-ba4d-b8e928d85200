const route = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    createSessionRating,
    studentScheduleRatingData,
    updateSessionRating,
    getSessionRatingsData,
    calculateSessionRatings,
    getSessionFeedbacks,
    calculateMultipleSchedulesRating,
} = require('./sessionRating.controller');
const {
    createRatingValidator,
    updateRatingValidator,
    getRatingValidator,
    getRatingsCalculationValidator,
    studentScheduleRatingValidator,
    multipleSchedulesValidator,
} = require('./sessionRating.validator');
const { validate } = require('../../../middleware/validation');

route.post(
    '/',
    validate([{ schema: createRatingValidator, property: 'body' }]),
    catchAsync(createSessionRating),
);
route.get(
    '/studentScheduleRating',
    validate([{ schema: studentScheduleRatingValidator, property: 'query' }]),
    catchAsync(studentScheduleRatingData),
);
route.put(
    '/',
    validate([{ schema: updateRatingValidator, property: 'body' }]),
    catchAsync(updateSessionRating),
);
route.get(
    '/',
    validate([{ schema: getRatingValidator, property: 'query' }]),
    catchAsync(getSessionRatingsData),
);
route.get(
    '/summary',
    validate([{ schema: getRatingsCalculationValidator, property: 'query' }]),
    catchAsync(calculateSessionRatings),
);
route.get(
    '/feedBack',
    validate([{ schema: getRatingsCalculationValidator, property: 'query' }]),
    catchAsync(getSessionFeedbacks),
);
route.post(
    '/schedulesSummary',
    validate([{ schema: multipleSchedulesValidator, property: 'body' }]),
    catchAsync(calculateMultipleSchedulesRating),
);

module.exports = route;
