const route = require('express').Router();
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const catchAsync = require('../utility/catch-async');
const {
    getCourseSessionStatusDetail,
    updateCourseSessionStatusDetail,
    getTotalScheduleCount,
} = require('./session_status_management_controller');
const {
    getCourseSessionStatusValidator,
    updateCourseSessionStatusValidator,
} = require('./session_status_management_validator');
route.get(
    '/getCourseSessionStatusDetail',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCourseSessionStatusValidator,
    catchAsync(getCourseSessionStatusDetail),
);
route.put(
    '/updateCourseSessionStatusDetail/:sessionStatusId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateCourseSessionStatusValidator,
    catchAsync(updateCourseSessionStatusDetail),
);
// Student grouping
route.get('/student-grouping/get-total-scheduleCount', catchAsync(getTotalScheduleCount));
module.exports = route;
