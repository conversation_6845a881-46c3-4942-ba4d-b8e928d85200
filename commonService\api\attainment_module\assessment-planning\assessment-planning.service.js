const { convertToMongoObjectId, query } = require('../../../utility/common');
const {
    LOCAL,
    PROGRAM_CALENDAR,
    ROLE_ASSIGNS,
    DIGI_COURSE,
    COURSE_SCHEDULE,
    COURSE_COORDINATOR,
} = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const {
    courseFormatting,
    yearLevelFormatting,
    userCourseIdFormatting,
} = require('../../serviceAdapter/adapter.formatter');
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGNS);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);

const getProgramYearLevelList = async ({ _institution_id, programId, institutionCalendarId }) => {
    try {
        let yearLevelList;
        if (BASIC_DATA_FROM === LOCAL) {
            yearLevelList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        'level.year': 1,
                        'level.level_no': 1,
                        'level.rotation': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!yearLevelList) return [];
        return yearLevelFormatting({ yearLevelList, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseList = async ({ _institution_id, programId, institutionCalendarId }) => {
    try {
        let courseList;
        if (BASIC_DATA_FROM === LOCAL) {
            courseList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        'level.year': 1,
                        'level.level_no': 1,
                        'level.rotation': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        'level.course.courses_number': 1,
                        'level.course.credit_hours': 1,
                        'level.rotation_course.rotation_count': 1,
                        'level.rotation_course.course._course_id': 1,
                        'level.rotation_course.course.courses_name': 1,
                        'level.rotation_course.course.courses_number': 1,
                        'level.rotation_course.course.credit_hours': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!courseList) return [];
        return courseFormatting({ courseList, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserCourseBasedProgramList = async ({
    _institution_id,
    user_id,
    role_id,
    institutionCalendarId,
    programId,
    term,
}) => {
    try {
        let roleAssignData;
        let courseCoordinatorData;
        let courseScheduleData;
        let isCourseAdmin = false;
        if (BASIC_DATA_FROM === LOCAL) {
            roleAssignData = await roleAssignSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _user_id: convertToMongoObjectId(user_id),
                    },
                    {
                        'roles._id': 1,
                        'roles._role_id': 1,
                        'roles.role_name': 1,
                        'roles.isAdmin': 1,
                        'roles.program._program_id': 1,
                    },
                )
                .lean();
            if (roleAssignData && roleAssignData.roles) {
                const userRoleData = roleAssignData.roles.find(
                    (roleElement) =>
                        roleElement._role_id.toString() === role_id.toString() &&
                        (roleElement.role_name === COURSE_COORDINATOR
                            ? true
                            : roleElement.program.find(
                                  (programElement) =>
                                      programElement._program_id.toString() ===
                                      programId.toString(),
                              )),
                );
                if (userRoleData) {
                    if (userRoleData.isAdmin)
                        return {
                            isAdmin: true,
                            courseIds: [],
                        };
                    isCourseAdmin = userRoleData && userRoleData.role_name === COURSE_COORDINATOR;
                }
            }
            if (isCourseAdmin) {
                courseCoordinatorData = await courseSchema
                    .find(
                        {
                            ...query,
                            _institution_id: convertToMongoObjectId(_institution_id),
                            // _program_id: convertToMongoObjectId(programId),
                            'coordinators._institution_calendar_id':
                                convertToMongoObjectId(institutionCalendarId),
                            'coordinators._user_id': convertToMongoObjectId(user_id),
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
            }
            if (!isCourseAdmin)
                courseScheduleData = await courseScheduleSchema
                    .distinct('_course_id', {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        type: 'regular',
                        term,
                        'staffs._staff_id': convertToMongoObjectId(user_id),
                    })
                    .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        return {
            isAdmin: false,
            courseIds: userCourseIdFormatting({
                courseCoordinatorData,
                courseScheduleData,
                dataFrom: BASIC_DATA_FROM,
            }),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getCourseList,
    getProgramYearLevelList,
    getUserCourseBasedProgramList,
};
