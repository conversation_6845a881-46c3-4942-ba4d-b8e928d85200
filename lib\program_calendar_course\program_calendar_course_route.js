const express = require('express');
const route = express.Router();
const program = require('./program_calendar_course_controller');
const validator = require('./program_calendar_course_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');
const { programCalendarCodeAddingService } = require('./programCalendar.helper');
const catchAsync = require('../utility/catch-async');

route.get(
    '/rotation_course_list/:id/:batch/:level_no' /* , validator.course_id_level_type */,
    program.rotation_course_list_get,
);
route.get(
    '/course_list/:id/:batch/:level' /* , validator.course_id_level_type */,
    [
        userPolicyAuthentication([
            'program_calendar:dashboard:add_course',
            'program_calendar:dashboard:course:view',
        ]),
    ],
    program.get_course_list,
);
route.get(
    '/rotation_course/:id/:batch/:level/:rotation' /* , validator.course_id_level_type */,
    program.rotation_course,
);
route.get(
    '/rotation/:calendar/:rotation_no/:course',
    validator.rotation_course_get,
    program.rotation_course_get,
);
route.get(
    '/year_course/:calendar/:year_no',
    validator.course_list_year_get,
    program.course_list_year_get,
);
route.get(
    '/level_course/:calendar/:level_no',
    validator.course_list_level_get,
    program.course_list_level_get,
);
route.get('/:calendar/:term/:course', validator.course_get, program.course_get);
route.post('/', validator.program, program.insert);
// route.post('/rotation_course_add', validator.rotation_course_add, program.rotation_course_add);
route.post('/rotation_course_add', validator.rotation_course_add, program.rotation_course_adding);
route.post(
    '/rotation_course_add_manual',
    validator.rotation_course_add,
    program.rotation_course_add_manual,
);
route.post('/rotation_course_move', validator.rotation_course_move, program.rotation_course_swap);
route.put('/', validator.program_update, program.update);
route.put('/rotation', validator.program_update, program.update_rotation_course);
route.put(
    '/rotation_manual_course' /* , validator.update_rotation_course_manual */,
    program.update_rotation_course_manual,
);
route.delete('/', validator.program_delete, program.delete);
route.delete(
    '/rotation_manual_course_delete' /* , validator.rotation_manual_course_delete */,
    program.rotation_manual_course_delete,
);
route.delete('/event', validator.program_delete_event, program.delete_event);

// Program Calendar Term, Year, Level Code Generating Service
route.post('/programCalendarCodeGenerate', catchAsync(programCalendarCodeAddingService));
module.exports = route;
