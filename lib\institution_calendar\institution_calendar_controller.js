const constant = require('../utility/constants');
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const { deleteLmsSettingCalendar } = require('../lmsStudentSetting/lmsStudentSetting.controller');
/* TODO part-3 Add more filter in enrolled student and staff
const CourseSchedules = require('../models/course_schedule'); */
const institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const institution_formate = require('./institution_calendar_formate');
const ObjectId = common_files.convertToMongoObjectId;

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.INSTITUTION,
                localField: '_institution_id',
                foreignField: '_id',
                as: 'institution',
            },
        },
        { $unwind: '$institution' },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_primary_calendar_id',
                foreignField: '_id',
                as: 'primary_calendar',
            },
        },
        { $unwind: { path: '$primary_calendar', preserveNullAndEmptyArrays: true } },
        { $sort: { _id: -1 } },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(institution_calendar, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.listAllResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            /* doc.data */ institution_formate.institution(doc.data),
        );
        // common_files.list_all_response(res, 200, true, "institution_calendar list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* institution_formate.institution_calendar(doc.data) */);
    } else {
        common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.INSTITUTION,
                localField: '_institution_id',
                foreignField: '_id',
                as: 'institution',
            },
        },
        { $unwind: '$institution' },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_primary_calendar_id',
                foreignField: '_id',
                as: 'primary_calendar',
            },
        },
        { $unwind: { path: '$primary_calendar', preserveNullAndEmptyArrays: true } },
        { $sort: { updatedAt: -1 } },
    ];
    const doc = await base_control.get_aggregate(institution_calendar, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "institution_calendar details", /* doc.data */institution_formate.institution_ID(doc.data[0]));
        common_files.com_response(
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_DETAILS'),
            doc.data /* institution_formate.institution_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.insert = async (req, res) => {
    let objs = {};
    const checks = await base_control.check_id(institution, {
        _id: { $in: req.body._institution_id },
        isDeleted: false,
    });
    if (checks.status) {
        if (req.body.calendar_type == constant.PRIMARY) {
            objs = {
                calendar_name: req.body.calendar_name,
                calendar_type: req.body.calendar_type,
                primary_calendar: req.body.primary_calendar,
                batch: req.body.batch,
                start_date: req.body.start_date,
                end_date: req.body.end_date,
                _creater_id: req.body._creater_id,
                _institution_id: req.body._institution_id,
            };
        } else {
            objs = {
                calendar_name: req.body.calendar_name,
                _primary_calendar_id: req.body._primary_calendar_id,
                calendar_type: req.body.calendar_type,
                primary_calendar: req.body.primary_calendar,
                batch: req.body.batch,
                start_date: req.body.start_date,
                end_date: req.body.end_date,
                _creater_id: req.body._creater_id,
                _institution_id: req.body._institution_id,
            };
        }
        const doc = await base_control.insert(institution_calendar, objs);
        if (doc.status) {
            // console.log(req.body.event);
            // console.log(doc.responses);
            common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('INSTITUTION_CALENDAR_ADDED_SUCCESSFULLY'),
                doc.responses,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSHING_REFERENCE_ID'),
        );
    }
};

exports.update = async (req, res) => {
    let checks = { status: true };
    if (req.body._institution_id != undefined) {
        checks = await base_control.check_id(institution, {
            _id: { $in: req.body._institution_id },
            isDeleted: false,
        });
    }
    if (checks.status) {
        const object_id = req.params.id;
        const doc = await base_control.update(institution_calendar, object_id, req.body);
        if (doc.status) {
            const institutionCalenderDoc = await institution_calendar.findOne(
                { _id: object_id },
                { end_date: 1, isActive: 1 },
            );
            if (
                (institutionCalenderDoc.isActive && new Date(req.body.end_date) >= new Date()) ||
                (req.body.isActive && new Date(institutionCalenderDoc.end_date) >= new Date())
            ) {
                await deleteLmsSettingCalendar({ id: req.params.id });
            }
            common_files.comResponseWithRequest(
                req,
                res,
                201,
                true,
                req.t('INSTITUTION_CALENDAR_UPDATE_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.comResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSHING_REFERENCE_ID'),
        );
    }
};

exports.delete = async (req, res) => {
    const object_id = req.params.id;
    const doc = await base_control.delete(institution_calendar, object_id);
    if (doc.status) {
        await deleteLmsSettingCalendar({ id: object_id });
        common_files.comResponseWithRequest(
            req,
            res,
            201,
            true,
            req.t('INSTITUTION_CALENDAR_DELETED_SUCCESSFULLY'),
            doc.data,
        );
    } else {
        common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }

        const doc = await base_control.get_list(institution_calendar, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('INSTITUTION_CALENDAR_LIST'),
                institution_formate.institution_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
        );
    }
};

exports.list_mon_two_date = async (req, res) => {
    // let id = ObjectId('5ea93af58d49c51140fcf63e');
    // let aggre = [
    //     { $match: { '_id': ObjectId(id) } },
    //     { $match: { 'isDeleted': false } }
    // ];
    // let doc = await base_control.get_aggregate(institution_calendar, aggre);
    // if (doc.status) {
    //     var dateFuture = new Date(doc.data[0].end_date);
    //     var dateNow = new Date();

    //     var seconds = Math.floor((dateFuture - (dateNow)) / 1000);
    //     var minutes = Math.floor(seconds / 60);
    //     var hours = Math.floor(minutes / 60);
    //     var days = Math.floor(hours / 24);

    //     hours = hours - (days * 24);
    //     minutes = minutes - (days * 24 * 60) - (hours * 60);
    //     seconds = seconds - (days * 24 * 60 * 60) - (hours * 60 * 60) - (minutes * 60);

    //     console.log(dateFuture);
    //     console.log('Time Left : ' ,days,'d', hours,'h', minutes,'m', seconds,'s');

    //     // common_files.com_response(res, 200, true, "institution_calendar details", /* doc.data */institution_formate.institution_ID(doc.data[0]));
    //     common_files.com_response(res, 200, true, "institution_calendar details", doc.data/* institution_formate.institution_ID(doc.data[0]) */);
    // } else {
    //     common_files.com_response(res, 500, false, "Error", doc.data);
    // }

    // let aggre = [];
    // let id = req.params.id;
    const dates = [];
    let start_date;
    let end_date;
    if (req.query.start_date != undefined && req.query.end_date != undefined) {
        start_date = req.query.start_date;
        end_date = req.query.end_date;
        const start = start_date.split('-');
        const end = end_date.split('-');
        const startYear = parseInt(start[0]);
        const endYear = parseInt(end[0]);
        // let dates = [];

        for (let i = startYear; i <= endYear; i++) {
            const endMonth = i != endYear ? 11 : parseInt(end[1]) - 1;
            const startMon = i === startYear ? parseInt(start[1]) - 1 : 0;
            for (let j = startMon; j <= endMonth; j = j > 12 ? j % 12 || 11 : j + 1) {
                const month = j + 1;
                const displayMonth = month < 10 ? '0' + month : month;
                dates.push([i, displayMonth, '01'].join('-'));
            }
        }
        // console.log(dates);
    }
    common_files.com_response(
        res,
        200,
        true,
        req.t('MONTH_IN_BETWEEN_TWO_DATES'),
        dates /* institution_formate.institution_calendar_event(doc.data) */,
    );
};

exports.institutionCalenders = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { userId } = req.params;
        if (!_institution_id) {
            common_files.comResponseWithRequest(req, res, 500, false, req.t('ERROR_ID_NOT_MATCH'));
        }
        const institutionCalenders = await institution_calendar
            .find(
                {
                    _institution_id: common_files.convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    status: constant.PUBLISHED,
                },
                { calendar_name: 1, start_date: 1, end_date: 1, isActive: true },
            )
            .sort({ _id: -1 })
            .lean();
        /* TODO part-1 Add more filter in enrolled student and staff
        const courseSchedules = await CourseSchedules.find(
            {
                isDeleted: false,
                _institution_id: common_files.convertToMongoObjectId(_institution_id),
                $or: [
                    {
                        'staffs._staff_id': common_files.convertToMongoObjectId(userId),
                    },
                    { 'students._id': common_files.convertToMongoObjectId(userId) },
                ],
            },
            { _institution_calendar_id: 1 },
        ).lean();
        */
        if (institutionCalenders && institutionCalenders.length) {
            /* TODO part-2 Add more filter in enrolled student and staff
             const institutionCalenderIds = [
                ...new Set(
                    courseSchedules.map((courseSchedule) =>
                        courseSchedule._institution_calendar_id.toString(),
                    ),
                ),
            ];
            const institutionCalendersMapped = institutionCalenders.filter((institutionCalender) =>
                institutionCalenderIds.includes(institutionCalender._id.toString()),
            );
            */
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('INSTITUTION_CALENDAR_LIST'),
                institutionCalenders,
            );
        } else {
            common_files.comResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ERROR_ID_NOT_MATCH'),
                institutionCalenders,
            );
        }
    } catch (error) {
        common_files.comResponseWithRequest(req, res, 500, true, req.t('ERROR'), error);
    }
};

exports.listAll = async (req, res) => {
    try {
        const calendarList = await institution_calendar.find(
            { isDeleted: false },
            {
                _id: 1,
                calendar_name: 1,
                calendar_type: 1,
                batch: 1,
                start_date: 1,
                end_date: 1,
                status: 1,
                isActive: 1,
            },
        );
        common_files.sendResponseWithRequest(req, res, 200, true, 'CALENDAR LIST', calendarList);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
