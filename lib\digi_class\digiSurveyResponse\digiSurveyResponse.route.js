const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    saveSurveyResponse,
    getSurveyQuestion,
    checkIsSurveyClosed,
} = require('./digiSurveyResponse.controller');
const {
    getSurveyQuestionValidator,
    saveSurveyResponseValidator,
    checkIsSurveyClosedValidator,
} = require('./digiSurveyResponse.validator');

router.put('/saveSurveyResponse', saveSurveyResponseValidator, catchAsync(saveSurveyResponse));
router.get('/getSurveyQuestion', getSurveyQuestionValidator, catchAsync(getSurveyQuestion));
router.get('/checkIsSurveyClosed', checkIsSurveyClosedValidator, catchAsync(checkIsSurveyClosed));

module.exports = router;
