const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const getProgramListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
    }),
}).unknown(true);

const getProgramCourseListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    getProgramListSchema,
    getProgramCourseListSchema,
};
