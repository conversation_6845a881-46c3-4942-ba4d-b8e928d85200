const {
    setRateLimitHeaders,
    createRateLimiter,
    consumeRateLimiter,
    handleTooManyRequests,
    getKeyForRateLimit,
    doRateLimit,
    getClientIpAddress,
} = require('../helper/rateLimit.helper');

const {
    publicRoutes: {
        LOGIN,
        FORGOT_PASSWORD,
        SIGNUP,
        DEFAULT_ROUTES,
        FORGOT_PASSWORD_SEND_OTP,
        SELF_SIGNUP,
    },
} = require('../service/endpoint.util');

const { getRateLimiterConfig } = require('../helper/rateLimit.helper');

const rateLimitAndValidate = async (req, res, next) => {
    try {
        const { url } = req;

        const ipAddress = getClientIpAddress(req);

        const isRateLimitEnabled = await doRateLimit(req);
        if (!isRateLimitEnabled) {
            next();
        } else {
            // get points and duration for specific routes
            const rateLimiterConfig = [
                LOGIN,
                FORGOT_PASSWORD,
                SIGNUP,
                FORGOT_PASSWORD_SEND_OTP,
                SELF_SIGNUP,
            ].includes(url)
                ? getRateLimiterConfig(url)
                : getRateLimiterConfig(DEFAULT_ROUTES);
            // create instance for rateLimiter
            const rateLimiter = createRateLimiter(rateLimiterConfig);

            try {
                if (
                    [
                        LOGIN,
                        FORGOT_PASSWORD,
                        SIGNUP,
                        FORGOT_PASSWORD_SEND_OTP,
                        SELF_SIGNUP,
                    ].includes(url)
                ) {
                    const emailRateLimitKey = getKeyForRateLimit(
                        ipAddress,
                        [
                            LOGIN,
                            FORGOT_PASSWORD,
                            SIGNUP,
                            FORGOT_PASSWORD_SEND_OTP,
                            SELF_SIGNUP,
                        ].includes(url)
                            ? req.body?.email
                            : req.body?.mobile,
                        url,
                    );
                    const emailRateLimiterResponse = await consumeRateLimiter(
                        rateLimiter,
                        emailRateLimitKey,
                    );
                    setRateLimitHeaders(res, 'Email', rateLimiterConfig, emailRateLimiterResponse);
                } else {
                    const userId = req?.tokenPayload?._id;
                    if (userId) {
                        const userRateLimitKey = getKeyForRateLimit(ipAddress, userId);
                        const emailRateLimiterResponse = await consumeRateLimiter(
                            rateLimiter,
                            userRateLimitKey,
                        );
                        setRateLimitHeaders(
                            res,
                            'UserId',
                            rateLimiterConfig,
                            emailRateLimiterResponse,
                        );
                    }
                }
                next();
            } catch (error) {
                handleTooManyRequests(res, url);
            }
        }
    } catch (error) {
        console.log('Rate Limiting error', error);
        next();
    }
};

exports.validateRateLimit = rateLimitAndValidate;
