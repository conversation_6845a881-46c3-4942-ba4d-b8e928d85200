const departments = require('../department/department_formate');
const department_division = require('../department_division/department_division_formate');
const constant = require('../utility/constants');

module.exports = {
    department_subject: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                master: element.master,
                title: element.title,
                shared: element.shared,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            if (element.master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
                obj.department = departments.department_ID_Only(element.department);
            } else {
                obj.division = department_division.department_division_ID_Only(element.division);
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    department_subject_ID: (doc) => {
        let obj = {
            _id: doc._id,
            master: doc.master,
            title: doc.title,
            shared: doc.shared,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        if (doc.master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
            obj.department = departments.department_ID_Only(doc.department);
        } else {
            obj.division = department_division.department_division_ID_Only(doc.division);
        }
        return obj;
    },

    department_subject_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            master: doc.master,
            title: doc.title,
            master: doc._master_id,
            shared: doc.shared,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    department_subject_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                master: element.master,
                title: element.title,
                master: element._master_id,
                shared: element.shared,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },
}