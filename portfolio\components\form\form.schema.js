const { Schema } = require('mongoose');

const pagesSchema = [
    {
        name: { type: String, trim: true },
        elements: [
            {
                title: { type: String, trim: true },
                type: { type: String, trim: true },
                name: { type: String, trim: true },
                elements: [
                    {
                        choices: [],
                        type: { type: String, trim: true },
                        name: { type: String, trim: true },
                        title: { type: String, trim: true },
                        attachment: {
                            key: { type: String, trim: true },
                            bucket: { type: String, trim: true },
                            type: { type: String, trim: true },
                        },
                        imageFit: { type: String, trim: true },
                        sourceType: { type: String, trim: true },
                        readOnly: { type: Boolean },
                        storeDataAsText: { type: Boolean },
                        showPreview: { type: Boolean },
                        response: { type: String },
                        value: { type: String },
                        fill: { type: Boolean },
                        html: { type: String },
                    },
                ],
            },
        ],
    },
];

module.exports = { pagesSchema };
