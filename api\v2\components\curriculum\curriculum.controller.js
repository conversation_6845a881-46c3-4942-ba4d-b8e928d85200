const curriculumSchema = require('./curriculum.model');
const programSchema = require('../program-input/program-input.model');
const settingSchema = require('../setting/setting.model');
const sessionTypesSchema = require('../session-delivery-types/session-delivery-types.model');
const { COURSE_TYPE } = require('../../utility/constants');
const constant = require('../../utility/constants');
const courseSchema = require('../course/course.model');
const { convertToMongoObjectId, getModel } = require('../../utility/common');
const { CURRICULUM } = require('../../utility/constants');

const addCurriculum = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const {
            _institution_id,
            _program_id,
            curriculumName,
            hourAs,
            valueAs,
            credit,
            programStartAt,
            programEndAt,
            yearLevel,
            isPhaseLevel,
            withoutLevel,
        } = body;
        //check duplicate
        const curriculumData = await curriculumModel
            .find({
                _program_id: convertToMongoObjectId(_program_id),
                isDeleted: false,
            })
            .lean()
            .select('curriculumName');
        let isDuplicateCurriculumName = false;
        if (curriculumData.length) {
            curriculumData.forEach((curriculum) => {
                if (curriculum.curriculumName.toLowerCase() === curriculumName.toLowerCase()) {
                    isDuplicateCurriculumName = true;
                }
            });
        }
        if (isDuplicateCurriculumName) {
            return { statusCode: 404, message: 'DUPLICATE_CURRICULUM_NAME' };
        }
        const creditArr = [];
        const creditZerothArray = credit[0];
        if (hourAs === 'total' && valueAs === 'fixed_value') {
            if (creditZerothArray.sessionType === 'total' && creditZerothArray.max > 0)
                creditArr.push({
                    sessionType: creditZerothArray.sessionType,
                    min: 0,
                    max: creditZerothArray.max,
                });
            else return { statusCode: 404, message: 'CHECK_CREDIT_HOURS_DATA' };
        } else if (hourAs === 'total' && valueAs === 'range_values') {
            if (
                creditZerothArray.sessionType === 'total' &&
                creditZerothArray.min > 0 &&
                creditZerothArray.max > 0
            )
                creditArr.push({
                    sessionType: creditZerothArray.sessionType,
                    min: creditZerothArray.min,
                    max: creditZerothArray.max,
                });
            else return { statusCode: 404, message: 'CHECK_CREDIT_HOURS_DATA' };
        } else if (hourAs === 'split_up' && valueAs === 'fixed_value') {
            credit.forEach((element) => {
                creditArr.push({ sessionType: element.sessionType, min: 0, max: element.max });
            });
        } else if (hourAs === 'split_up' && valueAs === 'range_values') {
            credit.forEach((element) => {
                creditArr.push({
                    sessionType: element.sessionType,
                    min: element.min,
                    max: element.max,
                });
            });
        }
        const obj = {
            _institution_id,
            _program_id,
            curriculumName,
            creditHours: {
                hourAs,
                valueAs,
                credit: creditArr,
            },
            programDuration: {
                startAt: programStartAt,
                endAt: programEndAt,
            },
            yearLevel,
            isPhaseLevel,
            withoutLevel,
        };
        const curriculumDoc = await curriculumModel.create(obj);
        if (!curriculumDoc) return { statusCode: 404, message: 'UNABLE_TO_ADD_CURRICULUM' };
        return {
            statusCode: 201,
            message: 'CURRICULUM_ADDED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCurriculum = async ({ params = {}, headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, constant.COURSE, courseSchema);
        const { id } = params;
        const {
            _program_id,
            curriculumName,
            hourAs,
            valueAs,
            credit,
            programStartAt,
            programEndAt,
            yearLevel,
            isPhaseLevel,
            withoutLevel,
            phaseLevel,
        } = body;

        //check duplicate
        const curriculumCheck = await curriculumModel
            .find({
                _id: { $ne: convertToMongoObjectId(id) },
                _program_id: convertToMongoObjectId(_program_id),
                isDeleted: false,
            })
            .lean()
            .select('curriculumName');
        let isDuplicateCurriculumName = false;
        if (curriculumCheck.length) {
            curriculumCheck.forEach((curriculum) => {
                if (curriculum.curriculumName.toLowerCase() === curriculumName.toLowerCase()) {
                    isDuplicateCurriculumName = true;
                }
            });
        }
        if (isDuplicateCurriculumName) {
            return { statusCode: 404, message: 'DUPLICATE_CURRICULUM_NAME' };
        }
        const obj = {
            curriculumName,
            creditHours: {
                hourAs,
                valueAs,
                credit,
            },
            yearLevel,
            isPhaseLevel,
            withoutLevel,
            phaseLevel,
            programDuration: {
                startAt: programStartAt,
                endAt: programEndAt,
            },
        };
        const curriculumDoc = await curriculumModel.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            obj,
        );
        if (!curriculumDoc) return { statusCode: 404, message: 'UNABLE_TO_UPDATE_CURRICULUM' };
        const deletedLevelIds = [];
        const newLevelIds = [];
        const bulkUpdate = [];
        const bulkUpdateForCourse = [];
        if (curriculumDoc.withoutLevel === true) {
            yearLevel.forEach((yearElement) => {
                if (yearElement._id) newLevelIds.push(yearElement._id);
            });
            curriculumDoc.yearLevel.forEach((yearElement) => {
                if (yearElement && yearElement.length) {
                    if (!newLevelIds.includes(String(yearElement._id))) {
                        deletedLevelIds.push(String(yearElement._id));
                    }
                }
            });
            yearLevel.forEach((yearElement) => {
                bulkUpdate.push({
                    updateMany: {
                        filter: {
                            isDeleted: false,
                            'yearLevel._preRequisite_id': convertToMongoObjectId(id),
                        },
                        update: {
                            $set: {
                                'yearLevel.$[yearId].preRequisiteName': curriculumName,
                            },
                        },
                        arrayFilters: [
                            {
                                'yearId._id': convertToMongoObjectId(yearElement._id),
                            },
                        ],
                    },
                });
                if (yearElement && yearElement.length) {
                    bulkUpdate.push({
                        updateMany: {
                            filter: {
                                isDeleted: false,
                                'yearLevel._preRequisite_id': convertToMongoObjectId(id),
                            },
                            update: {
                                $set: {
                                    'yearLevel.$[yearId].yType': yearElement.yType,
                                    'yearLevel.$[yearId].startWeek': yearElement.startWeek,
                                    'yearLevel.$[yearId].endWeek': yearElement.endWeek,
                                },
                            },
                            arrayFilters: [
                                { 'yearId._preRequisite_id': convertToMongoObjectId(id) },
                            ],
                        },
                    });
                    bulkUpdateForCourse.push({
                        updateMany: {
                            filter: {
                                isDeleted: false,
                                _curriculum_id: convertToMongoObjectId(id),
                                $or: [
                                    {
                                        'courseRecurringYearWise._year_id': convertToMongoObjectId(
                                            yearElement._id,
                                        ),
                                    },
                                    {
                                        'courseOccurringYearWise._year_id': convertToMongoObjectId(
                                            yearElement._id,
                                        ),
                                    },
                                ],
                            },
                            update: {
                                'courseRecurringYearWise.$[levelId].year': yearElement.yType,
                                'courseOccurringYearWise.$[levelId].year': yearElement.yType,
                            },
                            arrayFilters: [
                                {
                                    'levelId._year_id': convertToMongoObjectId(levelElement._id),
                                },
                            ],
                        },
                    });
                }
            });
        } else {
            yearLevel.forEach((yearElement) => {
                yearElement.levels.forEach((levelElement) => {
                    if (levelElement._id) newLevelIds.push(levelElement._id);
                });
            });
            curriculumDoc.yearLevel.forEach((yearElement) => {
                yearElement.levels.forEach((levelElement) => {
                    if (!newLevelIds.includes(String(levelElement._id))) {
                        deletedLevelIds.push(String(levelElement._id));
                    }
                });
            });
            yearLevel.forEach((yearElement) => {
                bulkUpdate.push({
                    updateMany: {
                        filter: {
                            isDeleted: false,
                            'yearLevel._preRequisite_id': convertToMongoObjectId(id),
                        },
                        update: {
                            $set: {
                                'yearLevel.$[yearId].preRequisiteName': curriculumName,
                            },
                        },
                        arrayFilters: [
                            {
                                'yearId._id': convertToMongoObjectId(yearElement._id),
                            },
                        ],
                    },
                });
                yearElement.levels.forEach((levelElement) => {
                    if (levelElement) {
                        bulkUpdate.push({
                            updateMany: {
                                filter: {
                                    isDeleted: false,
                                    'yearLevel._preRequisite_id': convertToMongoObjectId(id),
                                },
                                update: {
                                    $set: {
                                        'yearLevel.$[yearId].levels.$[levelId].levelName':
                                            levelElement.levelName,
                                        'yearLevel.$[yearId].levels.$[levelId].startWeek':
                                            levelElement.startWeek,
                                        'yearLevel.$[yearId].levels.$[levelId].endWeek':
                                            levelElement.endWeek,
                                    },
                                },
                                arrayFilters: [
                                    { 'yearId._preRequisite_id': convertToMongoObjectId(id) },
                                    { 'levelId._id': convertToMongoObjectId(levelElement._id) },
                                ],
                            },
                        });
                        bulkUpdateForCourse.push({
                            updateMany: {
                                filter: {
                                    isDeleted: false,
                                    _curriculum_id: convertToMongoObjectId(id),
                                    $or: [
                                        {
                                            'courseRecurring._level_id': convertToMongoObjectId(
                                                levelElement._id,
                                            ),
                                        },
                                        {
                                            'courseOccurring._level_id': convertToMongoObjectId(
                                                levelElement._id,
                                            ),
                                        },
                                    ],
                                },
                                update: {
                                    'courseRecurring.$[levelId].levelNo': levelElement.levelName,
                                    'courseOccurring.$[levelId].levelNo': levelElement.levelName,
                                },
                                arrayFilters: [
                                    {
                                        'levelId._level_id': convertToMongoObjectId(
                                            levelElement._id,
                                        ),
                                    },
                                ],
                            },
                        });
                    }
                });
            });
        }
        const bulkUpdatesForRemoveOccurring = [];
        if (deletedLevelIds.length) {
            deletedLevelIds.forEach((levelId) => {
                bulkUpdatesForRemoveOccurring.push({
                    updateMany: {
                        filter: {
                            isDeleted: false,
                            _curriculum_id: convertToMongoObjectId(id),
                            $or: [
                                {
                                    'courseRecurring._level_id': convertToMongoObjectId(levelId),
                                },
                                {
                                    'courseOccurring._level_id': convertToMongoObjectId(levelId),
                                },
                            ],
                        },
                        update: {
                            $pull: {
                                courseOccurring: { _level_id: convertToMongoObjectId(levelId) },
                                courseRecurring: { _level_id: convertToMongoObjectId(levelId) },
                            },
                        },
                    },
                });
            });
        }
        if (bulkUpdate.length) await curriculumModel.bulkWrite(bulkUpdate);
        if (bulkUpdateForCourse.length) await courseModel.bulkWrite(bulkUpdateForCourse);
        if (bulkUpdatesForRemoveOccurring.length) {
            await courseModel.bulkWrite(bulkUpdatesForRemoveOccurring);
        }
        let yearIds = [];
        let levelIds = [];
        if (curriculumDoc.withoutLevel === true) {
            const years = curriculumDoc.yearLevel.filter(
                (yearElement) =>
                    !yearLevel
                        .map((yearElement2) => JSON.stringify(yearElement2._id))
                        .includes(JSON.stringify(yearElement._id)),
            );
            const levels = [];
            curriculumDoc.yearLevel.forEach((yearElement) => {
                yearLevel.forEach((inputYearElement) => {
                    if (String(yearElement._id) === String(inputYearElement._id)) {
                        if (yearElement.levels && yearElement.levels.length) {
                            levels.push(
                                yearElement.levels.filter(
                                    (levelElement) =>
                                        !inputYearElement.levels
                                            .map((levelElement2) =>
                                                JSON.stringify(levelElement2._id),
                                            )
                                            .includes(JSON.stringify(levelElement._id)),
                                ),
                            );
                        }
                    }
                });
            });
            yearIds = years.map((i) => convertToMongoObjectId(i._id));

            levelIds = [].concat(...levels).map((i) => convertToMongoObjectId(i._id));
        } else {
            const years = curriculumDoc.yearLevel.filter(
                (yearElement) =>
                    !yearLevel
                        .map((yearElement2) => JSON.stringify(yearElement2._id))
                        .includes(JSON.stringify(yearElement._id)),
            );
            const levels = [];
            curriculumDoc.yearLevel.forEach((yearElement) => {
                yearLevel.forEach((inputYearElement) => {
                    if (String(yearElement._id) === String(inputYearElement._id)) {
                        levels.push(
                            yearElement.levels.filter(
                                (levelElement) =>
                                    !inputYearElement.levels
                                        .map((levelElement2) => JSON.stringify(levelElement2._id))
                                        .includes(JSON.stringify(levelElement._id)),
                            ),
                        );
                    }
                });
            });
            yearIds = years.map((i) => convertToMongoObjectId(i._id));
            levelIds = [].concat(...levels).map((i) => convertToMongoObjectId(i._id));
        }
        if (yearIds.length !== 0) {
            const yearUpdateInCourseAssign = await courseModel.updateMany(
                {
                    isDeleted: false,
                    'courseAssignedDetails._curriculum_id': convertToMongoObjectId(id),
                    'courseAssignedDetails._year_id': { $in: yearIds },
                },
                {
                    $pull: {
                        courseAssignedDetails: {
                            _year_id: { $in: yearIds },
                        },
                    },
                },
            );
        }
        if (levelIds.length !== 0) {
            const levelUpdateInCourseAssign = await courseModel.updateMany(
                {
                    isDeleted: false,
                    'courseAssignedDetails._curriculum_id': convertToMongoObjectId(id),
                    'courseAssignedDetails._level_id': { $in: levelIds },
                },
                {
                    $pull: {
                        courseAssignedDetails: {
                            _level_id: { $in: levelIds },
                        },
                    },
                },
            );
        }
        return {
            statusCode: 201,
            message: 'CURRICULUM_UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAllCurriculum = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, constant.COURSE, courseSchema);
        const { _institution_id, _program_id } = params;
        const curriculumQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _program_id: convertToMongoObjectId(_program_id),
            isDeleted: false,
            isActive: true,
        };
        const curriculumDocs = await curriculumModel
            .find(curriculumQuery)
            .lean()
            .select(
                'curriculumName programDuration framework yearLevel isPhaseLevel withoutLevel creditHours',
            );
        if (!curriculumDocs) return { statusCode: 404, message: 'Error' };
        const curriculumIds = curriculumDocs.map((i) => convertToMongoObjectId(i._id));
        const courseDocs = await courseModel
            .find({
                $or: [
                    { _curriculum_id: { $in: curriculumIds } },
                    { 'courseAssignedDetails._curriculum_id': { $in: curriculumIds } },
                    {
                        'courseAssignedDetails.courseSharedWith._curriculum_id': {
                            $in: curriculumIds,
                        },
                    },
                ],
                isDeleted: false,
                isActive: true,
            })
            .select('courseAssignedDetails sessionDeliveryType isPhaseFlowWithOutLevel');
        const curriculumDatas = [];
        let isConfigured = true;
        let isPreRequisite = false;
        let certificateCount = 0;
        curriculumDocs.forEach((curriculumDoc, index) => {
            let levelData = [];
            const yearData = [];
            const creditHours = [];
            curriculumDoc.creditHours.credit.forEach((sessionElement) => {
                creditHours.push({
                    sessionType: sessionElement.sessionType,
                    minDuration: sessionElement.min,
                    maxDuration: sessionElement.max,
                    finishedDuration: 0,
                });
            });
            certificateCount = 0;
            if (curriculumDoc.withoutLevel === true) {
                curriculumDoc.yearLevel.forEach((yearElement) => {
                    let course_count = 0;
                    courseDocs.forEach((courseDoc) => {
                        courseDoc.courseAssignedDetails.forEach((courseAssignedDetail) => {
                            if (String(courseAssignedDetail._year_id) === String(yearElement._id)) {
                                course_count += 1;
                                courseDoc.sessionDeliveryType.forEach((session) => {
                                    if (
                                        curriculumDocs[index].creditHours.hourAs ===
                                        constant.SPLIT_UP
                                    ) {
                                        const creditHoursIndex = creditHours.findIndex(
                                            (creditHour) =>
                                                session.typeName === creditHour.sessionType,
                                        );
                                        if (creditHoursIndex !== -1)
                                            creditHours[creditHoursIndex].finishedDuration +=
                                                session.creditHours;
                                    } else if (
                                        curriculumDocs[index].creditHours.hourAs === constant.TOTAL
                                    ) {
                                        creditHours[0].finishedDuration += session.creditHours;
                                    }
                                });
                            } else if (
                                courseAssignedDetail.courseSharedWith &&
                                courseAssignedDetail.courseSharedWith.length
                            ) {
                                courseAssignedDetail.courseSharedWith.forEach((courseSharedWit) => {
                                    if (
                                        String(courseSharedWit._year_id) === String(yearElement._id)
                                    ) {
                                        course_count += 1;
                                        courseDoc.sessionDeliveryType.forEach((session) => {
                                            if (
                                                curriculumDocs[index].creditHours.hourAs ===
                                                constant.SPLIT_UP
                                            ) {
                                                const creditHoursIndex = creditHours.findIndex(
                                                    (creditHour) =>
                                                        session.typeName === creditHour.sessionType,
                                                );
                                                if (creditHoursIndex !== -1)
                                                    creditHours[
                                                        creditHoursIndex
                                                    ].finishedDuration += session.creditHours;
                                            } else if (
                                                curriculumDocs[index].creditHours.hourAs ===
                                                constant.TOTAL
                                            ) {
                                                creditHours[0].finishedDuration +=
                                                    session.creditHours;
                                            }
                                        });
                                    }
                                });
                            }
                        });
                    });
                    if (course_count === 0) {
                        isConfigured = false;
                    }
                    if (yearElement.certificate) {
                        certificateCount += 1;
                    }
                    if (yearElement._preRequisite_id) {
                        isPreRequisite = true;
                    }
                    levelData = [];
                    yearData.push({
                        _id: yearElement._id,
                        yType: yearElement.yType,
                        start_week: yearElement.startWeek,
                        end_week: yearElement.endWeek,
                        course_count,
                    });
                    if (curriculumDocs[index].creditHours.hourAs === constant.SPLIT_UP) {
                        const totalFinishedDuration = creditHours.reduce(
                            (acc, creditHours) => acc + creditHours.finishedDuration,
                            0,
                        );
                        creditHours[creditHours.length - 1].finishedDuration =
                            totalFinishedDuration;
                    }
                });
            } else {
                curriculumDoc.yearLevel.forEach((yearElement) => {
                    yearElement.levels.forEach((levelElement) => {
                        let course_count = 0;
                        courseDocs.forEach((courseDoc) => {
                            courseDoc.courseAssignedDetails.forEach((courseAssignedDetail) => {
                                if (
                                    String(courseAssignedDetail._level_id) ===
                                        String(levelElement._id) &&
                                    String(courseAssignedDetail._year_id) ===
                                        String(yearElement._id)
                                ) {
                                    course_count += 1;
                                    courseDoc.sessionDeliveryType.forEach((session) => {
                                        if (
                                            curriculumDocs[index].creditHours.hourAs ===
                                            constant.SPLIT_UP
                                        ) {
                                            const creditHoursIndex = creditHours.findIndex(
                                                (creditHour) =>
                                                    session.typeName === creditHour.sessionType,
                                            );
                                            if (creditHoursIndex !== -1)
                                                creditHours[creditHoursIndex].finishedDuration +=
                                                    session.creditHours;
                                        } else if (
                                            curriculumDocs[index].creditHours.hourAs ===
                                            constant.TOTAL
                                        ) {
                                            creditHours[0].finishedDuration += session.creditHours;
                                        }
                                    });
                                } else if (
                                    courseAssignedDetail.courseSharedWith &&
                                    courseAssignedDetail.courseSharedWith.length
                                ) {
                                    courseAssignedDetail.courseSharedWith.forEach(
                                        (courseSharedWit) => {
                                            if (
                                                String(courseSharedWit._level_id) ===
                                                    String(levelElement._id) &&
                                                String(courseSharedWit._year_id) ===
                                                    String(yearElement._id)
                                            ) {
                                                course_count += 1;
                                                courseDoc.sessionDeliveryType.forEach((session) => {
                                                    if (
                                                        curriculumDocs[index].creditHours.hourAs ===
                                                        constant.SPLIT_UP
                                                    ) {
                                                        const creditHoursIndex =
                                                            creditHours.findIndex(
                                                                (creditHour) =>
                                                                    session.typeName ===
                                                                    creditHour.sessionType,
                                                            );
                                                        if (creditHoursIndex !== -1)
                                                            creditHours[
                                                                creditHoursIndex
                                                            ].finishedDuration +=
                                                                session.creditHours;
                                                    } else if (
                                                        curriculumDocs[index].creditHours.hourAs ===
                                                        constant.TOTAL
                                                    ) {
                                                        creditHours[0].finishedDuration +=
                                                            session.creditHours;
                                                    }
                                                });
                                            }
                                        },
                                    );
                                }
                            });
                        });
                        levelData.push({
                            _id: levelElement._id,
                            level_name: levelElement.levelName,
                            start_week: levelElement.startWeek,
                            end_week: levelElement.endWeek,
                            course_count,
                        });
                        if (course_count === 0) {
                            isConfigured = false;
                        }
                    });
                    if (yearElement.certificate) {
                        certificateCount += 1;
                    }
                    if (yearElement._preRequisite_id) {
                        isPreRequisite = true;
                    }
                    const refactorCurriculumDoc = { ...yearElement };
                    refactorCurriculumDoc.levels = levelData;
                    levelData = [];
                    yearData.push(refactorCurriculumDoc);
                    if (curriculumDocs[index].creditHours.hourAs === constant.SPLIT_UP) {
                        const totalFinishedDuration = creditHours.reduce(
                            (acc, creditHours) => acc + creditHours.finishedDuration,
                            0,
                        );
                        creditHours[creditHours.length - 1].finishedDuration =
                            totalFinishedDuration;
                    }
                });
            }
            const refactorCurriculumDoc = {
                ...curriculumDoc,
                isConfigured,
                isPreRequisite,
                certificateCount,
            };
            refactorCurriculumDoc.yearLevel = yearData;
            refactorCurriculumDoc.creditHours.credit = creditHours;
            curriculumDatas.push(refactorCurriculumDoc);
        });
        return {
            statusCode: 200,
            message: 'CURRICULUM_LIST',
            data: curriculumDatas,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getParticularCurriculum = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { id } = params;
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, constant.COURSE, courseSchema);
        const curriculumDoc = await curriculumModel
            .findOne({
                _id: convertToMongoObjectId(id),
                isDeleted: false,
                isActive: true,
            })
            .select(
                'curriculumName creditHours programDuration framework yearLevel isPhaseLevel withoutLevel',
            )
            .lean();
        if (!curriculumDoc) return { statusCode: 404, message: 'Error' };
        let preRequisiteIds = [];
        if (curriculumDoc.withoutLevel) {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                if (yearElement._preRequisite_id) {
                    preRequisiteIds.push(yearElement._preRequisite_id);
                }
            });
        } else {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                if (yearElement._preRequisite_id) {
                    preRequisiteIds.push(yearElement._preRequisite_id);
                }
            });
        }
        preRequisiteIds = [...preRequisiteIds, id];
        const preRequisiteCurriculumDoc = await curriculumModel
            .find({
                _id: { $in: preRequisiteIds },
                isDeleted: false,
                isActive: true,
            })
            .select(
                'curriculumName creditHours programDuration framework yearLevel isPhaseLevel withoutLevel',
            )
            .lean();
        //TODO:We need to integrate course count foe each level and credit hour calculation
        const curriculumDatas = [];
        let levelData = [];
        const yearData = [];
        let isConfigured = true;
        let allCourse = 0;
        let standardCourse = 0;
        let selectiveCourse = 0;
        const creditHours = [];
        const courseDocs = await courseModel
            .find({
                $or: [
                    { _curriculum_id: convertToMongoObjectId(id) },
                    { 'courseAssignedDetails._curriculum_id': convertToMongoObjectId(id) },
                    {
                        'courseAssignedDetails.courseSharedWith._curriculum_id':
                            convertToMongoObjectId(id),
                    },
                ],
                isDeleted: false,
                isActive: true,
            })
            .select(
                'courseName courseType courseAssignedDetails sessionDeliveryType courseRecurring courseOccurring courseRecurringYearWise courseOccurringYearWise isActive',
            );
        const preCourseDocs = await courseModel
            .find({
                $or: [
                    { _curriculum_id: { $in: preRequisiteIds } },
                    { 'courseAssignedDetails._curriculum_id': { $in: preRequisiteIds } },
                    {
                        'courseAssignedDetails.courseSharedWith._curriculum_id': {
                            $in: preRequisiteIds,
                        },
                    },
                ],
                isDeleted: false,
                isActive: true,
            })
            .select(
                'courseName courseType courseAssignedDetails sessionDeliveryType courseRecurring courseOccurring isActive courseOccurringYearWise courseRecurringYearWise',
            );
        curriculumDoc.creditHours.credit.forEach((sessionElement) => {
            creditHours.push({
                sessionType: sessionElement.sessionType,
                minDuration: sessionElement.min,
                maxDuration: sessionElement.max,
                finishedDuration: 0,
            });
        });
        if (curriculumDoc.withoutLevel === true) {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                let preCurriculum;
                if (yearElement._preRequisite_id) {
                    preCurriculum = preRequisiteCurriculumDoc.filter(
                        (element) =>
                            element._id.toString() === yearElement._preRequisite_id.toString(),
                    );
                    preCurriculum[0].yearLevel.forEach((preYearElement) => {
                        let course_count = 0;
                        preCourseDocs.forEach((courseDoc) => {
                            let courseAssignForOccur;
                            let courseAssignFor;
                            courseDoc.courseOccurringYearWise.forEach((courseOccur) => {
                                if (String(courseOccur._year_id) === String(preYearElement._id)) {
                                    if (
                                        courseDoc.courseAssignedDetails &&
                                        courseDoc.courseAssignedDetails.length
                                    ) {
                                        courseAssignForOccur =
                                            courseDoc.courseAssignedDetails.filter((ele) => {
                                                return (
                                                    String(ele._year_id) ===
                                                    String(preYearElement._id)
                                                );
                                            });
                                        if (!courseAssignForOccur.length) {
                                            course_count += 1;
                                        }
                                        if (
                                            courseAssignForOccur.length &&
                                            courseAssignForOccur[0].isActive === true
                                        ) {
                                            course_count += 1;
                                        }
                                    } else {
                                        course_count += 1;
                                    }
                                }
                            });
                            courseDoc.courseRecurringYearWise.forEach((courseRecur) => {
                                if (String(courseRecur._year_id) === String(preYearElement._id)) {
                                    if (
                                        courseDoc.courseAssignedDetails &&
                                        courseDoc.courseAssignedDetails.length
                                    ) {
                                        courseAssignFor = courseDoc.courseAssignedDetails.filter(
                                            (ele) => {
                                                return (
                                                    String(ele._year_id) ===
                                                    String(preYearElement._id)
                                                );
                                            },
                                        );
                                        if (!courseAssignFor.length) {
                                            course_count += 1;
                                        }
                                        if (
                                            courseAssignFor.length &&
                                            courseAssignFor[0].isActive === true
                                        ) {
                                            course_count += 1;
                                        }
                                    } else {
                                        course_count += 1;
                                    }
                                }
                            });
                            courseDoc.courseAssignedDetails.forEach(
                                (courseAssignedDetail, index) => {
                                    if (
                                        courseAssignedDetail.courseSharedWith &&
                                        courseAssignedDetail.courseSharedWith.length
                                    ) {
                                        courseAssignedDetail.courseSharedWith.forEach(
                                            (courseSharedWit) => {
                                                if (
                                                    String(courseSharedWit._year_id) ===
                                                        String(preYearElement._id) &&
                                                    courseDoc.courseAssignedDetails[index]
                                                        .isActive === true
                                                ) {
                                                    course_count += 1;
                                                }
                                            },
                                        );
                                    }
                                },
                            );
                        });
                        if (course_count === 0) {
                            isConfigured = false;
                        }
                        yearData.push({
                            _id: preYearElement._id,
                            yType: preYearElement.yType,
                            start_week: preYearElement.startWeek,
                            end_week: preYearElement.endWeek,
                            course_count,
                        });
                    });
                } else {
                    let course_count = 0;
                    standardCourse = 0;
                    selectiveCourse = 0;
                    courseDocs.forEach((courseDoc) => {
                        let courseAssignForOccur;
                        let courseAssignFor;
                        courseDoc.courseOccurringYearWise.forEach((courseOccur) => {
                            if (String(courseOccur._year_id) === String(yearElement._id)) {
                                if (
                                    courseDoc.courseAssignedDetails &&
                                    courseDoc.courseAssignedDetails.length
                                ) {
                                    courseAssignForOccur = courseDoc.courseAssignedDetails.filter(
                                        (ele) => {
                                            return String(ele._year_id) === String(yearElement._id);
                                        },
                                    );
                                    if (!courseAssignForOccur.length) {
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                    if (
                                        courseAssignForOccur.length &&
                                        courseAssignForOccur[0].isActive === true
                                    ) {
                                        course_count += 1;
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                } else {
                                    course_count += 1;
                                    if (
                                        courseDoc.courseType === 'standard' &&
                                        !yearElement._preRequisite_id
                                    ) {
                                        standardCourse += 1;
                                    } else if (
                                        courseDoc.courseType === 'selective' ||
                                        (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                            !yearElement._preRequisite_id)
                                    ) {
                                        selectiveCourse += 1;
                                    }
                                }
                            }
                        });
                        courseDoc.courseRecurringYearWise.forEach((courseRecur) => {
                            if (String(courseRecur._year_id) === String(yearElement._id)) {
                                if (
                                    courseDoc.courseAssignedDetails &&
                                    courseDoc.courseAssignedDetails.length
                                ) {
                                    courseAssignFor = courseDoc.courseAssignedDetails.filter(
                                        (ele) => {
                                            return String(ele._year_id) === String(yearElement._id);
                                        },
                                    );
                                    if (!courseAssignFor.length) {
                                        course_count += 1;
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                    if (
                                        courseAssignFor.length &&
                                        courseAssignFor[0].isActive === true
                                    ) {
                                        course_count += 1;
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                } else {
                                    course_count += 1;
                                    if (
                                        courseDoc.courseType === 'standard' &&
                                        !yearElement._preRequisite_id
                                    ) {
                                        standardCourse += 1;
                                    } else if (
                                        courseDoc.courseType === 'selective' ||
                                        (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                            !yearElement._preRequisite_id)
                                    ) {
                                        selectiveCourse += 1;
                                    }
                                }
                            }
                        });
                        courseDoc.courseAssignedDetails.forEach((courseAssignedDetail, index) => {
                            if (
                                String(courseAssignedDetail._year_id) === String(yearElement._id) &&
                                courseDoc.courseAssignedDetails[index].isActive === true
                            ) {
                                if (!yearElement._preRequisite_id) {
                                    courseDoc.sessionDeliveryType.forEach((session) => {
                                        if (
                                            curriculumDoc.creditHours.hourAs === constant.SPLIT_UP
                                        ) {
                                            const creditHoursIndex = creditHours.findIndex(
                                                (creditHour) =>
                                                    session.typeName === creditHour.sessionType,
                                            );
                                            if (creditHoursIndex !== -1)
                                                creditHours[creditHoursIndex].finishedDuration +=
                                                    session.creditHours;
                                        } else if (
                                            curriculumDoc.creditHours.hourAs === constant.TOTAL
                                        ) {
                                            creditHours[0].finishedDuration += session.creditHours;
                                        }
                                    });
                                }
                            } else if (
                                courseAssignedDetail.courseSharedWith &&
                                courseAssignedDetail.courseSharedWith.length
                            ) {
                                courseAssignedDetail.courseSharedWith.forEach((courseSharedWit) => {
                                    if (
                                        String(courseSharedWit._year_id) ===
                                            String(yearElement._id) &&
                                        courseDoc.courseAssignedDetails[index].isActive === true
                                    ) {
                                        if (!yearElement._preRequisite_id) {
                                            course_count += 1;
                                            if (courseDoc.courseType === 'standard') {
                                                standardCourse += 1;
                                            } else if (
                                                courseDoc.courseType === 'selective' ||
                                                courseDoc.courseType === COURSE_TYPE.INDEPENDENT
                                            ) {
                                                selectiveCourse += 1;
                                            }
                                        }
                                        if (!yearElement._preRequisite_id) {
                                            courseDoc.sessionDeliveryType.forEach((session) => {
                                                if (
                                                    curriculumDoc.creditHours.hourAs ===
                                                    constant.SPLIT_UP
                                                ) {
                                                    const creditHoursIndex = creditHours.findIndex(
                                                        (creditHour) =>
                                                            session.typeName ===
                                                            creditHour.sessionType,
                                                    );
                                                    if (creditHoursIndex !== -1)
                                                        creditHours[
                                                            creditHoursIndex
                                                        ].finishedDuration += session.creditHours;
                                                } else if (
                                                    curriculumDoc.creditHours.hourAs ===
                                                    constant.TOTAL
                                                ) {
                                                    creditHours[0].finishedDuration +=
                                                        session.creditHours;
                                                }
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    });
                    if (!yearElement._preRequisite_id) {
                        allCourse += course_count;
                    }
                    if (course_count === 0) {
                        isConfigured = false;
                    }
                    yearData.push({
                        _id: yearElement._id,
                        yType: yearElement.yType,
                        start_week: yearElement.startWeek,
                        end_week: yearElement.endWeek,
                        course_count,
                    });
                }
            });
        } else {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                let preCurriculum;
                if (yearElement._preRequisite_id) {
                    preCurriculum = preRequisiteCurriculumDoc.filter(
                        (element) =>
                            element._id.toString() === yearElement._preRequisite_id.toString(),
                    );
                    preCurriculum[0].yearLevel.forEach((preYearElement) => {
                        preYearElement.levels.forEach((preLevelElement) => {
                            let course_count = 0;
                            preCourseDocs.forEach((courseDoc) => {
                                let courseAssignForOccur;
                                let courseAssignFor;
                                courseDoc.courseOccurring.forEach((courseOccur) => {
                                    if (
                                        String(courseOccur._level_id) ===
                                        String(preLevelElement._id)
                                    ) {
                                        if (
                                            courseDoc.courseAssignedDetails &&
                                            courseDoc.courseAssignedDetails.length
                                        ) {
                                            courseAssignForOccur =
                                                courseDoc.courseAssignedDetails.filter((ele) => {
                                                    return (
                                                        String(ele._level_id) ===
                                                            String(preLevelElement._id) &&
                                                        String(ele._year_id) ===
                                                            String(preYearElement._id)
                                                    );
                                                });
                                            if (!courseAssignForOccur.length) {
                                                course_count += 1;
                                            }
                                            if (
                                                courseAssignForOccur.length &&
                                                courseAssignForOccur[0].isActive === true
                                            ) {
                                                course_count += 1;
                                            }
                                        } else {
                                            course_count += 1;
                                        }
                                    }
                                });
                                courseDoc.courseRecurring.forEach((courseRecur) => {
                                    if (
                                        String(courseRecur._level_id) ===
                                        String(preLevelElement._id)
                                    ) {
                                        if (
                                            courseDoc.courseAssignedDetails &&
                                            courseDoc.courseAssignedDetails.length
                                        ) {
                                            courseAssignFor =
                                                courseDoc.courseAssignedDetails.filter((ele) => {
                                                    return (
                                                        String(ele._level_id) ===
                                                            String(preLevelElement._id) &&
                                                        String(ele._year_id) ===
                                                            String(preYearElement._id)
                                                    );
                                                });
                                            if (!courseAssignFor.length) {
                                                course_count += 1;
                                            }
                                            if (
                                                courseAssignFor.length &&
                                                courseAssignFor[0].isActive === true
                                            ) {
                                                course_count += 1;
                                            }
                                        } else {
                                            course_count += 1;
                                        }
                                    }
                                });
                                courseDoc.courseAssignedDetails.forEach(
                                    (courseAssignedDetail, index) => {
                                        if (
                                            courseAssignedDetail.courseSharedWith &&
                                            courseAssignedDetail.courseSharedWith.length
                                        ) {
                                            courseAssignedDetail.courseSharedWith.forEach(
                                                (courseSharedWit) => {
                                                    if (
                                                        String(courseSharedWit._level_id) ===
                                                            String(preLevelElement._id) &&
                                                        String(courseSharedWit._year_id) ===
                                                            String(preYearElement._id) &&
                                                        courseDoc.courseAssignedDetails[index]
                                                            .isActive === true
                                                    ) {
                                                        course_count += 1;
                                                    }
                                                },
                                            );
                                        }
                                    },
                                );
                            });
                            levelData.push({
                                _id: preLevelElement._id,
                                level_name: preLevelElement.levelName,
                                start_week: preLevelElement.startWeek,
                                end_week: preLevelElement.endWeek,
                                course_count,
                            });
                            if (course_count === 0) {
                                isConfigured = false;
                            }
                        });
                        const refactorCurriculumDoc = { ...preYearElement };
                        refactorCurriculumDoc.levels = levelData;
                        levelData = [];
                        yearData.push(refactorCurriculumDoc);
                    });
                } else {
                    yearElement.levels.forEach((levelElement) => {
                        let course_count = 0;
                        courseDocs.forEach((courseDoc) => {
                            let courseAssignForOccur;
                            let courseAssignFor;
                            courseDoc.courseOccurring.forEach((courseOccur) => {
                                if (String(courseOccur._level_id) === String(levelElement._id)) {
                                    if (
                                        courseDoc.courseAssignedDetails &&
                                        courseDoc.courseAssignedDetails.length
                                    ) {
                                        courseAssignForOccur =
                                            courseDoc.courseAssignedDetails.filter((ele) => {
                                                return (
                                                    String(ele._level_id) ===
                                                        String(levelElement._id) &&
                                                    String(ele._year_id) === String(yearElement._id)
                                                );
                                            });
                                        if (!courseAssignForOccur.length) {
                                            if (
                                                courseDoc.courseType === 'standard' &&
                                                !yearElement._preRequisite_id
                                            ) {
                                                standardCourse += 1;
                                            } else if (
                                                courseDoc.courseType === 'selective' ||
                                                (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                    !yearElement._preRequisite_id)
                                            ) {
                                                selectiveCourse += 1;
                                            }
                                        }
                                        if (
                                            courseAssignForOccur.length &&
                                            courseAssignForOccur[0].isActive === true
                                        ) {
                                            course_count += 1;
                                            if (
                                                courseDoc.courseType === 'standard' &&
                                                !yearElement._preRequisite_id
                                            ) {
                                                standardCourse += 1;
                                            } else if (
                                                courseDoc.courseType === 'selective' ||
                                                (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                    !yearElement._preRequisite_id)
                                            ) {
                                                selectiveCourse += 1;
                                            }
                                        }
                                    } else {
                                        course_count += 1;
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                }
                            });
                            courseDoc.courseRecurring.forEach((courseRecur) => {
                                if (String(courseRecur._level_id) === String(levelElement._id)) {
                                    if (
                                        courseDoc.courseAssignedDetails &&
                                        courseDoc.courseAssignedDetails.length
                                    ) {
                                        courseAssignFor = courseDoc.courseAssignedDetails.filter(
                                            (ele) => {
                                                return (
                                                    String(ele._level_id) ===
                                                        String(levelElement._id) &&
                                                    String(ele._year_id) === String(yearElement._id)
                                                );
                                            },
                                        );
                                        if (!courseAssignFor.length) {
                                            course_count += 1;
                                            if (
                                                courseDoc.courseType === 'standard' &&
                                                !yearElement._preRequisite_id
                                            ) {
                                                standardCourse += 1;
                                            } else if (
                                                courseDoc.courseType === 'selective' ||
                                                (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                    !yearElement._preRequisite_id)
                                            ) {
                                                selectiveCourse += 1;
                                            }
                                        }
                                        if (
                                            courseAssignFor.length &&
                                            courseAssignFor[0].isActive === true
                                        ) {
                                            course_count += 1;
                                            if (
                                                courseDoc.courseType === 'standard' &&
                                                !yearElement._preRequisite_id
                                            ) {
                                                standardCourse += 1;
                                            } else if (
                                                courseDoc.courseType === 'selective' ||
                                                (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                    !yearElement._preRequisite_id)
                                            ) {
                                                selectiveCourse += 1;
                                            }
                                        }
                                    } else {
                                        course_count += 1;
                                        if (
                                            courseDoc.courseType === 'standard' &&
                                            !yearElement._preRequisite_id
                                        ) {
                                            standardCourse += 1;
                                        } else if (
                                            courseDoc.courseType === 'selective' ||
                                            (courseDoc.courseType === COURSE_TYPE.INDEPENDENT &&
                                                !yearElement._preRequisite_id)
                                        ) {
                                            selectiveCourse += 1;
                                        }
                                    }
                                }
                            });
                            courseDoc.courseAssignedDetails.forEach(
                                (courseAssignedDetail, index) => {
                                    if (
                                        String(courseAssignedDetail._level_id) ===
                                            String(levelElement._id) &&
                                        String(courseAssignedDetail._year_id) ===
                                            String(yearElement._id) &&
                                        courseDoc.courseAssignedDetails[index].isActive === true
                                    ) {
                                        if (!yearElement._preRequisite_id) {
                                            courseDoc.sessionDeliveryType.forEach((session) => {
                                                if (
                                                    curriculumDoc.creditHours.hourAs ===
                                                    constant.SPLIT_UP
                                                ) {
                                                    const creditHoursIndex = creditHours.findIndex(
                                                        (creditHour) =>
                                                            session.typeName ===
                                                            creditHour.sessionType,
                                                    );
                                                    if (creditHoursIndex !== -1)
                                                        creditHours[
                                                            creditHoursIndex
                                                        ].finishedDuration += session.creditHours;
                                                } else if (
                                                    curriculumDoc.creditHours.hourAs ===
                                                    constant.TOTAL
                                                ) {
                                                    creditHours[0].finishedDuration +=
                                                        session.creditHours;
                                                }
                                            });
                                        }
                                    } else if (
                                        courseAssignedDetail.courseSharedWith &&
                                        courseAssignedDetail.courseSharedWith.length
                                    ) {
                                        courseAssignedDetail.courseSharedWith.forEach(
                                            (courseSharedWit) => {
                                                if (
                                                    String(courseSharedWit._level_id) ===
                                                        String(levelElement._id) &&
                                                    String(courseSharedWit._year_id) ===
                                                        String(yearElement._id) &&
                                                    courseDoc.courseAssignedDetails[index]
                                                        .isActive === true
                                                ) {
                                                    if (!yearElement._preRequisite_id) {
                                                        course_count += 1;
                                                        if (courseDoc.courseType === 'standard') {
                                                            standardCourse += 1;
                                                        } else if (
                                                            courseDoc.courseType === 'selective' ||
                                                            courseDoc.courseType ===
                                                                COURSE_TYPE.INDEPENDENT
                                                        ) {
                                                            selectiveCourse += 1;
                                                        }
                                                    }
                                                    if (!yearElement._preRequisite_id) {
                                                        courseDoc.sessionDeliveryType.forEach(
                                                            (session) => {
                                                                if (
                                                                    curriculumDoc.creditHours
                                                                        .hourAs ===
                                                                    constant.SPLIT_UP
                                                                ) {
                                                                    const creditHoursIndex =
                                                                        creditHours.findIndex(
                                                                            (creditHour) =>
                                                                                session.typeName ===
                                                                                creditHour.sessionType,
                                                                        );
                                                                    if (creditHoursIndex !== -1)
                                                                        creditHours[
                                                                            creditHoursIndex
                                                                        ].finishedDuration +=
                                                                            session.creditHours;
                                                                } else if (
                                                                    curriculumDoc.creditHours
                                                                        .hourAs === constant.TOTAL
                                                                ) {
                                                                    creditHours[0].finishedDuration +=
                                                                        session.creditHours;
                                                                }
                                                            },
                                                        );
                                                    }
                                                }
                                            },
                                        );
                                    }
                                },
                            );
                        });
                        levelData.push({
                            _id: levelElement._id,
                            level_name: levelElement.levelName,
                            start_week: levelElement.startWeek,
                            end_week: levelElement.endWeek,
                            course_count,
                        });
                        if (!yearElement._preRequisite_id) {
                            allCourse += course_count;
                        }
                        if (course_count === 0) {
                            isConfigured = false;
                        }
                    });
                }
                const refactorCurriculumDoc = { ...yearElement };
                refactorCurriculumDoc.levels = levelData;
                levelData = [];
                yearData.push(refactorCurriculumDoc);
            });
        }
        const refactorCurriculumDoc = {
            ...curriculumDoc,
            allCourse,
            selectiveCourse,
            standardCourse,
            isConfigured,
        };
        refactorCurriculumDoc.yearLevel = yearData;
        refactorCurriculumDoc.creditHours.credit = creditHours;
        curriculumDatas.push(refactorCurriculumDoc);
        if (curriculumDoc.creditHours.hourAs === constant.SPLIT_UP) {
            const totalFinishedDuration = creditHours.reduce(
                (acc, creditHours) => acc + creditHours.finishedDuration,
                0,
            );
            creditHours[creditHours.length - 1].finishedDuration = totalFinishedDuration;
        }
        return {
            statusCode: 200,
            message: 'CURRICULUM_DETAILS',
            data: curriculumDatas,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteCurriculum = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const settingsModel = getModel(tenantURL, constant.SETTINGS, settingSchema);
        const courseModel = getModel(tenantURL, constant.COURSE, courseSchema);
        const { id } = params;
        const curriculumData = await curriculumModel.find({
            'yearLevel._preRequisite_id': convertToMongoObjectId(id),
            isDeleted: false,
        });
        if (curriculumData.length) {
            return {
                statusCode: 404,
                message: 'CURRICULUM_IS_MAPPED_WITH_ANOTHER_PROGRAM_CURRICULUM',
            };
        }
        const curriculumDoc = await curriculumModel
            .findByIdAndUpdate({ _id: id }, { $set: { isDeleted: true } })
            .select('_institution_id');
        if (!curriculumDoc) return { statusCode: 404, message: 'UNABLE_TO_DELETE_CURRICULUM' };
        const curriculumUpdateInCourseAssign = await courseModel.updateMany(
            {
                isDeleted: false,
                'courseAssignedDetails._curriculum_id': convertToMongoObjectId(id),
            },
            {
                $pull: {
                    courseAssignedDetails: {
                        _curriculum_id: convertToMongoObjectId(id),
                    },
                },
            },
        );
        const settingList = await settingsModel
            .findOne({
                _institution_id: curriculumDoc._institution_id,
            })
            .lean()
            .select('globalConfiguration');
        if (!settingList) return { statusCode: 404, message: 'NO_SETTING' };
        let curriculumMode;
        const curriculumNamingSetting =
            settingList.globalConfiguration.programInput.curriculumNaming.forEach(
                (curriculumName) => {
                    if (curriculumName.isDefault === true) {
                        curriculumMode = curriculumName.mode;
                    }
                },
            );
        if (curriculumMode !== 3) {
            const curriculumDocs = await curriculumModel.aggregate([
                {
                    $match: {
                        _institution_id: curriculumDoc._institution_id,
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $group: {
                        _id: '$_program_id',
                        curriculum: {
                            $push: {
                                _id: '$_id',
                                curriculumName: '$curriculumName',
                                createdAt: '$createdAt',
                            },
                        },
                    },
                },
                { $sort: { createdAt: 1 } },
            ]);
            if (curriculumMode === 1) {
                const bulkWrites = [];
                curriculumDocs.forEach(async (curriculumDoc) => {
                    curriculumDoc.curriculum.forEach(async (curriculum, index) => {
                        bulkWrites.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(curriculum._id),
                                },
                                update: { curriculumName: `version ${index + 1}.0` },
                            },
                        });
                    });
                });
                if (bulkWrites.length) {
                    const docs = await curriculumModel.bulkWrite(bulkWrites);
                }
            } else if (curriculumMode === 2) {
                const bulkWrites = [];
                curriculumDocs.forEach(async (curriculumDoc) => {
                    curriculumDoc.curriculum.forEach(async (curriculum, index) => {
                        bulkWrites.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(curriculum._id),
                                },
                                update: { curriculumName: `version ${index + 1}` },
                            },
                        });
                    });
                });
                if (bulkWrites.length) {
                    const docs = await curriculumModel.bulkWrite(bulkWrites);
                }
            }
        }
        return {
            statusCode: 200,
            message: 'CURRICULUM_DELETED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listLevel = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { id } = params;
        const curriculumDoc = await curriculumModel
            .findOne({ _id: id, isDeleted: false })
            .lean()
            .select('yearLevel isPhaseLevel');
        const levelData = [];
        if (curriculumDoc.isPhaseLevel) {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                yearElement.levels.forEach((levelElement) => {
                    if (yearElement._preRequisite_id) {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            _year_id: yearElement._id,
                            yType: yearElement.yType,
                            ispreRequisite: true,
                        });
                    } else {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            _year_id: yearElement._id,
                            yType: yearElement.yType,
                            ispreRequisite: false,
                        });
                    }
                });
            });
        } else {
            curriculumDoc.yearLevel.forEach((yearElement) => {
                yearElement.levels.forEach((levelElement) => {
                    if (yearElement._preRequisite_id) {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            _year_id: yearElement._id,
                            yType: yearElement.yType,
                            ispreRequisite: true,
                        });
                    } else {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            _year_id: yearElement._id,
                            yType: yearElement.yType,
                            ispreRequisite: false,
                        });
                    }
                });
            });
        }
        return {
            statusCode: 200,
            message: 'LEVEL_DATA',
            data: levelData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listYears = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { id } = params;
        const curriculumDoc = await curriculumModel
            .findOne({ _id: id, isDeleted: false })
            .lean()
            .select('yearLevel isPhaseLevel');
        const yearData = [];
        curriculumDoc.yearLevel.forEach((yearElement) => {
            if (yearElement._preRequisite_id) {
                yearData.push({
                    _id: yearElement._id,
                    yType: yearElement.yType,
                    isPreRequisite: true,
                    noOfLevel: yearElement.noOfLevel,
                    levels: yearElement.levels,
                });
            } else {
                yearData.push({
                    _id: yearElement._id,
                    yType: yearElement.yType,
                    isPreRequisite: false,
                    noOfLevel: yearElement.noOfLevel,
                    levels: yearElement.levels,
                });
            }
        });
        return {
            statusCode: 200,
            message: 'YEAR_DATA',
            data: yearData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listLevelYearWise = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { id } = params;
        const { yearIds } = body;
        const curriculumDoc = await curriculumModel
            .findOne({ _id: id, isDeleted: false })
            .lean()
            .select('yearLevel isPhaseLevel');
        const levelData = [];
        curriculumDoc.yearLevel.forEach((yearElement) => {
            if (yearIds.includes(yearElement._id.toString())) {
                yearElement.levels.forEach((levelElement) => {
                    if (yearElement._preRequisite_id) {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            ispreRequisite: true,
                        });
                    } else {
                        levelData.push({
                            _id: levelElement._id,
                            levelName: levelElement.levelName,
                            startWeek: levelElement.startWeek,
                            endWeek: levelElement.endWeek,
                            ispreRequisite: false,
                        });
                    }
                });
            }
        });

        return {
            statusCode: 200,
            message: 'LEVEL_DATA',
            data: levelData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const archiveCurriculum = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { id } = params;
        const { isActive } = body;
        const curriculumDoc = await curriculumModel.findByIdAndUpdate(
            { _id: id },
            { $set: { isActive } },
        );
        if (!curriculumDoc) return { statusCode: 404, message: 'UNABLE_TO_ARCHIVE_CURRICULUM' };
        return {
            statusCode: 200,
            message: 'CURRICULUM_ARCHIVED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listArchived = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { _institution_id, _program_id } = params;
        const curriculumQuery = { _institution_id, _program_id, isActive: false };
        const curriculumDoc = await curriculumModel
            .find(curriculumQuery)
            .lean()
            .select('curriculumName');
        if (!curriculumDoc) return { statusCode: 404, message: 'Error' };
        return {
            statusCode: 200,
            message: 'CURRICULUM_ARCHIVED_LIST',
            data: curriculumDoc,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateLevel = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const { id, yearId, levelId } = params;
        const { _institution_id, levelName, startWeek, endWeek } = body;
        const curriculumDoc = await curriculumModel.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(id),
            },
            {
                $set: {
                    'yearLevel.$[year].levels.$[level].levelName': levelName,
                    'yearLevel.$[year].levels.$[level].startWeek': startWeek,
                    'yearLevel.$[year].levels.$[level].endWeek': endWeek,
                },
            },
            {
                arrayFilters: [
                    { 'year._id': convertToMongoObjectId(yearId) },
                    { 'level._id': convertToMongoObjectId(levelId) },
                ],
            },
        );
        if (curriculumDoc.nModified === 0) return { statusCode: 404, message: 'Error' };
        return {
            statusCode: 200,
            message: 'LEVEL_UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPreRequisite = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const programModel = getModel(tenantURL, constant.PROGRAM, programSchema);
        const { _institution_id } = params;
        const programList = await programModel
            .find({
                _institution_id,
                type: constant.PREREQUISITE,
                isDeleted: false,
            })
            .lean();
        if (!programList) return { statusCode: 404, message: 'NO_PROGRAMS' };
        const programIds = programList.map((i) => convertToMongoObjectId(i._id));
        const curriculumDocs = await curriculumModel
            .find({
                _program_id: { $in: programIds },
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            })
            .lean();
        const programData = [];
        programList.forEach((program) => {
            if (curriculumDocs)
                curriculumDocs.forEach((curriculumDoc) => {
                    if (curriculumDoc._program_id.toString() === program._id.toString()) {
                        curriculumDoc.programName = program.name;
                        programData.push(curriculumDoc);
                    }
                });
        });
        return {
            statusCode: 200,
            message: 'PROGRAM_LIST',
            data: programData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCurriculumName = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, constant.CURRICULUM, curriculumSchema);
        const settingsModel = getModel(tenantURL, constant.SETTINGS, settingSchema);
        const { _institution_id, _program_id } = params;
        const settingList = await settingsModel
            .findOne({
                _institution_id,
            })
            .lean()
            .select('globalConfiguration');
        if (!settingList) return { statusCode: 404, message: 'NO_SETTING' };
        let curriculumMode;
        const curriculumNamingSetting =
            settingList.globalConfiguration.programInput.curriculumNaming.forEach(
                (curriculumName) => {
                    if (curriculumName.isDefault === true) {
                        curriculumMode = curriculumName.mode;
                    }
                },
            );
        const curriculumDocs = await curriculumModel
            .find({
                _program_id: convertToMongoObjectId(_program_id),
                isDeleted: false,
            })
            .select('curriculumName');
        let curriculumName;
        if (curriculumMode === 1) {
            if (curriculumDocs) {
                curriculumName = `version ${curriculumDocs.length + 1}.0`;
            } else {
                curriculumName = `version 1.0`;
            }
        } else if (curriculumMode === 2) {
            if (curriculumDocs) {
                curriculumName = `version ${curriculumDocs.length + 1}`;
            } else {
                curriculumName = `version 1`;
            }
        } else {
            curriculumName = '';
        }
        return {
            statusCode: 200,
            message: 'CURRICULUM_NAME',
            data: curriculumName,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSessionDeliveryTypes = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionModel = getModel(
            tenantURL,
            constant.SESSION_DELIVERY_TYPES,
            sessionTypesSchema,
        );
        const { _institution_id, _program_id } = params;
        const sessionTypes = await sessionModel
            .find({
                _institution_id,
                _program_id,
                isDeleted: false,
            })
            .lean()
            .select('sessionName sessionSymbol');
        if (!sessionTypes) return { statusCode: 404, message: 'NO_SESSION_TYPES' };
        const sessionDeliveryTypes = [];
        const sessionDeliveryType = sessionTypes.forEach((session) => {
            sessionDeliveryTypes.push({
                sessionName: session.sessionName,
                sessionSymbol: session.sessionSymbol,
            });
        });
        return {
            statusCode: 200,
            message: 'SESSION_TYPES',
            data: sessionDeliveryTypes,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectiveCourseConfigAdd = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { _curriculum_id } = params;
        const { _program_id, _year_id, _level_id, noOfSelection, hoursType, selectives } = body;

        const curriculumCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!curriculumCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const selectiveCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                    'selectiveCourseConfiguration._year_id': convertToMongoObjectId(_year_id),
                    'selectiveCourseConfiguration._level_id': convertToMongoObjectId(_level_id),
                    'selectiveCourseConfiguration._program_id': convertToMongoObjectId(_program_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    selectiveCourseConfiguration: 1,
                },
            )
            .lean();
        if (!selectiveCheck) {
            const curriculumUpdate = await curriculumModel.updateOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                },
                {
                    $push: {
                        selectiveCourseConfiguration: {
                            _program_id,
                            _year_id,
                            _level_id,
                            noOfSelection,
                            hoursType,
                            selectives,
                        },
                    },
                },
            );
            if (curriculumUpdate)
                return {
                    statusCode: 201,
                    message: 'DS_UPDATED',
                };
        }
        const selectiveData = selectiveCheck.selectiveCourseConfiguration.find(
            (selectiveEntry) =>
                selectiveEntry._program_id.toString() === _program_id &&
                selectiveEntry._year_id.toString() === _year_id &&
                selectiveEntry._level_id.toString() === _level_id,
        );

        const selectiveQuery = {
            _id: convertToMongoObjectId(_curriculum_id),
            isDeleted: false,
        };
        const selectiveobject = {
            $set: {
                'selectiveCourseConfiguration.$[i]._year_id': _year_id,
                'selectiveCourseConfiguration.$[i]._program_id': _program_id,
                'selectiveCourseConfiguration.$[i]._level_id': _level_id,
                'selectiveCourseConfiguration.$[i].noOfSelection': noOfSelection,
                'selectiveCourseConfiguration.$[i].hoursType': hoursType,
                'selectiveCourseConfiguration.$[i].selectives': selectives,
            },
        };
        const selectivefilter = {
            arrayFilters: [
                {
                    'i._id': selectiveData._id,
                },
            ],
        };
        const selectiveUpdation = await curriculumModel.updateOne(
            selectiveQuery,
            selectiveobject,
            selectivefilter,
        );
        if (selectiveUpdation)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectiveDelete = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const { _curriculum_id, _selective_id } = params;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const curriculumCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!curriculumCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        const selectiveDeletion = await curriculumModel.updateOne(
            {
                _id: convertToMongoObjectId(_curriculum_id),
            },
            {
                $pull: {
                    selectiveCourseConfiguration: {
                        _id: convertToMongoObjectId(_selective_id),
                    },
                },
            },
        );
        if (selectiveDeletion)
            return {
                statusCode: 200,
                message: 'DS_DELETED',
            };
        return {
            statusCode: 500,
            message: 'DS_DELETE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectiveGet = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { _curriculum_id, _program_id, _year_id, _level_id } = params;
        const curriculumCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!curriculumCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const selectiveCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_curriculum_id),
                    'selectiveCourseConfiguration._year_id': convertToMongoObjectId(_year_id),
                    'selectiveCourseConfiguration._level_id': convertToMongoObjectId(_level_id),
                    'selectiveCourseConfiguration._program_id': convertToMongoObjectId(_program_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    selectiveCourseConfiguration: 1,
                },
            )
            .lean();
        if (
            selectiveCheck &&
            selectiveCheck.selectiveCourseConfiguration &&
            selectiveCheck.selectiveCourseConfiguration.length > 0
        ) {
            const selectiveData = selectiveCheck.selectiveCourseConfiguration.filter(
                (selectiveEntry) =>
                    selectiveEntry._program_id.toString() === _program_id &&
                    selectiveEntry._year_id.toString() === _year_id &&
                    selectiveEntry._level_id.toString() === _level_id,
            );
            return {
                statusCode: 200,
                message: 'DS_RETRIEVED',
                data: selectiveData,
            };
        }
        return {
            statusCode: 200,
            message: 'DS_GET_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    addCurriculum,
    updateCurriculum,
    listAllCurriculum,
    getParticularCurriculum,
    deleteCurriculum,
    archiveCurriculum,
    listArchived,
    updateLevel,
    getPreRequisite,
    getCurriculumName,
    getSessionDeliveryTypes,
    listLevel,
    selectiveCourseConfigAdd,
    selectiveDelete,
    selectiveGet,
    listLevelYearWise,
    listYears,
};
