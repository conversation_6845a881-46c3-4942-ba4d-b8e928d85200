let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');
let university = new Schema({

    institute_name: String,
    institute_type: String,
    no_of_college: Number,
    logo: String,
    address_details: {
        address: String,
        country: String,
        state: String,
        district: String,
        city: String,
        zipcode: String,
    },
    _user_id: {
        type: Schema.Types.ObjectId,
        ref: constant.USER
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }

},
    { timestamps: true });
module.exports = mongoose.model(constant.DIGI_UNIVERSITY, university);
