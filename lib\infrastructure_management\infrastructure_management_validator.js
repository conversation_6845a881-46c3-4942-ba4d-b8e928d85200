// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.infrastructure_management = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _building_id: Joi.string().alphanum().required(),
                    building_name: Joi.string().required(),
                    floor_no: Joi.string().allow('').optional(),
                    zone: Joi.array().optional().items(Joi.string().optional()),
                    room_no: Joi.string().min(3).required(),
                    name: Joi.string().min(2).required(),
                    usage: Joi.string()
                        .valid(
                            'Academic',
                            'Exam',
                            'Administrative',
                            'ACADEMIC',
                            'EXAM',
                            'ADMINISTRATIVE',
                        )
                        .required(),
                    delivery_type: Joi.array().items(
                        Joi.object().keys({
                            _delivery_type_id: Joi.string().alphanum().length(24).required(),
                            delivery_symbol: Joi.string().alphanum().min(1).required(),
                        }),
                    ),
                    timing: Joi.array().items(
                        Joi.object().keys({
                            _time_id: Joi.string().alphanum().required(),
                            /* time: Joi.string().min(1).required().error(error => {
                        return error;
                    }) */
                        }),
                    ),
                    program: Joi.array().items(
                        Joi.object().keys({
                            _program_id: Joi.string().alphanum().length(24).required(),
                            name: Joi.string().min(1).required(),
                        }),
                    ),
                    department: Joi.array().items(
                        Joi.object().keys({
                            _department_id: Joi.string().alphanum().length(24).required(),
                            name: Joi.string().min(1).required(),
                        }),
                    ),
                    subject: Joi.array().items(
                        Joi.object().keys({
                            _subject_id: Joi.string().alphanum().length(24).required(),
                            name: Joi.string().min(1).required(),
                        }),
                    ),
                    capacity: Joi.number(),
                    reserved: Joi.number(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.infrastructure_management_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
