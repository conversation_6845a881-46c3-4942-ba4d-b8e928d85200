let program_calendar_formate = require('../program_calendar/program_calendar_formate');

module.exports = {
    program_calendar_event: (doc) => {
        let formate_obj = [];
        doc.forEach(doc => {
            let obj = {
                _id: doc._id,
                event_date: doc.event_date,
                event_type: doc.event_type,
                event: doc.event,
                start_time: doc.start_time,
                end_time: doc.end_time,
                end_date: doc.end_date,
                program_calendar: program_calendar_formate.program_calendar_ID_Only(doc.program_calendar),
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    program_calendar_event_ID: (doc) => {
        let obj = {
            _id: doc._id,
            event_date: doc.event_date,
            event_type: doc.event_type,
            event: doc.event,
            start_time: doc.start_time,
            end_time: doc.end_time,
            end_date: doc.end_date,
            program_calendar: program_calendar_formate.program_calendar_ID_Only(doc.program_calendar),
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_event_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            event_date: doc.event_date,
            event_type: doc.event_type,
            event: doc.event,
            start_time: doc.start_time,
            end_time: doc.end_time,
            end_date: doc.end_date,
            program_calendar: doc._program_calendar_id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_event_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(doc => {
            let obj = {
                _id: doc._id,
                event_date: doc.event_date,
                event_type: doc.event_type,
                event: doc.event,
                start_time: doc.start_time,
                end_time: doc.end_time,
                end_date: doc.end_date,
                program_calendar: doc._program_calendar_id,
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}