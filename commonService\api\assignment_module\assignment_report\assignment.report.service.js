const {
    STUDENT_GROUP,
    GENDER,
    PUBLISH,
    DIGI_COURSE,
    DIGI_CURRICULUM,
    DIGI_SESSION_ORDER,
    PROMPT,
    IRAT,
    TRAT,
    RAT,
    RAT_REPORT,
} = require('../../../utility/constants');
const { convertToMongoObjectId } = require('../../../utility/common');
const assignmentAnswerSchema = require('../assignment_answer/assignment_answer.model');
const studentGroupSchema = require('mongoose').model(STUDENT_GROUP);
const digi_course_schema = require('mongoose').model(DIGI_COURSE);
const digi_curriculums_schema = require('mongoose').model(DIGI_CURRICULUM);
const digi_session_orders_schema = require('mongoose').model(DIGI_SESSION_ORDER);
const assignmentSettingsSchema = require('../assignment-settings/assignment-settings.model');
const { send_email } = require('../../../../lib/utility/common_functions');
const { getPaginationValues } = require('../../../utility/pagination');
const assignmentSchema = require('../assignment/assignment.model');
const assignmentPromptSchema = require('../assignment-prompt/assignment-prompt.model');
const assignmentPromptAnswerSchema = require('../assignment_prompt_answer/assignment_prompt_answer.model');
const { groupBy } = require('lodash');

// student count , male count , female count
const studentGenderCount = async ({
    institutionId,
    institutionCalendarId,
    programId,
    courseId,
    term,
    year_no,
    level_no,
    rotationCount,
}) => {
    try {
        const studentPipeline = [
            {
                $match: {
                    _institution_calendar_id: institutionCalendarId,
                    _institution_id: institutionId,
                    'master._program_id': programId,
                    'groups.courses._course_id': courseId,
                },
            },
            {
                $unwind: {
                    path: '$groups',
                },
            },
            {
                $match: {
                    'groups.term': term,
                    'groups.level': level_no,
                },
            },
            {
                $unwind: {
                    path: '$groups.courses',
                },
            },
            {
                $match: {
                    'groups.courses._course_id': courseId,
                },
            },
        ];
        const studentsWithCourseDetails = await studentGroupSchema.aggregate(studentPipeline);
        const matchedStudentData = studentsWithCourseDetails[0];
        if (
            studentsWithCourseDetails.length === 0 ||
            (matchedStudentData && typeof matchedStudentData !== 'object')
        ) {
            return {
                totalStudentCount: 0,
                maleStudentCount: 0,
                femaleStudentCount: 0,
            };
        }
        let maleStudentCount = 0;
        let femaleStudentCount = 0;
        const courseObject = matchedStudentData.groups.courses;
        if (courseObject && typeof courseObject === 'object') {
            const courseSettings = courseObject.setting;
            if (courseSettings && courseSettings.length) {
                const maleStudentIdMap = new Map();
                const femaleStudentIdMap = new Map();
                for (const { session_setting, gender, _group_no } of courseSettings) {
                    if (rotationCount !== 0 && _group_no !== rotationCount) continue;
                    if (session_setting && session_setting.length) {
                        for (const { groups } of session_setting) {
                            if (groups && groups.length) {
                                for (const { _student_ids } of groups) {
                                    for (const studentIdElement of _student_ids) {
                                        const studentId = studentIdElement.toString();
                                        if (gender === 'male') {
                                            if (maleStudentIdMap.has(studentId)) continue;
                                            maleStudentIdMap.set(studentId, true);
                                        }
                                        if (gender === 'female') {
                                            if (femaleStudentIdMap.has(studentId)) continue;
                                            femaleStudentIdMap.set(studentId, true);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                maleStudentCount = maleStudentIdMap.size;
                femaleStudentCount = femaleStudentIdMap.size;
            }
        }
        return {
            totalStudentCount: maleStudentCount + femaleStudentCount,
            maleStudentCount,
            femaleStudentCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const generateEmailContentForAssignmentReport = (emailContentData) => {
    try {
        const assignmentReportMailContentTemplate =
            '<h1>Assignment Grade Finalized Report</h1>' +
            '<p>Assignment report for the ' +
            ' ' +
            (emailContentData.programName
                ? `Program Name : <b>${emailContentData.programName}</b>`
                : '') +
            ' ' +
            (emailContentData.courseName
                ? ` Course Name : <b>${emailContentData.courseName}</b>`
                : '') +
            ' ' +
            'has been published.kindly visit link to view the report.</p>' +
            `<a href='https://digiclass.digivalsolutions.com/assignment'>Click here</a>` +
            '<p>Best regards.</p>';
        return assignmentReportMailContentTemplate;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const mailServiceForStaff = async ({ requiredMails, programName, courseName }) => {
    try {
        if (requiredMails.length === 0) return false;
        const emailContentHtmlFormate = generateEmailContentForAssignmentReport({
            ...(programName && { programName }),
            ...(courseName && { courseName }),
        });
        if (!emailContentHtmlFormate) return false;
        const mailOptions = {
            to_address: requiredMails,
            subject: 'Digi Class Assignment Grade Finalized Report Notification',
            data: emailContentHtmlFormate,
        };
        await send_email(mailOptions);
        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const findAssignmentTypesCount = ({ assignmentData }) => {
    try {
        let summativeGradeCount = 0;
        let formativeGradeCount = 0;
        let formativeUnGradeCount = 0;
        let formativeTotalMark = 0;
        let summativeTotalMark = 0;
        let iRatCount = 0;
        let tRatCount = 0;
        for (const assignment of assignmentData) {
            const { assignmentType, scoringType, totalScore, ratType } = assignment;
            if (assignmentType === 'Formative Assignment' && scoringType === 'Graded') {
                formativeGradeCount++;
                formativeTotalMark += totalScore;
            }

            if (assignmentType === 'Formative Assignment' && scoringType === 'Ungraded')
                formativeUnGradeCount++;
            if (assignmentType === 'Summative Assignment') {
                summativeGradeCount++;
                summativeTotalMark += totalScore;
            }
            if (ratType === IRAT) {
                iRatCount++;
            }
            if (ratType === TRAT) {
                tRatCount++;
            }
        }
        return {
            summativeGradeCount,
            formativeGradeCount,
            formativeUnGradeCount,
            formativeTotalMark,
            summativeTotalMark,
            iRatCount,
            tRatCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDataFromAssignment = async ({
    shouldSkipAndLimit = false,
    assignmentQuery,
    assignmentProject,
    limitData,
}) => {
    try {
        const assignmentReportQuery = assignmentSchema.find(assignmentQuery, assignmentProject);
        if (!shouldSkipAndLimit) {
            const { pageNo, limit } = limitData;
            const paginationData = getPaginationValues({ pageNo, limit });
            assignmentReportQuery.skip(paginationData.skip).limit(paginationData.limit);
        }
        const assignmentData = await assignmentReportQuery
            .populate({ path: 'basic.taxonomy', select: { name: 1 } })
            .populate({ path: 'createdBy', select: { name: 1 } })
            .lean();
        const promptIds = [];
        for (assignmentElement of assignmentData) {
            if (assignmentElement.taxonomyScope === PROMPT) {
                assignmentElement.prompts.map((promptIdElement) => promptIds.push(promptIdElement));
            }
        }
        if (promptIds.length) {
            const promptQuery = {
                isDeleted: false,
                _id: {
                    $in: promptIds.map((promptIdElement) =>
                        convertToMongoObjectId(promptIdElement),
                    ),
                },
            };
            const promptProject = {
                _id: 1,
                taxonomyIds: 1,
                assignmentId: 1,
            };
            const promptTaxonomies = await assignmentPromptSchema
                .find(promptQuery, promptProject)
                .populate({ path: 'taxonomyIds', select: { name: 1 } })
                .lean();
            const taxonomies = {};
            for (const taxonomyElement of promptTaxonomies) {
                const assignmentId = taxonomyElement.assignmentId.toString();
                if (!taxonomies.hasOwnProperty(assignmentId)) {
                    taxonomies[assignmentId] = [];
                }
                if (taxonomyElement.taxonomyIds && taxonomyElement.taxonomyIds.length) {
                    const existingTaxonomyIds = taxonomies[assignmentId].map((taxonomyElement) =>
                        taxonomyElement._id.toString(),
                    );
                    const newTaxonomyIds = taxonomyElement.taxonomyIds.filter(
                        (taxonomyElement) =>
                            !existingTaxonomyIds.includes(taxonomyElement._id.toString()),
                    );
                    taxonomies[assignmentId].push(...newTaxonomyIds);
                }
            }
            for (const assignmentElement of assignmentData) {
                const matchedTaxonomy = taxonomies[assignmentElement._id.toString()];
                if (matchedTaxonomy) {
                    assignmentElement.basic.taxonomy = [
                        ...assignmentElement.basic.taxonomy,
                        ...matchedTaxonomy,
                    ];
                }
            }
        }
        return assignmentData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDataAndTypesCountFromAssignment = async ({ assignmentQuery, assignmentProject }) => {
    try {
        if (assignmentQuery.hasOwnProperty('basic.scoringType')) {
            delete assignmentQuery['basic.scoringType'];
        }
        if (assignmentQuery.hasOwnProperty('basic.type')) {
            delete assignmentQuery['basic.type'];
        }
        if (!assignmentProject.hasOwnProperty('type')) {
            assignmentProject.assignmentType = '$basic.type';
        }
        if (!assignmentProject.hasOwnProperty('scoringType')) {
            assignmentProject.scoringType = '$basic.scoringType';
        }
        const assignmentTypeData = await assignmentSchema
            .find(assignmentQuery, assignmentProject)
            .lean();
        const assignmentTypesCountData = findAssignmentTypesCount({
            assignmentData: assignmentTypeData,
        });

        return assignmentTypesCountData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentListBasedOnAssignment = async ({ assignmentQuery, assignmentProject }) => {
    try {
        const assignmentAnswer = await assignmentAnswerSchema
            .find(assignmentQuery, assignmentProject)
            .populate({
                path: 'studentId',
                select: { name: 1, gender: 1, user_id: 1 },
            })
            .lean();
        const groupedByAssignment = groupBy(assignmentAnswer, 'assignmentId');
        return groupedByAssignment;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const constructReportCreateAndUpdateData = async ({ headers, body }) => {
    try {
        if (!headers && !body) return { statusCode: 400, message: 'Empty Data' };
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            _id,
            courseId,
            programId,
            year,
            level,
            term,
            courseName,
            programName,
            staffId,
            equivalenceMark,
            extraMarks,
            rotation_count,
            studentData,
            course_code,
            emailStaffIds,
            commonFieldData,
            reportType,
        } = body;

        const {
            markAdjustmentIsActive,
            deductMarkIsActive,
            markEquivalenceIsActive,
            extraMarksIsActive,
            isAssignment,
            isMarkAdjustment,
            isMarkAdjustmentReason,
            isObtained,
            isExtraMark,
            isDeductMark,
            isDeductReason,
            isFinalObtainedMark,
            isMarkEquivalence,
            isFinalObtainedPercentage,
            isGrade,
            isCriticalStatus,
        } = commonFieldData;

        if (studentData.length === 0) return { statusCode: 400, message: 'Student Data Empty' };
        const reportId = convertToMongoObjectId();
        const reportStudentDataCreateBulkWrite = [];
        const reportStudentDataUpdateBulkWrite = [];
        const summativeData = [];
        const formativeData = [];
        const formativeUnGradedData = [];
        const RATGradedData = [];
        const RATUngradedData = [];
        const studentObjectId = [];
        const studentAssignmentObjectData = studentData[0];
        const assignmentIds = [];
        if (!studentAssignmentObjectData && studentAssignmentObjectData.assignment.length === 0)
            return { statusCode: 400, message: 'Assignment Data Empty' };
        for (const assignmentElement of studentAssignmentObjectData.assignment) {
            const { assignmentType, assignmentId, assignmentName, assignmentMark } =
                assignmentElement;
            const assignmentObject = {
                assignmentName,
                assignmentMark,
                assignmentId: convertToMongoObjectId(assignmentId),
                assignmentType,
            };
            assignmentIds.push(assignmentId);
            switch (assignmentType) {
                case 'summative':
                    summativeData.push(assignmentObject);
                    break;
                case 'formativeGraded':
                    formativeData.push(assignmentObject);
                    break;
                case 'formativeUngraded':
                    formativeUnGradedData.push(assignmentObject);
                    break;
                case 'ratGraded':
                    RATGradedData.push(assignmentObject);
                    break;
                case 'ratUngraded':
                    RATUngradedData.push(assignmentObject);
                    break;
                default:
                    break;
            }
        }

        for (const individualStudent of studentData) {
            const {
                student_id,
                studentName,
                studentId,
                status,
                markAdjustment,
                obtained,
                extraMark,
                deductMark,
                finalObtainedMark,
                markEquivalence,
                finalObtainedPercentage,
                grade,
                criticalStatus,
                assignment,
            } = individualStudent;
            if (!status) return { statusCode: 400, message: 'Student Status Required' };
            const assignmentData = [];
            for (const assignmentObject of assignment) {
                assignmentData.push({
                    assignmentId: convertToMongoObjectId(assignmentObject.assignmentId),
                    assignmentName: assignmentObject.assignmentName,
                    assignmentMark: assignmentObject.assignmentMark,
                    studentMark: assignmentObject.studentMark,
                    adjustMark: assignmentObject.adjustMark,
                    assignmentType: assignmentObject.assignmentType,
                });
            }

            const reportConstructedData = {
                ...(_id ? { reportId: convertToMongoObjectId(_id) } : { reportId }),
                studentName,
                studentId: convertToMongoObjectId(studentId),
                ...(assignmentData.length !== 0 && {
                    'assignmentDetails.assignment': assignmentData,
                }),
                ...(markAdjustment && {
                    'markAdjustment.mark': markAdjustment.mark,
                    'markAdjustment.reasonForMarkUpdate.reason':
                        markAdjustment.reasonForMarkUpdate.reason,
                }),
                ...(obtained && {
                    'obtained.mark': obtained.mark,
                    'obtained.percentage': obtained.percentage,
                }),
                'extraMark.mark': extraMark,
                ...(deductMark && {
                    'deductMark.mark': deductMark.mark,
                    'deductMark.reasonForMarkUpdate.reason': deductMark.reasonForMarkUpdate.reason,
                }),
                'finalObtainedMark.mark': finalObtainedMark,
                ...(markEquivalence && {
                    'markEquivalence.equivalenceMark': markEquivalence.equivalenceMark,
                    'markEquivalence.studentMark': markEquivalence.studentMark,
                }),
                'finalObtainedPercentage.percentage': finalObtainedPercentage,
                'grade.studentGrade': grade,
                'criticalStatus.status': criticalStatus,
                status,
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            };

            let commonFieldsUpdate;
            if (status === PUBLISH) {
                studentObjectId.push(individualStudent.studentId);
                commonFieldsUpdate = {
                    ...(isAssignment && { 'assignmentDetails.isChecked': isAssignment }),
                    ...(isMarkAdjustment && { 'markAdjustment.isChecked': isMarkAdjustment }),
                    ...(isMarkAdjustmentReason && {
                        'markAdjustment.reasonForMarkUpdate.isChecked': isMarkAdjustmentReason,
                    }),
                    ...(markAdjustmentIsActive && {
                        'markAdjustment.isActive': markAdjustmentIsActive,
                    }),
                    ...(isObtained && { 'obtained.isChecked': isObtained }),
                    ...(isExtraMark && { 'extraMark.isChecked': isExtraMark }),
                    ...(isDeductMark && { 'deductMark.isChecked': isDeductMark }),
                    ...(isDeductReason && {
                        'deductMark.reasonForMarkUpdate.isChecked': isDeductReason,
                    }),
                    ...(deductMarkIsActive && {
                        'deductMark.isActive': deductMarkIsActive,
                    }),
                    ...(isFinalObtainedMark && {
                        'finalObtainedMark.isChecked': isFinalObtainedMark,
                    }),
                    ...(isMarkEquivalence && {
                        'markEquivalence.isChecked': isMarkEquivalence,
                    }),
                    ...(isFinalObtainedPercentage && {
                        'finalObtainedPercentage.isChecked': isFinalObtainedPercentage,
                    }),
                    ...(isGrade && { 'grade.isChecked': isGrade }),
                    ...(isCriticalStatus && { 'criticalStatus.isChecked': isCriticalStatus }),
                };
            }
            if (student_id) {
                reportStudentDataUpdateBulkWrite.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(student_id),
                        },
                        update: { ...reportConstructedData, ...commonFieldsUpdate },
                    },
                });
            } else {
                reportStudentDataCreateBulkWrite.push({
                    insertOne: {
                        document: { ...reportConstructedData, ...commonFieldsUpdate },
                    },
                });
            }
        }

        const assignmentsUpdatedData = {
            ...(summativeData && { summative: summativeData.length !== 0 ? summativeData : [] }),
            ...(formativeData && {
                formativeGraded: formativeData.length !== 0 ? formativeData : [],
            }),
            ...(formativeUnGradedData && {
                formativeUngraded: formativeUnGradedData.length !== 0 ? formativeUnGradedData : [],
            }),
            ...(RATGradedData && {
                ratGraded: RATGradedData.length !== 0 ? RATGradedData : [],
            }),
            ...(RATUngradedData && {
                ratUngraded: RATUngradedData.length !== 0 ? RATUngradedData : [],
            }),
        };

        const assignmentReportData = {
            ...(_id ? { _id: convertToMongoObjectId(_id) } : reportId && { _id: reportId }),
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            courseId,
            programId,
            year,
            level,
            term,
            courseName,
            programName,
            staffId,
            course_code,
            reportType,
            'equivalence.equivalenceMark': equivalenceMark,
            'equivalence.isActive': markEquivalenceIsActive,
            'extraMarks.isActive': extraMarksIsActive,
            ...(extraMarksIsActive && {
                'extraMarks.markType': extraMarks.markType,
                'extraMarks.mark': extraMarks.mark,
                'extraMarks.position': extraMarks.position,
            }),
            ...(assignmentsUpdatedData && { assignments: assignmentsUpdatedData }),
            ...(rotation_count && { rotation_count }),
        };

        return {
            _id,
            courseName,
            programName,
            assignmentIds,
            emailStaffIds,
            studentObjectId,
            deductMarkIsActive,
            extraMarksIsActive,
            assignmentReportData,
            markAdjustmentIsActive,
            reportStudentDataCreateBulkWrite,
            reportStudentDataUpdateBulkWrite,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const mailServiceForStudent = async ({ mailData, courseName }) => {
    try {
        if (!mailData || mailData.length === 0) return false;
        const mailContent =
            '<h1>Assignment Grade Finalized Report</h1>' +
            `<p>Dear  Student</p>` +
            `<p>Assignment report for the <b>${courseName}</b> has been published, kindly visit link to view the full report</p>` +
            `<a href='https://digiclass.digivalsolutions.com/assignment/student/reports'>Click here</a>` +
            '<p>Best regards.</p>';

        if (!mailContent) return false;
        const mailOptions = {
            to_address: mailData,
            subject: 'Digi Class Assignment Grade Finalized Report Notification',
            data: mailContent,
        };
        await send_email(mailOptions);

        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const filterQuery = {
    isActive: true,
    isDeleted: false,
};

const getSessionData = async ({ _course_id, _program_id, _institution_id }) => {
    try {
        const sessionData = await digi_session_orders_schema.aggregate([
            {
                $match: {
                    ...filterQuery,
                    _course_id,
                    _program_id,
                    _institution_id,
                },
            },
            {
                $unwind: '$session_flow_data',
            },
            {
                $unwind: '$session_flow_data.slo',
            },
            {
                $replaceRoot: {
                    newRoot: '$session_flow_data.slo',
                },
            },
        ]);

        return sessionData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const curriculumProject = {
    curriculamId: '$_id',
    _id: '$framework._id',
    name: '$framework.name',
    code: '$framework.code',
    domains: '$framework.domains',
    curriculum_name: '$curriculum_name',
};

const getCurriculumOutcomeData = async ({ _program_id, _institution_id }) => {
    try {
        const curriculumData = await digi_curriculums_schema
            .findOne(
                {
                    ...filterQuery,
                    _program_id,
                    _institution_id,
                },
                curriculumProject,
            )
            .lean();

        return curriculumData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseOutcomeData = async ({
    _course_id,
    _program_id,
    _curriculum_id,
    _institution_id,
}) => {
    try {
        const courseProject = {
            courseId: '$_id',
            _id: '$framework._id',
            code: '$framework.code',
            name: '$framework.name',
            domains: '$framework.domains',
        };

        const courseData = await digi_course_schema
            .findOne(
                {
                    ...filterQuery,
                    _id: _course_id,
                    _program_id,
                    _curriculum_id,
                    _institution_id,
                },
                courseProject,
            )
            .lean();

        return courseData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const constructOutcomeData = async ({ courseData, curriculumData, allData, cloList }) => {
    try {
        for (const courseElement of courseData.domains) {
            for (const curriculamElement of curriculumData.domains) {
                if (courseElement.name === curriculamElement.name) {
                    courseElement.ploList = curriculamElement.ploList;
                }
            }
        }
        courseData.curriculum_name = curriculumData.curriculum_name;
        courseData.curriculamId = curriculumData.curriculamId;
        allData.push(courseData);
        return { allData, cloList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const ploMappedCloConstruct = async ({
    isThisOtherProgramData = false,
    allCloId,
    cloList,
    courseData,
    curriculumData,
    allData,
}) => {
    try {
        // construct plo and mapped clo
        for (const curriculumElement of curriculumData.domains) {
            const ploList = [];
            for (const ploElement of curriculumElement.plo) {
                const mappedCloId = [];
                for (const mappedCloElement of ploElement.clos) {
                    const clo_id = mappedCloElement.clo_id.toString();
                    if (allCloId.includes(clo_id)) {
                        mappedCloId.push(clo_id);
                    }
                }

                ploList.push({
                    mappedCloId,
                    _id: ploElement._id,
                    no: ploElement.no,
                    name: ploElement.name,
                    isActive: ploElement.isActive,
                    isDeleted: ploElement.isDeleted,
                });
            }
            curriculumElement.ploList = ploList;
            delete curriculumElement.plo;
        }
        if (!isThisOtherProgramData) {
            return constructOutcomeData({ courseData, curriculumData, allData, cloList });
        }
        return curriculumData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const checkCourseCloMappedWithOtherPrograms = async ({ allCloId, curriculamId }) => {
    try {
        const curriculumQuery = {
            ...filterQuery,
            'framework.domains.plo.clos.clo_id': { $in: allCloId },
            _id: { $ne: convertToMongoObjectId(curriculamId) },
        };

        const curriculumData = await digi_curriculums_schema
            .find(curriculumQuery, curriculumProject)
            .lean();

        if (!curriculumData.length) return [];

        const updatedCurriculumData = [];
        for (const curriculumElement of curriculumData) {
            const getConstructedData = await ploMappedCloConstruct({
                allCloId,
                isThisOtherProgramData: true,
                curriculumData: curriculumElement,
            });
            updatedCurriculumData.push(getConstructedData);
        }

        return updatedCurriculumData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getOutcomeAndMappedOutcomeData = async ({ courseData, curriculumData }) => {
    try {
        // construct clo and mapped slo
        const allCloId = [];
        const cloList = [];
        for (const courseElement of courseData.domains) {
            for (const cloElement of courseElement.clo) {
                const mappedSloId = [];
                for (const mappedSloElement of cloElement.slos) {
                    const slo_id = mappedSloElement.slo_id.toString();
                    mappedSloId.push(slo_id);
                }

                allCloId.push(cloElement._id.toString());
                cloList.push({
                    mappedSloId,
                    _id: cloElement._id,
                    no: cloElement.no,
                    name: cloElement.name,
                    isActive: cloElement.isActive,
                    isDeleted: cloElement.isDeleted,
                });
            }
            delete courseElement.clo;
        }
        const cloMappedWithOtherProgramsData = await checkCourseCloMappedWithOtherPrograms({
            allCloId,
            curriculamId: curriculumData.curriculamId,
        });
        return ploMappedCloConstruct({
            allCloId,
            cloList,
            courseData,
            curriculumData,
            allData: cloMappedWithOtherProgramsData,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssignmentStudentData = async ({ assignmentData }) => {
    try {
        if (!assignmentData && assignmentData.length === 0) return;
        const promptId = [];
        const assignmentId = [];
        for (const assignmentElement of assignmentData) {
            assignmentId.push(assignmentElement._id);
            if (assignmentElement.basic.prompts.length !== 0) {
                assignmentElement.basic.prompts.map((promptElement) =>
                    promptId.push(promptElement),
                );
            } else {
                assignmentElement.assignment_type = 'assignment';
            }
        }

        const studentQuery = {
            isDeleted: false,
            assignmentId: { $in: assignmentId },
        };

        const studentProject = {
            studentId: 1,
            assignmentId: 1,
            staffEvaluation: 1,
        };

        const studentData = await assignmentAnswerSchema
            .find(studentQuery, studentProject)
            .populate({ path: 'studentId', select: { name: 1, gender: 1, user_id: 1 } })
            .lean();

        for (const studentElement of studentData) {
            const staffId = studentElement.staffEvaluation.releasedStaffId;
            let releasedStaffDataObject;
            if (staffId) {
                releasedStaffDataObject = studentElement.staffEvaluation.allStaffData.filter(
                    (staffObject) => staffObject.staffId.toString() === staffId.toString(),
                );
                studentElement.staffEvaluation = releasedStaffDataObject[0];
            }
            if (!releasedStaffDataObject) {
                studentElement.staffEvaluation = {};
            }
        }

        const groupedStudentData = groupBy(studentData, (studentElement) => [
            studentElement.assignmentId,
            studentElement.studentId._id,
        ]);

        return { promptId, studentData: groupedStudentData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPromptData = async ({ promptId, assignmentData }) => {
    try {
        if (!assignmentData || !assignmentData.length || !promptId.length)
            return { promptData: false };

        const promptQuery = {
            isDeleted: false,
            _id: { $in: promptId },
        };

        const promptProject = {
            _id: 1,
            assignmentId: 1,
            score: '$score.value',
            childPromptIds: 1,
            learningOutcome: 1,
        };

        const assignmentPromptData = await assignmentPromptSchema
            .find(promptQuery, promptProject)
            .lean();

        if (!assignmentPromptData.length) return { promptData: false };

        const parentPromptData = {};
        const assignmentTypeData = {};

        // get parent prompt id for prompt answer staff evaluation object
        for (const promptElement of assignmentPromptData) {
            //set assignment_type
            if (
                promptElement.learningOutcome &&
                promptElement.learningOutcome.outcomeId.length !== 0 &&
                !assignmentTypeData.hasOwnProperty(promptElement.assignmentId)
            ) {
                assignmentTypeData[promptElement.assignmentId] = 'prompt';
            }

            const individualPromptId = promptElement._id.toString();
            if (promptElement.childPromptIds.length === 0) {
                parentPromptData[individualPromptId] = individualPromptId;
            } else {
                for (const childPromptElement of promptElement.childPromptIds) {
                    parentPromptData[childPromptElement] = individualPromptId;
                }
            }
        }

        const groupedPromptData = groupBy(assignmentPromptData, 'assignmentId');

        for (const assignmentElement of assignmentData) {
            if (groupedPromptData.hasOwnProperty(assignmentElement._id)) {
                assignmentElement.promptData = groupedPromptData[assignmentElement._id];
            }
            // set type
            if (assignmentTypeData.hasOwnProperty(assignmentElement._id)) {
                assignmentElement.assignment_type = assignmentTypeData[assignmentElement._id];
            } else {
                assignmentElement.assignment_type = 'assignment';
            }
        }

        return { promptData: true, parentPromptData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPromptAnswer = async ({ promptId, studentData, parentPromptData }) => {
    try {
        const promptAnswerQuery = {
            isDeleted: false,
            assignmentPromptId: { $in: promptId },
        };

        const promptAnswerProject = {
            studentId: 1,
            assignmentId: 1,
            staffEvaluation: 1,
            assignmentPromptId: 1,
        };

        const assignmentPromptAnswerData = await assignmentPromptAnswerSchema
            .find(promptAnswerQuery, promptAnswerProject)
            .lean();

        if (assignmentPromptAnswerData.length == 0) return [];

        const groupedPromptAnswer = groupBy(assignmentPromptAnswerData, (promptAnswerElement) => [
            promptAnswerElement.assignmentId,
            promptAnswerElement.studentId,
        ]);

        const studentDataKeys = Object.keys(groupedPromptAnswer);

        for (const studentKeyElement of studentDataKeys) {
            if (studentData.hasOwnProperty(studentKeyElement)) {
                const studentIndividualData = studentData[studentKeyElement];
                const staffId = studentIndividualData[0].staffEvaluation.staffId;
                if (staffId) {
                    const individualStudentPromptAnswerData =
                        groupedPromptAnswer[studentKeyElement];
                    const staffData = [];
                    for (const individualPromptElement of individualStudentPromptAnswerData) {
                        const releasedStaffData = individualPromptElement.staffEvaluation.filter(
                            (individualPromptStaffElement) =>
                                individualPromptStaffElement.staffId.toString() ===
                                staffId.toString(),
                        );
                        if (releasedStaffData.length) {
                            releasedStaffData[0].assignmentPromptId =
                                parentPromptData[individualPromptElement.assignmentPromptId];
                            staffData.push(releasedStaffData[0]);
                        }
                    }
                    studentIndividualData[0].studentPromptAnswerData = staffData;
                } else {
                    studentIndividualData[0].studentPromptAnswerData = [];
                }
            }
        }

        return true;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const filterAssignmentsByReportType = (assignments, reportType) => {
    const isRatReports = reportType === RAT_REPORT;

    if (isRatReports) {
        return {
            summative: [],
            formativeGraded: [],
            formativeUngraded: [],
            ratGraded: assignments?.ratGraded || [],
            ratUngraded: assignments?.ratUngraded || [],
        };
    }

    return {
        summative: assignments?.summative || [],
        formativeGraded: assignments?.formativeGraded || [],
        formativeUngraded: assignments?.formativeUngraded || [],
        ratGraded: [],
        ratUngraded: [],
    };
};

const filterStudentAssignmentsByReportType = (studentAssignments, reportType) => {
    const isRatReports = reportType === RAT_REPORT;

    return (
        studentAssignments?.filter((assignment) => {
            return isRatReports
                ? assignment.assignmentType === 'ratGraded' ||
                      assignment.assignmentType === 'ratUngraded'
                : assignment.assignmentType !== 'ratGraded' &&
                      assignment.assignmentType !== 'ratUngraded';
        }) || []
    );
};

const filterAssignmentQueryByReportType = (assignmentQuery, reportType) => {
    const isRatReports = reportType === RAT_REPORT;

    if (isRatReports) {
        assignmentQuery['basic.type'] = RAT;
    } else {
        assignmentQuery['basic.type'] = { $ne: RAT };
    }

    return assignmentQuery;
};

module.exports = {
    getPromptData,
    getSessionData,
    getPromptAnswer,
    studentGenderCount,
    getDataFromAssignment,
    mailServiceForStaff,
    mailServiceForStudent,
    getCourseOutcomeData,
    getCurriculumOutcomeData,
    getAssignmentStudentData,
    getOutcomeAndMappedOutcomeData,
    getStudentListBasedOnAssignment,
    getDataAndTypesCountFromAssignment,
    constructReportCreateAndUpdateData,
    filterAssignmentsByReportType,
    filterStudentAssignmentsByReportType,
    filterAssignmentQueryByReportType,
};
