const { convertToMongoObjectId } = require('../utility/common');
const sessionSettingsModel = require('./global_session_settings_model');
const institutionSchema = require('../models/institution');
const { ABSENT, EXCLUDE, DS_INSTITUTION_KEY, DS_PROGRAM_KEY } = require('../utility/constants');
const {
    SERVICES: { LATE_CONFIGURATION },
} = require('../utility/util_keys');
exports.createInstitutionSessionSetting = async ({ body = {}, headers = {} }) => {
    try {
        const {
            isSameTimeForEveryDay,
            workingDays,
            sessions,
            scheduleForNonWorkingDays,
            staffFacial,
            studentFacial,
            selfRegistrationDocument,
        } = body;
        const { _institution_id } = headers;
        const isAlreadySettingsPresent = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        const basicDetails = {
            isSameTimeForEveryDay,
            ...(isSameTimeForEveryDay === true && { sessions }),
            workingDays,
            scheduleForNonWorkingDays,
            staffFacial,
            studentFacial,
            selfRegistrationDocument,
        };
        if (isAlreadySettingsPresent) {
            const updatedSessionDoc = await sessionSettingsModel.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { basicDetails },
            );
            return { statusCode: 200, message: 'SUCCESSFULLY_UPDATED' };
        }
        const newGlobalSession = await sessionSettingsModel.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            basicDetails,
        });
        if (newGlobalSession) {
            return { statusCode: 200, message: 'SUCCESS' };
        }
        return { statusCode: 400, message: 'UNABLE_TO_CREATE' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getInstitutionSessionSetting = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const institutionSessionSetting = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'basicDetails.isSameTimeForEveryDay': 1,
                    'basicDetails.sessions': 1,
                    'basicDetails.scheduleForNonWorkingDays': 1,
                    'basicDetails.workingDays.sessions': 1,
                    'basicDetails.workingDays.day': 1,
                    'basicDetails.workingDays.isActive': 1,
                    'basicDetails.staffFacial': 1,
                    'basicDetails.studentFacial': 1,
                    'basicDetails.selfRegistrationDocument': 1,
                    'basicDetails.schedulePermission': 1,
                },
            )
            .lean();
        if (!institutionSessionSetting) {
            return {
                statusCode: 200,
                message: 'SUCCESS',
                data: [],
            };
        }
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: institutionSessionSetting.basicDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.deleteInstitutionSessionSetting = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const deleteInstitutionSession = await sessionSettingsModel.updateOne(
            { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
            { $set: { isDeleted: true, isActive: false } },
        );
        if (!deleteInstitutionSession.modifiedCount) {
            return { statusCode: 400, message: 'UNABLE_TO_DELETE' };
        }
        return { statusCode: 200, message: 'DELETED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const TardisSchema = require('./global_session_tardis_model');

exports.getTardis = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const tardisData = await TardisSchema.find({
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
        });
        return {
            statusCode: 200,
            data: tardisData,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.createTardis = async ({ headers = {}, body = {} }) => {
    try {
        const { name, isActive = true, isDeleted = false, _id, short_code } = body;
        const { _institution_id } = headers;
        const updatedDoc = await TardisSchema.findOneAndUpdate(
            { _id: _id ? convertToMongoObjectId(_id) : convertToMongoObjectId() },
            { $set: { name, isActive, isDeleted, _institution_id, short_code } },
            { upsert: true, new: true },
        );
        return {
            statusCode: 200,
            data: updatedDoc,
            message: _id ? 'UPDATED_SUCCESSFULLY' : 'CREATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error.codeName === 'DuplicateKey') {
            return {
                statusCode: 409,
                message: 'DUPLICATE_NAME_FOUND',
            };
        }
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.deleteTardis = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id } = body;
        await TardisSchema.updateOne(
            {
                _id: convertToMongoObjectId(_id),
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { $set: { isDeleted: true, isActive: false } },
        );
        return {
            statusCode: 200,
            message: 'DELETED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.lateConfiguration = async ({ headers = {}, body = {} }) => {
    try {
        const { lateConfig } = body;
        const { _institution_id } = headers;
        if (LATE_CONFIGURATION === 'false') {
            return { statusCode: 200, message: 'This service unavailable for this Institution' };
        }
        const updateInstitutionData = await institutionSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: { lateConfig },
            },
        );
        return {
            statusCode: 200,
            message: 'UPDATED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.updateStaffFacial = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { staffFacial } = query;
        const isAlreadySettingsPresent = await sessionSettingsModel.findOneAndUpdate(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { $set: { 'basicDetails.staffFacial': staffFacial } },
        );
        if (isAlreadySettingsPresent) {
            return {
                statusCode: 200,
                message: 'UPDATED_SUCCESSFULLY',
            };
        }
    } catch (error) {
        if (error instanceof Error) throw error;
    }
};
exports.updateSchedulePermission = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { create, edit } = query;
        const schedulePermissionSetting = await sessionSettingsModel
            .findOneAndUpdate(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    $set: {
                        'basicDetails.schedulePermission': { create, edit },
                    },
                },
            )
            .lean();
        if (schedulePermissionSetting) {
            return {
                statusCode: 200,
                message: 'UPDATED_SUCCESSFULLY',
            };
        }
    } catch (error) {
        if (error instanceof Error) throw error;
    }
};

exports.getLateConfig = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const lateConfigData = await institutionSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    lateConfig: 1,
                },
            )
            .lean();
        if (!lateConfigData) {
            return {
                statusCode: 200,
                message: 'SUCCESS',
                data: [],
            };
        }
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: lateConfigData,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
    }
};
//Session status management.
exports.getGlobalSessionStatusDetails = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const globalSessionStatusDetails = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { sessionStatusDetails: 1 },
            )
            .lean();
        let currentGlobalSessionStatusDetail = {
            isEnabled: false,
            activeToInactive: ABSENT,
            inactiveToActive: EXCLUDE,
        };
        if (globalSessionStatusDetails && globalSessionStatusDetails.sessionStatusDetails) {
            currentGlobalSessionStatusDetail = globalSessionStatusDetails.sessionStatusDetails;
        }
        return {
            statusCode: 200,
            message: 'Global session status details',
            data: currentGlobalSessionStatusDetail,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.updateGlobalSessionStatusDetails = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { sessionStatusDetails } = body;
        const updatedSessionStatusDetail = await sessionSettingsModel.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: { sessionStatusDetails },
            },
        );
        const updatedCount = updatedSessionStatusDetail.modifiedCount;
        return {
            statusCode: updatedCount ? 200 : 400,
            message: updatedCount
                ? 'Global session status details updated successfully'
                : 'Unable to update global session status details',
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

// Schedule Staff & Student Attendance Captcha Control
exports.getGlobalScheduleStaffStudentCaptchaControl = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const globalSessionStatusDetails = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { scheduleAttendanceConfig: 1 },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'Captcha Control Details',
            data:
                globalSessionStatusDetails && globalSessionStatusDetails.scheduleAttendanceConfig
                    ? globalSessionStatusDetails.scheduleAttendanceConfig
                    : {
                          staffCaptcha: false,
                          studentCaptcha: false,
                      },
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.updateGlobalScheduleStaffStudentCaptchaControl = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { scheduleAttendanceConfig } = body;
        const updatedSessionStatusDetail = await sessionSettingsModel.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: { scheduleAttendanceConfig },
            },
        );
        const updatedCount = updatedSessionStatusDetail.modifiedCount;
        return {
            statusCode: updatedCount ? 200 : 400,
            message: updatedCount
                ? 'Schedule Staff & Student Attendance Captcha Control updated successfully'
                : 'Unable to update Schedule Staff & Student Attendance Captcha Control',
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
//global session document details
exports.getGlobalSessionDocumentDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type = DS_INSTITUTION_KEY, programId = '' } = query;
        const globalSessionDocumentDetails = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { institutionDocumentDetails: 1, programDocumentDetails: 1 },
            )
            .lean();
        let sessionContentDetails = {
            content: '',
            url: [],
            type,
            programId,
        };
        if (globalSessionDocumentDetails) {
            const { institutionDocumentDetails = {}, programDocumentDetails = [] } =
                globalSessionDocumentDetails;
            if (type === DS_PROGRAM_KEY) {
                const currentProgramDocumentDetail = programDocumentDetails.find(
                    (programDocumentDetailELement) =>
                        String(programDocumentDetailELement.programId) === String(programId),
                );
                if (currentProgramDocumentDetail) {
                    sessionContentDetails = {
                        content: currentProgramDocumentDetail.content || '',
                        url: currentProgramDocumentDetail.url || [],
                        type: currentProgramDocumentDetail.type || DS_PROGRAM_KEY,
                        programId: currentProgramDocumentDetail.programId,
                        _id: currentProgramDocumentDetail._id || '',
                    };
                }
            } else {
                if (Object.keys(institutionDocumentDetails).length) {
                    sessionContentDetails = {
                        content: institutionDocumentDetails.content || '',
                        url: institutionDocumentDetails.url || [],
                        type: institutionDocumentDetails.type || DS_INSTITUTION_KEY,
                        _id: institutionDocumentDetails._id || '',
                    };
                }
            }
        }
        return {
            statusCode: '200',
            message: 'global session document details',
            data: sessionContentDetails,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.createGlobalSessionDocumentDetails = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type } = query;
        const { content, url, programId } = body;
        let updateObj = {};
        if (type === DS_PROGRAM_KEY) {
            updateObj = {
                $push: {
                    programDocumentDetails: { content, url, programId, type },
                },
            };
        } else if (type === DS_INSTITUTION_KEY) {
            updateObj = {
                institutionDocumentDetails: { content, url, type },
            };
        }
        let response = {
            statusCode: '201',
            message: 'global session document created successfully',
        };
        const createdSessionDocumentDetails = await sessionSettingsModel.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            updateObj,
            { new: true },
        );
        if (!createdSessionDocumentDetails.modifiedCount) {
            response = {
                statusCode: '400',
                message: 'unable to create global session document',
            };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.listGlobalSessionDocumentDetails = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const allGlobalSessionDocument = await sessionSettingsModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                { institutionDocumentDetails: 1, programDocumentDetails: 1 },
            )
            .lean();
        return {
            statusCode: '200',
            message: 'all global session document details',
            data: allGlobalSessionDocument
                ? [
                      allGlobalSessionDocument.institutionDocumentDetails || {},
                      ...(allGlobalSessionDocument.programDocumentDetails || []),
                  ]
                : [],
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.updateGlobalSessionDocumentDetails = async ({ headers = {}, query = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, updateId } = query;
        const { content, url } = body;
        let findObj = { _institution_id: convertToMongoObjectId(_institution_id) };
        let updateObj = {};
        if (type === DS_PROGRAM_KEY) {
            findObj = {
                ...findObj,
                'programDocumentDetails._id': convertToMongoObjectId(updateId),
            };
            updateObj = {
                'programDocumentDetails.$.content': content,
                'programDocumentDetails.$.url': url,
            };
        } else if (type === DS_INSTITUTION_KEY) {
            updateObj = {
                'institutionDocumentDetails.content': content,
                'institutionDocumentDetails.url': url,
            };
        }
        let response = {
            statusCode: '201',
            message: 'global session document updated successfully',
        };
        const createdSessionDocumentDetails = await sessionSettingsModel.updateOne(
            findObj,
            updateObj,
        );
        if (!createdSessionDocumentDetails.modifiedCount) {
            response = {
                statusCode: '400',
                message: 'unable to update global session document',
            };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.deleteGlobalSessionDocumentDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, deleteId } = query;
        let deleteObj = {};
        if (type === DS_PROGRAM_KEY) {
            deleteObj = {
                $pull: { programDocumentDetails: { _id: convertToMongoObjectId(deleteId) } },
            };
        } else if (type === DS_INSTITUTION_KEY) {
            deleteObj = {
                $unset: { institutionDocumentDetails: 1 },
            };
        }
        let response = {
            statusCode: '200',
            message: 'global session document deleted successfully',
        };
        const createdSessionDocumentDetails = await sessionSettingsModel.updateOne(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            deleteObj,
        );
        if (!createdSessionDocumentDetails.modifiedCount) {
            response = {
                statusCode: '400',
                message: 'unable to delete global session document',
            };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

exports.getDisciplinaryRemarksVisibility = async () => {
    try {
        const globalSessionSettings = await sessionSettingsModel.findOne(
            {},
            {
                disciplinaryRemarks: 1,
            },
        );
        return {
            statusCode: 200,
            message: 'success',
            data: globalSessionSettings?.disciplinaryRemarks,
        };
    } catch (error) {
        return { statusCode: 500, message: error.message };
    }
};
exports.updateDisciplinaryRemarkVisibility = async ({ body = {} }) => {
    try {
        const { studentVisibility = false, commentVisibility = false } = body;
        await sessionSettingsModel.updateOne(
            {},
            {
                $set: {
                    disciplinaryRemarks: {
                        studentVisibility,
                        commentVisibility,
                    },
                },
            },
        );
        return {
            statusCode: 200,
            message: 'Updated successfully',
        };
    } catch (error) {
        return { statusCode: 500, message: error.message };
    }
};

exports.updateStudentFacial = async ({ query = {}, headers = {} }) => {
    try {
        const globalSettings = await sessionSettingsModel.findOneAndUpdate(
            {
                _institution_id: convertToMongoObjectId(headers._institution_id),
            },
            { $set: { 'basicDetails.studentFacial': query.isFacial } },
        );

        if (globalSettings) {
            return {
                statusCode: 200,
                message: 'UPDATED_SUCCESSFULLY',
            };
        }
    } catch (error) {
        if (error instanceof Error) throw error;
    }
};

exports.updateStudentSelfRegistrationDocument = async ({ query = {}, headers = {} }) => {
    try {
        const globalSettings = await sessionSettingsModel.findOneAndUpdate(
            {
                _institution_id: convertToMongoObjectId(headers._institution_id),
            },
            { $set: { 'basicDetails.selfRegistrationDocument': query.isSelfRegistration } },
        );

        if (globalSettings) {
            return {
                statusCode: 200,
                message: 'UPDATED_SUCCESSFULLY',
            };
        }
    } catch (error) {
        if (error instanceof Error) throw error;
    }
};
