const constant = require('../utility/constants');
const lms_review = require('mongoose').model(constant.LMS_REVIEW);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const lms = require('mongoose').model(constant.LMS);
const institution = require('mongoose').model(constant.INSTITUTION);
// const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const scheduleAttendanceReasons = require('../models/schedule_attendance_reason');
const ScheduleAttendanceModel = require('../models/schedule_attendance');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const disciplinaryRemarksSchema = require('../disciplinary_remarks/disciplinaryRemarks.model');
const {
    getWarningSettingBasedCalendar,
} = require('../lmsStudentSetting/lmsStudentSetting.controller');
const {
    response_function,
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
} = require('../utility/common');
const {
    get_list,
    get,
    get_list_populate,
    bulk_write,
    update_condition,
    update_condition_array_filter,
    dsGetAllWithSortAsJSON,
    get_list_sort,
} = require('../base/base_controller');
const {
    COURSE_SCHEDULE,
    DIGI_COURSE,
    DIGI_PROGRAM,
    EVENT_WHOM: { STAFF, STUDENT },
    NOTIFY_VIA: { MOBILE, EMAIL },
    WEEKDAYS,
    FULL_TIME,
    PART_TIME,
    COMPLETED,
    ABSENT,
    LEAVE,
    PRESENT,
    LEAVE_FLOW_TYPE: { APPROVE },
    LEAVE_FLOW_TYPE_STATUS: { REJECTED },
    ROLE,
    PRIMARY,
    PENDING,
    SCHEDULE_TYPES: { REGULAR, SUPPORT_SESSION, EVENT },
    LEAVE_TYPE: { PERMISSION, ONDUTY },
    DIGI_SESSION_DELIVERY_TYPES,
    DIGI_SESSION_ORDER,
    SESSION_MODE: { MANUAL },
    STUDENT_CRITERIA_MANIPULATION,
    STUDENT_GROUP,
    APPROVED,
    COURSE_WISE,
    EXCLUDE,
    LATE_CONFIG,
} = require('../utility/constants');
const { updateStudentGroupFlatCacheData } = require('../student_group/student_group_services');
const lmsStudentSetting = require('../lmsStudentSetting/lmsStudentSetting.model');
const lmsStudent = require('../lmsStudent/lmsStudent.model');
const { LMS_VERSION } = require('../utility/util_keys');
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const course = require('mongoose').model(DIGI_COURSE);
const role = require('mongoose').model(ROLE);
const program = require('mongoose').model(DIGI_PROGRAM);
const session_delivery_type = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const studentGroupCollection = require('mongoose').model(STUDENT_GROUP);
const courseScheduleCollection = require('mongoose').model(COURSE_SCHEDULE);
const {
    getLateAutoAndManualRange,
    getLateConfigAndStudentLateAbsent,
    getLateLabelForSchedule,
    getConfigAndStudentLateAbsentForSingleStudent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    updateStudentGroupRedisKey,
    calculateAttendancePercentages,
    filterAbsentSchedules,
} = require('../utility/utility.service');
const dateFormatter = (year, month, date, hours, minute, format) => {
    const dateFormat = new Date(
        year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ' ' + format,
    );
    return dateFormat;
};
const compareDate = function (a, b) {
    if (new Date(a.sort_date).getTime() < new Date(b.sort_date).getTime()) {
        return -1;
    }
    if (new Date(a.sort_date).getTime() > new Date(b.sort_date).getTime()) {
        return 1;
    }
    return 0;
};

const courseCreditContactHours = async (
    sessionDeliveryTypesData,
    courseScheduled,
    sessionOrderData,
    credit_hours,
    studentId,
) => {
    const credit_hoursData = [];
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        let sessionCompletedHours = 0;
        let endSchedule = 0;
        let scheduleSessionCount = 0;
        let attendanceCount = 0;
        if (deliveryTypeData.length) {
            // Altering Based on Scheduled Session SG wise
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    if (studentId) {
                        attendanceCount += sessionSchedule.filter(
                            (ele2) =>
                                ele2 &&
                                ele2.students &&
                                ele2.students.find(
                                    (ele) =>
                                        ele &&
                                        ele._id.toString() === studentId.toString() &&
                                        (ele.status === PRESENT || ele.status === ONDUTY),
                                ),
                        ).length;
                    }
                    endSchedule++;
                    sessionCompletedHours += sessionOrderElement.duration;
                }
            }
        }
        const sessionCompletedCredit = sessionCompletedHours !== 0 ? sessionCompletedHours / 60 : 0;
        const credit_hours_data =
            deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0;
        const completed_credit_hours =
            sessionCompletedCredit / parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours: credit_hours_data,
            completed_credit_hours,
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours:
                parseInt(
                    deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
                ) * parseInt(sessionTypeElement.contact_hour_per_credit_hour),
            no_of_sessions: scheduleSessionCount,
            completed_sessions: endSchedule,
            present_count: attendanceCount,
            completed_percentage: (
                (parseFloat(completed_credit_hours) / parseFloat(credit_hours_data)) *
                100
            ).toFixed(2),
        });
    }
    return credit_hoursData;
};

const studentAbsentCreditContactHours = async (
    sessionDeliveryTypesData,
    courseScheduled,
    _student_id,
) => {
    const credit_hoursData = [];
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        const absentSchedules = deliveryTypeData.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === _student_id.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE ||
                            ele2.status === PERMISSION),
                ),
        );
        const denialPercentage = (absentSchedules.length / deliveryTypeData.length) * 100;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            completed_schedule: deliveryTypeData.filter((ele) => ele.status === COMPLETED).length,
            completed_percentage: denialPercentage ? denialPercentage.toFixed(2) : '0.0',
        });
    }
    return credit_hoursData;
};
const studentAbsentCreditContactHoursForWarning = async (
    sessionDeliveryTypesData,
    courseScheduled,
    _student_id,
) => {
    const credit_hoursData = [];
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        const absentSchedules = deliveryTypeData.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === _student_id.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE ||
                            ele2.status === PERMISSION),
                ),
        );
        const denialPercentage =
            (absentSchedules.length /
                deliveryTypeData.filter((ele) => ele.status === COMPLETED).length) *
            100;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            completed_schedule: deliveryTypeData.filter((ele) => ele.status === COMPLETED).length,
            completed_percentage: denialPercentage ? denialPercentage.toFixed(2) : '0.0',
        });
    }
    return credit_hoursData;
};
const studentAbsentCreditContactHoursForAbsence = async (
    sessionDeliveryTypesData,
    courseScheduled,
    _student_id,
) => {
    const credit_hoursData = [];
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                            isActive: ele2.isActive,
                        };
                    }),
            ];
        }
        const absentSchedules = deliveryTypeData.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === _student_id.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE ||
                            ele2.status === PERMISSION),
                ),
        );
        const denialPercentage =
            (absentSchedules.length /
                deliveryTypeData.filter((ele) => ele.isActive === true).length) *
            100;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            completed_schedule: deliveryTypeData.filter((ele) => ele.status === COMPLETED).length,
            completed_percentage: denialPercentage ? denialPercentage.toFixed(2) : '0.0',
        });
    }
    return credit_hoursData;
};

exports.students_register = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term, gender },
            query: { rotationNo },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const studentGroupData = await get(
            student_group,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses': 1,
                'groups.students.name': 1,
                'groups.students._student_id': 1,
                'groups.students.academic_no': 1,
                'groups.students.gender': 1,
            },
        );
        if (!studentGroupData.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('STUDENT_GROUP_NOT_FOUND'), []));
        const groupData = studentGroupData.data.groups.find(
            (ele) => ele.term === term && ele.level === levelNo,
        );
        if (!groupData)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('YEAR_LEVEL_NOT_FOUND'), []));
        let studentData = groupData.students;
        const courseData = groupData.courses.find(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );

        if (!courseData)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const courseGenderGroupsData = courseData.setting.filter((ele) =>
            rotationNo
                ? parseInt(ele._group_no) === parseInt(rotationNo) && ele.gender === gender
                : ele.gender === gender,
        );
        let courseStudents = [];
        for (courseGenderData of courseGenderGroupsData) {
            if (
                courseGenderData &&
                courseGenderData &&
                courseGenderData.session_setting[0] &&
                courseGenderData.session_setting[0].groups
            )
                for (settingElement of courseGenderData.session_setting[0].groups) {
                    courseStudents = [...courseStudents, ...settingElement._student_ids];
                }
        }
        if (courseStudents.length === 0)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('STUDENT_RECORDS_NOT_FOUND'), []));
        //Course Schedule
        const scheduleData = await get_list(
            course_schedule,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                type: REGULAR,
                term,
                'students._id': { $in: courseStudents },
            },
            {
                _id: 1,
                students: 1,
                status: 1,
                'session._session_id': 1,
                'session.session_type': 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];

        //Leave
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (!leave_category.status)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('NO_DATA_FOUND'),
                        req.t('NO_DATA_FOUND'),
                    ),
                );
        const leave_type = leave_category.data.category.filter(
            (ele) => ele.isDeleted === false && ele.category_to === STUDENT,
        );

        leave_category.data.student_warning_absence_calculation = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
        let absencePercentage = 0;
        if (
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0
        ) {
            const denialData = leave_category.data.student_warning_absence_calculation.findIndex(
                (ele) => ele.warning === 'Denial',
            );
            if (denialData !== -1) {
                absencePercentage =
                    leave_category.data.student_warning_absence_calculation[denialData]
                        .absence_percentage;

                if (
                    courseData.student_absence_percentage &&
                    courseData.student_absence_percentage !== 0
                ) {
                    absencePercentage = courseData.student_absence_percentage;
                    leave_category.data.student_warning_absence_calculation[
                        denialData
                    ].absence_percentage = absencePercentage;
                }
            }
        }
        let warningAbsenceData = leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        );
        warningAbsenceData = warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        if (courseData.student_absence_percentage && courseData.student_absence_percentage !== 0)
            absencePercentage = courseData.student_absence_percentage;
        const leave_list = await get_list(
            lms_review,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                user_type: STUDENT,
                // type: { $in: leave_type },
            },
            {
                leave_category: 1,
                _user_id: 1,
                type: 1,
                leave_type: 1,
                days: 1,
            },
        );
        leave_list.data = leave_list.status ? leave_list.data : [];

        // const courseDetails = await get(
        //     course,
        //     {
        //         isDeleted: false,
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _id: convertToMongoObjectId(courseId),
        //     },
        //     {
        //         'credit_hours._id': 1,
        //         'credit_hours._session_id': 1,
        //         'credit_hours.credit_hours': 1,
        //         'credit_hours.type_name': 1,
        //         'credit_hours.contact_hours': 1,
        //         'credit_hours.type_symbol': 1,
        //     },
        // );

        const sessionDeliveryTypes = await get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
            },
            {},
        );

        // const sessionOrder = await get(
        //     session_order,
        //     {
        //         isDeleted: false,
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _program_id: convertToMongoObjectId(programId),
        //         _course_id: convertToMongoObjectId(courseId),
        //     },
        //     {
        //         _course_id: 1,
        //         'session_flow_data._id': 1,
        //         'session_flow_data.duration': 1,
        //     },
        // );

        studentData = clone(
            studentData.filter((ele) =>
                courseStudents.find((ele2) => ele2.toString() === ele._student_id.toString()),
            ),
        );
        for (studentElement of studentData) {
            // Schedule Datas
            const studentSchedule = scheduleData.data.filter(
                (ele) =>
                    ele &&
                    ele.students.find(
                        (ele2) => ele2._id.toString() === studentElement._student_id.toString(),
                    ),
            );
            const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
            const absentSchedules = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE ||
                            ele2.status === PERMISSION) /*  ||
                            ele2.status === 'onduty' */,
                ),
            );
            const presentSchedules = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        (ele2.status === PRESENT || ele2.status === ONDUTY),
                ),
            );
            studentElement.session_attended = presentSchedules.length;
            studentElement.session_conducted = completedSchedule.length;
            studentElement.total_session = studentSchedule.length;
            studentElement.absence_session = absentSchedules.length;
            const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
            studentElement.absence = denialPercentage.toFixed(2);
            const warningData = warningAbsenceData.find(
                (ele) =>
                    ele.absence_percentage &&
                    parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
            );
            studentElement.warning = warningData ? warningData.warning : '';
            // Credit Hours Calculation
            const creditHoursData = await studentAbsentCreditContactHours(
                sessionDeliveryTypes.data,
                studentSchedule,
                studentElement._student_id.toString(),
            );
            studentElement.credit_hours = creditHoursData.sort((a, b) => {
                let comparison = 0;
                if (a.type_symbol > b.type_symbol) {
                    comparison = -1;
                } else if (a.type_symbol < b.type_symbol) {
                    comparison = 1;
                }
                return comparison;
            });

            // Leave Datas
            const leaveData = [];
            const studentLeave = leave_list.data.filter(
                (ele) => ele._user_id.toString() === studentElement._student_id.toString(),
            );
            const leaveCategoryDatas = [...new Set(leave_type.map((ele) => ele.category_type))];
            for (levelCategory of leaveCategoryDatas) {
                if (levelCategory === PERMISSION)
                    leaveData.push({
                        type: PERMISSION,
                        count: studentLeave.filter((ele) => ele.type === PERMISSION).length,
                    });
                else {
                    const leaveDatas = leave_type.filter(
                        (ele) => ele.category_type === levelCategory,
                    );
                    for (levelElement of leaveDatas)
                        for (levelTypeElement of levelElement.leave_type) {
                            if (
                                levelTypeElement.isDeleted === false &&
                                levelTypeElement.isActive === true
                            ) {
                                const lmsTypeData = studentLeave.filter(
                                    (ele) =>
                                        ele &&
                                        ele.leave_type &&
                                        ele.leave_type._leave_type_id &&
                                        ele.leave_type._leave_type_id.toString() ===
                                            levelTypeElement._id.toString(),
                                );
                                let lmsTypeDataDays = 0;
                                if (lmsTypeData.length !== 0)
                                    lmsTypeDataDays = lmsTypeData
                                        .map((ele) => ele.days)
                                        .reduce((accumulator, current) => accumulator + current);
                                leaveData.push({
                                    type: levelTypeElement.type_name,
                                    count: lmsTypeDataDays,
                                });
                            }
                        }
                }
            }
            studentElement.leave_types = leaveData;
        }
        const response = {
            absence_percentage: absencePercentage,
            students: studentData,
        };
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('STUDENT_REGISTER'), response));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
exports.reason_update = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { schedule_id, student_id },
            body: { status, reason, updated_by },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const scheduleData = await get(
            course_schedule,
            {
                _id: convertToMongoObjectId(schedule_id),
            },
            { _id: 1, merge_status: 1, merge_with: 1 },
        );

        if (!scheduleData.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSE_SCHEDULE_NOT_FOUND'),
                        req.t('COURSE_SCHEDULE_NOT_FOUND'),
                    ),
                );
        const createReason = await scheduleAttendanceReasons.create({
            reason,
            status,
            staffs: updated_by,
        });
        if (scheduleData.data.merge_status) {
            //Update Student attendance all merge data
            const scheduleIds = scheduleData.data.merge_with.map((ele) => ele.schedule_id);
            scheduleIds.push(schedule_id);
            const bulk = [];
            for (id of scheduleIds) {
                bulk.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(id),
                        },
                        update: {
                            $set: {
                                'students.$[i].status': status,
                                'students.$[i].primaryStatus': status,
                                'students.$[i].mode': MANUAL,
                                'students.$[i].reason': reason,
                                'students.$[i].updated_by': updated_by,
                                'students.$[i].time': Date.now(),
                            },
                            $push: {
                                'students.$[i].reasonIds': { _id: createReason._id },
                            },
                        },
                        arrayFilters: [{ 'i._id': convertToMongoObjectId(student_id) }],
                    },
                });
            }
            //return res.send(bulk);
            if (bulk.length > 0) await bulk_write(course_schedule, bulk);
        } else {
            const query = { _id: convertToMongoObjectId(schedule_id) };
            const obj = {
                $set: {
                    'students.$[i].status': status,
                    'students.$[i].primaryStatus': status,
                    'students.$[i].mode': MANUAL,
                    'students.$[i].reason': reason,
                    'students.$[i].updated_by': updated_by,
                    'students.$[i].time': Date.now(),
                },
                $push: {
                    'students.$[i].reasonIds': { _id: createReason._id },
                },
            };
            const filter = {
                arrayFilters: [{ 'i._id': convertToMongoObjectId(student_id) }],
            };
            const doc = await update_condition_array_filter(course_schedule, query, obj, filter);
            if (!doc.status)
                return res
                    .status(404)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('FAILED_TO_UPDATE'),
                            req.t('FAILED_TO_UPDATE'),
                        ),
                    );
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    req.t('UPDATED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.students_attendance_sheet = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term, gender, user_id },
            query: { rotationCount },
        } = req;
        console.time('studentCriteriaData');
        let studentCriteriaData = await get(studentCriteriaCollection, {
            institutionCalendarId,
            programId,
            courseId,
            // yearNo,
            levelNo,
            term,
            rotationCount,
            studentId: convertToMongoObjectId(user_id),
        });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount,
            lateExcludeManagement,
        });
        if (!studentCriteriaData.status) studentCriteriaData.data = {};
        if (LMS_VERSION === 'V2') {
            studentCriteriaData = await lmsDenialSchema
                .find({
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    programId,
                    courseId,
                    levelNo,
                    term,
                    ...(rotationCount && rotationCount > 0 && { rotationCount }),
                    isActive: true,
                    isDeleted: false,
                })
                .sort({ updatedAt: -1 })
                .lean();
            const isMixedStudent =
                studentCriteriaData.length &&
                studentCriteriaData.find(
                    (denialStudentElement) =>
                        ((rotationCount && rotationCount > 0
                            ? parseInt(rotationCount) === denialStudentElement.rotationCount
                            : true) &&
                            denialStudentElement.term === term &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                        (denialStudentElement.term === term &&
                            denialStudentElement.studentId &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.studentId.toString() === user_id.toString()),
                );
            studentCriteriaData = isMixedStudent;
        }

        console.timeEnd('studentCriteriaData');
        console.time('studentGroupData');
        const studentGroupData = await get(
            studentGroupCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term.toString(),
                'groups.level': levelNo.toString(),
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses._course_id': 1,
                'groups.courses.student_absence_percentage': 1,
            },
        );
        console.timeEnd('studentGroupData');
        if (!studentGroupData.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        [],
                    ),
                );
        const groupData = studentGroupData.data.groups.findIndex(
            (ele) => ele.term === term && ele.level.toString() === levelNo.toString(),
        );
        if (groupData === -1)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, true, req.t('YEAR_LEVEL_NOT_FOUND'), []),
                );
        const courseData = studentGroupData.data.groups[groupData].courses.findIndex(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        if (courseData === -1)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        let denialLabel;
        //V2 is based on new lms settings
        if (LMS_VERSION === 'V2') {
            //Leave
            const leave_category_doc = await lmsStudentSetting
                .find(
                    {
                        isDeleted: false,
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    { warningConfig: 1, classificationType: 1 },
                )
                .lean();
            if (leave_category_doc.length) {
                const leaveIndex = leave_category_doc.findIndex(
                    (categoryElement) => categoryElement.classificationType === 'leave',
                );
                const warningConfigForCalendar = await getWarningSettingBasedCalendar({
                    _institution_calendar_id: institutionCalendarId,
                });
                if (warningConfigForCalendar && warningConfigForCalendar.length) {
                    leave_category_doc[leaveIndex].warningConfig = warningConfigForCalendar;
                }
            }
            let leave_category = leave_category_doc.find((doc) => {
                if (doc.classificationType === LEAVE) {
                    return doc;
                }
            });
            if (!leave_category)
                leave_category = {
                    warningConfig: [
                        {
                            isActive: false,
                            labelName: 'Denial',
                            percentage: 101,
                            message: 'Denial Warning',
                            colorCode: '',
                        },
                    ],
                    finaleWarning: '',
                    denialWarning: 'Denial',
                    denialLabel: 'Denial',
                    leaveCalculation: 'sessions',
                };
            denialLabel =
                leave_category.warningConfig[leave_category.warningConfig.length - 1].labelName;
            leave_category.warningConfig = clone(
                leave_category.warningConfig.filter(
                    (warningElement) => warningElement.isActive === true,
                ),
            );
            let absencePercentage = 0;
            let adminRestrictedAttendance = false;
            if (leave_category.warningConfig && leave_category.warningConfig.length !== 0) {
                const denialData = leave_category.warningConfig.findIndex(
                    (warningElement) => warningElement.labelName === denialLabel,
                );
                if (denialData !== -1) {
                    adminRestrictedAttendance =
                        leave_category.warningConfig[denialData].adminRestrictedAttendance || false;
                    absencePercentage = leave_category.warningConfig[denialData].percentage;
                    if (studentCriteriaData) {
                        absencePercentage = studentCriteriaData.absencePercentage;
                        leave_category.warningConfig[denialData].percentage =
                            studentCriteriaData.absencePercentage;
                    }
                }
            }
            let warningAbsenceData = leave_category.warningConfig.filter(
                (warningElement) => warningElement.isActive === true,
            );
            warningAbsenceData = warningAbsenceData.sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            let manipulationStatus = false;
            if (
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage &&
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage !== 0
            ) {
                absencePercentage =
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage;
            }
            if (studentCriteriaData && studentCriteriaData.absencePercentage) {
                absencePercentage = studentCriteriaData.absencePercentage;
                manipulationStatus = true;
            }
            if (
                absencePercentage &&
                warningAbsenceData.length &&
                warningAbsenceData[0].percentage &&
                warningAbsenceData[0].percentage < absencePercentage
            )
                warningAbsenceData[0].percentage = absencePercentage;

            //Course Schedule
            const sort = { schedule_date: 1 };
            console.time('courseScheduleData');
            const scheduleQuery = {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                type: REGULAR,
                term,
            };
            if (rotationCount && rotationCount !== 0) scheduleQuery.rotation_count = rotationCount;
            const courseScheduleData = await course_schedule
                .find(scheduleQuery, {
                    status: 1,
                    students: 1,
                    merge_status: 1,
                    merge_with: 1,
                    _id: 1,
                    staffs: 1,
                    mode: 1,
                    start: 1,
                    schedule_date: 1,
                    session: 1,
                    sessionDetail: 1,
                    scheduleEndDateAndTime: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleStartFrom: 1,
                    isMissedToComplete: 1,
                    lateConfigRange: 1,
                    _program_id: 1,
                    _course_id: 1,
                    _institution_calendar_id: 1,
                    term: 1,
                    level_no: 1,
                    rotation_count: 1,
                    classModeType: 1,
                })
                .populate({
                    path: 'students.reasonIds._id',
                    select: { staffs: 1, reason: 1, status: 1, createdAt: 1 },
                })
                .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
                .sort(sort)
                .lean();
            console.timeEnd('courseScheduleData');
            const scheduleData = {
                data: courseScheduleData.filter(
                    (scheduleElement) =>
                        scheduleElement.status === COMPLETED &&
                        scheduleElement.students.find(
                            (studentIdElement) =>
                                studentIdElement._id.toString() === user_id.toString(),
                        ),
                ),
            };
            const studentSchedule = scheduleData.data.filter((scheduleElement) => {
                const studentScheduleWithoutExclude = scheduleElement.students.find(
                    (studentIdElement) => studentIdElement._id.toString() === user_id.toString(),
                );
                return (
                    studentScheduleWithoutExclude &&
                    studentScheduleWithoutExclude.status !== EXCLUDE
                );
            });
            const studentTotalScheduleForWarningAbsence = courseScheduleData.filter(
                (scheduleElement) => {
                    const studentScheduleWithoutExclude = scheduleElement.students.find(
                        (studentIdElement) =>
                            studentIdElement._id.toString() === user_id.toString(),
                    );
                    return (
                        studentScheduleWithoutExclude &&
                        studentScheduleWithoutExclude.status !== EXCLUDE
                    );
                },
            );
            let normalSchedules = [];
            if (scheduleData.data.length > 0) {
                //Merge Schedule handle
                const mergedSchedules = clone(
                    scheduleData.data.filter(
                        (scheduleElement) => scheduleElement.merge_status === true,
                    ),
                );
                mergedSchedules.forEach((eleMS, indexMS) => {
                    eleMS.merge_with.forEach((eleMW, indexMW) => {
                        const mergedLoc = mergedSchedules.findIndex(
                            (scheduleElement) =>
                                scheduleElement._id.toString() === eleMW.schedule_id.toString(),
                        );
                        if (mergedLoc !== -1) {
                            if (!mergedSchedules[indexMS].merge_sessions)
                                mergedSchedules[indexMS].merge_sessions = [];
                            mergedSchedules[indexMS].merge_sessions.push({
                                session: mergedSchedules[mergedLoc].session,
                                student_groups: mergedSchedules[mergedLoc].student_groups,
                            });
                            mergedSchedules.splice(mergedLoc, 1);
                        } else {
                            const csInd = courseScheduleData.findIndex(
                                (scheduleElement) =>
                                    scheduleElement._id.toString() === eleMW.schedule_id.toString(),
                            );
                            if (csInd !== -1) {
                                if (!mergedSchedules[indexMS].merge_sessions)
                                    mergedSchedules[indexMS].merge_sessions = [];
                                mergedSchedules[indexMS].merge_sessions.push({
                                    session: courseScheduleData[csInd].session,
                                    student_groups: courseScheduleData[csInd].student_groups,
                                });
                            }
                        }
                    });
                });
                normalSchedules = clone([
                    ...clone(
                        scheduleData.data.filter(
                            (scheduleElement) => scheduleElement.merge_status === false,
                        ),
                    ),
                    ...mergedSchedules,
                ]);
            }
            const countDeliveryType = (schedule, student_data, sessionTypeArr) => {
                const sessionTypeInd = sessionTypeArr.findIndex(
                    (eleST) =>
                        eleST.session_type && eleST.session_type === schedule.session.session_type,
                );
                if (sessionTypeInd == -1 && student_data.status !== EXCLUDE) {
                    let completed = 0;
                    if (schedule.status === COMPLETED) completed = 1;

                    sessionTypeArr.push({
                        session_type: schedule.session.session_type,
                        delivery_symbol: schedule.session.delivery_symbol,
                        total: 1,
                        completed,
                        attendance_present_count:
                            student_data.status === PRESENT ||
                            student_data.status === ONDUTY ||
                            student_data.status === PERMISSION
                                ? 1
                                : 0,
                    });
                } else if (student_data.status !== EXCLUDE) {
                    sessionTypeArr[sessionTypeInd].total += 1;
                    if (schedule.status === COMPLETED)
                        sessionTypeArr[sessionTypeInd].completed += 1;
                    if (
                        student_data.status === PRESENT ||
                        student_data.status === ONDUTY ||
                        student_data.status === PERMISSION
                    )
                        sessionTypeArr[sessionTypeInd].attendance_present_count += 1;
                }
                return sessionTypeArr;
            };
            const studentSessions = [];
            let sessionType = [];
            let studentName;
            let staffNames = [];
            let session_wise = false;
            let course_wise = false;
            normalSchedules.forEach((eleSD) => {
                const student_data = eleSD.students.find(
                    (scheduleElement) =>
                        scheduleElement._id &&
                        scheduleElement._id.toString() === user_id.toString(),
                );
                studentName = { _id: student_data._id, studentName: student_data.name };
                const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo,
                    term,
                    rotationCount,
                    lateExcludeManagement,
                    studentId: user_id,
                }).lateExclude;
                if (lateExcludeForStudent && !course_wise) {
                    course_wise = true;
                }
                let lateLabel = null;
                if (!lateExclude && !lateExcludeForStudent) {
                    const { lateLabel: updateLateLabel } = getLateLabelForSchedule({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        schedule: eleSD,
                        student_data,
                        _institution_calendar_id: institutionCalendarId,
                        courseId,
                        programId,
                        levelNo,
                        term,
                        rotationCount,
                        lateExcludeManagement,
                    });
                    lateLabel = updateLateLabel;
                }
                delete student_data.name;
                if (student_data.updated_by) {
                    staffNames.push({
                        _id: student_data.updated_by._staff_id,
                        staffName: student_data.updated_by.staff_name,
                    });
                    delete student_data.updated_by.staff_name;
                }
                eleSD.staffs.forEach((element) => {
                    staffNames.push({ _id: element._staff_id, staffName: element.staff_name });
                    delete element.staff_name;
                    delete element._id;
                });
                if (student_data.status) {
                    sessionType = clone(countDeliveryType(eleSD, student_data, sessionType));
                    if (eleSD.merge_status) {
                        for (const sch_id of eleSD.merge_with) {
                            const scheduleData = courseScheduleData.find(
                                (eleCSD) => eleCSD._id.toString() === sch_id.schedule_id.toString(),
                            );
                            if (
                                !scheduleData ||
                                !scheduleData.students ||
                                !scheduleData.students.length
                            ) {
                                continue;
                            }
                            const merge_student_data = scheduleData.students.find(
                                (scheduleElement) =>
                                    scheduleElement._id &&
                                    scheduleElement._id.toString() === user_id.toString(),
                            );
                            if (merge_student_data && merge_student_data._id)
                                sessionType = clone(
                                    countDeliveryType(
                                        scheduleData,
                                        merge_student_data,
                                        sessionType,
                                    ),
                                );
                        }
                    }
                    //Merge session concat
                    let delivery_type = eleSD.session.delivery_symbol + eleSD.session.delivery_no;
                    if (eleSD.merge_sessions && eleSD.merge_sessions.length > 0) {
                        eleSD.merge_sessions.forEach((eleMS) => {
                            delivery_type +=
                                ',' + eleMS.session.delivery_symbol + eleMS.session.delivery_no;
                        });
                    }
                    const lateExcludeForStudentSession =
                        checkLateExcludeConfigurationForCourseOrStudent({
                            _institution_calendar_id: institutionCalendarId,
                            courseId,
                            programId,
                            levelNo,
                            term,
                            rotationCount,
                            lateExcludeManagement,
                            studentId: user_id,
                            sessionId: eleSD.session._session_id,
                        }).lateExclude;
                    if (lateExcludeForStudentSession && !session_wise) {
                        session_wise = true;
                    }
                    studentSessions.push({
                        _id: eleSD._id,
                        session: delivery_type,
                        s_no: eleSD.session.s_no,
                        sessionId: eleSD.session._session_id,
                        schedule_date: eleSD.schedule_date,
                        lateConfigRange: eleSD.lateConfigRange
                            ? eleSD.lateConfigRange
                            : 'undefined',
                        start: eleSD.start,
                        mode: eleSD.mode,
                        merge_status: eleSD.merge_status,
                        merge_with: eleSD.merge_status === true ? eleSD.merge_with : [],
                        staffs: eleSD.staffs,
                        students: student_data,
                        reasonForLate: student_data.reasonForLate,
                        attendance_status: student_data.status,
                        status: eleSD.status,
                        sessionDetail: eleSD.sessionDetail,
                        scheduleEndDateAndTime: eleSD.scheduleEndDateAndTime,
                        scheduleStartDateAndTime: eleSD.scheduleStartDateAndTime,
                        scheduleStartFrom: eleSD.scheduleStartFrom,
                        isMissedToComplete:
                            typeof eleSD.isMissedToComplete !== 'undefined'
                                ? eleSD.isMissedToComplete
                                : false,
                        lateLabel: lateLabel ? lateLabel.lateLabel : null,
                        lateExcludeForStudentSession,
                        classModeType: eleSD.classModeType,
                    });
                }
            });
            studentSessions.forEach((eleSS, indexSS) => {
                const scheduling_date = new Date(eleSS.schedule_date);
                studentSessions[indexSS].sort_date = dateFormatter(
                    scheduling_date.getFullYear(),
                    scheduling_date.getMonth() + 1,
                    scheduling_date.getDate(),
                    eleSS.start.hour,
                    eleSS.start.minute,
                    eleSS.start.format,
                );
            });
            const sortedStudentSessions = studentSessions.sort(compareDate);
            //Completed Status
            let total_session_completed = 0;
            sortedStudentSessions.forEach((scheduleElement) => {
                if (scheduleElement.status === COMPLETED) total_session_completed++;
                if (scheduleElement.merge_status) {
                    scheduleElement.merge_with.forEach((eleMW) => {
                        const ind = courseScheduleData.findIndex(
                            (eleCSD) =>
                                eleCSD._id.toString() === eleMW.schedule_id.toString() &&
                                eleCSD.status === COMPLETED,
                        );
                        if (ind != -1) total_session_completed++;
                    });
                }
            });
            //PRESENT STATUS
            const disciplinaryRemarksData = await disciplinaryRemarksSchema
                .find(
                    {
                        institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                        studentId: convertToMongoObjectId(user_id),
                        type: constant.DISCIPLINARY_REMARKS_TYPE.SCHEDULE_LEVEL,
                        isDeleted: false,
                        isActive: true,
                    },
                    { scheduleId: 1, tardisId: 1 },
                )
                .populate({ path: 'tardisId', select: { name: 1, short_code: 1 } });

            let totalPresent = 0;
            let totalAbsent = 0;
            let totalLeave = 0;
            let totalPermission = 0;
            let totalOnDuty = 0;
            let totalCompletedSchedule = 0;

            sortedStudentSessions.forEach((scheduleElement) => {
                if (scheduleElement.status === COMPLETED) {
                    totalCompletedSchedule++;
                }

                let currentStatus = scheduleElement.attendance_status;

                // Handle merged schedules
                if (scheduleElement.merge_status) {
                    scheduleElement.merge_with.forEach((eleMW) => {
                        const ind = courseScheduleData.findIndex(
                            (eleCSD) => eleCSD._id.toString() === eleMW.schedule_id.toString(),
                        );
                        if (ind != -1) {
                            if (
                                courseScheduleData[ind].students &&
                                courseScheduleData[ind].students.length
                            ) {
                                const merge_schedule_student_data = courseScheduleData[
                                    ind
                                ].students.find(
                                    (scheduleElement) =>
                                        scheduleElement._id &&
                                        scheduleElement._id.toString() === user_id.toString(),
                                );
                                if (
                                    merge_schedule_student_data &&
                                    merge_schedule_student_data.status
                                ) {
                                    currentStatus = merge_schedule_student_data.status;
                                }
                            }
                        }
                    });
                }

                // Count by status
                switch (currentStatus) {
                    case PRESENT:
                        totalPresent++;
                        break;
                    case ABSENT:
                        totalAbsent++;
                        break;
                    case LEAVE:
                        totalLeave++;
                        break;
                    case PERMISSION:
                        totalPermission++;
                        break;
                    case ONDUTY:
                        totalOnDuty++;
                        break;
                    default:
                        totalAbsent++;
                }

                const tardisDetails = disciplinaryRemarksData.find(
                    (remarkElement) =>
                        remarkElement.scheduleId.toString() === scheduleElement._id.toString(),
                );
                if (tardisDetails) {
                    scheduleElement.students.tardisId = tardisDetails.tardisId;
                }
            });
            const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: institutionCalendarId,
                courseId,
                programId,
                levelNo,
                term,
                rotationCount,
                lateExcludeManagement,
                studentId:
                    sortedStudentSessions && sortedStudentSessions.length
                        ? sortedStudentSessions[0]?.students?._id
                        : null,
            }).lateExclude;
            let studentLateAbsent = 0;
            let lateConfig = {};
            if (!lateExcludeForStudent && !lateExclude) {
                const { studentLateAbsent: updateStudentLateAbsent, lateConfig: updateLateConfig } =
                    getConfigAndStudentLateAbsentForSingleStudent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        sortedStudentSessions,
                        _institution_calendar_id: institutionCalendarId,
                        courseId,
                        programId,
                        levelNo,
                        term,
                        rotationCount,
                        lateExcludeManagement,
                    });
                studentLateAbsent = updateStudentLateAbsent;
                lateConfig = updateLateConfig;
            }
            // Use the reusable function for attendance percentage calculations
            const { presentPercentage, absentPercentage, warningPercentage } =
                calculateAttendancePercentages({
                    totalPresent,
                    totalAbsent,
                    totalLeave,
                    totalPermission,
                    totalOnDuty,
                    totalCompletedSchedule,
                    totalSchedules: studentSchedule.length,
                    studentLateAbsent,
                });

            const student_attendance_details = {
                total_session_completed: studentSchedule.length,
                present: totalPresent,
                percentage: presentPercentage,
            };
            const warningData = warningAbsenceData.find(
                (warningElement) =>
                    warningElement.percentage &&
                    parseFloat(warningPercentage) > parseFloat(warningElement.percentage),
            );
            staffNames = [
                ...new Map(
                    staffNames.map((staffElement) => [staffElement._id.toString(), staffElement]),
                ).values(),
            ]; // getting unique staff names
            const staffIds = staffNames.map((staffId) => staffId._id);

            const staffLists = await user.find({ _id: { $in: staffIds } }, { user_id: 1 });

            staffNames.forEach((staffs) => {
                const getAcademicIds = staffLists.find(
                    (staffId) => staffId._id.toString() === staffs._id.toString(),
                );
                staffs.academicId = getAcademicIds.user_id ? getAcademicIds.user_id : '';
            });

            const responseData = {
                absencePercentage,
                manipulationStatus,
                warning: warningData ? warningData.labelName : '',
                adminRestrictedAttendance,
                studentName,
                staffNames,
                studentSessions: sortedStudentSessions,
                sessionType,
                student_attendance_details,
                denialLabel,
                lateConfig,
                studentLateAbsent,
                session_wise,
                course_wise,
            };
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STUDENT_ATTENDANCE_REGISTER'),
                        responseData,
                    ),
                );
        }
        //Leave
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (!leave_category.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('NO_DATA_FOUND'),
                        req.t('NO_DATA_FOUND'),
                    ),
                );

        const leave_type = leave_category.data.category.filter(
            (ele) => ele.isDeleted === false && ele.category_to === STUDENT,
        );
        leave_category.data.student_warning_absence_calculation = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
        let absencePercentage = 0;
        if (
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0
        ) {
            const denialData = leave_category.data.student_warning_absence_calculation.findIndex(
                (ele) => ele.warning === 'Denial',
            );
            if (denialData !== -1) {
                absencePercentage =
                    leave_category.data.student_warning_absence_calculation[denialData]
                        .absence_percentage;

                if (
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage &&
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage !== 0
                ) {
                    absencePercentage =
                        studentGroupData.data.groups[groupData].courses[courseData]
                            .student_absence_percentage;
                    leave_category.data.student_warning_absence_calculation[
                        denialData
                    ].absence_percentage = absencePercentage;
                }
            }
        }
        let warningAbsenceData = leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        );
        warningAbsenceData = warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        if (warningAbsenceData[1] && warningAbsenceData[1].warning)
            warningAbsenceData[1].warning = 'Final Warning';
        let manipulationStatus = false;
        if (
            studentGroupData.data.groups[groupData].courses[courseData]
                .student_absence_percentage &&
            studentGroupData.data.groups[groupData].courses[courseData]
                .student_absence_percentage !== 0
        ) {
            absencePercentage =
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage;
        }
        if (studentCriteriaData.data && studentCriteriaData.data.absencePercentage) {
            absencePercentage = studentCriteriaData.data.absencePercentage;
            manipulationStatus = true;
        }
        if (
            absencePercentage &&
            warningAbsenceData.length &&
            warningAbsenceData[0].absence_percentage &&
            warningAbsenceData[0].absence_percentage < absencePercentage
        )
            warningAbsenceData[0].absence_percentage = absencePercentage;

        //Course Schedule
        const sort = { schedule_date: 1 };
        console.time('courseScheduleData');
        const scheduleQuery = {
            isDeleted: false,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            level_no: levelNo,
            type: REGULAR,
            term,
        };
        if (rotationCount && rotationCount !== 0) scheduleQuery.rotation_count = rotationCount;
        const courseScheduleData = await course_schedule
            .find(scheduleQuery, {
                status: 1,
                students: 1,
                merge_status: 1,
                merge_with: 1,
                _id: 1,
                staffs: 1,
                mode: 1,
                start: 1,
                schedule_date: 1,
                session: 1,
                sessionDetail: 1,
                scheduleEndDateAndTime: 1,
                scheduleStartDateAndTime: 1,
                isMissedToComplete: 1,
            })
            .populate({
                path: 'students.reasonIds._id',
                select: { staffs: 1, reason: 1, status: 1, createdAt: 1 },
            })
            .sort(sort)
            .lean();
        console.timeEnd('courseScheduleData');
        const scheduleData = {
            data: courseScheduleData.filter(
                (scheduleElement) =>
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students.find(
                        (studentIdElement) =>
                            studentIdElement._id.toString() === user_id.toString(),
                    ),
            ),
        };
        const studentSchedule = scheduleData.data.filter((scheduleElement) => {
            const studentScheduleWithoutExclude = scheduleElement.students.find(
                (studentIdElement) => studentIdElement._id.toString() === user_id.toString(),
            );
            return (
                studentScheduleWithoutExclude && studentScheduleWithoutExclude.status !== EXCLUDE
            );
        });
        let normalSchedules = [];
        if (scheduleData.data.length > 0) {
            //Merge Schedule handle
            const mergedSchedules = clone(
                scheduleData.data.filter((ele) => ele.merge_status === true),
            );

            mergedSchedules.forEach((eleMS, indexMS) => {
                eleMS.merge_with.forEach((eleMW, indexMW) => {
                    const mergedLoc = mergedSchedules.findIndex(
                        (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                    );
                    if (mergedLoc !== -1) {
                        if (!mergedSchedules[indexMS].merge_sessions)
                            mergedSchedules[indexMS].merge_sessions = [];
                        mergedSchedules[indexMS].merge_sessions.push({
                            session: mergedSchedules[mergedLoc].session,
                            student_groups: mergedSchedules[mergedLoc].student_groups,
                        });
                        mergedSchedules.splice(mergedLoc, 1);
                    } else {
                        const csInd = courseScheduleData.findIndex(
                            (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                        );
                        if (csInd !== -1) {
                            if (!mergedSchedules[indexMS].merge_sessions)
                                mergedSchedules[indexMS].merge_sessions = [];
                            mergedSchedules[indexMS].merge_sessions.push({
                                session: courseScheduleData[csInd].session,
                                student_groups: courseScheduleData[csInd].student_groups,
                            });
                        }
                    }
                });
            });
            normalSchedules = clone([
                ...clone(scheduleData.data.filter((ele) => ele.merge_status === false)),
                ...mergedSchedules,
            ]);
        }

        const countDeliveryType = (schedule, student_data, sessionTypeArr) => {
            const sessionTypeInd = sessionTypeArr.findIndex(
                (eleST) =>
                    eleST.session_type && eleST.session_type === schedule.session.session_type,
            );
            if (sessionTypeInd == -1 && student_data.status !== EXCLUDE) {
                let completed = 0;
                if (schedule.status === COMPLETED) completed = 1;

                sessionTypeArr.push({
                    session_type: schedule.session.session_type,
                    delivery_symbol: schedule.session.delivery_symbol,
                    total: 1,
                    completed,
                    attendance_present_count:
                        student_data.status === PRESENT || student_data.status === ONDUTY ? 1 : 0,
                });
            } else if (student_data.status !== EXCLUDE) {
                sessionTypeArr[sessionTypeInd].total += 1;
                if (schedule.status === COMPLETED) sessionTypeArr[sessionTypeInd].completed += 1;
                if (student_data.status === PRESENT || student_data.status === ONDUTY)
                    sessionTypeArr[sessionTypeInd].attendance_present_count += 1;
            }
            return sessionTypeArr;
        };

        const studentSessions = [];
        let sessionType = [];
        let studentName;
        let staffNames = [];
        normalSchedules.forEach((eleSD) => {
            const student_data = eleSD.students.find(
                (ele) => ele._id && ele._id.toString() === user_id.toString(),
            );
            studentName = { _id: student_data._id, studentName: student_data.name };
            delete student_data.name;
            if (student_data.updated_by) {
                staffNames.push({
                    _id: student_data.updated_by._staff_id,
                    staffName: student_data.updated_by.staff_name,
                });
                delete student_data.updated_by.staff_name;
            }
            eleSD.staffs.forEach((element) => {
                staffNames.push({ _id: element._staff_id, staffName: element.staff_name });
                delete element.staff_name;
                delete element._id;
            });
            if (student_data.status) {
                sessionType = clone(countDeliveryType(eleSD, student_data, sessionType));
                if (eleSD.merge_status) {
                    for (const sch_id of eleSD.merge_with) {
                        const scheduleData = courseScheduleData.find(
                            (eleCSD) => eleCSD._id.toString() === sch_id.schedule_id.toString(),
                        );
                        if (scheduleData.students.length == 0) continue;
                        const merge_student_data = scheduleData.students.find(
                            (ele) => ele._id && ele._id.toString() === user_id.toString(),
                        );
                        if (merge_student_data && merge_student_data._id)
                            sessionType = clone(
                                countDeliveryType(scheduleData, merge_student_data, sessionType),
                            );
                    }
                }
                //Merge session concat
                let delivery_type = eleSD.session.delivery_symbol + eleSD.session.delivery_no;
                if (eleSD.merge_sessions && eleSD.merge_sessions.length > 0) {
                    eleSD.merge_sessions.forEach((eleMS) => {
                        delivery_type +=
                            ',' + eleMS.session.delivery_symbol + eleMS.session.delivery_no;
                    });
                }

                studentSessions.push({
                    _id: eleSD._id,
                    session: delivery_type,
                    s_no: eleSD.session.s_no,
                    schedule_date: eleSD.schedule_date,
                    start: eleSD.start,
                    mode: eleSD.mode,
                    merge_status: eleSD.merge_status,
                    merge_with: eleSD.merge_status === true ? eleSD.merge_with : [],
                    staffs: eleSD.staffs,
                    students: student_data,
                    attendance_status: student_data.status,
                    status: eleSD.status,
                    sessionDetail: eleSD.sessionDetail,
                    scheduleEndDateAndTime: eleSD.scheduleEndDateAndTime,
                    scheduleStartDateAndTime: eleSD.scheduleStartDateAndTime,
                    isMissedToComplete:
                        typeof eleSD.isMissedToComplete !== 'undefined'
                            ? eleSD.isMissedToComplete
                            : false,
                });
            }
        });
        studentSessions.forEach((eleSS, indexSS) => {
            const scheduling_date = new Date(eleSS.schedule_date);
            studentSessions[indexSS].sort_date = dateFormatter(
                scheduling_date.getFullYear(),
                scheduling_date.getMonth() + 1,
                scheduling_date.getDate(),
                eleSS.start.hour,
                eleSS.start.minute,
                eleSS.start.format,
            );
        });
        const sortedStudentSessions = studentSessions.sort(compareDate);
        //Completed Status
        let total_session_completed = 0;
        sortedStudentSessions.forEach((ele) => {
            if (ele.status === COMPLETED) total_session_completed++;
            if (ele.merge_status) {
                ele.merge_with.forEach((eleMW) => {
                    const ind = courseScheduleData.findIndex(
                        (eleCSD) =>
                            eleCSD._id.toString() === eleMW.schedule_id.toString() &&
                            eleCSD.status === COMPLETED,
                    );
                    if (ind != -1) total_session_completed++;
                });
            }
        });
        //ATTENDANCE STATUS COUNTING
        let totalPresent = 0;
        let totalAbsent = 0;
        let totalLeave = 0;
        let totalPermission = 0;
        let totalOnDuty = 0;
        let totalCompletedSchedule = 0;

        sortedStudentSessions.forEach((ele) => {
            if (ele.status === COMPLETED) {
                totalCompletedSchedule++;
            }

            let currentStatus = ele.attendance_status;

            // Handle merged schedules
            if (ele.merge_status) {
                ele.merge_with.forEach((eleMW) => {
                    const ind = courseScheduleData.findIndex(
                        (eleCSD) => eleCSD._id.toString() === eleMW.schedule_id.toString(),
                    );
                    if (ind != -1) {
                        if (
                            courseScheduleData[ind].students &&
                            courseScheduleData[ind].students.length
                        ) {
                            const merge_schedule_student_data = courseScheduleData[
                                ind
                            ].students.find(
                                (ele) => ele._id && ele._id.toString() === user_id.toString(),
                            );
                            if (merge_schedule_student_data && merge_schedule_student_data.status) {
                                currentStatus = merge_schedule_student_data.status;
                            }
                        }
                    }
                });
            }

            // Count by status
            switch (currentStatus) {
                case PRESENT:
                    totalPresent++;
                    break;
                case ABSENT:
                    totalAbsent++;
                    break;
                case LEAVE:
                    totalLeave++;
                    break;
                case PERMISSION:
                    totalPermission++;
                    break;
                case ONDUTY:
                    totalOnDuty++;
                    break;
                default:
                    totalAbsent++;
            }
        });

        // Use the reusable function for attendance percentage calculations
        const { presentPercentage, absentPercentage, warningPercentage } =
            calculateAttendancePercentages({
                totalPresent,
                totalAbsent,
                totalLeave,
                totalPermission,
                totalOnDuty,
                totalCompletedSchedule,
                totalSchedules: studentSchedule.length,
                studentLateAbsent: 0, // No late absent calculation in this section
            });

        const student_attendance_details = {
            total_session_completed: studentSchedule.length,
            present: totalPresent,
            percentage: presentPercentage,
        };
        const warningData = warningAbsenceData.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(percentage) > parseFloat(ele.absence_percentage),
        );
        staffNames = [
            ...new Map(staffNames.map((element) => [element._id.toString(), element])).values(),
        ]; // getting unique staff names
        const staffIds = staffNames.map((staffId) => staffId._id);

        const staffLists = await user.find({ _id: { $in: staffIds } }, { user_id: 1 });

        staffNames.forEach((staffs) => {
            const getAcademicIds = staffLists.find(
                (staffId) => staffId._id.toString() === staffs._id.toString(),
            );
            staffs.academicId = getAcademicIds.user_id ? getAcademicIds.user_id : '';
        });

        const responseData = {
            absencePercentage,
            manipulationStatus,
            warning: warningData ? warningData.warning : '',
            studentName,
            staffNames,
            studentSessions: sortedStudentSessions,
            sessionType,
            student_attendance_details,
        };
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_ATTENDANCE_REGISTER'),
                    responseData,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.program_course_list = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, userId, roleId },
        } = req;

        const role_assign_list = await get(
            role_assign,
            {
                _user_id: convertToMongoObjectId(userId),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            {},
        );
        if (!role_assign_list.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USERS_ROLE_NOT_FOUND'),
                        req.t('USERS_ROLE_NOT_FOUND'),
                    ),
                );
        const roleData = role_assign_list.data.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        let programIds = [];
        const courseCoordinatorData = [];
        if (roleData.role_name === 'Course Coordinator') {
            const CourseData = await get_list(
                course,
                {
                    'coordinators._user_id': convertToMongoObjectId(userId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _id: 1,
                    _program_id: 1,
                    coordinators: 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            );
            if (!CourseData.status)
                return res
                    .status(404)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('COURSE_NOT_FOUND'),
                            req.t('COURSE_NOT_FOUND'),
                        ),
                    );
            for (courseElement of CourseData.data) {
                programIds.push(courseElement._program_id.toString());
                const userCourseData = courseElement.coordinators.filter(
                    (coordinatorElement) =>
                        coordinatorElement._user_id.toString() === userId.toString(),
                );
                for (userElement of userCourseData)
                    courseCoordinatorData.push({
                        courseId: courseElement._id,
                        programId: courseElement._program_id,
                        term: userElement.term,
                        year: userElement.year,
                        level_no: userElement.level_no,
                        versionNo: courseElement.versionNo || 1,
                        versioned: courseElement.versioned || false,
                        versionName: courseElement.versionName || '',
                        versionedFrom: courseElement.versionedFrom || null,
                        versionedCourseIds: courseElement.versionedCourseIds || [],
                    });
            }
        } else {
            programIds = roleData.program.map((ele) => ele._program_id.toString());
        }
        programIds = [...new Set(programIds)];
        const program_list = await get_list(
            program,
            {
                _id: { $in: programIds },
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            { name: 1, code: 1, program_type: 1, term: 1 },
        );
        if (!program_list.status)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));

        //Credit Hours Get in Program Calendar
        const requiredPopulatedKeys = {
            versionedCourseIds: 1,
            versionNo: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
        };
        let programCalendar = await program_calendar
            .find(
                {
                    _program_id: { $in: programIds },
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _program_id: 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.rotation': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                },
            )
            .populate([
                {
                    path: 'level.course._course_id',
                    select: requiredPopulatedKeys,
                },
                {
                    path: 'level.rotation_course.course._course_id',
                    select: requiredPopulatedKeys,
                },
            ])
            .lean();
        program_list.data = program_list.status ? clone(program_list.data) : [];
        programCalendar = programCalendar && programCalendar.length ? clone(programCalendar) : [];
        for (programElement of program_list.data) {
            const programCalendarData = programCalendar.find(
                (ele) => ele._program_id.toString() === programElement._id.toString(),
            );
            if (programCalendarData) {
                const levelData = [];
                const termData = [];
                if (roleData.role_name === 'Course Coordinator') {
                    for (levelElement of programCalendarData.level) {
                        const coordinatorCourses = courseCoordinatorData.filter(
                            (courseElement) =>
                                courseElement.programId.toString() ===
                                    programCalendarData._program_id.toString() &&
                                courseElement.term.toString() === levelElement.term.toString() &&
                                courseElement.year.toString() === levelElement.year.toString() &&
                                courseElement.level_no.toString() ===
                                    levelElement.level_no.toString(),
                        );
                        if (coordinatorCourses.length) {
                            let levelCourse = [];
                            const rotationCourse = [];
                            if (levelElement.rotation === 'no')
                                levelCourse = levelElement.course.filter((courseElement) =>
                                    coordinatorCourses.find(
                                        (coordinatorCourseElement) =>
                                            coordinatorCourseElement.courseId.toString() ===
                                            courseElement._course_id._id.toString(),
                                    ),
                                );
                            else {
                                for (rotationElement of levelElement.rotation_course) {
                                    const courseList = rotationElement.course.filter(
                                        (courseElement) =>
                                            coordinatorCourses.find(
                                                (coordinatorCourseElement) =>
                                                    coordinatorCourseElement.courseId.toString() ===
                                                    courseElement._course_id._id.toString(),
                                            ),
                                    );
                                    if (courseList.length)
                                        rotationCourse.push({
                                            rotation_count: rotationElement.rotation_count,
                                            course: courseList,
                                        });
                                }
                            }
                            levelData.push({
                                term: levelElement.term,
                                year: levelElement.year,
                                level_no: levelElement.level_no,
                                rotation: levelElement.rotation,
                                course: levelCourse,
                                rotation_course: rotationCourse,
                            });
                        }
                        if (
                            termData.findIndex(
                                (termElement) =>
                                    termElement.toString() === levelElement.term.toString(),
                            ) === -1
                        )
                            termData.push(levelElement.term);
                    }
                    programElement.term = programElement.term.filter((termElement) =>
                        termData.find(
                            (levelTermElement) =>
                                levelTermElement.toLowerCase() ===
                                termElement.term_name.toLowerCase(),
                        ),
                    );
                    programElement.level = levelData;
                } else programElement.level = programCalendarData.level;
            }
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), program_list));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.updateStudentAbsencePercentage = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                _program_id,
                _course_id,
                level_no,
                term,
                absence_percentage,
            },
        } = req;
        const institution_check = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            'master._program_id': convertToMongoObjectId(_program_id),
            'groups.term': term,
            'groups.level': level_no,
            'groups.courses._course_id': convertToMongoObjectId(_course_id),
        };
        const objs = {
            $set: {
                'groups.$[i].courses.$[j].student_absence_percentage': absence_percentage,
            },
        };
        const filter = {
            arrayFilters: [
                {
                    'i.term': term,
                    'i.level': level_no,
                },
                { 'j._course_id': convertToMongoObjectId(_course_id) },
            ],
        };
        const { status: updateStatus } = await update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: _course_id,
            level: level_no,
            batch: term,
        });
        if (!updateStatus)
            return res
                .status(410)
                .send(
                    response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_UPDATE_ABSENCE_PERCENTAGE'),
                        req.t('UNABLE_TO_UPDATE_ABSENCE_PERCENTAGE'),
                    ),
                );
        return res
            .status(200)
            .send(
                response_function(
                    res,
                    200,
                    true,
                    req.t('ABSENCE_PERCENTAGE_UPDATED_SUCCESSFULLY'),
                    req.t('ABSENCE_PERCENTAGE_UPDATED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

exports.criteriaManipulatedStudentList = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: {
                institutionCalendarId,
                programId,
                courseId,
                levelNo,
                term,
                gender,
                absencePercentage,
            },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const studentGroupData = await get(
            student_group,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses': 1,
                'groups.students.name': 1,
                'groups.students._student_id': 1,
                'groups.students.academic_no': 1,
                'groups.students.gender': 1,
            },
        );
        if (!studentGroupData.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('STUDENT_GROUP_NOT_FOUND'), []));
        const groupData = studentGroupData.data.groups.find(
            (ele) => ele.term === term && ele.level === levelNo,
        );
        if (!groupData)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('YEAR_LEVEL_NOT_FOUND'), []));
        let studentData = groupData.students;
        const courseData = groupData.courses.find(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );

        if (!courseData)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const courseGenderData = courseData.setting.find((ele) => ele.gender === gender);
        let courseStudents = [];
        if (
            courseGenderData &&
            courseGenderData &&
            courseGenderData.session_setting[0] &&
            courseGenderData.session_setting[0].groups
        )
            for (settingElement of courseGenderData.session_setting[0].groups) {
                courseStudents = [...courseStudents, ...settingElement._student_ids];
            }

        if (courseStudents.length === 0)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('STUDENT_RECORDS_NOT_FOUND'), []));
        //Course Schedule
        const scheduleData = await get_list(
            course_schedule,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                type: REGULAR,
                term,
                'students._id': { $in: courseStudents },
            },
            {
                _id: 1,
                students: 1,
                status: 1,
                'session._session_id': 1,
                'session.session_type': 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];

        studentData = clone(
            studentData.filter((ele) =>
                courseStudents.find((ele2) => ele2.toString() === ele._student_id.toString()),
            ),
        );

        //Warning calc
        //Leave
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (!leave_category.status)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('NO_DATA_FOUND'),
                        req.t('NO_DATA_FOUND'),
                    ),
                );

        leave_category.data.student_warning_absence_calculation = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
        let absencePercentageN = 0;
        if (
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0
        ) {
            const denialData = leave_category.data.student_warning_absence_calculation.findIndex(
                (ele) => ele.warning === 'Denial',
            );
            if (denialData !== -1) {
                absencePercentageN =
                    leave_category.data.student_warning_absence_calculation[denialData]
                        .absence_percentage;

                if (
                    courseData.student_absence_percentage &&
                    courseData.student_absence_percentage !== 0
                ) {
                    absencePercentageN = courseData.student_absence_percentage;
                    leave_category.data.student_warning_absence_calculation[
                        denialData
                    ].absence_percentage = absencePercentageN;
                }
            }
        }
        let warningAbsenceData = leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        );
        warningAbsenceData = warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        });

        //////

        const denialStudents = [];
        for (studentElement of studentData) {
            // Schedule Datas
            const studentSchedule = scheduleData.data.filter(
                (ele) =>
                    ele &&
                    ele.students.find(
                        (ele2) => ele2._id.toString() === studentElement._student_id.toString(),
                    ),
            );
            const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
            const absentSchedules = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        (ele2.status === ABSENT || ele2.status === LEAVE),
                ),
            );
            studentElement.session_attended = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        ele2.status === PRESENT,
                ),
            ).length;
            studentElement.session_conducted = completedSchedule.length;
            studentElement.total_session = studentSchedule.length;
            // studentElement.absence = absentSchedules.length;
            const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
            studentElement.absence = denialPercentage.toFixed(2);
            if (parseFloat(denialPercentage) >= parseFloat(absencePercentage))
                denialStudents.push(studentElement);

            //Warning
            const warningData = warningAbsenceData.find(
                (ele) =>
                    ele.absence_percentage &&
                    parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
            );
            studentElement.warning = warningData ? warningData.warning : '';
            /////
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('STUDENT_REGISTER'), denialStudents));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
exports.leave_list_web = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, userId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('INSTITUTION_NOT_FOUND'), []));

        const query_user = {
            isDeleted: false,
            isActive: true,
            user_type: 'student',
        };
        let user_list = await get_list(user, query_user, { _id: 1, name: 1 });
        user_list = user_list.status ? user_list.data : [];

        //Leave List
        const query = {
            isDeleted: false,
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            _user_id: userId,
        };
        const doc = await get_list(lms_review, query, {
            leave_category: 1,
            leave_type: 1,
            approved_by: 1,
            _leave_reason_doc: 1,
            application_date: 1,
            reason: 1,
            from: 1,
            to: 1,
            days: 1,
            type: 1,
            level_approvers: 1,
        });
        if (!doc.status)
            return res.status(200).send(response_function(res, 200, true, req.t('LEAVE_LIST'), []));
        const leave_list = clone(doc.data);
        leave_list.forEach((eleLL, indexLL) => {
            const userData = user_list.find(
                (eleUL) => eleUL._id.toString() === eleLL.approved_by._person_id.toString(),
            );

            if (userData) leave_list[indexLL].approved_by.name = userData.name;
            else leave_list[indexLL].approved_by.name = '';
        });
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('LEAVE_LIST'), leave_list));
    } catch (error) {
        return res.status(200).send(response_function(res, 200, true, error.toString(), []));
    }
};
exports.course_students_register = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term, gender },
            query: { rotationNo, groupIds, sessionGroupIds, isDeliveryGroup },
        } = req;
        console.time('studentGroupData');
        const isDeliveryChange = isDeliveryGroup === 'true';
        let groupIdsArray = [];
        if (groupIds && isDeliveryChange) {
            try {
                if (Array.isArray(groupIds)) {
                    groupIdsArray = groupIds;
                } else {
                    groupIdsArray = JSON.parse(groupIds);
                }
            } catch (error) {
                groupIdsArray = [groupIds];
            }
        }

        let sessionGroupIdsArray = [];
        if (sessionGroupIds && isDeliveryChange) {
            try {
                if (Array.isArray(sessionGroupIds)) {
                    sessionGroupIdsArray = sessionGroupIds;
                } else {
                    sessionGroupIdsArray = JSON.parse(sessionGroupIds);
                }
            } catch (error) {
                sessionGroupIdsArray = [sessionGroupIds];
            }
        }

        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount: rotationNo,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo,
            term,
            rotationCount: rotationNo,
            lateExcludeManagement,
        });
        const studentGroupData = await get(
            studentGroupCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term.toString(),
                'groups.level': levelNo.toString(),
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses': 1,
                'groups.students.name': 1,
                'groups.students._student_id': 1,
                'groups.students.academic_no': 1,
                'groups.students.gender': 1,
            },
        );
        console.timeEnd('studentGroupData');
        if (!studentGroupData.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        [],
                    ),
                );
        const groupData = studentGroupData.data.groups.findIndex(
            (ele) => ele.term === term && ele.level.toString() === levelNo.toString(),
        );
        if (groupData === -1)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, true, req.t('YEAR_LEVEL_NOT_FOUND'), []),
                );
        let studentData = clone(studentGroupData.data.groups[groupData].students);
        const courseData = studentGroupData.data.groups[groupData].courses.findIndex(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        if (courseData === -1)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const courseGenderData = studentGroupData.data.groups[groupData].courses[
            courseData
        ].setting.filter((settingElement) => {
            if (isDeliveryChange && !rotationNo) {
                return true;
            }
            const isGroupIdMatch = groupIdsArray
                .map((groupElement) => groupElement.toString())
                .includes(settingElement._id.toString());

            const isRotationMatch = rotationNo
                ? parseInt(settingElement._group_no) === parseInt(rotationNo)
                : true;

            if (isDeliveryChange) {
                return rotationNo ? isRotationMatch : isGroupIdMatch;
            }
            return rotationNo
                ? isRotationMatch && settingElement.gender === gender
                : settingElement.gender === gender;
        });

        let courseStudents = [];
        if (courseGenderData.length)
            for (courseSettingElement of courseGenderData)
                if (courseSettingElement && courseSettingElement.session_setting)
                    for (settingElement of courseSettingElement.session_setting) {
                        const filterGroups =
                            isDeliveryChange && sessionGroupIdsArray.length > 0
                                ? settingElement.groups.filter((groupElement) =>
                                      sessionGroupIdsArray
                                          .map((sessionGroupElement) =>
                                              sessionGroupElement.toString(),
                                          )
                                          .includes(groupElement._id.toString()),
                                  )
                                : settingElement.groups;
                        for (groupElement of filterGroups) {
                            courseStudents = [...courseStudents, ...groupElement._student_ids];
                        }
                    }
        courseStudents = [
            ...new Set(courseStudents.map((studentElement) => studentElement.toString())),
        ];
        if (courseStudents.length === 0)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STUDENT_RECORDS_NOT_FOUND'),
                        [],
                    ),
                );
        console.time('scheduleData');
        const scheduleData = await get_list(
            courseScheduleCollection,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                type: REGULAR,
                term,
                rotation_count: rotationNo,
                'students._id': {
                    $in: courseStudents.map((studentElement) =>
                        convertToMongoObjectId(studentElement),
                    ),
                },
            },
            {
                _id: 1,
                students: 1,
                status: 1,
                _course_id: 1,
                _program_id: 1,
                level_no: 1,
                term: 1,
                rotation_count: 1,
                isActive: 1,
                'session._session_id': 1,
                'session.session_type': 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                'sessionDetail.start_time': 1,
                'sessionDetail.end_time': 1,
                _institution_calendar_id: 1,
            },
        );
        if (!scheduleData.status) scheduleData.data = [];
        console.timeEnd('scheduleData');

        //Leave
        //V2 is based on new lms settings
        if (LMS_VERSION === 'V2') {
            //Leave
            const leave_category_doc = await lmsStudentSetting
                .find(
                    {
                        isDeleted: false,
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    { classificationType: 1, catagories: 1, warningConfig: 1 },
                )
                .lean();
            if (leave_category_doc.length) {
                const leaveIndex = leave_category_doc.findIndex(
                    (categoryElement) => categoryElement.classificationType === 'leave',
                );
                const warningConfigForCalendar = await getWarningSettingBasedCalendar({
                    _institution_calendar_id: institutionCalendarId,
                });
                if (warningConfigForCalendar && warningConfigForCalendar.length) {
                    leave_category_doc[leaveIndex].warningConfig = warningConfigForCalendar;
                }
            }
            // if (!leave_category_doc)
            //     return res
            //         .status(200)
            //         .send(
            //             response_function(
            //                 res,
            //                 200,
            //                 true,
            //                 req.t('NO_DATA_FOUND'),
            //                 req.t('NO_DATA_FOUND'),
            //             ),
            //         );
            let leave_category = leave_category_doc.find((categoryElement) => {
                if (categoryElement.classificationType === LEAVE) return categoryElement;
            });
            if (!leave_category)
                leave_category = {
                    warningConfig: [
                        {
                            isActive: false,
                            labelName: 'Denial',
                            percentage: 101,
                            message: 'Denial Warning',
                            colorCode: '',
                        },
                    ],
                    finaleWarning: '',
                    denialWarning: 'Denial',
                    denialLabel: 'Denial',
                    leaveCalculation: 'sessions',
                };

            const denialLabelName =
                leave_category.warningConfig[leave_category.warningConfig.length - 1].labelName;

            leave_category.warningConfig = clone(
                leave_category.warningConfig.filter((ele) => ele.isActive === true),
            );
            let absencePercentage = 0;
            if (leave_category.warningConfig && leave_category.warningConfig.length !== 0) {
                const warningConfig = leave_category.warningConfig;
                const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
                    let comparison = 0;
                    if (Number(a.percentage) > Number(b.percentage)) {
                        comparison = -1;
                    } else if (Number(a.percentage) < Number(b.percentage)) {
                        comparison = 1;
                    }
                    return comparison;
                });
                const denialData = leave_category.warningConfig.findIndex(
                    (ele) => ele.labelName === denialLabelName,
                );
                if (denialData !== -1) {
                    absencePercentage = leave_category.warningConfig[denialData].percentage;
                    if (
                        studentGroupData.data.groups[groupData].courses[courseData]
                            .student_absence_percentage &&
                        studentGroupData.data.groups[groupData].courses[courseData]
                            .student_absence_percentage !== 0
                    ) {
                        absencePercentage =
                            studentGroupData.data.groups[groupData].courses[courseData]
                                .student_absence_percentage;
                        leave_category.warningConfig[denialData].percentage = absencePercentage;
                    }
                }
            }
            let warningAbsenceData = leave_category.warningConfig.filter(
                (ele) => ele.isActive === true,
            );
            warningAbsenceData =
                warningAbsenceData && warningAbsenceData.length
                    ? warningAbsenceData.sort((a, b) => {
                          let comparison = 0;
                          if (parseInt(a.percentage) > parseInt(b.percentage)) {
                              comparison = -1;
                          } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                              comparison = 1;
                          }
                          return comparison;
                      })
                    : [
                          {
                              labelName: 'Denial',
                              percentage: 100,
                              message: 'Denial Warning',
                          },
                      ];
            let manipulationStatus = false;
            if (
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage &&
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage !== 0
            ) {
                absencePercentage =
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage;
                manipulationStatus = true;
            }
            //Session completed
            const completedScheduleOverall = scheduleData.data.filter(
                (scheduleElement) => scheduleElement && scheduleElement.status === COMPLETED,
            ).length;
            const totalScheduleOverall = scheduleData.data.filter(
                (scheduleElement) => scheduleElement && scheduleElement.isActive === true,
            ).length;
            const scheduleDates = scheduleData.data
                .filter(
                    (scheduleElement) => scheduleElement && scheduleElement.status === COMPLETED,
                )
                .map((scheduleElement) => {
                    return {
                        $and: [
                            {
                                'dateAndTimeRange.startDate': {
                                    $lte: new Date(scheduleElement.scheduleStartDateAndTime),
                                },
                            },
                            {
                                'dateAndTimeRange.endDate': {
                                    $gte: new Date(scheduleElement.scheduleEndDateAndTime),
                                },
                            },
                        ],
                    };
                });
            const leave_list = scheduleDates.length
                ? await lmsStudent
                      .find(
                          {
                              isDeleted: false,
                              _institution_id: convertToMongoObjectId(_institution_id),
                              _institution_calendar_id:
                                  convertToMongoObjectId(institutionCalendarId),
                              approvalStatus: APPROVED,
                              $or: scheduleDates,
                          },
                          {
                              categoryId: 1,
                              categoryName: 1,
                              studentId: 1,
                              typeId: 1,
                              typeName: 1,
                              classificationType: 1,
                              noOfHours: 1,
                          },
                      )
                      .lean()
                : [];
            const sessionDeliveryTypes = await get_list(
                session_delivery_type,
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                },
                {},
            );
            studentData = clone(
                studentData.filter((scheduleElement) =>
                    courseStudents.find(
                        (scheduleElementStudent) =>
                            scheduleElementStudent &&
                            scheduleElement._student_id &&
                            scheduleElementStudent.toString() ===
                                scheduleElement._student_id.toString(),
                    ),
                ),
            );
            console.time('studentCriteriaData');
            // const studentCriteriaData = await get_list(studentCriteriaCollection, {
            //     programId,
            //     courseId,
            //     // yearNo,
            //     levelNo,
            //     term,
            //     rotationCount: rotationNo,
            //     studentId: {
            //         $in: courseStudents.map((studentElement) =>
            //             convertToMongoObjectId(studentElement),
            //         ),
            //     },
            // });

            const studentCriteriaData = await lmsDenialSchema
                .find({
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    programId,
                    courseId,
                    levelNo,
                    term,
                    ...(rotationNo && rotationNo > 0 && { rotationCount: rotationNo }),
                    isActive: true,
                    isDeleted: false,
                })
                .sort({ updatedAt: -1 })
                .lean();

            console.timeEnd('studentCriteriaData');
            const denialManipulationForCourse = studentCriteriaData.find(
                (denialStudentElement) =>
                    (rotationNo && rotationNo > 0
                        ? parseInt(rotationNo) === denialStudentElement.rotationCount
                        : true) &&
                    denialStudentElement.term === term &&
                    denialStudentElement.courseId.toString() === courseId.toString() &&
                    denialStudentElement.typeWiseUpdate === COURSE_WISE,
            );
            if (denialManipulationForCourse && denialManipulationForCourse.absencePercentage)
                absencePercentage = denialManipulationForCourse.absencePercentage;
            for (studentElement of studentData) {
                const studentSchedule = scheduleData.data.filter((scheduleElement) => {
                    const studentDetailsWithExcludeStatus =
                        scheduleElement &&
                        scheduleElement.students.find(
                            (scheduleElementStudent) =>
                                scheduleElementStudent._id &&
                                studentElement._student_id &&
                                scheduleElementStudent._id.toString() ===
                                    studentElement._student_id.toString(),
                        );
                    return (
                        studentDetailsWithExcludeStatus &&
                        studentDetailsWithExcludeStatus.status !== EXCLUDE
                    );
                });
                const completedSchedule = studentSchedule.filter(
                    (scheduleElement) => scheduleElement.status === COMPLETED,
                );
                // Filter absent schedules using common utility function
                const absentSchedules = filterAbsentSchedules(
                    completedSchedule,
                    studentElement._student_id,
                );
                const presentSchedules = completedSchedule.filter((scheduleElement) =>
                    scheduleElement.students.find(
                        (scheduleElementStudent) =>
                            scheduleElementStudent._id &&
                            studentElement._student_id &&
                            scheduleElementStudent._id.toString() ===
                                studentElement._student_id.toString() &&
                            scheduleElementStudent.status === PRESENT /*  ||
                                scheduleElementStudent.status === ONDUTY ||
                                scheduleElementStudent.status === PERMISSION */,
                    ),
                );
                const presentSchedulesWithoutOnDuty = completedSchedule.filter((scheduleElement) =>
                    scheduleElement.students.find(
                        (scheduleElementStudent) =>
                            scheduleElementStudent._id &&
                            studentElement._student_id &&
                            scheduleElementStudent._id.toString() ===
                                studentElement._student_id.toString() &&
                            scheduleElementStudent.status === PRESENT,
                    ),
                );
                const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: institutionCalendarId,
                    courseId,
                    programId,
                    levelNo,
                    term,
                    rotationCount: rotationNo,
                    lateExcludeManagement,
                    studentId: studentElement._student_id,
                }).lateExclude;
                let lateConfig = {};
                let studentLateAbsent = 0;
                if (!lateExclude && !lateExcludeForStudent) {
                    const {
                        lateConfig: updatedLateConfig,
                        studentLateAbsent: updatedStudentLateAbsent,
                    } = getLateConfigAndStudentLateAbsent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        scheduleData: presentSchedulesWithoutOnDuty,
                        studentElement,
                        lateExcludeManagement,
                    });
                    lateConfig = updatedLateConfig;
                    studentLateAbsent = updatedStudentLateAbsent;
                }

                studentElement.session_attended = presentSchedulesWithoutOnDuty.length;
                studentElement.session_conducted = completedSchedule.length;
                studentElement.total_session = studentSchedule.length;
                studentElement.absence_session = absentSchedules.length;

                // Schedule Attendance Counts
                const scheduleStudentCount = {
                    present_count: 0,
                    absent_count: 0,
                    onduty_count: 0,
                    leave_count: 0,
                    permission_count: 0,
                    studentLateAbsent,
                };
                for (completedScheduleElement of completedSchedule) {
                    const scheduleStudent = completedScheduleElement.students.find(
                        (scheduleStudentElement) =>
                            scheduleStudentElement._id.toString() ===
                            studentElement._student_id.toString(),
                    );
                    if (scheduleStudent && scheduleStudent.status)
                        switch (scheduleStudent.status) {
                            case PRESENT:
                                scheduleStudentCount.present_count++;
                                break;
                            case ONDUTY:
                                scheduleStudentCount.onduty_count++;
                                break;
                            case PERMISSION:
                                scheduleStudentCount.permission_count++;
                                break;
                            case LEAVE:
                                scheduleStudentCount.leave_count++;
                                break;
                            case ABSENT:
                                scheduleStudentCount.absent_count++;
                                break;
                            default:
                                break;
                        }
                }
                studentElement.scheduleAttendance = scheduleStudentCount;
                //New changes
                const presentSchedulesLength = presentSchedules.length;
                const absentSchedulesLength = absentSchedules.length;
                const completedScheduleLength = completedSchedule.length;
                const totalScheduleLength = studentSchedule.length;

                // Use the reusable function for attendance percentage calculations
                const { presentPercentage, absentPercentage, warningPercentage } =
                    calculateAttendancePercentages({
                        totalPresent: presentSchedulesLength,
                        totalAbsent: absentSchedulesLength,
                        totalLeave: scheduleStudentCount.leave_count,
                        totalPermission: scheduleStudentCount.permission_count,
                        totalOnDuty: scheduleStudentCount.onduty_count,
                        totalCompletedSchedule: completedScheduleLength,
                        totalSchedules: totalScheduleLength,
                        studentLateAbsent,
                    });

                studentElement.attendance = presentPercentage;
                studentElement.absent = absentPercentage;
                studentElement.warning_absence = warningPercentage;
                studentElement.studentLateAbsent = studentLateAbsent;
                studentElement.lateConfig = lateConfig;
                //studentElement.total_leaves = { leave_types, total_days };
                const denialPercentage =
                    ((scheduleStudentCount.absent_count +
                        scheduleStudentCount.leave_count +
                        studentLateAbsent) /
                        totalScheduleLength) *
                    100;
                //studentElement.absence = denialPercentage.toFixed(2);
                const studentManipulation = studentCriteriaData.find(
                    (denialStudentElement) =>
                        ((rotationNo && rotationNo > 0
                            ? parseInt(rotationNo) === denialStudentElement.rotationCount
                            : true) &&
                            denialStudentElement.term === term &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                        (denialStudentElement.term === term &&
                            denialStudentElement.studentId &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.studentId.toString() ===
                                studentElement._student_id.toString()),
                );
                studentElement.manipulationStatus = false;
                studentElement.manipulationPercentage = 0;
                const studentWarningAbsence = clone(warningAbsenceData);
                if (
                    studentWarningAbsence[0].labelName === denialLabelName &&
                    studentManipulation &&
                    studentManipulation.absencePercentage &&
                    studentWarningAbsence[0] &&
                    studentWarningAbsence[0].percentage &&
                    studentWarningAbsence[0].percentage < studentManipulation.absencePercentage
                ) {
                    studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                    studentElement.manipulationStatus = true;
                    studentElement.manipulationPercentage = studentManipulation.absencePercentage;
                }
                const warningData = studentWarningAbsence.find(
                    (ele) =>
                        ele.percentage &&
                        parseFloat(warningPercentage) > parseFloat(ele.percentage),
                );
                studentElement.warning = warningData ? warningData.labelName : '';
                // Credit Hours Calculation Warning absence
                const creditHoursData = await studentAbsentCreditContactHoursForWarning(
                    sessionDeliveryTypes.data,
                    studentSchedule,
                    studentElement._student_id.toString(),
                );
                studentElement.credit_hours = creditHoursData.sort((a, b) => {
                    let comparison = 0;
                    if (a.type_symbol > b.type_symbol) {
                        comparison = -1;
                    } else if (a.type_symbol < b.type_symbol) {
                        comparison = 1;
                    }
                    return comparison;
                });

                // Credit Hours Calculation Absence
                const absenceData = await studentAbsentCreditContactHoursForAbsence(
                    sessionDeliveryTypes.data,
                    studentSchedule,
                    studentElement._student_id.toString(),
                );
                studentElement.absence = absenceData.sort((a, b) => {
                    let comparison = 0;
                    if (a.type_symbol > b.type_symbol) {
                        comparison = -1;
                    } else if (a.type_symbol < b.type_symbol) {
                        comparison = 1;
                    }
                    return comparison;
                });

                // Leave Datas
                const leaveData = [];
                const studentLeave = leave_list.filter(
                    (ele) =>
                        ele.studentId &&
                        ele.studentId.toString() === studentElement._student_id.toString(),
                );
                const leaveCategoryDatas = [
                    ...new Set(leave_category_doc.map((ele) => ele.classificationType)),
                ];
                for (levelCategory of leaveCategoryDatas) {
                    const leaveDatas = leave_category_doc.filter(
                        (leaveElement) => leaveElement.classificationType === levelCategory,
                    );
                    for (levelElement of leaveDatas) {
                        for (levelCategoryElement of levelElement.catagories) {
                            for (levelTypeElement of levelCategoryElement.types) {
                                const lmsTypeData = studentLeave.filter(
                                    (leaveElement) =>
                                        leaveElement &&
                                        leaveElement.typeId &&
                                        leaveElement.typeId.toString() ===
                                            levelTypeElement._id.toString(),
                                );
                                let lmsTypeDataDays = 0;
                                if (lmsTypeData.length !== 0)
                                    lmsTypeDataDays = lmsTypeData.length /* lmsTypeData
                                        .map((ele) => ele.noOfHours)
                                        .reduce((accumulator, current) => accumulator + current) */;
                                leaveData.push({
                                    category: levelCategoryElement.categoryName.trim(),
                                    type: levelTypeElement.typeName.trim(),
                                    count: lmsTypeDataDays,
                                });
                            }
                        }
                    }
                }
                studentElement.leave_types = leaveData;
            }
            const response = {
                absence_percentage: absencePercentage,
                manipulationStatus,
                overall_completed_schedule: completedScheduleOverall,
                overall_total_schedule: totalScheduleOverall,
                students: studentData,
                denialLabelName,
            };
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STUDENT_REGISTER'),
                        response,
                    ),
                );
        }
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (!leave_category.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('NO_DATA_FOUND'),
                        req.t('NO_DATA_FOUND'),
                    ),
                );

        const leave_type = leave_category.data.category.filter(
            (ele) => ele.isDeleted === false && ele.category_to === STUDENT,
        );

        leave_category.data.student_warning_absence_calculation = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
        let absencePercentage = 0;
        if (
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0
        ) {
            const denialData = leave_category.data.student_warning_absence_calculation.findIndex(
                (ele) => ele.warning === 'Denial',
            );
            if (denialData !== -1) {
                absencePercentage =
                    leave_category.data.student_warning_absence_calculation[denialData]
                        .absence_percentage;
                if (
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage &&
                    studentGroupData.data.groups[groupData].courses[courseData]
                        .student_absence_percentage !== 0
                ) {
                    absencePercentage =
                        studentGroupData.data.groups[groupData].courses[courseData]
                            .student_absence_percentage;
                    leave_category.data.student_warning_absence_calculation[
                        denialData
                    ].absence_percentage = absencePercentage;
                }
            }
        }
        let warningAbsenceData = leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        );
        warningAbsenceData = warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        if (warningAbsenceData[1] && warningAbsenceData[1].warning)
            warningAbsenceData[1].warning = 'Final Warning';
        let manipulationStatus = false;
        if (
            studentGroupData.data.groups[groupData].courses[courseData]
                .student_absence_percentage &&
            studentGroupData.data.groups[groupData].courses[courseData]
                .student_absence_percentage !== 0
        ) {
            absencePercentage =
                studentGroupData.data.groups[groupData].courses[courseData]
                    .student_absence_percentage;
            manipulationStatus = true;
        }
        //Session completed
        const completedScheduleOverall = scheduleData.data.filter(
            (ele) => ele && ele.status === COMPLETED,
        ).length;
        const totalScheduleOverall = scheduleData.data.filter(
            (ele) => ele && ele.isActive === true,
        ).length;
        const leave_list = await get_list(
            lms_review,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                user_type: STUDENT,
                // type: { $in: leave_type },
            },
            {
                leave_category: 1,
                _user_id: 1,
                type: 1,
                leave_type: 1,
                days: 1,
            },
        );
        leave_list.data = leave_list.status ? leave_list.data : [];
        const sessionDeliveryTypes = await get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
            },
            {},
        );
        studentData = clone(
            studentData.filter((ele) =>
                courseStudents.find((ele2) => ele2.toString() === ele._student_id.toString()),
            ),
        );
        console.time('studentCriteriaData');
        const studentCriteriaData = await get_list(studentCriteriaCollection, {
            programId,
            courseId,
            // yearNo,
            levelNo,
            term,
            rotationCount: rotationNo,
            studentId: {
                $in: courseStudents.map((studentElement) => convertToMongoObjectId(studentElement)),
            },
        });
        if (!studentCriteriaData.status) studentCriteriaData.data = [];
        console.timeEnd('studentCriteriaData');
        for (studentElement of studentData) {
            const studentSchedule = scheduleData.data.filter((scheduleElement) => {
                const studentDetailsWithExcludeStatus =
                    scheduleElement &&
                    scheduleElement.students.find(
                        (scheduleElementStudent) =>
                            scheduleElementStudent._id &&
                            studentElement._student_id &&
                            scheduleElementStudent._id.toString() ===
                                studentElement._student_id.toString(),
                    );
                return (
                    studentDetailsWithExcludeStatus &&
                    studentDetailsWithExcludeStatus.status !== EXCLUDE
                );
            });
            const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
            // Use common utility function to filter absent schedules
            const absentSchedules = filterAbsentSchedules(
                completedSchedule,
                studentElement._student_id,
                [ABSENT, LEAVE],
            );
            const presentSchedules = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        (ele2.status === PRESENT || ele2.status === ONDUTY),
                ),
            );
            studentElement.session_attended = presentSchedules.length;
            studentElement.session_conducted = completedSchedule.length;
            studentElement.total_session = studentSchedule.length;
            studentElement.absence_session = absentSchedules.length;
            //New changes
            // Use the reusable function for attendance percentage calculations
            const { presentPercentage, absentPercentage, warningPercentage } =
                calculateAttendancePercentages({
                    totalPresent: presentSchedules.length,
                    totalAbsent: absentSchedules.length,
                    totalLeave: 0, // This section doesn't have leave count tracking
                    totalPermission: 0, // This section doesn't have permission count tracking
                    totalOnDuty: 0, // This section doesn't have on duty count tracking
                    totalCompletedSchedule: completedSchedule.length,
                    totalSchedules: studentSchedule.length,
                    studentLateAbsent: 0, // This section doesn't have late absent tracking
                });

            studentElement.attendance = presentPercentage;
            studentElement.absent = absentPercentage;
            studentElement.warning_absence = warningPercentage;
            //

            //studentElement.total_leaves = { leave_types, total_days };
            // Use the same logic for denial percentage calculation
            const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
            //studentElement.absence = denialPercentage.toFixed(2);
            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement._student_id.toString(),
            );
            studentElement.manipulationStatus = false;
            studentElement.manipulationPercentage = 0;
            const studentWarningAbsence = clone(warningAbsenceData);
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].absence_percentage &&
                studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
                studentElement.manipulationStatus = true;
                studentElement.manipulationPercentage = studentManipulation.absencePercentage;
            }
            const warningData = studentWarningAbsence.find(
                (ele) =>
                    ele.absence_percentage &&
                    parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
            );
            studentElement.warning = warningData ? warningData.warning : '';
            // Credit Hours Calculation Warning absence
            const creditHoursData = await studentAbsentCreditContactHoursForWarning(
                sessionDeliveryTypes.data,
                studentSchedule,
                studentElement._student_id.toString(),
            );
            studentElement.credit_hours = creditHoursData.sort((a, b) => {
                let comparison = 0;
                if (a.type_symbol > b.type_symbol) {
                    comparison = -1;
                } else if (a.type_symbol < b.type_symbol) {
                    comparison = 1;
                }
                return comparison;
            });

            // Credit Hours Calculation Absence
            const absenceData = await studentAbsentCreditContactHoursForAbsence(
                sessionDeliveryTypes.data,
                studentSchedule,
                studentElement._student_id.toString(),
            );
            studentElement.absence = absenceData.sort((a, b) => {
                let comparison = 0;
                if (a.type_symbol > b.type_symbol) {
                    comparison = -1;
                } else if (a.type_symbol < b.type_symbol) {
                    comparison = 1;
                }
                return comparison;
            });

            // Leave Datas
            const leaveData = [];
            const studentLeave = leave_list.data.filter(
                (ele) => ele._user_id.toString() === studentElement._student_id.toString(),
            );
            const leaveCategoryDatas = [...new Set(leave_type.map((ele) => ele.category_type))];
            for (levelCategory of leaveCategoryDatas) {
                if (levelCategory === PERMISSION)
                    leaveData.push({
                        type: PERMISSION,
                        count: studentLeave.filter((ele) => ele.type === PERMISSION).length,
                    });
                else {
                    const leaveDatas = leave_type.filter(
                        (ele) => ele.category_type === levelCategory,
                    );
                    for (levelElement of leaveDatas)
                        for (levelTypeElement of levelElement.leave_type) {
                            if (
                                levelTypeElement.isDeleted === false &&
                                levelTypeElement.isActive === true
                            ) {
                                const lmsTypeData = studentLeave.filter(
                                    (ele) =>
                                        ele &&
                                        ele.leave_type &&
                                        ele.leave_type._leave_type_id &&
                                        ele.leave_type._leave_type_id.toString() ===
                                            levelTypeElement._id.toString(),
                                );
                                let lmsTypeDataDays = 0;
                                if (lmsTypeData.length !== 0)
                                    lmsTypeDataDays = lmsTypeData
                                        .map((ele) => ele.days)
                                        .reduce((accumulator, current) => accumulator + current);
                                leaveData.push({
                                    type: levelTypeElement.type_name,
                                    count: lmsTypeDataDays,
                                });
                            }
                        }
                }
            }
            studentElement.leave_types = leaveData;
        }
        const response = {
            absence_percentage: absencePercentage,
            manipulationStatus,
            overall_completed_schedule: completedScheduleOverall,
            overall_total_schedule: totalScheduleOverall,
            students: studentData,
        };
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('STUDENT_REGISTER'), response));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.studentAttendanceHistory = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { scheduleId, studentId },
        } = req;
        const scheduleAttendance = await ScheduleAttendanceModel.find(
            {
                scheduleId: { $in: [convertToMongoObjectId(scheduleId)] },
            },
            {
                scheduleId: 1,
                modeBy: 1,
                status: 1,
                'students.status': 1,
                'students.primaryStatus': 1,
                'students._student_id': 1,
                'students.time': 1,
                createdAt: 1,
            },
        ).lean();
        let courseScheduleData = await course_schedule
            .findOne(
                {
                    _id: convertToMongoObjectId(scheduleId),
                },
                { students: 1 },
            )
            .populate({
                path: 'students.reasonIds._id',
                select: { staffs: 1, reason: 1, status: 1, createdAt: 1 },
            })
            .lean();
        if (courseScheduleData && courseScheduleData.students) {
            courseScheduleData = courseScheduleData.students.find(
                (element) => element._id.toString() === studentId.toString(),
            );
        }
        for (const scheduleAttendanceList of scheduleAttendance) {
            scheduleAttendanceList.students = scheduleAttendanceList.students.find(
                (element) => element._student_id.toString() === studentId.toString(),
            );
        }
        return res.status(200).send(
            responseFunctionWithRequest(req, 200, true, req.t('STUDENT_ATTENDANCE_REGISTER'), {
                scheduleAttendance,
                courseScheduleData,
            }),
        );
    } catch (error) {
        console.log('error', error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
