let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let department_subject = new Schema({
    /* type: {
        type: String,
        require: true,
        enum: [constant.DEPARTMENT_MASTER.DEPARTMENT, constant.DEPARTMENT_MASTER.DIVISION]
    }, */
    master: {
        type: String,
        require: true,
        enum: [constant.DEPARTMENT_MASTER.DEPARTMENT, constant.DEPARTMENT_MASTER.DIVISION]
    },
    _master_id: {
        type: Schema.Types.ObjectId,
        required: true
    },
    title: {
        type: String
    },
    shared: Boolean,
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DEPARTMENT_SUBJECT, department_subject);