let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let institution = new Schema({
    event_calendar: {
        type: String,
        enum: [constant.CALENDAR.INSTITUTION, constant.CALENDAR.PROGRAM,constant.MODEL.COURSE],
        required: true
    },
    event_type: {
        type: String,
        enum: [constant.EVENT_TYPE.HOLIDAY, constant.EVENT_TYPE.ORIENTATION, constant.EVENT_TYPE.TRAINING, constant.EVENT_TYPE.EXAM, constant.EVENT_TYPE.GENERAL],
        required: true
    },
    event_whom: {
        type: String,
        enum: [constant.EVENT_WHOM.STAFF, constant.EVENT_WHOM.STUDENT, constant.EVENT_WHOM.BOTH]
    },
    event_gender: {
        type: String,
        enum: [constant.GENDER.MALE, constant.GENDER.FEMALE, constant.GENDER.BOTH]
    },
    event_name: {
        first_language: {
            type: String
        },
        second_language: {
            type: String
        }
    },
    event_description: {
        first_language: {
            type: String
        },
        second_language: {
            type: String
        }
    },
    event_date: {
        type: Date,
        required: true
    },
    start_time: {
        type: Date,
        required: true
    },
    end_time: {
        type: Date,
        required: true
    },
    end_date: {
        type: Date,
        required: true
    },
    _infrastructure_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INFRASTRUCTURE
    },
    _calendar_id: {
        type: Schema.Types.ObjectId,
        required: true,
        sparse: true
    },
    review: [{
        _reviewer_ids: {
            type: Schema.Types.ObjectId,
            ref: constant.STAFF
        },
        reviews: Boolean,
        reviewer_comment: String,
        dean_feedback: Boolean,
        dean_comment: String,
        expire: {
            expire_date: Date,
            expire_time: Date
        },
        status: String
    }],
    status: String,
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.CALENDAR_EVENT, institution);