const { convertToMongoObjectId, clone } = require('../utility/common');
const {
    PROGRAM_CALENDAR,
    COURSE_SCHEDULE,
    ONGOING,
    COMPLETED,
    PENDING,
    MISSED,
    ABSENT,
    PRESENT,
    DS_PENDING,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../utility/constants');
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const programSchema = require('mongoose').model(DIGI_PROGRAM);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const getDateWiseAttendanceReport = async ({
    _institution_id,
    sessionDate,
    programIds,
    institutionCalendarId,
    start,
    end,
    term,
    year,
    levelNo,
    courseId,
    rotationCount,
    sessionStatus,
    courseCoordinatorFilter,
}) => {
    try {
        const scheduleQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            ...(!end && { schedule_date: new Date(sessionDate) }),
            // schedule_date: new Date(sessionDate),
            ...(courseCoordinatorFilter.length && { $or: courseCoordinatorFilter }),
            ...(programIds && { _program_id: { $in: programIds } }),
            ...(institutionCalendarId &&
                !courseCoordinatorFilter.length && {
                    _institution_calendar_id: { $in: institutionCalendarId },
                }),
            ...(term && { term }),
            ...(year && { year_no: year }),
            ...(levelNo && { level_no: levelNo }),
            ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
            ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
        };
        // if (end)
        //     scheduleQuery.$and = [
        //         {
        //             'start.hour': { $gte: start },
        //         },
        //         { 'start.hour': { $lte: end } },
        //     ];
        // else if (start) scheduleQuery['start.hour'] = start;
        if (end)
            scheduleQuery.scheduleStartDateAndTime = {
                $gte: new Date(start),
                $lte: new Date(end),
            };
        else if (start) scheduleQuery.scheduleStartDateAndTime = { $gte: new Date(start) };
        const scheduleStatus = [];
        let lateProject = false;
        if (sessionStatus) {
            if (!Array.isArray(sessionStatus)) sessionStatus = [sessionStatus];
            sessionStatus.forEach((sessionStatusElement) => {
                switch (sessionStatusElement) {
                    case COMPLETED:
                        scheduleStatus.push(sessionStatusElement);
                        break;
                    case 'notstarted':
                        scheduleStatus.push(DS_PENDING);
                        break;
                    case 'inprogress':
                        scheduleStatus.push(ONGOING);
                        break;
                    case 'upcoming':
                        scheduleStatus.push(DS_PENDING);
                        break;
                    case 'missed':
                        scheduleStatus.push(MISSED);
                        break;
                    case 'late':
                    case 'early':
                        lateProject = true;
                        break;
                    // case 'cancelled':
                    //     scheduleQuery.isActive = false;
                    //     break;
                    default:
                        break;
                }
            });
            if (
                !sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'cancelled')
            ) {
                scheduleQuery.isActive = true;
            } else scheduleStatus.push(DS_PENDING);
            if (!scheduleStatus.length) {
                if (
                    sessionStatus.find(
                        (sessionStatusElement) => sessionStatusElement === 'cancelled',
                    )
                ) {
                    scheduleQuery.isActive = false;
                } else scheduleQuery.status = { $in: scheduleStatus };
            } else {
                scheduleQuery.status = { $in: scheduleStatus };
                // if (
                //     sessionStatus.find(
                //         (sessionStatusElement) => sessionStatusElement === 'cancelled',
                //     )
                // )
                // scheduleQuery.$or = [{ status: { $in: scheduleStatus } }, { isActive: false }];
                // else scheduleQuery.status = { $in: scheduleStatus };
            }
            if (!sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'merged'))
                scheduleQuery.merge_status = false;
        } else scheduleQuery.merge_status = false;

        const scheduleProject = {
            program_name: 1,
            course_name: 1,
            course_code: 1,
            _course_id: 1,
            title: 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'staffs.staff_name': 1,
            'staffs._staff_id': 1,
            mode: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            // start: 1,
            // end: 1,
            status: 1,
            isActive: 1,
            'merge_with.schedule_id': 1,
            ...(programIds &&
                (!Array.isArray(programIds) || programIds.length === 1) && {
                    year_no: 1,
                    level_no: 1,
                    term: 1,
                }),
            ...(courseId && {
                'student_groups.group_name': 1,
                'student_groups.session_group.group_name': 1,
            }),
            ...(lateProject && {
                'sessionDetail.start_time': 1,
                'sessionDetail.stop_time': 1,
            }),
            type: 1,
            classModeType: 1,
            startWithOutFace: 1,
            scheduleStartFrom: 1,
            'sessionDetail.start_time': 1,
            'sessionDetail.stop_time': 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .find(scheduleQuery, scheduleProject)
            .populate({ path: 'staffs._staff_id', select: 'mobile' })
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .sort({ scheduleStartDateAndTime: 1 })
            .lean();
        console.timeEnd('courseScheduleData');
        if (courseScheduleData.length) {
            const courseIds = courseScheduleData.map(
                (courseScheduleDataElement) => courseScheduleDataElement._course_id,
            );
            const courseDetailsWithVersion = await courseSchema
                .find(
                    {
                        _id: { $in: courseIds },
                    },
                    {
                        // versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        // versionedFrom: 1,
                        // versionedCourseIds: 1,
                    },
                )
                .lean();
            courseScheduleData.forEach((courseScheduleElement) => {
                const courseVersionedDetails = courseDetailsWithVersion.find(
                    (courseDetailsWithVersionElement) =>
                        courseDetailsWithVersionElement._id.toString() ===
                        courseScheduleElement._course_id.toString(),
                );
                // courseScheduleElement.versionNo =
                //     (courseVersionedDetails && courseVersionedDetails.versionNo) || 1;
                courseScheduleElement.versioned =
                    (courseVersionedDetails && courseVersionedDetails.versioned) || false;
                courseScheduleElement.versionName =
                    (courseVersionedDetails && courseVersionedDetails.versionName) || '';
                // courseScheduleElement.versionedFrom =
                //     (courseVersionedDetails && courseVersionedDetails.versionedFrom) || null;
                // courseScheduleElement.versionedCourseIds =
                //     (courseVersionedDetails && courseVersionedDetails.versionedCourseIds) || [];
            });
        }
        const mergedSchedule = courseScheduleData.filter(
            (scheduleElement) => scheduleElement.merge_with && scheduleElement.merge_with.length,
        );
        const scheduleReport = clone(
            courseScheduleData.filter((scheduleElement) => !scheduleElement.merge_with.length),
        );
        let mergedScheduleId = [];
        mergedSchedule.forEach((mergedScheduleElement) => {
            const scheduleMergeId = mergedScheduleElement.merge_with.map((mergeWithElement) =>
                mergeWithElement.schedule_id._id.toString(),
            );
            scheduleMergeId.push(mergedScheduleElement._id.toString());
            if (
                !mergedScheduleId.filter((mergedScheduleIdElement) =>
                    scheduleMergeId.find(
                        (scheduleMergeIdElement) =>
                            scheduleMergeIdElement.toString() ===
                            mergedScheduleIdElement.toString(),
                    ),
                ).length
            ) {
                scheduleReport.push(mergedScheduleElement);
            }
            mergedScheduleId = [...mergedScheduleId, ...scheduleMergeId];
        });
        const pendingSchedules = clone(
            scheduleReport.filter(
                (scheduleElement) => scheduleElement.isActive && scheduleElement.status === PENDING,
            ),
        );
        const cancelledScheduleList = clone(
            scheduleReport.filter((scheduleElement) => scheduleElement.isActive === false),
        );
        let scheduleReportList = clone(
            scheduleReport.filter((scheduleElement) => scheduleElement.status !== PENDING),
        );
        const notStartedUpcomingSchedules = [];
        const currentTime = new Date();
        if (sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'notstarted')) {
            pendingSchedules.forEach((pendingScheduleElement) => {
                if (new Date(pendingScheduleElement.scheduleStartDateAndTime) < currentTime)
                    notStartedUpcomingSchedules.push(pendingScheduleElement);
            });
        }
        if (sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'upcoming')) {
            pendingSchedules.forEach((pendingScheduleElement) => {
                if (new Date(pendingScheduleElement.scheduleStartDateAndTime) > currentTime)
                    notStartedUpcomingSchedules.push(pendingScheduleElement);
            });
        }
        scheduleReportList = [
            ...scheduleReportList,
            ...notStartedUpcomingSchedules,
            ...cancelledScheduleList,
        ].filter((scheduleElement, index, self) => {
            return (
                self.findIndex(
                    (scheduleElement2) =>
                        String(scheduleElement2._id) === String(scheduleElement._id),
                ) === index
            );
        });
        const constantSummary = {
            totalSchedule: 0,
            completedSchedule: 0,
            pendingSchedule: 0,
            missedSchedule: 0,
            ongoingSchedule: 0,
            cancelledSchedule: 0,
        };
        const sessionSummary = {
            ...constantSummary,
            totalSchedule: scheduleReportList.length,
            mergedSchedule: mergedSchedule.length,
        };
        const sessionMode = [
            { mode: 'remote', ...constantSummary },
            { mode: 'onsite', ...constantSummary },
        ];
        scheduleReportList.forEach((scheduleElement) => {
            const sessionModeIndex = sessionMode.findIndex(
                (sessionModeElement) => sessionModeElement.mode === scheduleElement.mode,
            );
            sessionMode[sessionModeIndex].totalSchedule++;
            if (scheduleElement.isActive) {
                switch (scheduleElement.status) {
                    case COMPLETED:
                        sessionSummary.completedSchedule++;
                        sessionMode[sessionModeIndex].completedSchedule++;
                        break;
                    case PENDING:
                        sessionSummary.pendingSchedule++;
                        sessionMode[sessionModeIndex].pendingSchedule++;
                        break;
                    case MISSED:
                        sessionSummary.missedSchedule++;
                        sessionMode[sessionModeIndex].missedSchedule++;
                        break;
                    case ONGOING:
                        sessionSummary.ongoingSchedule++;
                        sessionMode[sessionModeIndex].ongoingSchedule++;
                        break;
                    default:
                        break;
                }
            } else {
                sessionSummary.cancelledSchedule++;
                sessionMode[sessionModeIndex].cancelledSchedule++;
            }
        });
        const scheduleResponse = {
            ...sessionSummary,
            sessionMode,
            scheduleList: scheduleReportList.sort((a, b) => {
                const comp =
                    new Date(a.scheduleStartDateAndTime) > new Date(b.scheduleStartDateAndTime)
                        ? 1
                        : new Date(a.scheduleStartDateAndTime) <
                          new Date(b.scheduleStartDateAndTime)
                        ? -1
                        : 0;
                return comp;
            }),
        };
        return scheduleResponse;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramCourse = async ({
    sessionDate,
    programIds,
    institutionCalendarId,
    courseCoordinatorFilter,
}) => {
    try {
        const programCalendarList = await programCalendarSchema
            .findOne(
                {
                    ...(programIds && { _program_id: convertToMongoObjectId(programIds) }),
                    ...(institutionCalendarId.length && {
                        _institution_calendar_id: { $in: institutionCalendarId },
                    }),
                    // 'level.start_date': { $lte: new Date(sessionDate) },
                    // 'level.end_date': { $gte: new Date(sessionDate) },
                },
                {
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.versionNo': 1,
                    'level.course.versioned': 1,
                    'level.course.versionName': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course.versioned': 1,
                    'level.rotation_course.course.versionName': 1,
                    'level.rotation_course.course.versionNo': 1,
                },
            )
            .populate([
                {
                    path: 'level.rotation_course.course._course_id',
                    select: { versionedCourseIds: 1 },
                },
                { path: 'level.course._course_id', select: { versionedCourseIds: 1 } },
            ])
            .lean();

        if (!programCalendarList) {
            return [];
        }
        let programCourses = [];
        for (levelElement of programCalendarList.level) {
            if (levelElement.rotation === 'no')
                programCourses = [
                    ...programCourses,
                    ...levelElement.course.map((courseElement) => {
                        return {
                            term: levelElement.term,
                            year: levelElement.year,
                            level_no: levelElement.level_no,
                            rotation: levelElement.rotation,
                            ...courseElement,
                            versionName: courseElement.versionName || '',
                            versionNo: courseElement.versionNo || 1,
                            versioned: courseElement.versioned || false,
                            versionedCourseIds: courseElement._course_id?.versionedCourseIds || [],
                            _course_id: courseElement._course_id?._id || '',
                        };
                    }),
                ];
            else
                for (rotationElement of levelElement.rotation_course) {
                    programCourses = [
                        ...programCourses,
                        ...rotationElement.course.map((courseElement) => {
                            return {
                                term: levelElement.term,
                                year: levelElement.year,
                                level_no: levelElement.level_no,
                                rotation: levelElement.rotation,
                                rotation_count: rotationElement.rotation_count,
                                ...courseElement,
                                versionName: courseElement.versionName || '',
                                versionNo: courseElement.versionNo || 1,
                                versioned: courseElement.versioned || false,
                                versionedCourseIds:
                                    courseElement._course_id?.versionedCourseIds || [],
                                _course_id: courseElement._course_id?._id || '',
                            };
                        }),
                    ];
                }
        }
        return courseCoordinatorFilter.length
            ? programCourses.filter((programCourseElement) =>
                  courseCoordinatorFilter.find(
                      (courseCoordinatorElement) =>
                          courseCoordinatorElement._course_id.toString() ===
                              programCourseElement._course_id.toString() &&
                          courseCoordinatorElement.year_no === programCourseElement.year &&
                          courseCoordinatorElement.level_no === programCourseElement.level_no &&
                          courseCoordinatorElement.term === programCourseElement.term,
                  ),
              )
            : programCourses;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getScheduleReportDetails = async ({ _institution_id, scheduleId }) => {
    try {
        const scheduleQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            $or: [
                {
                    _id: convertToMongoObjectId(scheduleId),
                },
                {
                    'merge_with.schedule_id': convertToMongoObjectId(scheduleId),
                },
            ],
        };
        const scheduleProject = {
            program_name: 1,
            course_name: 1,
            course_code: 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session.session_topic': 1,
            'staffs.staff_name': 1,
            'staffs._staff_id': 1,
            mode: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            start: 1,
            end: 1,
            status: 1,
            isActive: 1,
            'merge_with.schedule_id': 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            'students._id': 1,
            'students.status': 1,
            'students.mode': 1,
            'sessionDetail.start_time': 1,
            'sessionDetail.stop_time': 1,
            type: 1,
            title: 1,
            infra_name: 1,
            isMissedToComplete: 1,
            _program_id: 1,
            classModeType: 1,
            scheduleStartFrom: 1,
        };
        console.info({ scheduleQuery });
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .findOne(scheduleQuery, scheduleProject)
            .populate([
                { path: 'staffs._staff_id', select: 'mobile' },
                {
                    path: 'merge_with.schedule_id',
                    select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
                },
                {
                    path: '_course_id',
                    select: {
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                },
            ])
            .lean();
        console.timeEnd('courseScheduleData');
        let scheduleResponse = {
            studentCount: courseScheduleData.students.length,
            studentAttendanceReport: {
                present: {
                    auto: courseScheduleData.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.mode &&
                            studentElement.status === PRESENT &&
                            studentElement.mode === 'auto',
                    ).length,
                    manual: courseScheduleData.isMissedToComplete
                        ? courseScheduleData.students.filter(
                              (studentElement) =>
                                  studentElement &&
                                  studentElement.status &&
                                  studentElement.status === PRESENT,
                          ).length
                        : courseScheduleData.students.filter(
                              (studentElement) =>
                                  studentElement &&
                                  studentElement.mode &&
                                  studentElement.status === PRESENT &&
                                  studentElement.mode === 'manual',
                          ).length,
                },
                absent: courseScheduleData.students.filter(
                    (studentElement) =>
                        studentElement && studentElement.status && studentElement.status === ABSENT,
                ).length,
                pending: courseScheduleData.students.filter(
                    (studentElement) =>
                        studentElement &&
                        studentElement.status &&
                        studentElement.status === PENDING,
                ).length,
            },
        };
        scheduleResponse.versionNo = courseScheduleData._course_id.versionNo || 1;
        scheduleResponse.versioned = courseScheduleData._course_id.versioned || false;
        scheduleResponse.versionName = courseScheduleData._course_id.versionName || '';
        scheduleResponse.versionedFrom = courseScheduleData._course_id.versionedFrom || null;
        scheduleResponse.versionedCourseIds =
            courseScheduleData._course_id.versionedCourseIds || [];
        delete courseScheduleData.students;
        delete courseScheduleData._course_id;
        scheduleResponse = {
            ...scheduleResponse,
            ...courseScheduleData,
        };
        return scheduleResponse;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttendanceReport = async (req, res) => {
    try {
        const {
            body: { course_names, course_codes, schedule_date, start_time, program_names },
        } = req;
        const scheduleQuery = {
            isActive: true,
            isDeleted: false,
            schedule_date: new Date(schedule_date),
            'start.hour': parseInt(start_time),
        };
        if (course_codes && course_codes.length) scheduleQuery.course_code = { $in: course_codes };
        if (course_names && course_names.length) scheduleQuery.course_name = { $in: course_names };
        if (program_names && program_names.length)
            scheduleQuery.program_name = { $in: program_names };
        // const csQuery = {};
        // const csProject = {
        //     'programs.remoteScheduling._id': 1,
        //     'programs.remoteScheduling.meetingUsername': 1,
        //     'programs.remoteScheduling.associatedEmail': 1,
        //     'programs.remoteScheduling.password': 1,
        //     'programs.remoteScheduling.passCode': 1,
        //     'programs.remoteScheduling.meetingUrl': 1,
        // };
        // const infraData = await CourseScheduleSetting.find(csQuery, csProject);
        const scheduleProject = {
            _id: 1,
            course_name: 1,
            course_code: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            status: 1,
            staffs: 1,
            students: 1,
            mode: 1,
            infra_name: 1,
            program_name: 1,
            // _infra_id: 1,
        };
        const scheduleList = await course_schedule.find(scheduleQuery, scheduleProject).sort({
            program_name: 1,
            course_code: 1,
        });
        const scheduleAttendanceReport = [];
        // const changes = [];
        for (scheduleElement of scheduleList) {
            // const infraDetail = infraData
            //     .map((a) => a.programs)
            //     .flat()
            //     .map((b) =>
            //         b.remoteScheduling.filter(
            //             (i) =>
            //                 scheduleElement._infra_id &&
            //                 i._id.toString() === scheduleElement._infra_id.toString(),
            //         ),
            //     )
            //     .flat()
            //     .shift();
            // if (infraDetail)
            //     changes.push({
            //         program_name: scheduleElement.program_name,
            //         course_name: scheduleElement.course_name,
            //         course_code: scheduleElement.course_code,
            //         mode: scheduleElement.mode,
            //         staffs: scheduleElement.staffs,
            //         infraDetail,
            //     });
            scheduleAttendanceReport.push({
                _id: scheduleElement._id,
                program_name: scheduleElement.program_name,
                course_name: scheduleElement.course_name,
                course_code: scheduleElement.course_code,
                mode: scheduleElement.mode,
                start: scheduleElement.start,
                end: scheduleElement.end,
                scheduleStatus: scheduleElement.status,
                sessionDetail: scheduleElement.sessionDetail,
                staffCount: scheduleElement.staffs.length,
                staffs: scheduleElement.staffs,
                staffAttendanceReport: {
                    present: scheduleElement.staffs.filter(
                        (staffElement) =>
                            staffElement && staffElement.status && staffElement.status === PRESENT,
                    ).length,
                    absent: scheduleElement.staffs.filter(
                        (staffElement) =>
                            staffElement && staffElement.status && staffElement.status === ABSENT,
                    ).length,
                    pending: scheduleElement.staffs.filter(
                        (staffElement) =>
                            staffElement && staffElement.status && staffElement.status === PENDING,
                    ).length,
                },
                studentCount: scheduleElement.students.length,
                studentAttendanceReport: {
                    present: {
                        auto: scheduleElement.students.filter(
                            (studentElement) =>
                                studentElement &&
                                studentElement.mode &&
                                studentElement.status === PRESENT &&
                                studentElement.mode === 'auto',
                        ).length,
                        manual: scheduleElement.students.filter(
                            (studentElement) =>
                                studentElement &&
                                studentElement.mode &&
                                studentElement.status === PRESENT &&
                                studentElement.mode === 'manual',
                        ).length,
                    },
                    totalPresent: scheduleElement.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.status &&
                            studentElement.status === PRESENT,
                    ).length,
                    absent: scheduleElement.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.status &&
                            studentElement.status === ABSENT,
                    ).length,
                    pending: scheduleElement.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.status &&
                            studentElement.status === PENDING,
                    ).length,
                },
            });
        }
        return res.status(200).send(
            response_function(res, 200, true, 'Attendance Report', {
                totalSchedule: scheduleAttendanceReport.length,
                ongoingSchedule: scheduleAttendanceReport.filter(
                    (scheduleElement) => scheduleElement.scheduleStatus === ONGOING,
                ).length,
                completedSchedule: scheduleAttendanceReport.filter(
                    (scheduleElement) => scheduleElement.scheduleStatus === 'completed',
                ).length,
                notStartedSchedule: scheduleAttendanceReport.filter(
                    (scheduleElement) => scheduleElement.scheduleStatus === 'pending',
                ).length,
                scheduleList: scheduleAttendanceReport,
                // changes,
            }),
        );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};

const getUserProgram = async ({ userProgramIds }) => {
    try {
        const programData = await programSchema
            .find({ isDeleted: false, isActive: true, _id: { $in: userProgramIds } }, { name: 1 })
            .lean();
        return programData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDateWiseAttendanceReportExport = async ({
    _institution_id,
    sessionDate,
    programIds,
    institutionCalendarId,
    start,
    end,
    term,
    year,
    levelNo,
    courseId,
    rotationCount,
    sessionStatus,
    courseCoordinatorFilter,
}) => {
    try {
        const scheduleQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            ...(!end && { schedule_date: new Date(sessionDate) }),
            // schedule_date: new Date(sessionDate),
            ...(courseCoordinatorFilter.length && { $or: courseCoordinatorFilter }),
            ...(programIds && { _program_id: { $in: programIds } }),
            ...(institutionCalendarId &&
                !courseCoordinatorFilter.length && {
                    _institution_calendar_id: { $in: institutionCalendarId },
                }),
            ...(term && { term }),
            ...(year && { year_no: year }),
            ...(levelNo && { level_no: levelNo }),
            ...(courseId && { _course_id: convertToMongoObjectId(courseId) }),
            ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
        };
        if (end)
            scheduleQuery.scheduleStartDateAndTime = {
                $gte: new Date(start),
                $lte: new Date(end),
            };
        else if (start) scheduleQuery.scheduleStartDateAndTime = { $gte: new Date(start) };
        const scheduleStatus = [];
        if (sessionStatus) {
            if (!Array.isArray(sessionStatus)) sessionStatus = [sessionStatus];
            for (sessionStatusElement of sessionStatus) {
                switch (sessionStatusElement) {
                    case COMPLETED:
                        scheduleStatus.push(sessionStatusElement);
                        break;
                    case 'notstarted':
                        scheduleStatus.push(DS_PENDING);
                        break;
                    case 'inprogress':
                        scheduleStatus.push(ONGOING);
                        break;
                    case 'upcoming':
                        scheduleStatus.push(DS_PENDING);
                        break;
                    case 'missed':
                        scheduleStatus.push(MISSED);
                        break;
                    default:
                        break;
                }
            }
            if (
                !sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'cancelled')
            ) {
                scheduleQuery.isActive = true;
            } else scheduleStatus.push(DS_PENDING);
            if (!scheduleStatus.length) {
                if (
                    sessionStatus.find(
                        (sessionStatusElement) => sessionStatusElement === 'cancelled',
                    )
                ) {
                    scheduleQuery.isActive = false;
                } else scheduleQuery.status = { $in: scheduleStatus };
            } else {
                scheduleQuery.status = { $in: scheduleStatus };
                // if (
                //     sessionStatus.find(
                //         (sessionStatusElement) => sessionStatusElement === 'cancelled',
                //     )
                // )
                // scheduleQuery.$or = [{ status: { $in: scheduleStatus } }, { isActive: false }];
                // else scheduleQuery.status = { $in: scheduleStatus };
            }
            if (!sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'merged'))
                scheduleQuery.merge_status = false;
        } else scheduleQuery.merge_status = false;

        const scheduleProject = {
            program_name: 1,
            course_name: 1,
            course_code: 1,
            _course_id: 1,
            title: 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session.session_topic': 1,
            'staffs.staff_name': 1,
            'staffs._staff_id': 1,
            mode: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            start: 1,
            end: 1,
            status: 1,
            isActive: 1,
            'merge_with.schedule_id': 1,
            type: 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            'students._id': 1,
            'students.status': 1,
            'students.mode': 1,
            'sessionDetail.start_time': 1,
            'sessionDetail.stop_time': 1,
            infra_name: 1,
            _program_id: 1,
            classModeType: 1,
            scheduleStartFrom: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .find(scheduleQuery, scheduleProject)
            .populate({ path: 'staffs._staff_id', select: 'mobile' })
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .sort({ scheduleStartDateAndTime: 1 })
            .lean();
        console.timeEnd('courseScheduleData');
        if (courseScheduleData.length) {
            const courseIds = courseScheduleData.map(
                (courseScheduleDataElement) => courseScheduleDataElement._course_id,
            );
            const courseDetailsWithVersion = await courseSchema
                .find(
                    {
                        _id: { $in: courseIds },
                    },
                    {
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                )
                .lean();
            for (const courseScheduleELement of courseScheduleData) {
                const courseVersionedDetails = courseDetailsWithVersion.find(
                    (courseDetailsWithVersionElement) =>
                        courseDetailsWithVersionElement._id.toString() ===
                        courseScheduleELement._course_id.toString(),
                );
                courseScheduleELement.versionNo =
                    (courseVersionedDetails && courseVersionedDetails.versionNo) || 1;
                courseScheduleELement.versioned =
                    (courseVersionedDetails && courseVersionedDetails.versioned) || false;
                courseScheduleELement.versionName =
                    (courseVersionedDetails && courseVersionedDetails.versionName) || '';
                courseScheduleELement.versionedFrom =
                    (courseVersionedDetails && courseVersionedDetails.versionedFrom) || null;
                courseScheduleELement.versionedCourseIds =
                    (courseVersionedDetails && courseVersionedDetails.versionedCourseIds) || [];
            }
        }
        const mergedSchedule = courseScheduleData.filter(
            (scheduleElement) => scheduleElement.merge_with && scheduleElement.merge_with.length,
        );
        const scheduleReport = clone(
            courseScheduleData.filter((scheduleElement) => !scheduleElement.merge_with.length),
        );
        let mergedScheduleId = [];
        for (const mergedScheduleElement of mergedSchedule) {
            const scheduleMergeId = mergedScheduleElement.merge_with.map((mergeWithElement) =>
                mergeWithElement.schedule_id._id.toString(),
            );
            scheduleMergeId.push(mergedScheduleElement._id.toString());
            if (
                !mergedScheduleId.filter((mergedScheduleIdElement) =>
                    scheduleMergeId.find(
                        (scheduleMergeIdElement) =>
                            scheduleMergeIdElement.toString() ===
                            mergedScheduleIdElement.toString(),
                    ),
                ).length
            ) {
                scheduleReport.push(mergedScheduleElement);
            }
            mergedScheduleId = [...mergedScheduleId, ...scheduleMergeId];
        }
        const pendingSchedules = clone(
            scheduleReport.filter(
                (scheduleElement) => scheduleElement.isActive && scheduleElement.status === PENDING,
            ),
        );
        const cancelledScheduleList = clone(
            scheduleReport.filter((scheduleElement) => scheduleElement.isActive === false),
        );
        let scheduleReportList = clone(
            scheduleReport.filter((scheduleElement) => scheduleElement.status !== PENDING),
        );
        const notStartedUpcomingSchedules = [];
        const currentTime = new Date();
        if (sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'notstarted')) {
            for (const pendingScheduleElement of pendingSchedules)
                if (new Date(pendingScheduleElement.scheduleStartDateAndTime) < currentTime)
                    notStartedUpcomingSchedules.push(pendingScheduleElement);
        }
        if (sessionStatus.find((sessionStatusElement) => sessionStatusElement === 'upcoming')) {
            for (const pendingScheduleElement of pendingSchedules)
                if (new Date(pendingScheduleElement.scheduleStartDateAndTime) > currentTime)
                    notStartedUpcomingSchedules.push(pendingScheduleElement);
        }
        scheduleReportList = [
            ...scheduleReportList,
            ...notStartedUpcomingSchedules,
            ...cancelledScheduleList,
        ].filter((scheduleElement, index, self) => {
            return (
                self.findIndex(
                    (scheduleElement2) =>
                        String(scheduleElement2._id) === String(scheduleElement._id),
                ) === index
            );
        });
        const studentAttendance = {
            scheduleCount: scheduleReportList.length,
            total: 0,
            auto: 0,
            manual: 0,
            absent: 0,
        };
        for (scheduleElement of scheduleReportList) {
            scheduleElement.studentAttendance = {
                studentCount: scheduleElement.students.length,
                studentAttendanceReport: {
                    present: {
                        auto: scheduleElement.students.filter(
                            (studentElement) =>
                                studentElement &&
                                studentElement.mode &&
                                studentElement.status === PRESENT &&
                                studentElement.mode === 'auto',
                        ).length,
                        manual: scheduleElement.students.filter(
                            (studentElement) =>
                                studentElement &&
                                studentElement.mode &&
                                studentElement.status === PRESENT &&
                                studentElement.mode === 'manual',
                        ).length,
                    },
                    absent: scheduleElement.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.status &&
                            studentElement.status === ABSENT,
                    ).length,
                    pending: scheduleElement.students.filter(
                        (studentElement) =>
                            studentElement &&
                            studentElement.status &&
                            studentElement.status === PENDING,
                    ).length,
                },
            };
            studentAttendance.total += scheduleElement.studentAttendance.studentCount;
            studentAttendance.auto +=
                scheduleElement.studentAttendance.studentAttendanceReport.present.auto;
            studentAttendance.manual +=
                scheduleElement.studentAttendance.studentAttendanceReport.present.manual;
            studentAttendance.absent +=
                scheduleElement.studentAttendance.studentAttendanceReport.absent;
            delete scheduleElement.students;
        }
        const scheduleResponse = {
            scheduleList: scheduleReportList.sort((a, b) => {
                const comp =
                    new Date(a.scheduleStartDateAndTime) > new Date(b.scheduleStartDateAndTime)
                        ? 1
                        : new Date(a.scheduleStartDateAndTime) <
                          new Date(b.scheduleStartDateAndTime)
                        ? -1
                        : 0;
                return comp;
            }),
            allScheduleStudentAttendance: studentAttendance,
        };
        return scheduleResponse;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getDateWiseAttendanceReport,
    getProgramCourse,
    getAttendanceReport,
    getScheduleReportDetails,
    getUserProgram,
    getDateWiseAttendanceReportExport,
};
