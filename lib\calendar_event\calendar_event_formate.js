let institution_calendar_formate = require('../institution_calendar/institution_calendar_formate');
let common_fun = require('../utility/common_functions');

module.exports = {
    institution_calendar_event: (doc) => {
        let formate_obj = [];
        doc.forEach(doc => {
            let obj = {
                _id: doc._id,
                event_date: doc.event_date,
                event_type: doc.event_type,
                language: doc.language,
                first_language: doc.first_language,
                second_language: doc.second_language,
                start_time: doc.start_time,
                end_time: doc.end_time,
                end_date: doc.end_date,
                institution_calendar: institution_calendar_formate.institution_ID_Only(doc.institution_calendar),
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    institution_calendar_event_ID: (doc) => {
        let obj = {
            _id: common_fun.check_value_or_null(doc, '_id') || '',
            event_type: common_fun.check_value_or_null(doc, 'event_type') || '',
            event_whom: common_fun.check_value_or_null(doc, 'event_whom') || '',
            event_name: {
                first_language: common_fun.check_value_or_null(doc, 'event_name.first_language') || '',
                second_language: common_fun.check_value_or_null(doc, 'event_name.second_language') || '',
            },
            event_description: {
                first_language: common_fun.check_value_or_null(doc, 'event_name.first_language') || '',
                second_language: common_fun.check_value_or_null(doc, 'event_name.second_language') || '',
            },
            event_date: common_fun.check_value_or_null(doc, 'event_date') || '',
            start_time: common_fun.check_value_or_null(doc, 'start_time') || '',
            end_time: common_fun.check_value_or_null(doc, 'end_time') || '',
            end_date: common_fun.check_value_or_null(doc, 'end_date') || '',
            _infrastructure_id: common_fun.check_value_or_null(doc, '_infrastructure_id') || '',
            review: institution_calendar_event_review(common_fun.check_value_or_null(doc, 'review')),
            status: common_fun.check_value_or_null(doc, 'status') || '',
            // institution_calendar: institution_calendar_formate.institution_ID_Only( common_fun.check_value_or_null(doc, 'institution_calendar)')||'',
            isDeleted: common_fun.check_value_or_null(doc, 'isDeleted') || '',
            isActive: common_fun.check_value_or_null(doc, 'isActive') || '',
        }
        return obj;
    },


    institution_calendar_event_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            event_date: doc.event_date,
            event_type: doc.event_type,
            language: doc.language,
            first_language: doc.first_language,
            second_language: doc.second_language,
            start_time: doc.start_time,
            end_time: doc.end_time,
            end_date: doc.end_date,
            institution_calendar: doc._calendar_id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    institution_calendar_event_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(doc => {
            let obj = {
                _id: doc._id,
                event_date: doc.event_date,
                event_type: doc.event_type,
                language: doc.language,
                first_language: doc.first_language,
                second_language: doc.second_language,
                start_time: doc.start_time,
                end_time: doc.end_time,
                end_date: doc.end_date,
                institution_calendar: doc._calendar_id,
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}

function institution_calendar_event_review(docs) {
    let formate_obj = [];
    console.log(docs.length);
    docs.forEach(doc => {
        if (common_fun.check_value_or_null(doc, '_reviewer_ids') != undefined) {
            let obj = {
                _reviewer_ids: common_fun.check_value_or_null(doc, '_reviewer_ids') || '',
                reviewer_name: common_fun.check_value_or_null(doc, 'reviewer_name') || '',
                // reviews: typeof (common_fun.check_value_null(doc, 'doc.reviews')) == "boolean" ? doc.reviews : '',
                reviews: typeof (common_fun.check_value_or_null(doc, 'reviews')) == "boolean" ? doc.reviews : '',
                reviewer_comment: common_fun.check_value_or_null(doc, 'reviewer_comment') || '',
                dean_feedback: typeof (common_fun.check_value_or_null(doc, 'dean_feedback')) == "boolean" ? doc.dean_feedback : '',
                dean_comment: common_fun.check_value_or_null(doc, 'dean_comment') || '',
                expire: {
                    expire_date: common_fun.check_value_or_null(doc, 'expire.expire_date') || '',
                    expire_time: common_fun.check_value_or_null(doc, 'expire.expire_time') || ''
                },
                status: common_fun.check_value_or_null(doc, 'status') || ''
            }
            formate_obj.push(obj);
        }
    });
    return formate_obj;
}