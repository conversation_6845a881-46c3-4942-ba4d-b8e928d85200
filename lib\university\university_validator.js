// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.university = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    name: Joi.string()
                        .alphanum()
                        .min(3)
                        .max(20)
                        .error((error) => {
                            return error;
                        }),
                    university_code: Joi.string()
                        .alphanum()
                        .min(2)
                        .max(10)
                        .error((error) => {
                            return error;
                        }),
                    location: Joi.string()
                        .alphanum()
                        .min(1)
                        .max(20)
                        .error((error) => {
                            return error;
                        }),
                    _country_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.university_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
