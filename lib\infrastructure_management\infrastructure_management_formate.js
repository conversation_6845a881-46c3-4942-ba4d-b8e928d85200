module.exports = {

    infrastructure_management: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _building_id: element._building_id,
                building_name: element.building_name,
                floor_no: element.floor_no,
                zone: element.zone,
                room_no: element.room_no,
                name: element.name,
                usage: element.usage,
                delivery_type: element.delivery_type,
                timing: element.timing,
                program: element.program,
                department: element.department,
                subject: element.subject,
                capacity: element.capacity,
                reserved: element.reserved,
                _id: element._id,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    infrastructure_management_ID: (doc) => {
        let obj = {
            _building_id: doc._building_id,
            building_name: doc.building_name,
            floor_no: doc.floor_no,
            zone: doc.zone,
            room_no: doc.room_no,
            name: doc.name,
            usage: doc.usage,
            delivery_type: doc.delivery_type,
            timing: doc.timing,
            program: doc.program,
            department: doc.department,
            subject: doc.subject,
            capacity: doc.capacity,
            reserved: doc.reserved,
            _id: doc._id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    }
}