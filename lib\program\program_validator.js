// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.program = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    _institution_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    }),
                    name: Joi.string().min(3).max(50).error(error => {
                        return error;
                    }),
                    level: Joi.string().alphanum().min(3).max(20).error(error => {
                        return error;
                    }),
                    level_no: Joi.number().min(1).max(25).error(error => {
                        return error;
                    }),
                    degree: Joi.string().min(2).max(20).error(error => {
                        return error;
                    }),
                    no: Joi.string().alphanum().min(2).max(20).error(error => {
                        return error;
                    }),
                    theory_credit: Joi.number().min(0).max(1000).error(error => {
                        return error;
                    }),
                    practicals_credit: Joi.number().min(0).max(1000).error(error => {
                        return error;
                    }),
                    clinic_credit: Joi.number().min(0).max(1000).error(error => {
                        return error;
                    }),
                    total_credit: Joi.number().min(1).max(10000).error(error => {
                        return error;
                    }),
                    interim: Joi.boolean().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
        // )
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.program_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.abbreviation = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            abbreviation: Joi.object().keys({
                theory: Joi.string().alphanum().min(1).max(20).error(error => {
                    return error;
                }),
                practical: Joi.string().alphanum().min(1).max(20).error(error => {
                    return error;
                }),
                clinic: Joi.string().alphanum().min(1).max(20).error(error => {
                    return error;
                }),
                credit_hours: Joi.string().min(1).max(20).error(error => {
                    return error;
                }),
                contact_hours: Joi.string().min(1).max(20).error(error => {
                    return error;
                })
            }).unknown(true)
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}