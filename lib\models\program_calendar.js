const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const program_calendar = new Schema(
    {
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
            required: true,
        },
        _program_id: [
            {
                type: Schema.Types.ObjectId,
                ref: constant.DIGI_PROGRAM,
                required: true,
            },
        ],
        programCode: { type: String },
        level: [
            {
                term: {
                    type: String,
                    // enum: [constant.BATCH.REGULAR, constant.BATCH.INTERIM],
                },
                // Codes Unify Inputs
                termCode: { type: String },
                yearCode: { type: String },
                levelCode: { type: String },
                curriculumCode: { type: String },
                year: String,
                level_no: String,
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.PROGRAM,
                },
                curriculum: {
                    type: String,
                    // enum: ['1.0', '2.0']
                },
                start_date: Date,
                end_date: Date,
                events: [
                    {
                        _event_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.CALENDAR_EVENT,
                        },
                        event_type: {
                            type: String,
                            enum: [
                                constant.EVENT_TYPE.HOLIDAY,
                                constant.EVENT_TYPE.ORIENTATION,
                                constant.EVENT_TYPE.TRAINING,
                                constant.EVENT_TYPE.EXAM,
                                constant.EVENT_TYPE.GENERAL,
                            ],
                            required: true,
                        },
                        event_name: String,
                        event_date: Date,
                        start_time: Date,
                        end_time: Date,
                        end_date: Date,
                    },
                ],
                // _event_id: [{
                //     type: Schema.Types.ObjectId,
                //     ref: constant.CALENDAR_EVENT
                // }],
                rotation: {
                    type: String,
                    enum: ['yes', 'no'],
                },
                rotation_count: {
                    type: Number,
                    default: 0,
                },
                course: [
                    {
                        _course_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_COURSE,
                        },
                        courses_name: {
                            type: String,
                            required: true,
                        },
                        courses_number: {
                            type: String,
                            required: true,
                        },
                        study_level: {
                            type: Number,
                            required: true,
                        },
                        model: {
                            type: String,
                            // enum: [constant.MODULES.COURSE, constant.MODULES.module, constant.mo],
                            required: true,
                        },
                        credit_hours: [
                            {
                                type_name: String,
                                type_symbol: String,
                                credit_hours: Number,
                            },
                        ],
                        start_date: Date,
                        end_date: Date,
                        courses_events: [
                            {
                                _event_id: {
                                    type: Schema.Types.ObjectId,
                                    ref: constant.CALENDAR_EVENT,
                                },
                                event_type: {
                                    type: String,
                                    enum: [
                                        constant.EVENT_TYPE.HOLIDAY,
                                        constant.EVENT_TYPE.ORIENTATION,
                                        constant.EVENT_TYPE.TRAINING,
                                        constant.EVENT_TYPE.EXAM,
                                        constant.EVENT_TYPE.GENERAL,
                                    ],
                                    required: true,
                                },
                                event_name: String,
                                event_date: Date,
                                start_time: Date,
                                end_time: Date,
                                end_date: Date,
                            },
                        ],
                        // _event_id: [{
                        //     type: Schema.Types.ObjectId,
                        //     ref: constant.CALENDAR_EVENT
                        // }],
                        color_code: String,
                        // fe_data: {
                        //     row_start: Number,
                        //     row_end: Number,
                        //     column_start: Number,
                        //     column_end: Number,
                        //     total_column: Number
                        // }
                        versionNo: Number,
                        versioned: Boolean,
                        versionName: String,
                        versionedFrom: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_COURSE,
                        },
                    },
                ],
                rotation_course: [
                    {
                        rotation_count: {
                            type: Number,
                        },
                        rotationGroupCode: { type: String },
                        course: [
                            {
                                _course_id: {
                                    type: Schema.Types.ObjectId,
                                    ref: constant.DIGI_COURSE,
                                },
                                courses_name: {
                                    type: String,
                                    required: true,
                                },
                                courses_number: {
                                    type: String,
                                    required: true,
                                },
                                study_level: {
                                    type: Number,
                                    required: true,
                                },
                                model: {
                                    type: String,
                                    // enum: [constant.MODEL.COURSE, constant.MODEL.MODULE, constant.MODEL.ELECTIVE],
                                    required: true,
                                },
                                credit_hours: [
                                    {
                                        type_name: String,
                                        type_symbol: String,
                                        credit_hours: Number,
                                    },
                                ],
                                start_date: Date,
                                end_date: Date,
                                start_week: Number,
                                end_week: Number,
                                courses_events: [
                                    {
                                        _event_id: {
                                            type: Schema.Types.ObjectId,
                                            ref: constant.CALENDAR_EVENT,
                                        },
                                        event_type: {
                                            type: String,
                                            enum: [
                                                constant.EVENT_TYPE.HOLIDAY,
                                                constant.EVENT_TYPE.ORIENTATION,
                                                constant.EVENT_TYPE.TRAINING,
                                                constant.EVENT_TYPE.EXAM,
                                                constant.EVENT_TYPE.GENERAL,
                                            ],
                                            required: true,
                                        },
                                        event_name: String,
                                        event_date: Date,
                                        start_time: Date,
                                        end_time: Date,
                                        end_date: Date,
                                    },
                                ],
                                // _event_id: [{
                                //     type: Schema.Types.ObjectId,
                                //     ref: constant.CALENDAR_EVENT
                                // }],
                                color_code: String,
                                versionNo: Number,
                                versioned: Boolean,
                                versionName: String,
                                _batch_course_id: [
                                    {
                                        type: Schema.Types.ObjectId,
                                        ref: constant.COURSE,
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
        dean: {
            _dean_id: {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
            name: {
                first: {
                    type: String,
                    trim: true,
                },
                middle: {
                    type: String,
                    trim: true,
                },
                last: {
                    type: String,
                    trim: true,
                },
                family: {
                    type: String,
                    trim: true,
                },
            },
            review: Boolean,
            comment: String,
            status: String,
            timestamp: Date,
        },
        creater: {
            _creater_id: {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
            name: {
                first: {
                    type: String,
                    trim: true,
                },
                middle: {
                    type: String,
                    trim: true,
                },
                last: {
                    type: String,
                    trim: true,
                },
                family: {
                    type: String,
                    trim: true,
                },
            },
        },
        review: [
            {
                _reviewer_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.USER,
                },
                reviewer_name: {
                    first: {
                        type: String,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
                reviews: Boolean,
                reviewer_comment: String,
                creater_feedback: Boolean,
                creater_comment: String,
                expire: {
                    expire_date: Date,
                    expire_time: Date,
                },
                status: String,
                reviewer_timestamp: Date,
                creater_timestamp: Date,
            },
        ],
        status: String,
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.PROGRAM_CALENDAR, program_calendar);
