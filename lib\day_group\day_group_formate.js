module.exports = {

    day_group: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                days: element.days, 
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    day_group_ID: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            days: doc.days, 
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    }
}