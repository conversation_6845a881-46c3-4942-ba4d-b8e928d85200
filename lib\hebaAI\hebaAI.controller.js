const courseScheduleSchemas = require('../models/course_schedule');
const externalDataSyncSchema = require('../models/externalDataSync');
const courseSchema = require('../models/digi_course');
const studentGroupSchema = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');
const {
    SCHEDULE_TYPES: { REGULAR },
    GENDER: { MALE, FEMALE },
    MISSED,
    PENDING,
    PRESENT,
    ABSENT,
} = require('../utility/constants');
const institutionCalendarsSchema = require('../models/institution_calendar');
const roleAssignSchema = require('../models/role_assign');
const courseSessionOrderSchema = require('../models/digi_session_order');
const { getLevelsFromRedis } = require('./hebaAI.service');
const { getUserRoleProgramList } = require('../utility/utility.service');
const programCalendarSchema = require('../models/program_calendar');
const { getUserCourseLists } = require('../digi_class/course_session/course_session_service');

const studentCourseList = async ({ query = {} }) => {
    try {
        const { studentId } = query;
        console.time('studentGroupData');
        const studentGroupData = await studentGroupSchema
            .distinct('_institution_calendar_id', {
                'groups.courses.setting.session_setting.groups._student_ids':
                    convertToMongoObjectId(studentId),
            })
            .lean();
        console.timeEnd('studentGroupData');

        const institutionCalendarData = await institutionCalendarsSchema
            .findOne(
                { _id: { $in: studentGroupData }, isActive: true, isDeleted: false },
                { calendar_name: 1 },
            )
            .sort({ _id: -1 })
            .lean();
        console.time('usersCourseList');
        const usersCourseList = await getUserCourseLists({
            userId: studentId,
            type: 'student',
            institutionCalendarId: convertToMongoObjectId('6560732001fe42649f3d21f8'),
            // institutionCalendarId: institutionCalendarData._id,
        });
        console.timeEnd('usersCourseList');

        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: usersCourseList,
            // data: { usersCourseList, institutionCalendarData },
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const courseSessionList = async ({ query = {} }) => {
    try {
        const { courseId } = query;
        console.time('courseSessionData');
        const courseSessionData = await courseSessionOrderSchema
            .findOne(
                {
                    _course_id: convertToMongoObjectId(courseId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session_flow_data._id': 1,
                    'session_flow_data.s_no': 1,
                    'session_flow_data.delivery_type': 1,
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_no': 1,
                    'session_flow_data.delivery_topic': 1,
                },
            )
            .lean();
        console.timeEnd('courseSessionData');
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data:
                courseSessionData && courseSessionData.session_flow_data
                    ? courseSessionData.session_flow_data
                    : [],
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const scheduleListBasedDates = async ({ query = {} }) => {
    try {
        const { userId, startDate, endDate } = query;
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .find(
                {
                    $or: [
                        { 'students._id': convertToMongoObjectId(userId) },
                        { 'staffs._staff_id': convertToMongoObjectId(userId) },
                    ],
                    ...(endDate
                        ? {
                              schedule_date: {
                                  $gte: new Date(startDate),
                                  $lte: new Date(endDate),
                              },
                          }
                        : { schedule_date: new Date(startDate) }),
                    // type: REGULAR,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_type': 1,
                    'session.session_topic': 1,
                    mode: 1,
                    schedule_date: 1,
                    status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    'staffs.staff_name': 1,
                },
            )
            .lean();
        console.timeEnd('courseScheduleData');
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: courseScheduleData,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const studentLeaveAbsentSchedule = async ({ query = {} }) => {
    try {
        const { userId } = query;
        // const institutionCalendarData = await institutionCalendarsSchema
        //     .findOne(
        //         { _id: { $in: studentGroupData }, isActive: true, isDeleted: false },
        //         { calendar_name: 1 },
        //     )
        //     .sort({ _id: -1 })
        //     .lean();
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .find(
                {
                    // institutionCalendarId: institutionCalendarData._id,
                    _institution_calendar_id: convertToMongoObjectId('6560732001fe42649f3d21f8'),
                    'students._id': convertToMongoObjectId(userId),
                    type: REGULAR,
                    // status: { $ne: PRESENT },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_type': 1,
                    'session.session_topic': 1,
                    mode: 1,
                    // schedule_date: 1,
                    status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    // 'staffs.staff_name': 1,
                    'students.$': 1,
                },
            )
            .lean();
        console.timeEnd('courseScheduleData');
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: courseScheduleData,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const scheduleListDetails = async ({ query = {} }) => {
    try {
        const { scheduleDate } = query;
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .find(
                {
                    schedule_date: new Date(scheduleDate).setUTCHours(0, 0, 0, 0),
                    // type: REGULAR,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    type: 1,
                    // 'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    // 'session.session_type': 1,
                    // 'session.session_topic': 1,
                    mode: 1,
                    schedule_date: 1,
                    status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    'staffs.staff_name': 1,
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    'sessionDetail.start_time': 1,
                    'sessionDetail.stop_time': 1,
                    'sessionDetail.mode': 1,
                    // 'students._id': 1,
                    'students.status': 1,
                    _institution_calendar_id: 1,
                },
            )
            .sort({ scheduleStartDateAndTime: 1 })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .lean();
        console.timeEnd('courseScheduleData');
        const mergedScheduleIdSet = new Set();
        const scheduleData = [];
        const scheduleMap = new Map(
            courseScheduleData.map((schedule) => [schedule._id.toString(), schedule]),
        );
        courseScheduleData.forEach((scheduleElement) => {
            const scheduleIdStr = scheduleElement._id.toString();
            if (scheduleElement.merge_status && !mergedScheduleIdSet.has(scheduleIdStr)) {
                scheduleElement.mergedSchedule = [];
                scheduleElement.merge_with.forEach((mergeElement) => {
                    const mergedSchedule = scheduleMap.get(mergeElement.schedule_id.toString());
                    if (mergedSchedule) {
                        scheduleElement.mergedSchedule.push(mergedSchedule.session);
                        mergedScheduleIdSet.add(mergeElement.schedule_id.toString());
                    }
                });
            }
            const attendanceCounts = scheduleElement.students.reduce(
                (counts, student) => {
                    if (student.status === PRESENT) {
                        counts.presentCount++;
                    } else if (student.status === ABSENT) {
                        counts.absentCount++;
                    }
                    return counts;
                },
                { totalStudent: scheduleElement.students.length, presentCount: 0, absentCount: 0 },
            );
            scheduleElement.studentAttendance = attendanceCounts;
            delete scheduleElement.students;
            scheduleData.push(scheduleElement);
        });

        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: scheduleData,
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const scheduleWithDetails = async ({ query = {} }) => {
    try {
        const { scheduleId } = query;
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(scheduleId),
                },
                {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    type: 1,
                    // 'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    // 'session.session_type': 1,
                    // 'session.session_topic': 1,
                    mode: 1,
                    schedule_date: 1,
                    status: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    'staffs.staff_name': 1,
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    'sessionDetail.start_time': 1,
                    'sessionDetail.stop_time': 1,
                    'sessionDetail.mode': 1,
                    'students.primaryTime': 1,
                    'students.status': 1,
                    'students.name': 1,
                    'students._id': 1,
                },
            )
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .lean();
        console.timeEnd('courseScheduleData');
        const attendanceCounts = courseScheduleData.students.reduce(
            (counts, student) => {
                if (student.status === PRESENT) {
                    counts.presentCount++;
                } else if (student.status === ABSENT) {
                    counts.absentCount++;
                }
                return counts;
            },
            { totalStudent: courseScheduleData.students.length, presentCount: 0, absentCount: 0 },
        );
        courseScheduleData.studentAttendance = attendanceCounts;
        if (courseScheduleData.merge_status) {
            courseScheduleData.mergedSchedule = [];
            courseScheduleData.merge_with.forEach((mergeElement) => {
                courseScheduleData.mergedSchedule.push(mergeElement.schedule_id.session);
            });
            delete courseScheduleData.merge_with;
        }
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: courseScheduleData,
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

// Student Attendance Changes Service Schedule wise
const scheduleAttendanceChange = async ({ body = {} }) => {
    try {
        const { scheduleId, studentWithStatus } = body;
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(scheduleId),
                },
                {
                    students: 1,
                },
            )
            .lean();
        console.timeEnd('courseScheduleData');
        studentWithStatus.forEach((studentStatusElement) => {
            const studentIndex = courseScheduleData.students.findIndex(
                (studentElement) =>
                    String(studentElement._id) === String(studentStatusElement.studentId),
            );
            if (studentIndex !== -1) {
                courseScheduleData.students[studentIndex].status = studentStatusElement.status;
            }
        });
        await courseScheduleSchemas.updateOne(
            { _id: convertToMongoObjectId(scheduleId) },
            { $set: { students: courseScheduleData.students } },
        );
        return {
            statusCode: 200,
            message: 'SUCCESS',
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

// Student Attendance Changes Service Schedule wise
const externalDataSync = async ({ body = {} }) => {
    try {
        const { uniqueId, sTerm, level, commonData } = body;
        await externalDataSyncSchema.updateOne(
            { uniqueId, sTerm, level },
            { $set: { uniqueId, sTerm, level, commonData } },
            { upsert: true },
        );
        return {
            statusCode: 200,
            message: 'SUCCESS',
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

const externalDataGet = async ({ query = {} }) => {
    try {
        const { uniqueId, sTerm, level } = query;
        const syncData = await externalDataSyncSchema
            .find(
                { ...(uniqueId && { uniqueId }), ...(sTerm && { sTerm }), ...(level && { level }) },
                { uniqueId: 1, sTerm: 1, level: 1, commonData: 1 },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'SUCCESS',
            data: syncData,
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};

module.exports = {
    studentCourseList,
    courseSessionList,
    scheduleListBasedDates,
    studentLeaveAbsentSchedule,
    scheduleWithDetails,
    scheduleListDetails,
    scheduleAttendanceChange,
    externalDataSync,
    externalDataGet,
};
