const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const digi_course = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        course_type: {
            type: String,
            required: true,
        },
        course_recurring: [
            {
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                level_no: {
                    type: String,
                },
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year_no: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        course_occurring: [
            {
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                level_no: {
                    type: String,
                },
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year_no: {
                    type: String,
                },
                isAssigned: Boolean,
            },
        ],
        course_code: {
            type: String,
        },
        course_code_labels: [
            {
                language: { type: String },
                label: { type: String },
            },
        ],
        course_name: {
            type: String,
        },
        course_name_labels: [
            {
                language: { type: String },
                label: { type: String },
            },
        ],
        duration: {
            type: Number,
        },
        start_week: Number,
        end_week: Number,
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
        },
        _curriculum_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_CURRICULUM,
        },
        administration: {
            _program_id: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
            },
            program_name: String,
            _department_id: {
                type: Schema.Types.ObjectId,
                ref: constant.DEPARTMENT,
            },
            department_name: String,
            _subject_id: {
                type: Schema.Types.ObjectId,
                ref: constant.DEPARTMENT_SUBJECT,
            },
            subject_name: String,
        },
        participating: [
            {
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.PROGRAM,
                },
                program_name: String,
                _department_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DEPARTMENT,
                },
                department_name: String,
                _subject_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DEPARTMENT_SUBJECT,
                },
                subject_name: String,
            },
        ],
        credit_hours: [
            {
                type_name: String,
                type_symbol: String,
                credit_hours: Number,
                contact_hours: Number,
                duration_split: Boolean,
                duration: String,
                duration_per_contact_hour: Number,
                _session_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DIGI_SESSION_DELIVERY_TYPES,
                },
                delivery_type: [
                    {
                        _delivery_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_SESSION_DELIVERY_TYPES,
                        },
                        delivery_type: String,
                        delivery_symbol: String,
                        duration: Number,
                        isActive: Boolean,
                    },
                ],
            },
        ],
        allow_editing: {
            type: Boolean,
            default: false,
        },
        session_status: Boolean,
        achieve_target: Boolean,
        // course_occurrence: [
        //     {
        //         program: {
        //             program_name: String,
        //             _program_id: {
        //                 type: Schema.Types.ObjectId,
        //                 ref: constant.PROGRAM,
        //             },
        //         },
        //         curriculum: String,
        //         year: String,
        //         level_no: String,
        //     },
        // ],
        course_assigned_details: [
            {
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.PROGRAM,
                },
                program_name: String,
                _curriculum_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DIGI_CURRICULUM,
                },
                curriculum_name: String,
                _year_id: {
                    type: Schema.Types.ObjectId,
                },
                year: String,
                _level_id: {
                    type: Schema.Types.ObjectId,
                },
                level_no: {
                    type: String,
                },
                course_duration: {
                    start_week: Number,
                    end_week: Number,
                    total: Number,
                },
                isConfigured: {
                    type: Boolean,
                    default: false,
                },
                course_shared_with: [
                    {
                        _program_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.PROGRAM,
                        },
                        program_name: String,
                        _curriculum_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_CURRICULUM,
                        },
                        curriculum_name: String,
                        _year_id: {
                            type: Schema.Types.ObjectId,
                        },
                        year: String,
                        _level_id: {
                            type: Schema.Types.ObjectId,
                        },
                        level_no: String,
                    },
                ],
                mapping_type: {
                    type: String,
                    default: '',
                },
                content_mapping_type: {
                    type: String,
                    default: '',
                },
                break_session_flow: {
                    type: Boolean,
                    default: false,
                },
                manualAttendance: {
                    type: Boolean,
                    default: false,
                },
                isStaffAssigned: {
                    type: Boolean,
                    default: false,
                },
                isMissedToSchedule: {
                    type: Boolean,
                    default: false,
                },
                assignedStaffIds: [
                    {
                        staffId: { type: Schema.Types.ObjectId, ref: constant.USER },
                        staffName: {
                            first: {
                                type: String,
                                trim: true,
                            },
                            middle: {
                                type: String,
                                trim: true,
                            },
                            last: {
                                type: String,
                                trim: true,
                            },
                            family: {
                                type: String,
                                trim: true,
                            },
                        },
                    },
                ],
                auto_assign_session_default_settings: {
                    type: Boolean,
                    default: false,
                },
                auto_end_attendance_in: {
                    type: Number,
                    default: 5,
                },
                same_time_for_delivery_group: {
                    status: {
                        type: Boolean,
                        default: false,
                    },
                    delivery_type: [
                        {
                            delivery_type_id: {
                                type: Schema.Types.ObjectId,
                            },
                            delivery_type_name: {
                                type: String,
                            },
                        },
                    ],
                },
                manage_topics: [
                    {
                        _institution_calendar_id: {
                            type: Schema.Types.ObjectId,
                        },
                        delivery_type_details: [
                            {
                                session_id: {
                                    type: Schema.Types.ObjectId,
                                },
                                delivery_type_id: {
                                    type: Schema.Types.ObjectId,
                                },
                                delivery_type: String,
                                delivery_symbol: String,
                            },
                        ],
                        topics: [
                            {
                                title: {
                                    type: String,
                                    trim: true,
                                },
                            },
                        ],
                        isDeleted: {
                            type: Boolean,
                            default: false,
                        },
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                    },
                ],

                isDeleted: {
                    type: Boolean,
                    default: false,
                },
                isActive: {
                    type: Boolean,
                    default: true,
                },
            },
        ],
        framework: {
            _id: {
                type: String,
                trim: true,
            },
            name: {
                type: String,
                trim: true,
            },
            code: {
                type: String,
                trim: true,
            },
            domains: [
                {
                    no: String,
                    name: String,
                    clo: [
                        {
                            no: String,
                            name: String,
                            year_id: Schema.Types.ObjectId,
                            year_name: String,
                            level_id: Schema.Types.ObjectId,
                            level_name: String,
                            slos: [
                                {
                                    slo_id: String,
                                    delivery_type_id: String,
                                    delivery_symbol: String,
                                    delivery_no: Number,
                                    no: String,
                                    name: String,
                                    mapped_value: String,
                                },
                            ],
                            isDeleted: {
                                type: Boolean,
                                default: false,
                            },
                            isActive: {
                                type: Boolean,
                                default: true,
                            },
                        },
                    ],
                },
            ],
        },
        coordinators: [
            {
                _institution_calendar_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.INSTITUTION_CALENDAR,
                },
                year: String,
                level_no: {
                    type: String,
                },
                term: {
                    type: String,
                    trim: true,
                },
                _user_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.USER,
                },
                user_name: {
                    first: {
                        type: String,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
                status: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        versionNo: { type: Number, default: 1 },
        versioned: { type: Boolean, default: false },
        versionName: { type: String, default: 'default' },
        versionedFrom: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_COURSE,
            default: null,
        },
        versionedCourseIds: [{ type: Schema.Types.ObjectId, ref: constant.DIGI_COURSE }],
        // versioning: [
        //     {
        //         _course_id: {
        //             type: Schema.Types.ObjectId,
        //             ref: constant.DIGI_COURSE,
        //         },
        //         versionNo: Number,
        //     },
        // ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        settings: {
            staffFacial: {
                type: Boolean,
            },
            studentFacial: {
                type: Boolean,
            },
            sessionChange: {
                type: Boolean,
            },
            autoEndAttendance: {
                type: Number,
                default: 5,
            },
            updatedBy: {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_COURSE, digi_course);
