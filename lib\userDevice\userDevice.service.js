const userRegisteredDetailSchema = require('../models/userRegisteredDetail');
const { logger } = require('../utility/util_keys');
const { ACTIVE, INACTIVE } = require('../utility/constants');
const { convertToMongoObjectId } = require('../utility/common');

/**
 * Get user registered devices by userId
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - Array of user registered devices
 */
const getUserRegisteredDevices = async (userId) => {
    try {
        const userDevices = await userRegisteredDetailSchema
            .find(
                { userId: convertToMongoObjectId(userId) },
                {
                    _id: 1,
                    registeredOn: 1,
                    deviceType: 1,
                    deviceBrandName: 1,
                    deviceID: 1,
                    deviceNumber: 1,
                    deviceOSVersion: 1,
                    status: 1,
                    unregisteredBy: 1,
                    unregisteredOn: 1,
                    unregisteredReason: 1,
                },
            )
            .sort({ createdAt: -1 })
            .lean();

        logger.info(
            'userDevice.service -> getUserRegisteredDevices -> Retrieved %d devices for user %s',
            userDevices.length,
            userId,
        );

        return {
            status: true,
            data: userDevices,
            message: 'User registered devices retrieved successfully',
        };
    } catch (error) {
        logger.error(
            { error },
            'userDevice.service -> getUserRegisteredDevices -> Error retrieving user devices for user %s',
            userId,
        );
        return {
            status: false,
            data: null,
            message: 'Error retrieving user devices',
            error: error.message,
        };
    }
};

/**
 * Update device status to inactive to allow new device registration
 * @param {Object} params - Parameters for device deactivation
 * @param {string} params.userId - User ID
 * @param {string} params.dbRecordId - Database record ID to deactivate
 * @param {string} params.deactivatedBy - User ID who is performing the deactivation
 * @param {string} params.unregisteredReason - Reason for deactivation (optional)
 * @returns {Promise<Object>} - Result of the deactivation operation
 */
const deactivateUserDevice = async ({
    userId,
    dbRecordId,
    deactivatedBy,
    unregisteredReason = 'User requested device deactivation',
}) => {
    try {
        // Find the device registration by database record ID
        const deviceRegistration = await userRegisteredDetailSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(dbRecordId),
                    userId: convertToMongoObjectId(userId),
                    status: ACTIVE,
                },
                { _id: 1, deviceID: 1 },
            )
            .lean();

        if (!deviceRegistration) {
            logger.warn(
                'userDevice.service -> deactivateUserDevice -> No active device registration found for user %s and record ID %s',
                userId,
                dbRecordId,
            );
            return {
                status: false,
                data: null,
                message: 'No active device registration found for this user and record',
            };
        }

        // Update the device registration to inactive
        const updatedDevice = await userRegisteredDetailSchema.updateOne(
            { _id: convertToMongoObjectId(dbRecordId) },
            {
                $set: {
                    status: INACTIVE,
                    unregisteredBy: convertToMongoObjectId(deactivatedBy),
                    unregisteredOn: new Date(),
                    unregisteredReason,
                },
            },
        );

        if (updatedDevice.modifiedCount > 0) {
            logger.info(
                'userDevice.service -> deactivateUserDevice -> Device %s (record %s) deactivated for user %s by %s',
                deviceRegistration.deviceID,
                dbRecordId,
                userId,
                deactivatedBy,
            );

            return {
                status: true,
                data: updatedDevice,
                message: 'Device deactivated successfully. User can now register a new device.',
            };
        }

        logger.warn(
            'userDevice.service -> deactivateUserDevice -> Failed to deactivate device record %s for user %s',
            dbRecordId,
            userId,
        );
        return {
            status: false,
            data: null,
            message: 'Failed to deactivate device',
        };
    } catch (error) {
        logger.error(
            { error },
            'userDevice.service -> deactivateUserDevice -> Error deactivating device for user %s and record %s',
            userId,
            dbRecordId,
        );
        return {
            status: false,
            data: null,
            message: 'Error deactivating device',
            error: error.message,
        };
    }
};

module.exports = {
    getUserRegisteredDevices,
    deactivateUserDevice,
};
