const { inherits } = require('util');

function ApiError({ statusCode = 500, message = '', isOperational = true, stack = '' }) {
    // this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.message = message;
    this.isOperational = isOperational;

    if (stack) this.stack = stack;
    else Error.captureStackTrace(this, this.constructor);
}

inherits(ApiError, Error);

module.exports = ApiError;
