let constant = require('../utility/constants');
var department_subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var department = require('mongoose').model(constant.DEPARTMENT);
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const department_subject_formate = require('./department_subject_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_master_id', foreignField: '_id', as: 'department' } },
        {
            $unwind: {
                path: '$department',
                preserveNullAndEmptyArrays: true
            }
        },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_master_id', foreignField: '_id', as: 'division' } },
        {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(department_subject, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "department_subject list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ department_subject_formate.department_subject(doc.data));
        common_files.list_all_response(res, 200, true, "department_subject list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* department_subject_formate.department_subject(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_master_id', foreignField: '_id', as: 'department' } },
        {
            $unwind: {
                path: '$department',
                preserveNullAndEmptyArrays: true
            }
        },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_master_id', foreignField: '_id', as: 'division' } },
        {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        },
    ];
    let doc = await base_control.get_aggregate(department_subject, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "department_subject details", /* doc.data */department_subject_formate.department_subject_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let master_ids = [], title_name = [];;
    let status = false, datas;
    let checks, master;
    let docs, doc_update = { status: true };

    await req.body.data.forEach(element => {
        if (element.id.length == 0) {
            title_name.push(element.title);
        }
        if (master_ids.indexOf(element._master_id) == -1) {
            master = element.master;
            master_ids.push(element._master_id);
        }
    });

    if (master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
        checks = await base_control.check_id(department, { _id: { $in: master_ids }, 'isDeleted': false });
    } else if (master == constant.DEPARTMENT_MASTER.DIVISION) {
        checks = await base_control.check_id(department_division, { _id: { $in: master_ids }, 'isDeleted': false });
    }
    if (checks.status) {
        let docs1 = { status: true };
        //await req.body.data.forEach(async (doc, index) => {
        let query = { title: { $in: title_name }, _master_id: { $in: master_ids }, isDeleted: false };
        docs1 = await base_control.get_list(department_subject, query, {});
        //});
        if (!docs1.status) {
            await req.body.data.forEach(async (doc, index) => {
                let objects = {
                    master: doc.master,
                    title: doc.title,
                    _master_id: doc._master_id,
                    shared: doc.shared
                };
                if (doc.id == '' && doc.id.length == 0) {
                    docs = await base_control.insert(department_subject, objects);
                    if (docs.status && doc_update.status) {
                        if (master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
                            doc_update = await base_control.update_push_pull(department, doc._master_id, { $push: { _subject_id: docs.responses._id } });
                        } else {
                            doc_update = await base_control.update_push_pull(department_division, doc._master_id, { $push: { _subject_id: docs.responses._id } });
                        }
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                } else {
                    docs = await base_control.update(department_subject, doc.id, objects);
                    if (docs.status && doc_update.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                }
                if (req.body.data.length == index + 1) {
                    if (status) {
                        common_files.com_response(res, 201, true, "department_subject Added successfully", datas);
                    } else {
                        common_files.com_response(res, 500, false, "Error ", datas);
                    }
                }
            });
        } else {
            common_files.com_response(res, 500, false, "Error duplicate values found ", "This content already present in DB");
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._master_id != undefined && req.body.master != undefined) {
        if (req.body.master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
            checks = await base_control.check_id(department, { _id: { $in: req.body._master_id }, 'isDeleted': false });
        } else if (req.body.master == constant.DEPARTMENT_MASTER.DIVISION) {
            checks = await base_control.check_id(department_division, { _id: { $in: req.body._master_id }, 'isDeleted': false });
        }
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(department_subject, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "department_subject update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let opout;
    let aggre = [
        { $match: { '_id': ObjectId(object_id) } },
        { $project: { master: 1, _master_id: 1 } }
    ];
    let docs = await base_control.get_aggregate(department_subject, aggre);
    if (docs.data[0].master == constant.DEPARTMENT_MASTER.DEPARTMENT) {
        opout = await base_control.update_push_pull(department, docs.data[0]._master_id, { $pull: { _subject_id: object_id } });
    } else if (docs.data[0].master == constant.DEPARTMENT_MASTER.DIVISION) {
        opout = await base_control.update_push_pull(department_division, docs.data[0]._master_id, { $pull: { _subject_id: object_id } });
    }
    let doc = await base_control.delete(department_subject, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "department_subject deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(department_subject, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "department_subject List", department_subject_formate.department_subject_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};