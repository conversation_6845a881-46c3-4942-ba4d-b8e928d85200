const axios = require('axios');
const JWT = require('jsonwebtoken');
const URL = 'https://api.zoom.us/v2/';
const fs = require('fs');

const zoomConfig = {
    production: {
        APIKey: process.env.ZOOM_API_KEY || 'Empty Zoom API Key',
        APISecret: process.env.ZOOM_API_SECRET_KEY || 'Empty Zoom Secret Key',
    },
};
const payload = {
    iss: zoomConfig.production.APIKey,
    exp: new Date().getTime() + 5000,
};
const token = JWT.sign(payload, zoomConfig.production.APISecret);
const options = {
    headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token,
    },
};
const createZoomMeeting = async (req) => {
    options.method = 'POST';
    options.url = URL + 'users/' + req.zoomUserId + '/meetings';
    options.data = JSON.stringify({
        topic: req.topic,
        type: 2,
        start_time: req.start_time,
        duration: req.duration,
        password: '1234',
        agenda: req.agenda,
        settings: {
            host_video: true,
            approval_type: 0,
            join_before_host: true,
            jbh_time: 5,
            use_pmi: true,
            auto_recording: 'local',
            meeting_authentication: true,
            enforce_login: true,
            enforce_login_domains: 'gmail.com',
        },
    });
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error creating Zoom meeting:', error.response?.data || error.message);
        throw error;
    }
};

const createZoomUser = async (req) => {
    options.method = 'POST';
    options.url = URL + 'users';
    options.data = JSON.stringify({
        action: 'custCreate',
        user_info: {
            email: req.email,
            type: 1,
            first_name: req.first_name,
            last_name: req.last_name,
        },
    });
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error creating Zoom user:', error.response?.data || error.message);
        throw error;
    }
};

const getZoomUserList = async (req) => {
    options.method = 'GET';
    options.url = URL + 'users';
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};

const getZoomMeetingParticipantsList = async (req) => {
    options.method = 'GET';
    options.url = URL + 'report/meetings/' + req.meetingId + '/participants';
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};

const getZoomUUIDList = async (req) => {
    const getZoomOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    getZoomOptions.method = 'GET';
    getZoomOptions.url = URL + 'past_meetings/' + req.meetingId + '/instances';
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};
const getZoomRecordList = async (req) => {
    const getZoomRecordOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    getZoomRecordOptions.method = 'GET';
    getZoomRecordOptions.url = URL + 'meetings/' + req.meetingUuId + '/recordings';
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};

const getZoomParticipantsList = async (req) => {
    const getZoomRecordOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    getZoomRecordOptions.method = 'GET';
    getZoomRecordOptions.url =
        URL +
        'report/meetings/' +
        req.meetingUuId +
        '/participants?page_size=' +
        req.page_size +
        '&next_page_token=' +
        req.next_page_token;
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};
const getZoomHostUserList = async (req) => {
    const getZoomUserOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    getZoomUserOptions.method = 'GET';
    getZoomUserOptions.url = URL + 'users?page_size=300';
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};
const getZoomMeetingDuration = async (req) => {
    const getZoomUserOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    getZoomUserOptions.method = 'GET';
    getZoomUserOptions.url = URL + 'past_meetings/' + req.meetingId;
    try {
        const response = await axios(options);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};
const createHostZoomMeeting = async (req) => {
    const ZoomMeetingOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    ZoomMeetingOptions.method = 'POST';
    ZoomMeetingOptions.url = URL + 'users/' + req.userId + '/meetings';
    ZoomMeetingOptions.data = JSON.stringify(req.params);
    try {
        const response = await axios(ZoomMeetingOptions);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};
const createParticipantURL = async (req) => {
    const ZoomMeetingOptions = {
        headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer ' + req.token,
        },
    };
    ZoomMeetingOptions.method = 'POST';
    ZoomMeetingOptions.url = URL + 'meetings/' + req.meetingId + '/registrants';
    ZoomMeetingOptions.data = JSON.stringify(req.params);
    try {
        const response = await axios(ZoomMeetingOptions);
        return response.data;
    } catch (error) {
        console.error('Error fetching Zoom user list:', error.response?.data || error.message);
        throw error;
    }
};

const downloadZoomVideo = async (req) => {
    const options = {
        method: 'GET',
        url: req.url,
    };
    try {
        const response = await axios(options);
        const file = fs.createWriteStream(req.fileLocation);
        response.data.pipe(file);
        return await new Promise((resolve, reject) => {
            file.on('finish', () => resolve(req.fileLocation));
            file.on('error', (err) => reject(err));
        });
    } catch (error) {
        console.error('Error downloading Zoom video:', error.response?.data || error.message);
        throw error;
    }
};
const deleteZoomRecordList = async (req) => {
    const options = {
        method: 'DELETE',
        url: `${URL}meetings/${req.meetingId}/recordings/${req.recordingId}`,
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${req.token}`,
        },
    };
    return axios(options)
        .then((response) => {
            if (response.status === 204) {
                console.log('Deleted successfully from Zoom');
                return 'Deleted Successfully';
            }
            console.log('Not deleted successfully from Zoom');
            return 'Not Deleted Successfully';
        })
        .catch((error) => {
            console.error('Error deleting Zoom recording:', error.response?.data || error.message);
            throw error;
        });
};
const hostZoomMeetingEnd = async (req) => {
    const options = {
        method: 'PUT',
        url: `${URL}meetings/${req.meetingId}/status`,
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${req.token}`,
        },
        data: req.params, // No need to stringify; axios handles JSON automatically
    };

    return axios(options)
        .then((response) => {
            if (response.status === 204) {
                console.log('Ended Zoom meeting successfully');
                return 'Ended Successfully';
            }
            console.log('Failed to end Zoom meeting');
            return 'Not Ended Successfully';
        })
        .catch((error) => {
            console.error('Error ending Zoom meeting:', error.response?.data || error.message);
            throw error;
        });
};
const getVideoLink = async (req) => {
    const options2 = {
        url: req.url,
        method: 'GET',
    };
    return axios(options2)
        .then((response) => response.data)
        .catch((error) => {
            console.error('Error fetching Zoom user list:', error.response?.data || error.message);
            throw error;
        });
};
const tokenGeneration = (req) => {
    const zoomMultiConfig = {
        production: {
            APIKey: req.API_KEY,
            APISecret: req.API_SECRET_KEY,
        },
    };

    const multiPayload = {
        iss: zoomMultiConfig.production.APIKey,
        exp: new Date().getTime() + 5000,
    };
    return JWT.sign(multiPayload, zoomMultiConfig.production.APISecret);
};
module.exports = {
    createZoomMeeting,
    createZoomUser,
    getZoomUserList,
    getZoomMeetingParticipantsList,
    getZoomUUIDList,
    getZoomRecordList,
    getVideoLink,
    tokenGeneration,
    deleteZoomRecordList,
    downloadZoomVideo,
    getZoomParticipantsList,
    getZoomHostUserList,
    createHostZoomMeeting,
    createParticipantURL,
    hostZoomMeetingEnd,
    getZoomMeetingDuration,
};
