const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const departmentSetting = new Schema({
    _user_id: {
        type: String,
        required: true,
    },
    _institution_calendar_id: {
        type: String,
        required: true,
    },
    _role_id: {
        type: String,
        required: true,
    },
    settings: [
        {
            _department_id: {
                type: String,
                required: true,
            },
            _program_id: {
                type: String,
                required: true,
            },
            _subject_id: {
                type: String,
                required: true,
            },
            _course_id: {
                type: String,
                required: true,
            },
            isConfigured: {
                type: Boolean,
                required: true,
            },
            term: {
                type: String,
                required: true,
            },
            rotation_count: {
                type: Number,
            },
        },
    ],
});

module.exports = mongoose.model('department_setting', departmentSetting);
