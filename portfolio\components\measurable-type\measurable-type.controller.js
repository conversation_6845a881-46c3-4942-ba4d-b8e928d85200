const MeasurableService = require('./measurable-type.service');

const { ITEM_TYPES } = require('../../common/utils/constants');

const createMeasurableType = async ({ body: { name, code } = {} }) => {
    const measurable = await MeasurableService.createMeasurableType({ name, code });

    return { message: 'MEASURABLE_CREATED_SUCCESSFULLY', data: measurable };
};

const getMeasurableTypes = async () => {
    const measurable = await MeasurableService.getMeasurableTypes();

    return { message: 'MEASURABLE_RETRIEVED_SUCCESSFULLY', data: measurable };
};

const updateMeasurableType = async ({ query: { measurableTypeId }, body: { name, code } = {} }) => {
    const measurable = await MeasurableService.updateMeasurableType({
        measurableTypeId,
        name,
        code,
    });

    return { message: 'MEASURABLE_UPDATED_SUCCESSFULLY', data: measurable };
};

const deleteMeasurableType = async ({ query: { measurableTypeId } }) => {
    await MeasurableService.deleteMeasurableType({ measurableTypeId });

    return { message: 'MEASURABLE_DELETED_SUCCESSFULLY' };
};

const getApplicationMeasurableTypes = async () => {
    return { message: 'RETRIEVED_SUCCESSFULLY', data: ITEM_TYPES };
};

module.exports = {
    createMeasurableType,
    getMeasurableTypes,
    updateMeasurableType,
    deleteMeasurableType,
    getApplicationMeasurableTypes,
};
