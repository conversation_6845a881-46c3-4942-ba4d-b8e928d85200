const express = require('express');
const route = express.Router();
const program_calendar = require('./program_calendar_controller');
const validator = require('./program_calendar_validator');

route.get('/date_check/:start/:end'/* , validator.program_calendar_setting_year */, program_calendar.date_check);
route.post('/list', program_calendar.list_values);
route.get('/:id', validator.program_calendar_id, program_calendar.list_id);
route.get('/', program_calendar.list);
route.post('/', validator.program_calendar, program_calendar.insert);
route.put('/:id', validator.program_calendar_id, validator.program_calendar, program_calendar.update);
route.delete('/:program/:calendar'/* , validator.program_calendar_id */, program_calendar.delete);

route.get('/landing/:program/:calendar', validator.program_calendar_setting, program_calendar.landing);
route.get('/calendar_get/:program/:year', validator.program_calendar_program_setting_year, program_calendar.calendar_get);
route.get('/dashboard/:id', validator.program_calendar_id, program_calendar.dashboard);
route.post('/calendar_setting', program_calendar.calendar_setting);
route.post('/calendar_level_date', program_calendar.calendar_level_date);
route.post('/rotation_setting', program_calendar.rotation_setting);
route.get('/setting_get/:program/:calendar', validator.program_calendar_setting, program_calendar.calendar_setting_get);
route.get('/setting_get_all/:calendar', validator.calendar_get_setting, program_calendar.calendar_get_setting);
route.get('/setting_year_get/:calendar/:year', validator.program_calendar_setting_year, program_calendar.calendar_setting_year_get);

//Interim Batch GETs
route.get('/setting_interim_get/:program/:calendar', validator.program_calendar_setting, program_calendar.calendar_interim_setting_get);
route.get('/setting_interim_year_get/:calendar/:year', validator.program_calendar_setting_year, program_calendar.calendar_interim_setting_year_get);

route.get('/interim_landing/:program/:calendar', validator.program_calendar_setting, program_calendar.interim_landing);
route.get('/interim_calendar_get/:program/:year', validator.program_calendar_program_setting_year, program_calendar.interim_calendar_get);
module.exports = route;