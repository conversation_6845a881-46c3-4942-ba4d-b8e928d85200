const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const { CHILD_EMERGENCY, USER } = require('../utility/constants');
const ObjectId = Schemas.Types.ObjectId;
const childEmergencySchemas = new Schemas(
    {
        _institution_id: { type: ObjectId },
        childId: {
            type: ObjectId,
            ref: USER,
        },
        latitude: { type: String },
        longitude: { type: String },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(CHILD_EMERGENCY, childEmergencySchemas);
