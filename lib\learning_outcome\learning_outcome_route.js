const express = require('express');
const route = express.Router();
const { validate } = require('../../middleware/validation');
// controller

const {
    individualReport,
    selfEvaluationSurveyReport,
    allSLOAverage,
    comparisonReport,
    selfEvaluationSurveyCLO,
} = require('./learning_outcome_controller');
// validator
const {
    individualReportSchema: { query: individualReportQuerySchema },
    selfEvaluationSurveyReportSchema: { query: selfEvaluationSurveyReportQuerySchema },
} = require('./learning_outcome_validator');

// leaning  individual-report staff view
route.get(
    '/individual-report',
    validate([{ schema: individualReportQuerySchema, property: 'query' }]),
    individualReport,
);
route.get(
    '/self-evaluation-survey-report',
    validate([{ schema: selfEvaluationSurveyReportQuerySchema, property: 'query' }]),
    selfEvaluationSurveyReport,
);

route.get(
    '/allSLOAverage/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
    allSLOAverage,
);

route.get(
    '/comparisonReport/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
    comparisonReport,
);

route.get(
    '/self-evaluation-survey/clo/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term',
    selfEvaluationSurveyCLO,
);
module.exports = route;
