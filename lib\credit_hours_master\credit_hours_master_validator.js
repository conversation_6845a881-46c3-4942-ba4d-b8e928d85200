const Joi = require('joi');
const common_files = require('../utility/common');

exports.credit_hours_master = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                        return error;
                    }),
                    year: Joi.number().min(1).max(10).required().error(error => {
                        return error;
                    }),
                    level: Joi.number().min(1).max(15).required().error(error => {
                        return error;
                    }),
                    course_no: Joi.number().min(1).max(20).required().error(error => {
                        return error;
                    }),
                    credit_hours: Joi.number().min(1).max(1000).required().error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_master_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    year: Joi.number().min(1).max(10).error(error => {
                        return error;
                    }),
                    level: Joi.number().min(1).max(5).error(error => {
                        return error;
                    }),
                    course_no: Joi.number().min(1).max(20).error(error => {
                        return error;
                    }),
                    credit_hours: Joi.number().min(1).max(1000).error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_master_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}