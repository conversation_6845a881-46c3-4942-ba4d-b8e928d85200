const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../../utility/constants');
const attachmentSchema = {
    url: String,
    signedUrl: String,
    sizeInKb: Number,
    name: String,
};
const assignmentPromptAnswerSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        studentId: {
            type: Schema.Types.ObjectId,
            ref: constant.USER,
        },
        assignmentId: {
            type: Schema.Types.ObjectId,
        },
        assignmentPromptId: {
            type: Schema.Types.ObjectId,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isReset: {
            type: Boolean,
            default: false,
        },
        choiceType: {
            type: [
                {
                    choices: [
                        {
                            isCorrect: {
                                type: Boolean,
                                default: true,
                            },
                        },
                    ],
                    attachments: [attachmentSchema],
                    score: Number,
                },
            ],
            default: [],
        },
        singleAnswer: {
            type: [
                {
                    answers: [
                        {
                            answerText: String,
                        },
                    ],
                    attachments: [attachmentSchema],
                    score: Number,
                },
            ],
            default: [],
        },
        matchTheFollowing: {
            type: [
                {
                    rightValues: [
                        {
                            leftId: Schema.Types.ObjectId,
                            rightId: Schema.Types.ObjectId,
                            value: { type: String },
                        },
                    ],
                    attachments: [attachmentSchema],
                    score: Number,
                },
            ],
            default: [],
        },
        fillInTheBlanks: {
            type: [
                {
                    answers: {
                        type: [String],
                        default: [],
                    },
                    attachments: [attachmentSchema],
                    score: {
                        type: Number,
                        default: 0,
                    },
                },
            ],
            default: [],
        },
        staffEvaluation: [
            {
                staffId: { type: Schema.Types.ObjectId, ref: constant.USER },
                score: { type: Number },
                learningOutcome: [
                    {
                        outcomeId: { type: Schema.Types.ObjectId },
                        isAchieved: { type: Boolean },
                        overwritten: { type: Boolean, default: false },
                    },
                ],
                rubrics: [
                    {
                        criteriaId: { type: Schema.Types.ObjectId },
                        selected: { type: Boolean },
                        dimensional: [
                            {
                                dimensionalId: { type: Schema.Types.ObjectId },
                                points: { type: Number },
                            },
                        ],
                        subRubrics: [
                            {
                                criteriaId: { type: Schema.Types.ObjectId },
                                selected: { type: Boolean },
                                dimensional: [
                                    {
                                        dimensionalId: { type: Schema.Types.ObjectId },
                                        points: { type: Number },
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
        attachments: [attachmentSchema],

        isResubmitted: { type: Boolean },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.ASSIGNMENT_PROMPT_ANSWER, assignmentPromptAnswerSchema);
