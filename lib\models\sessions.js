const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const sessions = new Schema(
    {
        year: {
            type: String,
            required: true,
        },
        level: {
            type: String,
            required: true,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: constant.COURSE,
        },
        delivery_type: {
            type: String,
            required: true,
        },
        delivery_mode: {
            type: String,
            required: true,
        },
        delivery: {
            type: String,
            required: true,
        },
        delivery_symbol: {
            type: String,
            required: true,
        },
        session_flow_no: {
            type: Number,
            required: true,
        },
        topic: String,
        infra: {
            type: String,
            required: true,
        },
        session_date: {
            type: Date,
            required: true,
        },
        start_time: {
            type: Date,
            required: true,
        },
        end_time: {
            type: Date,
            required: true,
        },
        uuid: {
            type: String,
            required: true,
        },
        _student_group: {
            type: String,
        },
        students: [],
        student_leaves: [
            {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
        ],
        _staff_id: [
            {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
        ],
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
            required: true,
        },
        status: String,
        socket_port: String,
        retake: {
            type: Boolean,
            default: false,
        },
        session: [
            {
                session_mode: String,
                start_time: Date,
                stop_time: Date,
                _start_by: {
                    type: Schema.Types.ObjectId,
                    ref: constant.USER,
                },
                attendees: [
                    {
                        _student_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.USER,
                        },
                        user_id: String,
                        student_name: {
                            first: String,
                            last: String,
                            middle: String,
                            family: String,
                        },
                        status: String,
                        mode: String,
                        time: Date,
                    },
                ],
                staff_attendees: [
                    {
                        _staff_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.USER,
                        },
                        user_id: String,
                        staff_name: {
                            first: String,
                            last: String,
                            middle: String,
                            family: String,
                        },
                        status: String,
                        mode: String,
                        time: Date,
                        comment: String,
                    },
                ],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.SESSION, sessions);
