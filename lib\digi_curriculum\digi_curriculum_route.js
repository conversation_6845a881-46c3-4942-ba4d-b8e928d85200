const express = require('express');
const route = express.Router();
const digi_curriculum = require('./digi_curriculum_controller');
const {
    userPolicyAuthentication,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
const validator = require('./digi_curriculum_validator');

route.post(
    '/data_check_curriculum',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_curriculum.data_check_curriculum,
);
route.post('/import_curriculum', digi_curriculum.import_curriculum);
route.post(
    '/',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.curriculum_insert,
    digi_curriculum.insert,
);
route.get(
    '/get_pre_requisite',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_curriculum.get_pre_requisite,
);
route.get('/filter_year_program_by_curriculum', digi_curriculum.filter_year_program_by_curriculum);
route.put(
    '/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.id,
    validator.curriculum_insert,
    digi_curriculum.update_curriculum,
);
route.put(
    '/archive_curriculum/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.id,
    digi_curriculum.archive_curriculum,
);
route.put(
    '/update_level/:id/:year_id/:level_id',
    validator.id,
    validator.year_id,
    validator.level_id,
    digi_curriculum.update_level,
);
route.get('/archived_list', digi_curriculum.list_archived);
route.get('/', digi_curriculum.list);
route.get(
    '/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    validator.id,
    digi_curriculum.list_id,
);
route.get(
    '/get_curriculum_by_program_id/:program_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_curriculum.get_curriculum_by_program_id,
);
route.delete('/:id', validator.id, digi_curriculum.delete_curriculum);
route.post('/upsert_standard_range_settings', digi_curriculum.upsert_standard_range_settings);

module.exports = route;
