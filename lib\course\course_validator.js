const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.course = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            courses_name: Joi.string().allow(' ').min(3).trim().error(error => {
                return error;
            }),
            study_year: Joi.number().min(1).error(error => {
                return error;
            }),
            study_level: Joi.number().min(1).error(error => {
                return error;
            }),
            order: Joi.number().min(1).error(error => {
                return error;
            }),
            courses_number: Joi.string().allow(' ').min(3).trim().error(error => {
                return error;
            }),
            duration: Joi.number().min(1).error(error => {
                return error;
            }),
            model: Joi.string().min(3).trim().error(error => {
                return error;
            }),
            theory_credit: Joi.number().min(0).max(1000).error(error => {
                return error;
            }),
            practical_credit: Joi.number().min(0).max(1000).error(error => {
                return error;
            }),
            clinical_credit: Joi.number().min(0).max(1000).error(error => {
                return error;
            }),
            elective: Joi.array().items({ topics: Joi.string().min(3).allow("") }).error(error => {
                return error;
            }),
            _administration_department_id: Joi.string().alphanum().length(24).allow('').error(error => {
                return error;
            }),
            _administration_division_id: Joi.string().alphanum().length(24).allow('').error(error => {
                return error;
            }),
            _administration_subject_id: Joi.string().alphanum().length(24).allow('').error(error => {
                return error;
            }),
            _participating_department_id: Joi.array().items(Joi.string().alphanum().length(24).allow('')).error(error => {
                return error;
            }),
            _participating_division_id: Joi.array().items(Joi.string().alphanum().length(24).allow('')).error(error => {
                return error;
            }),
            _participating_subject_id: Joi.array().items(Joi.string().alphanum().length(24).allow('')).error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.course_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.course_id_level = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            level: Joi.number().min(1).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.course_id_level_type = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            level: Joi.number().min(1).required().error(error => {
                return error;
            }),
            type: Joi.string().valid(constant.MODEL.COURSE, constant.MODEL.MODULE, constant.MODEL.ELECTIVE).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.course_id_sub_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            sub_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}