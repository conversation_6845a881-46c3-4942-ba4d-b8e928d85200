image: node:12.18.1

pipelines:
  branches:
    develop:
      - step:
          name: Deploy to dev server
          # define which caches to use
          caches:
            # provided by bitbucket to cache node_modules
            - node
          script:
            - pipe: atlassian/rsync-deploy:0.4.3
              variables:
                USER: $SSH_USER
                SERVER: $SSH_SERVER
                REMOTE_PATH: $REMOTE_PATH_DEV/digischedulerapi
                LOCAL_PATH: $BITBUCKET_CLONE_DIR/*
                DEBUG: 'true'
            - echo "Deployed successfully!"    
      - step:
          name: Restart app
          script:
            - echo 'Restarting app via pm2...'
            - pipe: atlassian/ssh-run:0.2.6
              variables:
                SSH_USER: $SSH_USER
                SERVER: $SSH_SERVER
                MODE: 'command'
                COMMAND: $REMOTE_PATH_DEV/digischedulerapi/remote-scripts.sh
            - echo 'Restarted successfully!'