const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { INSTITUTION, DEPARTMENT_SUBJECT, ACADEMIC, ADMIN } = require('../../utility/constants');
const { subjectSchema, sharedWithSchema } = require('./department-subject.schema');

const departmentSubjectSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _parent_id: {
            type: Schema.Types.ObjectId,
            default: null,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
        },
        programName: {
            type: String,
        },
        departmentName: {
            type: String,
        },
        subject: {
            type: [subjectSchema],
            default: [],
        },
        sharedWith: {
            type: [sharedWithSchema],
            default: null,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            enum: [ACADEMIC, ADMIN],
        },
    },
    {
        timestamps: true,
    },
);

module.exports = departmentSubjectSchema;
