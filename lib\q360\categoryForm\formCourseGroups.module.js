const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_CATEGORY,
    QAPC_FORM_SETTING,
    QAPC_FORM_SETTING_COURSES,
    QAPC_FORM_COURSES_GROUPS,
    ALL,
    EVERY,
    INDIVIDUAL,
    INSTITUTION,
    PUBLISHED,
    DRAFT,
    INSTITUTION_CALENDAR,
} = require('../../utility/constants');

const formCourseGroupSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        categoryId: { type: ObjectId, ref: QAPC_FORM_CATEGORY },
        categoryFormId: { type: ObjectId, ref: QAPC_FORM_SETTING },
        categoryFormCourseId: { type: ObjectId, ref: QAPC_FORM_SETTING_COURSES },
        term: { type: String },
        attemptTypeName: { type: String },
        executionsPer: {
            type: Boolean,
            default: false,
        },
        academicYear: {
            type: String,
        },
        group: {
            type: String,
        },
        status: { type: String, enum: [PUBLISHED, DRAFT], default: DRAFT },
        groupName: { type: String },
        minimum: { type: Number },
        startMonth: { type: Number },
        endMonth: { type: Number },
        createdDocument: [
            {
                institutionCalenderId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
                minimum: { type: Number },
            },
        ],
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_FORM_COURSES_GROUPS, formCourseGroupSchema);
