const Joi = require('joi');
const { MulterError } = require('multer');
const { DISCIPLINARY_REMARKS_TYPE } = require('../utility/constants');
const { multipleFileUploadWithRestrictions } = require('../utility/file_upload');

const addNewDisciplinaryRemarksValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                scheduleId: Joi.string().length(24),
                institutionCalendarId: Joi.string().length(24).required(),
                studentId: Joi.string().length(24).required(),
                tardisId: Joi.string().length(24).required(),
                comment: Joi.string().optional().allow(''),
                attachments: Joi.array(),
                reportedBy: Joi.string().length(24).required(),
                type: Joi.string()
                    .valid(
                        DISCIPLINARY_REMARKS_TYPE.INSTITUTION_LEVEL,
                        DISCIPLINARY_REMARKS_TYPE.SCHEDULE_LEVEL,
                    )
                    .required(),
            }),
        })
        .unknown(true);

    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const updateDisciplinaryRemarksValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                remarkId: Joi.string().length(24).required(),
                comment: Joi.string().optional().allow(''),
                tardisId: Joi.string().length(24).optional(),
                attachments: Joi.array().optional(),
                reportedBy: Joi.string().length(24).optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const deleteDisciplinaryRemarksValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                remarkId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);

    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const getStudentsDisciplinaryRemarksValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                studentId: Joi.string().length(24).required(),
                pageNo: Joi.number().required(),
                limit: Joi.number().required(),
                searchKey: Joi.string().optional().allow(''),
                remarkKeyId: Joi.string().length(24).optional().allow(''),
                staffId: Joi.string().length(24).optional().allow(''),
            }),
            Headers: Joi.object().keys({
                _institution_calendar_id: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const getScheduleDetailsValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                scheduleId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);

    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const fileUploadValidator = (req, res, next) => {
    multipleFileUploadWithRestrictions(req, res, (err) => {
        if (err instanceof MulterError) {
            return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
        }
        if (err) {
            return res.status(500).send('Please change file format and upload');
        }
        next();
    });
};

const sendRemarkMailValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                remarkId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const getSignedUrlsValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                fileUrls: Joi.array().required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getMailDetailsValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                remarkId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
module.exports = {
    addNewDisciplinaryRemarksValidator,
    updateDisciplinaryRemarksValidator,
    deleteDisciplinaryRemarksValidator,
    getStudentsDisciplinaryRemarksValidator,
    getScheduleDetailsValidator,
    fileUploadValidator,
    sendRemarkMailValidator,
    getSignedUrlsValidator,
    getMailDetailsValidator,
};
