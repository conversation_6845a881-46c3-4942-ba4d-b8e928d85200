const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const { addTypes, typeList, deleteType } = require('./announcement_setting_controller');
const { addTypeValidator, deleteTypeValidator } = require('./announcement_setting_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const { validate } = require('../../middleware/validation');

router.post(
    '/addTypes',
    validate([{ schema: addTypeValidator, property: 'body' }]),
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(addTypes),
);
router.get('/typeList', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], catchAsync(typeList));
router.put(
    '/deleteType',
    validate([{ schema: deleteTypeValidator, property: 'body' }]),
    [userPolicyAuthentication([])],
    catchAsync(deleteType),
);
module.exports = router;
