const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    stringRQSchema,
    booleanRQSchema,
    numberSchema,
    stringSchema,
    dateSchema,
    booleanSchema,
} = require('../../../utility/validationSchemas');
const optionalStringSchema = Joi.string().trim().optional();
const dateFieldsSchema = Joi.object({
    type: stringSchema,
    dateAndTime: dateSchema,
    duration: Joi.object({
        value: stringSchema,
        type: stringSchema,
    }),
    courseOrSession: Joi.object({
        type: stringSchema,
        after: stringSchema,
    }),
    previousDate: Joi.object({
        date: dateSchema,
        reason: stringSchema,
    }),
});
const studentSchema = Joi.object({
    studentId: stringRQSchema,
    name: stringRQSchema,
    academicId: stringRQSchema,
});
const ratGroupSchema = Joi.object({
    groupLeaderId: Joi.alternatives().try(objectIdSchema, stringRQSchema),
    groupStudents: Joi.array().items(studentSchema),
});

exports.createAssignmentValidator = Joi.object({
    assignments: Joi.array().items(
        Joi.object({
            _id: objectIdSchema,
            _institution_id: objectIdSchema,
            _institution_calendar_id: objectIdSchema,
            _course_id: objectIdSchema,
            courseId: objectIdRQSchema,
            programId: objectIdRQSchema,
            createdBy: objectIdRQSchema,
            course_code: stringRQSchema,
            courseName: stringRQSchema,
            programName: stringRQSchema,
            _program_id: objectIdSchema,
            contributor: objectIdSchema,
            year_no: stringRQSchema,
            level_no: stringRQSchema,
            rotation_count: numberSchema,
            term: stringRQSchema,
            subject: Joi.array().items(
                Joi.object({
                    subjectName: stringRQSchema,
                    subjectId: objectIdSchema,
                }),
            ),
            isDraft: booleanRQSchema,
            pause: booleanSchema,
            hide: booleanSchema,
            isDeleted: booleanSchema,
            deletedReason: stringSchema,
            isActive: booleanRQSchema,
            isRecurAssignment: booleanSchema,
            isDuplicate: booleanSchema,
            isCopyFrom: objectIdSchema,
            notificationStatus: stringRQSchema,
            reportCreated: booleanSchema,
            ratType: optionalStringSchema,
            ratId: objectIdSchema,
            basic: Joi.object({
                title: stringRQSchema,
                description: stringSchema,
                instructionAttachment: Joi.array().items(
                    Joi.object({
                        url: stringSchema,
                        signedUrl: stringSchema,
                        sizeInKb: numberSchema,
                        name: stringSchema,
                        _id: objectIdSchema,
                    }),
                ),
                type: stringRQSchema,
                category: Joi.object({
                    name: optionalStringSchema,
                    _id: Joi.string().alphanum().length(24).optional(),
                }),
                sessionUnit: Joi.array().items(
                    Joi.object({
                        _session_id: objectIdSchema,
                        session: stringSchema,
                        selectedSession: objectIdSchema,
                    }),
                ),
                totalScore: numberSchema,
                ratMarks: Joi.object({
                    individual: Joi.object({
                        mark: numberSchema,
                        weightage: Joi.alternatives().try(stringSchema, numberSchema),
                    }),
                    grouped: Joi.object({
                        mark: numberSchema,
                        weightage: Joi.alternatives().try(stringSchema, numberSchema),
                    }),
                }),
                scoringType: stringRQSchema,
                gradeAs: stringRQSchema,
                totalAttempts: numberSchema,
                allowAssignmentsInFinalGrades: booleanSchema,
                learningOutcome: Joi.object({
                    type: stringSchema,
                    lo: Joi.array().items(objectIdSchema),
                }),
                taxonomy: Joi.array().items(objectIdSchema),
                taxonomyScope: stringSchema,
                evaluationTools: Joi.array().items(stringSchema),
                prompts: Joi.array().items(stringSchema),
                isGroupProject: booleanSchema,
                evaluation: Joi.object({
                    type: stringSchema,
                    rubricsId: objectIdSchema,
                }),
                scoreUnit: stringSchema,
                allPrompts: Joi.array().items(stringSchema),
            }),
            submission: Joi.object({
                isGroupProject: booleanSchema,
                assignTo: Joi.array().items(
                    Joi.object({
                        isGroup: booleanSchema,
                        isAllStudent: booleanSchema,
                        studentIds: Joi.array().items(studentSchema),
                        groups: Joi.array().items(
                            Joi.object({
                                groupId: objectIdSchema,
                                name: stringSchema,
                                subGroup: Joi.array().items(
                                    Joi.object({
                                        subGroupId: objectIdSchema,
                                        subGroupName: stringSchema,
                                    }),
                                ),
                            }),
                        ),
                        specialDue: Joi.array().items(
                            Joi.object({
                                dueDate: dateSchema,
                                description: stringSchema,
                            }),
                        ),
                        ratGroups: Joi.array().items(ratGroupSchema),
                    }),
                ),
                specialDue: Joi.array().items(
                    Joi.object({
                        dueDate: dateSchema,
                        description: stringSchema,
                    }),
                ),
                isEnable: booleanSchema,
                isDue: booleanSchema,
                due: dateFieldsSchema,
                start: dateFieldsSchema,
                end: dateFieldsSchema,
                remindStudents: Joi.object({
                    checked: booleanSchema,
                    duration: stringSchema,
                }),
                recurring: Joi.object({
                    checked: booleanSchema,
                    type: stringSchema,
                    at: stringSchema,
                    days: Joi.array().items(stringSchema),
                    time: stringSchema,
                }),
                includePreviousDiscussions: booleanSchema,
                recurWhenThisCourseIsActive: booleanSchema,
                endIsChecked: booleanSchema,
                allStudents: Joi.array().items(studentSchema),
                type: stringSchema,
                startIsChecked: booleanSchema,
                ratSubmissionDates: Joi.object({
                    individual: Joi.object({
                        due: dateFieldsSchema,
                        start: dateFieldsSchema,
                        end: dateFieldsSchema,
                        remindStudents: Joi.object({
                            checked: booleanSchema,
                            duration: stringSchema,
                        }),
                        recurring: Joi.object({
                            checked: booleanSchema,
                            type: stringSchema,
                            at: stringSchema,
                            days: Joi.array().items(stringSchema),
                            time: stringSchema,
                        }),
                        includePreviousDiscussions: booleanSchema,
                        recurWhenThisCourseIsActive: booleanSchema,
                    }),
                    grouped: Joi.object({
                        due: dateFieldsSchema,
                        start: dateFieldsSchema,
                        end: dateFieldsSchema,
                        remindStudents: Joi.object({
                            checked: booleanSchema,
                            duration: stringSchema,
                        }),
                        recurring: Joi.object({
                            checked: booleanSchema,
                            type: stringSchema,
                            at: stringSchema,
                            days: Joi.array().items(stringSchema),
                            time: stringSchema,
                        }),
                        includePreviousDiscussions: booleanSchema,
                        recurWhenThisCourseIsActive: booleanSchema,
                    }),
                }),
            }),
            evaluation: Joi.object({
                evaluationBy: Joi.array().items(stringSchema),
                evaluators: Joi.array().items(
                    Joi.object({
                        evaluatorId: objectIdSchema,
                        evaluatorName: stringSchema,
                        assignmentGroupIds: Joi.array().items(stringSchema),
                        studentIds: Joi.array().items(stringSchema),
                        prompts: Joi.array().items(stringSchema),
                        isReconciler: booleanSchema,
                    }),
                ),
                gradingScore: stringSchema,
                dueDays: numberSchema,
                setAnonymousStudent: booleanSchema,
                setAnonymousStaff: booleanSchema,
                checkPlagiarism: booleanSchema,
                multiStaffMultiStudent: booleanSchema,
                isStudentsAnonymous: booleanSchema,
            }),
            reflection: Joi.object({
                addReflectionQuestions: booleanSchema,
            }),
            publishedAt: dateSchema,
            generalSettings: Joi.object({
                extendAssignmentLimit: booleanSchema,
                rubricsVisibleToStudent: booleanSchema,
                randomisePrompt: booleanSchema,
                studentSeeAfterAttempting: stringSchema,
                allowStudentToUnSubmit: booleanSchema,
                allowStudentToCheckPlagiarism: booleanSchema,
                allowStudentToAttachFile: booleanSchema,
                fileType: Joi.array().items(stringSchema),
                fileSizeLimit: stringSchema,
                addDiscussionThread: booleanSchema,
            }),
        }),
    ),
});

exports.duplicateAssignmentValidator = Joi.object({
    assignmentIds: Joi.array().items(objectIdRQSchema),
    courseId: objectIdSchema,
    courseCode: stringSchema,
    courseName: stringSchema,
    rotationCount: numberSchema,
    duplicateTo: stringSchema,
});

exports.resetAssignmentValidator = Joi.object({
    assignmentId: objectIdRQSchema,
});

exports.revertReleasedGradeValidator = Joi.object({
    assignmentId: objectIdRQSchema,
    studentId: objectIdRQSchema,
    staffId: objectIdRQSchema,
    isMultiStaff: booleanRQSchema,
});

const dateField = Joi.object()
    .keys({
        type: stringSchema,
        dateAndTime: dateSchema,
        duration: Joi.object().keys({
            value: stringSchema,
            type: stringSchema,
        }),
        courseOrSession: Joi.object().keys({
            type: stringSchema,
            after: stringSchema,
        }),
        previousDate: Joi.object().keys({
            date: dateSchema,
            reason: stringSchema,
        }),
    })
    .unknown(true);

exports.updateSubmissionDateValidator = Joi.object({
    submission: Joi.object()
        .keys({
            due: dateField,
            start: dateField,
            end: dateField,
            remindStudents: Joi.object().keys({
                checked: booleanSchema,
                duration: stringSchema,
            }),
            recurring: Joi.object().keys({
                checked: booleanSchema,
                type: stringSchema,
                at: stringSchema,
                days: Joi.array().items(stringSchema),
                time: stringSchema,
            }),
            includePreviousDiscussions: booleanSchema,
            recurWhenThisCourseIsActive: booleanSchema,
        })
        .unknown(true),
});

exports.paramIDValidator = Joi.object({
    id: objectIdRQSchema,
});
