const mongoose = require('mongoose');
const { DIGI_SURVEY, DIGI_SURVEY_SECTION, DIGI_SURVEY_QUESTION } = require('../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const digiSurveySectionSchemas = new Schema(
    {
        surveyId: {
            type: ObjectId,
            ref: DIGI_SURVEY,
        },
        sectionName: {
            type: String,
        },
        sectionDescription: {
            type: String,
        },
        sessionPosition: {
            type: String,
        },
        questionIds: [
            {
                type: ObjectId,
                ref: DIGI_SURVEY_QUESTION,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(DIGI_SURVEY_SECTION, digiSurveySectionSchemas);
