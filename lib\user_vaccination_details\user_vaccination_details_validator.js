const Joi = require('joi');
const {
    EVENT_WHOM: { STUDENT, STAFF, BOTH },
} = require('../utility/constants');
function getAddUserVaccinationDetailsValidate() {
    const schema = {
        body: Joi.object().keys({
            _user_id: Joi.string().alphanum().length(24).required(),
            user_type: Joi.string().valid(STUDENT, STAFF, BOTH).required(),
            vaccination_status: Joi.boolean().required(),
            vaccination_type_id: Joi.string().alphanum().length(24).when('vaccination_status', {
                is: true,
                then: Joi.required(),
                otherwise: Joi.optional(),
            }),
            vaccination_type: Joi.string().when('vaccination_status', {
                is: true,
                then: Joi.required(),
                otherwise: Joi.optional(),
            }),
            dosage_taken: Joi.array().items(
                Joi.object({
                    dosage_count: Joi.number().when('vaccination_status', {
                        is: true,
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    date: Joi.date().optional().allow(''),
                }),
            ),
            immunity_period_status: Joi.boolean().when('vaccination_status', {
                is: false,
                then: Joi.required(),
                otherwise: Joi.optional(),
            }),
            immunity_period_end_at: Joi.date().when('immunity_period_status', {
                is: true,
                then: Joi.required(),
                otherwise: Joi.optional().allow(''),
            }),
        }),
    };
    return schema;
}
module.exports = {
    getAddUserVaccinationDetailsValidate: getAddUserVaccinationDetailsValidate(),
};
