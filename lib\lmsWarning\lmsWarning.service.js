const {
    WARNING_CONFIG_TYPE: { COMPREHENSIVE },
} = require('../utility/constants');

const getCurrentDate = () => {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() - 1);
    return currentDate;
};

const getCurrentTime = () => new Date();

const filterWarningData = (studentWarningData, warningConfigBased) =>
    warningConfigBased
        ? studentWarningData.filter(
              (studentWarningElement) => studentWarningElement.warningMode === COMPREHENSIVE,
          )
        : studentWarningData;

const buildLevelDetailsMap = (studentCourseList) => {
    const levelDetails = new Map();
    studentCourseList.forEach((courseDetails) => {
        const filterKey = `${courseDetails._institution_calendar_id}${courseDetails._program_id}${courseDetails.term}${courseDetails.level}`;
        if (!levelDetails.has(filterKey)) {
            levelDetails.set(filterKey, {
                startDate: new Date(courseDetails.start_date),
                endDate: new Date(courseDetails.end_date),
                institutionCalendarId: courseDetails._institution_calendar_id,
                programId: courseDetails._program_id,
                term: courseDetails.term,
                year: courseDetails.year,
                level: courseDetails.level,
            });
        }
    });
    return levelDetails;
};

const updateStudentWarningElement = (studentWarningElement, user, studentWarning) => {
    if (user) {
        studentWarningElement.acknowledge = user.acknowledge;
        studentWarningElement.acknowledgedData = user.acknowledgedData;
    } else {
        studentWarningElement.acknowledge = false;
    }
    if (studentWarning) {
        if (studentWarning.isActive && user?.notificationInfo?.isSent) {
            studentWarningElement.isShownFor = true;
        }
        Object.assign(studentWarningElement, {
            warningName: studentWarning.labelName,
            colorCode: studentWarning.colorCode,
            session: studentWarning.warningValue,
            message: studentWarning.message,
            acknowledgeToStudent: studentWarning.acknowledgeToStudent,
            markItMandatory: studentWarning.markItMandatory,
            restrictCourseAccess: studentWarning.restrictCourseAccess,
            adminRestrictedAttendance: studentWarning.adminRestrictedAttendance,
        });
    }
};

const checkCourseActivity = (studentWarningElement, studentCourseDetail, currentTime) => {
    if (studentCourseDetail) {
        const courseStartDateTime = new Date(studentCourseDetail.startDate);
        const courseEndDateTime = new Date(studentCourseDetail.endDate);
        if (currentTime >= courseStartDateTime && currentTime <= courseEndDateTime) {
            studentWarningElement.isActiveCourse = true;
        }
    }
};

const updateWarningCalendarDetails = (studentWarningElement, institutionCalendar, currentDate) => {
    const warningCalendar = institutionCalendar.find(
        (calendarElement) =>
            String(calendarElement._id) === String(studentWarningElement.institutionCalendarId),
    );
    if (warningCalendar) {
        studentWarningElement.calendarEndDate = new Date(warningCalendar.end_date);
        studentWarningElement.calendarActive = warningCalendar.isActive;
        studentWarningElement.dashboardShown =
            warningCalendar.isActive && warningCalendar.end_date.getTime() >= currentDate.getTime();
    }
};

const filterAndSortWarnings = (studentWarningData, currentDate) => {
    studentWarningData = studentWarningData.filter(
        (studentWarningElement) => studentWarningElement.isShownFor,
    );
    studentWarningData.sort((a, b) => {
        if (a.calendarActive === b.calendarActive) {
            return b.calendarEndDate.getTime() - a.calendarEndDate.getTime();
        }
        return a.calendarActive ? -1 : 1;
    });
    return studentWarningData;
};

const checkAllAcknowledgements = (studentWarningData, currentDate) =>
    !studentWarningData
        .filter(
            (data) =>
                data.isActiveCourse && data.calendarEndDate.getTime() >= currentDate.getTime(),
        )
        .find((element) => element.acknowledge === false && element.markItMandatory === true);

const comprehensiveWarning = ({
    studentId,
    studentWarningData,
    warningConfigBased,
    warningConfig,
    studentCourseList,
    institutionCalendar,
}) => {
    const currentDate = getCurrentDate();
    const currentTime = getCurrentTime();
    studentWarningData = filterWarningData(studentWarningData, warningConfigBased);
    const levelDetails = buildLevelDetailsMap(studentCourseList);
    console.time('Warning iteration');
    studentWarningData.forEach((studentWarningElement) => {
        studentWarningElement.isShownFor = false;
        studentWarningElement.level = studentWarningElement.levelNo;
        studentWarningElement.year = studentWarningElement.yearNo;
        delete studentWarningElement.levelNo;
        delete studentWarningElement.yearNo;
        const user = studentWarningElement.user?.find(
            (userElement) => String(userElement.userId) === String(studentId),
        );
        const studentWarning = warningConfig.find(
            (warningConfigElement) =>
                String(warningConfigElement._id) === String(studentWarningElement.warningId),
        );
        updateStudentWarningElement(studentWarningElement, user, studentWarning);
        delete studentWarningElement.user;
        const filterKey = `${studentWarningElement.institutionCalendarId}${studentWarningElement.programId}${studentWarningElement.term}${studentWarningElement.level}`;
        const studentCourseDetail = levelDetails.get(filterKey);
        checkCourseActivity(studentWarningElement, studentCourseDetail, currentTime);
        updateWarningCalendarDetails(studentWarningElement, institutionCalendar, currentDate);
    });
    console.timeEnd('Warning iteration');
    studentWarningData = filterAndSortWarnings(studentWarningData, currentDate);
    const allAcknowledgement = checkAllAcknowledgements(studentWarningData, currentDate);

    return { studentWarningData, allAcknowledgement };
};

module.exports = { comprehensiveWarning };
