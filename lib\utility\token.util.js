const JWT = require('jsonwebtoken');
const ms = require('ms');
const { encryptData, decryptData } = require('./encrypt_decrypt.util');
const { redisClient } = require('../../config/redis-connection');
const { tokenUserRedisCacheKey, getSessionUUIDFromRedis } = require('./utility.service');
const { v4: uuidv4 } = require('uuid');
const { AUTH_SESSION } = require('./util_keys');
const _getExpiresAtDate = ({ expiresIn = '1h' }) => {
    const inMs = ms(expiresIn);
    return new Date(Date.now() + inMs);
};

/**
 * Generate token
 * @param {string} userId
 * @param {string} _id
 * @param {string} userType
 * @param {string} userRoleData
 * @param {string} sessionUUID
 * @param {string} expiresIn
 * @param {string} [secret]
 * @returns {string}
 */
async function _generateToken({
    userId,
    _id,
    userType,
    userRoleData,
    sessionUUID,
    expiresIn = process.env.JWT_ACCESS_DURATION,
    secret = process.env.JWT_SECRET,
}) {
    const payload = {
        payload: encryptData({
            content: {
                userId,
                _id,
                userType,
                userRoleData,
                sessionUUID,
            },
        }),
    };

    return JWT.sign(payload, secret, { expiresIn, algorithm: 'HS512' });
}

/**
 * Verify token and return payload (or throw an error if it is not valid)
 * @param {string} token
 * @returns {Promise<Token>}
 */
async function verifyToken({ token }) {
    const payload = JWT.verify(token, process.env.JWT_SECRET);

    return payload;
}

/**
 * Generate auth tokens
 * @param {User} user
 * @returns {Promise<Object>}
 */
async function generateAuthTokens({
    userId = '',
    _id = '',
    userType = '',
    userRoleData = [],
    sessionUUID = '',
}) {
    const jwtAccessDuration = process.env.JWT_ACCESS_DURATION;
    const jwtRefreshDuration = process.env.JWT_REFRESH_DURATION;
    const accessToken = await _generateToken({ userId, _id, userType, userRoleData, sessionUUID });
    const refreshToken = await _generateToken({
        userId,
        _id,
        userType,
        userRoleData,
        sessionUUID,
        expiresIn: jwtRefreshDuration,
        secret: process.env.JWT_SECRET,
    });
    // Convert JWT duration to seconds for Redis EX
    const redisExpiryInSeconds = Math.floor(ms(jwtAccessDuration) / 1000);
    await redisClient.Client.set(
        tokenUserRedisCacheKey({ userId: _id }),
        sessionUUID,
        'EX',
        redisExpiryInSeconds,
    );
    return {
        access: {
            token: accessToken,
            expires: _getExpiresAtDate({ expiresIn: jwtAccessDuration }),
        },
        refresh: {
            token: refreshToken,
            expires: _getExpiresAtDate({ expiresIn: jwtRefreshDuration }),
        },
    };
}

/**
 * Verify refresh token and return payload (or throw an error if it is not valid)
 * @param {string} token
 * @returns {Promise<Token>}
 */
async function verifyRefreshToken(req, res) {
    if (req.headers && req.headers.authorization) {
        const authorization = req.get('authorization');
        const token = authorization.split('Bearer ')[1];
        JWT.verify(token, process.env.JWT_SECRET, async (err, decode) => {
            if (err || !decode?.payload) {
                return res.status(401).send({
                    status_code: 401,
                    status: false,
                    message: 'Please authenticate',
                    data: 'Authentication code not matching',
                });
            }
            const decryptPayload = decryptData({ content: decode?.payload });
            if (AUTH_SESSION === 'true') {
                const userSessionUUID = await getSessionUUIDFromRedis({
                    userId: decryptPayload?._id,
                });
                if (userSessionUUID !== decryptPayload.sessionUUID) {
                    return res.status(401).send({
                        status_code: 401,
                        status: false,
                        message: 'Please authenticate',
                        data: 'Authentication code not matching',
                    });
                }
            }
            decryptPayload.sessionUUID = uuidv4();
            const tokens = await generateAuthTokens(decryptPayload);
            return res.status(200).send({
                status_code: 200,
                status: true,
                message: 'Refreshed new token',
                data: { tokens },
            });
        });
    }
}
module.exports = {
    generateAuthTokens,
    verifyToken,
    verifyRefreshToken,
    _generateToken,
};
