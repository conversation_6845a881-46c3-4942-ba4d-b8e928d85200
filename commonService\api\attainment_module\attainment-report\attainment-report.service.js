const { convertToMongoObjectId, query } = require('../../../utility/common');
const {
    LOCAL,
    DIGI_PROGRAM,
    PROGRAM_CALENDAR,
    DIGI_CURRICULUM,
    DIGI_COURSE,
    INSTITUTION_CALENDAR,
    ROLE_ASSIGNS,
    COURSE_SCHEDULE,
    COURSE_COORDINATOR,
} = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const {
    programWithTermFormatting,
    courseLevelTermFormatting,
    programPLOFormatting,
    coursesCLOsFormatting,
    InstitutionCalendarsFormatting,
    yearLevelCourseFormatting,
    userProgramIdsFormatting,
} = require('../../serviceAdapter/adapter.formatter');
// const {} = require('../../serviceAdapter/adapter.formatter');
const programSchema = require('mongoose').model(DIGI_PROGRAM);
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const curriculumSchema = require('mongoose').model(DIGI_CURRICULUM);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const institutionCalendarSchema = require('mongoose').model(INSTITUTION_CALENDAR);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGNS);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);

const getProgramWithTermList = async ({ _institution_id, userRoleProgram }) => {
    try {
        let programList;
        if (BASIC_DATA_FROM === LOCAL) {
            programList = await programSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: { $in: userRoleProgram },
                    },
                    { name: 1, 'term.term_name': 1 },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        return programWithTermFormatting({ programList, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLevelCourseList = async ({
    _institution_id,
    programId,
    institutionCalendarId,
    term,
    userProgramCourses,
}) => {
    try {
        let courseList;
        if (BASIC_DATA_FROM === LOCAL) {
            courseList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        'level.level_no': 1,
                        'level.rotation': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        'level.rotation_course.rotation_count': 1,
                        'level.rotation_course.course._course_id': 1,
                        'level.rotation_course.course.courses_name': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!courseList) return [];
        // return courseList;
        return courseLevelTermFormatting({
            courseList,
            term,
            userProgramCourses,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramPLOWithCLO = async ({
    _institution_id,
    programId,
    institutionCalendarId,
    curriculumId,
}) => {
    try {
        let programCurriculum;
        let yearLevelList;
        if (BASIC_DATA_FROM === LOCAL) {
            programCurriculum = await curriculumSchema
                .findOne(
                    {
                        ...query,
                        _id: convertToMongoObjectId(curriculumId),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                    },
                    {
                        curriculum_name: 1,
                        'framework.domains.plo._id': 1,
                        'framework.domains.plo.no': 1,
                        'framework.domains.plo.name': 1,
                        'framework.domains.plo.isActive': 1,
                        'framework.domains.plo.isDeleted': 1,
                        'framework.domains.plo.clos.clo_id': 1,
                        'framework.domains.plo.clos.no': 1,
                        'framework.domains.plo.clos.level_name': 1,
                        'framework.domains.plo.clos.mapped_value': 1,
                        'framework.domains.plo.clos.content_mapped_value': 1,
                    },
                )
                .lean();
            yearLevelList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        'level.year': 1,
                        'level.level_no': 1,
                        'level.curriculum': 1,
                        'level.rotation': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        // 'level.course.courses_number': 1,
                        'level.rotation_course.rotation_count': 1,
                        'level.rotation_course.course._course_id': 1,
                        'level.rotation_course.course.courses_name': 1,
                        // 'level.rotation_course.course.courses_number': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        // return { programCurriculum, yearLevelList };
        if (programCurriculum === null || yearLevelList === null) return undefined;
        return programPLOFormatting({
            programCurriculum,
            yearLevelList,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCoursesCLOs = async ({ _institution_id, courseIds }) => {
    try {
        let coursesCLO;
        if (BASIC_DATA_FROM === LOCAL) {
            coursesCLO = await courseSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: { $in: courseIds },
                    },
                    {
                        'framework.domains.clo._id': 1,
                        // 'framework.domains.clo.no': 1,
                        // 'framework.domains.clo.name': 1,
                        'framework.domains.clo.isActive': 1,
                        'framework.domains.clo.isDeleted': 1,
                        'course_assigned_details._curriculum_id': 1,
                        // 'course_assigned_details._level_id': 1,
                        'course_assigned_details.level_no': 1,
                        'course_assigned_details.mapping_type': 1,
                        'course_assigned_details.content_mapping_type': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!coursesCLO) return [];
        // return coursesCLO;
        return coursesCLOsFormatting({ coursesCLO, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstitutionCalendars = async ({ _institution_id, institutionCalendarId }) => {
    try {
        let calendarData;
        if (BASIC_DATA_FROM === LOCAL) {
            calendarData = await institutionCalendarSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        status: 'published',
                    },
                    { _id: 1, calendar_name: 1 },
                )
                .sort({ _id: -1 })
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        return InstitutionCalendarsFormatting({
            calendarData,
            institutionCalendarId,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getYearLevelCourseList = async ({ programId, institutionCalendarId, term }) => {
    try {
        let courseList;
        if (BASIC_DATA_FROM === LOCAL) {
            courseList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        'level.year': 1,
                        'level.level_no': 1,
                        'level.rotation': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        'level.course.courses_number': 1,
                        'level.course.model': 1,
                        'level.rotation_course.rotation_count': 1,
                        'level.rotation_course.course._course_id': 1,
                        'level.rotation_course.course.courses_name': 1,
                        'level.rotation_course.course.courses_number': 1,
                        'level.rotation_course.course.model': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!courseList) return [];
        // return courseList;
        return yearLevelCourseFormatting({ courseList, term, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserRoleProgramListIds = async ({ _institution_id, user_id, role_id }) => {
    try {
        let roleAssignData;
        let courseCoordinatorData;
        let courseScheduleData;
        let isCourseAdmin = false;
        let isProgramAdmin = false;
        if (BASIC_DATA_FROM === LOCAL) {
            roleAssignData = await roleAssignSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _user_id: convertToMongoObjectId(user_id),
                    },
                    {
                        'roles._id': 1,
                        'roles._role_id': 1,
                        'roles.role_name': 1,
                        'roles.isAdmin': 1,
                        'roles.program._program_id': 1,
                    },
                )
                .lean();
            if (roleAssignData && roleAssignData.roles) {
                const userRoleData = roleAssignData.roles.find(
                    (roleElement) => roleElement._role_id.toString() === role_id,
                );
                isProgramAdmin = userRoleData && userRoleData.isAdmin;
                isCourseAdmin = userRoleData && userRoleData.role_name === COURSE_COORDINATOR;
            }
            if (isCourseAdmin) {
                courseCoordinatorData = await courseSchema
                    .find(
                        {
                            ...query,
                            _institution_id: convertToMongoObjectId(_institution_id),
                            // 'coordinators._institution_calendar_id':
                            //     convertToMongoObjectId(institutionCalendarId),
                            'coordinators._user_id': convertToMongoObjectId(user_id),
                        },
                        {
                            _program_id: 1,
                        },
                    )
                    .lean();
                roleAssignData = undefined;
            }
            if (!isCourseAdmin && !isProgramAdmin)
                courseScheduleData = await courseScheduleSchema
                    .distinct('_program_id', {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        // _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        type: 'regular',
                        'staffs._staff_id': convertToMongoObjectId(user_id),
                    })
                    .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        // return { roleAssignData, courseCoordinatorData /* courseScheduleData */ };
        return userProgramIdsFormatting({
            roleAssignData,
            role_id,
            courseCoordinatorData,
            courseScheduleData,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getProgramWithTermList,
    getLevelCourseList,
    getProgramPLOWithCLO,
    getCoursesCLOs,
    getInstitutionCalendars,
    getYearLevelCourseList,
    getUserRoleProgramListIds,
};
