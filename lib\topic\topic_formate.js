let course_formate = require('../course/course_formate');

module.exports = {
    topic: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                label: element.label,
                content: element.content,
                contact_hours: element.contact_hours,
                course: course_formate.course_ID_Only(element.course),
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    topic_ID: (doc) => {
        let obj = {
            _id: doc._id,
            label: doc.label,
            content: doc.content,
            contact_hours: doc.contact_hours,
            course: course_formate.course_ID_Only(doc.course),
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    topic_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            label: doc.label,
            content: doc.content,
            contact_hours: doc.contact_hours,
            course: doc._course_id,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    topic_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                label: element.label,
                content: element.content,
                contact_hours: element.contact_hours,
                course: element._course_id,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}