{"ROLE_LIST": "Role List", "FAILED_TO_LIST_INSTITUTES": "Failed to list institutes", "FAILED_TO_GET_INSTITUTE": "Failed to get institute", "INSTITUTE_WITH_SAME_CODE_ACCREDITATION_ALREADY_EXISTS": "An institute with same code or accreditation already exists", "INSTITUTE_WITH_SAME_CODE_ALREADY_EXISTS": "An institute with same code already exists", "INSTITUTE_WITH_SAME_ACCREDITATION_ALREADY_EXISTS": "An institute with same accreditation already exists", "INSTITUTION_UPDATED": "Institution updated successfully", "INSTITUTION_CREATE_FAILED": "Failed to create institute", "INSTITUTION_ADDED": "Institution added successfully", "FAILED_TO_UPDATE_INSTITUTE": "Failed to update institute", "FAILED_TO_ADD_INSTITUTE_DESCRIPTION": "Failed to add institute description", "INSTITUTE_DESCRIPTION_ADDED": "Institute description added", "FAILED_TO_ADD_INSTITUTE_PORTFOLIO": "Failed to add institute portfolio", "INSTITUTE_PORTFOLIO_ADDED": "Institute portfolio added", "FAILED_TO_EDIT_INSTITUTE_PORTFOLIO": "Failed to edit institute portfolio", "INSTITUTE_PORTFOLIO_EDITED": "Institute portfolio added", "FAILED_TO_REMOVE_PORTFOLIO_MEDIA": "Failed to remove portfolio media", "PORTFOLIO_MEDIA_REMOVED": "Portfolio media removed", "FAILED_TO_REMOVE_INSTITUTE_DESCRIPTION": "Failed to remove institute description", "REMOVED_INSTITUTE_DESCRIPTION": "Removed institute description", "FAILED_TO_REMOVE_PORTFOLIO": "Failed to remove portfolio", "PORTFOLIO_REMOVED": "Portfolio removed", "INSTITUTE_DELETE_FAILED": "Institute delete failed", "INSTITUTE_DELETED_SUCCESSFULLY": "Institute deleted successfully", "INSTITUTE_LOGO_UPDATE_FAILED": "Institute logo update failed", "INSTITUTE_LOGO_UPDATED": "Institute logo updated", "FAILED_TO_GET_INSTITUTE_LOGO": "Failed to institute logo", "INSTITUTE_ARCHIEVED_SUCCESSFULLY": "College archieved successfully", "INSTITUTE_ARCHIEVE_FAILED": "College archieve failed", "INSTITUTE_RESTORED_SUCCESSFULLY": "College restored successfully", "INSTITUTE_RESTORE_FAILED": "College restore failed", "YOUR_ARE_EXCEEDING_THE_NUMBER_OF_INSTITUTION": "You are exceeding the number of institutes that can be created", "INSTITUTE_CANNOT_BE_DELETED": "Institute cannot be deleted since it has child institutes", "SESSTION_TYPE_CREATION_FAILED": "Failed to create session type", "SESSION_TYPE_CREATED": "Session type created", "SESSTION_TYPE_NOT_FOUND": "Session type not found", "DELIVERY_TYPE_CREATED": "delivery type created", "FALED_TO_CREATE_DELIVERY_TYPE": "Failed to create delivery type", "SESSION_TYPE_UPDATED_SUCCESSFULLY": "Session type updated successfully", "FAILED_TO_UPDATE_SESSION_TYPE": "Failed to update Session Type", "DELIVERY_TYPE_UPDATED_SUCCESSFULLY": "Delivery type updated successfully", "FAILED_TO_UPDATE_DELIVERY_TYPE": "Failed to update delivery type", "FAILED_TO_DELETE_SESSION_TYPE": "Failed to delete session type", "SESSION_TYPE_DELETED_SUCCESSFULLY": "session type deleted successfully", "DELIVERY_TYPE_DELETED_SUCCESSFULLY": "Delivery Type Deleted successfully", "FAILED_TO_DELETE_DELIVERY_TYPE": "Failed to delete Delivery Type", "NO_DATA_FOUND": "No Data Found", "DATA_FOUND": "Data found", "VALIDATION_ERROR": "Validation error", "DUPLICATE_CURRICULUM_NAME": "Duplicate curriculum name", "CHECK_CREDIT_HOURS_DATA": "Check credit hours data", "UNABLE_TO_ADD_CURRICULUM": "Unable to add curriculum", "CURRICULUM_ADDED_SUCCESSFULLY": "Curriculum added successfully", "UNABLE_TO_UPDATE_CURRICULUM": "Unable to updated curriculum", "CURRICULUM_UPDATED_SUCCESSFULLY": "Curriculum updated successfully", "CURRICULUM_LIST": "Curriculum list", "Error": "Error", "CURRICULUM_DETAILS": "Curriculum details", "UNABLE_TO_DELETE_CURRICULUM": "Unable to delete curriculum", "CURRICULUM_DELETED_SUCCESSFULLY": "Curriculum deleted successfully", "UNABLE_TO_ARCHIVE_CURRICULUM": "Unable to archive curriculum", "CURRICULUM_ARCHIVED_SUCCESSFULLY": "Curriculum archived successfully", "CURRICULUM_ARCHIVED_LIST": "Curriculum archived list", "LEVEL_UPDATED_SUCCESSFULLY": "level updated successfully", "NO_PROGRAMS": "No programs", "PROGRAM_LIST": "Program list", "INSTITUTION_SETTINGS_NOT_FOUND": "Institution settings not found. Configure global configuration settings.", "PROGRAM_CODE_EXISTS": "A program with same code already exists", "PROGRAM_CODE_OR_NAME_EXISTS": "A program with same name or code exists", "PROGRAM_NAME_EXISTS": "A program with same name exists", "ADD_PROGRAM_FAILED": "Failed to add program", "PROGRAM_ADDED": "Program added", "FAILED_TO_LIST_PROGRAMS": "Failed to list programs", "FAILED_TO_EDIT_PROGRAM": "Failed to edit program", "PROGRAM_EDITED": "Program edited", "CANNOT_ARCHIEVE_PROGRAM_COURSES_ARE_SCHEDULED": "Cannot archieve program. program courses are scheduled", "FAILED_TO_ARCHIEVE_PROGRAM": "Failed to archieve program", "PROGRAM_ARCHIEVED": "Program archieved", "FAILED_TO_DELETE_PROGRAM": "Failed to delete program", "PROGRAM_DELETED": "Program deleted", "FAILED_TO_UPDATE_PROGRAM_GOALS": "Failed to update program goals", "PROGRAM_GOALS_UPDATED": "program goals updated", "FAILED_TO_UPDATE_PROGRAM_DESCRIPTION": "Failed to update program description", "PROGRAM_DESCRIPTION_UPDATED": "Program description updated", "FAILED_TO_ADD_PROGRAM_PORTFOLIO": "Failed to add program portfolio", "PROGRAM_PORTFOLIO_ADDED": "Program portfolio added", "FAILED_TO_EDIT_PROGRAM_PORTFOLIO": "Failed to edit program portfolio", "PROGRAM_PORTFOLIO_EDITED": "Program portfolio edited", "THERE_ARE_NO_DEPARTMENTS_UNDER_THIS_PROGRAM": "There are no departments under this program", "PROGRAM_COULD_NOT_BE_CONFIGURED": "program could not be configured", "PROGRAM_CONFIGURED": "Program configured", "BREAK_NAME_ALREADY_EXISTS": "Break name already exists", "BREAK_NOT_FOUND": "Break not found", "EVENT_TYPE_NAME_ALREADY_EXISTS": "Event type name already exists", "EVENT_TYPE_NOT_FOUND": "Event type not found", "COURSE_CODE_ALREADY_EXISTS": "Course code already exists", "COURSE_CREATED_SUCCESSFULLY": "course created successfully", "FAILED_TO_CREATE_COURSE": "Failed to create course", "FAILED_TO_LIST_SESSIONS": "Failed to list sessions", "FAILED_TO_ADD_SESSION": "Failed to add session", "SESSION_ADDED": "Session added", "FAILED_TO_EDIT_SESSION": "Failed to edit session", "SESSION_EDITED": "Session edited", "FAILED_TO_DELETE_SESSION": "Failed to delete session", "SESSION_DELETED": "Session deleted", "FAILED_TO_RESTORE_PROGRAM": "Failed to restore program", "PROGRAM_RESTORED": "Program restored", "UNABLE_TO_ADD_COURSE_THEME": "Unable to add course theme", "COURSE_THEME_ADDED_SUCCESSFULLY": "Course theme added successfully", "UNABLE_TO_LIST_COURSE_THEMES": "Unable to list course theme", "COURSE_THEMES_LIST": "Course theme list", "UNABLE_TO_GET_COURSE_THEME": "Unable to get course theme", "COURSE_THEME": "Course theme", "UNABLE_TO_DELETE_COURSE_THEME": "Unable to delete course theme", "COURSE_THEME_DELETED_SUCCESSFULLY": "Course theme deleted successfully", "UNABLE_TO_GET_SUB_COURSE_THEME": "Unable to get sub course theme", "SUB_COURSE_THEME": "Sub course theme", "FAILED_TO_GET_COURSE": "Failed to get course", "COURSE_UPDATED_SUCCESSFULLY": "course updated successfully", "FAILED_TO_UPDATE_COURSE": "Failed to update course", "PORTFOLIO_UPDATED_SUCCESSFULLY": "Portfolio updated successfully", "PORTFOLIO_DELETED_SUCCESSFULLY": "Portfolio deleted successfully", "FAILED_TO_UPDATE_PORTFOLIO": "Failed to update portfolio ", "UPDATE_SUCCESSFULLY": "update successfully", "FAILED_TO_UPDATE": "Failed to update", "PAGENO_REQUIRED": "pageNo is required in query", "LIMIT_REQUIRED": "limit is required in query", "INSTITUTE_ID_REQUIRED": "Institute id required in params should be a valid mongodb id", "INSTITUTE_NAME_VALIDATION": "Institute name is required and must be 1-150 characters long and must contain only alphabets and numbers", "INSTITUTE_TYPE_VALIDATION": "Institute type is required and should be either of University or College", "INSTITUTE_CODE_VALIDATION": "Institute code is required must be 1-20 characters long", "ACCREDITATION_VALIDATION": "Accreditation is required and must be a string", "NO_OF_COLLEGES_VALIDATION": "Number of colleges must be an integer greater than or equal to 0", "ADDRESS_VALIDATION": "Address must be 1-150 characters long", "COUNTRY_VALIDATION": "Country must be a string", "COUNTRY_ID_VALIDATION": "Country Id must be an integer", "STATE_VALIDATION": "State must be a string", "STATE_ID_VALIDATION": "State Id must be an integer", "DISTRICT_VALIDATION": "District must be a string", "CITY_VALIDATION": "City must be a string", "ZIPCODE_VALIDATION": "Zip code is a string and must be 1-25 characters long", "ISUNIVERSITY_VALIDATION": "Is University field must be a boolean", "PARENT_INSTITUTE_VALIDATION": "Parent institute must be a valid mongodb id", "INSTITUTE_DESCRIPTION_VALIDATION": "Institute description is required and must be a string", "PORTFOLIO_TITLE_VALIDATION": "Portfolio title is required and must be a string", "PORTFOLIO_DESCRIPTION_VALIDATION": "Portfolio description is required and must be a string", "PORTFOLIO_ID_VALIDATION": "Portfolio id is required and must be a valid mongodb id", "FAILED_TO_LINK_SESSIONS": "Failes to link sessions", "LINKED_SESSIONS": "Linked sessions", "FAILED_TO_DELETE_LINK_SESSIONS": "Failed to delete link sessions", "LINK_SESSIONS_DELETED": "Link sessions deleted", "FAILED_TO_UPDATE_LINK_SESSIONS": "Failed to update link sessions", "LINK_SESSIONS_UPDATED": "Link sessions updated", "NO_SESSIONS": "No sessions", "LIST_UNLINKED_SESSIONS": "List unlinked sessions", "INSTITUTE_PORTFOLIO_MEDIA": "Institute portfolio media url must be a string", "INSTITUTE_DESCRIPTION_MEDIA": "Institute description media url must be a string", "INSTITUTE_LOGO": "Institute logo url must be a string", "INSTITUTION_NOT_FOUND": "Institute not found", "NO_OF_TERMS": "Terms for this program should be equal to number of terms", "PROGRAM_ID_REQUIRED": "Program id is required", "PROGRAM_NAME_REQUIRED": "Program name is required and should be length 3 to 60", "PROGRAM_TYPE_REQUIRED": "Program type is required", "TYPE_REQUIRED": "Type of program is required", "PROGRAM_CODE_REQUIRED": "Program code is required", "NO_OF_TERMS_REQUIRED": "Number of terms is required", "TERM_NO_REQUIRED": "Term number is required", "TERM_NAME_REQUIRED": "Term name is required", "TERM_REQUIRED": "program terms are required", "PROGRAM_LEVEL": "Program level is required", "PROGRAM_DEGREE": "Program degree is required", "INSTITUTION_ID_REQUIRED": "Institution id is required", "PROGRAM_TYPE": "program type must be a string", "PROGRAM_SEARCH": "Program search key must be a string", "BREAK_NAME_REQUIRED": "Break name is required", "BREAK_DAYS_REQUIRED": "Break days are required", "BREAK_ID_REQUIRED": "Break id is required", "EVENT_NAME_TYPE_REQUIRED": "Event type is required", "EVENT_IS_LEAVE_REQUIRED": "Event isLeave field required", "CURRICULUM_ID_REQUIRED": "Curriculum id is required", "CREDIT_HOURS_ID_REQUIRED": "Credit hours id is required", "PROGRAM_DOES_NOT_EXISTS": "Program does not exists", "GOAL_CONTENT_VALIDATION": "Goal content must be a string", "MEDIA_URL": "Media url must be a string", "YEAR_ID_REQUIRED": "year id is required", "LEVEL_ID_REQUIRED": "level id is required", "DATA_RETRIEVED": "Data retrieved", "FAILED_TO_GET_LIST": "Failed to get list", "DS_DATA_RETRIEVED": "Data retrieved", "DS_NOT_FOUND": "Not found", "DS_NO_DATA_FOUND": "No data found", "DS_ADDED": "Added successfully", "DS_UPDATED": "Updated successfully", "DS_DELETED": "Deleted successfully", "DS_ADD_FAILED": "Failed to add", "DS_UPDATE_FAILED": "Failed to update", "DS_DELETE_FAILED": "Failed to delete", "DS_GET_FAILED": "Failed to get data", "DS_CREATED": "Created successfully", "DS_CREATE_FAILED": "Failed to create", "DS_ARCHIVED": "Archived successfully", "DS_ARCHIVED_FAILED": "Failed to arch<PERSON>ve", "DS_RESTORE": "Restored successfully", "DS_RESTORE_FAILED": "Failed to restore", "SESSION_NAME_REQUIRED": "Session Name required", "SESSION_SYMBOL_REQUIRED": "Session Symbol required", "CONTACT_HOURS_OR_PREIOD_OF_WEEK": "Contact Hours or Period of Week required", "SESSIONOR_PERIOD_DURATION_REQUIRED": "Session Or Period Duration required", "CREDIT_HOURS_SHOULD_BE_STANDARD_DYNAMIC": "Credit Hours SHould be standard or dynamic", "DELIVERY_NAME_REQUIRED": "Delivery Name required", "DELIVERY_SYMBOL_REQUIRED": "Delivery Symbol required", "DELIVERY_DURATION_REQUIRED": "Delivery Duration required ", "DURATION_REQUIRED": "Duration required and must be number", "SESSION_ID_REQUIRED": "Session Id required", "DELIVERY_ID_REQUIRED": "Delivery Id required", "COURSE_CODE_REQUIRED": "Course Code required", "COURSE_NAME_REQUIRED": "Course Name required", "COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE": "Course Type must be standard or selective", "DEPARTMENT_ID_REQUIRED": "Department Id required", "DEPARTMENT_NAME_REQUIRED": "Department name is required and should be length 3 to 150", "SUBJECT_ID_REQUIRED": "Subject Id required", "SUBJECT_NAME_REQUIRED": "Subject Name required", "COURSE_ID_REQUIRED": "Course Id required", "TYPE_MUST_BE_COURSE_OR_CONFIGURATION": "type must be course or configuration", "PORTFOLIO_ID_REQUIRED": "Portfolio id required", "DELIVERY_TYPE_REQUIRED": "Delivery type required", "CURRICULUM_NAME_REQUIRED": "Curriculum name required", "SELECTIVE_MIN_VALUE_REQUIRED": "Selective value minimum 1 required", "HOURS_TYPE_MUST_BE": "Hours type must be total / split-up ", "NO_OF_SELECTION": "Number Of selection needed ", "SELECTIVE_ID_REQUIRED": "Selective Id required ", "LABEL_NAME_SHOULD_BE_ALPHABET": "Label Name should be only alphabet ", "DEPARTMENT_SUBJECT_TYPE_REQUIRED": "Type of department is required and must be either of academic or admin", "DEPARTMENT_NAME_EXISTS": "Department name already exists", "ASSIGN_ID_REQUIRED": "Assign Id required", "INSTITUTION_SEARCH_VALIDATION": "Search key must be a string", "INSTITUTION_SORT_VALIDATION": "Institution sort key must be a boolean", "INSTITUTION_STATUS_VALIDATION": "Institution status key must be either of active or archived", "SUBJECTS_NAME_VALIDATION": "Subjects already exists", "FAILED_TO_GET_SETTINGS": "Failed to get settings", "DS_SAVED": "Saved successfully", "DS_SAVE_FAILED": "Failed to save", "DEPARTMENT_NOT_FOUND": "Department not found", "SESSION_NAME_ALREADY_EXISTS": "Session name already exists", "SESSION_SYMBOL_ALREADY_EXISTS": "session symbol already exists", "DELIVERY_NAME_ALREADY_EXISTS": "Delivery name already exists", "DELIVERY_SYMBOL_ALREADY_EXISTS": "Delivery Symbol already exists", "PROGRAM_TAB": "Tab values should be either of archived or active", "NO_OF_LEVEL": "no of level is required", "DUPLICATE_SUBJECT_NAMES": "Subject names are same", "DUPLICATE_NAMES": "Names are same", "NAME_EXISTS": "Name already exists", "CODE_EXISTS": "Code exists", "INDEPENDENT_COURSE_TAB": "Tab is must be either of ADDED or DRAFTED", "SEARCH_KEY": "Search key must be a string", "CURRICULUM_IS_MAPPED_WITH_ANOTHER_PROGRAM_CURRICULUM": "Curriculum is mapped with another program curriculum", "SESSION_TYPE_ALREADY_MAPPED_WITH_CURRICULUM": "Session type already mapped with curriculum", "SUBJECT_IS_ASSIGNED_TO_COURSE": "Subject is assigned to course", "DELIVERY_TYPE_IS_ADDED_IN_SESSION_ORDER": "Delivery type is added in session order", "DEPARTMENT_IS_ASSIGNED_TO_COURSE": "Department is assigned to course", "DUPLICATE_COURSE_NAME": "Duplicate course name", "DUPLICATE_COURSE_CODE": "Duplicate course code", "SESSION_TYPE_ALREADY_MAPPED_WITH_COURSES": "Session type already mapped with course", "DELIVERY_TYPE_ALREADY_MAPPED_WITH_COURSES": "Delivery type already mapped with course", "SHARED_DEPARTMENT_SUBJECT_IS_MAPPED_WITH_ANOTHER_PROGRAM_COURSE": "Shared department is mapped with another program course", "SHARED_SUBJECT_IS_MAPPED_WITH_ANOTHER_PROGRAM_COURSE": "Shared subject is mapped with another program course", "SHOW_SHARED_REQUIRED": "showShared key must be either true or false", "ALLOW_MIXED_VACCINE": "allow key is required and must be a boolean", "DUPLICATE_EVENT_NAME": "Event name already exists", "DUPLICATE_BREAK_NAME": "Break name already exists", "MAIL_NOT_SEND": "mail not send", "MAIL_SEND": "mail send", "USER_DATA_IMPORTED_SUCCESSFULLY": "user data imported successfully", "INCORRECT_EMAIL_OR_PASSWORD": "Incorrect Email or Password", "ONLY_SIGNED_USER_ABLE_TO_ACCESS_PLS_DO_SIGNUP": "Only signed user able to access, Pls do SingUp", "ONLY_SIGNED_USER_ABLE_TO_ACCESS": "Only signed user able to access", "YOUR_PROFILE_HAS_NOT_COMPLETED_ASK_ADMIN": "Your profile has not completed ask admin regarding this", "YOUR_ACCOUNT_HAS_BEEN_DEACTIVATED_PLEASE_CONTACT_ADMINISTRATOR": "Your account has been deactivated please contact administrator", "YOU_ARE_NOT_ASSOCIATED_FOR_THIS_APP": "You are not associated for this app", "WELCOME_TO_DIGICLASS": "Welcome to Digiclass", "USER_NOT_FOUND": "User not found", "ERROR_UNABLE_TO_IMPORT_USER_DETAILS": "Error unable to import user details", "ERROR_FOUND_DUPLICATE_INVALID_DATA_IN_UPLOAD_FILE": "Error found duplicate invalid data in upload file", "DUPLICATE_NATIONALITY_ID_FOUND": "Duplicate nationality id found", "DUPLICATE_EMPLOYEE_ID": "Duplicate employee id", "USER_DATA_MODIFIED_SUCCESSFULLY": "User data modified successfully", "ERROR_USER_ID_NOT_MATCH": "Error user id not match", "USER_LIST": "User List", "YOUR_PROFILE_HAS_COMPLETED_DO_LOGIN": "Your profile has been completed, can login to proceed further", "SIGNED_IN_SUCCESSFULLY": "Signed in successfully", "UPDATED_SUCCESSFULLY": "Updated successfully", "USER_ID_NOT_FOUND": "User id not found", "CHECK_CREDENTIALS": "Check Credentials"}