let constant = require('../utility/constants');
var department = require('mongoose').model(constant.DEPARTMENT);
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
var program = require('mongoose').model(constant.PROGRAM);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const department_formate = require('./department_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: 'division._subject_id', foreignField: '_id', as: 'division_subject' } },
        // { $addFields: { 'division.subjects': '$division_subject' } },
        // { $unwind: '$division' },
        {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'sub_id',
                        cond: {
                            $in: ['$$sub_id._id', '$division._subject_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: { $first: '$department_title' },
                division: { $push: '$division' },
                _subject_id: { $first: '$_subject_id' },
                program: { $first: '$program' },
                isActive: { $first: '$isActive' },
                isDeleted: { $first: '$isDeleted' }
            }
        },

        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },

        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "department list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ department_formate.department(doc.data));
        // common_files.list_all_response(res, 200, true, "department list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* department_formate.department(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: 'division._subject_id', foreignField: '_id', as: 'division_subject' } },
        // { $addFields: { 'division.subjects': '$division_subject' } },
        {
            $unwind: {
                path: '$division',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'sub_id',
                        cond: {
                            $in: ['$$sub_id._id', '$division._subject_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: { $first: '$department_title' },
                division: { $push: '$division' },
                _subject_id: { $first: '$_subject_id' },
                program: { $first: '$program' },
                isActive: { $first: '$isActive' },
                isDeleted: { $first: '$isDeleted' }
            }
        },

        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } }
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "department details", /* doc.data */department_formate.department_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "department details", doc.data/* department_formate.department_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let doc, query, docs = { status: false };
    let checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    if (checks.status) {
        if (req.body.id != '' && req.body.id.length != 0) {
            query = { 'department_title': req.body.department_title, '_program_id': req.body._program_id, 'isDeleted': false };
            docs = await base_control.get(department, query, {});
        }
        if (!docs.status) {
            let objects = {
                department_title: req.body.department_title,
                _program_id: req.body._program_id
            };
            if (req.body.id == '' && req.body.id.length == 0) {
                doc = await base_control.insert(department, objects);
            } else {
                doc = await base_control.update(department, req.body.id, objects);
            }
            if (doc.status) {
                common_files.com_response(res, 201, true, "department Added successfully", doc);
            } else {
                common_files.com_response(res, 500, false, "Error", doc.data);
            }
        } else {
            common_files.com_response(res, 500, false, "Error duplicate values found", 'This content already present in DB');
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(department, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "department update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(department, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "department deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(department, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "department List", department_formate.department_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_department_division_subject = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(department, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "department List", department_formate.department_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program_department = async (req, res) => {
    let id = ObjectId(req.params.id);
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(id) } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: 'division._subject_id', foreignField: '_id', as: 'division_subject' } },
        // { $addFields: { 'division.subjects': '$division_subject' } },
        // { $unwind: '$division' },
        { $unwind: { path: '$division', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                'division.subjects': {
                    $filter: {
                        input: '$division_subject',
                        as: 'sub_id',
                        cond: {
                            $in: ['$$sub_id._id', '$division._subject_id']
                        }
                    }
                }
            }
        },
        {
            $group: {
                _id: '$_id',
                department_title: { $first: '$department_title' },
                division: { $push: '$division' },
                _subject_id: { $first: '$_subject_id' },
                program: { $first: '$program' },
                isActive: { $first: '$isActive' },
                isDeleted: { $first: '$isDeleted' }
            }
        },

        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },

        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(department, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "department list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ department_formate.department(doc.data));
        // common_files.list_all_response(res, 200, true, "department list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* department_formate.department(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_department_program_no = async (req, res) => {
    let program_list = await base_control.get_list(program, { no: req.params.no }, { name: 1, no: 1 });
    console.log(program_list.data);
    console.log(program_list.data.map(i => i._id));
    let ids = program_list.data.map(i => i._id);
    let doc = await base_control.get_list(department, { _program_id: { $in: ids }, isDeleted: false }, { department_title: 1, division: 1, subject: 1 });
    if (doc.status) {
        common_files.com_response(res, 200, false, "Department List based on Program Number", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_division_subject = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        { $project: { _id: 1, 'division._id': 1, 'division.title': 1, 'subject._id': 1, 'subject.title': 1 } }
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "department details", /* doc.data */department_formate.department_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "department details", doc.data/* department_formate.department_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};


exports.list_depart_division_subject = async (req, res) => {
    let department_ids = [], division_ids = [];
    req.body._department_id.forEach(ind_data => {
        if (ind_data.length == 24) {
            if (department_ids.indexOf(ObjectId(ind_data)) == -1 && ind_data.length == 24) {
                department_ids.push(ObjectId(ind_data));
            }
        }
    });
    req.body._division_id.forEach(ind_data => {
        if (ind_data.length == 24) {
            if (division_ids.indexOf(ObjectId(ind_data)) == -1 && ind_data.length == 24) {
                division_ids.push(ObjectId(ind_data));
            }
        }
    });
    let aggre = [
        { $match: { '_id': { $in: department_ids } } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'department_division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'direct_subject' } },
        { $project: { _id: 1, department_title: 1, 'department_division._id': 1, 'department_division.title': 1, 'direct_subject._id': 1, 'direct_subject.title': 1 } }
    ];
    let doc = await base_control.get_aggregate(department, aggre);
    let aggre1 = [
        { $match: { '_id': { $in: division_ids } } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_subject_id', foreignField: '_id', as: 'subject' } },
        { $project: { _id: 1, title: 1, 'subject._id': 1, 'subject.title': 1 } },

    ];
    let doc1 = await base_control.get_aggregate(department_division, aggre1);
    common_files.com_response(res, 200, true, "department -> division,subject details", department_formate.department_division_subject_list(doc.data, doc1.data));
};