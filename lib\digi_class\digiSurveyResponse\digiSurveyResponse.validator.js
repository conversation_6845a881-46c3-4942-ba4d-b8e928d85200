const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.getSurveyQuestionValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            surveyId: objectId.error(() => 'SURVEY ID REQUIRED'),
            userId: objectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.saveSurveyResponseValidator = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            institutionCalendarId: objectId.error(() => 'CALENDAR ID REQUIRED'),
            surveyId: objectId.error(() => 'SURVEY ID REQUIRED'),
            userId: objectId.error(() => 'USER ID REQUIRED'),
            answers: Joi.array()
                .items(
                    Joi.object({
                        name: Joi.string().error(() => 'QUESTION NAME IS REQUIRED'),
                        sectionNo: Joi.string().error(() => 'SECTION NO IS REQUIRED'),
                    }).unknown(true),
                )
                .error((error) => error),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.checkIsSurveyClosedValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            surveyId: objectId.error(() => 'SURVEY ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
