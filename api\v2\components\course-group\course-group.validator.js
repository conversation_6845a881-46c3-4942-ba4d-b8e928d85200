const Joi = require('joi');

const listCourseGroupsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            curriculumId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
            yearId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
            levelId: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseGroupValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _curriculum_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
            _year_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
            _level_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
            groupName: Joi.string().error(() => {
                return 'GROUP_NAME_REQUIRED';
            }),
            isPhaseFlowWithOutLevel: Joi.boolean().error(() => {
                return 'GROUP_NAME_REQUIRED';
            }),
            _course_ids: Joi.array().items(
                Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'COURSE_IDS_REQUIRED';
                    }),
            ),
        }),
    })
    .unknown(true);

const courseUnGroupValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            groupId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'GROUP_ID_REQUIRED';
                }),
            groupName: Joi.string().error(() => {
                return 'GROUP_NAME_REQUIRED';
            }),
            _course_ids: Joi.array().items(
                Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'COURSE_IDS_REQUIRED';
                    }),
            ),
        }),
    })
    .unknown(true);

module.exports = { listCourseGroupsValidator, courseGroupValidator, courseUnGroupValidator };
