const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const constant = require('../utility/constants');

const actionSchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    policy: { type: String },
});

const pageSchema = new Schema({
    // Faculty list page
    name: {
        type: String,
        required: true,
        trim: true,
    },
    url: String, // /student or /faculty (particular page)
    actions: [actionSchema],
    tabs: [
        {
            // All students, validated, pending validation
            name: {
                type: String,
                required: true,
                trim: true,
            },
            url: String, // /student or /faculty (particular page)
            actions: [actionSchema],
            subTabs: [
                {
                    // verification pending, profile verification, biometric verification
                    name: {
                        type: String,
                        required: true,
                        trim: true,
                    },
                    url: String, // /student or /faculty (particular page)
                    actions: [actionSchema],
                },
            ],
        },
    ],
});

const moduleSchema = new Schema({
    // User management
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    url: String, // /user (includes both student and faculty)
    pages: [pageSchema],
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true,
    },
});

module.exports = model(constant.MODULES, moduleSchema);
