const { DIGI_COURSE, COURSE_SCHEDULE, USER, COMPLETED } = require('../utility/constants');
const courseModal = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const userSchemas = require('mongoose').model(USER);
const { convertToMongoObjectId } = require('../utility/common');

const updateCourseWiseFacialSettings = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const {
            studentFacial,
            staffFacial,
            sessionChange,
            autoEndAttendance,
            programId,
            courseId,
        } = body;
        const result = await courseModal.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(courseId),
                _program_id: convertToMongoObjectId(programId),
                'settings.updatedBy': convertToMongoObjectId(_user_id),
            },
            {
                $set: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    'settings.studentFacial': studentFacial,
                    'settings.staffFacial': staffFacial,
                    'settings.sessionChange': sessionChange,
                    'settings.autoEndAttendance': autoEndAttendance,
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
            },
            {
                upsert: true,
            },
        );

        const action = result.upsertedCount > 0 ? 'created' : 'updated';

        return {
            statusCode: 200,
            message: `Facial settings ${action} successfully`,
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseFacialData = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { programId, courseId } = query;

        const courseWiseData = await courseModal
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(courseId),
                    _program_id: convertToMongoObjectId(programId),
                    'settings.updatedBy': convertToMongoObjectId(_user_id),
                },
                {
                    _id: 1,
                    _program_id: 1,
                    settings: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseWiseData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getCourseWiseUserDetails = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_calendar_id } = headers;
        const { _program_id, _course_id, year_no, level_no, term, rotation, rotation_count } =
            query;

        const courseWiseData = await courseScheduleSchema
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    _course_id: convertToMongoObjectId(_course_id),
                    term,
                    year_no,
                    level_no,
                    ...(rotation === 'yes' && {
                        rotation_count,
                    }),
                },
                {
                    staffs: 1,
                },
            )
            .lean();

        const staffIds = new Set();
        courseWiseData.forEach((schedule) => {
            schedule.staffs?.forEach((staff) => {
                if (staff._staff_id) {
                    staffIds.add(staff._staff_id.toString());
                }
            });
        });

        const uniqueStaffIds = Array.from(staffIds);
        const userData = await userSchemas
            .find(
                {
                    _id: uniqueStaffIds,
                    isDeleted: false,
                    isActive: true,
                    status: COMPLETED,
                },
                { name: 1, user_id: 1, gender: 1 },
            )
            .lean();

        return { statusCode: 200, message: 'DATA_RETRIEVED', data: userData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    updateCourseWiseFacialSettings,
    getCourseWiseFacialData,
    getCourseWiseUserDetails,
};
