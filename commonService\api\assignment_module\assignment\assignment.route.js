const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    addAssignment,
    updateAssignment,
    updateRatAssignments,
    getAssignment,
    listAssignment,
    deleteAssignment,
    listSession,
    listInstructor,
    getStudentGroups,
    updateSubmissionDue,
    getStudentCourseGroups,
    listStudentAssignments,
    listStudentCourse,
    updateStatus,
    listStaffAssignments,
    extendDue,
    studentListForAssignment,
    updateStudentData,
    updateMultipleStudentData,
    studentAssignmentMarkUpdate,
    getProgramCalenderData,
    getDuplicateAssignments,
    getResetAssignments,
    extendIndividualAssignmentDueDate,
    extendIndividualAssignmentStudentDueDate,
    revertReleasedGrade,
} = require('./assignment.controller');
const { validate } = require('../../../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');
const {
    paramIDValidator,
    createAssignmentValidator,
    updateSubmissionDateValidator,
    revertReleasedGradeValidator,
    duplicateAssignmentValidator,
    resetAssignmentValidator,
} = require('./assignment.validator');

router.post(
    '/add-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createAssignmentValidator, property: 'body' }]),
    catchAsync(addAssignment),
);
router.put(
    '/update-assignment/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateAssignment),
);
router.put(
    '/update-rat-assignments/:ratId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateRatAssignments),
);
router.put(
    '/update-submission-due/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: paramIDValidator, property: 'params' },
        { schema: updateSubmissionDateValidator, property: 'body' },
    ]),
    catchAsync(updateSubmissionDue),
);
router.put(
    '/update-studentData',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateStudentData),
);
router.put(
    '/revert-releasedGrade',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: revertReleasedGradeValidator, property: 'body' }]),
    catchAsync(revertReleasedGrade),
);
router.put(
    '/update-MultipleStudentData',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateMultipleStudentData),
);
router.get(
    '/get-assignment/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(getAssignment),
);
router.get(
    '/list-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listAssignment),
);
router.delete(
    '/delete-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(deleteAssignment),
);
router.get(
    '/list-session/:_course_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listSession),
);
router.get(
    '/list-instructor',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listInstructor),
);
router.get(
    '/get_student_group/:_institution_calendar_id/:_program_id/:_course_id/:term',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getStudentGroups),
);
router.get(
    '/get_student_course_group',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getStudentCourseGroups),
);
router.get(
    '/list-student-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(listStudentAssignments),
);
router.get(
    '/list-student-course',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(listStudentCourse),
);
router.get(
    '/student-list/:assignmentId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(studentListForAssignment),
);

router.patch(
    '/update-status',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateStatus),
);
router.patch(
    '/extend-due',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(extendDue),
);
router.get(
    '/list-staff-assignment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listStaffAssignments),
);

router.put(
    '/assignment-mark-update',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(studentAssignmentMarkUpdate),
);

router.get(
    '/get-program-calendar',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getProgramCalenderData),
);

router.put(
    '/duplicate',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: duplicateAssignmentValidator, property: 'body' }]),
    catchAsync(getDuplicateAssignments),
);
router.put(
    '/reset',
    validate([{ schema: resetAssignmentValidator, property: 'query' }]),
    catchAsync(getResetAssignments),
);

router.put(
    '/extend-assignment-date',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(extendIndividualAssignmentDueDate),
);

router.put(
    '/extend-student-date',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(extendIndividualAssignmentStudentDueDate),
);

module.exports = router;
