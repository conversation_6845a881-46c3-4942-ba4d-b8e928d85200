const router = require('express').Router();
const { getInstitutePrograms, getRoleBasedLists } = require('./program-input.controller');
const { listProgramsValidator } = require('./program-input.validator');
const catchAsync = require('../../../utility/catch-async');
const { validate } = require('../../../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.get(
    '/list-programs',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: listProgramsValidator, property: 'query' }]),
    catchAsync(getInstitutePrograms),
);

router.get(
    '/role-based-modules',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: listProgramsValidator, property: 'query' }]),
    catchAsync(getRoleBasedLists),
);

module.exports = router;
