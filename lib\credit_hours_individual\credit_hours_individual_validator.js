const Joi = require('joi');
const common_files = require('../utility/common');

exports.credit_hours_individual = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                        return error;
                    }),
                    year: Joi.number().min(1).max(10).required().error(error => {
                        return error;
                    }),
                    level: Joi.number().min(1).max(15).required().error(error => {
                        return error;
                    }),
                    theory_credit_hours: Joi.number().min(0).max(1000).required().error(error => {
                        return error;
                    }),
                    pratical_credit_hours: Joi.number().min(0).max(1000).required().error(error => {
                        return error;
                    }),
                    clinical_credit_hours: Joi.number().min(0).max(1000).required().error(error => {
                        return error;
                    }),
                    rotation: Joi.string().valid('yes', 'no').required().error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_individual_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    year: Joi.number().min(1).max(10).error(error => {
                        return error;
                    }),
                    level: Joi.number().min(1).max(15).error(error => {
                        return error;
                    }),
                    theory_credit_hours: Joi.number().min(1).max(1000).error(error => {
                        return error;
                    }),
                    pratical_credit_hours: Joi.number().min(1).max(1000).error(error => {
                        return error;
                    }),
                    clinical_credit_hours: Joi.number().min(1).max(1000).error(error => {
                        return error;
                    }),
                    rotation: Joi.string().valid('yes', 'no').error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_individual_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}