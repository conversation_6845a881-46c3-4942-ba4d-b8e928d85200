let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let credit_hours_master = new Schema({
    year: {
        type: Number,
        required: true
    },
    level: {
        type: Number,
        required: true
    },
    course_no: {
        type: Number,
        required: true
    },
    credit_hours: {
        type: Number,
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.CREDIT_HOURS_MASTER, credit_hours_master);