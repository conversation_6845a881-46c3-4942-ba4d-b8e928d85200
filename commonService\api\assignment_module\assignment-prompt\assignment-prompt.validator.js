const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    booleanRQSchema,
    numberSchema,
    stringSchema,
    booleanSchema,
} = require('../../../utility/validationSchemas');

const attachments = Joi.array().items(
    Joi.object({
        url: stringSchema,
        signedUrl: stringSchema,
        sizeInKb: numberSchema,
        name: stringSchema,
    }).unknown(true),
);

exports.createPromptValidator = Joi.object({
    score: Joi.object({
        value: numberSchema,
        unit: stringSchema,
        type: stringSchema,
        mandatoryToAttemptAtleast: numberSchema,
        questionText: stringSchema,
        isMandatory: booleanSchema,
        instructionAttachment: Joi.object({
            value: stringSchema,
            attachments,
        }).unknown(true),
        hint: Joi.object({
            value: stringSchema,
            attachments,
        }).unknown(true),
        choiceType: Joi.object({
            isSingleChoice: booleanSchema,
            randomizeChoice: booleanSchema,
            choices: Joi.array().items(
                Joi.object({
                    isCorrect: booleanSchema,
                    value: stringSchema,
                    attachments,
                }).unknown(true),
            ),
        }).unknown(true),
        singleAnswer: Joi.object({
            maxCharLimit: numberSchema,
            answers: Joi.array().items(
                Joi.object({ answerText: stringSchema, attachments }).unknown(true),
            ),
        }).unknown(true),
        matchTheFollowing: Joi.object({
            randomizeChoice: booleanSchema,
            leftValues: Joi.array().items(
                Joi.object({ value: stringSchema, attachments }).unknown(true),
            ),
            rightValues: Joi.array().items(
                Joi.object({ value: stringSchema, attachments }).unknown(true),
            ),
        }).unknown(true),
        fillInTheBlanks: Joi.object({
            isCaseSensitive: booleanSchema,
            showOptionsToSelect: booleanSchema,
            options: Joi.array().items(Joi.array().items(stringSchema)),
            answers: Joi.array().items(Joi.array().items(stringSchema)),
        }).unknown(true),
        inCorrectScore: Joi.object({
            isActive: booleanSchema,
            value: numberSchema,
        }).unknown(true),
    }),
    category: stringSchema,
    parentPromptId: objectIdSchema,
    childPromptIds: Joi.array().items(objectIdSchema),
    taxonomyIds: Joi.array().items(objectIdSchema),
    showTaxonomy: Joi.boolean(),
    showOutcome: Joi.boolean(),
    subjectId: objectIdSchema,
    _id: objectIdSchema,
    assignmentId: objectIdSchema,
    rubricsId: objectIdSchema,
    learningOutcome: Joi.object({
        type: stringSchema,
        outcomeId: Joi.array().items(objectIdSchema),
    }).unknown(true),
});

exports.getPromptValidator = Joi.object({ promptId: objectIdRQSchema }).unknown(true);

exports.promptListValidator = Joi.object({
    assignmentId: objectIdRQSchema,
    promptIds: Joi.array().items(objectIdRQSchema),
}).unknown(true);

exports.promptWithoutAnswerValidator = Joi.object({
    studentId: objectIdSchema,
    subjectId: objectIdSchema,
    assignmentId: objectIdRQSchema,
    promptIds: Joi.array().items(objectIdRQSchema),
    isAnswerNeeded: booleanRQSchema,
    staffId: objectIdSchema.allow(''),
}).unknown(true);
