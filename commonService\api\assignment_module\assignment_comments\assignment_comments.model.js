const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const constant = require('../../../utility/constants');

const assignmentCommentsSchema = new Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: constant.INSTITUTION,
        },
        subjectId: {
            type: ObjectId,
        },
        assignmentId: {
            type: ObjectId,
        },
        studentId: {
            type: ObjectId,
        },
        comments: [
            {
                staffId: { type: ObjectId },
                studentId: { type: ObjectId },
                message: { type: String },
                staffName: { type: String },
                studentName: { type: String },
                attachments: [
                    {
                        url: String,
                        signedUrl: String,
                        sizeInKb: Number,
                        name: String,
                    },
                ],
                time: Date,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.ASSIGNMENT_COMMENTS, assignmentCommentsSchema);
