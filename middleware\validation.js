const Joi = require('joi');
const { responseFunctionWithRequest } = require('../lib/utility/common');
const validate = (validations) => {
    return async (req, res, next) => {
        const errorMessage = [];
        if (validations.length > 0) {
            validations.forEach((validation) => {
                const { schema, property } = validation;
                const { error } = (Joi.isSchema(schema) ? schema : Joi.object(schema)).validate(
                    req[property],
                );
                const valid = error == null;
                if (!valid) {
                    const { details } = error;
                    const message = details.map((i) => req.t(i.message));
                    errorMessage.push(message);
                }
            });
        }
        if (errorMessage.length > 0) {
            return res
                .status(422)
                .send(
                    responseFunctionWithRequest(
                        req,
                        422,
                        false,
                        req.t('VALIDATION_ERROR'),
                        errorMessage,
                    ),
                );
        }

        next();
    };
};

exports.validate = validate;
