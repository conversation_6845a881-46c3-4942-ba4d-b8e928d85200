const { Joi } = require('../../common/middlewares/validation');
const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const startFormSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        formId: objectIdRQSchema,
    }),
}).unknown(true);

const updateStudentResponseSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
    body: Joi.object({
        pageId: objectIdRQSchema,
        sectionId: objectIdRQSchema,
        section: Joi.object().required(),
    }),
}).unknown(true);

const submitFormSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
    body: Joi.object({
        pages: Joi.array().min(1).required(),
    }),
}).unknown(true);

const getStudentResponseSchema = Joi.object({
    query: Joi.object({
        formId: objectIdRQSchema,
        portfolioId: objectIdRQSchema,
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
    }),
}).unknown(true);

const uploadStudentAttachmentSchema = Joi.object({
    body: Joi.object({
        studentResponseId: objectIdRQSchema,
        pageId: objectIdRQSchema.optional(),
        sectionId: objectIdRQSchema.optional(),
        questionId: objectIdRQSchema.optional(),
        attachment: Joi.string().required(),
        email: Joi.string().optional().allow(''),
    }),
});

const deleteStudentAttachmentSchema = Joi.object({
    query: Joi.object({
        studentResponseId: objectIdRQSchema,
    }),
});

const validateChildIdSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
        childrenId: objectIdRQSchema,
        deliveryTypeId: objectIdSchema.optional(),
    }),
}).unknown(true);

module.exports = {
    startFormSchema,
    updateStudentResponseSchema,
    submitFormSchema,
    getStudentResponseSchema,
    uploadStudentAttachmentSchema,
    deleteStudentAttachmentSchema,
    validateChildIdSchema,
};
