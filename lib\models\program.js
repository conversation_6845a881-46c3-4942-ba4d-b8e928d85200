let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let program = new Schema({
    name: {
        type: String,
        required: true
    },
    level: {
        type: String,
        required: true
    },
    level_no: {
        type: String,
        required: true
    },
    degree: {
        type: String,
        required: true
    },
    no: {
        type: String,
        required: true
    },
    theory_credit: {
        type: Number,
        required: true
    },
    practicals_credit: {
        type: Number,
        required: true
    },
    clinic_credit: {
        type: Number,
        required: true
    },
    total_credit: {
        type: Number,
        required: true
    },
    interim: {
        type: <PERSON><PERSON>an,
        default: false,
        require: true
    },
    abbreviation: {
        theory: String,
        practical: String,
        clinic: String,
        credit_hours: String,
        contact_hours: String
    },
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.PROGRAM, program);