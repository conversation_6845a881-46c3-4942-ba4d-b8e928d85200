const course = require('../../models/digi_course');
const User = require('../../models/user');
const Document = require('../../models/document_manager');
const Activity = require('../../models/activities');
const session_order = require('../../models/digi_session_order');
const base_control = require('../../base/base_controller');
const common_files = require('../../utility/common');
const CourseSchedule = require('../../models/course_schedule');
const schedule_attendance = require('../../models/schedule_attendance');
const studentGroupSchema = require('../../models/student_group');
const institutionCalendarSchema = require('../../models/institution_calendar');
const getJSON = base_control.dsGetAllWithSortAsJSON;
const InstitutionCalendars = require('../../utility/institution_calendar');
const {
    getAllCourses,
    getCourse,
    getCourseDocuments,
    getScheduleById,
    getScheduleByDate,
    getCoursesByStaffId,
    getSloBySessionIdAndCourseId,
    getStudentCourseIds,
    getRatingBySessions,
    getInfraById,
    getCourses,
    getUserCourseDetails,
    getUserCourseLists,
    removeUserCourseRedis,
    getStaffCourseAttendance,
    getStudentListFromStudentGroup,
    getSessionListFromSchedule,
    getUserLevelComprehensiveWarning,
    getUserLevelComprehensiveAttendance,
    studentGroupDeliveryExtract,
} = require('./course_session_service');

const {
    sendResponse,
    sendResponseWithRequest,
    convertToMongoObjectId,
    cs,
} = require('../../utility/common');
const {
    timestampNow,
    toObjectId,
    convertingRiyadhToUTC,
} = require('../../utility/common_functions');
const {
    INVALID_ID,
    DS_UPDATE_FAILED,
    DS_UPDATED,
    DS_DATA_RETRIEVED,
    SCHEDULE_TYPES: { REGULAR, EVENT, SUPPORT_SESSION },
    PUBLISHED,
    DC_STAFF,
    ATTENDANCE_DEFAULT_END_TIME,
    COMPLETED,
    ONGOING,
    ON_SITE,
    DC_STUDENT,
    EXCLUDE,
    MANUAL,
} = require('../../utility/constants');
const { get, dsGetAllWithSortAsJSON, bulk_write } = require('../../base/base_controller');
const {
    getCoursesSessionList,
    getScheduleBySessionOrScheduleList,
    getSessionWithScheduleBasedCourse,
} = require('./courseSession.service');
const { studentGroupByGroupName } = require('../activities/activities_service');
const {
    logger,
    SERVICES: { MULTI_SCHEDULE_START },
} = require('../../utility/util_keys');
const { PM } = require('../../utility/enums');
const { RUNNING } = require('../../utility/constants');
const {
    getLateAutoAndManualRange,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    getLateLabelForSchedule,
    checkRestrictCourse,
    filterStudentsForCourseRestriction,
} = require('../../utility/utility.service');

exports.getDropdownSchedule = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const {
            userId,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            courseAdmin,
        } = req.query;
        const query = {
            _course_id: convertToMongoObjectId(courseId),
            $or: [
                { 'session._session_id': convertToMongoObjectId(sessionId), merge_status: false },
                { _id: convertToMongoObjectId(sessionId) },
            ],

            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            _program_id: convertToMongoObjectId(_program_id),
            year_no,
            level_no,
            term,
            rotation,
            isActive: true,
            isDeleted: false,
        };
        if (courseAdmin === 'true') {
            // check course admin verification
            const courses = await course.find({
                'coordinators._user_id': convertToMongoObjectId(userId),
            });
            if (courses.length) {
                const currentCourseIds = courses.map((course) => course._id);
                if (currentCourseIds) {
                    query._course_id = { $in: currentCourseIds };
                }
            } else {
                query['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
        } else {
            query['staffs._staff_id'] = convertToMongoObjectId(userId);
        }
        if (rotation_count) {
            query.rotation_count = rotation_count;
        }
        const project = {};

        const courseSchedules = (await getJSON(CourseSchedule, query, project)).data;

        if (!courseSchedules.length)
            sendResponseWithRequest(req, res, 200, true, req.t('SCHEDULE_NOT_FOUND'), null);
        const sessions = [];
        for (const courseSchedule of courseSchedules) {
            const { student_groups, _id, merge_status, merge_with } = courseSchedule;
            const groupByGroupName = studentGroupByGroupName(student_groups);
            if (groupByGroupName && groupByGroupName.length && !merge_status) {
                let studentGroups = groupByGroupName.map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                });
                studentGroups = [...new Set(studentGroups)];
                studentGroups = studentGroups.toString();
                sessions.push({ _id, studentGroupNames: studentGroups });
            }
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.student_groups);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                    if (scheduleStudents && scheduleStudents.length) {
                        scheduleStudents = scheduleStudents.concat(student_groups);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    }
                    const groupByGroupName = studentGroupByGroupName(scheduleStudents);
                    let studentGroups = groupByGroupName.map((student_group) => {
                        const { group_name, session_group } = student_group;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((groupNameEntry) => {
                                let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    });
                    studentGroups = [...new Set(studentGroups)];
                    studentGroups = studentGroups.toString();
                    sessions.push({ _id, studentGroupNames: studentGroups });
                }
            }
        }
        sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), sessions);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.course_session_flow_list = async (req, res) => {
    try {
        const cond = {
            _course_id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        const proj = {};
        const doc = await base_control.get(session_order, cond, proj);
        const delivery_types = [
            ...new Set(doc.data.session_flow_data.map((item) => item.delivery_type)),
        ];
        // delivery_types = [...new Set(delivery_types)]
        const data_doc = [];
        delivery_types.forEach((element) => {
            const data = doc.data.session_flow_data.filter(
                (item) => item.delivery_type === element,
            );
            const session = [];
            data.forEach((flow) => {
                session.push({
                    _id: flow._id,
                    delivery_symbol: flow.delivery_symbol,
                    _session_id: flow._session_id,
                    delivery_topic: flow.delivery_topic,
                    delivery_type: flow.delivery_type,
                    delivery_no: flow.delivery_no,
                    s_no: flow.s_no,
                    duration: flow.duration,
                    document_count: 3,
                    activity_count: 6,
                    session_date: '2021-01-23T00:00:00.000Z',
                    start_time: '2021-01-23T03:30:00.000Z',
                    end_time: '2021-01-23T05:30:00.000Z',
                    status: 'pending',
                });
            });
            data_doc.push({
                delivery_type: element,
                count: data.length,
                rating: 4.3,
                session_status: {
                    ongoing: 23,
                    total: 52,
                },
                assignment: 5,
                session_flow: session,
            });
        });

        if (!doc.status) return sendResponse(res, 200, false, req.t('COURSE_NOT_FOUND'), []);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), data_doc);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.document_course_session_flow_list = async (req, res) => {
    try {
        const aggre = [
            {
                $match: {
                    course_assigned_details: {
                        $exists: true,
                        $ne: [],
                    },
                    isDeleted: false,
                    isActive: true,
                },
            },
            { $unwind: { path: '$course_assigned_details', preserveNullAndEmptyArrays: true } },
            {
                $group: {
                    _id: '$_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    year: { $first: '$course_assigned_details.year' },
                    level: { $first: '$course_assigned_details.level_no' },
                    program: { $first: '$course_assigned_details.program_name' },
                },
            },

            {
                $lookup: {
                    from: 'digi_session_orders',
                    localField: '_id',
                    foreignField: '_course_id',
                    as: 'session_flow',
                },
            },
            { $unwind: { path: '$session_flow', preserveNullAndEmptyArrays: true } },
            {
                $unwind: {
                    path: '$session_flow.session_flow_data',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    session_flows: {
                        _id: '$session_flow.session_flow_data._id',
                        delivery_topic: '$session_flow.session_flow_data.delivery_topic',
                    },
                },
            },
            {
                $group: {
                    _id: '$_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    year: { $first: '$year' },
                    level: { $first: '$level' },
                    program: { $first: '$program' },
                    session_flows: { $push: '$session_flows' },
                },
            },
        ];
        const doc = await base_control.get_aggregate(course, aggre);
        if (!doc.status) return sendResponse(res, 200, false, req.t('COURSE_NOT_FOUND'), []);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), doc.data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.course = async (req, res) => {
    const { userId, courseId } = req.params;
    const {
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotation,
        rotationCount,
        coordinators,
        courseAdmin,
        type,
    } = req.query;
    const { _institution_id } = req.headers;
    try {
        if (!_institution_id) {
            return sendResponseWithRequest(req, res, 400, false, req.t('INVALID_INSTITUTION_ID'));
        }
        const courses = courseId
            ? await getCourse(
                  userId,
                  courseId,
                  institutionCalendarId,
                  programId,
                  yearNo,
                  levelNo,
                  term,
                  rotation,
                  rotationCount,
                  courseAdmin,
                  type,
                  null,
                  _institution_id,
              )
            : await getAllCourses(userId, coordinators, institutionCalendarId, _institution_id);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), courses);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getCourseSessionTab = async (req, res) => {
    const { userId, courseId } = req.params;
    const {
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotation,
        rotationCount,
        courseAdmin,
    } = req.query;
    try {
        const csQuery = {
            schedule_date: { $exists: true },
            isDeleted: false,
        };
        const staff = await User.findOne(
            { _id: convertToMongoObjectId(userId), user_type: 'staff' },
            { _id: 1 },
        );
        if (staff) {
            let courseParams;
            if (!courseAdmin || courseAdmin === 'false') {
                csQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
        } else {
            csQuery['students._id'] = convertToMongoObjectId(userId);
        }
        if (courseId) {
            csQuery._course_id = convertToMongoObjectId(courseId);
            csQuery._institution_calendar_id = convertToMongoObjectId(institutionCalendarId);
            csQuery._program_id = convertToMongoObjectId(programId);
            csQuery.year_no = yearNo;
            csQuery.level_no = levelNo;
            csQuery.term = term;
            if (rotation) {
                csQuery.rotation = rotation;
            }
            if (rotationCount) {
                csQuery.rotation_count = rotationCount;
            }
        }
        const csProject = {
            session: 1,
            type: 1,
            schedule_date: 1,
            merge_status: 1,
            merge_with: 1,
        };
        let courseSchedules = await CourseSchedule.find(csQuery, csProject)
            .sort({ 'sessions.delivery_symbol': 1, 'sessions.delivery_no': 1 })
            .lean();
        const totalCourseSchedules = courseSchedules;
        let courseSessionSchedules = courseSchedules;

        // Start -> It is used to remove duplicate sessions from array
        const duplicateChkInCourseSchedules = [];
        const schduleSessionId = [];
        for (courseSchedule of courseSchedules) {
            if (
                !duplicateChkInCourseSchedules.find(
                    (item) =>
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        item.session &&
                        item.session._session_id &&
                        item.session._session_id.toString() ===
                            courseSchedule.session._session_id.toString(),
                )
            ) {
                duplicateChkInCourseSchedules.push(courseSchedule);
            }
        }
        // End -> It is used to remove duplicate sessions from array

        courseSchedules = duplicateChkInCourseSchedules;
        courseSchedules = courseSchedules.map((courseSchedule) => {
            let count = 0;
            const courseSession = courseSessionSchedules.find(
                (courseSessionSchedule) =>
                    courseSessionSchedule._id.toString() === courseSchedule._id.toString(),
            );
            if (courseSession) {
                if (!courseSession.merge_status) {
                    count = 1;
                }
                if (courseSession.merge_status) {
                    count = 1;
                    courseSession.merge_with.forEach((mergeWithEntry) => {
                        courseSessionSchedules = courseSessionSchedules.filter(
                            (courseSessionSchedule) =>
                                courseSessionSchedule._id.toString() !==
                                mergeWithEntry.schedule_id.toString(),
                        );
                    });
                }
            }
            return {
                name: courseSchedule.session
                    ? courseSchedule.session.session_type
                    : courseSchedule.type === 'support_session'
                    ? 'Support Session'
                    : courseSchedule.type === 'event'
                    ? 'Event'
                    : courseSchedule.type,
                type: courseSchedule.session
                    ? courseSchedule.session.session_type
                    : courseSchedule.type,
                count,
            };
        });
        const courseScheduleSessions = courseSchedules;
        courseSchedules = courseSchedules.reduce((courseSchedule, courseScheduleEntry) => {
            const splittedCourseSession = courseSchedule.find(
                (course) => course.type.toString() === courseScheduleEntry.type,
            );
            if (!splittedCourseSession) {
                return courseSchedule.concat([courseScheduleEntry]);
            }
            return courseSchedule;
        }, []);
        let totalCount = 0;
        courseSchedules = courseSchedules.map((courseSchedule) => {
            const count = courseScheduleSessions.filter(
                (courseScheduleSession) =>
                    courseScheduleSession.type === courseSchedule.type &&
                    courseScheduleSession.count,
            ).length;
            courseSchedule.count = count;
            totalCount += count;
            return courseSchedule;
        });
        // today sessions
        const todayDate = new Date(common_files.convertToUtcFormat(new Date()));
        const todaySessions = totalCourseSchedules.filter((courseScheduleSession) => {
            if (courseScheduleSession.schedule_date.toString() === todayDate.toString())
                return courseScheduleSession;
        }).length;
        courseSchedules.unshift({
            name: 'Today',
            type: 'today',
            count: todaySessions,
        });
        courseSchedules.push({ name: 'All', type: 'all', count: totalCount });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            courseSchedules,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.getCourses = async (req, res) => {
    const {
        params: { staffId },
        headers: { _institution_calendar_id: headerInstitutionCalendarId },
        query: { _institution_calendar_id: queryInstitutionCalendarId },
    } = req;

    try {
        let institutionCalendarId = headerInstitutionCalendarId;
        if (
            queryInstitutionCalendarId &&
            queryInstitutionCalendarId.toString() !== headerInstitutionCalendarId.toString()
        ) {
            institutionCalendarId = queryInstitutionCalendarId;
        }
        const courses = await getCourses(staffId, institutionCalendarId);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), courses);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.courseSessionFlow = async (req, res) => {
    try {
        const cond = {
            _course_id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        const proj = {};
        const doc = await base_control.get(session_order, cond, proj);
        const deliveryTypes = [
            ...new Set(doc.data.session_flow_data.map((item) => item.delivery_type)),
        ];
        const sessionOrders = [];
        deliveryTypes.forEach((element) => {
            const data = doc.data.session_flow_data.filter(
                (item) => item.delivery_type === element,
            );
            const session = [];
            data.forEach((flow) => {
                session.push({
                    _id: flow._id,
                    delivery_symbol: flow.delivery_symbol,
                    _session_id: flow._session_id,
                    delivery_topic: flow.delivery_topic,
                    delivery_type: flow.delivery_type,
                    delivery_no: flow.delivery_no,
                    s_no: flow.s_no,
                    duration: flow.duration,
                    document_count: 3,
                    activity_count: 6,
                    session_date: '2021-01-23T00:00:00.000Z',
                    start_time: '2021-01-23T03:30:00.000Z',
                    end_time: '2021-01-23T05:30:00.000Z',
                    status: 'pending',
                });
            });
            sessionOrders.push({
                delivery_type: element,
                count: data.length,
                rating: 4.3,
                session_status: {
                    ongoing: 23,
                    total: 52,
                },
                assignment: 5,
                session_flow: session,
            });
        });
        const sessionFlows = sessionOrders.map((dd) => dd.session_flow);
        // eslint-disable-next-line no-sequences
        const filteredData = sessionFlows.reduce((r, e) => (r.push(...e), r), []);
        if (!doc.status) return sendResponse(res, 200, false, req.t('COURSE_NOT_FOUND'), []);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), filteredData);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.courseSessionFlowSlo = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const slos = await getSloBySessionIdAndCourseId(courseId, sessionId);
        if (slos.length === 0)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_SLOS_FOUND'), []);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), slos);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getStudentDocuments = async (req, res) => {
    try {
        const { studentId } = req.params;
        const courseIds = await getStudentCourseIds(convertToMongoObjectId(studentId));
        const dQuery = { isDeleted: false, isActive: true, _course_id: { $in: courseIds } };
        const dProject = {
            type: 1,
            name: 1,
            url: 1,
            _course_id: 1,
            _session_flow_id: 1,
            shared: 1,
        };
        const response = (await getJSON(Document, dQuery, dProject)).data;
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseDocuments = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const response = await getCourseDocuments(courseId, sessionId);
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSessions = async (req, res) => {
    try {
        const { userId } = req.params;
        const {
            courseId,
            sessionId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            type,
            rotation,
            rotationCount,
            mergedStatus,
            scheduleId,
            term,
            courseAdmin,
        } = req.query;
        const courses = await getCourse(
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            courseAdmin,
            '',
            true,
        );
        let courseType;
        const cResult = await get(course, { _id: convertToMongoObjectId(courseId) }, {});
        if (cResult.status) courseType = cResult.data.course_type;
        const response = [];
        let allSchedules = courses.map((course) =>
            course.sessions.map((session) => {
                session.schedules.forEach((schedule) => {
                    schedule.courseType = courseType;
                });
                return session.schedules;
            }),
        );

        // Getting Schedule Attendance (Buzz, ReTake for All & Absent, Engager)
        let scheduleAttendanceByScheduleId;
        if (scheduleId) {
            scheduleAttendanceByScheduleId = await schedule_attendance.findOne(
                {
                    scheduleId: { $in: [scheduleId] },
                },
                { _id: 1 },
            );
        }
        // eslint-disable-next-line prefer-spread
        allSchedules = [].concat.apply([], [].concat.apply([], allSchedules));
        if (courses.length) {
            courses.forEach((c) => {
                c.sessions.forEach((session) => {
                    let sessionSchedules = session.schedules;
                    sessionSchedules = sessionSchedules.filter(
                        (sessionSchedule) =>
                            sessionSchedule.merge_status.toString() === mergedStatus,
                    );
                    const isRetake = !!scheduleAttendanceByScheduleId;
                    if (
                        session._session_id &&
                        sessionId &&
                        (session._session_id.toString() === sessionId.toString() ||
                            sessionSchedules.find(
                                (sessionSchedule) =>
                                    sessionSchedule._id.toString() === sessionId.toString(),
                            ))
                    ) {
                        if (mergedStatus === 'true') {
                            sessionSchedules = sessionSchedules.filter(
                                (sessionSchedule) =>
                                    sessionSchedule._id.toString() === sessionId.toString() ||
                                    sessionSchedule.session._session_id.toString() ===
                                        sessionId.toString(),
                            );
                        }
                        sessionSchedules = sessionSchedules.map((sessionSchedule) => {
                            sessionSchedule.program_name = c.program_name;
                            sessionSchedule.isRetake = isRetake;
                            if (sessionSchedule.merge_status) {
                                const mergedSessions = sessionSchedule.merge_with.map(
                                    (mergeWith) => {
                                        const sessionDetails = allSchedules.find(
                                            (courseScheduleEntry) =>
                                                courseScheduleEntry._id.toString() ===
                                                mergeWith.schedule_id.toString(),
                                        );
                                        if (sessionDetails) {
                                            mergeWith.session = {
                                                _session_id: sessionDetails.session._session_id,
                                                s_no: sessionDetails.session.s_no,
                                                delivery_symbol:
                                                    sessionDetails.session.delivery_symbol,
                                                delivery_no: sessionDetails.session.delivery_no,
                                                session_type: sessionDetails.session.session_type,
                                                session_topic: sessionDetails.session.session_topic,
                                            };
                                        }
                                        return mergeWith;
                                    },
                                );
                                sessionSchedule.merge_with = mergedSessions;
                            }
                            return sessionSchedule;
                        });
                        if (sessionSchedules.length) {
                            response.push({
                                _session_id: session._session_id,
                                delivery_symbol: session.delivery_symbol,
                                delivery_no: session.delivery_no,
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                documentCount: session.documentCount,
                                activityCount: session.activityCount,
                                isActive: c.isActive,
                                isDeleted: c.isDeleted,
                                schedules: sessionSchedules,
                                program_name: c.program_name,
                                level: c.level,
                                year: c.year,
                                term: c.term,
                            });
                        }
                    } else if (session.session_type && type && session.session_type === type) {
                        let sessionSchedules = session.schedules;
                        sessionSchedules = sessionSchedules.map((sessionSchedule) => {
                            sessionSchedule.program_name = c.program_name;
                            sessionSchedule.isRetake = isRetake;
                            return sessionSchedule;
                        });
                        sessionSchedules = sessionSchedules.filter(
                            (sessionSchedule) => sessionSchedule._id.toString() === scheduleId,
                        );
                        if (sessionSchedules.length) {
                            response.push({
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                documentCount: session.documentCount,
                                activityCount: session.activityCount,
                                schedules: sessionSchedules,
                                program_name: c.program_name,
                                level: c.level,
                                year: c.year,
                                term: c.term,
                            });
                        }
                    }
                });
            });
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        }
        return sendResponse(res, 200, true, req.t('SCHEDULED_LISTS_NOT_FOUND'), []);
    } catch (error) {
        console.log(error);
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSchedule = async (req, res) => {
    try {
        const { scheduleId } = req.params;
        const response = await getScheduleById(scheduleId);
        const feedBack = await getRatingBySessions([convertToMongoObjectId(scheduleId)]);

        const infraIds = response.map((infraIds) => infraIds._infra_id);

        const infraDetails = await getInfraById(infraIds);
        const getScheduleAttendance = await schedule_attendance
            .find(
                {
                    scheduleId: { $in: scheduleId },
                    status: RUNNING,
                },
                {
                    _id: 1,
                    students: 1,
                    scheduleId: 1,
                    status: 1,
                    _staff_id: 1,
                    modeBy: 1,
                    createdAt: 1,
                },
            )
            .lean();
        const courseDoc = await course
            .findOne(
                { _id: response[0]._course_id },
                {
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details.year': 1,
                    'course_assigned_details.level_no': 1,
                    'course_assigned_details.auto_end_attendance_in': 1,
                },
            )
            .lean();
        for (resp of response) {
            if (getScheduleAttendance && getScheduleAttendance.length) {
                const retakeDetails = getScheduleAttendance.find((retakeId) =>
                    retakeId.scheduleId.find(
                        (scheduleId) => scheduleId.toString() === resp._id.toString(),
                    ),
                );
                resp.retakeDetails = retakeDetails;
                resp.isRetake = true;
            }
            if (courseDoc) {
                resp.auto_end_attendance_in =
                    courseDoc?.course_assigned_details?.find(
                        (courseAssignElement) =>
                            courseAssignElement?._program_id.toString() ===
                                resp?._program_id.toString() &&
                            courseAssignElement?.year.toString() === resp?.year_no.toString() &&
                            courseAssignElement?.level_no.toString() === resp?.level_no.toString(),
                    )?.auto_end_attendance_in ?? ATTENDANCE_DEFAULT_END_TIME;
            }
            const feedBacks = feedBack.find(
                (feedBackDetail) => feedBackDetail._session_id.toString() === resp._id.toString(),
            );
            resp.feedBacks = feedBacks;
            const infraDatas = infraDetails
                .map((a) => a.programs)
                .flat()
                .map((b) =>
                    b.remoteScheduling.filter(
                        (i) => i._id.toString() === resp._infra_id.toString(),
                    ),
                )
                .flat()
                .shift();
            if (infraDatas) resp.infraDatas = infraDatas;
            const { warningData, restrictCourseAccess } = await getUserCourseDetails({
                userId: req.headers._user_id,
                courseId: resp._course_id,
                level: resp.level_no,
                term: resp.term,
                type: resp.students.find(
                    (studentElement) => studentElement._id.toString() === req.headers._user_id,
                )
                    ? DC_STUDENT
                    : DC_STAFF,
                rotationCount: resp.rotation_count,
                institutionCalendarId: resp._institution_calendar_id,
                _institution_id: resp._institution_id,
            });
            resp.warningData = warningData;
            resp.restrictCourseAccess = restrictCourseAccess;
            const courseResult = await course
                .findOne(
                    {
                        _id: convertToMongoObjectId(resp._course_id),
                    },
                    { course_type: 1 },
                )
                .lean();
            if (courseResult) resp.course_type = courseResult.course_type;
            if (resp.merge_status) {
                const scheduleIds = resp.merge_with.map((mergeWith) => mergeWith.schedule_id);
                const mergedSchedules = (
                    await dsGetAllWithSortAsJSON(CourseSchedule, { _id: { $in: scheduleIds } }, {})
                ).data;
                resp.merge_with.forEach((mergeWith) => {
                    const schedule = mergedSchedules.find((mergedSchedule) => {
                        return mergedSchedule._id.toString() === mergeWith.schedule_id.toString();
                    });
                    if (schedule) mergeWith.session = schedule.session;
                });
            }
        }
        if (response.length)
            return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SCHEDULED_LISTS_NOT_FOUND'),
            response,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

const getCount = async (schedules, sessionIds, mode, userId, _institution_id) => {
    const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
    const activitiesOrDocuments = [];
    let uniqueSessionIds = [...new Set(sessionIds.map((sessionId) => sessionId.toString()))];
    uniqueSessionIds = uniqueSessionIds.map((uniqueSessionId) =>
        convertToMongoObjectId(uniqueSessionId),
    );
    if (mode === 'activity') {
        const activities = await Activity.find(
            {
                $or: [
                    { 'sessionFlowIds._id': { $in: uniqueSessionIds } },
                    { createdBy: convertToMongoObjectId(userId) },
                    { createdBy: { $ne: convertToMongoObjectId(userId) }, status: PUBLISHED },
                    { scheduleIds: { $in: uniqueSessionIds } },
                ],
                isDeleted: false,
                _institution_calendar_id: { $in: institutionCalendarIds },
            },
            { status: 1, createdBy: 1, scheduleIds: 1, sessionFlowIds: 1, rotation_count: 1 },
        ).lean();
        schedules.forEach((schedule) => {
            const sessionId =
                schedule.session && schedule.session._session_id
                    ? convertToMongoObjectId(schedule.session._session_id)
                    : convertToMongoObjectId(schedule._id);
            const counts = activities.filter((activity) => {
                const {
                    sessionFlowIds,
                    scheduleIds,
                    sessionId: activitySessionId,
                    rotation_count,
                } = activity;
                let sessionFlowSessionIds;
                if (sessionFlowIds && sessionFlowIds.length) {
                    sessionFlowSessionIds = sessionFlowIds.map((sessionFlowId) =>
                        sessionFlowId._id.toString(),
                    );
                }

                const sessionSchedules =
                    scheduleIds && scheduleIds.length
                        ? scheduleIds.map((scheduleId) => scheduleId.toString())
                        : [];
                if (
                    sessionSchedules.includes(sessionId.toString()) ||
                    sessionFlowSessionIds.includes(sessionId.toString()) ||
                    (activitySessionId && activitySessionId.toString() === sessionId.toString())
                ) {
                    if (
                        (schedule.rotation_count &&
                            rotation_count &&
                            rotation_count.toString() === schedule.rotation_count.toString()) ||
                        (!schedule.rotation_count && !rotation_count)
                    ) {
                        return true;
                    }
                }
                return false;
            }).length;
            activitiesOrDocuments.push({ sessionId: schedule._id, counts });
        });
    }
    if (mode === 'document') {
        const documents = await Document.find(
            {
                'sessionOrScheduleIds._id': { $in: uniqueSessionIds },
                isDeleted: false,
                _institution_calendar_id: { $in: institutionCalendarIds },
            },

            { _id: 1, sessionOrScheduleIds: 1, rotation_count: 1 },
        ).lean();
        schedules.forEach((schedule) => {
            const sessionId =
                schedule.session && schedule.session._session_id
                    ? convertToMongoObjectId(schedule.session._session_id)
                    : convertToMongoObjectId(schedule._id);
            const counts = documents.filter((document) =>
                !schedule.rotation_count && !document.rotation_count
                    ? document.sessionOrScheduleIds.find(
                          (sessionOrScheduleId) =>
                              sessionOrScheduleId._id.toString() === sessionId.toString(),
                      )
                    : document.sessionOrScheduleIds.find(
                          (sessionOrScheduleId) =>
                              sessionOrScheduleId._id.toString() === sessionId.toString(),
                      ) && schedule.rotation_count
                    ? document.rotation_count.toString() === schedule.rotation_count.toString()
                    : true,
            ).length;
            activitiesOrDocuments.push({ sessionId: schedule._id, counts });
        });
    }
    return activitiesOrDocuments;
};

exports.getScheduleByDate = async (req, res) => {
    try {
        const { userId, date } = req.params;
        const { timeZone } = req.query;
        const { _institution_id } = req.headers;
        const schedules = await getScheduleByDate(userId, date, timeZone, _institution_id);
        const sessionIds = schedules
            .filter(
                (schedule) =>
                    schedule.type === REGULAR ||
                    schedule.type === SUPPORT_SESSION ||
                    schedule.type === EVENT,
            )
            .map(
                (schedule) =>
                    (schedule.session &&
                        schedule.session._session_id &&
                        convertToMongoObjectId(schedule.session._session_id)) ||
                    convertToMongoObjectId(schedule._id),
            );
        const feedBack = await getRatingBySessions(sessionIds, userId);
        const documentCounts = await getCount(
            schedules,
            sessionIds,
            'document',
            '',
            _institution_id,
        );
        const activityCounts = await getCount(
            schedules,
            sessionIds,
            'activity',
            userId,
            _institution_id,
        );
        schedules.map((resp) => {
            const feedBacks = feedBack.find(
                (fB) => fB._session_id && resp._id && cs(fB._session_id) === cs(resp._id),
            );
            if (feedBacks) resp.feedBacks = feedBacks;
            if (documentCounts.length) {
                const documentCount = documentCounts.find(
                    (documentCountEntry) =>
                        documentCountEntry.sessionId.toString() === resp._id.toString(),
                );
                if (documentCount) {
                    resp.documentCount = documentCount.counts;
                }
            }
            if (activityCounts.length) {
                const activityCount = activityCounts.find(
                    (activityCountEntry) =>
                        (resp.type === REGULAR ||
                            resp.type === SUPPORT_SESSION ||
                            resp.type === EVENT) &&
                        ((resp.session &&
                            resp.session._session_id &&
                            resp.session._session_id.toString() ===
                                activityCountEntry.sessionId.toString()) ||
                            resp._id.toString() === activityCountEntry.sessionId.toString()),
                );
                if (activityCount) {
                    resp.activityCount = activityCount.counts;
                }
            }
            return resp;
        });
        if (schedules.length)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t(DS_DATA_RETRIEVED),
                schedules,
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SCHEDULED_LISTS_NOT_FOUND'),
            schedules,
        );
    } catch (error) {
        console.log(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getTodaySchedule = async (req, res) => {
    try {
        const { userId } = req.params;
        const response = await getScheduleByDate(userId, timestampNow());
        if (response.length)
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponse(res, 200, true, req.t('TODAYS_SCHEDULED_LIST_NOT_FOUND'), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseOnly = async (req, res) => {
    try {
        const { staffId } = req.params;
        const response = await getCoursesByStaffId(staffId);
        if (response.length)
            return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), response);
        return sendResponse(res, 200, true, req.t('COURSE_LIST_NOT_FOUND'), response);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// update Feedback
exports.updateFeedback = async (req, res) => {
    try {
        const { rating, comments } = req.body;
        const { scheduleId, studentId } = req.params;

        //check if ratings are empty or zero
        if (!rating || rating === '0') return sendResponse(res, 200, false, req.t('SELECT_RATING'));
        // check if document exists
        const docCount = await base_control.dsGetCount(CourseSchedule, {
            'students._id': convertToMongoObjectId(studentId),
            _id: convertToMongoObjectId(scheduleId),
        });
        if (!docCount) return sendResponse(res, 200, false, INVALID_ID);
        // update the document
        const updateQuery = { _id: convertToMongoObjectId(scheduleId) };
        const data = { $set: { 'students.$[i].feedBack': { rating, comments } } };
        arrayFilter = [{ 'i._id': convertToMongoObjectId(studentId) }];
        const { success } = await base_control.dsCustomUpdate(
            CourseSchedule,
            updateQuery,
            data,
            arrayFilter,
        );
        if (!success) return sendResponse(res, 200, false, DS_UPDATE_FAILED);
        return sendResponse(res, 200, true, DS_UPDATED);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseSession = async (req, res) => {
    try {
        const { userId, courseId } = req.params;
        const { institutionCalendarId, programId, yearNo, levelNo, rotation, rotationCount } =
            req.query;
        // const { type, userId, institutionCalendarId } = req.query;
        logger.info(
            'courseSessionController -> getCourseSession -> %s Course Module %s start',
            userId,
        );
        const courseDatas = await getCoursesSessionList(
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            rotation,
            rotationCount,
            userId,
            courseId,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { courses: courseDatas });
    } catch (error) {
        logger.error(
            'courseSessionController -> getCourses -> %s Course Module error : %o',
            req.query.userId,
            { error },
        );
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// update riyadhToUTC
exports.riyadhToUTC = async (req, res) => {
    try {
        const courseSchedule = await CourseSchedule.find(
            { scheduleStartDateAndTime: { $exists: false } },
            {
                _id: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
            },
        );

        if (!courseSchedule) {
            return sendResponse(res, 200, true, req.t('NO_DATA_FOUND'));
        }

        const scheduleDateRecords = [];

        for (const schedules of courseSchedule) {
            const {
                start: { hour: startHour, minute: startMinute, format: startFormat },
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
            } = schedules;
            console.log(schedule_date);
            if (
                !schedules.scheduleStartDateAndTime &&
                !schedules.scheduleEndDateAndTime &&
                schedules.schedule_date
            ) {
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertingRiyadhToUTC(
                    schedule_date,
                    startHours,
                    startMinute,
                );
                const endDateAndTime = convertingRiyadhToUTC(schedule_date, endHours, endMinute);

                scheduleDateRecords.push({
                    updateOne: {
                        filter: {
                            _id: schedules._id,
                        },
                        update: {
                            $set: {
                                scheduleStartDateAndTime: startDateAndTime,
                                scheduleEndDateAndTime: endDateAndTime,
                            },
                        },
                    },
                });
            }
        }

        if (scheduleDateRecords.length > 0) {
            const doc = await bulk_write(CourseSchedule, scheduleDateRecords);
            if (doc.status) {
                return sendResponse(res, 200, false, req.t(DS_UPDATED));
            }
            return sendResponse(res, 200, false, req.t(DS_UPDATE_FAILED));
        }
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getScheduleBySessionOrScheduleId = async (req, res) => {
    try {
        const {
            query: { userId, type, institutionCalendarId },
            params: { sessionOrScheduleId },
        } = req;
        logger.info(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s start ',
            userId,
            sessionOrScheduleId,
        );
        const courseDatas = await getScheduleBySessionOrScheduleList(
            sessionOrScheduleId,
            institutionCalendarId,
            userId,
            type,
        );
        logger.info(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s end ',
            userId,
            sessionOrScheduleId,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), courseDatas);
    } catch (error) {
        logger.error(
            'dashboardController -> getScheduleBySessionOrScheduleId -> %s SessionId %s error : %o',
            req.query.userId,
            { error },
        );
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourseSessionWithSchedule = async (req, res) => {
    const { userId, courseId } = req.params;
    const {
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotation,
        rotationCount,
        type,
        filterType,
        timeZone,
    } = req.query;
    try {
        const courseSessionList = await getSessionWithScheduleBasedCourse(
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            type,
            filterType,
            timeZone,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), courseSessionList);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSessionsOfCourse = async (req, res) => {
    const { userId, courseId } = req.params;
    const { institutionCalendarId, programId, yearNo, levelNo, courseAdmin } = req.query;
    const { term, rotation, rotationCount, coordinators } = req.query;
    try {
        const courses = await getCourse(
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            courseAdmin,
        );
        const sessions = courses.map((course) => course.sessions).flat(1);
        const schedules = sessions.map((session) => session.schedules).flat(1);
        const studentIds = [
            ...new Set(
                schedules
                    .map((schedule) => schedule.students)
                    .flat(1)
                    .map((student) => student._id),
            ),
        ];
        const staffIds = [
            ...new Set(
                schedules
                    .map((schedule) => schedule.staffs)
                    .flat(1)
                    .map((staff) => staff._staff_id),
            ),
        ];
        const userIds = studentIds.concat(staffIds);
        const users = await User.find(
            {
                _id: { $in: userIds },
                ioToken: { $exists: true, $ne: null },
            },
            { _id: 1 },
        ).lean();
        const ioUserIds = users.map((user) => user._id.toString());
        for (const session of sessions) {
            for (const schedule of session.schedules) {
                schedule.students = schedule.students.filter((student) =>
                    ioUserIds.includes(student._id.toString()),
                );
                schedule.staffs = schedule.staffs.filter((staff) =>
                    ioUserIds.includes(staff._staff_id.toString()),
                );
            }
        }
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), sessions);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.userCourses = async (req, res) => {
    const { userId } = req.params;
    const { type, institutionCalendarId } = req.query;
    try {
        logger.info(
            { userId, type, institutionCalendarId },
            'courseSessionController -> userCourses -> Course List start',
        );
        const usersCourseList = await getUserCourseLists({ userId, type, institutionCalendarId });
        logger.info(
            { userId, type, institutionCalendarId },
            'courseSessionController -> userCourses -> Course List End',
        );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            usersCourseList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.userCourseSessionDetails = async (req, res) => {
    const { userId, courseId } = req.params;
    const { type, institutionCalendarId, level, term, rotationCount, programId, year } = req.query;
    const { _institution_id } = req.headers;
    try {
        const userCourseDetailsObjects = {
            userId,
            courseId,
            level,
            term,
            type,
            rotationCount,
            institutionCalendarId,
            _institution_id,
            programId,
            year,
        };
        logger.info(
            userCourseDetailsObjects,
            'courseSessionController -> userCourseSessionDetails -> Course Session Details start',
        );
        const usersCourseList = await getUserCourseDetails(userCourseDetailsObjects);
        logger.info(
            userCourseDetailsObjects,
            'courseSessionController -> userCourseSessionDetails -> Course Session Details End',
        );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            usersCourseList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.userCourseRedisReset = async (req, res) => {
    const { userId } = req.params;
    const { institutionCalendarId } = req.query;
    try {
        logger.info(
            { userId, institutionCalendarId },
            'courseSessionController -> userCourses -> Redis Reset start',
        );
        const usersCourseReset = await removeUserCourseRedis({
            userId,
            institutionCalendarId,
        });
        logger.info(
            { userId, institutionCalendarId },
            'courseSessionController -> userCourses -> Redis Reset End',
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), usersCourseReset);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.scheduleDetailsWithSession = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { userId } = req.params;
        const {
            courseId,
            sessionId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            type,
            rotation,
            rotationCount,
            mergedStatus,
            scheduleId,
            term,
            courseAdmin,
            operationBy,
            userType,
        } = req.query;

        const requestingKeys = {
            userId,
            userType,
            courseId,
            sessionId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            type,
            rotation,
            rotationCount,
            mergedStatus,
            scheduleId,
            term,
            courseAdmin,
            operationBy,
        };
        logger.info({ requestingKeys }, 'courseSession -> scheduleDetailsWithSession -> start');
        const scheduleQuery = {
            ...(userType && userType === DC_STAFF && type === REGULAR
                ? { 'session._session_id': convertToMongoObjectId(sessionId) }
                : { _id: convertToMongoObjectId(scheduleId) }),
            ...(userType &&
                userType === DC_STAFF &&
                ((courseAdmin !== undefined && courseAdmin === false) ||
                    courseAdmin === undefined) && {
                    $or: [
                        {
                            'staffs._staff_id': convertToMongoObjectId(userId),
                        },
                        {
                            'attendanceTakingStaff.staffId': convertToMongoObjectId(userId),
                        },
                    ],
                }),
            ...(rotationCount && { rotation_count: rotationCount }),
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            isDeleted: false,
            term,
            year_no: yearNo,
            level_no: levelNo,
            _course_id: convertToMongoObjectId(courseId),
            _program_id: convertToMongoObjectId(programId),
        };
        const scheduleProject = {
            merge_status: 1,
            _id: 1,
            status: 1,
            ...(userType && userType === DC_STAFF
                ? {
                      students: 1,
                  }
                : {
                      students: 1,
                  }),
            'sessionDetail.attendance_mode': 1,
            'sessionDetail.start_time': 1,
            'sessionDetail.stop_time': 1,
            'sessionDetail.startBy': 1,
            'zoomDetail.zoomStartUrl': 1,
            'zoomDetail.zoomTotalDuration': 1,
            'teamsDetail.teamsStartUrl': 1,
            'teamsDetail.teamsTotalDuration': 1,
            remotePlatform: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            'staffs._staff_id': 1,
            'staffs.staff_name': 1,
            'staffs.feedBack': 1,
            'staffs.duration': 1,
            'staffs.status': 1,
            'staffs.time': 1,
            _program_id: 1,
            mode: 1,
            _infra_id: 1,
            infra_name: 1,
            'subjects.subject_name': 1,
            isActive: 1,
            'session._session_id': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session.session_topic': 1,
            'session.session_type': 1,
            term: 1,
            year_no: 1,
            level_no: 1,
            _course_id: 1,
            course_name: 1,
            course_code: 1,
            _institution_calendar_id: 1,
            topic: 1,
            title: 1,
            type: 1,
            sub_type: 1,
            program_name: 1,
            rotation: 1,
            // rotation_no: 1,
            rotation_count: 1,
            isLive: 1,
            retakeStatus: 1,
            socket_port: 1,
            uuid: 1,
            schedule_date: 1,
            start: 1,
            end: 1,
            faceAuthentication: 1,
            attendanceTakingStaff: 1,
            manualType: 1,
            isMissedToComplete: 1,
            outsideCampus: 1,
            selfAttendance: 1,
            classModeType: 1,
            scheduleStartFrom: 1,
            classLeaders: 1,
            externalStaffs: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await CourseSchedule.find(scheduleQuery, scheduleProject)
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .populate({
                path: '_course_id',
                select: {
                    course_type: 1,
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details.level_no': 1,
                    'course_assigned_details.auto_end_attendance_in': 1,
                    'course_assigned_details.isMissedToSchedule': 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .populate({
                path: '_infra_id',
                select: {
                    zone: 1,
                    building_name: 1,
                    floor_no: 1,
                    room_no: 1,
                    name: 1,
                    radius: 1,
                    latitude: 1,
                    longitude: 1,
                },
            })
            .populate({ path: 'externalStaffs', select: { name: 1 } })
            .populate({ path: 'classLeaders', select: { name: 1 } })
            .lean();
        console.timeEnd('courseScheduleData');
        if (!courseScheduleData.length)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );

        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({ _institution_id });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            lateExcludeManagement,
        });
        let getScheduleAttendance;
        if (userType && userType === DC_STAFF) {
            const ongoingSchedule = courseScheduleData.find(
                (scheduleElement) =>
                    scheduleElement.sessionDetail &&
                    scheduleElement.status &&
                    scheduleElement.mode &&
                    scheduleElement.mode === 'onsite' &&
                    scheduleElement.sessionDetail.attendance_mode === COMPLETED &&
                    scheduleElement.status === ONGOING,
            );
            if (ongoingSchedule && ongoingSchedule._id)
                getScheduleAttendance = await schedule_attendance
                    .findOne(
                        {
                            scheduleId: { $in: ongoingSchedule._id },
                        },
                        {
                            _id: 1,
                            scheduleId: 1,
                            status: 1,
                            _staff_id: 1,
                            modeBy: 1,
                            createdAt: 1,
                            students: 1,
                        },
                    )
                    .sort({ _id: -1 })
                    .lean();
        }
        let scheduleSessionDetails;
        const ids = await filterStudentsForCourseRestriction({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            userIds: courseScheduleData.flatMap((scheduleElement) =>
                scheduleElement.students.map((studentElement) => studentElement._id.toString()),
            ),
        });
        const nonRestrictedCourseIds = new Set(ids);
        const projection =
            userType === DC_STAFF
                ? { 'settings.staffFacial': 1 }
                : userType === DC_STUDENT
                ? { 'settings.studentFacial': 1 }
                : {};
        const courseFacialData = await course.findOne(
            {
                _id: convertToMongoObjectId(courseId),
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
            },
            projection,
        );
        const faceAuthenticationValue =
            (userType === DC_STAFF && courseFacialData?.settings?.staffFacial) ||
            (userType === DC_STUDENT && courseFacialData?.settings?.studentFacial) ||
            false;
        for (const scheduleElement of courseScheduleData) {
            for (const studentElement of scheduleElement.students) {
                if (
                    !nonRestrictedCourseIds.has(studentElement._id.toString()) &&
                    scheduleElement.status !== COMPLETED
                ) {
                    studentElement.isRestricted = true;
                }
            }
            scheduleElement.isRetake = false;
            scheduleElement.faceAuthentication = faceAuthenticationValue;
            if (!scheduleSessionDetails)
                scheduleSessionDetails =
                    scheduleElement.session && scheduleElement.session.delivery_symbol
                        ? {
                              _session_id: sessionId,
                              delivery_symbol: scheduleElement.session.delivery_symbol,
                              delivery_no: scheduleElement.session.delivery_no,
                              session_type: scheduleElement.session.session_type,
                              session_topic: scheduleElement.session.session_topic,
                              program_name: scheduleElement.program_name,
                              level: scheduleElement.level_no,
                              year: scheduleElement.year_no,
                              term: scheduleElement.term,
                              isActive: scheduleElement.isActive,
                          }
                        : {
                              program_name: scheduleElement.program_name,
                              level: scheduleElement.level_no,
                              year: scheduleElement.year_no,
                              term: scheduleElement.term,
                              session_type: scheduleElement.type,
                              session_topic: scheduleElement.title,
                          };
            if (userType !== DC_STAFF) {
                scheduleElement.students = scheduleElement.students.filter(
                    (studentElement) => studentElement._id.toString() === userId.toString(),
                );
                let lateLabel;
                if (
                    scheduleElement.students.length &&
                    scheduleElement.students[0] &&
                    scheduleElement.students[0]._id
                ) {
                    const LateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id: institutionCalendarId,
                        programId,
                        courseId,
                        yearNo,
                        levelNo,
                        term,
                        rotation,
                        rotationCount,
                        studentId: scheduleElement.students[0]._id,
                        lateExcludeManagement,
                    }).lateExclude;
                    if (
                        !lateExclude &&
                        !LateExcludeForStudent &&
                        scheduleElement.type === 'regular'
                    ) {
                        const { lateLabel: retrievedLateLabel } = getLateLabelForSchedule({
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            schedule: scheduleElement,
                            student_data: scheduleElement.students[0],
                            _institution_calendar_id: institutionCalendarId,
                            programId,
                            courseId,
                            yearNo,
                            levelNo,
                            term,
                            rotation,
                            rotationCount,
                            lateExcludeManagement,
                        });
                        lateLabel = retrievedLateLabel; // Assign the retrieved lateLabel value
                    }
                }
                scheduleElement.lateLabel = lateLabel ? lateLabel.lateLabel : null;
            } else {
                scheduleElement.students = scheduleElement.students.filter(
                    (studentElement) => studentElement.status !== EXCLUDE,
                );
                if (getScheduleAttendance) {
                    scheduleElement.retakeDetails = getScheduleAttendance;
                    scheduleElement.isRetake = true;
                }
            }
            const courseAssignedDetails = scheduleElement._course_id.course_assigned_details.find(
                (assignedDetails) =>
                    assignedDetails._program_id.toString() === programId.toString() &&
                    assignedDetails.level_no.toString() === levelNo.toString(),
            );
            scheduleElement.courseType = scheduleElement._course_id.course_type;
            scheduleElement.versionNo = scheduleElement._course_id.versionNo || 1;
            scheduleElement.versioned = scheduleElement._course_id.versioned || false;
            scheduleElement.versionName = scheduleElement._course_id.versionName || '';
            scheduleElement.versionedFrom = scheduleElement._course_id.versionedFrom || null;
            scheduleElement.versionedCourseIds =
                scheduleElement._course_id.versionedCourseIds || [];
            scheduleElement.auto_end_attendance_in =
                courseAssignedDetails?.auto_end_attendance_in || ATTENDANCE_DEFAULT_END_TIME;
            scheduleElement.isMissedToSchedule = courseAssignedDetails?.isMissedToSchedule || false;
            scheduleElement._course_id = scheduleElement._course_id._id;
            const mergedScheduleObject = [];
            if (scheduleElement.merge_status) {
                for (mergedScheduleElement of scheduleElement.merge_with) {
                    mergedScheduleObject.push({
                        schedule_id: mergedScheduleElement.schedule_id._id,
                        session: mergedScheduleElement.schedule_id.session,
                    });
                }
                scheduleElement.session.merge_with = mergedScheduleObject;
                delete scheduleElement.merge_with;
            }
            scheduleElement.merge_with = mergedScheduleObject;
            if (userType == DC_STAFF && scheduleElement.status === COMPLETED) {
                const filterRatingStudents = scheduleElement.students.filter(
                    (studentElement) => studentElement.feedBack && studentElement.feedBack.rating,
                );
                const sumOfRatings = filterRatingStudents
                    .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                    .reduce((a, b) => a + b, 0);
                const count = filterRatingStudents.length;
                scheduleElement.feedBack = {
                    _session_id: sessionId,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                };
            }
            if (scheduleElement._infra_id) {
                let infraName =
                    scheduleElement._infra_id.name + ', ' + scheduleElement._infra_id.floor_no;
                if (scheduleElement._infra_id.zone.length) {
                    infraName += ', ' + scheduleElement._infra_id.zone.toString();
                }
                infraName +=
                    ', ' +
                    scheduleElement._infra_id.room_no +
                    ', ' +
                    scheduleElement._infra_id.building_name;
                scheduleElement.infra_name = infraName;
            }
        }
        let manualAttendance = false;
        if (userType === DC_STAFF) {
            const courseData = await course.findOne(
                {
                    _id: convertToMongoObjectId(courseId),
                    'course_assigned_details._program_id': convertToMongoObjectId(programId),
                    'course_assigned_details.level_no': levelNo,
                },
                {
                    'course_assigned_details.$': 1,
                },
            );
            if (courseData.course_assigned_details.length) {
                courseData.course_assigned_details.filter((courseElement) => {
                    if (
                        typeof courseElement.manualAttendance === 'boolean' &&
                        courseElement.manualAttendance === true
                    ) {
                        manualAttendance = true;
                    }
                });
            }
            // ! Staff Multi Schedule
            if (MULTI_SCHEDULE_START === 'true') {
                const multiScheduleCheckQuery = courseScheduleData.map((scheduleElement) => {
                    return {
                        _id: { $ne: convertToMongoObjectId(scheduleElement._id) },
                        scheduleStartDateAndTime: scheduleElement.scheduleStartDateAndTime,
                        scheduleEndDateAndTime: scheduleElement.scheduleEndDateAndTime,
                    };
                });
                const multiScheduleList = await CourseSchedule.find(
                    {
                        isDeleted: false,
                        isActive: true,
                        'staffs._staff_id': convertToMongoObjectId(userId),
                        merge_status: false,
                        $or: multiScheduleCheckQuery,
                    },
                    { scheduleStartDateAndTime: 1, scheduleEndDateAndTime: 1 },
                ).lean();
                courseScheduleData.forEach((scheduleElement) => {
                    scheduleElement.multiSchedule = !!multiScheduleList.find(
                        (multiScheduleElement) =>
                            new Date(multiScheduleElement.scheduleStartDateAndTime).getTime() ===
                                new Date(scheduleElement.scheduleStartDateAndTime).getTime() &&
                            new Date(multiScheduleElement.scheduleEndDateAndTime).getTime() ===
                                new Date(scheduleElement.scheduleEndDateAndTime).getTime(),
                    );
                });
            }
        }
        let warningData;
        let restrictCourseAccess;
        if (userType === DC_STUDENT) {
            ({ labelName: warningData, restrictCourseAccess } = await checkRestrictCourse({
                _institution_id,
                _institution_calendar_id: institutionCalendarId,
                programId,
                courseId,
                yearNo,
                levelNo,
                term,
                rotationCount,
                userId,
            }));
        }
        const response = [
            {
                schedules: courseScheduleData,
                ...scheduleSessionDetails,
                manualAttendance,
                warningData,
                restrictCourseAccess,
            },
        ];
        logger.info('courseSession -> scheduleDetailsWithSession -> end');
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        console.log(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.userCalendars = async (req, res) => {
    try {
        const { userId, userType } = req.params;
        const { isCourseAdmin } = req.query;
        const requestingKeys = { userId, userType, isCourseAdmin };
        logger.info({ requestingKeys }, 'courseSession -> userCalendars -> start');
        let institutionCalendarIds = [];
        if (userType && userType === DC_STAFF) {
            // Course Coordinator
            if (isCourseAdmin) {
                const courseCoordinatorDatas = await course.find(
                    {
                        isDeleted: false,
                        isActive: true,
                        'coordinators._user_id': convertToMongoObjectId(userId),
                    },
                    {
                        'coordinators.status': 1,
                        'coordinators._user_id': 1,
                        'coordinators._institution_calendar_id': 1,
                    },
                );
                const courseCalendarId = courseCoordinatorDatas
                    .map((courseElement) =>
                        courseElement.coordinators
                            .filter(
                                (coordinatorElement) =>
                                    coordinatorElement._user_id.toString() === userId.toString() &&
                                    coordinatorElement.status,
                            )
                            .map((coordinatorElement) =>
                                coordinatorElement._institution_calendar_id.toString(),
                            )
                            .flat(),
                    )
                    .flat();
                if (courseCalendarId && courseCalendarId.length)
                    institutionCalendarIds = [...new Set(courseCalendarId)];
            } else {
                const scheduleQuery = {
                    isDeleted: false,
                    ...(userType &&
                        userType === DC_STAFF && {
                            'staffs._staff_id': convertToMongoObjectId(userId),
                        }),
                };
                console.time('courseScheduleData');
                const courseScheduleData = await CourseSchedule.distinct(
                    '_institution_calendar_id',
                    scheduleQuery,
                ).lean();
                console.timeEnd('courseScheduleData');
                if (courseScheduleData) institutionCalendarIds = courseScheduleData;
            }
        } else {
            // Student Group Based Flow
            const studentStudentGroupData = await studentGroupSchema.distinct(
                '_institution_calendar_id',
                {
                    'groups.courses.setting.session_setting.groups._student_ids':
                        convertToMongoObjectId(userId),
                },
            );
            if (studentStudentGroupData) institutionCalendarIds = studentStudentGroupData;
        }
        let institutionCalenderData = [];
        if (institutionCalendarIds.length) {
            institutionCalenderData = await institutionCalendarSchema
                .find(
                    {
                        _id: { $in: institutionCalendarIds },
                        isDeleted: false,
                        status: PUBLISHED,
                    },
                    { calendar_name: 1, start_date: 1, end_date: 1, isActive: true },
                )
                .sort({ _id: -1 })
                .lean();
        }
        logger.info('courseSession -> userCalendars -> end');
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            institutionCalenderData,
        );
    } catch (error) {
        console.log(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.staffCourseExport = async (req, res) => {
    const {
        userId,
        courseId,
        institutionCalendarId,
        programId,
        yearNo,
        levelNo,
        term,
        rotationCount,
        group_name,
        delivery_symbol,
        session_group_id,
        gender,
        groupNo,
        sessionGroupId,
    } = req.query;
    const { _institution_id } = req.headers;
    try {
        const parsingObject = {
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            group_name,
            delivery_symbol,
            session_group_id,
            gender,
            groupNo,
            sessionGroupId,
        };
        logger.info(parsingObject, 'courseSessionController -> staffCourseExport -> start');
        const courseAttendanceReport = await getStaffCourseAttendance({
            userId,
            courseId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            _institution_id,
            group_name,
            delivery_symbol,
            session_group_id,
            gender,
            groupNo,
            sessionGroupId,
        });
        logger.info(parsingObject, 'courseSessionController -> staffCourseExport -> End');
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            courseAttendanceReport,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.updateClassMode = async (req, res) => {
    try {
        const { mode, scheduleId, staffId } = req.body;
        const updateResult = await CourseSchedule.updateMany(
            { _id: { $in: scheduleId } },
            { $set: { classModeType: mode, offlineModeTriggeredBy: staffId } },
        );
        if (!updateResult.modifiedCount) {
            return sendResponse(res, 404, true, 'No schedules found');
        }
        return sendResponse(res, 200, true, 'Data updated successfully');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
exports.updateOfflineAttendance = async (req, res) => {
    try {
        const { updateAttendance } = req.body;
        const bulkUpdate = [];
        const courseScheduleDocs = await CourseSchedule.find(
            {
                _id: {
                    $in: updateAttendance.map((attendanceElement) =>
                        convertToMongoObjectId(attendanceElement.scheduleId),
                    ),
                },
            },
            {
                students: 1,
                staffs: 1,
            },
        ).lean();
        for (const attendanceElement of updateAttendance) {
            const {
                scheduleId,
                scheduleStatus,
                startDateAndTime,
                endDateAndTime,
                staffId,
                students,
                staffs,
            } = attendanceElement;
            const offlineSchedule = courseScheduleDocs.find(
                (scheduleElement) => scheduleElement._id.toString() === scheduleId,
            );
            for (const studentElement of offlineSchedule.students) {
                const scheduleStudent = students.find(
                    (updateElement) => updateElement.studentId === studentElement._id.toString(),
                );
                if (scheduleStudent) {
                    studentElement.status = scheduleStudent.attendanceStatus;
                    studentElement.mode = MANUAL;
                    studentElement.primaryStatus = scheduleStudent.attendanceStatus;
                }
            }
            for (const staffElement of offlineSchedule.staffs) {
                const scheduleStaff = staffs.find(
                    (updateElement) => updateElement.staffId === staffElement._staff_id.toString(),
                );
                if (scheduleStaff) staffElement.status = scheduleStaff.attendanceStatus;
            }
            bulkUpdate.push({
                updateMany: {
                    filter: {
                        $or: [
                            { _id: convertToMongoObjectId(scheduleId) },
                            { 'merge_with.schedule_id': convertToMongoObjectId(scheduleId) },
                        ],
                    },
                    update: {
                        $set: {
                            sessionDetail: {
                                start_time: startDateAndTime,
                                stop_time: endDateAndTime,
                                startBy: staffId,
                                attendance_mode: 'completed',
                                mode: 'M',
                            },
                            status: scheduleStatus,
                            staffs: offlineSchedule.staffs,
                            students: offlineSchedule.students,
                        },
                    },
                },
            });
        }
        const offlineAttendanceUpdate = await CourseSchedule.bulkWrite(bulkUpdate);
        if (!offlineAttendanceUpdate.modifiedCount) {
            return sendResponse(res, 404, true, 'No schedules found');
        }
        return sendResponse(res, 200, true, 'Attendance updated successfully');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getStudentList = async (req, res) => {
    const {
        institutionCalendarId,
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
    } = req.query;
    const { _institution_id } = req.headers;
    try {
        const studentList = await getStudentListFromStudentGroup({
            programId,
            year,
            level,
            rotation,
            rotationCount,
            term,
            courseId,
            institutionCalendarId,
            _institution_id,
        });
        if (studentList.sgStudentList.length) {
            const inActiveUsers = await User.find(
                {
                    _id: {
                        $in: studentList.sgStudentList.map((studentElement) =>
                            convertToMongoObjectId(studentElement._student_id),
                        ),
                    },
                    isActive: false,
                },
                {
                    _id: 1,
                },
            ).lean();
            if (inActiveUsers.length) {
                const userSet = new Set();
                for (const userElement of inActiveUsers) {
                    userSet.add(userElement._id.toString());
                }
                for (const studentElement of studentList.sgStudentList) {
                    if (userSet.has(studentElement._student_id.toString())) {
                        studentElement.isActive = false;
                    }
                }
            }
        }
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), studentList);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getSessionListBasedGroups = async (req, res) => {
    const {
        institutionCalendarId,
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
    } = req.query;
    const { _institution_id } = req.headers;
    try {
        const sessionGroupList = await getSessionListFromSchedule({
            programId,
            year,
            level,
            rotation,
            rotationCount,
            term,
            courseId,
            institutionCalendarId,
            _institution_id,
        });
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), sessionGroupList);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.userLevelComprehensiveWarning = async (req, res) => {
    const { type, institutionCalendarId, programId, userId, term, levelNo } = req.query;
    try {
        logger.info(
            { type, institutionCalendarId, programId, userId, term, levelNo },
            'courseSessionController -> userLevelComprehensiveWarning -> Warning start',
        );
        const usersLevelWarningList = await getUserLevelComprehensiveWarning({
            type,
            institutionCalendarId,
            programId,
            userId,
            term,
            levelNo,
        });
        logger.info(
            { type, institutionCalendarId, programId, userId, levelNo },
            'courseSessionController -> userLevelComprehensiveWarning -> Warning End',
        );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            usersLevelWarningList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.userLevelComprehensiveAttendance = async (req, res) => {
    const { type, institutionCalendarId, programId, userId, term, levelNo } = req.query;
    try {
        logger.info(
            { type, institutionCalendarId, programId, userId, term, levelNo },
            'courseSessionController -> userLevelComprehensiveAttendance -> StudentAttendance start',
        );
        const usersLevelWarningList = await getUserLevelComprehensiveAttendance({
            type,
            institutionCalendarId,
            programId,
            userId,
            term,
            levelNo,
        });
        logger.info(
            { type, institutionCalendarId, programId, userId, levelNo },
            'courseSessionController -> userLevelComprehensiveAttendance -> StudentAttendance End',
        );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            usersLevelWarningList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getStaffCourseScheduleStudentGroup = async (req, res) => {
    const { _user_id } = req.headers;
    const { institutionCalendarId, courseId, levelNo, yearNo, term, programId, rotationCount } =
        req.body;
    try {
        const staffScheduleDatas = await CourseSchedule.find(
            {
                isDeleted: false,
                isActive: true,
                ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'staffs._staff_id': convertToMongoObjectId(_user_id),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                year_no: yearNo,
                term,
                _program_id: convertToMongoObjectId(programId),
                type: REGULAR,
            },
            {
                'student_groups.gender': 1,
                'student_groups.session_group': 1,
                'student_groups.delivery_symbol': 1,
                'student_groups.group_name': 1,
                'student_groups.group_id': 1,
                'student_groups.group_no': 1,
                'student_groups.session_group_id': 1,
            },
        ).lean();
        if (!staffScheduleDatas.length) {
            return sendResponseWithRequest(req, res, 404, true, 'No schedules found');
        }
        const filteredData = {};
        staffScheduleDatas.forEach((scheduleElement) => {
            scheduleElement.student_groups.forEach((groupElement) => {
                const gender = groupElement.gender;
                if (!filteredData[gender]) {
                    filteredData[gender] = {};
                }
                groupElement.session_group.forEach((session) => {
                    const deliveryLetter = studentGroupDeliveryExtract({
                        deliveryGroupName: session.group_name,
                    });
                    const groupNo = session.group_no;
                    if (deliveryLetter && groupNo !== null) {
                        const exists =
                            filteredData[gender][deliveryLetter] &&
                            filteredData[gender][deliveryLetter].some(
                                (existingGroup) =>
                                    existingGroup.group_name === groupElement.group_name &&
                                    existingGroup.group_no === groupNo,
                            );

                        if (!exists) {
                            if (!filteredData[gender][deliveryLetter]) {
                                filteredData[gender][deliveryLetter] = [];
                            }

                            filteredData[gender][deliveryLetter].push({
                                group_name: groupElement.group_name,
                                group_no: groupNo,
                                group_id: groupElement.group_id,
                                session_group_id: session.session_group_id,
                            });
                        }
                    }
                });
            });
        });
        const transformedResult = {
            studentGroups: Object.keys(filteredData).map((gender) => ({
                gender,
                deliveryGroups: Object.keys(filteredData[gender]).map((deliveryName) => ({
                    deliveryName,
                    groupId: filteredData[gender][deliveryName][0].group_id,
                    groups: filteredData[gender][deliveryName].map((groupElement) => ({
                        groupNo: groupElement.group_no,
                        groupName: groupElement.group_name,
                        sessionGroupId: groupElement.session_group_id,
                    })),
                })),
            })),
        };

        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            transformedResult,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
