const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    updateLevelUserAccess,
    getLevelUserAccess,
    listOfProgram,
    getCurriculumByProgramId,
    userRoleBasedCurriculum,
} = require('./levelBasedAccess.controller');
const {
    updateLevelUserAccessValidator,
    listOfProgramValidator,
    getCurriculumByProgramIdValidator,
    userRoleBasedCurriculumValidator,
} = require('./levelBasedAccess.validator');
const { validate } = require('../../middleware/validation');

router.put(
    '/updateLevelUserAccess',
    validate(updateLevelUserAccessValidator),
    catchAsync(updateLevelUserAccess),
);
router.get('/getLevelUserAccess', validate(listOfProgramValidator), catchAsync(getLevelUserAccess));
router.get('/listOfProgram', validate(listOfProgramValidator), catchAsync(listOfProgram));
router.get(
    '/getCurriculumByProgramId/:programId',
    validate(getCurriculumByProgramIdValidator),
    catchAsync(getCurriculumByProgramId),
);
router.get(
    '/userRoleBasedCurriculum',
    validate(userRoleBasedCurriculumValidator),
    catchAsync(userRoleBasedCurriculum),
);
module.exports = router;
