const program_formate = require('../program/program_formate');

// exports.credit_hours_calc_ID_Only = (doc) => {
//     //console.log(doc);
//     let obj = {
//         _id: doc._id,
//         credit_hours_calc_title: doc.credit_hours_calc_title,
//         division: doc._division_id,
//         subject: doc._subject_id,
//         program: doc._program_id,
//         isActive: doc.isActive,
//         isDeleted: doc.isDeleted
//     }
//     return obj;
// }

module.exports = {
    credit_hours_calc: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                session_type: element.session_type,
                credit_hours: element.credit_hours,
                contact_hours: element.contact_hours,
                per_session: element.per_session,
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    credit_hours_calc_ID: (doc) => {
        let obj = {
            _id: doc._id,
            session_type: doc.session_type,
            credit_hours: doc.credit_hours,
            contact_hours: doc.contact_hours,
            per_session: doc.per_session,
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_calc_ID_Only: function (doc) {
        let obj = {
            _id: doc._id,
            session_type: doc.session_type,
            credit_hours: doc.credit_hours,
            contact_hours: doc.contact_hours,
            per_session: doc.per_session,
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_calc_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                session_type: element.session_type,
                credit_hours: element.credit_hours,
                contact_hours: element.contact_hours,
                per_session: element.per_session,
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}