const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const { validate } = require('../../middleware/validation');
const {
    getCourseWarning,
    studentAcknowledge,
    staffAutoWarning,
    sendManualMail,
    getCourseWarningForCalendar,
    getLmsWarningConfig,
    createLmsWarningConfig,
} = require('./lmsWarning.controller');
const {
    courseWarningValidator,
    studentAcknowledgeValidator,
    staffAutoWarningValidator,
    getLmsWarningConfigValidator,
    createLmsWarningConfigValidator,
} = require('./lmsWarning.validator');
require('./lmsWarning.validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.get(
    '/courseWarning',
    validate(courseWarningValidator),
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(getCourseWarning),
);
router.get(
    '/courseWarningForCalendar',
    validate(courseWarningValidator),
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(getCourseWarningForCalendar),
);
router.put(
    '/studentAcknowledge',
    validate(studentAcknowledgeValidator),
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(studentAcknowledge),
);
router.put(
    '/sendManualMail',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(sendManualMail),
);
router.get(
    '/staffAutoWarning',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(staffAutoWarningValidator),
    catchAsync(staffAutoWarning),
);

//lms student waring config new settings flow
router.get(
    '/getLmsWarningConfig',
    validate(getLmsWarningConfigValidator),
    catchAsync(getLmsWarningConfig),
);
router.post(
    '/createLmsWarningConfig',
    validate(createLmsWarningConfigValidator),
    catchAsync(createLmsWarningConfig),
);

module.exports = router;
