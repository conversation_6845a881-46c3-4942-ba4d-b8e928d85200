const DynamicComponentService = require('./dynamic-component.service');

const createDynamicOutcomeComponent = async ({ body: { component }, user: { userId } = {} }) => {
    const dynamicOutcomeComponent = await DynamicComponentService.createDynamicOutcomeComponent({
        component,
        userId,
    });

    return { statusCode: 201, message: 'CREATED_SUCCESSFULLY', data: dynamicOutcomeComponent };
};

const getDynamicOutcomeComponents = async ({ query: { componentId } }) => {
    const dynamicOutcomeComponents = await DynamicComponentService.getDynamicOutcomeComponents({
        componentId,
    });

    return { statusCode: 200, message: 'RETRIEVED_SUCCESSFULLY', data: dynamicOutcomeComponents };
};

const deleteDynamicOutcomeComponent = async ({ query: { componentId } }) => {
    const dynamicOutcomeComponent = await DynamicComponentService.deleteDynamicOutcomeComponent({
        componentId,
    });

    return { statusCode: 200, message: 'DELETED_SUCCESSFULLY' };
};

module.exports = {
    createDynamicOutcomeComponent,
    getDynamicOutcomeComponents,
    deleteDynamicOutcomeComponent,
};
