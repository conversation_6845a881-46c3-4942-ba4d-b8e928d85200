const Joi = require('joi');
const assignmentPromptValidator = async () => {
    Joi.object().keys({
        Headers: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTION ID REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            assignmentId: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'ASSIGNMENT ID REQUIRED';
                }),
            assignmentPromptId: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'ASSIGNMENT PROMPT ID REQUIRED';
                }),
            choiceType: Joi.array().items(
                Joi.object().keys({
                    choices: Joi.array().items(
                        Joi.object({
                            isCorrect: Joi.boolean().default(true),
                            attachments: Joi.array().items(
                                Joi.string().error(() => {
                                    return 'ERROR URL TYPE';
                                }),
                            ),
                        }),
                    ),
                }),
            ),
            singleAnswer: Joi.array().items(
                Joi.object().keys({
                    answers: Joi.array().items(
                        Joi.object().keys({
                            answerText: Joi.string().error(() => {
                                return 'ANSWER TEXT TYPE MUST BE STRING';
                            }),
                            attachments: Joi.array().items(
                                Joi.string().error(() => {
                                    return 'ERROR URL TYPE';
                                }),
                            ),
                        }),
                    ),
                }),
            ),
            matchTheFollowing: Joi.array().items(
                Joi.object().keys({
                    rightValues: Joi.array().items(
                        Joi.object().keys({
                            value: Joi.string().error(() => {
                                return 'VALUE MUST BE STRING';
                            }),
                            attachments: Joi.array().items(
                                Joi.string().error(() => {
                                    return 'ERROR URL TYPE';
                                }),
                            ),
                        }),
                    ),
                }),
            ),
            fillInTheBlanks: Joi.array().items(
                Joi.object().keys({
                    answers: Joi.array().items(
                        Joi.string().error(() => {
                            return 'ANSWER TYPR MUST BE STRING';
                        }),
                    ),
                    attachments: Joi.array().items(
                        Joi.string().error(() => {
                            return 'ERROR URL TYPE';
                        }),
                    ),
                }),
            ),
        }),
    });
};

const assignmentPromptAnswerGetValidator = async () => {
    Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'ID_REQUIRED';
                }),
        }),
    });
};
module.exports = { assignmentPromptValidator, assignmentPromptAnswerGetValidator };
