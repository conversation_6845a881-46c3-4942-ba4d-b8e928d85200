const express = require('express');
const route = express.Router();
const department_division = require('./department_division_controller');
const validater = require('./department_division_validator');
route.get('/department/:id', validater.department_division_id, department_division.list_department_division_id);
route.post('/list', department_division.list_values);
route.get('/:id', validater.department_division_id, department_division.list_id);
route.get('/subject/:id', validater.department_division_id, department_division.list_subject);
route.get('/', department_division.list);
route.post('/', validater.department_division, department_division.insert);
route.put('/:id', validater.department_division_id, validater.department_division_update, department_division.update);
route.delete('/:id', validater.department_division_id, department_division.delete);

module.exports = route;