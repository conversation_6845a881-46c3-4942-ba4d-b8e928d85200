const express = require('express');
const route = express.Router();
const {
    userViewNotification,
    typeList,
    updateViewedNotification,
    singleListAnnouncement,
    receiveNotificationCount,
} = require('./announcement_controller');
const {
    typeListValidator,
    userViewNotificationValidator,
    updateViewedNotificationValidator,
    singleAnnouncementValidator,
    userIdValidateInQuery,
} = require('./announcement_validator');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
const { validate } = require('../../../middleware/validation');

route.get(
    '/userViewNotification',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: userViewNotificationValidator, property: 'query' }]),
    userViewNotification,
);
route.get(
    '/typeList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: typeListValidator, property: 'query' }]),
    typeList,
);
route.put(
    '/updateViewedNotification',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: updateViewedNotificationValidator, property: 'query' }]),
    updateViewedNotification,
);
route.get(
    '/singleListAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: singleAnnouncementValidator, property: 'query' }]),
    singleListAnnouncement,
);
route.get(
    '/receiveNotificationCount',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: userIdValidateInQuery, property: 'query' }]),
    receiveNotificationCount,
);
module.exports = route;
