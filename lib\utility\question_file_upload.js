/* eslint-disable no-buffer-constructor */
const mime = require('mime-types');
const multer = require('multer');
const { BUCKET_DOCUMENT, AWS_REGION } = require('./util_keys');
const fs = require('fs');
const AWS = require('aws-sdk');
const {
    ITEM_IMAGE_FILE_TYPES,
    ITEM_AUDIO_FILE_TYPES,
    ITEM_VIDEO_FILE_TYPES,
} = require('./constants');

const { AWS_ACCESS_KEY, AWS_SECRET_KEY } = require('./util_keys');

const s3 = new AWS.S3({
    accessKeyId: AWS_ACCESS_KEY,
    secretAccessKey: AWS_SECRET_KEY,
    multipart_threshold: '1GB',
    max_bandwidth: '50MB/s',
    multipart_chunksize: '1GB',
    region: AWS_REGION,
    maxRetries: 5,
    httpOptions: {
        timeout: 7200000,
        connectTimeout: 7200000,
    },
});

const {
    serviceAws: { putS3Object, deleteS3Object },
} = require('../../service');
const { getS3UnsignedUrl } = require('../../service/aws.service');
const localStorePath = './public/documents/';
// get file name
const getFileName = ({ id, file }) => {
    return `${id}/${Date.now()}.${file.originalname.split('.').pop()}`;
};
// Question attachments
uploadQuestionAttachment = async (id, file) => {
    const fileName = getFileName({ id, file });
    await putS3Object({
        bucket: BUCKET_DOCUMENT + '/activities',
        key: fileName,
        body: file.buffer,
    });
    const fileUploadUrl =
        `https://s3-${AWS_REGION}.amazonaws.com/` + BUCKET_DOCUMENT + '/activities/' + fileName;

    return fileUploadUrl;
};
uploadAttachment = multer({
    fileFilter: (req, file, cb) => {
        const { mimetype } = file;
        const fileType = mimetype.split('/')[0];
        const format = mimetype.split('/')[1];
        console.log('inside fileFilter under multer');
        if (
            (fileType === 'image' && ITEM_IMAGE_FILE_TYPES.includes(format)) ||
            (fileType === 'audio' && ITEM_AUDIO_FILE_TYPES.includes(format)) ||
            (fileType === 'video' && ITEM_VIDEO_FILE_TYPES.includes(format))
        ) {
            cb(undefined, true);
        } else {
            cb(new Error('Un-Supported file type'), false);
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 40,
    },
});

deleteAttachment = async (attachment) => {
    await deleteS3Object({
        bucket: BUCKET_DOCUMENT,
        key: attachment,
    });
};

getUnsignedUrl = async (link, id) => {
    const fileName = link.split('/').pop();
    const unsignedUrl = await getS3UnsignedUrl({
        bucket: BUCKET_DOCUMENT + '/activities',
        key: `${id}/${fileName}`,
    });
    return unsignedUrl;
};

localFileStorage = async (bytes, fileName) => {
    const fileBuffers = new Buffer(bytes.length);
    for (let i = 0; i < bytes.length; i++) {
        fileBuffers[i] = bytes[i];
    }
    if (!fs.existsSync(localStorePath)) {
        fs.mkdirSync(localStorePath);
    }
    fs.writeFile(localStorePath + fileName, fileBuffers, 'binary', function (err) {
        if (err) {
            return false;
        }
        return true;
    });
};

removeFolder = (file) => {
    fs.unlink(localStorePath + file, (errs) => {
        if (errs) throw errs;
    });
};
const uploadFileToAws = async (file, topicName) => {
    return new Promise((resolve, reject) => {
        fs.readFile(file.path, async function (err, data) {
            if (err) throw err; // Something went wrong!
            const contentType = mime.lookup(file.path);
            const options = {
                partSize: 10 * 1024 * 1024,
                // how many concurrent uploads
                queueSize: 5,
            };
            const pathName = new Date().getTime() + '/' + topicName;
            await s3
                .upload(
                    {
                        Bucket: BUCKET_DOCUMENT,
                        Key: pathName,
                        Body: data,
                        // ACL: 'public-read',
                        ContentType: contentType,
                        ContentLength: file.size,
                    },
                    options,
                    async (err, data) => {
                        if (err) {
                            console.log('error');
                            return reject(err);
                        }
                        console.log(data);
                    },
                )
                .on('httpUploadProgress', function (evt) {
                    console.log('Uploaded :: ' + parseInt((evt.loaded * 100) / evt.total) + '%');
                })
                .promise();

            const fileUploadUrl =
                `https://s3-${AWS_REGION}.amazonaws.com/` + BUCKET_DOCUMENT + '/' + pathName;
            return resolve(fileUploadUrl);
        });
    });
};

module.exports = {
    uploadAttachment,
    uploadQuestionAttachment,
    deleteAttachment,
    getUnsignedUrl,
    localFileStorage,
    removeFolder,
    uploadFileToAws,
};
