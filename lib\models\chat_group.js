const {
    USER,
    CHAT_GROUP,
    DIGI_COURSE,
    DIGI_PROGRAM,
    INSTITUTION_CALENDAR,
} = require('../utility/constants');
const {
    Schema,
    Types: { ObjectId },
    model,
} = require('mongoose');

const members = new Schema(
    {
        userId: {
            type: ObjectId,
            ref: USER,
        },
        userType: { type: String },
        isBlocked: {
            type: Boolean,
            default: false,
        },
        academicNo: { type: String },
        blockedDate: { type: Date },
    },
    { _id: false },
);

const digiChatSchema = new Schema(
    {
        channelName: {
            type: String,
            required: true,
        },
        channelType: {
            type: String,
        },
        _institution_calendar_id: {
            required: true,
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        members: [members],
        admin: [
            {
                type: ObjectId,
                ref: USER,
            },
        ],
        term: { type: String },
        year: { type: String },
        level: { type: String },
        rotation: { type: String },
        rotationCount: { type: String },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        gender: { type: String },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
        },
        course_no: { type: String },
        course_name: { type: String },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isBanned: {
            type: Boolean,
            default: false,
        },
        to: { type: String },
    },
    { timestamps: true },
);
module.exports = model(CHAT_GROUP, digiChatSchema);
