const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    stringRQSchema,
    booleanRQSchema,
    numberSchema,
    stringSchema,
    dateSchema,
    booleanSchema,
} = require('../../../utility/validationSchemas');

exports.createAssignmentValidator = Joi.object({
    courseId: objectIdRQSchema,
    programId: objectIdRQSchema,
    createdBy: objectIdRQSchema,
    course_code: stringRQSchema,
    courseName: stringRQSchema,
    isActive: booleanRQSchema,
    isDraft: booleanRQSchema,
    level_no: stringRQSchema,
    notificationStatus: stringRQSchema,
    programName: stringRQSchema,
    term: stringRQSchema,
    year_no: stringRQSchema,
    basic: Joi.object()
        .keys({
            title: stringRQSchema,
            type: stringRQSchema,
            scoringType: stringRQSchema,
        })
        .unknown(true),
}).unknown(true);

exports.duplicateAssignmentValidator = Joi.object({
    assignmentIds: Joi.array().items(objectIdRQSchema),
    courseId: objectIdSchema,
    courseCode: stringSchema,
    courseName: stringSchema,
    rotationCount: numberSchema,
    duplicateTo: stringSchema,
});

exports.resetAssignmentValidator = Joi.object({
    assignmentId: objectIdRQSchema,
});

exports.revertReleasedGradeValidator = Joi.object({
    assignmentId: objectIdRQSchema,
    studentId: objectIdRQSchema,
    staffId: objectIdRQSchema,
    isMultiStaff: booleanRQSchema,
});

const dateField = Joi.object()
    .keys({
        type: stringSchema,
        dateAndTime: dateSchema,
        duration: Joi.object().keys({
            value: stringSchema,
            type: stringSchema,
        }),
        courseOrSession: Joi.object().keys({
            type: stringSchema,
            after: stringSchema,
        }),
        previousDate: Joi.object().keys({
            date: dateSchema,
            reason: stringSchema,
        }),
    })
    .unknown(true);

exports.updateSubmissionDateValidator = Joi.object({
    submission: Joi.object()
        .keys({
            due: dateField,
            start: dateField,
            end: dateField,
            remindStudents: Joi.object().keys({
                checked: booleanSchema,
                duration: stringSchema,
            }),
            recurring: Joi.object().keys({
                checked: booleanSchema,
                type: stringSchema,
                at: stringSchema,
                days: Joi.array().items(stringSchema),
                time: stringSchema,
            }),
            includePreviousDiscussions: booleanSchema,
            recurWhenThisCourseIsActive: booleanSchema,
        })
        .unknown(true),
});

exports.paramIDValidator = Joi.object({
    id: objectIdRQSchema,
});
