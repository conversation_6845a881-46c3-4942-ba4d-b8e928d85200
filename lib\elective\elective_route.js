const express = require('express');
const route = express.Router();
const elective = require('./elective_controller');
const validater = require('./elective_validator');
// route.get('/complete/:id', validater.elective_id, elective.elective_complete);
// route.post('/list', elective.list_values);
// route.get('/:id', validater.elective_id, elective.list_id);
route.get('/', elective.list);
route.post('/', validater.elective_topic_allocation, elective.insert_topic_allocation);
// route.put('/:id', validater.elective_id, validater.elective, elective.update);
route.delete('/:course_id/:topic_id', validater.elective_id_topic_id, elective.delete);
route.get('/program/:id', validater.elective_id, elective.list_program);
// route.get('/subject/:id', validater.elective_id, elective.list_subjects);
route.get('/program/:id/:level', validater.elective_id_level, elective.list_elective_program_level);
// route.delete('/delete_department/:id/:sub_id', validater.elective_id_sub_id, elective.delete_department);
// route.delete('/delete_division/:id/:sub_id', validater.elective_id_sub_id, elective.delete_division);
route.post('/session_order', validater.elective_session_order, elective.elective_session_order_insert);
module.exports = route;