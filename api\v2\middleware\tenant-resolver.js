const { getConnectionByTenant } = require('../db-management/db-connection.service');
const { identifyOrigin } = require('./origin-resolver.util');
const { ENABLE_SAAS } = require('../utility/util_keys');
const { CLIENT_URI } = require('../utility/constants');

const tenantResolver = (req, _, next) => {
    // const tenant = req.headers.tenant;
    //todo verify origin with the decoded token
    let tenantSubdomain;
    if (!req.headers.origin) {
        tenantSubdomain = 'ecs-dsweb-staging';
    } else {
        const { subdomain } = identifyOrigin(req.headers.origin);
        tenantSubdomain = subdomain;
    }
    let tenantConnectionURL;
    if (ENABLE_SAAS == 'true') {
        tenantConnectionURL = getConnectionByTenant(tenantSubdomain);
    } else {
        tenantConnectionURL = getConnectionByTenant(CLIENT_URI);
    }
    if (!tenantConnectionURL) return { statusCode: 409, message: 'Origin not found in records' };
    req.headers.tenantURL = tenantConnectionURL;
    next();
};

module.exports = { tenantResolver };
