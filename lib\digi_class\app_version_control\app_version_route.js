const express = require('express');
const route = express.Router();
const { appVersionGet, appVersionAdd } = require('./app_version_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get('/versionList', [userPolicyAuthentication([])], appVersionGet);
route.post('/', [userPolicyAuthentication([])], appVersionAdd);

module.exports = route;
