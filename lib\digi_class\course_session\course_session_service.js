const { dsGetAllWithSortAsJSON, get, get_list } = require('../../base/base_controller');
const CourseSchedule = require('../../models/course_schedule');
const Course = require('../../models/digi_course');
const Activity = require('../../models/activities');
const Question = require('../../models/question');
const Document = require('../../models/document_manager');
const Program = require('../../models/digi_programs');
const InstitutionCalendar = require('../../models/institution_calendar');
const CourseScheduleSetting = require('../../models/course_schedule_setting');
const ProgramCalendar = require('../../models/program_calendar');
const User = require('../../models/user');
const InstitutionCalendars = require('../../utility/institution_calendar');
const ScheduleAttendanceModel = require('../../models/schedule_attendance');
const ScheduleMultiDeviceAttendanceSchema = require('../../models/schedule_multi-device_attendance');
const studentGroupSchema = require('../../models/student_group');

const {
    convertToMongoObjectId,
    convertToUtcFormat,
    convertToUtcEndFormat,
    convertToUtcTimeFormat,
    sendResponse,
} = require('../../utility/common');
const {
    COMPLETED,
    PENDING,
    PROGRAM_CALENDAR,
    PRESENT,
    ABSENT,
    LEAVE,
    DC_STAFF,
    SCHEDULE_TYPES: { EVENT, SUPPORT_SESSION, REGULAR },
    DAYS,
    COURSE_SCHEDULE,
    TIME_GROUP_BOOKING_TYPE,
    DS_CLO_KEY,
    DS_SLO_KEY,
    DRAFT,
    SCHEDULE,
    STUDENT_CRITERIA_MANIPULATION,
    STUDENT_GROUP,
    ATTENDANCE_DEFAULT_END_TIME,
    RUNNING,
    SURPRISE_QUIZ,
    DC_STUDENT,
    PUBLISHED,
    REDIS_FOLDERS: { USER_COURSES, USER_INSTITUTION_CALENDAR },
    BOTH,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    COURSE_WISE,
    EXCLUDE,
    STUDENT_GROUP_MODE: { COURSE, ROTATION, FYD },
    GENDER: { MALE },
    STUDENT_WARNING_REDIS,
    LMS_STUDENT_SETTING,
    WARNING_CONFIG_TYPE,
    SESSION_MODE: { AUTO },
    LATE_CONFIG,
    COURSE_HANDOUT,
} = require('../../utility/constants');
const { convertingRiyadhToUTC, dateTimeBasedConverter } = require('../../utility/common_functions');
const { PM } = require('../../utility/enums');
const {
    logger,
    SERVICES: { FACE_AUTH_TYPE, LATE_ABSENT_CONFIGURATION },
} = require('../../utility/util_keys');
const query = { isDeleted: false, isActive: true };
const cs = (str) => str.toString();
const clone = (object) => JSON.parse(JSON.stringify(object));
const getJSON = dsGetAllWithSortAsJSON;
const { getCourseAdminParams } = require('../course_admin/course_admin_service');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const studentGroup = require('mongoose').model(STUDENT_GROUP);
const constant = require('../../utility/constants');
const lms = require('mongoose').model(constant.LMS);
const lmsDenialSchema = require('../../lms_denial/lms_denial_model');
const warningMailSchema = require('../../models/warning_mail');
const lmsStudentSettingSchema = require('mongoose').model(LMS_STUDENT_SETTING);
const lmsLateConfigSettingSchema = require('../../lmsAttendanceConfig/lmsAttendanceConfig.model');
const courseHandoutSchema = require('../../courseHandout/courseHandout.model');
const {
    lmsNewSetting,
    getLateAutoAndManualRange,
    getLateConfigAndStudentLateAbsent,
    getLateConfigAndStudentLateAbsentForStaffExport,
    getConfigAndStudentLateAbsentForSingleStudent,
    getLateLabelForSchedule,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    checkRestrictCourse,
    multiCourseWarningsForStudent,
    lmsNewSettingWithOptions,
    getWarningDataFromRedis,
} = require('../../utility/utility.service');

const { getUniqueStudentGroup } = require('../dashboard/dashboard.service');
const { getHandoutValue } = require('../../courseHandout/courseHandout.service');

const getInfraById = async (infraIds) => {
    try {
        csQuery = { 'programs.remoteScheduling._id': { $in: infraIds } };
        csProject = { 'programs.remoteScheduling': 1 };

        return await CourseScheduleSetting.find(csQuery, csProject).lean();
    } catch (error) {
        throw new Error(error);
    }
};
const { redisClient } = require('../../../config/redis-connection');
const { luaScript } = require('../../../service/redisCache.service');

// Leave Setting Data
const lmsSettings = async () => {
    try {
        let warningAbsenceData = [
            { warning: 'Denial', absence_percentage: 101, warning_message: 'Denial' },
        ];
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (
            leave_category &&
            leave_category &&
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0 &&
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ).length !== 0
        )
            warningAbsenceData = clone(
                leave_category.data.student_warning_absence_calculation.filter(
                    (ele) => ele.isDeleted === false,
                ),
            );
        warningAbsenceData = clone(
            warningAbsenceData.sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                    comparison = -1;
                } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                    comparison = 1;
                }
                return comparison;
            }),
        );

        const finaleWarning =
            warningAbsenceData.length !== 1
                ? warningAbsenceData[1] && warningAbsenceData[1].warning
                    ? warningAbsenceData[1].warning
                    : undefined
                : undefined;
        const denialWarning = warningAbsenceData[0].warning;
        return {
            warningAbsenceData,
            finaleWarning,
            denialWarning,
        };
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        throw new Error(error);
    }
};
const getCourseIds = async (userId, type) => {
    try {
        const csQuery = {
            isDeleted: false,
            isActive: true,
        };
        csQuery[type === DC_STAFF ? 'staffs._staff_id' : 'students._id'] =
            convertToMongoObjectId(userId);

        const csProject = { _course_id: 1, _id: 0 };
        const courseSchedule = await CourseSchedule.find(csQuery, csProject).lean();
        return [...new Set(courseSchedule.map((schedule) => schedule._course_id))];
    } catch (error) {
        throw new Error(error);
    }
};
const getRatingByCourses = async (
    institutionCalendarId,
    courseIds,
    staffId,
    year,
    level,
    term,
    rotation,
    rotationCount,
) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            termQuery = [term];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
        } else {
            courseQuery = courseIds;
            yearQuery = year;
            levelQuery = level;
            rotationQuery = rotation;
            termQuery = term;
            rotationCountQuery = rotationCount;
        }
        const fbQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            term: { $in: termQuery },
            $or: [
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'sessionDetail.startBy': convertToMongoObjectId(staffId),
                },
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'staffs.status': PRESENT,
                },
            ],
            students: { $exists: true, $type: 'array', $ne: [] },
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };

        const fbProject = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            term: 1,
            rotation_count: 1,
            rotation: 1,
            session: 1,
        };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject);
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    courseSchedule.session &&
                    courseSchedule.session._session_id &&
                    staff._staff_id.toString() === staffId.toString() &&
                    staff.status === PRESENT,
            ),
        );

        const feedBacks = [];

        if (courseQuery && courseQuery.length) {
            let i = 0;
            courseQuery.map((courseId) => {
                const courseDetails = courseSchedules.filter((courseSchedule) => {
                    if (courseSchedule._course_id.toString() === courseId.toString()) {
                        return true;
                    }
                    return false;
                });
                let sumOfRatings = 0;
                let count = 0;
                let levelOfCourseId;
                let yearOfCourseId;
                const schedulePush = [];
                let rotation_count;
                let rotation;
                courseDetails.map((courseDetail) => {
                    const { students, level_no, year_no, _id, term } = courseDetail;

                    rotation_count = courseDetail.rotation_count ? courseDetail.rotation_count : '';
                    rotation = courseDetail.rotation ? courseDetail.rotation : '';
                    termOfCourseId = term;
                    levelOfCourseId = level_no;
                    yearOfCourseId = year_no;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);
                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                    students.map((student) => {
                        if (student.feedBack && student.feedBack.rating) {
                            schedulePush.push(_id);
                        }
                    });
                });

                if (count) {
                    feedBacks.push({
                        _course_id: courseId,
                        level_no: levelOfCourseId,
                        year_no: yearOfCourseId,
                        term: termOfCourseId,
                        totalFeedback: count,
                        avgRating: count ? (sumOfRatings / count).toFixed(1) : 0,
                        sessionCount: schedulePush.length,
                        rotation,
                        rotationCount: rotation_count,
                    });
                }
                i++;
            });
        }
        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};
const getRatingBySessions = async (sessionIds, staffId) => {
    try {
        let courseSchedules;
        if (sessionIds && staffId) {
            courseSchedules = await CourseSchedule.find(
                {
                    _id: { $in: sessionIds },
                    $or: [
                        {
                            'staffs._staff_id': convertToMongoObjectId(staffId),
                            'sessionDetail.startBy': convertToMongoObjectId(staffId),
                        },
                        {
                            'staffs._staff_id': convertToMongoObjectId(staffId),
                            'staffs.status': PRESENT,
                        },
                    ],
                    students: { $exists: true, $type: 'array', $ne: [] },
                },
                { _id: 1, students: 1 },
            );
        } else {
            courseSchedules = await CourseSchedule.find(
                {
                    _id: { $in: sessionIds },
                    students: { $exists: true, $type: 'array', $ne: [] },
                },
                { _id: 1, students: 1 },
            );
        }

        const feedBacks = [];
        const sessionIdList = sessionIds.map((sessionId) => sessionId.toString());
        if (sessionIds) {
            const sessionIdFilters = sessionIds.filter((sessionId) => sessionId);
            sessionIdFilters.map((sessionIdss) => {
                const courseDetails = courseSchedules.filter((courseSchedule) => {
                    if (sessionIdList.includes(courseSchedule._id.toString())) {
                        return true;
                    }
                    return false;
                });
                let sumOfRatings = 0;
                let count = 0;

                courseDetails.map((courseDetail) => {
                    const { students } = courseDetail;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);

                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                });
                if (count !== 0) {
                    feedBacks.push({
                        _session_id: sessionIdss,
                        totalFeedback: count,
                        avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                    });
                }
            });
        }

        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentCourseIds = async (studentId) => {
    try {
        const csQuery = { 'students._id': studentId };
        const csProject = { _course_id: 1, _id: 0 };
        const courseSchedule = await CourseSchedule.find(csQuery, csProject);
        return [...new Set(courseSchedule.map((schedule) => schedule._course_id))];
    } catch (error) {
        throw new Error(error);
    }
};
const getCourses = async (staffId, institutionCalendarIds) => {
    try {
        const courseScheduleQuery = {
            schedule_date: { $exists: true },
            isDeleted: false,
            _institution_calendar_id: { $in: institutionCalendarIds },
        };

        if (staffId) {
            courseScheduleQuery.$or = [
                { 'staffs._staff_id': convertToMongoObjectId(staffId) },
                { 'students._id': convertToMongoObjectId(staffId) },
            ];
        }
        const coursesList = await CourseSchedule.find(courseScheduleQuery, {
            course_name: 1,
            course_code: 1,
            _program_id: 1,
            _institution_calendar_id: 1,
            year_no: 1,
            program_name: 1,
            level_no: 1,
            rotation: 1,
            rotation_count: 1,
            term: 1,
            _course_id: 1,
        })
            .populate({
                path: '_course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        coursesLists = [];
        coursesList.filter(function (coursesListsEntry) {
            const chkCoursesList = coursesLists.findIndex(
                (coursesListEntry) =>
                    coursesListEntry._program_id.toString() ===
                        coursesListsEntry._program_id.toString() &&
                    coursesListEntry._institution_calendar_id.toString() ===
                        coursesListsEntry._institution_calendar_id.toString() &&
                    coursesListEntry.year_no === coursesListsEntry.year_no &&
                    coursesListEntry.level_no === coursesListsEntry.level_no &&
                    coursesListEntry.rotation === coursesListsEntry.rotation &&
                    coursesListEntry.rotation_count === coursesListsEntry.rotation_count &&
                    coursesListEntry.term == coursesListsEntry.term &&
                    coursesListEntry._course_id._id.toString() ===
                        coursesListsEntry._course_id._id.toString(),
            );
            if (chkCoursesList <= -1) {
                coursesListsEntry._id = coursesListsEntry._course_id._id;
                coursesLists.push(coursesListsEntry);
            }
            return null;
        });
        coursesLists = coursesLists.filter((coursesList) => coursesList.rotation_count !== null);
        return coursesLists;
    } catch (error) {
        throw new Error(error);
    }
};

// get course admin
const getCourseAdmin = async (userId, institutionCalendarId) => {
    let courseAdmin = false;
    let courses = await getCourses(userId);
    courses = courses.filter(
        (course) => course._institution_calendar_id.toString() === institutionCalendarId.toString(),
    );
    courseQuery = courses.map((course) => {
        return {
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
            _id: convertToMongoObjectId(course._id),
            'coordinators.term': course.term,
            'coordinators.year': course.year_no,
            'coordinators.level_no': course.level_no,
        };
    });
    const courseFindQuery = {
        'coordinators._user_id': convertToMongoObjectId(userId),
        'coordinators.status': true,
    };
    if (courseQuery.length > 0) courseFindQuery.$or = courseQuery;
    const coordinators = await Course.find(courseFindQuery, { _id: 1 }).lean();
    if (coordinators.length) {
        courseAdmin = true;
    }
    return courseAdmin;
};

const getCourseIdsWithSessions = async (
    staffId,
    type,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    courseAdmin,
) => {
    try {
        const orQuery = [];
        const csQuery = {
            schedule_date: { $exists: true },
            isDeleted: false,
        };
        if (type) {
            if (type !== 'all' && type !== 'today') {
                orQuery.push(
                    {
                        'session.session_type': type,
                    },
                    {
                        type,
                    },
                );
            } else if (type === 'today') {
                const todayDate = convertToUtcFormat(new Date());
                csQuery.schedule_date = new Date(todayDate);
            }
        }
        if (courseAdmin !== 'true') {
            // check course admin verification
            const staff = await User.findOne(
                { _id: convertToMongoObjectId(staffId), user_type: 'staff' },
                { _id: 1 },
            );
            if (staff) {
                csQuery['staffs._staff_id'] = convertToMongoObjectId(staffId);
            } else {
                csQuery['students._id'] = convertToMongoObjectId(staffId);
            }
            if (_institution_calendar_id) {
                csQuery._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
            }
            if (_course_id) {
                csQuery._course_id = convertToMongoObjectId(_course_id);
                csQuery._program_id = convertToMongoObjectId(_program_id);
                csQuery.year_no = year_no;
                csQuery.level_no = level_no;
                csQuery.term = term;
                if (rotation) {
                    csQuery.rotation = rotation;
                }
                if (rotation_count) {
                    csQuery.rotation_count = rotation_count;
                }
            }
            if (orQuery.length) {
                csQuery.$or = orQuery;
            }
        } else {
            let courseParams;
            if (_institution_calendar_id) {
                courseParams = await getCourseAdminParams(staffId, _institution_calendar_id);
            } else {
                const institutionCalendarId = await InstitutionCalendar.findOne(
                    { status: PUBLISHED },
                    { _id: 1 },
                ).sort({
                    _id: -1,
                });
                courseParams = await getCourseAdminParams(staffId, institutionCalendarId._id);
            }
            if (courseParams.length) {
                if (_program_id) {
                    csQuery._course_id = convertToMongoObjectId(_course_id);
                    csQuery._institution_calendar_id =
                        convertToMongoObjectId(_institution_calendar_id);
                    csQuery._program_id = convertToMongoObjectId(_program_id);
                    csQuery.year_no = year_no;
                    csQuery.level_no = level_no;
                    csQuery.term = term;
                    if (rotation) {
                        csQuery.rotation = rotation;
                    }
                    if (rotation_count) {
                        csQuery.rotation_count = rotation_count;
                    }
                } else {
                    csQuery.$or = courseParams;
                }
            } else {
                return {
                    courseIds: [],
                    programIds: [],
                    year: [],
                    courseSchedule: [],
                    level: [],
                    rotation: [],
                    rotationCount: [],
                    courses: [],
                };
            }
        }
        const csProject = {
            _course_id: 1,
            session: 1,
            student_groups: 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            end: 1,
            start: 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            _infra_id: 1,
            infra_id: '$_infra_id',
            infra_name: 1,
            staffs: 1,
            status: 1,
            sessionDetail: 1,
            students: 1,
            uuid: 1,
            socket_port: 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            isActive: 1,
            isDeleted: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            zoomDetail: 1,
            teamsDetail: 1,
            remotePlatform: 1,
            retakeStatus: 1,
            isLive: 1,
            isMissedToComplete: 1,
            classModeType: 1,
        };
        let courseSchedule = await CourseSchedule.find(csQuery, csProject)
            .sort({ 'sessions.delivery_symbol': 1, 'sessions.delivery_no': 1 })
            .populate([
                {
                    path: '_infra_id',
                    select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
                },
                {
                    path: '_course_id',
                    select: { course_assigned_details: 1 },
                },
            ])
            .lean();
        const studentLists = courseSchedule.reduce((accumulator, currentValue) => {
            return accumulator.concat(currentValue.students);
        }, []);
        const studentIds = studentLists.map((student) => student._id);
        const usersLists = await User.find(
            { _id: { $in: studentIds } },
            { user_id: 1, isActive: 1 },
        );
        let mergedSchedules = courseSchedule.filter(
            (courseScheduleEntry) => courseScheduleEntry.merge_status,
        );
        mergedSchedules = mergedSchedules.map((mergedSchedule) => mergedSchedule.merge_with);
        // eslint-disable-next-line no-sequences
        mergedSchedules = mergedSchedules.reduce((r, e) => (r.push(...e), r), []);
        const mergedScheduleIds = mergedSchedules.map((mergedSchedule) =>
            convertToMongoObjectId(mergedSchedule.schedule_id),
        );
        mergedSchedules = await CourseSchedule.find({ _id: { $in: mergedScheduleIds } }, csProject);
        const scheduleIds = courseSchedule.map((scheduleId) => scheduleId._id);
        const getScheduleAttendance = await ScheduleAttendanceModel.find(
            {
                scheduleId: { $in: scheduleIds },
                status: RUNNING,
            },
            { _id: 1, scheduleId: 1, status: 1, _staff_id: 1, modeBy: 1, createdAt: 1 },
        );
        courseSchedule = courseSchedule.map((schedule) => {
            schedule.isLive = schedule && schedule.isLive ? schedule.isLive : false;
            let sumOfRatings = 0;
            let count = 0;
            let mergedStudents = [];
            const { students, _infra_id, staffs, merge_status, merge_with, _program_id, level_no } =
                schedule;
            students.forEach((student) => {
                const checkStudent = usersLists.find(
                    (studentEntry) => studentEntry._id.toString() === student._id.toString(),
                );
                student.user_id = checkStudent ? checkStudent.user_id : '';
            });
            schedule.students = students.filter((activeCheck) =>
                usersLists.find(
                    (userElement) =>
                        userElement._id.toString() === activeCheck._id.toString() &&
                        userElement.isActive,
                ),
            );
            if (getScheduleAttendance && getScheduleAttendance.length) {
                const retakeDetails = getScheduleAttendance.find((retakeId) =>
                    retakeId.scheduleId.find(
                        (scheduleId) => scheduleId.toString() === schedule._id.toString(),
                    ),
                );

                schedule.retakeDetails = retakeDetails;
            }
            const startBy = staffs.find(
                (staff) => staff._staff_id.toString() === staffId.toString(),
            );
            const courseId = schedule._course_id._id;
            const checkAutoEndAttendanceIn = schedule._course_id.course_assigned_details.find(
                (element) =>
                    element._program_id.toString() === _program_id.toString() &&
                    element.level_no === level_no,
            );
            schedule.auto_end_attendance_in =
                checkAutoEndAttendanceIn && checkAutoEndAttendanceIn.auto_end_attendance_in
                    ? checkAutoEndAttendanceIn.auto_end_attendance_in
                    : ATTENDANCE_DEFAULT_END_TIME;

            schedule._course_id = courseId;
            mergedStudents = mergedStudents.concat(students);
            if (merge_status) {
                const mergedWithSchedules = merge_with.map((mergeWith) => mergeWith.schedule_id);
                mergedWithSchedules.forEach((mergedWithSchedule) => {
                    const mergedWithScheduleStudents = mergedSchedules.find(
                        (mergedSchedule) =>
                            mergedSchedule._id.toString() === mergedWithSchedule.toString(),
                    );
                    if (mergedWithScheduleStudents) {
                        mergedStudents = mergedStudents.concat(mergedWithScheduleStudents.students);
                    }
                });
            }

            if (
                (courseAdmin !== 'true' &&
                    mergedStudents &&
                    mergedStudents.length &&
                    startBy &&
                    startBy.status === PRESENT) ||
                (courseAdmin === 'true' && mergedStudents && mergedStudents.length)
            ) {
                sumOfRatings += mergedStudents
                    .filter((student) => student.feedBack && student.feedBack.rating)
                    .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                    .reduce((a, b) => a + b, 0);

                count += mergedStudents.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
            }
            if (count) {
                schedule.feedBack = {
                    _session_id: schedule._id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                };
            }
            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                schedule.infra_name = infraName;
            }
            // if (schedule.mode === TIME_GROUP_BOOKING_TYPE.REMOTE) {
            //     const infraDetail = infraDetails
            //         .map((a) => a.programs)
            //         .flat()
            //         .map((b) =>
            //             b.remoteScheduling.filter(
            //                 (i) =>
            //                     schedule.infra_id &&
            //                     i._id.toString() === schedule.infra_id.toString(),
            //             ),
            //         )
            //         .flat()
            //         .shift();

            //     const {
            //         start: { hour: startHour, minute: startMinute, format: startFormat },
            //         end: { hour: endHour, minute: endMinute, format: endFormat },
            //         schedule_date,
            //         _infra_id,
            //     } = schedule;

            //     if (!schedule.scheduleStartDateAndTime && !schedule.scheduleEndDateAndTime) {
            //         const startHours =
            //             startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
            //         const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
            //         const startDateAndTime = convertingRiyadhToUTC(
            //             schedule_date,
            //             startHours,
            //             startMinute,
            //         );
            //         const endDateAndTime = convertingRiyadhToUTC(
            //             schedule_date,
            //             endHours,
            //             endMinute,
            //         );
            //         schedule.scheduleStartDateAndTime = startDateAndTime;
            //         schedule.scheduleEndDateAndTime = endDateAndTime;
            //     }

            //     if (infraDetail) {
            //         schedule.infra_name =
            //             infraDetail.meetingTitle +
            //             '\n' +
            //             infraDetail.meetingUrl +
            //             '\n' +
            //             infraDetail.meetingId;
            //         const remoteInfraDetails = {
            //             meetingTitle: infraDetail.meetingTitle,
            //             meetingUrl: infraDetail.meetingUrl,
            //             meetingId: infraDetail.meetingId,
            //             passCode: infraDetail.passCode,
            //             apiKey:
            //                 infraDetail.apiKey && infraDetail.apiKey.length !== 0
            //                     ? infraDetail.apiKey
            //                     : '',
            //             apiSecretKey:
            //                 infraDetail.apiSecretKey && infraDetail.apiSecretKey.length
            //                     ? infraDetail.apiSecretKey
            //                     : '',
            //             associatedEmail: infraDetail.associatedEmail,
            //             password: infraDetail.password,
            //         };
            //         schedule.infraDatas = remoteInfraDetails;
            //     }
            // }
            return schedule;
        });

        const courseIds = [...new Set(courseSchedule.map((schedule) => cs(schedule._course_id)))];
        // Getting Course
        const programCalendarData = await ProgramCalendar.find(
            {
                // _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                // _program_id: convertToMongoObjectId(_program_id),
            },
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.level_no': 1,
                'level.term': 1,
                'level.year': 1,
                'level.end_date': 1,
                'level.start_date': 1,
                'level.course._course_id': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );

        const splittedCourses = [];
        for (const courseId of courseIds) {
            const filterCourseSchedule = courseSchedule.filter(
                (courseScheduleEntry) => cs(courseScheduleEntry._course_id) === cs(courseId),
            );
            // different calendar and year and level based courses
            const courses = filterCourseSchedule.reduce((acc, current) => {
                const x = acc.find(
                    (item) =>
                        item._institution_calendar_id.toString() ===
                            current._institution_calendar_id.toString() &&
                        item._program_id.toString() === current._program_id.toString() &&
                        item.year_no === current.year_no &&
                        item.level_no === current.level_no &&
                        item.term === current.term &&
                        ((item.rotation.toString() === 'yes' &&
                            item.rotation_count &&
                            current.rotation_count &&
                            item.rotation_count.toString() === current.rotation_count.toString()) ||
                            item.rotation.toString() === 'no'),
                );

                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            if (courses.length) {
                for (const course of courses) {
                    const {
                        _institution_calendar_id,
                        _program_id,
                        year_no,
                        level_no,
                        rotation,
                        rotation_count,
                        _course_id,
                        term,
                    } = course;
                    if (programCalendarData) {
                        const { level } = programCalendarData.find(
                            (calendarElement) =>
                                calendarElement._institution_calendar_id.toString() ===
                                    _institution_calendar_id.toString() &&
                                calendarElement._program_id.toString() === _program_id.toString(),
                        );
                        const levelsEntry = level.find(
                            (levelEntry) =>
                                levelEntry.year === year_no &&
                                levelEntry.level_no === level_no &&
                                levelEntry.term === term,
                        );
                        const { course: coursesEntry, rotation_course } = levelsEntry;
                        let dates;
                        if (rotation === 'yes') {
                            dates = rotation_course
                                .filter(
                                    (rotationCourseEntry) =>
                                        rotationCourseEntry.rotation_count === rotation_count,
                                )
                                .map((rCourse) => rCourse.course)
                                .flat()
                                .find(
                                    (rotationCourse) =>
                                        rotationCourse._course_id.toString() ===
                                        _course_id.toString(),
                                );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                            course.rotation_count = rotation_count;
                        } else {
                            dates = coursesEntry.find(
                                (courseEntry) =>
                                    courseEntry._course_id.toString() === _course_id.toString(),
                            );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                        }
                    }
                }
                splittedCourses.push(courses);
            }
        }
        // eslint-disable-next-line prefer-spread
        const mergedCourses = [].concat.apply([], splittedCourses);
        const programIds = [...new Set(courseSchedule.map((schedule) => schedule._program_id))];
        const year = [...new Set(courseSchedule.map((schedule) => schedule.year_no))];
        const level = [...new Set(courseSchedule.map((schedule) => schedule.level_no))];
        const courseTerm = [...new Set(courseSchedule.map((schedule) => schedule.term))];
        const rotations = [...new Set(courseSchedule.map((schedule) => schedule.rotation))];
        const rotationCount = [
            ...new Set(courseSchedule.map((schedule) => schedule.rotation_count)),
        ];
        return {
            courseIds,
            programIds,
            year,
            courseSchedule,
            level,
            rotation: rotations,
            rotationCount,
            courses: mergedCourses,
            term: courseTerm,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getPrograms = async (programIds) => {
    try {
        const pQuery = { ...query, _id: { $in: programIds } };
        const pProject = { name: 1 };
        return (await getJSON(Program, pQuery, pProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getProgramName = (programs, programId) => {
    let programName;
    programs.forEach((program) => {
        if (cs(program._id) === cs(programId)) {
            programName = program.name;
        }
    });
    return programName;
};

const getStudentAttendedSession = (courseSchedules, studentId) => {
    let attended = 0;
    courseSchedules.forEach((cSchedule) => {
        if (!cSchedule.students) return;
        if (
            cSchedule.type === REGULAR &&
            cSchedule.status === COMPLETED &&
            cSchedule.students.find(
                (student) =>
                    student.status === 'present' &&
                    student._id &&
                    studentId &&
                    cs(student._id) === cs(studentId),
            )
        ) {
            attended++;
        }
    });
    return attended;
};

const getStudentAbsentSession = (courseSchedules, studentId) => {
    let absent = 0;
    courseSchedules.forEach((cSchedule) => {
        if (!cSchedule.students) return;
        if (
            cSchedule.type === REGULAR &&
            cSchedule.status === COMPLETED &&
            cSchedule.students.find(
                (student) =>
                    (student.status === 'absent' || student.status === 'leave') &&
                    student._id &&
                    studentId &&
                    cs(student._id) === cs(studentId),
            )
        ) {
            absent++;
        }
    });
    return absent;
};

const getSessions = (
    lmsData,
    studentCriteriaData,
    courseSchedules,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    userId,
    rotation,
    rotation_count,
    getSchedule,
    lateDurationRange,
    manualLateRange,
    manualLateData,
    lateExcludeManagement,
) => {
    try {
        let courseSessions = [];
        const courseSchedulesEntry =
            rotation === 'yes'
                ? courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation &&
                          rotation_count &&
                          rotation_count === courseSchedule.rotation_count,
                  )
                : courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation,
                  );

        const isExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter((courseSession) => {
                if (
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    courseSession._session_id &&
                    sessionIdOrType &&
                    courseSession._session_id.toString() === sessionIdOrType.toString()
                ) {
                    if (
                        mergeStatus &&
                        scheduleId.toString() === courseSession.scheduleId.toString()
                    ) {
                        return true;
                    }
                    if (!mergeStatus) {
                        return true;
                    }
                }
                return false;
            }).length;

        const isSessionTypeExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter(
                (courseSession) =>
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    !courseSession._session_id &&
                    courseSession.session_topic === sessionIdOrType &&
                    courseSession.scheduleId &&
                    scheduleId.toString() === courseSession.scheduleId.toString(),
            ).length;

        courseSchedulesEntry.forEach((courseSchedule) => {
            courseSchedule.student_groups = courseSchedule.student_groups.map((studentGroup) => {
                studentGroup.students = [];
                return studentGroup;
            });
            if (
                courseSchedule.session &&
                !isExist(
                    courseSchedule.merge_status,
                    courseSchedule.session._session_id,
                    courseSchedule._id,
                )
            ) {
                const { _id, session, merge_status, merge_with, student_groups, status } =
                    courseSchedule;
                session.merge_status = merge_status;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (session && !merge_status) {
                    session.status = status;
                    session.student_groups = student_groups;
                    session.scheduleId = _id;
                    courseSessions.push(session);
                } else {
                    const mergedSessions = [];
                    const mergedStudents = [];
                    merge_with.forEach((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                    mergeWith.schedule_id.toString() &&
                                courseScheduleEntry.session &&
                                courseScheduleEntry.session._session_id.toString() ===
                                    mergeWith.session_id.toString(),
                        );
                        if (sessionDetails) {
                            mergeWith.session = {
                                _session_id: sessionDetails.session._session_id,
                                s_no: sessionDetails.session.s_no,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                                topic: sessionDetails.topic,
                                session_type: sessionDetails.session.session_type,
                                session_topic: sessionDetails.session.session_topic,
                            };
                            if (sessionDetails.students && sessionDetails.students.length) {
                                mergedStudents.push(sessionDetails.students);
                            }
                        }
                        mergedSessions.push(mergeWith);
                    });
                    const mergedScheduleIds = merge_with.map((mergeWith) =>
                        mergeWith.schedule_id.toString(),
                    );
                    let sessionMerged = merge_with.filter((mergeWith) => mergeWith.session_id);
                    sessionMerged = sessionMerged.map((mergeWith) =>
                        mergeWith.session_id.toString(),
                    );
                    courseSchedule.merge_with = mergedSessions;
                    if (mergedStudents.length) {
                        if (courseSchedule.students && courseSchedule.students.length)
                            mergedStudents.push(courseSchedule.students);
                        // eslint-disable-next-line prefer-spread
                        const concatMergedStudents = [].concat.apply([], mergedStudents);
                        //Sort the result to show beedback first then only we can identify using find if merge is scheduled
                        const sortFeedbackBasedStudents = concatMergedStudents.sort(function (
                            a,
                            b,
                        ) {
                            return b.feedBack ? 1 : -1;
                        });
                        // duplicate student removed
                        const students = sortFeedbackBasedStudents.reduce((acc, current) => {
                            const x = acc.find(
                                (item) => item._id.toString() === current._id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                        courseSchedule.students = students;
                    }
                    const duplicateMergedSession = courseSessions.find(
                        (courseSession) =>
                            courseSession._session_id &&
                            sessionMerged.includes(courseSession._session_id.toString()) &&
                            courseSession.merge_status.toString() ===
                                courseSchedule.merge_status.toString() &&
                            mergedScheduleIds.includes(courseSession.scheduleId.toString()),
                    );

                    const mergedSessionSchedules = courseSchedulesEntry.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    session.scheduleId = _id;
                    if (!duplicateMergedSession) courseSessions.push(session);
                }
            }
            // splitted to session type based
            const sessionTypes = [EVENT, SUPPORT_SESSION];
            if (
                courseSchedule.type &&
                sessionTypes.includes(courseSchedule.type) &&
                !isSessionTypeExist(
                    courseSchedule.merge_status,
                    courseSchedule.title,
                    courseSchedule._id,
                )
            ) {
                const { type, merge_status, student_groups, merge_with } = courseSchedule;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (type) {
                    courseSessions.push({
                        scheduleId: courseSchedule._id,
                        session_type: courseSchedule.type,
                        session_topic: courseSchedule.title,
                        merge_status,
                        student_groups,
                    });
                }
                if (merge_status) {
                    const mergedSessions = merge_with.map((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString(),
                        );
                        mergeWith.session = {
                            session_type: sessionDetails.type,
                            session_topic: sessionDetails.title,
                        };
                        return mergeWith;
                    });
                    courseSchedule.merge_with = mergedSessions;
                    courseSessions.push(session);
                }
            }
        });

        let totalSessions = 0;
        let completedSessions = 0;
        let warningCount = 0;
        let presentCount = 0;
        let leaveCount = 0;
        let absentCount = 0;
        let ondutyCount = 0;
        let permissionCount = 0;
        // sort by session
        let courseSessionsRegular = courseSessions.filter(
            (courseSession) => courseSession._session_id,
        );
        const courseSessionsOthers = courseSessions.filter(
            (courseSession) => !courseSession._session_id,
        );
        courseSessionsRegular = courseSessionsRegular.sort((a, b) =>
            a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
        );
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id,
            programId: _program_id,
            courseId: _course_id,
            yearNo: year_no,
            levelNo: level_no,
            term,
            rotation,
            rotationCount: rotation_count,
            lateExcludeManagement,
        });
        const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id,
            programId: _program_id,
            courseId: _course_id,
            yearNo: year_no,
            levelNo: level_no,
            term,
            rotation,
            rotationCount: rotation_count,
            lateExcludeManagement,
            studentId: userId,
        }).lateExclude;
        courseSessions = courseSessionsRegular.concat(courseSessionsOthers);
        const courseScheduleStringify = clone(courseSchedulesEntry);
        courseSessions.forEach((courseSession) => {
            const { merge_status: courseSessionMergeStatus, scheduleId } = courseSession;
            // courseSession.documentCount = 0; // To Do
            // courseSession.activityCount = 0; // To Do
            let schedules = courseSession._session_id
                ? courseScheduleStringify.filter((courseSchedule) => {
                      if (
                          courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          if (
                              courseSessionMergeStatus &&
                              scheduleId.toString() === courseSchedule._id.toString()
                          ) {
                              return true;
                          }
                      } else if (
                          !courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          return true;
                      }
                  })
                : courseScheduleStringify.filter(
                      (courseSchedule) =>
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSession.session_topic === courseSchedule.title &&
                          courseSchedule._id &&
                          courseSchedule._id.toString() === scheduleId.toString(),
                  );
            schedules = schedules.map((schedule) => {
                const {
                    start: { hour: startHour, minute: startMinute, format: startFormat },
                    end: { hour: endHour, minute: endMinute, format: endFormat },
                    schedule_date,
                } = schedule;

                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    startHours,
                    startMinute,
                    0,
                );
                const endDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    endHours,
                    endMinute,
                    0,
                );
                schedule.start_date = startDateAndTime;
                schedule.end_date = endDateAndTime;
                return schedule;
            });
            courseSession.absentCount = 0;
            courseSession.ondutyCount = 0;
            courseSession.permissionCount = 0;
            courseSession.leaveCount = 0;
            courseSession.warningCount = 0;
            courseSession.presentCount = 0;
            courseSession.totalSchedules = 0;
            schedules.forEach((cSchedule) => {
                if (
                    cSchedule.session &&
                    cSchedule.session._session_id &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students &&
                    cSchedule.students.length
                ) {
                    const students = cSchedule.students.reduce(
                        (scheduleStudents, currentStudent) => {
                            const uniqueStudent = scheduleStudents.find(
                                (scheduleStudent) =>
                                    scheduleStudent._id &&
                                    scheduleStudent._id.toString() ===
                                        currentStudent._id.toString(),
                            );
                            if (!uniqueStudent) {
                                return scheduleStudents.concat([currentStudent]);
                            }
                            return scheduleStudents;
                        },
                        [],
                    );

                    students.forEach((student) => {
                        if (cs(student._id) === cs(userId)) {
                            let lateLabel = null;
                            if (!lateExclude && !lateExcludeForStudent) {
                                const { lateLabel: retrievedLateLabel } = getLateLabelForSchedule({
                                    lateDurationRange,
                                    manualLateRange,
                                    manualLateData,
                                    schedule: cSchedule,
                                    student_data: student,
                                    _institution_calendar_id,
                                    programId: _program_id,
                                    courseId: _course_id,
                                    yearNo: year_no,
                                    levelNo: level_no,
                                    term,
                                    rotationCount: rotation_count,
                                    lateExcludeManagement,
                                });
                                lateLabel = retrievedLateLabel; // Assign the retrieved lateLabel value
                            }
                            courseSession.lateLabel = lateLabel ? lateLabel.lateLabel : null;
                            if (student.status === 'on_duty') {
                                courseSession.ondutyCount++;
                                if (cSchedule.merge_status) {
                                    courseSession.ondutyCount += cSchedule.merge_with.length;
                                }
                            }
                            if (student.status === 'permission') {
                                courseSession.permissionCount++;
                                if (cSchedule.merge_status) {
                                    courseSession.permissionCount += cSchedule.merge_with.length;
                                }
                            }
                            if (student.status === 'absent' || student.status === 'pending') {
                                courseSession.absentCount++;
                                if (cSchedule.merge_status) {
                                    courseSession.absentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (
                                student.status === 'present' /* ||
                                    student.status === 'on_duty' ||
                                    student.status === 'permission' */ &&
                                cSchedule.isActive
                            ) {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (student.status === 'leave') courseSession.leaveCount++;
                            if (student.status === 'leave' || student.status === 'absent') {
                                courseSession.warningCount++; //To Do
                                if (cSchedule.merge_status) {
                                    courseSession.warningCount += cSchedule.merge_with.length;
                                }
                            }
                        }
                    });

                    cSchedule.staffs.forEach((staff) => {
                        if (cSchedule.status === COMPLETED && cs(staff._staff_id) === cs(userId)) {
                            if (staff.status === 'absent' || staff.status === 'pending')
                                courseSession.absentCount++;
                            if (
                                staff.status === 'present' /* ||
                                    staff.status === 'on_duty' ||
                                    staff.status === 'permission' */ &&
                                cSchedule.isActive
                            ) {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (staff.status === 'leave') courseSession.leaveCount++;
                            if (staff.status === 'leave' || staff.status === 'absent')
                                courseSession.warningCount++;
                        }
                    });
                    if (cSchedule.isActive === true) courseSession.totalSchedules++;
                }
                cSchedule.student_groups = cSchedule.student_groups.map((studentGroup) => {
                    studentGroup.students = [];
                    return studentGroup;
                });
                if (!getSchedule) {
                    cSchedule.studentsCount = cSchedule.students.filter(
                        (studentELement) => studentELement.status !== EXCLUDE,
                    ).length;
                    const studentData = cSchedule.students.find(
                        (studentElement) => studentElement._id.toString() === userId.toString(),
                    );
                    cSchedule.students = studentData
                        ? [
                              {
                                  _id: studentData._id,
                                  status: studentData.status,
                                  isRestricted: studentData.isRestricted,
                                  reasonForLate: studentData.reasonForLate,
                              },
                          ]
                        : [];
                }
            });
            warningCount += courseSession.warningCount;
            presentCount += courseSession.presentCount;
            leaveCount += courseSession.leaveCount;
            absentCount += courseSession.absentCount;
            ondutyCount += courseSession.ondutyCount;
            permissionCount += courseSession.permissionCount;
            courseSession.schedules = schedules;
        });

        const sessionsCount = courseSessions.filter(
            (courseSession) =>
                courseSession._session_id &&
                courseSession.schedules.find((schedule) => schedule.isActive),
        );
        sessionsCount.forEach((session) => {
            totalSessions += session.schedules.length;
        });
        //form adding merge sessions count
        const mergedSessions = courseSessions.filter((courseSession) => courseSession.merge_status);
        if (mergedSessions) {
            for (mergedSessionsEntry of mergedSessions) {
                totalSessions += mergedSessionsEntry.merge_with.length;
            }
        }
        const groupNames = [];

        for (const courseSession of courseSessions) {
            if (courseSession.student_groups) {
                for (const studentGroup of courseSession.student_groups) {
                    if (
                        studentGroup.group_name &&
                        !groupNames.find((groupName) => groupName === studentGroup.group_name)
                    ) {
                        groupNames.push(studentGroup.group_name);
                    }
                }
            }
        }

        const uniqueSessionTypes = [
            ...new Set(
                courseSessions
                    .map((type) => {
                        if (type._session_id) return type.session_type;
                    })
                    .filter((item) => item),
            ),
        ];
        const sessDetails = courseSessions.map((courseSession) => {
            const groupNamesArray = [];

            if (courseSession.student_groups) {
                courseSession.student_groups.forEach((groupName) => {
                    groupNamesArray.push(groupName.group_name);
                });
            }
            courseSession.groupNames = groupNamesArray;
            return courseSession;
        });
        const courseSessionDetails = groupNames.map((groupName) => {
            const totalGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );
            const completedGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === COMPLETED &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const pendingGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === PENDING &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const courseSessions = uniqueSessionTypes.map((uniqueSessionType) => {
                const completedCount = completedGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === COMPLETED &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                const pendingCount = pendingGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === PENDING &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;
                const totalCount = totalGroup.filter(
                    (sessionData) =>
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                return {
                    deliveryName: uniqueSessionType,
                    totalCount,
                    completedCount,
                    pendingCount,
                };
            });
            const totalGroups = totalGroup.length;
            return {
                studentGroupName: groupName.split('-').splice(-2).join('-'),
                totalSessions: totalGroups,
                completedSessions: completedGroup.length,
                pendingSessions: pendingGroup.length,
                courseSessions,
            };
        });
        sessionsCount.forEach((session) => {
            const sessionCompleted = session.schedules.filter((courseSchedule) => {
                if (courseSchedule.status === COMPLETED) {
                    //we didn't get student array while getting data for staff side
                    if (courseSchedule.students.length) {
                        return courseSchedule.students.some(
                            (studentElement) =>
                                studentElement._id.toString() === userId.toString() &&
                                studentElement.status !== EXCLUDE,
                        );
                    }
                    return true;
                }
                return false;
            });
            if (sessionCompleted.length && courseSchedules.length) {
                completedSessions += sessionCompleted.length;
            }
            const mergedCompletedSessions = sessionCompleted.filter(
                (courseSession) => courseSession.merge_status,
            );
            if (mergedCompletedSessions) {
                for (mergedCompletedSessionsEntry of mergedCompletedSessions) {
                    completedSessions += mergedCompletedSessionsEntry.merge_with.length;
                }
            }
        });
        const totalScheduleCount = courseSchedulesEntry.filter(
            (scheduleElement) =>
                scheduleElement.isActive &&
                !scheduleElement.isDeleted &&
                scheduleElement.type === REGULAR &&
                scheduleElement.students.some(
                    (studentElement) =>
                        studentElement._id.toString() === userId.toString() &&
                        studentElement.status !== EXCLUDE,
                ),
        ).length;
        const attendedSessions = getStudentAttendedSession(courseSchedulesEntry, userId);
        const absentSessions = getStudentAbsentSession(courseSchedulesEntry, userId);
        let studentLateAbsent = 0;
        let lateConfig = [];
        if (!lateExclude && !lateExcludeForStudent) {
            const { studentLateAbsent: updateStudentLateAbsent, lateConfig: updateLateConfig } =
                getLateConfigAndStudentLateAbsent({
                    lateDurationRange,
                    manualLateRange,
                    manualLateData,
                    scheduleData: courseSchedulesEntry.filter(
                        (scheduleElement) => scheduleElement && scheduleElement.type === REGULAR,
                    ),
                    studentElement: { _student_id: userId },
                    lateExcludeManagement,
                });
            studentLateAbsent = updateStudentLateAbsent;
            lateConfig = updateLateConfig;
        }
        const denialPercentage = ((absentSessions + studentLateAbsent) / totalScheduleCount) * 100;
        const studentWarningAbsence = clone(lmsData.warningAbsenceData);
        if (studentCriteriaData.length) {
            const studentManipulation = studentCriteriaData.find(
                (absenceElement) =>
                    (absenceElement.studentId &&
                        absenceElement.studentId.toString() === userId.toString()) ||
                    absenceElement.typeWiseUpdate === COURSE_WISE,
            );
            userId.manipulationStatus = false;
            userId.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].percentage &&
                studentWarningAbsence[0].percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                userId.manipulationStatus = true;
                userId.manipulationPercentage = studentManipulation.absencePercentage;
            }
        }
        const absentPercentage = ((warningCount + studentLateAbsent) / totalScheduleCount) * 100;
        return {
            courseSessionDetails,
            warningCount,
            presentCount,
            ondutyCount,
            permissionCount,
            totalSessions: totalScheduleCount,
            completedSessions,
            courseSessions,
            attendedSessions,
            leaveCount,
            absentCount,
            absentPercentage,
            lateConfig,
            studentLateAbsent,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getAllCourses = async (staffId, coordinators, institutionCalendarId, _institution_id) => {
    try {
        const { courseIds, courseSchedule, courses, year, level, rotation, term, rotationCount } =
            await getCourseIdsWithSessions(staffId, '', '', institutionCalendarId);
        const feedBackData = await getRatingByCourses(
            institutionCalendarId,
            courseIds,
            staffId,
            year,
            level,
            term,
            rotation,
            rotationCount,
        );
        const courseIdsResults = courses.map((courseId) => {
            return courseId._course_id;
        });
        let lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
        });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId: courseIds,
            yearNo: year,
            levelNo: level,
            term,
            rotation,
            rotationCount,
        });
        const studentCriteriaData = await lmsDenialSchema
            .find({
                courseId: {
                    $in: courseIds.map((courseIdElement) =>
                        convertToMongoObjectId(courseIdElement),
                    ),
                },
                levelNo: level,
                yearNo: year,
                rotation,
                ...(rotation === 'yes' && { rotationCount }),
                term,
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 })
            .lean();
        const lmsClonedData = clone(lmsData);
        const sgQuery = {
            isDeleted: false,
            _institution_calendar_id: institutionCalendarId,
            'groups.courses._course_id': { $in: courseIds },
        };
        const studentGroups = await studentGroup.find(sgQuery).lean();
        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIdsResults },
            },
            {
                _id: 1,
                course_type: 1,
                'coordinators._user_id': 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );
        const adminChkcourses = [];
        if (coordinators === 'true') {
            for (adminCourses of courseTypeResult) {
                const userData = [];
                if (adminCourses.coordinators) {
                    for (courseAdmin of adminCourses.coordinators) {
                        const user = await User.findOne(
                            { _id: courseAdmin._user_id },
                            { name: 1, email: 1 },
                        );
                        userData.push({ name: user.name, email: user.email });
                    }
                }
                adminChkcourses.push({ _course_id: adminCourses._id, coordinatorData: userData });
            }
        }
        const redisKeysForWarnings = new Map();
        let coursesList = courses.filter(
            ({
                _institution_calendar_id,
                _program_id,
                _course_id,
                year_no,
                level_no,
                term,
                rotation_count,
            }) => {
                redisKeysForWarnings.set(
                    `${
                        constant.STUDENT_WARNING_REDIS
                    }:${_institution_calendar_id.toString()}_${_program_id.toString()}_${_course_id.toString()}_${year_no}_${level_no}_${term}_${
                        rotation_count || null
                    }`,
                );
                return true;
            },
        );
        const allCourseWarnings = await multiCourseWarningsForStudent({
            keysMap: redisKeysForWarnings,
            warningConfig: lmsData.warningAbsenceData,
            userId: staffId,
        });
        coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                level_no,
                year_no,
                term: courseTerm,
                isActive,
                subjects,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;
            courseAdminNames = adminChkcourses.find(
                (adminCourses) => adminCourses._course_id.toString() === courseId.toString(),
            );
            for (const student of studentGroups) {
                for (const groups of student.groups) {
                    if (groups.level === level_no) {
                        for (const course of groups.courses) {
                            if (
                                lmsData.warningAbsenceData[0] &&
                                course.student_absence_percentage &&
                                course.student_absence_percentage !== 0
                            ) {
                                lmsData.warningAbsenceData[0].percentage =
                                    course.student_absence_percentage;
                            } else {
                                lmsData = lmsClonedData;
                            }
                        }
                    }
                }
            }
            const result = getSessions(
                lmsData,
                studentCriteriaData,
                courseSchedule,
                courseId,
                _institution_calendar_id,
                _program_id,
                year_no,
                level_no,
                courseTerm,
                staffId,
                rotation,
                rotation_count,
                lateDurationRange,
                manualLateRange,
                manualLateData,
                lateExcludeManagement,
            );
            const {
                courseSessions,
                courseSessionDetails,
                totalSessions,
                completedSessions,
                attendedSessions,
                presentCount,
                warningCount,
                leaveCount,
                absentCount,
                absentPercentage,
                studentLateAbsent,
            } = result;
            const { labelName, restrictCourseAccess } =
                allCourseWarnings.get(
                    `${
                        constant.STUDENT_WARNING_REDIS
                    }:${course._institution_calendar_id.toString()}_${course._program_id.toString()}_${course._id.toString()}_${
                        course.year
                    }_${course.level}_${course.term}_${
                        course.rotation_count ? course.rotation_count : null
                    }`,
                ) ?? {};
            course.warningData = labelName;
            course.restrictCourseAccess = restrictCourseAccess;
            let feedback;
            let rotationCount;
            if (!course.rotation_count) {
                rotationCount = '';
            } else {
                rotationCount = course.rotation_count;
            }

            const feedBacks = rotationCount
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.term === courseTerm &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          feedBackDetail.rotationCount === rotationCount,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.term === courseTerm &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) feedback = feedBacks;

            let course_type;
            let versionNo;
            let versioned;
            let versionName;
            let versionedFrom;
            let versionedCourseIds;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) {
                course_type = courseTypes.course_type;
                versionNo = courseTypes.versionNo || 1;
                versioned = courseTypes.versioned || false;
                versionName = courseTypes.versionName || '';
                versionedFrom = courseTypes.versionedFrom || null;
                versionedCourseIds = courseTypes.versionedCourseIds || [];
            }

            return {
                _id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                courseAdminNames,
                courseSessionDetails,
                totalSessions,
                completedSessions,
                attendedSessions,
                leaveCount,
                absentCount,
                warningCount,
                // warningData,
                // restrictCourseAccess,
                presentCount,
                absentPercentage,
                subjects,
                year: year_no,
                level: level_no,
                term: courseTerm,
                isActive,
                feedback,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
                versionNo,
                versioned,
                versionName,
                versionedFrom,
                versionedCourseIds,
            };
        });
        return coursesList;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourse = async (
    staffId,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    courseAdmin,
    type,
    getSchedule,
    _institution_id,
) => {
    try {
        let result = await getCourseIdsWithSessions(
            staffId,
            type,
            _course_id,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            courseAdmin,
        );
        const { courseSchedule, courses } = result;
        let lmsData = await lmsNewSetting({ _institution_id, _institution_calendar_id });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            programId: _program_id,
            courseId: _course_id,
            yearNo: year_no,
            levelNo: level_no,
            term,
            rotation,
            rotationCount: rotation_count,
        });
        const studentCriteriaData = await lmsDenialSchema
            .find({
                courseId: convertToMongoObjectId(_course_id),
                _institution_calendar_id,
                levelNo: level_no,
                yearNo: year_no,
                programId: convertToMongoObjectId(_program_id),
                term,
                rotation,
                ...(rotation === 'yes' && { rotationCount: parseInt(rotation_count) }),
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 })
            .lean();
        const lmsClonedData = clone(lmsData);
        const sgQuery = {
            isDeleted: false,
            _institution_calendar_id,
            'groups.courses._course_id': _course_id,
        };
        const studentGroups = await studentGroup.find(sgQuery).lean();
        const courseIds = courses.map((courseId) => {
            return courseId._course_id;
        });
        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIds },
            },
            {
                _id: 1,
                course_type: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );

        let allScheduleIds = courseSchedule.map((schedule) => {
            return schedule.session
                ? schedule.session._session_id.toString()
                : schedule._id.toString();
        });
        allScheduleIds = [...new Set(allScheduleIds)];
        allScheduleIds = allScheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));

        // If Multi Device Mode Enabled mean have to check other Device Attendance
        let scheduleDeviceData;
        if (FACE_AUTH_TYPE === BOTH) {
            console.log('Inside Both Multi Device Flow');
            const scheduleDeviceQuery = {
                scheduleId: { $in: allScheduleIds },
                status: RUNNING,
                isDeleted: false,
                isActive: true,
            };
            const scheduleDeviceProject = { scheduleId: 1, studentIds: 1 };
            console.time('scheduleDeviceData');
            scheduleDeviceData = await ScheduleMultiDeviceAttendanceSchema.find(
                scheduleDeviceQuery,
                scheduleDeviceProject,
            ).lean();
            console.timeEnd('scheduleDeviceData');
        }
        // todo work for single get schedule add document ,activities count changes
        // let activities = [];
        // let documents = [];
        // if (getSchedule) {
        //     const activityQuery = {
        //         $or: [
        //             { scheduleIds: { $in: allScheduleIds } },
        //             { 'sessionFlowIds._id': { $in: allScheduleIds } },
        //         ],
        //         isDeleted: false,
        //         _program_id: convertToMongoObjectId(_program_id),
        //         year_no,
        //         level_no,
        //         term,
        //         rotation,
        //         _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        //     };
        //     if (rotation_count) {
        //         activityQuery.rotation_count = rotation_count;
        //     }
        //     activities = (
        //         await getJSON(Activity, activityQuery, {
        //             sessionFlowIds: 1,
        //             scheduleIds: 1,
        //             _program_id: 1,
        //             courseId: 1,
        //             year_no: 1,
        //             level_no: 1,
        //             term: 1,
        //             rotation_count: 1,
        //             rotation: 1,
        //             _institution_calendar_id: 1,
        //         })
        //     ).data;
        //     documents = (
        //         await getJSON(
        //             Document,
        //             {
        //                 'sessionOrScheduleIds._id': { $in: allScheduleIds },
        //                 isDeleted: false,
        //                 isActive: true,
        //             },
        //             {},
        //         )
        //     ).data;
        // }
        const feedBackData = await getRatingByCourses(
            _institution_calendar_id,
            _course_id,
            staffId,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
        );
        const redisKeysForWarnings = new Map();
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id: calendarId,
                _program_id: programId,
                program_name,
                course_name,
                course_code,
                level_no: levelNo,
                year_no: yearNo,
                term: courseTerm,
                isActive,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;
            redisKeysForWarnings.set(
                `${
                    constant.STUDENT_WARNING_REDIS
                }:${calendarId.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
                    rotation_count || null
                }`,
            );
            for (const student of studentGroups) {
                for (const groups of student.groups) {
                    if (groups.level === level_no) {
                        for (const course of groups.courses) {
                            if (
                                lmsData.warningAbsenceData[0] &&
                                course.student_absence_percentage &&
                                course.student_absence_percentage !== 0
                            ) {
                                lmsData.warningAbsenceData[0].percentage =
                                    course.student_absence_percentage;
                            } else {
                                lmsData = lmsClonedData;
                            }
                        }
                    }
                }
            }
            result = getSessions(
                lmsData,
                studentCriteriaData,
                courseSchedule,
                courseId,
                calendarId,
                programId,
                yearNo,
                levelNo,
                courseTerm,
                staffId,
                rotation,
                rotation_count,
                getSchedule,
                lateDurationRange,
                manualLateRange,
                manualLateData,
                lateExcludeManagement,
            );
            const {
                courseSessionDetails,
                absentCount,
                leaveCount,
                warningCount,
                presentCount,
                totalSessions,
                completedSessions,
                courseSessions,
                attendedSessions,
                absentPercentage,
                lateConfig,
                studentLateAbsent,
                ondutyCount,
                permissionCount,
            } = result;
            let feedback;
            const feedBacks = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._course_id.toString() === courseId.toString() &&
                    feedBackDetail.level_no === level_no &&
                    feedBackDetail.term === courseTerm &&
                    feedBackDetail.year_no === year_no &&
                    feedBackDetail.rotation === rotation &&
                    rotation_count &&
                    feedBackDetail.rotationCount === rotation_count,
            );
            if (feedBacks) feedback = feedBacks;
            let course_type;
            let versionNo;
            let versioned;
            let versionName;
            let versionedFrom;
            let versionedCourseIds;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) {
                course_type = courseTypes.course_type;
                versionNo = courseTypes.versionNo || 1;
                versioned = courseTypes.versioned || false;
                versionName = courseTypes.versionName || '';
                versionedFrom = courseTypes.versionedFrom || null;
                versionedCourseIds = courseTypes.versionedCourseIds || [];
            }
            return {
                _id: courseId,
                _program_id: programId,
                courseSessionDetails,
                warningCount,
                // warningData,
                // restrictCourseAccess,
                presentCount,
                absentCount,
                leaveCount,
                ondutyCount,
                permissionCount,
                program_name,
                course_name,
                course_code,
                totalSessions,
                completedSessions,
                attendedSessions,
                absentPercentage,
                year: yearNo,
                level: levelNo,
                term: courseTerm,
                isActive,
                feedback,
                sessions: courseSessions,
                _institution_calendar_id: calendarId,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
                versionNo,
                versioned,
                versionName,
                versionedFrom,
                versionedCourseIds,
                lateConfig,
                studentLateAbsent,
            };
        });
        const allCourseWarnings =
            (await multiCourseWarningsForStudent({
                keysMap: redisKeysForWarnings,
                warningConfig: lmsData.warningAbsenceData,
                userId: staffId,
            })) ?? new Map();

        for (const course of coursesList) {
            const { labelName, restrictCourseAccess } =
                allCourseWarnings.get(
                    `${
                        constant.STUDENT_WARNING_REDIS
                    }:${course._institution_calendar_id.toString()}_${course._program_id.toString()}_${course._id.toString()}_${
                        course.year
                    }_${course.level}_${course.term}_${
                        course.rotation_count ? course.rotation_count : null
                    }`,
                ) ?? {};
            course.warningData = labelName;
            course.restrictCourseAccess = restrictCourseAccess;
            for (const session of course.sessions) {
                const { schedules } = session;
                //// todo count changes
                // if (getSchedule) {
                //     session.documentCount = 0;
                //     session.activityCount = 0;
                // }
                for (const schedule of schedules) {
                    const {
                        _id,
                        _program_id,
                        _course_id: courseId,
                        year_no,
                        level_no,
                        term,
                        rotation_count,
                        rotation,
                        _institution_calendar_id,
                    } = schedule;
                    /// todo count changes
                    // if (getSchedule) {
                    //     schedule.documentCount = 0;
                    //     schedule.activityCount = 0;
                    //     let docs;
                    //     let filteredActivities;
                    //     if (schedule.session && schedule.session._session_id) {
                    //         docs = documents.filter((doc) =>
                    //             doc.sessionOrScheduleIds
                    //                 .map((id) => id._id.toString())
                    //                 .includes(schedule.session._session_id.toString()),
                    //         );
                    //         schedule.documentCount = docs.length;
                    //         filteredActivities = activities.filter((activity) => {
                    //             if (
                    //                 activity._program_id.toString() === _program_id.toString() &&
                    //                 activity.courseId.toString() === courseId.toString() &&
                    //                 activity.year_no === year_no &&
                    //                 activity.level_no === level_no &&
                    //                 activity.term === term &&
                    //                 activity.rotation === rotation &&
                    //                 activity._institution_calendar_id.toString() ===
                    //                     _institution_calendar_id.toString() &&
                    //                 // activity.sessionFlowIds.length &&
                    //                 activity.sessionFlowIds.find(
                    //                     (sessionFlowIdElement) =>
                    //                         sessionFlowIdElement._id.toString() ===
                    //                         schedule.session._session_id.toString(),
                    //                 )
                    //             ) {
                    //                 return true;
                    //             }
                    //             return false;
                    //         });
                    //         schedule.activityCount = filteredActivities.length;
                    //     } else {
                    //         docs = documents.filter((doc) =>
                    //             doc.sessionOrScheduleIds
                    //                 .map((id) => id._id.toString())
                    //                 .includes(schedule._id.toString()),
                    //         );
                    //         schedule.documentCount += docs.length;
                    //         filteredActivities = activities.filter(
                    //             (activity) =>
                    //                 activity.scheduleIds &&
                    //                 // activity.scheduleIds.length &&
                    //                 activity.scheduleIds.find(
                    //                     (scheduleElement) =>
                    //                         scheduleElement.toString() === schedule._id.toString(),
                    //                 ),
                    //         );
                    //         schedule.activityCount += filteredActivities.length;
                    //     }
                    //     session.documentCount = docs.length;
                    //     session.activityCount = filteredActivities.length;
                    // }
                    // student duplicate removed
                    let students = schedule.students;
                    students = students.reduce((acc, current) => {
                        const x = acc.find(
                            (item) => item._id && item._id.toString() === current._id.toString(),
                        );
                        if (!x) {
                            return acc.concat([current]);
                        }
                        return acc;
                    }, []);

                    // If Multi Device Mode Enabled mean have to check other Device Attendance
                    if (FACE_AUTH_TYPE === BOTH && scheduleDeviceData) {
                        const scheduleWiseDeviceData = scheduleDeviceData.find(
                            (scheduleDeviceElement) =>
                                scheduleDeviceElement.scheduleId.toString() === _id.toString(),
                        );
                        if (scheduleWiseDeviceData && scheduleWiseDeviceData.studentIds) {
                            for (studentIdElement of scheduleWiseDeviceData.studentIds) {
                                const scheduleStudentIndex = students.findIndex(
                                    (scheduleStudentElement) =>
                                        scheduleStudentElement._id.toString() ===
                                            studentIdElement.toString() &&
                                        scheduleStudentElement.status === PENDING,
                                );
                                if (scheduleStudentIndex !== -1) {
                                    students[scheduleStudentIndex].status = PRESENT;
                                    // students[scheduleStudentIndex].mode = 'auto';
                                }
                            }
                        }
                    }
                    schedule.students = students;
                }
            }
            const feedBacks = rotation_count
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.term === term &&
                          feedBackDetail.rotation === rotation &&
                          rotation_count &&
                          feedBackDetail.rotationCount === rotation_count,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.term === term &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) course.feedback = feedBacks;
        }
        return coursesList;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseDocuments = async (courseId, sessionId) => {
    try {
        const dQuery = { ...query };
        if (courseId) dQuery._course_id = convertToMongoObjectId(courseId);
        if (sessionId) dQuery._session_flow_id = convertToMongoObjectId(sessionId);
        const dProject = {
            type: 1,
            name: 1,
            url: 1,
            _course_id: 1,
            _session_flow_id: 1,
            shared: 1,
        };
        return (await getJSON(Document, dQuery, dProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getScheduleById = async (scheduleId) => {
    try {
        csQuery = { ...query, _id: convertToMongoObjectId(scheduleId) };
        csProject = {};
        return (await getJSON(CourseSchedule, csQuery, csProject)).data;
    } catch (error) {
        throw new Error(error);
    }
};

// get question
const formatQuestions = async (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentCorrectAnswered = 0;
    for (const activityQuestion of activityQuestions) {
        const { _id, options } = activityQuestion;
        if (options && options.length > 0) {
            for (const option of options) {
                const { _id: optionId, answer: optionAnswer } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = optionAnswer;
                    studentAnsweredOptionId = optionId;
                }
            }
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }
    }
    return {
        questions,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

const getScheduleByDate = async (userId, date, timeZone, _institution_id) => {
    try {
        const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        csQuery = {
            $or: [
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
                { 'attendanceTakingStaff.staffId': convertToMongoObjectId(userId) },
                { 'students._id': convertToMongoObjectId(userId) },
            ],
            ...query,
        };
        const timeZonedStartDate = dateTimeBasedConverter(date, timeZone);
        const timeZonedBeginDate = timeZonedStartDate.clone().startOf('day');
        const timeZonedEndDate = timeZonedStartDate.clone().endOf('day');
        csQuery.scheduleStartDateAndTime = {
            $gte: new Date(timeZonedBeginDate),
            $lte: new Date(timeZonedEndDate),
        };
        // const currentDate = new Date(date);
        const start = new Date(timeZonedStartDate);
        const startTimestamp = new Date(timeZonedStartDate).getTime();
        // const currentDateEnd = new Date(date);
        // currentDateEnd.setHours(23, 59, 59, 59);
        // csQuery.scheduleStartDateAndTime = { $gte: currentDate, $lte: currentDateEnd };
        // csQuery.schedule_date = new Date(start);
        csProject = {
            isDeleted: 0,
            createdAt: 0,
            updatedAt: 0,
        };
        csQuery._institution_calendar_id = { $in: institutionCalendarIds };
        let courseSchedules = await CourseSchedule.find(csQuery, csProject)
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .populate({
                path: '_course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        // if merged session exists
        courseSchedules = courseSchedules.map((courseSchedule) => {
            const {
                start: { hour: startHour, minute: startMinute, format: startFormat },
                end: { hour: endHour, minute: endMinute, format: endFormat },
                schedule_date,
                _infra_id,
            } = courseSchedule;

            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                courseSchedule.infra_name = infraName;
            }

            if (
                !courseSchedule.scheduleStartDateAndTime &&
                !courseSchedule.scheduleEndDateAndTime
            ) {
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertingRiyadhToUTC(
                    schedule_date,
                    startHours,
                    startMinute,
                );
                const endDateAndTime = convertingRiyadhToUTC(schedule_date, endHours, endMinute);
                courseSchedule.scheduleStartDateAndTime = startDateAndTime;
                courseSchedule.scheduleEndDateAndTime = endDateAndTime;
            }

            if (courseSchedule.merge_status) {
                const mergedSessions = courseSchedule.merge_with.map((mergeWith) => {
                    const sessionDetails = courseSchedules.find(
                        (courseScheduleEntry) =>
                            courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString() &&
                            courseScheduleEntry.session &&
                            courseScheduleEntry.session._session_id.toString() ===
                                mergeWith.session_id.toString(),
                    );
                    if (sessionDetails) {
                        mergeWith.session = {
                            _session_id: sessionDetails.session._session_id,
                            s_no: sessionDetails.session.s_no,
                            delivery_symbol: sessionDetails.session.delivery_symbol,
                            delivery_no: sessionDetails.session.delivery_no,
                        };
                    }
                    return mergeWith;
                });
                courseSchedule.merge_with = mergedSessions;
            }
            courseSchedule.versionNo = courseSchedule._course_id.versionNo || 1;
            courseSchedule.versioned = courseSchedule._course_id.versioned || false;
            courseSchedule.versionName = courseSchedule._course_id.versionName || '';
            courseSchedule.versionedFrom = courseSchedule._course_id.versionedFrom || null;
            courseSchedule.versionedCourseIds = courseSchedule._course_id.versionedCourseIds || [];
            courseSchedule._course_id = courseSchedule._course_id._id;
            return courseSchedule;
        });

        // session duplicate removed
        const mergedCourseSchedules = [];
        courseSchedules.forEach((courseSchedule) => {
            const { merge_status, merge_with, student_groups } = courseSchedule;
            if (merge_status) {
                const mergedScheduleIds = merge_with.map((mergedWith) => mergedWith.schedule_id);
                const duplicateSessions = mergedScheduleIds.filter((mergedScheduleId) =>
                    mergedCourseSchedules.find(
                        (mergedCourseSchedule) =>
                            mergedCourseSchedule._id.toString() === mergedScheduleId.toString(),
                    ),
                );
                if (duplicateSessions.length === 0) {
                    const mergedSessionSchedules = courseSchedules.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    mergedCourseSchedules.push(courseSchedule);
                }
            } else {
                mergedCourseSchedules.push(courseSchedule);
            }
        });
        courseSchedules = mergedCourseSchedules;
        const programId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._program_id)),
        ][0];
        const courseId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._course_id._id)),
        ][0];
        const institutionCalenderId = [
            ...new Set(
                courseSchedules.map((courseSchedule) => courseSchedule._institution_calendar_id),
            ),
        ][0];
        const institutionId = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule._institution_id)),
        ][0];
        const term = [...new Set(courseSchedules.map((courseSchedule) => courseSchedule.term))][0];
        const levelNo = [
            ...new Set(courseSchedules.map((courseSchedule) => courseSchedule.level_no)),
        ][0];
        const { status: programCalendarStatus, data: programCalendars } = await get_list(
            ProgramCalendar,
            {
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: { $in: institutionCalendarIds },
                'level.term': term,
                'level.level_no': levelNo,
            },
            {},
        );
        if (programCalendarStatus && programCalendars && programCalendars.length) {
            for (const programCalendarELement of programCalendars) {
                const programCalenderLevels = programCalendarELement.level.find(
                    (programCalendar) =>
                        programCalendar.level_no === levelNo && programCalendar.term === term,
                );
                if (programCalenderLevels) {
                    let programCalenderCourseEvents = programCalenderLevels.course.find(
                        (courseEntry) => courseEntry._course_id.toString() === courseId.toString(),
                    );
                    if (programCalenderCourseEvents && programCalenderCourseEvents.courses_events) {
                        programCalenderCourseEvents = programCalenderCourseEvents.courses_events;
                    } else {
                        programCalenderCourseEvents = [];
                    }
                    programCalenderCourseEvents = programCalenderCourseEvents.filter(
                        (programCalenderCourseEvent) =>
                            new Date(programCalenderCourseEvent.event_date).getTime() ===
                            startTimestamp,
                    );
                    const programCalenderEvents = programCalenderLevels.events.filter(
                        (programCalenderLevel) =>
                            new Date(programCalenderLevel.event_date).getTime() === startTimestamp,
                    );
                    if (programCalenderCourseEvents.length) {
                        programCalenderCourseEvents = programCalenderCourseEvents.map(
                            (programCalenderEvent) => {
                                let startDate = programCalenderEvent.start_time.setHours(
                                    programCalenderEvent.start_time.getHours() + 5,
                                );
                                startDate += 30 * 60000;
                                let endDate = programCalenderEvent.end_time.setHours(
                                    programCalenderEvent.end_time.getHours() + 5,
                                );
                                endDate += 30 * 60000;
                                return {
                                    _id: programCalenderEvent._id,
                                    _event_id: programCalenderEvent._event_id,
                                    event_type: programCalenderEvent.event_type,
                                    event_name: programCalenderEvent.event_name,
                                    start_date: new Date(startDate),
                                    end_date: new Date(endDate),
                                    type: 'calender_event',
                                };
                            },
                        );
                        courseSchedules = courseSchedules.concat(programCalenderCourseEvents);
                        courseSchedules = courseSchedules.reduce((acc, current) => {
                            const x = acc.find(
                                (item) =>
                                    item._event_id &&
                                    item._event_id.toString() === current._event_id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                    }
                    if (programCalenderEvents.length) {
                        programCalenderEvent = programCalenderEvents.map((programCalenderEvent) => {
                            let startDate = programCalenderEvent.start_time.setHours(
                                programCalenderEvent.start_time.getHours() + 5,
                            );
                            startDate += 30 * 60000;
                            let endDate = programCalenderEvent.end_time.setHours(
                                programCalenderEvent.end_time.getHours() + 5,
                            );
                            endDate += 30 * 60000;
                            return {
                                _id: programCalenderEvent._id,
                                _event_id: programCalenderEvent._event_id,
                                event_type: programCalenderEvent.event_type,
                                event_name: programCalenderEvent.event_name,
                                start_date: new Date(startDate),
                                end_date: new Date(endDate),
                                type: 'calender_event',
                            };
                        });
                        courseSchedules = courseSchedules.concat(programCalenderEvents);
                        courseSchedules = courseSchedules.reduce((acc, current) => {
                            const x = acc.find(
                                (item) =>
                                    item._event_id &&
                                    item._event_id.toString() === current._event_id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                    }
                }
            }
        }
        //Remote/Extra Schedule Get
        const { status: courseScheduleSettingStatus, data: courseScheduleSettings } = await get(
            CourseScheduleSetting,
            {
                _institution_id: convertToMongoObjectId(institutionId),
                isActive: true,
                isDeleted: false,
            },
            {},
        );

        if (courseScheduleSettingStatus && courseScheduleSettings) {
            const programEvents = courseScheduleSettings.programs.find(
                (program) => program._program_id.toString() === programId.toString(),
            );
            const extraCurricularAndBreakTiming = programEvents.extraCurricularAndBreakTiming;
            let breakTimings = extraCurricularAndBreakTiming.filter((breakTiming) => {
                const { days, isDeleted } = breakTiming;
                const constantDays = Object.values(DAYS);
                let currentDay = new Date(start).getDay();
                currentDay = constantDays[currentDay];
                if (!isDeleted && days.includes(currentDay)) {
                    return true;
                }
                return false;
            });

            if (breakTimings.length) {
                breakTimings = breakTimings.map((breakTiming) => {
                    const {
                        startTime: { hour: startHour, minute: startMinute, format: startFormat },
                        endTime: { hour: endHour, minute: endMinute, format: endFormat },
                    } = breakTiming;

                    const startHours =
                        startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                    const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                    const startDateAndTime = new Date(
                        convertToUtcTimeFormat(start, startHours, startMinute, 0),
                    );
                    const endDateAndTime = new Date(
                        convertToUtcTimeFormat(start, endHours, endMinute, 0),
                    );
                    return {
                        days: breakTiming.days,
                        startTime: breakTiming.startTime,
                        endTime: breakTiming.endTime,
                        allowCourseCoordinatesToEdit: breakTiming.allowCourseCoordinatesToEdit,
                        isDeleted: breakTiming.isDeleted,
                        _id: breakTiming._id,
                        _institution_calendar_id: breakTiming._institution_calendar_id,
                        title: breakTiming.title,
                        gender: breakTiming.gender,
                        mode: breakTiming.mode,
                        type: breakTiming.type,
                        sessionType: 'other',
                        start_date: startDateAndTime,
                        end_date: endDateAndTime,
                    };
                });
                courseSchedules = courseSchedules.concat(breakTimings);
            }
        }

        // today activity
        const getCourseInfo = await getCourses(userId, institutionCalendarIds);
        const activityQuery = {
            'schedule.startDateAndTime': {
                $gte: new Date(timeZonedBeginDate),
                $lte: new Date(timeZonedEndDate),
            },
            status: { $ne: DRAFT },
            type: SCHEDULE,
            isDeleted: false,
            _institution_calendar_id: { $in: institutionCalendarIds },
        };
        const end = new Date(timeZonedStartDate).getTime();
        // end = new Date(end).getTime();
        if (getCourseInfo && getCourseInfo.length) {
            if (getCourseInfo.length > 0) {
                const _institution_calendar_id = [
                    ...new Set(
                        getCourseInfo.map((getCourse) => getCourse._institution_calendar_id),
                    ),
                ];
                const _program_id = [
                    ...new Set(getCourseInfo.map((getCourse) => getCourse._program_id)),
                ];

                const term = [...new Set(getCourseInfo.map((getCourse) => getCourse.term))];

                const year_no = [...new Set(getCourseInfo.map((getCourse) => getCourse.year_no))];

                const level_no = [...new Set(getCourseInfo.map((getCourse) => getCourse.level_no))];

                const _course_id = [...new Set(getCourseInfo.map((getCourse) => getCourse._id))];

                const rotation = [...new Set(getCourseInfo.map((getCourse) => getCourse.rotation))];

                const rotation_count = [
                    ...new Set(getCourseInfo.map((getCourse) => getCourse.rotation_count)),
                ];
                activityQuery.$and = [
                    { _institution_calendar_id: { $in: _institution_calendar_id } },
                    { _program_id: { $in: _program_id } },
                    { term: { $in: term } },
                    { year_no: { $in: year_no } },
                    { level_no: { $in: level_no } },
                    { courseId: { $in: _course_id } },
                    { rotation: { $in: rotation } },
                    { rotation_count: { $in: rotation_count } },
                ];
            }

            activityQuery.$or = [
                { createdBy: convertToMongoObjectId(userId) },
                { createdBy: { $ne: convertToMongoObjectId(userId) }, status: { $ne: DRAFT } },
            ];
        }
        let activities = await Activity.find(activityQuery)
            .populate({
                path: 'courseId',
                select: {
                    course_code: 1,
                    course_name: 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        if (activities.length) {
            // Gathering Datas from certain collections
            // Questions gathering
            let activityQuestionIds = [];
            let activitySessionIds = [];
            let activityCourseScheduleIds = [];
            for (const activityElement of activities) {
                activityQuestionIds = [
                    ...activityQuestionIds,
                    ...activityElement.questions.map((question) =>
                        convertToMongoObjectId(question._id),
                    ),
                ];
                activitySessionIds = [
                    ...activitySessionIds,
                    ...activityElement.sessionFlowIds.map((sessionFlowId) =>
                        convertToMongoObjectId(sessionFlowId._id),
                    ),
                ];
                activityCourseScheduleIds = [
                    ...activityCourseScheduleIds,
                    ...activityElement.scheduleIds.map((scheduleId) =>
                        convertToMongoObjectId(scheduleId),
                    ),
                ];
            }
            const activityQuestionDatas = await Question.find({
                _id: { $in: activityQuestionIds },
                isDeleted: false,
            });
            // Course Schedule Data gathering
            const courseScheduleList = await CourseSchedule.find(
                {
                    $or: [
                        {
                            'session._session_id': { $in: activitySessionIds },
                        },
                        {
                            _id: { $in: activityCourseScheduleIds },
                        },
                    ],
                },
                {},
            );
            // Merged Session gathering
            let mergedScheduleIds = [];
            for (scheduleElement of courseScheduleList) {
                if (scheduleElement.merge_status) {
                    mergedScheduleIds = [
                        ...mergedScheduleIds,
                        ...scheduleElement.merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        ),
                    ];
                }
            }
            let mergedCourseScheduleData = [];
            if (mergedScheduleIds.length)
                mergedCourseScheduleData = await CourseSchedule.find({
                    _id: { $in: mergedScheduleIds },
                }).lean();
            // if student exists
            if (getCourseInfo && !getCourseInfo.length) {
                const studentCourseSchedules = courseScheduleList.filter((courseSchedule) =>
                    courseSchedule.students.find(
                        (student) => student._id.toString() === userId.toString(),
                    ),
                );
                const studentCourseScheduleIds = studentCourseSchedules.map(
                    (studentCourseSchedule) => studentCourseSchedule._id.toString(),
                );
                activities = activities.filter((activity) =>
                    activity.scheduleIds.find((scheduleId) =>
                        studentCourseScheduleIds.includes(scheduleId.toString()),
                    ),
                );
            }
            const activityDetails = [];

            for (const activity of activities) {
                const { scheduleIds, sessionId, sessionFlowIds, questions, students } = activity;

                const questionDetails = [];
                if (questions.length) {
                    let questionIds;
                    questionIds = questions.sort((a, b) => {
                        return a.order - b.order;
                    });
                    questionIds = questions.map((question) => question._id.toString());
                    for (questionIdElement of questionIds) {
                        const questionDataElement = activityQuestionDatas.find(
                            (ele) => ele._id.toString() === questionIdElement.toString(),
                        );
                        if (questionDataElement) questionDetails.push(questionDataElement);
                    }
                }
                const courseSchedules = courseScheduleList.filter((ele) =>
                    scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                );
                let studentGroupsName = '';
                for (const scheduleId of courseSchedules) {
                    const { merge_status, merge_with } = scheduleId;

                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const mergedSchedules = mergedCourseScheduleData.filter((ele) =>
                                scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                            );

                            mergedSchedules.push(scheduleId);
                            mergedSchedules.forEach((courseSchedule) => {
                                const { student_groups, _id } = courseSchedule;
                                if (student_groups && student_groups.length) {
                                    let studentGroups = student_groups.map((student_group) => {
                                        const { group_name, session_group } = student_group;
                                        let groupName = group_name.split('-').slice(-2);
                                        groupName = groupName[1]
                                            ? groupName[0] + '-' + groupName[1]
                                            : groupName[0];
                                        if (session_group && session_group.length) {
                                            let sessionGroup = session_group.map(
                                                (groupNameEntry) => {
                                                    let groupNames = groupNameEntry.group_name
                                                        .split('-')
                                                        .slice(-2);
                                                    groupNames = groupNames[1]
                                                        ? groupNames[0] + '-' + groupNames[1]
                                                        : groupNames[0];
                                                    return groupNames;
                                                },
                                            );
                                            sessionGroup = sessionGroup.toString();
                                            groupName += '(' + sessionGroup + ')';
                                        }
                                        return groupName;
                                    });
                                    studentGroups = studentGroups.toString();
                                    studentGroupsName += studentGroups + ',';
                                }
                            });
                        }
                    } else {
                        const { student_groups } = scheduleId;
                        if (student_groups && student_groups.length) {
                            let studentGroups = student_groups.map((student_group) => {
                                const { group_name, session_group } = student_group;
                                let groupName = group_name.split('-').slice(-2);
                                groupName = groupName[1]
                                    ? groupName[0] + '-' + groupName[1]
                                    : groupName[0];
                                if (session_group && session_group.length) {
                                    let sessionGroup = session_group.map((groupNameEntry) => {
                                        let groupNames = groupNameEntry.group_name
                                            .split('-')
                                            .slice(-2);
                                        groupNames = groupNames[1]
                                            ? groupNames[0] + '-' + groupNames[1]
                                            : groupNames[0];
                                        return groupNames;
                                    });
                                    sessionGroup = sessionGroup.toString();
                                    groupName += '(' + sessionGroup + ')';
                                }
                                return groupName;
                            });
                            studentGroups = studentGroups.toString();
                            studentGroupsName += studentGroups + ',';
                        }
                    }
                }
                // let sessionIds = sessionFlowIds.map((sessionFlowId) =>
                //     convertToMongoObjectId(sessionFlowId._id),
                // );
                // const sessions = await CourseSchedule.find(
                //     {
                //         $or: [
                //             {
                //                 'session._session_id': { $in: sessionIds },
                //             },
                //             {
                //                 _id: { $in: sessionIds },
                //             },
                //         ],
                //     },
                //     { session: 1, title: 1, type: 1, _id: 1 },
                // ).lean();
                const sessionIds = sessionFlowIds.map((sessionFlowId) =>
                    sessionFlowId._id.toString(),
                );
                const sessions = courseScheduleList
                    .filter(
                        (ele) =>
                            ele.session &&
                            ele.session._session_id &&
                            sessionIds.find(
                                (ele2) => ele2.toString() === ele.session._session_id.toString(),
                            ),
                    )
                    .map((ele3) => {
                        return {
                            _id: ele3._id,
                            title: ele3.title,
                            type: ele3.type,
                            session: ele3.session,
                        };
                    });
                // sessionIds = sessionIds.map((sessionFlow) => sessionFlow.toString());
                const sessionDetails = sessionFlowIds.map((sessionId) => {
                    const sessionDetail = sessions.find(
                        (sessionEntry) =>
                            (sessionEntry.session &&
                                sessionEntry.session._session_id.toString() ===
                                    sessionId._id.toString()) ||
                            (sessionEntry.type === sessionId.type &&
                                sessionEntry._id.toString() === sessionId._id.toString()),
                    );
                    if (sessionDetail) {
                        const { session, _id, title, type } = sessionDetail;
                        if (session) {
                            const { _session_id } = session;
                            if (
                                session &&
                                _session_id &&
                                sessionIds.includes(_session_id.toString())
                            ) {
                                return {
                                    _id: session._session_id,
                                    s_no: session.s_no,
                                    delivery_symbol: session.delivery_symbol,
                                    delivery_no: session.delivery_no,
                                    session_type: session.session_type,
                                    session_topic: session.session_topic,
                                    type: REGULAR,
                                };
                            }
                        }
                        if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                            return { _id, title, type };
                        }
                    }
                });

                let answeredQuestions;
                answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
                if (answeredStudent) answeredQuestions = answeredStudent.questions;

                let activityQuestions;
                if (questionDetails && questionDetails.length) {
                    activityQuestions = questionDetails.map((selectedQuestion) => {
                        const { _id } = selectedQuestion;
                        const questionType = questions.find(
                            (q) => q._id.toString() === _id.toString(),
                        );
                        if (questionType) {
                            selectedQuestion.type = questionType.type;
                            selectedQuestion.order = questionType.order;
                        }
                        return selectedQuestion;
                    });
                    activityQuestions = await formatQuestions(activityQuestions, answeredQuestions);
                }
                const schedule = courseSchedules.find(
                    (scheduleId) =>
                        scheduleId.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleId.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );
                if (scheduleIds && scheduleIds.length) {
                    const courseSchedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                    });
                    const mergedStudents = [];
                    for (const scheduleIdEntry of courseSchedules) {
                        const {
                            _id: scheduleId,
                            merge_status,
                            merge_with,
                            students,
                        } = scheduleIdEntry;
                        let scheduleStudents;
                        if (merge_status) {
                            const scheduleIds = merge_with.map((mergeWith) =>
                                convertToMongoObjectId(mergeWith.schedule_id),
                            );
                            if (scheduleIds && scheduleIds.length) {
                                const schedules = await CourseSchedule.find({
                                    _id: { $in: scheduleIds },
                                    isDeleted: false,
                                    isActive: true,
                                }).lean();
                                scheduleStudents = schedules.map((schedule) => schedule.students);
                                // eslint-disable-next-line prefer-spread
                                scheduleStudents = [].concat.apply([], scheduleStudents);
                            }
                        }
                        if (scheduleStudents && scheduleStudents.length) {
                            scheduleStudents = scheduleStudents.concat(students);
                            // eslint-disable-next-line prefer-spread
                            scheduleStudents = [].concat.apply([], scheduleStudents);
                        } else {
                            scheduleStudents = students;
                        }
                        if (scheduleStudents && scheduleStudents.length) {
                            mergedStudents.push(scheduleStudents);
                        }
                    }
                    // eslint-disable-next-line prefer-spread
                    totalStudents = [].concat.apply([], mergedStudents);
                    // different calendar and year and level based courses
                    totalStudents = totalStudents.reduce((acc, current) => {
                        const studentCount = acc.find(
                            (item) => item._id.toString() === current._id.toString(),
                        );
                        if (!studentCount) {
                            return acc.concat([current]);
                        }
                        return acc;
                    }, []);
                }

                activityDetails.push({
                    activityId: activity._id,
                    status: activity.status,
                    name: activity.name,
                    quizType: activity.quizType,
                    schedule: activity.schedule,
                    questions: activity.questions,
                    socketEventStaffId: activity.socketEventStaffId,
                    staffStartWithExam: activity.staffStartWithExam,
                    socketEventStudentId: activity.socketEventStudentId,
                    totalStudentAnsweredCount: students ? students.length : 0,
                    totalStudentCount: totalStudents ? totalStudents.length : 0,
                    createdBy: activity.createdBy,
                    setQuizTime: activity.setQuizTime,
                    courseId: activity.courseId._id,
                    course_name: activity.courseId.course_name,
                    versionNo: activity.courseId.versionNo || 1,
                    versioned: activity.courseId.versioned || false,
                    versionName: activity.courseId.versionName || '',
                    versionedFrom: activity.courseId.versionedFrom || null,
                    versionedCourseIds: activity.courseId.versionedCourseIds || [],
                    type: 'activity',
                    studentGroupName: getUniqueStudentGroup(studentGroupsName),
                    sessionId,
                    sessionType: schedule && schedule.type ? schedule.type : undefined,
                    _institution_calendar_id: activity._institution_calendar_id,
                    year_no: activity.year_no,
                    level_no: activity.level_no,
                    term: activity.term,
                    _program_id: activity._program_id,
                    _course_id: activity._course_id,
                    merge_status: schedule ? schedule.merge_status : undefined,
                    _id:
                        (schedule && schedule._id ? schedule._id : undefined) ||
                        (activity && activity._id ? activity._id : undefined),
                    sessionFlowIds: sessionDetails,
                    rotation: activity.rotation,
                    rotation_count: activity.rotation_count ? activity.rotation_count : undefined,
                    isNewActivity: !!(activity.correctionType && activity.correctionType !== ''),
                });
            }
            courseSchedules = courseSchedules.concat(activityDetails);
        }
        return courseSchedules;
    } catch (error) {
        throw new Error(error);
    }
};

const getCoursesByStaffId = async (staffId) => {
    try {
        const courseIds = await getCourseIds(staffId, DC_STAFF);
        const cQuery = { ...query, _id: { $in: courseIds } };
        const cProject = {
            course_name: 1,
            course_code: 1,
            _program_id: 1,
            program_name: 1,
            term: 1,
            year_no: 1,
            level_no: 1,
            rotation: 1,
            rotation_count: 1,
            'course_assigned_details.year': 1,
        };
        const courses = await Course.find(cQuery, cProject).lean();
        const courseSchedules = await CourseSchedule.find(
            { _course_id: { $in: courseIds } },
            { _id: 1, _course_id: 1, year_no: 1, level_no: 1 },
        ).lean();

        const year = [];
        const level = [];
        const courseId = [];
        const rotation = [];
        const rotationCount = [];
        courseSchedules.map((courseDatas) => {
            courseId.push(courseDatas._course_id);
            year.push(courseDatas.year_no);
            level.push(courseDatas.level_no);
            rotation.push(courseDatas.rotation);
            rotationCount.push(courseDatas.rotation_count);
        });
        const feedBackData = await getRatingByCourses(
            courseId,
            staffId,
            year,
            level,
            rotation,
            rotationCount,
        );

        courses.forEach((course) => {
            course.totalSessions = courseSchedules.filter(
                (cSchedules) => cs(cSchedules._course_id) === cs(course._id),
            ).length;
            course.completedSessions = courseSchedules.filter(
                (cSchedules) =>
                    cs(cSchedules._course_id) === cs(course._id) &&
                    cSchedules.status === 'completed',
            ).length;
            course.feedBack = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._course_id.toString() === course._id.toString() &&
                    feedBackDetail.level_no === course.level_no &&
                    feedBackDetail.year_no === course.year_no &&
                    feedBackDetail.rotation === course.rotation &&
                    course.rotation_count &&
                    feedBackDetail.rotation_count === course.rotation_count,
            );
        });
        return courses;
    } catch (error) {
        throw new Error(error);
    }
};

const getCoursesByStudentId = async (studentId) => {
    try {
        const csQuery = { 'students._id': studentId, isDeleted: false, isActive: true };
        const csProject = {};
        const courseSchedules = await CourseSchedule.find(csQuery, csProject);
        const courseIds = [...new Set(courseSchedules.map((schedule) => schedule._course_id))];
        const cQuery = { ...query, _id: { $in: courseIds } };
        const cProject = { course_name: 1, course_code: 1, _program_id: 1 };
        const courses = (await getJSON(Course, cQuery, cProject)).data;
        const getSessionCount = (_course_id, flag) => {
            if (flag === 'total') {
                return courseSchedules.filter(
                    (cSchedule) => cs(cSchedule._course_id) === cs(_course_id),
                ).length;
            }
            if (flag === COMPLETED) {
                return courseSchedules.filter(
                    (cSchedule) =>
                        cs(cSchedule._course_id) === cs(_course_id) &&
                        cSchedule.status === COMPLETED,
                ).length;
            }
            return courseSchedules.filter(
                (cSchedule) =>
                    cs(cSchedule._course_id) === cs(_course_id) &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students.find(
                        (student) => cs(student._id) === cs(studentId) && student.status === flag,
                    ),
            ).length;
        };
        courses.forEach((course) => {
            course.totalSessions = getSessionCount(course._id, 'total');
            course.completedSessions = getSessionCount(course._id, COMPLETED);
            course.attendedSessions = getSessionCount(course._id, PRESENT);
            course.absentSessions = getSessionCount(course._id, ABSENT);
            course.leaveSessions = getSessionCount(course._id, LEAVE);
            course.warningOfSessions = getSessionCount(course._id, ABSENT); // To Do
            course.startDate = new Date(); // To Do
            course.endDate = new Date(); // To Do
            course.level = 3;
        });
        return courses;
    } catch (error) {
        throw new Error(error);
    }
};

const getSloBySessionIdAndCourseId = async (courseId, sessionId) => {
    try {
        const query = {
            _id: convertToMongoObjectId(courseId),
            isDeleted: false,
            isActive: true,
        };
        const courseResult = await Course.findOne(query);
        const sessionSlos = [];
        if (courseResult) {
            const domains = courseResult.framework.domains;
            domains.forEach((domainItem) => {
                if (!domainItem.clo) return;
                domainItem.clo.forEach((cloItem) => {
                    if (cloItem.slos && cloItem.slos.length) {
                        cloItem.slos.forEach((sloItem) => {
                            if (
                                sloItem.delivery_type_id &&
                                sloItem.delivery_type_id.toString() === sessionId.toString() &&
                                sloItem.mapped_value === 'TRUE'
                            ) {
                                sessionSlos.push({
                                    _id: sloItem.slo_id,
                                    name: sloItem.name,
                                    no: sloItem.no,
                                    type: DS_SLO_KEY,
                                });
                            }
                        });
                    }
                });
            });
            if (!sessionSlos.length) {
                domains.forEach((domainItem) => {
                    if (!domainItem.clo) return;
                    domainItem.clo.forEach((cloItem) => {
                        if (!cloItem.isDeleted) {
                            sessionSlos.push({
                                _id: cloItem._id,
                                name: cloItem.name,
                                no: cloItem.no,
                                type: DS_CLO_KEY,
                            });
                        }
                    });
                });
            }
        }
        const sessionCloAndSlo = sessionSlos.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        return sessionCloAndSlo;
    } catch (error) {
        throw new Error(error);
    }
};

const getSloPlusCloBySessionIdAndCourseId = async (courseId, sessionId, type) => {
    try {
        const query = {
            _id: convertToMongoObjectId(courseId),
            isDeleted: false,
            isActive: true,
        };
        const courseResult = await Course.findOne(query);
        const sessionSlos = [];
        if (courseResult) {
            const domains = courseResult.framework.domains;
            domains.forEach((domainItem) => {
                if (!domainItem.clo) return;
                domainItem.clo.forEach((cloItem) => {
                    if (!cloItem.isDeleted) {
                        sessionSlos.push({
                            _id: cloItem._id,
                            name: cloItem.name,
                            no: cloItem.no,
                            type: DS_CLO_KEY,
                        });
                    }

                    if (cloItem.slos && cloItem.slos.length) {
                        cloItem.slos.forEach((sloItem) => {
                            if (
                                sloItem.delivery_type_id &&
                                sloItem.delivery_type_id.toString() === sessionId.toString() &&
                                sloItem.mapped_value === 'TRUE'
                            ) {
                                sessionSlos.push({
                                    _id: sloItem.slo_id,
                                    name: sloItem.name,
                                    no: sloItem.no,
                                    type: DS_SLO_KEY,
                                });
                            }
                        });
                    }
                });
            });
            if (!sessionSlos.length) {
                domains.forEach((domainItem) => {
                    if (!domainItem.clo) return;
                    domainItem.clo.forEach((cloItem) => {
                        if (!cloItem.isDeleted) {
                            sessionSlos.push({
                                _id: cloItem._id,
                                name: cloItem.name,
                                no: cloItem.no,
                                type: DS_CLO_KEY,
                            });
                        }
                    });
                });
            }
        }
        const sloIds = sessionSlos.map((sessionSlo) => convertToMongoObjectId(sessionSlo._id));
        let questionQuery = {
            status: COMPLETED,
            courseId: convertToMongoObjectId(courseId),
            'sessionFlowIds._id': convertToMongoObjectId(sessionId),
        };

        const activities = await Activity.find(questionQuery, { questions: 1 }).lean();
        const activityQuestions = activities
            .filter((activity) => activity.questions && activity.questions.length)
            .map((activityEntry) => activityEntry.questions);
        // eslint-disable-next-line prefer-spread
        let questionIds = [].concat.apply([], activityQuestions);
        questionIds = questionIds.map((questionId) => convertToMongoObjectId(questionId._id));

        questionQuery = {
            isDeleted: false,
            _id: { $in: questionIds },
            sloIds: { $in: sloIds },
            sessionId: convertToMongoObjectId(sessionId),
        };
        const questions = await Question.find(questionQuery, {
            _id: 1,
            sloIds: 1,
            sessionId: 1,
        }).lean();
        let questionSessions = sessionSlos;
        // eslint-disable-next-line no-sequences
        questionSessions = questionSessions.reduce((questionSession, currentQuestionSession) => {
            const existQuestionSession = questionSession.find(
                (questionSessionEntry) =>
                    questionSessionEntry._id.toString() === currentQuestionSession._id.toString(),
            );
            if (!existQuestionSession) {
                return questionSession.concat([currentQuestionSession]);
            }
            return questionSession;
        }, []);
        if (type === 'question') {
            questionSessions = questionSessions.filter((sessionSlo) =>
                questions.find((question) => {
                    const sloOrClos = question.sloIds.map((sloId) => sloId.toString());
                    if (
                        sloOrClos.includes(sessionSlo._id.toString()) &&
                        sessionId.toString() === question.sessionId.toString()
                    ) {
                        return true;
                    }
                    return false;
                }),
            );
        }
        return questionSessions;
    } catch (error) {
        throw new Error(error);
    }
};
const getFeedbackByStaff = async (staffId) => {
    try {
        const fbQuery = {
            $or: [
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'sessionDetail.startBy': convertToMongoObjectId(staffId),
                },
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'staffs.status': PRESENT,
                },
            ],
            students: { $exists: true, $type: 'array', $ne: [] },
            isDeleted: false,
            isActive: true,
        };
        const fbProject = { students: 1, session: 1, staffs: 1, _id: 1 };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject);
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    staff._staff_id.toString() === staffId.toString() && staff.status === PRESENT,
            ),
        );
        let sumOfRatings = 0;
        let count = 0;
        let sessionCount = 0;
        let sessions = courseSchedules.filter(
            (courseSchedule) => courseSchedule.session && courseSchedule.session._session_id,
        );
        // const supportAndEventSessions = courseSchedules.filter(
        //     (courseSchedule) => !courseSchedule.session,
        // );
        if (sessions && sessions.length) {
            sessions = sessions.map((sessionEntry) => sessionEntry.session._session_id.toString());
            sessions = [...new Set(sessions)];
            sessionCount += sessions.length;
        }
        // if (supportAndEventSessions && supportAndEventSessions.length) {
        //     sessionCount += supportAndEventSessions.length;
        // }
        const schedulePush = [];
        courseSchedules = courseSchedules.filter(
            (courseSchedule) => courseSchedule.session && courseSchedule.session._session_id,
        );
        courseSchedules.map((courseDetail) => {
            const { students, _id } = courseDetail;

            sumOfRatings += students
                .filter(
                    (student) =>
                        student.status === PRESENT && student.feedBack && student.feedBack.rating,
                )
                .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                .reduce((a, b) => a + b, 0);
            if (students) {
                count += students.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
                students.map((student) => {
                    if (student.feedBack && student.feedBack.rating) {
                        schedulePush.push(_id);
                    }
                });
            }
        });
        return {
            staff_id: staffId,
            totalFeedback: count,
            avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
            sessionCount: schedulePush.length,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const isDeleteActive = {
    isDeleted: false,
    isActive: true,
};

const getUserCourseLists = async ({ userId, type, institutionCalendarId }) => {
    try {
        // User Course Details
        const userCourseRedisKey = `${USER_COURSES}:${userId}-${institutionCalendarId}`;
        if (type === DC_STUDENT) {
            const userCoursesCache = await redisClient.Client.get(userCourseRedisKey);
            if (userCoursesCache && JSON.parse(userCoursesCache).length) {
                return JSON.parse(userCoursesCache).sort((a, b) => {
                    let comparison = 0;
                    if (new Date(a.end_date) > new Date(b.end_date)) {
                        comparison = 1;
                    } else if (new Date(a.end_date) < new Date(b.end_date)) {
                        comparison = -1;
                    }
                    return comparison;
                });
            }
        }
        const scheduleQuery =
            type === DC_STUDENT
                ? {
                      'students._id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      isDeleted: false,
                      //   ...isDeleteActive,
                  }
                : {
                      'staffs._staff_id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      isDeleted: false,
                      //   ...isDeleteActive,
                  };
        let scheduleDatasAggregate = await CourseSchedule.aggregate([
            {
                $match: scheduleQuery,
            },
            {
                $group: {
                    _id: {
                        _program_id: '$_program_id',
                        term: '$term',
                        _course_id: '$_course_id',
                        year_no: '$year_no',
                        level_no: '$level_no',
                        rotation_count: '$rotation_count',
                    },
                    program_name: { $first: '$program_name' },
                },
            },
        ]);
        if (type === DC_STUDENT) {
            const studentStudentGroupAggregate = await studentGroup.aggregate([
                {
                    $match: {
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        'groups.courses.setting.session_setting.groups._student_ids':
                            convertToMongoObjectId(userId),
                    },
                },
                { $unwind: { path: '$groups' } },
                { $unwind: { path: '$groups.courses' } },
                { $unwind: { path: '$groups.courses.setting' } },
                {
                    $match: {
                        'groups.courses.setting.session_setting.groups._student_ids':
                            convertToMongoObjectId(userId),
                    },
                },
                {
                    $group: {
                        _id: {
                            _program_id: '$master._program_id',
                            term: '$groups.term',
                            level_no: '$groups.level',
                            rotation: '$groups.rotation',
                            rotation_count: '$groups.courses.setting._group_no',
                            _course_id: '$groups.courses._course_id',
                            year_no: '$master.year',
                        },
                        program_name: { $first: '$master.program_name' },
                    },
                },
            ]);
            if (studentStudentGroupAggregate)
                scheduleDatasAggregate = [
                    ...scheduleDatasAggregate,
                    ...studentStudentGroupAggregate,
                ];
        }
        const programCalendarData = await ProgramCalendar.find(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isDeleted: false,
                status: PUBLISHED,
                _program_id: {
                    $in: scheduleDatasAggregate.map(
                        (scheduleElement) => scheduleElement._id._program_id,
                    ),
                },
            },
            {
                _program_id: 1,
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.rotation': 1,
                'level.course._course_id': 1,
                'level.course.courses_name': 1,
                'level.course.courses_number': 1,
                'level.course.model': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.courses_name': 1,
                'level.rotation_course.course.courses_number': 1,
                'level.rotation_course.course.model': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        )
            .populate({
                path: 'level.course._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .populate({
                path: 'level.rotation_course.course._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        const userCourseList = [];
        for (programCalendarElement of programCalendarData) {
            for (levelElement of programCalendarElement.level) {
                if (levelElement.rotation === 'no') {
                    for (courseElement of levelElement.course) {
                        const scheduleCourse = scheduleDatasAggregate.find(
                            (scheduleElement) =>
                                scheduleElement._id._program_id.toString() ===
                                    programCalendarElement._program_id.toString() &&
                                scheduleElement._id.term === levelElement.term &&
                                scheduleElement._id.year_no === levelElement.year &&
                                scheduleElement._id.level_no === levelElement.level_no &&
                                scheduleElement._id._course_id.toString() ===
                                    courseElement._course_id._id.toString(),
                        );
                        if (scheduleCourse) {
                            userCourseList.push({
                                _institution_calendar_id: institutionCalendarId,
                                _program_id: programCalendarElement._program_id.toString(),
                                _id: courseElement._course_id._id,
                                year: levelElement.year,
                                level: levelElement.level_no,
                                term: levelElement.term,
                                course_code: courseElement.courses_number,
                                course_name: courseElement.courses_name,
                                course_type: courseElement.model,
                                versionNo: courseElement._course_id.versionNo || 1,
                                versioned: courseElement._course_id.versioned || false,
                                versionName: courseElement._course_id.versionName || '',
                                versionedFrom: courseElement._course_id.versionedFrom || null,
                                versionedCourseIds:
                                    courseElement._course_id.versionedCourseIds || [],
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                program_name: scheduleCourse.program_name,
                                rotation: levelElement.rotation,
                            });
                        }
                    }
                } else
                    for (rotationElement of levelElement.rotation_course) {
                        for (courseElement of rotationElement.course) {
                            const scheduleCourse = scheduleDatasAggregate.find(
                                (scheduleElement) =>
                                    scheduleElement._id.rotation_count ===
                                        rotationElement.rotation_count &&
                                    scheduleElement._id._program_id.toString() ===
                                        programCalendarElement._program_id.toString() &&
                                    scheduleElement._id.term === levelElement.term &&
                                    scheduleElement._id.year_no === levelElement.year &&
                                    scheduleElement._id.level_no === levelElement.level_no &&
                                    scheduleElement._id._course_id.toString() ===
                                        courseElement._course_id._id.toString(),
                            );
                            if (scheduleCourse) {
                                userCourseList.push({
                                    _institution_calendar_id: institutionCalendarId,
                                    _program_id: programCalendarElement._program_id.toString(),
                                    _id: courseElement._course_id._id,
                                    year: levelElement.year,
                                    level: levelElement.level_no,
                                    term: levelElement.term,
                                    course_code: courseElement.courses_number,
                                    course_name: courseElement.courses_name,
                                    course_type: courseElement.model,
                                    versionNo: courseElement._course_id.versionNo || 1,
                                    versioned: courseElement._course_id.versioned || false,
                                    versionName: courseElement._course_id.versionName || '',
                                    versionedFrom: courseElement._course_id.versionedFrom || null,
                                    versionedCourseIds:
                                        courseElement._course_id.versionedCourseIds || [],
                                    start_date: courseElement.start_date,
                                    end_date: courseElement.end_date,
                                    program_name: scheduleCourse.program_name,
                                    rotation: levelElement.rotation,
                                    rotation_count: rotationElement.rotation_count,
                                });
                            }
                        }
                    }
            }
        }
        if (type === DC_STUDENT) {
            await redisClient.Client.set(userCourseRedisKey, JSON.stringify(userCourseList));
        }
        return userCourseList.sort((a, b) => {
            let comparison = 0;
            if (new Date(a.end_date) > new Date(b.end_date)) {
                comparison = 1;
            } else if (new Date(a.end_date) < new Date(b.end_date)) {
                comparison = -1;
            }
            return comparison;
        });
    } catch (error) {
        throw new Error(error);
    }
};

const getUserCourseListsForMultiCalendar = async ({ userId, type, institutionCalendarId }) => {
    try {
        const institutionCalendarIdString = institutionCalendarId.map((calendarElement) =>
            calendarElement.toString(),
        );
        const userCourseRedisKey = `${USER_COURSES}:${userId}-${institutionCalendarIdString}`;
        if (type === DC_STUDENT) {
            const userCoursesCache = await redisClient.Client.get(userCourseRedisKey);
            if (userCoursesCache && JSON.parse(userCoursesCache).length) {
                return JSON.parse(userCoursesCache).sort((a, b) => {
                    let comparison = 0;
                    if (new Date(a.end_date) > new Date(b.end_date)) {
                        comparison = 1;
                    } else if (new Date(a.end_date) < new Date(b.end_date)) {
                        comparison = -1;
                    }
                    return comparison;
                });
            }
        }
        const scheduleQuery =
            type === DC_STUDENT
                ? {
                      'students._id': convertToMongoObjectId(userId),
                      _institution_calendar_id: { $in: institutionCalendarId },
                      isDeleted: false,
                  }
                : {
                      'staffs._staff_id': convertToMongoObjectId(userId),
                      _institution_calendar_id: { $in: institutionCalendarId },
                      isDeleted: false,
                  };
        let scheduleDatasAggregate = await CourseSchedule.aggregate([
            { $match: scheduleQuery },
            {
                $group: {
                    _id: {
                        _program_id: '$_program_id',
                        _institution_calendar_id: '$_institution_calendar_id',
                        term: '$term',
                        _course_id: '$_course_id',
                        year_no: '$year_no',
                        level_no: '$level_no',
                        rotation_count: '$rotation_count',
                    },
                    program_name: { $first: '$program_name' },
                },
            },
        ]);

        if (type === DC_STUDENT) {
            // Aggregate student group data
            const studentStudentGroupAggregate = await studentGroup.aggregate([
                {
                    $match: {
                        _institution_calendar_id: { $in: institutionCalendarId },
                        'groups.courses.setting.session_setting.groups._student_ids':
                            convertToMongoObjectId(userId),
                    },
                },
                { $unwind: { path: '$groups' } },
                { $unwind: { path: '$groups.courses' } },
                { $unwind: { path: '$groups.courses.setting' } },
                {
                    $match: {
                        'groups.courses.setting.session_setting.groups._student_ids':
                            convertToMongoObjectId(userId),
                    },
                },
                {
                    $group: {
                        _id: {
                            _program_id: '$master._program_id',
                            _institution_calendar_id: '$_institution_calendar_id',
                            term: '$groups.term',
                            level_no: '$groups.level',
                            rotation: '$groups.rotation',
                            rotation_count: '$groups.courses.setting._group_no',
                            _course_id: '$groups.courses._course_id',
                            year_no: '$master.year',
                        },
                        program_name: { $first: '$master.program_name' },
                    },
                },
            ]);

            if (studentStudentGroupAggregate) {
                scheduleDatasAggregate = [
                    ...scheduleDatasAggregate,
                    ...studentStudentGroupAggregate,
                ];
            }
        }

        // Fetch program calendar data
        const programCalendarData = await ProgramCalendar.find(
            {
                _institution_calendar_id: { $in: institutionCalendarId },
                isDeleted: false,
                status: PUBLISHED,
                _program_id: {
                    $in: scheduleDatasAggregate.map(
                        (scheduleElement) => scheduleElement._id._program_id,
                    ),
                },
            },
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.rotation': 1,
                'level.course._course_id': 1,
                'level.course.courses_name': 1,
                'level.course.courses_number': 1,
                'level.course.model': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.courses_name': 1,
                'level.rotation_course.course.courses_number': 1,
                'level.rotation_course.course.model': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        ).lean();

        const userCourseList = [];

        // Loop through program calendar data and populate userCourseList
        for (programCalendarElement of programCalendarData) {
            for (levelElement of programCalendarElement.level) {
                if (levelElement.rotation === 'no') {
                    for (courseElement of levelElement.course) {
                        const scheduleCourse = scheduleDatasAggregate.find(
                            (scheduleElement) =>
                                scheduleElement._id._program_id.toString() ===
                                    programCalendarElement._program_id.toString() &&
                                scheduleElement._id._institution_calendar_id.toString() ===
                                    programCalendarElement._institution_calendar_id.toString() &&
                                scheduleElement._id.term === levelElement.term &&
                                scheduleElement._id.year_no === levelElement.year &&
                                scheduleElement._id.level_no === levelElement.level_no &&
                                scheduleElement._id._course_id.toString() ===
                                    courseElement._course_id.toString(),
                        );

                        if (scheduleCourse) {
                            userCourseList.push({
                                _institution_calendar_id:
                                    programCalendarElement._institution_calendar_id,
                                _program_id: programCalendarElement._program_id.toString(),
                                _id: courseElement._course_id,
                                year: levelElement.year,
                                level: levelElement.level_no,
                                term: levelElement.term,
                                course_code: courseElement.courses_number,
                                course_name: courseElement.courses_name,
                                course_type: courseElement.model,
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                program_name: scheduleCourse.program_name,
                                rotation: levelElement.rotation,
                            });
                        }
                    }
                } else {
                    for (rotationElement of levelElement.rotation_course) {
                        for (courseElement of rotationElement.course) {
                            const scheduleCourse = scheduleDatasAggregate.find(
                                (scheduleElement) =>
                                    scheduleElement._id.rotation_count ===
                                        rotationElement.rotation_count &&
                                    scheduleElement._id._program_id.toString() ===
                                        programCalendarElement._program_id.toString() &&
                                    scheduleElement._id._institution_calendar_id.toString() ===
                                        programCalendarElement._institution_calendar_id.toString() &&
                                    scheduleElement._id.term === levelElement.term &&
                                    scheduleElement._id.year_no === levelElement.year &&
                                    scheduleElement._id.level_no === levelElement.level_no &&
                                    scheduleElement._id._course_id.toString() ===
                                        courseElement._course_id.toString(),
                            );

                            if (scheduleCourse) {
                                userCourseList.push({
                                    _institution_calendar_id:
                                        programCalendarElement._institution_calendar_id,
                                    _program_id: programCalendarElement._program_id.toString(),
                                    _id: courseElement._course_id,
                                    year: levelElement.year,
                                    level: levelElement.level_no,
                                    term: levelElement.term,
                                    course_code: courseElement.courses_number,
                                    course_name: courseElement.courses_name,
                                    course_type: courseElement.model,
                                    start_date: courseElement.start_date,
                                    end_date: courseElement.end_date,
                                    program_name: scheduleCourse.program_name,
                                    rotation: levelElement.rotation,
                                    rotation_count: rotationElement.rotation_count,
                                });
                            }
                        }
                    }
                }
            }
        }
        if (type === DC_STUDENT) {
            await redisClient.Client.set(userCourseRedisKey, JSON.stringify(userCourseList));
        }
        return userCourseList.sort((a, b) => {
            let comparison = 0;
            if (new Date(a.end_date) > new Date(b.end_date)) {
                comparison = 1;
            } else if (new Date(a.end_date) < new Date(b.end_date)) {
                comparison = -1;
            }
            return comparison;
        });
    } catch (error) {
        throw new Error(error);
    }
};

const getStudentListFromStudentGroup = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    institutionCalendarId,
    courseId,
    _institution_id,
}) => {
    try {
        if (!programId || !year) return [];
        let masterGroup = [];
        let sgStudentList = [];
        let totalStudentCount = 0;
        let maleStudentCount = 0;
        let femaleStudentCount = 0;
        let key = `${constant.COURSE_STUDENT}:${year}-${level}-${term}-${courseId}-${institutionCalendarId}`;
        if (rotationCount) {
            key = `${constant.COURSE_STUDENT}:${year}-${level}-${term}-${courseId}-${institutionCalendarId}-${rotationCount}`;
        }
        const students = await redisClient.Client.get(key);
        if (students) {
            ({
                masterGroup,
                sgStudentList,
                totalStudentCount,
                maleStudentCount,
                femaleStudentCount,
            } = JSON.parse(students));
            return {
                masterGroup,
                sgStudentList,
                totalStudentCount,
                maleStudentCount,
                femaleStudentCount,
            };
        }

        const studentGroupData = await studentGroup
            .findOne(
                {
                    'master._program_id': programId,
                    'master.year': year,
                    groups: {
                        $elemMatch: {
                            level,
                            ...(term && { term }),
                        },
                    },
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    ...(_institution_id && {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    }),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._id': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups.group_name': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.courses.setting.session_setting.delivery_type': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.group_mode': 1,
                    'groups.students.course_group_status._course_id': 1,
                    'groups.students._student_id': 1,
                    'groups.students.academic_no': 1,
                    'groups.students.name': 1,
                    'groups.students.gender': 1,
                    'groups.group_setting.gender': 1,
                    'groups.group_setting.groups._id': 1,
                    'groups.group_setting.groups.gender': 1,
                    'groups.group_setting.groups.group_no': 1,
                    'groups.group_setting.groups.group_name': 1,
                    'groups.rotation_group_setting._id': 1,
                    'groups.rotation_group_setting.gender': 1,
                    'groups.rotation_group_setting.group_no': 1,
                    'groups.rotation_group_setting.group_name': 1,
                },
            )
            .lean();
        if (!studentGroupData) {
            return [];
        }
        for (groupElement of studentGroupData.groups) {
            const courseInd = groupElement.courses.findIndex(
                (courseElement) => courseElement._course_id.toString() === courseId.toString(),
            );
            if (term !== groupElement.term || courseInd === -1 || level !== groupElement.level) {
                continue;
            }
            sgStudentList = groupElement.students
                .filter((studentElement) =>
                    studentElement.course_group_status.some(
                        (courseGroupStatusElement) =>
                            courseId.toString() === courseGroupStatusElement._course_id.toString(),
                    ),
                )
                .map(({ _student_id, academic_no, name, gender }) => ({
                    _student_id,
                    user_id: academic_no,
                    name,
                    gender,
                    isActive: true,
                }));
            for (const setting of groupElement.courses[courseInd].setting) {
                if (groupElement.group_mode !== COURSE) {
                    for (const [
                        sessionTypeIndex,
                        settingElement,
                    ] of setting.session_setting.entries()) {
                        if (groupElement.group_mode.toString() === ROTATION.toString()) {
                            masterGroup.push(
                                ...groupElement.rotation_group_setting
                                    .map((rotationGroupSettingElement) => {
                                        if (
                                            rotationGroupSettingElement.group_no.toString() ===
                                            rotationCount
                                        ) {
                                            const group_name =
                                                rotationGroupSettingElement.group_name.split('-');
                                            return {
                                                _id: rotationGroupSettingElement._id,
                                                gender: rotationGroupSettingElement.gender,
                                                rotation: 'yes',
                                                group_no: rotationGroupSettingElement.group_no,
                                                rotation_count:
                                                    rotationGroupSettingElement.group_no,
                                                group_name: group_name
                                                    .slice(group_name.length - 2, group_name.length)
                                                    .join('-'),
                                                delivery_type: settingElement.delivery_type,
                                                delivery_symbol: settingElement.session_type,
                                                session_group: [],
                                            };
                                        }
                                        return null;
                                    })
                                    .filter(Boolean),
                            );
                        } else if (groupElement.group_mode.toString() === FYD.toString()) {
                            masterGroup.push(
                                ...groupElement.group_setting.flatMap(
                                    (foundationGroupSettingElement) =>
                                        foundationGroupSettingElement.groups.map(
                                            (foundationGroupElement) => {
                                                const group_name =
                                                    foundationGroupElement.group_name.split('-');
                                                return {
                                                    _id: foundationGroupElement._id,
                                                    gender: foundationGroupSettingElement.gender,
                                                    group_no: foundationGroupElement.group_no,
                                                    group_name: group_name
                                                        .slice(
                                                            group_name.length - 2,
                                                            group_name.length,
                                                        )
                                                        .join('-'),
                                                    delivery_type: settingElement.delivery_type,
                                                    delivery_symbol: settingElement.session_type,
                                                    session_group: [],
                                                };
                                            },
                                        ),
                                ),
                            );
                        }
                        const masterGroupLocation = masterGroup.findIndex(
                            (masterGroupElement) =>
                                masterGroupElement.group_no.toString() ===
                                    setting._group_no.toString() &&
                                masterGroupElement.gender === setting.gender &&
                                masterGroupElement.delivery_type === settingElement.delivery_type &&
                                masterGroupElement.delivery_symbol === settingElement.session_type,
                        );
                        if (masterGroupLocation !== -1) {
                            masterGroup[masterGroupLocation].session_group = (
                                setting.gender === BOTH
                                    ? sgStudentList.length
                                    : sgStudentList.filter(
                                          (sgStudentListElement) =>
                                              sgStudentListElement.gender === setting.gender,
                                      ).length !== 0
                            )
                                ? setting.session_setting[sessionTypeIndex].groups.map(
                                      (groupsElement) => {
                                          const group_name = groupsElement.group_name.split('-');
                                          return {
                                              _id: groupsElement._id,
                                              group_name: group_name
                                                  .slice(group_name.length - 3, group_name.length)
                                                  .join('-'),
                                              _student_ids: groupsElement._student_ids,
                                          };
                                      },
                                  )
                                : [];
                        }
                    }
                } else {
                    for (settingElement of setting.session_setting) {
                        if (!settingElement.delivery_type) {
                            continue;
                        }
                        const group_name =
                            setting.gender === BOTH
                                ? 'SG-1'
                                : setting.gender === 'male'
                                ? 'MG-1'
                                : 'FG-1';
                        masterGroup.push({
                            _id: setting._id,
                            gender: setting.gender,
                            group_name,
                            delivery_type: settingElement.delivery_type,
                            delivery_symbol: settingElement.session_type,
                            session_group: (
                                setting.gender === BOTH
                                    ? sgStudentList.length
                                    : sgStudentList.filter((ele) => ele.gender === setting.gender)
                                          .length !== 0
                            )
                                ? settingElement.groups.map((groupsElement) => {
                                      const group_name = groupsElement.group_name.split('-');
                                      return {
                                          _id: groupsElement._id,
                                          group_name: group_name
                                              .slice(group_name.length - 3, group_name.length)
                                              .join('-'),
                                          _student_ids: groupsElement._student_ids,
                                      };
                                  })
                                : [],
                        });
                    }
                }
            }
        }

        const studentIdsSet = new Set();
        masterGroup.forEach((groupElement) => {
            groupElement.session_group.forEach((sessionElement) => {
                (sessionElement._student_ids || []).forEach((studentElement) => {
                    studentIdsSet.add(studentElement.toString());
                });
            });
        });
        sgStudentList = sgStudentList.filter((student) =>
            studentIdsSet.has(student._student_id.toString()),
        );
        totalStudentCount = sgStudentList ? sgStudentList.length : 0;
        maleStudentCount = sgStudentList
            ? sgStudentList.filter((studentElement) => studentElement.gender === MALE).length
            : 0;
        femaleStudentCount = totalStudentCount - maleStudentCount;
        if (masterGroup.length && sgStudentList.length) {
            await redisClient.Client.set(
                key,
                JSON.stringify({
                    masterGroup,
                    sgStudentList,
                    totalStudentCount,
                    maleStudentCount,
                    femaleStudentCount,
                }),
            );
        }
        return {
            masterGroup,
            sgStudentList,
            totalStudentCount,
            maleStudentCount,
            femaleStudentCount,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getSessionListFromSchedule = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    institutionCalendarId,
    courseId,
    _institution_id,
}) => {
    try {
        const scheduleQuery = {
            ...isDeleteActive,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _course_id: convertToMongoObjectId(courseId),
            _program_id: convertToMongoObjectId(programId),
            level_no: level,
            term,
            type: REGULAR,
            ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
        };
        const scheduleGroup = await CourseSchedule.find(scheduleQuery, {
            'student_groups.group_name': 1,
            'student_groups.group_id': 1,
            'student_groups.delivery_symbol': 1,
            'student_groups.session_group.session_group_id': 1,
            'student_groups.session_group.group_name': 1,
        }).lean();
        if (!scheduleGroup) {
            return [];
        }
        const masterGroup = new Map();
        const uniqueIds = new Set();
        for (const scheduleElement of scheduleGroup) {
            for (const groupElement of scheduleElement.student_groups) {
                for (const sessiongroupElement of groupElement.session_group) {
                    const idString =
                        groupElement.group_name +
                        groupElement.delivery_symbol +
                        sessiongroupElement.session_group_id.toString();
                    if (!uniqueIds.has(idString)) {
                        uniqueIds.add(idString);
                        const sessionGroupName = sessiongroupElement.group_name.split('-');
                        masterGroup.set(idString, {
                            group_name: groupElement.group_name,
                            delivery_symbol: groupElement.delivery_symbol,
                            session_group_name: sessionGroupName
                                .slice(sessionGroupName.length - 3, sessionGroupName.length)
                                .join('-'),
                            session_group_id: sessiongroupElement.session_group_id,
                        });
                    }
                }
            }
        }

        const masterGroupValue = [...masterGroup.values()];
        const groupedMasterData = masterGroupValue.reduce((acc, masterGroupElement) => {
            const existingGroup = acc.find(
                (groupElement) => groupElement.groupName === masterGroupElement.group_name,
            );
            if (!existingGroup) {
                acc.push({
                    groupName: masterGroupElement.group_name,
                    delivery: [
                        {
                            deliverySymbol: masterGroupElement.delivery_symbol,
                            sessionGroup: [
                                {
                                    sessionGroupName: masterGroupElement.session_group_name,
                                    sessionGroupId: masterGroupElement.session_group_id,
                                },
                            ],
                        },
                    ],
                });
            } else {
                const existingDelivery = existingGroup.delivery.find(
                    (deliveryElement) =>
                        deliveryElement.deliverySymbol === masterGroupElement.delivery_symbol,
                );
                if (!existingDelivery) {
                    existingGroup.delivery.push({
                        deliverySymbol: masterGroupElement.delivery_symbol,
                        sessionGroup: [
                            {
                                sessionGroupName: masterGroupElement.session_group_name,
                                sessionGroupId: masterGroupElement.session_group_id,
                            },
                        ],
                    });
                } else {
                    existingDelivery.sessionGroup.push({
                        sessionGroupName: masterGroupElement.session_group_name,
                        sessionGroupId: masterGroupElement.session_group_id,
                    });
                }
            }
            return acc;
        }, []);
        return groupedMasterData;
    } catch (error) {
        throw new Error(error);
    }
};

const getUserCourseDetails = async ({
    userId,
    courseId,
    programId,
    year,
    level,
    term,
    type,
    rotationCount,
    institutionCalendarId,
    _institution_id,
}) => {
    try {
        const scheduleQuery =
            type === DC_STUDENT
                ? {
                      ...isDeleteActive,
                      // isDeleted: false,
                      'students._id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      _course_id: convertToMongoObjectId(courseId),
                      level_no: level,
                      term,
                      type: REGULAR,
                      ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
                  }
                : {
                      ...isDeleteActive,
                      //   isDeleted: false,
                      'staffs._staff_id': convertToMongoObjectId(userId),
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      _course_id: convertToMongoObjectId(courseId),
                      level_no: level,
                      term,
                      type: REGULAR,
                      ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
                  };
        const scheduleAggregateQuery = [
            { $match: scheduleQuery },
            {
                $project: {
                    status: 1,
                    _program_id: 1,
                    session: 1,
                    year_no: 1,
                    scheduleStartDateAndTime: 1,
                    'sessionDetail.start_time': 1,
                    ...(type === DC_STUDENT && {
                        'students._id': 1,
                        'students.status': 1,
                        'students.tardisId': 1,
                        'students.primaryTime': 1,
                        'students.lateExclude': 1,
                        'students.isRestricted': 1,
                    }),
                    ...(type !== DC_STUDENT && {
                        'staffs.status': 1,
                        'staffs._staff_id': 1,
                        'students.feedBack.rating': 1,
                    }),
                },
            },
        ];
        if (type === DC_STUDENT)
            scheduleAggregateQuery.push(
                { $unwind: { path: '$students', preserveNullAndEmptyArrays: true } },
                { $match: { 'students._id': convertToMongoObjectId(userId) } },
            );
        else
            scheduleAggregateQuery.push(
                { $unwind: { path: '$staffs', preserveNullAndEmptyArrays: true } },
                { $match: { 'staffs._staff_id': convertToMongoObjectId(userId) } },
            );
        let scheduleDatasAggregate = await CourseSchedule.aggregate(scheduleAggregateQuery);
        let excludeCount = 0;
        const totalScheduleCount = scheduleDatasAggregate.length;
        if (type === DC_STUDENT) {
            scheduleDatasAggregate = scheduleDatasAggregate.filter(
                (scheduleDatasAggregateELement) =>
                    scheduleDatasAggregateELement.students.status !== EXCLUDE,
            );
            excludeCount = totalScheduleCount - scheduleDatasAggregate.length;
        }
        const feedBack = { avgRating: 0.0, sessionCount: 0, totalFeedback: 0, rating: 0 };
        if (type !== DC_STUDENT) {
            for (scheduleElement of scheduleDatasAggregate) {
                for (studentElement of scheduleElement.students) {
                    if (studentElement.feedBack && studentElement.feedBack.rating) {
                        feedBack.totalFeedback++;
                        feedBack.rating += studentElement.feedBack.rating;
                    }
                }
            }
        }
        const { totalStudentCount, maleStudentCount, femaleStudentCount } =
            await getStudentListFromStudentGroup({
                programId,
                year,
                level,
                rotationCount,
                term,
                institutionCalendarId,
                courseId,
                _institution_id,
            });

        feedBack.avgRating = feedBack.rating / feedBack.totalFeedback;
        // Student LMS
        let warningData = '';
        let studentLateAbsent = 0;
        let restrictCourseAccess = false;
        if (type === DC_STUDENT) {
            ({ labelName: warningData, restrictCourseAccess } =
                (await checkRestrictCourse({
                    _institution_id,
                    _institution_calendar_id: institutionCalendarId,
                    programId: scheduleDatasAggregate[0]?._program_id,
                    courseId,
                    yearNo: scheduleDatasAggregate[0]?.year_no,
                    levelNo: level,
                    term,
                    rotationCount,
                    userId,
                })) ?? {});
        }
        if (type === DC_STUDENT) {
            const lmsData = await lmsNewSetting({
                _institution_id,
                _institution_calendar_id: institutionCalendarId,
            });
            const { lateDurationRange, manualLateRange, manualLateData } =
                await getLateAutoAndManualRange({
                    _institution_id,
                });
            const { lateExcludeManagement } = await checkExclude({
                _institution_id,
                _institution_calendar_id: institutionCalendarId,
                courseId,
                levelNo: level,
                term,
                rotationCount,
            });
            const sgQuery = {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'groups.level': level,
                'groups.term': term,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
                ...(rotationCount && { 'groups.rotation_count': rotationCount }),
            };
            const studentGroups = await studentGroup
                .find(sgQuery, {
                    'groups.courses.student_absence_percentage': 1,
                })
                .lean();
            for (const student of studentGroups) {
                for (const groups of student.groups) {
                    if (groups.level === level && group.term === term) {
                        for (const course of groups.courses.filter(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        )) {
                            if (
                                lmsData.warningAbsenceData[0] &&
                                course.student_absence_percentage &&
                                course.student_absence_percentage !== 0
                            ) {
                                lmsData.warningAbsenceData[0].percentage =
                                    course.student_absence_percentage;
                            }
                        }
                    }
                }
            }
            const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: institutionCalendarId,
                programId: scheduleDatasAggregate.length
                    ? scheduleDatasAggregate[0]._program_id
                    : null,
                courseId,
                levelNo: level,
                term,
                rotationCount,
                lateExcludeManagement,
            });
            const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: institutionCalendarId,
                programId: scheduleDatasAggregate.length
                    ? scheduleDatasAggregate[0]._program_id
                    : null,
                courseId,
                levelNo: level,
                term,
                rotationCount,
                studentId: userId,
                lateExcludeManagement,
            }).lateExclude;
            if (!lateExclude && !lateExcludeForStudent) {
                const { studentLateAbsent: updateStudentLateAbsent } =
                    getConfigAndStudentLateAbsentForSingleStudent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        sortedStudentSessions: scheduleDatasAggregate,
                        lateExcludeManagement,
                        _institution_calendar_id: institutionCalendarId,
                        programId: scheduleDatasAggregate.length
                            ? scheduleDatasAggregate[0]._program_id
                            : null,
                        levelNo: level,
                        term,
                        courseId,
                        rotationCount,
                    });
                studentLateAbsent = updateStudentLateAbsent;
            }
        }
        // }
        const completedSchedule = scheduleDatasAggregate.filter(
            (scheduleElement) => scheduleElement.status === COMPLETED,
        );
        const courseDetailsResponse = {
            absentCount: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === ABSENT
                    : scheduleElement.staffs.status === ABSENT,
            ).length,
            attendedSessions: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === PRESENT
                    : scheduleElement.staffs.status === PRESENT,
            ).length,
            leaveCount: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === LEAVE
                    : scheduleElement.staffs.status === LEAVE,
            ).length,
            permissionCount: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === PERMISSION
                    : scheduleElement.staffs.status === PERMISSION,
            ).length,
            ondutyCount: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === ONDUTY
                    : scheduleElement.staffs.status === ONDUTY,
            ).length,
            presentCount: completedSchedule.filter((scheduleElement) =>
                type === DC_STUDENT
                    ? scheduleElement.students.status === PRESENT
                    : scheduleElement.staffs.status === PRESENT,
            ).length,
            completedSessions: completedSchedule.length,
            totalSessions: scheduleDatasAggregate.length,
            ...(type === DC_STUDENT && { warningData }),
            ...(type === DC_STUDENT && { restrictCourseAccess }),
            ...(type === DC_STUDENT && { excludeCount }),
            ...(type !== DC_STUDENT && feedBack.totalFeedback && { feedBack }),
            studentLateAbsent,
            totalStudentCount,
            maleStudentCount,
            femaleStudentCount,
        };
        console.info({ courseDetailsResponse });
        return courseDetailsResponse;
    } catch (error) {
        throw new Error(error);
    }
};

const removeUserCourseRedis = async ({ userId, institutionCalendarId }) => {
    try {
        // const userCourseRedisKey = `${USER_COURSES}:${userId}-${institutionCalendarId}`;
        const userCourseRedisKey = [];
        if (userId && institutionCalendarId)
            userCourseRedisKey.push(`${USER_COURSES}:${userId}-${institutionCalendarId}`);
        else if (userId) userCourseRedisKey.push(`${USER_COURSES}:${userId}*`);
        userCourseRedisKey.push(`${USER_INSTITUTION_CALENDAR}:${userId}`);
        return await redisClient.Client.eval(
            luaScript,
            0,
            JSON.stringify(userCourseRedisKey),
            (error, result) => {
                if (error) {
                    console.error('Error deleting keys:', error);
                    return;
                }
                console.log(`Deleted ${result} keys.`);
            },
        );
    } catch (error) {
        throw new Error(error);
    }
};

const getStaffCourseAttendance = async ({
    userId,
    courseId,
    institutionCalendarId,
    programId,
    yearNo,
    levelNo,
    term,
    rotationCount,
    _institution_id,
    group_name,
    delivery_symbol,
    session_group_id,
    gender,
    // groupNo,
    sessionGroupId,
}) => {
    try {
        const staffSchedules = await CourseSchedule.find(
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                ...(gender && gender !== 'all' && { 'student_groups.gender': gender }),
                // ...(groupNo !== 'all' && {
                //     'student_groups.session_group.group_no': {
                //         $all: groupNo.split(',').map(Number),
                //     },
                // }),
                'staffs._staff_id': convertToMongoObjectId(userId),
                term,
                _course_id: convertToMongoObjectId(courseId),
                _program_id: convertToMongoObjectId(programId),
                year_no: yearNo,
                level_no: levelNo,
                ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
                type: 'regular',
                ...(group_name && {
                    'student_groups.group_name': group_name,
                }),
                // ...(delivery_symbol !== 'all' && {
                //     'student_groups.delivery_symbol': delivery_symbol,
                // }),
                // ...(session_group_id && {
                //     'student_groups.session_group.session_group_id':
                //         convertToMongoObjectId(session_group_id),
                // }),
                ...(sessionGroupId && {
                    'student_groups.session_group.session_group_id':
                        convertToMongoObjectId(sessionGroupId),
                }),
            },
            {
                isActive: 1,
                status: 1,
                session: 1,
                course_name: 1,
                course_code: 1,
                program_name: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
                mode: 1,
                _course_id: 1,
                _program_id: 1,
                level_no: 1,
                rotation_count: 1,
                _institution_calendar_id: 1,
                scheduleStartDateAndTime: 1,
                'sessionDetail.start_time': 1,
                'students.status': 1,
                'students._id': 1,
                'students.name': 1,
                'students.tardisId': 1,
                'students.isRestricted': 1,
                'students.primaryTime': 1,
                'students.lateExclude': 1,
                'student_groups.gender': 1,
                'student_groups.session_group.group_no': 1,
                classModeType: 1,
                'student_groups.session_group._session_group_id': 1,
                _student_group_id: 1,
            },
        )
            .sort({ scheduleStartDateAndTime: 1 })
            .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
            .populate({ path: 'students._id', select: { user_id: 1 } })
            .populate({
                path: '_course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        let studentIds = [];
        if (group_name || delivery_symbol || session_group_id) {
            const masterGroup = [];
            const studentGroupData = await studentGroup.findById(
                {
                    _id: convertToMongoObjectId(staffSchedules[0]._student_group_id),
                },
                {
                    'groups.term': 1,
                    'groups.group_mode': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting': 1,
                    'groups.rotation_group_setting': 1,
                    'groups.group_setting': 1,
                },
            );
            for (groupElement of studentGroupData.groups) {
                const courseInd = groupElement.courses.findIndex(
                    (courseElement) =>
                        courseElement._course_id._id.toString() === courseId.toString(),
                );
                if (term === groupElement.term && courseInd !== -1) {
                    for (const setting of groupElement.courses[courseInd].setting) {
                        if (groupElement.group_mode !== COURSE) {
                            for (const [
                                sessionTypeIndex,
                                settingElement,
                            ] of setting.session_setting.entries()) {
                                if (
                                    delivery_symbol != null &&
                                    delivery_symbol != settingElement.session_type
                                ) {
                                    continue;
                                }
                                if (groupElement.group_mode.toString() === ROTATION.toString()) {
                                    for (const rotationGroupSettingElement of groupElement.rotation_group_setting) {
                                        if (
                                            group_name != null &&
                                            group_name != rotationGroupSettingElement.group_name
                                        ) {
                                            continue;
                                        }
                                        if (
                                            parseInt(rotationGroupSettingElement.group_no) ===
                                            parseInt(rotationCount)
                                        ) {
                                            masterGroup.push({
                                                _id: rotationGroupSettingElement._id,
                                                gender: rotationGroupSettingElement.gender,
                                                rotation: 'yes',
                                                group_no: rotationGroupSettingElement.group_no,
                                                rotation_count:
                                                    rotationGroupSettingElement.group_no,
                                                group_name: rotationGroupSettingElement.group_name,
                                                delivery_type: settingElement.delivery_type,
                                                delivery_symbol: settingElement.session_type,
                                                session_group: [],
                                            });
                                        }
                                    }
                                } else if (groupElement.group_mode.toString() === FYD.toString()) {
                                    for (const foundationGroupSettingElement of groupElement.group_setting) {
                                        for (const foundationGroupElement of foundationGroupSettingElement.groups) {
                                            if (
                                                group_name != null &&
                                                group_name != foundationGroupElement.group_name
                                            ) {
                                                continue;
                                            }
                                            masterGroup.push({
                                                _id: foundationGroupElement._id,
                                                gender: foundationGroupSettingElement.gender,
                                                group_no: foundationGroupElement.group_no,
                                                group_name: foundationGroupElement.group_name,
                                                delivery_type: settingElement.delivery_type,
                                                delivery_symbol: settingElement.session_type,
                                                session_group: [],
                                            });
                                        }
                                    }
                                }
                                const masterGroupLocation = masterGroup.findIndex(
                                    (masterGroupElement) =>
                                        masterGroupElement.group_no.toString() ===
                                            setting._group_no.toString() &&
                                        masterGroupElement.gender === setting.gender &&
                                        masterGroupElement.delivery_type ===
                                            settingElement.delivery_type &&
                                        masterGroupElement.delivery_symbol ===
                                            settingElement.session_type,
                                );
                                if (masterGroupLocation !== -1) {
                                    for (const groupsElement of setting.session_setting[
                                        sessionTypeIndex
                                    ].groups) {
                                        studentIds = [...studentIds, ...groupsElement._student_ids];
                                    }
                                }
                            }
                        } else {
                            for (settingElement of setting.session_setting) {
                                if (settingElement.delivery_type) {
                                    if (
                                        delivery_symbol != null &&
                                        delivery_symbol != settingElement.session_type
                                    ) {
                                        continue;
                                    }
                                    const group_names =
                                        setting.gender === BOTH
                                            ? 'SG-1'
                                            : setting.gender === 'male'
                                            ? 'MG-1'
                                            : 'FG-1';
                                    if (group_name != null && group_name != group_names) {
                                        continue;
                                    }
                                    for (const groupsElement of settingElement.groups) {
                                        studentIds = [...studentIds, ...groupsElement._student_ids];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        studentIds = studentIds.map((id) => id.toString());
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
            lateExcludeManagement,
        });
        const responseObject = {
            courseDetails: {
                program_name: staffSchedules[0].program_name,
                course_name: staffSchedules[0].course_name,
                course_code: staffSchedules[0].course_code,
                versionNo: staffSchedules[0]._course_id.versionNo || 1,
                versioned: staffSchedules[0]._course_id.versioned || false,
                versionName: staffSchedules[0]._course_id.versionName || '',
                versionedFrom: staffSchedules[0]._course_id.versionedFrom || null,
                versionedCourseIds: staffSchedules[0]._course_id.versionedCourseIds || [],
            },
        };
        const { warningConfig } = await lmsNewSettingWithOptions({
            _institution_calendar_id: institutionCalendarId,
            _institution_id,
            project: {
                'warningConfig._id': 1,
                'warningConfig.labelName': 1,
            },
            sort: true,
        });
        const key = `${STUDENT_WARNING_REDIS}:${institutionCalendarId.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
            rotationCount || null
        }`;
        const warningsMap = await getWarningDataFromRedis(new Map().set(key, 0));
        const warnings = warningsMap.get(key) ?? {};
        const studentWarningDetails = [];
        const warningConfigMap = new Map(
            warningConfig
                .filter((warning) => warning.isActive)
                .map((warning) => [warning._id.toString(), warning]),
        );
        for (const [warningIdElement, studentIds] of Object.entries(warnings)) {
            const warningObject = warningConfigMap.get(warningIdElement);
            if (warningObject) {
                for (const studentIdElement of studentIds) {
                    studentWarningDetails.push({
                        studentId: studentIdElement.toString(),
                        warningLabel: warningObject.labelName,
                    });
                }
            }
        }
        const attendanceHeader = [];
        const studentsData = [];
        for (scheduleElement of staffSchedules) {
            if (
                delivery_symbol != null &&
                delivery_symbol != scheduleElement.session.delivery_symbol
            ) {
                continue;
            }
            const sessionName = `${scheduleElement.session.delivery_symbol}${
                scheduleElement.session.delivery_no
            }-${scheduleElement.student_groups[0].gender.toUpperCase()}${
                scheduleElement.student_groups[0].session_group[0].group_no
            }`;
            attendanceHeader.push({
                schedule_id: scheduleElement._id,
                session: sessionName,
                s_no: scheduleElement.session.s_no,
                session_no: scheduleElement.session.delivery_no,
                delivery_symbol: scheduleElement.session.delivery_symbol,
                delivery_type: scheduleElement.session.session_type,
                session_topic: scheduleElement.session.session_topic,
                schedule_status: scheduleElement.status,
                schedule_date: scheduleElement.schedule_date,
                start: scheduleElement.start,
                end: scheduleElement.end,
                isActive: scheduleElement.isActive,
            });
            for (studentElement of scheduleElement.students) {
                if (
                    (group_name || delivery_symbol || session_group_id) &&
                    !studentIds.includes(studentElement._id._id.toString())
                ) {
                    continue;
                }
                const studentIndex = studentsData.findIndex(
                    (studentIdElement) =>
                        studentIdElement._id.toString() === studentElement._id._id.toString(),
                );
                const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: institutionCalendarId,
                    programId,
                    courseId,
                    levelNo,
                    term,
                    rotationCount,
                    lateExcludeManagement,
                    studentId: studentElement._id._id,
                }).lateExclude;
                let lateLabel = null;
                if (!lateExclude && !lateExcludeForStudent) {
                    const { lateLabel: updatedLateLabel } = getLateLabelForSchedule({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        schedule: scheduleElement,
                        student_data: studentElement,
                        _institution_calendar_id: institutionCalendarId,
                        programId,
                        courseId,
                        levelNo,
                        term,
                        rotationCount,
                        lateExcludeManagement,
                    });
                    lateLabel = updatedLateLabel;
                }
                if (studentIndex === -1) {
                    const { studentLateAbsent } = getLateConfigAndStudentLateAbsentForStaffExport({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        scheduleData: staffSchedules,
                        studentElement: { _student_id: studentElement._id._id },
                        lateExcludeManagement,
                        _institution_calendar_id: institutionCalendarId,
                        programId,
                        courseId,
                        levelNo,
                        term,
                        rotationCount,
                    });
                    const studentWarning = studentWarningDetails.find(
                        (studentWarningElement) =>
                            studentWarningElement.studentId.toString() ===
                            studentElement._id._id.toString(),
                    );
                    studentsData.push({
                        _id: studentElement._id._id,
                        name: studentElement.name,
                        academicId: studentElement._id.user_id,
                        schedules: [
                            {
                                scheduleId: scheduleElement._id,
                                status: studentElement.status,
                                isRestricted: studentElement?.isRestricted,
                                tardisId: studentElement.tardisId,
                                lateLabel: lateLabel ? lateLabel.lateLabel : null,
                                classModeType: scheduleElement.classModeType,
                            },
                        ],
                        studentLateAbsent: studentLateAbsent || 0,
                        warningLabel:
                            studentWarning && studentWarning.warningLabel
                                ? studentWarning.warningLabel
                                : '',
                    });
                } else {
                    studentsData[studentIndex].schedules.push({
                        scheduleId: scheduleElement._id,
                        status: studentElement.status,
                        isRestricted: studentElement?.isRestricted,
                        tardisId: studentElement.tardisId,
                        lateLabel: lateLabel ? lateLabel.lateLabel : null,
                        classModeType: scheduleElement.classModeType,
                    });
                }
            }
        }
        responseObject.attendanceHeader = attendanceHeader;
        responseObject.studentsData = studentsData;
        return responseObject;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const userWarningDenialStatus = async ({
    studentIds,
    courseId,
    level,
    term,
    institutionCalendarId,
    _institution_id,
    rotationCount,
}) => {
    try {
        let warningData = '';
        const lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
        });
        const scheduleDatasAggregate = await CourseSchedule.find(
            {
                ...isDeleteActive,
                'students._id': {
                    $in: studentIds.map((studentElement) =>
                        convertToMongoObjectId(studentElement._student_id),
                    ),
                },
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: level,
                term: { $regex: new RegExp(term, 'i') },
                type: REGULAR,
                ...(rotationCount && { rotation_count: parseInt(rotationCount) }),
            },
            {
                status: 1,
                'students._id': 1,
                'students.status': 1,
            },
        ).lean();

        const studentCriteriaData = await lmsDenialSchema
            .find({
                courseId: convertToMongoObjectId(courseId),
                _institution_calendar_id: institutionCalendarId,
                levelNo: level,
                term,
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 })
            .lean();
        const studentGroups = await studentGroup
            .find(
                {
                    isDeleted: false,
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'groups.level': { $regex: new RegExp(level, 'i') },
                    'groups.term': { $regex: new RegExp(term, 'i') },
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                    ...(rotationCount && { 'groups.rotation_count': rotationCount }),
                },
                {
                    'groups.courses.student_absence_percentage': 1,
                },
            )
            .lean();
        for (const studentElement of studentGroups) {
            for (const groupsElement of studentElement.groups) {
                if (
                    groupsElement.level &&
                    groupsElement.level.toLowerCase() === level.toLowerCase() &&
                    group.term &&
                    group.term.toLowerCase() === term.toLowerCase()
                ) {
                    for (const course of groupsElement.courses.filter(
                        (courseElement) =>
                            courseElement._course_id.toString() === courseId.toString(),
                    )) {
                        if (
                            lmsData.warningAbsenceData[0] &&
                            course.student_absence_percentage &&
                            course.student_absence_percentage !== 0
                        ) {
                            lmsData.warningAbsenceData[0].percentage =
                                course.student_absence_percentage;
                        }
                    }
                }
            }
        }
        const studentWarningAbsence = clone(lmsData.warningAbsenceData);
        for (const studentElement of studentIds) {
            const isMixedStudent =
                studentCriteriaData.length &&
                studentCriteriaData.find(
                    (denialStudentElement) =>
                        ((rotationCount && rotationCount > 0
                            ? parseInt(rotationCount) === denialStudentElement.rotationCount
                            : true) &&
                            denialStudentElement.term === term &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                        (denialStudentElement.term === term &&
                            denialStudentElement.studentId &&
                            denialStudentElement.courseId.toString() === courseId.toString() &&
                            denialStudentElement.studentId.toString() ===
                                studentElement._student_id.toString()),
                );
            let scheduledStudent = [];
            for (const scheduledElement of scheduleDatasAggregate) {
                const existingStudent = scheduledElement.students.find(
                    (studentIdElement) =>
                        studentIdElement._id.toString() === studentElement._student_id.toString(),
                );
                if (existingStudent) {
                    scheduledStudent.push({
                        _id: scheduledElement._id,
                        students: existingStudent,
                        status: scheduledElement.status,
                    });
                }
            }
            scheduledStudent = scheduledStudent.filter(
                (scheduledElement) => scheduledElement.students.status !== EXCLUDE,
            );
            const denialPercentage =
                (scheduledStudent.filter(
                    (scheduleElement) =>
                        scheduleElement.status === COMPLETED &&
                        (scheduleElement.students.status === ABSENT ||
                            scheduleElement.students.status === LEAVE),
                ).length /
                    scheduledStudent.length) *
                100;
            studentWarningAbsence[0].isManipulated = false;
            if (isMixedStudent) {
                if (
                    isMixedStudent &&
                    isMixedStudent.absencePercentage &&
                    studentWarningAbsence[0] &&
                    studentWarningAbsence[0].percentage &&
                    studentWarningAbsence[0].percentage < isMixedStudent.absencePercentage
                ) {
                    studentWarningAbsence[0].percentage = isMixedStudent.absencePercentage;
                    studentWarningAbsence[0].isManipulated = true;
                }
            }
            warningData =
                studentWarningAbsence[0].labelName === lmsData.denialLabel &&
                studentWarningAbsence[0].isManipulated
                    ? studentWarningAbsence.find(
                          (studentWarningElement) =>
                              studentWarningElement.percentage &&
                              parseFloat(denialPercentage) >
                                  parseFloat(studentWarningElement.percentage),
                      )
                    : lmsData.warningAbsenceData.find(
                          (warningDataElement) =>
                              warningDataElement.percentage &&
                              parseFloat(denialPercentage) >
                                  parseFloat(warningDataElement.percentage),
                      );
            if (warningData && lmsData.denialLabel === warningData.labelName) {
                studentElement.isDenial = true;
            }
        }
        return studentIds;
    } catch (error) {
        throw new Error(error);
    }
};

const getUserLevelComprehensiveWarning = async ({
    type,
    institutionCalendarId,
    programId,
    userId,
    term,
    levelNo,
}) => {
    try {
        const lmsSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    classificationType: LEAVE,
                    isDeleted: false,
                },
                {
                    // Warning mode Configuration
                    warningMode: 1,
                    warningConfigBased: 1,

                    // Comprehensive Warning Config
                    'comprehensiveWarningConfig._id': 1,
                    'comprehensiveWarningConfig.isActive': 1,
                    'comprehensiveWarningConfig.labelName': 1,
                    'comprehensiveWarningConfig.warningValue': 1,
                    'comprehensiveWarningConfig.colorCode': 1,
                    'comprehensiveWarningConfig.notificationToStudent': 1,
                    // 'comprehensiveWarningConfig.notificationToStaff': 1,
                    leaveCalculation: 1,
                },
            )
            .lean();

        if (
            !lmsSettingData ||
            (lmsSettingData.warningMode &&
                lmsSettingData.warningMode !== WARNING_CONFIG_TYPE.COMPREHENSIVE)
        )
            return {};

        // Last Warning Card Details
        const studentWarningCard = await warningMailSchema
            .findOne(
                {
                    'user.userId': convertToMongoObjectId(userId),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    programId: convertToMongoObjectId(programId),
                    levelNo,
                    term,
                },
                {
                    warningId: 1,
                    createdAt: 1,
                },
            )
            .sort({ createdAt: -1 })
            .lean();
        if (!studentWarningCard) return {};
        const studentWarning = lmsSettingData.comprehensiveWarningConfig.find(
            (warningConfigElement) =>
                String(warningConfigElement._id) === String(studentWarningCard.warningId),
        );
        const studentResponse = {
            warningLabel: studentWarning?.labelName,
            warningValue: studentWarning?.warningValue,
            warningColorCode: studentWarning?.colorCode,
            sendAt: studentWarningCard?.createdAt,
        };

        return studentResponse;
    } catch (error) {
        throw new Error(error);
    }
};

const autoLateConfigSetting = async () => {
    if (LATE_ABSENT_CONFIGURATION !== 'true') return [];
    const autoLateConfig = await lmsLateConfigSettingSchema
        .find({ mode: LATE_CONFIG, 'lateConfig.lateType': AUTO }, { lateConfig: 1 })
        .lean();
    return autoLateConfig.map(({ lateConfig, _id }) => ({
        ...lateConfig.range,
        _id: String(_id),
    }));
};

const getUserLevelComprehensiveAttendance = async ({
    type,
    institutionCalendarId,
    programId,
    userId,
    term,
    levelNo,
}) => {
    try {
        const lateConfigSetting = await autoLateConfigSetting();
        const studentSchedule = await CourseSchedule.find(
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                level_no: levelNo,
                term,
                'students._id': convertToMongoObjectId(userId),
                status: COMPLETED,
            },
            {
                'students.$': 1,
                'sessionDetail.start_time': 1,
                status: 1,
            },
        ).lean();
        const studentResponse = {
            scheduleConductedCount: studentSchedule.length,
            presentCount: 0,
            absentCount: 0,
            actualPresent: 0,
            actualAbsent: 0,
            permission: 0,
            onDuty: 0,
            leave: 0,
            lateAbsent: 0,
            excludePresent: 0,
            presentPercentage: 0,
            mark: 0,
        };
        const studentLateData = [];
        const incrementLateData = (rangeItem) => {
            const studentLateIndex = studentLateData.findIndex(({ _id }) => _id === rangeItem._id);
            if (studentLateIndex === -1) {
                studentLateData.push({ _id: rangeItem._id, late: 1 });
            } else {
                studentLateData[studentLateIndex].late++;
            }
        };
        studentSchedule.forEach(({ students, sessionDetail }) => {
            const studentAttendance = students.find(({ _id }) => String(_id) === String(userId));
            if (studentAttendance) {
                const { status, primaryTime } = studentAttendance;
                switch (status) {
                    case PRESENT:
                        studentResponse.presentCount++;
                        studentResponse.actualPresent++;
                        break;
                    case ABSENT:
                        studentResponse.absentCount++;
                        studentResponse.actualAbsent++;
                        break;
                    case ONDUTY:
                        studentResponse.presentCount++;
                        studentResponse.onDuty++;
                        break;
                    case PERMISSION:
                        studentResponse.presentCount++;
                        studentResponse.permission++;
                        break;
                    case LEAVE:
                        studentResponse.absentCount++;
                        studentResponse.leave++;
                        break;
                    default:
                        break;
                }
                if (status === PRESENT && primaryTime && sessionDetail?.start_time) {
                    const timeDifferenceInMinutes =
                        (new Date(primaryTime) - new Date(sessionDetail.start_time)) / (1000 * 60);
                    lateConfigSetting.forEach((rangeItem) => {
                        if (
                            (!rangeItem.endRange &&
                                rangeItem.startRange < timeDifferenceInMinutes) ||
                            (rangeItem.endRange &&
                                rangeItem.startRange < timeDifferenceInMinutes &&
                                rangeItem.endRange >= timeDifferenceInMinutes)
                        ) {
                            incrementLateData(rangeItem);
                        }
                    });
                }
            }
        });
        studentLateData.forEach(({ _id, late }) => {
            const lateConfig = lateConfigSetting.find(({ _id: configId }) => configId === _id);
            if (lateConfig) {
                const lateAbsent = Math.floor(late / lateConfig.noOfLate) * lateConfig.noOfAbsent;
                studentResponse.presentCount -= lateAbsent;
                studentResponse.absentCount += lateAbsent;
                studentResponse.lateAbsent += lateAbsent;
            }
        });
        studentResponse.presentPercentage =
            (studentResponse.presentCount / studentResponse.scheduleConductedCount) * 100 || 0;
        return studentResponse;
    } catch (error) {
        throw new Error(error);
    }
};

const studentGroupDeliveryExtract = ({ deliveryGroupName }) => {
    const deliverySplit = deliveryGroupName.split('-');
    return deliverySplit[deliverySplit.length - 2];
};

const getHandoutScheduleIds = async ({ courseScheduleData, _institution_id }) => {
    try {
        let handoutScheduleIds = [];
        const handoutValue = await getHandoutValue({ _institution_id });
        if (handoutValue?.handout) {
            const courseHandoutData = await courseHandoutSchema
                .find(
                    {
                        scheduleIds: {
                            $in: courseScheduleData.map((scheduleElement) =>
                                convertToMongoObjectId(scheduleElement._id),
                            ),
                        },
                        isDeleted: false,
                        isActive: true,
                    },
                    {
                        _id: 0,
                        scheduleIds: 1,
                    },
                )
                .lean();
            handoutScheduleIds = courseHandoutData.flatMap((scheduleElement) =>
                scheduleElement.scheduleIds.map((scheduleIdElement) => String(scheduleIdElement)),
            );
        }
        return handoutScheduleIds;
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = {
    getAllCourses,
    getCourse,
    getCourseIds,
    getCourseDocuments,
    getScheduleById,
    getScheduleByDate,
    getCoursesByStaffId,
    getCoursesByStudentId,
    getSloBySessionIdAndCourseId,
    getSloPlusCloBySessionIdAndCourseId,
    getStudentCourseIds,
    getFeedbackByStaff,
    getRatingBySessions,
    getInfraById,
    getCourses,
    getUserCourseLists,
    getUserCourseListsForMultiCalendar,
    getUserCourseDetails,
    removeUserCourseRedis,
    getCourseAdmin,
    getProgramName,
    getPrograms,
    getStaffCourseAttendance,
    userWarningDenialStatus,
    getStudentListFromStudentGroup,
    getSessionListFromSchedule,
    getUserLevelComprehensiveWarning,
    getUserLevelComprehensiveAttendance,
    studentGroupDeliveryExtract,
    getHandoutScheduleIds,
};
