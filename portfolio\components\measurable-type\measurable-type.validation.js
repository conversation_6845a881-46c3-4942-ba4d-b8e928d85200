const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const createMeasurableTypeSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const updateMeasurableTypeSchema = Joi.object({
    query: Joi.object({
        measurableTypeId: objectIdRQSchema,
    }),
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const deleteMeasurableTypeSchema = Joi.object({
    query: Joi.object({
        measurableTypeId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    createMeasurableTypeSchema,
    updateMeasurableTypeSchema,
    deleteMeasurableTypeSchema,
};
