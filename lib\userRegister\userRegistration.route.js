const {
    verifyEmail,
    verifyUserOTP,
    userPasswordVerification,
    getUserDetails,
    updateUserDetails,
    approvalUserList,
    userRegistrationApproveReject,
    signedURLForBiometricData,
    getDocument,
    deleteUser,
    programDetails,
    updateUserDetailInApprovalFlow,
} = require('./userRegister.controller');
const express = require('express');
const router = express.Router();
const { multipleFileUploadForUserRegistration } = require('../utility/file_upload');
const catchAsync = require('../utility/catch-async');
const { MulterError } = require('multer');
const {
    verifyEmailValidator,
    verifyUserOTPValidator,
    sendUserDataValidator,
    updateUserDetailsValidator,
    checkUserPasswordValidator,
    dataForApprovalValidator,
    dataApprovalResultValidator,
    signedURLForBiometricDataValidator,
    getDocumentValidator,
    deleteUserValidator,
    programNameValidator,
    updateUserDetailInApprovalFlowValidator,
} = require('./userRegister.validate');
const { authMiddleware } = require('../../middleware');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const multipleFileUpload = multipleFileUploadForUserRegistration.fields([
    { name: 'face', maxCount: 1 },
    { name: 'document', maxCount: 1 },
]);

router.post(
    '/verifyEmail',
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    verifyEmailValidator,
    catchAsync(verifyEmail),
);
router.get(
    '/programName',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    programNameValidator,
    catchAsync(programDetails),
);
router.post(
    '/verifyUserOTP',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    verifyUserOTPValidator,
    catchAsync(verifyUserOTP),
);
router.post(
    '/checkUserPassword',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    checkUserPasswordValidator,
    catchAsync(userPasswordVerification),
);
router.post(
    '/getUserDetails',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    sendUserDataValidator,
    catchAsync(getUserDetails),
);
router.post(
    '/updateUserDetails',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    (req, res, next) => {
        multipleFileUpload(req, res, (err) => {
            if (err instanceof MulterError) {
                return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
            }
            if (err) {
                return res
                    .status(500)
                    .send(
                        response_function(
                            res,
                            500,
                            false,
                            'Please change file format and upload',
                            err.toString(),
                        ),
                    );
            }
            next();
        });
    },
    updateUserDetailsValidator,
    catchAsync(updateUserDetails),
);
router.post(
    '/dataForApproval',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    dataForApprovalValidator,
    catchAsync(approvalUserList),
);
router.post(
    '/updateUserDetailInApprovalFlow',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateUserDetailInApprovalFlowValidator,
    catchAsync(updateUserDetailInApprovalFlow),
);
router.post(
    '/dataApprovalResult',
    authMiddleware,
    dataApprovalResultValidator,
    catchAsync(userRegistrationApproveReject),
);
router.post(
    '/signedURLForBiometricData',
    signedURLForBiometricDataValidator,
    catchAsync(signedURLForBiometricData),
);

router.post('/getDocument', authMiddleware, getDocumentValidator, catchAsync(getDocument));
router.delete(
    '/deleteUser',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    deleteUserValidator,
    catchAsync(deleteUser),
);
module.exports = router;
