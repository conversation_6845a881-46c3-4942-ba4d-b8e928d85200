const router = require('express').Router();

router.use('/component', require('../components/components-management/dynamic-component.route'));
router.use('/measurable', require('../components/measurable-type/measurable-type.route'));
router.use('/form', require('../components/form/form.route'));
router.use('/rubric', require('../components/rubric/rubric.route'));
router.use('/role', require('../components/role/role.route'));
router.use('/evaluation', require('../components/evaluation/evaluation.route'));
router.use('/course', require('../components/course/course.route'));
router.use('/portfolio', require('../components/portfolio/portfolio.route'));
router.use('/user', require('../components/user/user.route'));
router.use('/form-type', require('../components/form-type/form-type.route'));

const studentRouter = require('express').Router();

studentRouter.use('/', require('../components/student-response/student-response.route'));
studentRouter.use('/', require('../components/student/student.route'));

router.use('/student', studentRouter);
router.use('/variable', require('../components/variable/variable.route'));
router.use('/report', require('../components/report/report.route'));

module.exports = router;
