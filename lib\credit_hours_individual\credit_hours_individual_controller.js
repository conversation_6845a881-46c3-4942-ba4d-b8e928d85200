let constant = require('../utility/constants');
var credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
var program = require('mongoose').model(constant.PROGRAM);
var credit_hours_master = require('mongoose').model(constant.CREDIT_HOURS_MASTER);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const credit_hours_individual_formate = require('./credit_hours_individual_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    // let aggre = [
    //     { $match: { 'isDeleted': false } },
    //     { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
    //     { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
    //     { $skip: skips }, { $limit: limits },
    // ];

    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'credit_hours_calcs', localField: '_program_id', foreignField: '_program_id', as: 'credit_hours_calcs' } },
        {
            $addFields: {
                TTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Theory', '$$calcs.session_type']
                        }
                    }
                },
                PTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Pratical', '$$calcs.session_type']
                        }
                    }
                },
                CTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Clinical', '$$calcs.session_type']
                        }
                    }
                }
            }
        },
        { $unwind: { path: '$TTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$PTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$CTC', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                theory_total_contact_hours: {
                    $multiply: ['$theory_credit_hours', '$TTC.contact_hours']
                },
                pratical_total_contact_hours: {
                    $multiply: ['$pratical_credit_hours', '$PTC.contact_hours']
                },
                clinical_total_contact_hours: {
                    $multiply: ['$clinical_credit_hours', '$CTC.contact_hours']
                },
                total_credit_hours: {
                    $sum: ['$theory_credit_hours', '$pratical_credit_hours', '$clinical_credit_hours']
                }
            }
        },
        { $addFields: { total_contact_hours: { $sum: ['$theory_total_contact_hours', '$pratical_total_contact_hours', '$clinical_total_contact_hours'] } } },
        {
            $addFields: {
                theory_contact_hours: '$TTC.contact_hours',
                pratical_contact_hours: '$PTC.contact_hours',
                clinical_contact_hours: '$CTC.contact_hours'
            }
        },
        {
            $group: {
                _id: '$_id',
                year: {
                    $first: '$year'
                },
                level: {
                    $first: '$level'
                },
                theory_credit_hours: {
                    $first: '$theory_credit_hours'
                },
                pratical_credit_hours: {
                    $first: '$pratical_credit_hours'
                },
                clinical_credit_hours: {
                    $first: '$clinical_credit_hours'
                },
                theory_total_contact_hours: {
                    $first: '$theory_total_contact_hours'
                },
                pratical_total_contact_hours: {
                    $first: '$pratical_total_contact_hours'
                },
                clinical_total_contact_hours: {
                    $first: '$clinical_total_contact_hours'
                },
                total_credit_hours: {
                    $first: '$total_credit_hours'
                },
                total_contact_hours: {
                    $first: '$total_contact_hours'
                },
                theory_contact_hours: {
                    $first: '$theory_contact_hours'
                },
                pratical_contact_hours: {
                    $first: '$pratical_contact_hours'
                },
                clinical_contact_hours: {
                    $first: '$clinical_contact_hours'
                },
                program: {
                    $first: '$program'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }

            }
        },
        { $sort: { level: 1 } },
        { $skip: skips }, { $limit: limits },
    ];

    let doc = await base_control.get_aggregate(credit_hours_individual, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "credit_hours_individual list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_individual_formate.credit_hours_individual(doc.data));
        // common_files.list_all_response(res, 200, true, "credit_hours_individual list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_individual_formate.credit_hours_individual(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'credit_hours_calcs', localField: '_program_id', foreignField: '_program_id', as: 'credit_hours_calcs' } },
        {
            $addFields: {
                TTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Theory', '$$calcs.session_type']
                        }
                    }
                },
                PTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Pratical', '$$calcs.session_type']
                        }
                    }
                },
                CTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Clinical', '$$calcs.session_type']
                        }
                    }
                }
            }
        },
        { $unwind: { path: '$TTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$PTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$CTC', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                theory_total_contact_hours: {
                    $multiply: ['$theory_credit_hours', '$TTC.contact_hours']
                },
                pratical_total_contact_hours: {
                    $multiply: ['$pratical_credit_hours', '$PTC.contact_hours']
                },
                clinical_total_contact_hours: {
                    $multiply: ['$clinical_credit_hours', '$CTC.contact_hours']
                },
                total_credit_hours: {
                    $sum: ['$theory_credit_hours', '$pratical_credit_hours', '$clinical_credit_hours']
                }
            }
        },
        { $addFields: { total_contact_hours: { $sum: ['$theory_total_contact_hours', '$pratical_total_contact_hours', '$clinical_total_contact_hours'] } } },
        {
            $addFields: {
                theory_contact_hours: '$TTC.contact_hours',
                pratical_contact_hours: '$PTC.contact_hours',
                clinical_contact_hours: '$CTC.contact_hours'
            }
        },
        {
            $group: {
                _id: '$_id',
                year: {
                    $first: '$year'
                },
                level: {
                    $first: '$level'
                },
                theory_credit_hours: {
                    $first: '$theory_credit_hours'
                },
                pratical_credit_hours: {
                    $first: '$pratical_credit_hours'
                },
                clinical_credit_hours: {
                    $first: '$clinical_credit_hours'
                },
                theory_total_contact_hours: {
                    $first: '$theory_total_contact_hours'
                },
                pratical_total_contact_hours: {
                    $first: '$pratical_total_contact_hours'
                },
                clinical_total_contact_hours: {
                    $first: '$clinical_total_contact_hours'
                },
                total_credit_hours: {
                    $first: '$total_credit_hours'
                },
                total_contact_hours: {
                    $first: '$total_contact_hours'
                },
                theory_contact_hours: {
                    $first: '$theory_contact_hours'
                },
                pratical_contact_hours: {
                    $first: '$pratical_contact_hours'
                },
                clinical_contact_hours: {
                    $first: '$clinical_contact_hours'
                },
                program: {
                    $first: '$program'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }

            }
        },
        { $sort: { level: 1 } }
    ];
    let doc = await base_control.get_aggregate(credit_hours_individual, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "credit_hours_individual details", /* doc.data */credit_hours_individual_formate.credit_hours_individual_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "credit_hours_individual details", doc.data/* credit_hours_individual_formate.credit_hours_individual_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let docs = { status: true };
    let program_ids = [];
    let status, datas, credit_check = false;
    let doc_update = { status: true };
    await req.body.data.forEach(element => {
        if (program_ids.indexOf(element._program_id) == -1) {
            program_ids.push(element._program_id);
        }
    });
    let checks = await base_control.check_id(program, { _id: { $in: program_ids }, 'isDeleted': false });
    if (checks.status) {
        // console.log(program_ids);
        // console.log(checks.data);
        // console.log(checks.data[0]._id);
        let aggre = [
            { $match: { '_program_id': ObjectId(checks.data[0]._id) } },
            { $match: { 'isDeleted': false } },
            { $sort: { year: 1 } },
            { $project: { year: 1, credit_hours: 1 } }
        ];
        let total_credits = await base_control.get_aggregate(credit_hours_master, aggre);
        // console.log(1, total_credits);
        // console.log('sender data ',req.body.data);
        await total_credits.data.forEach(async (total_credits_doc, index) => {
            let tpc_total_credits = 0;
            await req.body.data.forEach(async (user_doc, index) => {
                if (total_credits_doc.year == user_doc.year) {
                    tpc_total_credits += (user_doc.theory_credit_hours + user_doc.pratical_credit_hours + user_doc.clinical_credit_hours);
                }
            });
            // console.log(2, tpc_total_credits);
            // console.log(3, total_credits_doc.credit_hours);
            if (total_credits_doc.credit_hours == tpc_total_credits) {
                credit_check = true;
            } else {
                credit_check = false;
                return;
            }
        });
        // console.log(credit_check);
        if (credit_check) {
            await req.body.data.forEach(async (doc, index) => {
                let objects = {
                    year: doc.year,
                    level: doc.level,
                    theory_credit_hours: doc.theory_credit_hours,
                    pratical_credit_hours: doc.pratical_credit_hours,
                    clinical_credit_hours: doc.clinical_credit_hours,
                    rotation: doc.rotation,
                    _program_id: doc._program_id
                };
                if (doc.id == '' && doc.id.length == 0) {
                    docs = await base_control.insert(credit_hours_individual, objects);
                    if (docs.status && doc_update.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                } else {
                    docs = await base_control.update(credit_hours_individual, doc.id, objects);
                    if (docs.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                }
                if (req.body.data.length == index + 1) {
                    if (status) {
                        common_files.com_response(res, 201, true, "credit_hours_individual Added successfully", docs);
                    } else {
                        common_files.com_response(res, 500, false, "Error", docs);
                    }
                }
            });
        } else {
            common_files.com_response(res, 500, false, "Error Credit Hours Not match", 'Check Parshing Total Credit Hours Not match');
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;

        //Check with master credit hours before updateing
        // let aggre = [
        //     { $match: { '_id': ObjectId(object_id) } },
        //     { $match: { 'isDeleted': false } },
        //     // { $project: { year: 1, credit_hours: 1 } }
        // ];
        // let total_credits = await base_control.get_aggregate(credit_hours_individual, aggre);

        // await total_credits.data.forEach(async (total_credits_doc) => {
        //     let tpc_total_credits = 0;
        //     await req.body.data.forEach(async (user_doc) => {
        //         if (total_credits_doc.year == user_doc.year) {
        //             tpc_total_credits += (user_doc.theory_credit_hours + user_doc.pratical_credit_hours + user_doc.clinical_credit_hours);
        //         }
        //     });
        //     if (total_credits_doc.credit_hours == tpc_total_credits) {
        //         credit_check = true;
        //     } else {
        //         credit_check = false;
        //         return;
        //     }
        // });
        // console.log(credit_check);
        // if (credit_check) {


        let doc = await base_control.update(credit_hours_individual, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "credit_hours_individual update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(credit_hours_individual, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "credit_hours_individual deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else {
            proj = {};
        }

        let doc = await base_control.get_list(credit_hours_individual, query, proj);
        console.log(doc);
        if (doc.status) {
            common_files.com_response(res, 200, true, "credit_hours_individual List", credit_hours_individual_formate.credit_hours_individual_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.year_level = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $sort: { year: 1, level: 1 } },
        { $project: { year: 1, level: 1, rotation: 1 } }
    ];
    let last = 0;
    let objs = [];
    let doc = await base_control.get_aggregate(credit_hours_individual, aggre);
    // console.log(doc);
    doc.data.forEach(element1 => {
        if (last != element1.year) {
            last = element1.year;
            let temp = { year: (element1.year).toString(), level: [] };
            doc.data.forEach(element => {
                if (element.year == element1.year) {
                    temp.level.push(element.level.toString());
                    // console.log(element.level);
                }
            });
            // console.log(temp);
            objs.push(temp);
        }
        // console.log('new');
    });
    if (doc.status) {
        common_files.com_response(res, 200, true, "Credit hours individual year level lists", objs);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.level = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $sort: { level: 1 } },
        { $project: { level: 1 } }
    ];
    let last = 0;
    let objs = [];
    let doc = await base_control.get_aggregate(credit_hours_individual, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "Credit hours individual year level lists", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: 'credit_hours_calcs', localField: '_program_id', foreignField: '_program_id', as: 'credit_hours_calcs' } },
        {
            $addFields: {
                TTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Theory', '$$calcs.session_type']
                        }
                    }
                },
                PTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Practical', '$$calcs.session_type']
                        }
                    }
                },
                CTC: {
                    $filter: {
                        input: '$credit_hours_calcs',
                        as: 'calcs',
                        cond: {
                            $eq: ['Clinical', '$$calcs.session_type']
                        }
                    }
                }
            }
        },
        { $unwind: { path: '$TTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$PTC', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$CTC', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                theory_total_contact_hours: {
                    $multiply: ['$theory_credit_hours', '$TTC.contact_hours']
                },
                pratical_total_contact_hours: {
                    $multiply: ['$pratical_credit_hours', '$PTC.contact_hours']
                },
                clinical_total_contact_hours: {
                    $multiply: ['$clinical_credit_hours', '$CTC.contact_hours']
                },
                total_credit_hours: {
                    $sum: ['$theory_credit_hours', '$pratical_credit_hours', '$clinical_credit_hours']
                }
            }
        },
        { $addFields: { total_contact_hours: { $sum: ['$theory_total_contact_hours', '$pratical_total_contact_hours', '$clinical_total_contact_hours'] } } },
        {
            $addFields: {
                theory_contact_hours: '$TTC.contact_hours',
                pratical_contact_hours: '$PTC.contact_hours',
                clinical_contact_hours: '$CTC.contact_hours'
            }
        },
        {
            $group: {
                _id: '$_id',
                year: {
                    $first: '$year'
                },
                level: {
                    $first: '$level'
                },
                rotation: {
                    $first: '$rotation'
                },
                theory_credit_hours: {
                    $first: '$theory_credit_hours'
                },
                pratical_credit_hours: {
                    $first: '$pratical_credit_hours'
                },
                clinical_credit_hours: {
                    $first: '$clinical_credit_hours'
                },
                theory_total_contact_hours: {
                    $first: '$theory_total_contact_hours'
                },
                pratical_total_contact_hours: {
                    $first: '$pratical_total_contact_hours'
                },
                clinical_total_contact_hours: {
                    $first: '$clinical_total_contact_hours'
                },
                total_credit_hours: {
                    $first: '$total_credit_hours'
                },
                total_contact_hours: {
                    $first: '$total_contact_hours'
                },
                theory_contact_hours: {
                    $first: '$theory_contact_hours'
                },
                pratical_contact_hours: {
                    $first: '$pratical_contact_hours'
                },
                clinical_contact_hours: {
                    $first: '$clinical_contact_hours'
                },
                program: {
                    $first: '$program'
                },
                isActive: {
                    $first: '$isActive'
                },
                isDeleted: {
                    $first: '$isDeleted'
                }
            }
        },
        { $sort: { level: 1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(credit_hours_individual, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "credit_hours_individual list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_individual_formate.credit_hours_individual(doc.data));
        common_files.list_all_response(res, 200, true, "credit_hours_individual list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_individual_formate.credit_hours_individual(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};