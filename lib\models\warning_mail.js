const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const { WARNING_MAIL, DIGI_COURSE, USER, DIGI_PROGRAM } = require('../utility/constants');

const warningMailSchemas = new Schemas(
    {
        sendDate: { type: Date },
        courseId: {
            type: Schemas.Types.ObjectId,
            ref: DIGI_COURSE,
        },
        course_name: {
            type: String,
        },
        course_code: {
            type: String,
        },
        yearNo: {
            type: String,
        },
        levelNo: {
            type: String,
        },
        term: {
            type: String,
        },
        programId: {
            type: Schemas.Types.ObjectId,
            ref: DIGI_PROGRAM,
        },
        program_name: {
            type: String,
        },
        institutionCalendarId: {
            type: Schemas.Types.ObjectId,
        },
        warningId: {
            type: Schemas.Types.ObjectId,
        },
        userIds: [
            {
                type: Schemas.Types.ObjectId,
                ref: USER,
            },
        ],
        user: [
            {
                userId: { type: Schemas.Types.ObjectId, ref: USER },
                name: {
                    first: {
                        type: String,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
                user_id: { type: String },
                percentage: { type: Number },
                presentPercentage: { type: Number },
                acknowledge: {
                    type: Boolean,
                    default: false,
                },
                acknowledgedData: { type: Date },
                notificationInfo: {
                    isSent: { type: Boolean, default: false },
                    notifyToCourseStaff: { type: Boolean, default: false },
                    sentBy: { type: String },
                    sentDate: { type: Date },
                },
            },
        ],
        staffs: [
            {
                _id: {
                    type: Schemas.Types.ObjectId,
                },
                sendStudents: [
                    {
                        type: Schemas.Types.ObjectId,
                    },
                ],
                sendDate: { type: Date },
            },
        ],
        rotationCount: {
            type: Number,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        allowedUser: [
            {
                userId: {
                    type: Schemas.Types.ObjectId,
                    ref: USER,
                },
                updatedPercentage: Number,
                _id: false,
            },
        ],
    },
    { timestamps: true },
);
module.exports = mongoose.model(WARNING_MAIL, warningMailSchemas);
