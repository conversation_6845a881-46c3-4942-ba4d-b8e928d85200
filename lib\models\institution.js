const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const institution = new Schema(
    {
        type: {
            type: String,
            //required: true
        },
        name: {
            type: String,
            //required: true
        },
        code: {
            type: String,
            //required: true
        },
        logo: {
            type: String,
            //required: true,
        },
        accreditation: {
            type: String,
            //required: true,
        },
        parent_institute: {
            type: String,
        },
        timezone: {
            type: String,
            //required: true,
        },
        language_preference: {
            type: String,
            //required: true,
        },
        about: {
            type: String,
        },
        vision: {
            type: String,
        },
        mission: {
            type: String,
        },
        extra_title: [
            {
                title: String,
                content: String,
            },
        ],
        address: {
            type: String,
            //required: true,
        },
        location: {
            type: String,
        },
        _country_id: {
            type: Schema.Types.ObjectId,
            ref: constant.COUNTRY,
            //required: true
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        bleStaff: { type: String },
        bleStudent: { type: String },
        anomalyRestrict: { type: Boolean, default: false },
        anomalyPassKey: { type: String, default: false },
        manualAnomaly: { type: String, default: false },
        lateConfig: {
            lateAbsenceThreshold: { type: Boolean, default: false },
            minuteRange: { type: Number },
            label: { type: String },
        },
        facePassKey: { type: String, default: false },
        userRegistrationFacePassKey: { type: String },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.INSTITUTION, institution);
