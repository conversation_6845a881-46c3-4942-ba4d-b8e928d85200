require('dotenv').config();
const logger = require('./pino.config');
module.exports = {
    APP_STATE: process.env.NODE_ENV || 'development',

    // Super Admin
    ADMIN_USER: process.env.ADMIN_USER,
    ADMIN_PASS: process.env.ADMIN_PASS,

    // AWS KEY & Bucket
    AWS_ACCESS_KEY_V2: process.env.AWS_ACCESS_KEY_V2,
    AWS_SECRET_KEY_V2: process.env.AWS_SECRET_KEY_V2,
    AWS_SECRET_BUCKET_NAME_V2: process.env.AWS_SECRET_BUCKET_NAME_V2,

    BUCKET_PATH: 'ibnsina.digiproducts',
    AWS_ACCESS_KEY: process.env.AWS_ACCESS_KEY,
    AWS_SECRET_KEY: process.env.AWS_SECRET_KEY,
    AWS_REGION: process.env.AWS_REGION,
    AWS_APP_ID: process.env.AWS_APP_ID,
    AWS_PUSH_APP_ID: process.env.AWS_PUSH_APP_ID,
    AWS_SENDER_ID: process.env.AWS_SENDER_ID,
    AWS_CHARSET: process.env.AWS_CHARSET,
    SENDER_ADDRESS: process.env.SENDER_ADDRESS,
    BUCKET_NAME: process.env.AWS_BUCKET_NAME,
    BUCKET_NAME_DOC: process.env.AWS_BUCKET_NAME_DOC,
    BUCKET_NAME_USER_DATA: process.env.AWS_BUCKET_NAME_USER_DATA,
    BUCKET_DOCUMENT: process.env.AWS_BUCKET_DOCUMENT,
    AWS_URL: process.env.AWS_URL,

    //OUR-SMS Saudi SMS service
    SMS_USERNAME: process.env.SMS_USERNAME,
    SMS_PASSWORD: process.env.SMS_PASSWORD,
    SMS_SENDER: process.env.SMS_SENDER,
    SMS_RETURN: process.env.SMS_RETURN,

    JWT_SECRET_KEY: process.env.JWT_SECRET_KEY || 'digischedulerfrZmDcOIT8', // JWT secret key
    apiKey: process.env.API_KEY || 'fHUuioTyELUantbGA7O4qP7NkY6JMdE8', // api key

    RANDOM_PASSWORD_LENGTH: process.env.RANDOM_PASSWORD_LENGTH || 8,

    OTP_EXPIRY_DURATION_IN_SECS: process.env.OTP_EXPIRY_DURATION_IN_SECS || 300,

    UNIFONIC_APP_SID: process.env.UNIFONIC_KEY || 'XxKK7NiIU0K0YIWegFyQve2Lgu9We',

    // nodemailer
    // EMAIL_USERNAME: process.env.EMAIL_USERNAME || 'digischeduler',
    // EMAIL_PASSWORD: process.env.EMAIL_PASSWORD || 'Digival@16',
    EMAIL_USERNAME: process.env.EMAIL_USERNAME || 'ibnsinanationalcollege',
    EMAIL_PASSWORD: process.env.EMAIL_PASSWORD || 'fmi9!44kQE3p@UZ',
    EMAIL_SENDER_ADDRESS:
        process.env.EMAIL_SENDER_ADDRESS || '"DigiScheduler "<<EMAIL>>',

    SENDGRID_API_KEY: '*********************************************************************',
    // SENDGRID_API_KEY: '*********************************************************************',

    // Slack Chat Service Key - OTP Service.
    SLACK_AUTH_TOKEN:
        '*******************************************************************************',
    SLACK_CHANNEL: process.env.SLACK_CHANNEL || 'ds-v2-email',

    MONGO_DB: process.env.dbUrl,
    MONGO_DB_V2: process.env.dbUrlV2,

    FE_URL: process.env.FE_URL || 'https://staging.digischeduler.digivalsolutions.com',
    SIGN_UP_URL: process.env.SIGN_UP_URL,
    STAFF_SIGN_UP_URL: process.env.STAFF_SIGN_UP_URL,
    logger,
    LOCAL_TIMEZONE: 'Asia/Calcutta',
    TIMEZONE: 'Asia/Riyadh',
    FCM_SECRET_KEY: process.env.FCM_SECRET_KEY,
    CRYPTO_KEY: process.env.CRYPTO_KEY || '',
    DSCRONKEY: process.env.DSCRONKEY || '',
    ADMIN_DB_URI: process.env.ADMIN_DB_URI,
    USER_INSTITUTIONS: 'user_institutions',
    ENABLE_SAAS: process.env.ENABLE_SAAS,

    SERVICES: {
        FACE_AUTH_TYPE: process.env.FACE_AUTH_TYPE || 'mobile',
        LABEL_CHANGE: process.env.LABEL_CHANGE || 'true',
        CHAT_ENABLED: process.env.CHAT_ENABLED || 'false',
        GENDER_MERGE: process.env.GENDER_MERGE || 'true',
        MOBILE: process.env.MOBILE || 'false',
        DOCUMENT: process.env.DOCUMENT || 'false',
        ATTAINMENT_MODULE: process.env.ATTAINMENT_MODULE || 'false',
        REACT_APP_ENVIRONMENT: process.env.REACT_APP_ENVIRONMENT || 'ecs-indian',
        REACT_APP_COLLEGE_NAME: process.env.REACT_APP_COLLEGE_NAME || 'Digival Institute',
        REACT_APP_COUNTRY_CODE: process.env.REACT_APP_COUNTRY_CODE || '91',
        REACT_APP_COUNTRY_CODE_LENGTH: process.env.REACT_APP_COUNTRY_CODE_LENGTH || '10',
        REACT_APP_ROTATION_LEVEL: process.env.REACT_APP_ROTATION_LEVEL || '[9,10]',
        REACT_APP_ACTIVE_VERSION: process.env.REACT_APP_ACTIVE_VERSION || 'v1',
        REACT_APP_INSTITUTION_ID:
            process.env.REACT_APP_INSTITUTION_ID || '5e5d0f1a15b4d600173d5692',
    },

    DEEP_LINK_BASE_URL: process.env.DEEP_LINK_BASE_URL || '',
    DEEP_LINK_FIREBASE_URL: process.env.DEEP_LINK_FIREBASE_URL || '',
    DEEP_LINK_DC_ANDROID_BUNDLE_ID: process.env.DEEP_LINK_DC_ANDROID_BUNDLE_ID || '',
    DEEP_LINK_DC_IOS_BUNDLE_ID: process.env.DEEP_LINK_DC_IOS_BUNDLE_ID || '',
    DEEP_LINK_DC_APP_STORE_ID: process.env.DEEP_LINK_DC_APP_STORE_ID || '',
    DEEP_LINK_SOCIAL_TITLE: process.env.DEEP_LINK_SOCIAL_TITLE || '',
    DEEP_LINK_SOCIAL_DESCRIPTION: process.env.DEEP_LINK_SOCIAL_DESCRIPTION || '',
    DEEP_LINK_SOCIAL_IMAGE: process.env.DEEP_LINK_SOCIAL_IMAGE || '',
    ELASTIC_CLIENT_URL: process.env.ELASTIC_CLIENT_URL || '',
    ELASTIC_CLIENT_USER_NAME: process.env.ELASTIC_CLIENT_USER_NAME || '',
    ELASTIC_CLIENT_PASSWORD: process.env.ELASTIC_CLIENT_PASSWORD || '',

    DIGIVAL_CLOUD_PROVIDER: process.env.DIGIVAL_CLOUD_PROVIDER || 'AWS',
    OCI_REGION: process.env.OCI_REGION || '',
    OCI_AWS_S3_API: process.env.OCI_AWS_S3_API || '',
    OCI_ACCESS_KEY_ID: process.env.OCI_ACCESS_KEY_ID || '',
    OCI_SECRET_ACCESS_KEY: process.env.OCI_SECRET_ACCESS_KEY || '',
};
