const express = require('express');
const route = express.Router();
const controller = require('./controller');
const validator = require('./validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get('/:id', validator.impact_mapping_type_id, controller.get);
route.get('/', [userPolicyAuthentication(['program_input:programs:view'])], controller.getAll);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'program_input:programs:add_program',
            'program_input:programs:add_pre-requisite',
        ]),
    ],
    validator.impact_mapping_type,
    controller.insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'program_input:programs:add_program',
            'program_input:programs:add_pre-requisite',
        ]),
    ],
    validator.impact_mapping_type_id,
    validator.impact_mapping_type,
    controller.update,
);
route.delete(
    '/:id',
    [
        userPolicyAuthentication([
            'program_input:programs:add_program',
            'program_input:programs:add_pre-requisite',
        ]),
    ],
    validator.impact_mapping_type_id,
    controller.delete,
);

module.exports = route;
