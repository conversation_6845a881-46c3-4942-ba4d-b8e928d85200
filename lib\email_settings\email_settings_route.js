const catchAsync = require('../utility/catch-async');
const {
    createNewEmailContent,
    getEmailContent,
    updateEmailContent,
    resetEmailContent,
} = require('./email_settings_controller');
const {
    updateEmailContentValidator,
    getEmailContentValidator,
} = require('./email_settings_validator');

const route = require('express').Router();

route.post('/createEmailContent', updateEmailContentValidator, catchAsync(createNewEmailContent));
route.get('/getEmailContent', getEmailContentValidator, catchAsync(getEmailContent));
route.put('/updateEmailContent', updateEmailContentValidator, catchAsync(updateEmailContent));
route.put('/resetEmailContent', getEmailContentValidator, catchAsync(resetEmailContent));

module.exports = route;
