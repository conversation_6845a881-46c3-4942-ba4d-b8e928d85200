const mongoose = require('mongoose');
const { Schema } = mongoose;

const { INSTITUTION } = require('../../../utility/constants');
const { moduleSchema } = require('../module/module.schema');
const { convertToMongoObjectId } = require('../../../utility/common');

const roleSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _parent_id: {
            type: Schema.Types.ObjectId,
            default: null,
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
        modules: [moduleSchema],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        permission: {
            isSuperAdmin: {
                type: Boolean,
                default: false,
            },
            isUniversityAdmin: {
                type: Boolean,
                default: false,
            },
            isEditable: {
                type: Boolean,
                default: true,
            },
        },
    },
    { timestamps: true },
);

roleSchema.query.findAllRoles = function ({ _institution_id = '', _parent_id = '' }) {
    return this.find({
        ...(_institution_id && { _institution_id: convertToMongoObjectId(_institution_id) }),
        ...(_parent_id && { _parent_id: convertToMongoObjectId(_parent_id) }),
        'permission.isSuperAdmin': false,
        'permission.isUniversityAdmin': false,
        isDeleted: false,
        isActive: true,
    });
};

module.exports = {
    roleSchema,
};
