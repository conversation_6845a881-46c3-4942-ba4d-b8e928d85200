const digiSurveyBankSchema = require('./digiSurveyBank.model');
const surveySchemas = require('../../models/digiSurvey');
const programCalendarSchema = require('../../models/program_calendar');
const userModulePermissionSchema = require('../../user_module_permission/userModulePermission.model');
const userSurveyTypeListSchema = require('./digiUserSurveyTypeList.model');
const { convertToMongoObjectId } = require('../../utility/common');
const {
    ACTIVE,
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    DS_INSTITUTION_KEY,
    TEMPLATE,
    GENERAL,
    ALL,
    ALL_PROGRAM,
    ALL_COURSE,
    SPECIFIC_PROGRAM,
    SPECIFIC_COURSE,
    PUBLISHED,
    DS_DELETE,
    DS_UPDATE,
    DS_CURRICULUM_KEY,
    DS_LEVEL_KEY,
    INSTITUTION_CALENDAR_KEY,
    ROTATION: { YES, NO },
    PUBLISH_TIMING: { NOW, LATER },
    UPCOMING,
    INPROGRESS,
    PUBLISH,
    EXPIRE,
    DS_TERM_KEY,
    DS_YEAR_KEY,
    DRAFT,
} = require('../../utility/constants');
const {
    USER_PERMISSION_MODULE_NAMES: { SURVEY },
} = require('../../utility/util_keys');
const {
    getUserPermissionProgramAndCourseIds,
    generateFrameWorkQuestions,
    cloneSurveyTemplate,
    getSelectedProgramUserDetails,
    getSelectedProgramWithCourseDetails,
} = require('./digiSurveyBank.service');
const { activateRedisTimer, getExpireTimeInSeconds } = require('../../utility/utility.service');
const {
    allProgramList,
    singleProgramDetail,
    ploDetails,
    cloDetails,
} = require('../../utility/programInput.helper');
const { getPaginationValues } = require('../../../commonService/utility/pagination');
const { getSurveyUserListFromRedis } = require('../digiSurvey/digiSurvey.service');

exports.getSurveyBankList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { publishLevel, userId } = query;
        const { limit, skip } = getPaginationValues(query);
        const {
            userProgramIds = [],
            userCurriculumIds = [],
            userYearLevelIds = [],
            userCourseIds = [],
        } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: ALL,
        });
        //get only isEdited:false data because those are updated data..true is maintained as old template (which is need for existing published survey)
        let surveyBankFindQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isEdited: false,
            isDeleted: false,
        };
        const commonCondition = {
            surveyType: TEMPLATE,
            status: ACTIVE,
        };
        const constructedCourseLevelPublishQuery = {
            publishFor: SPECIFIC_COURSE,
            'courseIds.overAllCourseIds.programId': { $in: userProgramIds },
            'courseIds.overAllCourseIds.curriculumName': {
                $in: userCurriculumIds,
            },
            'courseIds.overAllCourseIds.yearNo': {
                $in: userYearLevelIds.map(
                    (userYearLevelIdElement) => userYearLevelIdElement.split('-')[0],
                ),
            },
            'courseIds.overAllCourseIds.levelNo': {
                $in: userYearLevelIds.map(
                    (userYearLevelIdElement) => userYearLevelIdElement.split('-')[1],
                ),
            },
            'courseIds.overAllCourseIds.courseId': {
                $in: userCourseIds,
            },
        };
        switch (publishLevel) {
            case ALL:
                surveyBankFindQuery = {
                    ...surveyBankFindQuery,
                    $or: [
                        {
                            createdBy: convertToMongoObjectId(userId),
                            surveyType: GENERAL,
                        },
                        {
                            ...commonCondition,
                            publishTo: DS_INSTITUTION_KEY,
                        },
                        {
                            ...commonCondition,
                            publishFor: { $in: [ALL_PROGRAM, ALL_COURSE] },
                        },
                        {
                            ...commonCondition,
                            publishFor: SPECIFIC_PROGRAM,
                            programIds: { $in: userProgramIds },
                        },
                        {
                            ...commonCondition,
                            ...constructedCourseLevelPublishQuery,
                        },
                    ],
                };
                break;
            case DS_INSTITUTION_KEY:
                surveyBankFindQuery = {
                    ...surveyBankFindQuery,
                    ...commonCondition,
                    publishTo: DS_INSTITUTION_KEY,
                };
                break;
            case DS_PROGRAM_KEY:
                surveyBankFindQuery = {
                    ...surveyBankFindQuery,
                    ...commonCondition,
                    $or: [
                        {
                            publishFor: ALL_PROGRAM,
                        },
                        { publishFor: SPECIFIC_PROGRAM, programIds: { $in: userProgramIds } },
                    ],
                };
                break;
            case DS_COURSE_KEY:
                surveyBankFindQuery = {
                    ...surveyBankFindQuery,
                    ...commonCondition,
                    $or: [
                        {
                            publishFor: ALL_COURSE,
                        },
                        {
                            ...constructedCourseLevelPublishQuery,
                        },
                    ],
                };
                break;
            default:
                surveyBankFindQuery = {
                    ...surveyBankFindQuery,
                    createdBy: convertToMongoObjectId(userId),
                    surveyType: GENERAL,
                };
        }
        const activeSurveyBankList = await digiSurveyBankSchema
            .find(surveyBankFindQuery, {
                _id: 0,
                surveyBankId: '$_id',
                surveyType: 1,
                surveyName: 1,
                surveyDescription: 1,
                createdBy: 1,
                publishTo: 1,
                programIds: 1,
                courseIds: 1,
                learningOutcome: 1,
                tags: 1,
                kpi: 1,
                questions: 1,
                publishFor: 1,
                learningOutcomeDetails: 1,
                status: 1,
            })
            .skip(skip)
            .limit(limit)
            .populate([
                { path: 'programIds', select: { name: 1, code: 1 } },
                {
                    path: 'courseIds.overAllCourseIds.courseId',
                    select: { course_name: 1, course_code: 1 },
                },
            ])
            .sort({ _id: -1 })
            .lean();
        return {
            statusCode: 200,
            message: 'ACTIVE_TEMPLATE_LIST',
            data: activeSurveyBankList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSurveyTemplate = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, isTemplate, surveyLevel, isTotalCountNeeded, isRunner, searchKey } = query;
        const { limit, skip } = getPaginationValues(query);
        let totalDocument = 0;
        if (isTotalCountNeeded === 'true') {
            totalDocument = await digiSurveyBankSchema.countDocuments({
                _institution_id: convertToMongoObjectId(_institution_id),
                isEdited: false,
                isDeleted: false,
                ...(isRunner === 'true' && { status: ACTIVE }),
                ...(isTemplate === 'true'
                    ? { isTemplate: true }
                    : { createdBy: convertToMongoObjectId(userId) }),
                ...(surveyLevel && { surveyLevel }),
                ...(searchKey &&
                    searchKey.trim() !== '' && {
                        surveyName: { $regex: searchKey, $options: 'i' },
                    }),
                ...(isRunner === 'false' && { createdBy: convertToMongoObjectId(userId) }),
            });
        }
        //get only isEdited:false data because those are updated data..true is maintained as old template (which is need for existing published survey)
        let surveyTemplateList = [];
        surveyTemplateList = await digiSurveyBankSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isEdited: false,
                    isDeleted: false,
                    ...(isRunner === 'true' && { status: ACTIVE }),
                    ...(isTemplate === 'true'
                        ? { isTemplate: true }
                        : { createdBy: convertToMongoObjectId(userId) }),
                    ...(surveyLevel && { surveyLevel }),
                    ...(searchKey &&
                        searchKey.trim() !== '' && {
                            surveyName: { $regex: searchKey, $options: 'i' },
                        }),
                    ...(isRunner === 'false' && { createdBy: convertToMongoObjectId(userId) }),
                },
                {
                    surveyName: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    surveyType: 1,
                    questions: 1,
                    status: 1,
                    cloneName: 1,
                    learningOutcome: 1,
                },
            )
            .skip(skip)
            .limit(limit)
            .sort({ updatedAt: -1 })
            .lean();
        if (surveyTemplateList && surveyTemplateList.length) {
            surveyTemplateList = surveyTemplateList.map((surveyTemplateListElement) => {
                const noOfQuestions =
                    surveyTemplateListElement.questions && surveyTemplateListElement.questions.pages
                        ? surveyTemplateListElement.questions.pages.reduce((acc, curr) => {
                              return acc + (curr.elements ? curr.elements.length : 0);
                          }, 0)
                        : 0;
                const { questions, ...rest } = surveyTemplateListElement;
                return {
                    ...rest,
                    noOfQuestions,
                };
            });
        }
        return {
            statusCode: 200,
            message: 'USER_CREATED_TEMPLATE',
            data: { surveyTemplateList, totalDocument },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getDraftSurvey = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { isTemplate, surveyLevel, isTotalCountNeeded, searchKey, institutionCalendarId } =
            query;
        const { limit, skip } = getPaginationValues(query);
        let totalDocument = 0;
        if (isTotalCountNeeded === 'true') {
            totalDocument = await surveySchemas.countDocuments({
                _institution_id: convertToMongoObjectId(_institution_id),
                institutionCalendarId: convertToMongoObjectId(institutionCalendarId),

                isDeleted: false,
                surveyLevel,
                status: DRAFT,
                createdBy: convertToMongoObjectId(_user_id),
                ...(isTemplate === 'true' ? { isTemplate: true } : { isTemplate: false }),
                ...(searchKey &&
                    searchKey.trim() !== '' && {
                        surveyName: { $regex: searchKey, $options: 'i' },
                    }),
            });
        }
        const draftSurveyList = await surveySchemas
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    isDeleted: false,
                    surveyLevel,
                    status: DRAFT,
                    createdBy: convertToMongoObjectId(_user_id),
                    ...(isTemplate === 'true' ? { isTemplate: true } : { isTemplate: false }),
                    ...(searchKey &&
                        searchKey.trim() !== '' && {
                            surveyName: { $regex: searchKey, $options: 'i' },
                        }),
                },
                {
                    surveyName: 1,
                    surveyVersionName: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    surveyType: 1,
                    noOfQuestion: 1,
                    status: 1,
                    learningOutcome: 1,
                },
            )
            .skip(skip)
            .limit(limit)
            .sort({ updatedAt: -1 })
            .populate({ path: 'surveyBankId', select: { cloneName: 1 } })
            .lean();
        return {
            statusCode: 200,
            message: 'DRAFT_SURVEY_LIST',
            data: { draftSurveyList, totalDocument },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAllProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programSearchKey } = query;
        const programList = await allProgramList({
            institutionId: _institution_id,
            searchKey: programSearchKey,
        });
        return {
            statusCode: 200,
            message: 'TOTAL_PROGRAM_LIST',
            data: programList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSingleProgramDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        const constructedProgramDetail = await singleProgramDetail({
            institutionId: _institution_id,
            programId,
        });
        return {
            statusCode: 200,
            message: 'SINGLE_PROGRAM_DETAIL',
            data: constructedProgramDetail,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createNewSurvey = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            isTemplate,
            surveyName,
            surveyDescription,
            createdBy,
            surveyLevel,
            surveyType,
            learningOutcome,
            outcomeSectionPlacement,
            ratingScaleConfiguration,
            manageQuestionTags,
            mapLearningOutcomes,
            status,
            questions,
            isEdited,
            existingSurveyBankId,
            cloneName,
            cloneId,
        } = body;
        if (cloneId) {
            return await cloneSurveyTemplate({ cloneId, cloneName });
        }
        const createObj = {
            _institution_id,
            isTemplate,
            surveyName,
            surveyDescription,
            createdBy,
            surveyLevel,
            surveyType,
            learningOutcome,
            outcomeSectionPlacement,
            ratingScaleConfiguration,
            manageQuestionTags,
            mapLearningOutcomes,
            status,
            questions,
            isEdited: false,
        };
        if (isEdited && existingSurveyBankId) {
            const isTemplateSurveyConducted = await surveySchemas.findOne({
                surveyBankId: convertToMongoObjectId(existingSurveyBankId),
            });
            if (!isTemplateSurveyConducted) {
                const updateSurveyTemplate = await digiSurveyBankSchema.updateOne(
                    {
                        _id: convertToMongoObjectId(existingSurveyBankId),
                    },
                    createObj,
                );
                return {
                    statusCode: updateSurveyTemplate.modifiedCount ? 201 : 400,
                    message: updateSurveyTemplate.modifiedCount
                        ? 'SURVEY_TEMPLATE_CREATED_SUCCESSFULLY'
                        : 'UNABLE_TO_CREATE_SURVEY_SUCCESSFULLY',
                };
            }
            await digiSurveyBankSchema.updateOne(
                {
                    _id: convertToMongoObjectId(existingSurveyBankId),
                },
                {
                    $set: { isEdited },
                },
            );
        }
        const createdSurveyTemplate = await digiSurveyBankSchema.create(createObj);
        return {
            statusCode: createdSurveyTemplate ? 201 : 400,
            message: createdSurveyTemplate
                ? 'SURVEY_TEMPLATE_CREATED_SUCCESSFULLY'
                : 'UNABLE_TO_CREATE_SURVEY_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateSurveyTemplate = async ({ body = {} }) => {
    try {
        //only status update and isDeleted is handled here..template over all edit is handled in createNewSurvey api
        const { updateType, status, updateId } = body;
        let updateQuery = {};
        const findQuery = { _id: convertToMongoObjectId(updateId) };
        switch (updateType) {
            case DS_DELETE:
                updateQuery = { $set: { isDeleted: true } };
                break;
            case DS_UPDATE:
                updateQuery = { $set: { status } };
                break;
            default:
                break;
        }
        const updatedSurveyTemplate = await digiSurveyBankSchema.updateOne(findQuery, updateQuery);
        let response = { statusCode: 200, message: 'SURVEY_TEMPLATE_CHANGES_UPDATED_SUCCESSFULLY' };
        if (!updatedSurveyTemplate.modifiedCount) {
            response = { statusCode: 400, message: 'UNABLE_TO_UPDATE_SURVEY_TEMPLATE_CHANGES' };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSingleSurveyTemplate = async ({ query = {} }) => {
    try {
        const { surveyBankId } = query;
        const singleSurveyTemplate = await digiSurveyBankSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyBankId),
                },
                {
                    surveyName: 1,
                    surveyDescription: 1,
                    createdBy: 1,
                    surveyLevel: 1,
                    surveyType: 1,
                    learningOutcome: 1,
                    outcomeSectionPlacement: 1,
                    ratingScaleConfiguration: 1,
                    manageQuestionTags: 1,
                    mapLearningOutcomes: 1,
                    status: 1,
                    questions: 1,
                    cloneName: 1,
                    existingSelectedCourseId: 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'SINGLE_SURVEY_TEMPLATE',
            data: singleSurveyTemplate || {},
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, institutionCalendarId } = query;
        const { userProgramDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: DS_PROGRAM_KEY,
            institutionCalendarId,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_PROGRAM_LIST',
            data: userProgramDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionCurriculumList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, userId, institutionCalendarId } = query;
        const { userCurriculumDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: DS_CURRICULUM_KEY,
            programId,
            institutionCalendarId,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_CURRICULUM_LIST',
            data: userCurriculumDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionTermList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, userId, institutionCalendarId } = query;
        const { userTermDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: DS_TERM_KEY,
            programId,
            institutionCalendarId,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_TERM_LIST',
            data: userTermDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionYearList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, userId, institutionCalendarId, term } = query;
        const { userYearDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: DS_YEAR_KEY,
            programId,
            institutionCalendarId,
            term,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_YEAR_LIST',
            data: userYearDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionLevelList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, userId, institutionCalendarId, term, yearNo } = query;
        const { userLevelDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: DS_LEVEL_KEY,
            programId,
            institutionCalendarId,
            term,
            yearNo,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_LEVEL_LIST',
            data: userLevelDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionCourseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { institutionCalendarId, programId, curriculumName, term, yearNo, levelNo, userId } =
            query;
        const { userCourseDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            institutionCalendarId,
            requiredId: DS_COURSE_KEY,
            programId,
            term,
            curriculumName,
            yearNo,
            levelNo,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_COURSE_LIST',
            data: userCourseDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getPloDetails = async ({ query = {} }) => {
    try {
        const { programId, curriculumName } = query;
        const programFrameworkDetails = await ploDetails({ programId, curriculumName });
        return {
            statusCode: 200,
            message: 'PLO_DETAILS',
            data: programFrameworkDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCloDetails = async ({ query }) => {
    try {
        const { courseId } = query;
        const courseFrameworkDetails = await cloDetails({ courseId });
        return {
            statusCode: 200,
            message: 'CLO_DETAILS',
            data: [courseFrameworkDetails],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPermissionCalendarList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId } = query;
        const { userInstitutionCalendarDetails = [] } = await getUserPermissionProgramAndCourseIds({
            institutionId: _institution_id,
            userId,
            requiredId: INSTITUTION_CALENDAR_KEY,
        });
        return {
            statusCode: 200,
            message: 'USER_PERMISSION_CALENDAR_LIST',
            data: userInstitutionCalendarDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserSelectedProgramDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            userId,
            institutionCalendarId,
            programId,
            curriculumName,
            term,
            yearNo,
            levelNo,
            courseId,
            rotationCount,
        } = query;
        const selectedProgramWithCourseDetails = await getSelectedProgramWithCourseDetails({
            _institution_id,
            userId,
            institutionCalendarId,
            programId,
            curriculumName,
            term,
            yearNo,
            levelNo,
            courseId,
            rotationCount,
        });
        return {
            statusCode: 200,
            message: 'SINGLE_PROGRAM_DETAIL',
            data: selectedProgramWithCourseDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.publishSurvey = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            programId,
            term,
            curriculumName,
            yearNo,
            levelNo,
            courseId,
            createdBy,
            surveyBankId,
            isTemplate,
            surveyType,
            surveyName,
            surveyDescription,
            surveyVersionName,
            surveyLevel,
            noOfQuestion,
            status,
            publishDate,
            expiryDate,
            questions,
            surveyUsers,
            outComeDetails,
            userResponseSettings,
            surveyPublicUserDetails,
            selectedParticipants,
            checkedParticipants,
            isMandator,
            isEdit,
            isReconduct,
            existingSurveyId,
            learningOutcome,
            mappedOutcomeDetails,
            isDraft,
            mappedTagDetails,
            multiSelectedProgramIds,
            existingSelectedCourseId,
        } = body;
        const currentDateAndTime = new Date();
        const expireDateAndTime = new Date(expiryDate);
        if (!isDraft && status === NOW && expireDateAndTime <= currentDateAndTime) {
            return {
                statusCode: 200,
                message: 'EXPIRE_DATE_MESSAGE',
            };
        }
        const surveyDataFields = {
            _institution_id,
            institutionCalendarId,
            programId,
            term,
            curriculumName,
            yearNo,
            levelNo,
            courseId,
            createdBy,
            surveyBankId,
            isTemplate,
            surveyType,
            surveyName,
            surveyDescription,
            surveyVersionName,
            surveyLevel,
            noOfQuestion,
            publishDate: new Date(publishDate),
            expiryDate: new Date(expiryDate),
            questions,
            surveyUsers,
            outComeDetails,
            userResponseSettings,
            surveyPublicUserDetails,
            selectedParticipants,
            checkedParticipants,
            isMandator,
            status: isDraft ? DRAFT : status === LATER ? UPCOMING : INPROGRESS,
            learningOutcome,
            mappedOutcomeDetails,
            mappedTagDetails,
            multiSelectedProgramIds,
            existingSelectedCourseId,
        };
        let publishedSurveyId;
        if (
            ((isEdit || isDraft) && existingSurveyId) ||
            (!isReconduct && !isEdit && !isDraft && existingSurveyId)
        ) {
            // if its edited , updated changes in same survey
            publishedSurveyId = existingSurveyId;
            const updatedSurveyData = await surveySchemas.updateOne(
                { _id: convertToMongoObjectId(existingSurveyId) },
                surveyDataFields,
            );
            if (!updatedSurveyData.modifiedCount) {
                response = { statusCode: 400, message: 'UNABLE_TO_PUBLISH_SURVEY' };
            }
            await getSurveyUserListFromRedis({ surveyId: existingSurveyId, redisType: DS_UPDATE });
        } else {
            if (isReconduct && existingSurveyId) {
                // if its reConducted , delete existing survey detail and create as new
                const updatedSurveyData = await surveySchemas.updateOne(
                    { _id: convertToMongoObjectId(existingSurveyId) },
                    { isDeleted: true },
                );
                if (!updatedSurveyData.modifiedCount) {
                    response = { statusCode: 400, message: 'UNABLE_TO_RECONDUCT_SURVEY' };
                }
            }
            // normal creation flow
            const publishedSurveyData = await surveySchemas.create(surveyDataFields);
            if (!publishedSurveyData) {
                response = { statusCode: 400, message: 'UNABLE_TO_PUBLISH_SURVEY' };
            }
            publishedSurveyId = publishedSurveyData._id;
        }

        if (!isDraft && status === NOW) {
            const expireTimeInSeconds = getExpireTimeInSeconds({
                endDateAndTime: expireDateAndTime,
                startDateAndTime: currentDateAndTime,
            });
            if (expireTimeInSeconds > 0) {
                await activateRedisTimer({
                    moduleName: SURVEY,
                    uniqueId: publishedSurveyId,
                    documentStatus: EXPIRE,
                    timeInSeconds: expireTimeInSeconds,
                });
            }
        } else if (!isDraft && status === LATER) {
            const publishedDateAndTime = new Date(publishDate);
            const publishTimeInSeconds = getExpireTimeInSeconds({
                endDateAndTime: publishedDateAndTime,
                startDateAndTime: currentDateAndTime,
            });
            const expireTimeInSeconds = getExpireTimeInSeconds({
                endDateAndTime: expireDateAndTime,
                startDateAndTime: currentDateAndTime,
            });
            if (publishTimeInSeconds > 0 && expireTimeInSeconds > 0) {
                await activateRedisTimer({
                    moduleName: SURVEY,
                    uniqueId: publishedSurveyId,
                    documentStatus: PUBLISH,
                    timeInSeconds: publishTimeInSeconds,
                });
                await activateRedisTimer({
                    moduleName: SURVEY,
                    uniqueId: publishedSurveyId,
                    documentStatus: EXPIRE,
                    timeInSeconds: expireTimeInSeconds,
                });
            }
        }
        return { statusCode: 201, message: 'SURVEY_PUBLISHED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createUserSurveyTypeList = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userSurveyTypeList } = body;
        const surveyTypeList = await userSurveyTypeListSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { $set: { userSurveyTypeList } },
            { upsert: true },
        );
        const isUpdateSuccessfully =
            surveyTypeList.modifiedCount ||
            surveyTypeList.upsertedCount ||
            surveyTypeList.matchedCount;
        return {
            statusCode: isUpdateSuccessfully ? 201 : 400,
            message: isUpdateSuccessfully
                ? 'USER_SURVEY_TYPE_LIST_UPDATED_SUCCESSFULLY'
                : 'UNABLE_TO_UPDATE_USER_SURVEY_TYPE_LIST',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserSurveyTypeList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const surveyTypeList = await userSurveyTypeListSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            { userSurveyTypeList: 1 },
        );
        return {
            statusCode: 200,
            message: 'USER_SURVEY_TYPE_LIST',
            data: surveyTypeList ? surveyTypeList.userSurveyTypeList : [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getTotalUserList = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            userId,
            institutionCalendarId,
            programId,
            curriculumName,
            term,
            yearNo,
            levelNo,
            courseId,
            rotationCount,
            selectedUserGroups,
        } = body;
        const selectedProgramWithCourseDetails = await getSelectedProgramWithCourseDetails({
            _institution_id,
            userId,
            institutionCalendarId,
            programId,
            curriculumName,
            term,
            yearNo,
            levelNo,
            courseId,
            rotationCount,
        });
        const totalUserList = await getSelectedProgramUserDetails({
            institutionCalendarId,
            programId,
            programDetails: selectedProgramWithCourseDetails,
            selectedUserGroups,
        });
        return {
            statusCode: 200,
            message: 'TOTAL_USER_LIST',
            data: totalUserList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
