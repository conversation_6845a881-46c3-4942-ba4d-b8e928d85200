const assignmentSettings = require('./assignment-settings.model');
const assignmentPromptSchema = require('../assignment-prompt/assignment-prompt.model');
const assignmentSchema = require('../assignment/assignment.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { getSizeOfUrl } = require('./assignment-settings.util');
const { getSubjectList } = require('../course/course.service');
const { getSignedURL } = require('../../../../lib/utility/common_functions');

const checkCourseAndSessionDataExist = [
    'After Session Begins',
    'After Session Ends',
    'After Course Starts',
    'Before Course Ends',
];

const startEndDueDateConstruction = async ({ dateField }) => {
    const currentDate = new Date();
    if (
        dateField.type === '' ||
        dateField.type === 'Date' ||
        !(
            checkCourseAndSessionDataExist.includes(dateField.type) ||
            checkCourseAndSessionDataExist.includes(dateField.duration.type)
        )
    ) {
        dateField.dateAndTime = currentDate;
    }
    return dateField;
};

const createExistAssignmentData = async ({
    term,
    type,
    year,
    level,
    _course_id,
    _program_id,
    _institution_id,
    _institution_calendar_id,
    new_institution_calendar_id,
}) => {
    try {
        if (
            !(
                year &&
                term &&
                type &&
                level &&
                _course_id &&
                _program_id &&
                _institution_id &&
                _institution_calendar_id &&
                new_institution_calendar_id
            )
        )
            return { isValid: false, message: `Error in recurring ${type}` };
        const assignmentIds = {};
        const promptIds = [];
        const withoutPromptAssignmentData = [];
        const promptDataBulkWrite = [];
        const promptAssignmentData = [];
        const filter = {
            term,
            isActive: true,
            isDeleted: false,
            level_no: level,
            year_no: year,
            'basic.type': type,
            'submission.assignTo.isAllStudent': true,
            _course_id: convertToMongoObjectId(_course_id),
            _program_id: convertToMongoObjectId(_program_id),
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        };
        const removeFields = {
            createdAt: 0,
            updatedAt: 0,
            publishedAt: 0,
            evaluation: 0,
            'submission.assignTo': 0,
            'basic.sessionUnit': 0,
            'basic.learningOutcome': 0,
            _institution_calendar_id: 0,
        };
        const existingAssignment = await assignmentSchema.find(filter, removeFields).lean();
        if (existingAssignment.length === 0)
            return { isValid: false, message: `No ${type} in the selected year level term` };
        for (const assignmentElement of existingAssignment) {
            const assignmentNewObjectId = convertToMongoObjectId();
            assignmentIds[assignmentElement._id] = {
                existingDocId: assignmentElement._id,
                newDocId: assignmentNewObjectId,
            };
            assignmentElement._institution_calendar_id = convertToMongoObjectId(
                new_institution_calendar_id,
            );
            assignmentElement._id = assignmentNewObjectId;
            assignmentElement.isDraft = true;
            assignmentElement.isRecurAssignment = true;
            assignmentElement.isActive = false;

            assignmentElement.submission.start = await startEndDueDateConstruction({
                dateField: assignmentElement.submission.start,
            });
            assignmentElement.submission.end = await startEndDueDateConstruction({
                dateField: assignmentElement.submission.end,
            });
            assignmentElement.submission.due = await startEndDueDateConstruction({
                dateField: assignmentElement.submission.due,
            });
            if (assignmentElement.basic.prompts.length) {
                promptAssignmentData.push({ insertOne: { document: assignmentElement } });
                promptIds.push(...assignmentElement.basic.prompts);
            } else {
                withoutPromptAssignmentData.push({ insertOne: { document: assignmentElement } });
            }
        }

        const newPromptData = {};
        if (promptIds.length) {
            const convertedPromptId = promptIds.map((item) => convertToMongoObjectId(item));
            const assignmentPromptData = await assignmentPromptSchema
                .find({ _id: { $in: convertedPromptId } })
                .lean();
            if (assignmentPromptData.length === 0)
                return { isValid: false, message: `Error in recurring ${type}` };
            for (const individualPromptObject of assignmentPromptData) {
                const promptAssignmentId = individualPromptObject.assignmentId.toString();
                const existAssignmentObject = assignmentIds[individualPromptObject.assignmentId];
                if (promptAssignmentId === existAssignmentObject.existingDocId.toString()) {
                    const assignmentPromptNewObjectId = convertToMongoObjectId();
                    individualPromptObject.assignmentId = existAssignmentObject.newDocId;
                    individualPromptObject._id = assignmentPromptNewObjectId;
                    promptDataBulkWrite.push({
                        insertOne: {
                            document: individualPromptObject,
                        },
                    });
                    if (!newPromptData[existAssignmentObject.newDocId]) {
                        newPromptData[existAssignmentObject.newDocId] = [
                            assignmentPromptNewObjectId,
                        ];
                    } else {
                        newPromptData[existAssignmentObject.newDocId].push(
                            assignmentPromptNewObjectId,
                        );
                    }
                }
            }

            for (const bulkObject of promptAssignmentData) {
                const bulkObjectId = bulkObject.insertOne.document._id.toString();
                if (newPromptData[bulkObjectId]) {
                    bulkObject.insertOne.document.basic.prompts = newPromptData[bulkObjectId];
                }
            }

            const createAssignmentPrompt = await assignmentPromptSchema.bulkWrite(
                promptDataBulkWrite,
            );
            if (!createAssignmentPrompt)
                return { isValid: false, message: `Error in recurring ${type}` };
        }

        const bulkOperations = [...withoutPromptAssignmentData, ...promptAssignmentData];

        const createAssignment = await assignmentSchema.bulkWrite(bulkOperations);
        if (!createAssignment) return { isValid: false, message: `Error in recurring ${type}` };
        return { isValid: true };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.addProgramSettings = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { programId, gradingSystem, gamification } = body;
        const checkAssignmentSettings = await assignmentSettings
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'programSettings.programId': convertToMongoObjectId(programId),
                },
                { _id: 1, programSettings: 1 },
            )
            .lean();
        let assignmentUpdate;
        if (checkAssignmentSettings) {
            assignmentUpdate = await assignmentSettings.updateOne(
                { _id: convertToMongoObjectId(checkAssignmentSettings._id) },
                {
                    $set: {
                        programSettings: {
                            ...(gradingSystem && {
                                gradingSystem,
                            }),
                            ...(gamification && {
                                gamification,
                            }),
                            programId: checkAssignmentSettings.programSettings.programId,
                        },
                    },
                },
            );
        } else {
            const insertObject = {
                _institution_id,
                _institution_calendar_id,
                programSettings: {
                    programId,
                    ...(gradingSystem && {
                        gradingSystem,
                    }),
                    ...(gamification && {
                        gamification,
                    }),
                },
            };
            assignmentUpdate = await assignmentSettings.create(insertObject);
        }
        if (!assignmentUpdate) return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getProgramSettings = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { programId } = query;
        const AssignmentSettings = await assignmentSettings
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'programSettings.programId': convertToMongoObjectId(programId),
            })
            .lean();
        if (
            AssignmentSettings &&
            AssignmentSettings.programSettings &&
            AssignmentSettings.programSettings.gamification &&
            AssignmentSettings.programSettings.gamification.creditPointsFor &&
            AssignmentSettings.programSettings.gamification.creditPointsFor.badges &&
            AssignmentSettings.programSettings.gamification.creditPointsFor.badges.badge.length > 0
        ) {
            for (const badgeData of AssignmentSettings.programSettings.gamification.creditPointsFor
                .badges.badge) {
                badgeData.signedUrl = await getSignedURL(badgeData.badgeIcon);
            }
        }

        if (!AssignmentSettings || !AssignmentSettings.programSettings)
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: AssignmentSettings.programSettings,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.addCourseSettings = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { programId, courseId, general, evaluation, plagiarismAndCollusion } = body;
        const checkAssignmentSettings = await assignmentSettings
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'courseSettings.programId': convertToMongoObjectId(programId),
                    'courseSettings.courseId': convertToMongoObjectId(courseId),
                },
                { _id: 1, courseSettings: 1 },
            )
            .lean();
        let assignmentUpdate;
        if (
            checkAssignmentSettings &&
            checkAssignmentSettings.courseSettings.programId &&
            checkAssignmentSettings.courseSettings.courseId
        ) {
            assignmentUpdate = await assignmentSettings.updateOne(
                { _id: convertToMongoObjectId(checkAssignmentSettings._id) },
                {
                    $set: {
                        ...(general && { 'courseSettings.general': general }),
                        ...(evaluation && { 'courseSettings.evaluation': evaluation }),
                        ...(plagiarismAndCollusion && {
                            'courseSettings.plagiarismAndCollusion': plagiarismAndCollusion,
                        }),
                    },
                },
            );
        } else {
            const insertObject = {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                courseSettings: {
                    ...(general && {
                        general,
                    }),
                    ...(evaluation && {
                        evaluation,
                    }),
                    ...(plagiarismAndCollusion && {
                        plagiarismAndCollusion,
                    }),
                    programId: convertToMongoObjectId(programId),
                    courseId: convertToMongoObjectId(courseId),
                },
            };
            assignmentUpdate = await assignmentSettings.create(insertObject);
        }

        if (
            general &&
            general.summative &&
            general.summative.copyAssignmentFromPrevious &&
            general.summative.copyAssignmentFromPrevious.isCopy === true
        ) {
            const { isValid, message } = await createExistAssignmentData({
                ...general.summative.copyAssignmentFromPrevious,
                type: 'Summative Assignment',
                _institution_id,
                new_institution_calendar_id: _institution_calendar_id,
            });
            if (!isValid)
                return {
                    message,
                    statusCode: 410,
                };
        }
        if (
            general &&
            general.formative &&
            general.formative.copyAssignmentFromPrevious &&
            general.formative.copyAssignmentFromPrevious.isCopy === true
        ) {
            const { isValid, message } = await createExistAssignmentData({
                ...general.formative.copyAssignmentFromPrevious,
                type: 'Formative Assignment',
                _institution_id,
                new_institution_calendar_id: _institution_calendar_id,
            });
            if (!isValid)
                return {
                    message,
                    statusCode: 410,
                };
        }
        if (!assignmentUpdate) return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCourseSettings = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { programId, courseId } = query;
        const AssignmentSettings = await assignmentSettings
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'courseSettings.programId': convertToMongoObjectId(programId),
                'courseSettings.courseId': convertToMongoObjectId(courseId),
            })
            .lean();
        if (!AssignmentSettings || !AssignmentSettings.courseSettings)
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: AssignmentSettings.courseSettings,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.addSubjectSettings = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            programId,
            courseId,
            subjectId,
            departmentId,
            general,
            evaluation,
            plagiarismAndCollusion,
        } = body;
        const checkAssignmentSettings = await assignmentSettings
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'subjectSettings.programId': convertToMongoObjectId(programId),
                    'subjectSettings.courseId': convertToMongoObjectId(courseId),
                    'subjectSettings.subjectId': convertToMongoObjectId(subjectId),
                    'subjectSettings.departmentId': convertToMongoObjectId(departmentId),
                },
                { _id: 1, subjectSettings: 1 },
            )
            .lean();
        let assignmentUpdate;
        if (checkAssignmentSettings) {
            assignmentUpdate = await assignmentSettings.updateOne(
                { _id: convertToMongoObjectId(checkAssignmentSettings._id) },
                {
                    $set: {
                        ...(general && { 'subjectSettings.general': general }),
                        ...(evaluation && { 'subjectSettings.evaluation': evaluation }),
                        ...(plagiarismAndCollusion && {
                            'subjectSettings.plagiarismAndCollusion': plagiarismAndCollusion,
                        }),
                        'subjectSettings.programId':
                            checkAssignmentSettings.subjectSettings.programId,
                        'subjectSettings.courseId':
                            checkAssignmentSettings.subjectSettings.courseId,
                        'subjectSettings.subjectId':
                            checkAssignmentSettings.subjectSettings.subjectId,
                        'subjectSettings.departmentId':
                            checkAssignmentSettings.subjectSettings.departmentId,
                    },
                },
            );
        } else {
            const insertObject = {
                _institution_id,
                _institution_calendar_id,
                subjectSettings: {
                    ...(general && {
                        general,
                    }),
                    ...(evaluation && {
                        evaluation,
                    }),
                    ...(plagiarismAndCollusion && {
                        plagiarismAndCollusion,
                    }),
                    programId,
                    courseId,
                    subjectId,
                    departmentId,
                },
            };
            assignmentUpdate = await assignmentSettings.create(insertObject);
        }
        if (!assignmentUpdate) return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getSubjectSettings = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { programId, courseId, departmentId, subjectId } = query;
        const AssignmentSettings = await assignmentSettings
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'subjectSettings.programId': convertToMongoObjectId(programId),
                'subjectSettings.courseId': convertToMongoObjectId(courseId),
                'subjectSettings.subjectId': convertToMongoObjectId(subjectId),
                'subjectSettings.departmentId': convertToMongoObjectId(departmentId),
            })
            .lean();
        const getSubjectLists = await getSubjectList({ _institution_id, courseId });
        const subjectIds = getSubjectLists.map((i) => convertToMongoObjectId(i.subjectId));
        const AssignmentCourseSettings = await assignmentSettings
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'courseSettings.programId': convertToMongoObjectId(programId),
                    'courseSettings.courseId': convertToMongoObjectId(courseId),
                },
                {
                    'courseSettings.general.summative.totalAssignmentMarks.value': 1,
                    'courseSettings.general.summative.assignmentLimit': 1,
                    'courseSettings.general.formative.totalAssignmentMarks.value': 1,
                    'courseSettings.general.formative.assignmentLimit': 1,
                },
            )
            .lean();

        const AssignmentSubjectSettings = await assignmentSettings
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'subjectSettings.programId': convertToMongoObjectId(programId),
                    'subjectSettings.courseId': convertToMongoObjectId(courseId),
                    'subjectSettings.subjectId': { $in: subjectIds },
                },
                {
                    'subjectSettings.general.summative.totalAssignmentMarks.value': 1,
                    'subjectSettings.general.summative.assignmentLimit': 1,
                    'subjectSettings.general.formative.totalAssignmentMarks.value': 1,
                    'subjectSettings.general.formative.assignmentLimit': 1,
                },
            )
            .lean();
        let totalSummativeMarks = 0;
        let totalSummativeLimit = 0;
        let totalFormativeMarks = 0;
        let totalFormativeLimit = 0;
        if (AssignmentSubjectSettings.length) {
            AssignmentSubjectSettings.forEach((setting) => {
                if (setting.subjectSettings.general) {
                    totalSummativeMarks +=
                        setting.subjectSettings.general?.summative?.totalAssignmentMarks?.value ||
                        0;
                    totalFormativeMarks +=
                        setting.subjectSettings.general?.formative?.totalAssignmentMarks?.value ||
                        0;
                    totalSummativeLimit +=
                        setting.subjectSettings.general?.summative?.assignmentLimit || 0;
                    totalFormativeLimit +=
                        setting.subjectSettings.general?.formative?.assignmentLimit || 0;
                }
            });
        }
        if (!AssignmentSettings || !AssignmentSettings.subjectSettings) {
            return {
                statusCode: 200,
                message: 'NO_DATA_FOUND',
                data: {
                    remainingMarks: {
                        totalSummativeMarks:
                            AssignmentCourseSettings.courseSettings.general.summative
                                .totalAssignmentMarks.value - totalSummativeMarks,
                        totalFormativeMarks:
                            AssignmentCourseSettings.courseSettings.general.formative
                                .totalAssignmentMarks.value - totalFormativeMarks,
                        totalSummativeLimit:
                            AssignmentCourseSettings.courseSettings.general.summative
                                .assignmentLimit - totalSummativeLimit,
                        totalFormativeLimit:
                            AssignmentCourseSettings.courseSettings.general.formative
                                .assignmentLimit - totalFormativeLimit,
                    },
                },
            };
        }
        AssignmentSettings.subjectSettings.remainingMarks = {
            totalSummativeMarks:
                AssignmentCourseSettings.courseSettings.general.summative.totalAssignmentMarks
                    .value - totalSummativeMarks,
            totalFormativeMarks:
                AssignmentCourseSettings.courseSettings.general.formative.totalAssignmentMarks
                    .value - totalFormativeMarks,
            totalSummativeLimit:
                AssignmentCourseSettings.courseSettings.general.summative.assignmentLimit -
                totalSummativeLimit,
            totalFormativeLimit:
                AssignmentCourseSettings.courseSettings.general.formative.assignmentLimit -
                totalFormativeLimit,
        };

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: AssignmentSettings.subjectSettings,
        };
    } catch (error) {
        console.log('error', error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.documentUpload = async (req) => {
    try {
        const { file } = req;
        const updatedFiles = [];
        for (const fileElement of file) {
            updatedFiles.push({
                url: fileElement,
                signedUrl: await getSignedURL(fileElement),
                size: await getSizeOfUrl(fileElement),
                name: fileElement.split('/').pop(),
            });
        }
        return {
            statusCode: 200,
            data: updatedFiles,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleDocumentUpload = async ({ body = {} }) => {
    try {
        const { file } = body;
        const name = file.split('/').pop();
        return {
            statusCode: 200,
            data: {
                url: file,
                signedUrl: await getSignedURL(file),
                size: await getSizeOfUrl(file),
                name,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.activeImageUrl = async ({ query = {} }) => {
    try {
        const { url } = query;
        return {
            statusCode: 200,
            data: {
                signedUrl: await getSignedURL(url),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
