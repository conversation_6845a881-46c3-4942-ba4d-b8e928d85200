const countrySchema = require('./country.model');
const { getModel, getCountryCodes } = require('../../utility/common');
const { COUNTRY } = require('../../utility/constants');

const getCountries = async ({ headers = {} }) => {
    const { tenantURL } = headers;
    const countriesModel = getModel(tenantURL, COUNTRY, countrySchema);
    const countries = await countriesModel.find({}).sort({ name: 1 }).lean();
    return { statusCode: 200, data: countries };
};

const listCodes = async () => {
    return { statusCode: 200, data: getCountryCodes() };
};

module.exports = { getCountries, listCodes };
