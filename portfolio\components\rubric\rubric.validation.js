const { Joi } = require('../../common/middlewares/validation');
const { POINT, PERCENTAGE } = require('../../common/utils/enums');

// Common schema
const objectId = Joi.string().hex().length(24);

const parameterSchema = Joi.object({
    name: Joi.string().required().messages({
        'any.required': 'PLEASE_PROVIDE_RUBRIC_PARAMETERS_NAME',
        'string.empty': 'PLEASE_PROVIDE_RUBRIC_PARAMETERS_NAME',
    }),
    _id: objectId.optional().messages({
        'string.length': 'PLEASE_PROVIDE_PARAMETERS_ID',
    }),
});

const createRubricSchema = Joi.object({
    body: Joi.object({
        _id: objectId.optional().messages({
            'string.length': 'PLEASE_PROVIDE_RUBRIC_ID',
        }),
        name: Joi.string().required().messages({
            'any.required': 'PLEASE_PROVIDE_RUBRIC_NAME',
            'string.empty': 'PLEASE_PROVIDE_RUBRIC_NAME',
        }),
        desc: Joi.string().optional().allow(''),
        scoring: Joi.string().valid(POINT, PERCENTAGE).required().messages({
            'any.only': 'PLEASE_PROVIDE_RUBRIC_SCORE',
            'any.required': 'PLEASE_PROVIDE_RUBRIC_SCORE',
            'string.empty': 'PLEASE_PROVIDE_RUBRIC_SCORE',
        }),
        isGlobal: Joi.boolean().optional(),
        isTemplate: Joi.boolean().optional(),
        parameters: Joi.array().items(parameterSchema).required(),
    }),
}).unknown(true);

const getRubricsSchema = Joi.object({
    query: Joi.object({
        rubricId: objectId.optional(),
    }),
}).unknown(true);

const updateRubricSchema = Joi.object({
    query: Joi.object({
        rubricId: objectId.required(),
    }),
    body: Joi.object({
        _id: objectId.optional().messages({
            'string.length': 'PLEASE_PROVIDE_RUBRIC_ID',
        }),
        name: Joi.string().optional(),
        desc: Joi.string().optional().allow(''),
        scoring: Joi.string().valid(POINT, PERCENTAGE).optional().messages({
            'any.only': 'PLEASE_PROVIDE_RUBRIC_SCORE',
        }),
        isGlobal: Joi.boolean().optional(),
        isTemplate: Joi.boolean().optional(),
        parameters: Joi.array().items(parameterSchema).optional(),
    }),
}).unknown(true);

const deleteRubricSchema = Joi.object({
    query: Joi.object({
        rubricId: objectId.required(),
    }),
}).unknown(true);

const createGlobalRubricSchema = Joi.object({
    body: Joi.object({
        _id: objectId.optional().messages({
            'string.length': 'PLEASE_PROVIDE_RUBRIC_ID',
        }),
        name: Joi.string().required().messages({
            'any.required': 'PLEASE_PROVIDE_RUBRIC_NAME',
            'string.empty': 'PLEASE_PROVIDE_RUBRIC_NAME',
        }),
        desc: Joi.string().optional().allow(''),
        scoring: Joi.string().valid(POINT, PERCENTAGE).required().messages({
            'any.only': 'PLEASE_PROVIDE_RUBRIC_SCORE',
            'any.required': 'PLEASE_PROVIDE_RUBRIC_SCORE',
            'string.empty': 'PLEASE_PROVIDE_RUBRIC_SCORE',
        }),
        parameters: Joi.array().items(parameterSchema).required(),
    }),
}).unknown(true);

module.exports = {
    createRubricSchema,
    getRubricsSchema,
    updateRubricSchema,
    deleteRubricSchema,
    createGlobalRubricSchema,
};
