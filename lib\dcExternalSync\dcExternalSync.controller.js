const userSchema = require('../models/user');
const institutionCalendarSchema = require('../models/institution_calendar');
const studentGroupsSchema = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');
const sessionDeliverySchema = require('../models/digi_session_delivery_types');
const { userWarningDenialStatus } = require('../digi_class/course_session/course_session_service');
const { encryptData, decryptData } = require('../utility/encrypt_decrypt.util');
exports.getInstitutionCalendars = async ({ query = {} }) => {
    try {
        const { previousCalendar } = query;
        const institutionCalendersData = await institutionCalendarSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    status: 'published',
                    ...(previousCalendar !== 'true' && { end_date: { $gte: new Date() } }),
                },
                { _id: 1, calendar_name: 1, start_date: 1, end_date: 1 },
            )
            .sort({ _id: -1 })
            .lean();
        if (!institutionCalendersData.length) return { statusCode: 400, message: 'No_Data' };
        return { statusCode: 200, message: 'List_Data', data: institutionCalendersData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.studentGroupingList = async ({ body = {} }) => {
    try {
        const { institutionCalendarId, programId, courseId, year, level, term, rotationNo } = body;

        const studentGroupData = await studentGroupsSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'groups.term': term,
                    'master.year': year,
                    'groups.level': level,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                },
                {
                    _institution_id: 1,
                    'groups.rotation': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.group_name': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups': 1,
                    'groups.students._student_id': 1,
                },
            )
            .lean();
        if (!studentGroupData) return { status: 400, message: 'Groups not found' };
        const studentData =
            studentGroupData.groups.find(
                (groupElement) =>
                    groupElement.level.toLowerCase() === level.toLowerCase() &&
                    groupElement.term.toLowerCase() === term.toLowerCase(),
            ) || {};

        const courseData =
            studentData.courses?.find(
                (courseElement) => String(courseId) === String(courseElement._course_id),
            ) || {};

        const studentList = await userSchema
            .find(
                {
                    isDeleted: false,
                    _id: {
                        $in: studentData.students.map(
                            (studentElement) => studentElement._student_id,
                        ),
                    },
                },
                { user_id: 1, name: 1, gender: 1, isActive: 1 },
            )
            .lean();

        let students = new Map();
        const sessionType = new Set();
        if (courseData.setting) {
            if (rotationNo)
                courseData.setting = courseData.setting.filter(
                    (settingElement) => String(settingElement._group_no) === String(rotationNo),
                );
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    sessionType.add(sessionElement.session_type);
                    sessionElement.groups.forEach((groupElement) => {
                        groupElement._student_ids.forEach((groupStudentElement) => {
                            const matchingStudent = studentList.find(
                                (studentElement) =>
                                    String(studentElement._id) === String(groupStudentElement),
                            );
                            if (matchingStudent && !students.has(String(groupStudentElement))) {
                                students.set(String(groupStudentElement), {
                                    _student_id: groupStudentElement,
                                    academic_no: matchingStudent.user_id,
                                    name: matchingStudent.name,
                                    gender: matchingStudent.gender,
                                    activeStatus: matchingStudent.isActive,
                                    isDenial: false,
                                });
                            }
                        });
                    });
                });
            });
        }
        const sessionDeliveryData = await sessionDeliverySchema
            .find(
                {
                    _program_id: convertToMongoObjectId(programId),
                    'delivery_types.delivery_symbol': { $in: Array.from(sessionType) },
                },
                {
                    _id: 0,
                    session_name: 1,
                    session_symbol: 1,
                    'delivery_types.delivery_name': 1,
                    'delivery_types.delivery_symbol': 1,
                },
            )
            .lean();
        const deliveryTypeMap = new Map();
        sessionDeliveryData.forEach((deliveryElement) => {
            deliveryElement.delivery_types.forEach((type) => {
                deliveryTypeMap.set(type.delivery_symbol.toLowerCase(), {
                    deliveryName: type.delivery_name,
                    sessionTypeName: deliveryElement.session_name,
                    sessionTypeCode: deliveryElement.session_symbol,
                });
            });
        });
        if (courseData.setting) {
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    const sessionName = deliveryTypeMap.get(
                        sessionElement.session_type.toLowerCase(),
                    );
                    if (sessionName) {
                        sessionElement.deliveryTypeCode = sessionElement.session_type;
                        sessionElement.deliveryTypeName = sessionName.deliveryName;
                        sessionElement.sessionTypeName = sessionName.sessionTypeName;
                        sessionElement.sessionTypeCode = sessionName.sessionTypeCode;
                        delete sessionElement.session_type;
                    }
                });
            });
        }
        if (courseData) {
            const splitData = courseData.course_no.split(' ');
            courseData.subject = splitData[0];
            courseData.catalogNbr = splitData[1];
        }
        students = Array.from(students.values());
        const studentDenialStatus = await userWarningDenialStatus({
            studentIds: students,
            courseId: courseData._course_id,
            level,
            term,
            institutionCalendarId,
            _institution_id: studentGroupData._institution_id,
        });
        const studentsGroupsDetails = {
            courseData,
            students: studentDenialStatus,
        };
        return { status: 200, message: 'Student List', data: studentsGroupsDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.individualStudentGroupNameList = async ({ body = {} }) => {
    try {
        const {
            institutionCalendarId,
            programId,
            courseId,
            year,
            level,
            term,
            rotationNo,
            studentId,
        } = body;
        const studentList = await userSchema
            .findOne(
                {
                    isDeleted: false,
                    _id: convertToMongoObjectId(studentId),
                },
                { user_id: 1, name: 1, gender: 1, isActive: 1 },
            )
            .lean();
        const studentGroupData = await studentGroupsSchema
            .findOne(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                    'groups.term': term,
                    'master.year': year,
                    'groups.level': level,
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                    'groups.students._student_id': convertToMongoObjectId(studentId),
                },
                {
                    _institution_id: 1,
                    'groups.rotation': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.group_name': 1,
                    'groups.courses.setting.session_setting.groups': 1,
                },
            )
            .lean();
        if (!studentGroupData) return { status: 400, message: 'Groups not found' };
        const studentData =
            studentGroupData.groups.find(
                (groupElement) =>
                    groupElement.level.toLowerCase() === level.toLowerCase() &&
                    groupElement.term.toLowerCase() === term.toLowerCase(),
            ) || {};
        const courseData =
            studentData.courses?.find(
                (courseElement) => String(courseId) === String(courseElement._course_id),
            ) || {};
        let studentDetails = {};
        if (courseData.setting) {
            if (rotationNo)
                courseData.setting = courseData.setting.filter(
                    (settingElement) => String(settingElement._group_no) === String(rotationNo),
                );
            courseData.setting.forEach((settingElement) => {
                settingElement.session_setting.forEach((sessionElement) => {
                    sessionElement.groups.forEach((groupElement) => {
                        groupElement._student_ids.forEach((groupStudentElement) => {
                            if (String(groupStudentElement) === String(studentId)) {
                                if (String(studentDetails?._student_id) === String(studentId)) {
                                    studentDetails.groups.push({
                                        groupId: groupElement._id,
                                        groupName: groupElement.group_name,
                                    });
                                } else {
                                    studentDetails = {
                                        _student_id: studentList._id,
                                        academic_no: studentList.user_id,
                                        name: studentList.name,
                                        gender: studentList.gender,
                                        activeStatus: studentList.isActive,
                                    };
                                    studentDetails.groups = [
                                        {
                                            groupId: groupElement._id,
                                            groupName: groupElement.group_name,
                                        },
                                    ];
                                }
                            }
                        });
                    });
                });
            });
        }
        const studentDenialStatus = await userWarningDenialStatus({
            studentIds: [{ _student_id: studentId }],
            courseId,
            level,
            term,
            institutionCalendarId,
            _institution_id: studentGroupData._institution_id,
        });
        studentDetails.isDenial = studentDenialStatus?.[0]?.isDenial || false;
        return { status: 200, message: 'Student Group List', data: studentDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.encryptDecryptData = async ({ body = {} }) => {
    try {
        const { inputData, method, handledBy } = body;
        let processedData = [];
        switch (method) {
            case 'encrypt':
                processedData = encryptData({ content: inputData, handledBy });
                break;
            case 'decrypt':
                processedData = decryptData({ content: inputData, handledBy });
                break;
            default:
                processedData = inputData;
                break;
        }
        return { status: 200, message: 'Processed Data', data: processedData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
