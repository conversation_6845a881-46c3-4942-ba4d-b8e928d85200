const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { PORTFOLIO_VARIABLE } = require('../../common/utils/constants');

const schema = new Schema(
    {
        name: { type: String },
        type: { type: String },
        institutionCalendarId: { type: ObjectId },
        programId: { type: ObjectId },
        courseId: { type: ObjectId },
        variable: { name: { type: String }, value: { type: String } },
    },
    { timestamps: true },
);

module.exports = model(PORTFOLIO_VARIABLE, schema);
