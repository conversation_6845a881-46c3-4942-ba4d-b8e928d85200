let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let University = new Schema({
    name: {
        type: String,
        unique: true,
        required: true
    },
    university_code: {
        type: String,
        unique: true,
        required: true
    },
    location: {
        type: String
    },
    _country_id: {
        type: Schema.Types.ObjectId,
        ref: constant.COUNTRY,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.UNIVERSITY, University);