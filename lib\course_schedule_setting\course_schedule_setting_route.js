const express = require('express');
const route = express.Router();
const course_schedule_setting = require('./course_schedule_setting_controller');
const {
    programIdValidate,
    idValidate,
    insertRemoteScheduling,
} = require('./course_schedule_setting_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.post(
    '/:programId',
    [userPolicyAuthentication(['infrastructure_management:remote:level_list:room_details:add'])],
    insertRemoteScheduling,
    course_schedule_setting.insertRemoteScheduling,
);
route.get(
    '/:programId',
    [userPolicyAuthentication(['infrastructure_management:remote:level_list:room_details:view'])],
    programIdValidate,
    course_schedule_setting.listRemoteScheduling,
);
route.get(
    '/remote-scheduling/list/:programId',
    [userPolicyAuthentication(['infrastructure_management:remote:level_list:view'])],
    course_schedule_setting.programCurriculumLevelYearList,
);
route.delete(
    '/:id/:programId',
    [userPolicyAuthentication(['infrastructure_management:remote:level_list:room_details:delete'])],
    idValidate,
    programIdValidate,
    course_schedule_setting.deleteRemoteScheduling,
);
route.put(
    '/:id/:programId',
    [userPolicyAuthentication(['infrastructure_management:remote:level_list:room_details:edit'])],
    idValidate,
    programIdValidate,
    insertRemoteScheduling,
    course_schedule_setting.updateRemoteScheduling,
);

module.exports = route;
