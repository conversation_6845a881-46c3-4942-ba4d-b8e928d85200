const {
    response_function,
    convertToMongoObjectId,
    responseFunctionWithRequest,
} = require('../utility/common');
const { get_list, get } = require('../base/base_controller');
const {
    INSTITUTION,
    PROGRAM_CALENDAR,
    // DIGI_COURSE,
    DIGI_SESSION_ORDER /* , USER */,
    STUDENT_GROUP,
    COURSE_SCHEDULE,
    DIGI_COURSE,
    ROLE_ASSIGN,
} = require('../utility/constants');

const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
// const course = require('mongoose').model(DIGI_COURSE);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const student_group = require('mongoose').model(STUDENT_GROUP);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const course = require('mongoose').model(DIGI_COURSE);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
// const user = require('mongoose').model(USER);

// Get Program -> Level wise Course Course List with Schedule/SG Status
exports.getCourseList = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, institution_calendar, userId, roleId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        // const { status: pc_status, data: pc_data } = await get(
        //     program_calendar,
        //     {
        //         _program_id: convertToMongoObjectId(programId),
        //         _institution_calendar_id: convertToMongoObjectId(institution_calendar),
        //         status: 'published',
        //     },
        //     {},
        // );
        const pc_data = await program_calendar
            .findOne({
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institution_calendar),
                status: 'published',
            })
            .populate({ path: 'level.course._course_id', select: { versionedCourseIds: 1 } })
            .lean();
        if (!pc_data)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Program calendar Not found',
                        'Program calendar Not found',
                    ),
                );
        // Student Group check
        const { status: sgStatus, data: sgData } = await get_list(
            student_group,
            {
                isDeleted: false,
                'master._program_id': convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institution_calendar),
            },
            {},
        );

        //Course Listing based on Role & Coordinator
        const role_assign_list = await get(
            role_assign,
            {
                _user_id: convertToMongoObjectId(userId),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            {},
        );
        if (!role_assign_list.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Users Role not found',
                        'Users Role not found',
                    ),
                );
        const roleData = role_assign_list.data.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        const courseData = [];
        if (!roleData.isAdmin) {
            const { status: ccr_status, data: ccr_data } = await get_list(
                course,
                {
                    'coordinators._user_id': convertToMongoObjectId(userId),
                    'coordinators._institution_calendar_id':
                        convertToMongoObjectId(institution_calendar),
                    isActive: true,
                    isDeleted: false,
                },
                { coordinators: 1 },
            );
            if (!ccr_status)
                return res
                    .status(404)
                    .send(
                        response_function(
                            res,
                            404,
                            false,
                            'Courses Not found',
                            'Courses Not found',
                        ),
                    );
            ccr_data.forEach((courseElement) => {
                courseElement.coordinators.forEach((coordinatorElement) => {
                    if (coordinatorElement._user_id.toString() === userId.toString())
                        courseData.push({
                            course_id: courseElement._id.toString(),
                            _institution_calendar_id:
                                coordinatorElement._institution_calendar_id.toString(),
                            term: coordinatorElement.term,
                        });
                });
            });
        }

        const year_level = [];
        const level_term_dates = [];
        let course_ids = [];
        let term = pc_data.level.map((item) => item.term);
        for (year_element of pc_data.level) {
            level_term_dates.push({
                term: year_element.term,
                year: year_element.year,
                level_no: year_element.level_no,
                start_date: year_element.start_date,
                end_date: year_element.end_date,
            });
            const courses = [];
            let sgCoursePublish = [];
            if (year_element.rotation === 'yes') {
                year_element.rotation_course[0].course.forEach((ele1) => {
                    let sgCourseStatus = false;
                    if (sgStatus) {
                        const yearLoc = sgData.findIndex(
                            (ele2) => ele2.master.year === year_element.year,
                        );
                        if (yearLoc !== -1) {
                            const levelLoc = sgData[yearLoc].groups.findIndex(
                                (ele3) =>
                                    ele3.level === year_element.level_no &&
                                    ele3.term === year_element.term,
                            );
                            if (levelLoc !== -1) {
                                const courseLoc = sgData[yearLoc].groups[
                                    levelLoc
                                ].courses.findIndex(
                                    (ele4) =>
                                        ele4._course_id.toString() ===
                                        ele1._course_id._id.toString(),
                                );
                                if (courseLoc !== -1) {
                                    sgCourseStatus =
                                        sgData[yearLoc].groups[levelLoc].courses[courseLoc].setting
                                            .length !== 0;
                                }
                                for (courseStudent of sgData[yearLoc].groups[levelLoc].students) {
                                    sgCoursePublish = sgCoursePublish.concat(
                                        courseStudent.course_group_status,
                                    );
                                }
                            }
                        }
                    }
                    let coursePublishStatus = false;
                    sgCoursePublish = [...new Set(sgCoursePublish)];
                    for (sgPublishElement of sgCoursePublish) {
                        if (
                            !coursePublishStatus &&
                            sgPublishElement._course_id.toString() ===
                                ele1._course_id._id.toString() &&
                            sgPublishElement.status === 'published'
                        ) {
                            coursePublishStatus = true;
                        }
                    }
                    if (
                        roleData.isAdmin ||
                        courseData.findIndex(
                            (ele) =>
                                ele.course_id.toString() === ele1._course_id._id.toString() &&
                                ele._institution_calendar_id.toString() ===
                                    institution_calendar.toString() &&
                                ele.term === year_element.term,
                        ) !== -1
                    )
                        courses.push({
                            _course_id: ele1._course_id._id,
                            versionedCourseIds: ele1._course_id.versionedCourseIds || [],
                            courses_name: ele1.courses_name,
                            courses_number: ele1.courses_number,
                            start_date: ele1.start_date,
                            end_date: ele1.end_date,
                            versionNo: ele1.versionNo || 1,
                            versioned: ele1.versioned || false,
                            versionName: ele1.versionName || '',
                            versionedFrom: ele1.versionedFrom || null,
                            student_group_status:
                                coursePublishStatus &&
                                sgCourseStatus &&
                                coursePublishStatus === sgCourseStatus,
                        });
                });
                course_ids = course_ids.concat(
                    year_element.rotation_course[0].course.map((i) =>
                        convertToMongoObjectId(i._course_id._id),
                    ),
                );
            } else {
                year_element.course.forEach((ele1) => {
                    let sgCourseStatus = false;
                    if (sgStatus) {
                        const yearLoc = sgData.findIndex(
                            (ele2) => ele2.master.year === year_element.year,
                        );
                        if (yearLoc !== -1) {
                            const levelLoc = sgData[yearLoc].groups.findIndex(
                                (ele3) =>
                                    ele3.level === year_element.level_no &&
                                    ele3.term === year_element.term,
                            );
                            if (levelLoc !== -1) {
                                const courseLoc = sgData[yearLoc].groups[
                                    levelLoc
                                ].courses.findIndex(
                                    (ele4) =>
                                        ele4._course_id.toString() ===
                                        ele1._course_id._id.toString(),
                                );
                                if (courseLoc !== -1) {
                                    sgCourseStatus =
                                        sgData[yearLoc].groups[levelLoc].courses[courseLoc].setting
                                            .length !== 0;
                                }
                                for (courseStudent of sgData[yearLoc].groups[levelLoc].students) {
                                    sgCoursePublish = sgCoursePublish.concat(
                                        courseStudent.course_group_status,
                                    );
                                }
                            }
                        }
                    }
                    let coursePublishStatus = false;
                    sgCoursePublish = [...new Set(sgCoursePublish)];
                    for (sgPublishElement of sgCoursePublish) {
                        if (
                            !coursePublishStatus &&
                            sgPublishElement._course_id.toString() ===
                                ele1._course_id._id.toString() &&
                            sgPublishElement.status === 'published'
                        ) {
                            coursePublishStatus = true;
                        }
                    }
                    if (
                        roleData.isAdmin ||
                        courseData.findIndex(
                            (ele) =>
                                ele.course_id.toString() === ele1._course_id._id.toString() &&
                                ele._institution_calendar_id.toString() ===
                                    institution_calendar.toString() &&
                                ele.term === year_element.term,
                        ) !== -1
                    )
                        courses.push({
                            _course_id: ele1._course_id._id,
                            versionedCourseIds: ele1._course_id.versionedCourseIds || [],
                            courses_name: ele1.courses_name,
                            courses_number: ele1.courses_number,
                            start_date: ele1.start_date,
                            end_date: ele1.end_date,
                            versionNo: ele1.versionNo || 1,
                            versioned: ele1.versioned || false,
                            versionName: ele1.versionName || '',
                            versionedFrom: ele1.versionedFrom || null,
                            student_group_status:
                                coursePublishStatus &&
                                sgCourseStatus &&
                                coursePublishStatus === sgCourseStatus,
                        });
                });
                course_ids = course_ids.concat(
                    year_element.course.map((i) => convertToMongoObjectId(i._course_id._id)),
                );
            }
            if (
                year_level.indexOf(
                    (i) => i.level_no.toString() === year_element.level_no.toString(),
                ) === -1 &&
                courses.length !== 0
            ) {
                year_level.push({
                    year: year_element.year,
                    level_no: year_element.level_no,
                    term: year_element.term,
                    curriculum: year_element.curriculum,
                    rotation: year_element.rotation === 'yes',
                    courses,
                });
            }
        }
        course_ids = [...new Set(course_ids)];

        // Course Listing
        const { data: c_data } = await get_list(
            course,
            {
                _id: { $in: course_ids },
                isActive: true,
                isDeleted: false,
                // _program_id: convertToMongoObjectId(program),
            },
            {
                coordinators: 1,
                _program_id: 1,
                level_no: 1,
                year: 1,
                'course_assigned_details.course_shared_with': 1,
                'course_assigned_details.year': 1,
                'course_assigned_details.level_no': 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );
        const courseSharePrograms = [];
        for (crsElement of c_data) {
            for (assignedElement of crsElement.course_assigned_details) {
                for (shareElement of assignedElement.course_shared_with) {
                    if (
                        !courseSharePrograms.find(
                            (ele) => ele.toString() === crsElement._program_id.toString(),
                        ) &&
                        shareElement._program_id.toString() === programId.toString()
                    )
                        courseSharePrograms.push(crsElement._program_id.toString());
                }
            }
        }
        courseSharePrograms.push(programId.toString());
        term = [...new Set(term)];
        const { status: cs_status, data: cs_data } = await get_list(
            session_order,
            {
                _course_id: { $in: course_ids },
                isActive: true,
                isDeleted: false,
            },
            {
                _course_id: 1,
                'session_flow_data._id': 1,
                'session_flow_data.delivery_symbol': 1,
            },
        );

        // Schedule Status check
        const { status: csStatus, data: csData } = await get_list(
            course_schedule,
            {
                isDeleted: false,
                isActive: true,
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institution_calendar),
                _program_id: { $in: courseSharePrograms },
                // _program_id: convertToMongoObjectId(program),
                type: 'regular',
            },
            {
                _program_id: 1,
                type: 1,
                term: 1,
                year_no: 1,
                level_no: 1,
                _course_id: 1,
                session: 1,
            },
        );

        year_level.forEach((i, index) => {
            const level_course_data = [];
            i.courses.forEach((course_element) => {
                let no_session = 0;
                let deliverySymbol = [];
                let schedule_status = false;
                if (cs_status) {
                    const course__session_order_ind = cs_data.findIndex(
                        (ind) => ind._course_id.toString() === course_element._course_id.toString(),
                    );
                    if (course__session_order_ind !== -1) {
                        no_session = cs_data[course__session_order_ind].session_flow_data.length;
                        deliverySymbol = cs_data[course__session_order_ind].session_flow_data.map(
                            (ele) => ele.delivery_symbol,
                        );
                        deliverySymbol = [...new Set(deliverySymbol)];
                    }
                }
                const coordinators = c_data.find(
                    (courseElement) =>
                        courseElement._id.toString() === course_element._course_id.toString(),
                );
                const courseCoordinatorInd =
                    coordinators && coordinators.coordinators.length !== 0
                        ? coordinators.coordinators.findIndex(
                              (element) => element.term === i.term && element.status === true,
                          )
                        : -1;
                let isShared = false;
                let isAllowedToSchedule = true;
                let cadYear = 0;
                let cadLevel = 0;
                if (coordinators) {
                    const cad = coordinators.course_assigned_details.filter(
                        (ele) =>
                            ele.course_shared_with.length !== 0 &&
                            ele.course_shared_with.some(
                                (ele2) =>
                                    ele2.year == i.year &&
                                    ele2.level_no == i.level_no &&
                                    ele2._program_id.toString() == programId.toString(),
                            ),
                    );
                    if (cad.length) {
                        cadYear = cad[0].year;
                        cadLevel = cad[0].level_no;
                        isShared = true;
                        course_element.student_group_status = true;
                    }
                }
                if (csStatus && no_session !== 0) {
                    let session_list = csData.filter(
                        (ele) =>
                            (isShared
                                ? ele._program_id.toString() ===
                                      coordinators._program_id.toString() &&
                                  ele.year_no.toString() === cadYear.toString() &&
                                  ele.level_no.toString() === cadLevel.toString()
                                : ele._program_id.toString() === programId.toString() &&
                                  ele.year_no.toString() === i.year.toString() &&
                                  ele.level_no.toString() === i.level_no.toString()) &&
                            ele._course_id.toString() === course_element._course_id.toString() &&
                            ele.term.toString() === i.term.toString(),
                    );
                    if (isShared && session_list.length !== 0) isAllowedToSchedule = false;
                    // Need to Course Schedule Listing
                    session_list = session_list.filter(
                        (item, index) =>
                            session_list.findIndex(
                                (ele) =>
                                    item.session &&
                                    item.session._session_id &&
                                    ele.session._session_id.toString() ===
                                        item.session._session_id.toString(),
                            ) === index,
                    );
                    schedule_status = no_session <= session_list.length;
                }

                level_course_data.push({
                    _course_id: course_element._course_id,
                    courses_name: course_element.courses_name,
                    courses_number: course_element.courses_number,
                    start_date: course_element.start_date,
                    end_date: course_element.end_date,
                    versionNo: course_element.versionNo || 1,
                    versioned: course_element.versioned || false,
                    versionName: course_element.versionName || '',
                    versionedFrom: course_element.versionedFrom || null,
                    versionedCourseIds: course_element.versionedCourseIds || [],
                    credit_hours: [],
                    delivery_symbol: deliverySymbol,
                    course_coordinator:
                        courseCoordinatorInd !== -1
                            ? coordinators.coordinators[courseCoordinatorInd]
                            : {},
                    no_session,
                    student_group_status: course_element.student_group_status,
                    schedule_status,
                    isShared,
                    isAllowedToSchedule,
                });
            });
            year_level[index].courses = level_course_data;
        });
        const response = {
            term,
            courses: year_level,
            level_term_dates,
        };
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Program wise Year Level Course List', response),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
exports.getCourseListRefactored = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { programId, institution_calendar, userId, roleId } = req.params;
        const roleAssignList = await role_assign
            .findOne(
                {
                    _user_id: convertToMongoObjectId(userId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    'roles._role_id': 1,
                    'roles.isAdmin': 1,
                },
            )
            .lean();
        if (!roleAssignList)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Users Role not found',
                        'Users Role not found',
                    ),
                );
        const roleData = roleAssignList.roles.find(
            (roleElement) => roleElement._role_id.toString() === roleId,
        );
        const courseCoordinatorCheck = new Set();
        const courseData = [];
        if (!roleData.isAdmin) {
            const courseDocs = await course
                .find(
                    {
                        'coordinators._user_id': convertToMongoObjectId(userId),
                        'coordinators._institution_calendar_id':
                            convertToMongoObjectId(institution_calendar),
                        isActive: true,
                        isDeleted: false,
                    },
                    { coordinators: 1 },
                )
                .lean();
            if (!courseDocs.length)
                return res
                    .status(404)
                    .send(
                        response_function(
                            res,
                            404,
                            false,
                            'Courses Not found',
                            'Courses Not found',
                        ),
                    );
            for (const courseElement of courseDocs) {
                for (coordinatorElement of courseElement.coordinators) {
                    if (
                        coordinatorElement._institution_calendar_id.toString() ===
                        institution_calendar
                    ) {
                        const key = `${courseElement._id.toString()}${coordinatorElement._institution_calendar_id.toString()}${coordinatorElement.term.toLowerCase()}`;
                        if (!courseCoordinatorCheck.has(key)) {
                            courseCoordinatorCheck.add(key);
                        }
                        if (coordinatorElement._user_id.toString() === userId.toString()) {
                            courseData.push({
                                course_id: courseElement._id.toString(),
                                _institution_calendar_id:
                                    coordinatorElement._institution_calendar_id.toString(),
                                term: coordinatorElement.term,
                            });
                        }
                    }
                }
            }
        }
        const programCalendarDoc = await program_calendar
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(institution_calendar),
                    status: 'published',
                },
                {
                    _program_id: 1,
                    'level.curriculum': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.start_date': 1,
                    'level.end_date': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.start_date': 1,
                    'level.course.end_date': 1,
                    'level.course.versionNo': 1,
                    'level.course.versioned': 1,
                    'level.course.versionName': 1,
                    'level.course.versionedFrom': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course.start_date': 1,
                    'level.rotation_course.course.end_date': 1,
                    'level.rotation_course.course.versionNo': 1,
                    'level.rotation_course.course.versioned': 1,
                    'level.rotation_course.course.versionName': 1,
                },
            )
            .populate({ path: 'level.course._course_id', select: { versionedCourseIds: 1 } })
            .lean();
        if (!programCalendarDoc)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        'Program calendar Not found',
                        'Program calendar Not found',
                    ),
                );
        const studentGroupDocs = await student_group
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    _institution_calendar_id: convertToMongoObjectId(institution_calendar),
                    'master._program_id': convertToMongoObjectId(programId),
                },
                {
                    'master.year': 1,
                    'groups._id': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.students.course_group_status': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._id': 1,
                },
            )
            .lean();
        const yearLevel = [];
        const level_term_dates = [];
        const courseIds = [];
        const studentGroups = {};
        for (const studentGroupDocElement of studentGroupDocs) {
            for (const groupElement of studentGroupDocElement.groups) {
                const keys =
                    studentGroupDocElement.master.year.toLowerCase().trim() +
                    groupElement.level.toLowerCase().trim() +
                    groupElement.term.toLowerCase().trim();
                for (const courseElement of groupElement.courses) {
                    const courseSettingKey = keys + courseElement._course_id.toString();
                    studentGroups[courseSettingKey] = courseElement.setting.length !== 0;
                }
                if (!studentGroups[keys]) {
                    studentGroups[keys] = false;
                } else if (studentGroups[keys] === true) {
                    continue;
                }
                studentGroups[keys] = groupElement.students.some((courseStudentsElement) =>
                    courseStudentsElement.course_group_status.some(
                        (statusElement) => statusElement.status === 'published',
                    ),
                );
            }
        }
        const termSet = new Set();
        const yearLevelCheck = {};
        for (yearElement of programCalendarDoc.level) {
            termSet.add(yearElement.term);
            level_term_dates.push({
                term: yearElement.term,
                year: yearElement.year,
                level_no: yearElement.level_no,
                start_date: yearElement.start_date,
                end_date: yearElement.end_date,
            });
            const courses = [];
            const isRotation = yearElement.rotation === 'yes';
            const coursesForYearElement = isRotation
                ? yearElement.rotation_course[0].course
                : yearElement.course;
            coursesForYearElement.forEach((courseElement) => {
                courseIds.push(courseElement._course_id._id);
                let sgCourseStatus = false;
                const keys =
                    yearElement.year.toLowerCase().trim() +
                    yearElement.level_no.toLowerCase().trim() +
                    yearElement.term.toLowerCase().trim();
                const courseSettingsKey = keys + courseElement._course_id._id.toString();
                sgCourseStatus = studentGroups[courseSettingsKey];
                let coursePublishStatus = false;
                if (studentGroups[keys]) {
                    coursePublishStatus = true;
                }
                const coordinatorKey = `${courseElement._course_id._id.toString()}${institution_calendar.toString()}${yearElement.term.toLowerCase()}`;
                if (roleData.isAdmin || courseCoordinatorCheck.has(coordinatorKey))
                    courses.push({
                        _course_id: courseElement._course_id._id,
                        versionedCourseIds: courseElement._course_id.versionedCourseIds || [],
                        courses_name: courseElement.courses_name,
                        courses_number: courseElement.courses_number,
                        start_date: courseElement.start_date,
                        end_date: courseElement.end_date,
                        student_group_status:
                            coursePublishStatus &&
                            sgCourseStatus &&
                            coursePublishStatus === sgCourseStatus,
                    });
            });
            const levelKey = yearElement.level_no.toLowerCase().trim();
            if (!yearLevelCheck[levelKey] && courses.length !== 0) {
                yearLevelCheck[levelKey] = false;
                yearLevel.push({
                    year: yearElement.year,
                    level_no: yearElement.level_no,
                    term: yearElement.term,
                    curriculum: yearElement.curriculum,
                    rotation: yearElement.rotation === 'yes',
                    courses,
                });
            }
        }
        const term = Array.from(termSet);
        const courseList = await course
            .find(
                {
                    _id: { $in: courseIds },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    coordinators: 1,
                    _program_id: 1,
                    level_no: 1,
                    year: 1,
                    'course_assigned_details.course_shared_with': 1,
                    'course_assigned_details.year': 1,
                    'course_assigned_details.level_no': 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            )
            .lean();
        const courseShareProgramSet = new Set();
        const courseCoordinatorAssignDetails = {};
        const courseProgramIds = {};
        for (const courseElement of courseList) {
            courseProgramIds[courseElement._id.toString()] = courseElement._program_id.toString();
            for (const coordinatorElement of courseElement.coordinators) {
                const coordinatorKey = `${courseElement._id.toString()}${coordinatorElement.term
                    .toLowerCase()
                    .trim()}${
                    coordinatorElement.status
                }${coordinatorElement._institution_calendar_id.toString()}`;
                courseCoordinatorAssignDetails[coordinatorKey] = coordinatorElement;
            }
            for (const assignedElement of courseElement.course_assigned_details) {
                const sharedElements = assignedElement.course_shared_with.find(
                    (sharedElement) =>
                        sharedElement._program_id.toString() === programId.toString(),
                );
                if (sharedElements) {
                    courseShareProgramSet.add(courseElement._program_id.toString());
                    const assignDetailsKey = `${courseElement._id.toString()}${sharedElements.year
                        .toLowerCase()
                        .trim()}${sharedElements.level_no
                        .toLowerCase()
                        .trim()}${sharedElements._program_id.toString()}`;
                    courseCoordinatorAssignDetails[assignDetailsKey] = {
                        courseAssignedDetailsYear: sharedElements.year,
                        courseAssignedDetailsLevelNo: sharedElements.level_no,
                    };
                }
            }
        }
        const courseSharePrograms = Array.from(courseShareProgramSet);
        courseSharePrograms.push(programId.toString());
        const sessionOrderData = await session_order
            .find(
                {
                    _program_id: { $in: courseSharePrograms },
                    _course_id: { $in: courseIds },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _course_id: 1,
                    'session_flow_data._id': 1,
                    'session_flow_data.delivery_symbol': 1,
                },
            )
            .lean();
        const sessionOrderCourseData = {};
        for (const { _course_id, session_flow_data } of sessionOrderData) {
            const sessionOrderKeys = _course_id.toString();
            const deliverySymbolSet = new Set();
            for (const sessionFlowDataElement of session_flow_data) {
                deliverySymbolSet.add(sessionFlowDataElement.delivery_symbol);
            }
            const deliverySymbol = Array.from(deliverySymbolSet);
            sessionOrderCourseData[sessionOrderKeys] = {
                sessionFlowDataLength: session_flow_data.length,
                deliverySymbol,
            };
        }
        const courseScheduleData = await course_schedule
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institution_calendar),
                    _program_id: { $in: courseSharePrograms },
                    type: 'regular',
                },
                {
                    _program_id: 1,
                    type: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    _course_id: 1,
                    'session._session_id': 1,
                },
            )
            .lean();
        const courseScheduleDetails = {};
        for (const courseScheduleDataElement of courseScheduleData) {
            const { _program_id, year_no, level_no, _course_id, term, session } =
                courseScheduleDataElement;
            const keys =
                _program_id.toString() +
                year_no.toLowerCase().trim() +
                level_no.toLowerCase().trim() +
                _course_id.toString() +
                term.toLowerCase().trim();
            if (!courseScheduleDetails[keys]) {
                courseScheduleDetails[keys] = [];
            }
            const existingSessions = new Set(
                courseScheduleDetails[keys].map((courseScheduleDetailsElement) =>
                    courseScheduleDetailsElement.session?._session_id?.toString(),
                ),
            );
            if (session && !existingSessions.has(session._session_id?.toString())) {
                courseScheduleDetails[keys].push(courseScheduleDataElement);
            }
        }
        for (const [yearIndex, yearElement] of yearLevel.entries()) {
            const level_course_data = [];
            for (const courseElement of yearElement.courses) {
                let no_session = 0;
                let deliverySymbol = [];
                let schedule_status = false;
                const courseId = courseElement._course_id.toString();
                no_session = sessionOrderCourseData[courseId]
                    ? sessionOrderCourseData[courseId].sessionFlowDataLength
                        ? sessionOrderCourseData[courseId].sessionFlowDataLength
                        : 0
                    : 0;
                deliverySymbol = sessionOrderCourseData[courseId]
                    ? sessionOrderCourseData[courseId].deliverySymbol
                        ? sessionOrderCourseData[courseId].deliverySymbol
                        : []
                    : [];
                const courseCoordinatorAssignDetailsKey =
                    courseId + yearElement.term.toLowerCase().trim() + true + institution_calendar;
                const courseCoordinator = courseCoordinatorAssignDetails[
                    courseCoordinatorAssignDetailsKey
                ]
                    ? courseCoordinatorAssignDetails[courseCoordinatorAssignDetailsKey]
                    : {};
                let isShared = false;
                let isAllowedToSchedule = true;
                let cadYear = 0;
                let cadLevel = 0;
                const courseProgramId = courseProgramIds[courseElement._course_id.toString()];
                const courseAssignDetailsKeys =
                    courseId +
                    yearElement.year.toLowerCase().trim() +
                    yearElement.level_no.toLowerCase().trim() +
                    programId.toString();
                const courseAssignDetails = courseCoordinatorAssignDetails[courseAssignDetailsKeys];
                if (courseAssignDetails) {
                    cadYear = courseAssignDetails.courseAssignedDetailsYear;
                    cadLevel = courseAssignDetails.courseAssignedDetailsLevelNo;
                    isShared = true;
                    courseElement.student_group_status = true;
                }
                if (courseScheduleData.length && no_session !== 0) {
                    const keys = isShared
                        ? `${courseProgramId}${cadYear.toLowerCase().trim()}${cadLevel
                              .toLowerCase()
                              .trim()}${courseId}${yearElement.term.toLowerCase().trim()}`
                        : `${programId.toString()}${yearElement.year
                              .toLowerCase()
                              .trim()}${yearElement.level_no
                              .toLowerCase()
                              .trim()}${courseId}${yearElement.term.toLowerCase().trim()}`;
                    const sessionList = courseScheduleDetails[keys];
                    if (isShared && sessionList && sessionList.length !== 0) {
                        isAllowedToSchedule = false;
                    }
                    if (sessionList && sessionList.length) {
                        schedule_status = no_session <= sessionList.length;
                    }
                }
                const courseVersionedDetails = courseList.find(
                    (courseListElement) =>
                        courseListElement._id.toString() === courseElement._course_id.toString(),
                );
                level_course_data.push({
                    _course_id: courseElement._course_id,
                    courses_name: courseElement.courses_name,
                    courses_number: courseElement.courses_number,
                    start_date: courseElement.start_date,
                    end_date: courseElement.end_date,
                    versionNo: courseVersionedDetails?.versionNo || 1,
                    versioned: courseVersionedDetails?.versioned || false,
                    versionName: courseVersionedDetails?.versionName || '',
                    versionedFrom: courseVersionedDetails?.versionedFrom || null,
                    versionedCourseIds: courseVersionedDetails?.versionedCourseIds || [],
                    credit_hours: [],
                    delivery_symbol: deliverySymbol,
                    course_coordinator: courseCoordinator,
                    no_session,
                    student_group_status: courseElement.student_group_status,
                    schedule_status,
                    isShared,
                    isAllowedToSchedule,
                });
            }
            yearLevel[yearIndex].courses = level_course_data;
        }
        const response = {
            term,
            courses: yearLevel,
            level_term_dates,
        };
        return res
            .status(200)
            .send(
                response_function(res, 200, true, 'Program wise Year Level Course List', response),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
// Get Schedule Based on User
exports.getUserSessionList = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { userId, instCalId, date },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const csQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(instCalId),
            schedule_date: new Date(date),
            'staffs._staff_id': convertToMongoObjectId(userId),
            isDeleted: false,
            isActive: true,
        };
        const csProject = {
            session: 1,
            _session_id: '$session._session_id',
            _program_id: 1,
            start: 1,
            end: 1,
            _id: 1,
            type: 1,
            sub_type: 1,
            rotation: 1,
            rotation_count: 1,
            term: 1,
            _student_group_id: 1,
            year_no: 1,
            level_no: 1,
            _course_id: 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            staffs: 1,
            _infra_id: 1,
            infra_name: 1,
        };
        const coursePopulate = { path: '_course_id', select: { course_code: 1, course_name: 1 } };
        const programPopulate = { path: '_program_id', select: { name: 1, code: 1 } };
        const cs_data = await course_schedule
            .find(csQuery, csProject)
            .populate(coursePopulate)
            .populate(programPopulate)
            .populate({
                path: 'merge_with.schedule_id',
                select: { session: 1 },
            });
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    'User wise Session List based on Date',
                    cs_data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};
