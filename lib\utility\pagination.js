const getPageNo = (pageNo) => (parseInt(pageNo) > 0 ? parseInt(pageNo) : 1);

const getSkip = (limit, pageNo) => limit * (pageNo - 1);

const getPageLimit = (limit) => (parseInt(limit) > 0 ? parseInt(limit) : 10);

const getPaginationValues = (query) => {
    let { pageNo, limit } = query;
    limit = getPageLimit(limit);
    pageNo = getPageNo(pageNo);
    const skip = getSkip(limit, pageNo);
    return { limit, pageNo, skip };
};

module.exports = {
    getPageNo,
    getSkip,
    getPageLimit,
    getPaginationValues,
};
