const constant = require('../utility/constants');
// const program = require('mongoose').model(constant.DIGI_PROGRAM);
// const institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const role = require('mongoose').model(constant.ROLE);
const user = require('mongoose').model(constant.USER);
// const program = require('mongoose').model(constant.PROGRAM);
const digi_program = require('mongoose').model(constant.DIGI_PROGRAM);
// const department = require('mongoose').model(constant.DEPARTMENT);
const digi_department = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const { clearItem, allRoleAssignDatas } = require('../../service/cache.service');
const moduleSchema = require('../models/module');
const { createPolicyKey } = require('../utility/common_functions');

// Updating Course Flat Caching Data
const updateUserRoleAssignFlatCacheData = async (userId) => {
    clearItem(`${userId.toString()}-userRoleAssign`);
    await allRoleAssignDatas(userId);
};

/**
 * Returning Role assign list based on User ID
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Assigned Role Get>}
 */
async function list_id(req, res) {
    try {
        const role_assign_list = await base_control.get(
            role_assign,
            {
                _user_id: ObjectId(req.params.id),
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            },
            {},
        );
        if (!role_assign_list.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('THERE_IS_NO_ROLE_ASSIGNED_FOR_THIS_USER'),
                        req.t('THERE_IS_NO_ROLE_ASSIGNED_FOR_THIS_USER'),
                    ),
                );
        const roles = [];
        for (const element of role_assign_list.data.roles) {
            const assigned = [];
            const other_department = [];
            const programs = element.program.map((i) => i._program_id);
            // const departments = await base_control.get_list(
            //     department,
            //     { _program_id: { $in: programs }, isDeleted: false, isActive: true },
            //     { _id: 1, department_title: 1, _program_id: 1 },
            // );
            const new_departments = await base_control.get_list(
                digi_department,
                { program_id: { $in: programs }, isDeleted: false, isActive: true },
                { _id: 1, department_name: 1, program_id: 1 },
            );
            if (new_departments.status) {
                // if (departments.status)
                //     departments.data.forEach((sub_element) => {
                //         const pro = element.program.findIndex(
                //             (i) => i._program_id.toString() == sub_element._program_id.toString(),
                //         );
                //         if (pro != -1)
                //             other_department.push({
                //                 _department_id: sub_element._id,
                //                 department_title: sub_element.department_title,
                //                 program_name: element.program[pro].program_name,
                //                 _program_id: element.program[pro]._id,
                //             });
                //     });
                if (new_departments.status)
                    new_departments.data.forEach((sub_element) => {
                        const pro = element.program.findIndex(
                            (i) => i._program_id.toString() == sub_element.program_id.toString(),
                        );
                        if (pro != -1)
                            other_department.push({
                                _department_id: sub_element._id,
                                department_title: sub_element.department_name,
                                program_name: element.program[pro].program_name,
                                _program_id: element.program[pro]._id,
                            });
                    });
                element.department.forEach((depart_element) => {
                    const pos = other_department.findIndex(
                        (i) =>
                            i._department_id.toString() == depart_element._department_id.toString(),
                    );
                    if (pos != -1) assigned.push(other_department[pos]);
                });
            }
            const others = {
                isDefault: element.isDefault,
                isAdmin: element.isAdmin,
                _id: element._id,
                _role_id: element._role_id,
                role_name: element.role_name,
                program: element.program,
                department: assigned,
                other_department,
            };
            roles.push(others);
        }
        //Get Report Role and Data
        const populate = { path: '_role_id', select: { user_name: 1, roles: 1 } };
        const user_check = await base_control.get_list_populate(
            user,
            { _id: ObjectId(role_assign_list.data.report_to._user_id) },
            { name: 1, user_id: 1 },
            populate,
        );
        let user_id = '';
        let role = '';
        let program = [];
        if (user_check.status) {
            user_id = user_check.data[0].user_id;
            if (user_check.data[0]._role_id) {
                role =
                    user_check.data[0]._role_id.roles[
                        user_check.data[0]._role_id.roles.findIndex((i) => i.isDefault == true)
                    ].role_name;
                program =
                    user_check.data[0]._role_id.roles[
                        user_check.data[0]._role_id.roles.findIndex((i) => i.isDefault == true)
                    ].program;
            }
        }
        const report_to = {
            name: role_assign_list.data.report_to.name,
            _user_id: role_assign_list.data.report_to._user_id,
            user_id,
            role,
            program,
        };
        const datas = {
            user_name: role_assign_list.data.user_name,
            report_to,
            isActive: role_assign_list.data.isActive,
            _id: role_assign_list.data._id,
            _institution_id: role_assign_list.data._institution_id,
            _user_id: role_assign_list.data._user_id,
            roles,
        };
        if (role_assign_list.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('ASSIGNED_ROLES_LIST'),
                        datas,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ASSIGNED_ROLES_NOT_FOUND'),
                    {},
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
}

/**
 * Assigning Role based on User
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Role assign>}
 */
async function insert(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const role_assign_list = await base_control.get(
            role_assign,
            {
                _user_id: ObjectId(req.body._user_id),
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            },
            {},
        );

        const user_ids = [ObjectId(req.body._user_id)];
        if (req.body._report_to && req.body._report_to.length !== 0)
            user_ids.push(ObjectId(req.body._report_to));
        const users_check = await base_control.get_list(
            user,
            {
                _id: user_ids,
                isDeleted: false,
                isActive: true,
            },
            { name: 1 },
        );
        if (!users_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        req.t('USER_NOT_FOUND'),
                    ),
                );

        let program_ids = [];
        let department_ids = [];
        let programIdCheck = false;
        let roleName;
        req.body.roles.forEach((element) => {
            if (element.role_name !== constant.COURSE_COORDINATOR) {
                program_ids = program_ids.concat(element._program_ids);
                department_ids = department_ids.concat(element._department_ids);
                if (!element._program_ids.length) {
                    programIdCheck = true;
                    roleName = element.role_name;
                }
            }
        });

        if (programIdCheck) {
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('Program not found for ' + roleName),
                        req.t('Program not found for ' + roleName),
                    ),
                );
        }
        const new_program_check = await base_control.get_list(
            digi_program,
            { _id: program_ids, isDeleted: false, isActive: true },
            { name: 1 },
        );
        if (!new_program_check.status && program_ids.length > 0)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        let new_departments;
        if (department_ids.length != 0) {
            new_departments = await base_control.get_list(
                digi_department,
                { _id: { $in: department_ids }, isDeleted: false, isActive: true },
                { _id: 1, department_name: 1, program_id: 1 },
            );
            if (!new_departments.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('DEPARTMENT_NOT_FOUND'),
                            req.t('DEPARTMENT_NOT_FOUND'),
                        ),
                    );
        }
        const roles = [];
        req.body.roles.forEach((element) => {
            const programs = [];
            const departments = [];
            if (element._program_ids && element._program_ids.length != 0) {
                element._program_ids.forEach((sub_element) => {
                    const pro =
                        new_program_check.data.findIndex(
                            (i) => i._id.toString() == sub_element.toString(),
                        ) !== -1
                            ? new_program_check.data[
                                  new_program_check.data.findIndex(
                                      (i) => i._id.toString() == sub_element.toString(),
                                  )
                              ].name
                            : '';
                    programs.push({
                        _program_id: sub_element,
                        program_name: pro,
                    });
                });
            }
            if (element._department_ids && element._department_ids.length != 0) {
                element._department_ids.forEach((sub_element) => {
                    departments.push({
                        _department_id: sub_element,
                        department_title:
                            new_departments.data[
                                new_departments.data.findIndex(
                                    (i) => i._id.toString() == sub_element.toString(),
                                )
                            ].department_title,
                    });
                });
            }
            roles.push({
                _role_id: element._role_id,
                role_name: element.role_name,
                program: programs,
                department: departments,
                isDefault: element.is_default,
                isAdmin: element.is_admin,
            });
        });
        const objs = {
            _institution_id: req.headers._institution_id,
            _user_id: req.body._user_id,
            user_name:
                users_check.data[
                    users_check.data.findIndex(
                        (i) => i._id.toString() == req.body._user_id.toString(),
                    )
                ].name,
            roles,
            report_to: {},
            // report_to: {
            //     _user_id: req.body._report_to,
            //     name:
            //         users_check.data[
            //             users_check.data.findIndex(
            //                 (i) => i._id.toString() == req.body._report_to.toString(),
            //             )
            //         ].name,
            // },
        };
        if (req.body._report_to && req.body._report_to.length !== 0) {
            objs.report_to = {
                _user_id: req.body._report_to,
                name: users_check.data[
                    users_check.data.findIndex(
                        (i) => i._id.toString() == req.body._report_to.toString(),
                    )
                ].name,
            };
        }
        let doc;
        if (!role_assign_list.status) {
            doc = await base_control.insert(role_assign, objs);
            await base_control.update(user, ObjectId(req.body._user_id), {
                _role_id: ObjectId(doc.responses._id),
            });
        } else {
            console.log(role_assign_list);
            doc = await base_control.update_condition(
                role_assign,
                { _user_id: ObjectId(req.body._user_id) },
                { $set: objs },
            );
        }
        updateUserRoleAssignFlatCacheData(req.body._user_id);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('ROLE_ASSIGNED_TO_USER'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_ASSIGN_ROLE_TO_USER'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
}

/**
 * Returning Role assign list based on User ID
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Assigned Role Get>}
 */
async function department_list(req, res) {
    try {
        const role_check = await base_control.get(
            role,
            {
                _id: ObjectId(req.body._role_id),
                /* _institution_id: ObjectId(req.headers._institution_id), */ isDeleted: false,
            },
            {},
        );
        if (!role_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_ROLE'),
                        req.t('UNABLE_TO_FIND_ROLE'),
                    ),
                );

        // const program_check = await base_control.get_list(
        //     program,
        //     { _id: req.body._program_ids, isDeleted: false, isActive: true },
        //     { name: 1 },
        // );
        const new_program_check = await base_control.get_list(
            digi_program,
            { _id: req.body._program_ids, isDeleted: false, isActive: true },
            { name: 1 },
        );
        if (
            // !program_check.status &&
            !new_program_check.status /*  && program_check.data.length != req.body._program_ids.length */
        )
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );

        // const departments = await base_control.get_list(
        //     department,
        //     { _program_id: { $in: req.body._program_ids }, isDeleted: false, isActive: true },
        //     { _id: 1, department_title: 1, _program_id: 1 },
        // );
        const new_departments = await base_control.get_list(
            digi_department,
            { program_id: { $in: req.body._program_ids }, isDeleted: false, isActive: true },
            { _id: 1, department_name: 1, program_id: 1 },
        );
        if (!new_departments.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        req.t('DEPARTMENT_NOT_FOUND'),
                    ),
                );
        const role_department = await base_control.get_list(
            role_assign,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
                isActive: true,
            },
            {},
        );
        let departments_ids = [];
        if (role_department.status)
            role_department.data.forEach((element) => {
                element.roles.forEach((sub_element) => {
                    if (sub_element._role_id.toString() == req.body._role_id.toString())
                        departments_ids = departments_ids.concat(sub_element.department);
                });
            });
        const det = departments_ids.map((i) => i._department_id.toString());
        let datas = [];
        let pro = -1;
        // if (departments.status)
        //     datas = departments.data.filter((item) => !det.includes(item._id.toString()));
        if (new_departments.status)
            datas = datas.concat(
                new_departments.data.filter((item) => !det.includes(item._id.toString())),
            );

        const program_departments = [];
        datas.forEach((element) => {
            // if (element._program_id)
            //     pro = program_check.data.findIndex(
            //         (i) => i._id.toString() == element._program_id.toString(),
            //     );
            if (element.program_id)
                pro = new_program_check.data.findIndex(
                    (i) => i._id.toString() == element.program_id.toString(),
                );
            if (pro != -1) {
                const dept = element.department_title
                    ? element.department_title
                    : element.department_name;
                program_departments.push({
                    _id: element._id,
                    department_title: dept,
                    program_name: new_program_check.data[pro].name,
                    _program_id: new_program_check.data[pro]._id,
                });
            }
        });
        return res
            .status(201)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_LIST'),
                    program_departments,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
}

/**
 * Returning Staff User List along with Role and programs
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Assigned Role Get>}
 */
async function report_to_list(req, res) {
    try {
        // const program_check = await base_control.get_list(
        //     digi_program,
        //     { isDeleted: false, isActive: true },
        //     { name: 1 },
        // );
        // if (!program_check.status)
        //     return res
        //         .status(410)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 404,
        //                 false,
        //                 'Programs not found',
        //                 'Programs not found',
        //             ),
        //         );
        // const program_ids = program_check.data.map((i) => ObjectId(i._id));
        // console.log(program_ids);
        const user_query = {
            user_type: 'staff',
            // academic_allocation: {
            //     $elemMatch: { _program_id: { $in: program_ids } },
            // },
            isDeleted: false,
            isActive: true,
        };
        const populate = { path: '_role_id', select: { user_name: 1, roles: 1 } };
        const user_check = await base_control.get_list_populate(
            user,
            user_query,
            { name: 1, user_id: 1 },
            populate,
        );
        if (!user_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        req.t('USER_NOT_FOUND'),
                    ),
                );
        const user_details = [];
        user_check.data.forEach((element) => {
            const datas = {
                _id: element._id,
                name: element.name,
                user_id: element.user_id,
                role: '',
                program: [],
            };
            if (element._role_id && element._role_id.roles) {
                role_loc = element._role_id.roles.findIndex((i) => i.isDefault == true);
                if (role_loc != -1) {
                    datas.role = element._role_id.roles[role_loc].role_name;
                    datas.program = element._role_id.roles[role_loc].program;
                }
            }
            user_details.push(datas);
        });
        return res
            .status(201)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('DEPARTMENT_LIST'),
                    user_details,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
}

const addPolicyKey = async (req, res) => {
    try {
        const bulkUpdateModule = [];
        const bulkUpdateRole = [];
        const moduleData = await moduleSchema.find().lean();
        moduleData?.forEach((moduleElement) => {
            const moduleName = moduleElement?.name;
            moduleElement?.pages?.forEach((pageElement) => {
                const pageName = pageElement?.name;
                pageElement?.actions?.forEach((actionElement) => {
                    actionElement.policy = createPolicyKey({
                        policyKey: `${moduleName}+${pageName}+${actionElement?.name}`,
                    });
                });
                pageElement?.tabs?.forEach((tabElement) => {
                    const tabName = tabElement?.name;
                    tabElement?.actions?.forEach((tagActionElement) => {
                        tagActionElement.policy = createPolicyKey({
                            policyKey: `${moduleName}+${pageName}+${tabName}+${tagActionElement?.name}`,
                        });
                    });
                    tabElement?.subTabs?.forEach((subTabElement) => {
                        const subTabName = subTabElement?.name;
                        subTabElement?.actions?.forEach((subTagActionElement) => {
                            subTagActionElement.policy = createPolicyKey({
                                policyKey: `${moduleName}+${pageName}+${tabName}+${subTabName}+${subTagActionElement?.name}`,
                            });
                        });
                    });
                });
            });
            bulkUpdateModule.push({
                updateOne: {
                    filter: { _id: ObjectId(moduleElement._id) },
                    update: { $set: { ...moduleElement } },
                },
            });
        });
        await moduleSchema.bulkWrite(bulkUpdateModule);
        const roleDatas = await role.find().lean();
        roleDatas?.forEach((roleElement) => {
            const modulePolicy = [];
            roleElement?.modules?.forEach((moduleElement) => {
                const moduleList = moduleData.find(
                    (moduleDataElement) =>
                        String(moduleDataElement._id) === String(moduleElement._id),
                );
                if (moduleList) {
                    const moduleName = moduleList?.name;
                    moduleElement?.pages?.forEach((pageElement) => {
                        const pageName = pageElement?.name;
                        pageElement?.actions?.forEach((actionElement) => {
                            actionElement.policy = createPolicyKey({
                                policyKey: `${moduleName}+${pageName}+${actionElement?.name}`,
                            });
                            modulePolicy.push(actionElement?.policy);
                        });
                        pageElement?.tabs?.forEach((tabElement) => {
                            const tabName = tabElement?.name;
                            tabElement?.actions?.forEach((tagActionElement) => {
                                tagActionElement.policy = createPolicyKey({
                                    policyKey: `${moduleName}+${pageName}+${tabName}+${tagActionElement?.name}`,
                                });
                                modulePolicy.push(tagActionElement.policy);
                            });
                            tabElement?.subTabs?.forEach((subTabElement) => {
                                const subTabName = subTabElement?.name;
                                subTabElement?.actions?.forEach((subTagActionElement) => {
                                    subTagActionElement.policy = createPolicyKey({
                                        policyKey: `${moduleName}+${pageName}+${tabName}+${subTabName}+${subTagActionElement?.name}`,
                                    });
                                    modulePolicy.push(subTagActionElement.policy);
                                });
                            });
                        });
                    });
                }
            });
            if (modulePolicy.length)
                bulkUpdateRole.push({
                    updateOne: {
                        filter: { _id: ObjectId(roleElement._id) },
                        update: { $set: { policy: modulePolicy } },
                    },
                });
        });
        await role.bulkWrite(bulkUpdateRole);
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    bulkUpdateRole,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
module.exports = {
    list_id,
    insert,
    department_list,
    report_to_list,
    addPolicyKey,
};
