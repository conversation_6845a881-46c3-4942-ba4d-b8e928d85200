const { DS_DATA_RETRIEVED } = require('./constants');

module.exports = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next))
        .then(({ statusCode = 200, message = DS_DATA_RETRIEVED, data = {} }) => {
            const translatedMessage = req.t(message);
            res.status(statusCode).send({ message: translatedMessage, data });
        })
        .catch((err) => next(err));
};
