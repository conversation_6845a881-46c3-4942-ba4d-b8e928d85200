const institution = require('../models/institution');
const courseScheduleSetting = require('../models/course_schedule_setting');
const curriculum = require('../models/digi_curriculum');
const program = require('../models/digi_programs');
const course_schedule = require('../models/course_schedule');
const course_schedule_delivery_settings = require('../models/course_schedule_delivery_settings');
const {
    get,
    get_list,
    get_list_sort,
    insert,
    update_condition_array_filter,
    update_condition,
    bulk_write,
} = require('../base/base_controller');
const { response_function, responseFunctionWithRequest } = require('../utility/common');
const { convertToMongoObjectId } = require('../utility/common');

async function insertRemoteScheduling(req, res) {
    try {
        const {
            headers: { _institution_id },
            body,
            params: { programId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const courseScheduleSettingCheck = await get(courseScheduleSetting, {
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        let obj = {};
        let doc = '';
        // body.term = body.term.toLowerCase();
        if (!courseScheduleSettingCheck.status) {
            //Insert
            obj = {
                _institution_id,
                programs: {
                    _program_id: programId,
                    remoteScheduling: [body],
                },
            };
            doc = await insert(courseScheduleSetting, obj);
        } else {
            const programInd = courseScheduleSettingCheck.data.programs.findIndex(
                (ele) => ele._program_id.toString() === programId.toString(),
            );

            const query = { _institution_id: convertToMongoObjectId(_institution_id) };
            if (programInd != -1) {
                //Check already exist
                const remoteSchInd = courseScheduleSettingCheck.data.programs[
                    programInd
                ].remoteScheduling.findIndex(
                    (ele) =>
                        ele.meetingTitle.toLowerCase() === body.meetingTitle.toLowerCase() &&
                        ele.term === body.term &&
                        ele.levelId.toString() == body.levelId.toString() &&
                        ele.isDeleted == false,
                );
                if (remoteSchInd != -1)
                    return res
                        .status(410)
                        .send(
                            responseFunctionWithRequest(
                                req,
                                410,
                                false,
                                req.t('MEETING_TITLE_ALREADY_EXIST'),
                            ),
                        );
                //Program Present
                obj = {
                    $push: {
                        'programs.$[p].remoteScheduling': body,
                    },
                };
                filter = {
                    arrayFilters: [{ 'p._program_id': programId }],
                };
                doc = await update_condition_array_filter(
                    courseScheduleSetting,
                    query,
                    obj,
                    filter,
                );
            } else {
                //Program not Present
                obj = {
                    $push: {
                        programs: {
                            _program_id: programId,
                            remoteScheduling: body,
                        },
                    },
                };
                doc = await update_condition(courseScheduleSetting, query, obj);
            }
        }

        if (doc.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('REMOTE_SCHEDULING_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_ADD_REMOTE_SCHEDULING'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('ERROR'), error.toString()));
    }
}
async function listRemoteScheduling(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { programId },
        } = req;

        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );

        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, req.t('INSTITUTION_NOT_FOUND')));

        const courseScheduleSettingData = await get(courseScheduleSetting, {
            _institution_id: convertToMongoObjectId(_institution_id),
        });

        if (!courseScheduleSettingData.status)
            return res.status(200).send(response_function(res, 200, false, req.t('NO_RECORDS')));
        const programInd = courseScheduleSettingData.data.programs.findIndex(
            (ele) => ele._program_id.toString() === programId.toString(),
        );

        if (programInd == -1)
            return res.status(200).send(response_function(res, 200, false, req.t('NO_RECORDS')));
        const remote_scheduling_data = courseScheduleSettingData.data.programs[
            programInd
        ].remoteScheduling.filter((ele) => ele.isDeleted == false);

        return res
            .status(200)
            .send(
                response_function(
                    res,
                    201,
                    true,
                    req.t('LIST_REMOTE_SCHEDULING'),
                    remote_scheduling_data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR'), error.toString()));
    }
}
async function updateRemoteScheduling(req, res) {
    try {
        const {
            headers: { _institution_id },
            body,
            params: { programId, id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, req.t('INSTITUTION_NOT_FOUND')));

        const courseScheduleSettingCheck = await get(courseScheduleSetting, {
            _institution_id: convertToMongoObjectId(_institution_id),
        });
        const programInd = courseScheduleSettingCheck.data.programs.findIndex(
            (ele) => ele._program_id.toString() == programId.toString(),
        );
        if (programInd != -1) {
            //Check already exist
            const remote_sch_ind = courseScheduleSettingCheck.data.programs[
                programInd
            ].remoteScheduling.findIndex(
                (ele) =>
                    ele.meetingTitle.toLowerCase() == body.meetingTitle.toLowerCase() &&
                    ele.term === body.term &&
                    ele.levelId.toString() == body.levelId.toString() &&
                    ele._id.toString() != id.toString() &&
                    ele.isDeleted == false,
            );
            if (remote_sch_ind != -1)
                return res
                    .status(410)
                    .send(response_function(res, 410, false, req.t('MEETING_TITLE_ALREADY_EXIST')));
            const queryUpdate = {
                _institution_id: convertToMongoObjectId(req.headers._institution_id),
            };
            const obj = {
                $set: {
                    'programs.$[pid].remoteScheduling.$[rid].meetingTitle': body.meetingTitle,
                    'programs.$[pid].remoteScheduling.$[rid].meetingUrl': body.meetingUrl,
                    'programs.$[pid].remoteScheduling.$[rid].meetingUsername': body.meetingUsername,
                    'programs.$[pid].remoteScheduling.$[rid].associatedEmail': body.associatedEmail,
                    'programs.$[pid].remoteScheduling.$[rid].gender': body.gender,
                    'programs.$[pid].remoteScheduling.$[rid].meetingId': body.meetingId,
                    'programs.$[pid].remoteScheduling.$[rid].passCode': body.passCode,
                    'programs.$[pid].remoteScheduling.$[rid].password': body.password,
                    'programs.$[pid].remoteScheduling.$[rid].term': body.term,
                    'programs.$[pid].remoteScheduling.$[rid].levelId': body.levelId,
                    'programs.$[pid].remoteScheduling.$[rid].levelName': body.levelName,
                    'programs.$[pid].remoteScheduling.$[rid].yearId': body.yearId,
                    'programs.$[pid].remoteScheduling.$[rid].yearName': body.yearName,
                    'programs.$[pid].remoteScheduling.$[rid].apiKey': body.apiKey,
                    'programs.$[pid].remoteScheduling.$[rid].apiSecretKey': body.apiSecretKey,
                    'programs.$[pid].remoteScheduling.$[rid].remotePlatform': body.remotePlatform,
                },
            };
            filter = {
                arrayFilters: [{ 'pid._program_id': programId }, { 'rid._id': id }],
            };
            const doc = await update_condition_array_filter(
                courseScheduleSetting,
                queryUpdate,
                obj,
                filter,
            );
            if (doc.status) {
                // Updating in course schedule delivery setting
                const course_schedule_delivery_settings_data = await get_list(
                    course_schedule_delivery_settings,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _infra_id: convertToMongoObjectId(id),
                    },
                    { _id: 1 },
                );
                if (course_schedule_delivery_settings_data.status) {
                    const bulk_data = [];
                    for (eleCourseScheduleDeliverySettings of course_schedule_delivery_settings_data.data) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(
                                        eleCourseScheduleDeliverySettings._id,
                                    ),
                                },
                                update: {
                                    $set: {
                                        infra_name: body.meetingTitle,
                                    },
                                },
                            },
                        });
                    }
                    if (bulk_data.length > 0)
                        await bulk_write(course_schedule_delivery_settings, bulk_data);
                }

                // Updating in Course Schedule
                const course_schedule_data = await get_list(
                    course_schedule,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _infra_id: convertToMongoObjectId(id),
                    },
                    { _id: 1 },
                );
                if (course_schedule_data.status) {
                    const bulk_data_cs = [];
                    for (eleCourseScheduleData of course_schedule_data.data) {
                        bulk_data_cs.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(eleCourseScheduleData._id),
                                },
                                update: {
                                    $set: {
                                        infra_name: body.meetingTitle,
                                    },
                                },
                            },
                        });
                    }
                    if (bulk_data_cs.length > 0) await bulk_write(course_schedule, bulk_data_cs);
                }

                return res
                    .status(200)
                    .send(
                        response_function(
                            res,
                            200,
                            true,
                            req.t('REMOTE_SCHEDULING_UPDATED_SUCCESSFULLY'),
                            doc.data,
                        ),
                    );
            }
            return res
                .status(410)
                .send(
                    response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_UPDATE_REMOTE_SCHEDULING'),
                        doc.data,
                    ),
                );
        }
        return res.status(410).send(response_function(res, 410, false, req.t('PROGRAM_NOT_FOUND')));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR'), error.toString()));
    }
}
async function deleteRemoteScheduling(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { programId, id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, req.t('INSTITUTION_NOT_FOUND')));
        // Course Schedule Check
        const { status: cs_status } = await get(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(req.headers._institution_id),
                // _institution_calendar_id: ObjectId(_institution_calendar_id),
                _infra_id: convertToMongoObjectId(req.params.id),
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (cs_status)
            return res
                .status(409)
                .send(
                    response_function(
                        res,
                        409,
                        false,
                        req.t('UNABLE_TO_REMOVE_INFRA_BECAUSE_ITS_SCHEDULED'),
                        req.t('UNABLE_TO_REMOVE_INFRA_BECAUSE_ITS_SCHEDULED'),
                    ),
                );

        const query = { _institution_id: convertToMongoObjectId(req.headers._institution_id) };
        const obj = {
            $set: {
                'programs.$[pid].remoteScheduling.$[rid].isDeleted': true,
            },
        };
        filter = {
            arrayFilters: [{ 'pid._program_id': programId }, { 'rid._id': id }],
        };
        const doc = await update_condition_array_filter(courseScheduleSetting, query, obj, filter);
        if (doc.status)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('REMOTE_SCHEDULING_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_DELETE_REMOTE_SCHEDULING'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR'), error.toString()));
    }
}
async function programCurriculumLevelYearList(req, res) {
    const {
        headers: { _institution_id },
        params: { programId },
    } = req;

    const institutionCheck = await get(
        institution,
        { _id: convertToMongoObjectId(_institution_id) },
        { _id: 1 },
    );

    if (!institutionCheck.status)
        return res
            .status(404)
            .send(responseFunctionWithRequest(req, 404, false, req.t('INSTITUTION_NOT_FOUND')));

    const programData = await get(
        program,
        { _id: convertToMongoObjectId(programId), isDeleted: false },
        { term: 1 },
    );
    if (!programData.status)
        return res
            .status(404)
            .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_NOT_FOUND'), []));
    const curriculumData = await get_list_sort(
        curriculum,
        { _program_id: convertToMongoObjectId(programId), isDeleted: false },
        { _id: 1, year_level: 1, createdAt: 1 },
        { curriculum_name: -1 },
    );
    if (!curriculumData.status)
        return res
            .status(404)
            .send(responseFunctionWithRequest(req, 200, true, req.t('CURRICULUM_NOT_FOUND'), []));

    const courseScheduleSettingData = await get(courseScheduleSetting, {
        _institution_id: convertToMongoObjectId(_institution_id),
    });
    let programInd = -1;
    if (courseScheduleSettingData.status) {
        programInd = courseScheduleSettingData.data.programs.findIndex(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
    }
    const yearLevelWiseRemoteScheduleData = [];
    for (const data of curriculumData.data[0].year_level) {
        for (const level of data.levels) {
            let remoteScheduleData = [];
            if (programInd != -1) {
                remoteScheduleData = courseScheduleSettingData.data.programs[
                    programInd
                ].remoteScheduling.filter(
                    (ele) =>
                        ele.yearId == data._id.toString() &&
                        ele.levelId == level._id.toString() &&
                        ele.isDeleted == false,
                );
            }
            if (data._pre_requisite_id) continue;
            yearLevelWiseRemoteScheduleData.push({
                year_id: data._id,
                year: data.y_type,
                level_name: level.level_name,
                level_id: level._id,
                remote_schedule_data: remoteScheduleData,
            });
        }
    }

    const termWiseData = [];
    for (term of programData.data.term) {
        const termWiseRemoteScheduleData = [];
        for (data of yearLevelWiseRemoteScheduleData) {
            const filteredTermData = data.remote_schedule_data.filter(
                (ele) => ele.term == term.term_name,
            );
            termWiseRemoteScheduleData.push({
                year_id: data.year_id,
                year: data.year,
                level_name: data.level_name,
                level_id: data.level_id,
                remote_schedule_data: filteredTermData,
            });
        }
        const obj = {
            termName: term.term_name,
            termNo: term.term_no,
            levels: termWiseRemoteScheduleData,
        };
        termWiseData.push(obj);
    }

    return res
        .status(200)
        .send(
            responseFunctionWithRequest(
                req,
                201,
                true,
                req.t('LIST_REMOTE_SCHEDULING'),
                termWiseData,
            ),
        );
}
module.exports = {
    insertRemoteScheduling,
    listRemoteScheduling,
    updateRemoteScheduling,
    deleteRemoteScheduling,
    programCurriculumLevelYearList,
};
