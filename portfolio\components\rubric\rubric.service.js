const RubricModel = require('./rubric.model');
const UserModel = require('../../../lib/models/user');

const {
    addDocuments,
    updateDocument,
    getDocument,
    getDocuments,
} = require('../../base/base.helper');
const { checkIfTrue } = require('../../common/utils/common.util');

const rubricProjection = {
    name: 1,
    desc: 1,
    scoring: 1,
    parameters: 1,
    isGlobal: 1,
    createdBy: 1,
    updatedAt: 1,
    isTemplate: 1,
};

const createRubric = async ({
    name = '',
    desc = '',
    scoring = '',
    parameters = [],
    isTemplate = false,
    isGlobal = false,
    userId,
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    const result = await addDocuments({
        Model: RubricModel,
        data: {
            name,
            desc,
            scoring,
            parameters,
            isTemplate,
            isGlobal,
            createdBy: { name: user.name, id: userId },
        },
    });

    return result;
};

const getRubrics = async ({ rubricId, isTemplate, isGlobal }) => {
    if (rubricId) {
        const rubric = await getDocument({
            Model: RubricModel,
            query: { isDeleted: false, _id: rubricId },
            project: rubricProjection,
            canThrowError: false,
        });

        return rubric;
    }

    if (checkIfTrue(isGlobal)) {
        const rubrics = await RubricModel.find(
            { isDeleted: false, isGlobal: true },
            rubricProjection,
        ).lean();

        return rubrics;
    }

    const rubrics = await getDocuments({
        Model: RubricModel,
        query: {
            isDeleted: false,
            ...(isTemplate && { isTemplate: checkIfTrue(isTemplate) }),
            ...(isGlobal && { isGlobal: checkIfTrue(isGlobal) }),
        },
        project: rubricProjection,
        canThrowError: false,
    });

    return rubrics;
};

const updateRubric = async ({
    rubricId,
    name,
    desc,
    scoring,
    parameters,
    createdBy,
    isTemplate,
    isGlobal,
}) => {
    await updateDocument({
        Model: RubricModel,
        query: { isDeleted: false, _id: rubricId },
        updateDoc: { $set: { name, desc, scoring, parameters, createdBy, isTemplate, isGlobal } },
    });
};

const deleteRubric = async ({ rubricId }) => {
    await updateDocument({
        Model: RubricModel,
        query: { isDeleted: false, _id: rubricId },
        updateDoc: { isDeleted: true },
    });
};

const createGlobalRubric = async ({
    name = '',
    desc = '',
    scoring = '',
    parameters = [],
    userId,
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();
    if (!user) {
        throw new NotFoundError('USER_NOT_FOUND');
    }

    await RubricModel.deleteMany({ isGlobal: true });

    const result = await addDocuments({
        Model: RubricModel,
        data: { name, desc, scoring, parameters, isGlobal: true, createdBy: user },
    });

    return result;
};

module.exports = {
    createRubric,
    getRubrics,
    updateRubric,
    deleteRubric,
    createGlobalRubric,
};
