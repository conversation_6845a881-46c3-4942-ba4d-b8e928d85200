const Joi = require('joi');
const { objectIdRQSchema, stringRQSchema, numberSchema } = require('../utility/validationSchemas');

function getCourseCourseCoordinatorsValidate() {
    const schema = {
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().min(3).required(),
            _user_id: Joi.string().allow('').required(),
            _institution_calendar_id: Joi.string().alphanum().length(24),
        }),
    };
    return schema;
}

const studentGroupWithDeliveryValidator = Joi.object({
    institutionCalendarId: objectIdRQSchema,
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    levelNo: stringRQSchema,
    term: stringRQSchema,
    rotationCount: numberSchema,
});

module.exports = {
    getCourseCourseCoordinatorsValidate: getCourseCourseCoordinatorsValidate(),
    studentGroupWithDeliveryValidator,
};
