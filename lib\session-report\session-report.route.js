const express = require('express');
const catchAsync = require('../utility/catch-async');
const {
    getAttendanceReport,
    getProgramCourseList,
    getScheduleDetails,
    getUserPrograms,
    getAttendanceReportExport,
} = require('./session-report.controller');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');
const route = express.Router();

// Attendance Report
route.get(
    '/attendanceReport/:sessionDate',
    [userPolicyAuthentication(['curriculum_monitoring:dashboard:view'])],
    catchAsync(getAttendanceReport),
);
route.get(
    '/programCourseList/:sessionDate',
    [userPolicyAuthentication(['curriculum_monitoring:dashboard:view'])],
    catchAsync(getProgramCourseList),
);
route.get(
    '/attendanceReportExport/:sessionDate',
    [userPolicyAuthentication(['curriculum_monitoring:dashboard:all_deliveries_export'])],
    catchAsync(getAttendanceReportExport),
);
route.get(
    '/scheduleDetails',
    [userPolicyAuthentication(['curriculum_monitoring:dashboard:view'])],
    catchAsync(getScheduleDetails),
);
route.get(
    '/userProgramList',
    [userPolicyAuthentication(['curriculum_monitoring:dashboard:filter_program'])],
    catchAsync(getUserPrograms),
);

module.exports = route;
