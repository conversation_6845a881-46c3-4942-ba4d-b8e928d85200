const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const {
    USER,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    YEAR_LEVEL_AUTHOR,
    YEAR_LEVEL_AUTHOR_TYPE_MODULE,
} = require('../utility/constants');

const yearLevelAuthorSchema = new Schema(
    {
        users: [
            {
                userId: {
                    type: ObjectId,
                    ref: USER,
                },
                modules: [
                    {
                        type: String,
                        enum: [
                            YEAR_LEVEL_AUTHOR_TYPE_MODULE.STUDENT_GROUP,
                            YEAR_LEVEL_AUTHOR_TYPE_MODULE.SCHEDULE,
                        ],
                    },
                ],
            },
        ],
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        curriculumId: { type: ObjectId, ref: DIGI_CURRICULUM },
        yearName: { type: String },
        levelName: { type: String },
        // termName: { type: String },
        authorType: {
            type: String,
            enum: [
                YEAR_LEVEL_AUTHOR_TYPE_MODULE.YEAR,
                YEAR_LEVEL_AUTHOR_TYPE_MODULE.LEVEL,
                YEAR_LEVEL_AUTHOR_TYPE_MODULE.STUDENT_GROUP,
                YEAR_LEVEL_AUTHOR_TYPE_MODULE.SCHEDULE,
            ],
        },
    },
    { timestamps: true },
);
module.exports = model(YEAR_LEVEL_AUTHOR, yearLevelAuthorSchema);
