const { EMQ, MQ, CQ, EQ, MCQ, TF, SAQ } = require('./enums');
const constants = Object.freeze({
    ROOM_TYPE: {
        LAB: 'lab',
        LECTURE_ROOM: 'lecture_room',
        CLINIC: 'clinic',
    },

    GENDER: {
        MALE: 'male',
        FEMALE: 'female',
        BOTH: 'both',
    },
    DEPARTMENT_MASTER: {
        DEPARTMENT: 'department',
        DIVISION: 'division',
    },
    MODEL: {
        COURSE: 'course',
        MODULE: 'module',
        ELECTIVE: 'elective',
    },
    USER_STATUS: {
        REGISTERED: 'registered',
        REGISTRATION_PENDING: 'registration pending',
        INACTIVE: 'inactive',
        REQUEST_SENT: 'request sent',
    },
    EXPIRED: 'expired',
    PRODUCTION_ENV: 'production',
    LOCAL_ENV: 'local',
    DEV_ENV: 'development',
    COLLEGE: 'colleges',
    COUNTRY: 'countries',
    ACCREDITATION_TYPE: 'accreditation_type',
    CITY: 'cities',
    STATES: 'states',
    COUNTRY_STATE_CITY: 'country_state_cities',
    LMS: 'lms',
    LMS_REVIEW: 'lms_review',
    LMS_ROLES: 'lms_roles',
    LMS_STUDENT_ABSENCE_WARNING_CALCULATION: 'lms_student_absence_warning_calculation',
    LMS_CATEGORYSCHM: 'lms_categories',
    LMS_CATEGRORY_LEAVE_TYPE: 'lms_leave_type',
    LMS_STAFF_LEAVE: 'staff_leave',
    LMS_STAFF_LEAVE_SETTINGS: 'staff_leave_settings',
    COURSE: 'courses',
    DEPARTMENT: 'departments',
    INFRASTRUCTURE: 'infrastructures',
    INFRASTRUCTUREEVENTS: 'infrastructure_events',
    INFRASTRUCTURE_DELIVERY_TYPE: 'infrastructure_delivery_types',
    INFRASTRUCTURE_MANAGEMENT: 'infrastructure_managements',
    INFRASTRUCTURE_EVENTS: 'infrastructure_events',
    DAY_GROUP: 'day_groups',
    TIME_GROUP: 'time_groups',
    COLLEGE_BUILDING: 'college_buildings',
    LECTURE_ROOM: 'lecture_rooms',
    HOSPITAL: 'hospitals',
    MODULES: 'modules',
    POSITION: 'positions',
    PROGRAM: 'programs',
    ROLE: 'roles',
    PERMISSION: 'permissions',
    PRIVILEGE: 'privileges',
    ROLE_SET: 'role_sets',
    PERMISSION_SET: 'permission_sets',
    SEMESTER: 'semesters',
    STAFF: 'staffs',
    STUDENT: 'students',
    TOPIC: 'topics',
    UNIVERSITY: 'universities',
    STUDENT_GROUP: 'student_groups',
    INSTITUTION_CALENDAR: 'institution_calendars',
    PROGRAM_CALENDAR: 'program_calendars',
    CALENDAR_EVENT: 'calendar_events',
    PROGRAM_CALENDAR_EVENT: 'program_calendar_events',
    PROGRAM_CALENDAR_COURSE: 'program_calendar_courses',
    INSTITUTION: 'institutions',
    INSTITUTION_V2: 'institutionv2',
    DEPARTMENT_DIVISIONS: 'department_divisions',
    DEPARTMENT_SUBJECT: 'department_subjects',
    CREDIT_HOURS_MASTER: 'credit_hours_masters',
    CREDIT_HOURS_CALC: 'credit_hours_calcs',
    CREDIT_HOURS_INDIVIDUAL: 'credit_hours_individuals',
    SESSION_TYPE: 'session_types',
    SESSION_ORDER: 'session_orders',
    RESET_TOKEN: 'reset_tokens',
    INSTITUTION_CALENDAR_EVENT_REVIEW: 'institution_calendar_event_reviews',
    NOTIFICATIONS: 'notifications',
    USER: 'users',
    DIGI_CHAT: 'digi_chat',
    USER_HISTORY: 'user_historys',
    STUDENT_LEAVE_REGISTER: 'student_leave_registers',
    STAFF_COMMITTEE: 'staff_committees',
    ROLES_OFFICES_LIST: 'roles_offices_lists',
    ROLES_PERMISSION: 'roles_permissions',
    ROLE_ASSIGN_TO_STAFF: 'role_assign_to_staff',
    INFRASTRUCTURE_EVENT_EXAM: 'infrastructure_event_exams',
    INFRASTRUCTURE_DELIVERY_MEDIUM: 'infrastructure_delivery_mediums',
    COURSE_STAFF_ALLOCATION: 'course_staff_allocation',
    COURSE_SCHEDULE: 'course_schedules',
    COURSE_SCHEDULE_SETTING: 'course_schedule_setting',
    COURSE_SCHEDULE_DELIVERY_SETTINGS: 'course_schedule_delivery_settings',
    COURSE_MANAGEMENT_SETTING: 'course_management_setting',
    SESSION: 'sessions',
    DOCUMENT_MANAGER: 'document_managers',
    USER_VACCINATION_DETAILS: 'user_vaccination_details',
    VACCINATION: 'vaccination',
    APP_VERSION_CONTROL: 'app_version_control',
    STUDENT_CRITERIA_MANIPULATION: 'student_criteria_manipulation',

    OFFICE_LIST: 'office',
    ROLE_LIST: 'role',

    ACTIVE: 'active',
    ARCHIVED: 'archived',
    INACTIVE: 'inactive',
    BLOCKED: 'blocked',

    IMPORTED: 'imported',
    PASSWORD_CONFIRMED: 'password_confirmed',
    PROFILE_UPDATING: 'profile_updating',
    RESUBMITTED: 're_submitted',
    PROFILE_UPDATED: 'profile_updated',
    VERIFICATION_DONE: 'verification_done',
    PENDING: 'pending',
    CORRECT: 'correct',
    CORRECTION_REQUIRED: 'error',
    VALID: 'valid',
    INVALID: 'invalid',
    DONE: 'success',
    PUBLISHED: 'published',

    STUDENTS: 'student',
    FACULTY: 'faculty',

    PRIMARY: 'primary',
    AUXILIARY: 'auxiliary',
    SECONDARY: 'secondary',

    ACADEMIC: 'academic',
    ADMINISTRATION: 'administration',
    ADMIN: 'admin',
    BOTH: 'both',
    ONLINE: 'online',
    ON_SITE: 'on_site',
    FULL_TIME: 'full_time',
    PART_TIME: 'part_time',
    BY_DATE: 'by_date',
    BY_DAY: 'by_day',
    DAYS: {
        SUNDAY: 'sunday',
        MONDAY: 'monday',
        TUESDAY: 'tuesday',
        WEDNESDAY: 'wednesday',
        THURSDAY: 'thursday',
        FRIDAY: 'friday',
        SATURDAY: 'saturday',
    },
    WEEKDAYS: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],
    GREGORIAN: 'gregorian',
    HIJRI: 'hijri',
    EVENT_TYPE: {
        EXAM: 'exam',
        HOLIDAY: 'holiday',
        TRAINING: 'training',
        ORIENTATION: 'orientation',
        GENERAL: 'general',
    },
    NOTIFY_VIA: {
        ALL: 'all',
        MOBILE: 'mobile',
        EMAIL: 'email',
        DIGISCHEDULER: 'digischeduler',
        DIGICLASS: 'digiclass',
    },
    DEAN: 'dean',
    REVIEWER: 'reviewer',
    FORWARDER: 'forwarder',
    APPROVER: 'approver',

    EVENT_WHOM: {
        STUDENT: 'student',
        STAFF: 'staff',
        BOTH: 'both',
    },

    EVENT_MODE: {
        ONLINE: 'online',
        OFFLINE: 'offline',
    },

    NOTIFICATION_PRIORITY: {
        LOW: 'low',
        MEDIUM: 'medium',
        HIGH: 'high',
    },

    RELATION: {
        PARENT: 'parent',
        GUARDIAN: 'guardian',
        FATHER: 'father',
        MOTHER: 'mother',
        SIBLING: 'sibling',
        GRANDPARENT: 'grandparent',
        AUNT: 'aunt',
        UNCLE: 'uncle',
        SPOUSE: 'spouse',
    },

    BATCH: {
        REGULAR: 'regular',
        INTERIM: 'interim',
        BOTH: 'both',
    },

    CALENDAR: {
        INSTITUTION: 'institution',
        PROGRAM: 'program',
    },

    YEAR_LEVEL: {
        YEAR: 'year',
        LEVEL: 'level',
    },

    STUDENT_GROUP_MODE: {
        FYD: 'foundation',
        COURSE: 'course',
        ROTATION: 'rotation',
    },

    DIRECTION: {
        UP: 'up',
        DOWN: 'down',
    },

    BY_DATE_WEEK: {
        DATE: 'date',
        WEEK: 'week',
    },

    DAYS_MODE: {
        DAILY: 'daily',
        WEEKLY: 'weekly',
    },
    COURSE_MANAGEMENT_SESSION_TYPE: {
        EXTRA_CURRICULAR: 'extra_curricular',
        BREAK: 'break',
    },
    TIME_GROUP_BOOKING_TYPE: {
        ONSITE: 'onsite',
        REMOTE: 'remote',
    },

    FLOOR_CATEGORY: {
        LEVEL: 'level',
        BASEMENT: 'basement',
    },

    BUILDING_TYPE: {
        HOSPITAL: 'hospital',
        COLLEGE: 'college',
    },

    LEAVE_FLOW_TYPE: {
        REVIEW: 'review',
        FORWARD: 'forward',
        APPROVE: 'approve',
    },
    PAYMENT_STATUS: {
        PAID: 'paid',
        UNPAID: 'unpaid',
    },
    LEAVE_FLOW_TYPE_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved',
        REJECTED: 'rejected',
    },
    LMS_STAFF_TYPE: {
        ACADEMIC_AND_ADMINISTRATIVE: 'academic_and_administrative_staff',
        ADMINISTRATIVE: 'administrative',
    },
    LMS_ROLE_TYPE: {
        REVIEWER: 'reviewer',
        FORWARDER: 'forwarder',
        APPROVER: 'approver',
        REPORT_ABSENCE: 'report_absence',
    },
    LEAVE_TYPE: {
        ONDUTY: 'on_duty',
        LEAVE: 'leave',
        PERMISSION: 'permission',
        REPORT_ABSENCE: 'report_absence',
    },
    ROLE_ASSIGN: 'role_assigns',
    DIGI_INSTITUTE: 'digi_institute',
    DIGI_PROGRAM: 'digi_programs',
    DIGI_DEPARTMENT_SUBJECT: 'digi_department_subject',
    CURRICULUM: 'curriculums',
    SESSION_DELIVERY_TYPES: 'session_delivery_types',
    DIGI_UNIVERSITY: 'digi_university',
    DIGI_COLLEGE: 'digi_college',
    DIGI_COURSE: 'digi_course',
    DIGI_SESSION_ORDER: 'digi_session_order',
    DIGI_COURSE_ASSIGN: 'digi_course_assign',
    DIGI_COURSE_GROUP: 'digi_course_group',
    STUDENT_SESSION_SURVEY: 'student_session_survey',
    INSTITUTION_SETTINGS: 'institution_settings',
    UNIVERSITY_INSTITUTIONS: 'university_institutions',
    // COURSE_SCHEDULE_SETTING: "course_schedule_setting",
    HOURS_AS: {
        TOTAL: 'total',
        SPLIT_UP: 'split_up',
    },
    SET_VALUE_AS: {
        FIXED_VALUE: 'fixed_value',
        RANGE_OF_VALUES: 'range_of_values',
    },
    INSTITUTION_TYPE: {
        INDIVIDUAL: 'individual',
        GROUP: 'group',
    },
    COURSE_TYPE: {
        STANDARD: 'standard',
        SELECTIVE: 'selective',
        INDEPENDENT: 'independent',
    },
    FRAMEWORK: 'frameworks',
    MAPPING_TYPES: {
        IMPACT: 'impact_mapping_types',
        CONTENT: 'content_mapping_types',
    },
    MAPPING_TYPE_LIST: {
        IMPACT: 'impact',
        ALIGNMENT: 'alignment',
    },
    CONTENT_MAPPING_TYPE: {
        REQUIRED: 'required',
        OPTIONAL: 'optional',
    },
    MASTER_TYPE: {
        PROGRAM: 'program',
        PREREQUISITE: 'pre-requisite',
    },
    GRADUATE_TYPE: {
        UG: 'ug',
        PG: 'pg',
    },
    UG: 'undergraduate',
    PG: 'postgraduate',
    DASHBOARD_SETTING: 'dashboard_settings',

    /* DEPARTMENT_TYPE: {
      STUDY: 'study',
      TRAINING: 'training',
      RESEARCH: 'research'
  }, */
    // from DA
    DS_NONE: 'none',
    // collection names
    DS_USER: 'User',
    DS_STUDENT: 'Student',
    DS_FACULTY: 'Faculty',
    DS_COUNTRY: 'Country',
    DS_TEST_CENTER: 'Test_center',
    DS_ROLE: 'Role',
    DS_INSTITUTION: 'Institution',
    DS_MODULE: 'Module',
    DS_PERMISSION: 'Permission',
    DS_UI_ELEMENT: 'ui_element',
    DS_TOKEN: 'token',
    DS_ITEM_TYPE: 'Item_type',
    DS_ITEM: 'Item',
    DS_EXAM_TYPE: 'Exam_type',
    DS_FRAMEWORK: 'Framework',
    DS_ASSESSMENT: 'assessment',
    DS_ASSESSMENT_ANSWER: 'assessmentAnswer',
    DS_TAXONOMY: 'Taxonomy',
    DS_MAPPING: 'Mapping',
    DS_PROGRAM: 'Program',
    DS_CLO_MAPPING: 'Clo_mapping',
    DS_ACTIVITY_LOG: 'activity_log',
    DS_ASSESSMENT_TYPE: 'assessment_type',
    DS_ATTEMPT_TYPE: 'attempt_type',
    DS_EXAM_TIME: 'exam_time',
    DS_EXAM_DATE: 'exam_date',
    DS_EXAM: 'exam',
    DS_EXAM_COURSE_GROUP: 'exam_course_group',
    DS_COURSE_GROUP: 'course_group',
    DS_TIME_TABLE: 'time_table',
    DS_UPLOAD_GROUP: 'upload_group',
    DS_ACADEMIC_YEAR: 'academic_year',

    DS_TYPE_STUDENT: 'student',
    DS_TYPE_FACULTY: 'faculty',

    DS_FORMATIVE: 'formative',
    DS_SUMMATIVE: 'summative',

    DS_PRODUCTION_ENV: 'production',
    DS_LOCAL_ENV: 'local',
    DS_DEV_ENV: 'development',
    DS_TEST_ENV: 'test',

    DS_ACTIVE: 'active',
    DS_INACTIVE: 'inactive',
    DS_BLOCKED: 'blocked',

    DS_SURVEY: 'survey',
    DS_PENDING: 'pending',
    DS_CORRECTION_REQUIRED: 'error',
    DS_DONE: 'success',
    DS_SETTINGS: 'setting',
    DS_ROLE_STUDENT: 'student',
    DS_ROLE_FACULTY: 'faculty',
    DS_ROLE_SUPER_ADMIN: 'super_admin',
    DS_SUPERADMINUSERNAME: '<EMAIL>',
    DS_SETTING_ID: '5ea702ad8c016c25c890682b',
    DS_MONGODB_ID_LENGTH: 24,

    // Strings
    DS_NAME_ALREADY_EXISTS: 'Name already exists',
    DS_NAME_OR_CODE_ALREADY_EXISTS: 'Name or code already exists',
    DS_NAME_OR_ROOM_NUMBER_ALREADY_EXISTS: 'Name or room number already exists',

    DS_ALREADY_EXISTS: 'Already exists',
    DS_INTERNAL_SERVER_ERROR: 'Internal server error',
    DS_DATA_RETRIEVED: 'DS_DATA_RETRIEVED',
    DS_NOT_FOUND: 'DS_NOT_FOUND', // 404 - file not found
    DS_NO_DATA_FOUND: 'DS_NO_DATA_FOUND', // 200 - empty array
    DS_ADDED: 'DS_ADDED',
    DS_UPDATED: 'DS_UPDATED',
    DS_ARCHIVED: 'DS_ARCHIVED',
    DS_ARCHIVED_FAILED: 'DS_ARCHIVED_FAILED',
    DS_RESTORE: 'DS_RESTORE',
    DS_RESTORE_FAILED: 'DS_RESTORE_FAILED',
    DS_DELETED: 'DS_DELETED',
    DS_ADD_FAILED: 'DS_ADD_FAILED',
    DS_UPDATE_FAILED: 'DS_UPDATE_FAILED',
    DS_DELETE_FAILED: 'DS_DELETE_FAILED',
    DS_GET_FAILED: 'DS_GET_FAILED',
    DS_CREATED: 'DS_CREATED',
    DS_CREATE_FAILED: 'DE_CREATE_FAILED',
    DS_DUPLICATE_FOUND: 'Duplicate found',
    DS_NOTHING_TO_UPDATE: 'Nothing to update',
    DS_INVALID_ID: 'Invalid Id',
    DS_INVALID_TYPE_FIELD: 'Exam Type must be string',
    DS_GROUPED: 'Grouped',
    DS_UNGROUPED: 'Ungrouped',
    DS_GROUP_FAILED: 'Grouping failed',
    DS_UNGROUP_FAILED: 'DS_UNGROUP_FAILED',
    DS_SAVED: 'DS_SAVED',
    DS_SAVE_FAILED: 'DS_SAVE_FAILED',

    DS_ALL: 'All',

    DS_IMPORT_ALERT_MSG:
        'Any conflicts with the existing data like mismatch, duplicates will be removed during import.',

    // Permissions strings
    DS_CREATE: 'create',
    DS_READ: 'view',
    DS_UPDATE: 'update',
    DS_DELETE: 'delete',
    DS_ALLOWED: 'allowed',

    DS_INVIGILATOR_TYPE_1: 'invigilator1',
    DS_INVIGILATOR_TYPE_2: 'invigilator2',
    DS_INVIGILATOR_TYPE_3: 'invigilator3',

    DS_THEME_KEY: 'theme',
    DS_SUBTHEME_KEY: 'sub-theme',
    DS_SELECTIVE_KEY: 'selective',
    DS_PROGRAM_KEY: 'program',
    DS_TERM_KEY: 'term',
    DS_YEAR_KEY: 'year',
    DS_PHASE_KEY: 'phase',
    DS_LEVEL_KEY: 'level',
    DS_CURRICULUM_KEY: 'curriculum',
    DS_ROTATION_GROUP_KEY: 'rotationGroup',
    DS_COURSE_KEY: 'course',
    DS_MODULE_KEY: 'module',
    DS_ELECTIVE_KEY: 'elective',
    DS_CLO_KEY: 'clo',
    DS_SUBJECT_KEY: 'subject',
    DS_TOPIC_KEY: 'topic',
    DS_SLO_KEY: 'slo',
    DS_STANDARD_KEY: 'standard',
    DS_PRE_REQUISITE: 'Pre-Requisite Course',

    // testcenter
    DS_REPEAT_SESSION: 'repeat_session',

    // signature
    DS_ADMIN_SIGNATURE: 'Dean',

    DS_INVIGILATORS_UNASSIGNED: 'invigilators_unassigned',
    DS_UNPUBLISHED_ASSESSMENTS: 'unpublished_assessments',
    DS_STUDENTS_UNFILLED: 'students_unfilled',

    // export user types
    DS_EXPORT_ALL_INVIGILATORS: 'allInvigilators',
    DS_EXPORT_INDIVIDUAL_INVIGILATORS: 'individualInvigilator',
    DS_EXPORT_ALL_STUDENTS: 'allStudents',

    DS_NOTIFICATION_NOT_SENT: 'notificationNotSent',

    // assessment status
    DS_STATUS_NOT_STARTED: 'Not Started',
    DS_GENERIC_TYPE: 'Generic Type',
    DS_VERSION: 'version',

    // exams
    DS_ON_GOING_EXAM: 'onGoingExam',
    DS_PREVIOUS_EXAM: 'previousExam',

    DS_SUBJECT_EXPERT_REVIEWER: 'subjectExpertReviewer',
    DS_MEDICAL_EDUCATIONIST: 'medicalEducationist',

    // Question bank setting
    DS_IMPORT_FILE_FORMART: 'importFileFormats',
    DS_EXPORT_FILE_FORMART: 'exportFileFormats',
    DS_PERMISSION_QUESTION_BANK_SETTINGS: 'permissions',

    // Item type
    DS_ASSESSMENT_ITEM_TYPE: 'assessment',
    DS_SURVEY_ITEM_TYPE: 'survey',

    DS_MULTIPLE_QUESTIONS_ITEM_TYPE: [EMQ, MQ, CQ, EQ],
    DS_MANDATORY_MULTIPLE_QUESTIONS_ITEM_TYPE: [EMQ, MQ, CQ],
    DS_ASSESSMENT_ITEM_TYPES: [MCQ, TF, SAQ, EMQ, MQ, CQ, EQ],

    // courses
    DS_COURSES: 'courses',

    DS_SHORT_CODE: 'shortCode',

    // Topic types
    DS_EXTRACT_SINGLE_ITEM: 'extractSingleItem',
    DS_EXTRACT_GROUP_OF_ITEMS: 'extractGroupOfItems',

    DS_WITHOUT_SECTION: 'without_section',
    DS_WITH_SECTION: 'with_section',

    DS_DIGI_ASSESS_WEB: 'DA',
    DS_DIGI_ASSESS_DESKTOP: 'DD',

    DS_ASSESSMENT_AUTHOR: 'assessmentAuthor',
    DS_ITEM_AUTHOR: 'itemAuthor',

    // Surveys setting
    DS_PROGRAM_SURVEYS: 'programSurveys',
    DS_GENERAL_SURVEYS: 'generalSurveys',

    // allowed file types
    DS_ITEM_IMAGE_FILE_TYPES: ['jpeg', 'jpg', 'png'],
    DS_ITEM_AUDIO_FILE_TYPES: ['mp3', 'mpeg', 'wav'],
    DS_ITEM_VIDEO_FILE_TYPES: ['mp4'],
    DS_ITEM_APPLICATION_FILE_TYPES: ['pdf'],
    DS_IDENTITY: 'identity',
    DS_ALIGNMENT: 'ALIGNMENT',
    DS_IMPACT: 'IMPACT',
    DS_CONTENT_MAP: 'CONTENT_MAP',

    DS_OPTIONAL: 'OPTIONAL',
    DS_REQUIRED: 'REQUIRED',

    AM: 'AM',
    PM: 'PM',
    SCHEDULE_TYPES: {
        SCHEDULE: 'schedule',
        REGULAR: 'regular',
        EVENT: 'event',
        SUPPORT_SESSION: 'support_session',
    },
    SCHEDULE_EVENT_TYPES: {
        GENERAL: 'general',
        EXAM: 'exam',
        TRAINING: 'training',
        FEEDBACK: 'feedback',
        COUNSELLING: 'counselling',
        LECTURE: 'lecture',
        TUTORIAL: 'tutorial',
        SEMINAR: 'seminar',
        INTERACTIVE_LECTURE: 'interactive_lecture',
        HOSPITAL_SESSION: 'hospital_session',
    },

    // activities
    ACTIVITIES: 'activity',

    // activities status
    DRAFT: 'draft',
    NOT_DRAFT: 'not_draft',
    COMPLETED: 'completed',
    MISSED: 'missed',
    STARTED: 'started',
    NOT_STARTED: 'not_started',
    QUESTION_TYPE: [MCQ, TF, SAQ, EMQ, MQ, CQ, EQ],
    DC_STAFF: 'staff',
    DC_STUDENT: 'student',

    // Question
    QUESTION: 'question',

    // allowed file types
    ITEM_IMAGE_FILE_TYPES: ['jpeg', 'jpg', 'png', 'gif'],
    ITEM_AUDIO_FILE_TYPES: ['mp3', 'mpeg', 'wav'],
    ITEM_VIDEO_FILE_TYPES: ['mp4'],
    ITEM_APPLICATION_FILE_TYPES: ['pdf'],

    // Taxonomy
    TAXONOMY: 'taxonomy',
    ONGOING: 'ongoing',
    PRESENT: 'present',
    ABSENT: 'absent',
    LEAVE: 'leave',
    SESSION_MODE: {
        START: 'start',
        STOP: 'stop',
        JOIN: 'join',
        CLOSE: 'close',
        MANUAL: 'manual',
        AUTO: 'auto',
    },
    IOS: 'ios',
    ANDROID: 'android',
    WEB: 'web',
    SCHEDULE: 'schedule',
    APP_NOTIFICATIONS: 'app_notifications',
    SUPPORT_EVENT: 'support_event',

    SUMMARY: 'summary',
    SLO: 'slo',
    CLO: 'clo',
    PLO: 'plo',
    DASHBOARD: {
        ACTIVITIES: 'activities',
        SESSIONS: 'sessions',
        COURSES: 'courses',
        DOCUMENTS: 'documents',
        RATINGS: 'ratings',
    },

    REPORT_ANALYTICS: {
        PROGRAM_LIST: 'program_list',
        PLO: 'plo',
        CREDIT_HOURS: 'credit_hours',
        NO_STUDENT: 'no_student',
        NO_STAFF: 'no_staff',
    },
    PROGRAM_REPORT_SETTINGS: 'program_report_setting',
    WARNING_MAIL: 'warning_mail',
    SETTINGS: 'setting',
    PREREQUISITE: 'pre-requisite',
    COREQUISITE: 'co-requisite',
    TOTAL: 'total',
    COMMON: 'common',
    INDEPENDENT: 'independent',
    SPLIT_UP: 'split_up',
    FIXED_VALUE: 'fixed_value',
    RANGE_VALUES: 'range_values',
    CREDIT_HOURS_MODE: {
        STANDARD: 'standard',
        DYNAMIC: 'dynamic',
    },
    INDEPENDENT_COURSE_CREDIT_HOURS_MODE: {
        STANDARD: 'standard',
        DYNAMIC: 'dynamic',
        CONTACT: 'contact',
    },
    COURSE_TYPE_GET: {
        COURSE: 'course',
        COURSE_CONFIG: 'course-config',
    },
    SELECTIVE_HOUR_TYPE: {
        TOTAL: 'total',
        SPLIT_UP: 'split-up',
    },
    DRAFTED: 'DRAFTED',
    ADDED: 'ADDED',
    ADD: 'add',
    REMOVE: 'remove',
    ALL: 'all',
    SELECTED: 'selected',
    CLIENT_URI: 'tenantURI',
    DS_ONSITE_KEY: 'Onsite',
    DS_REMOTE_KEY: 'Remote',
    APPROVE: 'approve',
    TOGGLE_CREDIT_HOURS: 'credit hours',
    TOGGLE_PERIOD_WEEK: 'period/week',
    TOGGLE_DELIVERY_DURATION: 'delivery duration',
    DEPARTMENT_HIERARCHY: 'department_hierarchy',
    ADMINISTRATIVE: 'administrative',
});

module.exports = constants;
