const express = require('express');
const router = express.Router();
const {
    getAccreditation,
    addAccreditation,
    editAccreditation,
    deleteAccreditation,
} = require('./accreditation.controller');
const catchAsync = require('../../utility/catch-async');

router.get('/', catchAsync(getAccreditation));
router.post('/add-accreditation-type', catchAsync(addAccreditation));
router.put('/edit-accreditation-type', catchAsync(editAccreditation));
router.delete('/delete-accreditation-type/:id', catchAsync(deleteAccreditation));

module.exports = router;
