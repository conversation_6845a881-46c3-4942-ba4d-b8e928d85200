const Joi = require('joi');

// update Program Report Setting schema
function updateProgramReportSettingSchema() {
    const schema = {
        body: Joi.object().keys({
            scalePoints: Joi.array().items(
                Joi.object().keys({
                    sNo: Joi.number(),
                    scalePointSurveyValue: Joi.string(),
                    description: Joi.string(),
                    colorCode: Joi.string(),
                }),
            ),
            questions: Joi.array().items(
                Joi.object().keys({
                    content: Joi.string(),
                }),
            ),
            selfEvaluationSurvey: Joi.object().keys({
                isChecked: Joi.boolean(),
                scalePoints: Joi.array().items(
                    Joi.object().keys({
                        sNo: Joi.number(),
                        scalePointSurveyValue: Joi.string(),
                        description: Joi.string(),
                        colorCode: Joi.string(),
                    }),
                ),
                limit: Joi.object().keys({
                    from: Joi.number(),
                    to: Joi.number(),
                }),
                benchMarks: Joi.array().items(
                    Joi.object().keys({
                        name: Joi.string(),
                        shortName: Joi.string(),
                        maxValue: Joi.number(),
                        colorCode: Joi.string(),
                        module: Joi.string(),
                    }),
                ),
                scalePointStatus: Joi.string().valid(...['draft', 'saved']),
                benchMarkStatus: Joi.string().valid(...['draft', 'saved']),
                advance: Joi.object().keys({
                    getWrittenFeedbackFromStudent: Joi.boolean(),
                }),
            }),
            sessionExperienceSurvey: Joi.object().keys({
                isChecked: Joi.boolean(),
                noOfQuestion: Joi.number(),
                scalePoints: Joi.array().items(
                    Joi.object().keys({
                        sNo: Joi.number(),
                        scalePointSurveyValue: Joi.string(),
                        description: Joi.string(),
                        colorCode: Joi.string(),
                    }),
                ),
                questions: Joi.array().items(
                    Joi.object().keys({
                        content: Joi.string(),
                        scalePoints: Joi.array().items(
                            Joi.object().keys({
                                sNo: Joi.number(),
                                scalePointSurveyValue: Joi.string(),
                                description: Joi.string(),
                                colorCode: Joi.string(),
                            }),
                        ),
                    }),
                ),
                limit: Joi.object().keys({
                    from: Joi.number(),
                    to: Joi.number(),
                }),
                separateRatingScaleValueForEachQuestion: Joi.boolean(),
                benchMarks: Joi.array().items(
                    Joi.object().keys({
                        name: Joi.string(),
                        shortName: Joi.string(),
                        maxValue: Joi.number(),
                        colorCode: Joi.string(),
                        module: Joi.string(),
                    }),
                ),
                scalePointStatus: Joi.string().valid(...['draft', 'saved']),
                benchMarkStatus: Joi.string().valid(...['draft', 'saved']),
                advance: Joi.object().keys({
                    getWrittenFeedbackFromStudent: Joi.boolean(),
                    allowStudentsToRateAnyTime: Joi.boolean(),
                }),
            }),
            courseAdminUserPermission: Joi.boolean(),
            activity: Joi.object().keys({
                isChecked: Joi.boolean(),
                benchMarks: Joi.array().items(
                    Joi.object().keys({
                        name: Joi.string(),
                        shortName: Joi.string(),
                        maxValue: Joi.number(),
                        colorCode: Joi.string(),
                        module: Joi.string(),
                    }),
                ),
                benchMarkStatus: Joi.string().valid(...['draft', 'saved']),
            }),
            status: Joi.string().valid(...['draft', 'saved']),
            step: Joi.number(),
            rattingScaleForSurvey: Joi.string().valid(...['common', 'independent']),
            benchMarkForSurveyAndActivities: Joi.string().valid(...['common', 'independent']),
            benchMarkValue: Joi.string().valid(...['points', 'percentage']),
            benchMarks: Joi.array().items(
                Joi.object().keys({
                    name: Joi.string(),
                    shortName: Joi.string(),
                    maxValue: Joi.number(),
                    colorCode: Joi.string(),
                    module: Joi.string(),
                }),
            ),
            scalePointStatus: Joi.string().valid(...['draft', 'saved']),
            benchMarkStatus: Joi.string().valid(...['draft', 'saved']),
        }),
        query: {
            _program_id: Joi.string().optional(),
            _institution_id: Joi.string().optional(),
        },
    };
    return schema;
}
// get Program Report Setting
function getProgramReportSettingSchema() {
    const schema = {
        query: Joi.object().keys({
            _program_id: Joi.string().required(),
            _institution_id: Joi.string().required(),
        }),
    };
    return schema;
}

// update Program Report Setting schema
function updateProgramReportSettingCourseSchema() {
    const schema = {
        body: Joi.object().keys({
            course: Joi.object().keys({
                courseId: Joi.string(),
                year: Joi.string(),
                level: Joi.string(),
                term: Joi.string(),
                rotation: Joi.string(),
                rotationCount: Joi.number(),
                selfEvaluationSurvey: Joi.boolean(),
                sessionExperienceSurvey: Joi.boolean(),
            }),
        }),
        query: Joi.object().keys({
            _program_id: Joi.string().required(),
            _institution_id: Joi.string().required(),
        }),
    };
    return schema;
}
// get Program Report Setting
function getProgramReportSettingBenchMarkSchema() {
    const schema = {
        query: Joi.object().keys({
            _program_id: Joi.string().required(),
            _institution_id: Joi.string().required(),
        }),
    };
    return schema;
}

module.exports = {
    getProgramReportSettingSchema: getProgramReportSettingSchema(),
    updateProgramReportSettingSchema: updateProgramReportSettingSchema(),
    getProgramReportSettingBenchMarkSchema: getProgramReportSettingBenchMarkSchema(),
    updateProgramReportSettingCourseSchema: updateProgramReportSettingCourseSchema(),
};
