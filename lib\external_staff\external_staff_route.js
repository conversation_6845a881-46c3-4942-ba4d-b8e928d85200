const route = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    addExternalStaff,
    editExternalStaff,
    getExternalStaff,
    deleteExternalStaff,
} = require('./external_staff_controller');
const {
    addExternalStaffValidation,
    editExternalStaffValidation,
    getExternalStaffValidation,
    deleteExternalStaffValidation,
} = require('./external_staff_validator');
route.post('/addExternalStaff', addExternalStaffValidation, catchAsync(addExternalStaff));
route.put('/editExternalStaff', editExternalStaffValidation, catchAsync(editExternalStaff));
route.get('/getExternalStaff', getExternalStaffValidation, catchAsync(getExternalStaff));
route.put('/deleteStaff/:_id', deleteExternalStaffValidation, catchAsync(deleteExternalStaff));
module.exports = route;
