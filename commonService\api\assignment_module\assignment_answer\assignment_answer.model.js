const mongoose = require('mongoose');
const {
    PENDING,
    S<PERSON><PERSON>ITTED,
    COMPLETED,
    TO_GRADE,
    IN_GRADING,
    IN_REVIEW,
    GRADED,
    RELEASED,
    ASSIGNMENT_ANSWER,
    EXCUSED,
    RESUBMITTED,
    USER,
    ASSIGNMENT_GROUP,
    INSTITUTION_CALENDAR,
    ASSIGNMENT_PROMPT_ANSWER,
} = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const dateField = {
    dateAndTime: { type: Date },
    previousDate: {
        date: {
            type: Date,
        },
        reason: {
            type: String,
        },
    },
};

const assignmentAnswerSchemas = new Schema(
    {
        _institution_id: {
            type: ObjectId,
        },
        _institution_calendar_id: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        assignmentId: {
            type: ObjectId,
        },
        studentId: {
            type: ObjectId,
            ref: USER,
        },
        programId: {
            type: ObjectId,
        },
        courseId: {
            type: ObjectId,
        },
        groupId: {
            type: ObjectId,
            ref: ASSIGNMENT_GROUP,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isReset: {
            type: Boolean,
            default: false,
        },
        isDuplicate: {
            type: Boolean,
            default: false,
        },
        promptsAnswer: { type: [String], ref: ASSIGNMENT_PROMPT_ANSWER },
        ratId: { type: ObjectId },
        ratType: { type: String },
        assignmentAnswer: [
            {
                value: String,
                attachments: [
                    {
                        url: String,
                        signedUrl: String,
                        sizeInKb: Number,
                        name: String,
                    },
                ],
            },
        ],
        isDraft: { type: Boolean },
        needToResubmit: { type: Boolean },
        releaseGrade: { type: Boolean },
        noOfAttempt: { type: Number, default: 0 },
        score: { type: Number },
        credits: { type: Number },
        status: {
            type: String,
            default: PENDING,
            enum: [SUBMITTED, PENDING, COMPLETED, RESUBMITTED],
        },
        submittedNote: { type: String },
        specialDue: {
            startDate: dateField,
            dueDate: dateField,
            endDate: dateField,
        },
        submittedAt: { type: Date },
        staffEvaluation: {
            allStaffData: [
                {
                    staffId: { type: ObjectId, ref: USER },
                    individualStaffStatus: {
                        type: String,
                        default: TO_GRADE,
                        enum: [TO_GRADE, IN_GRADING, IN_REVIEW, GRADED, RELEASED, EXCUSED],
                    },
                    totalMark: { type: Number },
                    learningOutcome: [
                        {
                            outcomeId: { type: ObjectId },
                            isAchieved: { type: Boolean },
                            overwritten: { type: Boolean, default: false },
                        },
                    ],
                    rubrics: [
                        {
                            criteriaId: { type: ObjectId },
                            selected: { type: Boolean },
                            dimensional: [
                                {
                                    dimensionalId: { type: ObjectId },
                                    points: { type: Number },
                                },
                            ],
                            subRubrics: [
                                {
                                    criteriaId: { type: ObjectId },
                                    selected: { type: Boolean },
                                    dimensional: [
                                        {
                                            dimensionalId: { type: ObjectId },
                                            points: { type: Number },
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                    excusedNote: { type: String },
                },
            ],
            releasedStaffId: { type: ObjectId, ref: USER },
            totalMark: { type: Number },
            staffStatus: { type: String },
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSIGNMENT_ANSWER, assignmentAnswerSchemas);
