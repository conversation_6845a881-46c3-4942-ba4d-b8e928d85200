const express = require('express');
const route = express.Router();
const department_subject = require('./department_subject_controller');
const validater = require('./department_subject_validator');
route.post('/list', department_subject.list_values);
route.get('/:id', validater.department_subject_id, department_subject.list_id);
route.get('/', department_subject.list);
route.post('/', validater.department_subject, department_subject.insert);
route.put('/:id', validater.department_subject_id, validater.department_subject_update, department_subject.update);
route.delete('/:id', validater.department_subject_id, department_subject.delete);

module.exports = route;