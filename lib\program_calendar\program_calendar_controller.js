const constant = require('../utility/constants');
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const program = require('mongoose').model(constant.PROGRAM);
const digi_program = require('mongoose').model(constant.DIGI_PROGRAM);
const digi_curriculum = require('mongoose').model(constant.DIGI_CURRICULUM);
const course = require('mongoose').model(constant.COURSE);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user_model = require('mongoose').model(constant.USER);
const role = require('mongoose').model(constant.ROLE);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const courseSchema = require('../models/digi_course');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const { updateProgramCalendarRedisData } = require('../utility/utility.service');
const program_calendar_formate = require('./program_calendar_formate');
// const institution_formate = require('../institution/institution_formate');
// const { USER } = require('../utility/constants');
const ObjectId = common_files.convertToMongoObjectId;
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
const { clearItem, allProgramCalendarDatas } = require('../../service/cache.service');

// Updating Program Calendar Flat Caching Data
const updateProgramCalendarFlatCacheData = async () => {
    clearItem('allProgramCalendar');
    await allProgramCalendarDatas();
};

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.COURSE,
                localField: '_course_id',
                foreignField: '_id',
                as: 'course',
            },
        },
        { $unwind: '$course' },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_institution_calendar_id',
                foreignField: '_id',
                as: 'institution',
            },
        },
        { $unwind: '$institution' },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            /* doc.data */ program_calendar_formate.program_calendar(doc.data),
        );
        // common_files.list_all_response(res, 200, true, "PROGRAM_CALENDAR_LIST", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* program_calendar_formate.program_calendar(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.COURSE,
                localField: '_course_id',
                foreignField: '_id',
                as: 'course',
            },
        },
        { $unwind: '$course' },
        {
            $lookup: {
                from: constant.INSTITUTION_CALENDAR,
                localField: '_institution_calendar_id',
                foreignField: '_id',
                as: 'institution',
            },
        },
        { $unwind: '$institution' },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'program_calendar details',
            /* doc.data */ program_calendar_formate.program_calendar_ID(doc.data[0]),
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.insert = async (req, res) => {
    const checks = await base_control.check_id(institution_calendar, {
        _id: { $in: req.body._institution_calendar_id },
        isDeleted: false,
    });
    const checks1 = await base_control.check_id(course, {
        _id: { $in: req.body._course_id },
        isDeleted: false,
    });
    if (checks.status && checks1.status) {
        const doc = await base_control.insert(program_calendar, req.body);
        updateProgramCalendarFlatCacheData();
        updateProgramCalendarRedisData({
            _institution_calendar_id: req.body._institution_calendar_id,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                201,
                true,
                req.t('PROGRAM_CALENDAR_ADDED_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.update = async (req, res) => {
    let checks = { status: true };
    const checks1 = { status: true };
    if (req.body._institution_id != undefined) {
        checks = await base_control.check_id(institution_calendar, {
            _id: { $in: req.body._institution_calendar_id },
            isDeleted: false,
        });
    }
    if (req.body._course_id != undefined) {
        checks = await base_control.check_id(course, {
            _id: { $in: req.body._course_id },
            isDeleted: false,
        });
    }
    if (checks.status && checks1.status) {
        const object_id = req.params.id;
        const doc = await base_control.update(program_calendar, object_id, req.body);
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _institution_calendar_id: req.body._institution_calendar_id,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                201,
                true,
                req.t('PROGRAM_CALENDAR_UPDATED_SUCCESSFULLY'),
                doc.data,
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.delete = async (req, res) => {
    // let object_id = req.params.id;
    const doc = await program_calendar.deleteOne({
        _program_id: { $in: req.params.program },
        _institution_calendar_id: ObjectId(req.params.calendar),
    });
    updateProgramCalendarFlatCacheData();
    await updateProgramCalendarRedisData({
        _institution_calendar_id: req.params.calendar,
    });
    common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_DELETED_SUCCESSFULLY'), doc);
    // if (doc.status) {
    //     common_files.com_response(res, 201, true, "PROGRAM_CALENDAR_DELETED_SUCCESSFULLY", doc.data);
    // } else {
    //     common_files.com_response(res, 500, false, "Error", doc.data);
    // }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }
        const doc = await base_control.get_list(program_calendar, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_LIST'),
                program_calendar_formate.program_calendar_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
        );
    }
};

exports.dashboard = async (req, res) => {
    try {
        // const id = ObjectId(req.params.id);
        // let program_data = await base_control.get_list(program, { 'isDeleted': false }, {});
        const program_data = await base_control.get(
            digi_program,
            { _id: ObjectId(req.params.id), isDeleted: false },
            {},
        );
        if (!program_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );

        // const program_cri = [];
        // const program_no = null;
        const interim = false;
        let curriculums = [];
        // program_no = program_data.data[program_data.data.findIndex(i => i._id == req.params.id)].no;
        // program_data.data.forEach(element => {
        //     if (program_no == element.no) {
        //         let ver = element.name.substring((element.name.length - 3), (element.name.length));
        //         program_cri.push({ _id: element._id, name: element.name, version: ver });
        //         curriculums.push(ver);
        //         interim = element.interim;
        //     }
        // });
        // if (curriculums.length != 0) {

        // let aggre = [
        //     { $match: { '_program_id': id } },
        //     { $match: { 'isDeleted': false } },
        //     { $addFields: { level_rotation: { level_no: '$level', rotation: '$rotation' } } },
        //     { $sort: { year: 1, 'level_rotation.level_no': 1 } },
        //     {
        //         $group: {
        //             _id: '$year',
        //             id: { $first: '$_id' },
        //             year: { $first: '$year' },
        //             level: { $push: '$level_rotation' }
        //         }
        //     },
        //     { $sort: { year: 1, 'level.level_no': 1 } },
        //     { $project: { year: 1, level: 1 } }
        // ];
        // let doc = await base_control.get_aggregate(credit_hours_individual, aggre);
        // let ins_cal = await base_control.get_list_sort(institution_calendar, { 'isDeleted': false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED }, { calendar_name: 1, start_date: 1, end_date: 1 }, { updatedAt: -1 });
        // curriculums = curriculums.sort();
        // if (doc.status) {
        //     let resp = {
        //         institution_calendar: ins_cal.data,
        //         interim: interim || false,
        //         year_level: doc.data,
        //         curriculum: curriculums
        //     }
        //     common_files.com_response(res, 200, true, "PROGRAM_CALENDAR_DASHBOARD", resp);
        // } else {
        //     common_files.com_response(res, 200, false, "Error No data found", doc.data);
        // }

        //Altering Structure for New Program Inputs Data
        let year_data = [];
        let level_data = [];
        let year_no = 0;
        // let level_no = 0;
        // interim = program_data.data.term.length != 1;
        const curriculum_data = await base_control.get_list(
            digi_curriculum,
            { _program_id: ObjectId(req.params.id), isDeleted: false, isActive: true },
            {},
        );
        if (!curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );
        for (element of curriculum_data.data) {
            year_data = [];
            year_no = 0;
            level_no = 0;
            curriculums.push(element.curriculum_name);
            for (year_element of element.year_level) {
                year_no++;
                level_data = [];
                for (level_element of year_element.levels) {
                    level_no++;
                    level_data.push({
                        _level_id: level_element._id,
                        level_no: level_element.level_name,
                        rotation: 'no',
                    });
                }
                if (!year_element._pre_requisite_id)
                    year_data.push({
                        _id: year_no,
                        _year_id: year_element._id,
                        year: year_element.y_type,
                        level: level_data,
                    });
            }
        }
        const ins_cal = await base_control.get_list_sort(
            institution_calendar,
            { isDeleted: false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED },
            { calendar_name: 1, start_date: 1, end_date: 1 },
            { updatedAt: -1 },
        );
        curriculums = curriculums.sort();
        // if (doc.status) {
        const resp = {
            institution_calendar: ins_cal.data,
            interim: interim || false,
            year_level: year_data,
            curriculum: curriculums,
            term: program_data.data.term,
        };
        common_files.comResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_DASHBOARD'),
            resp,
        );
        // } else {
        //     common_files.com_response(res, 200, false, "Error No data found", doc.data);
        // }
        // } else {
        //     common_files.com_response(res, 404, false, "Error PROGRAM_NOT_FOUND", 'Error PROGRAM_NOT_FOUND');
        // }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.calendar_setting = async (req, res) => {
    try {
        // const pro_ids = [];
        const arr_obj = [];
        // const program_cri = [];
        // const program_project = { year: 1, level: 1, rotation: 1, _program_id: 1 };
        // let program_data = await base_control.get_list(program, { 'isDeleted': false }, {});
        const program_data = await base_control.get_list(
            digi_program,
            { _id: ObjectId(req.body._program_id), isDeleted: false },
            {},
        );
        if (!program_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const institution_calender_checks = await base_control.check_id(institution_calendar, {
            _id: req.body._institution_calendar_id,
            isDeleted: false,
        });
        if (!institution_calender_checks.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                    ),
                );

        // Getting Dean based on role
        const roleData = await base_control.get(
            role,
            {
                name: { $nin: ['Super Admin', 'ADMIN'] },
                'modules.pages.name': 'Institution Calendar',
                $and: [
                    { 'modules.pages.actions.name': 'Publish' },
                    { 'modules.pages.actions.name': 'Create' },
                    { 'modules.pages.actions.name': 'Add Event' },
                ],
            },
            { _id: 1 },
        );
        const roleAssign = await base_control.get(
            role_assign,
            {
                'roles._role_id': ObjectId(roleData.data._id),
            },
            { _user_id: 1 },
        );
        const query = { user_type: 'staff', isDeleted: false };
        const project = { _id: 1, name: 1, email: 1, role: 1 };
        const staff_datas = await base_control.get_list(user_model, query, project);
        let user_data = {};
        let dean_data = {};
        staff_datas.data.forEach((element) => {
            if (element._id == req.body._creater_id) {
                user_data = {
                    _id: element._id,
                    name: element.name,
                    email: element.email,
                };
            }
            if (
                roleAssign.status &&
                element._id.toString() == roleAssign.data._user_id.toString()
            ) {
                dean_data = {
                    _id: element._id,
                    name: element.name,
                    email: element.email,
                };
            }
            // if (element.role === 'Dean') {
            //     dean_data = {
            //         _id: element._id,
            //         name: element.name,
            //         email: element.email,
            //     };
            // }
        });
        // let program_ids = [], program_no = null;
        // program_data.data.forEach(element => {
        //     if (element._id == req.body._program_id) {
        //         program_no = element.no;
        //     }
        // });
        // program_data.data.forEach(element => {
        //     if (program_no == element.no) {
        //         program_ids.push(element._id);
        //         let ver = element.name.substring((element.name.length - 3), (element.name.length));
        //         program_cri.push({ _id: element._id, version: ver });
        //         pro_ids.push(element._id);
        //     }
        // });
        // let program_year_level = await base_control.get_list_sort(credit_hours_individual, { _program_id: pro_ids, 'isDeleted': false }, program_project, { level: 1 });
        // req.body.data.forEach(async (data_element, index) => {
        //     if (data_element.type == constant.YEAR_LEVEL.YEAR) {
        //         data_element.curriculum.forEach(element => {
        //             program_year_level.data.forEach(sub_element => {
        //                 if (element.year_level == sub_element.year && (sub_element._program_id).toString() == (program_cri[program_cri.findIndex(i => i.version == element.version)]._id).toString()) {
        //                     arr_obj.push({
        //                         term: data_element.batch,
        //                         year: sub_element.year,
        //                         level_no: sub_element.level,
        //                         curriculum: element.version,
        //                         rotation: sub_element.rotation,
        //                         _program_id: program_cri[program_cri.findIndex(i => i.version == element.version)]._id
        //                     })
        //                 }
        //             });
        //         });
        //     } else if (data_element.type == constant.YEAR_LEVEL.LEVEL) {
        //         data_element.curriculum.forEach(element => {
        //             program_year_level.data.forEach(sub_element => {
        //                 if (element.year_level == sub_element.level && (sub_element._program_id).toString() == (program_cri[program_cri.findIndex(i => i.version == element.version)]._id).toString()) {
        //                     arr_obj.push({
        //                         term: data_element.batch,
        //                         year: sub_element.year,
        //                         level_no: element.year_level,
        //                         curriculum: element.version,
        //                         rotation: sub_element.rotation,
        //                         _program_id: program_cri[program_cri.findIndex(i => i.version == element.version)]._id
        //                     })
        //                 }
        //             });
        //         });
        //     }
        //     Object.assign(objs, { level: arr_obj });
        //     if (req.body.data.length == (index + 1)) {
        //         if (program_ids.length != 0 && institution_calender_checks.status) {
        //             let doc = await base_control.insert(program_calendar, objs);
        //             if (doc.status) {
        //                 common_files.com_response(res, 201, true, "Program Calendar Curriculum Setting assined successfully", { Program_Calendar_ID: doc.responses._id });
        //             } else {
        //                 common_files.com_response(res, 500, false, "Error", doc.data);
        //             }
        //         } else {
        //             common_files.com_response(res, 404, false, "ERROR_ID_NOT_MATCH", 'CHECK_PARSING_REFERENCE_ID');
        //         }
        //     }
        // });
        const curriculum_data = await base_control.get_list_sort(
            digi_curriculum,
            { _program_id: ObjectId(req.body._program_id), isDeleted: false },
            {},
            { curriculum_name: 1 },
        );
        const objs = {
            _institution_calendar_id: req.body._institution_calendar_id,
            _program_id: [req.body._program_id],
            dean: {
                _dean_id: dean_data._id,
                name: dean_data.name,
                status: 'waiting',
            },
            creater: {
                _creater_id: user_data._id,
                name: user_data.name,
            },
        };
        for (data_element of req.body.data) {
            if (data_element.type == constant.YEAR_LEVEL.YEAR) {
                // data_element.curriculum.forEach(element => {
                for (element of data_element.curriculum) {
                    const year_level_datas = curriculum_data.data.findIndex(
                        (i) => i.curriculum_name == element.version,
                    );
                    if (year_level_datas != -1) {
                        const loc = curriculum_data.data[year_level_datas].year_level.findIndex(
                            (i) => i.y_type == element.year_level,
                        );
                        if (loc != -1)
                            for (level_element of curriculum_data.data[year_level_datas].year_level[
                                loc
                            ].levels) {
                                arr_obj.push({
                                    term: data_element.batch,
                                    year: curriculum_data.data[year_level_datas].year_level[loc]
                                        .y_type,
                                    level_no: level_element.level_name,
                                    curriculum: element.version,
                                    rotation: 'no', // Need ot get Data from FE
                                    _program_id: ObjectId(req.body._program_id),
                                });
                            }
                    }
                }
            } else if (data_element.type == constant.YEAR_LEVEL.LEVEL) {
                for (element of data_element.curriculum) {
                    const year_level_datas = curriculum_data.data.findIndex(
                        (i) => i.curriculum_name == element.version,
                    );
                    if (year_level_datas != -1) {
                        for (year_element of curriculum_data.data[year_level_datas].year_level) {
                            for (level_element of year_element.levels) {
                                if (level_element.level_name == element.year_level)
                                    arr_obj.push({
                                        term: data_element.batch,
                                        year: year_element.y_type,
                                        level_no: level_element.level_name,
                                        curriculum: element.version,
                                        rotation: 'no', // Need ot get Data from FE
                                        _program_id: ObjectId(req.body._program_id),
                                    });
                            }
                        }
                    }
                }
            }
            Object.assign(objs, { level: arr_obj });
        }
        // return res.send(objs)
        const doc = await base_control.insert(program_calendar, objs);
        updateProgramCalendarFlatCacheData();
        updateProgramCalendarRedisData({
            _institution_calendar_id: req.body._institution_calendar_id,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_SET_PROGRAM_CALENDAR_SETTING'),
                        req.t('UNABLE_TO_SET_PROGRAM_CALENDAR_SETTING'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_SETTING_CREATED_SUCCESSFULLY'),
                    req.t('PROGRAM_CALENDAR_SETTING_CREATED_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        common_files.com_response(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.calendar_level_date = async (req, res) => {
    const program_calendar_check = await base_control.check_id(
        program_calendar,
        { _id: req.body._id, isDeleted: false },
        {},
    );
    if (program_calendar_check.status) {
        const objs = {
            'level.$[i].start_date': req.body.start_date,
            'level.$[i].end_date': req.body.end_date,
        };
        const cond = { _id: req.body._id, isDeleted: false };
        const filter = {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level_no': req.body.level_no }],
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            objs,
            filter,
        );
        updateProgramCalendarFlatCacheData();
        // let doc = await base_control.update_condition(program_calendar, cond, objs);
        await updateProgramCalendarRedisData({
            _id: req.body._id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('START_DATE_AND_END_DATE_ASSIGNED_SUCCESSFULLY_TO_PARTICULAR_LEVEL'),
                req.t('START_DATE_AND_END_DATE_ASSIGNED_SUCCESSFULLY_TO_PARTICULAR_LEVEL'),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.rotation_setting = async (req, res) => {
    try {
        const calendar_check = await base_control.get(
            program_calendar,
            { _id: ObjectId(req.body._calendar_id), isDeleted: false },
            { 'level.level_no': 1, 'level.rotation': 1 },
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        // if (calendar_check.data.level[calendar_check.data.level.findIndex(i => i.level_no == req.body.level_no)].rotation == 'yes') {
        const rot = [];
        for (let i = 1; i <= req.body.rotation_count; i++) {
            rot.push({ rotation_count: i });
        }
        const objs = {
            $set: {
                'level.$[i].rotation': 'yes',
                'level.$[i].rotation_count': req.body.rotation_count,
                'level.$[i].rotation_course': rot,
            },
        };
        const cond = { _id: ObjectId(req.body._calendar_id), isDeleted: false };
        const filter = {
            arrayFilters: [{ 'i.level_no': req.body.level_no, 'i.term': req.body.batch }],
        };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            objs,
            filter,
        );
        updateProgramCalendarFlatCacheData();
        await updateProgramCalendarRedisData({
            _id: req.body._calendar_id,
            term: req.body.batch,
            level_no: req.body.level_no,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_SET_ROTATION_COUNT'),
                        req.t('UNABLE_TO_SET_ROTATION_COUNT'),
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('ROTATION_COUNT_IS_ASSIGNED'),
                    req.t('ROTATION_COUNT_IS_ASSIGNED'),
                ),
            );
        // } else {
        //     return res.status(410).send(common_files.response_function(res, 410, false, "This level doesn't contain rotation please check level no", "This level doesn't contain rotation please check level no"));
        // }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

// Need to verify the Aggregation
exports.calendar_setting_get = async (req, res) => {
    const program_check = await base_control.check_id(
        program,
        { _id: req.params.program, isDeleted: false },
        {},
    );
    const calendar_check = await base_control.check_id(
        institution_calendar,
        { _id: req.params.calendar, isDeleted: false },
        {},
    );

    if (program_check.status && calendar_check.status) {
        const program_id = ObjectId(req.params.program);
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _program_id: ObjectId(program_id) } },
            { $match: { _institution_calendar_id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $sort: { updatedAt: -1 } },
            { $limit: 1 },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            // { $match: { 'level.term': constant.BATCH.REGULAR } },
            // { $sort: { 'level.year': 1, 'level.level_no': 1 } },
            // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
            {
                $group: {
                    _id: '$level.year',
                    id: {
                        $first: '$_id',
                    },
                    year: { $first: '$level.year' },
                    level: {
                        $push: '$level',
                    },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1, 'level._id': 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            let resp = {};
            if (doc.data.length > 0) {
                resp = { _id: doc.data[0]._id };
                const ars = [];
                doc.data.forEach((element) => {
                    ars.push({ year: element.year, level: element.level });
                });
                Object.assign(resp, { year_level: ars });
            }
            common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_SETTING'), resp);
        } else {
            common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.calendar_setting_year_get = async (req, res) => {
    const calendar_check = await base_control.check_id(
        program_calendar,
        { _id: req.params.calendar, isDeleted: false },
        {},
    );

    if (calendar_check.status) {
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            // { $match: { 'level.term': constant.BATCH.REGULAR } },
            { $match: { 'level.year': req.params.year } },
            // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
            { $sort: { 'level.level_no': 1 } },
            {
                $group: {
                    _id: '$level.year',
                    id: {
                        $first: '$_id',
                    },
                    year: { $first: '$level.year' },
                    level: {
                        $push: '$level',
                    },
                },
            },
            { $addFields: { _id: '$id' } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_SETTING_YEAR_DATA'),
                doc.data,
            );
        } else {
            common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.calendar_get_setting = async (req, res) => {
    const calendar_check = await base_control.check_id(
        program_calendar,
        { _id: req.params.calendar, isDeleted: false },
        {},
    );
    if (calendar_check.status) {
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            // { $match: { 'level.term': constant.BATCH.REGULAR } },
            // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
            {
                $group: {
                    _id: '$level.year',
                    id: {
                        $first: '$_id',
                    },
                    year: { $first: '$level.year' },
                    level: {
                        $push: '$level',
                    },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_SETTING_YEAR_DATA'),
                doc.data,
            );
        } else {
            common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.landing = async (req, res) => {
    try {
        const program_check = await base_control.check_id(
            digi_program,
            { _id: req.params.program, isDeleted: false },
            {},
        );
        // let program_check = await base_control.check_id(program, { _id: req.params.program, 'isDeleted': false }, {});
        if (!program_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const calendar_check = await base_control.check_id(
            institution_calendar,
            { _id: req.params.calendar, isDeleted: false },
            {},
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                    ),
                );

        const program_id = ObjectId(req.params.program);
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _program_id: ObjectId(program_id) } },
            { $match: { _institution_calendar_id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $sort: { updatedAt: -1 } },
            { $limit: 1 },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            {
                $addFields: {
                    'level.srt_level': {
                        $toInt: {
                            $trim: {
                                input: {
                                    $substr: [
                                        '$level.level_no',
                                        { $subtract: [{ $strLenCP: '$level.level_no' }, 2] },
                                        2,
                                    ],
                                },
                                chars: ' ',
                            },
                        },
                    },
                },
            },
            // { $sort: { 'level.year': 1, 'level._id': 1 } },
            {
                $group: {
                    _id: '$level.year',
                    id: { $first: '$_id' },
                    year: { $first: '$level.year' },
                    level: { $push: '$level' },
                    status: { $first: '$status' },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1, 'level._id': 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (!doc.status) {
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NOT_FOUND'),
                        [],
                    ),
                );
        }
        const uniqueCourseIds = new Set();
        for (const { level } of doc.data) {
            for (const { rotation, rotation_course, course } of level) {
                if (rotation === 'yes') {
                    for (const { course } of rotation_course) {
                        for (const { _course_id } of course) {
                            uniqueCourseIds.add(_course_id);
                        }
                    }
                } else {
                    for (const { _course_id } of course) {
                        uniqueCourseIds.add(_course_id);
                    }
                }
            }
        }
        const courseDetails = await courseSchema
            .find(
                {
                    _id: { $in: Array.from(uniqueCourseIds) },
                },
                {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            )
            .lean();
        const constructedCourseDetails = new Map(
            courseDetails.map(
                ({
                    versionName = '',
                    versionNo = 1,
                    versioned = false,
                    versionedFrom = null,
                    versionedCourseIds = [],
                    _id,
                }) => [
                    _id.toString(),
                    {
                        versionName,
                        versionNo,
                        versioned,
                        versionedFrom,
                        versionedCourseIds,
                    },
                ],
            ),
        );
        const programLevelsWithCourseDetails = doc.data.map((programElement) => ({
            ...programElement,
            level: programElement.level.map((levelElement) => {
                if (levelElement.rotation === 'yes') {
                    return {
                        ...levelElement,
                        rotation_course: levelElement.rotation_course.map((rotationElement) => ({
                            ...rotationElement,
                            course: rotationElement.course.map((rotationCourseElement) => {
                                const courseVersionedDetails = constructedCourseDetails.get(
                                    rotationCourseElement._course_id.toString(),
                                );
                                return {
                                    ...rotationCourseElement,
                                    versionName: courseVersionedDetails?.versionName || '',
                                    versionNo: courseVersionedDetails?.versionNo || 1,
                                    versioned: courseVersionedDetails?.versioned || false,
                                    versionedFrom: courseVersionedDetails?.versionedFrom || null,
                                    versionedCourseIds:
                                        courseVersionedDetails?.versionedCourseIds || [],
                                };
                            }),
                        })),
                    };
                }
                return {
                    ...levelElement,
                    course: levelElement.course.map((courseElement) => {
                        const courseVersionedDetails = constructedCourseDetails.get(
                            courseElement._course_id.toString(),
                        );
                        return {
                            ...courseElement,
                            versionName: courseVersionedDetails?.versionName || '',
                            versionNo: courseVersionedDetails?.versionNo || 1,
                            versioned: courseVersionedDetails?.versioned || false,
                            versionedFrom: courseVersionedDetails?.versionedFrom || null,
                            versionedCourseIds: courseVersionedDetails?.versionedCourseIds || [],
                        };
                    }),
                };
            }),
        }));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_SETTING'),
                    programLevelsWithCourseDetails,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.calendar_get = async (req, res) => {
    const program_id = ObjectId(req.params.program);
    const aggre = [
        { $match: { _id: ObjectId(program_id) } },
        { $match: { isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.year': req.params.year } },
        // { $match: { 'level.term': constant.BATCH.REGULAR } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
        // { $unwind: { path: '$level.course', preserveNullAndEmptyArrays: true } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level.course._event_id', foreignField: '_id', as: 'level.course.courses_events' } },
        {
            $group: {
                _id: '$level.year',
                id: { $first: '$_id' },
                year: { $first: '$level.year' },
                level: { $push: '$level' },
            },
        },
        { $addFields: { _id: '$id' } },
        { $sort: { year: 1, 'level.level_no': 1 } },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_SETTING'), doc.data);
    } else {
        common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
    }
};

exports.calendar_interim_setting_get = async (req, res) => {
    const program_check = await base_control.check_id(
        program,
        { _id: req.params.program, isDeleted: false },
        {},
    );
    const calendar_check = await base_control.check_id(
        institution_calendar,
        { _id: req.params.calendar, isDeleted: false },
        {},
    );

    if (program_check.status && calendar_check.status) {
        const program_id = ObjectId(req.params.program);
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _program_id: ObjectId(program_id) } },
            { $match: { _institution_calendar_id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $sort: { updatedAt: -1 } },
            { $limit: 1 },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $addFields: { 'level.year_level_no': { $toInt: '$level.level_no' } } },
            {
                $sort: {
                    'level.year': 1,
                    'level.term': -1,
                    'level.year_level_no': 1,
                    'level.level_no': 1,
                },
            },
            // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
            {
                $group: {
                    _id: '$level.year',
                    id: { $first: '$_id' },
                    year: { $first: '$level.year' },
                    level: { $push: '$level' },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1, 'level.term': -1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            let r_level = null;
            let i_level = null;
            let sem1 = [];
            let sem2 = [];
            const year = [];
            let level_filter;
            doc.data.forEach((element) => {
                sem1 = [];
                sem2 = [];
                r_level = null;
                i_level = null;
                if (element.level.length == 4) {
                    element.level.forEach((level_element) => {
                        if (level_element.term == constant.BATCH.REGULAR) {
                            if (r_level == null) {
                                r_level = level_element.level_no;
                            }
                            if (r_level == level_element.level_no) {
                                sem1.push(level_element);
                            } else {
                                sem2.push(level_element);
                            }
                        } else {
                            if (i_level == null) {
                                i_level = level_element.level_no;
                            }
                            if (i_level == level_element.level_no) {
                                sem2.push(level_element);
                            } else {
                                sem1.push(level_element);
                            }
                        }
                    });
                    level_filter = {
                        year: element.year,
                        level: {
                            semester1: sem1,
                            semester2: sem2,
                        },
                    };
                } else {
                    level_filter = {
                        year: element.year,
                        level: {
                            semester1: element.level,
                        },
                    };
                }
                year.push(level_filter);
            });
            const responce = {
                _id: doc.data[0]._id,
                id: doc.data[0].id,
                level: year,
            };
            common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_SETTING'), responce);
        } else {
            common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.calendar_interim_setting_year_get = async (req, res) => {
    const calendar_check = await base_control.check_id(
        program_calendar,
        { _id: req.params.calendar, isDeleted: false },
        {},
    );
    if (calendar_check.status) {
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.year': req.params.year } },
            // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
            { $sort: { 'level.term': -1 } },
            {
                $group: {
                    _id: '$level.term',
                    id: {
                        $first: '$_id',
                    },
                    year: { $first: '$level.year' },
                    level: {
                        $push: '$level',
                    },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1, 'level.term': -1, 'level.level_no': 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (doc.status) {
            let r_level = null;
            let i_level = null;
            const sem1 = [];
            const sem2 = [];
            doc.data.forEach((element) => {
                element.level.forEach((level_element) => {
                    if (level_element.term == constant.BATCH.REGULAR) {
                        if (r_level == null) {
                            r_level = level_element.level_no;
                        }
                        if (r_level == level_element.level_no) {
                            sem1.push(level_element);
                        } else {
                            sem2.push(level_element);
                        }
                    } else {
                        if (i_level == null) {
                            i_level = level_element.level_no;
                        }
                        if (i_level == level_element.level_no) {
                            sem2.push(level_element);
                        } else {
                            sem1.push(level_element);
                        }
                    }
                });
            });
            const responce = {
                _id: doc.data[0]._id,
                id: doc.data[0].id,
                year: doc.data[0].year,
                level: [
                    {
                        semester1: sem1,
                        semester2: sem2,
                    },
                ],
            };
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_SETTING_YEAR_DATA'),
                responce,
            );
        } else {
            common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.interim_landing = async (req, res) => {
    try {
        // let program_check = await base_control.check_id(program, { _id: req.params.program, 'isDeleted': false }, {});
        const program_check = await base_control.check_id(
            digi_program,
            { _id: req.params.program, isDeleted: false },
            {},
        );
        if (!program_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const calendar_check = await base_control.check_id(
            institution_calendar,
            { _id: req.params.calendar, isDeleted: false },
            {},
        );
        if (!calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                        req.t('INSTITUTION_CALENDAR_NOT_FOUND'),
                    ),
                );

        const program_id = ObjectId(req.params.program);
        const calendar_id = ObjectId(req.params.calendar);
        const aggre = [
            { $match: { _program_id: ObjectId(program_id) } },
            { $match: { _institution_calendar_id: ObjectId(calendar_id) } },
            { $match: { isDeleted: false } },
            { $sort: { updatedAt: -1 } },
            { $limit: 1 },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            {
                $sort: {
                    'level.year': 1,
                    'level.term': -1 /* , 'level.year_level_no': 1, "level.level_no": 1 */,
                },
            },
            {
                $group: {
                    _id: '$level.year',
                    id: { $first: '$_id' },
                    year: { $first: '$level.year' },
                    level: { $push: '$level' },
                    status: { $first: '$status' },
                },
            },
            { $addFields: { _id: '$id' } },
            { $sort: { year: 1, 'level.term': -1, 'level.level_no': 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        [],
                    ),
                );
        let r_level = null;
        let i_level = null;
        let sem1 = [];
        let sem2 = [];
        const year = [];
        let level_filter;
        for (element of doc.data) {
            sem1 = [];
            sem2 = [];
            r_level = null;
            i_level = null;
            if (element.level.length == 4) {
                for (level_element of element.level) {
                    if (level_element.term == constant.BATCH.REGULAR) {
                        if (r_level == null) {
                            r_level = level_element.level_no;
                        }
                        if (r_level == level_element.level_no) {
                            sem1.push(level_element);
                        } else {
                            sem2.push(level_element);
                        }
                    } else {
                        if (i_level == null) {
                            i_level = level_element.level_no;
                        }
                        if (i_level == level_element.level_no) {
                            sem2.push(level_element);
                        } else {
                            sem1.push(level_element);
                        }
                    }
                }
                level_filter = {
                    year: element.year,
                    level: {
                        semester1: sem1,
                        semester2: sem2,
                    },
                };
            } else {
                level_filter = {
                    year: element.year,
                    level: {
                        semester1: element.level,
                    },
                };
            }
            year.push(level_filter);
        }
        const responce = {
            _id: doc.data[0]._id,
            id: doc.data[0].id,
            level: year,
            status: doc.data[0].status,
        };
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_LANDING'),
                    responce,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.interim_calendar_get = async (req, res) => {
    const program_id = ObjectId(req.params.program);
    const aggre = [
        { $match: { _id: ObjectId(program_id) } },
        { $match: { isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.year': req.params.year } },
        { $sort: { 'level.term': -1 } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'level.events' } },
        // { $unwind: { path: '$level.course', preserveNullAndEmptyArrays: true } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level.course._event_id', foreignField: '_id', as: 'level.course.courses_events' } },
        {
            $group: {
                _id: '$level.term',
                id: { $first: '$_id' },
                year: { $first: '$level.year' },
                level: { $push: '$level' },
            },
        },
        { $addFields: { _id: '$id' } },
        { $sort: { year: 1, 'level.term': -1, 'level.level_no': 1 } },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        let r_level = null;
        let i_level = null;
        const sem1 = [];
        const sem2 = [];
        doc.data.forEach((element) => {
            element.level.forEach((level_element) => {
                /* let date_responce = [];
                if (level_element.start_date != undefined && level_element.end_date != undefined) {
                    let d1 = moment(level_element.start_date), d2 = moment(level_element.end_date);
                    let count = 1, start, end, objs = {};
                    start = d1.format('DD MMM');
                    end = d2.format('DD MMM');
                    while (true) {
                        if (d1.format('dddd') == 'Thursday') {
                            end = d1.format('DD MMM');
                            objs = {
                                rang: start + ' - ' + end,
                                cal_week: 23,
                                acad_week: count
                            }
                            date_responce.push(objs);
                        }
                        d1 = d1.add(1, 'days');
                        if (d1.format('dddd') == 'Sunday') {
                            start = d1.format('DD MMM');
                            count++;
                        }
                        if (count > 40 || d1.format() === d2.format()) {
                            break;
                        }
                    }
                    // console.log(date_responce);
                }
                let ele = level_element;
                Object.assign(ele, { 'header': date_responce }); */
                if (level_element.term == constant.BATCH.REGULAR) {
                    if (r_level == null) {
                        r_level = level_element.level_no;
                    }
                    if (r_level == level_element.level_no) {
                        sem1.push(level_element);
                    } else {
                        sem2.push(level_element);
                    }
                } else {
                    if (i_level == null) {
                        i_level = level_element.level_no;
                    }
                    if (i_level == level_element.level_no) {
                        sem2.push(level_element);
                    } else {
                        sem1.push(level_element);
                    }
                }
            });
        });
        const responce = {
            _id: doc.data[0]._id,
            id: doc.data[0].id,
            year: doc.data[0].year,
            level: [
                {
                    semester1: sem1,
                    semester2: sem2,
                },
            ],
        };
        common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_LANDING'), responce);
    } else {
        common_files.com_response(res, 200, false, req.t('NOT_FOUND'), req.t('NOT_FOUND'));
    }
};

const moment = require('moment');
exports.date_check = async (req, res) => {
    let d1 = moment(req.params.start);
    const d2 = moment(req.params.end);
    let count = 1;
    const date_responce = [];
    let start;
    let end;
    let objs = {};
    start = d1.format('DD MMM');
    end = d2.format('DD MMM');
    console.log('Rang : ', start, ' - ', end);
    // eslint-disable-next-line no-constant-condition
    while (true) {
        if (d1.format('dddd') == 'Thursday') {
            end = d1.format('DD MMM');
            objs = {
                rang: start + ' - ' + end,
                cal_week: 23,
                acad_week: count,
            };
            date_responce.push(objs);
        }
        d1 = d1.add(1, 'days');
        if (d1.format('dddd') == 'Sunday') {
            start = d1.format('DD MMM');
            count++;
        }
        if (count > 40 || d1.format() === d2.format()) {
            break;
        }
    }
    common_files.com_response(res, 200, true, req.t('DATA_CHECKS'), date_responce);
};
