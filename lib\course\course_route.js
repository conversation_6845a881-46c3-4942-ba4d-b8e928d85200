const express = require('express');
const route = express.Router();
const course = require('./course_controller');
const validater = require('./course_validator');
route.get('/complete/:id', validater.course_id, course.course_complete);
route.post('/list', course.list_values);
route.get('/:id', validater.course_id, course.list_id);
route.get('/elective/:id', validater.course_id, course.list_elective_id);
route.get('/', course.list);
route.post('/', validater.course, course.insert);
route.put('/:id', validater.course_id, validater.course, course.update);
route.delete('/:id', validater.course_id, course.delete);
route.get('/program/:id', validater.course_id, course.list_program);
route.get('/subject/:id', validater.course_id, course.list_subjects);
route.get('/program/:id/:level', validater.course_id_level, course.list_course_program_level);
route.get('/:id/:level/:type', validater.course_id_level_type, course.list_course_program_level_type);
route.delete('/delete_department/:id/:sub_id', validater.course_id_sub_id, course.delete_department);
route.delete('/delete_division/:id/:sub_id', validater.course_id_sub_id, course.delete_division);
module.exports = route;