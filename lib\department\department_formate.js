const program_formate = require('../program/program_formate');
const department_division_formate = require('../department_division/department_division_formate');
const department_subject = require('../department_subject/department_subject_formate');

exports.department_ID_Only = (doc) => {
    //console.log(doc);
    let obj = {
        _id: doc._id,
        department_title: doc.department_title,
        division: doc._division_id,
        subject: doc._subject_id,
        program: doc._program_id,
        isActive: doc.isActive,
        isDeleted: doc.isDeleted
    }
    return obj;
}

module.exports = {
    department: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                department_title: element.department_title,
                division: department_division_formate.department_division_ID_Subject(element.division),
                subject: department_subject.department_subject_ID_Array_Only(element.subject),
                /* speciality: element.speciality,
                topic: element.topic, */
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    department_ID: (doc) => {
        let obj = {
            _id: doc._id,
            department_title: doc.department_title,
            division: department_division_formate.department_division_ID_Subject(doc.division),
            subject: department_subject.department_subject_ID_Array_Only(doc.subject),
            /* speciality: doc.speciality,
            topic: doc.topic, */
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    department_ID_Onlys: function (doc) {
        let obj = {
            _id: doc._id,
            department_title: doc.department_title,
            division: doc._division_id,
            subject: doc._subject_id,
            /* speciality: doc.speciality,
            topic: doc.topic, */
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    department_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                department_title: element.department_title,
                division: element._division_id,
                subject: element._subject_id,
                /* speciality: element._speciality_id,
                topic: element._topic_id, */
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    department_division_subject_list: (doc, doc1) => {
        let division_datas = [], subject_datas = [];
        doc.forEach(department_data => {
            department_data.department_division.forEach(division_data => {
                division_datas.push(division_data);
            });
            department_data.direct_subject.forEach(subject_data => {
                subject_datas.push(subject_data);
            });
        });
        doc1.forEach(divisions => {
            divisions.subject.forEach(divisions_subject => {
                subject_datas.push(divisions_subject);
            });
        });
        let responces = {
            division: division_datas,
            subject: subject_datas
        };

        return responces;
    },
}