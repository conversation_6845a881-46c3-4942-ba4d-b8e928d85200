const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const program = new Schema(
    {
        program_type: {
            type: String,
            trim: true,
        },
        name: {
            type: String,
            trim: true,
            required: true,
        },
        code: {
            type: String,
            trim: true,
            required: true,
        },
        level: {
            type: String,
            trim: true,
        },
        type: {
            type: String,
            trim: true,
        },
        degree: {
            type: String,
            trim: true,
        },
        no_term: {
            type: Number,
        },
        term: [
            {
                term_no: Number,
                term_name: String,
            },
        ],
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        college_program_type: {
            type: String,
            trim: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_PROGRAM, program);
