const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    groupCourses,
    listGroups,
    unGroupOrEditCourses,
    renameGroupName,
} = require('./course-group.controller');
const {
    listCourseGroupsValidator,
    courseGroupValidator,
    courseUnGroupValidator,
} = require('./course-group.validator');
const { validate } = require('../../utility/input-validation');

router.get('/groups/:id', validate(listCourseGroupsValidator), catchAsync(listGroups));
router.post('/group-courses', validate(courseGroupValidator), catchAsync(groupCourses));
router.post('/ungroup-courses', validate(courseUnGroupValidator), catchAsync(unGroupOrEditCourses));
router.post(
    '/edit-course-group',
    validate(courseUnGroupValidator),
    catchAsync(unGroupOrEditCourses),
);
router.patch('/rename-group-name', validate(courseUnGroupValidator), catchAsync(renameGroupName));

module.exports = router;
