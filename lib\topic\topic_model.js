let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../../utility/constants');

let topic = new Schema({
    label: {
        type: String,
        required: true
    },
    content: {
        type: String,
        required: true
    },
    contact_hours: {
        type: Number,
        required: true
    },
    _course_id: {
        type: Schema.Types.ObjectId,
        ref: constant.COURSE,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.TOPIC, topic);