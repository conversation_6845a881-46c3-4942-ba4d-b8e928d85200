const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    getAssessmentTypes,
    addAssessmentType,
    updateAssessmentType,
    removeAssessmentType,
    toggleTypes,
    toggleAssessmentTypes,
    programList,
} = require('./assessment-management.controller');
const {
    getAssessmentValidator,
    addAssessmentValidator,
    removeAssessmentValidator,
    toggleAssessmentTypeValidator,
    // toggleAssessmentValidator,
} = require('./assessment-management.validator');
const { validate } = require('../../../../middleware/validation');
const {
    getAssessmentPlanning,
    toggleAssessmentPlanningTypes,
    addAssessment,
    updateAssessment,
    removeAssessment,
    courseList,
    getAssessmentPlanningDashboard,
    getAssessmentPlanningProgramView,
    getAssessmentPlanningLevelView,
} = require('../assessment-planning/assessment-planning.controller');
const {
    getAssessmentCourseProgramPlanning,
    toggleAssessmentCourseProgramPlanning,
    addAssessmentCourseProgram,
    updateAssessmentCourseProgram,
    removeAssessmentCourseProgram,
} = require('../assessment-course-program/assessment-course-program.controller');
const {
    getCourseCLOSLO_ProgramPLO,
    getAssessment,
    assessmentFeeding,
    getCourseStudentList,
    getProgramLevelsStudentList,
    publishedAssignment,
} = require('../assessment-library/assessment-library.controller');

// Assessment Setting Flow
router.get('/assessment-types', validate(getAssessmentValidator), catchAsync(getAssessmentTypes));
router.post(
    '/add-assessment-type',
    validate(addAssessmentValidator),
    catchAsync(addAssessmentType),
);
router.put(
    '/update-assessment-type',
    // validate(removeAssessmentValidator),
    catchAsync(updateAssessmentType),
);
router.delete(
    '/remove-assessment-type',
    validate(removeAssessmentValidator),
    catchAsync(removeAssessmentType),
);
router.post('/toggle-types', /*  validate(toggleAssessmentValidator), */ catchAsync(toggleTypes));
router.post(
    '/toggle-assessment-types',
    validate(toggleAssessmentTypeValidator),
    catchAsync(toggleAssessmentTypes),
);

// Assessment Library
router.get('/program-list', /* validate(getAssessmentValidator), */ catchAsync(programList));
router.get('/course-list', /* validate(getAssessmentValidator), */ catchAsync(courseList));

// Assessment Planning
router.get('/assessment-planning-dashboard', catchAsync(getAssessmentPlanningDashboard));
router.get('/assessment-planning', catchAsync(getAssessmentPlanning));
router.get('/assessment-planning-programView', catchAsync(getAssessmentPlanningProgramView));
router.get('/assessment-planning-levelView', catchAsync(getAssessmentPlanningLevelView));
router.post('/toggle-assessment-planning', catchAsync(toggleAssessmentPlanningTypes));
router.post('/add-assessment', catchAsync(addAssessment));
router.put('/update-assessment', catchAsync(updateAssessment));
router.delete('/remove-assessment', catchAsync(removeAssessment));

// Assessment Course Program
router.get('/assessment-course-program', catchAsync(getAssessmentCourseProgramPlanning));
router.post('/toggle-assessment-course-program', catchAsync(toggleAssessmentCourseProgramPlanning));
router.post('/add-assessment-course-program', catchAsync(addAssessmentCourseProgram));
router.put('/update-assessment-course-program', catchAsync(updateAssessmentCourseProgram));
router.delete('/remove-assessment-course-program', catchAsync(removeAssessmentCourseProgram));

// Assessment Library
router.get('/plo-clo-slo', catchAsync(getCourseCLOSLO_ProgramPLO));
router.get('/getAssessment', catchAsync(getAssessment));
router.get('/courseStudentList', catchAsync(getCourseStudentList));
router.get('/programStudentList', catchAsync(getProgramLevelsStudentList));
router.put('/assessmentFeeding', catchAsync(assessmentFeeding));

//parent App Assessment
router.put('/parentApp/publishedAssignment', catchAsync(publishedAssignment));
module.exports = router;
