let mongoose = require("mongoose");
let Schema = mongoose.Schema;
let constant = require("../utility/constants");

let infrastructure_delivery_type = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    primary_delivery_type: {
      type: Number,
      required: true,
    },
    delivery_symbol: {
      type: String,
      required: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);
module.exports = mongoose.model(  constant.INFRASTRUCTURE_DELIVERY_TYPE,  infrastructure_delivery_type
);
