// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');

exports.hospital = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            name: Joi.string().min(3).max(50).error(error => {
                return error;
            }),
            location: Joi.string().min(2).max(100).error(error => {
                return error;
            }),
             
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.hospital_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}