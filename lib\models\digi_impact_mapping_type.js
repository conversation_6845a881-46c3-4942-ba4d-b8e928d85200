let mongoose = require("mongoose");
let Schema = mongoose.Schema;
let constant = require("../utility/constants");

let impactMappingTypeSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    code: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    _institution_id: {
      type: Schema.Types.ObjectId,
      ref: constant.INSTITUTION,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model(constant.MAPPING_TYPES.IMPACT, impactMappingTypeSchema);
