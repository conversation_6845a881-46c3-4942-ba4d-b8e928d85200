const { convertToMongoObjectId } = require('../utility/common');
const { GROUPS, FAMILIES } = require('../utility/constants');
const tagMasterSchema = require('./tagMaster.model');

exports.getTagMasterSettings = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const findQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        const project = {
            tags: 1,
            groups: 1,
            families: 1,
        };
        let tagMasterSettings;
        tagMasterSettings = await tagMasterSchema.findOne(findQuery, project).lean();
        if (!tagMasterSettings) {
            tagMasterSettings = await tagMasterSchema.create({ ...findQuery });
        }
        return {
            statusCode: 200,
            message: 'Tag Master Setting',
            data: tagMasterSettings,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.manageTagMasterGroupFamilySettings = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, tags = [], groups = [], families = [] } = body;
        const findQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        let updateQuery = {};
        switch (type) {
            case GROUPS:
                updateQuery = { $push: { groups: { $each: groups } } };
                break;
            case FAMILIES:
                updateQuery = { $push: { families: { $each: families } } };
                break;
            default:
                updateQuery = { $push: { tags: { $each: tags } } };
        }
        const newTagMasterSettings = await tagMasterSchema.updateOne(findQuery, updateQuery);
        let response = { statusCode: 200, message: `${type} added successfully` };
        if (!newTagMasterSettings.modifiedCount) {
            response = { statusCode: 400, message: `unable to added ${type} ` };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.updatedTagMasterSettings = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, updateId, code = '', name = '', description = '' } = body;
        let findQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        let updateQuery = {};
        switch (type) {
            case GROUPS:
                findQuery = { ...findQuery, 'groups._id': convertToMongoObjectId(updateId) };
                updateQuery = {
                    $set: {
                        'groups.$.code': code,
                        'groups.$.name': name,
                        'groups.$.description': description,
                    },
                };
                break;
            case FAMILIES:
                findQuery = { ...findQuery, 'families._id': convertToMongoObjectId(updateId) };
                updateQuery = {
                    $set: {
                        'families.$.code': code,
                        'families.$.name': name,
                        'families.$.description': description,
                    },
                };
                break;
            default:
                findQuery = { ...findQuery, 'tags._id': convertToMongoObjectId(updateId) };
                updateQuery = {
                    $set: {
                        'tags.$.code': code,
                        'tags.$.name': name,
                        'tags.$.description': description,
                    },
                };
        }
        const updatedTagMasterSettings = await tagMasterSchema.updateOne(findQuery, updateQuery);
        let response = { statusCode: 200, message: `${type} updated successfully` };
        if (!updatedTagMasterSettings.modifiedCount) {
            response = { statusCode: 400, message: `unable to update ${type} ` };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteTagMasterSettings = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { type, tagGroupId } = query;
        const convertedDeleteId = convertToMongoObjectId(tagGroupId);
        const tagMasterSettingFindQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
        };
        let tagMasterSettingDeleteQuery = {};
        let arrayFilter = {};
        switch (type) {
            case GROUPS:
                tagMasterSettingDeleteQuery = {
                    $pull: {
                        groups: { _id: convertedDeleteId },
                        'families.$[familyGroupIndex].groups': convertedDeleteId,
                    },
                };
                arrayFilter = {
                    arrayFilters: [{ 'familyGroupIndex.groups': convertedDeleteId }],
                };
                break;
            case FAMILIES:
                tagMasterSettingDeleteQuery = {
                    $pull: {
                        families: { _id: convertedDeleteId },
                    },
                };
                break;
            default:
                tagMasterSettingDeleteQuery = {
                    $pull: {
                        tags: { _id: convertedDeleteId },
                        'groups.$[groupTagIndex].tags': convertedDeleteId,
                    },
                };
                arrayFilter = {
                    arrayFilters: [{ 'groupTagIndex.tags': convertedDeleteId }],
                };
        }
        const updatedTagMasterSettings = await tagMasterSchema.updateOne(
            tagMasterSettingFindQuery,
            tagMasterSettingDeleteQuery,
            arrayFilter,
        );
        let response = { statusCode: 200, message: `${type} deleted successfully` };
        if (!updatedTagMasterSettings.modifiedCount) {
            response = { statusCode: 400, message: `unable to delete ${type} ` };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.updatedOverAllTagMasterSettings = async ({ body = {} }) => {
    try {
        const { groups = [], families = [], _id } = body;
        const updateSettings = await tagMasterSchema.updateOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            { $set: { groups, families } },
        );
        let response = { statusCode: 200, message: `Tag Master Settings Updated Successfully` };
        if (!updateSettings.matchedCount && !updateSettings.modifiedCount) {
            response = { statusCode: 400, message: `Unable To Update Tag Master Settings ` };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
