const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { FORM } = require('../../common/utils/constants');
const { rubric } = require('../rubric/rubric.schema');

const schema = new Schema(
    {
        title: { type: String, trim: true },
        type: {
            code: { type: String, trim: true },
            name: { type: String, trim: true },
            _id: { type: ObjectId },
        },
        description: { type: String },
        pages: [
            {
                elements: [
                    {
                        title: { type: String, trim: true },
                        type: { type: String, trim: true },
                        name: { type: String, trim: true },
                        users: [
                            {
                                role: { type: String, trim: true },
                                email: { type: String },
                                userId: { type: ObjectId },
                                name: { type: String },
                                view: { type: Boolean },
                                edit: { type: Boolean },
                                modify: { type: Boolean },
                                fill: { type: Boolean },
                                evaluate: { type: Boolean },
                            },
                        ],
                        roles: [
                            {
                                role: { type: String, trim: true },
                                view: { type: Boolean },
                                edit: { type: Boolean },
                                modify: { type: Boolean },
                                fill: { type: Boolean },
                                evaluate: { type: Boolean },
                                roleId: { type: ObjectId },
                            },
                        ],
                        elements: [],
                    },
                ],
                name: { type: String, trim: true },
            },
        ],
        isDeleted: { type: Boolean, default: false },
        status: { type: String },
        marks: { type: Number },
        evaluations: [
            {
                role: { type: String },
                roleId: { type: ObjectId },
                marks: { type: Number },
                rubrics: [rubric],
            },
        ],
        publishedDate: { type: Date },
        createdBy: {
            id: { type: ObjectId },
            name: {
                first: {
                    type: String,
                    trim: true,
                },
                middle: {
                    type: String,
                    trim: true,
                },
                last: {
                    type: String,
                    trim: true,
                },
            },
        },
        isTemplate: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(FORM, schema);
