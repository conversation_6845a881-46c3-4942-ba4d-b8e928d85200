const StudentResponseService = require('./student-response.service');

const startForm = async ({
    query: { formId, portfolioId, componentId, childrenId, scheduleId },
    headers: { user_id: userId } = {},
}) => {
    const result = await StudentResponseService.startForm({
        formId,
        portfolioId,
        componentId,
        childrenId,
        userId,
        scheduleId,
    });

    return { message: 'FORM_STARTED_SUCCESSFULLY', data: result };
};

const updateStudentResponse = async ({
    query: { studentResponseId },
    body: { pageId = '', sectionId = '', section = {} },
    headers: { user_id: userId } = {},
}) => {
    await StudentResponseService.updateStudentResponse({
        studentResponseId,
        pageId,
        sectionId,
        section,
        userId,
    });

    return { statusCode: 200, message: 'FORM_UPDATED_SUCCESSFULLY' };
};

const submitForm = async ({
    query: { studentResponseId },
    body: { pages = [] },
    headers: { user_id: userId } = {},
}) => {
    const result = await StudentResponseService.submitForm({ studentResponseId, pages, userId });

    return { message: 'FORM_SUBMITTED_SUCCESSFULLY', data: result };
};

const getStudentResponse = async ({
    query: { formId, portfolioId, componentId, childrenId, scheduleId },
    headers: { user_id: userId } = {},
}) => {
    const response = await StudentResponseService.getStudentResponse({
        formId,
        portfolioId,
        componentId,
        childrenId,
        userId,
        scheduleId,
    });

    return { message: 'STUDENT_RESPONSE_FETCHED_SUCCESSFULLY', data: response };
};

module.exports = {
    startForm,
    updateStudentResponse,
    submitForm,
    getStudentResponse,
};
