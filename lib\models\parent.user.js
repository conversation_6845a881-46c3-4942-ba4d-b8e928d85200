const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const constant = require('../utility/constants');
const ObjectId = Schemas.Types.ObjectId;

const parentUserSchemas = new Schemas(
    {
        _institution_id: { type: ObjectId },
        email: {
            type: String,
            required: true,
            unique: true,
            sparse: true,
        },
        relationType: { type: String },
        name: {
            type: String,
        },
        password: String,
        mobile: {
            type: Number,
        },
        childIds: [
            {
                type: ObjectId,
                ref: constant.USER,
            },
        ],
        device_type: String,
        fcm_token: {
            type: String,
        },
        otp: {
            no: {
                type: Number,
                default: 0,
            },
            expiry_date: {
                type: Date,
                default: Date.now(),
            },
        },
        last_login: {
            type: Date,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.PARENT_USER, parentUserSchemas);
