const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    LEVEL_BASED_ACCESS,
    USER,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    ROLE,
    INSTITUTION,
} = require('../utility/constants');

const levelBasedAccessSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        studentGroup: { type: Boolean, default: false },
        schedule: { type: Boolean, default: false },
        userIds: [{ type: ObjectId, ref: USER }],
        roleId: { type: ObjectId, ref: ROLE },
        level: { type: String },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        curriculumId: { type: ObjectId, ref: DIGI_CURRICULUM },
    },
    {
        timestamps: true,
    },
);
module.exports = model(LEVEL_BASED_ACCESS, levelBasedAccessSchema);
