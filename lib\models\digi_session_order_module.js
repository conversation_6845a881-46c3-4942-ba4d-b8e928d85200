const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const digi_session_order_modules = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_COURSE,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
            required: true,
        },
        moduleName: { type: String, trim: true },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_SESSION_ORDER_MODULE, digi_session_order_modules);
