const roleModel = require('../lib/models/role');
// const roleAssignModel = require('../lib/models/role_assign');
const { convertToMongoObjectId } = require('../lib/utility/common');

const { LOCAL_ENV, DC_STAFF } = require('../lib/utility/constants');
const { APP_STATE } = require('../lib/utility/util_keys');

const defaultPolicy = {
    DC_STUDENT: 'dc_student',
    DC_STAFF: 'dc_staff',
    SIGNUP: 'signup',
    FORGET: 'forget',
};

const defaultProgramInputPolicy = {
    VIEW: ['program_input:programs:view', 'program_input:programs:active_programs:view'],
    EDIT: ['program_input:programs:active_programs:edit'],
    ADD: ['program_input:programs:add_program'],
};

// const defaultArrayPolicy = Object.values(defaultPolicy);

/**
 * Middleware for user policy authentication
 * @param {Array} policiesArray - Array of policies to match
 * @param {Object} options - Configuration for parameter or query-based user type
 * @param {string} options.paramName - Parameter name to extract user type
 * @param {string} options.queryName - Query name to extract user type
 * @returns {Function} Middleware function
 */
const userPolicyAuthentication = (policiesArray, { paramName, queryName } = {}) => {
    return async (req, res, next) => {
        if (APP_STATE === LOCAL_ENV) next();
        const {
            _id: userIdFromToken,
            userType: userTypeFromToken,
            userRoleData,
        } = req.tokenPayload || {};
        const { _user_id, user_id, role_id } = req.headers;
        const userTypeFromUrl = paramName
            ? req.params[paramName]?.toLowerCase()
            : queryName && req.query[queryName]?.toLowerCase();
        const userIdFromHeaders = _user_id || user_id;
        let defaultArrayPolicy =
            userTypeFromToken === DC_STAFF
                ? [
                      defaultPolicy.DC_STAFF,
                      defaultPolicy.SIGNUP,
                      defaultPolicy.FORGET,
                      ...defaultProgramInputPolicy.VIEW,
                      ...defaultProgramInputPolicy.EDIT,
                      ...defaultProgramInputPolicy.ADD,
                  ]
                : [defaultPolicy.DC_STUDENT, defaultPolicy.SIGNUP, defaultPolicy.FORGET];
        if (
            /* !policiesArray.length && */ userTypeFromToken === DC_STAFF &&
            policiesArray.some((item) => item.includes(':'))
        ) {
            // Getting User Policy based on Role ID
            const userRoleId = userRoleData.find(
                (userRoleElement) => String(userRoleElement._role_id) === role_id,
            );
            if (userRoleId) {
                // ! Need to load Datas to Redis for Easier Operation
                const rolePolicy = await roleModel
                    .findOne(
                        { _id: convertToMongoObjectId(role_id) },
                        {
                            policy: 1,
                        },
                    )
                    .lean();
                if (rolePolicy?.policy)
                    defaultArrayPolicy = [...defaultArrayPolicy, ...rolePolicy.policy];
            }
        }

        if (
            userIdFromToken === userIdFromHeaders &&
            (!userTypeFromUrl || userTypeFromUrl === userTypeFromToken.toLowerCase()) &&
            policiesArray.some((policy) => defaultArrayPolicy.includes(policy))
        ) {
            return next();
        }
        return res.status(403).send({
            status_code: 403,
            status: false,
            message: 'You are not authorized to perform this operation',
        });
    };
};

module.exports = {
    defaultPolicy,
    /* checkUserPolicy, */ userPolicyAuthentication,
    defaultProgramInputPolicy,
};
