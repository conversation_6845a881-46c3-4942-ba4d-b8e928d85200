const constant = require('../utility/constants');
const modules = require('mongoose').model(constant.MODULES);
const institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');

exports.list = async (req, res) => {
    const module_list = await base_control.get_list(
        modules,
        {
            _institution_id: common_files.convertToMongoObjectId(req.headers._institution_id),
            isDeleted: false,
        },
        {},
    );
    if (module_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    'Modules List',
                    module_list.data,
                ),
            );
    return res
        .status(200)
        .send(common_files.responseFunctionWithRequest(req, 200, true, 'Modules List', []));
};

exports.list_id = async (req, res) => {
    const module_list = await base_control.get(
        modules,
        {
            _institution_id: common_files.convertToMongoObjectId(req.headers._institution_id),
            _id: common_files.convertToMongoObjectId(req.params.id),
            isDeleted: false,
        },
        {},
    );
    if (module_list.status)
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    'Modules Single Get',
                    module_list.data,
                ),
            );
    return res
        .status(200)
        .send(common_files.response_function(res, 200, true, 'Modules Single Get', {}));
};

exports.insert = async (req, res) => {
    const institution_check = await base_control.get(institution, {
        _id: req.headers._institution_id,
        isDeleted: false,
    });
    if (!institution_check.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    'Institution not found',
                    'Institution not found',
                ),
            );
    req.body._institution_id = req.headers._institution_id;
    const doc = await base_control.insert(modules, req.body);
    if (doc.status)
        return res
            .status(201)
            .send(common_files.response_function(res, 201, true, 'Modules Created', doc.data));
    return res
        .status(410)
        .send(
            common_files.response_function(res, 410, false, 'Unable to Create Modules', doc.data),
        );
};

exports.update = async (req, res) => {
    const doc = await base_control.update(
        modules,
        common_files.convertToMongoObjectId(req.params.id),
        req.body,
    );
    if (doc.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Modules Updated', doc.data));
    return res
        .status(410)
        .send(
            common_files.response_function(res, 410, false, 'Unable to Update Modules', doc.data),
        );
};

exports.delete = async (req, res) => {
    const doc = await base_control.delete(
        modules,
        common_files.convertToMongoObjectId(req.params.id),
    );
    if (doc.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Modules Removed', doc.data));
    return res
        .status(410)
        .send(
            common_files.response_function(res, 410, false, 'Unable to Modules Removed', doc.data),
        );
};

exports.page_update = async (req, res) => {
    const doc = await base_control.update_condition(
        modules,
        { _id: common_files.convertToMongoObjectId(req.params.id) },
        { $push: { pages: req.body } },
    );
    if (doc.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Modules Updated', doc.data));
    return res
        .status(410)
        .send(
            common_files.response_function(res, 410, false, 'Unable to Update Modules', doc.data),
        );
};
