const mongoose = require('mongoose');
const {
    LMS_STUDENT_SETTING_CALENDAR,
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    DAYS_MODE: { YEAR, MONTH },
    ROL<PERSON>,
    USER,
    DIGI_PROGRAM,
    CALENDAR: { PROGRAM },
    CUSTOM,
    ALL,
    ANY,
    BY_USER,
    ROLE_LIST,
    AUTOMATIC,
    MANUAL,
    CUMULATIVE,
    INDIVIDUAL,
    USER_BASED,
    ROLE_BASED,
    ALL_USERS,
    ANY_ONE_IN_EACH_ROLE,
    ANY_ONE_USER,
    ANY_ONE_IN_ANY_ROLE,
    TILL_THE_END_OF_LEVEL,
    START_OF_THE_NEXT_LEVEL,
    END_OF_THE_COURSES,
    START_OF_THE_NEXT_ACADEMIC_YEAR,
    END_OF_THE_ACADEMIC_YEAR,
} = require('../utility/constants');
const { MALE, FEMALE, BOTH } = require('../utility/enums');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;

const lmsSettingCalendarSchemas = new Schema(
    {
        _institution_id: { type: ObjectId },
        _institution_calendar_id: { type: ObjectId },
        warningConfig: [
            {
                typeName: { type: Number },
                unappliedLeaveConsideredAs: { type: String },
                labelName: { type: String },
                percentage: { type: Number },
                colorCode: { type: String },
                message: { type: String },
                denialManagement: {
                    accessType: { type: String },
                    roleIds: [{ type: ObjectId, ref: ROLE }],
                    userIds: [{ type: ObjectId, ref: USER }],
                    turnAroundTime: {
                        type: String,
                        enum: [
                            TILL_THE_END_OF_LEVEL,
                            START_OF_THE_NEXT_LEVEL,
                            END_OF_THE_COURSES,
                            START_OF_THE_NEXT_ACADEMIC_YEAR,
                            END_OF_THE_ACADEMIC_YEAR,
                        ],
                    },
                },
                denialCondition: { type: String },
                categoryWisePercentage: [
                    {
                        categoryId: ObjectId,
                        categoryName: String,
                        percentage: Number,
                    },
                ],
                notificationToParent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                },
                isAdditionStaffNotify: { type: Boolean },
                notificationRoleIds: [{ type: ObjectId, ref: ROLE }],
                notificationToStudent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                    sendNotificationAuthority: [{ type: ObjectId, ref: ROLE }],
                },
                notificationToStaff: { type: Boolean, default: false },
                restrictCourseAccess: { type: Boolean, default: false },
                isActive: { type: Boolean, default: true },
                //below key is phase2
                meetingWithStudent: { type: Boolean, default: false },
                meetingRoleIds: [{ type: ObjectId, ref: ROLE }],
                acknowledgeToStudent: { type: Boolean, default: false },
                markItMandatory: { type: Boolean, default: false },
                turnAroundTime: { type: Number },
                escalationLevels: [
                    {
                        levelName: { type: String },
                        escalatingRoleIds: [{ type: ObjectId, ref: ROLE }],
                        turnAroundTime: { type: Number },
                    },
                ],
            },
        ],
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(LMS_STUDENT_SETTING_CALENDAR, lmsSettingCalendarSchemas);
