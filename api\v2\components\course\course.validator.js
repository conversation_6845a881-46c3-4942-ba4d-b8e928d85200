const Joi = require('joi');
const {
    COURSE_TYPE,
    COURSE_TYPE_GET,
    ADDED,
    DRAFTED,
    PREREQUISITE,
    COREQUISITE,
} = require('../../utility/constants');

const courseAddSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                _curriculum_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .allow(null)
                    .required()
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .allow(null)
                    .required()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                courseCode: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_CODE_REQUIRED';
                    }),
                courseName: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_NAME_REQUIRED';
                    }),
                courseType: Joi.string()
                    .valid(COURSE_TYPE.STANDARD, COURSE_TYPE.SELECTIVE)
                    .required()
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.string()
                            .min(1)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.string()
                            .min(1)
                            .required()
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const courseConfigAddSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _course_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error(() => {
                        return 'COURSE_ID_REQUIRED';
                    }),
                _curriculum_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
                duration: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.number(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return 'DURATION_REQUIRED';
                    }),
                courseCode: Joi.string().error(() => {
                    return 'COURSE_CODE_REQUIRED';
                }),
                courseName: Joi.string().error(() => {
                    return 'COURSE_NAME_REQUIRED';
                }),
                isYearLongCourse: Joi.boolean().error(() => {
                    return error;
                }),
                isPhaseFlowWithOutLevel: Joi.boolean().error((error) => {
                    return error;
                }),
                courseOccurringYearWise: Joi.array().items(
                    Joi.object({
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
                courseRecurringYearWise: Joi.array().items(
                    Joi.object({
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
                courseOccurring: Joi.array().items(
                    Joi.object({
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                        _level_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        levelNo: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
                courseRecurring: Joi.array().items(
                    Joi.object({
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                        _level_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        levelNo: Joi.string()
                            .min(1)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),

                portfolio: Joi.array().items(
                    Joi.object({
                        url: Joi.string().error((error) => {
                            return error;
                        }),
                        title: Joi.string().error((error) => {
                            return error;
                        }),
                        description: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                ),
                allowEditing: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.boolean().required(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return error;
                    }),
                achieveTarget: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.boolean().required(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return error;
                    }),

                draft: Joi.boolean()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                courseAssignedDetails: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _curriculum_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'CURRICULUM_ID_REQUIRED';
                            }),
                        curriculumName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'CURRICULUM_NAME_REQUIRED';
                            }),
                        _year_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        _level_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        levelNo: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        courseDuration: Joi.any().when('draft', {
                            is: false,
                            then: Joi.object({
                                startWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                endWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                total: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                            }).required(),
                            otherwise: Joi.object({
                                startWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                endWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                total: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                            }).optional(),
                        }),
                        courseSharedWith: Joi.array().items(
                            Joi.object({
                                _program_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'PROGRAM_ID_REQUIRED';
                                    }),
                                programName: Joi.string().error(() => {
                                    return 'PROGRAM_NAME_REQUIRED';
                                }),
                                _curriculum_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'CURRICULUM_ID_REQUIRED';
                                    }),
                                curriculumName: Joi.string().error(() => {
                                    return 'CURRICULUM_NAME_REQUIRED';
                                }),
                                _year_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                year: Joi.string().error((error) => {
                                    return error;
                                }),
                                _level_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                levelNo: Joi.string().error((error) => {
                                    return error;
                                }),
                            }),
                        ),
                    }),
                ),
                preRequisiteCourses: Joi.array().items(
                    Joi.object({
                        _course_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_NAME_REQUIRED';
                            }),
                        courseCode: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_CODE_REQUIRED';
                            }),
                    }),
                ),
                coRequisiteCourses: Joi.array().items(
                    Joi.object({
                        _course_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_NAME_REQUIRED';
                            }),
                        courseCode: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_CODE_REQUIRED';
                            }),
                    }),
                ),
                courseType: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.string()
                            .valid(COURSE_TYPE.STANDARD, COURSE_TYPE.SELECTIVE)
                            .required(),
                        otherwise: Joi.string()
                            .valid(COURSE_TYPE.STANDARD, COURSE_TYPE.SELECTIVE)
                            .optional(),
                    })
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.any().when('draft', {
                        is: false,
                        then: Joi.string().alphanum().length(24).required(),
                        otherwise: Joi.string().alphanum().length(24).optional(),
                    }),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
                sessionDeliveryType: Joi.array().items(
                    Joi.object({
                        typeName: Joi.string()
                            .min(1)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        typeSymbol: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).max(50).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        contactHours: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number().required(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        creditHours: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number().required(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        durationSplit: Joi.boolean().error((error) => {
                            return error;
                        }),
                        duration: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return 'DURATION_REQUIRED';
                            }),
                        _session_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'SESSION_ID_REQUIRED';
                            }),
                        deliveryType: Joi.array().items(
                            Joi.object({
                                _delivery_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'DELIVERY_ID_REQUIRED';
                                    }),
                                deliveryType: Joi.string()
                                    .min(1)
                                    .max(100)
                                    .error(() => {
                                        return 'DELIVERY_TYPE_REQUIRED';
                                    }),
                                deliverySymbol: Joi.any()
                                    .when('draft', {
                                        is: false,
                                        then: Joi.string().min(1).max(50).required(),
                                        otherwise: Joi.string().optional(),
                                    })
                                    .error(() => {
                                        return 'DELIVERY_SYMBOL_REQUIRED';
                                    }),
                                duration: Joi.number().error((error) => {
                                    return error;
                                }),
                            }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const portfolioDeleteSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'COURSE_ID_REQUIRED';
                    }),
                _portfolio_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'PORTFOLIO_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const requisiteCourseSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                courses: Joi.array().items(
                    Joi.object({
                        _course_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_NAME_REQUIRED';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const courseGetSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'COURSE_ID_REQUIRED';
                    }),
                type: Joi.string()
                    .valid(COURSE_TYPE_GET.COURSE, COURSE_TYPE_GET.COURSE_CONFIG)
                    .required()
                    .error(() => {
                        return 'TYPE_MUST_BE_COURSE_OR_CONFIGURATION';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const courseGetCurriculamWiseSchema = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _curriculum_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const courseGetProgramWiseSchema = Joi.object()
    .keys({
        body: Joi.object().keys({
            levelIds: Joi.array().items(
                Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'LEVEL_ID_REQUIRED';
                    }),
            ),
            type: Joi.string().required().valid(PREREQUISITE, COREQUISITE),
        }),
        params: Joi.object()
            .keys({
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                _curriculum_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'COURSE_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const addCourseThemeValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .required()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                courseId: Joi.string()
                    .required()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                themes: Joi.array().items(
                    Joi.object({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        name: Joi.string()
                            .min(2)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        symbol: Joi.string()
                            .min(1)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        color: Joi.string()
                            .min(2)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        description: Joi.string()
                            .min(2)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        subThemes: Joi.array().items(
                            Joi.object({
                                _id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                name: Joi.string()
                                    .min(2)
                                    .max(100)
                                    .error((error) => {
                                        return error;
                                    }),
                                symbol: Joi.string()
                                    .min(1)
                                    .max(100)
                                    .error((error) => {
                                        return error;
                                    }),
                                color: Joi.string()
                                    .min(2)
                                    .max(100)
                                    .error((error) => {
                                        return error;
                                    }),
                                description: Joi.string()
                                    .min(2)
                                    .max(100)
                                    .error((error) => {
                                        return error;
                                    }),
                            }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const getCourseThemeValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            courseId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
            themeId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'THEME_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const getSubCourseThemeValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            courseId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
            subThemeId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SUB_THEME_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const listCourseThemeValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            courseId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const linkSessionValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .required()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                id: Joi.string()
                    .min(2)
                    .max(100)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                sessionOrder: Joi.array().items(
                    Joi.object({
                        sNO: Joi.number().error((error) => {
                            return error;
                        }),
                        deliverySymbol: Joi.string()
                            .min(1)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        deliveryNo: Joi.string().error((error) => {
                            return error;
                        }),
                        _session_id: Joi.string()
                            .required()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _theme_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _deliveryType_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const editLinkSessionValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
            linkedSessionId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'LINK_SESSION_ID_REQUIRED';
                }),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                sessionOrder: Joi.array().items(
                    Joi.object({
                        sNO: Joi.number().error((error) => {
                            return error;
                        }),
                        deliverySymbol: Joi.string()
                            .min(1)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        deliveryNo: Joi.string().error((error) => {
                            return error;
                        }),
                        _session_id: Joi.string()
                            .required()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _theme_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _deliveryType_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const getUnlinkedSessionsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const getLinkedSessionsOrderValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseDeleteSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseGetListSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _curriculum_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
            _year_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
            _level_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseAssigningValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            courseAssignedDetails: Joi.object({
                _program_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                isActive: Joi.boolean()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                programName: Joi.string()
                    .required()
                    .error(() => {
                        return 'PROGRAM_NAME_REQUIRED';
                    }),
                _curriculum_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
                curriculumName: Joi.string()
                    .required()
                    .error(() => {
                        return 'CURRICULUM_NAME_REQUIRED';
                    }),
                _year_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                year: Joi.string()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                _level_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                levelNo: Joi.string().error((error) => {
                    return error;
                }),
                courseDuration: Joi.object({
                    startWeek: Joi.number().error(() => {
                        return 'START_WEEK_REQUIRED';
                    }),
                    endWeek: Joi.number().error(() => {
                        return 'START_WEEK_REQUIRED';
                    }),
                    total: Joi.number().error(() => {
                        return 'START_WEEK_REQUIRED';
                    }),
                }).required(),
                isSharedWithIndependentCourse: Joi.boolean().error((error) => {
                    return error;
                }),
                courseSharedWith: Joi.array().items(
                    Joi.object({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string().error(() => {
                            return 'PROGRAM_NAME_REQUIRED';
                        }),
                        _curriculum_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'CURRICULUM_ID_REQUIRED';
                            }),
                        curriculumName: Joi.string().error(() => {
                            return 'CURRICULUM_NAME_REQUIRED';
                        }),
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        year: Joi.string().error((error) => {
                            return error;
                        }),
                        _level_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        levelNo: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                ),
            }),
        }),
    })
    .unknown(true);

const assignDeleteSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
            _assign_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'ASSIGN_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseEditSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                _curriculum_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .allow(null)
                    .required()
                    .error(() => {
                        return 'CURRICULUM_ID_REQUIRED';
                    }),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .allow(null)
                    .required()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                courseCode: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_CODE_REQUIRED';
                    }),
                courseName: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_NAME_REQUIRED';
                    }),
                courseType: Joi.string()
                    .valid(COURSE_TYPE.STANDARD, COURSE_TYPE.SELECTIVE)
                    .required()
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string()
                            .min(1)
                            .required()
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.string()
                            .min(1)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.string()
                            .min(1)
                            .required()
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const addIndependentCourseValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                courseCode: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_CODE_REQUIRED';
                    }),
                courseName: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_NAME_REQUIRED';
                    }),
                courseType: Joi.string()
                    .valid(COURSE_TYPE.INDEPENDENT)
                    .required()
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                administration: Joi.object({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error(() => {
                            return 'PROGRAM_ID_REQUIRED';
                        }),
                    programName: Joi.string()
                        .min(3)
                        .max(60)
                        .error(() => {
                            return 'PROGRAM_NAME_REQUIRED';
                        }),
                    _department_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'DEPARTMENT_ID_REQUIRED';
                        }),
                    departmentName: Joi.string()
                        .min(3)
                        .max(150)
                        .required()
                        .error(() => {
                            return 'DEPARTMENT_NAME_REQUIRED';
                        }),

                    _subject_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'SUBJECT_ID_REQUIRED';
                        }),
                    subjectName: Joi.string()
                        .required()
                        .error(() => {
                            return 'SUBJECT_NAME_REQUIRED';
                        }),
                }),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string()
                            .min(3)
                            .max(60)
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.string()
                            .min(3)
                            .max(150)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.string()
                            .required()
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const getIndependentCoursesValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            searchKey: Joi.string().error(() => {
                return 'SEARCH_KEY';
            }),
            limit: Joi.number()
                .integer()
                .required()
                .error(() => 'LIMIT_REQUIRED'),
            pageNo: Joi.number()
                .integer()
                .required()
                .error(() => 'PAGENO_REQUIRED'),
        }),
    })
    .unknown(true);

const getIndependentCourseValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            courseId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'COURSE_ID_REQUIRED';
                }),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const GetProgramCurriculumListValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const editIndependentCourseValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
                courseCode: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_CODE_REQUIRED';
                    }),
                courseName: Joi.string()
                    .required()
                    .error(() => {
                        return 'COURSE_NAME_REQUIRED';
                    }),
                courseType: Joi.string()
                    .valid(COURSE_TYPE.INDEPENDENT)
                    .required()
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_STANDARD_SELECTIVE';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.string().alphanum().length(24).required(),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                administration: Joi.object({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error(() => {
                            return 'PROGRAM_ID_REQUIRED';
                        }),
                    programName: Joi.string()
                        .min(3)
                        .max(60)
                        .error(() => {
                            return 'PROGRAM_NAME_REQUIRED';
                        }),
                    _department_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'DEPARTMENT_ID_REQUIRED';
                        }),
                    departmentName: Joi.string()
                        .min(3)
                        .max(150)
                        .required()
                        .error(() => {
                            return 'DEPARTMENT_NAME_REQUIRED';
                        }),

                    _subject_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'SUBJECT_ID_REQUIRED';
                        }),
                    subjectName: Joi.string()
                        .required()
                        .error(() => {
                            return 'SUBJECT_NAME_REQUIRED';
                        }),
                }),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string()
                            .min(3)
                            .max(60)
                            .required()
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.string()
                            .min(3)
                            .max(150)
                            .required()
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.string()
                            .required()
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
            })
            .unknown(true),
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SUBJECT_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const independentCourseConfigAddSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                _course_id: Joi.string()
                    .alphanum()
                    .required()
                    .length(24)
                    .error(() => {
                        return 'COURSE_ID_REQUIRED';
                    }),
                duration: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.number(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return 'DURATION_REQUIRED';
                    }),
                courseCode: Joi.string().error(() => {
                    return 'COURSE_CODE_REQUIRED';
                }),
                courseName: Joi.string().error(() => {
                    return 'COURSE_NAME_REQUIRED';
                }),
                portfolio: Joi.array().items(
                    Joi.object({
                        url: Joi.string().error((error) => {
                            return error;
                        }),
                        title: Joi.string().error((error) => {
                            return error;
                        }),
                        description: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                ),
                allowEditing: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.boolean().required(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return error;
                    }),
                achieveTarget: Joi.any()
                    .when('draft', {
                        is: false,
                        then: Joi.boolean().required(),
                        otherwise: Joi.optional(),
                    })
                    .error((error) => {
                        return error;
                    }),

                draft: Joi.boolean()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                courseAssignedDetails: Joi.array().items(
                    Joi.object({
                        courseDuration: Joi.any().when('draft', {
                            is: false,
                            then: Joi.object({
                                startWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                endWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                total: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                            }).required(),
                            otherwise: Joi.object({
                                startWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                endWeek: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                                total: Joi.number().error(() => {
                                    return 'START_WEEK_REQUIRED';
                                }),
                            }).optional(),
                        }),
                        courseSharedWith: Joi.array().items(
                            Joi.object({
                                _program_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'PROGRAM_ID_REQUIRED';
                                    }),
                                programName: Joi.string().error(() => {
                                    return 'PROGRAM_NAME_REQUIRED';
                                }),
                                _curriculum_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'CURRICULUM_ID_REQUIRED';
                                    }),
                                curriculumName: Joi.string().error(() => {
                                    return 'CURRICULUM_NAME_REQUIRED';
                                }),
                                _year_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                year: Joi.string().error((error) => {
                                    return error;
                                }),
                                _level_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                levelNo: Joi.string().error((error) => {
                                    return error;
                                }),
                            }),
                        ),
                    }),
                ),
                preRequisiteCourses: Joi.array().items(
                    Joi.object({
                        _course_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_NAME_REQUIRED';
                            }),
                        courseCode: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_CODE_REQUIRED';
                            }),
                    }),
                ),
                coRequisiteCourses: Joi.array().items(
                    Joi.object({
                        _course_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_NAME_REQUIRED';
                            }),
                        courseCode: Joi.string()
                            .min(1)
                            .error(() => {
                                return 'COURSE_CODE_REQUIRED';
                            }),
                    }),
                ),
                courseType: Joi.string()
                    .valid(COURSE_TYPE.INDEPENDENT)
                    .required()
                    .error(() => {
                        return 'COURSE_TYPE_MUST_BE_INDEPENDENT';
                    }),
                courseEditor: Joi.object({
                    _staff_id: Joi.any().when('draft', {
                        is: false,
                        then: Joi.string().alphanum().length(24).required(),
                        otherwise: Joi.string().alphanum().length(24).optional(),
                    }),
                    name: Joi.object({
                        first: Joi.string().optional(),
                        middle: Joi.string().optional().allow(''),
                        last: Joi.string().optional().allow(''),
                        family: Joi.string().optional().allow(''),
                    }).optional(),
                }).optional(),
                participating: Joi.array().items(
                    Joi.object({
                        _program_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                        _department_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'DEPARTMENT_ID_REQUIRED';
                            }),
                        departmentName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'DEPARTMENT_NAME_REQUIRED';
                            }),

                        _subject_id: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().alphanum().length(24).required(),
                                otherwise: Joi.string().alphanum().length(24).optional(),
                            })
                            .error(() => {
                                return 'SUBJECT_ID_REQUIRED';
                            }),
                        subjectName: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error(() => {
                                return 'SUBJECT_NAME_REQUIRED';
                            }),
                    }),
                ),
                sessionDeliveryType: Joi.array().items(
                    Joi.object({
                        typeName: Joi.string()
                            .min(1)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        typeSymbol: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.string().min(1).max(50).required(),
                                otherwise: Joi.string().optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        contactHours: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number().required(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        creditHours: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number().required(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return error;
                            }),
                        durationSplit: Joi.boolean().error((error) => {
                            return error;
                        }),
                        duration: Joi.any()
                            .when('draft', {
                                is: false,
                                then: Joi.number(),
                                otherwise: Joi.optional(),
                            })
                            .error((error) => {
                                return 'DURATION_REQUIRED';
                            }),
                        _session_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'SESSION_ID_REQUIRED';
                            }),
                        deliveryType: Joi.array().items(
                            Joi.object({
                                _delivery_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'DELIVERY_ID_REQUIRED';
                                    }),
                                deliveryType: Joi.string()
                                    .min(1)
                                    .max(100)
                                    .error(() => {
                                        return 'DELIVERY_TYPE_REQUIRED';
                                    }),
                                deliverySymbol: Joi.any()
                                    .when('draft', {
                                        is: false,
                                        then: Joi.string().min(1).max(50).required(),
                                        otherwise: Joi.string().optional(),
                                    })
                                    .error(() => {
                                        return 'DELIVERY_SYMBOL_REQUIRED';
                                    }),
                                duration: Joi.number().error((error) => {
                                    return error;
                                }),
                            }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const getIndependentCourseConfigValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const independentCourseAssignUpdateValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _course_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            draft: Joi.boolean()
                .required()
                .error((error) => {
                    return error;
                }),
            courseAssignedDetails: Joi.array().items(
                Joi.object({
                    courseDuration: Joi.any().when('draft', {
                        is: false,
                        then: Joi.object({
                            startWeek: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                            endWeek: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                            total: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                        }).required(),
                        otherwise: Joi.object({
                            startWeek: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                            endWeek: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                            total: Joi.number().error(() => {
                                return 'START_WEEK_REQUIRED';
                            }),
                        }).optional(),
                    }),
                    courseSharedWith: Joi.array().items(
                        Joi.object({
                            _program_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error(() => {
                                    return 'PROGRAM_ID_REQUIRED';
                                }),
                            programName: Joi.string().error(() => {
                                return 'PROGRAM_NAME_REQUIRED';
                            }),
                            _curriculum_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error(() => {
                                    return 'CURRICULUM_ID_REQUIRED';
                                }),
                            curriculumName: Joi.string().error(() => {
                                return 'CURRICULUM_NAME_REQUIRED';
                            }),
                            _year_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            year: Joi.string().error((error) => {
                                return error;
                            }),
                            _level_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            levelNo: Joi.string().error((error) => {
                                return error;
                            }),
                        }),
                    ),
                }),
            ),
        }),
    })
    .unknown(true);

module.exports = {
    addCourseThemeValidator,
    getCourseThemeValidator,
    getSubCourseThemeValidator,
    listCourseThemeValidator,
    getUnlinkedSessionsValidator,
    linkSessionValidator,
    editLinkSessionValidator,
    courseAddSchema,
    courseConfigAddSchema,
    portfolioDeleteSchema,
    requisiteCourseSchema,
    courseGetSchema,
    courseGetCurriculamWiseSchema,
    courseGetProgramWiseSchema,
    courseDeleteSchema,
    courseGetListSchema,
    courseAssigningValidation,
    assignDeleteSchema,
    addIndependentCourseValidator,
    getIndependentCoursesValidator,
    getIndependentCourseValidator,
    courseEditSchema,
    GetProgramCurriculumListValidator,
    editIndependentCourseValidator,
    independentCourseConfigAddSchema,
    getIndependentCourseConfigValidator,
    independentCourseAssignUpdateValidator,
};
