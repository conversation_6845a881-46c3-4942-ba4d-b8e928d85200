const express = require('express');
const route = express.Router();
const college_building = require('../college_building/college_building_controller');
const validater = require('./college_building_validator');
route.post('/list', college_building.list_values);
route.get('/locations', college_building.locations_list);
route.get('/:id', validater.college_building_id, college_building.list_id);
route.get('/', college_building.list);
route.post('/', validater.college_building, college_building.insert);
route.put('/:id', validater.college_building_id, validater.college_building, college_building.update);
route.delete('/:id', validater.college_building_id, college_building.delete);
module.exports = route;