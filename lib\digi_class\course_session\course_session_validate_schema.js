const Joi = require('joi');
const { com_response, comResponseWithRequest } = require('../../utility/common');

// update feedback schema
function updateCourseSessionSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                rating: Joi.string().optional(),
                comments: Joi.string().optional(),
            }),
            params: {
                scheduleId: Joi.string().length(24),
                studentId: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// pushStudent
function pushStudentSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                courseId: Joi.string().optional(),
                staffId: Joi.string().optional(),
                sessionId: Joi.string().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// get courses by staff id
function getCoursesParamSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                staffId: Joi.string().length(24),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
const updateClassModeSchema = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                mode: Joi.string(),
                staffId: Joi.string().alphanum().length(24),
                scheduleId: Joi.array().items(Joi.string().alphanum().length(24).required()),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

const updateAttendanceSchema = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                updateAttendance: Joi.array().items(
                    Joi.object({
                        scheduleId: Joi.string().alphanum().length(24).required(),
                        scheduleStatus: Joi.string(),
                        staffId: Joi.string().alphanum().length(24).required(),
                        staffs: Joi.array().items(
                            Joi.object({
                                staffId: Joi.string().alphanum().length(24).required(),
                                attendanceStatus: Joi.string(),
                            }),
                        ),
                        startDateAndTime: Joi.string(),
                        endDateAndTime: Joi.string(),
                        students: Joi.array().items(
                            Joi.object({
                                studentId: Joi.string().alphanum().length(24).required(),
                                attendanceStatus: Joi.string(),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

const updateStaffCourseExport = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                userId: Joi.string(),
                courseId: Joi.string(),
                institutionCalendarId: Joi.string(),
                programId: Joi.string(),
                levelNo: Joi.string(),
                yearNo: Joi.string(),
                term: Joi.string(),
                delivery_symbol: Joi.string(),
                gender: Joi.string(),
                groupNo: Joi.string(),
                rotationCount: Joi.string(),
                sessionGroupId: Joi.string(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

module.exports = {
    updateCourseSessionSchema,
    pushStudentSchema,
    getCoursesParamSchema,
    updateClassModeSchema,
    updateAttendanceSchema,
    updateStaffCourseExport,
};
