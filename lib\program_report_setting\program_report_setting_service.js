const constant = require('../utility/constants');
const ProgramReportSettings = require('../models/program_report_setting');
const { convertToMongoObjectId } = require('../utility/common');
exports.getProgramReport = async (_program_id, _institution_id) => {
    const programReportSettings = await ProgramReportSettings.findOne(
        {
            _program_id: convertToMongoObjectId(_program_id),
            // _institution_id: convertToMongoObjectId(_institution_id),
        },
        {
            status: 1,
            scalePoints: 1,
            scalePointStructure: 1,
            questions: 1,
            noOfQuestion: 1,
            _program_id: 1,
            _institution_id: 1,
            selfEvaluationSurvey: 1,
            sessionExperienceSurvey: 1,
            activity: 1,
            courseAdminUserPermission: 1,
            rattingScaleForSurvey: 1,
            benchMarkForSurveyAndActivities: 1,
            step: 1,
            benchMarkValue: 1,
            benchMarks: 1,
            courses: 1,
            scalePointStatus: 1,
            benchMarkStatus: 1,
        },
    ).lean();
    return programReportSettings;
};
exports.getProgramReportBenchMark = async (_program_id, _institution_id) => {
    const programReportBenchMark = await ProgramReportSettings.findOne(
        {
            _program_id: convertToMongoObjectId(_program_id),
            // _institution_id: convertToMongoObjectId(_institution_id),
        },
        {
            _program_id: 1,
            _institution_id: 1,
            benchMarks: 1,
            benchMarkStatus: 1,
            scalePoints: 1,
            status: 1,
        },
    ).lean();
    return programReportBenchMark;
};
