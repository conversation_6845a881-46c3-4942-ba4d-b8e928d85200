const programSchema = require('./program-input.model');
const courseSchema = require('../course/course.model');
const userSchema = require('../user-management/user.model');
const institutionSchema = require('../institution/institution.model');
const programInputFormat = require('./program-input.format');
// const CourseSchedule = require('../course-schedule/course-schedule.model');
const departmentSubjectSchema = require('../departments-subject/department-subject.model');
const curriculumSchema = require('../curriculum/curriculum.model');
const sessionDeliveryTypesSchema = require('../session-delivery-types/session-delivery-types.model');
const settingsSchema = require('../setting/setting.model');
const {
    ARCHIVED,
    ACTIVE,
    MASTER_TYPE,
    DS_DELETE,
    SESSION_DELIVERY_TYPES,
    CURRICULUM,
} = require('../../utility/constants');
const constant = require('../../utility/constants');
const {
    getPresignedUrlsForPrograms,
    populateSessionCurriculumDeptSubjects,
} = require('./program-input.util');
const { getPaginationValues } = require('../../utility/pagination');
const {
    DS_DELETED,
    DS_GET_FAILED,
    DS_DELETE_FAILED,
    DS_ARCHIVED_FAILED,
    DS_ARCHIVED,
    DS_RESTORE_FAILED,
    DS_RESTORE,
    DS_SAVED,
    DS_SAVE_FAILED,
    PROGRAM,
    USER,
    INSTITUTION,
    SETTINGS,
    DEPARTMENT_SUBJECT,
    COURSE,
} = require('../../utility/constants');
const {
    isIDEquals,
    convertToMongoId,
    convertToMongoObjectId,
    getModel,
} = require('../../utility/common');
const {
    formatGlobalConfiguration,
    transformProgramSpecificBasicDetails,
    formatGlobalConfigurationForProgram,
} = require('../setting/setting.format');

const addProgram = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const {
            programName,
            programType,
            _program_type_id,
            type,
            code,
            level,
            degree,
            noOfTerms,
            terms,
            _institution_id,
        } = body;
        let programData = {};
        if (type === MASTER_TYPE.PREREQUISITE) {
            programData = {
                ...programData,
                name: programName,
                type: MASTER_TYPE.PREREQUISITE,
                code,
                noOfTerms,
                programType,
                terms,
                _institution_id,
            };
        } else {
            if (!programType) return { statusCode: 400, message: 'PROGRAM_TYPE_REQUIRED' };
            programData = {
                ...programData,
                name: programName,
                programType,
                _program_type_id,
                type,
                code,
                noOfTerms,
                terms,
                _institution_id,
                level,
                degree,
            };
        }

        const institution = await institutionModel.findById(_institution_id).lean();
        if (!institution) {
            return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };
        }
        if (noOfTerms !== terms.length) {
            return { statusCode: 400, message: 'NO_OF_TERMS' };
        }

        const institutionSettings = await settingsModel
            .findOne(
                { _institution_id },
                {
                    'globalConfiguration.basicDetails.isGenderSegregation': 1,
                    'globalConfiguration.basicDetails.timeZone': 1,
                    'globalConfiguration.basicDetails.eventType': 1,
                    'globalConfiguration.basicDetails.breaks': 1,
                    'globalConfiguration.programInput.curriculumNaming': 1,
                    'globalConfiguration.programInput.creditHours': 1,
                    'globalConfiguration.programInput.programDurationFormat': 1,
                },
            )
            .lean();

        if (!institutionSettings) {
            return { statusCode: 400, message: 'INSTITUTION_SETTINGS_NOT_FOUND' };
        }

        const programCodeExists = await programModel
            .findOne({
                isDeleted: false,
                _institution_id,
                code: new RegExp('^' + code + '$', 'i'),
            })
            .lean();
        if (programCodeExists) {
            return { statusCode: 400, message: 'CODE_EXISTS' };
        }

        const programNameExists = await programModel
            .findOne({
                isDeleted: false,
                _institution_id,
                name: new RegExp('^' + programName + '$', 'i'),
            })
            .lean();
        if (programNameExists) {
            return { statusCode: 400, message: 'NAME_EXISTS' };
        }

        let basicDetails = {};
        let programInput = {};
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.basicDetails
        ) {
            const { isGenderSegregation, timeZone, eventType, breaks } =
                institutionSettings.globalConfiguration.basicDetails;
            basicDetails = {
                isGenderSegregation:
                    institutionSettings.globalConfiguration.basicDetails.isGenderSegregation,
                timeZone: institutionSettings.globalConfiguration.basicDetails.timeZone,
                eventType: institutionSettings.globalConfiguration.basicDetails.eventType,
                breaks: institutionSettings.globalConfiguration.basicDetails.breaks,
            };
        }
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.programInput
        ) {
            programInput = {
                curriculumNaming:
                    institutionSettings.globalConfiguration.programInput.curriculumNaming,
                creditHours: institutionSettings.globalConfiguration.programInput.creditHours,
                programDurationFormat:
                    institutionSettings.globalConfiguration.programInput.programDurationFormat,
            };
        }

        programData = { ...programData, settings: { basicDetails, programInput } };

        const newProgram = await programModel.create(programData);
        if (!newProgram) return { statusCode: 500, message: DS_SAVE_FAILED };
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// helper

const populatePrograms = async (
    programs,
    curriculumModel,
    departmentSubjectModel,
    sessionDeliveryModel,
) => {
    const programIds = programs.map((program) => convertToMongoId(program._id));
    const departmentsAndSubjects = await departmentSubjectModel
        .find(
            {
                _program_id: { $in: programIds },
                isActive: true,
                isDeleted: false,
            },
            { subject: 1, _program_id: 1 },
        )
        .lean();
    const curriculum = await curriculumModel
        .find(
            { _program_id: { $in: programIds }, isActive: true, isDeleted: false },
            { curriculumName: 1, _program_id: 1 },
        )
        .lean();
    const sessionDeliveryTypes = await sessionDeliveryModel
        .find(
            {
                _program_id: { $in: programIds },
                isDeleted: false,
            },
            {
                sessionSymbol: 1,
                sessionName: 1,
                _program_id: 1,
                deliveryTypes: 1,
            },
        )
        .lean();
    const populatedPrograms = populateSessionCurriculumDeptSubjects(
        programs,
        departmentsAndSubjects,
        curriculum,
        sessionDeliveryTypes,
    );
    return populatedPrograms;
};

const populateSingleProgram = async (
    programs,
    curriculumModel,
    departmentSubjectModel,
    sessionDeliveryModel,
    courseModel,
) => {
    const programIds = programs.map((program) => convertToMongoId(program._id));
    const departmentsAndSubjects = await departmentSubjectModel
        .find(
            {
                _program_id: { $in: programIds },
                isActive: true,
                isDeleted: false,
            },
            { subject: 1, _program_id: 1 },
        )
        .lean();
    const curriculum = await curriculumModel
        .find(
            { _program_id: { $in: programIds }, isActive: true, isDeleted: false },
            { curriculumName: 1, _program_id: 1, yearLevel: 1, creditHours: 1 },
        )
        .lean();
    const curriculumIds = curriculum.map((i) => convertToMongoObjectId(i._id));
    const courseDocs = await courseModel
        .find({
            $or: [
                { _curriculum_id: { $in: curriculumIds } },
                { 'courseAssignedDetails._curriculum_id': { $in: curriculumIds } },
                { 'courseAssignedDetails.courseSharedWith._curriculum_id': { $in: curriculumIds } },
            ],
            isDeleted: false,
            isActive: true,
        })
        .select('courseAssignedDetails sessionDeliveryType');
    const curriculumDatas = [];
    let isConfigured = true;
    let isPreRequisite = false;
    let certificateCount = 0;
    curriculum.forEach((curriculumDoc, index) => {
        let levelData = [];
        const yearData = [];
        const creditHours = [];
        curriculumDoc.creditHours.credit.forEach((sessionElement) => {
            creditHours.push({
                sessionType: sessionElement.sessionType,
                minDuration: sessionElement.min,
                maxDuration: sessionElement.max,
                finishedDuration: 0,
            });
        });
        certificateCount = 0;
        curriculumDoc.yearLevel.forEach((yearElement) => {
            yearElement.levels.forEach((levelElement) => {
                let course_count = 0;
                courseDocs.forEach((courseDoc) => {
                    courseDoc.courseAssignedDetails.forEach((courseAssignedDetail) => {
                        if (
                            String(courseAssignedDetail._level_id) === String(levelElement._id) &&
                            String(courseAssignedDetail._year_id) === String(yearElement._id)
                        ) {
                            course_count += 1;
                            courseDoc.sessionDeliveryType.forEach((session) => {
                                if (curriculum[index].creditHours.hourAs === constant.SPLIT_UP) {
                                    const creditHoursIndex = creditHours.findIndex(
                                        (creditHour) => session.typeName === creditHour.sessionType,
                                    );
                                    if (creditHoursIndex !== -1)
                                        creditHours[creditHoursIndex].finishedDuration +=
                                            session.creditHours;
                                } else if (
                                    curriculum[index].creditHours.hourAs === constant.TOTAL
                                ) {
                                    creditHours[0].finishedDuration += session.creditHours;
                                }
                            });
                        } else if (
                            courseAssignedDetail.courseSharedWith &&
                            courseAssignedDetail.courseSharedWith.length
                        ) {
                            courseAssignedDetail.courseSharedWith.forEach((courseSharedWit) => {
                                if (
                                    String(courseSharedWit._level_id) ===
                                        String(levelElement._id) &&
                                    String(courseSharedWit._year_id) === String(yearElement._id)
                                ) {
                                    course_count += 1;
                                    courseDoc.sessionDeliveryType.forEach((session) => {
                                        if (
                                            curriculum[index].creditHours.hourAs ===
                                            constant.SPLIT_UP
                                        ) {
                                            const creditHoursIndex = creditHours.findIndex(
                                                (creditHour) =>
                                                    session.typeName === creditHour.sessionType,
                                            );
                                            if (creditHoursIndex !== -1)
                                                creditHours[creditHoursIndex].finishedDuration +=
                                                    session.creditHours;
                                        } else if (
                                            curriculum[index].creditHours.hourAs === constant.TOTAL
                                        ) {
                                            creditHours[0].finishedDuration += session.creditHours;
                                        }
                                    });
                                }
                            });
                        }
                    });
                });
                levelData.push({
                    _id: levelElement._id,
                    level_name: levelElement.levelName,
                    start_week: levelElement.startWeek,
                    end_week: levelElement.endWeek,
                    course_count,
                });
                if (course_count === 0) {
                    isConfigured = false;
                }
            });
            if (yearElement.certificate) {
                certificateCount += 1;
            }
            if (yearElement._preRequisite_id) {
                isPreRequisite = true;
            }
            const refactorCurriculumDoc = { ...yearElement };
            refactorCurriculumDoc.levels = levelData;
            levelData = [];
            yearData.push(refactorCurriculumDoc);
        });
        if (curriculum[index].creditHours.hourAs === constant.SPLIT_UP) {
            const totalFinishedDuration = creditHours.reduce(
                (acc, creditHours) => acc + creditHours.finishedDuration,
                0,
            );
            creditHours[creditHours.length - 1].finishedDuration = totalFinishedDuration;
        }
        const refactorCurriculumDoc = {
            ...curriculumDoc,
            isConfigured,
            isPreRequisite,
            certificateCount,
        };
        refactorCurriculumDoc.yearLevel = yearData;
        refactorCurriculumDoc.creditHours.credit = creditHours;
        curriculumDatas.push(refactorCurriculumDoc);
    });
    const sessionDeliveryTypes = await sessionDeliveryModel
        .find(
            {
                _program_id: { $in: programIds },
                isDeleted: false,
            },
            {
                sessionSymbol: 1,
                sessionName: 1,
                _program_id: 1,
                deliveryTypes: 1,
            },
        )
        .lean();
    const populatedProgram = populateSessionCurriculumDeptSubjects(
        programs,
        departmentsAndSubjects,
        curriculumDatas,
        sessionDeliveryTypes,
        courseDocs,
        true,
    );
    return populatedProgram;
};

const getInstitutePrograms = async ({ query = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programInputModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypesSchema,
        );
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { id } = params;
        const { searchKey, tab, programType } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        let dbQuery = { _institution_id: convertToMongoId(id), isDeleted: false };
        const totalProgramsList = await programInputModel.find(dbQuery).countDocuments().lean();
        if (programType && programType === MASTER_TYPE.PREREQUISITE) {
            dbQuery = { ...dbQuery, type: MASTER_TYPE.PREREQUISITE };
        } else if (programType) {
            dbQuery = { ...dbQuery, programType };
        }
        if (searchKey && searchKey.length) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    { name: { $regex: searchKey, $options: 'i' } },
                    { code: { $regex: searchKey, $options: 'i' } },
                ],
            };
        }
        if (tab === ARCHIVED) {
            dbQuery = { ...dbQuery, isActive: false };
        } else if (tab === ACTIVE) {
            dbQuery = { ...dbQuery, isActive: true };
        }
        let programs = await programInputModel
            .find(dbQuery, {
                code: 1,
                name: 1,
                programType: 1,
                degree: 1,
                type: 1,
                noOfTerms: 1,
                terms: 1,
                _id: 1,
                level: 1,
                isActive: 1,
            })
            .sort({ _id: -1 })
            .skip(skip)
            .limit(limit)
            .lean();
        programs = await populatePrograms(
            programs,
            curriculumModel,
            departmentSubjectModel,
            sessionDeliveryModel,
        );
        const totalPrograms = await programInputModel.find(dbQuery).countDocuments().lean();
        const activeQueryCount = {
            _institution_id: id,
            isDeleted: false,
            isActive: true,
        };
        const archiveQueryCount = {
            _institution_id: id,
            isDeleted: false,
            isActive: false,
        };
        if (searchKey && searchKey.length) {
            activeQueryCount.$or = [
                { name: { $regex: searchKey, $options: 'i' } },
                { code: { $regex: searchKey, $options: 'i' } },
            ];
            archiveQueryCount.$or = [
                { name: { $regex: searchKey, $options: 'i' } },
                { code: { $regex: searchKey, $options: 'i' } },
            ];
        }
        if (programType) {
            activeQueryCount.programType = programType;
            archiveQueryCount.programType = programType;
        }
        dbQuery.isActive = true;
        const activeProgramCount = await programInputModel.find(dbQuery).countDocuments().lean();
        dbQuery.isActive = false;
        const archivedProgramCount = await programInputModel.find(dbQuery).countDocuments().lean();
        if (!programs || Number.isNaN(totalPrograms))
            return { statusCode: 500, message: DS_GET_FAILED };
        const totalPages = Math.ceil(totalPrograms / limit);
        return {
            statusCode: 200,
            data: {
                totalPages,
                currentPage: pageNo,
                programs: await programInputFormat.program(programs, id),
                activeProgramCount,
                archivedProgramCount,
                total: totalProgramsList,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editProgram = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { id } = params;
        const {
            programName,
            programType,
            _program_type_id,
            type,
            code,
            level,
            degree,
            noOfTerms,
            terms,
            _institution_id,
        } = body;
        let updateQuery = {};
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const userModel = getModel(tenantURL, USER, userSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        if (type === MASTER_TYPE.PREREQUISITE) {
            updateQuery = {
                ...updateQuery,
                name: programName,
                type,
                code,
                noOfTerms,
                programType: MASTER_TYPE.PREREQUISITE,
                terms,
            };
        } else {
            if (!programType) return { statusCode: 400, message: 'PROGRAM_TYPE_REQUIRED' };
            updateQuery = {
                ...updateQuery,
                name: programName,
                programType,
                _program_type_id,
                code,
                noOfTerms,
                terms,
                level,
                type,
                degree,
            };
        }

        if (programName) {
            const programNameExists = await programModel.findOne({
                _id: { $ne: id },
                name: new RegExp('^' + programName + '$', 'i'),
                isDeleted: false,
                _institution_id,
            });
            if (programNameExists) {
                return { statusCode: 400, message: 'NAME_EXISTS' };
            }
            const updateAllocation = await userModel.updateMany(
                {
                    'academic_allocation._program_id': convertToMongoObjectId(id),
                },
                {
                    'academic_allocation.$[i].programName': programName,
                },
                {
                    arrayFilters: [
                        {
                            'i._program_id': convertToMongoObjectId(id),
                        },
                    ],
                },
            );
            const updateStudentProgram = await userModel.updateMany(
                {
                    'program._program_id': convertToMongoObjectId(id),
                },
                {
                    'program.value': programName,
                },
            );
        }

        if (code) {
            const programCodeExists = await programModel
                .findOne({
                    _id: { $ne: id },
                    code: new RegExp('^' + code + '$', 'i'),
                    isDeleted: false,
                    _institution_id,
                })
                .lean();
            if (programCodeExists) {
                return { statusCode: 400, message: 'CODE_EXISTS' };
            }
        }

        if (noOfTerms && terms && noOfTerms !== terms.length) {
            return { statusCode: 400, message: 'NO_OF_TERMS' };
        }

        const program = await programModel
            .findByIdAndUpdate(id, updateQuery, {
                new: true,
                select: { code: 1, name: 1, programType: 1, degree: 1 },
            })
            .lean();
        if (!program) return { statusCode: 500, message: DS_SAVE_FAILED };
        // start updating department subject table
        const DepartmentSubjects = await departmentSubjectModel.find(
            { $or: [{ _program_id: id }, { 'subject.sharedWith._program_id': id }] },
            {},
        );
        const bulkUpdate = [];
        for (DepartmentSubjectList of DepartmentSubjects) {
            if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                for (subjectList of DepartmentSubjectList.subject) {
                    if (subjectList.sharedWith.length > 0) {
                        const programIdCheck = subjectList.sharedWith.filter(function (element) {
                            return element._program_id.toString() === id.toString();
                        });
                        if (programIdCheck.length > 0) {
                            for (sharedProgram of programIdCheck)
                                bulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoId(DepartmentSubjectList._id),
                                        },
                                        update: {
                                            $set: {
                                                'subject.$[subjectId].sharedWith.$[programId].programName':
                                                    programName,
                                            },
                                        },
                                        arrayFilters: [
                                            {
                                                'programId._id': convertToMongoId(
                                                    sharedProgram._id,
                                                ),
                                            },
                                            {
                                                'subjectId._id': convertToMongoId(subjectList._id),
                                            },
                                        ],
                                    },
                                });
                        }
                    }
                    bulkUpdate.push({
                        updateMany: {
                            filter: {
                                _program_id: convertToMongoId(id),
                            },
                            update: {
                                $set: {
                                    programName,
                                },
                            },
                        },
                    });
                }
            }
            if (DepartmentSubjectList.sharedWith && DepartmentSubjectList.sharedWith.length > 0) {
                const programSharedCheck = DepartmentSubjectList.sharedWith.filter(function (
                    element,
                ) {
                    return element._program_id.toString() === id.toString();
                });

                for (sharedProgram of programSharedCheck) {
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                _id: convertToMongoId(DepartmentSubjectList._id),
                            },
                            update: {
                                $set: {
                                    'sharedWith.$[programId].programName': programName,
                                },
                            },
                            arrayFilters: [
                                {
                                    'programId._id': convertToMongoId(sharedProgram._id),
                                },
                            ],
                        },
                    });
                }
            }
        }
        await departmentSubjectModel.bulkWrite(bulkUpdate);
        const courseDoc = await courseModel.updateMany(
            {
                'participating._program_id': convertToMongoObjectId(id),
                isDeleted: false,
            },
            { $set: { 'participating.$[dept].programName': programName } },
            {
                arrayFilters: [
                    {
                        'dept._program_id': convertToMongoObjectId(id),
                    },
                ],
            },
        );
        const courseDocFroAdmin = await courseModel.updateMany(
            {
                'administration._program_id': convertToMongoObjectId(id),
                isDeleted: false,
            },
            { $set: { 'administration.programName': programName } },
        );
        // end updating department subject table
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const archiveProgram = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        // TODO : Need to check Schedule Status for Program Archive
        // const coursesScheduled = await CourseSchedule.countDocuments({ _program_id: id }).exec();
        // if (coursesScheduled) {
        //     throw new Error('CANNOT_ARCHIEVE_PROGRAM_COURSES_ARE_SCHEDULED');
        // }
        const updateQuery = { isActive: false };
        const program = await programModel
            .findByIdAndUpdate(id, updateQuery, {
                new: true,
            })
            .lean();
        if (!program) return { statusCode: 500, message: DS_ARCHIVED_FAILED };
        return { statusCode: 200, message: DS_ARCHIVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const restoreArchivedProgram = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const updateQuery = { isActive: true };
        const program = await programModel
            .findByIdAndUpdate(id, updateQuery, {
                new: true,
            })
            .lean();
        if (!program) return { statusCode: 500, message: DS_RESTORE_FAILED };
        return { statusCode: 200, message: DS_RESTORE };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteProgram = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        const programExists = await programModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findByIdAndUpdate(id, { isDeleted: true }, { new: true })
            .lean();

        const result = await departmentSubjectModel.update(
            {
                _program_id: id,
            },
            {
                isDeleted: true,
            },
        );

        const DepartmentSubjects = await departmentSubjectModel.find(
            {
                $or: [
                    { 'subject.sharedWith._program_id': convertToMongoId(id) },
                    { sharedWith: { $elemMatch: { _program_id: convertToMongoId(id) } } },
                ],
            },
            { subject: 1, sharedWith: 1 },
        );
        const bulkUpdate = [];
        for (DepartmentSubjectList of DepartmentSubjects) {
            if (
                DepartmentSubjectList &&
                DepartmentSubjectList.sharedWith &&
                DepartmentSubjectList.sharedWith.length
            ) {
                const filteredSharedToPrograms = DepartmentSubjectList.sharedWith.filter(
                    (elem) => elem._program_id.toString() !== id.toString(),
                );
                bulkUpdate.push({
                    updateOne: {
                        filter: { _id: convertToMongoId(DepartmentSubjectList._id) },
                        update: { $set: { sharedWith: filteredSharedToPrograms } },
                    },
                });
            }
            if (DepartmentSubjectList.subject && DepartmentSubjectList.subject.length > 0) {
                for (subjectList of DepartmentSubjectList.subject) {
                    if (subjectList.sharedWith.length > 0) {
                        const departmentIdCheck = subjectList.sharedWith.filter(function (element) {
                            return element._program_id.toString() === id.toString();
                        });
                        if (departmentIdCheck.length > 0) {
                            for (sharedProgram of departmentIdCheck)
                                bulkUpdate.push({
                                    updateOne: {
                                        filter: {
                                            _id: convertToMongoId(DepartmentSubjectList._id),
                                        },
                                        update: {
                                            $pull: {
                                                'subject.$[i].sharedWith': { _program_id: id },
                                            },
                                        },
                                        arrayFilters: [
                                            {
                                                'i._id': convertToMongoId(subjectList._id),
                                            },
                                        ],
                                    },
                                });
                        }
                    }
                }
            }
        }

        await departmentSubjectModel.bulkWrite(bulkUpdate);

        if (!program || !result) return { statusCode: 500, message: DS_DELETE_FAILED };
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addOrEditProgramGoals = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const { media, content } = body;
        const programExists = await programModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findByIdAndUpdate(
                id,
                {
                    $set: { goals: { mediaURL: media, content } },
                },
                { new: true, select: { goals: 1 } },
            )
            .lean();
        if (!program) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addOrEditProgramObjectives = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const { media, content } = body;
        const programExists = await programModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findByIdAndUpdate(
                id,
                {
                    $set: { objectives: { mediaURL: media, content } },
                },
                { new: true, select: { objectives: 1 } },
            )
            .lean();
        if (!program) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addProgramPortfolio = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { title, description, media } = body;
        const { id } = params;
        let newPortfolio = {
            title,
            description,
        };
        if (media) {
            newPortfolio = { ...newPortfolio, mediaURL: media };
        }
        const programExists = await programModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findByIdAndUpdate(
                id,
                {
                    $push: { portfolio: newPortfolio },
                },
                { new: true, select: { portfolio: 1 } },
            )
            .lean();
        if (!program) {
            return { statusCode: 500, data: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editProgramPortfolio = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const programInputModel = getModel(tenantURL, PROGRAM, programSchema);
        const { title, description, portfolioId, media } = body;
        const { id } = params;
        let portfolio = {
            'portfolio.$.title': title,
            'portfolio.$.description': description,
        };
        if (media) {
            portfolio = { ...portfolio, 'portfolio.$.mediaURL': media };
        }
        if (!media) {
            portfolio = { ...portfolio, 'portfolio.$.mediaURL': null };
        }
        const programExists = await programInputModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programInputModel
            .findOneAndUpdate(
                { _id: id, 'portfolio._id': portfolioId },
                {
                    $set: portfolio,
                },
                {
                    returnDocument: 'after',
                    select: { portfolio: 1 },
                },
            )
            .lean();
        if (!program) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const configureProgram = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { id } = params;
        const stages = [
            {
                $match: { _program_id: id },
            },
            {
                $group: {
                    _id: '$_id',
                    subject: { $first: '$subject' },
                    _program_id: { $first: '$_program_id' },
                },
            },
        ];
        const departments = await departmentSubjectModel.aggregate(stages);
        if (!departments.length) {
            return { statusCode: 400, message: 'THERE_ARE_NO_DEPARTMENTS_UNDER_THIS_PROGRAM' };
        }
        let configuredProgram = await programModel.findByIdAndUpdate(
            id,
            { isConfigured: true },
            { new: true },
        );
        if (!configuredProgram) {
            return { statusCode: 500, message: 'PROGRAM_COULD_NOT_BE_CONFIGURED' };
        }
        configuredProgram = await getPresignedUrlsForPrograms(configuredProgram);
        return {
            statusCode: 200,
            message: 'PROGRAM_CONFIGURED',
            data: { configuredProgram, departments },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addBreaks = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { programId, breaks, session, _institution_id } = body;
        const programExists = await programModel.findById(programId).lean();
        const institutionSettings = await settingsModel.findOne({ _institution_id }).lean();
        let isDuplicateBreakName = false;
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.basicDetails &&
            institutionSettings.globalConfiguration.basicDetails.breaks &&
            institutionSettings.globalConfiguration.basicDetails.breaks.length
        ) {
            institutionSettings.globalConfiguration.basicDetails.breaks.forEach((data) => {
                if (data.name.toLowerCase() === breaks.name.toLowerCase()) {
                    isDuplicateBreakName = true;
                }
            });
        }
        if (isDuplicateBreakName) {
            return { statusCode: 404, message: 'DUPLICATE_BREAK_NAME' };
        }
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.breaks.name': breaks.name,
            })
            .lean();
        if (program) {
            return { statusCode: 410, message: 'BREAK_NAME_ALREADY_EXISTS' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                $push: {
                    'settings.basicDetails.breaks': {
                        name: breaks.name,
                        session,
                        workingDays: breaks.days,
                        isParentConfig: false,
                    },
                },
            },
        );

        if (!programUpdate) {
            return { statusCode: 410, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editBreaks = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { programId, breaks, session, _institution_id } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        const institutionSettings = await settingsModel.findOne({ _institution_id }).lean();
        let isDuplicateBreakName = false;
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.basicDetails &&
            institutionSettings.globalConfiguration.basicDetails.breaks &&
            institutionSettings.globalConfiguration.basicDetails.breaks.length
        ) {
            institutionSettings.globalConfiguration.basicDetails.breaks.forEach((data) => {
                if (data.name.toLowerCase() === breaks.name.toLowerCase()) {
                    isDuplicateBreakName = true;
                }
            });
        }
        if (isDuplicateBreakName) {
            return { statusCode: 404, message: 'DUPLICATE_BREAK_NAME' };
        }
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.breaks._id': id,
                'settings.basicDetails.breaks.isParentConfig': false,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'BREAK_NOT_FOUND' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                'settings.basicDetails.breaks.$[i].name': breaks.name,
                'settings.basicDetails.breaks.$[i].isParentConfig': false,
                ...(session && { 'settings.basicDetails.breaks.$[i].session': session }),
                ...(breaks.days && {
                    'settings.basicDetails.breaks.$[i].workingDays': breaks.days,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!programUpdate) {
            return { statusCode: 410, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeBreaks = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { programId } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.breaks._id': id,
                'settings.basicDetails.breaks.isParentConfig': false,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'BREAK_NOT_FOUND' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                $pull: { 'settings.basicDetails.breaks': { _id: id } },
            },
        );

        if (!programUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addEventType = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { programId, eventType, _institution_id } = body;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const institutionSettings = await settingsModel.findOne({ _institution_id }).lean();
        let isDuplicateEventTypeName = false;
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.basicDetails &&
            institutionSettings.globalConfiguration.basicDetails.eventType &&
            institutionSettings.globalConfiguration.basicDetails.eventType.length
        ) {
            institutionSettings.globalConfiguration.basicDetails.eventType.forEach((data) => {
                if (data.name.toLowerCase() === eventType.name.toLowerCase()) {
                    isDuplicateEventTypeName = true;
                }
            });
        }
        if (isDuplicateEventTypeName) {
            return { statusCode: 404, message: 'DUPLICATE_EVENT_NAME' };
        }
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.eventType.name': eventType.name,
            })
            .lean();
        if (program) {
            return { statusCode: 410, message: 'EVENT_TYPE_NAME_ALREADY_EXISTS' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                $push: {
                    'settings.basicDetails.eventType': {
                        name: eventType.name,
                        isLeave: eventType.isLeave,
                        isParentConfig: false,
                    },
                },
            },
        );

        if (!programUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editEventType = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const { programId, eventType, isRemove, _institution_id } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const institutionSettings = await settingsModel.findOne({ _institution_id }).lean();
        let isDuplicateEventTypeName = false;
        if (
            institutionSettings &&
            institutionSettings.globalConfiguration &&
            institutionSettings.globalConfiguration.basicDetails &&
            institutionSettings.globalConfiguration.basicDetails.eventType &&
            institutionSettings.globalConfiguration.basicDetails.eventType.length
        ) {
            institutionSettings.globalConfiguration.basicDetails.eventType.forEach((data) => {
                if (data.name.toLowerCase() === eventType.name.toLowerCase()) {
                    isDuplicateEventTypeName = true;
                }
            });
        }
        if (isDuplicateEventTypeName) {
            return { statusCode: 404, message: 'DUPLICATE_EVENT_NAME' };
        }
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.eventType._id': id,
                'settings.basicDetails.eventType.isParentConfig': false,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'EVENT_TYPE_NOT_FOUND' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                ...(isRemove && {
                    $pull: {
                        'settings.basicDetails.eventType': {
                            _id: id,
                        },
                    },
                }),
                ...(!isRemove && {
                    'settings.basicDetails.eventType.$[i].name': eventType.name,
                    'settings.basicDetails.eventType.$[i].isLeave': eventType.isLeave,
                    'settings.basicDetails.eventType.$[i].isParentConfig': false,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!programUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeEventType = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { programId } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
                'settings.basicDetails.eventType._id': id,
                'settings.basicDetails.eventType.isParentConfig': false,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'EVENT_TYPE_NOT_FOUND' };
        }

        const programUpdate = await programModel.updateOne(
            { _id: programId },
            {
                $pull: { 'settings.basicDetails.eventType': { _id: id } },
            },
        );

        if (!programUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCurriculum = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { programId } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne(
                {
                    _id: programId,
                    'settings.programInput.curriculumNaming._id': id,
                },
                { settings: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }

        const {
            programInput: { curriculumNaming = [] },
        } = program.settings;

        if (curriculumNaming.length) {
            curriculumNaming.forEach((curriculumNamingEntry) => {
                curriculumNamingEntry.isDefault = false;

                if (isIDEquals(curriculumNamingEntry._id, id)) {
                    curriculumNamingEntry.isDefault = true;
                }
            });
        }

        const programInputUpdate = await programModel.updateOne(
            { _id: programId },
            { 'settings.programInput.curriculumNaming': curriculumNaming },
        );

        if (!programInputUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }

        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCreditHours = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { programId } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne(
                {
                    _id: programId,
                    'settings.programInput.creditHours._id': id,
                },
                { settings: 1, _institution_id: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }

        const {
            programInput: { creditHours = [] },
        } = program.settings;

        if (creditHours.length) {
            creditHours.forEach((creditHoursEntry) => {
                creditHoursEntry.isDefault = false;

                if (isIDEquals(creditHoursEntry._id, id)) {
                    creditHoursEntry.isDefault = true;
                }
            });
        }

        const programInputUpdate = await programModel.updateOne(
            { _id: programId },
            { 'settings.programInput.creditHours': creditHours },
        );

        if (!programInputUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateProgramDurationFormat = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { programId, withoutLevel } = body;
        const { id } = params;
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne(
                {
                    _id: programId,
                    'settings.programInput.programDurationFormat._id': id,
                },
                { settings: 1, _institution_id: 1 },
            )
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }

        const {
            programInput: { programDurationFormat = [] },
        } = program.settings;

        if (programDurationFormat.length) {
            programDurationFormat.forEach((programDurationFormatEntry) => {
                programDurationFormatEntry.isDefault = false;

                if (isIDEquals(programDurationFormatEntry._id, id)) {
                    programDurationFormatEntry.isDefault = true;
                    programDurationFormatEntry.withoutLevel = withoutLevel;
                }
            });
        }

        const programInputUpdate = await programModel.updateOne(
            { _id: programId },
            { 'settings.programInput.programDurationFormat': programDurationFormat },
        );

        if (!programInputUpdate) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteParentInstituteBreak = async ({ body = {}, headers = {} }) => {
    const { programId } = body;
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }

        const removeBreak = await programModel.updateOne(
            {
                _id: programId,
                'settings.basicDetails.breaks.isParentConfig': true,
            },
            {
                $pull: { 'settings.basicDetails.breaks': { _id: id } },
            },
        );
        if (!removeBreak) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteParentInstituteEvent = async ({ body = {}, headers = {} }) => {
    const { programId } = body;
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const programExists = await programModel.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel
            .findOne({
                _id: programId,
            })
            .lean();
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }
        const eventRemove = await programModel.updateOne(
            {
                _id: programId,
                'settings.basicDetails.eventType.isParentConfig': true,
            },
            {
                $pull: { 'settings.basicDetails.eventType': { _id: id } },
            },
        );
        if (!eventRemove) {
            return { statusCode: 500, message: DS_SAVE_FAILED };
        }
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgram = async ({ params = {}, headers = {} }) => {
    const { id } = params;
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypesSchema,
        );
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const programExists = await programModel.findById(id).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const program = await programModel.findOne(
            { _id: id, isDeleted: false },
            {
                programType: 1,
                _program_type_id: 1,
                name: 1,
                code: 1,
                level: 1,
                type: 1,
                degree: 1,
                terms: 1,
                noOfTerms: 1,
                goals: 1,
                objectives: 1,
                portfolio: 1,
                isActive: 1,
                _institution_id: 1,
            },
        );
        if (!program) {
            return { statusCode: 410, message: 'PROGRAM_NOT_FOUND' };
        }
        //todo: framework logic is yet to be completed
        let populatedProgram = await populateSingleProgram(
            [program],
            curriculumModel,
            departmentSubjectModel,
            sessionDeliveryModel,
            courseModel,
        );
        populatedProgram = await programInputFormat.program(
            populatedProgram,
            program._institution_id,
        );
        return { statusCode: 200, data: { program: populatedProgram } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramSettings = async ({ params = {}, query = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { id } = params;
        const { programId } = query;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypesSchema,
        );
        const settingsModel = getModel(tenantURL, SETTINGS, settingsSchema);
        const programSettings = await programModel
            .findOne({ _id: programId }, { settings: 1 })
            .lean();
        const sessionDeliveryType = await sessionDeliveryModel
            .find({
                _program_id: convertToMongoObjectId(programId),
                isDeleted: false,
            })
            .lean();
        if (sessionDeliveryType && sessionDeliveryType.length) {
            programSettings.settings.programInput.isCreditHourEditable = false;
        } else {
            programSettings.settings.programInput.isCreditHourEditable = true;
        }
        const institutionSettings = await settingsModel.findOne({ _institution_id: id }).lean();
        if (!programSettings || !institutionSettings) {
            return { statusCode: 400, message: 'FAILED_TO_GET_SETTINGS' };
        }
        return {
            statusCode: 200,
            data: {
                programSettings: transformProgramSpecificBasicDetails(programSettings),
                institutionSettings: formatGlobalConfigurationForProgram(institutionSettings),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteProgramGoals = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { _institution_id, programId } = body;
        const updatedProgram = await programModel
            .findOneAndUpdate(
                {
                    _id: programId,
                    _institution_id,
                },
                {
                    $set: { goals: {} },
                },
                { new: true },
            )
            .lean();
        if (!updatedProgram) return { statusCode: 500, message: DS_SAVE_FAILED };
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteProgramObjectives = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { _institution_id, programId } = body;
        const updatedProgram = await programModel
            .findOneAndUpdate(
                {
                    _id: programId,
                    _institution_id,
                },
                {
                    $set: { objectives: {} },
                },
            )
            .lean();
        if (!updatedProgram) return { statusCode: 500, message: DS_SAVE_FAILED };
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteProgramPortfolio = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { _institution_id, programId, portfolioId } = body;
        const updatedProgram = await programModel
            .findOneAndUpdate(
                {
                    _id: programId,
                    _institution_id,
                },
                {
                    $pull: { portfolio: { _id: portfolioId } },
                },
            )
            .lean();
        if (!updatedProgram) return { statusCode: 500, message: DS_SAVE_FAILED };
        return { statusCode: 200, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateTimeZone = async ({ body = {} }) => {
    try {
        const { programId, timeZone } = body;
        const programExists = await ProgramInput.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };

        const programUpdate = await ProgramInput.updateOne(
            { _id: programId },
            {
                $set: { 'settings.basicDetails.timeZone': timeZone },
            },
        );

        if (!programUpdate) {
            return { statusCode: 410, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateGenderSegregation = async ({ body = {} }) => {
    try {
        const { programId, isGenderSegregation } = body;
        const programExists = await ProgramInput.findById(programId).lean();
        if (!programExists) return { statusCode: 400, message: 'PROGRAM_DOES_NOT_EXISTS' };
        const programUpdate = await ProgramInput.updateOne(
            { _id: programId },
            {
                $set: { 'settings.basicDetails.isGenderSegregation': isGenderSegregation },
            },
        );

        if (!programUpdate) {
            return { statusCode: 410, message: DS_SAVE_FAILED };
        }
        return { statusCode: 201, message: DS_SAVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    addProgram,
    getInstitutePrograms,
    editProgram,
    archieveProgram: archiveProgram,
    deleteProgram,
    configureProgram,
    addOrEditProgramGoals,
    addOrEditProgramObjectives,
    addProgramPortfolio,
    editProgramPortfolio,
    addBreaks,
    removeBreaks,
    editBreaks,
    addEventType,
    editEventType,
    removeEventType,
    updateCurriculum,
    updateCreditHours,
    updateProgramDurationFormat,
    deleteParentInstituteBreak,
    deleteParentInstituteEvent,
    restoreArchivedProgram,
    getProgram,
    getProgramSettings,
    deleteProgramObjectives,
    deleteProgramGoals,
    deleteProgramPortfolio,
    updateTimeZone,
    updateGenderSegregation,
};
