const Joi = require('joi');
const constant = require('../../utility/constants');
exports.addHierarchyValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                mode: Joi.string()
                    .required()
                    .valid(constant.DS_CREATE, constant.DS_UPDATE)
                    .error((error) => error),
                _id: Joi.string().alphanum().length(24).when('mode', {
                    is: constant.DS_UPDATE,
                    then: Joi.required(),
                    otherwise: Joi.optional(),
                }),
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => 'INSTITUTE_ID_REQUIRED'),
                _department_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'DEPARTMENT_NAME_REQUIRED';
                    }),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                type: Joi.string()
                    .valid(constant.COLLEGE, constant.PROGRAM, constant.DEPARTMENT)
                    .error((error) => {
                        return error;
                    })
                    .when('mode', {
                        is: constant.DS_CREATE,
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                hierarchy: Joi.array()
                    .items(
                        Joi.object().keys({
                            name: Joi.string()
                                .required()
                                .error(() => 'HIERARCHY_NAME_REQUIRED'),
                            isActive: Joi.boolean()
                                .required()
                                .error((error) => error),
                            stage: Joi.number()
                                .required()
                                .error((error) => error),
                            _id: Joi.string().alphanum().length(24).when('mode', {
                                is: constant.DS_UPDATE,
                                then: Joi.required(),
                                otherwise: Joi.optional(),
                            }),
                        }),
                    )
                    .required()
                    .error(() => 'HIERARCHY_NAME_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);
exports.getHierarchyValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => 'INSTITUTE_ID_REQUIRED'),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
                _department_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'DEPARTMENT_ID_REQUIRED';
                    }),
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'ID_REQUIRED';
                    }),
                type: Joi.string()
                    .valid(constant.COLLEGE, constant.PROGRAM, constant.DEPARTMENT)
                    .optional()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
exports.deleteHierarchyValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            _hierarchy_id: Joi.string()
                .alphanum()
                .length(24)
                .optional()
                .error(() => {
                    return 'HIERARCHY_ID_REQUIRED';
                }),
            _label_id: Joi.string()
                .alphanum()
                .length(24)
                .optional()
                .error(() => {
                    return 'LABEL_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                isActive: Joi.boolean()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
