const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const { getDashboardSchema, getDashboardModuleSchema } = require('./dashboard_validate_schema');
const {
    getDashboard,
    getDocument,
    getCourses,
    getSessions,
    getRating,
    getBleRange,
    updateBleRange,
    getManualStaffDetail,
} = require('./dashboard_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

// get dashboard
route.get(
    '/',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getDashboardSchema,
    getDashboard,
);
route.get(
    '/getDocument',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getDashboardModuleSchema,
    getDocument,
);
route.get(
    '/getCourses',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getDashboardModuleSchema,
    getCourses,
);
route.get(
    '/getSessions',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getDashboardModuleSchema,
    getSessions,
);
route.get(
    '/getRating',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            queryName: 'type',
        }),
    ],
    getDashboardModuleSchema,
    getRating,
);
route.get(
    '/getBleRange',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getBleRange,
);
route.put('/updateBleRange', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], updateBleRange);
route.get(
    '/getManualStaffDetail',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getManualStaffDetail,
);
module.exports = route;
