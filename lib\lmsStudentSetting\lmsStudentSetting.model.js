const mongoose = require('mongoose');
const {
    LMS_STUDENT_SETTING,
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    DAYS_MODE: { YEAR, MONTH },
    ROLE,
    USER,
    DIGI_PROGRAM,
    CA<PERSON>NDAR: { PROGRAM },
    CUSTOM,
    ALL,
    ANY,
    BY_USER,
    ROLE_LIST,
    AUTOMATIC,
    MANUAL,
    CUMULA<PERSON>VE,
    INDIVIDUAL,
    USER_BASED,
    ROLE_BASED,
    ALL_USERS,
    ANY_ONE_IN_EACH_ROLE,
    ANY_ONE_USER,
    ANY_ONE_IN_ANY_ROLE,
    TILL_THE_END_OF_LEVEL,
    START_OF_THE_NEXT_LEVEL,
    END_OF_THE_COURSES,
    START_OF_THE_NEXT_ACADEMIC_YEAR,
    END_OF_THE_ACADEMIC_YEAR,
    WARNING_CONFIG_TYPE,
    WARNING_CONFIG_BASED,
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
    ABSENT,
    PRESENT,
    EXCLUDE,
} = require('../utility/constants');
const { MALE, FEMALE, BOTH } = require('../utility/enums');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;

const lmsSettingSchemas = new Schema(
    {
        _institution_id: { type: ObjectId },
        classificationType: { type: String, enum: [ONDUTY, PERMISSION, LEAVE] },
        generalConfig: {
            parentApplyPermission: { type: Boolean, default: false },
            parentAcknowledgePermission: { type: Boolean, default: false },
        },
        catagories: [
            {
                categoryName: { type: String },
                isActive: {
                    type: Boolean,
                    default: true,
                },
                isDefault: {
                    type: Boolean,
                    default: false,
                },
                percentage: { type: Number },
                types: [
                    {
                        typeName: { type: String },
                        typeDescription: { type: String },
                        typeColor: { type: String },
                        noHours: { type: Number },
                        percentage: { type: Number },
                        isReasonFromApplicant: { type: Boolean, default: false },
                        isProofToBeAttached: { type: Boolean, default: false },
                        frequency: {
                            setFrequency: { type: Number },
                            frequencyBy: { type: String, enum: [YEAR, MONTH] },
                            frequencyByMonth: { type: Number },
                        },
                        attachmentMandatory: { type: Boolean, default: true },
                        attendanceStatus: { type: Boolean, default: false },
                        allowedDuringExam: { type: Boolean, default: false },
                        excludePresent: { type: Boolean, default: false },
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                        attendanceType: { type: String, enum: [ABSENT, PRESENT, EXCLUDE] },
                    },
                ],
            },
        ],
        termsAndCondition: { type: String },
        leaveCalculation: { type: String },
        leaveApplicationCriteria: { type: String, enum: [SESSION, DAY] },
        levelApprover: [
            {
                programIds: [{ type: ObjectId, ref: DIGI_PROGRAM }],
                genderSegregation: { type: Boolean, default: false },
                isExceptionalProgram: { type: Boolean },
                level: [
                    {
                        levelName: { type: String },
                        roleIds: [{ type: ObjectId, ref: ROLE }],
                        approvalConfig: {
                            type: String,
                            enum: [
                                ALL_USERS,
                                ANY_ONE_IN_EACH_ROLE,
                                ANY_ONE_USER,
                                ANY_ONE_IN_ANY_ROLE,
                            ],
                        },
                        categoryBased: { type: String, enum: [USER_BASED, ROLE_BASED] },
                        userIds: [{ type: ObjectId, ref: USER }],
                        turnAroundTime: { type: Number },
                        escalateRequest: { type: Boolean, default: false },
                        skipPreviousLevelApproval: { type: Boolean, default: false },
                        gender: { type: String, enum: [MALE, FEMALE, BOTH] },
                        overwritePreviousLevelApproval: { type: Boolean, default: false },
                    },
                ],
            },
        ],
        warningConfig: [
            {
                typeName: { type: Number },
                unappliedLeaveConsideredAs: { type: String },
                labelName: { type: String },
                percentage: { type: Number },
                colorCode: { type: String },
                message: { type: String },
                denialManagement: {
                    accessType: { type: String },
                    roleIds: [{ type: ObjectId, ref: ROLE }],
                    userIds: [{ type: ObjectId, ref: USER }],
                    turnAroundTime: {
                        type: String,
                        enum: [
                            TILL_THE_END_OF_LEVEL,
                            START_OF_THE_NEXT_LEVEL,
                            END_OF_THE_COURSES,
                            START_OF_THE_NEXT_ACADEMIC_YEAR,
                            END_OF_THE_ACADEMIC_YEAR,
                        ],
                    },
                },
                denialCondition: { type: String, enum: [CUMULATIVE, INDIVIDUAL] },
                categoryWisePercentage: [
                    {
                        categoryId: ObjectId,
                        categoryName: String,
                        percentage: Number,
                    },
                ],
                notificationToParent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                },
                isAdditionStaffNotify: { type: Boolean },
                notificationRoleIds: [{ type: ObjectId, ref: ROLE }],
                notificationToStudent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                    sendNotificationAuthority: [{ type: ObjectId, ref: ROLE }],
                },
                notificationToStaff: { type: Boolean, default: false },
                restrictCourseAccess: { type: Boolean, default: false },
                isActive: { type: Boolean, default: true },
                //below key is phase2
                meetingWithStudent: { type: Boolean, default: false },
                meetingRoleIds: [{ type: ObjectId, ref: ROLE }],
                acknowledgeToStudent: { type: Boolean, default: false },
                markItMandatory: { type: Boolean, default: false },
                adminRestrictedAttendance: { type: Boolean, default: false },
                scheduleStaffRestrictedAttendance: { type: Boolean, default: false },
                turnAroundTime: { type: Number },
                escalationLevels: [
                    {
                        levelName: { type: String },
                        escalatingRoleIds: [{ type: ObjectId, ref: ROLE }],
                        turnAroundTime: { type: Number },
                    },
                ],
            },
        ],
        warningMode: {
            type: String,
            enum: [WARNING_CONFIG_TYPE.COURSE_BASED, WARNING_CONFIG_TYPE.COMPREHENSIVE],
            default: WARNING_CONFIG_TYPE.COURSE_BASED,
        },
        warningConfigBased: {
            type: String,
            enum: [WARNING_CONFIG_BASED.HOUR, WARNING_CONFIG_BASED.SESSION],
        },
        comprehensiveWarningConfig: [
            {
                typeName: { type: Number },
                unappliedLeaveConsideredAs: { type: String },
                labelName: { type: String },
                warningValue: { type: Number },
                colorCode: { type: String },
                message: { type: String },
                denialManagement: {
                    accessType: { type: String },
                    roleIds: [{ type: ObjectId, ref: ROLE }],
                    userIds: [{ type: ObjectId, ref: USER }],
                    turnAroundTime: {
                        type: String,
                        enum: [
                            TILL_THE_END_OF_LEVEL,
                            START_OF_THE_NEXT_LEVEL,
                            END_OF_THE_COURSES,
                            START_OF_THE_NEXT_ACADEMIC_YEAR,
                            END_OF_THE_ACADEMIC_YEAR,
                        ],
                    },
                },
                denialCondition: { type: String, enum: [CUMULATIVE, INDIVIDUAL] },
                categoryWisePercentage: [
                    {
                        categoryId: ObjectId,
                        categoryName: String,
                        warningValue: Number,
                    },
                ],
                notificationToParent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                },
                isAdditionStaffNotify: { type: Boolean },
                notificationRoleIds: [{ type: ObjectId, ref: ROLE }],
                notificationToStudent: {
                    isActive: Boolean,
                    setType: { type: String, enum: [AUTOMATIC, MANUAL] },
                    sendNotificationAuthority: [{ type: ObjectId, ref: ROLE }],
                },
                notificationToStaff: { type: Boolean, default: false },
                restrictCourseAccess: { type: Boolean, default: false },
                isActive: { type: Boolean, default: true },
                acknowledgeToStudent: { type: Boolean, default: false },
                markItMandatory: { type: Boolean, default: false },
                adminRestrictedAttendance: { type: Boolean, default: false },
                scheduleStaffRestrictedAttendance: { type: Boolean, default: false },
                //below key is phase2
                // meetingWithStudent: { type: Boolean, default: false },
                // meetingRoleIds: [{ type: ObjectId, ref: ROLE }],
                // turnAroundTime: { type: Number },
                // escalationLevels: [
                //     {
                //         levelName: { type: String },
                //         escalatingRoleIds: [{ type: ObjectId, ref: ROLE }],
                //         turnAroundTime: { type: Number },
                //     },
                // ],
            },
        ],
        leavePolicy: [
            {
                documentTitle: { type: String },
                description: { type: String },
                attachmentDocument: [
                    {
                        url: String,
                        signedUrl: String,
                        sizeInKb: Number,
                        name: String,
                    },
                ],
                isDocumentValidity: { type: Boolean },
                documentValidity: {
                    startDate: { type: Date },
                    endDate: { type: Date },
                },
                noExpiryDate: { type: Boolean },
                allowStudentVisibilityStatus: { type: Boolean },
                updatedAt: { type: Date },
            },
        ],
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(LMS_STUDENT_SETTING, lmsSettingSchemas);
