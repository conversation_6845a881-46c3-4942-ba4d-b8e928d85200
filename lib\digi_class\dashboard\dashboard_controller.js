// constants
const {
    sendResponse,
    convertToMongoObjectId,
    sendResponseWithRequest,
} = require('../../utility/common');
const {
    DC_STAFF,
    DS_DATA_RETRIEVED,
    DC_STUDENT,
    PENDING,
    ONGOING,
} = require('../../utility/constants');
const { getInstitutionCalendar } = require('../../../service/cache.service');
const { logger } = require('../../utility/util_keys');
const institutionSchema = require('../../models/institution');
const {
    getDashboardDocumentList,
    getDashboardCoursesList,
    getDashboardSessionsList,
    getDashboardRating,
    getDashboardDocumentListOptimized,
} = require('./dashboard.service');
const { getStaffData, getStudentData } = require('./dashboard_service');
const { getUserCourseLists } = require('../course_session/course_session_service');
const courseScheduleSchema = require('../../models/course_schedule');
const roleSchema = require('../../models/role');
const roleAssignSchema = require('../../models/role_assign');
exports.getDashboard = async (req, res) => {
    const { type, userId, moduleType, courseAdmin } = req.query;
    const { _institution_id } = req.headers;

    try {
        logger.info(
            'dashboardController -> getDashboard -> %s Module : %s start',
            userId,
            moduleType,
        );
        const data =
            type === DC_STAFF
                ? await getStaffData(userId, moduleType, courseAdmin, _institution_id)
                : await getStudentData(userId, moduleType, _institution_id);
        logger.info(
            'dashboardController -> getDashboard -> %s Module : %s end',
            userId,
            moduleType,
        );
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), data);
    } catch (error) {
        logger.error(
            'dashboardController -> getDashboard -> %s Module : %s error : %o',
            req.query.userId,
            req.query.moduleType,
            { error },
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getDocument = async (req, res) => {
    try {
        const { type, userId, courseAdmin } = req.query;
        const { _institution_calendar_id: institutionCalendarId } = req.headers;

        // Todo Disabling this do to server overLoad
        // logger.info('dashboardController -> getDocument -> %s start', userId);
        // const documentDatas = await getDashboardDocumentList(
        //     userId,
        //     type,
        //     institutionCalendarId || (await getInstitutionCalendar())._id,
        //     courseAdmin,
        // );
        // return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { documents: documentDatas });

        logger.info('dashboardController -> getDocument -> %s start', userId);
        const documentDatas = await getDashboardDocumentListOptimized(
            userId,
            type,
            institutionCalendarId || (await getInstitutionCalendar())._id,
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { documents: documentDatas });
    } catch (error) {
        logger.error('dashboardController -> getDocument -> %s error : %o', req.query.userId, {
            error,
        });
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getCourses = async (req, res) => {
    try {
        const { type, userId } = req.query;
        const { _institution_calendar_id: institutionCalendarId } = req.headers;

        // Todo Disabling this do to server overLoad
        // logger.info('dashboardController -> getCourses -> %s Course Module %s start', userId);
        // const courseDatas = await getDashboardCoursesList(userId, type, institutionCalendarId);
        // return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { courses: courseDatas });

        // Course Based on user from Redis & Alternate of DB
        logger.info(
            { userId, type, institutionCalendarId },
            'courseSessionController -> userCourses -> Course List start',
        );
        const usersCourseList = await getUserCourseLists({ userId, type, institutionCalendarId });
        logger.info(
            { userId, type, institutionCalendarId },
            'courseSessionController -> userCourses -> Course List End',
        );
        return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), {
            courses: usersCourseList.map((courseElement) => {
                return {
                    _course_id: courseElement._id,
                    _id: courseElement._id,
                    _institution_calendar_id: courseElement._institution_calendar_id,
                    _program_id: courseElement._program_id,
                    course_name: courseElement.course_name,
                    course_number: courseElement.course_code,
                    course_type: courseElement.course_type,
                    start_date: courseElement.start_date,
                    end_date: courseElement.end_date,
                    program_name: courseElement.program_name,
                    rotation: courseElement.rotation,
                    rotation_count: courseElement.rotation_count,
                    term: courseElement.term,
                    year: courseElement.year,
                    level: courseElement.level,
                    course_code_labels: null,
                    course_name_labels: null,
                };
            }),
        });
        // const { _institution_id } = req.headers;
        // logger.info('dashboardController -> getCourses -> %s Course Module %s start', userId);
        // const courseDatas = await getDashboardCoursesList(userId, type, _institution_id);
        // return sendResponse(res, 200, true, req.t(DS_DATA_RETRIEVED), { courses: courseDatas });
    } catch (error) {
        logger.error(
            'dashboardController -> getCourses -> %s Course Module error : %o',
            req.query.userId,
            { error },
        );
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.getSessions = async (req, res) => {
    try {
        const { type, userId, dateTime, timeZone } = req.query;
        const { _institution_id } = req.headers;
        logger.info('dashboardController -> getSessions -> %s Course Module %s start', userId);
        const sessionsDatas = await getDashboardSessionsList(
            userId,
            type,
            _institution_id,
            dateTime || new Date(),
            timeZone,
        );
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            sessions: sessionsDatas,
        });
    } catch (error) {
        logger.error(
            'dashboardController -> getSessions -> %s Course Module error : %o',
            req.query.userId,
            { error },
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.getRating = async (req, res) => {
    try {
        const { userId } = req.query;
        /*   const { _institution_calendar_id: institutionCalendarId } = req.headers; */
        const { _institution_id } = req.headers;
        logger.info('dashboardController -> getRating -> %s start', userId);
        const ratingDatas = await getDashboardRating(userId, _institution_id);
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), ratingDatas);
    } catch (error) {
        logger.error('dashboardController -> getRating -> %s Course error : %o', req.query.userId, {
            error,
        });
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.getBleRange = async (req, res) => {
    try {
        const id = req.headers._institution_id;
        const bleRangeData = await institutionSchema.findOne(
            {
                _id: convertToMongoObjectId(id),
            },
            { bleStaff: 1, bleStudent: 1 },
        );
        if (!bleRangeData) {
            return sendResponse(res, 200, false, 'NO_DATA');
        }
        return sendResponse(res, 200, true, 'LIST_DATA', bleRangeData);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.updateBleRange = async (req, res) => {
    try {
        const id = req.headers._institution_id;
        const bleRange = req.body;
        const bleRangeData = await institutionSchema.updateOne(
            {
                _id: convertToMongoObjectId(id),
            },
            {
                $set: bleRange,
            },
        );
        if (!bleRangeData) {
            return sendResponse(res, 200, false, 'UNABLE_TO_UPDATE');
        }
        return sendResponse(res, 200, true, 'UPDATE_DATA');
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getManualStaffDetail = async (req, res) => {
    try {
        const { userId } = req.query;
        const { _institution_id } = req.headers;
        const courseScheduleData = await courseScheduleSchema.countDocuments({
            status: [PENDING, ONGOING],
            isDeleted: false,
            // $and: [
            //     {
            //         scheduleStartDateAndTime: { $gte: new Date() },
            //     },
            //     {
            //         scheduleEndDateAndTime: { $gt: new Date() },
            //     },
            // ],
            'attendanceTakingStaff.staffId': convertToMongoObjectId(userId),
        });
        const primaryStaffData = await courseScheduleSchema.countDocuments({
            status: [PENDING, ONGOING],
            isDeleted: false,
            'staffs._staff_id': convertToMongoObjectId(userId),
            // $and: [
            //     {
            //         scheduleStartDateAndTime: { $gte: new Date() },
            //     },
            //     {
            //         scheduleEndDateAndTime: { $gt: new Date() },
            //     },
            // ],
        });
        const manualStaff = !!courseScheduleData;
        const scheduleStaff = !!courseScheduleData;
        const primaryStaff = !!primaryStaffData;
        // let manualRole = false;
        // const subAttendanceStaff = await roleSchema
        //     .find(
        //         {
        //             _institution_id: convertToMongoObjectId(_institution_id),
        //             isDeleted: false,
        //             'modules.name': 'Secondary Attendance',
        //             'modules.pages': {
        //                 $elemMatch: {
        //                     name: 'Attendance',
        //                     'actions.name': 'View',
        //                 },
        //             },
        //         },
        //         {
        //             _id: 1,
        //         },
        //     )
        //     .lean();
        // if (subAttendanceStaff.length) {
        //     const listSubAttendanceStaff = await roleAssignSchema.countDocuments({
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _user_id: convertToMongoObjectId(userId),
        //         'roles._role_id': {
        //             $in: subAttendanceStaff.map((subStaffElement) =>
        //                 convertToMongoObjectId(subStaffElement._id),
        //             ),
        //         },
        //     });
        //     manualRole = !!listSubAttendanceStaff;
        // }
        sendResponseWithRequest(req, res, 200, true, req.t('STAFF_SCHEDULED'), {
            manualStaff,
            scheduleStaff,
            primaryStaff,
            // manualRole,
        });
    } catch (error) {
        logger.error('dashboardController -> getManualStaffDetails ->', req.query.userId, {
            error,
        });
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
