const elasticClient = require('../../../config/elasticSearch');

const getRecords = async ({ indexName, query, _source }) => {
    try {
        const searchData = await elasticClient.search({
            index: indexName,
            body: { query },
            _source,
        });
        return searchData;
    } catch (error) {
        if (error.meta.statusCode === 404) return [];
        throw new Error(error);
    }
};

const addRecords = ({ indexName, document }) => {
    try {
        const searchData = elasticClient.index({
            index: indexName,
            body: document,
        });
        return searchData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const bulkWrite = ({ indexName, dataset }) => {
    try {
        const operations = dataset.flatMap((doc) => [{ index: { _index: indexName } }, doc]);
        const bulkResponse = elasticClient.bulk({ refresh: true, body: operations });
        return bulkResponse;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const updateRecords = async ({ indexName, query, script }) => {
    try {
        const searchData = await elasticClient.updateByQuery({
            index: indexName,
            body: { query, script },
            refresh: true,
        });
        return searchData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const removeRecords = async ({ indexName, query }) => {
    try {
        const searchData = await elasticClient.deleteByQuery({
            index: indexName,
            body: { query },
        });
        return searchData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

module.exports = { getRecords, addRecords, updateRecords, removeRecords, bulkWrite };
