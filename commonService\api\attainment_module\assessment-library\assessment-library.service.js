const { convertToMongoObjectId, query } = require('../../../utility/common');
const {
    LOCAL,
    DIGI_SESSION_ORDER,
    DIGI_COURSE,
    DIGI_CURRICULUM,
    PROGRAM_CALENDAR,
    STUDENT_GROUP,
} = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const {
    courseSLOFormatting,
    courseCLOFormatting,
    coursePLOFormatting,
    courseStudentFormatting,
    programLevelsStudentFormatting,
} = require('../../serviceAdapter/adapter.formatter');
const courseSessionOrderSchema = require('mongoose').model(DIGI_SESSION_ORDER);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const curriculumSchema = require('mongoose').model(DIGI_CURRICULUM);
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const studentGroupSchema = require('mongoose').model(STUDENT_GROUP);

const getCourseSLO = async ({ _institution_id, programId, courseId }) => {
    try {
        let courseSLO;
        if (BASIC_DATA_FROM === LOCAL) {
            courseSLO = await courseSessionOrderSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                        _course_id: convertToMongoObjectId(courseId),
                    },
                    {
                        'session_flow_data.slo': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!courseSLO) return [];
        return courseSLOFormatting({ courseSLO, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseCLO = async ({ _institution_id, courseId }) => {
    try {
        let courseCLO;
        if (BASIC_DATA_FROM === LOCAL) {
            courseCLO = await courseSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(courseId),
                    },
                    {
                        'framework.domains.clo._id': 1,
                        'framework.domains.clo.no': 1,
                        'framework.domains.clo.name': 1,
                        'framework.domains.clo.isActive': 1,
                        'framework.domains.clo.isDeleted': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!courseCLO) return [];
        return courseCLOFormatting({ courseCLO, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramPLO = async ({ _institution_id, programId, institutionCalendarId }) => {
    try {
        let programCurriculum;
        let yearLevelList;
        if (BASIC_DATA_FROM === LOCAL) {
            programCurriculum = await curriculumSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _program_id: convertToMongoObjectId(programId),
                    },
                    {
                        curriculum_name: 1,
                        'framework.domains.plo._id': 1,
                        'framework.domains.plo.no': 1,
                        'framework.domains.plo.name': 1,
                        'framework.domains.plo.isActive': 1,
                        'framework.domains.plo.isDeleted': 1,
                    },
                )
                .lean();
            yearLevelList = await programCalendarSchema
                .findOne(
                    {
                        ...query,
                        _program_id: convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    },
                    {
                        'level.term': 1,
                        // 'level.year': 1,
                        'level.level_no': 1,
                        'level.curriculum': 1,
                        // 'level.rotation': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!programCurriculum || !yearLevelList) return [];
        return coursePLOFormatting({ programCurriculum, yearLevelList, dataFrom: BASIC_DATA_FROM });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseStudents = async ({
    _institution_id,
    programId,
    courseId,
    institutionCalendarId,
    term,
    level,
    rotationCount,
}) => {
    try {
        let studentGroup;
        if (BASIC_DATA_FROM === LOCAL) {
            studentGroup = await studentGroupSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        'master._program_id': convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        'groups.level': level,
                        'groups.term': term,
                        'groups.courses._course_id': convertToMongoObjectId(courseId),
                    },
                    {
                        'groups.level': 1,
                        'groups.term': 1,
                        'groups.courses._course_id': 1,
                        'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                        'groups.students._student_id': 1,
                        'groups.students.academic_no': 1,
                        'groups.students.name': 1,
                        'groups.students.gender': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!studentGroup) return [];
        return courseStudentFormatting({
            studentGroup,
            courseId,
            term,
            level,
            rotationCount,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramLevelStudents = async ({
    _institution_id,
    programId,
    institutionCalendarId,
    term,
    level,
}) => {
    try {
        let studentGroup;
        if (BASIC_DATA_FROM === LOCAL) {
            studentGroup = await studentGroupSchema
                .findOne(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        'master._program_id': convertToMongoObjectId(programId),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        'groups.level': { $in: level },
                        'groups.term': term,
                    },
                    {
                        'groups.level': 1,
                        'groups.term': 1,
                        // 'groups.courses._course_id': 1,
                        // 'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                        'groups.students._student_id': 1,
                        'groups.students.academic_no': 1,
                        'groups.students.name': 1,
                        'groups.students.gender': 1,
                    },
                )
                .lean();
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
        }
        if (!studentGroup) return [];
        // return studentGroup;
        return programLevelsStudentFormatting({
            studentGroup,
            term,
            level,
            dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getCourseSLO,
    getCourseCLO,
    getProgramPLO,
    getCourseStudents,
    getProgramLevelStudents,
};
