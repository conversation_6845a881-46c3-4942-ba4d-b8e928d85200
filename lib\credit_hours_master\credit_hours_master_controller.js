let constant = require('../utility/constants');
var credit_hours_master = require('mongoose').model(constant.CREDIT_HOURS_MASTER);
var program = require('mongoose').model(constant.PROGRAM);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const credit_hours_master_formate = require('./credit_hours_master_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(credit_hours_master, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "credit_hours_master list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_master_formate.credit_hours_master(doc.data));
        // common_files.list_all_response(res, 200, true, "credit_hours_master list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_master_formate.credit_hours_master(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
    ];
    let doc = await base_control.get_aggregate(credit_hours_master, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "credit_hours_master details", /* doc.data */credit_hours_master_formate.credit_hours_master_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "credit_hours_master details", doc.data/* credit_hours_master_formate.credit_hours_master_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let docs = { status: true };
    let program_ids = [];
    let status, datas, credit_check = false;
    let doc_update = { status: true };
    await req.body.data.forEach(element => {
        if (program_ids.indexOf(element._program_id) == -1) {
            program_ids.push(element._program_id);
        }
    });
    let checks = await base_control.check_id(program, { _id: { $in: program_ids }, 'isDeleted': false });
    if (checks.status) {

        //Check is in this program has Master Credit hours Or not
        // let check_master = await base_control.get_aggregate(program, { _program_id: { $in: program_ids }, 'isDeleted': false });
        // console.log(check_master);

        let aggre = [
            { $match: { '_id': ObjectId(checks.data[0]._id) } },
            { $match: { 'isDeleted': false } },
            { $project: { total_credit: 1 } }
        ];
        let total_credits_doc = await base_control.get_aggregate(program, aggre);
        // console.log(total_credits_doc);
        //await total_credits.data.forEach(async (total_credits_doc, index) => {
        let tpc_total_credits = 0;
        await req.body.data.forEach(async (user_doc, index) => {
            tpc_total_credits += user_doc.credit_hours;
        });
        // console.log(tpc_total_credits);
        if (total_credits_doc.data[0].total_credit == tpc_total_credits) {
            credit_check = true;
        } else {
            credit_check = false;
        }
        //});
        // console.log(total_credits_doc.data[0].total_credit, tpc_total_credits);
        // console.log(credit_check);
        if (credit_check) {
            await req.body.data.forEach(async (doc, index) => {
                let objects = {
                    year: doc.year,
                    level: doc.level,
                    course_no: doc.course_no,
                    credit_hours: doc.credit_hours,
                    _program_id: doc._program_id
                };
                if (doc.id == '' && doc.id.length == 0) {
                    docs = await base_control.insert(credit_hours_master, objects);
                    if (docs.status && doc_update.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                } else {
                    docs = await base_control.update(credit_hours_master, doc.id, objects);
                    if (docs.status) {
                        status = true;
                        datas = docs;
                    } else {
                        datas = docs;
                        status = false;
                    }
                }
                if (req.body.data.length == index + 1) {
                    if (status) {
                        common_files.com_response(res, 201, true, "credit_hours_master Added successfully", docs);
                    } else {
                        common_files.com_response(res, 500, false, "Error", docs.data);
                    }
                }
            });
        } else {
            common_files.com_response(res, 500, false, "Error Credit Hours Not match", 'Check Parshing Total Credit Hours Not match with Program credit hours');
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        //console.log(req.body);
        let doc = await base_control.update(credit_hours_master, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "credit_hours_master update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(credit_hours_master, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "credit_hours_master deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(credit_hours_master, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "credit_hours_master List", credit_hours_master_formate.credit_hours_master_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program = async (req, res) => {
    let id = ObjectId(req.params.id);
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': id } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: '$program' },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(credit_hours_master, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "credit_hours_master list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_master_formate.credit_hours_master(doc.data));
        // common_files.list_all_response(res, 200, true, "credit_hours_master list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_master_formate.credit_hours_master(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.is_credit_available = async (req, res) => {
    let id = ObjectId(req.params.id);
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': id } },
        { $addFileds: { 'checks': { $eq: [] } } }
    ];
    let doc = await base_control.get_aggregate(credit_hours_master, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "credit_hours_master list", /* doc.data */ credit_hours_master_formate.credit_hours_master(doc.data));
        common_files.list_all_response(res, 200, true, "credit_hours_master list", doc.data /* credit_hours_master_formate.credit_hours_master(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};