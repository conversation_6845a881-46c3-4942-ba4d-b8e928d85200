const express = require('express');
const route = express.Router();
const validator = require('./lms_validator');
const lms = require('./lms_controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

/* Setting Listing */
// route.get('/report_absence', /* validator.to, */ lms.report_absence);
route.put('/report_absence', /*  validator.permission_settings_update, */ lms.report_absence);

/* LMS Approver */
route.get(
    '/lms_approver',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    /* validator.insert_lms_roles, */ lms.lms_approver_list,
);
route.post('/lms_approver_add', /* validator.insert_lms_roles, */ lms.lms_approver_add);
route.put('/lms_approver_edit/:id', /* validator.insert_lms_roles, */ lms.lms_approver_edit);
route.delete('/lms_approver_remove/:id', /* validator.insert_lms_roles, */ lms.lms_approver_remove);

/* Student Warning and Absence Calc */
route.post(
    '/insert_student_warning_absence_calculation',
    /* validator.insert_student_warning_absence_calculation, */ lms.insert_student_warning_absence_calculation,
);
route.put(
    '/update_student_warning_absence_calculation/:master_id/:sub_id',
    /* validator.lms_master_sub_id,  validator.insert_student_warning_absence_calculation,*/ lms.update_student_warning_absence_calculation,
);
route.get(
    '/get_student_warning_absence_calculation/:id',
    /* validator.id, */ lms.get_student_warning_absence_calculation,
);
route.delete(
    '/delete_student_warning_absence_calculation/:id',
    validator.id,
    lms.delete_student_warning_absence_calculation,
);

/* Setting Listing */
route.get(
    '/permissions/:to',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.to,
    lms.list_permissions,
);
route.put(
    '/permissions',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.permission_settings_update,
    lms.set_permissions,
);

/* HR Contact */
route.get('/hr', lms.list_hr);
route.put('/hr', lms.set_hr);

route.put('/lms_roles_get', lms.lms_roles_set);
route.post('/lms_roles_assign', lms.lms_roles_assign);

/* overall settings */
route.put('/settings/:id', validator.Settings_id, lms.update_settings);

/* category */
route.post('/', validator.insert_category, lms.insert_category);
route.put('/:id', validator.id, validator.update_category, lms.update_category);
route.get('/list', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], lms.list_category);
route.get('/single/:id', validator.id, lms.single_category);
route.get('/:id', validator.id, lms.list_category_id);
route.delete('/:id', validator.id, lms.delete_category);

route.put('/status', lms.update_status);
route.put('/:id/:typeid', validator.id, validator.typeid, lms.update);

/* leave type */
route.post('/leavetype', validator.insert_leave_type, lms.insert_leave_type);
route.put(
    '/leavetype/:category_id/:leave_type_id',
    validator.leave_type_update_id,
    validator.update_leave_type,
    lms.update_leave_type,
);
route.put(
    '/leavetype/activate_deactivate/:category_id/:leave_type_id',
    validator.leave_type_update_id,
    lms.activate_deactivate_leave_type,
);
route.get('/:id/:leavetype', validator.id, lms.list_leave_type);
route.delete(
    '/leave_type/delete/:id/:leavetype/:typeid',
    validator.id,
    validator.typeid,
    lms.delete_leave_type,
);

/* LMS ROLES */
route.post('/lms_roles/:role_type', /* validator.insert_lms_roles, */ lms.insert_lms_roles);

module.exports = route;
