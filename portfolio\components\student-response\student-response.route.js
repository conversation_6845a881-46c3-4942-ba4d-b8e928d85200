const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const StudentResponseController = require('./student-response.controller');
const {
    startFormSchema,
    updateStudentResponseSchema,
    submitFormSchema,
    getStudentResponseSchema,
} = require('./student-response.validation');

router.post('/start', validate(startFormSchema), catchAsync(StudentResponseController.startForm));

router.put(
    '/response',
    validate(updateStudentResponseSchema),
    catchAsync(StudentResponseController.updateStudentResponse),
);

router.put('/submit', validate(submitFormSchema), catchAsync(StudentResponseController.submitForm));

router.get(
    '/response',
    validate(getStudentResponseSchema),
    catchAsync(StudentResponseController.getStudentResponse),
);

module.exports = router;
