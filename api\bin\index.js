const dbConfig = require('../v2/config/db.config');
const { logger } = require('../v2/utility/util_keys');

// db connection
dbConfig
    .doConnect()
    .then(() => {
        console.log('Mongoose connection successfully');
    })
    .catch((err) => console.error({ err }, 'Mongoose connection error'));

function gracefulExit() {
    dbConfig.doDisconnect().then(() => {
        logger.warn('Mongoose connection closed on Application Timeout/Termination');
        // exit application with success code
        process.exit(0);
    });
}

process
    .on('SIGINT', () => {
        logger.warn('SIGINT signal received.');
        gracefulExit();
        process.exit(0);
    })
    .on('SIGTERM', () => {
        logger.warn('SIGTERM signal received.');
        server.close(() => {
            logger.warn('Http server closed.');

            gracefulExit();
            process.exit(0);
        });
    });
