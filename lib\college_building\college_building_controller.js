let constant = require('../utility/constants');
var college_building = require('mongoose').model(constant.COLLEGE_BUILDING); 
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const college_building_formate = require('./college_building_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let query = { 'isDeleted': false };
    let project = { _id: 1, name: 1, location: 1, isDeleted: 1, isActive: 1 };
    let doc = await base_control.list(college_building, req.query.limit, req.query.pageNo, query, project);
    if (doc.status) {
        common_files.list_all_response(res, 200, true, "College Building List", doc.totalDoc, doc.totalPages, doc.currentPage, college_building_formate.college_building(doc.data));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.locations_list = async (req, res) => {
    let query = { 'isDeleted': false }; 
    let doc = await base_control.distinct_list(college_building, 'location', query);
 
    if (doc.status) {
        common_files.com_response(res, 200, true, "College building location List",  doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let query = { _id: id, 'isDeleted': false };
    let project = { _id: 1, name: 1, location: 1, isDeleted: 1, isActive: 1 };
    let doc = await base_control.get(college_building, query, project);
    if (doc.status) {
        let formated = college_building_formate.college_building_ID(doc.data);
        common_files.com_response(res, 200, true, "College Building List", formated);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let query = { 'name': req.body.name, 'isDeleted': false };
    let project = { _id: 1, name: 1, location: 1, isDeleted: 1, isActive: 1 };
    let docs = await base_control.get(college_building, query, project);
    if (!docs.status) {
        let doc = await base_control.insert(college_building, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "College Building Added successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    }
    else {
        common_files.com_response(res, 500, false, "Error duplicate values found ", "This content already present in DB");
    }
};

exports.update = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.update(college_building, object_id, req.body);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Update successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.delete = async (req, res) => {
    let id = req.params.id;
 
        let doc = await base_control.delete(college_building, id);
        if (doc.status) {
            common_files.com_response(res, 201, true, "Deleted successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
     
};

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(college_building, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "College Building List", college_building_formate.college_building(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};