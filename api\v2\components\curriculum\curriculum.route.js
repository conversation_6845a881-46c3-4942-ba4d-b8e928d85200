const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    addCurriculum,
    updateCurriculum,
    listAllCurriculum,
    getParticularCurriculum,
    archiveCurriculum,
    updateLevel,
    listArchived,
    getPreRequisite,
    deleteCurriculum,
    getCurriculumName,
    getSessionDeliveryTypes,
    listLevel,
    selectiveCourseConfigAdd,
    selectiveDelete,
    selectiveGet,
    listLevelYearWise,
    listYears,
} = require('./curriculum.controller');
const {
    curriculumIdValidator,
    yearId,
    levelId,
    institutionId,
    addCurriculumValidator,
    updateCurriculumValidator,
    listAllCurriculumValidator,
    selectiveCourseConfigValidation,
    selectiveDeleteSchema,
    selectiveGetSchema,
} = require('./curriculum.validator');
const { validate } = require('../../utility/input-validation');

router.post('/add-curriculum', validate(addCurriculumValidator), catchAsync(addCurriculum));
router.put(
    '/update-curriculum/:id',
    validate(updateCurriculumValidator),
    catchAsync(updateCurriculum),
);
router.get('/list-level/:id', validate(curriculumIdValidator), catchAsync(listLevel));

router.get('/list-year/:id', validate(curriculumIdValidator), catchAsync(listYears));
router.get(
    '/list-level-year-wise/:id',
    validate(curriculumIdValidator),
    catchAsync(listLevelYearWise),
);
router.get(
    '/list-all-curriculum/:_institution_id/:_program_id',
    validate(listAllCurriculumValidator),
    catchAsync(listAllCurriculum),
);
router.get(
    '/get-particular-curriculum/:id',
    validate(curriculumIdValidator),
    catchAsync(getParticularCurriculum),
);
router.put(
    '/archive-curriculum/:id',
    validate(curriculumIdValidator),
    catchAsync(archiveCurriculum),
);
router.put(
    '/update-level/:id/:yearId/:levelId',
    validate(curriculumIdValidator),
    validate(yearId),
    validate(levelId),
    catchAsync(updateLevel),
);
router.get(
    '/archived-list/:_institution_id/:_program_id',
    validate(listAllCurriculumValidator),
    catchAsync(listArchived),
);
router.get(
    '/get-pre-requisite/:_institution_id',
    validate(institutionId),
    catchAsync(getPreRequisite),
);
router.delete(
    '/delete-curriculum/:id',
    validate(curriculumIdValidator),
    catchAsync(deleteCurriculum),
);
router.get(
    '/get-curriculum-name/:_institution_id/:_program_id',
    validate(listAllCurriculumValidator),
    catchAsync(getCurriculumName),
);
router.get(
    '/get-session-types/:_institution_id/:_program_id',
    validate(listAllCurriculumValidator),
    catchAsync(getSessionDeliveryTypes),
);
router.put(
    '/selective-course-configuration/:_curriculum_id',
    validate(selectiveCourseConfigValidation),
    catchAsync(selectiveCourseConfigAdd),
);
router.delete(
    '/selective-course-configuration/:_curriculum_id/:_selective_id',
    validate(selectiveDeleteSchema),
    catchAsync(selectiveDelete),
);
router.get(
    '/selective-course-configuration/:_curriculum_id/:_program_id/:_year_id/:_level_id',
    validate(selectiveGetSchema),
    catchAsync(selectiveGet),
);
module.exports = router;
