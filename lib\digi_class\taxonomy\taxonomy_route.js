const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const {
    createTaxonomySchema,
    updateTaxonomySchema,
    getTaxonomySchema,
    deleteTaxonomySchema,
} = require('./taxonomy_validate_schema');

const {
    getTaxonomy,
    createTaxonomy,
    updateTaxonomy,
    deleteTaxonomy,
} = require('./taxonomy_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

// get, create, update,delete activities
route.get(
    '/',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    /* getTaxonomySchema, */ getTaxonomy,
);

route.post('/', [userPolicyAuthentication([])], createTaxonomySchema, createTaxonomy);

route.put('/:id', [userPolicyAuthentication([])], updateTaxonomySchema, updateTaxonomy);
route.delete('/:id', [userPolicyAuthentication([])], deleteTaxonomySchema, deleteTaxonomy);

module.exports = route;
