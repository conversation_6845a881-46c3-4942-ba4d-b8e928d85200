const Activities = require('../../models/activities');
const Question = require('../../models/question');
const CourseSchedule = require('../../models/course_schedule');
const Taxonomy = require('../../models/taxonomy');
const Notification = require('../../models/notification');
const User = require('../../models/user');
const {
    insert,
    get,
    get_list,
    dsCustomUpdate,
    dsDeleteOne,
    dsDeleteAll,
} = require('../../base/base_controller');
const {
    convertToMongoObjectId,
    response_function,
    list_all_response_function,
    sendResponse,
    cs,
    axiosCall,
    clone,
    sendResponseWithRequest,
} = require('../../utility/common');
const { timestampNow } = require('../../utility/common_functions');
const {
    getSloBySessionIdAndCourseId,
    getSloPlusCloBySessionIdAndCourseId,
} = require('../course_session/course_session_service');
const {
    deleteAttachment,
    localFileStorage,
    removeFolder,
    getUnsignedUrl,
} = require('../../utility/question_file_upload');
const { allCompletedActivities, clearItem } = require('../../../service/cache.service');
// push notification
const { sendNotificationPush } = require('../../../service/pushNotification.service');
const { getAllCountNotification } = require('../dashboard/dashboard_service');
const { getClosBySloIds, getClos } = require('../learning_outcomes/learning_outcomes.service');
// constants
const {
    DC_STAFF,
    DS_NO_DATA_FOUND,
    DS_ADDED,
    DS_ADD_FAILED,
    DS_DATA_RETRIEVED,
    DC_STUDENT,
    DS_UPDATED,
    DS_DELETED,
    DS_UPDATE_FAILED,
    STARTED,
    COMPLETED,
    ITEM_AUDIO_FILE_TYPES,
    ITEM_VIDEO_FILE_TYPES,
    SCHEDULE,
    PUBLISHED,
    NOT_STARTED,
    SCHEDULE_TYPES: { REGULAR },
    DS_STUDENT,
    DS_CLO_KEY,
    DS_SLO_KEY,
    DRAFT,
} = require('../../utility/constants');
const {
    TIME,
    ONE_BY_ONE,
    OLD,
    NEW,
    QUIZ,
    POLL,
    SURVEY,
    OPEN_ENDED,
    LIKERTSCALE,
} = require('../../utility/enums');
const {
    getStudentGroup,
    sendQuizScheduleNotification,
    getAllActivity,
    capitalizeFirstLetter,
    sendNotificationByActivityId,
    getAllSessionList,
    getAllAdminActivities,
} = require('./activities_service');
const { sendDashboardNotification } = require('../notification/notification_service');
const project = {
    courseId: 1,
    sessionFlowIds: 1,
    name: 1,
    type: 1,
    questions: 1,
    status: 1,
    startTime: 1,
    endTime: 1,
    createdBy: 1,
    quizType: 1,
    setQuizTime: 1,
    socketEventStaffId: 1,
    socketEventStudentId: 1,
    staffStartWithExam: 1,
    students: 1,
    studentCompletedQuiz: 1,
    schedule: 1,
    sessionId: 1,
    scheduleIds: 1,
    studentGroupId: 1,
    student_groups: 1,
};

// get all activities
exports.getActivities = async (req, res) => {
    try {
        const {
            type,
            userId,
            courseId,
            sessionId,
            scheduleId,
            mergeStatus,
            limit,
            page,
            search,
            mode,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            courseAdmin,
            _institution_calendar_id: queryInstitutionCalendarId,
        } = req.query;
        const { _institution_calendar_id: headerInstitutionCalendarId } = req.headers;
        let institutionCalendarId = headerInstitutionCalendarId;
        if (
            queryInstitutionCalendarId &&
            queryInstitutionCalendarId.toString() !== headerInstitutionCalendarId.toString()
        ) {
            institutionCalendarId = queryInstitutionCalendarId;
        }
        const { totalDoc, totalPages, currentPage, activities } = await getAllActivity(
            type,
            userId,
            limit,
            page,
            institutionCalendarId,
            courseAdmin,
            courseId,
            sessionId,
            scheduleId,
            mergeStatus,
            search,
            mode,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            institutionCalendarId,
            rotation_count,
        );

        if (!activities || !activities.length) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('ACTIVITY_NOT_FOUND'), []));
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t(DS_DATA_RETRIEVED),
                        totalDoc,
                        totalPages,
                        currentPage,
                        activities,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DATA_RETRIEVED'), activities));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.message));
    }
};

// get all activities
exports.getAdminActivities = async (req, res) => {
    try {
        const { userId, courseId, limit, page } = req.query;
        const { totalDoc, totalPages, currentPage, activities } = await getAllAdminActivities(
            userId,
            limit,
            page,
            courseId,
        );
        console.log('b');
        if (!activities || !activities.length) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('ACTIVITY_NOT_FOUND'), []));
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t(DS_DATA_RETRIEVED),
                        totalDoc,
                        totalPages,
                        currentPage,
                        activities,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DATA_RETRIEVED'), activities));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.message));
    }
};
// get  activities
exports.getActivity = async (req, res) => {
    try {
        const {
            params: { id, scheduleId },
            query: { studentId, mergeStatus },
        } = req;
        const activityQuery = { _id: id, isDeleted: false };
        if (scheduleId) activityQuery.scheduleIds = convertToMongoObjectId(scheduleId);
        let activities = await Activities.findOne(activityQuery, {})
            .populate({
                path: 'courseId',
                select: {
                    course_code: 1,
                    course_name: 1,
                    versionedCourseIds: 1,
                    versionedFrom: 1,
                    versionName: 1,
                    versioned: 1,
                    versionNo: 1,
                },
            })
            .exec();
        if (!activities) return sendResponse(res, 200, false, req.t('NO_DATA_FOUND'));
        const {
            _id: activityId,
            sessionId,
            status,
            courseId,
            sessionFlowIds,
            name,
            type,
            quizType,
            createdBy,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            students,
            questions,
            scheduleIds,
            schedule,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            courseAdmin,
        } = activities;
        let studentGroupsName = '';
        let selectedQuestions = [];
        let questionIds;
        questionIds = questions.sort((a, b) => {
            return a.order - b.order;
        });
        questionIds = questions.map((question) => convertToMongoObjectId(question._id));
        if (questionIds.length) {
            selectedQuestions = await Question.find({
                _id: { $in: questionIds },
                isDeleted: false,
            });
            if (selectedQuestions.length) {
                selectedQuestions = questionIds.map((questionId) => {
                    const questionDetails = selectedQuestions.find(
                        (question) => question._id.toString() === questionId.toString(),
                    );
                    return questionDetails;
                });
                selectedQuestions = selectedQuestions.map((selectedQuestion) => {
                    const { _id } = selectedQuestion;
                    const questionType = questions.find((q) => q._id.toString() === _id.toString());
                    if (questionType) {
                        selectedQuestion.questionMoved = questionType.questionMoved;
                        selectedQuestion.acceptQuestionResponse =
                            questionType.acceptQuestionResponse;
                        selectedQuestion.type = questionType.type;
                        selectedQuestion.order = questionType.order;
                    }
                    return selectedQuestion;
                });
            }
        }
        let answeredQuestions;
        if (studentId) {
            answeredStudent = students.find((s) => cs(s._studentId) === cs(studentId));
            if (answeredStudent) answeredQuestions = answeredStudent.questions;
        }
        let activityQuestions;
        if (selectedQuestions.length) {
            activityQuestions = await formatQuestions(selectedQuestions, answeredQuestions);
        }

        // get session and slos details
        const sessionFlowSlos = [];
        let sessionFlows = sessionFlowIds.map((sessionFlowId) =>
            convertToMongoObjectId(sessionFlowId._id),
        );
        if (sessionId) {
            sessionFlows = [...sessionFlows, ...[sessionId]];
        }

        let sessions = await CourseSchedule.find(
            {
                _course_id: convertToMongoObjectId(courseId._id),
                $or: [
                    {
                        'session._session_id': { $in: sessionFlows },
                    },
                    {
                        _id: { $in: sessionFlows },
                    },
                ],
            },
            { session: 1, title: 1, type: 1, _id: 1 },
        ).lean();

        let sessionIdDetail;
        if (sessionId) {
            sessionIdDetail = sessions.find(
                (sessionEntry) =>
                    (sessionId &&
                        sessionEntry.session &&
                        sessionEntry.session._session_id.toString() === sessionId._id.toString()) ||
                    (sessionId && sessionEntry._id.toString() === sessionId._id.toString()),
            );
            if (sessionIdDetail.session) {
                sessionIdDetail.session.type = REGULAR;
            }

            sessionIdDetail =
                sessionIdDetail && sessionIdDetail.session
                    ? sessionIdDetail.session
                    : sessionIdDetail && sessionIdDetail.title
                    ? {
                          _id: sessionIdDetail._id,
                          title: sessionIdDetail.title,
                          type: sessionIdDetail.type,
                      }
                    : '';
        }
        sessionFlows = sessionFlows.map((sessionFlow) => sessionFlow.toString());
        const sessionDetails = sessionFlowIds.map((sessionId) => {
            const sessionDetail = sessions.find(
                (sessionEntry) =>
                    (sessionId &&
                        sessionEntry.session &&
                        sessionEntry.session._session_id.toString() === sessionId._id.toString()) ||
                    (sessionEntry.type === sessionId.type &&
                        sessionEntry._id.toString() === sessionId._id.toString()),
            );
            if (sessionDetail) {
                const { session, _id, title, type } = sessionDetail;
                if (session) {
                    const { _session_id } = session;
                    if (session && _session_id && sessionFlows.includes(_session_id.toString())) {
                        return {
                            _id: session._session_id,
                            s_no: session.s_no,
                            delivery_symbol: session.delivery_symbol,
                            delivery_no: session.delivery_no,
                            session_type: session.session_type,
                            session_topic: session.session_topic,
                            type: REGULAR,
                        };
                    }
                }
                if (title && type !== REGULAR && sessionFlows.includes(_id.toString())) {
                    return { _id, title, type };
                }
            }
        });
        sessions = sessions.filter((session) => session.session);
        sessions = sessions.map((session) => session.session);
        for (const sessionFlowId of sessionFlowIds) {
            const sessionDetail = sessions.find((s) => cs(s._session_id) === cs(sessionFlowId._id));
            if (sessionDetail) {
                let slos = await getSloPlusCloBySessionIdAndCourseId(
                    courseId._id,
                    sessionFlowId._id,
                );
                slos = slos.filter(
                    (value, index, array) =>
                        array.findIndex((sloIds) => sloIds._id === value._id) === index,
                );
                sessionFlowSlos.push({ session: sessionDetail, slos });
            }
        }
        let totalStudents;
        if (scheduleIds && scheduleIds.length) {
            const courseSchedules = await CourseSchedule.find({
                _id: { $in: scheduleIds },
            });
            // Merged Session gathering
            let mergedScheduleIds = [];
            for (scheduleElement of courseSchedules) {
                if (scheduleElement.merge_status) {
                    mergedScheduleIds = [
                        ...mergedScheduleIds,
                        ...scheduleElement.merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        ),
                    ];
                }
            }
            let mergedCourseScheduleData = [];
            if (mergedScheduleIds.length)
                mergedCourseScheduleData = await CourseSchedule.find({
                    _id: { $in: mergedScheduleIds },
                });
            const mergedStudents = [];
            let studentGroups = [];
            for (const scheduleIdEntry of courseSchedules) {
                const { _id: scheduleId, merge_status, merge_with, students } = scheduleIdEntry;
                let scheduleStudents;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const schedules = mergedCourseScheduleData.filter((ele) =>
                            scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                        );
                        scheduleStudents = schedules.map((schedule) => schedule.students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);

                        schedules.push(scheduleIdEntry);
                        schedules.forEach((courseSchedule) => {
                            const { student_groups, _id } = courseSchedule;
                            studentGroups.push(student_groups);
                        });
                    }
                } else {
                    const { student_groups } = scheduleIdEntry;
                    studentGroups.push(student_groups);
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.concat(students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                } else {
                    scheduleStudents = students;
                }
                if (scheduleStudents && scheduleStudents.length) {
                    mergedStudents.push(scheduleStudents);
                }
            }
            // eslint-disable-next-line no-sequences
            studentGroups = studentGroups.reduce((r, e) => (r.push(...e), r), []);
            // eslint-disable-next-line no-sequences
            studentGroups = studentGroups.reduce((studentGroup, currentStudentGroup) => {
                const x = studentGroup.find(
                    (item) =>
                        item.group_id.toString() === currentStudentGroup.group_id.toString() &&
                        item.session_group.length &&
                        currentStudentGroup.session_group.length &&
                        item.session_group.find((sessionGroup) => {
                            const currentSessionStudentGroup =
                                currentStudentGroup.session_group.map((sessionGroup) =>
                                    sessionGroup.session_group_id.toString(),
                                );
                            if (
                                currentSessionStudentGroup.includes(
                                    sessionGroup.session_group_id.toString(),
                                )
                            )
                                return true;
                        }),
                );
                if (!x) {
                    return studentGroup.concat([currentStudentGroup]);
                }
                return studentGroup;
            }, []);
            if (studentGroups && studentGroups.length) {
                studentGroups = studentGroups.map((student_group) => {
                    const { group_name, session_group } = student_group;
                    let groupName = group_name.split('-').slice(-2);
                    groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                    if (session_group && session_group.length) {
                        let sessionGroup = session_group.map((groupNameEntry) => {
                            let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                            groupNames = groupNames[1]
                                ? groupNames[0] + '-' + groupNames[1]
                                : groupNames[0];
                            return groupNames;
                        });
                        sessionGroup = sessionGroup.toString();
                        groupName += '(' + sessionGroup + ')';
                    }
                    return groupName;
                });
                studentGroups = studentGroups.toString();
                studentGroupsName += studentGroups;
            }
            // eslint-disable-next-line prefer-spread
            totalStudents = [].concat.apply([], mergedStudents);
            // different calendar and year and level based courses
            totalStudents = totalStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
        }
        activities = {
            questions:
                activityQuestions && activityQuestions.questions ? activityQuestions.questions : [],
            status,
            sessionId,
            sessionIdDetail,
            studentGroupName: studentGroupsName,
            _id: activityId,
            courseId,
            sessionFlowIds: sessionDetails || [],
            sessionsAndSlos: sessionFlowSlos,
            name: name !== null ? name : '',
            type,
            quizType,
            createdBy,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            schedule,
            scheduleIds,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            answeredCount:
                activityQuestions && activityQuestions.answeredCount
                    ? activityQuestions.answeredCount
                    : 0,
            totalQuestionCount:
                activityQuestions && activityQuestions.totalQuestionCount
                    ? activityQuestions.totalQuestionCount
                    : 0,
            studentCorrectAnsweredCount:
                activityQuestions && activityQuestions.studentCorrectAnswered
                    ? activityQuestions.studentCorrectAnswered
                    : 0,
            totalStudentAnsweredCount: students.length,
            totalStudentCount: totalStudents ? totalStudents.length : 0,
            courseAdmin,
        };
        return sendResponse(res, 200, true, req.t('DATA_RETRIEVED'), activities);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getAdminActivities = async (req, res) => {
    try {
        const { userId, courseId, limit, page } = req.query;
        const { totalDoc, totalPages, currentPage, activities } = await getAllAdminActivities(
            userId,
            limit,
            page,
            courseId,
        );

        if (!activities || !activities.length) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('ACTIVITY_NOT_FOUND'), []));
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t('DATA_RETRIEVED'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        activities,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DATA_RETRIEVED'), activities));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.message));
    }
};

getActivity = async (id, userId) => {
    try {
        const activityQuery = { _id: id, isDeleted: false, isActive: true };
        let activities = await Activities.findOne(activityQuery, project)
            .populate({ path: 'createdBy', select: { name: 1, _id: 1 } })
            .exec();
        if (!activities) return sendResponse(res, 200, false, DS_NO_DATA_FOUND);
        const {
            _id: activityId,
            sessionId,
            scheduleIds,
            status,
            name,
            type,
            quizType,
            createdBy,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            students,
            questions,
        } = activities;
        let totalStudents;
        let studentGroupsName = '';
        if (scheduleIds && scheduleIds.length) {
            const courseSchedules = await CourseSchedule.find({
                _id: { $in: scheduleIds },
            });
            const mergedStudents = [];
            for (const scheduleIdEntry of courseSchedules) {
                const { _id: scheduleId, merge_status, merge_with, students } = scheduleIdEntry;
                let scheduleStudents;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const schedules = await CourseSchedule.find({
                            _id: { $in: scheduleIds },
                            isDeleted: false,
                            isActive: true,
                        });
                        scheduleStudents = schedules.map((schedule) => schedule.students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);

                        schedules.push(scheduleIdEntry);
                        schedules.forEach((courseSchedule) => {
                            const { student_groups, _id } = courseSchedule;
                            if (student_groups && student_groups.length) {
                                let studentGroups = student_groups.map((student_group) => {
                                    const { group_name, session_group } = student_group;
                                    let groupName = group_name.split('-').slice(-2);
                                    groupName = groupName[1]
                                        ? groupName[0] + '-' + groupName[1]
                                        : groupName[0];
                                    if (session_group && session_group.length) {
                                        let sessionGroup = session_group.map((groupNameEntry) => {
                                            let groupNames = groupNameEntry.group_name
                                                .split('-')
                                                .slice(-2);
                                            groupNames = groupNames[1]
                                                ? groupNames[0] + '-' + groupNames[1]
                                                : groupNames[0];
                                            return groupNames;
                                        });
                                        sessionGroup = sessionGroup.toString();
                                        groupName += '(' + sessionGroup + ')';
                                    }
                                    return groupName;
                                });
                                studentGroups = studentGroups.toString();
                                studentGroupsName += studentGroups;
                            }
                        });
                    }
                } else {
                    const { student_groups } = scheduleIdEntry;
                    if (student_groups && student_groups.length) {
                        let studentGroups = student_groups.map((student_group) => {
                            const { group_name, session_group } = student_group;
                            let groupName = group_name.split('-').slice(-2);
                            groupName = groupName[1]
                                ? groupName[0] + '-' + groupName[1]
                                : groupName[0];
                            if (session_group && session_group.length) {
                                let sessionGroup = session_group.map((groupNameEntry) => {
                                    let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                    groupNames = groupNames[1]
                                        ? groupNames[0] + '-' + groupNames[1]
                                        : groupNames[0];
                                    return groupNames;
                                });
                                sessionGroup = sessionGroup.toString();
                                groupName += '(' + sessionGroup + ')';
                            }
                            return groupName;
                        });
                        studentGroups = studentGroups.toString();
                        studentGroupsName += studentGroups;
                    }
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.concat(students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                } else {
                    scheduleStudents = students;
                }
                if (scheduleStudents && scheduleStudents.length) {
                    mergedStudents.push(scheduleStudents);
                }
            }
            // eslint-disable-next-line prefer-spread
            totalStudents = [].concat.apply([], mergedStudents);
            // different calendar and year and level based courses
            totalStudents = totalStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
        }
        let questionIds;
        questionIds = questions.sort((a, b) => {
            return a.order - b.order;
        });
        questionIds = questions.map((question) => convertToMongoObjectId(question._id));
        let selectedQuestions = [];
        if (questionIds.length)
            selectedQuestions = await Question.find({
                _id: { $in: questionIds },
                isDeleted: false,
            });

        selectedQuestions = selectedQuestions.map((selectedQuestion) => {
            const { _id } = selectedQuestion;
            const questionType = questions.find((q) => q._id.toString() === _id.toString());
            if (questionType) {
                selectedQuestion.questionMoved = questionType.questionMoved;
                selectedQuestion.acceptQuestionResponse = questionType.acceptQuestionResponse;
                selectedQuestion.type = questionType.type;
                selectedQuestion.order = questionType.order;
            }
            return selectedQuestion;
        });
        let answeredQuestions;
        if (userId) {
            answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
            if (answeredStudent) answeredQuestions = answeredStudent.questions;
        }
        const activityQuestions = await formatQuestionsWithOutAttachment(
            selectedQuestions,
            answeredQuestions,
        );
        let sessionDetails;
        if (sessionId) {
            sessionDetails = {
                _id: sessionId._id,
                session_date: sessionId.session_date,
                start_time: sessionId.start_time,
                end_time: sessionId.end_time,
            };
        }
        activities = {
            status,
            _id: activityId,
            name: name !== null ? name : '',
            type,
            quizType,
            createdBy,
            studentGroupsName,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            questions,
            answeredCount: activityQuestions.answeredCount,
            totalQuestionCount: activityQuestions.totalQuestionCount,
            studentCorrectAnsweredCount: activityQuestions.studentCorrectAnswered,
            totalStudentAnsweredCount: students.length,
            totalStudentCount: totalStudents.length,
        };
        return activities;
    } catch (error) {
        throw new Error(error);
    }
};

// create activities
exports.createActivities = async (req, res) => {
    try {
        const {
            courseId,
            sessionFlowIds,
            activityId,
            quizType,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            name,
            createdBy,
            status,
            questions,
            courseAdmin,
        } = req.body;
        let data;
        const bulkInserts = [];
        const questionIds = [];
        const orderPlace = [];
        let msg = '';
        // create question
        if (activityId && questions && questions.length) {
            questions.forEach((question) => {
                const {
                    questionId,
                    options,
                    name: questionName,
                    questionType,
                    feedback,
                    attachments,
                    sessionId: questionSessionId,
                    sloIds,
                    taxonomyIds,
                    order,
                    questionViewType,
                    describeYourQuestion,
                    itemCategory,
                    aiNoOfOptions,
                    generateFeedback,
                    surveyQuestionType,
                    maxCharacterLimit,
                } = question;
                let questionOptions;

                if (!questionId) {
                    if (options && options.length) {
                        questionOptions = options.map((option) => {
                            const questionOption = { answer: option.answer };
                            if (option.attachments) questionOption.attachments = option.attachments;
                            if (option.name) questionOption.text = option.name;
                            return questionOption;
                        });
                    }
                    data = {
                        _activityId: convertToMongoObjectId(activityId),
                        text: questionName,
                        questionType,
                        options: questionOptions,
                        feedback,
                    };
                    orderPlace.push(order);
                    if (!options.length && surveyQuestionType === OPEN_ENDED) {
                        data.maxCharacterLimit = maxCharacterLimit;
                        data.surveyQuestionType = surveyQuestionType;
                    }
                    if (surveyQuestionType && surveyQuestionType === LIKERTSCALE) {
                        data.surveyQuestionType = surveyQuestionType;
                    }
                    if (questionSessionId) data.sessionId = questionSessionId;
                    if (sloIds) data.sloIds = sloIds.map((sloId) => convertToMongoObjectId(sloId));
                    if (taxonomyIds)
                        data.taxonomyIds = taxonomyIds.map((taxonomyId) =>
                            convertToMongoObjectId(taxonomyId),
                        );
                    if (attachments) data.attachments = attachments;
                    if (questionViewType) data.questionViewType = questionViewType;
                    if (describeYourQuestion) data.describeYourQuestion = describeYourQuestion;
                    if (itemCategory) data.itemCategory = itemCategory;
                    if (aiNoOfOptions) data.aiNoOfOptions = aiNoOfOptions;
                    if (typeof generateFeedback === 'boolean')
                        data.generateFeedback = generateFeedback;
                    bulkInserts.push({ insertOne: { document: data } });
                } else
                    questionIds.push({ id: convertToMongoObjectId(questionId), type: OLD, order });
            });

            if (bulkInserts.length) {
                await Question.bulkWrite(bulkInserts)
                    .then((result) => {
                        if (result) {
                            const { insertedIds } = result;
                            for (const insertedId in insertedIds) {
                                if (insertedIds.hasOwnProperty(insertedId)) {
                                    questionIds.push({
                                        id: insertedIds[insertedId],
                                        type: NEW,
                                        order: orderPlace[insertedId],
                                    });
                                }
                            }
                        }
                    })
                    .catch((err) => {
                        return sendResponse(res, 200, false, req.t('FAILED_TO_ADD'));
                    });
            }
            if (status) {
                const updateData = {};
                updateData.status = status;
                if (status === PUBLISHED) updateData.status = NOT_STARTED;
                if (questionIds.length) {
                    const question = questionIds.map((questionId) => {
                        return {
                            _id: questionId.id,
                            type: questionId.type,
                            order: questionId.order,
                        };
                    });
                    updateData.questions = question;
                }
                await dsCustomUpdate(
                    Activities,
                    { _id: convertToMongoObjectId(activityId) },
                    { $set: updateData },
                );
                sendNotificationByActivityId(activityId, 'schedule');
            }
        } else {
            // create quiz
            data = {
                name,
                courseId,
                sessionFlowIds,
                quizType,
                createdBy,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
            };
            if (courseAdmin === true) {
                data.courseAdmin = courseAdmin;
            }
            const { status: insertStatus, responses } = await insert(Activities, data);

            if (quizType === QUIZ) {
                msg = req.t('QUIZ_ADDED_SUCCESSFULLY');
            } else if (quizType === POLL) {
                msg = req.t('POLL_ADDED_SUCCESSFULLY');
            } else if (quizType === SURVEY) {
                msg = req.t('SURVERY_ADDED_SUCCESSFULLY');
            } else {
                msg = req.t('ADDED_SUCCESSFULLY');
            }
            if (!insertStatus) return sendResponse(res, 200, false, req.t('FAILED_TO_ADD'));
            return sendResponse(res, 200, true, msg, responses);
        }
        const activity = await Activities.findOne({ _id: convertToMongoObjectId(activityId) });
        let statusMsg = '';
        if (status === PUBLISHED) {
            statusMsg = PUBLISHED;
        } else {
            statusMsg = 'drafted';
        }

        if (activity.quizType === QUIZ) {
            msg =
                status === PUBLISHED
                    ? req.t('QUIZ_PUBLISHED_SUCCESSFULLY')
                    : req.t('QUIZ_DRAFTED_SUCCESSFULLY');
        } else if (activity.quizType === POLL) {
            msg =
                status === PUBLISHED
                    ? req.t('POLL_PUBLISHED_SUCCESSFULLY')
                    : req.t('POLL_DRAFTED_SUCCESSFULLY');
        } else if (activity.quizType === SURVEY) {
            msg =
                status === PUBLISHED
                    ? req.t('SURVEY_PUBLISHED_SUCCESSFULLY')
                    : req.t('SURVEY_DRAFTED_SUCCESSFULLY');
        } else {
            msg = '' + statusMsg + ' successfully';
        }
        return sendResponse(res, 200, true, msg);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.uploadFile = async (req, res) => {
    try {
        const {
            params: { id: activityId },
            files,
        } = req;
        let data;
        // files validation check
        if (files) {
            const videoLength = [];
            const audioLength = [];
            const { attachment } = files;
            if (attachment && attachment.length) {
                for (const attachmentEntry of attachment) {
                    const { originalname, buffer, mimetype } = attachmentEntry;
                    localFileStorage(buffer, originalname);
                    const fileType = mimetype.split('/')[0];
                    const format = mimetype.split('/')[1];
                    removeFolder(originalname);
                }
            }

            /*   const cond = (el) => el >= 60;
              if (videoLength && videoLength.length && videoLength.some(cond))
                  return sendResponse(res, 200, false, 'Video duration not allowed to 60 sec');
              if (audioLength && audioLength.length && audioLength.some(cond))
                  return sendResponse(res, 200, false, 'Audio duration not allowed to 60 sec'); */
            if (activityId) {
                // file attachment
                if (attachment && attachment.length) {
                    const uploadAttachment = await uploadQuestionAttachment(
                        activityId,
                        attachment[0],
                    );
                    if (uploadAttachment) {
                        unsignedUrl = await getUnsignedUrl(uploadAttachment, activityId);
                        data = { url: uploadAttachment, viewUrl: unsignedUrl };
                    }
                }
            }
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('ADDED_SUCCESSFULLY'), data);
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

// update activities
exports.updateActivities = async (req, res) => {
    try {
        const {
            body: {
                name,
                type,
                questions,
                status,
                schedule,
                sessionId,
                scheduleIds,
                sessionFlowIds,
                sessionRemoved,
                // _program_id,
                // year_no,
                // level_no,
                // rotation,
                // rotation_count,
                // _institution_calendar_id,
            },
            params: { id: activityId },
        } = req;
        let data;
        let updateQuery;
        const questionIds = [];
        const bulkUpdates = [];
        const bulkInserts = [];
        const orderPlace = [];
        // get activity
        const activity = await Activities.findOne({ _id: activityId });
        if (!activity)
            return sendResponseWithRequest(req, res, 200, false, req.t('ACTIVITY_NOT_FOUND'));
        const {
            questions: activityQuestions,
            sessionFlowIds: activitySessionFlowIds,
            quizType,
            status: activityStatus,
            type: activityType,
        } = activity;
        const activityQuestionIds = activityQuestions.map(
            (activityQuestion) => activityQuestion._id,
        );
        const oldActivityQuestions = await Question.find({ _id: { $in: activityQuestionIds } });
        if (questions && questions.length) {
            questions.forEach((question) => {
                const {
                    questionId,
                    type: activityQuestionType,
                    name: questionName,
                    questionType,
                    options,
                    feedback,
                    attachments,
                    sessionId: questionSessionId,
                    sloIds,
                    taxonomyIds,
                    order,
                    questionViewType,
                    describeYourQuestion,
                    itemCategory,
                    aiNoOfOptions,
                    generateFeedback,
                    surveyQuestionType,
                    maxCharacterLimit,
                } = question;

                const updateData = {
                    _activityId: activityId,
                    order,
                };
                if (questionName) {
                    updateData.text = questionName;
                }
                if (surveyQuestionType) {
                    updateData.surveyQuestionType = surveyQuestionType;
                }
                if (maxCharacterLimit) {
                    updateData.maxCharacterLimit = maxCharacterLimit;
                }
                if (questionType) {
                    updateData.questionType = questionType;
                }
                if (questionViewType) {
                    updateData.questionViewType = questionViewType;
                }
                if (describeYourQuestion) {
                    updateData.describeYourQuestion = describeYourQuestion;
                }
                if (itemCategory) {
                    updateData.itemCategory = itemCategory;
                }
                if (aiNoOfOptions) {
                    updateData.aiNoOfOptions = aiNoOfOptions;
                }
                if (typeof generateFeedback === 'boolean') {
                    updateData.generateFeedback = generateFeedback;
                }
                if (feedback) updateData.feedback = feedback;
                if (attachments) updateData.attachments = attachments;
                if (questionSessionId) {
                    updateData.sessionId = questionSessionId;
                    updateData.sloIds = sloIds;
                    updateData.taxonomyIds = taxonomyIds;
                }
                if (questionId) {
                    if (
                        activityQuestions.find(
                            (activityQuestion) =>
                                activityQuestion._id.toString() === questionId.toString(),
                        )
                    ) {
                        questionIds.push({ id: questionId, type: activityQuestionType, order });
                        if (activityQuestionType !== OLD) {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _activityId: convertToMongoObjectId(activityId),
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: updateData,
                                },
                            });
                        }
                    } else {
                        questionIds.push({ id: questionId, type: activityQuestionType, order });
                        bulkUpdates.push({
                            updateOne: {
                                filter: {
                                    _activityId: convertToMongoObjectId(activityId),
                                    _id: convertToMongoObjectId(questionId),
                                },
                                update: { isDeleted: true },
                            },
                        });
                    }
                } else {
                    updateData.options = options.map((option) => {
                        return {
                            text: option.name,
                            answer: option.answer,
                            attachments: option.attachments,
                        };
                    });
                    orderPlace.push(order);
                    bulkInserts.push({ insertOne: { document: updateData } });
                }
                if (questionId && options && options.length) {
                    const oldActivityQuestion = oldActivityQuestions.find(
                        (activityQuestionEntry) =>
                            activityQuestionEntry._id.toString() === questionId.toString(),
                    );

                    let activityOptions;
                    if (oldActivityQuestion) activityOptions = oldActivityQuestion.options;

                    options.forEach((option) => {
                        const {
                            optionId: _id,
                            name: text,
                            answer,
                            attachments: optionAttachments,
                        } = option;
                        if (
                            activityOptions &&
                            activityOptions.find(
                                (activityOptionEntry) =>
                                    _id && activityOptionEntry._id.toString() === _id.toString(),
                            )
                        ) {
                            const optionUpdateData = {
                                'options.$[i].text': text,
                                'options.$[i].answer': answer,
                            };
                            if (optionAttachments) {
                                optionUpdateData['options.$[i].attachments'] = optionAttachments;
                            }

                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(questionId) },
                                    update: { $set: optionUpdateData },
                                    arrayFilters: [{ 'i._id': convertToMongoObjectId(_id) }],
                                },
                            });
                        } else {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            options: { _id: convertToMongoObjectId(_id) },
                                        },
                                    },
                                },
                            });
                        }
                        if (!_id && (text || (optionAttachments && optionAttachments.length))) {
                            const optionUpdateData = { text, answer };
                            if (optionAttachments) {
                                optionUpdateData.attachments = optionAttachments;
                            }
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(questionId) },
                                    update: { $push: { options: optionUpdateData } },
                                },
                            });
                        }
                    });

                    const optionIds = options.map(
                        (option) => option.optionId && option.optionId.toString(),
                    );
                    let removeOptions = [];
                    if (activityOptions) {
                        removeOptions = activityOptions.filter(
                            (activityOption) => !optionIds.includes(activityOption._id.toString()),
                        );
                    }

                    if (removeOptions.length) {
                        const removeOptionIds = removeOptions.map(
                            (removeOption) => removeOption._id,
                        );
                        removeOptionIds.forEach((removeOptionId) => {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: {
                                        _id: convertToMongoObjectId(questionId),
                                    },
                                    update: {
                                        $pull: {
                                            options: {
                                                _id: convertToMongoObjectId(removeOptionId),
                                            },
                                        },
                                    },
                                },
                            });
                        });
                    }
                }
            });

            if (bulkUpdates.length) {
                await Question.bulkWrite(bulkUpdates)
                    .then()
                    .catch((e) => {
                        return sendResponseWithRequest(
                            req,
                            res,
                            422,
                            false,
                            req.t('UPDATE_ERROR'),
                            e.message,
                        );
                    });
            }

            if (bulkInserts.length) {
                await Question.bulkWrite(bulkInserts)
                    .then((result) => {
                        if (result) {
                            const { insertedIds } = result;
                            for (const insertedId in insertedIds) {
                                if (insertedIds.hasOwnProperty(insertedId)) {
                                    questionIds.push({
                                        id: insertedIds[insertedId].toString(),
                                        type: NEW,
                                        order: orderPlace[insertedId],
                                    });
                                }
                            }
                        }
                    })
                    .catch((e) => {
                        return sendResponseWithRequest(
                            req,
                            res,
                            422,
                            false,
                            req.t('UPDATE_ERROR'),
                            e.message,
                        );
                    });
            }
        } else {
            updateQuery = { _id: activityId };
            if (type === SCHEDULE) {
                const getMergeSchedules = await CourseSchedule.find(
                    {
                        _id: { $in: scheduleIds },
                    },
                    {
                        merge_with: 1,
                        merge_status: 1,
                        _id: 1,
                    },
                );
                const mergedSessions = [];
                for (const getMergeSchedule of getMergeSchedules) {
                    const { merge_status, merge_with } = getMergeSchedule;
                    if (merge_status) {
                        const mergedscheduleIds = merge_with.map((mergeWith) =>
                            mergedSessions.push(mergeWith.schedule_id.toString()),
                        );
                    }
                }
                const courseScheduleIds = [...scheduleIds, ...mergedSessions];
                data = {
                    $set: {
                        type,
                        schedule,
                        sessionId,
                        scheduleIds: courseScheduleIds,
                    },
                };
                if (activityStatus === NOT_STARTED) {
                    sendQuizScheduleNotification(
                        insert,
                        sendNotificationPush,
                        activity,
                        sessionId,
                        schedule,
                        courseScheduleIds,
                    );
                }
            } else {
                if (quizType === QUIZ) {
                    // if session changes in update page
                    const sessionCheck = await deleteSelectedQuestions(
                        activitySessionFlowIds,
                        activityQuestions,
                        sessionFlowIds,
                        sessionRemoved,
                    );
                    if (sessionCheck.isChanged) {
                        const msg = req.t('DO_YOU_WANT_TO_CHANGE_SESSION');
                        if (sessionCheck) return sendResponse(res, 200, true, msg, sessionCheck);
                    }
                }
                if (name && sessionFlowIds && sessionFlowIds.length) {
                    data = {
                        $set: {
                            name,
                            sessionFlowIds,
                            // _program_id,
                            // year_no,
                            // level_no,
                            // rotation,
                            // rotation_count,
                            // _institution_calendar_id,
                        },
                    };
                }
            }
            if (data) {
                updateQuery = { _id: convertToMongoObjectId(activityId) };
                const { success } = await dsCustomUpdate(Activities, updateQuery, data);
                if (!success)
                    return sendResponseWithRequest(req, res, 200, false, req.t('FAILED_TO_UPDATE'));
            }
        }

        if (status) {
            const updateData = {};
            updateData.status = status;
            if (status === PUBLISHED) updateData.status = NOT_STARTED;
            if (questionIds.length) {
                const question = questionIds.map((questionId) => {
                    return { _id: questionId.id, type: questionId.type, order: questionId.order };
                });
                updateData.questions = question;
            }
            await dsCustomUpdate(
                Activities,
                { _id: convertToMongoObjectId(activityId) },
                { $set: updateData },
            );

            if (activityType === SCHEDULE) {
                sendQuizScheduleNotification(insert, sendNotificationPush, activity);
            }
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

exports.getStudentGroupBySessionId = async (req, res) => {
    try {
        const { sessionId } = req.params;
        const studentGroups = await getStudentGroup(sessionId);
        if (studentGroups.length)
            return sendResponse(res, 200, true, req.t('STUDENT_GROUP_LIST'), studentGroups);
        return sendResponse(res, 200, true, req.t('STUDENT_GROUP_NOT_FOUND'), []);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// if session changes in update screen remove old session based questions
deleteSelectedQuestions = async (
    activitySessionFlowIds,
    activityQuestions,
    sessionFlowIds,
    sessionRemoved,
) => {
    activitySessionFlowIds = activitySessionFlowIds.map((activitySessionFlowId) =>
        activitySessionFlowId._id.toString(),
    );
    sessionFlowIds = sessionFlowIds.map((sessionFlowId) => sessionFlowId._id);
    // check sessions flow mismatch
    const misMatchSessions = activitySessionFlowIds.filter(
        (activitySessionFlowId) => !sessionFlowIds.includes(activitySessionFlowId),
    );
    const questionIds = activityQuestions.map((activityQuestion) =>
        convertToMongoObjectId(activityQuestion._id),
    );
    const questions = await Question.find({
        isDeleted: false,
        _id: {
            $in: questionIds,
        },
    });

    // mismatched session questions
    if (misMatchSessions.length > 0 && !sessionRemoved) {
        let sessions = misMatchSessions.map((sessionQuestion) =>
            convertToMongoObjectId(sessionQuestion),
        );
        sessions = [...new Set(sessions)];
        const courseSchedules = await CourseSchedule.find(
            {
                isDeleted: false,
                'session._session_id': {
                    $in: sessions,
                },
            },
            { session: 1, _id: -1 },
        );
        const sessionDetail = [];
        if (courseSchedules) {
            sessions.forEach((session) => {
                const sessionDetails = courseSchedules.find(
                    (courseSchedule) =>
                        courseSchedule.session._session_id.toString() === session.toString(),
                );
                if (sessionDetails) {
                    sessionDetail.push(
                        sessionDetails.session.delivery_symbol +
                            '' +
                            sessionDetails.session.delivery_no,
                    );
                }
            });
        }

        if (sessionDetail.length) {
            return {
                isChanged: true,
                session: sessionDetail,
            };
        }
        return {
            isChanged: true,
            session: sessionDetail,
        };
    }
    //delete attached links
    const removedQuestionIds = [];
    for (const question of questions) {
        if (
            question &&
            question.sessionId &&
            misMatchSessions.includes(question.sessionId.toString())
        ) {
            removedQuestionIds.push(convertToMongoObjectId(question._id));
            for (const attachments of question.options) {
                for (const links of attachments.attachments) {
                    await deleteAttachment(links.link);
                }
            }
            for (const mainAttachments of question.attachments) {
                await deleteAttachment(mainAttachments.link);
            }
        }
    }
    if (removedQuestionIds.length) {
        await dsDeleteAll(Question, { _id: { $in: removedQuestionIds } });
    }
    return removedQuestionIds;
};

// delete activities
exports.deleteActivities = async (req, res) => {
    try {
        const {
            body: { questionId, optionId, feedback, type },
            params: { id },
        } = req;
        let result;
        if (questionId) {
            const { options, attachments } = await Question.findOne(
                { _id: convertToMongoObjectId(questionId) },
                { options: 1, attachments: 1 },
            );
            if (optionId) {
                const attachment = options.find((option) => cs(option._id) === cs(optionId));
                if (attachment.attachments && attachment.attachments.length)
                    await deleteAttachment(attachment.attachments[0]);
                result = await dsCustomUpdate(
                    Question,
                    { _id: convertToMongoObjectId(questionId) },
                    { $pull: { options: { _id: convertToMongoObjectId(optionId) } } },
                );
            } else if (!feedback) {
                if (attachments.length) await deleteAttachment(attachments[0].link);
                result = await dsCustomUpdate(
                    Question,
                    { _id: convertToMongoObjectId(questionId) },
                    { $set: { isDeleted: false, attachments: [] } },
                );
            } else if (questionId && feedback) {
                result = await dsCustomUpdate(
                    Question,
                    { _id: convertToMongoObjectId(questionId) },
                    { $set: { feedback: [] } },
                );
            }
        } else if (type === SCHEDULE) {
            result = await dsCustomUpdate(
                Activities,
                { _id: convertToMongoObjectId(id) },
                {
                    $unset: {
                        type: 1,
                        schedule: 1,
                        sessionId: 1,
                        scheduleIds: 1,
                        studentGroupId: 1,
                    },
                },
            );
        } else result = await dsDeleteOne(Activities, id);
        if (!result.success)
            return sendResponseWithRequest(req, res, 200, false, req.t('DELETE_ERROR'));
        return sendResponseWithRequest(req, res, 200, true, req.t('DELETED_SUCCESSFULLY'));
    } catch (error) {
        return sendResponseWithRequest(req, res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

// question answer update by student
exports.questionAnsweredByStudent = async (req, res) => {
    try {
        const {
            body: { activityId, questions: studentQuestions },
            params: { id: userId },
        } = req;
        let questions = studentQuestions;
        //to find duplicate answers
        questions = questions.reduce((acc, current) => {
            const x = acc.find(
                (item) =>
                    item._questionId &&
                    item._questionId.toString() === current._questionId.toString(),
            );
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        let updateData;
        let arrayFilters = {};
        const activity = await Activities.findOne({
            _id: convertToMongoObjectId(activityId),
            'students._studentId': convertToMongoObjectId(userId),
        });
        if (activity && activity.students && activity.students.length) {
            const activityStudents = activity.students;
            const studentAnsweredQuestions = activityStudents.find(
                (activityStudent) => activityStudent._studentId.toString() === userId.toString(),
            );
            if (studentAnsweredQuestions) {
                let answeredQuestions = studentAnsweredQuestions.questions;
                answeredQuestions = answeredQuestions.map((answeredQuestion) =>
                    answeredQuestion._questionId.toString(),
                );
                questions = questions.filter(
                    (question) => !answeredQuestions.includes(question._questionId.toString()),
                );
            }
            updateData = { 'students.$[j].questions': { $each: questions } };
            arrayFilters = [{ 'j._studentId': convertToMongoObjectId(userId) }];
            await Activities.updateOne(
                { _id: convertToMongoObjectId(activityId) },
                { $push: updateData },
                { arrayFilters },
            );
        } else {
            updateData = {
                students: {
                    _studentId: convertToMongoObjectId(userId),
                    questions,
                },
            };
            await Activities.updateOne(
                { _id: convertToMongoObjectId(activityId) },
                { $push: updateData },
            );
        }

        //  dead code to be removed
        // if (!activity) {
        //     bulkUpdate.push({
        //         updateOne: {
        //             filter: { _id: convertToMongoObjectId(activityId) },
        //             update: {
        //                 $push: {
        //                     students: {
        //                         _studentId: convertToMongoObjectId(userId),
        //                         questions,
        //                     },
        //                 },
        //             },
        //         },
        //     });
        // } else {
        //     questions.forEach((question) => {
        //         bulkUpdate.push({
        //             updateOne: {
        //                 filter: { _id: convertToMongoObjectId(activityId) },
        //                 update: {
        //                     $push: { 'students.$[j].questions': question },
        //                 },
        //                 arrayFilters: [{ 'j._studentId': convertToMongoObjectId(userId) }],
        //             },
        //         });
        //     });
        // }

        // if (bulkUpdate.length > 0) {
        //     await Activities.bulkWrite(bulkUpdate)
        //         .then()
        //         .catch((e) => {
        //             return res
        //                 .status(500)
        //                 .send(response_function(res, 422, false, req.t('UPDATE_ERROR'), e.message));
        //         });
        // }

        // const {
        //     socketEventStaffId: staffEventId,
        //     staffStartWithExam,
        //     students: activityStudents,
        //     studentCompletedQuiz,
        //     scheduleIds,
        // } = await Activities.findOne({
        //     _id: convertToMongoObjectId(activityId),
        // });
        // bulkUpdates = [];
        // const totalStudents = activityStudents;

        // const studentCheck = studentCompletedQuiz.find(
        //     (studentCompleted) => studentCompleted.toString() === userId.toString(),
        // );

        // if (staffStartWithExam === TIME && !studentCheck) {
        //     bulkUpdates.push({
        //         updateOne: {
        //             filter: { _id: convertToMongoObjectId(activityId) },
        //             update: {
        //                 $push: { studentCompletedQuiz: convertToMongoObjectId(userId) },
        //             },
        //         },
        //     });
        // }
        // if (bulkUpdates.length > 0) {
        //     await Activities.bulkWrite(bulkUpdates)
        //         .then()
        //         .catch((e) => {
        //             return res
        //                 .status(500)
        //                 .send(response_function(res, 422, false, req.t('UPDATE_ERROR'), e.message));
        //         });
        // }

        // let staffResult;
        // // if staff started exam with time based
        // if (staffStartWithExam === TIME) {
        //     staffResult = {
        //         examOnLive: true,
        //         data: await ({ activityId, totalStudents }),
        //     };
        // } else if (staffStartWithExam === ONE_BY_ONE) {
        //     staffResult = {
        //         examOnLive: true,
        //         data: await getStaffOneByOneResult({ activityId, totalStudents }),
        //     };
        // }
        // const sendSocketData = [];
        // const courseScheduleIds = scheduleIds.map((scheduleId) =>
        //     convertToMongoObjectId(scheduleId),
        // );
        // let courseSchedules = await CourseSchedule.find(
        //     {
        //         _id: { $in: courseScheduleIds },
        //         isDeleted: false,
        //         isActive: true,
        //     },
        //     { staffs: 1 },
        // ).lean();
        // courseSchedules = courseSchedules.map((courseSchedule) => courseSchedule.staffs);
        // // eslint-disable-next-line no-sequences
        // let staffIds = courseSchedules.reduce((r, e) => (r.push(...e), r), []);
        // staffIds = staffIds.map((staffId) => staffId._staff_id.toString());
        // staffIds = [...new Set(staffIds)];
        // if (staffIds && staffIds.length) {
        //     for (const staffId of staffIds) {
        //         eventId = staffId;
        //         data = { activity: JSON.stringify(await getActivity(activityId, staffId)) };
        //         sendSocketData.push({ eventId, data });
        //     }
        // }
        // eventId = staffEventId;
        // data = JSON.stringify(staffResult);
        // sendSocketData.push({ eventId, data });
        // if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('UPDATED_SUCCESSFULLY')));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

const sendActivityStartNotification = async (activity) => {
    try {
        const { _id: activityId, sessionId, scheduleIds, name, staffStartWithExam } = activity;
        const quizType = capitalizeFirstLetter(activity.quizType.toLowerCase());
        let totalStudents;
        if (scheduleIds && scheduleIds.length) {
            const mergedStudents = [];
            let staffIds = [];
            for (const scheduleIdEntry of scheduleIds) {
                const {
                    _id: scheduleId,
                    merge_status,
                    merge_with,
                    students,
                    staffs,
                } = scheduleIdEntry;
                let scheduleStudents;
                staffId = staffs.map((staff) => {
                    staff._id = staff._staff_id;
                    return staff;
                });
                staffIds = staffIds.concat(staffId);
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const schedules = await CourseSchedule.find({
                            _id: { $in: scheduleIds },
                            isDeleted: false,
                            isActive: true,
                        });
                        scheduleStudents = schedules.map((schedule) => schedule.students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    }
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.concat(students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                } else {
                    scheduleStudents = students;
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.map((scheduleStudent) => {
                        const { name, status, _id } = scheduleStudent;
                        return { name, status, _id, scheduleId };
                    });
                    mergedStudents.push(scheduleStudents);
                }
            }
            // eslint-disable-next-line prefer-spread
            totalStudents = [].concat.apply([], mergedStudents);
            // different calendar and year and level based courses
            totalStudents = totalStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            const totalStudentIds = totalStudents.map((totalStudent) =>
                convertToMongoObjectId(totalStudent._id),
            );
            let studentDetails = await User.find(
                {
                    _id: { $in: totalStudentIds },
                    $or: [{ fcm_token: { $exists: true } }, { web_fcm_token: { $exists: true } }],
                    device_type: { $exists: true },
                },
                {
                    name: 1,
                    mobile: 1,
                    biometric_data: 1,
                    device_type: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    socketEventId: 1,
                    user_type: 1,
                },
            );
            let studentSocketId = studentDetails.map((student) => {
                return {
                    socketEventId: student.socketEventId,
                    user_type: student.user_type,
                    _id: student._id,
                    userId: student._id,
                };
            });

            studentDetails = studentDetails.map((studentDetail) => {
                const { device_type, fcm_token, web_fcm_token, _id } = studentDetail;
                return { device_type, token: fcm_token, web_fcm_token, _user_id: _id };
            });
            if (studentDetails.length) {
                const title = `${quizType} Started`;
                for (const studentDetail of studentDetails) {
                    let message = name + '\n';
                    const {
                        token: fcm_token,
                        web_fcm_token,
                        _user_id: studentId,
                        device_type,
                    } = studentDetail;
                    const schedule = scheduleIds.find((scheduleId) =>
                        scheduleId.students.find(
                            (student) => student._id.toString() === studentId.toString(),
                        ),
                    );

                    if (schedule) {
                        const {
                            _id: scheduleId,
                            _course_id,
                            year_no,
                            level_no,
                            _program_id,
                            term,
                            _institution_calendar_id,
                            merge_status,
                            type,
                            course_name,
                            program_name,
                            rotation,
                            rotation_count,
                            session,
                        } = schedule;

                        message += staffStartWithExam === 'TIME' ? 'Standard' : 'One by one';
                        message += ' ' + quizType;
                        message += '\n' + program_name + ' • ' + level_no + ' • ' + course_name;
                        const pushData = {
                            Title: title,
                            Body: message,
                            ClickAction: 'activity_start',
                            ScheduleId: scheduleId,
                            Session: schedule.session ? session._session_id : undefined,
                            programId: _program_id,
                            institutionCalendarId: _institution_calendar_id,
                            yearNo: year_no,
                            levelNo: level_no,
                            term,
                            mergeStatus: merge_status,
                            mergeType: type,
                            CourseId: _course_id,
                            activityId,
                            StaffStartWithExam: staffStartWithExam,
                            _id: scheduleId,
                            courseId: _course_id,
                            notificationType: 'activity_start',
                            rotation,
                            rotation_count,
                            type: 'activity',
                        };
                        if (fcm_token) {
                            sendNotificationPush(fcm_token, pushData, device_type);
                        }
                        if (web_fcm_token) {
                            sendNotificationPush(web_fcm_token, pushData, device_type);
                        }
                    }
                }
                if (activity.scheduleIds && activity.scheduleIds.length) {
                    const scheduleIds = activity.scheduleIds.map((scheduleId) => scheduleId._id);
                    activity.scheduleIds = scheduleIds;
                }
                if (staffIds && staffIds.length) {
                    studentSocketId = studentSocketId.concat(staffIds);
                }
                activity.studentCompletedQuiz = false;
                activity.totalStudentAnsweredCount = 0;
                activity.studentCorrectAnsweredCount = 0;
                activity.totalStudentCount = totalStudents.length;
                activity.answeredCount = 0;
                if (studentSocketId && studentSocketId.length)
                    sendDashboardNotification(studentSocketId, activity);
            }
            if (scheduleIds && scheduleIds.length) {
                for (const scheduleId of scheduleIds) {
                    const users = totalStudents.filter((totalStudent) => {
                        if (totalStudent.scheduleId.toString() === scheduleId._id.toString()) {
                            return {
                                _id: totalStudent._id,
                            };
                        }
                    });
                    const data = {
                        users,
                        staffStartWithExam,
                        rotation: scheduleId.rotation,
                        rotation_count: scheduleId.rotation_count,
                        title: `${quizType} Started`,
                        description:
                            name +
                            '\n' +
                            `${
                                staffStartWithExam === 'TIME' ? 'Standard' : 'One by one'
                            } ${quizType}` +
                            '\n' +
                            scheduleId.program_name +
                            ' • ' +
                            scheduleId.level_no +
                            ' • ' +
                            scheduleId.course_name,
                        buttonAction: 'activity_start',
                        courseId: scheduleId._course_id,
                        activityId,
                        notificationType: 'activity_start',
                        programId: scheduleId._program_id,
                        institutionCalendarId: scheduleId._institution_calendar_id,
                        yearNo: scheduleId.year_no,
                        levelNo: scheduleId.level_no,
                        term: scheduleId.term,
                        mergeStatus: scheduleId.merge_status,
                        mergeType: scheduleId.type,
                        sessionId: scheduleId.session ? scheduleId.session._session_id : undefined,
                        scheduleId: scheduleId ? scheduleId._id : undefined,
                        type: 'activity',
                    };
                    await insert(Notification, data);
                }
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};

// quiz exam start by staff
exports.quizStartByStaff = async (req, res) => {
    try {
        const {
            body: {
                activityId,
                sessionId,
                scheduleIds,
                staffStartWithExam,
                questionId,
                time,
                courseAdmin,
            },
            params: { id: userId },
        } = req;
        let updateData;
        // if check activity already exists
        const activityQuery = {
            _id: convertToMongoObjectId(activityId),
            isDeleted: false,
            $or: [
                {
                    scheduleIds: { $exists: true, $ne: null },
                    sessionId: { $exists: true, $ne: null },
                },
            ],
        };
        const activity = await Activities.findOne(activityQuery);
        if (activity && activity.scheduleIds && activity.scheduleIds.length && !questionId)
            return sendResponse(res, 200, false, req.t('ALREADY_THIS_QUIZ_SCHEDULED'), null);
        //if (activity) return sendResponse(res, 200, false, 'Already one quiz started', updateData);
        if (questionId) {
            updateData = { 'questions.$[i].questionMoved': true };
            if (courseAdmin === true) {
                updateData.courseAdmin = true;
            }
            const data = { $set: updateData };
            const { success } = await dsCustomUpdate(
                Activities,
                { _id: convertToMongoObjectId(activityId) },
                data,
                [{ 'i._id': convertToMongoObjectId(questionId) }],
            );
            await moveNextQuestion({
                activityId,
                questionId,
            });
            updateData = { questionMoved: true };
            if (!success) sendResponse(res, 200, false, DS_UPDATE_FAILED);
        } else {
            const updateQuery = {
                _id: convertToMongoObjectId(activityId),
                // _sessionId: convertToMongoObjectId(sessionId),
            };
            const { staffEventId, studentEventId } = await this.createSocketEvent({
                activityId,
                scheduleIds,
                staffStartWithExam,
                time,
                userId,
            });
            const currentTime = timestampNow();
            let endTime = currentTime + parseInt(time);
            endTime = new Date(endTime);
            const seconds = endTime.getSeconds();
            endTime = new Date(
                endTime.getFullYear() +
                    '-' +
                    (endTime.getMonth() + 1) +
                    '-' +
                    endTime.getDate() +
                    ' ' +
                    endTime.getHours() +
                    ':' +
                    endTime.getMinutes(),
            );
            endTime = new Date(endTime);

            const getMergeSchedules = await CourseSchedule.find(
                {
                    _id: { $in: scheduleIds },
                },
                {
                    merge_with: 1,
                    merge_status: 1,
                    _id: 1,
                },
            );
            const mergedSessions = [];
            for (const getMergeSchedule of getMergeSchedules) {
                const { merge_status, merge_with } = getMergeSchedule;
                if (merge_status) {
                    const mergedscheduleIds = merge_with.map((mergeWith) =>
                        mergedSessions.push(mergeWith.schedule_id.toString()),
                    );
                }
            }
            const courseScheduleIds = [...scheduleIds, ...mergedSessions];
            updateData = {
                status: STARTED,
                socketEventStaffId: staffEventId,
                socketEventStudentId: studentEventId,
                quizStartedBy: convertToMongoObjectId(userId),
                staffStartWithExam,
                scheduleIds: courseScheduleIds,
                sessionId,
                startTime: currentTime,
            };
            if (courseAdmin === true) {
                updateData.courseAdmin = true;
            }

            // if staff started exam with time based
            if (staffStartWithExam === TIME) {
                updateData.setQuizTime = time;
                if (time !== 0) {
                    updateData.endTime = endTime;
                    updateData.seconds = seconds;
                }
            }
            const data = { $set: updateData };
            const { success } = await dsCustomUpdate(Activities, updateQuery, data);
            if (!success) return sendResponse(res, 200, false, req.t('UPDATE_ERROR'));

            const query = {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
                $or: [{ scheduleIds: { $exists: true, $ne: null } }],
            };
            const activityEntry = await Activities.findOne(query)
                .populate({
                    path: 'courseId',
                    select: { course_code: 1, course_name: 1 },
                })
                .lean();
            if (activityEntry) {
                const { scheduleIds, createdBy, questions } = activityEntry;
                const user = await User.findOne(
                    { _id: convertToMongoObjectId(createdBy) },
                    { _id: 1, name: 1 },
                ).lean();
                const courseSchedules = await CourseSchedule.find(
                    {
                        _id: { $in: scheduleIds },
                    },
                    {
                        _institution_calendar_id: 1,
                        _program_id: 1,
                        student_groups: 1,
                        session: 1,
                        year_no: 1,
                        level_no: 1,
                        term: 1,
                        _course_id: 1,
                        schedule_date: 1,
                        students: 1,
                        staffs: 1,
                        merge_with: 1,
                        merge_status: 1,
                        infra_name: 1,
                        type: 1,
                        course_name: 1,
                        course_code: 1,
                        program_name: 1,
                        rotation: 1,
                        rotation_count: 1,
                    },
                ).lean();
                if (courseSchedules) {
                    activityEntry.scheduleIds = courseSchedules;
                }
                if (user) {
                    activityEntry.createdBy = user;
                }
                activityEntry.totalQuestionCount = questions.length;
            }
            if (activityEntry) sendActivityStartNotification(activityEntry);
        }
        updateData.startTime = new Date();
        updateData.activityId = activityId;
        return sendResponse(res, 200, true, req.t('UPDATED_SUCCESSFULLY'), updateData);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.message);
    }
};

sendActivityEvent = async (studentSocketId, activityId) => {
    const sendSocketData = [];
    for (const socketId of studentSocketId) {
        const { _id: userId } = socketId;
        if (userId) {
            const eventId = userId;
            const data = JSON.stringify(await getActivity(activityId, userId));
            sendSocketData.push({ eventId, data });
        }
    }
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
};

// create socket event
exports.createSocketEvent = async ({
    activityId,
    scheduleIds,
    staffStartWithExam,
    time = '',
    userId,
}) => {
    const scheduleConvertIds = scheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));
    let totalStudents;
    const courseSchedules = await CourseSchedule.find({
        _id: { $in: scheduleConvertIds },
        isDeleted: false,
        isActive: true,
    });
    const mergedStudents = [];
    for (const courseSchedule of courseSchedules) {
        const { students, merge_status, merge_with } = courseSchedule;
        let scheduleStudents;
        if (merge_status) {
            const scheduleIds = merge_with.map((mergeWith) =>
                convertToMongoObjectId(mergeWith.schedule_id),
            );
            if (scheduleIds.length) {
                const schedules = await CourseSchedule.find({
                    _id: { $in: scheduleIds },
                    isDeleted: false,
                    isActive: true,
                });
                scheduleStudents = schedules.map((schedule) => schedule.students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            }
        }
        if (scheduleStudents && scheduleStudents.length) {
            scheduleStudents = scheduleStudents.concat(students);
            // eslint-disable-next-line prefer-spread
            scheduleStudents = [].concat.apply([], scheduleStudents);
        } else {
            scheduleStudents = students;
        }
        if (scheduleStudents && scheduleStudents.length) {
            mergedStudents.push(scheduleStudents);
        } else {
            mergedStudents.push(students);
        }
    }
    // eslint-disable-next-line prefer-spread
    totalStudents = [].concat.apply([], mergedStudents);
    // different calendar and year and level based courses
    totalStudents = totalStudents.reduce((acc, current) => {
        const x = acc.find((item) => item._id.toString() === current._id.toString());
        if (!x) {
            return acc.concat([current]);
        }
        return acc;
    }, []);
    const currentTime = timestampNow();
    let intervalTime;
    let staffResult;
    // if staff started exam with time based
    if (staffStartWithExam === TIME && time) {
        intervalTime = currentTime + time;
        // staff data
        staffResult = {
            examOnLive: true,
            data: await getStaffTimeBasedResult({ activityId, totalStudents }),
        };
    } else if (staffStartWithExam === ONE_BY_ONE) {
        staffResult = {
            examOnLive: true,
            data: await getStaffOneByOneResult({ activityId, totalStudents }),
        };
    }
    const staffEventId = 'staff_' + activityId + '_' + currentTime;
    const studentEventId = 'student_' + activityId + '_' + currentTime;
    let eventId;
    let data;

    // student quiz status
    const studentStatus = { examOnLive: true };
    const sendSocketData = [];
    // student response
    eventId = studentEventId;
    data = JSON.stringify(studentStatus);
    sendSocketData.push({ eventId, data });

    eventId = staffEventId;
    data = JSON.stringify(staffResult);
    sendSocketData.push({ eventId, data });
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    return {
        staffEventId,
        studentEventId,
    };
};

// quiz stop by staff
exports.quizStopByStaff = async (req, res) => {
    try {
        const {
            body: { activityId },
            params: { id: userId },
        } = req;

        const {
            socketEventStudentId: studentEventId,
            socketEventStaffId: staffEventId,
            scheduleIds,
            status,
            _institution_calendar_id,
        } = await Activities.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
            },
            {
                endTime: 1,
                socketEventStudentId: 1,
                socketEventStaffId: 1,
                sessionId: 1,
                scheduleIds: 1,
                status: 1,
                _institution_calendar_id: 1,
            },
        );

        if (status === COMPLETED) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('QUIZ_ALREADY_STOPPED')));
        }

        const updateQuery = {
            _id: convertToMongoObjectId(activityId),
        };

        const updateData = {
            quizStopBy: convertToMongoObjectId(userId),
            endTime: timestampNow(),
            status: COMPLETED,
        };

        let data = {
            $set: updateData,
        };
        const { success } = await dsCustomUpdate(Activities, updateQuery, data);
        if (!success) {
            return res.status(200).send(response_function(res, 200, false, DS_UPDATE_FAILED));
        }

        const studentStatus = { examOnLive: false };
        let eventId;

        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );
        let totalStudents;
        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        const mergedStaffs = [];
        for (const courseSchedule of courseSchedules) {
            const { students, staffs, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            } else {
                scheduleStudents = students;
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
            mergedStaffs.push(staffs);
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // eslint-disable-next-line prefer-spread
        const totalStaffs = [].concat.apply([], mergedStaffs);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        const studentIds = totalStudents.map((student) => convertToMongoObjectId(student._id));
        const staffIds = totalStaffs.map((staff) => convertToMongoObjectId(staff._staff_id));
        const userIds = studentIds.concat(staffIds);
        const userDetails = await User.find(
            { _id: { $in: userIds } },
            { device_type: 1, fcm_token: 1, socketEventId: 1, user_type: 1 },
        );
        const studentSocketId = userDetails.map((student) => {
            return {
                socketEventId: student.socketEventId,
                user_type: student.user_type,
                _id: student._id,
            };
        });
        const sendSocketData = [];
        // student response
        eventId = studentEventId;
        data = JSON.stringify(studentStatus);
        sendSocketData.push({ eventId, data });

        // staff data
        const staffResult = {
            examOnLive: false,
            data: await getStaffTimeBasedResult({ activityId, totalStudents }),
        };
        eventId = staffEventId;
        data = JSON.stringify(staffResult);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        sendNotificationByActivityId(activityId);
        // update cache data
        await clearItem('getAllCompletedActivities');
        await allCompletedActivities(_institution_calendar_id);
        return res.status(200).send(response_function(res, 200, true, DS_UPDATED, updateData));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

quizEndByTimeOut = async ({ activityId, currentTimeEntry, quizStartedBy }) => {
    const updateQuery = {
        _id: convertToMongoObjectId(activityId),
    };
    const updateData = {
        quizStopBy: convertToMongoObjectId(quizStartedBy),
        endTime: currentTimeEntry,
        status: COMPLETED,
    };
    let data = { $set: updateData };
    const { success } = await dsCustomUpdate(Activities, updateQuery, data);
    if (success) {
        sendNotificationByActivityId(activityId);
        const {
            socketEventStudentId: studentEventId,
            socketEventStaffId: staffEventId,
            sessionId,
            scheduleIds,
            _institution_calendar_id,
        } = await Activities.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
            },
            {
                endTime: 1,
                socketEventStudentId: 1,
                socketEventStaffId: 1,
                sessionId: 1,
                scheduleIds: 1,
                _institution_calendar_id: 1,
            },
        );
        const studentStatus = { examOnLive: false };
        let eventId;
        let totalStudents;
        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );
        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        const mergedStaffs = [];
        for (const courseSchedule of courseSchedules) {
            const { students, staffs, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            } else {
                scheduleStudents = students;
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
            mergedStaffs.push(staffs);
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // eslint-disable-next-line prefer-spread
        const totalStaffs = [].concat.apply([], mergedStaffs);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        studentIds = totalStudents.filter((student) => student._id);
        staffIds = totalStaffs.filter((staff) => staff._staff_id);
        const userIds = studentIds.concat(staffIds);
        const userDetails = await User.find(
            { _id: { $in: userIds } },
            { device_type: 1, fcm_token: 1, socketEventId: 1, user_type: 1 },
        );
        const studentSocketId = userDetails.map((student) => {
            return {
                socketEventId: student.socketEventId,
                user_type: student.user_type,
                _id: student._id,
            };
        });

        if (studentSocketId && studentSocketId.length > 0) {
            sendActivityEvent(studentSocketId, activityId);
        }
        const sendSocketData = [];
        // student response
        eventId = studentEventId;
        data = JSON.stringify(studentStatus);
        sendSocketData.push({ eventId, data });

        // staff data
        const staffResult = {
            examOnLive: false,
            data: await getStaffTimeBasedResult({ activityId, totalStudents: [] }),
        };
        eventId = staffEventId;
        data = JSON.stringify(staffResult);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);

        // update cache data
        await clearItem('getAllCompletedActivities');
        await allCompletedActivities(_institution_calendar_id);
    }
};

// staff move next question
moveNextQuestion = async ({ activityId, questionId }) => {
    // const updateData = {
    //     'questions.$[i].questionMoved': true,
    // };

    // let data = {
    //     $set: updateData,
    // };
    // const { success } = await dsCustomUpdate(
    //     Activities,
    //     { _id: convertToMongoObjectId(activityId) },
    //     data,
    //     [{ 'i._id': convertToMongoObjectId(questionId) }],
    // );
    const {
        socketEventStudentId: studentEventId,
        socketEventStaffId: staffEventId,
        questions,
    } = await Activities.findOne(
        {
            _id: convertToMongoObjectId(activityId),
            isDeleted: false,
        },
        { endTime: 1, socketEventStudentId: 1, socketEventStaffId: 1, questions: 1 },
    );

    const question = questions.find((question) => !question.questionMoved);

    const studentStatus = {
        examOnLive: true,
        movedToNextQuestion: true,
        acceptQuestionResponse: question.acceptQuestionResponse,
        nextQuestionId: question ? question._id : undefined,
    };
    const sendSocketData = [];
    let eventId;
    // student response
    eventId = studentEventId;
    data = JSON.stringify(studentStatus);
    sendSocketData.push({ eventId, data });

    // staff data
    const staffResult = {
        examOnLive: true,
        movedToNextQuestion: true,
        acceptQuestionResponse: question.acceptQuestionResponse,
        nextQuestionId: question ? question._id : undefined,
        // data: await getStaffTimeBasedResult({ activityId }),
    };
    eventId = staffEventId;
    data = JSON.stringify(staffResult);
    sendSocketData.push({ eventId, data });
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    return questions;
};

// get staff time result (quiz)
getStaffTimeBasedResult = async ({ activityId, totalStudents }) => {
    const { students, questions } = await Activities.findOne(
        { _id: convertToMongoObjectId(activityId) },
        { students: 1, questions: 1 },
    )
        .populate({ path: 'students._studentId', select: { name: 1, user_id: 1 } })
        .exec();
    let questionIds;
    questionIds = questions.sort((a, b) => {
        return a.order - b.order;
    });
    questionIds = questions.map((question) => question._id);
    const questionEntry = await Question.find({
        _id: { $in: questionIds },
    });
    const scheduleIds = questionEntry.map((question) => question.sessionId);
    const courseSchedules = await CourseSchedule.find({
        'session._session_id': { $in: scheduleIds },
        isDeleted: false,
        isActive: true,
    });

    if (questionEntry && questionEntry.length > 0) {
        let studentQuestion = questionEntry.map((question) => {
            const activityQuestion = questions.find(
                (questionEntry) => questionEntry._id.toString() === question._id.toString(),
            );
            const {
                _id,
                text,
                options,
                _activityId,
                questionType,
                attachments,
                feedback,
                sloIds,
                taxonomyIds,
                sessionId,
                surveyQuestionType,
                maxCharacterLimit,
            } = question;
            let studentWithQuestion;
            let studentAttendQuestionCount = 0;
            if (students && students.length > 0) {
                studentWithQuestion = students.filter((student) => {
                    const { questions: studentQuestions } = student;
                    if (
                        studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._questionId &&
                                (studentEntry._optionId || studentEntry.textAnswer) &&
                                studentEntry._questionId.toString() === _id.toString(),
                        )
                    ) {
                        studentAttendQuestionCount++;
                        return true;
                    }
                    return false;
                });
            }

            const studentOptions = options.map((option) => {
                const {
                    _id: optionId,
                    text: optionText,
                    answer,
                    attachments: optionAttachments,
                } = option;
                let studentWithOption;
                if (studentWithQuestion && studentWithQuestion.length > 0) {
                    studentWithOption = studentWithQuestion.filter((student) => {
                        const { questions: studentQuestions } = student;
                        if (
                            studentQuestions.find(
                                (studentEntry) =>
                                    studentEntry._optionId &&
                                    studentEntry._optionId.toString() === optionId.toString(),
                            )
                        ) {
                            return true;
                        }
                        return false;
                    });
                }
                const studentCount =
                    studentWithOption && studentWithOption.length > 0
                        ? studentWithOption.length
                        : 0;
                const percentage =
                    ((studentCount / studentAttendQuestionCount) * 100).toFixed() !== 'NaN'
                        ? parseFloat((studentCount / studentAttendQuestionCount) * 100).toFixed(2)
                        : 0;
                return {
                    _id: optionId,
                    text: optionText,
                    answer,
                    attachments: optionAttachments,
                    studentAnsweredCount: studentCount,
                    totalStudentAnswered: studentAttendQuestionCount,
                    percentage,
                };
            });
            const studentTextAnswer = [];
            if (surveyQuestionType === OPEN_ENDED) {
                if (studentWithQuestion && studentWithQuestion.length > 0) {
                    studentWithQuestion.forEach((student) => {
                        const { questions: studentQuestions } = student;
                        const matchedQuestions = studentQuestions.find(
                            (studentEntry) =>
                                !studentEntry._optionId &&
                                studentEntry.textAnswer &&
                                studentEntry._questionId.toString() === _id.toString(),
                        );
                        if (matchedQuestions) {
                            studentTextAnswer.push({
                                studentId: student._studentId,
                                _questionId: matchedQuestions._questionId,
                                textAnswer: matchedQuestions.textAnswer,
                            });
                        }
                    });
                }
            }
            let movedStatus = false;
            if (activityQuestion && activityQuestion.questionMoved) {
                movedStatus = true;
            }
            let acceptQuestionResponse = false;
            if (activityQuestion && activityQuestion.acceptQuestionResponse) {
                acceptQuestionResponse = activityQuestion.acceptQuestionResponse;
            }
            let sessionType;
            const sessionDatas = courseSchedules.find(
                (schedulesEntry) =>
                    sessionId &&
                    schedulesEntry.session &&
                    schedulesEntry.session._session_id &&
                    schedulesEntry.session._session_id.toString() === sessionId.toString(),
            );
            if (sessionDatas) {
                sessionType = sessionDatas.session;
            }
            return {
                _id,
                text,
                options: studentOptions,
                textAnswers: studentTextAnswer,
                _activityId,
                questionType,
                sloIds,
                taxonomyIds,
                feedback,
                attachments,
                studentAnsweredCount: studentAttendQuestionCount,
                questionMoved: movedStatus,
                acceptQuestionResponse,
                sessionId,
                sessionType,
                sessionDetails: sessionType,
                surveyQuestionType,
                maxCharacterLimit,
            };
        });
        studentQuestion = questionIds.map((questionId) => {
            const questionDetails = studentQuestion.find(
                (question) => question._id.toString() === questionId.toString(),
            );
            return questionDetails;
        });
        // format question
        const formatQuestion = formatQuestionsWithAttachmentLink(studentQuestion);
        return formatQuestion;
    }
    return [];
};

// get staff one by one result (quiz)
getStaffOneByOneResult = async ({ activityId, totalStudents }) => {
    const { students, questions: activityQuestions } = await Activities.findOne(
        { _id: convertToMongoObjectId(activityId) },
        { students: 1, questions: 1 },
    );
    let questionIds;
    questionIds = activityQuestions.sort((a, b) => {
        return a.order - b.order;
    });
    questionIds = activityQuestions.map((question) => question._id);
    const questions = await Question.find({
        _id: { $in: questionIds },
    });

    let studentQuestion = questions.map((question) => {
        const {
            _id,
            text,
            options,
            _activityId,
            questionType,
            feedback,
            sloIds,
            taxonomyIds,
            attachments,
        } = question;
        const activityQuestion = activityQuestions.find(
            (questionEntry) => questionEntry._id.toString() === question._id.toString(),
        );
        let studentWithQuestion;
        let studentAttendQuestionCount = 0;
        if (students && students.length > 0) {
            studentWithQuestion = students.filter((student) => {
                const { questions: studentQuestions } = student;
                if (
                    studentQuestions.find(
                        (studentEntry) =>
                            studentEntry._questionId &&
                            studentEntry._optionId &&
                            studentEntry._questionId.toString() === _id.toString(),
                    )
                ) {
                    studentAttendQuestionCount++;
                    return true;
                }
                return false;
            });
        }

        const studentOptions = options.map((option) => {
            const {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
            } = option;
            let studentWithOption;
            if (studentWithQuestion && studentWithQuestion.length > 0) {
                studentWithOption = studentWithQuestion.filter((student) => {
                    const { questions: studentQuestions } = student;
                    if (
                        studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._optionId &&
                                studentEntry._optionId.toString() === optionId.toString(),
                        )
                    ) {
                        return true;
                    }
                    return false;
                });
            }
            const studentCount =
                studentWithOption && studentWithOption.length > 0 ? studentWithOption.length : 0;
            const percentage =
                ((studentCount / studentAttendQuestionCount) * 100).toFixed() !== 'NaN'
                    ? parseFloat((studentCount / studentAttendQuestionCount) * 100).toFixed(2)
                    : 0;
            return {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
                studentAnsweredCount: studentCount,
                totalStudentAnswered: studentAttendQuestionCount,
                percentage,
            };
        });
        let movedStatus = false;
        if (activityQuestion && activityQuestion.questionMoved) {
            movedStatus = true;
        }
        let acceptQuestionResponse = false;
        if (activityQuestion && activityQuestion.acceptQuestionResponse) {
            acceptQuestionResponse = activityQuestion.acceptQuestionResponse;
        }
        return {
            _id,
            text,
            options: studentOptions,
            questionType,
            feedback,
            sloIds,
            taxonomyIds,
            attachments,
            studentAnsweredCount: studentAttendQuestionCount,
            questionMoved: movedStatus,
            acceptQuestionResponse,
            _activityId,
        };
    });

    studentQuestion = questionIds.map((questionId) => {
        const questionDetails = studentQuestion.find(
            (question) => question._id.toString() === questionId.toString(),
        );
        return questionDetails;
    });

    // format question
    const formatQuestion = formatQuestionsWithAttachmentLink(studentQuestion);
    return formatQuestion;
};

// get student result (quiz)
getStudentResult = async (activityId, userId) => {
    const { students, questions: activityQuestions } = await Activities.findOne(
        { _id: convertToMongoObjectId(activityId) },
        { students: 1, questions: 1 },
    ).lean();
    const studentData = students.filter(
        (studentEntry) => studentEntry._studentId.toString() === userId.toString(),
    );
    let questionIds;
    questionIds = activityQuestions.sort((a, b) => {
        return a.order - b.order;
    });
    questionIds = activityQuestions.map((question) => question._id);
    const questions = await Question.find({
        _id: { $in: questionIds },
    }).lean();
    const scheduleIds = questions.map((question) => question.sessionId);
    const courseSchedules = await CourseSchedule.find({
        'session._session_id': { $in: scheduleIds },
        isDeleted: false,
        isActive: true,
    });
    let studentQuestion = questions.map((question) => {
        const {
            _id,
            text,
            options,
            _activityId,
            questionType,
            sloIds,
            taxonomyIds,
            feedback,
            attachments,
            sessionId,
            maxCharacterLimit,
            surveyQuestionType,
        } = question;
        let studentWithQuestion;
        let studentAnswered;
        let studentAnsweredOptionId;
        let studentTextAnswer;
        if (studentData && studentData.length > 0) {
            studentWithQuestion = studentData.filter((student) => {
                const { questions: studentQuestions } = student;
                if (
                    studentQuestions.find(
                        (studentEntry) =>
                            studentEntry._questionId &&
                            studentEntry._questionId.toString() === _id.toString(),
                    )
                ) {
                    return true;
                }
                return false;
            });
            if (surveyQuestionType === OPEN_ENDED) {
                if (studentWithQuestion && studentWithQuestion.length > 0) {
                    const { questions: studentQuestions } = studentWithQuestion[0];
                    const matchedQuestions = studentQuestions.find(
                        (studentEntry) => studentEntry._questionId.toString() === _id.toString(),
                    );
                    if (matchedQuestions) {
                        studentTextAnswer = matchedQuestions.textAnswer
                            ? matchedQuestions.textAnswer
                            : null;
                    }
                }
            }
            options.forEach((option) => {
                const { _id: optionId, answer } = option;
                if (studentWithQuestion && studentWithQuestion.length > 0) {
                    const { questions: studentQuestions } = studentWithQuestion[0];
                    if (
                        studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._optionId &&
                                studentEntry._optionId.toString() === optionId.toString(),
                        )
                    ) {
                        studentAnswered = answer;
                        studentAnsweredOptionId = optionId;
                    }
                }
            });
        }
        let sessionType;
        const sessionDatas = courseSchedules.find(
            (schedulesEntry) =>
                schedulesEntry.session &&
                sessionId &&
                schedulesEntry.session._session_id &&
                schedulesEntry.session._session_id.toString() === sessionId.toString(),
        );
        if (sessionDatas) {
            sessionType = sessionDatas.session;
        }
        return {
            _id,
            text,
            options,
            questionType,
            feedback,
            sloIds,
            taxonomyIds,
            attachments,
            studentAnswered,
            studentAnsweredOptionId,
            _activityId,
            sessionId,
            sessionType,
            studentTextAnswer,
            sessionDetails: sessionType,
            surveyQuestionType,
            maxCharacterLimit,
        };
    });
    studentQuestion = questionIds.map((questionId) => {
        const questionDetails = studentQuestion.find(
            (question) => question._id.toString() === questionId.toString(),
        );
        return questionDetails;
    });
    // format question
    const formatQuestion = formatQuestionsWithAttachmentLink(studentQuestion);
    return formatQuestion;
};

// view result student and staff
exports.getResults = async (req, res) => {
    try {
        const {
            params: { id: activityId },
            query: { type, userId },
        } = req;
        let viewResults;
        const { students } = await Activities.findOne({
            _id: convertToMongoObjectId(activityId),
        });

        const totalStudents = students;
        if (type === DC_STAFF)
            viewResults = await getStaffTimeBasedResult({ activityId, totalStudents });
        if (type === DC_STUDENT) viewResults = await getStudentResult(activityId, userId);
        return sendResponse(res, 200, true, DS_UPDATED, viewResults);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// get students
exports.getStudents = async (req, res) => {
    try {
        const {
            params: { id: activityId },
        } = req;
        let totalStudents;
        const { scheduleIds, students: activityStudents } = await Activities.findOne({
            _id: convertToMongoObjectId(activityId),
        });

        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );

        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        for (const courseSchedule of courseSchedules) {
            const { students, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            } else {
                scheduleStudents = students;
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        const activityStudentIds = activityStudents.map((activityStudent) =>
            activityStudent._studentId.toString(),
        );
        let studentIds = totalStudents.map((student) => convertToMongoObjectId(student._id));
        studentIds = studentIds.filter((studentId) =>
            activityStudentIds.includes(studentId.toString()),
        );
        const Users = await User.find({
            _id: { $in: studentIds },
            isDeleted: false,
            isActive: true,
        });
        const response = [];
        totalStudents.forEach((Student) => {
            const academicId = Users.find((User) => User._id.toString() === Student._id.toString());
            if (academicId)
                response.push({
                    name: Student.name,
                    _id: Student._id,
                    status: Student.status,
                    mode: Student.mode,
                    academicId: academicId.user_id,
                });
        });

        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), response);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
// get question
formatQuestions = async (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentAnswered;
    let studentAnsweredOptionId;
    let studentTextAnswer;
    let studentCorrectAnswered = 0;
    for (const activityQuestion of activityQuestions) {
        const {
            _id,
            _activityId,
            text,
            questionType,
            attachments,
            options,
            answer,
            feedback,
            isDeleted,
            sloIds,
            taxonomyIds,
            sessionId,
            order,
            questionMoved,
            acceptQuestionResponse,
            type,
            questionViewType,
            describeYourQuestion,
            itemCategory,
            aiNoOfOptions,
            generateFeedback,
            surveyQuestionType,
            maxCharacterLimit,
        } = activityQuestion;
        const questionAttachments = [];
        // get attachments unsigned url
        if (attachments && attachments.length > 0) {
            for (const attachment of attachments) {
                const { size, _id: attachmentId, link } = attachment;
                let unsignedUrl;
                if (link) {
                    unsignedUrl = await getUnsignedUrl(link, _activityId);
                }
                questionAttachments.push({ size, _id: attachmentId, link: unsignedUrl });
            }
        }
        // get option attachments unsigned url
        const questionOptionWithAttachments = [];
        if (options && options.length > 0) {
            for (const option of options) {
                const {
                    _id: optionId,
                    attachments: optionAttachments,
                    text: optionText,
                    answer: optionAnswer,
                    order: optionOrder,
                } = option;
                const questionOptionAttachments = [];
                for (const optionAttachment of optionAttachments) {
                    const {
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link,
                    } = optionAttachment;
                    let unsignedUrl;
                    if (link) {
                        unsignedUrl = await getUnsignedUrl(link, _activityId);
                    }
                    questionOptionAttachments.push({
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link: unsignedUrl,
                    });
                }
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = optionAnswer;
                    studentAnsweredOptionId = optionId;
                    studentTextAnswer = optionText;
                }

                questionOptionWithAttachments.push({
                    _id: optionId,
                    text: optionText,
                    attachments: questionOptionAttachments,
                    answer: optionAnswer,
                    order: optionOrder,
                });
            }
        }
        if (!options.length) {
            studentTextAnswer =
                answeredQuestions &&
                answeredQuestions.find(
                    (studentEntry) => studentEntry._questionId.toString() === _id.toString(),
                );
            studentTextAnswer = studentTextAnswer ? studentTextAnswer.textAnswer : null;
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }
        let sloDetailList = [];
        if (sloIds && sloIds.length) {
            // slo name format
            const sloIdEntry = sloIds.map((sloId) => convertToMongoObjectId(sloId));
            const sloDetails = await getClosBySloIds(sloIdEntry);
            const cloDetails = await getClos(sloIdEntry);
            sloDetailList = sloIds
                .map((sloId) => {
                    const sloList = sloDetails.find((sloDetail) =>
                        sloDetail.slos.find((slo) => slo.slo_id === sloId.toString()),
                    );
                    let cloList;
                    if (cloDetails.length) {
                        cloList = cloDetails.find(
                            (cloDetail) => cloDetail._id.toString() === sloId.toString(),
                        );
                    }
                    let sloIdDetail;
                    if (sloList) {
                        sloIdDetail = sloList.slos.find((slo) => slo.slo_id === sloId.toString());
                        sloIdDetail._id = sloIdDetail.slo_id;
                        sloIdDetail.type = DS_SLO_KEY;
                    }
                    if (!sloIdDetail && cloList) {
                        sloIdDetail = {
                            _id: cloList._id,
                            name: cloList.name,
                            type: DS_CLO_KEY,
                            no: cloList.no,
                        };
                    }
                    return sloIdDetail;
                })
                .filter((item) => item); //removing undefined and null values from array using filter
        }
        let taxonomy;
        if (taxonomyIds && taxonomyIds.length) {
            // taxonomy
            const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
            taxonomy = await Taxonomy.find({ _id: { $in: taxonomyIdEntry } }, { name: 1, _id: 1 });
        }
        questions.push({
            _id,
            _activityId,
            text,
            questionType,
            attachments: questionAttachments,
            options: questionOptionWithAttachments,
            answer,
            feedback,
            isDeleted,
            sloIds: sloDetailList,
            taxonomyIds: taxonomy,
            sessionId,
            order,
            questionMoved,
            acceptQuestionResponse,
            questionAnswered,
            studentAnswered,
            studentAnsweredOptionId,
            type,
            questionViewType,
            describeYourQuestion,
            itemCategory,
            aiNoOfOptions,
            generateFeedback,
            surveyQuestionType,
            maxCharacterLimit,
            studentTextAnswer,
        });
    }
    const questionEntry = questions.sort((a, b) => {
        return a.order - b.order;
    });
    return {
        questions: questionEntry,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};
formatQuestionsWithOutAttachment = async (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentAnswered;
    let studentAnsweredOptionId;
    let studentCorrectAnswered = 0;
    for (const activityQuestion of activityQuestions) {
        const {
            _id,
            _activityId,
            text,
            questionType,
            attachments,
            options,
            answer,
            feedback,
            isDeleted,
            sloIds,
            taxonomyIds,
            sessionId,
            order,
            questionMoved,
            acceptQuestionResponse,
            type,
        } = activityQuestion;
        // get option attachments unsigned url
        const questionOptionWithAttachments = [];
        if (options && options.length > 0) {
            for (const option of options) {
                const {
                    _id: optionId,
                    attachments: optionAttachments,
                    text: optionText,
                    answer: optionAnswer,
                    order: optionOrder,
                } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = optionAnswer;
                    studentAnsweredOptionId = optionId;
                }

                questionOptionWithAttachments.push({
                    _id: optionId,
                    text: optionText,
                    attachments: optionAttachments,
                    answer: optionAnswer,
                    order: optionOrder,
                });
            }
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }
        let sloDetailList = [];
        if (sloIds && sloIds.length) {
            // slo name format
            const sloIdEntry = sloIds.map((sloId) => convertToMongoObjectId(sloId));
            const sloDetails = await getClosBySloIds(sloIdEntry);
            const cloDetails = await getClos(sloIdEntry);
            sloDetailList = sloIds
                .map((sloId) => {
                    const sloList = sloDetails.find((sloDetail) =>
                        sloDetail.slos.find((slo) => slo.slo_id === sloId.toString()),
                    );
                    let cloList;
                    if (cloDetails.length) {
                        cloList = cloDetails.find(
                            (cloDetail) => cloDetail._id.toString() === sloId.toString(),
                        );
                    }
                    let sloIdDetail;
                    if (sloList) {
                        sloIdDetail = sloList.slos.find((slo) => slo.slo_id === sloId.toString());
                        sloIdDetail._id = sloIdDetail.slo_id;
                        sloIdDetail.type = DS_SLO_KEY;
                    }
                    if (!sloIdDetail && cloList) {
                        sloIdDetail = {
                            _id: cloList._id,
                            name: cloList.name,
                            type: DS_CLO_KEY,
                            no: cloList.no,
                        };
                    }
                    return sloIdDetail;
                })
                .filter((item) => item); //removing undefined and null values from array using filter
        }
        let taxonomy;
        if (taxonomyIds && taxonomyIds.length) {
            // taxonomy
            const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
            taxonomy = await Taxonomy.find({ _id: { $in: taxonomyIdEntry } }, { name: 1, _id: 1 });
        }
        questions.push({
            _id,
            _activityId,
            text,
            questionType,
            attachments,
            options: questionOptionWithAttachments,
            answer,
            feedback,
            isDeleted,
            sloIds: sloDetailList,
            taxonomyIds: taxonomy,
            sessionId,
            order,
            questionMoved,
            acceptQuestionResponse,
            questionAnswered,
            studentAnswered,
            studentAnsweredOptionId,
            type,
        });
    }
    const questionEntry = questions.sort((a, b) => {
        return a.order - b.order;
    });
    return {
        questions: questionEntry,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

// format question with attachment link
formatQuestionsWithAttachmentLink = async (questions) => {
    const formatQuestions = [];
    for (const question of questions) {
        if (question) {
            const {
                attachments,
                options,
                _id,
                sloIds,
                textAnswers,
                taxonomyIds,
                _activityId,
                sessionId,
                isDeleted,
                feedback,
                text,
                studentAnsweredCount,
                questionMoved,
                acceptQuestionResponse,
                sessionType,
                sessionDetails,
                studentAnswered,
                studentAnsweredOptionId,
                studentTextAnswer,
                surveyQuestionType,
                maxCharacterLimit,
            } = question;
            const questionAttachments = [];
            // get attachments unsigned url
            if (attachments && attachments.length > 0) {
                for (const attachment of attachments) {
                    const { size, _id: attachmentId, link } = attachment;
                    let unsignedUrl;
                    if (link) {
                        unsignedUrl = await getUnsignedUrl(link, _activityId);
                    }
                    questionAttachments.push({ size, _id: attachmentId, link: unsignedUrl });
                }
                question.attachments = questionAttachments;
            }
            // get option attachments unsigned url
            const questionOptionWithAttachments = [];
            if (options && options.length > 0) {
                for (const option of options) {
                    const { attachments: optionAttachments } = option;
                    const questionOptionAttachments = [];
                    for (const optionAttachment of optionAttachments) {
                        const {
                            size: optionAttachmentSize,
                            _id: optionAttachmentId,
                            link,
                        } = optionAttachment;
                        let unsignedUrl;
                        if (link) {
                            unsignedUrl = await getUnsignedUrl(link, _activityId);
                        }
                        questionOptionAttachments.push({
                            size: optionAttachmentSize,
                            _id: optionAttachmentId,
                            link: unsignedUrl,
                        });
                    }

                    option.attachments = questionOptionAttachments;
                    questionOptionWithAttachments.push(option);
                }
                question.options = questionOptionWithAttachments;
            }
            let sloDetailList = sloIds;
            if (sloIds && sloIds.length) {
                // slo name format
                const sloIdEntry = sloIds.map((sloId) => convertToMongoObjectId(sloId));
                const sloDetails = await getClosBySloIds(sloIdEntry);
                const cloDetails = await getClos(sloIdEntry);
                sloDetailList = sloIds
                    .map((sloId) => {
                        const sloList = sloDetails.find((sloDetail) =>
                            sloDetail.slos.find((slo) => slo.slo_id === sloId.toString()),
                        );
                        let cloList;
                        if (cloDetails.length) {
                            cloList = cloDetails.find(
                                (cloDetail) => cloDetail._id.toString() === sloId.toString(),
                            );
                        }
                        let sloIdDetail;
                        if (sloList) {
                            sloIdDetail = sloList.slos.find(
                                (slo) => slo.slo_id === sloId.toString(),
                            );
                            sloIdDetail._id = sloIdDetail.slo_id;
                            sloIdDetail.type = DS_SLO_KEY;
                        }
                        if (!sloIdDetail && cloList) {
                            sloIdDetail = {
                                _id: cloList._id,
                                name: cloList.name,
                                type: DS_CLO_KEY,
                                no: cloList.no,
                            };
                        }
                        return sloIdDetail;
                    })
                    .filter((item) => item); //removing undefined and null values from array using filter
                question.sloIds = sloDetailList;
            }
            let taxonomy = taxonomyIds;
            if (taxonomyIds && taxonomyIds.length) {
                // taxonomy
                const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
                taxonomy = await Taxonomy.find(
                    { _id: { $in: taxonomyIdEntry } },
                    { name: 1, _id: 1 },
                );
                question.taxonomyIds = taxonomy;
            }
            formatQuestions.push({
                _id,
                isDeleted,
                sloIds: sloDetailList,
                taxonomyIds: taxonomy,
                _activityId,
                text,
                feedback,
                attachments: questionAttachments,
                sessionId,
                options: questionOptionWithAttachments,
                textAnswers,
                studentAnsweredCount,
                questionMoved,
                acceptQuestionResponse,
                sessionType,
                sessionDetails,
                studentAnswered,
                studentAnsweredOptionId,
                studentTextAnswer,
                surveyQuestionType,
                maxCharacterLimit,
            });
        }
    }
    return formatQuestions;
};

// format question with attachment link
formatQuestionsWithOutAttachmentLink = async (questions) => {
    for (const question of questions) {
        if (question) {
            const { sloIds, taxonomyIds } = question;
            if (sloIds && sloIds.length) {
                // slo name format
                const sloIdEntry = sloIds.map((sloId) => convertToMongoObjectId(sloId));
                const sloDetails = await getClosBySloIds(sloIdEntry);
                const cloDetails = await getClos(sloIdEntry);
                sloDetailList = sloIds
                    .map((sloId) => {
                        const sloList = sloDetails.find((sloDetail) =>
                            sloDetail.slos.find((slo) => slo.slo_id === sloId.toString()),
                        );
                        let cloList;
                        if (cloDetails.length) {
                            cloList = cloDetails.find(
                                (cloDetail) => cloDetail._id.toString() === sloId.toString(),
                            );
                        }
                        let sloIdDetail;
                        if (sloList) {
                            sloIdDetail = sloList.slos.find(
                                (slo) => slo.slo_id === sloId.toString(),
                            );
                            sloIdDetail._id = sloIdDetail.slo_id;
                            sloIdDetail.type = DS_SLO_KEY;
                        }
                        if (!sloIdDetail && cloList) {
                            sloIdDetail = {
                                _id: cloList._id,
                                name: cloList.name,
                                type: DS_CLO_KEY,
                                no: cloList.no,
                            };
                        }
                        return sloIdDetail;
                    })
                    .filter((item) => item); //removing undefined and null values from array using filter
                question.sloIds = sloDetailList;
            }
            if (taxonomyIds && taxonomyIds.length) {
                // taxonomy
                const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
                const taxonomy = await Taxonomy.find(
                    { _id: { $in: taxonomyIdEntry } },
                    { name: 1, _id: 1 },
                );
                question.taxonomyIds = taxonomy;
            }
        }
    }
    return questions;
};

// question answer update by student
exports.acceptQuestionResponse = async (req, res) => {
    try {
        const {
            body: { activityId, questionId, acceptQuestion },
            params: { id: userId },
        } = req;
        let updateData;
        if (userId && questionId) {
            updateData = { 'questions.$[i].acceptQuestionResponse': acceptQuestion };
            updateData = { $set: updateData };
            const { success } = await dsCustomUpdate(
                Activities,
                { _id: convertToMongoObjectId(activityId) },
                updateData,
                [{ 'i._id': convertToMongoObjectId(questionId) }],
            );
            await acceptQuestionResponseOnOff({ activityId, questionId, acceptQuestion });
            if (!success) {
                return res.status(200).send(response_function(res, 200, false, DS_UPDATE_FAILED));
            }
        }
        const data = { acceptQuestionResponse: acceptQuestion };
        return res.status(200).send(response_function(res, 200, true, DS_UPDATED, data));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

acceptQuestionResponseOnOff = async ({ activityId, questionId, acceptQuestion }) => {
    const { socketEventStudentId: studentEventId, questions } = await Activities.findOne(
        {
            _id: convertToMongoObjectId(activityId),
            isDeleted: false,
        },
        { endTime: 1, socketEventStudentId: 1, socketEventStaffId: 1, questions: 1 },
    );

    const question = questions.find((questionEntry) => !questionEntry.questionMoved);

    const studentStatus = {
        examOnLive: true,
        movedToNextQuestion: false,
        acceptQuestionResponse: acceptQuestion,
        nextQuestionId: '',
    };
    if (question) {
        studentStatus.nextQuestionId = question._id;
    }
    const sendSocketData = [];
    // student response
    const eventId = studentEventId;
    const data = JSON.stringify(studentStatus);
    sendSocketData.push({ eventId, data });
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
};

// get all QuestionBank
exports.getQuestionBank = async (req, res) => {
    try {
        const {
            body: { courseId, sessions },
        } = req;

        if (sessions && sessions.length > 1) {
            const sessionIds = sessions.map((session) => convertToMongoObjectId(session));
            const courseScheduledQuery = {
                isDeleted: false,
                'session._session_id': { $in: sessionIds },
            };

            const courseSchedules = await CourseSchedule.find(courseScheduledQuery, {
                session: 1,
            }).lean();

            const sessionDetails = [];
            for (const session of sessions) {
                let courseScheduleDetails = courseSchedules.filter((courseSchedule) => {
                    if (
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        courseSchedule.session._session_id.toString() === session.toString()
                    ) {
                        return true;
                    }
                    return false;
                });

                courseScheduleDetails = courseScheduleDetails.map(
                    (courseScheduleDetail) => courseScheduleDetail.session,
                );
                const sessionDetail = courseScheduleDetails.find(
                    (courseScheduleDetail) =>
                        courseScheduleDetail._session_id.toString() === session.toString(),
                );

                if (sessionDetail) {
                    sessionDetails.push(sessionDetail);
                }
            }
            const sessionDetailLists = sessionDetails.map((sessionDetail) => {
                sessionDetail._id = sessionDetail._session_id;
                return sessionDetail;
            });
            return res.status(200).send(
                response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), {
                    sessions: sessionDetailLists,
                }),
            );
        }
        const sessionId = sessions.toString();
        const slos = await getSloPlusCloBySessionIdAndCourseId(courseId, sessionId, 'question');
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), { sessions: slos }));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

exports.getQuestions = async (req, res) => {
    try {
        const {
            body: { questions },
        } = req;
        const questionIds = questions.map((question) => convertToMongoObjectId(question));
        let questionEntry = await Question.find(
            {
                isDeleted: false,
                _id: { $in: questionIds },
            },
            {
                isDeleted: 1,
                acceptQuestionResponse: 1,
                sloIds: 1,
                taxonomyIds: 1,
                text: 1,
                options: 1,
                feedback: 1,
                attachments: 1,
                sessionId: 1,
                _activityId: 1,
            },
        );
        if (questionEntry && questionEntry.length) {
            questionEntry = await formatQuestionsWithAttachmentLink(questionEntry);
        }
        return res.status(200).send(
            response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), {
                questions: questionEntry,
            }),
        );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

exports.getQuestionsBySession = async (req, res) => {
    try {
        const {
            body: { courseId, sessionId, sloOrCloId },
            query: { page, limit, search },
        } = req;
        const sessionIdConvert = convertToMongoObjectId(sessionId);
        const questionQuery = {
            status: COMPLETED,
            courseId: convertToMongoObjectId(courseId),
            'sessionFlowIds._id': sessionIdConvert,
        };

        const activities = await Activities.find(questionQuery).lean();
        const questions = activities
            .filter((activity) => activity.questions && activity.questions.length)
            .map((activityEntry) => activityEntry.questions);
        // eslint-disable-next-line prefer-spread
        let questionIds = [].concat.apply([], questions);
        questionIds = questionIds.map((questionId) => convertToMongoObjectId(questionId._id));
        const courseScheduledQuery = {
            isDeleted: false,
            'session._session_id': convertToMongoObjectId(sessionId),
        };
        const courseSchedules = await CourseSchedule.findOne(courseScheduledQuery, {
            session: 1,
        }).lean();
        const perPage = parseInt(limit > 0 ? limit : 10);
        const pageNo = parseInt(page > 0 ? page : 1);
        if (sessionId && !sloOrCloId) {
            const queryEntry = {
                isDeleted: false,
                _id: { $in: questionIds },
                sessionId: convertToMongoObjectId(sessionId),
            };
            if (search) {
                queryEntry.text = { $regex: search, $options: 'i' };
            }
            const questions = await Question.find(queryEntry, {
                isDeleted: 1,
                acceptQuestionResponse: 1,
                sloIds: 1,
                taxonomyIds: 1,
                text: 1,
                options: 1,
                feedback: 1,
                attachments: 1,
                sessionId: 1,
                _activityId: 1,
            })
                .skip(perPage * (pageNo - 1))
                .limit(perPage);
            const totalDoc = await Question.find(queryEntry).countDocuments().exec();
            const questionDetails = questions.filter(
                (question) =>
                    question.sessionId && question.sessionId.toString() === sessionId.toString(),
            );
            const questionList = [];
            // check question use and student correct answered
            for (const questionDetail of questionDetails) {
                const correctOption = questionDetail.options.find((option) => option.answer);
                const usedQuestions = activities.filter((activity) =>
                    activity.questions.find(
                        (question) => question._id.toString() === questionDetail._id.toString(),
                    ),
                );
                let studentAnsweredQuestions = usedQuestions
                    .filter((activity) =>
                        activity.students.find((student) =>
                            student.questions.find(
                                (question) =>
                                    question._questionId.toString() ===
                                    questionDetail._id.toString(),
                            ),
                        ),
                    )
                    .map((activity) => activity.students);
                // eslint-disable-next-line prefer-spread
                studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                studentAnsweredQuestions = studentAnsweredQuestions.map((studentAnsweredQuestion) =>
                    studentAnsweredQuestion.questions.filter(
                        (question) =>
                            question._questionId.toString() === questionDetail._id.toString(),
                    ),
                );
                // eslint-disable-next-line prefer-spread
                studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                const totalStudents = studentAnsweredQuestions.length;
                const correctAnsweredStudents = studentAnsweredQuestions.filter(
                    (studentAnsweredQuestion) =>
                        studentAnsweredQuestion._optionId &&
                        correctOption &&
                        studentAnsweredQuestion._optionId.toString() ===
                            correctOption._id.toString(),
                ).length;
                const percentage =
                    (correctAnsweredStudents / totalStudents) * 100
                        ? ((correctAnsweredStudents / totalStudents) * 100).toFixed()
                        : '0';
                const usedCount = usedQuestions && usedQuestions.length ? usedQuestions.length : 0;

                let sessionType;
                if (courseSchedules) {
                    sessionType = courseSchedules.session;
                }
                questionList.push({
                    _id: questionDetail._id,
                    _activityId: questionDetail._activityId,
                    isDeleted: questionDetail.isDeleted,
                    acceptQuestionResponse: questionDetail.acceptQuestionResponse,
                    sloIds: questionDetail.sloIds,
                    taxonomyIds: questionDetail.taxonomyIds,
                    text: questionDetail.text,
                    options: questionDetail.options,
                    feedback: questionDetail.feedback,
                    attachments: questionDetail.attachments,
                    sessionId: questionDetail.sessionId,
                    usedCount,
                    percentage,
                    sessionType,
                    sessionDetails: sessionType,
                });
            }
            let questionLists;
            if (questionDetails && questionDetails.length) {
                questionLists = await formatQuestionsWithOutAttachmentLink(questionList);
            }
            return res.status(200).send(
                response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), {
                    totalDoc,
                    totalPages: Math.ceil(totalDoc / perPage),
                    currentPage: pageNo,
                    questions: questionLists,
                }),
            );
        }
        const sessionIdConvertToString = sessionId.toString();
        let slos = await getSloPlusCloBySessionIdAndCourseId(courseId, sessionIdConvertToString);
        slos = slos
            .filter((sloId) => sloId._id.toString() === sloOrCloId.toString())
            .map((sloId) => sloId._id.toString());
        slos = [...new Set(slos)];
        const sloIds = slos.map((sloId) => convertToMongoObjectId(sloId));
        const queryEntry = {
            _id: { $in: questionIds },
            isDeleted: false,
            sloIds: { $elemMatch: { $in: sloIds } },
            sessionId: convertToMongoObjectId(sessionId),
        };
        if (search) {
            queryEntry.text = { $regex: search, $options: 'i' };
        }
        const QuestionQuery = await Question.find(queryEntry, {
            isDeleted: 1,
            acceptQuestionResponse: 1,
            sloIds: 1,
            taxonomyIds: 1,
            text: 1,
            options: 1,
            feedback: 1,
            attachments: 1,
            sessionId: 1,
            _activityId: 1,
        })
            .skip(perPage * (pageNo - 1))
            .limit(perPage);
        const totalDoc = await Question.find(queryEntry).countDocuments().exec();
        const sloQuestion = [];
        if (sloOrCloId) {
            for (const sloEntry of slos) {
                const questionDetails = QuestionQuery.filter((Questions) => {
                    const questionSloIds = Questions.sloIds.map((sloId) => sloId.toString());
                    return (
                        Questions.sessionId.toString() === sessionId.toString() &&
                        questionSloIds.includes(sloEntry.toString())
                    );
                });

                const questionList = [];
                // check question use and student correct answered

                for (const questionDetail of questionDetails) {
                    const correctOption = questionDetail.options.find((option) => option.answer);
                    const usedQuestions = activities.filter((activity) =>
                        activity.questions.find(
                            (question) => question._id.toString() === questionDetail._id.toString(),
                        ),
                    );
                    let studentAnsweredQuestions = usedQuestions
                        .filter((activity) =>
                            activity.students.find((student) =>
                                student.questions.find(
                                    (question) =>
                                        question._questionId.toString() ===
                                        questionDetail._id.toString(),
                                ),
                            ),
                        )
                        .map((activity) => activity.students);
                    // eslint-disable-next-line prefer-spread
                    studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                    studentAnsweredQuestions = studentAnsweredQuestions.map(
                        (studentAnsweredQuestion) =>
                            studentAnsweredQuestion.questions.filter(
                                (question) =>
                                    question._questionId.toString() ===
                                    questionDetail._id.toString(),
                            ),
                    );
                    // eslint-disable-next-line prefer-spread
                    studentAnsweredQuestions = [].concat.apply([], studentAnsweredQuestions);
                    const totalStudents = studentAnsweredQuestions.length;
                    const correctAnsweredStudents = studentAnsweredQuestions.filter(
                        (studentAnsweredQuestion) =>
                            studentAnsweredQuestion._optionId &&
                            correctOption &&
                            studentAnsweredQuestion._optionId.toString() ===
                                correctOption._id.toString(),
                    ).length;
                    const percentage =
                        (correctAnsweredStudents / totalStudents) * 100
                            ? ((correctAnsweredStudents / totalStudents) * 100).toFixed()
                            : '0';
                    const usedCount =
                        usedQuestions && usedQuestions.length ? usedQuestions.length : 0;

                    let sessionType;
                    if (courseSchedules) {
                        sessionType = courseSchedules.session;
                    }

                    questionList.push({
                        _id: questionDetail._id,
                        _activityId: questionDetail._activityId,
                        isDeleted: questionDetail.isDeleted,
                        acceptQuestionResponse: questionDetail.acceptQuestionResponse,
                        sloIds: questionDetail.sloIds,
                        taxonomyIds: questionDetail.taxonomyIds,
                        text: questionDetail.text,
                        options: questionDetail.options,
                        feedback: questionDetail.feedback,
                        attachments: questionDetail.attachments,
                        sessionId: questionDetail.sessionId,
                        usedCount,
                        percentage,
                        sessionType,
                    });
                }
                const question = await formatQuestionsWithOutAttachmentLink(questionList);
                if (question && question.length) sloQuestion.push(question);
            }
        }
        return res.status(200).send(
            response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), {
                totalDoc,
                totalPages: Math.ceil(totalDoc / perPage),
                currentPage: pageNo,
                questions: sloQuestion[0],
            }),
        );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// get Session And Student Group
exports.getSessionAndStudentGroup = async (req, res) => {
    try {
        const {
            params: { activityId, sessionId },
        } = req;

        if (activityId && !sessionId) {
            const ActivityQuery = await Activities.findOne({
                isDeleted: false,
                _id: convertToMongoObjectId(activityId),
            }).exec();

            const { courseId } = ActivityQuery;

            const CourseScheduleQuery = await CourseSchedule.find({
                isDeleted: false,
                _course_id: convertToMongoObjectId(courseId),
            }).exec();

            courseScheduleDetails = CourseScheduleQuery.map(
                (courseScheduleDetail) => courseScheduleDetail.session,
            );

            const uniqueSession = courseScheduleDetails.filter(
                (v, i, a) =>
                    a.findIndex((t) => t._session_id.toString() === v._session_id.toString()) === i,
            );

            return res
                .status(200)
                .send(response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), uniqueSession));
        }

        const studentGroupQuery = await CourseSchedule.find(
            {
                isDeleted: false,
                'session._session_id': convertToMongoObjectId(sessionId),
            },
            { student_groups: 1 },
        ).exec();

        const studentGroupData = [];
        for (const groupDatas of studentGroupQuery) {
            for (const groupData of groupDatas.student_groups) {
                studentGroupData.push({
                    group_id: groupData.group_id,
                    group_name: groupData.group_name,
                });
            }
        }

        const uniqueGroup = studentGroupData.filter(
            (v, i, a) => a.findIndex((t) => t.group_name === v.group_name) === i,
        );
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t(DS_DATA_RETRIEVED), uniqueGroup));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// select sessions
exports.selectSessions = async (req, res) => {
    try {
        const {
            params: { id },
            query: {
                userId,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
                courseAdmin,
            },
        } = req;
        const sessionSupportList = await getAllSessionList(
            id,
            userId,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            courseAdmin,
        );
        if (!sessionSupportList.length)
            return sendResponseWithRequest(req, res, 200, false, 'No data found', []);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            sessionSupportList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.exportActivity = async (req, res) => {
    try {
        const {
            params: { id },
        } = req;
        const activityData = await get(Activities, { _id: convertToMongoObjectId(id) }, {});
        if (!activityData.status) activityData.data = [];
        else activityData.data = JSON.parse(JSON.stringify(activityData.data));
        const questionIds = activityData.data.questions
            .map((questionElement) => questionElement._id.toString())
            .flat();
        // Student data
        const studentIds = activityData.data.students.map((ele) => ele._studentId);

        const studentData = await get_list(
            User,
            {
                _id: { $in: studentIds },
                isDeleted: false,
            },
            { user_id: 1, name: 1, gender: 1 },
        );
        if (!studentData.status) studentData.data = [];

        const questionList = await get_list(
            Question,
            {
                _id: { $in: questionIds },
                isDeleted: false,
            },
            {},
        );
        if (!questionList.status) questionList.data = [];

        const questionWithWrongOption = [];
        for (questionElement of activityData.data.questions) {
            const questionData = questionList.data.find(
                (ele) => ele._id.toString() === questionElement._id.toString(),
            );
            if (!questionData) continue;
            if (questionData.options.length) {
                const optionData = questionData.options.find((ele2) => ele2.answer === true);
                const wrongOptionData = questionData.options.find((ele2) => ele2.answer === false);
                if (questionData && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionData;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: convertToMongoObjectId(wrongOptionData._id),
                });
            }
        }
        for (activityStudentElement of activityData.data.students) {
            activityStudentElement.missingQuestionDatas = [];
            const missingQuestionDatas = questionWithWrongOption.filter(
                (wrongElement) =>
                    !activityStudentElement.questions.find(
                        (questionElement) =>
                            wrongElement._questionId.toString() ===
                            questionElement._questionId.toString(),
                    ),
            );
            if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                activityStudentElement.questions = [
                    ...activityStudentElement.questions,
                    ...missingQuestionDatas,
                ];
                activityStudentElement.missingQuestionDatas = missingQuestionDatas;
            }
        }
        let studentMark = 0;
        const studentActivities = [];
        for (studentElement of activityData.data.students) {
            let studentQuestionMarks = 0;
            let quizAnsweredCount = 0;
            for (questionElement of activityData.data.questions) {
                const questionData = studentElement.questions.find(
                    (eleQuestion) =>
                        eleQuestion._questionId.toString() === questionElement._id.toString(),
                );
                const missingQuestionData = studentElement.missingQuestionDatas.find(
                    (eleMissingQuestion) =>
                        eleMissingQuestion._questionId.toString() ===
                        questionElement._id.toString(),
                );
                if (questionData && questionData._optionId && !missingQuestionData) {
                    quizAnsweredCount++;
                    if (
                        questionData &&
                        questionData._optionId &&
                        questionElement.correctOption &&
                        questionElement.correctOption.toString() ===
                            questionData._optionId.toString()
                    )
                        studentQuestionMarks++;
                }
            }
            studentMark += (studentQuestionMarks / activityData.data.questions.length) * 100;

            const studentDetails = studentData.data.find(
                (ele) => ele._id.toString() === studentElement._studentId.toString(),
            );
            studentActivities.push({
                _id: activityData.data._id,
                name: activityData.data.name,
                questionCount: activityData.data.questions.length,
                noQuizAnswered: quizAnsweredCount,
                answeredRight: studentQuestionMarks,
                mark: (studentQuestionMarks / activityData.data.questions.length) * 100,
                studentDetails,
            });
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(DS_DATA_RETRIEVED),
            studentActivities,
        );
    } catch (error) {
        console.log(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.surveyExportActivity = async (req, res) => {
    try {
        const {
            params: { id },
        } = req;
        const activityData = await Activities.findOne(
            {
                _id: convertToMongoObjectId(id),
            },
            {
                questions: 1,
                students: 1,
            },
        )
            .populate({ path: '_program_id', select: { name: 1, type: 1 } })
            .populate({ path: 'courseId', select: { course_name: 1, course_code: 1 } })
            .populate({ path: 'students._studentId', select: { name: 1, user_id: 1 } })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1, start_date: 1, end_date: 1 },
            })
            .populate({
                path: 'scheduleIds',
                select: { 'staffs._staff_id': 1 },
                populate: {
                    path: 'staffs._staff_id',
                    select: { user_id: 1, name: 1 },
                },
            })
            .lean();
        const studentData = [];
        if (activityData.questions.length) {
            const questionsData = await Question.find({
                _id: {
                    $in: activityData.questions.map((questionElement) =>
                        convertToMongoObjectId(questionElement._id),
                    ),
                },
            });
            for (const questionElement of questionsData) {
                for (const studentQuestion of activityData.students) {
                    for (const studentQuestionElement of studentQuestion.questions) {
                        if (
                            studentQuestionElement._questionId.toString() ===
                            questionElement._id.toString()
                        ) {
                            if (
                                studentData.find(
                                    (studentElement) =>
                                        studentElement.studentId.toString() ===
                                        studentQuestion._studentId.user_id.toString(),
                                )
                            ) {
                                const studentDataIndex = studentData.findIndex(
                                    (studentElement) =>
                                        studentElement.studentId.toString() ===
                                        studentQuestion._studentId.user_id.toString(),
                                );
                                const answer =
                                    studentQuestionElement.textAnswer ??
                                    questionElement.options.find(
                                        (optionsId) =>
                                            optionsId._id.toString() ===
                                            studentQuestionElement._optionId.toString(),
                                    )?.text ??
                                    '';
                                const questionOrder = activityData.questions.find(
                                    (questionIdElement) =>
                                        questionIdElement._id.toString() ===
                                        questionElement._id.toString(),
                                ).order;
                                studentData[studentDataIndex].studentQuestionElement.push({
                                    questionName: questionElement.text,
                                    questionOrder,
                                    surveyQuestionType: questionElement.surveyQuestionType,
                                    answer,
                                });
                            } else {
                                const answer =
                                    studentQuestionElement.textAnswer ??
                                    questionElement.options.find(
                                        (optionsId) =>
                                            optionsId._id.toString() ===
                                            studentQuestionElement._optionId.toString(),
                                    )?.text ??
                                    '';
                                const questionOrder = activityData.questions.find(
                                    (questionIdElement) =>
                                        questionIdElement._id.toString() ===
                                        questionElement._id.toString(),
                                ).order;
                                studentData.push({
                                    student: studentQuestion._studentId.name,
                                    studentId: studentQuestion._studentId.user_id,
                                    _student_id: studentQuestion._studentId._id,
                                    studentQuestionElement: [
                                        {
                                            questionName: questionElement.text,
                                            surveyQuestionType: questionElement.surveyQuestionType,
                                            questionOrder,
                                            answer,
                                        },
                                    ],
                                });
                            }
                        }
                    }
                }
            }
        }
        studentData.forEach((studentElement) => {
            return studentElement.studentQuestionElement.sort(
                (a, b) => a.questionOrder - b.questionOrder,
            );
        });
        delete activityData.questions;
        delete activityData.students;
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            activityData,
            studentData,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

exports.activitySurveyExport = async (req, res) => {
    try {
        const {
            query: { activityId },
        } = req;
        console.time('activityData');
        const activityData = await Activities.findOne(
            {
                _id: convertToMongoObjectId(activityId),
            },
            {
                name: 1,
                type: 1,
                term: 1,
                year_no: 1,
                level_no: 1,
                questions: 1,
                students: 1,
            },
        )
            .populate({ path: '_program_id', select: { name: 1 /* , type: 1 */ } })
            .populate({ path: 'courseId', select: { course_name: 1, course_code: 1 } })
            .populate({
                path: 'students._studentId',
                select: { /* name: 1, user_id: 1, */ gender: 1 },
            })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 /* start_date: 1, end_date: 1 */ },
            })
            .populate({
                path: 'scheduleIds',
                select: { 'staffs._staff_id': 1, 'students._id': 1 },
                populate: [
                    {
                        path: 'staffs._staff_id',
                        select: { user_id: 1, name: 1 },
                    },
                    {
                        path: 'students._id',
                        select: { user_id: 1, gender: 1 },
                    },
                ],
            })
            .lean();
        console.timeEnd('activityData');
        const activityTotalStudent = [
            {
                mode: 'male',
                totalCount: 0,
                percentage: 0,
                studentIds: [],
            },
            {
                mode: 'female',
                totalCount: 0,
                percentage: 0,
                studentIds: [],
            },
        ];
        let totalStudents = 0;
        let studentDetails = [];
        if (activityData.scheduleIds && activityData.scheduleIds.length) {
            for (scheduleElement of activityData.scheduleIds) {
                for (activityStudentElement of activityTotalStudent) {
                    const scheduleStudentGenderCount = scheduleElement.students.filter(
                        (scheduleScheduleElement) =>
                            scheduleScheduleElement._id.gender === activityStudentElement.mode,
                    ).length;
                    activityStudentElement.totalCount += scheduleStudentGenderCount;
                    totalStudents += scheduleStudentGenderCount;
                    studentDetails = [
                        ...studentDetails,
                        ...scheduleElement.students.map((studentElement) => studentElement._id),
                    ];
                }
                delete scheduleElement.students;
            }
        }
        studentDetails = new Set(studentDetails);
        studentDetails = [...studentDetails];
        let questionsData = [];
        const attendedStudentIds = [];
        if (activityData.questions.length) {
            console.time('questionsData');
            questionsData = await Question.find(
                {
                    _id: {
                        $in: activityData.questions.map((questionElement) =>
                            convertToMongoObjectId(questionElement._id),
                        ),
                    },
                },
                {
                    _id: 1,
                    'options._id': 1,
                    'options.text': 1,
                    // 'options.answer': 1,
                    text: 1,
                    surveyQuestionType: 1,
                },
            ).lean();
            console.timeEnd('questionsData');
            for (questionElement of questionsData) {
                const questionSAQResponse = [];
                const questionOptions = questionElement.options.map((optionElement) => {
                    return {
                        ...optionElement,
                        ...{
                            studentResponse: [
                                {
                                    mode: 'male',
                                    count: 0,
                                    percentage: 0,
                                },
                                {
                                    mode: 'female',
                                    count: 0,
                                    percentage: 0,
                                },
                                {
                                    mode: 'total',
                                    count: 0,
                                    percentage: 0,
                                },
                            ],
                        },
                    };
                });
                const studentGenderCount = [
                    {
                        mode: 'male',
                        count: 0,
                        percentage: 0,
                    },
                    {
                        mode: 'female',
                        count: 0,
                        percentage: 0,
                    },
                    {
                        mode: 'total',
                        count: 0,
                        percentage: 0,
                    },
                ];
                for (studentElement of activityData.students) {
                    const studentQuestion = studentElement.questions.find(
                        (studentQuestionElement) =>
                            studentQuestionElement._questionId.toString() ===
                            questionElement._id.toString(),
                    );
                    if (studentQuestion) {
                        if (studentQuestion._optionId) {
                            const optionIndex = questionOptions.findIndex(
                                (questionOptionElement) =>
                                    questionOptionElement._id.toString() ===
                                    studentQuestion._optionId.toString(),
                            );
                            if (optionIndex !== -1) {
                                const genderIndex = questionOptions[
                                    optionIndex
                                ].studentResponse.findIndex(
                                    (studentResponseElement) =>
                                        studentResponseElement.mode ===
                                        studentElement._studentId.gender,
                                );
                                if (genderIndex !== -1) {
                                    questionOptions[optionIndex].studentResponse[genderIndex]
                                        .count++;
                                    questionOptions[optionIndex].studentResponse[2].count++;
                                    activityTotalStudent[genderIndex].studentIds.push(
                                        studentElement._studentId._id.toString(),
                                    );
                                    studentGenderCount[genderIndex].count++;
                                    studentGenderCount[2].count++;
                                }
                            }
                        } else if (studentQuestion.textAnswer) {
                            const studentDetail = studentDetails.find(
                                (studentDetailsElement) =>
                                    studentDetailsElement._id.toString() ===
                                    studentElement._studentId._id.toString(),
                            );
                            questionSAQResponse.push({
                                response: studentQuestion.textAnswer,
                                user_id: studentDetail ? studentDetail.user_id : '',
                                gender: studentDetail ? studentDetail.gender : '',
                            });
                        }
                    }
                    attendedStudentIds.push(studentElement._studentId._id);
                }
                for (questionOptionElement of questionOptions) {
                    for (studentResponseElement of questionOptionElement.studentResponse) {
                        const questionGenderCount = studentGenderCount.find(
                            (studentGenderCountElement) =>
                                studentGenderCountElement.mode === studentResponseElement.mode,
                        );
                        if (questionGenderCount && questionGenderCount.count) {
                            studentResponseElement.percentage =
                                (studentResponseElement.count / questionGenderCount.count) * 100;
                        }
                    }
                }
                delete questionElement.options;
                // questionElement.studentGenderCount = studentGenderCount;
                questionElement.questionOptions = questionOptions;
                questionElement.questionSAQResponse = questionSAQResponse;
            }
        }
        let attendedCount = 0;
        for (totalStudentElement of activityTotalStudent) {
            totalStudentElement.studentIds = [...new Set(totalStudentElement.studentIds)];
            totalStudentElement.count = totalStudentElement.studentIds.length;
            totalStudentElement.percentage =
                (totalStudentElement.count / totalStudentElement.totalCount) * 100;
            attendedCount += totalStudentElement.studentIds.length;
            delete totalStudentElement.studentIds;
        }
        activityTotalStudent.push({
            mode: 'total',
            totalCount: totalStudents,
            percentage: (attendedCount / totalStudents) * 100,
            count: attendedCount,
        });
        delete activityData.questions;
        delete activityData.students;
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), {
            activityTotalStudent,
            // studentGenderCount,
            activityData,
            questionsData,
            notAttendedStudents: studentDetails.filter(
                (studentElement) =>
                    !attendedStudentIds.find(
                        (activityStudentElement) =>
                            activityStudentElement.toString() === studentElement._id.toString(),
                    ),
            ),
        });
    } catch (error) {
        console.error(error);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
