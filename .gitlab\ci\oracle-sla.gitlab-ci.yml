.rules_oracle-sla:
  rules:
    - if: $CI_COMMIT_TAG
      when: manual

default:
    tags:
        - ds-api

.aws_image:
    image:
        name: amazon/aws-cli:2.9.21
        entrypoint: ['']

.build:
    extends: .aws_image
    services:
        - docker:23.0.0-dind
    before_script:
        - amazon-linux-extras install docker
        - aws --version
        - docker --version
        # TODO: use credential helpers
        - aws ecr get-login-password | docker login --username AWS --password-stdin $AWS_CONTAINER_REGISTRY

build oracle-sla:
  stage: build
  environment: oracle-sla
  extends:
    - .rules_oracle-sla
    - .build
  variables:
    OCI_LATEST_IMAGE: $OCI_LATEST
    OCI_TAG_IMAGE: $OCI_CONTAINER_REGISTRY:$CI_COMMIT_TAG
  script:
    - cp $SG_CRT_SLA ./lib/models/sg-sla.crt
    - echo tag=$CI_COMMIT_TAG
    - docker build -t $OCI_LATEST_IMAGE -t $OCI_TAG_IMAGE .
    - docker login -u $OCI_USER -p $OCI_AUTH_TOKEN $OCI_CONTAINER_REGISTRY
    - docker push $OCI_LATEST_IMAGE
    - docker push $OCI_TAG_IMAGE
