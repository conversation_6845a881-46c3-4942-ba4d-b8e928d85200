const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    mode: Joi.string()
                        .valid('auto', 'manual')
                        .required()
                        .error((error) => {
                            return req.t('MODE_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .when('mode', {
                            is: 'manual',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                    method: Joi.string()
                        .when('mode', {
                            is: 'auto',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('METHOD_REQUIRED');
                        }),
                    // delivery_group: Joi.array().items(
                    //     Joi.object().keys({
                    //         session_type: Joi.string().required().error(error => {
                    //             return req.t('_EVENT_ID_REQUIRED');
                    //         }),
                    //         group_no: Joi.number().min(1).max(99).required().error(error => {
                    //             return req.t('_EVENT_ID_REQUIRED');
                    //         })
                    //     })
                    // ).when('mode', { is: 'manual', then: Joi.required(), otherwise: Joi.optional() }).error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    mode: Joi.string()
                        .valid('auto', 'manual')
                        .required()
                        .error((error) => {
                            return req.t('MODE_REQUIRED');
                        }),
                    gender: Joi.string()
                        .alphanum()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .when('mode', {
                            is: 'manual',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                    method: Joi.string()
                        .when('mode', {
                            is: 'auto',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('METHOD_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string().required(),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    mode: Joi.string()
                        .valid('auto', 'manual')
                        .required()
                        .error((error) => {
                            return req.t('MODE_REQUIRED');
                        }),
                    gender: Joi.string()
                        .alphanum()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    method: Joi.string()
                        .when('mode', {
                            is: 'auto',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('METHOD_REQUIRED');
                        }),
                    session_type: Joi.array()
                        .items(Joi.string())
                        .when('mode', {
                            is: 'auto',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('SESSION_TYPE_REQUIRED');
                        }),
                    // group_no: Joi.number().min(1).max(99)
                    //     .when('mode', { is: 'auto', then: Joi.required(), otherwise: Joi.optional() }).error(error => {
                    //         return req.t('_EVENT_ID_REQUIRED');
                    //     }),
                    // master_group: Joi.number().min(1).max(99)
                    //     .when('mode', { is: 'manual', then: Joi.required(), otherwise: Joi.optional() }).error(error => {
                    //         return req.t('_EVENT_ID_REQUIRED');
                    //     }),
                    delivery_group: Joi.array()
                        .items(
                            Joi.object().keys({
                                session_type: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('SESSION_TYPE_REQUIRED');
                                    }),
                                group_no: Joi.number()
                                    .min(1)
                                    .max(99)
                                    .required()
                                    .error((error) => {
                                        return req.t('GROUP_NO_REQUIRED');
                                    }),
                            }),
                        )
                        .when('mode', {
                            is: 'manual',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        })
                        .error((error) => {
                            return req.t('DELIVERY_GROUP_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                    status: Joi.string().optional(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    old_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('OLD_GROUP_NO_REQUIRED');
                        }),
                    new_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('NEW_GROUP_NO_REQUIRED');
                        }),
                    _student_ids: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string()
                                .required()
                                .error((error) => {
                                    return req.t('SESSION_TYPE_REQUIRED');
                                }),
                            group_no: Joi.number()
                                .min(1)
                                .max(99)
                                .required()
                                .error((error) => {
                                    return req.t('GROUP_NO_REQUIRED');
                                }),
                        }),
                    ),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    master_group: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('MASTER_GROUP_REQUIRED');
                        }),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string()
                                .required()
                                .error((error) => {
                                    return req.t('SESSION_TYPE_REQUIRED');
                                }),
                            group_no: Joi.number()
                                .min(1)
                                .max(99)
                                .required()
                                .error((error) => {
                                    return req.t('GROUP_NO_REQUIRED');
                                }),
                        }),
                    ),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.elective_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    master_group: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('MASTER_GROUP_REQUIRED');
                        }),
                    old_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('OLD_GROUP_NO_REQUIRED');
                        }),
                    new_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('NEW_GROUP_NO_REQUIRED');
                        }),
                    _student_ids: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_elective_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    old_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('OLD_GROUP_NO_REQUIRED');
                        }),
                    new_group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('NEW_GROUP_NO_REQUIRED');
                        }),
                    _student_ids: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.student_group_course_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_course_student_list_filter = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_course_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_ID_REQUIRED');
                        }),
                    curriculum: Joi.string()
                        .valid('1.0', '2.0', '3.0')
                        .required()
                        .error((error) => {
                            return req.t('_CURRICULUM_ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.group_list_student_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    group: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('GROUP_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_groups_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.student_count_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.settings_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('ACADEMIC_NO_REQUIRED');
                                    }),
                                first_name: Joi.string()
                                    .trim()
                                    .required()
                                    .error((error) => {
                                        return req.t('FIRST_NAME_REQUIRED');
                                    }),
                                last_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('LAST_NAME_REQUIRED');
                                    }),
                                middle_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('MIDDLE_NAME_REQUIRED');
                                    }),
                                mark: Joi.number()
                                    .required()
                                    .error((error) => {
                                        return req.t('MARK_REQUIRED');
                                    }),
                                // gender: Joi.string().valid('Male', 'MALE', 'Female', 'FEMALE', constant.GENDER.MALE, constant.GENDER.FEMALE).required().error(error => {
                                //     return 'Pls Enter valid gender (male/female)';
                                // })
                                // _participation_course_id: Joi.array().items(Joi.string().alphanum().length(24).allow('').error(error => {
                                //     return req.t('_EVENT_ID_REQUIRED');
                                // })),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('ACADEMIC_NO_REQUIRED');
                                    }),
                                first_name: Joi.string()
                                    .trim()
                                    .required()
                                    .error((error) => {
                                        return req.t('FIRST_NAME_REQUIRED');
                                    }),
                                last_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('LAST_NAME_REQUIRED');
                                    }),
                                middle_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('MIDDLE_NAME_REQUIRED');
                                    }),
                                mark: Joi.number()
                                    .required()
                                    .error((error) => {
                                        return req.t('MARK_REQUIRED');
                                    }),
                                // gender: Joi.string().valid('Male', 'MALE', 'Female', 'FEMALE', constant.GENDER.MALE, constant.GENDER.FEMALE).error(error => {
                                //     return 'Pls Enter valid gender (male/female)';
                                // })
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('BATCH_REQUIRED');
                    }),
                level: Joi.string()
                    .required()
                    .error((error) => {
                        return req.t('LEVEL_REQUIRED');
                    }),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                'Male',
                                'MALE',
                                'Female',
                                'FEMALE',
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required()
                            .error((error) => {
                                return req.t('GENDER_REQUIRED');
                            }),
                        no_of_group: Joi.number()
                            .min(1)
                            .max(99)
                            .required()
                            .error((error) => {
                                return req.t('NO_OF_GROUP_REQUIRED');
                            }),
                        no_of_students: Joi.number()
                            .min(1)
                            .max(5000)
                            .required()
                            .error((error) => {
                                return req.t('NO_OF_STUDENT_REQUIRED');
                            }),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('BATCH_REQUIRED');
                    }),
                level: Joi.string()
                    .required()
                    .error((error) => {
                        return req.t('LEVEL_REQUIRED');
                    }),
                group_name: Joi.string().error((error) => {
                    return req.t('GROUP_NAME_REQUIRED');
                }),
                excess_count: Joi.number()
                    .min(1)
                    .max(99)
                    .error((error) => {
                        return req.t('EXCESS_COUNT_REQUIRED');
                    }),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                'Male',
                                'MALE',
                                'Female',
                                'FEMALE',
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required()
                            .error((error) => {
                                return req.t('GENDER_REQUIRED');
                            }),
                        group_no: Joi.number()
                            .min(1)
                            .max(6)
                            .required()
                            .error((error) => {
                                return req.t('GROUP_NO_REQUIRED');
                            }),
                        no_of_students: Joi.number()
                            .min(1)
                            .max(5000)
                            .required()
                            .error((error) => {
                                return req.t('NO_OF_STUDENT_REQUIRED');
                            }),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('BATCH_REQUIRED');
                    }),
                level: Joi.string()
                    .min(1)
                    .max(99)
                    .required()
                    .error((error) => {
                        return req.t('LEVEL_REQUIRED');
                    }),
                group_name: Joi.string().error((error) => {
                    return req.t('GROUP_NAME_REQUIRED');
                }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_COURSE_ID_REQUIRED');
                    }),
                excess_count: Joi.number()
                    .min(1)
                    .max(99)
                    .error((error) => {
                        return req.t('EXCESS_COUNT_REQUIRED');
                    }),
                gender: Joi.string()
                    .valid(
                        'Male',
                        'MALE',
                        'Female',
                        'FEMALE',
                        constant.GENDER.MALE,
                        constant.GENDER.FEMALE,
                        constant.GENDER.BOTH,
                    )
                    .required()
                    .error((error) => {
                        return req.t('GENDER_REQUIRED');
                    }),
                groups: Joi.array().items(
                    Joi.object().keys({
                        _group_no: Joi.number()
                            .min(1)
                            .max(99)
                            .required()
                            .error((error) => {
                                return req.t('_GROUP_NO_REQUIRED');
                            }),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_REQUIRED');
                                    }),
                                delivery_type: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_TYPE_REQUIRED');
                                    }),
                                no_of_group: Joi.number()
                                    .min(1)
                                    .max(99)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_GROUP_REQUIRED');
                                    }),
                                no_of_students: Joi.number()
                                    .min(1)
                                    .max(5000)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_STUDENT_REQUIRED');
                                    }),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('BATCH_REQUIRED');
                    }),
                group_name: Joi.string()
                    .required()
                    .error((error) => {
                        return req.t('GROUP_NAME_REQUIRED');
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_COURSE_ID_REQUIRED');
                    }),
                excess_count: Joi.number()
                    .min(1)
                    .max(99)
                    .required()
                    .error((error) => {
                        return req.t('EXCESS_COUNT_REQUIRED');
                    }),
                groups: Joi.array().items(
                    Joi.object().keys({
                        _group_no: Joi.number()
                            .min(1)
                            .max(10)
                            .required()
                            .error((error) => {
                                return req.t('_GROUP_NO_REQUIRED');
                            }),
                        gender: Joi.string()
                            .valid(
                                'Male',
                                'MALE',
                                'Female',
                                'FEMALE',
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required()
                            .error((error) => {
                                return req.t('GENDER_REQUIRED');
                            }),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_REQUIRED');
                                    }),
                                delivery_type: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_TYPE_REQUIRED');
                                    }),
                                no_of_group: Joi.number()
                                    .min(1)
                                    .max(99)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_GROUP_REQUIRED');
                                    }),
                                no_of_students: Joi.number()
                                    .min(1)
                                    .max(5000)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_STUDENT_REQUIRED');
                                    }),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return req.t('BATCH_REQUIRED');
                    }),
                level: Joi.string()
                    .required()
                    .error((error) => {
                        return req.t('LEVEL_REQUIRED');
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('_COURSE_ID_REQUIRED');
                    }),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                'Male',
                                'MALE',
                                'Female',
                                'FEMALE',
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required()
                            .error((error) => {
                                return req.t('GENDER_REQUIRED');
                            }),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_REQUIRED');
                                    }),
                                delivery_type: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('DELIVERY_TYPE_REQUIRED');
                                    }),
                                no_of_group: Joi.number()
                                    .min(1)
                                    .max(99)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_GROUP_REQUIRED');
                                    }),
                                no_of_students: Joi.number()
                                    .min(1)
                                    .max(5000)
                                    .required()
                                    .error((error) => {
                                        return req.t('NO_OF_STUDENT_REQUIRED');
                                    }),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.data_check_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    student: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('ACADEMIC_NO_REQUIRED');
                                    }),
                                first_name: Joi.string()
                                    .trim()
                                    .required()
                                    .error((error) => {
                                        return req.t('FIRST_NAME_REQUIRED');
                                    }),
                                last_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('LAST_NAME_REQUIRED');
                                    }),
                                middle_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('MIDDLE_NAME_REQUIRED');
                                    }),
                                mark: Joi.number()
                                    .required()
                                    .error((error) => {
                                        return req.t('MARK_REQUIRED');
                                    }),
                                // gender: Joi.string().valid('Male', 'MALE', 'Female', 'FEMALE', constant.GENDER.MALE, constant.GENDER.FEMALE).required().error(error => {
                                //     return 'Pls Enter valid gender (male/female)';
                                // })
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.year2_data_check_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return req.t('ACADEMIC_NO_REQUIRED');
                                    }),
                                first_name: Joi.string()
                                    .trim()
                                    .required()
                                    .error((error) => {
                                        return req.t('FIRST_NAME_REQUIRED');
                                    }),
                                last_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('LAST_NAME_REQUIRED');
                                    }),
                                middle_name: Joi.string()
                                    .trim()
                                    .allow('')
                                    .error((error) => {
                                        return req.t('MIDDLE_NAME_REQUIRED');
                                    }),
                                mark: Joi.number()
                                    .required()
                                    .error((error) => {
                                        return req.t('MARK_REQUIRED');
                                    }),
                                // gender: Joi.string().valid('Male', 'MALE', 'Female', 'FEMALE', constant.GENDER.MALE, constant.GENDER.FEMALE).required().error(error => {
                                //     return 'Pls Enter valid gender (male/female)';
                                // })
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.save_import_students_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    _institution_calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_INSTITUTION_CALENDAR_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    year: Joi.number()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    // curriculum: Joi.string().valid('1.0', '2.0', '3.0').required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // }),
                    from_level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('FROM_LEVEL_REQUIRED');
                        }),
                    to_level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('TO_LEVEL_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.get_student_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                    course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('COURSE_ID_REQUIRED');
                        }),
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    master_group: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('MASTER_GROUP_REQUIRED');
                        }),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string()
                                .required()
                                .error((error) => {
                                    return req.t('SESSION_TYPE_REQUIRED');
                                }),
                            group_no: Joi.number()
                                .min(1)
                                .max(99)
                                .required()
                                .error((error) => {
                                    return req.t('GROUP_NO_REQUIRED');
                                }),
                        }),
                    ),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_student_count = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    institution: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_INSTITUTION_ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_bulk_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24).required())
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.main_dashboard = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    institution: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_INSTITUTION_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.add_student_group = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    mark: Joi.number()
                        .min(0)
                        .max(100)
                        .required()
                        .error((error) => {
                            return req.t('MARK_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24).required())
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.student_search = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    academic_no: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.excess_count_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    to: Joi.string()
                        .valid('course', 'level')
                        .required()
                        .error((error) => {
                            return req.t('TO_REQUIRED');
                        }),
                    count: Joi.number()
                        .required()
                        .error((error) => {
                            return req.t('COUNT_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_group_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    fyd_group: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('FYD_GROUP_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    delivery: Joi.string()
                        .min(1)
                        .max(5)
                        .required()
                        .error((error) => {
                            return req.t('DELIVERY_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string()
                                .required()
                                .error((error) => {
                                    return req.t('SESSION_TYPE_REQUIRED');
                                }),
                            group_no: Joi.number()
                                .min(1)
                                .max(99)
                                .required()
                                .error((error) => {
                                    return req.t('GROUP_NO_REQUIRED');
                                }),
                        }),
                    ),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.get_course_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_bulk_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _student_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24).required())
                        .error((error) => {
                            return req.t('_STUDENT_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_get_student = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_add_student = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _student_id: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_ID_REQUIRED');
                        }),
                    mark: Joi.number()
                        .max(100)
                        .error((error) => {
                            return req.t('MARK_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_get_course_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    // group_no: Joi.number().min(1).max(30).required().error(error => {
                    //     return req.t('_EVENT_ID_REQUIRED');
                    // })
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_get_course_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.dashboard_course_groups = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('COURSE_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.std_global_search = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    institution: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_INSTITUTION_ID_REQUIRED');
                        }),
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.std_global_edit = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                    data: Joi.array().items(
                        Joi.object().keys({
                            mode: Joi.string()
                                .valid(
                                    constant.STUDENT_GROUP_MODE.FYD,
                                    constant.STUDENT_GROUP_MODE.COURSE,
                                    constant.STUDENT_GROUP_MODE.ROTATION,
                                )
                                .required()
                                .error((error) => {
                                    return req.t('MODE_REQUIRED');
                                }),
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .required()
                                .error((error) => {
                                    return req.t('_ID_REQUIRED');
                                }),
                            batch: Joi.string()
                                // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                                .required()
                                .error((error) => {
                                    return req.t('BATCH_REQUIRED');
                                }),
                            level: Joi.string()
                                .required()
                                .error((error) => {
                                    return req.t('LEVEL_REQUIRED');
                                }),
                            _course_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .required()
                                .error((error) => {
                                    return req.t('_COURSE_ID_REQUIRED');
                                }),
                            master_group: Joi.number()
                                .min(1)
                                .max(99)
                                .required()
                                .error((error) => {
                                    return req.t('MASTER_GROUP_REQUIRED');
                                }),
                            delivery_group: Joi.array().items(
                                Joi.object().keys({
                                    session_type: Joi.string()
                                        .required()
                                        .error((error) => {
                                            return req.t('SESSION_TYPE_REQUIRED');
                                        }),
                                    group_no: Joi.number()
                                        .min(1)
                                        .max(99)
                                        .required()
                                        .error((error) => {
                                            return req.t('GROUP_NO_REQUIRED');
                                        }),
                                }),
                            ),
                        }),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.add_student_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                    mark: Joi.number()
                        .min(0)
                        .max(100)
                        .error((error) => {
                            return req.t('MARK_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_course_clone = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    _from_course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_FROM_COURSE_ID_REQUIRED');
                        }),
                    _to_course_ids: Joi.array()
                        .items(Joi.string().alphanum().length(24).required())
                        .error((error) => {
                            return req.t('_TO_COURSE_IDS_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.get_clone = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.fyd_course_group_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    fyd_group: Joi.number()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('FYD_GROUP_REQUIRED');
                        }),
                    delivery: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('DELIVERY_REQUIRED');
                        }),
                    d_group_no: Joi.number()
                        .min(1)
                        .max(30)
                        .required()
                        .error((error) => {
                            return req.t('D_GROUP_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.individual_course_student_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.ind_course_group_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    course: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(
                            'Male',
                            'MALE',
                            'Female',
                            'FEMALE',
                            constant.GENDER.MALE,
                            constant.GENDER.FEMALE,
                            constant.GENDER.BOTH,
                        )
                        .required()
                        .error((error) => {
                            return req.t('GENDER_REQUIRED');
                        }),
                    delivery: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('DELIVERY_REQUIRED');
                        }),
                    d_group_no: Joi.number()
                        .min(1)
                        .max(30)
                        .required()
                        .error((error) => {
                            return req.t('D_GROUP_NO_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.rotation_add_student_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(99)
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    group_no: Joi.number()
                        .min(1)
                        .max(15)
                        .required()
                        .error((error) => {
                            return req.t('GROUP_NO_REQUIRED');
                        }),
                    _student_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('STUDENT_ID_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),

                    mark: Joi.number()
                        .min(0)
                        .max(100)
                        .required()
                        .error((error) => {
                            return req.t('MARK_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.rotation_get_student_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.get_student_courses_year_two_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.add_student_to_course_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    academic_no: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('ACADEMIC_NO_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _user_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_USER_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.add_student_to_course_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _student_id: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('_STUDENT_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.ind_course_std_list_dashboard = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required()
                        .error((error) => {
                            return req.t('BATCH_REQUIRED');
                        }),
                    level: Joi.string()
                        .required()
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
