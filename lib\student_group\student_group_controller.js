const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const institution = require('mongoose').model(constant.INSTITUTION);
// const program = require('mongoose').model(constant.PROGRAM);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
const course = require('mongoose').model(constant.DIGI_COURSE);
const {
    removeStudentSchedule,
    addingStudentSchedule,
    updateStudentGroupFlatCacheData,
} = require('./student_group_services');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
// const course = require('mongoose').model(constant.COURSE);
// const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);

// exports.level_list = async (req, res) => {
//     const program_data = await base_control.get_list(program, { isDeleted: false }, {});
//     if (program_data.status) {
//         let id = null;
//         program_data.data.forEach((main_element) => {
//             if (
//                 main_element.no === req.query.program_no &&
//                 main_element.name.substring(
//                     main_element.name.length - 3,
//                     main_element.name.length,
//                 ) === '2.0'
//             ) {
//                 id = main_element._id;
//             }
//         });

//         const aggre = [
//             { $match: { _program_id: id } },
//             { $match: { isDeleted: false } },
//             { $addFields: { level_rotation: { level_no: '$level', rotation: '$rotation' } } },
//             { $sort: { year: 1, 'level_rotation.level_no': 1 } },
//             {
//                 $group: {
//                     _id: '$year',
//                     id: { $first: '$_id' },
//                     year: { $first: '$year' },
//                     level: { $push: '$level_rotation' },
//                 },
//             },
//             { $sort: { year: 1, 'level.level_no': 1 } },
//             { $project: { year: 1, level: 1 } },
//         ];

//         const doc = await base_control.get_aggregate(credit_hours_individual, aggre);
//         console.log(doc);
//         common_files.com_response(res, 200, true, 'Year Level list', doc.data);
//     } else {
//         common_files.com_response(res, 500, false, 'Pls check Program no', 'Pls check Program no');
//     }
// };

// const { clearItem, allStudentGroupYesterday } = require('../../service/cache.service');

// // Updating Student Group Flat Caching Data
// const updateStudentGroupFlatCacheData = async () => {
//     clearItem('allStudentGroup');
//     await allStudentGroupYesterday();
// };

exports.fyd_import = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_list = req.body.students.map((i) => i.academic_no);
        const user_checks = await base_control.get_list(
            user,
            {
                $or: [{ _id: ObjectId(req.body._user_id) }, { user_id: student_list }],
                isDeleted: false,
            },
            {
                name: 1,
                gender: 1,
                user_id: 1,
            },
        );
        const staff_loc = user_checks.status
            ? user_checks.data.findIndex((i) => i._id.toString() === req.body._user_id.toString())
            : -1;
        if (!user_checks.status && staff_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const users = [];
        const user_ids = [];
        const already_exist = [];
        const duplicate = [];
        const not_found = [];
        const imported = [];
        const course_status = [];
        const mark = [];
        const courses = student_group_check.data.groups[student_group_row].courses.map(
            (i) => i._course_id,
        );
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }
        const male_user_ids = [];
        const female_user_ids = [];
        let max_mark = 100;
        max_mark =
            student_group_check.data.groups[student_group_row].rotation === 'yes' ? 100 : 100;
        await req.body.students.forEach((element) => {
            const pos = user_checks.data.findIndex(
                (i) => i.user_id.toString() === element.academic_no.toString(),
            ); //Here we are checking in user data(users collection)
            const dub = student_group_check.data.groups[student_group_row].students.findIndex(
                (i) => i.academic_no === element.academic_no,
            ); //Here we are checking in student group
            if (dub === -1) {
                //Here we are checking in student group
                if (pos !== -1) {
                    //Here we are checking in user data(users collection)
                    if (user_ids.indexOf(user_checks.data[pos]._id) === -1) {
                        if (
                            parseFloat(element.mark) < 0 ||
                            parseFloat(element.mark) > parseFloat(max_mark)
                        ) {
                            mark.push(element);
                        } else {
                            user_ids.push(user_checks.data[pos]._id);
                            users.push({
                                _student_id: user_checks.data[pos]._id,
                                academic_no: user_checks.data[pos].user_id,
                                name: user_checks.data[pos].name,
                                gender: user_checks.data[pos].gender,
                                mark: element.mark,
                                imported_on: common_fun.timestampNow(),
                                _imported_by: req.body._user_id,
                                imported_by: user_checks.data[staff_loc].name,
                                course_group_status: course_status,
                            });
                            imported.push(element);
                            if (user_checks.data[pos].gender === constant.GENDER.MALE)
                                male_user_ids.push(user_checks.data[pos]._id);
                            else female_user_ids.push(user_checks.data[pos]._id);
                        }
                    } else {
                        duplicate.push(element);
                    }
                } else {
                    not_found.push(element);
                }
            } else {
                already_exist.push(element);
            }
        });
        const objs = {
            $push: {
                'groups.$[i].ungrouped': user_ids,
                'groups.$[i].students': users,
            },
        };
        const filter = {
            arrayFilters: [
                {
                    'i.level': req.body.level,
                    'i.term': req.body.batch,
                },
            ],
        };
        const response = {
            male_count: male_user_ids.length,
            female_count: female_user_ids.length,
            not_found,
            duplicate,
            already_exist,
            imported,
            mark,
        };
        const doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_ADD_STUDENT'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SUCCESSFULLY_STUDENT_ADDED'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
const checkDuplicate = function (stud, index, academic_no) {
    const academic_nos_arr = stud.map((ele) => ele.academic_no);
    let flag = 0;
    const first_ind = academic_nos_arr.indexOf(academic_no);
    const last_ind = academic_nos_arr.lastIndexOf(academic_no);
    if (first_ind !== last_ind) flag = 1;
    return flag;
};
exports.data_check = async (req, res) => {
    try {
        const not_found = [];
        const duplicate = [];
        const data_check_arr = [];
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_list = req.body.students.map((i) => i.academic_no);
        const user_data = await base_control.get_list(
            user,
            { user_id: student_list, user_type: constant.EVENT_WHOM.STUDENT, isDeleted: false },
            { name: 1, gender: 1, user_id: 1 },
        );
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                    ),
                );
        const group_level_no = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (group_level_no === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        req.body.students.forEach((element, index) => {
            const pos = user_data.data.findIndex(
                (i) => i.user_id.toString() === element.academic_no.toString(),
            ); //Here we are checking in user data(users collection)
            // if (pos === -1) return res.status(404).send(common_files.response_function(res, 404, false, "Academic no is not found", 'Academic no is not found'));
            const dub = student_group_check.data.groups[group_level_no].students.findIndex(
                (i) => i.academic_no === element.academic_no,
            ); //Here we are checking in student group
            // if (dub === -1) return res.status(404).send(common_files.response_function(res, 404, false, "Unable to find academic number in student group", 'Unable to find academic number in student group'));
            const dup_in_excel = checkDuplicate(req.body.students, index, element.academic_no); // Check duplicate in excel
            if (dub === -1) {
                //Here we are checking in student group
                if (pos !== -1) {
                    //Here we are checking in user data(users collection)
                    if (dup_in_excel)
                        data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                } else {
                    not_found.push({ user_data: element, message: 'User not found' });
                    if (dup_in_excel)
                        data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                    else
                        data_check_arr.push({
                            user_data: element,
                            message: 'Invalid/Unregistered entry',
                        });
                }
            } else {
                duplicate.push({ user_data: element, message: 'Duplicate' });
                if (dup_in_excel)
                    data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                else
                    data_check_arr.push({
                        user_data: element,
                        message: 'Existing entry (will not be imported)',
                    });
            }
            //Also change this along for Rotation
            if (student_group_check.data.groups[group_level_no].rotation === 'yes') {
                if (parseFloat(element.mark) < 0 || parseFloat(element.mark) > 100) {
                    data_check_arr.push({
                        user_data: element,
                        message: 'CGPA range should be between 0 to 100',
                    });
                }
            } else {
                if (parseFloat(element.mark) < 0 || parseFloat(element.mark) > 100) {
                    data_check_arr.push({
                        user_data: element,
                        message: 'Mark Range should be between 0 to 100',
                    });
                }
            }
        });
        if (not_found.length === 0 && duplicate.length === 0 && data_check_arr.length === 0)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('DATA_CHECK_VALIDATION_SUCCESSFUL'),
                        [],
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('DATA_CHECK_ISSUE'),
                    data_check_arr,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(404)
            .send(
                common_files.response_function(res, 500, false, req.t('CATCH'), error.toString()),
            );
    }
};

exports.import_students = async (req, res) => {
    try {
        const program_check = await base_control.get_list(program, { isDeleted: false }, {});
        const institution_calendar_checks = await base_control.get(
            institution_calendar,
            { _id: req.body._institution_calendar_id, isDeleted: false },
            {},
        );
        const user_checks = await base_control.get(
            user,
            { _id: req.body._user_id, isDeleted: false },
            {},
        );
        const user_data = await base_control.get_list(
            user,
            { user_type: constant.EVENT_WHOM.STUDENT, isDeleted: false },
            { name: 1, gender: 1, user_id: 1 },
        );
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                    ),
                );

        const users = [];
        const user_ids = [];
        let objs;
        let obj;

        if (program_check.status && institution_calendar_checks.status && user_checks.status) {
            const program_cri = [];
            const program_ids = [];
            let program_no = null;
            program_check.data.forEach((element) => {
                //Check Program_id
                if (element._id === req.body._program_id) {
                    program_no = element.no;
                }
            });
            program_check.data.forEach((element) => {
                if (program_no === element.no) {
                    //Here Pushing same program no's ids
                    program_ids.push(element._id);
                    const ver = element.name.substring(
                        element.name.length - 3,
                        element.name.length,
                    ); //Version splitting from program name(Pharm 1.0)
                    program_cri.push({
                        _id: element._id,
                        name: element.name,
                        no: element.no,
                        version: ver,
                    });
                }
            });

            if (program_cri.findIndex((i) => i.version === req.body.curriculum) !== -1) {
                //Check Curriculum
                const student_group_check = await base_control.get(
                    student_group,
                    {
                        'master._program_id':
                            program_cri[
                                program_cri.findIndex((i) => i.version === req.body.curriculum)
                            ]._id,
                        'groups.curriculum': req.body.curriculum,
                        _institution_calendar_id: institution_calendar_checks.data._id,
                        'groups.term': req.body.batch,
                        'master.year': req.body.year,
                        'groups.level': req.body.level,
                        isDeleted: false,
                    },
                    {},
                );
                //                console.log(student_group_check);
                if (!student_group_check.status) {
                    //Check Student Group data exist by program_id,curriculum,term,year,level
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('NO_GROUP_FOUND_CHECK_THE_IDS'),
                                req.t('NO_GROUP_FOUND_CHECK_THE_IDS'),
                            ),
                        );
                }
                console.log('Updating');
                await req.body.student.forEach(async (element) => {
                    const pos = user_data.data.findIndex((i) => i.user_id === element.academic_no); //Here we are checking in user data(users collection)
                    const dub = student_group_check.data.groups[0].students.findIndex(
                        (i) => i.academic_no === element.academic_no,
                    ); //Here we are checking in student group
                    if (dub === -1) {
                        //Here we are checking in student group
                        if (pos !== -1) {
                            //Here we are checking in user data(users collection)
                            user_ids.push(user_data.data[pos]._id);
                            users.push({
                                _student_id: user_data.data[pos]._id,
                                academic_no: user_data.data[pos].user_id,
                                name: user_data.data[pos].name,
                                gender: user_data.data[pos].gender,
                                mark: element.mark,
                                imported_on: common_fun.timestampNow(),
                                _imported_by: req.body._user_id,
                                imported_by: user_checks.data.name,
                            });
                            //imported.push({"user_data":element,"message":"Already Exist"});
                        } else {
                            //not_found.push({"user_data":element,"message":"User not found"});
                        }
                    } else {
                        //duplicate.push({"user_data":element,"message":"Duplicate"});
                    }

                    objs = {
                        $push: {
                            'groups.$[i].ungrouped': user_ids,
                            'groups.$[i].students': users,
                        },
                    };
                    filter = {
                        arrayFilters: [
                            {
                                'i.level': req.body.level,
                                'i.term': req.body.batch,
                            },
                        ],
                    };

                    //let uquery = { 'master._program_id': program_cri[program_cri.findIndex(i => i.version === req.body.curriculum)]._id, 'groups.curriculum': req.body.curriculum, _institution_calendar_id: institution_calendar_checks.data._id, 'groups.term': req.body.batch, 'master.year': req.body.year, 'groups.level': req.body.level, 'isDeleted': false };
                    const uquery = {
                        'master._program_id':
                            program_cri[
                                program_cri.findIndex((i) => i.version === req.body.curriculum)
                            ]._id,
                        _institution_calendar_id: institution_calendar_checks.data._id,
                        'master.year': req.body.year,
                        isDeleted: false,
                    };
                    obj = await base_control.update_condition_array_filter(
                        student_group,
                        uquery,
                        objs,
                        filter,
                    );
                    console.log(obj);
                });
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                });
                /* if (obj.status) {
                        common_files.com_response(res, 200, true, "Students are updated Successfully");
                    } else {
                        common_files.com_response(res, 404, false, "Unable to Import student pls retry", "Unable to Import student pls retry");
                    } */
            } else {
                common_files.com_response(
                    res,
                    404,
                    false,
                    req.t('CHECK_CURRICULUM_VERSION'),
                    req.t('CHECK_CURRICULUM_VERSION_ITS_NOT_PRESENT_IN_SYSTEM'),
                );
            }
        } else {
            common_files.com_response(
                res,
                404,
                false,
                req.t('ID_NOT_MATCH'),
                req.t('CHECK_PARSING_REFERENCE_ID'),
            );
        }
    } catch (error) {
        console.log(error);
        common_files.com_response(res, 500, false, req.t('CATCH'), error.toString());
    }
};

exports.save_import_students = async (req, res) => {
    try {
        let student_data = [];
        let group_settings = [];
        let group_excess_count = 0;
        let ind = -1;
        let institution_calendar_id = '';
        if (req.body.batch === constant.BATCH.REGULAR) {
            institution_calendar_id = ObjectId(req.body._institution_calendar_id);
        } else {
            const ins_cal = await base_control.get_list_sort(
                institution_calendar,
                { isDeleted: false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED },
                { calendar_name: 1, start_date: 1, end_date: 1 },
                { updatedAt: -1 },
            );
            const ins_loc = ins_cal.data.findIndex(
                (i) => i._id.toString() === req.body._institution_calendar_id.toString(),
            );
            if (ins_loc + 1 === ins_cal.data.length)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('THERE_IS_NO_PREVIOUS_ACADEMIC_CALENDAR'),
                            req.t('THERE_IS_NO_PREVIOUS_ACADEMIC_CALENDAR'),
                        ),
                    );
            institution_calendar_id = ObjectId(ins_cal.data[ins_loc + 1]._id);
        }
        const user_checks = await base_control.get(
            user,
            { _id: req.body._user_id, isDeleted: false },
            { name: 1 },
        );
        if (!user_checks.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('IMPORTER_NOT_FOUND'),
                        req.t('IMPORTER_NOT_FOUND'),
                    ),
                );
        const student_group_data = await base_control.get(
            student_group,
            {
                'master._program_id': ObjectId(req.body._program_id),
                _institution_calendar_id: ObjectId(institution_calendar_id),
                isDeleted: false,
            },
            {},
        );
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_DATA_ARE_NOT_FOUND'),
                        req.t('STUDENT_DATA_ARE_NOT_FOUND'),
                    ),
                );
        ind = student_group_data.data.groups.findIndex(
            (element) => element.term === req.body.batch && element.level === req.body.from_level,
        );
        if (ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('DATA_NOT_FOUND'),
                        req.t('DATA_NOT_FOUND'),
                    ),
                );
        student_data = student_group_data.data.groups[ind].students;
        group_settings = student_group_data.data.groups[ind].group_setting;
        group_excess_count = student_group_data.data.groups[ind].group_excess_count
            ? student_group_data.data.groups[ind].group_excess_count
            : 0;
        let grouped_student_arr = [];
        const grouped_student_details = [];
        const course_status = [];
        const group_setting_datas = [];
        let group_setting_groups = [];
        let group_name = student_group_data.data.groups[ind].group_name;
        group_name = group_name.substring(0, group_name.length - 2) + '2L';
        let name;
        // Loop for get groups students
        for (let i = 0; i < group_settings.length; i++) {
            if (group_settings[i].gender === constant.GENDER.MALE) {
                name = `${group_name}-M-G`;
            } else if (group_settings[i].gender === constant.GENDER.FEMALE) {
                name = `${group_name}-F-G`;
            } else if (group_settings[i].gender === constant.GENDER.BOTH) {
                name = `${group_name}-MF-G`;
            }
            group_setting_groups = [];
            for (let j = 0; j < group_settings[i].groups.length; j++) {
                group_setting_groups.push({
                    _student_ids: group_settings[i].groups[j]._student_ids,
                    group_no: group_settings[i].groups[j].group_no,
                    group_name: name + group_settings[i].groups[j].group_no.toString(),
                });
                grouped_student_arr = grouped_student_arr.concat(
                    group_settings[i].groups[j]._student_ids,
                );
            }
            group_setting_datas.push({
                gender_type: group_settings[i].gender_type,
                gender: group_settings[i].gender,
                no_of_group: group_settings[i].no_of_group,
                no_of_student: group_settings[i].no_of_student,
                groups: group_setting_groups,
            });
        }
        //loop for pick matching group student details
        const student_group_data_current = await base_control.get(
            student_group,
            {
                'master._program_id': ObjectId(req.body._program_id),
                _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                isDeleted: false,
            },
            {},
        );
        interim_ind = student_group_data_current.data.groups.findIndex(
            (element) => element.term === req.body.batch && element.level === req.body.to_level,
        );
        if (interim_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('LEVEL_2_IS_NOT_FOUND'),
                        req.t('LEVEL_2_IS_NOT_FOUND'),
                    ),
                );
        const courses = student_group_data_current.data.groups[interim_ind].courses.map(
            (i) => i._course_id,
        );
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }
        for (let i = 0; i < student_data.length; i++) {
            const grouped_student_ind = grouped_student_arr.findIndex((ele) =>
                ele.equals(student_data[i]._student_id),
            );
            if (grouped_student_ind !== -1)
                grouped_student_details.push({
                    _student_id: student_data[i]._student_id,
                    name: student_data[i].name,
                    imported_by: user_checks.data.name,
                    master_group_status: 'pending',
                    academic_no: student_data[i].academic_no,
                    gender: student_data[i].gender,
                    mark: student_data[i].mark,
                    imported_on: common_fun.timestampNow(),
                    _imported_by: req.body._user_id,
                    course_group_status: course_status,
                });
            // grouped_student_details.push(student_data[i]);
        }
        const obj = {
            $set: {
                'groups.$[i].students': grouped_student_details,
                'groups.$[i].group_setting': group_setting_datas,
                'groups.$[i].group_excess_count': group_excess_count,
            },
        };
        const filter = {
            arrayFilters: [
                {
                    'i.level': req.body.to_level,
                    'i.term': req.body.batch,
                },
            ],
        };
        const query = {
            'master._program_id': ObjectId(req.body._program_id),
            _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
            isDeleted: false,
        };
        const result = await base_control.update_condition_array_filter(
            student_group,
            query,
            obj,
            filter,
        );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (result.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('STUDENT_DATA_UPDATED_SUCCESSFULLY'),
                        result.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_DATA_ARE_NOT_UPDATED'),
                    req.t('STUDENT_DATA_ARE_NOT_UPDATED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.com_response(res, 500, false, req.t('ERROR_CATCH'), error.toString()),
            );
    }
};

exports.group_list_filter = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        const objs = {
            _id: student_group_data.data._id,
            _institution_calendar_id: student_group_data.data._institution_calendar_id,
            group_name: student_group_data.data.groups[group_loc].group_name,
            master: student_group_data.data.master,
            group_excess_count: student_group_data.data.groups[group_loc].group_excess_count,
        };
        const students = [];
        let ung_count = 0;
        const group_student_list = [];
        let all_student_id = [];
        student_group_data.data.groups[group_loc].ungrouped.forEach((element) => {
            const std_data =
                student_group_data.data.groups[group_loc].students[
                    student_group_data.data.groups[group_loc].students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ];
            if (std_data.gender === req.params.gender) {
                ung_count++;
                // all_std++;
                all_student_id.push(element);
            }
        });
        group_student_list.push({ title: 'ungrouped', students_id: all_student_id });
        const g_list = { all: 0, ungrouped: ung_count };
        const g_count_list = {};
        if (student_group_data.data.groups[group_loc].rotation === 'yes') {
            student_group_data.data.groups[group_loc].rotation_group_setting.forEach((element) => {
                if (element.gender === req.params.gender) {
                    const gc = 'group' + element.group_no;
                    g_list[gc] = element._student_ids.length;
                    g_count_list[gc] = element.no_of_student;
                    all_student_id = all_student_id.concat(element._student_ids);
                    group_student_list.push({ title: gc, students_id: element._student_ids });
                }
            });
        } else {
            student_group_data.data.groups[group_loc].group_setting.forEach((element) => {
                if (element.gender === req.params.gender) {
                    element.groups.forEach((sub_element) => {
                        const gc = 'group' + sub_element.group_no;
                        g_list[gc] = sub_element._student_ids.length;
                        g_count_list[gc] = element.no_of_student;
                        all_student_id = all_student_id.concat(sub_element._student_ids);
                        group_student_list.push({
                            title: gc,
                            students_id: sub_element._student_ids,
                        });
                    });
                }
            });
        }
        group_student_list.push({ title: 'all', students_id: all_student_id });
        g_list.all = all_student_id.length;
        Object.assign(objs, { groups_list: g_list });
        Object.assign(objs, { groups_list_capacity: g_count_list });
        const group_data =
            group_student_list[group_student_list.findIndex((i) => i.title === req.params.group)];
        if (group_data === undefined)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PLEASE_CHECK_LEVEL_OR_TERM_OR_GENDER_OR_GROUP_NO'),
                        req.t('PLEASE_CHECK_LEVEL_OR_TERM_OR_GENDER_OR_GROUP_NO'),
                    ),
                );
        group_data.students_id.forEach((element) => {
            students.push(
                student_group_data.data.groups[group_loc].students[
                    student_group_data.data.groups[group_loc].students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ],
            );
        });
        Object.assign(objs, { students });
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('STUDENT_GROUP_DATA'), objs),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.setting_get = async (req, res) => {
    try {
        const query = {
            _id: ObjectId(req.params.id),
            'groups.term': req.params.batch,
            'groups.level': req.params.level,
            isDeleted: false,
        };
        const project = {};
        const doc = await base_control.get(student_group, query, project);

        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        doc.data,
                    ),
                );
        const group_ind = doc.data.groups.findIndex(
            (ele) => ele.term === req.params.batch && ele.level === req.params.level,
        );
        if (group_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );

        // let ic_get = await base_control.get(institution_calendar, { _id: ObjectId(doc.data._institution_calendar_id) }, { calendar_name: 1 });
        // let p_get = await base_control.get(program, { _id: ObjectId(doc.data.master._program_id) }, { name: 1 });

        // let group_name = ic_get.data.calendar_name + '-'
        //     + p_get.data.name.substring(0, 1) + 'P' + '-'
        //     + ((req.params.batch) ? 'RT' : 'IT') + '-'
        //     + doc.data.master.year + 'Y' + '-'
        //     + p_get.data.name.substring(0, 1) + 'P:' + doc.data.groups[group_ind].curriculum + '-'
        //     + doc.data.groups[group_ind].level + 'L';
        let male_count = 0;
        let female_count = 0;
        let grouped_status = false;
        //doc.data.groups[group_ind].group_setting
        const male_group_ind = doc.data.groups[group_ind].group_setting.findIndex(
            (ele) => ele.gender === constant.GENDER.MALE,
        );
        // if (male_group_ind === -1) return res.status(404).send(common_files.response_function(res, 404, false, "Male group setting is not found", "Male group setting is not found"));
        const group_name_male_arr = [];
        let male_obj = {};
        if (male_group_ind === -1) {
            male_obj = {
                group_name: group_name_male_arr,
                gender: constant.GENDER.MALE,
                no_of_group: 0,
                no_of_student: 0,
            };
        } else {
            for (
                let i = 0;
                i < doc.data.groups[group_ind].group_setting[male_group_ind].groups.length;
                i++
            ) {
                if (!grouped_status)
                    grouped_status =
                        doc.data.groups[group_ind].group_setting[male_group_ind].groups[i]
                            ._student_ids.length !== 0;
                group_name_male_arr.push(
                    doc.data.groups[group_ind].group_setting[male_group_ind].groups[i].group_name,
                );
            }
            male_obj = {
                group_name: group_name_male_arr,
                gender: doc.data.groups[group_ind].group_setting[male_group_ind].gender,
                no_of_group: doc.data.groups[group_ind].group_setting[male_group_ind].no_of_group,
                no_of_student:
                    doc.data.groups[group_ind].group_setting[male_group_ind].no_of_student,
            };
        }
        const female_group_ind = doc.data.groups[group_ind].group_setting.findIndex(
            (ele) => ele.gender === constant.GENDER.FEMALE,
        );
        // if (female_group_ind === -1) return res.status(404).send(common_files.response_function(res, 404, false, "Female group setting is not found", "Female group setting is not found"));
        const group_name_female_arr = [];
        let female_obj = {};
        if (female_group_ind === -1) {
            female_obj = {
                group_name: group_name_female_arr,
                gender: constant.GENDER.FEMALE,
                no_of_group: 0,
                no_of_student: 0,
            };
        } else {
            for (
                let i = 0;
                i < doc.data.groups[group_ind].group_setting[female_group_ind].groups.length;
                i++
            ) {
                if (!grouped_status)
                    grouped_status =
                        doc.data.groups[group_ind].group_setting[female_group_ind].groups[i]
                            ._student_ids.length !== 0;
                group_name_female_arr.push(
                    doc.data.groups[group_ind].group_setting[female_group_ind].groups[i].group_name,
                );
            }
            female_obj = {
                group_name: group_name_female_arr,
                gender: doc.data.groups[group_ind].group_setting[female_group_ind].gender,
                no_of_group: doc.data.groups[group_ind].group_setting[female_group_ind].no_of_group,
                no_of_student:
                    doc.data.groups[group_ind].group_setting[female_group_ind].no_of_student,
            };
        }
        const objs = {
            program_no: doc.data.master.program_no,
            program_name: doc.data.master.program_name,
            year: doc.data.master.year,
            level: doc.data.groups[group_ind].level,
            master_group_name: doc.data.groups[group_ind].group_name,
            //setting: doc.data.groups[group_ind].group_setting,
            groups: {
                male: male_obj,
                female: female_obj,
            },
            total: doc.data.groups[group_ind].ungrouped.length,
        };
        doc.data.groups[group_ind].ungrouped.forEach((element) => {
            const student_ind = doc.data.groups[group_ind].students.findIndex(
                (i) => i._student_id.toString() === element.toString(),
            );
            // if (student_ind === -1) return res.status(404).send(common_files.response_function(res, 404, false, "Student is not found", "Student is not found"));
            const std_data = doc.data.groups[group_ind].students[student_ind];
            if (std_data.gender === constant.GENDER.MALE) {
                male_count++;
            } else {
                female_count++;
            }
        });
        Object.assign(objs, { male_count, female_count });
        Object.assign(objs, {
            rotation_count: doc.data.groups[group_ind].rotation_count,
            grouped_status,
        });
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('GROUP_SETTING'), objs));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.group_student_count = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const group_list = [];
        doc.data.group_setting.forEach((element) => {
            element.groups.forEach((sub_element) => {
                const split = sub_element.group_name.split('-');
                const obj = {
                    group_no: sub_element.group_no,
                    group_name: sub_element.group_name,
                    group: split[split.length - 2] + '-' + split[split.length - 1],
                    gender: element.gender,
                    total: element.no_of_student,
                    occupied: sub_element._student_ids.length,
                };
                group_list.push(obj);
            });
        });

        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_SETTING_PAGE'), group_list);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.group_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const student_group_data = await base_control.get(
            student_group,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                _id: ObjectId(req.body._id),
                isDeleted: false,
            },
            {},
        );
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                    ),
                );
        let ind_group_setting = [];
        const groups_setting = [];
        let g_type = '';
        const group_name =
            student_group_data.data.groups[
                student_group_data.data.groups.findIndex((i) => i.level === req.body.level)
            ].group_name;
        req.body.groups.forEach((element) => {
            ind_group_setting = [];
            let name = `${group_name}-MF-G`;
            if (student_group_data.data.group_mode === 'rotation') {
                if (element.gender === constant.GENDER.MALE) {
                    name = `${group_name}-M-RG`;
                    g_type = 'specific';
                } else if (element.gender === constant.GENDER.FEMALE) {
                    name = `${group_name}-F-RG`;
                    g_type = 'specific';
                } else if (element.gender === constant.GENDER.BOTH) {
                    name = `${group_name}-MF-RG`;
                    g_type = 'mixed';
                }
            } else {
                if (element.gender === constant.GENDER.MALE) {
                    name = `${group_name}-M-G`;
                    g_type = 'specific';
                } else if (element.gender === constant.GENDER.FEMALE) {
                    name = `${group_name}-F-G`;
                    g_type = 'specific';
                } else if (element.gender === constant.GENDER.BOTH) {
                    name = `${group_name}-MF-G`;
                    g_type = 'mixed';
                }
            }
            const gs_obj = {
                gender_type: g_type,
                gender: element.gender,
                no_of_group: element.no_of_group,
                no_of_student: element.no_of_students,
                // excess_count: req.body.excess_count
            };
            for (let i = 1; i <= element.no_of_group; i++) {
                const igs_obj = {
                    group_no: i,
                    group_name: name + i.toString(),
                };
                ind_group_setting.push(igs_obj);
            }
            Object.assign(gs_obj, { groups: ind_group_setting });
            groups_setting.push(gs_obj);
        });
        let objects = { $set: { 'groups.$[i].group_setting': groups_setting } };
        let filter = {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
        };
        const obj = await base_control.update_condition_array_filter(
            student_group,
            { _id: ObjectId(req.body._id), isDeleted: false },
            objects,
            filter,
        );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        const courses =
            student_group_data.data.groups[
                student_group_data.data.groups.findIndex((i) => i.level === req.body.level)
            ].courses;
        for (element of courses) {
            objects = {
                $set: { 'groups.$[i].courses.$[j].setting': [] },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': element._course_id },
                ],
            };
            await base_control.update_condition_array_filter(
                student_group,
                { _id: ObjectId(req.body._id), isDeleted: false },
                objects,
                filter,
            );
        }
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (!obj.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_SET_SETTING_PLS_RETRY'),
                        req.t('UNABLE_TO_SET_SETTING_PLS_RETRY'),
                    ),
                );
        res.status(200).send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('STUDENTS_GROUP_SETTING_IS_CREATED'),
                req.t('STUDENTS_GROUP_SETTING_IS_CREATED'),
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.excess_count_change = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const project = {};
        let objs;
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        if (req.body.to === 'level') {
            objs = {
                $set: { 'groups.$[k].group_excess_count': req.body.count },
            };
        } else if (req.body.to === 'course') {
            objs = {
                $set: { 'groups.$[k].course_excess_count': req.body.count },
            };
        }
        const filter = {
            arrayFilters: [{ 'k.term': req.body.batch, 'k.level': req.body.level }],
        };
        const doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_SET_EXCESS_COUNT'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('EXCESS_COUNT_SUCCESSFULLY_ASSIGNED'),
                    req.t('EXCESS_COUNT_SUCCESSFULLY_ASSIGNED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

// Staff Notification Push
async function staffNotification(
    courseIds,
    batch,
    studentName,
    level_name,
    calendar_name,
    group_no,
) {
    //Course Details
    const courseDetails = await base_control.get_list(
        course,
        {
            _id: { $in: courseIds },
            isDeleted: false,
        },
        {
            course_name: 1,
            course_code: 1,
            coordinators: 1,
        },
    );
    let staffData = [];
    for (coordinatorsElement of courseDetails.data) {
        const datas = coordinatorsElement.coordinators.find((ele) => ele && ele.term === batch);
        if (datas) staffData = [...staffData, ...[datas]];
    }
    if (staffData.length != 0) {
        const user_data = await base_control.get_list(
            user,
            {
                _id: staffData.map((ele) => ele._user_id),
                isDeleted: false,
            },
            { name: 1, user_id: 1, email: 1, mobile: 1 },
        );
        if (user_data.status) {
            for (staffElement of user_data.data) {
                const data = staffElement;
                if (data) {
                    const name = common_fun.nameFormatter(data.name);
                    const term = batch.charAt(0).toUpperCase() + batch.slice(1);
                    const email_message =
                        '<p>Dear ' +
                        name +
                        ',<br>' +
                        common_fun.emailGreetingContent() +
                        'This email is to inform that the ' +
                        level_name +
                        ' year (' +
                        term +
                        ')' +
                        ' group for the academic year <b>' +
                        calendar_name +
                        '</b> has been changed for the  Student (' +
                        studentName +
                        '):<br>' +
                        level_name +
                        ' group : ' +
                        group_no +
                        '<br>For more details, kindly visit the Academic Affairs Office.<br>Thank you,<br>' +
                        common_fun.emailRegardsContent() +
                        '</p>';
                    const sms_message =
                        'DigiClass - Alert : (' +
                        studentName +
                        ') ' +
                        level_name +
                        ' group has been changed' +
                        ' ,Kindly check the mail.';
                    if (data.email !== undefined && data.email !== '')
                        common_fun.send_email(data.email, 'DigiClass Alert', email_message);
                    if (data.mobile !== undefined && data.mobile !== '')
                        common_fun.send_sms(data.mobile, sms_message);
                }
            }
        }
    }
}

exports.group_change = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        isDeleted: false,
    };
    const project = {};
    const student_group_data = await base_control.get(student_group, query, project);
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_GROUP_NOT_FOUND'),
                    req.t('STUDENT_GROUP_NOT_FOUND'),
                ),
            );
    const group_loc = student_group_data.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (group_loc === -1)
        return res
            .status(400)
            .send(
                common_files.response_function(
                    res,
                    400,
                    false,
                    req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                ),
            );
    let filter;
    let objs;
    let setting_loc;
    let setting_group_loc;
    let excess = 0;
    let capacity = 0;
    let strength = 0;
    // console.log(student_group_data.data.groups[group_loc].rotation);
    if (student_group_data.data.groups[group_loc].rotation === 'yes') {
        setting_loc = student_group_data.data.groups[group_loc].rotation_group_setting.findIndex(
            (i) =>
                i.gender === req.body.gender &&
                i.group_no.toString() === req.body.new_group_no.toString(),
        );
        if (setting_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                    ),
                );
        capacity =
            student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                .no_of_student;
        strength =
            student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                ._student_ids.length + 1;
    } else {
        setting_loc = student_group_data.data.groups[group_loc].group_setting.findIndex(
            (i) => i.gender === req.body.gender,
        );
        if (setting_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                    ),
                );
        setting_group_loc = student_group_data.data.groups[group_loc].group_setting[
            setting_loc
        ].groups.findIndex((i) => i.group_no.toString() === req.body.new_group_no.toString());
        if (setting_group_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_NEW_GROUP'),
                        req.t('STUDENT_GROUP_NOT_FOUND_ON_NEW_GROUP'),
                    ),
                );
        capacity =
            student_group_data.data.groups[group_loc].group_setting[setting_loc].no_of_student;
        strength =
            student_group_data.data.groups[group_loc].group_setting[setting_loc].groups[
                setting_group_loc
            ]._student_ids.length + 1;
    }
    excess = student_group_data.data.groups[group_loc].group_excess_count
        ? student_group_data.data.groups[group_loc].group_excess_count
        : 0;
    if (!(strength <= excess + capacity))
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                    req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                ),
            );
    if (student_group_data.data.groups[group_loc].rotation === 'yes') {
        objs = {
            $pull: {
                'groups.$[k].rotation_group_setting.$[j]._student_ids': ObjectId(
                    req.body._student_ids,
                ),
            },
            $push: {
                'groups.$[k].rotation_group_setting.$[l]._student_ids': ObjectId(
                    req.body._student_ids,
                ),
            },
        };
        filter = {
            arrayFilters: [
                { 'k.term': req.body.batch, 'k.level': req.body.level },
                { 'j.gender': req.body.gender, 'j.group_no': parseInt(req.body.old_group_no) },
                { 'l.gender': req.body.gender, 'l.group_no': parseInt(req.body.new_group_no) },
            ],
        };
    } else {
        objs = {
            $pull: {
                'groups.$[k].group_setting.$[i].groups.$[j]._student_ids': ObjectId(
                    req.body._student_ids,
                ),
            },
            $push: {
                'groups.$[k].group_setting.$[i].groups.$[l]._student_ids': ObjectId(
                    req.body._student_ids,
                ),
            },
        };
        filter = {
            arrayFilters: [
                { 'k.term': req.body.batch, 'k.level': req.body.level },
                { 'i.gender': req.body.gender },
                { 'j.group_no': parseInt(req.body.old_group_no) },
                { 'l.group_no': parseInt(req.body.new_group_no) },
            ],
        };
    }
    // Need to remove student in course groups
    console.log(
        'Remove ',
        await base_control.update_condition_array_filter(student_group, query, objs, filter),
    );
    let doc = { status: true, data: [] };
    for (const element of student_group_data.data.groups[group_loc].courses) {
        const gro =
            element.setting[
                element.setting.findIndex(
                    (i) =>
                        i._group_no.toString() === req.body.old_group_no.toString() &&
                        i.gender === req.body.gender,
                )
            ];
        if (gro) {
            if (gro.ungrouped.indexOf(req.body._student_ids.toString()) !== -1) {
                objs = {
                    $pull: {
                        'groups.$[k].courses.$[i].setting.$[j].ungrouped': ObjectId(
                            req.body._student_ids,
                        ),
                    },
                    $push: {
                        'groups.$[k].courses.$[i].setting.$[l].ungrouped': ObjectId(
                            req.body._student_ids,
                        ),
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'k.term': req.body.batch, 'k.level': req.body.level },
                        { 'i._course_id': ObjectId(element._course_id) },
                        { 'j._group_no': parseInt(req.body.old_group_no) },
                        { 'l._group_no': parseInt(req.body.new_group_no) },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            } else {
                for (sub_element of gro.session_setting) {
                    for (group_element of sub_element.groups) {
                        if (
                            group_element._student_ids.find(
                                (i) => i.toString() === req.body._student_ids.toString(),
                            )
                        ) {
                            objs = {
                                $pull: {
                                    'groups.$[k].courses.$[i].setting.$[j].session_setting.$[n].groups.$[l]._student_ids':
                                        ObjectId(req.body._student_ids),
                                },
                                $set: {
                                    'groups.$[k].students.$[s].course_group_status.$[c].status':
                                        constant.PENDING,
                                },
                            };
                            filter = {
                                arrayFilters: [
                                    { 'k.term': req.body.batch, 'k.level': req.body.level },
                                    { 'i._course_id': ObjectId(element._course_id) },
                                    {
                                        'j._group_no': parseInt(gro._group_no),
                                        'j.gender': req.body.gender,
                                    },
                                    {
                                        'n.session_type': sub_element.session_type,
                                        'n.delivery_type': sub_element.delivery_type,
                                    },
                                    { 'l.group_no': parseInt(group_element.group_no) },
                                    { 's._student_id': ObjectId(req.body._student_ids) },
                                    { 'c._course_id': ObjectId(element._course_id) },
                                ],
                            };
                            doc = await base_control.update_condition_array_filter(
                                student_group,
                                query,
                                objs,
                                filter,
                            );
                            console.log('2 ', doc);
                        }
                    }
                }
                objs = {
                    $push: {
                        'groups.$[k].courses.$[i].setting.$[m].ungrouped': ObjectId(
                            req.body._student_ids,
                        ),
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'k.term': req.body.batch, 'k.level': req.body.level },
                        { 'i._course_id': ObjectId(element._course_id) },
                        { 'm._group_no': req.body.new_group_no, 'm.gender': req.body.gender },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
                // console.log('3 ', doc);
            }
        }
    }
    // doc = { status: true, data: 'Updated Successfully' };
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                    doc.data,
                ),
            );
    await removeStudentSchedule(
        student_group_data.data._institution_id,
        student_group_data.data._institution_calendar_id,
        req.body.batch,
        req.body.level,
        undefined,
        [req.body._student_ids],
    );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    //Notify User about group change
    const student_loc_status = student_group_data.data.groups[group_loc].students.findIndex(
        (i) => i._student_id.toString() === req.body._student_ids.toString(),
    );
    if (
        student_group_data.data.groups[group_loc].students[student_loc_status]
            .master_group_status !== 'pending'
    ) {
        const institution_calendar_data = await base_control.get(
            institution_calendar,
            { _id: ObjectId(student_group_data.data._institution_calendar_id), isDeleted: false },
            { calendar_name: 1 },
        );
        const level_name =
            student_group_data.data.groups[group_loc].rotation === 'yes'
                ? 'Rotation'
                : 'Foundation';
        const user_data = await base_control.get(
            user,
            {
                _id: ObjectId(req.body._student_ids),
                user_type: constant.EVENT_WHOM.STUDENT,
                isDeleted: false,
            },
            { name: 1, user_id: 1, email: 1, mobile: 1 },
        );
        const email_message =
            '<p>Dear v_name,<br>' +
            common_fun.emailGreetingContent() +
            'This email is to inform  that your ' +
            level_name +
            ' year group for the academic year <b>v_academic</b> has been changed as <b>' +
            level_name +
            ' group group_no</b>. For more details, kindly visit the Academic Affairs Office.<br><br>' +
            'Thank you, <br>' +
            common_fun.emailRegardsContent() +
            '</p>';
        const sms_message =
            'Your ' +
            level_name +
            ' group is changed. Please check your registered email for details. For any query, visit Academic Affairs Office';
        const name = user_data.data.name.middle
            ? user_data.data.name.first +
              ' ' +
              user_data.data.name.middle +
              ' ' +
              user_data.data.name.last
            : user_data.data.name.first + ' ' + user_data.data.name.last;
        const replace_data = {
            v_name: name,
            v_academic: institution_calendar_data.data.calendar_name,
            group_no: req.body.new_group_no,
        };
        const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
        const message = email_message.replace(re, (matched) => replace_data[matched]);
        if (user_data.data.email !== undefined && user_data.data.email !== '')
            common_fun.send_email(
                user_data.data.email,
                'DigiClass Alert - Your ' + level_name + ' Groups Change',
                message,
            );
        const msg = 'DigiClass - ' + sms_message;
        if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
            common_fun.send_sms(user_data.data.mobile, msg);
        // staffNotification
        const courseIds = student_group_data.data.groups[group_loc].courses.map(
            (ele) => ele._course_id,
        );
        staffNotification(
            courseIds,
            req.body.batch,
            name,
            level_name,
            institution_calendar_data.data.calendar_name,
            req.body.new_group_no,
        );
    }
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                doc.data,
            ),
        );
};

exports.student_search = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const populate = { path: 'program._program_id', select: { _id: 1, name: 1, no: 1 } };
    const query = {
        user_id: req.params.academic_no,
        user_type: constant.EVENT_WHOM.STUDENT,
        isDeleted: false,
        isActive: true,
    };
    const doc = await base_control.get_list_populate(
        user,
        query,
        { _id: 1, name: 1, gender: 1, 'program.__program_id': 1 },
        populate,
    );
    if (!doc.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                ),
            );
    const query1 = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.params.id),
        'groups.level': req.params.level,
        'groups.term': req.params.batch,
        isDeleted: false,
    };
    const student_group_check = await base_control.get(student_group, query1, {});
    if (!student_group_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_GROUP_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const level_pos = student_group_check.data.groups.findIndex(
        (i) => i.level === req.params.level && i.term === req.params.batch,
    );
    const student_pos = student_group_check.data.groups[level_pos].students.findIndex(
        (i) => i.academic_no === req.params.academic_no,
    );
    const data = [{ group_name: student_group_check.data.groups[level_pos].group_name }];
    if (student_pos !== -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    409,
                    false,
                    req.t('THIS_STUDENT_ALREADY_IN_THIS_LEVEL'),
                    data,
                ),
            );
    res.status(200).send(
        common_files.response_function(
            res,
            200,
            true,
            req.t('STUDENT_DETAILS'),
            doc.data /* user_formate.user_ID(doc.data) */,
        ),
    );
};

exports.add_student_group = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        'groups.level': req.body.level,
        'groups.term': req.body.batch,
        isDeleted: false,
    };
    const student_group_check = await base_control.get(student_group, query, {});
    if (!student_group_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const user_checks = await base_control.get_list(
        user,
        { _id: { $in: [req.body._user_id, req.body._student_id] }, isDeleted: false },
        {},
    );
    if (!user_checks.status && user_checks.data.length !== 2)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                ),
            );
    const student_loc = user_checks.data.findIndex(
        (i) => i._id.toString() === req.body._student_id.toString(),
    );
    const staff_loc = user_checks.data.findIndex(
        (i) => i._id.toString() === req.body._user_id.toString(),
    );
    if (student_loc === -1 || staff_loc === -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                ),
            );
    const level_pos = student_group_check.data.groups.findIndex(
        (i) => i.level === req.body.level && i.term === req.body.batch,
    );
    if (level_pos === -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );

    for (const element of student_group_check.data.groups[level_pos].courses) {
        const courseObjs = {
            $pull: {
                'groups.$[k].courses.$[i]._removed_student_ids': ObjectId(req.body._student_id),
            },
        };
        const courseFilter = {
            arrayFilters: [
                { 'k.term': req.body.batch, 'k.level': req.body.level },
                { 'i._course_id': ObjectId(element._course_id) },
            ],
        };
        console.log(
            await base_control.update_condition_array_filter(
                student_group,
                query,
                courseObjs,
                courseFilter,
            ),
        );
    }

    const course_status = [];
    const courses = student_group_check.data.groups[level_pos].courses.map((i) => i._course_id);
    for (cro of courses) {
        course_status.push({ _course_id: cro, status: 'pending' });
    }
    const objs = {
        $push: {
            'groups.$[i].students': {
                _student_id: user_checks.data[student_loc]._id,
                academic_no: user_checks.data[student_loc].user_id,
                name: user_checks.data[student_loc].name,
                gender: user_checks.data[student_loc].gender,
                mark: req.body.mark,
                imported_on: common_fun.timestampNow(),
                _imported_by: req.body._user_id,
                imported_by: user_checks.data[staff_loc].name,
                course_group_status: course_status,
            },
            'groups.$[i].ungrouped': user_checks.data[student_loc]._id,
        },
    };
    const filter = {
        arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
    };
    const doc = await base_control.update_condition_array_filter(
        student_group,
        query,
        objs,
        filter,
    );
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_ADD_STUDENT'),
                    doc.data,
                ),
            );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
            ),
        );
};

exports.fyd_student_delete = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const group_course = student_group_data.data.groups[student_group_row].courses;
        const group_setting = student_group_data.data.groups[
            student_group_row
        ].group_setting.findIndex((i) => i.gender === req.body.gender);
        if (group_setting === -1) {
            const objs = {
                $pull: {
                    'groups.$[i].students': { _student_id: { $in: req.body._student_ids } },
                    'groups.$[i].ungrouped': { $in: req.body._student_ids },
                },
            };
            const filter = {
                arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
            };
            const doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (!doc.status)
                return res
                    .status(410)
                    .send(
                        common_files.response_function(
                            res,
                            410,
                            false,
                            req.t('UNABLE_TO_REMOVE_STUDENT'),
                            doc.data,
                        ),
                    );
            updateStudentGroupFlatCacheData();
            await updateStudentGroupRedisKey({
                courseId: req.body._course_id,
                level: req.body.level,
                batch: req.body.batch,
            });
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
                        req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
                    ),
                );
        }
        const gender_setting =
            student_group_data.data.groups[student_group_row].group_setting[group_setting].groups;
        let doc;
        const group = [];
        for (let index = 0; index < gender_setting.length; index++) {
            const element = gender_setting[index].group_no;
            group.push(element);
        }
        for (sub_element of group) {
            const objs = {
                $pull: {
                    'groups.$[i].students': { _student_id: { $in: req.body._student_ids } },
                    'groups.$[i].ungrouped': { $in: req.body._student_ids },
                    'groups.$[i].group_setting.$[j].groups.$[k]._student_ids': {
                        $in: req.body._student_ids,
                    },
                },
            };
            const filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j.gender': req.body.gender },
                    { 'k.group_no': sub_element },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            console.log('group student removed ', doc);
        }

        // Need to remove based on course
        // return res.send(group_course);
        const course_data = [];
        let setting_data = [];
        let session_setting_data = [];
        let groups_data = [];
        let master_remove = [];
        let un_group = [];
        let student_ids = [];
        for (course_element of group_course) {
            // console.log(course_element._removed_student_ids.filter(item => !remove.includes((item).toString())));
            master_remove = course_element._removed_student_ids.filter(
                (item) => !req.body._student_ids.includes(item.toString()),
            );
            setting_data = [];
            for (course_setting_element of course_element.setting) {
                session_setting_data = [];
                for (course_session_setting of course_setting_element.session_setting) {
                    groups_data = [];
                    for (course_groups of course_session_setting.groups) {
                        student_ids = course_groups._student_ids.filter(
                            (item) => !req.body._student_ids.includes(item.toString()),
                        );
                        groups_data.push({
                            _student_ids: student_ids,
                            _id: course_groups._id,
                            group_no: course_groups.group_no,
                            group_name: course_groups.group_name,
                        });
                    }
                    session_setting_data.push({
                        _id: course_session_setting._id,
                        group_name: course_session_setting.group_name,
                        session_type: course_session_setting.session_type,
                        delivery_type: course_session_setting.delivery_type,
                        no_of_group: course_session_setting.no_of_group,
                        no_of_student: course_session_setting.no_of_student,
                        groups: groups_data,
                    });
                }
                un_group = course_setting_element.ungrouped.filter(
                    (item) => !req.body._student_ids.includes(item.toString()),
                );
                setting_data.push({
                    ungrouped: un_group,
                    _id: course_setting_element._id,
                    _group_no: course_setting_element._group_no,
                    gender: course_setting_element.gender,
                    session_setting: session_setting_data,
                });
            }
            course_data.push({
                _removed_student_ids: master_remove,
                _id: course_element._id,
                _course_id: course_element._course_id,
                course_name: course_element.course_name,
                course_no: course_element.course_no,
                course_type: course_element.course_type,
                session_types: course_element.session_types,
                setting: setting_data,
            });
        }
        const objs = {
            $set: {
                'groups.$[i].courses': course_data,
            },
        };
        const filter = {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
        };
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);

        /* for (let index = 0; index < group_course.length; index++) {
            const ids = group_course[index]._course_id
            id.push(ids)
            const setting = group_course[index].setting
            for (let j = 0; j < setting.length; j++) {
                const element = setting[j]._group_no;
                grp_no.push(element)
            }
        }
        for (elements of id) {
            let group_course_row = student_group_data.data.groups[student_group_row].courses.findIndex(i => i._course_id === elements);
            if (group_course_row !== -1) {
                let group_course_session = student_group_data.data.groups[student_group_row].courses[group_course_row].session_types;
                for (sub_element of grp_no) {
                    let group_course_setting = student_group_data.data.groups[student_group_row].courses[group_course_row].setting.findIndex(i => (i.gender === req.body.gender && i._group_no === sub_element));
                    if (group_course_setting !== -1) {
                        let session_settings = student_group_data.data.groups[student_group_row].courses[group_course_row].setting[group_course_setting].session_setting
                        for (ele of group_course_session) {
                            let session_setting = session_settings.findIndex(i => i.session_type === ele.symbol);
                            if (session_setting !== -1) {
                                let group = student_group_data.data.groups[student_group_row].courses[group_course_row].setting[group_course_setting].session_setting[session_setting].groups
                                for (sub_ele of group) {
                                    let objs = {
                                        $pull: {
                                            'groups.$[i].courses.$[u]._removed_student_ids': { $in: req.body._student_ids },
                                            'groups.$[i].courses.$[u].setting.$[p].ungrouped': { $in: req.body._student_ids },
                                            'groups.$[i].courses.$[u].setting.$[p].session_setting.$[l].groups.$[m]._student_ids': { $in: req.body._student_ids }
                                        }
                                    };
                                    let filter = {
                                        arrayFilters: [
                                            { 'i.term': req.body.batch, 'i.level': req.body.level },
                                            { 'u._course_id': elements },
                                            { 'p.gender': req.body.gender, 'p._group_no': sub_element },
                                            { 'l.session_type': ele.symbol },
                                            { 'm.group_no': sub_ele.group_no }
                                        ]
                                    }
                                    doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
                                }
                            }
                        }
                    }
                }
            }
        } */
        await removeStudentSchedule(
            student_group_data.data._institution_id,
            student_group_data.data._institution_calendar_id,
            req.body.batch,
            req.body.level,
            undefined,
            req.body._student_ids,
        );
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_REMOVE_STUDENT'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
                    req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.student_bulk_delete = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _id: ObjectId(req.body._id), isDeleted: false };
    const project = {};
    const student_group_data = await base_control.get(student_group, query, project);
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_ids = req.body._student_ids;
    let doc;
    for (element of student_ids) {
        const filter = {
            arrayFilters: [
                { 'i.term': req.body.batch, 'i.level': req.body.level },
                { 'j.gender': req.body.gender },
                { 'k._student_ids': element },
            ],
        };
        const objs = {
            $pull: {
                'groups.$[i].students': { _student_id: { $in: req.body._student_ids } },
                'groups.$[i].ungrouped': { $in: req.body._student_ids },
                'groups.$[i].group_setting.$[j].groups.$[k]._student_ids': {
                    $in: req.body._student_ids,
                },
            },
        };
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
    }
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('BULK_DELETING_STUDENTS_FROM_GROUP'),
                    req.t('BULK_DELETING_STUDENTS_FROM_GROUP'),
                ),
            );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('DELETED_STUDENTS_FROM_ALL_GROUPS'),
                req.t('DELETED_STUDENTS_FROM_ALL_GROUPS'),
            ),
        );
};

exports.fyd_student_count_group = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        let institution_calendar_id = '';
        if (req.params.batch === constant.BATCH.REGULAR) {
            institution_calendar_id = ObjectId(req.params.institution);
        } else {
            const ins_cal = await base_control.get_list_sort(
                institution_calendar,
                { isDeleted: false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED },
                { calendar_name: 1, start_date: 1, end_date: 1 },
                { updatedAt: -1 },
            );
            const ins_loc = ins_cal.data.findIndex(
                (i) => i._id.toString() === req.params.institution.toString(),
            );
            if (ins_loc + 1 === ins_cal.data.length)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('THERE_IS_NO_PREVIOUS_ACADEMIC_CALENDAR'),
                            req.t('THERE_IS_NO_PREVIOUS_ACADEMIC_CALENDAR'),
                        ),
                    );
            institution_calendar_id = ObjectId(ins_cal.data[ins_loc + 1]._id);
        }
        const student_group_data = await base_control.get(
            student_group,
            {
                'master._program_id': ObjectId(req.params.program),
                _institution_calendar_id: ObjectId(institution_calendar_id),
                isDeleted: false,
            },
            {},
        );
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const group_count = [];
        const group = student_group_data.data.groups.findIndex(
            (i) => i.level === '1' && i.term === req.params.batch,
        );
        const group_settings = student_group_data.data.groups[group].group_setting;
        if (group_settings.length === 0)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('NO_GROUPS_CREATED_IN_LEVEL_1'),
                        req.t('NO_GROUPS_CREATED_IN_LEVEL_1'),
                    ),
                );
        const male_groups = student_group_data.data.groups[group].group_setting.findIndex(
            (i) => i.gender === 'male',
        );
        const male_std_group =
            student_group_data.data.groups[group].group_setting[male_groups].groups;
        if (male_std_group.length === 0)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('NO_MALE_GROUPS_CREATED_IN_LEVEL_1'),
                        req.t('NO_MALE_GROUPS_CREATED_IN_LEVEL_1'),
                    ),
                );
        const male_count = male_std_group.reduce((count, i) => count + i._student_ids.length, 0);
        const female_groups = student_group_data.data.groups[group].group_setting.findIndex(
            (i) => i.gender === 'female',
        );
        const female_std_group =
            student_group_data.data.groups[group].group_setting[female_groups].groups;
        if (female_std_group.length === 0)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('NO_FEMALE_GROUPS_CREATED_IN_LEVEL_1'),
                        req.t('NO_FEMALE_GROUPS_CREATED_IN_LEVEL_1'),
                    ),
                );
        const female_count = female_std_group.reduce(
            (count, i) => count + i._student_ids.length,
            0,
        );
        const setting = [];
        group_settings.forEach((sub_element) => {
            sub_element.groups.forEach((elements) => {
                const obj = {
                    name: elements.group_no,
                    group_name: 'Foundation group ' + elements.group_no,
                    gender: sub_element.gender,
                    groups_count: sub_element.groups.length,
                    strength: elements._student_ids.length,
                };
                setting.push(obj);
            });
        });
        group_count.push({
            group: setting,
            male_students: male_count,
            female_students: female_count,
            Total_students: male_count + female_count,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('GOT_LEVEL_1_STUDENT_GROUP_COUNT'),
                    group_count,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.get_student = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = {
            user_id: req.params.academic_no,
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const user_data = await base_control.get(user, query, project);

        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );

        const query_stud_group = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: req.params.id,
            isDeleted: false,
            isActive: true,
        };
        const student_group_data = await base_control.get_list(student_group, query_stud_group, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );

        const student_arr = [];
        let ungrouped_student_arr = [];
        const groups_ind = student_group_data.data[0].groups.findIndex(
            (element) => element.level === req.params.level && element.term === req.params.batch,
        );
        if (groups_ind === -1)
            res.status(404).send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
        const courses_ind = student_group_data.data[0].groups[groups_ind].courses.findIndex(
            (element) => element._course_id.toString() === req.params.course_id.toString(),
        );
        if (courses_ind === -1)
            res.status(404).send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_COURSE'),
                    req.t('UNABLE_TO_FIND_COURSE'),
                ),
            );
        const settings = student_group_data.data[0].groups[groups_ind].courses[courses_ind].setting;
        for (let i = 0; i < settings.length; i++) {
            ungrouped_student_arr = ungrouped_student_arr.concat(settings[i].ungrouped);
            for (let j = 0; j < settings[i].session_setting.length; j++) {
                for (let k = 0; k < settings[i].session_setting[j].groups.length; k++) {
                    // getting entire group student ids
                    //student_arr = student_arr.concat(settings[i].session_setting[j].groups[k]._student_ids)
                    student_arr.push({
                        student_ids: settings[i].session_setting[j].groups[k]._student_ids,
                        group_no: settings[i].session_setting[j].groups[k].group_no,
                        group_name: settings[i].session_setting[j].groups[k].group_name,
                    });
                }
            }
        }
        const ungrouped_ind = ungrouped_student_arr.findIndex((ele) =>
            ele.equals(user_data.data._id),
        );
        if (ungrouped_ind !== -1)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('STUDENT_EXIST_IN_UNGROUPED'),
                        [],
                    ),
                );

        let ind = -1;
        let group_no = '';
        let group_name = '';
        for (let i = 0; i < student_arr.length; i++) {
            ind = student_arr[i].student_ids.findIndex((ele) => ele.equals(user_data.data._id));
            if (ind !== -1) {
                group_no = student_arr[i].group_no;
                group_name = student_arr[i].group_name;
                break;
            }
        }
        const student_details = [
            {
                name: user_data.data.name,
                gender: user_data.data.gender,
                enrolled_program: user_data.data.programs,
                group_no,
                group_name,
                _id: user_data.data._id,
            },
        ];

        if (student_arr.length > 0) {
            if (ind !== -1)
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            false,
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE'),
                            student_details,
                        ),
                    );
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                        student_details,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                    student_details,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
exports.fyd_export = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );

        const objs = {
            _id: student_group_data.data._id,
            _institution_calendar_id: student_group_data.data._institution_calendar_id,
            group_name: student_group_data.data.groups[group_loc].group_name,
            master: student_group_data.data.master,
        };
        let students = [];
        const male_student = [];
        const female_student = [];
        if (student_group_data.data.groups[group_loc].rotation === 'yes') {
            student_group_data.data.groups[group_loc].rotation_group_setting.forEach((element) => {
                if (element.gender === 'male') {
                    students = [];
                    const gc = 'group' + element.group_no;
                    element._student_ids.forEach((student_element) => {
                        students.push(
                            student_group_data.data.groups[group_loc].students[
                                student_group_data.data.groups[group_loc].students.findIndex(
                                    (i) => i._student_id.toString() === student_element.toString(),
                                )
                            ],
                        );
                    });
                    male_student.push({ name: gc, students });
                } else {
                    students = [];
                    const gc = 'group' + element.group_no;
                    element._student_ids.forEach((student_element) => {
                        students.push(
                            student_group_data.data.groups[group_loc].students[
                                student_group_data.data.groups[group_loc].students.findIndex(
                                    (i) => i._student_id.toString() === student_element.toString(),
                                )
                            ],
                        );
                    });
                    female_student.push({ name: gc, students });
                }
            });
        }
        student_group_data.data.groups[group_loc].group_setting.forEach((element) => {
            if (element.gender === 'male') {
                element.groups.forEach((sub_element) => {
                    students = [];
                    const gc = 'group' + sub_element.group_no;
                    sub_element._student_ids.forEach((student_element) => {
                        students.push(
                            student_group_data.data.groups[group_loc].students[
                                student_group_data.data.groups[group_loc].students.findIndex(
                                    (i) => i._student_id.toString() === student_element.toString(),
                                )
                            ],
                        );
                    });
                    male_student.push({ name: gc, students });
                });
            } else {
                element.groups.forEach((sub_element) => {
                    students = [];
                    const gc = 'group' + sub_element.group_no;
                    sub_element._student_ids.forEach((student_element) => {
                        students.push(
                            student_group_data.data.groups[group_loc].students[
                                student_group_data.data.groups[group_loc].students.findIndex(
                                    (i) => i._student_id.toString() === student_element.toString(),
                                )
                            ],
                        );
                    });
                    female_student.push({ name: gc, students });
                });
            }
        });
        Object.assign(objs, { male: male_student, female: female_student });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('FOUNDATION_STUDENT_GROUP_DATA'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.fyd_group_export = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );

        const objs = {
            _id: student_group_check.data._id,
            _institution_calendar_id: student_group_check.data._institution_calendar_id,
            group_name: student_group_check.data.groups[student_group_row].group_name,
            master: student_group_check.data.master,
        };
        const group_student_list = [];
        let all_student_id = [];
        const course_datas = [];
        student_group_check.data.groups[student_group_row].courses.forEach((element) => {
            if (req.params.course !== 'all') {
                if (element._course_id.toString() === req.params.course.toString()) {
                    const settings =
                        element.setting[
                            element.setting.findIndex(
                                (i) =>
                                    i._group_no.toString() === req.params.fyd_group.toString() &&
                                    i.gender === req.params.gender,
                            )
                        ];
                    course_datas.push({
                        _course_id: element._course_id,
                        course_name: element.course_name,
                        course_no: element.course_no,
                        course_type: element.course_type,
                        setting: settings,
                        students: [],
                    });
                }
            } else {
                const settings =
                    element.setting[
                        element.setting.findIndex(
                            (i) =>
                                i._group_no.toString() === req.params.fyd_group.toString() &&
                                i.gender === req.params.gender,
                        )
                    ];
                course_datas.push({
                    _course_id: element._course_id,
                    course_name: element.course_name,
                    course_no: element.course_no,
                    course_type: element.course_type,
                    setting: settings,
                    students: [],
                });
            }
        });
        group_student_list.push({
            title: 'group' + req.params.fyd_group,
            group_no: req.params.fyd_group,
            course: course_datas,
        });
        course_datas.forEach((sub_element, sub_index) => {
            if (sub_element.setting && sub_element.setting.session_setting) {
                const session_setting = sub_element.setting.session_setting;
                let student = [];
                const temps = [];
                student = [];
                all_student_id = [];
                session_setting.forEach((ses_element) => {
                    ses_element.groups.forEach((group_element) => {
                        all_student_id = all_student_id.concat(group_element._student_ids);
                    });
                });
                all_student_id.forEach((element) => {
                    if (temps.indexOf(element.toString()) === -1) {
                        temps.push(element.toString());
                    }
                });
                all_student_id = [...temps];
                all_student_id = all_student_id.filter(
                    (item) => !sub_element.setting.ungrouped.includes(item.toString()),
                );
                all_student_id.forEach((master_group_element) => {
                    const temp =
                        student_group_check.data.groups[student_group_row].students[
                            student_group_check.data.groups[student_group_row].students.findIndex(
                                (i) => i._student_id.toString() === master_group_element.toString(),
                            )
                        ];
                    const data = {};
                    let course_session_index = -1;
                    if (temp) {
                        session_setting.forEach((session_element) => {
                            if (req.params.delivery !== 'all') {
                                if (
                                    req.params.delivery === session_element.session_type.toString()
                                ) {
                                    session_element.groups.forEach((group_element) => {
                                        if (
                                            group_element._student_ids.findIndex(
                                                (i) =>
                                                    i.toString() ===
                                                    master_group_element.toString(),
                                            ) !== -1
                                        )
                                            course_session_index = 'G' + group_element.group_no;
                                    });
                                    data[session_element.session_type.toString()] =
                                        course_session_index !== -1 ? course_session_index : '-';
                                }
                            } else {
                                session_element.groups.forEach((group_element) => {
                                    if (
                                        group_element._student_ids.findIndex(
                                            (i) => i.toString() === master_group_element.toString(),
                                        ) !== -1
                                    )
                                        course_session_index = 'G' + group_element.group_no;
                                });
                                data[session_element.session_type.toString()] =
                                    course_session_index !== -1 ? course_session_index : '-';
                            }
                        });
                        data._student_id = temp._student_id;
                        data.name = temp.name;
                        data.academic_no = temp.academic_no;
                        data.gender = temp.gender;
                        data.mark = temp.mark;
                        data.imported_on = temp.imported_on;
                        data.imported_by = temp.imported_by;
                        student.push(data);
                    }
                });
                group_student_list[0].course[sub_index].students = student;
                delete group_student_list[0].course[sub_index].setting;
            }
        });
        Object.assign(objs, { group_student_list });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('FOUNDATION_STUDENT_GROUP_DATA'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
exports.individual_data_check = async (req, res) => {
    try {
        // let user_data = await base_control.get_list(user, { user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false }, { name: 1, gender: 1, user_id: 1 });
        const student_list = req.body.students.map((i) => i.academic_no);
        const user_data = await base_control.get_list(
            user,
            { user_id: student_list, user_type: constant.EVENT_WHOM.STUDENT, isDeleted: false },
            { name: 1, gender: 1, user_id: 1 },
        );
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                        req.t('STUDENTS_ARE_NOT_REGISTERED'),
                    ),
                );
        const data_check_arr = [];
        let course_user_arr = [];
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const group_level_ind = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (group_level_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INVALID_LEVEL_AND_TERM'),
                        req.t('INVALID_LEVEL_AND_TERM'),
                    ),
                );
        const course_id_ind = student_group_check.data.groups[group_level_ind].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_id_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INVALID_COURSE_ID'),
                        req.t('INVALID_COURSE_ID'),
                    ),
                );

        //Concat grouped and ungrouped array in course settings
        const settings_arr =
            student_group_check.data.groups[group_level_ind].courses[course_id_ind].setting;
        for (let i = 0; i < settings_arr.length; i++) {
            //settings loop
            course_user_arr = course_user_arr.concat(settings_arr[i].ungrouped); //concat ungrouped arr
            for (let j = 0; j < settings_arr[i].session_setting.length; j++) {
                //session setting loop
                for (let k = 0; k < settings_arr[i].session_setting[j].groups.length; k++) {
                    //grouped data loop
                    course_user_arr = course_user_arr.concat(
                        settings_arr[i].session_setting[j].groups[k]._student_ids,
                    ); //concat grouped arr
                }
            }
        }

        await req.body.students.forEach((element, index) => {
            const dup_in_excel = checkDuplicate(req.body.students, index, element.academic_no); // Check duplicate in excel
            const pos = user_data.data.findIndex((i) => i.user_id === element.academic_no); //Here we are checking in user data(users collection)
            if (pos !== -1) {
                const ind = course_user_arr.findIndex((ele) => ele.equals(user_data.data[pos]._id));
                if (ind !== -1) {
                    if (dup_in_excel)
                        data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                    else
                        data_check_arr.push({
                            user_data: element,
                            message: 'Existing entry (will not be imported)',
                        });
                } else {
                    if (dup_in_excel)
                        data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                }
            } else {
                if (dup_in_excel === 1)
                    data_check_arr.push({ user_data: element, message: 'Duplicate found' });
                else
                    data_check_arr.push({
                        user_data: element,
                        message: 'Invalid/Unregistered entry',
                    });
            }
            if (parseFloat(element.mark) < 0 || parseFloat(element.mark) > 100) {
                data_check_arr.push({
                    user_data: element,
                    message: 'CGPA range should be between 0 to 100',
                });
            }
        });
        if (data_check_arr.length === 0)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, true, 'Data check successful', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('DATA_CHECK_FAILED'),
                    data_check_arr,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.add_student_to_course = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        let objs = {};
        let filter = {};

        // let query_user = { '_id': ObjectId(req.body._student_id), user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false, isActive: true };
        // let user_data = await base_control.get(user, query_user, {});
        // if (!user_data.status)
        //     return res.status(404).send(common_files.response_function(res, 404, false, "Student Academic No not found", req.t('STUDENT A_ADEMIC N_ N_T F_UND')));

        const user_checks = await base_control.get_list(
            user,
            { _id: { $in: [req.body._user_id, req.body._student_id] }, isDeleted: false },
            {},
        );
        if (!user_checks.status && user_checks.data.length !== 2)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const student_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._student_id.toString(),
        );
        const staff_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._user_id.toString(),
        );
        if (student_loc === -1 || staff_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const course_status = [];
        const courses = student_group_check.data.groups[student_group_row].courses.map(
            (i) => i._course_id,
        );
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }
        if (
            student_group_check.data.groups[student_group_row].students.findIndex(
                (i) => i._student_id.toString() === req.body._student_id.toString(),
            ) === -1
        ) {
            objs = {
                $push: {
                    'groups.$[i].students': {
                        _student_id: user_checks.data[student_loc]._id,
                        academic_no: user_checks.data[student_loc].user_id,
                        name: user_checks.data[student_loc].name,
                        gender: user_checks.data[student_loc].gender,
                        mark: req.body.mark,
                        imported_on: common_fun.timestampNow(),
                        _imported_by: req.body._user_id,
                        imported_by: user_checks.data[staff_loc].name,
                        master_group_status: 'published',
                        course_group_status: course_status,
                    },
                    'groups.$[i].courses.$[j].setting.$[k].ungrouped': req.body._student_id,
                },
                $pull: { 'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_id },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    { 'k.gender': user_checks.data[student_loc].gender },
                ],
            };
        } else {
            const levelStudentCourseIndex = student_group_check.data.groups[
                student_group_row
            ].students
                .find(
                    (studentElement) =>
                        studentElement._student_id.toString() === req.body._student_id.toString(),
                )
                .course_group_status.find(
                    (courseGroupStatus) =>
                        courseGroupStatus._course_id.toString() === req.body._course_id.toString(),
                );
            if (levelStudentCourseIndex) {
                objs = {
                    $push: {
                        'groups.$[i].courses.$[j].setting.$[k].ungrouped':
                            user_checks.data[student_loc]._id,
                    },
                    $set: {
                        'groups.$[i].students.$[s].course_group_status.$[m].status': 'pending',
                    },
                    $pull: {
                        'groups.$[i].courses.$[j]._removed_student_ids':
                            user_checks.data[student_loc]._id,
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._course_id': req.body._course_id },
                        { 'k.gender': user_checks.data[student_loc].gender },
                        { 's._student_id': req.body._student_id },
                        { 'm._course_id': req.body._course_id },
                    ],
                };
            } else {
                objs = {
                    $push: {
                        'groups.$[i].courses.$[j].setting.$[k].ungrouped':
                            user_checks.data[student_loc]._id,
                        'groups.$[i].students.$[s].course_group_status': {
                            _course_id: req.body._course_id,
                            status: 'pending',
                        },
                    },
                    $pull: {
                        'groups.$[i].courses.$[j]._removed_student_ids':
                            user_checks.data[student_loc]._id,
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._course_id': req.body._course_id },
                        { 'k.gender': user_checks.data[student_loc].gender },
                        { 's._student_id': req.body._student_id },
                    ],
                };
            }
        }
        const doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UPDATING_ISSUE'),
                        req.t('UPDATING_ISSUE'),
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENT_ADDED_TO_COURSE_SUCCESSFULLY'),
                    req.t('STUDENT_ADDED_TO_COURSE_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
exports.get_student_courses_year_two = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = {
            user_id: req.params.academic_no,
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const user_data = await base_control.get(user, query, project);

        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );

        const query_stud_group = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: req.params._id,
            isDeleted: false,
            isActive: true,
        };
        const student_group_data = await base_control.get_list(student_group, query_stud_group, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );

        const student_arr = [];
        let ungrouped_student_arr = [];
        const groups_ind = student_group_data.data[0].groups.findIndex(
            (element) => element.level === req.params.level && element.term === req.params.batch,
        );
        if (groups_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const courses_ind = student_group_data.data[0].groups[groups_ind].courses.findIndex(
            (element) => element._course_id.toString() === req.params._course_id.toString(),
        );
        if (courses_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('COURSE_IS_NOT_FOUND'),
                        req.t('COURSE_IS_NOT_FOUND'),
                    ),
                );
        const settings = student_group_data.data[0].groups[groups_ind].courses[courses_ind].setting;
        for (let i = 0; i < settings.length; i++) {
            ungrouped_student_arr = ungrouped_student_arr.concat(settings[i].ungrouped);
            for (let j = 0; j < settings[i].session_setting.length; j++) {
                for (let k = 0; k < settings[i].session_setting[j].groups.length; k++) {
                    // getting entire group student ids
                    //student_arr = student_arr.concat(settings[i].session_setting[j].groups[k]._student_ids)
                    student_arr.push({
                        student_ids: settings[i].session_setting[j].groups[k]._student_ids,
                        group_no: settings[i].session_setting[j].groups[k].group_no,
                        group_name: settings[i].session_setting[j].groups[k].group_name,
                    });
                }
            }
        }
        const ungrouped_ind = ungrouped_student_arr.findIndex((ele) =>
            ele.equals(user_data.data._id),
        );
        if (ungrouped_ind !== -1)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('STUDENT_EXIST_IN_UNGROUPED'),
                        [],
                    ),
                );
        let ind = -1;
        let group_no = '';
        let group_name = '';
        for (let i = 0; i < student_arr.length; i++) {
            ind = student_arr[i].student_ids.findIndex((ele) => ele.equals(user_data.data._id));
            // if (ind === -1) return res.status(404).send(common_files.response_function(res, 404, false, "student is not found", 'student is not found'));
            if (ind !== -1) {
                group_no = student_arr[i].group_no;
                group_name = student_arr[i].group_name;
                break;
            }
        }
        const student_details = [
            {
                name: user_data.data.name,
                gender: user_data.data.gender,
                enrolled_program: user_data.data.programs,
                group_no,
                group_name,
                _id: user_data.data._id,
            },
        ];

        if (student_arr.length > 0) {
            if (ind !== -1)
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            true,
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE'),
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE'),
                        ),
                    );
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                        student_details,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                    student_details,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.courseStudentGroupReset = async (req, res) => {
    try {
        const { studentGroupId, courseId, level, term, programId } = req.body;
        const { _institution_id } = req.headers;

        const courseStudentGroupUpdateQuery = {
            _institution_id: common_files.convertToMongoObjectId(_institution_id),
            _id: common_files.convertToMongoObjectId(studentGroupId),
            'groups.courses._course_id': common_files.convertToMongoObjectId(courseId),
            'groups.term': term,
            'groups.level': level,
            isDeleted: false,
            isActive: true,
        };
        const courseStudentGroupUpdate = await student_group.updateOne(
            courseStudentGroupUpdateQuery,
            {
                $set: {
                    'groups.$[levelTerm].courses.$[courseIndex].setting': [],
                },
            },
            {
                arrayFilters: [
                    {
                        'levelTerm.level': level,
                        'levelTerm.term': term,
                    },
                    {
                        'courseIndex._course_id': common_files.convertToMongoObjectId(courseId),
                    },
                ],
            },
        );
        return res.status(404).send({ data: courseStudentGroupUpdate });
    } catch (error) {
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
