const cs = (string) => string.toString();
const mongoose = require('mongoose');
const moment = require('moment');
const axios = require('axios');
const { isEncryptedRoute, encryptDecryptApplicable } = require('../../service/endpoint.util');
const {
    SERVICES: { ENCRYPT_PAYLOAD },
} = require('./util_keys');
const { encryptData } = require('./encrypt_decrypt.util');
const { encryptPayload } = require('../../middleware/encryptionDecryption.service');
const { CLIENT_END } = require('./constants');

module.exports = {
    clone: (object) => {
        return JSON.parse(JSON.stringify(object));
    },

    objectStringConvertor: (data) =>
        Object.fromEntries(Object.entries(data).map(([key, value]) => [key, String(value)])),

    mergeStandardWithCurriculum: (standardSettings, mergeFor, curriculum) => {
        const hasPreRequisite = curriculum.start_at != 1;
        const preRequestYears = hasPreRequisite ? curriculum.start_at - 1 : undefined;
        const domainIds = curriculum.framework.domains.map((domain) => cs(domain._id));
        const preReqArray = [];
        for (let i = 1; i <= preRequestYears; i++) preReqArray.push(i + '_PRE');
        standardSettings = standardSettings.filter((standardSetting) => {
            return (
                (standardSetting.year_program === cs(curriculum.end_at) ||
                    standardSetting.year_program.includes('_PRE')) &&
                domainIds.includes(cs(standardSetting.domain_id))
            );
        });
        if (mergeFor === 'curriculum') {
            const curriculumSettings = curriculum.standard_range_settings;
            standardSettings.forEach((standardSetting) => {
                const FoundCurriculumSetting = curriculumSettings.find(
                    (curriculumSetting) =>
                        curriculumSetting.year === standardSetting.year &&
                        curriculumSetting.year_program === standardSetting.year_program &&
                        cs(curriculumSetting.domain_id) === cs(standardSetting.domain_id),
                );
                if (FoundCurriculumSetting) {
                    standardSetting.higher_limit = FoundCurriculumSetting.higher_limit;
                    standardSetting.lower_limit = FoundCurriculumSetting.lower_limit;
                    standardSetting._id = FoundCurriculumSetting._id;
                    standardSetting.updated_from = 'curriculum';
                }
                standardSetting.isPreRequisite = false;
            });
        }
        if (hasPreRequisite) {
            standardSettings
                .filter((standardSetting) => !standardSetting.year_program.includes('_PRE'))
                .forEach((standardSetting) => {
                    if (standardSetting.year <= preRequestYears) {
                        const preSetting = standardSettings.find((s_setting) => {
                            return (
                                s_setting.year === standardSetting.year &&
                                s_setting.year_program === standardSetting.year + '_PRE' &&
                                cs(s_setting.domain_id) === cs(standardSetting.domain_id)
                            );
                        });
                        if (preSetting) {
                            standardSetting.higher_limit = preSetting.higher_limit;
                            standardSetting.lower_limit = preSetting.lower_limit;
                            standardSetting.isPreRequisite = true;
                        }
                    }
                });
        }
        return standardSettings;
    },

    list_all_response: (
        res,
        status_code,
        status,
        message,
        totalDoc,
        totalPages,
        currentPage,
        result_message,
    ) => {
        const server_response = {
            status_code,
            status,
            message,
            totalDoc,
            totalPages,
            currentPage,
            data: result_message,
        };
        return res.status(status_code).send(server_response);
    },

    listAllResponseWithRequest: (
        req,
        res,
        status_code,
        status,
        message,
        totalDoc,
        totalPages,
        currentPage,
        result_message,
    ) => {
        const translatedMessage = req.t(message);
        const url = req?.originalUrl?.split('?')?.at(0);
        let server_response = {
            status_code,
            status,
            message: translatedMessage,
            totalDoc,
            totalPages,
            currentPage,
            data: result_message,
        };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            server_response = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: {
                              status_code,
                              status,
                              message: translatedMessage,
                              totalDoc,
                              totalPages,
                              currentPage,
                              data: result_message,
                          },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              status,
                              message: translatedMessage,
                              totalDoc,
                              totalPages,
                              currentPage,
                              data: result_message,
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return res.status(status_code).send(server_response);
    },

    com_response(res, status_code, status, message, result_message) {
        const server_response = {
            status_code,
            status,
            message,
            data: result_message,
        };
        return res.status(status_code).send(server_response);
    },
    comResponseWithRequest: (req, res, status_code, status, message, result_message) => {
        const translatedMessage = req.t(message);
        const url = req?.originalUrl?.split('?')?.at(0);
        let server_response = {
            status_code,
            status,
            message: translatedMessage,
            data: result_message,
        };

        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            server_response = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: {
                              status_code,
                              status,
                              message: translatedMessage,
                              data: result_message,
                          },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              status,
                              message: translatedMessage,
                              data: result_message,
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return res.status(status_code).send(server_response);
    },

    list_user_response: (res, status_code, response) => {
        return res.status(status_code).send(response);
    },

    listUserResponse: (req, res, status_code, response) => {
        const url = req?.originalUrl?.split('?')?.at(0);
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            const server_response = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: { ...response },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: { ...response },
                          handledBy: CLIENT_END,
                      }),
            };
            return res.status(status_code).send(server_response);
        }
        return res.status(status_code).send(response);
    },

    response_dub_not(
        res,
        status_code,
        status,
        message,
        result_message,
        imported,
        not_found,
        duplicate,
    ) {
        const server_response = {
            status_code,
            status,
            message,
            data: result_message,
            imported,
            not_found,
            duplicate,
        };
        return res.status(status_code).send(server_response);
    },

    response_function(res, status_code, status, message, result_message) {
        const server_response = {
            status_code,
            status,
            message,
            data: result_message,
        };
        return server_response;
    },
    responseFunctionWithRequest(req, status_code, status, message, result_message) {
        const translatedMessage = req.t(message);
        const url = req?.originalUrl?.split('?')?.at(0);
        let server_response = {
            status_code,
            status,
            message: translatedMessage,
            data: result_message,
        };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            server_response = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: {
                              status_code,
                              status,
                              message: translatedMessage,
                              data: result_message,
                          },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              status,
                              message: translatedMessage,
                              data: result_message,
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return server_response;
    },
    list_all_response_function: (
        res,
        status_code,
        status,
        message,
        totalDoc,
        totalPages,
        currentPage,
        result_message,
    ) => {
        const server_response = {
            status_code,
            status,
            message,
            totalDoc,
            totalPages,
            currentPage,
            data: result_message,
        };
        return server_response;
    },
    listAllResponseFunctionWithRequest: (
        req,
        status_code,
        status,
        message,
        totalDoc,
        totalPages,
        currentPage,
        result_message,
    ) => {
        const translatedMessage = req.t(message);
        const url = req?.originalUrl?.split('?')?.at(0);
        let server_response = {
            status_code,
            status,
            message: translatedMessage,
            totalDoc,
            totalPages,
            currentPage,
            data: result_message,
        };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            server_response = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: {
                              status_code,
                              status,
                              message: translatedMessage,
                              totalDoc,
                              totalPages,
                              currentPage,
                              data: result_message,
                          },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              status,
                              message: translatedMessage,
                              data: result_message,
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }

        return server_response;
    },
    sendResponse(res, code, status, message, data) {
        const response_function = (
            response,
            status_code,
            api_status,
            toast_message,
            result_message,
        ) => {
            const server_response = {
                status_code,
                status: api_status,
                message: toast_message,
                data: result_message,
            };
            return server_response;
        };
        return res.status(code).send(response_function(res, code, status, message, data));
    },

    sendResponseWithRequest(req, res, code, status, message, data) {
        const translatedMessage = req.t(message);
        const url = req && req.originalUrl ? req.originalUrl.split('?')[0] : '';
        let responseData = { status_code: code, status, message: translatedMessage, data };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            responseData = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: { status_code: code, status, message: translatedMessage, data },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: { status_code: code, status, message: translatedMessage, data },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return res.status(code).send(responseData);
    },

    // from DA
    sendResult(res, status_code, message, result_message) {
        const response = {
            status_code,
            message,
            data: result_message,
        };
        return res.status(status_code).send(response);
    },
    sendResultWithRequest(req, res, status_code, message, result_message) {
        const translatedMessage = req.t(message);
        const url = req && req.originalUrl ? req.originalUrl.split('?')[0] : '';
        let responseData = { status_code, message: translatedMessage, data: result_message };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            responseData = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: { status_code, message: translatedMessage, data: result_message },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              message: translatedMessage,
                              data: result_message,
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return res.status(status_code).send(responseData);
    },

    sendErrorResponse(res, status_code, message, e, data) {
        const response = {
            status_code,
            message,
            error: e ? e.message : '',
            data: data || '',
        };
        return res.status(status_code).send(response);
    },

    sendErrorResponseWithRequest(req, res, status_code, message, e, data) {
        const translatedMessage = req.t(message);
        const url = req && req.originalUrl ? req.originalUrl.split('?')[0] : '';
        let responseData = {
            status_code,
            message: translatedMessage,
            error: e ? e.message : '',
            data: data || '',
        };
        if (
            ENCRYPT_PAYLOAD === 'true' &&
            isEncryptedRoute({
                url,
                urlMethod: req?.method,
                applicableTo: encryptDecryptApplicable.response,
            })
        ) {
            const encryptedSessionKey = req?.headers['x-session-key'];
            responseData = {
                data: encryptedSessionKey
                    ? encryptPayload({
                          data: {
                              status_code,
                              message: translatedMessage,
                              error: e ? e.message : '',
                              data: data || '',
                          },
                          encryptedSessionKey,
                      })
                    : encryptData({
                          content: {
                              status_code,
                              message: translatedMessage,
                              error: e ? e.message : '',
                              data: data || '',
                          },
                          handledBy: CLIENT_END,
                      }),
            };
        }
        return res.status(status_code).send(responseData);
    },

    convertToMongoObjectId: (id) =>
        id ? new mongoose.Types.ObjectId(id) : new mongoose.Types.ObjectId(),
    convertToUtcFormat: (date = new Date()) => {
        let utcDate = moment(date).utcOffset(0);
        utcDate.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
        utcDate.toISOString();
        utcDate = moment(utcDate).format();
        return utcDate;
    },
    convertToUtcEndFormat: (date = new Date()) => {
        let utcDate = moment(date).utcOffset(0);
        utcDate.set({ hour: 23, minute: 59, second: 59, millisecond: 0 });
        utcDate.toISOString();
        utcDate = moment(utcDate).format();
        return utcDate;
    },
    convertToUtcTimeFormat: (setDate, setHour, setMinute, setSecond) => {
        let date = new Date(setDate);
        date = date.setHours(setHour, setMinute, setSecond, 0);
        let utcDate = moment(date).utcOffset(0);
        utcDate.toISOString();
        utcDate = moment(utcDate).format();
        return utcDate;
    },
    convertToUtcDateAndTimeFormat: (setDate, setHour, setMinute, setSecond) => {
        let date = new Date(setDate);
        date = date.setHours(setHour, setMinute, setSecond, 0);
        utcDate = moment.utc(moment(date)).format();
        return utcDate;
    },
    cs: (str) => str.toString(),
    query: { isActive: true, isDeleted: false },
    join: (t, a, s) => {
        function format(m) {
            const f = new Intl.DateTimeFormat('en', m);
            return f.format(t);
        }
        return a.map(format).join(s);
    },
    axiosCall: (data) => {
        const config = {
            method: 'post',
            url: process.env.SOCKET_SERVER_URL + '/api/sendSocket',
            headers: {
                'Content-Type': 'application/json',
            },
            data: { data },
            maxContentLength: Infinity,
            maxBodyLength: Infinity,
        };
        axios(config)
            .then()
            .catch(function (error) {
                //console.log(error);
            });
    },
    fileFormatSubTabFilter: (subTab) => {
        let returnRegex;
        switch (subTab) {
            case 'doc':
                returnRegex = { $regex: /.*\.doc$/ };
                break;
            case 'dot':
                returnRegex = { $regex: /.*\.dot$/ };
                break;
            case 'docx':
                returnRegex = { $regex: /.*\.docx$/ };
                break;
            case 'dotx':
                returnRegex = { $regex: /.*\.dotx$/ };
                break;
            case 'docm':
                returnRegex = { $regex: /.*\.docm$/ };
                break;
            case 'dotm':
                returnRegex = { $regex: /.*\.dotm$/ };
                break;
            case 'xls':
                returnRegex = { $regex: /.*\.xls$/ };
                break;
            case 'xlt':
                returnRegex = { $regex: /.*\.xlt$/ };
                break;
            case 'xla':
                returnRegex = { $regex: /.*\.xla$/ };
                break;
            case 'xlsx':
                returnRegex = { $regex: /.*\.xlsx$/ };
                break;
            case 'xltx':
                returnRegex = { $regex: /.*\.xltx$/ };
                break;
            case 'xlsm':
                returnRegex = { $regex: /.*\.xlsm$/ };
                break;
            case 'xltm':
                returnRegex = { $regex: /.*\.xltm$/ };
                break;
            case 'xlam':
                returnRegex = { $regex: /.*\.xlam$/ };
                break;
            case 'xlsb':
                returnRegex = { $regex: /.*\.xlsb$/ };
                break;
            case 'pot':
                returnRegex = { $regex: /.*\.pot$/ };
                break;
            case 'pdf':
                returnRegex = { $regex: /.*\.pdf$/ };
                break;
            case 'ppt':
                returnRegex = { $regex: /.*\.ppt$/ };
                break;
            case 'pps':
                returnRegex = { $regex: /.*\.pps$/ };
                break;
            case 'ppa':
                returnRegex = { $regex: /.*\.ppa$/ };
                break;
            case 'pptx':
                returnRegex = { $regex: /.*\.pptx$/ };
                break;
            case 'potx':
                returnRegex = { $regex: /.*\.potx$/ };
                break;
            case 'ppsx':
                returnRegex = { $regex: /.*\.ppsx$/ };
                break;
            case 'mdb':
                returnRegex = { $regex: /.*\.mdb$/ };
                break;
            case 'gif':
                returnRegex = { $regex: /.*\.gif$/ };
                break;
            case 'mp4':
                returnRegex = { $regex: /.*\.mp4$/ };
                break;
            case 'png':
                returnRegex = { $regex: /.*\.png$/ };
                break;
            case 'jpg':
                returnRegex = { $regex: /.*\.jpg$/ };
                break;
            case 'jpeg':
                returnRegex = { $regex: /.*\.jpeg$/ };
                break;
            case 'tiff':
                returnRegex = { $regex: /.*\.tiff$/ };
                break;
            default:
                returnRegex = { $regex: /.*\.mp4$/ };
                break;
        }
        return returnRegex;
    },
    sortArrayByObjectAscending: (arr, key) => {
        const array = arr;

        array.sort((a, b) => {
            const keyA = a[key];
            const keyB = b[key];
            //  ascending
            if (keyA < keyB) return -1;
            if (keyA > keyB) return 1;
            return 0;
        });

        return array;
    },
    paginator: (items, current_page, per_page_items) => {
        const page = current_page || 1;
        const per_page = per_page_items || 10;
        const offset = (page - 1) * per_page;
        const paginatedItems = items.slice(offset).slice(0, per_page_items);
        const total_pages = Math.ceil(items.length / per_page);

        return {
            page,
            per_page,
            pre_page: page - 1 ? page - 1 : null,
            next_page: total_pages > page ? page + 1 : null,
            total: items.length,
            total_pages,
            data: paginatedItems,
        };
    },
    calculateTeamsDuration: (scheduleStartDateAndTime, scheduleEndDateAndTime, TeamsIntervals) => {
        let difference = 0;
        for (intervals of TeamsIntervals.attendanceIntervals) {
            if (
                new Date(intervals.joinDateTime) > new Date(scheduleStartDateAndTime) &&
                new Date(intervals.joinDateTime) < new Date(scheduleEndDateAndTime) &&
                new Date(scheduleEndDateAndTime) <= new Date(intervals.leaveDateTime)
            ) {
                const changingTime1 = new Date(scheduleEndDateAndTime);
                const changingTime2 = new Date(intervals.joinDateTime);
                difference += Math.floor(
                    (changingTime1.getTime() - changingTime2.getTime()) / 1000,
                );
            } else {
                const changedTime1 = new Date(intervals.leaveDateTime);
                const changedTime2 = new Date(intervals.joinDateTime);
                difference += Math.floor((changedTime1.getTime() - changedTime2.getTime()) / 1000);
            }
        }

        return difference;
    },
};
