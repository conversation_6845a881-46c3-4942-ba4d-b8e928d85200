const program_formate = require('../program/program_formate');

// exports.credit_hours_master_ID_Only = (doc) => {
//     //console.log(doc);
//     let obj = {
//         _id: doc._id,
//         credit_hours_master_title: doc.credit_hours_master_title,
//         division: doc._division_id,
//         subject: doc._subject_id,
//         program: doc._program_id,
//         isActive: doc.isActive,
//         isDeleted: doc.isDeleted
//     }
//     return obj;
// }

module.exports = {
    credit_hours_master: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                course_no: element.course_no,
                credit_hours: element.credit_hours,
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    credit_hours_master_ID: (doc) => {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            course_no: doc.course_no,
            credit_hours: doc.credit_hours,
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_master_ID_Only: function (doc) {
        let obj = {
            _id: doc._id,
            year: doc.year,
            level: doc.level,
            course_no: doc.course_no,
            credit_hours: doc.credit_hours,
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    credit_hours_master_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                year: element.year,
                level: element.level,
                course_no: element.course_no,
                credit_hours: element.credit_hours,
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}