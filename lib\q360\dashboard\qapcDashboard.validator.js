const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.createCategoryItemsValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
            user_id: objectId.error(() => {
                return 'USER_ID_REQUIRED';
            }),
            role_id: objectId.error(() => {
                return 'ROLEID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                institutionCalendarId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'INSTITUTION_CALENDAR_ID';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.updateCategoryItemsValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
            user_id: objectId.error(() => {
                return 'USER_ID_REQUIRED';
            }),
            role_id: objectId.error(() => {
                return 'ROLEID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                institutionCalendarId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'INSTITUTION_CALENDAR_ID';
                    }),
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.dashboardCategorySettingsValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
            user_id: objectId.error(() => {
                return 'USER_ID_REQUIRED';
            }),
            role_id: objectId.error(() => {
                return 'ROLEID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                institutionCalendarId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'INSTITUTION_CALENDAR_ID';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getQADashboardValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
            user_id: objectId.error(() => {
                return 'USER_ID_REQUIRED';
            }),
            role_id: objectId.error(() => {
                return 'ROLEID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                institutionCalendarId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'INSTITUTION_CALENDAR_ID';
                    }),
                startMonth: Joi.number().optional(),
                endMonth: Joi.number().optional(),
            })
            .unknown(true),
    })
    .unknown(true);
