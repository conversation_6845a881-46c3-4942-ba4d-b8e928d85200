const userModulePermissionSchema = require('../../user_module_permission/userModulePermission.model');
const programCalendarSchema = require('../../models/program_calendar');
const digiSurveyBankSchema = require('./digiSurveyBank.model');
const institutionCalendarSchema = require('../../models/institution_calendar');
const { convertToMongoObjectId } = require('../../utility/common');
const {
    PUBLISHED,
    INSTITUTION_CALENDAR_KEY,
    ALL,
    DS_CURRICULUM_KEY,
    DS_LEVEL_KEY,
    DS_COURSE_KEY,
    DS_TERM_KEY,
    ROTATION: { YES },
    DS_PROGRAM_KEY,
    SURVEY_QUESTION_TYPE: { RATING },
    START,
    END,
    GENDER: { MALE, FEMALE, BOTH },
    MALESTAFF,
    FEMALESTAFF,
    MALESTUDENT,
    FEMALESTUDENT,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    SCHEDULE_TYPES: { REGULAR },
    DS_YEAR_KEY,
} = require('../../utility/constants');
const courseScheduleSchema = require('../../models/course_schedule');
const userSchema = require('../../models/user');
const studentGroupSchema = require('../../models/student_group');
const {
    USER_PERMISSION_MODULE_NAMES: { SURVEY },
} = require('../../utility/util_keys');

const processUserAssignedCourses = async ({
    courseDetails,
    userCourseIds,
    rotation,
    rotation_count,
}) => {
    const userCurrentCourseDetails = [];
    courseDetails.forEach(({ _course_id = '', courses_name = '', courses_number = '' }) => {
        if (!userCourseIds.includes(String(_course_id))) {
            userCourseIds.push(String(_course_id));
            userCurrentCourseDetails.push({
                _course_id,
                courses_name,
                courses_number,
                rotation,
                rotationCount: rotation_count,
            });
        }
    });
    return userCurrentCourseDetails;
};

const processUserAssignedPrograms = async ({
    programDetails,
    requiredId,
    selectedCurriculumName,
    selectedYearNo,
    selectedLevelNo,
    selectedTerm,
    userCurriculumIds,
    userCourseIds,
    userTerms,
    userYearId,
    userLevelId,
}) => {
    const curriculumDetails = [];
    const levelDetails = [];
    const temDetails = [];
    const yearDetails = [];
    let courseDetails = [];
    for (const { level = [], _institution_calendar_id } of programDetails) {
        if (level.length) {
            for (const {
                curriculum = '',
                term = '',
                year = '',
                level_no = '',
                course = [],
                rotation = '',
                rotation_course = [],
            } of level) {
                if (
                    [ALL, DS_CURRICULUM_KEY].includes(requiredId) &&
                    !userCurriculumIds.includes(curriculum)
                ) {
                    userCurriculumIds.push(curriculum);
                    curriculumDetails.push({ curriculum });
                }
                if ([ALL, DS_TERM_KEY].includes(requiredId) && !userTerms.includes(term)) {
                    userTerms.push(term);
                    temDetails.push({ term });
                }
                if (requiredId === ALL || (requiredId === DS_YEAR_KEY && term === selectedTerm)) {
                    if (!userYearId.includes(year)) {
                        userYearId.push(year);
                        yearDetails.push({ year });
                    }
                }
                if (
                    requiredId === ALL ||
                    (requiredId === DS_LEVEL_KEY &&
                        term === selectedTerm &&
                        year === selectedYearNo)
                ) {
                    if (!userLevelId.includes(level_no)) {
                        userLevelId.push(level_no);
                        levelDetails.push({ Level: level_no, curriculumName: curriculum });
                    }
                }
                if (
                    requiredId === ALL ||
                    (requiredId === DS_COURSE_KEY &&
                        curriculum === selectedCurriculumName &&
                        term === selectedTerm &&
                        year === selectedYearNo &&
                        level_no === selectedLevelNo)
                ) {
                    if (rotation === YES) {
                        for (const { course = [], rotation_count = 0 } of rotation_course) {
                            const rotationCourseDetails = await processUserAssignedCourses({
                                courseDetails: course,
                                userCourseIds,
                                rotation,
                                rotation_count,
                            });
                            courseDetails = [...courseDetails, ...rotationCourseDetails];
                        }
                    } else {
                        const normalCourseDetails = await processUserAssignedCourses({
                            courseDetails: course,
                            userCourseIds,
                            rotation,
                            rotation_count: 0,
                        });
                        courseDetails = [...courseDetails, ...normalCourseDetails];
                    }
                }
            }
        }
    }
    return {
        curriculumDetails,
        levelDetails,
        courseDetails,
        temDetails,
        yearDetails,
    };
};

exports.getUserPermissionProgramAndCourseIds = async ({
    institutionId,
    userId,
    institutionCalendarId,
    requiredId,
    programId,
    curriculumName,
    term,
    yearNo,
    levelNo,
}) => {
    //ids stored to avoid  duplicates
    const userInstitutionCalendarIds = [];
    const userProgramIds = [];
    const userCurriculumIds = [];
    const userTerms = [];
    const userYearId = [];
    const userLevelId = [];
    const userCourseIds = [];
    // data stored with all details
    let userInstitutionCalendarDetails = [];
    const userProgramDetails = [];
    let userCurriculumDetails = [];
    let userTermDetails = [];
    let userYearDetails = [];
    let userLevelDetails = [];
    let userCourseDetails = [];

    const userPermissionProject = {
        selectedPrograms: 1,
        'selectedCourses._program_id': 1,
        'selectedCourses._institution_calendar_id': 1,
        ...([ALL, DS_CURRICULUM_KEY, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
            'selectedCourses.level.curriculum': 1,
        }),
        ...([ALL, DS_TERM_KEY, DS_YEAR_KEY, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
            'selectedCourses.level.term': 1,
        }),
        ...([ALL, DS_YEAR_KEY, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
            'selectedCourses.level.year': 1,
        }),
        ...([ALL, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
            'selectedCourses.level.level_no': 1,
        }),
        ...([ALL, DS_COURSE_KEY].includes(requiredId) && {
            'selectedCourses.level.rotation': 1,
            'selectedCourses.level.course._course_id': 1,
            'selectedCourses.level.course.courses_name': 1,
            'selectedCourses.level.course.courses_number': 1,
            'selectedCourses.level.rotation_course.rotation_count': 1,
            'selectedCourses.level.rotation_course.course._course_id': 1,
            'selectedCourses.level.rotation_course.course.courses_name': 1,
            'selectedCourses.level.rotation_course.course.courses_number': 1,
        }),
    };
    const userPermissionPopulate = [];
    if ([ALL, DS_PROGRAM_KEY].includes(requiredId)) {
        userPermissionPopulate.push({
            path: 'selectedCourses._program_id',
            select: { name: 1, code: 1 },
        });
    }
    const userPermissionDetails = await userModulePermissionSchema
        .findOne(
            {
                _institution_id: convertToMongoObjectId(institutionId),
                moduleName: SURVEY,
                userId: convertToMongoObjectId(userId),
            },
            userPermissionProject,
        )
        .populate(userPermissionPopulate)
        .lean();
    if (userPermissionDetails) {
        const { selectedCourses = [], selectedPrograms = [] } = userPermissionDetails;
        if (selectedPrograms.length && [ALL, DS_PROGRAM_KEY].includes(requiredId)) {
            const userPermissionProgramDetails = await programCalendarSchema
                .find(
                    {
                        _program_id: { $in: selectedPrograms },
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        status: PUBLISHED,
                        isDeleted: false,
                        isActive: true,
                    },
                    {
                        _program_id: 1,
                    },
                )
                .populate({ path: '_program_id', select: { name: 1, code: 1 } })
                .lean();
            userPermissionProgramDetails.forEach(({ _program_id = [] }) => {
                if (_program_id.length) {
                    const { _id, name, code } = _program_id[0];
                    userProgramIds.push(String(_id));
                    userProgramDetails.push({
                        _id,
                        name,
                        code,
                    });
                }
            });
        }
        if (
            selectedPrograms.length &&
            [
                ALL,
                INSTITUTION_CALENDAR_KEY,
                DS_CURRICULUM_KEY,
                DS_TERM_KEY,
                DS_YEAR_KEY,
                DS_LEVEL_KEY,
                DS_COURSE_KEY,
            ].includes(requiredId)
        ) {
            const programCalendarQuery = {
                ...(requiredId === ALL || requiredId === INSTITUTION_CALENDAR_KEY
                    ? {
                          _program_id: {
                              $in: selectedPrograms.map(
                                  (userSelectedProgramElement) => userSelectedProgramElement,
                              ),
                          },
                      }
                    : {
                          _program_id: convertToMongoObjectId(programId),
                      }),
                ...(institutionCalendarId && {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                }),
                status: PUBLISHED,
                isDeleted: false,
                isActive: true,
            };
            const programCalendarProject = {
                ...(requiredId === INSTITUTION_CALENDAR_KEY && { _institution_calendar_id: 1 }),
                ...((requiredId === ALL || requiredId !== INSTITUTION_CALENDAR_KEY) && {
                    'level.curriculum': 1,
                }),
                ...((requiredId === ALL || requiredId !== DS_CURRICULUM_KEY) && {
                    'level.term': 1,
                }),
                ...([ALL, DS_YEAR_KEY, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
                    'level.year': 1,
                }),
                ...([ALL, DS_LEVEL_KEY, DS_COURSE_KEY].includes(requiredId) && {
                    'level.level_no': 1,
                }),
                ...([ALL, DS_COURSE_KEY].includes(requiredId) && {
                    'level.rotation': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course._course_id': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                }),
            };
            let programCalendarDetails = [];
            if (requiredId === INSTITUTION_CALENDAR_KEY) {
                programCalendarDetails = await programCalendarSchema
                    .distinct('_institution_calendar_id', programCalendarQuery)
                    .lean();
                userInstitutionCalendarIds.push(...programCalendarDetails);
            } else {
                programCalendarDetails = await programCalendarSchema
                    .find(programCalendarQuery, programCalendarProject)
                    .lean();
            }
            if (programCalendarDetails && programCalendarDetails.length) {
                const { curriculumDetails, temDetails, yearDetails, levelDetails, courseDetails } =
                    await processUserAssignedPrograms({
                        programDetails: programCalendarDetails,
                        requiredId,
                        selectedCurriculumName: curriculumName,
                        selectedTerm: term,
                        selectedYearNo: yearNo,
                        selectedLevelNo: levelNo,
                        userCurriculumIds,
                        userTerms,
                        userYearId,
                        userLevelId,
                        userCourseIds,
                    });
                userCurriculumDetails = [...userCurriculumDetails, ...curriculumDetails];
                userTermDetails = [...userTermDetails, ...temDetails];
                userYearDetails = [...userYearDetails, ...yearDetails];
                userLevelDetails = [...userLevelDetails, ...levelDetails];
                userCourseDetails = [...userCourseDetails, ...courseDetails];
            }
        }
        if (selectedCourses.length) {
            if ([ALL, DS_PROGRAM_KEY].includes(requiredId)) {
                selectedCourses.forEach(
                    ({
                        _program_id: { _id = '', name = '', code = '' },
                        _institution_calendar_id = '',
                    }) => {
                        if (String(_institution_calendar_id) === String(institutionCalendarId)) {
                            userProgramIds.push(String(_id));
                            userProgramDetails.push({ _id, name, code });
                        }
                    },
                );
            }
            if (requiredId === INSTITUTION_CALENDAR_KEY) {
                userInstitutionCalendarIds.push(
                    ...new Set([
                        ...selectedCourses.map(
                            ({ _institution_calendar_id }) => _institution_calendar_id,
                        ),
                    ]),
                );
            }
            if (
                [
                    ALL,
                    DS_CURRICULUM_KEY,
                    DS_TERM_KEY,
                    DS_YEAR_KEY,
                    DS_COURSE_KEY,
                    DS_LEVEL_KEY,
                ].includes(requiredId)
            ) {
                let currentProgramDetails = selectedCourses;
                if (requiredId !== ALL) {
                    currentProgramDetails = selectedCourses.filter(
                        ({ _program_id = '', _institution_calendar_id = '' }) =>
                            String(_program_id) === String(programId) &&
                            String(_institution_calendar_id) === String(institutionCalendarId),
                    );
                }
                const { curriculumDetails, temDetails, yearDetails, levelDetails, courseDetails } =
                    await processUserAssignedPrograms({
                        programDetails: currentProgramDetails,
                        requiredId,
                        selectedCurriculumName: curriculumName,
                        selectedTerm: term,
                        selectedYearNo: yearNo,
                        selectedLevelNo: levelNo,
                        userCurriculumIds,
                        userTerms,
                        userYearId,
                        userLevelId,
                        userCourseIds,
                    });
                userCurriculumDetails = [...userCurriculumDetails, ...curriculumDetails];
                userTermDetails = [...userTermDetails, ...temDetails];
                userYearDetails = [...userYearDetails, ...yearDetails];
                userLevelDetails = [...userLevelDetails, ...levelDetails];
                userCourseDetails = [...userCourseDetails, ...courseDetails];
            }
        }
        if (requiredId === INSTITUTION_CALENDAR_KEY && userInstitutionCalendarIds.length) {
            userInstitutionCalendarDetails = await institutionCalendarSchema
                .find(
                    { _id: { $in: userInstitutionCalendarIds } },
                    { calendarId: '$_id', calendarName: '$calendar_name' },
                )
                .sort({ _id: -1 })
                .lean();
        }
    }
    return {
        userInstitutionCalendarDetails,
        userProgramDetails,
        userCurriculumDetails,
        userTermDetails,
        userYearDetails,
        userLevelDetails,
        userCourseDetails,
    };
};

exports.generateFrameWorkQuestions = async ({
    frameWorkDetails,
    surveyQuestions,
    learningOutcomeDetails,
}) => {
    if (!surveyQuestions.pages) {
        surveyQuestions = { pages: [{ elements: [] }] };
    }
    return surveyQuestions.pages.map((pageElement, pageIndex) => {
        if (
            learningOutcomeDetails &&
            learningOutcomeDetails.outcomePlacementOption === START &&
            pageIndex === 0
        ) {
            return {
                ...pageElement,
                elements: [
                    ...frameWorkDetails.map((frameWorkELement) => {
                        const {
                            learningOutcome = '',
                            programId = '',
                            curriculumName = '',
                            yearNo = '',
                            levelNo = '',
                            courseId = '',
                            frameWorkName = '',
                            domainName = '',
                            description = '',
                        } = frameWorkELement;
                        return {
                            type: RATING,
                            name: `${learningOutcomeDetails.prefixSentence} ${description}`,
                            learningOutcome,
                            curriculumName,
                            yearNo,
                            levelNo,
                            courseId,
                            programId,
                            frameWorkName,
                            domainName,
                        };
                    }),
                    ...pageElement.elements,
                ],
            };
        }
        if (
            learningOutcomeDetails &&
            learningOutcomeDetails.outcomePlacementOption === END &&
            pageIndex === surveyQuestions.pages.length - 1
        ) {
            return {
                ...pageElement,
                elements: [
                    ...pageElement.elements,
                    ...frameWorkDetails.map((frameWorkELement) => {
                        const {
                            learningOutcome = '',
                            programId = '',
                            curriculumName = '',
                            yearNo = '',
                            levelNo = '',
                            courseId = '',
                            frameWorkName = '',
                            domainName = '',
                            description = '',
                        } = frameWorkELement;
                        return {
                            type: RATING,
                            name: `${learningOutcomeDetails.prefixSentence} ${description}`,
                            learningOutcome,
                            curriculumName,
                            yearNo,
                            levelNo,
                            courseId,
                            programId,
                            frameWorkName,
                            domainName,
                        };
                    }),
                ],
            };
        }
        return {
            ...pageElement,
        };
    });
};

const handleStudentSelection = ({
    institutionCalendarId,
    programId,
    curriculumName,
    termName,
    yearNo,
    levelNo,
    courseId,
    rotationCount,
    selectedUserGroups,
}) => {
    const groupBasedDetails = new Map();
    const studentGroupFindQuery = [];
    const totalSelectedGroups = selectedUserGroups.filter(
        (groupElement) => groupElement !== MALESTAFF && groupElement !== FEMALESTAFF,
    );
    const isStudentSelected = totalSelectedGroups.length > 0;
    if (isStudentSelected) {
        const groupBasedConstructingKey = `${programId}-${curriculumName}-${termName}-${yearNo}-${levelNo}-${courseId}-${rotationCount}`;
        groupBasedDetails.set(groupBasedConstructingKey, {
            selectedGroupDetails: totalSelectedGroups,
        });
        studentGroupFindQuery.push({
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            'master._program_id': convertToMongoObjectId(programId),
            'master.year': yearNo,
        });
    }
    return { groupBasedDetails, studentGroupFindQuery };
};

const handleStaffSelection = ({
    institutionCalendarId,
    programId,
    termName,
    yearNo,
    levelNo,
    courseId,
    rotationCount,
    selectedUserGroups,
}) => {
    const genderBasedScheduleDetails = new Map();
    const courseScheduleFindQuery = [];
    const isStaffSelected =
        selectedUserGroups.includes(MALESTAFF) || selectedUserGroups.includes(FEMALESTAFF);
    if (isStaffSelected) {
        courseScheduleFindQuery.push({
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            term: termName,
            year_no: yearNo,
            level_no: levelNo,
            _course_id: convertToMongoObjectId(courseId),
            ...(rotationCount && { rotation_count: rotationCount }),
        });
    }
    return { genderBasedScheduleDetails, courseScheduleFindQuery };
};

const updateStaffDetailsWithGender = ({ staffDetailsWithGender, selectedUserGroups }) => {
    const staffGenderMap = new Map();
    staffDetailsWithGender.forEach(({ _id, gender, name, user_id, user_type }) => {
        staffGenderMap.set(String(_id), { gender, name, user_id, user_type, _id });
    });
    const validStaffIds = [];
    if (selectedUserGroups.includes(MALESTAFF) && selectedUserGroups.includes(FEMALESTAFF)) {
        validStaffIds.push(...staffGenderMap.values());
    } else if (selectedUserGroups.includes(MALESTAFF)) {
        validStaffIds.push(...[...staffGenderMap.values()].filter(({ gender }) => gender === MALE));
    } else if (selectedUserGroups.includes(FEMALESTAFF)) {
        validStaffIds.push(
            ...[...staffGenderMap.values()].filter(({ gender }) => gender === FEMALE),
        );
    }
    return validStaffIds;
};

const processStaffDetails = async ({ staffDetails, selectedUserGroups, programId }) => {
    const uniqueStaffIds = new Set();
    const staffIdsWithDetails = new Map();
    staffDetails.forEach(
        ({ staffs = [], term, year_no, level_no, _course_id, rotation_count = 0 }) => {
            staffs.forEach(({ _staff_id }) => {
                uniqueStaffIds.add(String(_staff_id));
                const staffProgramDetailsKey = `${programId}-${term}-${year_no}-${level_no}-${_course_id}-${rotation_count}`;
                const existingStaffProgramDetails = staffIdsWithDetails.get(String(_staff_id));
                if (!existingStaffProgramDetails) {
                    staffIdsWithDetails.set(String(_staff_id), [staffProgramDetailsKey]);
                } else if (!existingStaffProgramDetails.includes(staffProgramDetailsKey)) {
                    existingStaffProgramDetails.push(staffProgramDetailsKey);
                }
            });
        },
    );
    if (uniqueStaffIds.size) {
        const staffDetailsWithGender = await userSchema
            .find(
                { _id: { $in: [...uniqueStaffIds] } },
                { gender: 1, user_type: 1, user_id: 1, name: 1 },
            )
            .lean();
        let totalUniqueStaffIds = updateStaffDetailsWithGender({
            staffDetailsWithGender,
            selectedUserGroups,
        });
        totalUniqueStaffIds = totalUniqueStaffIds.map((totalUniqueIdElement) => {
            const staffProgramDetails = staffIdsWithDetails.get(String(totalUniqueIdElement._id));
            return {
                ...totalUniqueIdElement,
                staffProgramDetails,
            };
        });
        return totalUniqueStaffIds;
    }
    return [];
};

const handleCourseScheduleFindQuery = async ({
    totalCourseScheduleFindQuery,
    selectedUserGroups,
    programId,
}) => {
    if (totalCourseScheduleFindQuery.length) {
        const staffDetails = await courseScheduleSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    type: REGULAR,
                    $or: totalCourseScheduleFindQuery,
                },
                {
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    _course_id: 1,
                    rotation_count: 1,
                    'staffs._staff_id': 1,
                },
            )
            .lean();
        const validUniqueStaffIds = await processStaffDetails({
            staffDetails,
            selectedUserGroups,
            programId,
        });
        return { validUniqueStaffIds };
    }
    return { validUniqueStaffIds: new Set() };
};

const extractGroupName = ({ groupName = '', gender = '', groupMode = '' }) => {
    const parts = groupName.split('-');
    const genderPrefix = gender[0].toUpperCase();
    if (groupMode === ROTATION) {
        return `${genderPrefix}-R${parts[parts.length - 2]}`;
    }
    if (groupMode === FYD) {
        return `${genderPrefix}-${parts[parts.length - 2]}`;
    }
    return '';
};

const shouldAddStudentGroup = ({ selectedGroupDetails, groupMode, gender, groupName }) => {
    if (
        selectedGroupDetails.includes(MALESTUDENT) &&
        selectedGroupDetails.includes(FEMALESTUDENT)
    ) {
        return true;
    }
    if (
        (selectedGroupDetails.includes(MALESTUDENT) && gender === MALE) ||
        (selectedGroupDetails.includes(FEMALESTUDENT) && gender === FEMALE)
    ) {
        return true;
    }
    if (groupMode === ROTATION || groupMode === FYD) {
        const group = extractGroupName({ groupName, groupMode, gender });
        return selectedGroupDetails.includes(group);
    }
    if (groupMode === COURSE) {
        const genderGroup = gender === BOTH ? 'MFG-1' : gender === MALE ? 'MG-1' : 'FG-1';
        return selectedGroupDetails.includes(genderGroup);
    }
    return false;
};

const processSessionSettings = ({
    selectedGroupDetails,
    sessionSettings,
    groupMode,
    gender,
    groupBasedConstructedKey,
    validStudentProgramDetails,
}) => {
    const validStudentIds = new Set();
    const existingStudentProgramDetails = new Map([...validStudentProgramDetails]);
    for (const { group_name, groups = [] } of sessionSettings) {
        if (
            selectedGroupDetails.includes(MALESTUDENT) &&
            selectedGroupDetails.includes(FEMALESTUDENT)
        ) {
            let groupName = '';
            if (groupMode === ROTATION || groupMode === FYD) {
                groupName = extractGroupName({ groupName: group_name, groupMode, gender });
            }
            if (groupMode === COURSE) {
                groupName = gender === BOTH ? 'MFG-1' : gender === MALE ? 'MG-1' : 'FG-1';
            }
            for (const { _student_ids = [] } of groups) {
                _student_ids.forEach((studentIdElement) => {
                    validStudentIds.add(String(studentIdElement));
                    const studentProgramDetailsKey = `${groupBasedConstructedKey}-${groupName}`;
                    const studentExistingDetails = existingStudentProgramDetails.get(
                        String(studentIdElement),
                    );
                    if (!studentExistingDetails) {
                        existingStudentProgramDetails.set(String(studentIdElement), [
                            studentProgramDetailsKey,
                        ]);
                    } else if (!studentExistingDetails.includes(studentProgramDetailsKey)) {
                        studentExistingDetails.push(studentProgramDetailsKey);
                    }
                });
            }
        } else {
            shouldAddStudentGroup({
                selectedGroupDetails,
                groupMode,
                gender,
                groupName: group_name,
            });
            for (const { _student_ids = [] } of groups) {
                _student_ids.forEach((studentIdElement) =>
                    validStudentIds.add(String(studentIdElement)),
                );
            }
        }
    }
    return { validStudentIds, existingStudentProgramDetails };
};

const processStudentGroupData = ({ studentGroupData, totalStudentGroupDetails }) => {
    const uniqueStudentIds = new Set();
    let validStudentProgramDetails = new Map();
    for (const {
        _institution_calendar_id,
        master: { _program_id, year },
        groups = [],
    } of studentGroupData) {
        for (const { group_mode, level, term, curriculum, courses } of groups) {
            for (const { _course_id, setting = [] } of courses) {
                for (const { _group_no = 0, gender, session_setting } of setting) {
                    const rotationCount = group_mode === ROTATION ? _group_no : 0;
                    const groupBasedConstructedKey = `${String(
                        _program_id,
                    )}-${curriculum}-${term}-${year}-${level}-${String(
                        _course_id,
                    )}-${rotationCount}`;
                    const existingGroupBasedDetails =
                        totalStudentGroupDetails.get(groupBasedConstructedKey);
                    if (existingGroupBasedDetails) {
                        const { selectedGroupDetails } = existingGroupBasedDetails;
                        const { validStudentIds, existingStudentProgramDetails } =
                            processSessionSettings({
                                selectedGroupDetails,
                                sessionSettings: session_setting,
                                groupMode: group_mode,
                                gender,
                                groupBasedConstructedKey,
                                validStudentProgramDetails,
                            });
                        validStudentProgramDetails = new Map([
                            ...validStudentProgramDetails,
                            ...existingStudentProgramDetails,
                        ]);
                        validStudentIds.forEach((studentIdElement) =>
                            uniqueStudentIds.add(studentIdElement),
                        );
                    }
                }
            }
        }
    }
    return { uniqueStudentIds, validStudentProgramDetails };
};

const handleStudentGroupFindQuery = async ({
    totalStudentGroupFindQuery,
    totalStudentGroupDetails,
}) => {
    if (totalStudentGroupFindQuery.length) {
        const studentGroupData = await studentGroupSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    $or: totalStudentGroupFindQuery,
                },
                {
                    _institution_calendar_id: 1,
                    'master._program_id': 1,
                    'master.year': 1,
                    'groups.group_mode': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.curriculum': 1,
                    'groups.rotation': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.group_name': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                },
            )
            .lean();
        let { uniqueStudentIds, validStudentProgramDetails } = processStudentGroupData({
            studentGroupData,
            totalStudentGroupDetails,
        });
        if (uniqueStudentIds.size) {
            const studentAcademicIdWithGender = await userSchema
                .find(
                    { _id: { $in: [...uniqueStudentIds] } },
                    { gender: 1, user_type: 1, user_id: 1, name: 1 },
                )
                .lean();
            uniqueStudentIds = studentAcademicIdWithGender.map((totalUniqueStudentIdElement) => {
                const studentProgramDetails = validStudentProgramDetails.get(
                    String(totalUniqueStudentIdElement._id),
                );
                return {
                    ...totalUniqueStudentIdElement,
                    studentProgramDetails,
                };
            });
            return { uniqueStudentIds };
        }
    }
    return { uniqueStudentIds: new Set() };
};

exports.getSelectedProgramUserDetails = async ({
    institutionCalendarId,
    programId,
    selectedUserGroups = [],
    programDetails = [],
}) => {
    const userDetailContext = {
        totalCourseScheduleFindQuery: [],
        totalStudentGroupFindQuery: [],
        totalStudentGroupDetails: new Map(),
    };
    for (const {
        curriculum: curriculumName,
        term: termName,
        year: yearNo,
        level_no: levelNo,
        course,
        rotation_course,
        rotation,
    } of programDetails) {
        const processUserSelectedCourses = ({ course, rotationCount }) => {
            for (const { _course_id } of course) {
                const { courseScheduleFindQuery } = handleStaffSelection({
                    institutionCalendarId,
                    programId,
                    termName,
                    yearNo,
                    levelNo,
                    courseId: _course_id,
                    rotationCount,
                    selectedUserGroups,
                });
                userDetailContext.totalCourseScheduleFindQuery.push(...courseScheduleFindQuery);
                const { groupBasedDetails, studentGroupFindQuery } = handleStudentSelection({
                    institutionCalendarId,
                    programId,
                    curriculumName,
                    termName,
                    yearNo,
                    levelNo,
                    courseId: _course_id,
                    rotationCount,
                    selectedUserGroups,
                });
                userDetailContext.totalStudentGroupDetails = new Map([
                    ...userDetailContext.totalStudentGroupDetails,
                    ...groupBasedDetails,
                ]);
                userDetailContext.totalStudentGroupFindQuery.push(...studentGroupFindQuery);
            }
        };
        if (rotation === YES) {
            for (const { rotation_count, course } of rotation_course) {
                processUserSelectedCourses({ course, rotationCount: rotation_count });
            }
        } else {
            processUserSelectedCourses({ course, rotationCount: 0 });
        }
    }
    const { validUniqueStaffIds } = await handleCourseScheduleFindQuery({
        totalCourseScheduleFindQuery: userDetailContext.totalCourseScheduleFindQuery,
        selectedUserGroups,
        programId,
    });
    const { uniqueStudentIds } = await handleStudentGroupFindQuery({
        totalStudentGroupFindQuery: userDetailContext.totalStudentGroupFindQuery,
        totalStudentGroupDetails: userDetailContext.totalStudentGroupDetails,
    });
    return [...validUniqueStaffIds, ...uniqueStudentIds];
};

exports.cloneSurveyTemplate = async ({ cloneId, cloneName = '' }) => {
    const clonedSurveyTemplateDetails = await digiSurveyBankSchema
        .findOne(
            {
                _id: convertToMongoObjectId(cloneId),
            },
            {
                _institution_id: 1,
                isTemplate: 1,
                surveyName: 1,
                surveyDescription: 1,
                createdBy: 1,
                surveyLevel: 1,
                surveyType: 1,
                learningOutcome: 1,
                outcomeSectionPlacement: 1,
                ratingScaleConfiguration: 1,
                manageQuestionTags: 1,
                mapLearningOutcomes: 1,
                status: 1,
                questions: 1,
                _id: 0,
            },
        )
        .lean();
    const clonedSurveyTemplate = await digiSurveyBankSchema.create({
        ...clonedSurveyTemplateDetails,
        cloneName,
        isEdited: false,
    });
    return {
        statusCode: clonedSurveyTemplate ? 201 : 400,
        message: clonedSurveyTemplate
            ? 'SURVEY_TEMPLATE_CLONED_SUCCESSFULLY'
            : 'UNABLE_TO_CLONE_SURVEY_SUCCESSFULLY',
    };
};

exports.getSelectedProgramWithCourseDetails = async ({
    _institution_id,
    userId,
    institutionCalendarId,
    programId,
    curriculumName,
    term,
    yearNo,
    levelNo,
    courseId,
    rotationCount,
}) => {
    const userModulePermission = await userModulePermissionSchema
        .findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                moduleName: SURVEY,
                userId: convertToMongoObjectId(userId),
            },
            {
                selectedPrograms: 1,
                'selectedCourses._institution_calendar_id': 1,
                'selectedCourses._program_id': 1,
                'selectedCourses.level.course': 1,
                'selectedCourses.level.term': 1,
                'selectedCourses.level.year': 1,
                'selectedCourses.level.level_no': 1,
                'selectedCourses.level.curriculum': 1,
                'selectedCourses.level.rotation': 1,
                'selectedCourses.level.rotation_course.course': 1,
                'selectedCourses.level.rotation_course.rotation_count': 1,
            },
        )
        .lean();
    let programWithLevelDetails = [];
    const getUserPermissionLevels = async ({ programDetail }) => {
        return programDetail.level
            .filter(
                (levelElement) =>
                    (!curriculumName || levelElement.curriculum === curriculumName) &&
                    (!term || levelElement.term === term) &&
                    (!yearNo || levelElement.year === yearNo) &&
                    (!levelNo || levelElement.level_no === levelNo),
            )
            .map((filteredLevelElement) => {
                if (filteredLevelElement.rotation === YES) {
                    return {
                        ...filteredLevelElement,
                        rotation_course: filteredLevelElement.rotation_course
                            .filter(
                                (rotationElement) =>
                                    !rotationCount ||
                                    Number(rotationElement.rotation_count) ===
                                        Number(rotationCount),
                            )
                            .map((rotationElement) => ({
                                ...rotationElement,
                                course: rotationElement.course.filter(
                                    (rotationCourseElement) =>
                                        !courseId ||
                                        String(rotationCourseElement._course_id) ===
                                            String(courseId),
                                ),
                            })),
                    };
                }
                return {
                    ...filteredLevelElement,
                    course: filteredLevelElement.course.filter(
                        (courseElement) =>
                            !courseId || String(courseElement._course_id) === String(courseId),
                    ),
                };
            });
    };
    if (
        userModulePermission &&
        userModulePermission.selectedPrograms &&
        userModulePermission.selectedPrograms.length &&
        String(userModulePermission.selectedPrograms).includes(String(programId))
    ) {
        const programList = await programCalendarSchema
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    status: PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'level.curriculum': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course._course_id': 1,
                },
            )
            .lean();
        if (programList) {
            programWithLevelDetails = [
                ...(await getUserPermissionLevels({ programDetail: programList })),
            ];
        }
    }
    if (userModulePermission && userModulePermission.selectedCourses.length) {
        const particularlySelectedProgramId = userModulePermission.selectedCourses.find(
            (selectedCourseElement) =>
                String(selectedCourseElement._institution_calendar_id) ===
                    String(institutionCalendarId) &&
                String(selectedCourseElement._program_id) === String(programId),
        );
        if (particularlySelectedProgramId) {
            programWithLevelDetails = [
                ...(await getUserPermissionLevels({
                    programDetail: particularlySelectedProgramId,
                })),
            ];
        }
    }
    return programWithLevelDetails;
};
