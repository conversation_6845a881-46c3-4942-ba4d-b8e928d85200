const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    qualityAssuranceApproverList,
    formApplicationList,
    approverUserList,
    updateApproverBy,
    levelRoleUserList,
    approverCategoryList,
    formInitiatorUserList,
    sendEmailToFormInitiator,
    addComment,
    usersByCategoryId,
} = require('./formApprover.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

router.get(
    '/qualityAssuranceApproverList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(qualityAssuranceApproverList),
);
router.get(
    '/formApplicationList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(formApplicationList),
);
router.get(
    '/approverUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(approverUserList),
);
router.put(
    '/updateApproverBy',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateApproverBy),
);
router.get(
    '/levelRoleUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(levelRoleUserList),
);
router.get(
    '/approverCategoryList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(approverCategoryList),
);
//form initiator user list
router.get(
    '/formInitiatorUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(formInitiatorUserList),
);
router.post(
    '/sendEmailToFormInitiator',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(sendEmailToFormInitiator),
);
//add commend
router.post(
    '/addComment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(addComment),
);
router.get('/getCategoryUser', catchAsync(usersByCategoryId));
module.exports = router;
