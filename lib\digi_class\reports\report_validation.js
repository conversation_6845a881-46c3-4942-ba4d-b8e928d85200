const Joi = require('joi');

/**
 * studentId param validation
 * @param {params} req get params only from request
 * @param {*} res send error if validation failed
 * @param {*} next got to next function if validation succeeded
 * @returns error or callback next()
 */
exports.getCourseValidator = (req, res, next) => {
    const schema = Joi.object({
        institutionCalendarId: Joi.string()
            .length(24)
            .error((error) => {
                return req.t('INSTITUTION_CALENDER_ID_REQUIRED');
            }),
        studentId: Joi.string()
            .length(24)
            .error((error) => {
                return req.t('STUDENT_ID_REQUIRED');
            }),
    });
    const response = schema.validate(req.query);
    if (response.error) return res.send(response.error);
    return next();
};
