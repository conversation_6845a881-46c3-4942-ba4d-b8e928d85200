const announcementSchema = require('./announcement_model');
const userSchema = require('../models/user');
const parentUserSchema = require('../models/parent.user');
const { nameFormatter, searchData } = require('../utility/common_functions');
const { convertToMongoObjectId } = require('../utility/common');
const {
    getSignedUrl,
    getSizeOfUrl,
    getUserList,
    getSelectedUserProgram,
    getAcademicStaffDetails,
    getProgramUserDetails,
    filterUniqueId,
} = require('./announcement_service');
const { sendNotificationPush } = require('../../service/pushNotification.service');
const {
    REVOKED,
    PUBLISHED,
    SCHEDULED,
    DRAFT,
    EXPIRED,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    GENDER: { MALE, BOTH },
    USER,
    ACADEMIC,
    ADMINISTRATION,
    EVENT_WHOM: { STAFF },
} = require('../utility/constants');
const connection = require('../../config/redis-connection');
const roleSchema = require('../models/role');
const roleAssignSchema = require('../models/role_assign');
const programCalendarSchema = require('../models/program_calendar');
const announcementUserSettingSchema = require('./announcement_user_setting_model');
const studentGroupSchema = require('../models/student_group');
const dataLimit = 10;
const courseScheduledSchema = require('../models/course_schedule');
const programSchema = require('../models/digi_programs');
exports.createAnnouncement = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            announcementTitle,
            message,
            announcementType,
            priorityType,
            attachments,
            publishDate,
            expiryDate,
            selectUserGroup,
            publishStatus,
            status,
            user_Id,
        } = body;
        const userQueries = [];
        const parentUserQueries = [];
        const user_data = [];
        if (status === 'now' && selectUserGroup.length) {
            for (const selectUserGroupElement of selectUserGroup) {
                const userDataSplit = selectUserGroupElement.split('-');
                if (userDataSplit[0] === 'staff' && userDataSplit.length === 4) {
                    userQueries.push({
                        user_type: userDataSplit[0],
                        'employment.user_employment_type': userDataSplit[1],
                        $or: [
                            { staff_employment_type: userDataSplit[2] },
                            { staff_employment_type: 'both' },
                        ],
                        gender: userDataSplit[3],
                    });
                } else if (userDataSplit[0] === 'student' && userDataSplit.length === 2) {
                    userQueries.push({
                        user_type: userDataSplit[0],
                        gender: userDataSplit[1],
                    });
                } else if (userDataSplit[0] === 'digiConnect' && userDataSplit.length === 2) {
                    parentUserQueries.push({
                        relationType: userDataSplit[1],
                    });
                }
            }
        }
        let userData;
        let parentData;
        if (userQueries.length) {
            userData = await userSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    status: 'completed',
                    $or: userQueries,
                },
                {
                    _id: 1,
                    fcm_token: 1,
                    device_type: 1,
                    web_fcm_token: 1,
                },
            );
            user_data.push(...userData);
        }
        if (parentUserQueries.length) {
            parentData = await parentUserSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    $or: parentUserQueries,
                },

                { _id: 1, fcm_token: 1, device_type: 1 },
            );
            user_data.push(...parentData);
        }
        const announcementData = await announcementSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
            announcementTitle,
            message,
            announcementType: {
                name: announcementType.name || '',
                typeId: announcementType.typeId
                    ? convertToMongoObjectId(announcementType.typeId)
                    : null,
            },
            priorityType: {
                colorCode: priorityType.colorCode || '',
                name: priorityType.name || '',
                typeId: priorityType.typeId ? convertToMongoObjectId(priorityType.typeId) : null,
            },
            attachments,
            publishDate: new Date(publishDate),
            expiryDate: new Date(expiryDate),
            selectUserGroup,
            publishStatus,
            status: status === 'now' ? PUBLISHED : status === 'later' ? SCHEDULED : DRAFT,
            ...(user_data.length && {
                userViewList: user_data.map((userIdElement) => ({
                    userViewId: convertToMongoObjectId(userIdElement._id),
                    view: false,
                })),
            }),
        });
        const userDetails = await userSchema.findOne(
            {
                _id: convertToMongoObjectId(user_Id),
            },
            {
                name: 1,
                user_id: 1,
            },
        );
        const announcementId = announcementData._id;
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0) {
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
                );
            }
        } else if (status === 'later') {
            const currentDate = new Date();
            const startDate = new Date(publishDate);
            const endDate = new Date(expiryDate);
            if (
                Math.floor((startDate.getTime() - currentDate.getTime()) / 1000) > 0 &&
                Math.floor((endDate.getTime() - currentDate.getTime()) / 1000) > 0
            ) {
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-publish`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((startDate.getTime() - currentDate.getTime()) / 1000),
                );
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((endDate.getTime() - currentDate.getTime()) / 1000),
                );
            }
        }
        if (user_data.length && status === 'now') {
            for (userElement of user_data) {
                device_type = userElement.device_type;
                const pushData = {
                    Title: announcementTitle,
                    Body: message,
                    priorityType,
                    announcementType,
                    attachments,
                    sendBy: userDetails,
                    ClickAction: 'announcement',
                    publishDate,
                    announcementId: announcementData._id,
                };
                if (userElement.fcm_token) {
                    fcm_token = userElement.fcm_token;
                    sendNotificationPush(fcm_token, pushData, device_type);
                }
                if (userElement.web_fcm_token) {
                    web_fcm_token = userElement.web_fcm_token;
                    sendNotificationPush(web_fcm_token, pushData, device_type);
                }
            }
        }
        if (!announcementData) return { statusCode: 410, message: 'Not_Created_Data' };
        return { statusCode: 200, message: 'Data_Created' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.listManageAnnouncement = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { announcementType, priorityType, status, user_Id, page, limit } = query;
        let currentPage = 0;
        let currentLimit = 0;
        currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        currentLimit = page && limit ? Number(limit) : 10;
        const countQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
            ...(announcementType &&
                announcementType.length && {
                    'announcementType.name': { $in: announcementType },
                }),
            ...(priorityType &&
                priorityType.length && {
                    'priorityType.name': { $in: priorityType },
                }),
            ...(status &&
                status.length && {
                    status: { $in: status },
                }),
        };
        const announcementDataCount = await announcementSchema.countDocuments(countQuery);
        const announcementData = await announcementSchema
            .find(countQuery, {
                announcementTitle: 1,
                announcementType: 1,
                priorityType: 1,
                publishDate: 1,
                status: 1,
                createdAt: 1,
            })
            .sort({ createdAt: -1 })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        const listManagementDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
        });
        const boardDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(user_Id),
            status: PUBLISHED,
        });
        if (!announcementData.length)
            return {
                statusCode: 200,
                message: 'No_Data',
                data: { listManagementDataCount, boardDataCount },
            };
        return {
            statusCode: 200,
            message: 'List_Data',
            data: {
                announcementData,
                listManagementDataCount,
                boardDataCount,
                announcementDataCount,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.revokeStatus = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcement = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: 1,
            },
        );
        if (announcement.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement already expired' };
        }
        connection.redisClient.Client.del(`announcement:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement:${announcementId}-expire`);
        const announcementData = await announcementSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: REVOKED,
                isActive: false,
            },
        );
        if (!announcementData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATE' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.uploadFile = async ({ body = {} }) => {
    try {
        const { file } = body;
        const name = file.split('/').pop();
        return {
            statusCode: 200,
            data: {
                url: file,
                signedUrl: await getSignedUrl(file),
                size: await getSizeOfUrl(file),
                name,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.generateUrl = async ({ query = {} }) => {
    try {
        const { url } = query;
        return {
            statusCode: 200,
            data: {
                signedUrl: await getSignedUrl(url),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.listBoardAnnouncement = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { announcementType, priorityType, page, limit, user_Id } = query;
        let currentPage = 0;
        let currentLimit = 0;
        currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        currentLimit = page && limit ? Number(limit) : 10;
        const countQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(user_Id),
            isActive: true,
            status: PUBLISHED,
            ...(announcementType &&
                announcementType.length && {
                    'announcementType.name': { $in: announcementType },
                }),
            ...(priorityType &&
                priorityType.length && {
                    'priorityType.name': { $in: priorityType },
                }),
        };
        const announcementDataCount = await announcementSchema.countDocuments(countQuery);
        const announcementData = await announcementSchema
            .find(countQuery, {
                announcementTitle: 1,
                message: 1,
                announcementType: 1,
                priorityType: 1,
                publishDate: 1,
                status: 1,
                attachments: 1,
                createdBy: 1,
                'userViewList.$': 1,
            })
            .sort({ publishDate: -1 })
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        const listManagementDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
        });
        const boardDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(user_Id),
            status: PUBLISHED,
        });
        if (!announcementData.length)
            return {
                statusCode: 200,
                message: 'No_Data',
                data: { listManagementDataCount, boardDataCount },
            };
        return {
            statusCode: 200,
            message: 'List_Data',
            data: {
                announcementData,
                listManagementDataCount,
                boardDataCount,
                announcementDataCount,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.editAnnouncement = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            user_Id,
            announcementId,
            announcementTitle,
            message,
            announcementType,
            priorityType,
            attachments,
            publishDate,
            expiryDate,
            selectUserGroup,
            publishStatus,
            status,
        } = body;
        const userQueries = [];
        const parentUserQueries = [];
        const user_data = [];
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: 1,
            },
        );
        if (announcementStatus.status === PUBLISHED) {
            return { statusCode: 200, message: 'Announcement has been published ' };
        }
        if (announcementStatus.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement has expired' };
        }
        connection.redisClient.Client.del(`announcement:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement:${announcementId}-expire`);
        if (status === 'now' && selectUserGroup && selectUserGroup.length) {
            for (const selectUserGroupElement of selectUserGroup) {
                const userDataSplit = selectUserGroupElement.split('-');
                if (userDataSplit[0] === 'staff' && userDataSplit.length === 4) {
                    userQueries.push({
                        user_type: userDataSplit[0],
                        'employment.user_employment_type': userDataSplit[1],
                        $or: [
                            { staff_employment_type: userDataSplit[2] },
                            { staff_employment_type: 'both' },
                        ],
                        gender: userDataSplit[3],
                    });
                } else if (userDataSplit[0] === 'student' && userDataSplit.length === 2) {
                    userQueries.push({
                        user_type: userDataSplit[0],
                        gender: userDataSplit[1],
                    });
                } else if (userDataSplit[0] === 'digiConnect' && userDataSplit.length === 2) {
                    parentUserQueries.push({
                        relationType: userDataSplit[1],
                    });
                }
            }
        }
        let userData;
        let parentData;
        if (userQueries && userQueries.length) {
            userData = await userSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    status: 'completed',
                    $or: userQueries,
                },
                {
                    _id: 1,
                    fcm_token: 1,
                    device_type: 1,
                    web_fcm_token: 1,
                },
            );
            user_data.push(...userData);
        }
        if (parentUserQueries && parentUserQueries.length) {
            parentData = await parentUserSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    $or: parentUserQueries,
                },
                { _id: 1, fcm_token: 1, device_type: 1 },
            );
            user_data.push(...parentData);
        }
        const announcementData = await announcementSchema.findByIdAndUpdate(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(announcementId),
            },
            {
                $set: {
                    isActive: true,
                    ...(announcementTitle && { announcementTitle }),
                    ...(message && { message }),
                    ...(announcementType && {
                        announcementType: {
                            name: announcementType.name || '',
                            typeId: announcementType.typeId
                                ? convertToMongoObjectId(announcementType.typeId)
                                : null,
                        },
                    }),
                    ...(priorityType && {
                        priorityType: {
                            colorCode: priorityType.colorCode || '',
                            name: priorityType.name || '',
                            typeId: priorityType.typeId
                                ? convertToMongoObjectId(priorityType.typeId)
                                : null,
                        },
                    }),
                    ...(attachments && { attachments }),
                    ...(publishDate && { publishDate: new Date(publishDate) }),
                    ...(expiryDate && { expiryDate: new Date(expiryDate) }),
                    ...(selectUserGroup && { selectUserGroup }),
                    ...(publishStatus && { publishStatus }),
                    ...(status && {
                        status:
                            status === 'now' ? PUBLISHED : status === 'later' ? SCHEDULED : DRAFT,
                    }),
                    ...(user_data.length && {
                        userViewList: user_data.map((userIdElement) => ({
                            userViewId: convertToMongoObjectId(userIdElement._id),
                            view: false,
                        })),
                    }),
                },
            },
        );
        const userDetails = await userSchema.findOne(
            {
                _id: convertToMongoObjectId(user_Id),
            },
            {
                name: 1,
                user_id: 1,
            },
        );
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0)
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
                );
        } else if (status === 'later') {
            const currentDate = new Date();
            const startDate = new Date(publishDate);
            const endDate = new Date(expiryDate);
            if (
                Math.floor((startDate.getTime() - currentDate.getTime()) / 1000) > 0 &&
                Math.floor((endDate.getTime() - currentDate.getTime()) / 1000) > 0
            ) {
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-publish`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((startDate.getTime() - currentDate.getTime()) / 1000),
                );
                await connection.redisClient.Client.set(
                    `announcement:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement' }),
                    'EX',
                    Math.floor((endDate.getTime() - currentDate.getTime()) / 1000),
                );
            }
        }
        if (status === 'now' && user_data && user_data.length) {
            for (userElement of user_data) {
                device_type = userElement.device_type;
                const pushData = {
                    Title: announcementTitle,
                    Body: message,
                    priorityType,
                    announcementType,
                    attachments,
                    sendBy: userDetails,
                    ClickAction: 'announcement',
                    publishDate,
                    announcementId,
                };
                if (userElement.fcm_token) {
                    fcm_token = userElement.fcm_token;
                    sendNotificationPush(fcm_token, pushData, device_type);
                }
                if (userElement.web_fcm_token) {
                    web_fcm_token = userElement.web_fcm_token;
                    sendNotificationPush(web_fcm_token, pushData, device_type);
                }
            }
        }
        if (!announcementData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.userFilterType = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { user_Id, announcement } = query;
        let announcementData;
        if (announcement.toLowerCase() === 'manageannouncement' && user_Id) {
            announcementData = await announcementSchema.aggregate([
                {
                    $match: {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        createdBy: convertToMongoObjectId(user_Id),
                        // 'announcementType.name': { $ne: '' },
                        // 'priorityType.name': { $ne: '' },
                    },
                },
                {
                    $group: {
                        _id: null,
                        announcementType: { $addToSet: '$announcementType.name' },
                        priorityType: { $addToSet: '$priorityType.name' },
                        status: { $addToSet: '$status' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        announcementType: 1,
                        priorityType: 1,
                        status: 1,
                    },
                },
            ]);
        }
        if (announcement.toLowerCase() === 'board') {
            announcementData = await announcementSchema.aggregate([
                {
                    $match: {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        'userViewList.userViewId': convertToMongoObjectId(user_Id),
                        status: PUBLISHED,
                        isActive: true,
                        // 'announcementType.name': { $ne: '' },
                        // 'priorityType.name': { $ne: '' },
                    },
                },
                {
                    $group: {
                        _id: null,
                        announcementType: { $addToSet: '$announcementType.name' },
                        priorityType: { $addToSet: '$priorityType.name' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        announcementType: 1,
                        priorityType: 1,
                    },
                },
            ]);
        }

        if (!announcementData.length) return { statusCode: 200, message: 'NO_DATA' };
        for (announcementDataElement of announcementData) {
            announcementDataElement.announcementType =
                announcementDataElement.announcementType.filter(
                    (announcementTypeElement) => announcementTypeElement !== '',
                );
            announcementDataElement.priorityType = announcementDataElement.priorityType.filter(
                (priorityTypeElement) => priorityTypeElement !== '',
            );
        }
        return { statusCode: 200, message: 'LIST_DATA', data: announcementData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleListAnnouncement = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcementData = await announcementSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(announcementId),
                },
                {
                    announcementTitle: 1,
                    message: 1,
                    announcementType: 1,
                    priorityType: 1,
                    attachments: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    status: 1,
                    createdBy: 1,
                    selectUserGroup: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .lean();
        const updatedAttachments = [];
        if (announcementData?.attachments?.length) {
            for (const urlElement of announcementData.attachments) {
                const url = urlElement.url;
                const signedUrl = await getSignedUrl(url);
                const updatedUrlElement = {
                    _id: urlElement._id,
                    url: urlElement.url,
                    signedUrl,
                    name: urlElement.name,
                };
                updatedAttachments.push(updatedUrlElement);
            }
        }
        announcementData.attachments = updatedAttachments;
        if (!announcementData) return { statusCode: 410, message: 'No_Data' };
        return { statusCode: 200, message: 'List_Data', data: announcementData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.userViewedNotification = async ({ query = {} }) => {
    try {
        const { announcementId, userId } = query;
        const announcementData = await announcementSchema.updateMany(
            {
                _id: {
                    $in: announcementId.map((announcementIdElement) =>
                        convertToMongoObjectId(announcementIdElement),
                    ),
                },
                'userViewList.userViewId': convertToMongoObjectId(userId),
            },
            {
                $set: { 'userViewList.$.view': true },
            },
            {
                new: true,
            },
        );

        if (!announcementData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.userViewedNotificationList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, announcementType, priorityType, page, limit } = query;
        let currentPage = 0;
        let currentLimit = 0;
        currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        currentLimit = page && limit ? Number(limit) : 10;
        const countQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(userId),
            isActive: true,
            status: PUBLISHED,
            ...(announcementType &&
                announcementType.length && {
                    'announcementType.name': { $in: announcementType },
                }),
            ...(priorityType &&
                priorityType.length && {
                    'priorityType.name': { $in: priorityType },
                }),
        };
        const announcementDataCount = await announcementSchema.countDocuments(countQuery);
        const announcementData = await announcementSchema
            .find(countQuery, {
                announcementTitle: 1,
                message: 1,
                announcementType: 1,
                priorityType: 1,
                publishDate: 1,
                status: 1,
                attachments: 1,
                createdBy: 1,
                'userViewList.$': 1,
            })
            .sort({ publishDate: -1 })
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        if (!announcementData.length) return { statusCode: 410, message: 'NO_DATA' };
        return {
            statusCode: 200,
            message: 'DATA_LIST',
            data: { announcementData, announcementDataCount },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.receiveNotificationCount = async ({ query = {} }) => {
    try {
        const { userId } = query;
        const announcementDataCount = await announcementSchema.countDocuments({
            status: PUBLISHED,
            userViewList: {
                $elemMatch: {
                    userViewId: convertToMongoObjectId(userId),
                    view: false,
                },
            },
        });
        return {
            statusCode: 200,
            message: 'Data_Count',
            data: announcementDataCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.publishAnnouncement = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            { status: 1 },
        );
        if (announcementStatus.status === PUBLISHED) {
            return { statusCode: 200, message: 'Announcement has been published' };
        }
        if (announcementStatus.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement has expired' };
        }
        connection.redisClient.Client.del(`announcement:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement:${announcementId}-expire`);
        const announcementData = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                announcementTitle: 1,
                message: 1,
                announcementType: 1,
                priorityType: 1,
                expiryDate: 1,
                selectUserGroup: 1,
                createdBy: 1,
            },
        );
        const userQueries = [];
        const parentUserQueries = [];
        const user_data = [];
        for (const selectUserGroupElement of announcementData.selectUserGroup) {
            const userDataSplit = selectUserGroupElement.split('-');
            if (userDataSplit[0] === 'staff' && userDataSplit.length === 4) {
                userQueries.push({
                    user_type: userDataSplit[0],
                    'employment.user_employment_type': userDataSplit[1],
                    $or: [
                        { staff_employment_type: userDataSplit[2] },
                        { staff_employment_type: 'both' },
                    ],
                    gender: userDataSplit[3],
                });
            } else if (userDataSplit[0] === 'student' && userDataSplit.length === 2) {
                userQueries.push({
                    user_type: userDataSplit[0],
                    gender: userDataSplit[1],
                });
            } else if (userDataSplit[0] === 'digiConnect' && userDataSplit.length === 2) {
                parentUserQueries.push({
                    relationType: userDataSplit[1],
                });
            }
        }
        let userData;
        let parentData;
        if (userQueries && userQueries.length) {
            userData = await userSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    status: 'completed',
                    $or: userQueries,
                },
                {
                    _id: 1,
                    fcm_token: 1,
                    device_type: 1,
                    web_fcm_token: 1,
                },
            );
            user_data.push(...userData);
        }
        if (parentUserQueries && parentUserQueries.length) {
            parentData = await parentUserSchema.find(
                {
                    isActive: true,
                    isDeleted: false,
                    $or: parentUserQueries,
                },
                { _id: 1, fcm_token: 1, device_type: 1 },
            );
            user_data.push(...parentData);
        }
        const UpdateAnnouncement = await announcementSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                $set: {
                    status: PUBLISHED,
                    publishDate: new Date(),
                    ...(user_data.length && {
                        userViewList: user_data.map((userIdElement) => ({
                            userViewId: convertToMongoObjectId(userIdElement._id),
                            view: false,
                        })),
                    }),
                },
            },
        );
        const userDetails = await userSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementData.createdBy),
            },
            {
                name: 1,
                user_id: 1,
            },
        );
        const startDate = new Date();
        const endDate = announcementData.expiryDate;
        if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0) {
            await connection.redisClient.Client.set(
                `announcement:${announcementId}-expire`,
                JSON.stringify({ type: 'announcement' }),
                'EX',
                Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
            );
        }
        if (user_data && user_data.length) {
            for (userElement of user_data) {
                device_type = userElement.device_type;
                const pushData = {
                    Title: announcementData.announcementTitle,
                    Body: announcementData.message,
                    priorityType: announcementData.priorityType,
                    announcementType: announcementData.announcementType,
                    attachments: announcementData.attachments,
                    sendBy: userDetails,
                    ClickAction: 'announcement',
                    publishDate: new Date(),
                    announcementId: announcementData._id,
                };
                if (userElement.fcm_token) {
                    fcm_token = userElement.fcm_token;
                    sendNotificationPush(fcm_token, pushData, device_type);
                }
                if (userElement.web_fcm_token) {
                    web_fcm_token = userElement.web_fcm_token;
                    sendNotificationPush(web_fcm_token, pushData, device_type);
                }
            }
        }
        if (!announcementData) return { statusCode: 410, message: 'Not_Created_Data' };
        return { statusCode: 200, message: 'Data_Created' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.announcementUserList = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const announcementRole = await roleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'modules.name': 'Announcement',
                    'modules.pages': {
                        $elemMatch: {
                            name: 'Manage Announcement',
                            'actions.name': 'View',
                            tabs: {
                                $elemMatch: {
                                    name: 'Create Announcements',
                                    $and: [
                                        { 'actions.name': 'View' },
                                        { 'actions.name': 'Create' },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!announcementRole.length) {
            return { statusCode: 200, message: 'No Role' };
        }
        if (announcementRole.length) {
            const assignRoleUsers = await roleAssignSchema
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        'roles._role_id': {
                            $in: announcementRole.map((roleElement) =>
                                convertToMongoObjectId(roleElement._id),
                            ),
                        },
                    },
                    {
                        _user_id: 1,
                        'roles._role_id.$': 1,
                    },
                )
                .lean()
                .populate({ path: '_user_id', select: { name: 1, user_id: 1 } })
                .populate({ path: 'roles._role_id', select: { name: 1, _id: 0 } });
            if (!assignRoleUsers) {
                return { statusCode: 200, message: 'No_Data' };
            }
            return { statusCode: 200, message: 'List_Data', data: assignRoleUsers };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.programList = async () => {
    try {
        const programCalendarData = await programCalendarSchema
            .distinct('_program_id', {
                isActive: true,
                isDeleted: false,
                status: PUBLISHED,
            })
            .lean();
        if (!programCalendarData?.length) {
            return { statusCode: 200, message: 'No published program' };
        }
        const programDetails = await programSchema
            .find(
                {
                    _id: {
                        $in: programCalendarData.map((programElement) =>
                            convertToMongoObjectId(programElement),
                        ),
                    },
                },
                {
                    name: 1,
                    code: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'Program List', data: programDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleProgramDetails = async ({ query = {} }) => {
    try {
        const { programId } = query;
        const selectQuery = {
            versionNo: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        };
        const singleProgramData = await programCalendarSchema
            .find(
                {
                    _program_id: convertToMongoObjectId(programId),
                    status: PUBLISHED,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _institution_calendar_id: 1,
                    _program_id: 1,
                    'level.curriculum': 1,
                    'level.course._id': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course._course_id': 1,
                },
            )
            .sort({ _id: -1 })
            .populate([
                {
                    path: '_institution_calendar_id',
                    select: { calendar_name: 1 },
                },
                {
                    path: 'level.course._course_id',
                    select: selectQuery,
                },
                {
                    path: 'level.rotation_course.course._course_id',
                    select: selectQuery,
                },
            ])
            .lean();
        if (!singleProgramData) return { statusCode: 200, message: 'No_Data' };
        const singleProgramDataWithVersionedCourseDetails = singleProgramData.map(
            (programElement) => ({
                ...programElement,
                level: programElement.level.map((levelElement) => {
                    if (levelElement.rotation === 'yes') {
                        return {
                            ...levelElement,
                            rotation_course: levelElement.rotation_course.map(
                                (rotationElement) => ({
                                    ...rotationElement,
                                    course: rotationElement.course.map((rotationCourseElement) => {
                                        return {
                                            ...rotationCourseElement,
                                            versionName:
                                                rotationCourseElement._course_id?.versionName || '',
                                            versionNo:
                                                rotationCourseElement._course_id?.versionNo || 1,
                                            versioned:
                                                rotationCourseElement._course_id?.versioned ||
                                                false,
                                            versionedFrom:
                                                rotationCourseElement._course_id?.versionedFrom ||
                                                null,
                                            versionedCourseIds:
                                                rotationCourseElement._course_id
                                                    ?.versionedCourseIds || [],
                                            _course_id: rotationCourseElement._course_id?._id || '',
                                        };
                                    }),
                                }),
                            ),
                        };
                    }
                    return {
                        ...levelElement,
                        course: levelElement.course.map((courseElement) => {
                            return {
                                ...courseElement,
                                versionName: courseElement._course_id?.versionName || '',
                                versionNo: courseElement._course_id?.versionNo || 1,
                                versioned: courseElement._course_id?.versioned || false,
                                versionedFrom: courseElement._course_id?.versionedFrom || null,
                                versionedCourseIds:
                                    courseElement._course_id?.versionedCourseIds || [],
                                _course_id: courseElement._course_id?._id || '',
                            };
                        }),
                    };
                }),
            }),
        );
        return {
            statusCode: 200,
            message: 'Data List',
            data: singleProgramDataWithVersionedCourseDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.selectUserPrograms = async ({ body = {}, headers = {} }) => {
    try {
        const { selectedUserProgram, manageUser } = body;
        const { _institution_id } = headers;
        const bulkWrites = [];
        selectedUserProgram.forEach((userElement) => {
            if (userElement.type === 'mixed') {
                bulkWrites.push({
                    manageUser: convertToMongoObjectId(manageUser),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(userElement.userId),
                    selectedCourses: userElement.selectedCourses,
                    selectedPrograms: userElement.selectedPrograms,
                });
            }
            if (userElement.type === 'all') {
                bulkWrites.push({
                    manageUser: convertToMongoObjectId(manageUser),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(userElement.userId),
                    selectedPrograms: userElement.selectedPrograms,
                });
            }
        });
        if (bulkWrites.length) {
            const announcementUser = await announcementUserSettingSchema.insertMany(bulkWrites);
            if (!announcementUser) return { startCode: 410, message: 'Not_Created_data' };
            return { status: 200, message: 'Data_Created' };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getUserSelectedProgram = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { user_Id } = query;
        const programDetails = [];
        const populateSelectQuery = {
            versionNo: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        };
        const courseList = await announcementUserSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(user_Id),
                },
                {
                    selectedPrograms: 1,
                    'selectedCourses._institution_calendar_id': 1,
                    'selectedCourses._program_id': 1,
                    'selectedCourses.level.course': 1,
                    'selectedCourses.level.term': 1,
                    'selectedCourses.level.year': 1,
                    'selectedCourses.level.level_no': 1,
                    'selectedCourses.level.curriculum': 1,
                    'selectedCourses.level.rotation': 1,
                    'selectedCourses.level.rotation_course.course': 1,
                    'selectedCourses.level.rotation_course.rotation_count': 1,
                },
            )
            .sort({ _id: -1 })
            .populate([
                { path: 'selectedCourses._program_id', select: { name: 1, code: 1 } },
                {
                    path: 'selectedCourses._institution_calendar_id',
                    select: { calendar_name: 1, createdAt: 1 },
                },
                {
                    path: 'selectedCourses.level.course._course_id',
                    select: populateSelectQuery,
                },
                {
                    path: 'selectedCourses.level.rotation_course.course._course_id',
                    select: populateSelectQuery,
                },
            ])
            .lean();
        let programList;
        if (courseList?.selectedPrograms?.length) {
            programList = await programCalendarSchema
                .find(
                    {
                        _program_id: {
                            $in: courseList.selectedPrograms.map((programElement) =>
                                convertToMongoObjectId(programElement),
                            ),
                        },
                        status: PUBLISHED,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _institution_calendar_id: 1,
                        _program_id: 1,
                        'level.curriculum': 1,
                        'level.course._course_id': 1,
                        'level.course.courses_name': 1,
                        'level.course.courses_number': 1,
                        'level.term': 1,
                        'level.year': 1,
                        'level.level_no': 1,
                        'level.rotation': 1,
                        'level.rotation_count': 1,
                        'level.rotation_course.rotation_count': 1,
                        'level.rotation_course.course.courses_name': 1,
                        'level.rotation_course.course.courses_number': 1,
                        'level.rotation_course.course._course_id': 1,
                    },
                )
                .populate([
                    { path: '_program_id', select: { name: 1, code: 1 } },
                    {
                        path: '_institution_calendar_id',
                        select: { calendar_name: 1, createdAt: 1 },
                    },
                    {
                        path: 'level.course._course_id',
                        select: populateSelectQuery,
                    },
                    {
                        path: 'level.rotation_course.course._course_id',
                        select: populateSelectQuery,
                    },
                ])
                .lean();
            if (programList.length) {
                programDetails.push(...programList);
            }
        }

        let listData = [];
        if (courseList?.selectedCourses?.length) {
            programDetails.push(...courseList.selectedCourses);
        }
        programDetails?.forEach((programElement) => {
            const checkInstitutionCalendarId =
                programElement._institution_calendar_id &&
                programElement._institution_calendar_id._id;
            if (checkInstitutionCalendarId) {
                const existingData = listData.find(
                    (listElement) =>
                        listElement.institutionCalendarId.toString() ===
                        programElement._institution_calendar_id._id.toString(),
                );
                if (!existingData) {
                    listData.push({
                        institutionCalendarId: programElement._institution_calendar_id._id,
                        calendar_name: programElement._institution_calendar_id.calendar_name,
                        createdAt: programElement._institution_calendar_id.createdAt,
                        programList: [
                            {
                                program: programElement._program_id[0],
                                level: programElement.level,
                            },
                        ],
                    });
                } else {
                    existingData.programList.push({
                        program: programElement._program_id[0],
                        level: programElement.level,
                    });
                }
            }
        });
        //sort the institution_calendar
        listData = listData.sort((a, b) => {
            const createDate_a = new Date(a.createdAt);
            const createDate_b = new Date(b.createdAt);
            return createDate_b - createDate_a;
        });
        if (!listData.length) {
            return { statusCode: 200, message: 'No_Data' };
        }
        return { statusCode: 200, message: 'Data_List', data: listData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.newCreateAnnouncement = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            announcementTitle,
            message,
            announcementType,
            priorityType,
            attachments,
            publishDate,
            expiryDate,
            selectUserGroup,
            status,
            userId,
            isParent,
            selectedUserList,
            isStudent,
            digiClassConnect,
            digiClassrelation,
            staffType,
            selectedAdminTime,
            selectedProgram,
            selectedChildUser,
        } = body;
        const announcementUser = [];
        let childIds = [];
        if (status === 'now' && selectUserGroup.length) {
            if (
                selectUserGroup[0] === 'all' ||
                selectUserGroup[0] === 'female student' ||
                selectUserGroup[0] === 'male student'
            ) {
                const userData = await getUserList(selectUserGroup[0], _institution_id, userId);
                if (!userData.length) return { statusCode: 200, message: 'User Not Configured' };
                if ((isParent === true && userData.length) || selectUserGroup[0] === 'all') {
                    let parentData = await parentUserSchema
                        .find(
                            {
                                childIds: {
                                    $in: userData.map((userElement) =>
                                        convertToMongoObjectId(userElement._id),
                                    ),
                                },
                            },
                            {
                                _id: 1,
                                'childIds.$': 1,
                            },
                        )
                        .lean();
                    childIds = parentData.map((chileElement) => chileElement.childIds[0]);
                    parentData = parentData.map((parentIdElement) => {
                        return { _id: parentIdElement._id };
                    });
                    announcementUser.push(...parentData);
                }
                announcementUser.push(...userData);
            }
        }
        if (
            status === 'now' &&
            (selectUserGroup[0] === 'individual staff' ||
                selectUserGroup[0] === 'individual student') &&
            selectedUserList.length
        ) {
            if (isParent === true) {
                const parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: selectedUserList.map((userElement) =>
                                    convertToMongoObjectId(userElement._id),
                                ),
                            },
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                announcementUser.push(...parentData);
                childIds = selectedUserList.map((userElement) => userElement._id);
            }
            announcementUser.push(...selectedUserList);
        }
        //digiConnect all user
        if (
            status === 'now' &&
            selectUserGroup[0] === 'digiclass connect' &&
            digiClassConnect === 'all'
        ) {
            let userIds = [];
            let studentIds = [];
            let parentData = [];
            studentIds = await getSelectedUserProgram(_institution_id, userId, selectUserGroup[0]);
            if (studentIds.length) {
                parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: studentIds.map((userElement) =>
                                    convertToMongoObjectId(userElement),
                                ),
                            },
                            relationType: {
                                $in: digiClassrelation.map((relationElement) => relationElement),
                            },
                        },
                        {
                            _id: 1,
                            'childIds.$': 1,
                        },
                    )
                    .lean();
                if (parentData.length)
                    childIds = parentData.map((parentElement) => parentElement.childIds[0]);
            }
            if (!studentIds.length || !parentData.length) {
                return { statusCode: 200, message: 'User Not Configured' };
            }
            announcementUser.push(...parentData);
            if (isStudent === true) {
                parentData.forEach((parentElement) => {
                    userIds.push({ _id: parentElement.childIds[0] });
                });
            }
            if (userIds.length) {
                userIds = filterUniqueId({ userDetails: userIds });
                announcementUser.push(...userIds);
            }
        }
        if (
            status === 'now' &&
            selectUserGroup[0] === 'digiclass connect' &&
            digiClassConnect === 'specific'
        ) {
            if (isStudent === true && selectedChildUser.length) {
                announcementUser.push(...selectedChildUser);
            }
            childIds = selectedChildUser.map((childElement) => childElement._id);
            announcementUser.push(...selectedUserList);
        }
        // admin staff user
        if (
            status === 'now' &&
            selectUserGroup[0] === 'staff' &&
            staffType === 'admin' &&
            selectedAdminTime.length
        ) {
            const userQueries = [];
            for (const selectAdminElement of selectedAdminTime) {
                const userDataSplit = selectAdminElement.split('-');
                userQueries.push({
                    user_type: STAFF,
                    $or: [
                        { staff_employment_type: ADMINISTRATION },
                        { staff_employment_type: BOTH },
                    ],
                    'employment.user_employment_type': userDataSplit[0],
                    gender: userDataSplit[1],
                });
            }
            if (userQueries.length) {
                const adminUserData = await userSchema
                    .find(
                        {
                            isDeleted: false,
                            status: 'completed',
                            $or: userQueries,
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                if (adminUserData.length) {
                    announcementUser.push(...adminUserData);
                }
            }
        }
        //acdemic staff
        if (
            status === 'now' &&
            selectUserGroup[0] === 'staff' &&
            staffType === 'academic' &&
            selectedProgram.length
        ) {
            const userData = await getAcademicStaffDetails({ selectedProgram });
            if (userData.length) {
                announcementUser.push(...userData);
            }
        }
        if (status === 'now' && selectUserGroup[0] === 'program' && selectedProgram.length) {
            const programUserData = await getProgramUserDetails({ selectedProgram });
            announcementUser.push(...programUserData);
        }
        if (status === 'now' && !announcementUser.length) {
            return { statusCode: 200, message: 'User Not Configured' };
        }
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) < 0)
                return {
                    statusCode: 200,
                    message:
                        "The selected expiry date has already passed.Please choose a future date for the announcement's expiry",
                };
        }
        const announcementData = await announcementSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(userId),
            announcementTitle,
            message,
            announcementType: {
                name: announcementType.name || '',
                typeId: announcementType.typeId
                    ? convertToMongoObjectId(announcementType.typeId)
                    : null,
            },
            priorityType: {
                colorCode: priorityType.colorCode || '',
                name: priorityType.name || '',
                typeId: priorityType.typeId ? convertToMongoObjectId(priorityType.typeId) : null,
            },
            ...(attachments && { attachments }),
            publishDate: new Date(publishDate),
            expiryDate: new Date(expiryDate),
            selectUserGroup,
            ...(selectedUserList && { selectedUserList }),
            ...(typeof isParent === 'boolean' && { isParent }),
            status: status === 'now' ? PUBLISHED : status === 'later' ? SCHEDULED : DRAFT,
            ...(announcementUser.length && {
                userViewList: announcementUser.map((userIdElement) => ({
                    userViewId: convertToMongoObjectId(userIdElement._id),
                    view: false,
                })),
            }),
            ...(digiClassConnect && { digiClassConnect }),
            ...(digiClassrelation && { digiClassrelation }),
            ...(typeof isStudent === 'boolean' && { isStudent }),
            ...(staffType && { staffType }),
            ...(selectedAdminTime && { selectedAdminTime }),
            ...(selectedProgram && { selectedProgram }),
            ...(selectedChildUser && { selectedChildUser }),
            ...(childIds && { childIds }),
        });
        const userDetails = await userSchema.findOne(
            {
                _id: convertToMongoObjectId(userId),
            },
            {
                name: 1,
                user_id: 1,
            },
        );
        const announcementId = announcementData._id;
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0) {
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
                );
            }
        } else if (status === 'later') {
            const currentDate = new Date();
            const startDate = new Date(publishDate);
            const endDate = new Date(expiryDate);
            if (
                Math.floor((startDate.getTime() - currentDate.getTime()) / 1000) > 0 &&
                Math.floor((endDate.getTime() - currentDate.getTime()) / 1000) > 0
            ) {
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-publish`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((startDate.getTime() - currentDate.getTime()) / 1000),
                );
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((endDate.getTime() - currentDate.getTime()) / 1000),
                );
            }
        }
        const sendNotificationUser = [];
        if (status === 'now' && announcementUser.length) {
            const NotificationStudent = await userSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        status: 'completed',
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                        web_fcm_token: 1,
                    },
                )
                .lean();
            const NotificationParent = await parentUserSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                    },
                )
                .lean();
            sendNotificationUser.push(...NotificationStudent, ...NotificationParent);
        }
        if (status === 'now') {
            sendNotificationUser.forEach((userElement) => {
                device_type = userElement.device_type;
                const pushData = {
                    Title: announcementTitle,
                    Body: message,
                    priorityType,
                    announcementType,
                    attachments,
                    sendBy: userDetails,
                    ClickAction: 'announcement',
                    publishDate,
                    announcementId: announcementData._id,
                };
                if (userElement.fcm_token) {
                    fcm_token = userElement.fcm_token;
                    sendNotificationPush(fcm_token, pushData, device_type);
                }
                if (userElement.web_fcm_token) {
                    web_fcm_token = userElement.web_fcm_token;
                    sendNotificationPush(web_fcm_token, pushData, device_type);
                }
            });
        }
        if (!announcementData) return { statusCode: 200, message: 'Not_Created_Data' };
        return { statusCode: 200, message: 'Data_Created' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleCourseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _program_id,
            _institution_calendar_id,
            term,
            level_no,
            year,
            _course_id,
            curriculum,
            rotation,
            rotation_count,
            userGroup,
        } = query;
        let studentGroupQuery = {};
        let scheduledQuery = {};
        const groupName = [];
        if (userGroup === 'program') {
            if (rotation === 'no') {
                studentGroupQuery = {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'master._program_id': convertToMongoObjectId(_program_id),
                    'groups.term': term,
                    'groups.rotation': rotation,
                    'groups.curriculum': curriculum,
                    'master.year': year,
                    'groups.level': level_no,
                    'groups.courses._course_id': convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                };
                scheduledQuery = {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    term,
                    year_no: year,
                    level_no,
                    _course_id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                };
            } else {
                studentGroupQuery = {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'master._program_id': convertToMongoObjectId(_program_id),
                    'groups.term': term,
                    'groups.rotation': rotation,
                    'groups.curriculum': curriculum,
                    'master.year': year,
                    'groups.level': level_no,
                    'groups.courses.setting._group_no': rotation_count,
                    'groups.courses._course_id': convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                };
                scheduledQuery = {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    term,
                    year_no: year,
                    level_no,
                    rotation: 'yes',
                    rotation_count: Number(rotation_count),
                    _course_id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                };
            }
            const singleCourseGroupName = await studentGroupSchema
                .findOne(studentGroupQuery, {
                    _id: 1,
                    'groups.group_mode': 1,
                    'groups.term': 1,
                    'groups.level': 1,
                    'groups.curriculum': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.group_setting': 1,
                    'groups.rotation_group_setting': 1,
                })
                .lean();
            singleCourseGroupName?.groups?.forEach((groupElement) => {
                if (groupElement.group_mode === FYD) {
                    let selectedCourse;
                    if (
                        groupElement.term === term &&
                        groupElement.level === level_no &&
                        groupElement.curriculum === curriculum
                    ) {
                        selectedCourse = groupElement.group_setting;
                    }
                    selectedCourse?.forEach((groupSettingElement) => {
                        groupSettingElement?.groups?.forEach((groupNameElement) => {
                            const foundationGroupName = groupNameElement.group_name.split('-');
                            const group_name = foundationGroupName
                                .slice(foundationGroupName.length - 2)
                                .join('-');
                            if (!groupName.length) {
                                groupName.push(group_name);
                            }
                            if (!groupName.some((groupElement) => groupElement === group_name)) {
                                groupName.push(group_name);
                            }
                        });
                    });
                }
                if (groupElement.group_mode === ROTATION) {
                    groupElement?.rotation_group_setting?.forEach((groupSettingElement) => {
                        const rotationGroupName = groupSettingElement.group_name.split('-');
                        const group_name = rotationGroupName
                            .slice(rotationGroupName.length - 2)
                            .join('-');
                        let rotationName;
                        if (group_name.slice(-1) === rotation_count) rotationName = group_name;
                        if (rotationName && !groupName.length) {
                            groupName.push(rotationName);
                        }
                        if (
                            rotationName &&
                            !groupName.some((groupElement) => groupElement === rotationName)
                        ) {
                            groupName.push(rotationName);
                        }
                    });
                }
                if (groupElement.group_mode === COURSE) {
                    let selectedCourse;
                    if (
                        groupElement.term === term &&
                        groupElement.level === level_no &&
                        groupElement.curriculum === curriculum
                    ) {
                        selectedCourse = groupElement.courses;
                    }
                    selectedCourse?.forEach((courseElement) => {
                        if (
                            courseElement._course_id.toString() === _course_id.toString() &&
                            courseElement.setting.length
                        ) {
                            courseElement?.setting?.forEach((settingElement) => {
                                const regularGroupName =
                                    settingElement.gender === BOTH
                                        ? 'SG1'
                                        : settingElement.gender === MALE
                                        ? 'MG-1'
                                        : 'FG-1';
                                if (!groupName.includes(regularGroupName.toString())) {
                                    groupName.push(regularGroupName.toString());
                                }
                            });
                        }
                    });
                }
            });
            const scheduledData = await courseScheduledSchema.aggregate([
                {
                    $match: scheduledQuery,
                },
                {
                    $project: {
                        _id: 0,
                        'staffs._staff_id': 1,
                    },
                },
                {
                    $unwind: '$staffs',
                },
                {
                    $lookup: {
                        from: USER,
                        localField: 'staffs._staff_id',
                        foreignField: '_id',
                        as: 'staffDetails',
                    },
                },
                {
                    $project: {
                        'staffDetails.gender': 1,
                    },
                },
                {
                    $unwind: '$staffDetails',
                },
                {
                    $addFields: {
                        staffCategory: {
                            $cond: {
                                if: { $eq: ['$staffDetails.gender', 'male'] },
                                then: 'Male staff',
                                else: 'Female staff',
                            },
                        },
                    },
                },
                {
                    $group: {
                        _id: '$staffCategory',
                        staffDetails: { $addToSet: '$staffDetails' },
                    },
                },
                {
                    $project: {
                        _id: 1,
                    },
                },
            ]);
            if (scheduledData.length) {
                const staffDetails = scheduledData.map((staffElement) => staffElement._id);
                groupName.push(...staffDetails);
            }
            if (!groupName.length) return { statusCode: 200, message: 'NO DATA', data: [] };
            return { statusCode: 200, message: 'Data List', data: groupName };
        }
        if (userGroup === 'staff') {
            if (rotation === 'no') {
                scheduledQuery = {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    term,
                    year_no: year,
                    level_no,
                    _course_id: convertToMongoObjectId(_course_id),
                    rotation,
                    isDeleted: false,
                    isActive: true,
                };
            } else {
                scheduledQuery = {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    term,
                    year_no: year,
                    level_no,
                    rotation,
                    rotation_count: Number(rotation_count),
                    _course_id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                };
            }
            const staffData = await courseScheduledSchema.aggregate([
                {
                    $match: scheduledQuery,
                },
                {
                    $project: {
                        _id: 0,
                        'staffs._staff_id': 1,
                    },
                },
                {
                    $unwind: '$staffs',
                },
                {
                    $lookup: {
                        from: USER,
                        localField: 'staffs._staff_id',
                        foreignField: '_id',
                        as: 'staffDetails',
                    },
                },
                {
                    $project: {
                        'staffDetails._id': 1,
                        'staffDetails.gender': 1,
                        'staffDetails.employment.user_employment_type': 1,
                        'staffDetails.staff_employment_type': 1,
                    },
                },
                {
                    $unwind: '$staffDetails',
                },
                {
                    $match: {
                        $or: [
                            { 'staffDetails.staff_employment_type': ACADEMIC },
                            {
                                'staffDetails.staff_employment_type': BOTH,
                            },
                        ],
                    },
                },
                {
                    $group: {
                        _id: '$staffDetails.employment.user_employment_type',
                        gender: { $addToSet: '$staffDetails.gender' },
                    },
                },
            ]);
            if (!staffData.length) return { statusCode: 200, message: 'NO DATA', data: [] };
            return { statusCode: 200, message: 'Data List', data: staffData };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.individualStudentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, searchKey } = query;
        if (searchKey.trim() === '') {
            return { statusCode: 200, message: 'No Data', data: [] };
        }
        let dbQuery = {};
        if (searchKey && searchKey.length) {
            dbQuery = await searchData(searchKey);
        }
        const userIds = await getSelectedUserProgram(_institution_id, userId, (type = 'student'));
        if (!userIds.length) return { statusCode: 200, message: 'No Data', data: [] };
        if (userIds.length) {
            const userData = await userSchema
                .find(
                    {
                        _id: {
                            $in: userIds.map((userElement) => convertToMongoObjectId(userElement)),
                        },
                        $or: dbQuery.$or,
                    },
                    {
                        name: 1,
                        user_id: 1,
                        email: 1,
                        isActive: 1,
                    },
                )
                .limit(dataLimit)
                .lean();
            if (!userData.length) return { statusCode: 200, message: 'No Data', data: [] };
            return { statusCode: 200, message: 'List Data', data: userData };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.individualStaffList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, searchKey } = query;
        if (searchKey.trim() === '') {
            return { statusCode: 200, message: 'No Data', data: [] };
        }
        let dbQuery = {};
        if (searchKey?.length) {
            dbQuery = await searchData(searchKey);
        }
        const userIds = await getSelectedUserProgram(_institution_id, userId, (type = 'staff'));
        if (!userIds.length) return { statusCode: 200, message: 'No Data', data: [] };
        const userData = await userSchema
            .find(
                {
                    _id: {
                        $in: userIds.map((userElement) => convertToMongoObjectId(userElement)),
                    },
                    $or: dbQuery.$or,
                },
                {
                    name: 1,
                    user_id: 1,
                    isActive: 1,
                    email: 1,
                },
            )
            .limit(dataLimit)
            .lean();
        if (!userData.length) return { statusCode: 200, message: 'No Data', data: [] };
        return { statusCode: 200, message: 'List Data', data: userData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.digiconnectSpecificUser = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, searchKey } = query;
        if (searchKey.trim() === '') {
            return { statusCode: 200, message: 'No Data', data: [] };
        }
        let limitCount = 0;
        const userIds = await getSelectedUserProgram(
            _institution_id,
            userId,
            (type = 'digiConnect'),
        );
        const userDetails = [];
        let userData = [];
        if (!userIds.length) return { statusCode: 200, message: 'No Data', data: [] };
        userData = await parentUserSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    childIds: {
                        $in: userIds.map((userElement) => convertToMongoObjectId(userElement)),
                    },
                },
                { relationType: 1, 'childIds.$': 1 },
            )
            .populate({
                path: 'childIds',
                select: { name: 1, user_id: 1, isActive: 1, email: 1 },
            })
            .lean();
        if (searchKey) {
            userData.forEach((userElement) => {
                userElement.childIds = userElement.childIds.filter((userName) => {
                    const { name, user_id, email } = userName;
                    const searchData = searchKey.toLowerCase();
                    const userFullName = nameFormatter(name);
                    return (
                        email.toLowerCase().includes(searchData) ||
                        user_id.toLowerCase().includes(searchData) ||
                        userFullName.toLowerCase().includes(searchData)
                    );
                });
                userElement?.childIds?.forEach((childElement) => {
                    if (limitCount >= dataLimit) return;
                    const existingChild = userDetails.find(
                        (userDetailsElement) =>
                            userDetailsElement.childId.toString() === childElement._id.toString(),
                    );
                    if (!existingChild) {
                        userDetails.push({
                            parentDetails: [
                                {
                                    parentId: userElement._id,
                                    relationType: userElement.relationType,
                                    isCheckbox: true,
                                },
                            ],
                            childName: childElement.name,
                            childUserId: childElement.user_id,
                            childId: childElement._id,
                            childEmail: childElement.email,
                            isActive: childElement.isActive,
                        });
                        limitCount++;
                    } else {
                        existingChild.parentDetails.push({
                            parentId: userElement._id,
                            relationType: userElement.relationType,
                            isCheckbox: true,
                        });
                    }
                });
            });
        }

        if (!userDetails.length) return { statusCode: 200, message: 'No Data', data: [] };
        return { statusCode: 200, message: 'List Data', data: userDetails };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getUserSetting = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId } = query;
        const userSettingData = await announcementUserSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(userId),
                },
                {
                    selectedCourses: 1,
                    selectedPrograms: 1,
                },
            )
            .populate({ path: 'selectedCourses._program_id', select: { name: 1, code: 1 } })
            .populate({
                path: 'selectedCourses._institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .populate({ path: 'selectedPrograms', select: { name: 1, code: 1 } })
            .sort({ _id: -1 })
            .lean();
        if (!userSettingData)
            return {
                statusCode: 200,
                message: 'No Data',
                data: { selectedCourses: [], selectedPrograms: [] },
            };
        return { statusCode: 200, message: 'List Data', data: userSettingData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getSingleAnnouncementDetails = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const updatedAttachments = [];
        const populateSelectQuery = {
            versionNo: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        };
        const announcementData = await announcementSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(announcementId),
                },
                {
                    selectUserGroup: 1,
                    selectedUserList: 1,
                    digiClassrelation: 1,
                    announcementTitle: 1,
                    message: 1,
                    announcementType: 1,
                    priorityType: 1,
                    attachments: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    status: 1,
                    createdBy: 1,
                    isParent: 1,
                    digiClassConnect: 1,
                    isStudent: 1,
                    staffType: 1,
                    selectedAdminTime: 1,
                    selectedProgram: 1,
                    selectedChildUser: 1,
                },
            )
            .populate([
                {
                    path: 'selectedProgram.programList.level.course._course_id',
                    select: populateSelectQuery,
                },
                {
                    path: 'selectedProgram.programList.level.rotation_course.course._course_id',
                    select: populateSelectQuery,
                },
                { path: 'createdBy', select: { name: 1, user_id: 1 } },
            ])
            .lean();
        if (
            announcementData?.selectedUserList?.length &&
            (announcementData.selectUserGroup[0] === 'individual student' ||
                announcementData.selectUserGroup[0] === 'individual staff')
        ) {
            const userData = await userSchema
                .find(
                    {
                        _id: {
                            $in: announcementData.selectedUserList.map((userElement) =>
                                convertToMongoObjectId(userElement._id),
                            ),
                        },
                    },
                    {
                        name: 1,
                        user_id: 1,
                        isActive: 1,
                    },
                )
                .lean();
            announcementData.selectedUserList = userData;
        }
        if (
            announcementData?.selectedUserList?.length &&
            announcementData?.selectedChildUser?.length &&
            announcementData.selectUserGroup[0] === 'digiclass connect'
        ) {
            const checkParentData = await parentUserSchema
                .find(
                    {
                        _id: {
                            $in: announcementData.selectedUserList.map((userElement) =>
                                convertToMongoObjectId(userElement._id),
                            ),
                        },
                        childIds: {
                            $in: announcementData.selectedChildUser.map(
                                (childElement) => childElement._id,
                            ),
                        },
                    },
                    {
                        _id: 1,
                        'childIds.$': 1,
                        relationType: 1,
                    },
                )
                .populate({ path: 'childIds', select: { name: 1, user_id: 1, isActive: 1 } })
                .lean();
            const unCheckParentData = await parentUserSchema
                .find(
                    {
                        _id: {
                            $nin: announcementData.selectedUserList.map(
                                (userElement) => userElement._id,
                            ),
                        },
                        childIds: {
                            $in: checkParentData.map((parentElement) =>
                                parentElement.childIds.map((childElement) => childElement._id),
                            ),
                        },
                    },
                    {
                        _id: 1,
                        childIds: 1,
                        relationType: 1,
                    },
                )
                .lean();
            if (checkParentData.length) {
                const userDetails = [];
                checkParentData.forEach((userElement) => {
                    userElement?.childIds.forEach((childElement) => {
                        const existingChild = userDetails.find(
                            (userDetailsElement) =>
                                userDetailsElement.childId.toString() ===
                                childElement._id.toString(),
                        );
                        if (!existingChild) {
                            userDetails.push({
                                parentDetails: [
                                    {
                                        parentId: userElement._id,
                                        relationType: userElement.relationType,
                                        isCheckbox: true,
                                    },
                                ],
                                childName: childElement.name,
                                childUserId: childElement.user_id,
                                childId: childElement._id,
                                isActive: childElement.isActive,
                            });
                        } else {
                            existingChild.parentDetails.push({
                                parentId: userElement._id,
                                relationType: userElement.relationType,
                                isCheckbox: true,
                            });
                        }
                    });
                });
                if (userDetails.length) {
                    unCheckParentData.forEach((userElement) => {
                        userElement.childIds.forEach((childElement) => {
                            const existingChild = userDetails.find(
                                (userDetailsElement) =>
                                    userDetailsElement.childId.toString() ===
                                    childElement._id.toString(),
                            );
                            if (existingChild) {
                                existingChild.parentDetails.push({
                                    parentId: userElement._id,
                                    relationType: userElement.relationType,
                                    isCheckbox: false,
                                });
                            }
                        });
                    });
                }
                announcementData.selectedUserList = userDetails;
            }
        }
        if (announcementData?.attachments?.length) {
            for (const urlElement of announcementData.attachments) {
                const url = urlElement.url;
                const signedUrl = await getSignedUrl(url);
                const updatedUrlElement = {
                    signedUrl,
                    name: urlElement.name,
                    url: urlElement.url,
                };
                updatedAttachments.push(updatedUrlElement);
            }
            announcementData.attachments = updatedAttachments;
        }

        if (!announcementData) return { statusCode: 200, message: 'No Data' };
        return { statusCode: 200, message: 'List Data', data: announcementData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.newListManageAnnouncement = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { announcementType, priorityType, status, user_Id, page, limit } = query;
        let currentPage = 0;
        let currentLimit = 0;
        currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        currentLimit = page && limit ? Number(limit) : 10;
        const countQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
            ...(announcementType &&
                announcementType.length && {
                    'announcementType.name': { $in: announcementType },
                }),
            ...(priorityType &&
                priorityType.length && {
                    'priorityType.name': { $in: priorityType },
                }),
            ...(status &&
                status.length && {
                    status: { $in: status },
                }),
        };
        const announcementDataCount = await announcementSchema.countDocuments(countQuery);
        const announcementData = await announcementSchema
            .find(countQuery, {
                announcementTitle: 1,
                announcementType: 1,
                priorityType: 1,
                publishDate: 1,
                status: 1,
                createdAt: 1,
            })
            .sort({ createdAt: -1 })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        const listManagementDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            createdBy: convertToMongoObjectId(user_Id),
        });
        const boardDataCount = await announcementSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(user_Id),
            status: PUBLISHED,
        });
        const announcementUserSetting = await announcementUserSettingSchema
            .findOne(
                {
                    userId: convertToMongoObjectId(user_Id),
                },
                {
                    selectedPrograms: 1,
                    selectedCourses: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        let createButton = `Can't create announcement - program not assigned`;

        if (
            announcementUserSetting?.selectedPrograms?.length ||
            announcementUserSetting?.selectedCourses?.length
        ) {
            createButton = 'program assigned';
        }
        if (!announcementData.length)
            return {
                statusCode: 200,
                message: 'No_Data',
                data: { listManagementDataCount, boardDataCount, createButton },
            };
        return {
            statusCode: 200,
            message: 'List_Data',
            data: {
                announcementData,
                listManagementDataCount,
                boardDataCount,
                announcementDataCount,
                createButton,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.editNewAnnouncement = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            userId,
            announcementId,
            announcementTitle,
            message,
            announcementType,
            priorityType,
            attachments,
            publishDate,
            expiryDate,
            selectUserGroup,
            status,
            selectedUserList,
            isParent,
            digiClassConnect,
            digiClassrelation,
            isStudent,
            staffType,
            selectedAdminTime,
            selectedProgram,
            selectedChildUser,
        } = body;
        const announcementUser = [];
        let childIds = [];
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: 1,
            },
        );
        if (announcementStatus.status === PUBLISHED) {
            return { statusCode: 200, message: 'Announcement has been published ' };
        }
        if (announcementStatus.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement has expired' };
        }
        connection.redisClient.Client.del(`announcement2:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement2:${announcementId}-expire`);
        if (status === 'now' && selectUserGroup.length) {
            if (
                selectUserGroup[0] === 'all' ||
                selectUserGroup[0] === 'female student' ||
                selectUserGroup[0] === 'male student'
            ) {
                const userData = await getUserList(selectUserGroup[0], _institution_id, userId);
                if (!userData.length) return { statusCode: 200, message: 'User Not Configured' };
                if ((isParent === true && userData.length) || selectUserGroup[0] === 'all') {
                    let parentData = await parentUserSchema
                        .find(
                            {
                                childIds: {
                                    $in: userData.map((userElement) =>
                                        convertToMongoObjectId(userElement._id),
                                    ),
                                },
                            },
                            {
                                _id: 1,
                                'childIds.$': 1,
                            },
                        )
                        .lean();
                    childIds = parentData.map((chileElement) => chileElement.childIds[0]);
                    parentData = parentData.map((parentIdElement) => {
                        return { _id: parentIdElement._id };
                    });
                    announcementUser.push(...parentData);
                }
                announcementUser.push(...userData);
            }
        }
        if (
            status === 'now' &&
            (selectUserGroup[0] === 'individual staff' ||
                selectUserGroup[0] === 'individual student') &&
            selectedUserList.length
        ) {
            if (isParent === true) {
                const parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: selectedUserList.map((userElement) =>
                                    convertToMongoObjectId(userElement._id),
                                ),
                            },
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                announcementUser.push(...parentData);
                childIds = selectedUserList.map((userElement) => userElement._id);
            }
            announcementUser.push(...selectedUserList);
        }
        if (
            status === 'now' &&
            selectUserGroup[0] === 'digiclass connect' &&
            digiClassConnect === 'all'
        ) {
            let userIds = [];
            let studentIds = [];
            let parentData = [];
            studentIds = await getSelectedUserProgram(_institution_id, userId, selectUserGroup[0]);
            if (studentIds.length) {
                parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: studentIds.map((userElement) =>
                                    convertToMongoObjectId(userElement),
                                ),
                            },
                            relationType: {
                                $in: digiClassrelation.map((relationElement) => relationElement),
                            },
                        },
                        {
                            _id: 1,
                            'childIds.$': 1,
                        },
                    )
                    .lean();
                if (parentData.length)
                    childIds = parentData.map((parentElement) => parentElement.childIds[0]);
            }
            if (!studentIds.length || !parentData.length) {
                return { statusCode: 200, message: 'User Not Configured' };
            }
            announcementUser.push(...parentData);
            if (isStudent === true) {
                parentData.forEach((parentElement) => {
                    userIds.push({ _id: parentElement.childIds[0] });
                });
            }
            if (userIds.length) {
                userIds = filterUniqueId({ userDetails: userIds });
                announcementUser.push(...userIds);
            }
        }
        if (
            status === 'now' &&
            selectUserGroup[0] === 'digiclass connect' &&
            digiClassConnect === 'specific'
        ) {
            if (isStudent === true && selectedChildUser.length) {
                announcementUser.push(...selectedChildUser);
            }
            childIds = selectedChildUser.map((childElement) => childElement._id);
            announcementUser.push(...selectedUserList);
        }
        if (
            status === 'now' &&
            selectUserGroup[0] === 'staff' &&
            staffType === 'admin' &&
            selectedAdminTime.length
        ) {
            const userQueries = [];
            selectedAdminTime.forEach((selectAdminElement) => {
                const userDataSplit = selectAdminElement.split('-');
                userQueries.push({
                    user_type: STAFF,
                    $or: [
                        { staff_employment_type: ADMINISTRATION },
                        { staff_employment_type: BOTH },
                    ],
                    'employment.user_employment_type': userDataSplit[0],
                    gender: userDataSplit[1],
                });
            });
            if (userQueries.length) {
                const adminUserData = await userSchema
                    .find(
                        {
                            isDeleted: false,
                            status: 'completed',
                            $or: userQueries,
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                if (adminUserData.length) {
                    announcementUser.push(...adminUserData);
                }
            }
        }
        if (
            status === 'now' &&
            selectUserGroup[0] === 'staff' &&
            staffType === 'academic' &&
            selectedProgram.length
        ) {
            const userData = await getAcademicStaffDetails({ selectedProgram });
            if (userData.length) {
                announcementUser.push(...userData);
            }
        }
        if (status === 'now' && selectUserGroup[0] === 'program' && selectedProgram.length) {
            const programUserData = await getProgramUserDetails({ selectedProgram });
            announcementUser.push(...programUserData);
        }
        if (status === 'now' && !announcementUser.length) {
            return { statusCode: 200, message: 'User Not Configured' };
        }
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) < 0)
                return {
                    statusCode: 200,
                    message:
                        "The selected expiry date has already passed.Please choose a future date for the announcement's expiry",
                };
        }
        const announcementData = await announcementSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                $set: {
                    isActive: true,
                    ...(announcementTitle && { announcementTitle }),
                    ...(message && { message }),
                    ...(announcementType && {
                        announcementType: {
                            name: announcementType.name || '',
                            typeId: announcementType.typeId
                                ? convertToMongoObjectId(announcementType.typeId)
                                : null,
                        },
                    }),
                    ...(priorityType && {
                        priorityType: {
                            colorCode: priorityType.colorCode || '',
                            name: priorityType.name || '',
                            typeId: priorityType.typeId
                                ? convertToMongoObjectId(priorityType.typeId)
                                : null,
                        },
                    }),
                    ...(attachments && { attachments }),
                    ...(publishDate && { publishDate: new Date(publishDate) }),
                    ...(expiryDate && { expiryDate: new Date(expiryDate) }),
                    ...(selectUserGroup && { selectUserGroup }),
                    ...(status && {
                        status:
                            status === 'now' ? PUBLISHED : status === 'later' ? SCHEDULED : DRAFT,
                    }),
                    ...(typeof isParent === 'boolean' && { isParent }),
                    ...(digiClassConnect && { digiClassConnect }),
                    ...(digiClassrelation && { digiClassrelation }),
                    ...(typeof isStudent === 'boolean' && { isStudent }),
                    ...(selectedUserList && { selectedUserList }),
                    ...(announcementUser.length && {
                        userViewList: announcementUser.map((userIdElement) => ({
                            userViewId: convertToMongoObjectId(userIdElement._id),
                            view: false,
                        })),
                    }),
                    ...(staffType && { staffType }),
                    ...(selectedAdminTime && { selectedAdminTime }),
                    ...(selectedProgram && { selectedProgram }),
                    ...(selectedChildUser && { selectedChildUser }),
                    ...(childIds && { childIds }),
                },
            },
        );

        const userDetails = await userSchema.findOne(
            {
                _id: convertToMongoObjectId(userId),
            },
            {
                name: 1,
                user_id: 1,
            },
        );
        if (status === 'now') {
            const startDate = new Date();
            const endDate = new Date(expiryDate);
            if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0)
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
                );
        } else if (status === 'later') {
            const currentDate = new Date();
            const startDate = new Date(publishDate);
            const endDate = new Date(expiryDate);
            if (
                Math.floor((startDate.getTime() - currentDate.getTime()) / 1000) > 0 &&
                Math.floor((endDate.getTime() - currentDate.getTime()) / 1000) > 0
            ) {
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-publish`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((startDate.getTime() - currentDate.getTime()) / 1000),
                );
                await connection.redisClient.Client.set(
                    `announcement2:${announcementId}-expire`,
                    JSON.stringify({ type: 'announcement2' }),
                    'EX',
                    Math.floor((endDate.getTime() - currentDate.getTime()) / 1000),
                );
            }
        }

        const sendNotificationUser = [];
        if (status === 'now' && announcementUser.length) {
            const NotificationStudent = await userSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        status: 'completed',
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                        web_fcm_token: 1,
                    },
                )
                .lean();
            const NotificationParent = await parentUserSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                    },
                )
                .lean();
            sendNotificationUser.push(...NotificationStudent, ...NotificationParent);
        }
        if (status === 'now') {
            sendNotificationUser.forEach((userElement) => {
                device_type = userElement.device_type;
                const pushData = {
                    Title: announcementTitle,
                    Body: message,
                    priorityType,
                    announcementType,
                    attachments,
                    sendBy: userDetails,
                    ClickAction: 'announcement',
                    publishDate,
                    announcementId,
                };
                if (userElement.fcm_token) {
                    fcm_token = userElement.fcm_token;
                    sendNotificationPush(fcm_token, pushData, device_type);
                }
                if (userElement.web_fcm_token) {
                    web_fcm_token = userElement.web_fcm_token;
                    sendNotificationPush(web_fcm_token, pushData, device_type);
                }
            });
        }
        if (!announcementData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.revokeAnnouncement = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: 1,
            },
        );
        if (announcementStatus?.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement already expired' };
        }
        connection.redisClient.Client.del(`announcement2:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement2:${announcementId}-expire`);
        const announcementData = await announcementSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                status: REVOKED,
                userViewList: [],
                isActive: false,
            },
        );
        if (!announcementData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATE' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.immediatePublish = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            { status: 1 },
        );
        if (announcementStatus?.status === PUBLISHED) {
            return { statusCode: 200, message: 'Announcement has been published' };
        }
        if (announcementStatus?.status === EXPIRED) {
            return { statusCode: 200, message: 'Announcement has expired' };
        }
        connection.redisClient.Client.del(`announcement2:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement2:${announcementId}-expire`);
        const announcementData = await announcementSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(announcementId),
                },
                {
                    announcementTitle: 1,
                    selectUserGroup: 1,
                    message: 1,
                    priorityType: 1,
                    announcementType: 1,
                    attachments: 1,
                    publishDate: 1,
                    createdBy: 1,
                    isParent: 1,
                    isStudent: 1,
                    digiClassConnect: 1,
                    digiClassrelation: 1,
                    _institution_id: 1,
                    selectedUserList: 1,
                    expiryDate: 1,
                    staffType: 1,
                    selectedAdminTime: 1,
                    selectedProgram: 1,
                    selectedChildUser: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .lean();
        const announcementUser = [];
        let userData = [];
        let childIds = [];
        if (
            announcementData &&
            announcementData.selectUserGroup.length &&
            (announcementData.selectUserGroup[0] === 'all' ||
                announcementData.selectUserGroup[0] === 'female student' ||
                announcementData.selectUserGroup[0] === 'male student')
        ) {
            userData = await getUserList(
                announcementData.selectUserGroup[0],
                announcementData._institution_id,
                announcementData.createdBy._id,
            );
            if (
                (announcementData.isParent === true && userData.length) ||
                announcementData.selectUserGroup[0] === 'all'
            ) {
                let parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: userData.map((userElement) =>
                                    convertToMongoObjectId(userElement._id),
                                ),
                            },
                        },
                        {
                            _id: 1,
                            'childIds.$': 1,
                        },
                    )
                    .lean();
                childIds = parentData.map((chileElement) => chileElement.childIds[0]);
                parentData = parentData.map((parentIdElement) => {
                    return { _id: parentIdElement._id };
                });
                announcementUser.push(...parentData);
            }
            if (userData.length) announcementUser.push(...userData);
        }
        if (
            (announcementData.selectUserGroup[0] === 'individual staff' ||
                announcementData.selectUserGroup[0] === 'individual student') &&
            announcementData.selectedUserList.length
        ) {
            if (announcementData.isParent === true) {
                const parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: announcementData.selectedUserList.map((userElement) =>
                                    convertToMongoObjectId(userElement._id),
                                ),
                            },
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                childIds = announcementData.selectedUserList.map((userElement) => userElement._id);
                announcementUser.push(...parentData);
            }
            announcementUser.push(...announcementData.selectedUserList);
        }
        if (
            announcementData.selectUserGroup[0] === 'digiclass connect' &&
            announcementData.digiClassConnect === 'all'
        ) {
            let userIds = [];
            let studentIds = [];
            let parentData = [];

            studentIds = await getSelectedUserProgram(
                announcementData._institution_id,
                announcementData.createdBy._id,
                announcementData.selectUserGroup[0],
            );
            if (studentIds.length) {
                parentData = await parentUserSchema
                    .find(
                        {
                            childIds: {
                                $in: studentIds.map((userElement) =>
                                    convertToMongoObjectId(userElement),
                                ),
                            },
                            relationType: {
                                $in: announcementData.digiClassrelation.map(
                                    (relationElement) => relationElement,
                                ),
                            },
                        },
                        {
                            _id: 1,
                            'childIds.$': 1,
                        },
                    )
                    .lean();
                if (parentData.length)
                    childIds = parentData.map((parentElement) => parentElement.childIds[0]);
            }
            announcementUser.push(...parentData);
            if (announcementData.isStudent === true) {
                parentData?.forEach((parentElement) => {
                    userIds.push({
                        _id: parentElement.childIds[0],
                    });
                });
            }
            if (userIds.length) {
                userIds = filterUniqueId({ userDetails: userIds });
                userIds = announcementUser.push(...userIds);
            }
        }
        if (
            announcementData.selectUserGroup[0] === 'digiclass connect' &&
            announcementData.digiClassConnect === 'specific'
        ) {
            if (announcementData.isStudent === true && announcementData.selectedChildUser.length) {
                announcementUser.push(...announcementData.selectedChildUser);
            }
            childIds = announcementData.selectedChildUser.map((childElement) => childElement._id);
            announcementUser.push(...announcementData.selectedUserList);
        }
        if (
            announcementData.selectUserGroup[0] === 'staff' &&
            announcementData.staffType === 'admin' &&
            announcementData.selectedAdminTime.length
        ) {
            const userQueries = [];
            for (const selectAdminElement of announcementData.selectedAdminTime) {
                const userDataSplit = selectAdminElement.split('-');
                userQueries.push({
                    user_type: STAFF,
                    $or: [
                        { staff_employment_type: ADMINISTRATION },
                        { staff_employment_type: BOTH },
                    ],
                    'employment.user_employment_type': userDataSplit[0],
                    gender: userDataSplit[1],
                });
            }
            if (userQueries.length) {
                const adminUserData = await userSchema
                    .find(
                        {
                            isDeleted: false,
                            status: 'completed',
                            $or: userQueries,
                        },
                        {
                            _id: 1,
                        },
                    )
                    .lean();
                if (adminUserData.length) {
                    announcementUser.push(...adminUserData);
                }
            }
        }
        if (
            announcementData.selectUserGroup[0] === 'staff' &&
            announcementData.staffType === 'academic' &&
            announcementData.selectedProgram.length
        ) {
            const userData = await getAcademicStaffDetails({
                selectedProgram: announcementData.selectedProgram,
            });
            if (userData.length) {
                announcementUser.push(...userData);
            }
        }
        if (
            announcementData.selectUserGroup[0] === 'program' &&
            announcementData.selectedProgram.length
        ) {
            const programUserData = await getProgramUserDetails({
                selectedProgram: announcementData.selectedProgram,
            });
            announcementUser.push(...programUserData);
        }
        const UpdateAnnouncement = await announcementSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            {
                $set: {
                    status: PUBLISHED,
                    publishDate: new Date(),
                    ...(announcementUser.length && {
                        userViewList: announcementUser.map((userIdElement) => ({
                            userViewId: convertToMongoObjectId(userIdElement._id),
                            view: false,
                        })),
                    }),
                    ...(childIds && { childIds }),
                },
            },
        );
        const startDate = new Date();
        const endDate = announcementData.expiryDate;
        if (Math.floor((endDate.getTime() - startDate.getTime()) / 1000) > 0) {
            await connection.redisClient.Client.set(
                `announcement2:${announcementId}-expire`,
                JSON.stringify({ type: 'announcement2' }),
                'EX',
                Math.floor((endDate.getTime() - startDate.getTime()) / 1000),
            );
        }
        const sendNotificationUser = [];
        if (announcementUser.length) {
            const NotificationStudent = await userSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        status: 'completed',
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                        web_fcm_token: 1,
                    },
                )
                .lean();
            const NotificationParent = await parentUserSchema
                .find(
                    {
                        _id: announcementUser.map((userElement) =>
                            convertToMongoObjectId(userElement._id),
                        ),
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        device_type: 1,
                        fcm_token: 1,
                    },
                )
                .lean();
            sendNotificationUser.push(...NotificationStudent, ...NotificationParent);
        }
        sendNotificationUser.forEach((userElement) => {
            fcm_token = userElement.fcm_token;
            device_type = userElement.device_type;
            const pushData = {
                Title: announcementData.announcementTitle,
                Body: announcementData.message,
                priorityType: announcementData.priorityType,
                announcementType: announcementData.announcementType,
                attachments: announcementData.attachments,
                sendBy: announcementData.createdBy,
                ClickAction: 'announcement',
                publishDate: announcementData.publishDate,
                announcementId: announcementData._id,
            };
            if (userElement.fcm_token) {
                fcm_token = userElement.fcm_token;
                sendNotificationPush(fcm_token, pushData, device_type);
            }
            if (userElement.web_fcm_token) {
                web_fcm_token = userElement.web_fcm_token;
                sendNotificationPush(web_fcm_token, pushData, device_type);
            }
        });
        if (!UpdateAnnouncement) return { statusCode: 200, message: 'Not_Created_Data' };
        return { statusCode: 200, message: 'Data_Created' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteAnnouncement = async ({ query = {} }) => {
    try {
        const { announcementId } = query;
        const announcementStatus = await announcementSchema.findOne(
            {
                _id: convertToMongoObjectId(announcementId),
            },
            { status: 1 },
        );
        if (announcementStatus?.status === PUBLISHED) {
            return { statusCode: 200, message: 'Announcement has been published' };
        }
        connection.redisClient.Client.del(`announcement2:${announcementId}-publish`);
        connection.redisClient.Client.del(`announcement2:${announcementId}-expire`);
        const deleteAnnouncement = await announcementSchema.deleteOne({
            _id: convertToMongoObjectId(announcementId),
        });
        if (!deleteAnnouncement.deletedCount)
            return { statusCode: 410, message: 'Not delete announcement' };
        return { statusCode: 200, message: 'Delete announcement' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
