const { convertToMongoObjectId } = require('../utility/common');
const {
    DISCIPLINARY_REMARKS_TYPE,
    COURSE_SCHEDULE,
    USER,
    GLOBAL_SESSION_TARDIS,
} = require('../utility/constants');
const disciplinaryRemarksSchema = require('./disciplinaryRemarks.model');
const courseSchedulesSchema = require('../models/course_schedule');
const userSchema = require('../models/user');
const { getPaginationValues } = require('../utility/pagination');
const { send_email, getSignedURL } = require('../utility/common_functions');
const { makeRemarksMailContent } = require('./disciplinaryRemarks.service');

const addNewDisciplinaryRemarks = async ({ body = {} }) => {
    try {
        const {
            scheduleId,
            institutionCalendarId,
            studentId,
            tardisId,
            comment,
            attachments = [],
            type,
            reportedBy,
        } = body;
        if (type === DISCIPLINARY_REMARKS_TYPE.SCHEDULE_LEVEL) {
            const isDisciplinaryRemarksAlreadyCreated = await disciplinaryRemarksSchema
                .findOneAndUpdate(
                    {
                        studentId: convertToMongoObjectId(studentId),
                        institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                        scheduleId: convertToMongoObjectId(scheduleId),
                        type,
                        isDeleted: false,
                    },
                    {
                        comment,
                        tardisId,
                        reportedBy,
                    },
                    {
                        new: true,
                        upsert: true,
                    },
                )
                .lean();
            if (isDisciplinaryRemarksAlreadyCreated) {
                return {
                    status: true,
                    message: 'ALREADY_REMARKS_CREATED',
                    statusCode: 200,
                    data: isDisciplinaryRemarksAlreadyCreated,
                };
            }
        }
        const createdDisciplinaryRemarks = await disciplinaryRemarksSchema.create({
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
            studentId: convertToMongoObjectId(studentId),
            type,
            tardisId: convertToMongoObjectId(tardisId),
            reportedBy: convertToMongoObjectId(reportedBy),
            ...(scheduleId && { scheduleId: convertToMongoObjectId(scheduleId) }),
            ...(comment && { comment }),
            ...(attachments.length && { attachments }),
        });
        return {
            status: true,
            message: 'DOCUMENT_CREATED',
            statusCode: 201,
            data: createdDisciplinaryRemarks,
        };
    } catch (error) {
        return { statusCode: 500, status: false, message: error.message };
    }
};

const updateDisciplinaryRemarks = async ({ body = {} }) => {
    try {
        const { remarkId, comment, tardisId, attachments = [], reportedBy } = body;
        const updatedRemarks = await disciplinaryRemarksSchema.findOneAndUpdate(
            { _id: convertToMongoObjectId(remarkId) },
            {
                ...((comment === '' || comment) && { comment }),
                ...(tardisId && { tardisId: convertToMongoObjectId(tardisId) }),
                ...(attachments.length && { attachments }),
                ...(reportedBy && { reportedBy: convertToMongoObjectId(reportedBy) }),
            },
            {
                new: true,
            },
        );
        return {
            status: true,
            statusCode: 200,
            message: 'DISCIPLINARY_REMARK_UPDATED_SUCCESSFULLY',
            data: updatedRemarks,
        };
    } catch (error) {
        return { status: false, statusCode: 500, message: error.message };
    }
};

const deleteDisciplinaryRemarks = async ({ query = {} }) => {
    try {
        const { remarkId } = query;
        await disciplinaryRemarksSchema.updateOne(
            { _id: convertToMongoObjectId(remarkId) },
            { $set: { isDeleted: true, isActive: false } },
        );
        return {
            status: true,
            statusCode: 200,
            message: 'DISCIPLINARY_REMARKS_DELETED_SUCCESSFULLY',
        };
    } catch (error) {
        return { status: false, statusCode: 500, message: error.message };
    }
};

const getStudentsDisciplinaryRemarks = async ({ query, headers }) => {
    try {
        const { studentId, searchKey, remarkKeyId, staffId } = query;
        const { _institution_calendar_id } = headers;
        const { limit, pageNo, skip } = getPaginationValues(query);
        const studentRemarksQuery = {
            institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
            studentId: convertToMongoObjectId(studentId),
            isActive: true,
            isDeleted: false,
            ...(remarkKeyId && { tardisId: convertToMongoObjectId(remarkKeyId) }),
            ...(staffId && { reportedBy: convertToMongoObjectId(staffId) }),
        };

        const remarkAggregateQuery = [
            {
                $match: studentRemarksQuery,
            },
            {
                $lookup: {
                    from: USER,
                    localField: 'reportedBy',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $project: {
                                name: 1,
                            },
                        },
                    ],
                    as: 'reportedBy',
                },
            },
            {
                $lookup: {
                    from: GLOBAL_SESSION_TARDIS,
                    localField: 'tardisId',
                    foreignField: '_id',
                    pipeline: [
                        {
                            $project: {
                                _id: 1,
                                name: 1,
                                short_code: 1,
                            },
                        },
                    ],
                    as: 'tardisDetails',
                },
            },
            {
                $project: {
                    tardisId: { $arrayElemAt: ['$tardisDetails', 0] },
                    attachments: '$attachments',
                    comment: '$comment',
                    reportedBy: { $arrayElemAt: ['$reportedBy', 0] },
                    scheduleId: '$scheduleId',
                    type: '$type',
                    updatedAt: '$updatedAt',
                    mailStatus: '$mailData.isSent',
                },
            },
        ];
        if (searchKey) {
            remarkAggregateQuery.push(
                {
                    $match: {
                        $or: [
                            { 'tardisId.name': { $regex: searchKey, $options: 'i' } },
                            { 'tardisId.short_code': { $regex: searchKey, $options: 'i' } },
                            { 'reportedBy.name.first': { $regex: searchKey, $options: 'i' } },
                            { 'reportedBy.name.middle': { $regex: searchKey, $options: 'i' } },
                            { 'reportedBy.name.last': { $regex: searchKey, $options: 'i' } },
                        ],
                    },
                },
                {
                    $group: {
                        _id: null,
                        documents: { $push: '$$ROOT' },
                        totalCount: { $sum: 1 },
                    },
                },
                { $unwind: { path: '$documents' } },
                {
                    $replaceRoot: {
                        newRoot: {
                            $mergeObjects: ['$documents', { totalCount: '$totalCount' }],
                        },
                    },
                },
            );
        }
        remarkAggregateQuery.push(
            ...[
                {
                    $sort: { _id: -1 },
                },
                {
                    $skip: skip,
                },
                {
                    $limit: limit,
                },
                {
                    $lookup: {
                        from: COURSE_SCHEDULE,
                        localField: 'scheduleId',
                        foreignField: '_id',
                        let: { scheduleId: '$scheduleId' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $eq: ['$_id', '$$scheduleId'],
                                    },
                                },
                            },
                            {
                                $project: {
                                    students: {
                                        $filter: {
                                            input: '$students',
                                            as: 'student',
                                            cond: {
                                                $eq: [
                                                    '$$student._id',
                                                    convertToMongoObjectId(studentId),
                                                ],
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: { $arrayElemAt: ['$students._id', 0] },
                                    status: { $arrayElemAt: ['$students.status', 0] },
                                },
                            },
                        ],
                        as: 'student',
                    },
                },
            ],
        );

        const studentsRemarks = await disciplinaryRemarksSchema.aggregate(remarkAggregateQuery);
        const responseObj = { studentDisciplinaryRemarks: studentsRemarks, pageNo, limit };
        if (pageNo === 1 && studentsRemarks.length) {
            const totalDocs =
                studentsRemarks[0].totalCount ??
                (await disciplinaryRemarksSchema.countDocuments(studentRemarksQuery));
            responseObj.totalCount = totalDocs;
            const staffListIds = await disciplinaryRemarksSchema.distinct(
                'reportedBy',
                studentRemarksQuery,
            );
            const staffList = await userSchema
                .find({ _id: { $in: staffListIds } }, { name: 1, mobile: 1 })
                .lean();
            responseObj.staffList = staffList;
        }
        return {
            status: true,
            statusCode: 200,
            data: responseObj,
        };
    } catch (error) {
        return { status: false, statusCode: 500, message: error.message };
    }
};

const getScheduleDetails = async ({ query = {} }) => {
    try {
        const { scheduleId } = query;
        const scheduleDetails = await courseSchedulesSchema
            .findById(
                { _id: convertToMongoObjectId(scheduleId) },
                {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    'session.session_topic': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    year_no: 1,
                    level_no: 1,
                    schedule_date: 1,
                    start: 1,
                    type: 1,
                    title: 1,
                    'merge_with.schedule_id': 1,
                    scheduleStartDateAndTime: 1,
                },
            )
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .lean();
        return { status: true, statusCode: 200, data: scheduleDetails };
    } catch (error) {
        return { status: false, statusCode: 500, message: error.message };
    }
};

const uploadDisciplinaryRemarkFiles = async ({ body = {} }) => {
    try {
        const { urls } = body;
        let { existingUrl = [] } = body;
        existingUrl = typeof existingUrl === 'string' ? [existingUrl] : existingUrl;
        if (urls?.length || existingUrl.length) {
            return {
                statusCode: 200,
                message: 'DOCUMENTS_INSERTED',
                data: [...urls, ...existingUrl],
            };
        }
        return { statusCode: 200, message: 'DOCUMENTS_NOT_INSERTED', data: existingUrl };
    } catch (error) {
        return { status: false, statusCode: 500, message: error.message };
    }
};

const sendRemarksMail = async ({ body = {}, headers = {} }) => {
    try {
        const { remarkId } = body;
        const { _user_id } = headers;
        const disciplinaryRemarkData = await disciplinaryRemarksSchema
            .findOne(
                { _id: convertToMongoObjectId(remarkId) },
                {
                    comment: 1,
                    reportedBy: 1,
                    scheduleId: 1,
                    tardisId: 1,
                    studentId: 1,
                    updatedAt: 1,
                    type: 1,
                },
            )
            .populate({
                path: 'studentId',
                select: { name: 1, email: 1 },
            })
            .populate({
                path: 'reportedBy',
                select: { name: 1 },
            })
            .populate({
                path: 'scheduleId',
                select: {
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    'session.session_topic': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    year_no: 1,
                    level_no: 1,
                    schedule_date: 1,
                    start: 1,
                    type: 1,
                    title: 1,
                    merge_with: 1,
                },
                populate: {
                    path: 'merge_with.schedule_id',
                    select: {
                        'session.delivery_symbol': 1,
                        'session.delivery_no': 1,
                    },
                },
            })
            .populate({
                path: 'tardisId',
                select: { name: 1 },
            })
            .lean();
        if (!disciplinaryRemarkData) {
            return { statusCode: 404, message: 'Data not found' };
        }
        const { toMail, subject, mailContent } = makeRemarksMailContent({ disciplinaryRemarkData });
        await send_email(toMail, subject, mailContent);
        await disciplinaryRemarksSchema.updateOne(
            { _id: convertToMongoObjectId(remarkId) },
            {
                $set: {
                    'mailData.isSent': true,
                },
                $addToSet: {
                    'mailData.sentDetails': {
                        sentOn: new Date(),
                        sentBy: convertToMongoObjectId(_user_id),
                    },
                },
            },
        );
        return { statusCode: 200, message: 'success' };
    } catch (error) {
        return { statusCode: 500, message: error.message };
    }
};

const generateSignedURLs = async ({ body = {} }) => {
    try {
        const { fileUrls } = body;
        const signedUrls = await Promise.all(
            fileUrls.map((urlElement) => getSignedURL(urlElement)),
        );
        return { statusCode: 200, data: signedUrls };
    } catch (error) {
        return { statusCode: 500, message: error.message };
    }
};

const getMailSentDetails = async ({ query = {} }) => {
    try {
        const { remarkId } = query;

        const mailData = await disciplinaryRemarksSchema
            .findOne({ _id: convertToMongoObjectId(remarkId) }, { 'mailData.sentDetails': 1 })
            .populate({ path: 'mailData.sentDetails.sentBy', select: { name: 1 } })
            .lean();

        return { data: mailData?.mailData?.sentDetails, statusCode: 200, message: 'success' };
    } catch (error) {
        return { statusCode: 500, message: error.message };
    }
};
module.exports = {
    addNewDisciplinaryRemarks,
    updateDisciplinaryRemarks,
    deleteDisciplinaryRemarks,
    getStudentsDisciplinaryRemarks,
    getScheduleDetails,
    uploadDisciplinaryRemarkFiles,
    sendRemarksMail,
    generateSignedURLs,
    getMailSentDetails,
};
