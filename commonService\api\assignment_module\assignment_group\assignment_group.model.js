const mongoose = require('mongoose');
const {
    ASSIGNMENT_GROUP,
    SUBMITTED,
    PENDING,
    COMPLETED,
    EXCUSED,
} = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const assignmentGroupSchemas = new Schema(
    {
        _assignment_id: {
            type: ObjectId,
        },
        _institution_id: {
            type: ObjectId,
        },
        groupSetName: {
            type: String,
        },
        grouping: {
            type: String,
        },
        courseGroup: {
            type: String,
        },
        courseGroupId: [
            {
                groupId: ObjectId,
                subGroups: [
                    {
                        subGroup: String,
                        sessionGroups: [{ sessionGroupId: ObjectId }],
                    },
                ],
            },
        ],
        noOfGroup: { type: Number },
        enrollmentDate: {
            type: Date,
        },
        groups: [
            {
                groupName: { type: String },
                description: { type: String },
                memberLimit: {
                    type: Number,
                },
                students: [
                    {
                        studentId: { type: ObjectId },
                    },
                ],
                groupLeader: [
                    {
                        _student_id: ObjectId, //need to remove
                        studentName: { first: String, last: String }, //need to remove
                        studentId: String,
                        name: String,
                        academic_no: String,
                    },
                ],
                prompts: [{ _prompt_id: ObjectId }],
                instructor: [{ _staff_id: ObjectId, staffName: { first: String, last: String } }],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        term: {
            type: String,
        },
        rotation_count: {
            type: Number,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSIGNMENT_GROUP, assignmentGroupSchemas);
