{"WELCOME_MESSAGE": "Welcome", "SERVER_ERROR": "Server error", "VALIDATION_ERROR": "Validation error", "YOUR_CHANNELS_CREATED": "Your channels were successfully created", "YOUR_CHAT_REGISTRATION_COMPLETED": "Your chat registration completed", "YOUR_REGISTRATION_COMPLETED": "Your Registration Completed, <PERSON>", "PLEASE_AUTHENDICATE": "Please authenticate", "AUTHENDICATION_CODE_NOT_MATCHING": "Authentication code not matching", "USER_NOT_FOUND": "User not found!", "COURSE_NOT_FOUND": "Course not found!", "ACTIVITY_NOT_FOUND": "Activity not found!", "DATA_RETRIEVED": "Data retrieved", "NO_DATA_FOUND": "No data found!", "FAILED_TO_ADD": "Failed to add", "FAILED_TO_UPDATE": "Failed to update", "QUIZ_ADDED_SUCCESSFULLY": "Quiz added successfully", "POLL_ADDED_SUCCESSFULLY": "Poll added successfully", "SURVERY_ADDED_SUCCESSFULLY": "Survey added successfully", "QUIZ_DRAFTED_SUCCESSFULLY": "Quiz added successfully", "POLL_DRAFTED_SUCCESSFULLY": "Poll added successfully", "SURVERY_DRAFTED_SUCCESSFULLY": "Survey added successfully", "QUIZ_PUBLISHED_SUCCESSFULLY": "Quiz published successfully", "POLL_PUBLISHED_SUCCESSFULLY": "Poll published successfully", "SURVERY_PUBLISHED_SUCCESSFULLY": "Survey published successfully", "ADDED_SUCCESSFULLY": "Added successfully", "ACTIVITY_UPDATE_ERROR": "Activity update error", "UPDATE_ERROR": "Update error", "UPDATED_SUCCESSFULLY": "Updated successfully", "DELETE_ERROR": "Delete error", "DELETED_SUCCESSFULLY": "Deleted successfully", "DO_YOU_WANT_TO_CHANGE_SESSION": "Do you want to changes session", "STUDENT_GROUP_LIST": "Student group list", "STUDENT_GROUP_NOT_FOUND": "Student group not found", "ALREADY_THIS_QUIZ_SCHEDULED": "Already this quiz scheduled", "STANDARD": "standard", "SELECTIVE": "selective", "DUPLICATE_IN_EXCEL": "Duplicate in Excel", "PROGRAM_NAME_ALREADY_EXIST": "Program name already exist", "PROGRAM_CODE_ALREADY_EXIST": "Program code already exist", "TYPE_MUST_BE": "Type must be ", "PROGRAM_TYPE_MUST_BE": "Program Type must be ", "CHECK_PROGRAM_TYPE_IS_EMPTY": "Check Program Type is empty", "CHECK_PROGRAM_LEVEL_IS_EMPTY": "Check Program Level is empty", "CHECK_DEGREE_NAME_IS_EMPTY": "Check Degree Name is empty", "CHECK_TYPE_IS_EMPTY": "Check Type is empty", "CHECK_TERMS_AND_NO_OF_TERMS": "Check Terms and No of terms", "NO_OF_TERMS_MUST_BE_INTEGER": "No of terms must be integer", "INSTITUTION_NOT_FOUND": "Institution not found", "REQUIRED_FIELD_VALIDATION_FAILED": "Required field validation failed", "DATA_CHECK_VALIDATION_FAILED": "Data check validation failed", "DATA_CHECK_VALIDATION_SUCCESS": "Data check validation success", "PROGRAM_NOT_FOUND": "Program not found", "PROGRAM_LIST": "Program List", "DUPLICATE_PROGRAM": "Duplicate Program", "PROGRAM_CREATED": "Program Created", "UNABLE_TO_CREATE_PROGRAM": "Unable to Create Program", "UNABLE_TO_CHANGE_PROGRAM_NAME_ROLE_ASSIGN": "Unable to change program name role assign", "PROGRAM_NAME_CHANGED_IN_ROLE_ASSIGN": "Program name changed in role assign", "PROGRAM_UPDATED": "Program Updated", "PROGRAM_REMOVED": "Program Removed", "UNABLE_TO_REMOVED_PROGRAM": "Unable to Removed Program", "PROGRAM_DEPARTMENT_LIST": "Program Department List", "SIDE_BAR": "Side bar", "IS_REQUIRED": "is required", "PROGRAM_IMPORTED_SUCCESSFULLY": "Program imported successfully", "UNABLE_TO_IMPORT_PROGRAM": "Unable to import program", "DUPLICATE_DEPARTMENT_NAME": "Duplicate Department name", "DEPARTMENT_ADDED_SUCCESSFULLY": "Department added successfully", "UNABLE_TO_ADD_DEPARTMENT": "Unable to add department", "DEPARTMENT_UPDATED_SUCCESSFULLY": "Department updated successfully", "UNABLE_TO_UPDATE_DEPARTMENT": "Unable to update department", "DEPARTMENT_NOT_FOUND": "Department not found", "DEPARTMENT_SUBJECT_LIST": "Department Subject List", "THIS_DEPARTMENT_ASSOCIATED_WITH_COURSES": "This Department Associated with Courses", "DEPARTMENT_DELETED_SUCCESSFULLY": "Department deleted successfully", "DUPLICATE_SUBJECT_NAME": "Duplicate Subject name", "SUBJECT_ADDED_SUCCESSFULLY": "Subject added successfully", "UNABLE_TO_ADD_SUBJECT": "Unable to add subject", "SUBJECT_UPDATED_SUCCESSFULLY": "Subject updated successfully", "UNABLE_TO_UPDATE_SUBJECT": "Unable to update subject", "THIS_SUBJECT_IS_ASSOCIATED_WITH_COURSES": "This Subject is Associated with Courses", "SUBJECT_REMOVED_SUCCESSFULLY": "Subject Removed successfully", "DEPARTMENT_IS_ASSOCIATED_WITH_COURSE": "Department is Associated with Course", "DEPARTMENT_SHARED_SUCCESSFULLY": "Department shared successfully", "UNABLE_TO_SHARE_DEPARTMENT": "Unable to share department", "DEPARTMENT_SUBJECT_IS_ASSOCIATED_WITH_COURSE": "Department Subject is Associated with Course", "CHECK_PROGRAM_NAME_AND_CODE": "Check program name and code", "DEPARTMENT_ALREADY_EXIST": "Department already exist", "CHECK_SUBJECTS_ARE_DUPLICATE": "Check subjects are duplicate", "CHECK_SUBJECTS_IS_DUPLICATE_IN_BETWEEN_DEPARTMENTS": "Check subjects is duplicate in between departments ", "DEPARTMENTS_AND_SUBJECTS_IMPORTED_SUCCESSFULLY": "Departments and Subjects imported successfully", "UNABLE_TO_DEPARTMENTS_AND_SUBJECTS": "Unable to Departments and Subjects", "DUPLICATE_SESSION_NAME_SESSION_SYMBOL": "Duplicate Session_name/Session_symbol", "SESSION_TYPE_ADDED_SUCCESSFULLY": "Session type added successfully", "UNABLE_TO_ADD_SESSION_TYPES": "Unable to add Session Types", "SESSION_TYPE_UPDATED_SUCCESSFULLY": "Session type updated successfully", "SESSION_TYPE_LIST": "Session type List", "THIS_SESSION_TYPE_ASSOCIATED_WITH_COURSES": "This Session type Associated with Courses", "SESSION_TYPE_DELETED_SUCCESSFULLY": "Session type deleted successfully", "SORRY_NO_SESSION_TYPE_DATA_SO_YOU_CAN_NOT_ADD_DELIVERY_TYPE": "Sorry No session type data, So you can't add delivery type", "DUPLICATE_DELIVERY_TYPE_NAME_SYMBOL": "Duplicate Delivery type Name/Symbol", "DELIVERY_TYPE_ADDED_SUCCESSFULLY": "Delivery type added successfully", "UNABLE_TO_ADD_DELIVERY_TYPE": "Unable to add delivery type", "DELIVERY_TYPE_UPDATED_SUCCESSFULLY": "Delivery type updated successfully", "UNABLE_TO_UPDATE_DELIVERY_TYPE": "Unable to update delivery type", "THIS_DELIVERY_TYPE_ASSOCIATED_WITH_COURSES": "This Delivery Type Associated with Courses", "DELIVERY_TYPE_DELETED_SUCCESSFULLY": "Delivery type deleted successfully", "UNABLE_TO_DELETE_DELIVERY_TYPE": "Unable to delete delivery type", "CHECK_PROGRAM_NAME": "Check program name", "CHECK_SESSION_NAME_AND_SESSION_SYMBOL_ALREADY_EXIST": "Check Session name and session symbol already exist", "SESSION_NAME_OR_SESSION_SYMBOL_IS_EMPTY": "Session name OR session symbol is empty", "CHECK_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER": "Check contact hours per credit hour should be integer", "CHECK_SESSION_DURATION_SHOULD_BE_INTEGER": "Check Session Duration should be integer", "SESSION_TYPE_IMPORTED_SUCCESSFULLY": "Session type imported successfully", "UNABLE_TO_IMPORT_SESSION_TYPE": "Unable to import Session type", "SESSION_NAME_NOT_FOUND": "Session name not found", "DELIVERY_NAME_ALREADY_EXIST": "Delivery name already exist", "DELIVERY_NAME_OR_DELIVERY_SYMBOL_IS_EMPTY": "Delivery Name or Delivery symbol is empty", "CHECK_DELIVERY_DURATION_SHOULD_BE_INTEGER": "Check Delivery Duration should be integer", "SESSION_AND_DELIVERY_TYPE_NOT_FOUND": "Session and delivery type not found", "DELIVERY_TYPE_DATA_NOT_FOUND": "Delivery type data not found", "DELIVERY_TYPE_IMPORTED_SUCCESSFULLY": "Delivery type Imported successfully", "DUPLICATE_COURSE_CODE": "Duplicate course code", "COURSE_ADDED_SUCCESSFULLY": "Course added successfully", "UNABLE_TO_ADD_COURSE": "Unable to add course", "UNABLE_TO_UPDATE_COURSE": "Unable to update course", "COURSE_UPDATED_SUCCESSFULLY": "Course updated successfully", "COURSE_DELETED_SUCCESSFULLY": "Course deleted successfully", "UNABLE_TO_DELETE_COURSE": "Unable to delete course", "COURSES_LIST": "Courses List", "UNABLE_TO_ADD_SESSION_FLOW": "Unable to add Session Flow", "SESSION_FLOW_ADDED_SUCCESSFULLY": "Session Flow added successfully", "SESSION_FLOW_NOT_FOUND": "Session flow Not found", "COURSE_IS_SCHEDULED_YOU_CAN_NOT_CHANGE_THE_SUBJECT_DELIVERY_TYPE": "Course is scheduled. You can't change the subject, delivery type", "COURSE_DETAILS": "Course Details", "SESSION_FLOW_DETAILS": "Session Flow Details", "NOT_FOUND": "Not found", "PROGRAM_LEVEL_LIST": "Program Level List", "PROGRAM_CURRICULUM_YEAR_LEVEL_LIST": "Program curriculum year level list", "PROGRAM_SESSION_AND_DELIVERY_TYPE_LIST": "Program session and delivery type list", "COURSE_DRAFTED_SUCCESSFULLY": "Course drafted successfully", "UNABLE_TO_DRAFT_COURSE": "Unable to draft course", "CURRICULUM_NOT_FOUND": "Curriculum Not found", "YEAR_NOT_FOUND": "Year Not found", "LEVEL_NOT_FOUND": "Level Not found", "COURSE_START_END_WEEK_IS_NOT_IN_BETWEEN_OF_LEVEL_START_END_WEEK": "Course Start/End week is not in between of level Start/End week", "COURSE_ASSIGNED_SUCCESSFULLY": "Course assigned successfully", "UNABLE_TO_ASSIGN_COURSE": "Unable to assign course", "COURSE_ASSIGN_EDITED_SUCCESSFULLY": "Course assign edited successfully", "UNABLE_TO_EDIT_COURSE_ASSIGN": "Unable to edit course assign", "COURSE_ASSIGNED_TO_FRAMEWORK_CAN_NOT_BE_DELETED": "Course assigned to Framework, can't be deleted", "CURRICULUM_YEAR_NOT_FOUND": "Curriculum Year Not found", "CURRICULUM_LEVEL_NOT_FOUND": "Curriculum Level Not found", "ASSIGNED_COURSES_LIST": "Assigned Courses List", "COURSES_LIST_WITH_RECURRING_LEVEL": "Courses List with recurring level", "COURSES_LIST_BY_CURRICULUM_ID": "Courses List by curriculum ID", "CHECK_CURRICULUM": "Check Curriculum", "CHECK_START_WEEK": "check start week", "COURSE_START_AND_END_WEEK_ARE_NOT_MATCH_WITH_LEVEL": "Course start and end week are not match with level:", "COURSE_RECURRING_LEVEL_NOT_FOUND": "Course Recurring Level not found", "IS_NOT_PRESENT_IN_COURSE_RECURRING_LEVEL": "is not present in course recurring level", "LEVEL": "Level", "COURSE_CODE_ALREADY_EXIST": "Course code already exist", "COURSE_NAME_ALREADY_EXIST": "Course name already exist", "DELIVERING_SUBJECTS_IS_EMPTY": "Delivering Subjects is empty", "PARTICIPATING_DEPARTMENT_NOT_FOUND": "Participating Department not found : ", "PARTICIPATING_SUBJECT_NOT_FOUND": "Participating Subject not found :", "ADMINISTRATING_SUBJECT_IS_EMPTY": "Administrating Subject is empty", "ADMINISTRATING_DEPARTMENT_NOT_FOUND": "Administrating Department not found : ", "ADMINISTRATING_SUBJECT_NOT_FOUND": "Administrating Subject not found : ", "CHECK_THEORY_CREDIT_HOURS_SHOULD_BE_INTEGER": "Check Theory credit hours should be integer", "CHECK_THEORY_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER": "Check Theory contact hours per credit hour should be integer", "CHECK_THEORY_DURATION_IN_MINUTES_SHOULD_BE_INTEGER": "Check Theory duration in minutes should be integer", "CHECK_PRACTICAL_CREDIT_HOURS_SHOULD_BE_INTEGER": "Check Practical credit hours should be integer", "CHECK_PRACTICAL_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER": "Check practical contact hours per credit hour should be integer", "CHECK_PRACTICAL_DURATION_IN_MINUTES_SHOULD_BE_INTEGER": "Check practical duration in minutes should be integer", "CHECK_CLINICAL_CREDIT_HOURS_SHOULD_BE_INTEGER": "Check Clinical credit hours should be integer", "CHECK_CLINICAL_SHOULD_BE_INTEGER": "Check Clinical should be integer", "CHECK_CLINICAL_DURATION_IN_MINUTES_SHOULD_BE_INTEGER": "Check Clinical Duration in minutes should be integer", "CHECK_ALLOW_TO_EDIT_CREDIT_HOURS_WHILE_SCHEDULING_SHOULD_BE_BOOLEAN": "Check Allow_to_edit_credit_hours_while_scheduling should be boolean", "CHECK_SHOULD_ACHIEVE_TARGET_CREDIT_HOURS_SHOULD_BE_BOOLEAN": "Check Should_Achieve_target_credit_hours should be boolean", "COURSE_RECURRING_AT": "Course_Recurring_at", "CHECK_THEORY_CREDIT_HOURS": "Check theory credit hours", "CHECK_PRACTICAL_CREDIT_HOURS": "Check Practical credit hours", "CHECK_CLINICAL_CREDIT_HOURS": "Check clinical credit hours", "COURSE_IMPORTED_SUCCESSFULLY": "Course imported successfully", "UNABLE_TO_IMPORT_COURSE": "Unable to import course", "CHECK_COURSE_CODE": "Check Course code", "CHECK_YEAR": "Check Year", "CHECK_LEVEL": "Check Level:", "DELIVERY_SYMBOL_AND_DELIVERY_NUMBER_DUPLICATE": "Delivery Symbol and Delivery Number Duplicate: ", "S_NO_NUMBER_SHOULD_BE_INTEGER": "s_no number should be integer", "LEVELS_AND_DELIVERY_WEEKS_MISMATCH": "Levels and Delivery weeks mismatch", "CHECK_DELIVERY_TYPE_AND_SYMBOL": "Check Delivery type and symbol", "CREDIT_HOURS_FOR_SESSION_TYPE_IS": "Credit hours for session type is ", "SO_YOU_CAN_NOT_PUSH_DELIVERY_TYPE": ". So You can't push delivery type", "CHECK_DELIVERY_NUMBER_SHOULD_BE_INTEGER": "Check Delivery number should be integer", "CHECK_DELIVERY_TOPIC": "Check delivery topic", "SUBJECT_NOT_FOUND": "Subject not found", "CHECK_DURATION_MINUTES": "Check Duration Minutes", "SESSION_FLOW_IMPORTED_SUCCESSFULLY": "Session flow imported successfully", "UNABLE_TO_IMPORT_SESSION_FLOW": "Unable to import Session flow", "SESSION_FLOW_NOT_FOUND_FOR_THIS_COURSE": "Session flow not found for this course", "DELIVERY_SYMBOL_AND_DELIVERY_NUMBER_NOT_FOUND": "Delivery symbol and Delivery number not found", "SESSION_FLOW_DATA_NOT_FOUND": "Session flow data not found", "SUCCESSFULLY_IMPORTED": "Successfully imported", "DUPLICATE_CURRICULUM_NAME": "Duplicate Curriculum name", "CHECK_CREDIT_HOURS_DATA": "Check credit hours data", "CURRICULUM_ADDED_SUCCESSFULLY": "curriculum added successfully", "UNABLE_TO_ADD_CURRICULUM": "Unable to add curriculum", "CURRICULUM_UPDATED_SUCCESSFULLY": "curriculum updated successfully", "UNABLE_TO_UPDATE_CURRICULUM": "Unable to update curriculum", "CURRICULUM_LIST": "curriculum List", "CURRICULUM_DETAILS": "curriculum Details", "CURRICULUM_DELETED_SUCCESSFULLY": "curriculum deleted successfully", "UNABLE_TO_DELETE_CURRICULUM": "Unable to delete curriculum", "CURRICULUM_ARCHIVED_SUCCESSFULLY": "curriculum archived successfully", "UNABLE_TO_ARCHIVE_CURRICULUM": "Unable to archive curriculum", "CURRICULUM_ARCHIVED_LIST": "curriculum Archived List", "LEVEL_UPDATED_SUCCESSFULLY": "Level updated successfully", "CHECK_CURRICULUM_NAME_ALREADY_EXIST": "Check curriculum name already exist", "CHECK_HOUR_AS": "Check hour as", "CHECK_SET_VALUE_AS": "Check set value as", "CHECK_PROGRAM_DURATION_START_AT_MUST_BE_INTEGER": "Check program duration start at must be integer", "CHECK_PROGRAM_DURATION_END_AT_MUST_BE_INTEGER": "Check program duration end at must be integer", "COLLECTION_DATA_NOT_FOUND": "Collection data not found", "CURRICULUM_IMPORTED_SUCCESSFULLY": "Curriculum imported successfully", "UNABLE_TO_IMPORT_CURRICULUM": "Unable to import curriculum", "FRAMEWORK_ASSIGNED_TO_COURSES_CAN_NOT_BE_DELETED": "Framework assigned to courses, can't be deleted", "FRAMEWORK_COMMON_STANDARD_RANGE_SETTINGS": "Framework common standard range settings", "CURRICULUM_STANDARD_RANGE_SETTINGS": "Curriculum standard range settings", "ASSIGNED_TO_COURSES_CANNOT_BE": "assigned to courses, can't be", "MUST_ONLY_ALPHANUMERIC_WITH_24_CHARACTER": "must only contain alpha-numeric characters", "MIN_1_MAX_50_CHARACTERS_ALLOWED": "Minimum 1 and Maximum 50 characters allowed", "MIN_2_MAX_250_CHARACTERS_ALLOWED": "Minimum 2 and Maximum 250 characters allowed", "MIN_1_MAX_250_CHARACTERS_ALLOWED": "Minimum 1 and Maximum 250 characters allowed", "ONLY_NUMBER_ALLOWED": "Only Number allowed", "MIN_2_MAX_50_CHARACTERS_ALLOWED": "Minimum 2 and Maximum 50 characters allowed", "ONLY_TRUE_OR_FALSE_ALLOWED": "Only TRUE or FALSE allowed", "MIN_2_MAX_100_CHARACTERS_ALLOWED": "Minimum 2 and Maximum 100 characters allowed", "MIN_1_CHARACTERS_ALLOWED": "Minimum 1 characters allowed", "MIN_0_MAX_1000_NUMBER_ALLOWED": "Minimum 2 and Maximum 1000 numbers allowed", "MIN_1_MAX_100_CHARACTERS_ALLOWED": "Minimum 1 and Maximum 100 characters allowed", "MIN_1_MAX_6_CHARACTERS_ALLOWED": "Minimum 1 and Maximum 6 characters allowed", "MIN_2_MAX_1000_CHARACTERS_ALLOWED": "Minimum 2 and Maximum 1000 characters allowed", "SUCCESS": "success", "CLO_PLO_GRAPH_DATA": "clo plo graph data", "YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_COURSE_HAS_MAPPING": "You can't change framework as this course has mapping", "UPDATE_FRAMEWORK": "update framework", "ERROR_ON_UPDATE_FRAMEWORK": "Error on update framework", "ERROR": "error", "YOU_CAN_NOT_CHANGE_FRAMEWORK_AS_THIS_CURRICULUM_HAS_MAPPING": "You can't change framework as this Curriculum has mapping", "FRAMEWORK_ADDED_SUCCESSFULLY": "Framework added successfully", "UNABLE_TO_ADD_FRAMEWORK": "Unable to add framework", "REMOVE_FRAMEWORK": "Remove framework", "UNABLE_TO_REMOVE_FRAMEWORK": "Unable to remove framework", "PLO_ADDED_SUCCESSFULLY": "PLO added successfully", "UNABLE_TO_ADD_PLO": "Unable to add PLO", "PLO_UPDATED_SUCCESSFULLY": "PLO updated successfully", "UNABLE_TO_UPDATE_PLO": "Unable to update PLO", "ERROR_ON_DELETE_PLO": "Error on delete plo", "PLO_IS_MAPPED_WITH_CLO_CAN_NOT_BE_DELETED": "PLO is mapped with CLO, can't be deleted", "PLO_DELETED_SUCCESSFULLY": "PLO deleted successfully", "UNABLE_TO_DELETE": "Unable to Delete", "PLO_LIST": "PLO List", "CLO_ADDED_SUCCESSFULLY": "CLO added successfully", "UNABLE_TO_ADD_CLO": "Unable to add CLO", "CLO_UPDATED_SUCCESSFULLY": "CLO updated successfully", "UNABLE_TO_UPDATE_CLO": "Unable to update CLO", "CLO_IS_MAPPED_WITH_SLO_CAN_NOT_BE_DELETED": "CLO is mapped with SLO, can't be deleted", "CLO_IS_MAPPED_WITH_PLO_CAN_NOT_BE_DELETED": "CLO is mapped with PLO, can't be deleted", "CLO_DELETED_SUCCESSFULLY": "CLO deleted successfully", "CLO_LIST": "CLO List", "GET_MAPPING_TREE": "get Mapping Tree", "ERROR_ON_GETTING_MAPPING_TREE": "Error on getting mapping tree", "GET_MAPPING_MATRIX_OLD": "get mapping matrix old", "GET_MAPPING_MATRIX": "get mapping matrix", "VALIDATION_SUCCESS_AND_UPDATED_MAPPING_MATRIX": "Validation success and updated mapping matrix", "CURRICULUM_MAPPED_SUCCESSFULLY": "Curriculum mapped successfully", "UNABLE_TO_MAP_CURRICULUM": "Unable to map curriculum", "CURRICULUM_ALREADY_MAPPED_YOU_CANT_MAP_COURSE": "Curriculum already mapped, You cant map course", "COURSE_MAPPED_SUCCESSFULLY": "Course mapped successfully", "UNABLE_TO_MAP_COURSE": "Unable to map course", "COURSE_CONTENT_MAPPED_SUCCESSFULLY": "Course content mapped successfully", "PLO_CLO_MAPPED_SUCCESSFULLY": "PLO CLO mapped successfully", "UNABLE_TO_MAP_PLO_CLO": "Unable to map PLO CLO", "CLO_SLO_MAPPED_SUCCESSFULLY": "CLO SLO mapped successfully", "UNABLE_TO_MAP_CLO_SLO": "Unable to map CLO SLO", "SLO_ADDED_SUCCESSFULLY": "SLO added successfully", "UNABLE_TO_ADD_SLO": "Unable to add slo", "SLO_UPDATED_SUCCESSFULLY": "SLO updated successfully", "UNABLE_TO_UPDATE_SLO": "Unable to update slo", "SLO_IS_MAPPED_WITH_CLO_CAN_NOT_BE_DELETED": "SLO is mapped with CLO, can`t be deleted", "SLO_DELETED_SUCCESSFULLY": "SLO deleted successfully", "UNABLE_TO_DELETE_SLO": "Unable to delete slo", "SLO_CLO_GRAPH_DATA": "slo clo graph data", "USER_LIST": "User list", "USER_DETAILS": "user details", "ERROR_ID_NOT_FOUND_OR_DATA_MISMATCH": "Error Id not found or data mismatch", "USER_ADDED_SUCCESSFULLY": "user Added successfully", "USER_UPDATE_SUCCESSFULLY": "User update successfully", "ERROR_USER_DATA_UNABLE_TO_UPDATE": "Error User data unable to update", "FOUND_DUPLICATE_DATA": "Found Duplicate data", "FOUND_DUPLICATE_EMPLOYEE_ID": "Found duplicate Employee ID", "FOUND_DUPLICATE_EMAIL": "Found duplicate Email", "FOUND_DUPLICATE_NATIONALITY_ID": "Found duplicate Nationality ID", "FOUND_DUPLICATE_ACADEMIC_NO": "Found duplicate Academic No", "ERROR_USER_ID_NOT_MATCH": "Error User id not match", "CHECK_PARSING_REFERENCE_USER_ID": "Check Parsing reference User ID", "USER_DELETED_SUCCESSFULLY": "user deleted successfully", "ERROR_PARSE_FIELD_IN_BODY": "Error parse field in body", "USER_LOGIN_SUCCESSFULLY": "User login successfully", "PASSWORD_NOT_MATCH": "Password not Match", "SIGNUP_PROCESS_IS_OVER_CONTACT_ADMIN": "Signup process is Over contact Admin", "EMAIL_ID_NOT_FOUND": "Email id Not found", "PASSWORD_SUCCESSFULLY_CHANGED": "Password successfully changed", "UNABLE_TO_CHANGE_PASSWORD": "Unable to change password", "IS_YOUR_DIGISCHEDULER_PORTAL_REGISTRATION_CODE": "is your DigiClass portal registration code.", "MOBILE_REGISTERED_AND_OTP_SEND": "Mobile Registered and OTP Send", "THIS_MOBILE_NUMBER_ALREADY_EXIST": "This mobile number already exist", "OTP_VERIFIED_SUCCESSFULLY": "OTP verified successfully", "UNABLE_TO_VERIFY_OTP": "Unable to verify OTP", "OTP_EXPIRES": "OTP Expires", "INVALID_OTP": "Invalid OTP", "MOBILE_NO_NOT_MATCH": "Mobile No not match", "DUPLICATE_MOBILE_NUMBER": "Duplicate Mobile number", "LOGIN_NOT_FOUND": "Login not found", "DEAR_DIGISCHEDULER_USER": "Dear DigiClass User,", "IS_SECRET_OTP_FOR_YOU_ACCOUNT_LOGIN_THIS_VALID_FOR_3MIN_PLS_DO_NOT_SHARE_OTP_WITH_ANYONE": "is SECRET OTP for you account login, this valid for 3min. Pls do not share OTP with anyone", "DIGISCHEDULER_ALERT": "DigiClass Alert", "IS_YOUR_DIGISCHEDULER_LOGIN_OTP": "is your DigiClass login OTP.", "OTP_SEND": "OTP Send", "YOU_NEED_TO_SIGNUP_FIRST": "You need to Signup First", "YOUR_PROFILE_IS_NOT_COMPLETED_ASK_ADMIN": "Your Profile is not completed ask Admin", "UNABLE_TO_FIND_ADMIN_LOGIN": "Unable to Find <PERSON><PERSON>", "USER_LOGGED_IN_SUCCESSFULLY": "User logged in successfully", "USER_PROFILE_UPDATED_SUCCESSFULLY": "User Profile updated successfully", "ERROR_UNABLE_TO_UPDATE_PROFILE_PLS_VALIDATE_EACH_AND_EVERY_FIELDS": "Error Unable to update Profile Pls validate each and every fields", "ERROR_IN_USER_ID_IS_NOT_MATCH_IN_DATABASE": "Error in User ID is not match in Database", "USER_ID_IS_NOT_MATCH_IN_DATABASE": "User ID is not match in Database", "UNABLE_TO_UPDATE_PROFILE_PLS_VALIDATE_EACH_AND_EVERY_FIELDS": "Unable to update Profile Pls validate each and every fields :", "YOUR_SIGNUP_PROCESS_IN_DIGI_CLASS_IS_COMPLETED_PLEASE_VISIT_ADMIN_OFFICE_TO_COMPLETE_YOUR_BIOMETRIC_PROCESS_AND_REGISTRATION.": "Your signup process in DigiClass is completed, please visit admin office to complete your Biometric process(Face capture) and registration.", "DEAR": "Dear", "PROFILE_DOC_UPDATE_MSG": "Your signup process in DigiClass is completed, please visit admin office to complete your Biometric process(Face capture) and registration.", "USER_DOCUMENTS_UPDATE_SUCCESSFULLY": "user documents update successfully", "PROFILE_STAFF_DOC_UPDATE_MSG": "Your signup process in DigiClass is completed, please visit admin office to complete your Biometric process(Face capture) and registration.", "PROFILE_STAFF_DOC_UPDATE_EMAIL_MSG": "Your signup process in DigiClass is completed, please visit admin office to complete your Biometric process(Face capture) and registration.", "USER_DOCUMENTS_UPDATED_SUCCESSFULLY": "User Documents updated successfully", "MAIL_SEND_SUCCESSFULLY": "Mail Send successfully", "ERROR_IN_USER_DATA_IS_NOT_MATCH_DATABASE": "Error in User data is not match Database", "DUPLICATE_NATIONALITY_ID_FOUND": "Duplicate Nationality ID found", "DUPLICATE_EMPLOYEE_ID": "Duplicate Employee ID", "DUPLICATE_ACADEMIC_NO": "Duplicate Academic no", "USER_DATAS_MODIFIED_SUCCESSFULLY": "user datas Modified successfully", "USER_FACE_FINGER_SUCCESSFULLY": "User Face,Finger successfully", "ERROR_USER_ID_IS_NOT_FOUND": "Error User Id is not found", "SCHEDULED_SUBJECTS_CANNOT_BE_REMOVED": "Scheduled Subjects cannot be removed", "USER_ACADEMIC_IS_SUCCESSFULLY_ALLOCATED": "user Academic is successfully allocated", "ID_NOT_FOUND": "ID Not found", "IS_YOUR_DIGISCHEDULER_PORTAL_OTP_CODE": "is your DigiClass portal OTP code.", "MOBILE_OTP_SEND": "Mobile OTP Send", "UNABLE_TO_SEND_OTP_CHECK_MOBILE_NO": "Unable to send OTP check mobile no", "MOBILE_NUMBER_CHANGED_SUCCESSFULLY": "Mobile Number changed successfully", "CAN_NOT_IN_ACTIVE_SCHEDULED_STAFF": "Can not In-Active scheduled staff", "USER_ACTIVE_STATUS_IS_CHANGED": "User Active status is Changed", "USER_FINGER_SUCCESSFULLY_STORED_IN_DB": "User Finger successfully Stored in DB", "UNABLE_TO_INSERT_USER_THUMP_PLEASE_RETRY": "Unable to insert user thump please retry", "USER_FACE_REG_SMS": "Your Registration process in DigiClass is completed. Thank you for your co-operation.", "USER_FACE_REG_EMAIL": "Your Registration process in DigiClass application is completed. Thank you for your co-operation.", "USER_FACE_SUCCESSFULLY_STORED_IN_DB": "User Face successfully Stored in DB", "UNABLE_TO_REGISTER_YOUR_FACE_PLEASE_RETRY": "Unable to Register your Face please retry", "USER_DATAS_IMPORTED_SUCCESSFULLY": "User Datas Imported successfully", "USER_DATAS_IMPORTED_SUCCESSFULLY_SOME_DATA_ARE_NOT": "User Datas Imported successfully, Some Data' are not", "ERROR_UNABLE_TO_IMPORT_USER_DETAILS": "Error unable to Import user details", "ERROR_FOUND_DUPLICATE_INVALID_DATA_IN_UPLOAD_FILE": "Error Found duplicate/Invalid data in upload file", "STAFF_LIST": "Staff list", "STAFF_NOT_FOUND": "Staff Not found", "STAFF_DETAILS": "Staff Details", "PROGRAM_SUCCESSFULLY_PUSHED_INTO_USER": "Program Successfully pushed into User", "UNABLE_TO_PUSH_PROGRAM": "Unable to Push Program", "PROGRAM_ID_IS_WRONG": "Program Id is Wrong", "ERROR_ACADEMIC_NO_NOT_FOUND_OR_DATA_MISMATCH": "Error Academic no not found or data mismatch", "USER_DOCUMENT_SIGNED_URL": "User Document Signed URL", "USER_SEARCH_GET_LIST": "User Search GET list", "USER_IMAGE_GET_LIST": "User Image GET list", "ERROR_NO_IMAGE_FOUND": "Error No Image Found", "_EMPLOYEE_ID_DOC_REQUIRED": "_employee_id_doc required", "_EMPLOYEE_ID_REQUIRED": "employee_id Required", "FIRST_NAME_REQUIRED": "first_name Required", "MIDDLE_NAME_REQUIRED": "middle_name Required", "LAST_NAME_REQUIRED": "last_name Required", "EMAIL_IS_NOT_A_VALID_MAIL_ADDRESS_DO_VERIFY": "Email is not a valid mail address do verify", "NATIONALITY_ID_MIN_MAX_REQUIRED": "Nationality Id Required Minimum 5 to 25 characters", "ACADEMIC_NO_ALPHA_REQUIRED": "Academic Id required with Alphanumeric Characters", "FAMILY_NAME_REQUIRED": "family_name Required", "PROGRAM_NO_REQUIRED": "program_no Required", "ENROLLMENT_YEAR_REQUIRED": "enrollment_year Required", "BATCH_REQUIRED": "batch Required", "EMAIL_REQUIRED": "Email Required", "PASSWORD_REQUIRED": "Password Required", "MIN_8_MAX_25_CHARACTERS_ALLOWED": "Minimum 8 and Maximum 25 characters allowed", "MIN_8_MAX_20_CHARACTERS_ALLOWED": "Minimum 8 and Maximum 20 characters allowed", "THERE_IS_NO_ROLE_ASSIGNED_FOR_THIS_USER": "There is no Role assigned for this user", "ASSIGNED_ROLES_LIST": "Assigned Roles List", "ASSIGNED_ROLES_NOT_FOUND": "Assigned Roles Not found", "ROLE_ASSIGNED_TO_USER": "Role Assigned to User", "UNABLE_TO_ASSIGN_ROLE_TO_USER": "Unable to Assign Role to User", "UNABLE_TO_FIND_ROLE": "Unable to find Role", "DEPARTMENT_LIST": "Department List", "PROGRAM_CALENDAR_REVIEWS": "Program calendar Reviews", "PROGRAM_CALENDAR_PLEASE_CHECK_SOME_EVENT_DATE/ALREADY_PRESENT_IN_THAT_YEAR": "Check Event Data/Event Already Present in that Year", "ROLE_LIST": "Role List", "COUNTRY_LIST": "Country List", "LIST": "List", "LIST_BY_USERID": "List by UserID", "INSTITUTION_CALENDAR_LIST": "institution_calendar list", "INSTITUTION_CALENDAR_DETAILS": "institution_calendar details", "INSTITUTION_CALENDAR_ADDED_SUCCESSFULLY": "institution_calendar Added successfully", "ERROR_ID_NOT_MATCH": "Error id not match", "CHECK_PARSHING_REFERENCE_ID": "Check Parshing reference ID", "INSTITUTION_CALENDAR_UPDATE_SUCCESSFULLY": "institution_calendar update successfully", "INSTITUTION_CALENDAR_DELETED_SUCCESSFULLY": "institution_calendar deleted successfully", "MONTH_IN_BETWEEN_TWO_DATES": "Month in Between Two Dates", "CALENDAR_NAME_REQUIRED": "calendar_name: Required", "CALENDAR_TYPE_MUST_BE_PRIMARY_SECONDARY": "calendar_type: MUST BE PRIMARY SECONDARY", "_PRIMARY_CALENDAR_ID_REQUIRED": "_primary_calendar_id: Required", "_CALENDAR_ID_REQUIRED": "_creater_id: Required", "_INSTITUTION_ID_REQUIRED": "_institution_id: Required", "PRIMARY_CALENDAR_REQUIRED": "primary_calendar: MUST BE GREGORIAN HIJRI", "BATCH_REQUIRED_MIN_MAX": "batch: MIN 1 MAX 10 Characters Required", "START_DATE_REQUIRED": "start_date: Required", "END_DATE_REQUIRED": "end_date: Required", "ID_REQUIRED": "id Required", "INSTITUTION_CALENDAR_EVENT_LIST": "institution_calendar_event list", "INSTITUTION_CALENDAR_EVENT_DETAILS": "institution_calendar_event details", "EVENT_NOT_FOUND": "Event not found", "INSTITUTION_CALENDAR_EVENT_ADDED_SUCCESSFULLY": "institution_calendar_event Added successfully", "UNABLE_TO_ADD_EVENT": "Unable to Add Event", "THERE_IS_AN_EVENT_IN_SAME_DATE": "There is an Event in same date", "ID_NOT_MATCH": "Id not match", "CHECK_PARSING_REFERENCE_ID": "Check Parsing reference ID", "INSTITUTION_CALENDAR_EVENT_UPDATE_SUCCESSFULLY": "institution_calendar_event update successfully", "INSTITUTION_CALENDAR_EVENT_DELETED_SUCCESSFULLY": "institution_calendar_event deleted successfully", "THERE_IS_NO_EVENT_DATA_FOUND": "There is No Event Data found", "REVIEWER_ALREADY_PRESENT": "Reviewer already present", "REVIEWER_ID_NOT_MATCH": "Reviewer id not match", "REVIEWER_REMOVED_SUCCESSFULLY": "Reviewer Removed successfully", "REVIEWER_ID_OR_CALENDAR_ID_NOT_MATCH": "Reviewer ID Or Calendar ID not match", "REVIEW_ADDED_SUCCESSFULLY": "Review added successfully", "SEND_REVIEWER_NOTIFICATION_MSG": "<PERSON> has assigned you as Reviewer for Institution calendar, so please share your review for each events.", "SENDING_REVIEW_REQUEST": "Sending Review request", "YOUR_REVIEW_SENT_TO_DEAN_SUCCESSFULLY": "your review sent to <PERSON> successfully", "DIGISCHEDULER": "DigiClass", "INSTITUTION_CALENDAR_IS_SUCCESSFULLY_PUBLISHED": "Institution Calendar is Successfully Published", "UNABLE_TO_PUSH_NOTIFICATION_TO_USER": "Unable to Push Notification to user", "EVENT_CALENDAR_MUST_BE_INSTITUTION_OR_PROGRAM": "event_calendar Must be institution OR Program", "EVENT_TYPE_MUST_BE": "event_type Must be HOLIDAY OR OR<PERSON><PERSON>ATION OR TRAINING OR EXAM OR GENERAL", "EVENT_WHOM_MUST_BE": "event_whom Must be STAFF OR STUDENT OR BOTH", "EVENT_NAME_MIN_MAX": "event_name MIN 1 MAX 2500 Characters allowed", "SECOND_LANGUAGE_MIN_MAX": "second_language MIN 1 MAX 2500 Characters allowed", "FIRST_LANGUAGE_MIN_MAX": "first_language MIN 1 MAX 2500 Characters allowed", "EVENT_DATE_REQUIRED": "event_date Required", "START_TIME_REQUIRED": "start_time Required", "END_TIME_REQUIRED": "end_time Required", "_INFRASTRUCTURE_ID_REQUIRED": "_infrastructure_id Required", "_CALENDER_ID_REQUIRED": "_calendar_id Required", "EVENT_DESCRIPTION_REQUIRED": "event_description Required", "REVIEWER_ID_REQUIRED": "_reviewer_id Required", "EVENT_ID_REQUIRED": "_event_id Required", "REVIEWER_TYPE_REQUIRED": "reviewer_type Must be DEAN or REVIEWER", "REVIEW_MUST_BE_REQUIRED": "review Must be boolean", "REVIEW_COMMENT_MIN_REQUIRED": "review_comment Min 1 required", "TIME_GROUP_NOT_FOUND": "Time group not found", "TIME_GROUP_LIST": "Time group List", "TIME_GROUP_ADDED_SUCCESSFULLY": "Time group Added successfully", "UPDATE_SUCCESSFULLY": "Update successfully", "TIMINGS_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT": "Timings can't be deleted. It is already assigned in infrastructure management", "INFRA_NOT_FOUND": "Infra not found", "MUST_BE_ONSITE_REMOTE": "Must be onsite or remote", "MUST_BE_MALE_FEMALE_BOTH": "Must be male or female or both", "INFRASTRUCTURE_MANAGEMENT_LIST": "Infrastructure management list", "ERROR_UNABLE_TO_FIND": "Error unable to find", "DUPLICATE_RECORD": "Duplicate Record", "UNABLE_TO_REMOVE_INFRA_BECAUSE_ITS_SCHEDULED": "Unable to remove Infra because its Scheduled", "BUILDING_ID_REQUIRED": "_building_id is required", "BUILDING_NAME_REQUIRED": "building_name is required", "ROOM_NO_REQUIRED": "room_no required with min 3 characters", "NAME_REQUIRED": "name required ", "USAGE_MUST_BE": "usage must be Academic,Exam,Administrative,ACADEMIC,EXAM,ADMINISTRATIVE", "DELIVERY_TYPE_REQUIRED": "delivery_type is required", "DELIVERY_SYMBOL_REQUIRED": "delivery_symbol is required", "TIMING_REQUIRED": "timing is required", "PROGRAM_ID_REQUIRED": "_program_id is required", "SUBJECT_ID_REQUIRED": "_subject_id is required", "DEPARTMENT_REQUIRED": "_department_id is required", "DUPLICATE_BUILDING_AND_LOCATION_FOUND": "Duplicate Building and Location Found", "INFRASTRUCTURE_ADDED_SUCCESSFULLY": "Infrastructure Added successfully", "INFRASTRUCTURE_UPDATED_SUCCESSFULLY": "Infrastructure updated successfully", "BUILDING_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT": "Building can't be deleted. It is already assigned in infrastructure management", "INFRASTRUCTURE_DELETED_SUCCESSFULLY": "Infrastructure deleted successfully", "UNABLE_TO_FIND_BUILDING_HOSPITALS": "Unable to find Building & Hospitals", "INFRASTRUCTURES_LIST1": "Infrastructures List1", "INFRASTRUCTURES_LIST2": "Infrastructures List2", "CHECK_PARSING_ID": "Check parsing ID", "GOT_INFRASTRUCTURE_BY_ID": "Got infrastructure by ID", "GOT_INFRASTRUCTURE": "Got infrastructure", "LOCATION_REQUIRED": "location required", "TYPE_MUST_BE_REQUIRED": "type must be college or hospital required", "CATEGORY_MUST_BE_REQUIRED": "category must be level or basement required", "FLOOR_NAME_REQUIRED": "floor_name required", "ZONE_REQUIRED": "zone required", "PROGRAM_CALENDAR_NOT_FOUND": "Program calendar Not found", "STAFFS_NOT_FOUND": "Staffs Not found", "COURSES_NOT_FOUND": "Courses Not found", "COURSE_COORDINATORS": "Course Coordinators", "COURSE_COORDINATOR_ASSIGNED": "Course Coordinator Assigned", "ERROR_UNABLE_TO_CREATE_NEW_ROLE": "Error Unable to Create new Role", "COURSE_COORDINATOR_REMOVED": "Course Coordinator Removed", "UNABLE_TO_ASSIGN_COURSE_COORDINATOR": "Unable to Assign Course Coordinator", "YOU_HAVE_BEEN_ASSIGNED_AS_COURSE_COORDINATOR_FOR_": "You have been assigned as Course Coordinator for ", "COURSES": "courses", "THANK_YOU": "Thankyou", "ISNC": "ISNC", "ALL_COURSE_COORDINATORS_ALREADY_NOTIFIED": "All Course Coordinators already notified", "EMAIL_PUSHED_TO_COURSE_COORDINATORS": "<PERSON><PERSON> Pushed to Course Coordinators", "USERS_ROLE_NOT_FOUND": "Users Role not found", "COURSE_MANAGEMENT_SETTING_ADDED_SUCCESSFULLY": "Course Management Setting Added successfully", "SESSION_DATA_IS_NOT_FOUND": "Session data is not found", "SESSION_UPDATED_SUCCESSFULLY": "Session updated successfully", "SESSION_LIST_IS_NOT_FOUND": "Session List is not found", "SESSION_LIST": "Session List", "SESSION_TYPE_MUST_BE_BREAK_OR_EXTRA_CURRICULAR": "session_type must be break or extra_curricular", "TITLE_REQUIRED": "title required", "MODE_MUST_BE_WEEKLY_OR_DAILY": "mode must be weekly or daily", "SELECTED_DAYS": "selected_days must be sunday,monday,tuesday,wednesday,thursday,friday,saturday", "MEETING_TITLE_ALREADY_EXIST": "Meeting title already exist", "REMOTE_SCHEDULING_ADDED_SUCCESSFULLY": "Remote Scheduling added successfully", "UNABLE_TO_ADD_REMOTE_SCHEDULING": "Unable to add Remote Scheduling", "NO_RECORDS": "No Records", "LIST_REMOTE_SCHEDULING": "List remote scheduling", "REMOTE_SCHEDULING_UPDATED_SUCCESSFULLY": "Remote Scheduling updated successfully", "UNABLE_TO_UPDATE_REMOTE_SCHEDULING": "Unable to update remote scheduling", "REMOTE_SCHEDULING_DELETED_SUCCESSFULLY": "Remote scheduling deleted successfully", "UNABLE_TO_DELETE_REMOTE_SCHEDULING": "Unable to delete remote scheduling", "MEETINGTITLE_REQUIRED": "meetingTitle required", "MEETINGURL_REQUIRED": "meetingUrl required", "MEETINGUSERNAME_REQUIRED": "meetingUsername required", "ASSOCIATEDEMAIL_REQUIRED": "associatedEmail required", "MEETINGID_REQUIRED": "meetingId required", "PASSCODE_REQUIRED": "passCode required", "TERM_REQUIRED": "term required", "LEVELID_REQUIRED": "levelId required", "YEARID_REQUIRED": "yearId required", "YEARNAME_REQUIRED": "yearName required", "LEVELNAME_REQUIRED": "levelName required", "APIKEY_REQUIRED": "a<PERSON><PERSON><PERSON> required", "APISECRETKEY_REQUIRED": "apiSecret<PERSON>ey required", "PRORAMID_REQUIRED": "program_id required", "ERROR_REVIEWER_ALREADY_PRESENT": "Error Reviewer already present", "DUPLICATE_REVIEWER": "Duplicate Reviewer", "UNABLE_TO_ADD_REVIEWER": "Unable to add reviewer", "REVIEWER_ADD_SUCCESSFULLY": "Reviewer add Successfully", "UNABLE_TO_REMOVE_REVIEWER": "Unable to remove reviewer", "PROGRAM_CALENDAR_ID_NOT_MATCH": "Program Calendar Id not match", "UNABLE_TO_YOU_REVIEW": "Unable to you review", "UNABLE_TO_SEND_NOTIFICATION": "Unable to Send Notification", "INSTITUTION_HAS_ASSIGNED_YOU_AS_REVIEWER_FOR_PROGRAM_CALENDAR_SO_PLEASE_SHARE_YOU_REVIEW": "Institution has assigned you as Reviewer for Program calendar, so please share you review.", "REVIEW_REQUEST_SEND": "Review request send", "YOUR_REVIEW_SENT_TO_VICE_DEAN_SUCCESSFULLY": "Your review sent to Vice-<PERSON> successfully", "PROGRAM_CALENDAR_PUBLISHED_SUCCESSFULLY": "Program Calendar Published Successfully", "DEAN_NOT_FOUND": "<PERSON> not found", "PROGRAM_CALENDAR_SENT_TO_DEAN_SUCCESSFULLY": "Program Calendar sent to <PERSON> successfully", "PROGRAM_CALENDAR_REVIEWER_DETAILS": "Program calendar Reviewer details", "ERROR_REVIEWER_NOT_FOUND": "Error Reviewer not found", "ERROR_REVIEW_NOT_FOUND": "Error Review not found", "PROGRAM_CALENDAR_REVIEW": "Program calendar Review", "ERROR_REVIEWERS_NOT_FOUND": "Error Reviewers not found", "REVIEWER_REQUIRED": "reviewer required", "REVIEWER_TYPE_MUST_BE": "reviewer_type must be dean,reviewer,creator", "WHOM_MUST_BE": "whom must be dean,reviewer,creator", "TO_MUST_BE": "to must be dean,reviewer,creator,publish", "REVIEW_REQUIRED": "review required", "REVIEW_COMMENT_REQUIRED": "review_comment required", "MESSAGE_REQUIRED": "message required", "NOTIFY_MUST_BE": "notify_via must be sms,email,digiclass-admin,digiclass", "ROLE_SINGLE_GET": "Role Single Get", "DUPLICATE_ROLE": "Duplicate Role", "ROLE_CREATED": "Role Created", "UNABLE_TO_CREATE_ROLE": "Unable to Create Role", "ROLE_UPDATED": "Role Updated", "UNABLE_TO_UPDATE_ROLE": "Unable to Update Role", "ROLE_IS_ASSIGNED_TO_USER_UNABLE_TO_REMOVE": "Role is Assigned to User unable to remove", "ROLE_REMOVED": "Role Removed", "UNABLE_TO_ROLE_REMOVED": "Unable to Role Removed", "STAFFTYPE_REQUIRED": "staff_type required", "POSITIONTYPE_REQUIRED": "position_type required", "POSITIONFTYPE_REQUIRED": "position_type required", "ROLE_REQUIRED": "role required", "INSTITUTION_ID_REQUIRED": "_institution_id required", "STAFF_ASSIGNED_TO_ROLE_SUCCESSFULLY": "Staff assigned to role successfully", "OFFICEID_REQUIRED": "_office_id required", "ROLEID_REQUIRED": "_role_id required", "STAFFID_REQUIRED": "_staff_id required", "OFFICENAME_REQUIRED": "office_name required", "ROLENAME_REQUIRED": "role_name required", "STAFFNAME_REQUIRED": "staff_name required", "ACTIVESTAFFSTATUS_REQUIRED": "acting_staff_status boolean required", "ACTIVESTAFFID_REQUIRED": "_acting_staff_id required", "DEPTID_REQUIRED": "_dept_id required", "COURSEID_REQUIRED": "_course_id required", "INFRAID_REQUIRED": "_infra_id required", "PROGRAMNAME_REQUIRED": "program_name required", "DEPTNAME_REQUIRED": "dept_name required", "SUBJECTNAME_REQUIRED": "subject_name required", "COURSENAME_REQUIRED": "course_name required", "INFRANAME_REQUIRED": "infra_name required", "ROLE_MANAGEMENT_LIST": "role_management list", "NO_DOCUMENTS_FOUND": "No documents found", "DOCUMENT_LIST": "Document List", "DOCUMENT_EDIT_SUCCESSFULLY": "Document Edited successfully", "DOCUMENT_DELETED_SUCCESSFULLY": "Document Deleted Successfully", "UNABLE_TO_DELETE_DOCUMENT": "Unable to Delete Document", "UNABLE_TO_EDIT_DOCUMENT": "Unable to Edit Document", "UNABLE_TO_ADD_DOCUMENT": "Unable to Add Document", "DOCUMENT_ADDED_SUCCESSFULLY": "Document Added Successfully", "ISDEPARTMENTASSIGNED_KEY_REQUIRED": "isDepartmentAssigned key is required", "OLD_PROGRAM_ID_REQUIRED": "Old program Id required", "COURSE_ACTIVITY_SUMMARY": "Course Activity Summary", "SESSION_NOT_FOUND": "Session not found", "COURSE_LIST": "Course List", "STUDENT_LIST": "Student List", "STUDENT_NOT_FOUND": "Student Not found", "DEPARTMENT_SUBJECT_COURSE_LIST": "Department Subject Course List", "PROGRAM_WISE_REPORT": "Program Wise Report", "COURSE_SCHEDULE_CACHE_UPDATED": "Course Schedule Cache Updated", "CACHE_UPDATED": "<PERSON><PERSON>", "CACHE_CLEARED_AND_UPDATED": "Cache Cleared & Updated", "STAFF_DETAILS_AND_COURSE_LIST": "Staff Details & Course List", "LEVEL_AND_TERM_NOT_FOUND": "Level and term not found", "COURSE_SCHEDULE_NOT_FOUND": "Course schedule not found", "SESSION_STATUS": "Session Status", "USER_DATA_NOT_FOUND": "User data not found", "ATTENDANCE_LOG": "Attendance Log", "TERM_LEVEL_NOT_FOUND": "Term level not found", "STUDENT_GROUP_COURSE_NOT_FOUND": "Student group course not found", "COURSE_SETTINGS_NOT_FOUND": "Course Settings not found", "STUDENT_DETAILS": "Student Details", "GROUP_NOT_FOUND": "Group not found", "STUDENT_DETAILS_ATTENDANCE": "Student Details Attendance", "COURSE_OVERVIEW": "Course Overview", "COURSE_ATTENDANCE_REPORT": "Course Attendance Report", "NOT_AVAILABLE": "Not Available", "PROGRAM_AND_LEVEL_NOT_FOUND": "Program and level not found", "MANAGE_TOPIC_LIST": "Manage Topics list", "SCHEDULE_ALREADY_COMPLETED": "Schedule Already completed", "MERGE_SESSION_UPDATED_SUCCESSFULLY": "Merge Session updated successfully", "COURSE_STUDENT_GROUP_NOT_FOUND": "Course/Student group Not found", "REMOTE_EXTRA_BREAK_TIMING_NOT_FOUND": "Remote/Extra/Break Timing Not found", "COURSE_FLOW_NOT_FOUND": "Course flow Not found", "INFRASTRUCTURE_NOT_FOUND": "Infrastructure Not found", "DELIVERY_TYPE_LIST": "Delivery type list", "LECTURE_SETTING_ADDED_SUCCESSFULLY": "Lecture setting added successfully", "UNABLE_TO_ADD_LECTURE_SETTING": "Unable to add lecture setting", "UNABLE_TO_FIND_GROUP": "Unable to find group", "UNABLE_TO_ADD_COURSE_DELIVERY_SETTINGS": "Unable to add Course delivery settings", "UNABLE_TO_UPDATE_COURSE_DELIVERY_SETTINGS": "Unable to update Course delivery settings", "COURSE_DELIVERY_SETTINGS_ADDED_SUCCESSFULLY": "Course delivery settings added successfully", "COURSE_DELIVERY_SETTINGS_UPDATED_SUCCESSFULLY": "Course delivery settings updated successfully", "TOPICS_ADDED_SUCCESSFULLY": "Topics added successfully", "UNABLE_TO_ADD_TOPIC": "Unable to add topic", "TOPICS_UPDATED_SUCCESSFULLY": "Topics updated successfully", "TOPICS_NOT_FOUND": "topics not found", "UNABLE_TO_DELETE_TOPIC": "Unable to delete topic", "TOPICS_DELETED_SUCCESSFULLY": "Topics deleted successfully", "COURSE_SESSION_FLOW_NOT_FOUND": "Course Session flow Not found", "SESSION_ORDER_NOT_FOUND": "Session order Not found", "AVAILABLE_LIST": "Available List", "UNABLE_TO_SCHEDULE_RETRY": "Unable to Schedule retry", "SCHEDULED_SUCCESSFULLY": "Scheduled Successfully", "SETTING_TYPE_NOT_VALID": "Setting type not valid", "UNABLE_TO_UPDATE": "Unable to update", "LEVEL_NOT_FOUND_IN_STUDENT_GROUP": "Level Not Found in Student group", "USER_RECORD_NOT_FOUND": "User Record not found", "USER_ROLE_NOT_FOUND": "User Role not found", "NO_LEAVE_SETTING_FOUND": "No Leave Setting Found", "NO_LEAVES_FOUND": "No Leaves Found", "LEAVE_LIST": "Leave list", "STAFF_LEAVE_LIST": "Staff Leave List", "ERROR_CATCH": "Error_Catch", "ROLE_ASSIGN_RECORD_NOT_FOUND": "Role assign record not found", "NO_COURSE_SCHEDULED": "No course scheduled", "PROGRAM_LIST_RETRIEVED": "Program List retrieved", "STUDENT_GROUP_NOT_CREATED": "Student group not created", "PROGRAM_WISE_DATA": "Program wise data", "INTERNAL_SERVER_ISSUE": "Internal server issue", "PROGRAM_CALENDAR_ADDED_SUCCESSFULLY": "Program calendar added successfully", "PROGRAM_CALENDAR_UPDATED_SUCCESSFULLY": "Program calendar updated successfully", "PROGRAM_CALENDAR_DELETED_SUCCESSFULLY": "Program calendar deleted successfully", "PROGRAM_CALENDAR_LIST": "Program calendar list", "PROGRAM_CALENDAR_DASHBOARD": "Program calendar dashboard", "PROGRAM_CALENDAR_SETTING_CREATED_SUCCESSFULLY": "Program calendar setting created successfully", "START_DATE_AND_END_DATE_ASSIGNED_SUCCESSFULLY_TO_PARTICULAR_LEVEL": "Start date and End date assigned successfully to particular level", "UNABLE_TO_SET_ROTATION_COUNT": "Unable to Set Rotation count", "ROTATION_COUNT_IS_ASSIGNED": "Rotation count is assigned", "PROGRAM_CALENDAR_SETTING": "Program calendar setting", "PROGRAM_CALENDAR_SETTING_YEAR_DATA": "Program Calendar Setting year data", "PROGRAM_CALENDAR_LANDING": "Program Calendar Landing", "DATA_CHECKS": "Data checks", "UNABLE_TO_SET_PROGRAM_CALENDAR_SETTING": "Unable to set program calendar setting", "GET_OTP": "Get Otp", "STUDENT_ADDED_SUCCESSFULLY": "Student added successfully", "STUDENT_UPDATED_SUCCESSFULLY": "Student updated successfully", "STUDENT_DELETED_SUCCESSFULLY": "Student deleted successfully", "STUDENTS_ARE_NOT_REGISTERED": "STUDENTS_ARE_NOT_REGISTERED", "UNABLE_TO_FIND_LEVEL_OR_BATCH": "UNABLE_TO_FIND_LEVEL_OR_BATCH", "UNABLE_TO_ADD_STUDENT": "unable to add student", "SUCCESSFULLY_STUDENT_ADDED": "successfully student added", "DATA_CHECK_VALIDATION_SUCCESSFUL": "data check validation successful", "DATA_CHECK_ISSUE": "data check issue", "NO_GROUP_FOUND_CHECK_THE_IDS": "no group found check the ids", "CHECK_CURRICULUM_VERSION": "check curriculum version", "CHECK_CURRICULUM_VERSION_ITS_NOT_PRESENT_IN_SYSTEM": "check curriculum version its not present in system", "THERE_IS_NO_PREVIOUS_ACADEMIC_CALENDAR": "there is no previous academic calendar", "IMPORTER_NOT_FOUND": "importer not found", "STUDENT_DATA_ARE_NOT_FOUND": "student data are not found", "DATA_NOT_FOUND": "data not found", "LEVEL_2_IS_NOT_FOUND": "level 2 is not found", "STUDENT_DATA_UPDATED_SUCCESSFULLY": "student data updated successfully", "STUDENT_DATA_ARE_NOT_UPDATED": "student data are not updated", "CHECK_LEVEL_NO_ITS_WRONG": "check level no its wrong", "PLEASE_CHECK_LEVEL_OR_TERM_OR_GENDER_OR_GROUP_NO": "please check level or term or gender or group no", "GROUP_SETTING": "group setting", "UNABLE_TO_FIND_STUDENT_GROUP": "unable to find student group", "UNABLE_TO_SET_SETTING_PLS_RETRY": "unable to set setting pls retry", "STUDENTS_GROUP_SETTING_IS_CREATED": "students group setting is created", "UNABLE_SET_EXCESS_COUNT": "unable set excess count", "EXCESS_COUNT_SUCCESSFULLY_ASSIGNED": "excess count successfully assigned", "STUDENT_GROUP_NOT_FOUND_ON_GENDER": "student group not found on gender", "STUDENT_GROUP_NOT_FOUND_ON_NEW_GROUP": "student group not found on new group", "STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED": "student capacity exceeded increase extra allowed", "UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY": "unable to push student into group pls retry", "STUDENTS_ARE_GROUPED_BASED_ON_METHOD": "students are grouped based on method", "STUDENT_ACADEMIC_NO_NOT_FOUND": "student academic no not found", "STUDENT_GROUP_ID_NOT_MATCH": "student group id not match", "THIS_STUDENT_ALREADY_IN_THIS_LEVEL": "this student already in this level", "STUDENT_ACADEMIC_NO_NOT_MATCH": "student academic no not match", "SUCCESSFULLY_ADDED_STUDENT_MANUALLY": "successfully added student manually", "UNABLE_TO_REMOVE_STUDENT": "unable to remove student", "SUCCESSFULLY_REMOVED_STUDENT_MANUALLY": "successfully removed student manually", "BULK_DELETING_STUDENTS_FROM_GROUP": "bulk deleting students from group", "DELETED_STUDENTS_FROM_ALL_GROUPS": "deleted students from all groups", "NO_GROUPS_CREATED_IN_LEVEL_1": "no groups created in level 1", "NO_MALE_GROUPS_CREATED_IN_LEVEL_1": "no male groups created in level 1", "NO_FEMALE_GROUPS_CREATED_IN_LEVEL_1": "no female groups created in level 1", "GOT_LEVEL_1_STUDENT_GROUP_COUNT": "got level 1 student group count", "UNABLE_TO_FIND_COURSE": "unable to find course", "STUDENT_EXIST_IN_UNGROUPED": "student exist in ungrouped", "STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE": "student is already exist in this course", "YOU_CAN_ADD_STUDENT_TO_THIS_COURSE": "you can add student to this course", "FOUNDATION_STUDENT_GROUP_DATA": "foundation student group data", "INVALID_LEVEL_AND_TERM": "invalid level and term", "INVALID_COURSE_ID": "invalid course id", "DATA_CHECK_FAILED": "data check failed", "ID NOT MATCH": "id not match", "CHECK PARSING REFERENCE ID": "check parsing reference id", "STUDENT_ADDED_TO_COURSE_SUCCESSFULLY": "student added to course successfully", "COURSE_IS_NOT_FOUND": "course is not found", "ERROR_UNABLE_TO_SET_COURSE_GROUP_SETTING": "error unable to set course group setting", "STUDENTS_COURSE_GROUP_SETTING_IS_CREATED": "students course group setting is created", "STUDENT_GROUP_COURSE_DATA": "student group course data", "ERROR_UNABLE_TO_REMOVE_STUDENT_FROM_COURSE_GROUP": "error unable to remove student from course group", "SUCCESSFULLY EDITED STUDENT IN FOUNDATION COURSE GROUP": "successfully edited student in foundation course group", "ERROR STUDENT ACADEMIC NO NOT MATCH": "error student academic no not match", "COURSE_NOT_FOUND DO VERIFY COURSE SELECTION": "course not found do verify course selection", "UNABLE_TO_FIND_COURSE DO VERIFY COURSE SELECTION": "unable to find course do verify course selection", "ERROR_UNABLE_TO_ADD_STUDENT": "error unable to add student", "FOUNDATION_COURSE_DELIVERY_GROUP_STUDENT_DATA": "foundation course delivery group student data", "NO_SESSION_SETTINGS_ADDED": "no session settings added", "NO_SESSION_GROUPS_CREATED": "no session groups created", "SESSION_TYPE_NOT_MATCHING": "session type not matching", "GROUP_NO_NOT_FOUND": "group no not found", "ERROR_UNABLE_TO_EDIT_STUDENT": "error unable to edit student", "SUCCESSFULLY_EDITED_STUDENT_IN_GROUP": "successfully edited student in group", "ERROR_UNABLE_TO_DELETE_REMOVE_STUDENT_FROM_COURSE_GROUP": "error unable to delete remove student from course group", "SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP": "successfully removed student from course group", "ERROR_ID_NOT_FOUND": "error id not found", "ERROR_NO_MATCHING_COURSES": "error no matching courses", "GETTING_COURSES_WITH_MATCHING_DELIVERY_TYPES": "getting courses with matching delivery types", "UNABLE_TO_FIND_STUDENT_GROUP_PROGRAM": "unable to find student group program", "NO_MALE_GROUPS_CREATED": "no male groups created", "NO_FEMALE_GROUPS_CREATED": "no female groups created", "THERE_IS_NO_DELIVERY_TYPES": "there is no delivery types", "THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW": "there is no session in this course pls add session flow", "NO_GROUPS_CREATED": "no groups created", "FIRST_CREATE_GROUPS": "first create groups", "SAVED_GROUPS": "saved groups", "ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH": "error student academic no not match", "SUCCESSFULLY_STUDENTS_ADDED": "successfully students added", "UNABLE_TO_SET_COURSE_GROUP_SETTING": "unable to set course group setting", "UNABLE_TO_COURSE": "unable to course", "COURSE_STUDENT_GROUP_DATA": "course student group data", "STUDENT_GROUP_SETTING_PAGE": "student group setting page", "ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY": "error unable to push student into group pls retry", "COURSE_STUDENT_LIST": "course student list", "ERROR_UNABLE_TO_GET_LIST": "error unable to get list", "UNABLE_TO_GET_LIST": "unable to get list", "UNABLE_TO_FIND_INSTITUTION_CALENDAR": "unable to find institution calendar", "VICE_DEAN_COURSE_COORDINATOR_NOT_FOUND": "vice dean course coordinator not found", "COURSE_GROUP_SUCCESSFULLY_PUBLISHED": "course group successfully published", "GROUP_SUCCESSFULLY_PUBLISHED": "group successfully published", "ERROR_UNABLE_TO_SET_SETTING_PLS_RETRY": "error unable to set setting pls retry", "PLS_CHECK_GROUP_NAME_OR_GENDER_OR_TERM": "pls check group name or gender or term", "ERROR_UNABLE_TO_REMOVE_STUDENT": "error unable to remove student", "STUDENT_IS_ALREADY_EXIST_IN_THIS_GROUP": "student is already exist in this group", "YOU_CAN_ADD_STUDENT_TO_THIS_GROUP": "you can add student to this group", "ERROR_UPDATING": "error updating", "GET_ADVANCED_SETTINGS": "Get advanced settings", "UNABLE_TO_REMOVE_SCHEDULE_RETRY": "Unable to Remove Schedule retry", "SCHEDULED_REMOVED_SUCCESSFULLY": "Schedule Removed Successfully", "COURSE_DETAILS_NOT_FOUND": "Course Details not found", "COURSE_SCHEDULE_LIST": "Course schedule List", "MANAGE_COURSE_LIST": "Manage Course list", "UNABLE_TO_CANCEL_SESSION": "Unable to Cancel session", "CANCELLED_SESSION_SUCCESSFULLY": "Cancelled session successfully", "UNABLE_TO_REASSIGN_SESSION": "Unable to Reassign session", "REASSIGNED_SESSION_SUCCESSFULLY": "Reassigned session successfully", "DAYTIME_TIME_SUCCESSFULLY": "Day Time Added successfully", "DAYTIME_TIME_UPDATED_SUCCESSFULLY": "Day Time updated successfully", "DAYTIME_TIME_DELETED_SUCCESSFULLY": "Day Time Deleted successfully", "SETTING_NOT_FOUND": "Setting not found", "COURSE_NOT_FOUND_IN_LEVEL_TERM": "Course not found in level term", "AUTO_ASSIGN_DELIVERY_WISE_LISTING": "Auto Assign delivery wise Listing", "UNABLE_TO_DELIVERY_SETTINGS": "Unable to Delivery <PERSON>", "UNABLE_TO_SCHEDULE_SESSION_ALREADY_SCHEDULED_OR_TIMING_INFRA_STAFF_GROUP_NOT_AVAILABLE": "Unable to Schedule Session already scheduled or Timing/Infra/Staff/Group not available", "AUTO_SCHEDULED_SUCCESSFULLY": "Auto Scheduled Successfully", "YOU_CAN_DELETE_TOPIC": "You can delete topic", "SCHEDULE_NOT_FOUND": "Schedule Not found", "NO_RECORDS_TO_UPDATE": "No Records to update", "DETACH_SESSION_FAILED": "Detach Session failed", "DETACH_SESSION_SUCCESSFULLY": "Detach Session successfully", "UNABLE_TO_ADD_SUPPORT_SESSION": "Unable to add support session", "SUPPORT_SESSION_ADDED_SUCCESSFULLY": "Support session added successfully", "UNABLE_TO_UPDATE_SUPPORT_SESSION": "Unable to update support session", "SUPPORT_SESSION_UPDATED_SUCCESSFULLY": "Support session updated successfully", "UNABLE_TO_UPDATE_EVENT": "Unable to update Event", "EVENT_UPDATED_SUCCESSFULLY": "Event updated successfully", "STUDENT_GROUP_DATA": "Student Groups Data", "EMAIL_SENT_SUCCESSFULLY": "<PERSON>ail sent successfully", "COURSE_TIME_TABLE_IS_PUBLISHED": "Course TimeTable is Published", "LEVEL_NO_REQUIRED": "Level no required", "TOPIC_REQUIRED": "Level no required", "CATEGORY_LIST_BY_ID": "Category List By Id", "LEAVE_MANAGEMENT_LIST": "Leave Management List", "LEAVE_MANAGEMENT_CATEGORY": "Leave Management Category", "CATEGORY_DELETED_SUCCESSFULLY": "Category deleted Successfully", "LEAVE_TYPE_DELETED_SUCCESSFULLY": "Leave Type deleted Successfully", "CATEGORY_NOT_FOUND": "Category Not found", "LEAVE_TYPE_LIST": "Leave Type List", "DUPLICATE_FOUND_NOT_ABLE_TO_INSERT": "Duplicate found! Not able to insert", "DUPLICATE_LEAVE_TYPE": "Duplicate Leave type", "DUPLICATE_FOUND": "Duplicate Leave type", "LEAVE_TYPE_ADDED_SUCCESSFULLY": "Leave Type Added Successfully", "LEAVE_SETTINGS_NOT_FOUND": "Leave Settings Not found", "CATEGORY_LEVEL_TYPE_NOT_FOUND": "Category/Level Type Not found", "PERMISSION_UPDATED": "Permission Updated", "PERMISSION_LIST": "Permission List", "HR_CONTACT_UPDATED": "HR Contact Updated", "HR_CONTACT": "HR Contact", "DUPLICATE_WARNING": "Duplicate warning", "STUDENT_WARNING_ANDABSENCE_CALCULATION_LIST": "Student Warning and Absence Calculation list", "STAFF_LMS_ROLES_ARE_ASSIGNED": "Staff LMS Roles are assigned", "ERROR_UNABLE_TO_SET_STAFF_LMS_ROLES": "Error Unable to set Staff LMS Roles", "UNABLE_TO_FIND_SETTINGS": "Unable to Find LMS Settings", "STAFF_TYPE_HAS_TO_BE_UNIQUE_IN_APPROVERS": "Staff Type has to be Unique in Approvers", "DUPLICATE_ROLE_IN_STAFF_TYPE": "Duplicate Role in Staff type", "DUPLICATE_STAFF_IN_STAFF_TYPE": "Duplicate Staff in Staff type", "ERROR_UNABLE_TO_ADD_STAFF_APPROVER": "Error Unable to add Staff Approver", "STAFF_APPROVER_ADDED": "Staff Approver Added", "UNABLE_TO_FIND_APPROVER": "Unable to Find A<PERSON>rover", "USERS_LEAVE_ROLE_NOT_FOUND": "Users Leave Role not found", "STAFF_APPROVER_EDITED": "Staff Approver Edited", "ERROR_UNABLE_TO_EDIT_STAFF_APPROVED": "Error Unable to Edit Staff Approver", "UNABLE_TO_FIND_LMS_SETTINGS": "Unable to Find LMS Settings", "STAFF_APPROVER_REMOVED": "Staff Approver Removed", "ERROR_UNABLE_TO_REMOVE_STAFF_APPROVER": "Error Unable to Removed Staff Approver", "REPORT_ABSENCE_UPDATED": "Report Absence Updated", "ERROR_UNABLE_TO_UPDATE_STAFF_REPORT_ABSENCE": "Error Unable to update Staff Report Absence", "CATCH_ERROR": "catch error", "SUCCESSFULLY_EDITED_STUDENT_IN_ROTATION_COURSE_GROUP": "successfully edited student in rotation course group", "NO_GROUPS_FOUND": "NO_GROUPS_FOUND", "STUDENT_GROUP_SETTING_NOT_FOUND": "STUDENT_GROUP_SETTING_NOT_FOUND", "SUB_TYPE_REQUIRED": "sub_type required", "SCHEDULE_DATE_REQUIRED": "schedule_date required", "SCHEDULE_DATE_TIME_REQUIRED": "scheduleStartDateAndTime and scheduleEndDateAndTime required", "MODE_REQUIRED": "mode required", "INSTITUTION_CALENDER_ID_REQUIRED": "_institution_calendar_id required", "PROGRAM_NAME_REQUIRED": "program_name required", "COURSE_ID_REQUIRED": "_course_id required", "SESSION_TYPE_ID_REQUIRED": "_session_type_id required", "DELIVERY_ID_REQUIRED": "_delivery_id required", "YEAR_REQUIRED": "year required", "COURSE_NAME_REQUIRED": "course_name required", "YEAR_NO_REQUIRED": "course_name required", "TO_REQUIRED": "to required", "CATEGORY_TYPE_REQUIRED": "category_type required", "PERMISSION_HOURS_REQUIRED": "permission_hours required", "PERMISSION_FREQUENCY_REQUIRED": "permission_frequency required", "PERMISSION_FREQUENCY_BY_REQUIRED": "permission_frequency_by required", "IS_SCHEDULED_REQUIRED": "is_scheduled required", "IS_PERMISSION_ATTACHMENT_REQUIRED": "is_permission_attachment_required required", "CATEGORY_NAME_REQUIRED": "category_name required", "TYPE_NAME_REQUIRED": "type_name required", "GENDER_REQUIRED": "gender required", "PAYMENT_REQUIRED": "payment required", "ENTITLEMENT_REQUIRED": "entitlement required", "NO_DAYS_REQUIRED": "no_of_days required", "WEEKEND_CONSIDERATION_REQUIRED": "weekend_consideration required", "IS_REASON_REQUIRED": "is_reason_required required", "IS_ATTACHMENT_REQUIRED": "is_attachment_required required", "LEAVE_TYPE_ID_REQUIRED": "leave_type_id required", "TYPE_ID_REQUIRED": "typeid required", "APPLIED_LEAVE_LIST": "Applied Leave List", "REPORT_ABSENCE_LIST": "Report Absence List", "SCHEDULE_LIST_ALONG_WITH_SUBSTITUTE": "Schedule List along with Substitute", "USER_LEAVE_LIST": "User Leave List", "PRIMARY_PROGRAM_NOT_FOUND": "Primary program not found", "STAFF_EMPLOYMENT_TYPE_NOT_FOUND": "Staff Employment type not found", "DUPLICATE_ON_REPORT_ABSENCE_PERMISSION": "Duplicate on Report Absence/Permission/Leave/On_Duty", "LEAVE_APPLIED_SUCCESSFULLY": "Leave Applied Successfully", "ERROR_UNABLE_TO_FIND_LEAVE_SETTING": "Error Unable to find Leave Setting", "ERROR_UNABLE_TO_FIND_PERMISSION_SETTING": "Error Unable to find Permission Setting", "YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY": "You have crossed your Permission Frequency", "ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER": "Error Unable to find Institution calendar", "DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER": "Dates are away from Academic calendar", "YOU_HAVE_CROSSED_YOUR": "You have crossed your ", "MONTH_FREQUENCY_YEAR_FREQUENCY": " Month Frequency/Year Frequency", "LMS_REVIEW_DATA_NOT_FOUND": "LMS Review Data not found", "APPROVER_NOT_FOUND_CHECK_LOGGED_IN_ROLE": "Approver Not Found check logged in Role", "STUDENT_REPORT_ABSENCE_DETAILS": "Student report absence details", "USER_IS_NOT_TAKEN_ANY_LEAVE": "User is not taken any leave", "USER_LEAVE_OVERVIEW": "User Leave Overview", "PERMISSION_SETTINGS_NOT_FOUND": "Permission Settings not found", "PERMISSION_OVERVIEW": "Permission Overview", "YEAR_LEVEL_NOT_FOUND": "Year Level data not found", "STUDENT_RECORDS_NOT_FOUND": "Student records not found", "STUDENT_REGISTER": "Student Register", "STUDENT_ATTENDANCE_REGISTER": "Student Attendance Register", "UNABLE_TO_UPDATE_ABSENCE_PERCENTAGE": "Unable to Update Absence Percentage", "ABSENCE_PERCENTAGE_UPDATED_SUCCESSFULLY": "Absence Percentage Updated Successfully", "LEAVE_REASON_DOC_REQUIRED": "_leave_reason_doc required", "USER_TYPE_REQUIRED": "user_type required", "STAFF_ID_REQUIRED": "staff_id required", "IS_NOTICED_REQUIRED": "is_noticed required", "REASON_REQUIRED": "reason required", "CREATED_BY_REQUIRED": "created_by required", "APPLICATION_DATE_REQUIRED": "application_date required", "APPROVED_COMMENTS_REQUIRED": "approved_comments required", "STATUS_REQUIRED": "status required", "UPDATED_BY_REQUIRED": "updated_by required", "COMMENT_REQUIRED": "comment required", "PERSON_ID_REQUIRED": "_person_id required", "STUDENT_GROUP_CREATIONS": "student group creations", "THERE_IS_NO_STUDENT_GROUP_IN_CURRENT_ACADEMIC_YEAR": "there is no student group in current academic year", "STUDENT_GROUP_DASHBOARD": "student group dashboard", "STUDENT_GROUP_DASHBOARD_CRASHED": "student group dashboard crashed", "STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST": "student group program institution calendar list", "STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST_CRASHED": "student group program institution calendar list crashed", "ERROR_PROGRAM/INSTITUTION_CALENDAR_ID_NOT_MATCH": "error program/institution calendar id not match", "STUDENT ACADEMIC NO NOT FOUND": "student academic no not found", "STUDENTS_GLOBAL_EDIT_SUCCESS": "students global edit success", "STUDENT_SEARCH": "student search", "UNABLE_TO_REMOVED_STUDENTS_GROUP": "unable to removed students group", "STUDENTS_GROUP_REMOVED_SUCCESS": "students group removed success", "HARD_DELETE_ISSUE": "hard delete issue", "THERE_IS_NO_PROGRAM_CALENDAR_STILL_PUBLISHED_IN_CURRENT_ACADEMIC_YEAR": "there is no program calendar still published in current academic year", "STUDENT_CAPACITY_EXCEEDED_IN_FOUNDATION_GROUP_INCREASE_EXTRA_ALLOWED": "student capacity exceeded in foundation group increase extra allowed", "TAXONOMY_NAME_ALREADY_EXISTS": "taxonomy name already exists", "DOCUMENT_NOT_FOUND": "Document Not found", "DATA_UPDATION_MESSAGE": "Data updation message", "STATUS_MESSAGE": "Status message", "USER_ID_REQUIRED": "userId required", "SESSION_ID_REQUIRED": "sessionId required", "SESSIONS_REQUIRED": "sessions required", "STUDENT_ID_REQUIRED": "studentId required", "UNABLE_TO_ADD_SURVEY": "unable to add survey", "COURSE_SESSION_WISE_SLO_SURVEY": "course session wise slo survey", "UNABLE_TO_UPDATE_SURVEY": "unable to update survey", "COURSE_MULTI_SESSION_WISE_SLO_SURVEY_UPDATED": "course multi session wise slo survey updated", "COURSE_SESSION_WISE_SLO_SURVEY_STORING": "course session wise slo survey storing", "COURSE_SELF_EVALUATION_SURVEY": "course self evaluation survey", "ALREADY_YOU_HAVE_RUNNING_SESSION": "already you have running session", "ALREADY_YOU_RETAKE_FOR_ABSENT_COMPLETED": "already retake for absent completed for this schedule", "ATTENDANCE_ALREADY_CLOSED": "attendance already closed", "ATTENDANCE_ALREADY_STARTED": "attendance already started", "UNABLE_TO_START_SESSION": "unable to start session", "SESSION_ATTENDANCE_STARTED": "session attendance started", "ATTENDANCE_PROCESS_ALREADY_COMPLETED": "attendance process already completed", "SECRET_CODE_(UUID)_IS_NOT_MATCHING": "secret code (uuid) is not matching", "STUDENT_ALREADY_PRESENT": "student already present", "UNABLE_TO_PERSISTED_ATTENDANCE": "unable to persisted attendance", "SESSION_ATTENDANCE_PERSISTED_SUCCESSFULLY": "session attendance persisted successfully", "SESSION_ATTENDANCE_ALREADY_CLOSED": "session attendance already closed", "SESSION_ATTENDANCE_STOPPED": "session attendance stopped", "UNABLE_TO_STOP_ATTENDANCE": "unable to stop attendance", "ATTENDANCE_NOT_CLOSED": "attendance not closed", "ATTENDANCE_IS_ONGOING_YOU_CANT_STOP": "attendance is ongoing you cant stop", "SESSION_RESET_SUCCESSFULLY": "session reset successfully", "UNABLE_TO_RESET_SESSION": "unable to reset session", "SESSION_GET": "session get", "ATTENDANCE_PROCESS_IS_ONGOING": "attendance process is ongoing", "STUDENTS_NOT_FOUND": "students not found", "UNABLE_TO_PERSISTED_COMMENT": "unable to persisted comment", "SESSION_COMMENT_PERSISTED_SUCCESSFULLY": "session comment persisted successfully", "SESSION_REPORT": "session report", "SESSION_REPORT_PENDING": "session report pending", "SESSION_NOT_YET_STARTED": "session not yet started", "UNABLE_TO_CHANGE_ATTENDANCE": "unable to change attendance", "ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY": "attendance manually changed successfully", "SESSION_NOT_YET_START": "session not yet start", "SESSION_COMPLETED_SUCCESSFULLY": "session completed successfully", "UNABLE_TO_END_SESSION": "unable to end session", "THERE_IS_NO_SESSION_FOUND": "there is no session found", "SESSION_NOT_STARTED": "session not started", "SESSION_ENDED_UNABLE_TO_RETAKE_ATTENDANCE": "session ended unable to retake attendance", "ERROR_ON_BULK_WRITE": "error on bulk write", "RETAKE_ATTENDANCE_STARTED_SUCCESSFULLY": "retake attendance started successfully", "_ID_REQUIRED": "_id required", "EVENT_CALENDAR_REQUIRED": "event calendar required", "ROTATION_COUNT_REQUIRED": "rotation count required", "_COURSE_ID_REQUIRED": "_course id required", "EVENT_TYPE_REQUIRED": "event type required", "FIRST_LANGUAGE_REQUIRED": "first language required", "_EVENT_ID_REQUIRED": "_event id required", "_INSTITUTION_CALENDAR_ID_REQUIRED": "", "LEVEL_REQUIRED": "level required", "_PROGRAM_ID_REQUIRED": "program id required", "PROGRAM_CALENDAR EVENT ADDED SUCCESSFULLY": "program calendar event added successfully", "UNABLE_TO_ADD_EVENTS_IN_CALENDAR_CHECK_EVENT_DATE": "unable to add events in calendar check event date", "PROGRAM_CALENDAR_EVENT_UPDATE_SUCCESSFULLY": "program calendar event update successfully", "EVENT_REMOVED_SUCCESSFULLY_IN_PROGRAM_CALENDAR": "event removed successfully in program calendar", "PROGRAM_CALENDAR_EVENT_LIST": "program calendar event list", "UNABLE_TO_SYNC_EVENT_ID &gt;": "unable to sync event id &gt;", "PLEASE_CHECK_EVENT_DATE/ALREADY_EVENT_PRESENT_IN_THAT_YEAR": "please check event date/already event present in that year", "UNABLE_TO_COPY_EVENT_THESE_LEVEL_ &gt;": "unable to copy event these level &gt;", "ERROR_YEAR_LEVEL_NOT_MATCHING": "error year level not matching", "CHECK_YEAR_LEVEL_NOT_MATCHING": "check year level not matching", "PROGRAM_CALENDAR_EVENT_DETAILS": "program calendar event details", "PROGRAM_CALENDAR_COURSE_EVENT_DETAILS": "program calendar course event details", "UNABLE_TO_FIND_PROGRAM_CALENDAR_COURSE_EVENTS": "unable to find program calendar course events", "PROGRAM_CALENDAR_COURSE_EVENT_ADDED_SUCCESSFULLY": "program calendar course event added successfully", "ERROR_UNABLE_TO_UPDATE_EVENT": "error unable to update event", "PROGRAM_CALENDAR_EVENT UPDATE SUCCESSFULLY": "program calendar event update successfully", "EVENT_DATE_NOT_MATCH": "event date not match", "CHECK_EVENT_DATES": "check event dates", "UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME": "unable to create event retry after some time", "PROGRAM_CALENDAR_EVENT_SYNCED_SUCCESSFULLY": "program calendar event synced successfully", "Unable to Sync Event ID-&gt;": "unable to sync event id-&gt;", "_BATCH_COURSE_ID_REQUIRED": "batch course id required", "BY_REQUIRED": "by required", "START_WEEK_REQUIRED": "start week required", "END_WEEK_REQUIRED": "end week required", "COLOR_CODE_REQUIRED": "color code required", "COURSE_REQUIRED": "course required", "ROTATION_NO_REQUIRED": "rotation no required", "DIRECTION_REQUIRED": "direction required", "PROGRAM_CALENDAR_COURSE_DETAILS": "program calendar course details", "PROGRAM_CALENDAR_ROTATION_COURSE_DETAILS": "program calendar rotation course details", "ERROR_UNABLE_TO_FIND_COURSE:": "error unable to find course:", "PROGRAM_CALENDAR_COURSE_LIST": "program calendar course list", "THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_LEVEL": "this course is already present in same level", "ERROR_UNABLE_TO_ADD_COURSE_SO_RETRY": "error unable to add course so retry", "ERROR_UNABLE_TO_DELETE_COURSE_RETRY": "error unable to delete course retry", "UNABLE_TO_DELETE_COURSE_RETRY": "unable to delete course retry", "PROGRAM_CALENDAR_COURSE_DELETED_SUCCESSFULLY": "program calendar course deleted successfully", "PROGRAM_CALENDAR_COURSE_EVENT_DELETED_SUCCESSFULLY": "program calendar course event deleted successfully", "ERROR_UNABLE_TO_DELETE_EVENT_RETRY_SAME": "error unable to delete event retry same", "PROGRAM_CALENDAR_COURSE_UPDATED_SUCCESSFULLY": "program calendar course updated successfully", "UNABLE_TO_EDIT_COURSE_RETRY_SOME_OTHER_TIME": "unable to edit course retry some other time", "NO_COURSE_FOUND": "no course found", "ITS_NOT_ROTATION_LEVEL": "its not rotation level", "ROTATION_LEVEL_NO_NOT_MATCH": "rotation level no not match", "LEVEL_WISE_COURSE_LIST": "level wise course list", "ERROR_UNABLE_TO_FIND_COURSE": "error unable to find course", "ERROR_UNABLE_TO_FIND_COURSE ": "error unable to find course ", "PROGRAM_CALENDAR_ROTATION_COURSE_LIST": "program calendar rotation course list", "EVENTS_NOT_FOUND": "events not found", "LEVEL_NUMBER_IS_NOT_MATCHING_ALONG_BATCH": "level number is not matching along batch", "ROTATION_NOT_MATCHING_IN_THIS_LEVEL": "rotation not matching in this level", "THIS_COURSE_IS_ALREADY_PRESENT_IN_SAME_ROTATION": "this course is already present in same rotation", "PLS_SET_START_&_END_FIRST": "pls set start &_end first", "DATE_MISMATCH_PLS_CHECK_DATES": "date mismatch pls check dates", "ERROR_UNABLE_TO_ADD_COURSE_IN_ROTATION_SO_RETRY": "error unable to add course in rotation so retry", "PROGRAM_CALENDAR_COURSE_ADDED_SUCCESSFULLY": "program calendar course added successfully", "ROTATION_COUNT_MISMATCH": "rotation count mismatch", "PROGRAM_CALENDAR_COURSE_MOVED_SUCCESSFULLY": "program calendar course moved successfully", "ERROR_UNABLE_TO_MOVE_COURSE_IN_ROTATION_SO_RETRY": "error unable to move course in rotation so retry", "QUIZ_ALREADY_STOPPED": "Quiz already stopped", "DS_NO_DATA_FOUND": "No data found", "NO_SLOS_FOUND": "No slos found", "SCHEDULED_LISTS_NOT_FOUND": "Scheduled lists not found", "TODAYS_SCHEDULED_LIST_NOT_FOUND": "Today's scheduled list not found", "COURSE_LIST_NOT_FOUND": "Course list not found", "SELECT_RATING": "Please select rating", "DS_DATA_RETRIEVED": "Data retrieved", "DS_UPDATE_FAILED": "Failed to update data", "DS_UPDATED": "Updated Successfully", "NOTIFICATION_SENT": "Notification sent", "DS_DELETED": "Deleted", "USER_BLOCKED": "user blocked", "NO_CHANNEL_FOUND": "No channel found", "USER_UNBLOCKED": "User unblocked", "DS_ADDED": "Added", "DS_ADD_FAILED": "Add failed", "NOTIFICATIONS_NOT_FOUND": "Notifications not found", "NOTIFICATION_NOT_REMOVED": "Notification could not be removed", "NOTIFICATION_REMOVED": "Notification removed", "NOTIFICATION_UNDO": "Notification undone", "ACTIVITY_ID": "Activity Id must be a valid mongodb id", "CREATED_BY_ID": "Created by must be a valid mongodb id", "QUIZ_TYPE": "Quiz type must be one of quiz,poll or survey", "YEAR_NO": "Year no must be a string", "ROTATION_REQUIRED": "Rotation must be a string", "INSTITUTION_CALENDAR_ID": "Institutiton calendar id required", "ACTIVITY_TYPE_REQUIRED": "Activity type required", "IS_COURSE_ADMIN": "course admin must be a boolean", "QUESTIONS_ARRAY_REQUIRED": "Questions array required", "TERM_NO": "Term no. must be a string", "LEVEL_NO": "Level no. must be a string", "SESSIONID_REQUIRED": "Session id required", "SLO_CLO_ID_REQUIRED": "SLO CLO ID must be a valid mongodb id", "ACTIVITY_TYPE": "Activity type must be either of staff or student", "RATING": "Rating must be a string", "COMMENTS": "Comments must be a string", "SCHEDULE_ID_REQUIRED": "Schedule id must be a valid mongodb id", "STUDENT_STAFF_TYPE_REQUIRED": "Student staff type required and should be either of student or staff", "MODULE_TYPE_REQUIRED": "Module type required", "CREATED_BY_ID_REQUIRED": "Created by id required", "TYPE_REQUIRED": "Type required", "CHANNEL_NAME_REQUIRED": "Channel name required", "IMAGE_URL_REQUIRED": "Image url required", "MEMBERS_ID_REQUIRED": "Members id required", "CHANNEL_ID_REQUIRED": "Channel id required", "STAFFS_REQUIRED": "Staffs array required", "MERGE_STATUS_REQUIRED": "Merge status required", "PAGE_NO_REQUIRED": "Page number required", "LIMIT_REQUIRED": "Limit required", "SEARCH_KEY_REQUIRED": "Search key required", "QUESTION_ID_REQUIRED": "Question id required", "SCHEDULE_IDS_REQUIRED": "Schedule ids required", "SRI_SHANMUGHA_EDUCATION_INSTITUTIONS": "Sri Shanmugha Educational Institutions", "REMOTE_PLATFORM_REQUIRED": "Remote platform required", "GRADING_SYSTEM_REQUIRED": "grading system required", "GAMIFICATION_REQUIRED": "gamification required", "BUZZER_INITIATED": "buzzer initiated", "RETAKE_ATTENDANCE_TRIGGERED": "Retake attendance initiated", "ATTENDANCE_STOPPED": "Attendance Stopped", "ATTENDANCE_ACCEPTED": "Attendance accepted", "AVAILABLE_STATUS": "Available Status", "THERE_IS_NO_RUNNING_ATTENDANCE": "There is no running attendance", "MODE_BY_REQUIRED": "ModeBy required", "THERE_IS_NO_SCHEDULES_FOUND": "There is no schedules found", "QUESTIONS_REQUIRED": "Questions required to add", "QUESTIONS_ADDED": "Questions submitted successfully", "SCHEDULE_ATTENDANCE_ID_REQUIRED": "Schedule attendance id required", "THERE_IS_NO_QUIZ_DETAILS_FOUND": "Quiz Details not found", "QUIZ_TITLE_REQUIRED": "Quiz title required", "ACTIVE_TEMPLATE_LIST": "Active template list", "USER_CREATED_TEMPLATE": "User created template", "TOTAL_PROGRAM_LIST": "Total program list", "SINGLE_PROGRAM_DETAIL": "Single program detail", "SURVEY_TEMPLATE_CREATED_SUCCESSFULLY": "Survey Template Created Successfully", "UNABLE_TO_CREATE_SURVEY_SUCCESSFULLY": "Unable To Create Survey Template", "SURVEY_TEMPLATE_CHANGES_UPDATED_SUCCESSFULLY": "Survey template changes updated successfully", "UNABLE_TO_UPDATE_SURVEY_TEMPLATE_CHANGES": "Unable to update survey template changes", "SINGLE_SURVEY_TEMPLATE": "Single survey template", "USER_PERMISSION_PROGRAM_LIST": "User permission program list", "USER_PERMISSION_CURRICULUM_LIST": "User permission curriculum list", "USER_PERMISSION_LEVEL_LIST": "User permission Level list", "USER_PERMISSION_COURSE_LIST": "User permission course list", "USER_PERMISSION_CALENDAR_LIST": "User permission calendar list", "PLO_DETAILS": "Plo details", "CLO_DETAILS": "Clo details", "USER_NOT_CONFIGURED": "User Not Configured", "EXPIRE_DATE_MESSAGE": "The selected expiry date has already passed.Please choose a future date for the survey's expiry", "UNABLE_TO_PUBLISH_SURVEY": "Unable to publish survey", "SURVEY_PUBLISHED_SUCCESSFULLY": "Survey published successfully", "SELECTED_PROGRAM_USERS": "Selected program users", "DOCUMENT_CREATED": "Document created successfully", "ALREADY_REMARKS_CREATED": "Already Remarks created", "DISCIPLINARY_REMARKS_DELETED_SUCCESSFULLY": "Disciplinary Remark deleted successfully", "DISCIPLINARY_REMARK_UPDATED_SUCCESSFULLY": "Disciplinary Remark updated successfully ", "SURVEY_RESPONSE_SAVED_SUCCESSFULLY": "Survey Response Saved Successfully", "UNABLE_TO_SAVE_SURVEY_RESPONSE": "Unable to save survey response", "SURVEY_QUESTIONS": "Survey Questions", "USER_SURVEY_TYPE_LIST_UPDATED_SUCCESSFULLY": "User survey type list updated successfully", "UNABLE_TO_UPDATE_USER_SURVEY_TYPE_LIST": "Unable to update user survey type list", "USER_SURVEY_TYPE_LIST": "User Survey Type List", "SURVEY_TEMPLATE_CLONED_SUCCESSFULLY": "Survey template cloned successfully", "UNABLE_TO_CLONE_SURVEY_SUCCESSFULLY": "Unable to clone survey template", "USER_PERMISSION_TERM_LIST": "User permission term list", "USER_PERMISSION_YEAR_LIST": "User permission year list", "TOTAL_USER_LIST": "Total user list", "UNABLE_TO_RECONDUCT_SURVEY": "Unable to reconduct survey", "DRAFT_SURVEY_LIST": "Draft survey list", "OUTCOME_REPORT_RESPONSE_SETTINGS_UPDATED_SUCCESSFULLY": "Outcome report response settings updated successfully", "UNABLE_TO_UPDATE_OUTCOME_REPORT_RESPONSE_SETTINGS": "Unable to update Outcome report response settings", "OUTCOME_REPORT_RESPONSE_SETTINGS": "Outcome report response settings", "TAG_REPORT_UPDATED_SUCCESSFULLY": "Tag report updated successfully", "UNABLE_TO_UPDATE_TAG_REPORT": "Unable to update tag report", "TAG_REPORT_CREATED_SUCCESSFULLY": "Tag report created successfully", "UNABLE_TO_CREATE_TAG_REPORT": "Unable to create tag report", "DOCUMENTS_INSERTED": "Documents Inserted successfully", "DOCUMENTS_NOT_INSERTED": "Documents Insertion failed", "MOBILE_NUMBER_ALREADY_REGISTERED": "Mobile number already registered", "USER_ID_ALREADY_REGISTERED": "User ID already registered", "TAG_REPORT_LIST": "Tag Report List", "TAG_REPORT_HEADER_FILTERS": "Tag report header filters", "TAG_REPORT_DELETED_SUCCESSFULLY": "TAG_REPORT_DELETED_SUCCESSFULLY", "UNABLE_TO_DELETE_TAG_REPORT": "UNABLE_TO_DELETE_TAG_REPORT", "EXTERNAL_USERS_UPDATED_SUCCESSFULLY": "External users updated successfully", "CATEGORY_NAME_ALREADY_EXISTS": "Category name already exists", "CATEGORY_NAME_NOT_CREATED": "Category name not created", "CATEGORY_NAME_CREATED_SUCCESSFULLY": "Category name created successfully", "NOT_SAVED": "Not updated successfully", "SAVED_SUCCESSFULLY": "Saved successfully", "FORM_NAME_ALREADY_EXISTS": "form name already exist", "ROLE_NAME_NOT_CREATED": "Role name not created", "CREATED_NAME_SUCCESSFULLY": "Created name successfully", "FORM_CREATED_SUCCESSFULLY": "Form created successfully", "FORM_NOT_CREATED": "Form not created", "FORM_INITIATOR_HAS_STARTED_PROCESSING_EDITING_THE_PUBLISHED_SETTING_IS_NO_LONGER_ALLOWED": "Form initiator has started processing.Editing the published settings is no longer allowed", "UNABLE_TO_EXTENDED_SURVEY_DURATION": "Unable to extend survey duration", "SURVEY_DURATION_EXTENDED_SUCCESSFULLY": "Survey duration extended successfully"}