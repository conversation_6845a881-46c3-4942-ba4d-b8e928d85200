module.exports = {

    college_building: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                name: element.name,
                location: element.location, 
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    college_building_ID: (doc) => {
        let obj = {
            _id: doc._id,
            name: doc.name,
            location: doc.location, 
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    }
}