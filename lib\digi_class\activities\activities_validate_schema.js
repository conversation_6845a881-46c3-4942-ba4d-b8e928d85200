const Joi = require('joi');
const {
    DC_STAFF,
    DC_STUDENT,
    TIME_GROUP_BOOKING_TYPE: { ONSITE, REMOTE },
    SCHEDULE,
    PUBLISHED,
    DRAFT,
    NOT_DRAFT,
    NOT_STARTED,
    STARTED,
    ONGOING,
    COMPLETED,
} = require('../../utility/constants');
const { com_response, comResponseWithRequest } = require('../../utility/common');
const { TIME, ONE_BY_ONE, QUIZ, POLL, SURVEY, OLD, NEW } = require('../../utility/enums');
// create activities schema
function createActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                courseId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('VALID_MONGODB_ID');
                    }),
                sessionFlowIds: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string().length(24).optional(),
                        type: Joi.string(),
                    }),
                ),
                name: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('NAME_REQUIRED');
                    }),
                quizType: Joi.string()
                    .valid(...[QUIZ, POLL, SURVEY])
                    .optional()
                    .error(() => {
                        return req.t('QUIZ_TYPE');
                    }),
                activityId: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('ACTIVITY_ID');
                    }),
                createdBy: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('CREATED_BY_ID');
                    }),
                _program_id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('PROGRAM_ID_REQUIRED');
                    }),
                year_no: Joi.string().error(() => {
                    return req.t('YEAR_NO');
                }),
                term: Joi.string().error(() => {
                    return req.t('TERM_REQUIRED');
                }),
                level_no: Joi.string().error(() => {
                    return req.t('LEVEL_NO_REQUIRED');
                }),
                rotation: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_REQUIRED');
                    }),
                rotation_count: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_COUNT_REQUIRED');
                    }),
                _institution_calendar_id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('INSTITUTION_CALENDAR_ID');
                    }),
                type: Joi.string()
                    .valid(...[SCHEDULE])
                    .optional()
                    .error(() => {
                        return req.t('ACTIVITY_TYPE_REQUIRED');
                    }),
                status: Joi.string()
                    .valid(...[DRAFT, PUBLISHED])
                    .optional()
                    .error(() => {
                        return req.t('STATUS_REQUIRED');
                    }),
                courseAdmin: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('IS_COURSE_ADMIN');
                    }),
                questions: Joi.array()
                    .items(
                        Joi.object().keys({
                            questionId: Joi.string().length(24).optional(),
                            name: Joi.string().optional(),
                            options: Joi.array().optional(),
                            feedback: Joi.array().optional(),
                            createdBy: Joi.string().length(24).optional(),
                            sessionId: Joi.string().length(24),
                            sloIds: Joi.array().optional(),
                            taxonomyIds: Joi.array().optional(),
                            attachments: Joi.array().optional(),
                            order: Joi.number(),
                            type: Joi.string()
                                .valid(...[OLD, NEW])
                                .optional(),
                            questionType: Joi.string().optional(),
                            questionViewType: Joi.string().optional(),
                            describeYourQuestion: Joi.string().optional(),
                            itemCategory: Joi.string().optional(),
                            aiNoOfOptions: Joi.number().optional(),
                            generateFeedback: Joi.boolean().optional(),
                            surveyQuestionType: Joi.string().optional(),
                            maxCharacterLimit: Joi.number().optional(),
                        }),
                    )
                    .optional()
                    .error(() => {
                        return req.t('QUESTIONS_ARRAY_REQUIRED');
                    }),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// get activities schema
function getActivitySchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}
// get Question schema
function getQuestionBanksSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: {
                sessions: Joi.array()
                    .items(Joi.string().length(24))
                    .error(() => {
                        return req.t('SESSIONS_REQUIRED');
                    }),
                courseId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('COURSEID_REQUIRED');
                    }),
                _program_id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('PROGRAM_ID_REQUIRED');
                    }),
                year_no: Joi.string().error(() => {
                    return req.t('YEAR_NO');
                }),
                term: Joi.string().error(() => {
                    return req.t('TERM_NO');
                }),
                level_no: Joi.string().error(() => {
                    return req.t('LEVEL_NO');
                }),
                rotation: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_REQUIRED');
                    }),
                rotation_count: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_COUNT_REQUIRED');
                    }),
                _institution_calendar_id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('INSTITUTION_CALENDAR_ID');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}
function getQuestionSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: {
                sessionId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('SESSIONID_REQUIRED');
                    }),
                courseId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('COURSEID_REQUIRED');
                    }),
                sloOrCloId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('SLO_CLO_ID_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}
// get Quiz type schema
function getSessionAndStudentGroupSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                activityId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID');
                    }),
                sessionId: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('SESSIONID_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// get result schema
function getResultSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID');
                    }),
            },
            query: {
                type: Joi.string()
                    .valid(...[DC_STAFF, DC_STUDENT])
                    .error(() => {
                        return req.t('ACTIVITY_TYPE');
                    }),
                userId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}
// get students schema
function getStudentParamSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// get activities schema
function getActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                userId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
                courseId: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('COURSEID_REQUIRED');
                    }),
                type: Joi.string()
                    .valid(...[DC_STAFF, DC_STUDENT])
                    .error(() => {
                        return req.t('ACTIVITY_TYPE');
                    }),
                sessionId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('SESSIONID_REQUIRED');
                    }),
                scheduleId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('SCHEDULE_ID_REQUIRED');
                    }),
                mergeStatus: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('MERGE_STATUS_REQUIRED');
                    }),
                page: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('PAGE_NO_REQUIRED');
                    }),
                limit: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('LIMIT_REQUIRED');
                    }),
                courseAdmin: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('IS_COURSE_ADMIN');
                    }),
                search: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('SEARCH_KEY_REQUIRED');
                    }),
                mode: Joi.string()
                    .valid(...[NOT_STARTED, COMPLETED, DRAFT, STARTED, NOT_DRAFT])
                    .optional()
                    .error(() => {
                        return req.t('MODE_REQUIRED');
                    }),
                _program_id: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('PROGRAM_ID_REQUIRED');
                    }),
                year_no: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('YEAR_NO');
                    }),
                level_no: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('LEVELNO');
                    }),
                term: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('TERM_REQUIRED');
                    }),
                rotation: Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_REQUIRED');
                    }),
                rotation_count: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('ROTATION_COUNT_REQUIRED');
                    }),
                _institution_calendar_id: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('INSTITUTION_CALENDAR_ID');
                    }),
                mergedStatus: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('MERGED_STATUS_REQUIRED');
                    }),
            },
            headers: Joi.object({
                _institution_calendar_id: Joi.string().length(24),
            }).options({ allowUnknown: true }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// update activities schema
function updateActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                status: Joi.string(),
                isDelete: Joi.boolean().optional(),
                sessionFlowIds: Joi.array().optional(),
                name: Joi.string().optional(),
                // _program_id: Joi.string().length(24).optional(),
                // year_no: Joi.string().optional(),
                // level_no: Joi.string().optional(),
                // rotation: Joi.string().optional(),
                // rotation_count: Joi.string().optional().optional(),
                // _institution_calendar_id: Joi.string().length(24).optional(),
                quizType: Joi.string().optional(),
                type: Joi.string().optional(),
                questionType: Joi.string().optional(),
                courseAdmin: Joi.boolean(),
                questions: Joi.array()
                    .items(
                        Joi.object().keys({
                            questionId: Joi.string().length(24).optional(),
                            name: Joi.string().optional(),
                            questionType: Joi.string().optional(),
                            options: Joi.array().optional(),
                            type: Joi.string()
                                .valid(...[OLD, NEW])
                                .optional(),
                            feedback: Joi.array().optional(),
                            createdBy: Joi.string().length(24).optional(),
                            sessionId: Joi.string().length(24).optional(),
                            sloIds: Joi.array().optional(),
                            taxonomyIds: Joi.array().optional(),
                            attachments: Joi.array().optional(),
                            order: Joi.number(),
                            questionViewType: Joi.string().optional(),
                            describeYourQuestion: Joi.string().optional(),
                            itemCategory: Joi.string().optional(),
                            aiNoOfOptions: Joi.number().optional(),
                            generateFeedback: Joi.boolean().optional(),
                            surveyQuestionType: Joi.string()
                                .valid('Likert Scale', 'Open-Ended')
                                .optional(),
                            maxCharacterLimit: Joi.number().optional(),
                        }),
                    )
                    .optional(),
                schedule: Joi.object()
                    .keys({
                        startDateAndTime: Joi.string(),
                        endDateAndTime: Joi.string(),
                    })
                    .optional(),
                sessionId: Joi.string().length(24).optional(),
                scheduleIds: Joi.array().optional(),
                studentGroupId: Joi.string().length(24).optional(),
                sessionRemoved: Joi.boolean().optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// delete activities schema
function deleteActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                questionId: Joi.string().length(24),
                optionId: Joi.string().length(24).optional(),
                feedback: Joi.boolean().optional(),
                type: Joi.string()
                    .valid(...[SCHEDULE])
                    .optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// delete quiz start schema
function quizStartByStaffSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID_REQUIRED');
                    }),
                sessionId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('SESSION_ID_REQUIRED');
                    }),
                scheduleIds: Joi.array().error(() => {
                    return req.t('SCHEDULE_IDS_REQUIRED');
                }),
                staffStartWithExam: Joi.string().valid(...[TIME, ONE_BY_ONE]),
                time: Joi.number(),
                questionId: Joi.string().length(24).optional(),
                courseAdmin: Joi.boolean().optional(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// delete quiz stop schema
function quizStopByStaffSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID_REQUIRED');
                    }),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// answered question schema
function questionAnsweredByStudentSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID_REQUIRED');
                    }),
                questions: Joi.array().error(() => {
                    return req.t('QUESTIONS_ARRAY_REQUIRED');
                }),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

function acceptQuestionResponseSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                activityId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('ACTIVITY_ID_REQUIRED');
                    }),
                questionId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('QUESTION_ID_REQUIRED');
                    }),
                acceptQuestion: Joi.boolean(),
            }),
            params: {
                id: Joi.string().length(24),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// select session schema
function selectSessionsSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            params: {
                id: Joi.string().length(24).required(),
            },
            query: {
                userId: Joi.string().length(24).required(),
                _program_id: Joi.string().length(24).optional(),
                year_no: Joi.string().optional(),
                level_no: Joi.string().optional(),
                term: Joi.string().optional(),
                rotation: Joi.string().optional(),
                rotation_count: Joi.number().optional(),
                _institution_calendar_id: Joi.string().length(24).optional(),
                courseAdmin: Joi.boolean().optional(),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// get Admin activities schema
function getAdminActivitiesSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                userId: Joi.string()
                    .length(24)
                    .error(() => {
                        return req.t('USERID_REQUIRED');
                    }),
                courseId: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('COURSEID_REQUIRED');
                    }),
                page: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('PAGE_NO_REQUIRED');
                    }),
                limit: Joi.number()
                    .optional()
                    .error(() => {
                        return req.t('LIMIT_REQUIRED');
                    }),
            },
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}
module.exports = {
    createActivitiesSchema,
    getActivitySchema,
    getQuestionBanksSchema,
    getQuestionSchema,
    getSessionAndStudentGroupSchema,
    getActivitiesSchema,
    updateActivitiesSchema,
    deleteActivitiesSchema,
    quizStartByStaffSchema,
    quizStopByStaffSchema,
    questionAnsweredByStudentSchema,
    getResultSchema,
    getStudentParamSchema,
    acceptQuestionResponseSchema,
    selectSessionsSchema,
    getAdminActivitiesSchema,
};
