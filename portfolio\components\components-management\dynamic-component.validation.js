const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const createDynamicComponentSchema = Joi.object({
    body: Joi.object({
        component: Joi.object({
            children: Joi.array().required(),
            hierarchies: Joi.array().required(),
        }).required(),
    }),
}).unknown(true);

const getDynamicComponentSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
    }),
}).unknown(true);

const deleteDynamicComponentSchema = Joi.object({
    query: Joi.object({
        componentId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    createDynamicComponentSchema,
    getDynamicComponentSchema,
    deleteDynamicComponentSchema,
};
