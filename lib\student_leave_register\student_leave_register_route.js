const express = require('express');
const route = express.Router();
const student_leave_register = require('./student_leave_register_controller');
const validater = require('./student_leave_register_validator');
const file_upload = require('../utility/file_upload');
const multer = require('multer');

const leave_doc_upload = file_upload.uploadfile.fields([
    { name: 'leave_document', maxCount: 1 }
]);

route.post('/leave-register',(req, res, next) => {
    leave_doc_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            console.log('AWS error', err);
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    });
},validater.leave_document_upload,student_leave_register.insert);
route.get('/student-data/:id',validater.getStudentDataValidater,student_leave_register.getStudentData);
route.post('/notify-student',validater.notifyStudentValidater,student_leave_register.notifyStudent);


module.exports = route;