const { string, bool, boolean } = require('joi');
const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const lmsSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
        },
        category: [
            {
                category_to: {
                    type: String,
                },
                category_type: {
                    type: String,
                    // required: true
                },
                category_name: {
                    type: String,
                    // required: true
                },
                permission: {
                    permission_hours: {
                        type: Number,
                    },
                    permission_frequency: {
                        type: Number,
                    },
                    permission_frequency_by: {
                        type: String,
                    },
                    is_scheduled: {
                        type: Boolean,
                    },
                    is_permission_attachment_required: {
                        type: Boolean,
                    },
                },
                leave_type: [
                    {
                        type_name: {
                            type: String,
                            // required: true
                        },
                        desc: {
                            type: String,
                        },
                        gender: {
                            type: String,
                        },
                        payment: {
                            type: String,
                        },
                        entitlement: {
                            type: String,
                        },
                        no_of_days: {
                            type: Number,
                        },
                        weekend_consideration: {
                            type: Boolean,
                        },
                        per_month: {
                            type: Number,
                        },
                        is_reason_required: {
                            type: <PERSON><PERSON><PERSON>,
                        },
                        is_attachment_required: {
                            type: Boolean,
                        },
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                        isDeleted: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
                isActive: {
                    type: Boolean,
                    default: true,
                },
                isDeleted: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        student_warning_absence_calculation: [
            {
                warning: {
                    type: String,
                },
                absence_percentage: {
                    type: Number,
                },
                // conducted_session_percentage: {
                //     type: Number,
                // },
                warning_message: {
                    type: String,
                },
                isActive: {
                    type: Boolean,
                    default: true,
                },
                isDeleted: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        hr_contact: {
            type: String,
        },
        leave_approval: [
            {
                staff_type: [
                    {
                        type: String,
                    },
                ],
                _staff_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.USER,
                },
                staff_name: {
                    type: String,
                },
                level_no: {
                    type: Number,
                },
                approver_level_name: {
                    type: String,
                },
                approver_association: {
                    type: String,
                },
                role: {
                    _roles_id: {
                        type: Schema.Types.ObjectId,
                        ref: constant.ROLE,
                    },
                    role_name: {
                        type: String,
                    },
                },
            },
        ],
        reviewer_forwarder_approver: [
            {
                flow: String,
                staff_type: {
                    type: String,
                    required: true,
                },
                roles: [
                    {
                        role: String,
                        status: Boolean,
                    },
                ],
                user_ids: [
                    {
                        user_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.USER,
                        },
                        name: {
                            first: String,
                            last: String,
                            middle: String,
                            family: String,
                        },
                    },
                ],
                attachment_required: Boolean,
                isActive: {
                    type: Boolean,
                    default: true,
                },
            },
        ],
        // reviewer: [{
        //     staff_type: {
        //         type: String,
        //         required: true
        //     },
        //     roles: {
        //         dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         vice_dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         department_chairman: {
        //             type: Boolean,
        //             default: false
        //         },
        //         reporting_manager: {
        //             type: Boolean,
        //             default: false
        //         },
        //         custom: {
        //             type: Boolean,
        //             default: false
        //         }
        //     },
        //     user_ids: [{
        //         type: Schema.Types.ObjectId,
        //         ref: constant.USER
        //     }],
        //     isActive: {
        //         type: Boolean,
        //         default: true
        //     },
        //     isDeleted: {
        //         type: Boolean,
        //         default: false
        //     }
        // }],
        // forwarder: [{
        //     staff_type: {
        //         type: String,
        //         required: true
        //     },
        //     roles: {
        //         dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         vice_dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         department_chairman: {
        //             type: Boolean,
        //             default: false
        //         },
        //         reporting_manager: {
        //             type: Boolean,
        //             default: false
        //         },
        //         custom: {
        //             type: Boolean,
        //             default: false
        //         }
        //     },
        //     user_ids: [{
        //         type: Schema.Types.ObjectId,
        //         ref: constant.USER
        //     }],
        //     isActive: {
        //         type: Boolean,
        //         default: true
        //     },
        //     isDeleted: {
        //         type: Boolean,
        //         default: false
        //     },
        // }],
        // approver: [{
        //     staff_type: {
        //         type: String,
        //         required: true
        //     },
        //     roles: {
        //         dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         vice_dean: {
        //             type: Boolean,
        //             default: false
        //         },
        //         department_chairman: {
        //             type: Boolean,
        //             default: false
        //         },
        //         reporting_manager: {
        //             type: Boolean,
        //             default: false
        //         },
        //         custom: {
        //             type: Boolean,
        //             default: false
        //         }
        //     },
        //     user_ids: [{
        //         type: Schema.Types.ObjectId,
        //         ref: constant.USER
        //     }],
        //     isActive: {
        //         type: Boolean,
        //         default: true
        //     },
        //     isDeleted: {
        //         type: Boolean,
        //         default: false
        //     },
        // }],
        report_absence_document: Boolean,
        report_absence: [
            {
                staff_type: {
                    type: String,
                    required: true,
                },
                roles: {
                    dean: {
                        type: Boolean,
                        required: true,
                    },
                    vice_dean: {
                        type: Boolean,
                        required: true,
                    },
                    department_chairman: {
                        type: Boolean,
                        required: true,
                    },
                    reporting_manager: {
                        type: Boolean,
                        required: true,
                    },
                    custom: {
                        type: Boolean,
                        required: true,
                    },
                    is_attachment_needed: {
                        type: Boolean,
                        required: true,
                    },
                },
                user_ids: [
                    {
                        type: Schema.Types.ObjectId,
                        ref: constant.USER,
                    },
                ],
                isActive: {
                    type: Boolean,
                    default: true,
                },
                isDeleted: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(constant.LMS, lmsSchema);
