require('dotenv').config();
module.exports = {
    BASIC_DATA_FROM: process.env.BASIC_DATA_FROM || 'local',
    SIS_BASE_URL: process.env.SIS_BASE_URL,
    AWS_ACCESS_KEY: process.env.AWS_ACCESS_KEY,
    AWS_SECRET_KEY: process.env.AWS_SECRET_KEY,
    BUCKET_NAME: process.env.AWS_BUCKET_NAME,
    DIGIVAL_CLOUD_PROVIDER: process.env.DIGIVAL_CLOUD_PROVIDER || 'AWS',
    OCI_REGION: process.env.OCI_REGION || '',
    OCI_AWS_S3_API: process.env.OCI_AWS_S3_API || '',
    OCI_ACCESS_KEY_ID: process.env.OCI_ACCESS_KEY_ID || '',
    OCI_SECRET_ACCESS_KEY: process.env.OCI_SECRET_ACCESS_KEY || '',
    AWS_REGION: process.env.AWS_REGION || '',
    AWS_BUCKET_NAME_ASSIGNMENT: process.env.AWS_BUCKET_NAME_ASSIGNMENT,
};
