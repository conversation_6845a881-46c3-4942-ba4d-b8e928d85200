const mongoose = require('mongoose');
const { Schema } = mongoose;

// constants
const {
    COURSE_SCHEDULE_SETTING,
    DIGI_PROGRAM,
    INSTITUTION,
    INSTITUTION_CALENDAR,
    GENDER: { MALE, FEMALE, BOTH },
    DAYS_MODE: { DAILY, WEEKLY },
    COURSE_MANAGEMENT_SESSION_TYPE: { EXTRA_CURRICULAR, BREAK },
    DAYS: { SUNDAY, MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY },
    REMOTE_PLATFORM: { ZOOM, TEAMS },
} = require('../utility/constants');

// enums
const { AM, PM } = require('../utility/enums');

const courseScheduleSettingSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        programs: [
            {
                // program id
                _program_id: {
                    type: Schema.Types.ObjectId,
                    ref: DIGI_PROGRAM,
                    required: true,
                    trim: true,
                },
                extraCurricularAndBreakTiming: [
                    {
                        _institution_calendar_id: {
                            type: Schema.Types.ObjectId,
                            ref: INSTITUTION_CALENDAR,
                            required: true,
                        },
                        title: {
                            type: String,
                            trim: true,
                        },
                        type: {
                            type: String,
                            trim: true,
                            enum: [EXTRA_CURRICULAR, BREAK],
                        },
                        gender: {
                            type: String,
                            trim: true,
                            enum: [MALE, FEMALE, BOTH],
                        },
                        mode: {
                            type: String,
                            trim: true,
                            enum: [DAILY, WEEKLY],
                        },
                        days: [
                            {
                                type: String,
                                trim: true,
                                enum: [
                                    SUNDAY,
                                    MONDAY,
                                    TUESDAY,
                                    WEDNESDAY,
                                    THURSDAY,
                                    FRIDAY,
                                    SATURDAY,
                                ],
                            },
                        ],
                        startTime: {
                            hour: {
                                type: Number,
                                trim: true,
                            },
                            minute: {
                                type: Number,
                                trim: true,
                            },
                            format: {
                                type: String,
                                trim: true,
                                enum: [AM, PM],
                            },
                        },
                        endTime: {
                            hour: {
                                type: Number,
                                trim: true,
                            },
                            minute: {
                                type: Number,
                                trim: true,
                            },
                            format: {
                                type: String,
                                trim: true,
                                enum: [AM, PM],
                            },
                        },
                        allowCourseCoordinatesToEdit: {
                            type: Boolean,
                            default: false,
                        },
                        isDeleted: {
                            type: Boolean,
                            default: false,
                        },
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                    },
                ],
                remoteScheduling: [
                    {
                        remotePlatform: { default: TEAMS, type: String, enum: [null, ZOOM, TEAMS] },
                        meetingTitle: {
                            type: String,
                            trim: true,
                        },
                        meetingUrl: {
                            type: String,
                            trim: true,
                        },
                        meetingUsername: {
                            type: String,
                            trim: true,
                        },
                        associatedEmail: {
                            type: String,
                            trim: true,
                        },
                        gender: {
                            type: String,
                            trim: true,
                        },
                        meetingId: {
                            type: String,
                            trim: true,
                        },
                        passCode: {
                            type: String,
                            trim: true,
                        },
                        password: {
                            type: String,
                            trim: true,
                        },
                        term: {
                            type: String,
                            trim: true,
                        },
                        levelId: {
                            type: Schema.Types.ObjectId,
                        },
                        levelName: {
                            type: String,
                            trim: true,
                        },
                        yearId: {
                            type: Schema.Types.ObjectId,
                        },
                        yearName: {
                            type: String,
                            trim: true,
                        },
                        isDeleted: {
                            type: Boolean,
                            default: false,
                        },
                        apiKey: {
                            type: String,
                            trim: true,
                        },
                        apiSecretKey: {
                            type: String,
                            trim: true,
                        },
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                    },
                ],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(COURSE_SCHEDULE_SETTING, courseScheduleSettingSchema);
