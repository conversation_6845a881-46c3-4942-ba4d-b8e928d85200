const mongoose = require('mongoose');
const Schema = mongoose.Schema;
// constants
const { TAXONOMY, USER } = require('../utility/constants');

const taxonomySchema = new Schema(
    {
        name: {
            type: String,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: false,
        },
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(TAXONOMY, taxonomySchema);
