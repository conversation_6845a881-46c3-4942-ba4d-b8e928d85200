{"name": "@digival/digischeduler-api", "version": "8.5.5", "description": "Digi class Scheduler management system", "main": "index.js", "author": "PBP", "license": "ISC", "scripts": {"start": "node ./bin/ds-install", "start:dev": "nodemon ./bin/ds-install"}, "dependencies": {"@elastic/elasticsearch": "^7.13.0", "@slack/web-api": "^7.7.0", "aws-sdk": "^2.653.0", "axios": "^1.8.3", "bcryptjs": "^2.4.3", "body-parser": "^1.19.0", "compression": "^1.7.4", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "dotenv": "^8.2.0", "ejs": "^3.1.5", "elastic-apm-node": "^4.8.1", "exceljs": "^4.4.0", "express": "^4.21.2", "express-rate-limit": "^6.2.1", "firebase-admin": "^13.0.1", "flat-cache": "^5.0.0", "fs": "0.0.1-security", "fs-extra": "^10.0.0", "helmet": "^8.0.0", "i18next": "^23.16.8", "i18next-fs-backend": "^2.6.0", "i18next-http-middleware": "^3.7.0", "ioredis": "^4.28.5", "joi": "^17.13.3", "jwt-decode": "^3.1.2", "mathjs": "^12.4.1", "moment": "^2.24.0", "moment-timezone": "^0.5.33", "mongodb": "^6.11.0", "mongoose": "^8.9.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-s3": "^2.10.0", "node-cron": "^3.0.0", "nodejs-base64": "^2.0.0", "nodemailer": "^6.4.6", "oci-common": "^2.55.1", "oci-objectstorage": "^2.55.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "path": "^0.12.7", "pino": "^6.8.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "prettier": "^2.7.1", "pug": "^3.0.0", "random-number": "0.0.9", "rate-limiter-flexible": "^7.1.0", "sentiment": "^5.0.2", "serve-favicon": "^2.5.0", "stream-chat": "^8.45.3", "twilio": "^4.11.2", "uuid": "^11.0.3"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-prettier": "^3.1.3", "husky": "^4.2.5", "lint-staged": "^10.2.9", "nodemon": "^3.1.7"}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}