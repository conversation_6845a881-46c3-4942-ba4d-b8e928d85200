const axios = require('axios');
const {
    TEAMS_LOGIN_URL,
    TEAMS_MEETING_OPTIONS,
    TEAMS_GET_API_URL,
    TEAMS_V1_API_URL,
} = require('../lib/utility/constants');

const teamsTokenGeneration = (req) => {
    const teamsMeetingOptions = {
        method: 'POST',
        url: `${TEAMS_LOGIN_URL}${process.env.TEAMS_TENANT_ID}/oauth2/v2.0/token`,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: {
            client_id: process.env.TEAMS_CLIENT_ID,
            client_secret: process.env.TEAMS_CLIENT_SECRET,
            grant_type: process.env.TEAMS_GRANT_TYPE,
            redirect_uri: process.env.TEAMS_REDIRECT_URL,
            scope: process.env.TEAMS_SCOPE,
        },
    };

    return axios(teamsMeetingOptions)
        .then((response) => response.data)
        .catch((err) => {
            console.error('Error during token generation:', err);
            throw err;
        });
};

const teamsUserListByEmail = (req) => {
    const teamsMeetingOptions = {
        method: 'GET',
        url: `${TEAMS_MEETING_OPTIONS}${req.email}"`,
        headers: {
            ConsistencyLevel: 'eventual',
            Authorization: `Bearer ${req.token}`,
        },
    };

    return axios(teamsMeetingOptions)
        .then((response) => response.data)
        .catch((err) => {
            console.error('Error fetching user list by email:', err);
            throw err;
        });
};

const getTeamsMeetingOccurences = (req) => {
    const teamsMeetingOptions = {
        method: 'GET',
        url: `${TEAMS_GET_API_URL}users/${req.userId}/onlineMeetings/${req.meetingId}/attendanceReports?$expand=attendanceRecords`,
        headers: {
            Authorization: `Bearer ${req.token}`,
        },
    };

    return axios(teamsMeetingOptions)
        .then((response) => response.data)
        .catch((err) => {
            console.error('Error fetching meeting occurrences:', err);
            throw err;
        });
};

const getTeamsParticipantList = (req) => {
    const teamsMeetingOptions = {
        method: 'GET',
        url: `${TEAMS_GET_API_URL}users/${req.userId}/onlineMeetings/${req.meetingId}/attendanceReports/${req.reportId}?$expand=attendanceRecords`,
        headers: {
            Authorization: `Bearer ${req.token}`,
        },
    };

    return axios(teamsMeetingOptions)
        .then((response) => response.data)
        .catch((err) => {
            console.error('Error fetching participant list:', err);
            throw err;
        });
};

const createTeamsMeetingAPI = (req) => {
    const teamsMeetingOptions = {
        method: 'POST',
        url: `${TEAMS_V1_API_URL}users/${req.UserId}/onlineMeetings`,
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${req.token}`,
        },
        data: req.params, // No need to stringify, axios handles JSON automatically
    };

    return axios(teamsMeetingOptions)
        .then((response) => response.data)
        .catch((err) => {
            console.error('Error creating Teams meeting:', err);
            throw err;
        });
};

const msTeamUserData = async ({ token, userId }) => {
    const teamsMeetingOptions = {
        method: 'GET',
        url: `${TEAMS_V1_API_URL}users/${userId}?$select=displayName,givenName,userPrincipalName`,
        headers: {
            ConsistencyLevel: 'eventual',
            Authorization: `Bearer ${token}`,
        },
    };

    try {
        const response = await axios(teamsMeetingOptions);
        return response.data;
    } catch (err) {
        console.error('Error fetching Teams user data:', err);
        throw err;
    }
};

module.exports = {
    teamsTokenGeneration,
    teamsUserListByEmail,
    createTeamsMeetingAPI,
    getTeamsMeetingOccurences,
    getTeamsParticipantList,
    msTeamUserData,
};
