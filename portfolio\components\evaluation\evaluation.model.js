const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { PORTFOLIO_ASSIGN_EVALUATOR } = require('../../common/utils/constants');

const schema = new Schema(
    {
        programId: { type: ObjectId, required: true },
        courseId: { type: ObjectId, required: true },
        institutionCalendarId: { type: ObjectId, required: true },
        portfolioId: { type: ObjectId, required: true },
        componentId: { type: ObjectId, required: true },
        childrenId: { type: ObjectId, required: true },
        createdBy: { type: ObjectId, required: true },
        role: {
            name: { type: String },
            _id: { type: ObjectId },
        },
        canApproveEvaluation: { type: Boolean, default: false },
        evaluators: [
            {
                userId: { type: ObjectId, required: true },
                name: { first: { type: String }, middle: { type: String }, last: { type: String } },
                email: { type: String, required: true },
                employeeId: { type: String, required: true },
            },
        ],
        deliveryTypes: [
            {
                sessionId: { type: ObjectId },
                deliveryTypeId: { type: ObjectId },
                deliveryTypeName: { type: String },
                deliveryTypeSymbol: { type: String },
            },
        ],
    },
    { timestamps: true },
);

module.exports = model(PORTFOLIO_ASSIGN_EVALUATOR, schema);
