/*
 *  Module dependencies
 */

const { connect } = require('mongoose');
const {
    MONGO_DB,
    MONGODB_CLOUD_SERVICE,
    // MONGODB_SSL_CA,
    TLS_CA_FILE,
    TLS_CERTIFICATE_KEY_FILE,
} = require('../utility/util_keys');

/**
 * Expose models linker helper
 *
 * @param {Express} app `Express` instance
 */

module.exports = function models() {
    /*
     *  Connect to mongo
     */
    const mongooseOption = {
        // useNewUrlParser: true,
        // useCreateIndex: true,
        // useUnifiedTopology: true,
    };

    /*applicable only for self managed database */
    if (MONGODB_CLOUD_SERVICE === 'scalegrid') {
        console.log('connecting to scalegrid...');
        // mongooseOption.sslCA = `${__dirname}/${MONGODB_SSL_CA}`;
    }
    if (MONGODB_CLOUD_SERVICE === 'selfhost') {
        console.log('connecting to selfhost MongoDB...');
        mongooseOption.tlsCAFile = `${__dirname}/${TLS_CA_FILE}`;
        mongooseOption.tlsCertificateKeyFile = `${__dirname}/${TLS_CERTIFICATE_KEY_FILE}`;
    }

    connect(MONGO_DB, mongooseOption)
        .then(() => console.log(`Database connection established with ${MONGODB_CLOUD_SERVICE}`))
        .catch((err) => {
            console.error('Error while connecting to database:', err);
            process.exitCode = 1;
        });

    /**
     * Register models
     */

    require('./country');
    require('./course');
    require('./credit_hours_calc');
    require('./credit_hours_individual');
    require('./credit_hours_master');
    require('./department');
    require('./department_division');
    require('./department_subject');
    require('./session_order');
    require('./institution');
    require('./institution_calendar');
    require('./calendar_event');
    require('./notification_manager');
    require('./program');
    require('./infrastructures');
    require('./position');
    require('./staff');
    require('./role');
    require('./session_type');
    require('./program_calendar');
    require('./user');
    require('./parent.user');
    require('./user_history');
    require('./permission');
    require('./privilege');
    require('./permission_set');
    require('./role_set');
    require('./student_group');
    require('./staff_committee');
    require('./day_group');
    require('./time_group');
    require('./college_building');
    require('./hospital');
    require('./lms');
    require('./lms_review');
    require('./infrastructure_management');
    require('./roles_offices_list');
    require('./roles_permissions');
    require('./infrastructure_event');
    require('./infrastructure_event_exam');
    require('./infrastructure_delivery_medium');
    require('./course_schedule');
    require('./course_staff_allocation');
    require('./module');
    require('./role_assign');
    require('./sessions');
    require('./country_state_city');

    //New Program Input
    require('./digi_institute');
    require('./digi_programs');
    require('./digi_department_subject');
    require('./digi_session_delivery_types');
    require('./digi_curriculum');
    require('./digi_college');
    require('./digi_university');
    require('./digi_course');
    require('./digi_course_group');
    require('./digi_session_order');
    require('./digi_session_order_module');
    require('./digi_course_assign');
    require('./student_session_survey');
    require('./digi_labels');

    //course Schedule
    require('./course_schedule_setting');
    require('./course_schedule_delivery_settings');

    //Vaccination
    require('./vaccination');
    require('./user_vaccination_details');

    // mapping module
    require('./digi_framework');
    require('./digi_impact_mapping_type');
    require('./digi_content_mapping_type');
    require('./dashboard_setting');
    require('./department_setting');

    // App version Control
    require('./appVersions');

    require('./studentCriteriaManipulation');

    // DigiClass Schedule Multi Device Attendance Module
    require('./schedule_multi-device_attendance');

    require('./warning_mail');
    require('../lmsStudentSetting/lmsStudentSetting.model');
    //list Q360-Sub-model
    require('../q360/rolePermission/action.model');
};
