const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const { USER, DISCUSSION_TOPICS, DISCUSSION_REPLIES } = require('../../utility/constants');

const discussionRepliesSchema = new Schema(
    {
        discussion_id: {
            type: mongoose.Schema.ObjectId,
            ref: DISCUSSION_TOPICS,
            required: true,
        },
        reply_id: {
            type: mongoose.Schema.ObjectId,
            default: null,
        },
        comment: {
            type: String,
            required: true,
            minlength: 3,
            maxlength: 5000,
        },
        author: {
            type: ObjectId,
            ref: USER,
            required: true,
        },
        likes: {
            type: [ObjectId],
            ref: USER,
            default: [],
        },
        user_type: {
            type: String,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        readBy: {
            type: [ObjectId],
            ref: USER,
            default: [],
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(DISCUSSION_REPLIES, discussionRepliesSchema);
