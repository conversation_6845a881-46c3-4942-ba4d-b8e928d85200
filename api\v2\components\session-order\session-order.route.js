const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    addSession,
    editSession,
    deleteSession,
    listSessions,
    addIndependentCourseSession,
    editIndependentCourseSession,
    listIndependentCourseSessions,
} = require('./session-order.controller');
const {
    addEditSessionValidator,
    deleteSessionValidator,
    getSessionsValidator,
    addEditIndependentSessionValidator,
    getIndependentCourseSessionsValidator,
} = require('./session-order.validator');
const { validate } = require('../../utility/input-validation');

router.post('/add-session', validate(addEditSessionValidator), catchAsync(addSession));
router.post(
    '/add-independent-course-session',
    validate(addEditIndependentSessionValidator),
    catchAsync(addIndependentCourseSession),
);
router.put('/edit-session/:id', validate(addEditSessionValidator), catchAsync(editSession));
router.put('/edit-independent-course-session', catchAsync(editIndependentCourseSession));
router.delete('/delete-session/:id', validate(deleteSessionValidator), catchAsync(deleteSession));
router.get('/list-sessions', validate(getSessionsValidator), catchAsync(listSessions));
router.get(
    '/list-independent-sessions',
    validate(getIndependentCourseSessionsValidator),
    catchAsync(listIndependentCourseSessions),
);

module.exports = router;
