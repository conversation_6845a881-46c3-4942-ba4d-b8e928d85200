const Document = require('../../models/document_manager');
const courseSchedules = require('../../models/course_schedule');
const {
    get,
    update,
    update_push_pull,
    distinct_list,
    delete: customDelete,
} = require('../../base/base_controller');

const {
    convertToMongoObjectId,
    response_function,
    list_all_response_function,
    sendResponse,
    clone,
    listAllResponseFunctionWithRequest,
    responseFunctionWithRequest,
    sendResponseWithRequest,
} = require('../../utility/common');
const {
    getDocumentList,
    getCourseDocumentList,
    getCourseSessionDocumentList,
    getAllSessionList,
    DocumentCreations,
    multipleDocumentCreations,
    getZoomUsersDuration,
    createMeeting,
    createStudentJoinUrl,
    getCourseAdminDocuments,
    getCourseSessionDocumentListWithFilter,
    getCourseDocumentListWithFilter,
    courseParamsUpdate,
    checkStudentActivityOrDocument,
    getUserRecentDocumentList,
} = require('./document_manager_service');
const { BUCKET_DOCUMENT, logger } = require('../../utility/util_keys');
const file_manager = require('../../utility/file_upload');
const { getSignedURL } = require('../../utility/common_functions');
const { moveFile } = require('../../utility/file_upload');

// get all documents
exports.getDocuments = async (req, res) => {
    try {
        const {
            query: { limit, pageNo: page, search, type, subTab, tab, courseAdmin, remote },
            params: { userId },
            headers: { _institution_calendar_id },
        } = req;

        const { totalDoc, totalPages, currentPage, documents } = await getDocumentList(
            userId,
            limit,
            page,
            search,
            tab,
            subTab,
            type,
            courseAdmin,
            _institution_calendar_id,
            remote,
        );

        if (documents.length === 0) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documents,
                    ),
                );
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// get all course based documents
exports.getCourseDocuments = async (req, res) => {
    try {
        const {
            query: {
                limit,
                pageNo: page,
                search,
                type,
                subTab,
                tab,
                _program_id,
                courseAdmin,
                remote,
            },
            params: { userId, courseId },
        } = req;

        const { totalDoc, totalPages, currentPage, documents } = await getCourseDocumentList(
            userId,
            courseId,
            limit,
            page,
            search,
            tab,
            subTab,
            type,
            _program_id,
            courseAdmin,
            remote,
        );
        if (documents.length === 0) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documents,
                    ),
                );
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.getCourseAdminDocuments = async (req, res) => {
    try {
        const {
            query: { limit, pageNo: page, search, type, subTab, tab },
            params: { userId, courseId },
        } = req;

        const { totalDoc, totalPages, currentPage, documents } = await getCourseAdminDocuments(
            userId,
            courseId,
            limit,
            page,
            search,
            tab,
            subTab,
            type,
        );
        if (documents.length === 0) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documents,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
// get all course based documents
exports.getCourseSessionDocuments = async (req, res) => {
    try {
        const {
            query: { limit, pageNo: page, search, type, tab, subTab, _program_id, remote },
            params: { userId, courseId, sessionId },
        } = req;

        const { totalDoc, totalPages, currentPage, documents } = await getCourseSessionDocumentList(
            userId,
            courseId,
            sessionId,
            limit,
            page,
            search,
            tab,
            subTab,
            type,
            _program_id,
            remote,
        );
        if (documents.length === 0) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documents,
                    ),
                );
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// get all documents by session id
exports.getDocumentsBySession = async (req, res) => {
    try {
        const {
            params: { userId },
            body: { sessions },
        } = req;

        const sessionData = sessions.map((session) => convertToMongoObjectId(session));

        // get query document data
        const data = await Document.find({
            _user_id: convertToMongoObjectId(userId),
            _session_flow_id: { $in: sessionData },
            isDeleted: false,
        }).sort({ createdAt: -1 });

        const sessionIds = [];
        data.map(({ _session_flow_id }) => {
            _session_flow_id.map((ids) => {
                if (ids && sessionIds.indexOf(ids.toString()) < 0) {
                    sessionIds.push(ids.toString());
                }
            });
        });
        const courseSessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const doc = await distinct_list(courseSchedules, 'session', {
            isDeleted: false,
            'session._session_id': { $in: courseSessions },
        });

        const DocData = data.map(function (Docs) {
            Items = {
                starred: Docs.starred,
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                createdAt: Docs.createdAt,
            };
            const sessionDatas = [];
            for (const element of Docs._session_flow_id) {
                const sessionDetails = doc.data.find(
                    (session) => session._session_id.toString() === element.toString(),
                );
                if (sessionDetails) {
                    sessionDatas.push(sessionDetails);
                }
            }
            Items.sessions = sessionDatas;
            return Items;
        });

        if (DocData.length === 0) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('NO_DOCUMENTS_FOUND'), []));
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DOCUMENT_LIST'), DocData));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
// create document
exports.createDocument = async (req, res) => {
    try {
        const {
            files,
            params: { userId },
            body: {
                type,
                name,
                url,
                courseId,
                sessions,
                file,
                title,
                courseAdmin,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
            },
        } = req;

        const data = {
            type,
            name,
            url,
            title,
            _course_id: courseId,
            sessions,
            _user_id: userId,
            file,
            courseAdmin,
            _program_id,
            year_no,
            level_no,
            term,
            rotation,
            rotation_count,
            _institution_calendar_id,
            files,
        };
        const { status, insertedIds } = await multipleDocumentCreations([data]);
        if (!status) {
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_ADD_DOCUMENT'),
                        [],
                    ),
                );
        }
        data._id = insertedIds[0];
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DOCUMENT_ADDED_SUCCESSFULLY'),
                    data /* doc.data */,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// update document
exports.updateDocument = async (req, res) => {
    try {
        const {
            body: { starred, name, sessions },
            params: { id, userId },
        } = req;

        let updateDoc;
        let response;

        if (!sessions && !name) {
            if (starred === 'true') {
                updateDoc = { $push: { starred: convertToMongoObjectId(userId) } };
            } else {
                updateDoc = { $pull: { starred: convertToMongoObjectId(userId) } };
            }
            response = await update_push_pull(Document, convertToMongoObjectId(id), updateDoc);
        } else {
            const sessionIds = sessions.map((session) => {
                return {
                    type: session.type,
                    _id: convertToMongoObjectId(session._id),
                };
            });
            updateDoc = { sessionOrScheduleIds: sessionIds, name };
            response = await update(Document, convertToMongoObjectId(id), updateDoc);
        }

        if (!response.status) {
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_EDIT_DOCUMENT'),
                        [],
                    ),
                );
        }

        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_EDIT_SUCCESSFULLY')));
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// delete document
exports.deleteDocument = async (req, res) => {
    try {
        const {
            params: { id },
        } = req;
        const {
            status,
            data: { type, url },
        } = await get(Document, convertToMongoObjectId(id), {});
        if (!status) {
            return res
                .status(200)
                .send(response_function(res, 200, false, req.t('UNABLE_TO_DELETE_DOCUMENT'), []));
        }

        if (type !== 'url') {
            const urlSplit = url.split('/');
            const bucket = BUCKET_DOCUMENT.split('/');
            let fileName = '';
            if (urlSplit[urlSplit.length - 2] === bucket[1]) {
                fileName = urlSplit[urlSplit.length - 1];
            } else {
                //Need to handle / file name
            }
            await file_manager.remove(BUCKET_DOCUMENT, fileName);
        }
        const { status: removedStatus } = await customDelete(Document, convertToMongoObjectId(id));
        if (!removedStatus) {
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_DELETE_DOCUMENT'),
                        [],
                    ),
                );
        }

        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_DELETED_SUCCESSFULLY')),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
// select sessions
exports.selectSessions = async (req, res) => {
    try {
        const {
            params: { id },
            query: {
                userId,
                institutionCalendarId,
                programId,
                yearNo,
                term,
                levelNo,
                rotation,
                rotation_count,
                courseAdmin,
            },
        } = req;
        const sessionSupportList = await getAllSessionList(
            id,
            userId,
            institutionCalendarId,
            programId,
            yearNo,
            levelNo,
            term,
            '',
            rotation,
            rotation_count,
            courseAdmin,
        );
        if (!sessionSupportList.length)
            return sendResponseWithRequest(req, res, 200, false, req.t('NO_DATA_FOUND'), []);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SESSION_LIST'),
            sessionSupportList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};
exports.zoomUpload = async (req, res) => {
    try {
        const DoctList = await getZoomUsersDuration([req.body._id]);
        return sendResponse(res, 200, true, 'ZOOM MEETING', DoctList);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// get all documents
exports.getDocumentById = async (req, res) => {
    try {
        const {
            params: { _id },
        } = req;
        const userPopulate = {
            path: '_user_id',
            select: { name: 1, user_id: 1 },
        };
        const documentData = clone(
            await Document.findOne(
                { _id: convertToMongoObjectId(_id) },
                {
                    isDeleted: 0,
                    isActive: 0,
                    // createdAt: 0,
                    // updatedAt: 0,
                },
            ).populate(userPopulate),
        );
        if (!documentData)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, false, req.t('DOCUMENT_NOT_FOUND'), null),
                );
        documentData.UploadedBy = documentData._user_id ? documentData._user_id : {};
        documentData.starred = null;
        const sessionIds = documentData.sessionOrScheduleIds.map((sessionOrScheduleIdElement) =>
            convertToMongoObjectId(sessionOrScheduleIdElement._id),
        );
        const scheduleDatas = await distinct_list(courseSchedules, 'session', {
            isDeleted: false,
            $or: [{ 'session._session_id': { $in: sessionIds } }, { _id: { $in: sessionIds } }],
        });
        documentData.sessions = scheduleDatas.status ? scheduleDatas.data : [];
        const matchURLData = documentData.url.match(/ibnsina.digiproducts/g);
        if (
            matchURLData !== null ||
            (documentData.type === 'file' && documentData.url && documentData.url.split('/'))
        ) {
            const docUrl = documentData.url;
            if (docUrl.indexOf(' ') >= 0 || docUrl.indexOf(')') >= 0 || docUrl.indexOf('(') >= 0) {
                const ArrayIndex = docUrl.split('/');
                let bucketUrls = `${ArrayIndex[3]}`;
                for (let i = 4; i < ArrayIndex.length - 1; i++) {
                    bucketUrls += `/${ArrayIndex[i]}`;
                }
                const fileName = ArrayIndex[ArrayIndex.length - 1];
                const newFileName = fileName.replace(/[^0-9a-zA-Z.]/g, '_');
                ArrayIndex[ArrayIndex.length - 1] = newFileName;
                documentData.url = ArrayIndex.join('/');
                moveFile('/' + fileName, newFileName, bucketUrls);
                await Document.updateOne(
                    { _id: convertToMongoObjectId(_id) },
                    { $set: { url: documentData.url } },
                );
            }
            documentData.url = await getSignedURL(documentData.url);
        }
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_LIST'), documentData),
            );
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('SERVER_ERROR'), { error }));
    }
};

// get DOCUMENT_LIST based on Course Session
exports.getSessionDocuments = async (req, res) => {
    try {
        const {
            query: {
                limit,
                pageNo: page,
                search,
                tab,
                subTab,
                filter,
                institutionCalendarId,
                _program_id,
                year_no,
                level_no,
                rotation,
                rotation_count,
                term,
                type,
                remote,
            },
            params: { userId, courseId, sessionId },
        } = req;
        const { totalDoc, totalPages, currentPage, documentData } =
            await getCourseSessionDocumentListWithFilter(
                userId,
                courseId,
                sessionId,
                limit,
                page,
                search,
                tab,
                subTab,
                institutionCalendarId,
                filter,
                term,
                _program_id,
                year_no,
                level_no,
                rotation,
                rotation_count,
                type,
                remote,
            );
        if (documentData.length === 0) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documentData,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        logger.error(error, 'documentManagerController -> getSessionDocuments -> Error');
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// get DOCUMENT_LIST based on Course Session
exports.getCourseDocumentWithFilter = async (req, res) => {
    try {
        const {
            query: {
                type,
                limit,
                pageNo: page,
                search,
                tab,
                subTab,
                filter,
                institutionCalendarId,
                term,
                _program_id,
                year_no,
                level_no,
                rotation,
                rotation_count,
            },
            params: { userId, courseId },
        } = req;

        const { totalDoc, totalPages, currentPage, documentData } =
            await getCourseDocumentListWithFilter(
                userId,
                courseId,
                limit,
                page,
                search,
                tab,
                subTab,
                institutionCalendarId,
                filter,
                type,
                term,
                _program_id,
                year_no,
                level_no,
                rotation,
                rotation_count,
            );
        if (documentData.length === 0) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    list_all_response_function(
                        res,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documentData,
                    ),
                );
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        logger.error(error, 'documentManagerController -> getCourseDocumentWithFilter -> Error');
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// course params update
exports.courseParamsUpdateDocuments = async (req, res) => {
    try {
        // eslint-disable-next-line no-use-before-define
        const courseParamsUpdateDocuments = await courseParamsUpdate();
        return sendResponse(
            res,
            200,
            true,
            req.t('DATA_UPDATION_MESSAGE'),
            courseParamsUpdateDocuments.msg,
        );
    } catch (error) {
        logger.error(error, 'courseParamsUpdateDocuments -> courseParamsUpdateDocuments -> Error');
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
// check student activity or document available or not
exports.checkActivityOrDocument = async (req, res) => {
    try {
        const {
            body: {
                activityId,
                documentId,
                groupNames,
                type,
                _institution_calendar_id,
                year_no,
                level_no,
                term,
                rotation,
                _course_id,
                rotation_count,
                _program_id,
            },
            params: { userId },
        } = req;
        const data = {
            userId,
            activityId,
            documentId,
            groupNames,
            type,
            _institution_calendar_id,
            year_no,
            level_no,
            term,
            rotation,
            _course_id,
            rotation_count,
            _program_id,
        };

        // eslint-disable-next-line no-use-before-define
        const courseParamsUpdateDocuments = await checkStudentActivityOrDocument(data);
        return sendResponse(res, 200, true, req.t('STATUS_MESSAGE'), courseParamsUpdateDocuments);
    } catch (error) {
        logger.error(error, 'checkActivityOrDocument -> checkActivityOrDocument -> Error');
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

// Get User Course Based Documents
exports.getUserRecentDocuments = async (req, res) => {
    try {
        const {
            query: {
                limit,
                pageNo: page,
                search,
                type,
                subTab,
                tab,
                _institution_calendar_id: queryInstitutionCalendarId,
            },
            params: { userId },
            headers: { _institution_calendar_id: headerInstitutionCalendarId },
        } = req;
        let institutionCalendarId = headerInstitutionCalendarId;
        if (
            queryInstitutionCalendarId &&
            queryInstitutionCalendarId.toString() !== headerInstitutionCalendarId.toString()
        ) {
            institutionCalendarId = queryInstitutionCalendarId;
        }
        logger.info(
            { userId, limit, page, search, tab, subTab, type, institutionCalendarId },
            'documentManager -> getUserRecentDocuments -> User Document List start',
        );
        const { totalDoc, totalPages, currentPage, documents } = await getUserRecentDocumentList(
            userId,
            limit,
            page,
            search,
            tab,
            subTab,
            type,
            institutionCalendarId,
        );
        logger.info(
            { userId, limit, page, search, tab, subTab, type, institutionCalendarId },
            'documentManager -> getUserRecentDocuments -> User Document List end',
        );
        if (documents.length === 0) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NO_DOCUMENTS_FOUND'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        if (limit && page) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DOCUMENT_LIST'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        documents,
                    ),
                );
        }
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('DOCUMENT_LIST'), documents));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
