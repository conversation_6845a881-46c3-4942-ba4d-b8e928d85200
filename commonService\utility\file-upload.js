const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const keys = require('./util_keys');

const maxFileSize = keys.MAX_FILE_SIZE;
const s3 = new AWS.S3({
    accessKeyId: keys.AWS_ACCESS_KEY,
    secretAccessKey: keys.AWS_SECRET_KEY,
});

const s3Oci = new AWS.S3({
    region: keys.OCI_REGION,
    accessKeyId: keys.OCI_ACCESS_KEY_ID,
    secretAccessKey: keys.OCI_SECRET_ACCESS_KEY,
    endpoint: keys.OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = keys.DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const s3FileURL = ({ filePath, fileName }) => {
    return keys.DIGIVAL_CLOUD_PROVIDER === 'OCI'
        ? `${keys.OCI_AWS_S3_API}/${filePath}/${fileName}`
        : `https://s3-${keys.AWS_REGION}.amazonaws.com/${filePath}/${fileName}`;
};

const commonMulterUpload = (options) => {
    const {
        bucketName,
        folderPath = '',
        fileSizeLimit = 40 * 1024 * 1024,
        maxFiles = 1,
        urlFieldName = '',
        storeOriginalName = false,
    } = options;
    const multerConfig = {
        storage: multerS3({
            s3: s3Config,
            bucket: bucketName,
            contentType: multerS3.AUTO_CONTENT_TYPE,
            metadata(req, file, cb) {
                cb(null, { fieldName: file.fieldname });
            },
            key(req, file, cb) {
                const originalname = file.originalname;
                const fieldName = file.fieldname;
                let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
                const fullPath = folderPath !== '' ? `${folderPath}/${fileName}` : fileName;
                cb(null, fullPath);

                fileName = s3FileURL({
                    filePath: bucketName + (folderPath ? `/${folderPath}` : ''),
                    fileName,
                });

                const targetField = urlFieldName !== '' ? urlFieldName : fieldName;
                const fileData = storeOriginalName
                    ? [{ url: fileName, name: originalname }]
                    : fileName;

                if (!req.body[targetField]) {
                    req.body[targetField] =
                        maxFiles > 1
                            ? storeOriginalName
                                ? [{ url: fileName, name: originalname }]
                                : [fileData]
                            : fileData;
                } else if (maxFiles > 1) {
                    req.body[targetField].push(
                        storeOriginalName ? { url: fileName, name: originalname } : fileData,
                    );
                }
            },
        }),
        limits: {
            fileSize: fileSizeLimit,
        },
        fileFilter: (req, file, cb) => {
            if (
                file.mimetype == 'application/vnd.ms-powerpoint' ||
                file.mimetype == 'application/vnd.ms-excel' ||
                file.mimetype ==
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                file.mimetype ==
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                file.mimetype == 'application/msword' ||
                file.mimetype == 'application/pdf' ||
                file.mimetype == 'application/vnd.ms-powerpoint' ||
                file.mimetype ==
                    'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                file.mimetype == 'text/plain' ||
                file.mimetype == 'image/tiff' ||
                file.mimetype == 'image/gif' ||
                file.mimetype == 'image/bmp' ||
                file.mimetype == 'video/mp4' ||
                file.mimetype == 'video/x-ms-wmv' ||
                file.mimetype == 'video/webm' ||
                file.mimetype == 'video/mpeg' ||
                file.mimetype == 'video/quicktime' ||
                file.mimetype == 'video/x-flv' ||
                file.mimetype == 'video/3gpp' ||
                file.mimetype == 'video/x-msvideo' ||
                file.mimetype == 'image/png' ||
                file.mimetype == 'image/jpg' ||
                file.mimetype == 'audio/mpeg' ||
                file.mimetype == 'image/jpeg'
            ) {
                cb(null, true);
            } else {
                return cb(new Error('file type is not allowed'));
            }
        },
    };
    return multer(multerConfig);
};

const uploadDocumentFile = commonMulterUpload({
    bucketName: keys.AWS_BUCKET_NAME_ASSIGNMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const assignmentFilesUpload = commonMulterUpload({
    bucketName: keys.AWS_BUCKET_NAME_ASSIGNMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 2,
});

module.exports = { uploadDocumentFile, assignmentFilesUpload, commonMulterUpload };
