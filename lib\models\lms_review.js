const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const lmsReview = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
        },
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: constant.USER,
        },
        user_type: {
            type: String,
            required: true,
        },
        type: {
            type: String,
            required: true,
        },
        from: {
            type: Date,
            required: true,
        },
        to: {
            type: Date,
            required: true,
        },
        days: {
            type: Number,
        },
        permission_hour: {
            type: Number,
        },
        leave_category: {
            _leave_category_id: Schema.Types.ObjectId,
            name: {
                type: String,
            },
        },
        leave_type: {
            _leave_type_id: Schema.Types.ObjectId,
            name: {
                type: String,
            },
        },
        is_noticed: {
            type: Boolean,
        },
        reason: {
            type: String,
        },
        _leave_reason_doc: String,
        application_date: {
            type: Date,
            // required: true,
        },
        payment_status: {
            type: String,
        },
        session: [
            {
                program: String,
                course: String,
                delivery_type: String,
                session_no: Number,
                _substitute_staff_id: Schema.Types.ObjectId,
                _substitute_staff_name: {
                    first: String,
                    last: String,
                    middle: String,
                    family: String,
                },
            },
        ],
        created_by: {
            _person_id: Schema.Types.ObjectId,
        },
        approved_by: {
            _person_id: Schema.Types.ObjectId,
            date: {
                type: Date,
                // required: true,
            },
        },
        // applied: {
        //     type: String,
        //     default: 'pending'
        // },
        // review: {
        //     type: String,
        //     default: 'pending'
        // },
        // forwarded: {
        //     type: String,
        //     default: 'pending'
        // },
        // approval: {
        //     type: String,
        //     default: 'pending'
        // },
        status: String,
        status_time: {
            type: Date,
            default: Date.now(),
        },
        level_approvers: [
            {
                _person_id: Schema.Types.ObjectId,
                level_no: Number,
                comment_time: {
                    type: Date,
                    default: Date.now(),
                },
                comment: {
                    type: String,
                },
                status: String,
            },
        ],
        comments: [
            {
                _person_id: Schema.Types.ObjectId,
                comment_time: {
                    type: Date,
                    default: Date.now(),
                },
                comment: {
                    type: String,
                },
            },
        ],
        notifyVia: [
            {
                type: String,
            },
        ],
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.LMS_REVIEW, lmsReview);
