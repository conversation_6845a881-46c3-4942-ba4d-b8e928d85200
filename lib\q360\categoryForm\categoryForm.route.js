const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    getProgramList,
    programDetails,
    getInstitution,
    roleUserList,
    createDuplicateForm,
    getCategoryForm,
    updateCategoryForm,
    categoryTypeForm,
    getFormAttachment,
    getApprovalHierarchy,
    singleFormOccurrence,
    getCurriculum,
    createGroups,
    formConfigureList,
    editConfigure,
    concludingPhase,
    termList,
    resetFormCourseSetting,
    qapcGuideResources,
    qapcSignedUrl,
    unPublishedForm,
    getAllInstitutionCalender,
} = require('./categoryForm.controller');
const { qapcGuideResourcesValidator } = require('./categoryForm.validator');
const validator = require('./categoryForm.validator');
const { validate } = require('../../../middleware/validation');
const { userPolicyAuthentication } = require('../../../middleware/policy.middleware');

//program List
router.get(
    '/getProgramList',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getProgramValidator),
    catchAsync(getProgramList),
);
router.get(
    '/programDetails',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.programDetailsValidator),
    catchAsync(programDetails),
);
router.get(
    '/getCurriculum',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getCurriculumValidator),
    catchAsync(getCurriculum),
);
router.get(
    '/getInstitution',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    catchAsync(getInstitution),
);
router.get(
    '/roleUserList',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getProgramValidator),
    catchAsync(roleUserList),
);
router.get(
    '/termList',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getCurriculumValidator),
    catchAsync(termList),
);
//form creation
router.post(
    '/createDuplicateForm',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    validate(validator.createDuplicateFormValidator),
    catchAsync(createDuplicateForm),
);
router.get(
    '/getCategoryForm',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getCategoryFormValidator),
    catchAsync(getCategoryForm),
);
router.get(
    '/categoryTypeForm',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.categoryTypeFormValidator),
    catchAsync(categoryTypeForm),
);
//upload multiple file
router.post('/qapcGuideResources', qapcGuideResourcesValidator, catchAsync(qapcGuideResources));
router.get('/qapcSignedUrl', validate(validator.qapcSignedUrlValidator), catchAsync(qapcSignedUrl));
router.get(
    '/getFormAttachment',
    validate(validator.getFormAttachmentValidator),
    catchAsync(getFormAttachment),
);
router.get(
    '/getApprovalHierarchy',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getFormAttachmentValidator),
    catchAsync(getApprovalHierarchy),
);
router.get(
    '/singleFormOccurrence',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getFormAttachmentValidator),
    catchAsync(singleFormOccurrence),
);
router.put(
    '/updateCategoryForm',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    validate(validator.updateCategoryFormValidator),
    catchAsync(updateCategoryForm),
);
//update the configure
router.post(
    '/createGroups',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    validate(validator.createGroupsValidator),
    catchAsync(createGroups),
);
router.get(
    '/formConfigureList',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.formConfigureListValidator),
    catchAsync(formConfigureList),
);
router.put(
    '/editConfigure',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    validate(validator.editConfigureValidator),
    catchAsync(editConfigure),
);
router.get(
    '/concludingPhase',
    [userPolicyAuthentication(['q360:configure_template:view'])],
    validate(validator.getFormAttachmentValidator),
    catchAsync(concludingPhase),
);
//configure action edit
router.put(
    '/resetFormCourseSetting',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    validate(validator.resetFormCourseSettingValidator),
    catchAsync(resetFormCourseSetting),
);
//isUnpublished form
router.put(
    '/unPublishedForm',
    [userPolicyAuthentication(['q360:configure_template:view', 'q360:configure_template:edit'])],
    catchAsync(unPublishedForm),
);
//HEBA
router.get('/getAllInstitutionCalender', catchAsync(getAllInstitutionCalender));

module.exports = router;
