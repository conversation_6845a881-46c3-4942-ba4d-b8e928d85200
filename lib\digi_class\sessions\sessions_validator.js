const Joi = require('joi');
const common_files = require('../../utility/common');
const constant = require('../../utility/constants');

exports.grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string() /* // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM) */
                        .required(),
                    mode: Joi.string().valid('auto', 'manual').required(),
                    gender: Joi.string()
                        .alphanum()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    group_no: Joi.number().min(1).max(15).when('mode', {
                        is: 'manual',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    method: Joi.string().when('mode', {
                        is: 'auto',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    delivery_group: Joi.array()
                        .items(
                            Joi.object().keys({
                                session_type: Joi.string().required(),
                                group_no: Joi.number().min(1).max(20).required(),
                            }),
                        )
                        .when('mode', {
                            is: 'manual',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        }),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24)).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.rotation_grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    mode: Joi.string().valid('auto', 'manual').required(),
                    gender: Joi.string()
                        .alphanum()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    group_no: Joi.number().min(1).max(15).when('mode', {
                        is: 'manual',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    method: Joi.string().when('mode', {
                        is: 'auto',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24)).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_grouping = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    mode: Joi.string().valid('auto', 'manual').required(),
                    gender: Joi.string()
                        .alphanum()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    method: Joi.string().when('mode', {
                        is: 'auto',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    session_type: Joi.array().items(Joi.string()).when('mode', {
                        is: 'auto',
                        then: Joi.required(),
                        otherwise: Joi.optional(),
                    }),
                    // group_no: Joi.number().min(1).max(20)
                    //     .when('mode', { is: 'auto', then: Joi.required(), otherwise: Joi.optional() }).error(error => {
                    //         return error;
                    //     }),
                    // master_group: Joi.number().min(1).max(20)
                    //     .when('mode', { is: 'manual', then: Joi.required(), otherwise: Joi.optional() }).error(error => {
                    //         return error;
                    //     }),
                    delivery_group: Joi.array()
                        .items(
                            Joi.object().keys({
                                session_type: Joi.string().required(),
                                group_no: Joi.number().min(1).max(20).required(),
                            }),
                        )
                        .when('mode', {
                            is: 'manual',
                            then: Joi.required(),
                            otherwise: Joi.optional(),
                        }),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24)).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    old_group_no: Joi.number().min(1).max(15).required(),
                    new_group_no: Joi.number().min(1).max(15).required(),
                    _student_ids: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string().required(),
                            group_no: Joi.number().min(1).max(20).required(),
                        }),
                    ),
                    _student_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.rotation_course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    master_group: Joi.number().min(1).max(15).required(),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string().required(),
                            group_no: Joi.number().min(1).max(20).required(),
                        }),
                    ),
                    _student_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.elective_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    master_group: Joi.number().min(1).max(15).required(),
                    old_group_no: Joi.number().min(1).max(15).required(),
                    new_group_no: Joi.number().min(1).max(15).required(),
                    _student_ids: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.individual_elective_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    old_group_no: Joi.number().min(1).max(15).required(),
                    new_group_no: Joi.number().min(1).max(15).required(),
                    _student_ids: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.student_group_course_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    course: Joi.string().alphanum().length(24).required(),
                    group_no: Joi.number().min(1).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.individual_course_student_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    course: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.individual_course_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    curriculum: Joi.string().valid('1.0', '2.0', '3.0').required(),
                    level: Joi.number().min(1).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.group_list_student_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_groups_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.student_count_list = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.settings_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    course: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().required(),
                    _user_id: Joi.string().alphanum().length(24).required(),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string().required(),
                                first_name: Joi.string().alphanum().required(),
                                last_name: Joi.string().alphanum().required(),
                                middle_name: Joi.string().alphanum().required(),
                                mark: Joi.string().required(),
                                gender: Joi.string()
                                    .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                                    .required(),
                                // _participation_course_id: Joi.array().items(Joi.string().alphanum().length(24).allow('').error(error => {
                                //     return error;
                                // })),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_import = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    _user_id: Joi.string().alphanum().length(24).required(),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string().required(),
                                first_name: Joi.string().alphanum(),
                                last_name: Joi.string().alphanum(),
                                middle_name: Joi.string().alphanum(),
                                mark: Joi.string().required(),
                                gender: Joi.string().valid(
                                    constant.GENDER.MALE,
                                    constant.GENDER.FEMALE,
                                ),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().alphanum().length(24).required(),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required(),
                level: Joi.string().required(),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required(),
                        no_of_group: Joi.number().min(1).max(20).required(),
                        no_of_students: Joi.number().min(1).max(500).required(),
                    }),
                ),
            }),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.rotation_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().alphanum().length(24).required(),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required(),
                group_name: Joi.string().required(),
                excess_count: Joi.number().min(1).max(20).required(),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required(),
                        group_no: Joi.number().min(1).max(6).required(),
                        no_of_students: Joi.number().min(1).max(500).required(),
                    }),
                ),
            }),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().alphanum().length(24).required(),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required(),
                level: Joi.number().min(1).max(20).required(),
                group_name: Joi.string(),
                _course_id: Joi.string().alphanum().length(24).required(),
                excess_count: Joi.number().min(1).max(20),
                gender: Joi.string()
                    .valid(constant.GENDER.MALE, constant.GENDER.FEMALE, constant.GENDER.BOTH)
                    .required(),
                groups: Joi.array().items(
                    Joi.object().keys({
                        _group_no: Joi.number().min(1).max(20).required(),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string().required(),
                                no_of_group: Joi.number().min(1).max(20).required(),
                                no_of_students: Joi.number().min(1).max(500).required(),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.rotation_course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().alphanum().length(24).required(),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required(),
                group_name: Joi.string().required(),
                _course_id: Joi.string().alphanum().length(24).required(),
                excess_count: Joi.number().min(1).max(20).required(),
                groups: Joi.array().items(
                    Joi.object().keys({
                        _group_no: Joi.number().min(1).max(10).required(),
                        gender: Joi.string()
                            .valid(
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required(),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string().required(),
                                no_of_group: Joi.number().min(1).max(20).required(),
                                no_of_students: Joi.number().min(1).max(500).required(),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.individual_course_group_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _id: Joi.string().alphanum().length(24).required(),
                batch: Joi.string()
                    // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required(),
                level: Joi.string().required(),
                _course_id: Joi.string().alphanum().length(24).required(),
                groups: Joi.array().items(
                    Joi.object().keys({
                        gender: Joi.string()
                            .valid(
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .required(),
                        delivery_type_group: Joi.array().items(
                            Joi.object().keys({
                                delivery: Joi.string().required(),
                                no_of_group: Joi.number().min(1).max(20).required(),
                                no_of_students: Joi.number().min(1).max(500).required(),
                            }),
                        ),
                    }),
                ),
            }),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
exports.data_check_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    _user_id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().required(),
                    student: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string().required(),
                                first_name: Joi.string().alphanum().required(),
                                last_name: Joi.string().alphanum().required(),
                                middle_name: Joi.string().alphanum().required(),
                                mark: Joi.string().required(),
                                gender: Joi.string()
                                    .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                                    .required(),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
exports.year2_data_check_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    year: Joi.number().min(1).max(10).required(),
                    level: Joi.string().required(),
                    curriculum: Joi.string().valid('1.0', '2.0', '3.0').required(),
                    course_id: Joi.string().alphanum().length(24).required(),
                    _user_id: Joi.string().alphanum().length(24).required(),
                    students: Joi.array().items(
                        Joi.object()
                            .keys({
                                academic_no: Joi.string().required(),
                                first_name: Joi.string().alphanum().required(),
                                last_name: Joi.string().alphanum().required(),
                                middle_name: Joi.string().alphanum().required(),
                                mark: Joi.string().required(),
                                gender: Joi.string()
                                    .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                                    .required(),
                            })
                            .unknown(true),
                    ),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
exports.save_import_students_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _program_id: Joi.string().alphanum().length(24).required(),
                    _institution_calendar_id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    year: Joi.number().min(1).max(10).required(),
                    curriculum: Joi.string().valid('1.0', '2.0', '3.0').required(),
                    from_level: Joi.string().required(),
                    to_level: Joi.string().required(),
                    _user_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
exports.get_student_validator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    academic_no: Joi.string().required(),
                    course_id: Joi.string().alphanum().length(24).required(),
                    id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.fyd_course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    master_group: Joi.number().min(1).max(20).required(),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string().required(),
                            group_no: Joi.number().min(1).max(20).required(),
                        }),
                    ),
                    _student_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24)).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.fyd_student_count = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().min(1).max(20).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.fyd_bulk_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24).required()),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.main_dashboard = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string().alphanum().length(24).required(),
                    institution: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.add_student_group = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    mark: Joi.string().required(),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24).required()),
                    _user_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.student_search = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    academic_no: Joi.string().min(1).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.excess_count_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    to: Joi.string().valid('course', 'level').required(),
                    count: Joi.number().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.fyd_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.fyd_group_export = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    batch: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    fyd_group: Joi.string().min(1).max(20).required(),
                    course: Joi.string().alphanum().length(24).required(),
                    delivery: Joi.string().min(1).max(5).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.course_group_change = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                    delivery_group: Joi.array().items(
                        Joi.object().keys({
                            session_type: Joi.string().required(),
                            group_no: Joi.number().min(1).max(20).required(),
                        }),
                    ),
                    _student_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.get_course_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    level: Joi.string().min(1).max(20).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    _course_id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};

exports.rotation_bulk_student_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _id: Joi.string().alphanum().length(24).required(),
                    term: Joi.string()
                        // .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                        .required(),
                    level: Joi.string().min(1).max(20).required(),
                    _student_ids: Joi.array().items(Joi.string().alphanum().length(24).required()),
                })
                .unknown(true),
        })
        .unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.comResponseWithRequest(
                req,
                res,
                422,
                false,
                'validation error',
                err.details[0].message,
            );
        }
        return next();
    });
};
