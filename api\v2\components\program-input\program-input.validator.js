const Joi = require('joi');
const { ARCHIVED, ACTIVE, MASTER_TYPE } = require('../../utility/constants');
const session = require('../setting/setting.schema');
const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
    AM,
    PM,
} = require('../../utility/enums');

const programIdValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const addProgramValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programName: Joi.string()
                .min(3)
                .max(60)
                .required()
                .error(() => {
                    return 'PROGRAM_NAME_REQUIRED';
                }),
            programType: Joi.string().error(() => {
                return 'PROGRAM_TYPE_REQUIRED';
            }),
            _program_type_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'PROGRAM_TYPE_ID_REQUIRED';
                }),
            type: Joi.string()
                .required()
                .valid(MASTER_TYPE.PROGRAM, MASTER_TYPE.PREREQUISITE)
                .error(() => {
                    return 'TYPE_REQUIRED';
                }),
            code: Joi.string()
                .required()
                .error(() => {
                    return 'PROGRAM_CODE_REQUIRED';
                }),
            noOfTerms: Joi.number()
                .integer()
                .required()
                .error(() => {
                    return 'NO_OF_TERMS_REQUIRED';
                }),
            terms: Joi.array()
                .items(
                    Joi.object().keys({
                        termNo: Joi.number()
                            .integer()
                            .required()
                            .error(() => 'TERM_NO_REQUIRED'),
                        termName: Joi.string()
                            .required()
                            .error(() => 'TERM_NAME_REQUIRED'),
                    }),
                )
                .required()
                .error(() => 'TERM_REQUIRED'),
            level: Joi.string().error(() => 'PROGRAM_LEVEL'),
            degree: Joi.string().error(() => 'PROGRAM_DEGREE'),
            _institution_id: Joi.string()
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const editProgramValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            programName: Joi.string()
                .min(3)
                .max(60)
                .required()
                .error(() => {
                    return 'PROGRAM_NAME_REQUIRED';
                }),
            programType: Joi.string().error(() => {
                return 'PROGRAM_TYPE';
            }),
            _program_type_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'PROGRAM_TYPE_ID_REQUIRED';
                }),
            type: Joi.string()
                .required()
                .valid(MASTER_TYPE.PROGRAM, MASTER_TYPE.PREREQUISITE)
                .error(() => {
                    return 'TYPE_REQUIRED';
                }),
            code: Joi.string()
                .required()
                .error(() => {
                    return 'CODE_REQUIRED';
                }),
            noOfTerms: Joi.number()
                .integer()
                .required()
                .error(() => {
                    return 'NO_OF_TERMS_REQUIRED';
                }),
            terms: Joi.array()
                .items(
                    Joi.object().keys({
                        termNo: Joi.number()
                            .integer()
                            .required()
                            .error(() => 'TERM_NO_REQUIRED'),
                        termName: Joi.string()
                            .required()
                            .error(() => 'TERM_NAME_REQUIRED'),
                    }),
                )
                .required()
                .error(() => 'TERM_REQUIRED'),
            level: Joi.string().error(() => 'PROGRAM_LEVEL'),
            degree: Joi.string().error(() => 'PROGRAM_DEGREE'),
            _institution_id: Joi.string()
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const getProgramsValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                limit: Joi.number()
                    .integer()
                    .required()
                    .error(() => 'LIMIT_REQUIRED'),
                pageNo: Joi.number()
                    .integer()
                    .required()
                    .error(() => 'PAGENO_REQUIRED'),
                tab: Joi.string()
                    .valid(ARCHIVED, ACTIVE)
                    .error(() => 'PROGRAM_TAB'),
                programType: Joi.string().error(() => 'PROGRAM_TYPE'),
                searchKey: Joi.string().error(() => 'PROGRAM_SEARCH'),
            })
            .unknown(true),
        params: Joi.object().keys({
            id: Joi.string()
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const addGoalsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
        body: Joi.object().keys({
            content: Joi.string()
                .required()
                .error(() => 'GOAL_CONTENT_VALIDATION'),
            media: Joi.string().error(() => 'MEDIA_URL'),
            _institution_id: Joi.string()
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const addBreakValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            session: Joi.object().keys({
                start: Joi.object().keys({
                    hour: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_START_HOUR_REQUIRED'),
                    minute: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_START_MINUTE_REQUIRED'),
                    format: Joi.string()
                        .required()
                        .error(() => 'SESSION_START_FORMAT_REQUIRED'),
                }),
                end: Joi.object().keys({
                    hour: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_END_HOUR_REQUIRED'),
                    minute: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_END_MINUTE_REQUIRED'),
                    format: Joi.string()
                        .required()
                        .error(() => 'SESSION_END_FORMAT_REQUIRED'),
                }),
            }),
            breaks: Joi.object().keys({
                name: Joi.string()
                    .required()
                    .error(() => 'BREAK_NAME_REQUIRED'),
                days: Joi.array()
                    .required()
                    .items(
                        Joi.string()
                            .required()
                            .valid(MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY),
                    )
                    .error(() => 'BREAK_DAYS_REQUIRED'),
            }),
        }),
    })
    .unknown(true);

const editBreakValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'BREAK_ID_REQUIRED';
                    }),
            })
            .unknown(true),
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            session: Joi.object().keys({
                start: Joi.object().keys({
                    hour: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_START_HOUR_REQUIRED'),
                    minute: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_START_MINUTE_REQUIRED'),
                    format: Joi.string()
                        .required()
                        .error(() => 'SESSION_START_FORMAT_REQUIRED'),
                }),
                end: Joi.object().keys({
                    hour: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_END_HOUR_REQUIRED'),
                    minute: Joi.number()
                        .integer()
                        .required()
                        .error(() => 'SESSION_END_MINUTE_REQUIRED'),
                    format: Joi.string()
                        .required()
                        .error(() => 'SESSION_END_FORMAT_REQUIRED'),
                }),
            }),
            breaks: Joi.object().keys({
                name: Joi.string()
                    .required()
                    .error(() => 'BREAK_NAME_REQUIRED'),
                days: Joi.array()
                    .required()
                    .items(
                        Joi.string()
                            .required()
                            .valid(MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY),
                    )
                    .error(() => 'BREAK_DAYS_REQUIRED'),
            }),
        }),
    })
    .unknown(true);

const removeBreakValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'BREAK_ID_REQUIRED';
                    }),
            })
            .unknown(true),
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const addEventTypeValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            eventType: Joi.object()
                .required()
                .keys({
                    name: Joi.string()
                        .required()
                        .error(() => 'EVENT_TYPE_NAME_REQUIRED'),
                    isLeave: Joi.boolean()
                        .required()
                        .error(() => 'EVENT_IS_LEAVE_REQUIRED'),
                }),
        }),
    })
    .unknown(true);

const updateTimeZoneValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            timeZone: Joi.string()
                .required()
                .error(() => 'TIME_ZONE_REQUIRED'),
        }),
    })
    .unknown(true);

const updateGenderSegregationValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            isGenderSegregation: Joi.boolean()
                .required()
                .error(() => 'GENDER_SEGREGATION_REQUIRED'),
        }),
    })
    .unknown(true);

const editEventTypeValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'EVENT_ID_REQUIRED'),
        }),
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            eventType: Joi.object()
                .required()
                .keys({
                    name: Joi.string()
                        .required()
                        .error(() => 'EVENT_TYPE_NAME_REQUIRED'),
                    isLeave: Joi.boolean()
                        .required()
                        .error(() => 'EVENT_IS_LEAVE_REQUIRED'),
                }),
        }),
    })
    .unknown(true);

const removeEventTypeValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'EVENT_ID_REQUIRED'),
        }),
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const updateCurriculumValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'CURRICULUM_ID_REQUIRED'),
        }),
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const updateCreditHoursValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'CREDIT_HOURS_ID_REQUIRED'),
        }),
        body: Joi.object()
            .keys({
                programId: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => 'PROGRAM_ID_REQUIRED'),
                withoutLevel: Joi.boolean().error(() => 'WITHOUT_LEVEL_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);

const deleteParentInstituteBreakValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const deleteParentInstituteEventValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const getProgramInstitutionSettingsValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'EVENT_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const deleteProgramGoalsOrObjectivesValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const deleteProgramPortfolioValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
            programId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PROGRAM_ID_REQUIRED'),
            portfolioId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'PORTFOLIO_ID_VALIDATION'),
        }),
    })
    .unknown(true);

module.exports = {
    editProgramValidator,
    addProgramValidator,
    getProgramsValidator,
    programIdValidator,
    addBreakValidator,
    editBreakValidator,
    removeBreakValidator,
    updateGenderSegregationValidator,
    updateTimeZoneValidator,
    addEventTypeValidator,
    editEventTypeValidator,
    removeEventTypeValidator,
    updateCurriculumValidator,
    updateCreditHoursValidator,
    deleteParentInstituteBreakValidator,
    deleteParentInstituteEventValidator,
    addGoalsValidator,
    getProgramInstitutionSettingsValidator,
    deleteProgramGoalsOrObjectivesValidator,
    deleteProgramPortfolioValidator,
};
