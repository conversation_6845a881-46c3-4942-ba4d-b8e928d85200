const { convertToMongoObjectId, query } = require('../../../utility/common');
const { LOCAL, DIGI_PROGRAM, DIGI_COURSE, ROLE_ASSIGNS } = require('../../../utility/constants');
// const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const programSchema = require('mongoose').model(DIGI_PROGRAM);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGNS);
const {
    programRoleBasedFormatting,
    roleListFormatting,
} = require('../../serviceAdapter/adapter.formatter');

const getProgramList = async ({ _institution_id, staffId }) => {
    try {
        // let roleAssignedProgramList;
        // let courseProgramList;
        // if (BASIC_DATA_FROM === LOCAL) {
        const roleAssignedProgramList = await roleAssignSchema
            .find(
                {
                    ...query,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _user_id: convertToMongoObjectId(staffId),
                },
                { roles: 1 },
            )
            .lean();
        const courseProgramList = await courseSchema
            .find({
                ...query,
                _institution_id: convertToMongoObjectId(_institution_id),
                'coordinators._user_id': convertToMongoObjectId(staffId),
            })
            .lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return programRoleBasedFormatting({
            roleAssignedProgramList,
            courseProgramList,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getRoleModuleList = async ({ _institution_id, staffId }) => {
    try {
        // let roleAssignedProgramList;
        // if (BASIC_DATA_FROM === LOCAL) {
        const roleAssignedProgramList = await roleAssignSchema
            .find(
                {
                    ...query,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _user_id: convertToMongoObjectId(staffId),
                },
                { 'roles._role_id': 1 },
            )
            .populate('roles._role_id')
            .lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return roleListFormatting({
            roleAssignedProgramList,
            //  dataFrom: BASIC_DATA_FROM
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    getProgramList,
    getRoleModuleList,
};
