let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let notification_manager = new Schema({
    notification_type: {
        type: String,

    },
    notification_priority: {
        type: String,
        enum: [constant.NOTIFICATION_PRIORITY.LOW, constant.NOTIFICATION_PRIORITY.MEDIUM, constant.NOTIFICATION_PRIORITY.HIGH],

    },
    title: {
        type: String,

    },
    // description: {
    //     type: String,
    //     
    // },
    notification_dates: {
        start_date: {
            type: Date,

        },
        start_time: {
            type: Date,

        },
        end_date: {
            type: Date,

        },
        end_time: {
            type: Date,

        }
    },
    _master_id: {
        type: Schema.Types.ObjectId
    },
    web_link: {
        type: String,

    },
    _notify_to_id: [{
        type: Schema.Types.ObjectId,

    }],
    _infra_id: {
        type: Schema.Types.ObjectId,

    },
    isDeleted: {
        type: <PERSON>olean,
        default: false
    },
    isActive: {
        type: <PERSON>olean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.NOTIFICATIONS, notification_manager);