const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { QAPC_ROLE, TAG_LEVEL, INSTITUTION } = require('../../utility/constants');

const qapcRoleSchema = new Schema(
    {
        roleName: { type: String },
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        levels: [
            { type: String, enum: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION] },
        ],
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_ROLE, qapcRoleSchema);
