const mongoose = require('mongoose');
const { Schema } = mongoose;
const { INSTITUTION, PROGRAM } = require('../../utility/constants');

const sessionTypesSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: PROGRAM,
        },
        programName: String,
        sessionName: {
            type: String,
        },
        sessionSymbol: {
            type: String,
        },
        contactHoursOrPeriodOfWeek: {
            type: Number,
        },
        sessionOrPeriodDuration: {
            type: Number,
        },
        creditHoursMode: {
            type: String,
        },
        deliveryTypes: [
            {
                deliveryName: {
                    type: String,
                },
                deliverySymbol: {
                    type: String,
                },
                deliveryDuration: {
                    type: Number,
                },
                isDeleted: {
                    type: Boolean,
                    default: false,
                },
                isActive: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);

module.exports = sessionTypesSchema;
