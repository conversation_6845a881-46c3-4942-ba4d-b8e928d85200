const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('../models/student_group');
// const program = require('mongoose').model(constant.PROGRAM);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const institution = require('mongoose').model(constant.INSTITUTION);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
// const course = require('mongoose').model(constant.COURSE);
const course = require('mongoose').model(constant.DIGI_COURSE);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
// const session_order = require('mongoose').model(constant.SESSION_ORDER);
// const session_type = require('mongoose').model(constant.SESSION_TYPE);
const session_type = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
// const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const { updateStudentGroupFlatCacheData } = require('./student_group_services');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
exports.rotation_course_setting_get = async (req, res) => {
    try {
        const query = {
            _id: ObjectId(req.params.id),
            'master.term': req.params.batch,
            isDeleted: false,
        };
        const project = {};
        const doc = await base_control.get(student_group, query, project);

        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );

        const ic_get = await base_control.get(
            institution_calendar,
            { _id: ObjectId(doc.data._institution_calendar_id) },
            { calendar_name: 1 },
        );
        const p_get = await base_control.get(
            program,
            { _id: ObjectId(doc.data.master._program_id) },
            { name: 1 },
        );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params.course) },
            { courses_name: 1, courses_number: 1, model: 1 },
        );
        const course_session_order = await base_control.get_list(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params.course) },
            {},
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {
                /* _program_id: ObjectId(doc.data.master._program_id) */
            },
            {},
        );

        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        // const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];

        const group_name =
            ic_get.data.calendar_name +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P' +
            '-' +
            (req.params.batch ? 'RT' : 'IT') +
            '-' +
            doc.data.master.year +
            'Y' +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P:' +
            doc.data.master.curriculum +
            '-' +
            doc.data.master.level +
            'L-' +
            course_get.data.courses_number;

        const delivery = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        // symbol.forEach((element) => {
        //     const delivery_data =
        //         delivery_type.data[
        //             delivery_type.data.findIndex((i) => i.delivery_symbol === element)
        //         ];
        //     delivery.push({
        //         delivery_type: delivery_data.session_type,
        //         delivery_name: delivery_data.delivery_type,
        //         delivery_symbol: delivery_data.delivery_symbol,
        //     });
        // });
        // delivery.forEach((element) => {
        //     if (element.delivery_type === 'Theory')
        //         theory.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Practical')
        //         practical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Clinical')
        //         clinical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        // });
        // const session_dev_type = {
        //     Theory: theory,
        //     Practical: practical,
        //     Clinical: clinical,
        // };
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            session_names.push(delivery_data.session_name);
            delivery.push({
                session_type: delivery_data.session_name,
                delivery: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        const session_delivery = [];
        session_names = [...new Set(session_names)];
        session_names.forEach((element) => {
            const del = [];
            delivery.forEach((sub_element) => {
                if (sub_element.session_type === element)
                    del.push({
                        delivery_name: sub_element.delivery,
                        symbol: sub_element.symbol,
                    });
            });
            session_delivery.push({ session_type: element, delivery: del });
        });
        // if (course_get.data.model === constant.MODEL.ELECTIVE) {
        //     Object.assign(session_dev_type, { 'elective': '-' });
        // }
        let male_count = 0;
        let female_count = 0;
        const objs = {
            _id: doc.data._id,
            master: doc.data.master,
            group_name,
            session_type: session_delivery,
            course: course_get.data,
            setting:
                doc.data.courses[
                    doc.data.courses.findIndex(
                        (i) => i._course_id.toString() === req.params.course.toString(),
                    )
                ].setting,
            // setting: doc.data.rotation_group_setting
        };
        const groups_settings = [];
        doc.data.rotation_group_setting.forEach((element) => {
            male_count = 0;
            female_count = 0;
            element._student_ids.forEach((sub_element) => {
                const std_data =
                    doc.data.students[
                        doc.data.students.findIndex(
                            (i) => i._student_id.toString() === sub_element.toString(),
                        )
                    ];
                if (std_data.gender === constant.GENDER.MALE) {
                    male_count++;
                } else {
                    female_count++;
                }
            });
            groups_settings.push({
                group_no: element.group_no,
                group_name: element.group_name,
                gender: element.gender,
                male_count,
                female_count,
            });
        });
        // doc.data.ungrouped.forEach(element => {
        //     let std_data = doc.data.students[doc.data.students.findIndex(i => (i._student_id).toString() === (element).toString())];
        //     if (std_data.gender === constant.GENDER.MALE) {
        //         male_count++;
        //     } else { female_count++; }
        // });
        // Object.assign(objs, { 'male_count': male_count, 'female_count': female_count })
        Object.assign(objs, { rotation_setting: groups_settings });
        Object.assign(objs, { rotation_count: doc.data.rotation_count });
        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_SETTING_PAGE'), objs);
    } catch (error) {
        common_files.com_response(res, 500, false, req.t('CATCH_ERROR'), error.toString());
    }
};
/*
exports.rotation_course_group_setting = async (req, res) => {
    let query = { '_id': ObjectId(req.body._id), 'master.term': req.body.batch, 'isDeleted': false };
    let project = {};
    let ind_group_setting = [], groups_setting = [], g_type = [];
    let student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        // let student_groups = student_group_data.data.group_setting[student_group_data.data.group_setting.findIndex(i => i.gender === req.body.gender)];
        req.body.groups.forEach(element => {
            g_type = [];
            let name = req.body.group_name + '-G' + element._group_no;
            let gs_obj = {
                _group_no: element._group_no,
                gender: element.gender,
                excess_count: req.body.excess_count,
                ungrouped: student_group_data.data.rotation_group_setting[student_group_data.data.rotation_group_setting.findIndex(i => i.group_no === element._group_no)]._student_ids
            }
            element.delivery_type_group.forEach(sub_element => {
                ind_group_setting = [];
                for (let i = 1; i <= sub_element.no_of_group; i++) {
                    let igs_obj = {
                        group_no: i,
                        group_name: name + '-' + sub_element.delivery + '-' + i.toString()
                    }
                    ind_group_setting.push(igs_obj);
                }
                let ses_setting = {
                    group_name: name + '-' + sub_element.delivery,
                    session_type: sub_element.delivery,
                    no_of_group: sub_element.no_of_group,
                    no_of_student: sub_element.no_of_students,
                    groups: ind_group_setting
                };
                g_type.push(ses_setting);
            });
            Object.assign(gs_obj, { session_setting: g_type });
            groups_setting.push(gs_obj);
        });
        objs = { $set: { 'courses.$[i].setting': groups_setting } };
        filter = {
            arrayFilters: [{ 'i._course_id': req.body._course_id }]
        };
        // doc = { status: false, data: groups_setting }
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        if (doc.status) {
            common_files.com_response(res, 200, true, "Students Course group setting is created", "Students Course group setting is created");
        } else {
            common_files.com_response(res, 404, false, "Unable to Set Course Group Setting", doc.data);
        }
    } else {
        common_files.com_response(res, 404, false, "Error id not match", 'CHECK_PARSING_REFERENCE_ID');
    }
}
 */
exports.course_group_list_filter = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const objs = {
            _id: doc.data._id,
            _institution_calendar_id: doc.data._institution_calendar_id,
            group_name: doc.data.group_name,
            master: doc.data.master,
        };
        // let students = [];
        // let ung_count = 0, all_std = 0;
        const group_student_list = [];
        let all_student_id = [];
        const course_datas = [];
        doc.data.courses.forEach((element) => {
            course_datas.push({
                _course_id: element._course_id,
                course_name: element.course_name,
                course_no: element.course_no,
                grouped: [],
                ungrouped: [],
            });
        });
        Object.assign(objs, { course: doc.data.courses });
        const g_list = {};
        doc.data.group_setting.forEach((element) => {
            if (element.gender === req.params.gender) {
                element.groups.forEach((sub_element) => {
                    const gc = 'group' + sub_element.group_no;
                    g_list[gc] = sub_element._student_ids.length;
                    all_student_id = all_student_id.concat(sub_element._student_ids);
                    group_student_list.push({
                        title: gc,
                        group_no: sub_element.group_no,
                        course: course_datas,
                        students_id: sub_element._student_ids,
                    });
                });
            }
        });
        Object.assign(objs, { groups_list: g_list });
        group_student_list.forEach((element, index) => {
            element.course.forEach((sub_element, sub_index) => {
                const c_data =
                    doc.data.courses[
                        doc.data.courses.findIndex(
                            (i) => i._course_id.toString() === sub_element._course_id.toString(),
                        )
                    ];
                const setting_index = c_data.setting.findIndex(
                    (i) => i._group_no.toString() === element.group_no.toString(),
                );
                if (setting_index !== -1) {
                    const session_setting = c_data.setting[setting_index].session_setting;
                    // const session_type = session_setting.map((i) => i.session_type);
                    const student = [];
                    c_data.setting[setting_index].ungrouped.forEach((ungroup_element) => {
                        const temp =
                            doc.data.students[
                                doc.data.students.findIndex(
                                    (i) => i._student_id.toString() === ungroup_element.toString(),
                                )
                            ];
                        const data = {};
                        let course_session_index = -1;
                        session_setting.forEach((session_element) => {
                            session_element.groups.forEach((group_element) => {
                                if (
                                    group_element._student_ids.findIndex(
                                        (i) => i === temp._student_id,
                                    ) !== -1
                                ) {
                                    course_session_index = group_element._student_ids.findIndex(
                                        i === temp._student_id,
                                    );
                                }
                            });
                            data[session_element.session_type.toString()] =
                                course_session_index !== -1 ? course_session_index : '-';
                        });
                        // session_type.forEach(session_element => {
                        //     data[(session_element.toString())] = '-';
                        // });
                        data._student_id = temp._student_id;
                        data.name = temp.name;
                        data.academic_no = temp.academic_no;
                        data.gender = temp.gender;
                        data.mark = temp.mark;
                        data.imported_on = temp.imported_on;
                        data.imported_by = temp.imported_by;
                        student.push(data);
                    });
                    group_student_list[index].course[sub_index].ungrouped = student;
                }
            });
        });
        Object.assign(objs, { group_student_list });
        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_DATA'), objs);
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_UNABLE_TO_GET_LIST'),
            req.t('UNABLE_TO_GET_LIST'),
        );
    }
};

exports.rotation_course_grouped_list_filter = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const objs = {
            _id: doc.data._id,
            _institution_calendar_id: doc.data._institution_calendar_id,
            group_name: doc.data.group_name,
            master: doc.data.master,
        };
        const group_student_list = [];
        let all_student_id = [];
        const course_datas = [];
        doc.data.courses.forEach((element) => {
            const settings =
                element.setting[
                    element.setting.findIndex(
                        (i) => i._group_no.toString() === req.params.group.toString(),
                    )
                ];
            course_datas.push({
                _course_id: element._course_id,
                course_name: element.course_name,
                course_no: element.course_no,
                course_type: element.course_type,
                versionNo: element.versionNo || 1,
                versioned: element.versioned || false,
                versionName: element.versionName || '',
                versionedFrom: element.versionedFrom || null,
                setting: settings,
                grouped: [],
                ungrouped: [],
            });
        });
        const g_list = {};
        doc.data.rotation_group_setting.forEach((element) => {
            if (element.gender === req.params.gender) {
                const gc = 'group' + element.group_no;
                g_list[gc] = element._student_ids.length;
            }
        });
        group_student_list.push({
            title: 'group' + req.params.group,
            group_no: req.params.group,
            course: course_datas,
        });
        course_datas.forEach((sub_element, sub_index) => {
            if (sub_element.setting && sub_element.setting.session_setting) {
                const session_setting = sub_element.setting.session_setting;
                const session_types = session_setting.map((i) => i.session_type);
                let student = [];
                sub_element.setting.ungrouped.forEach((ungroup_element) => {
                    const temp =
                        doc.data.students[
                            doc.data.students.findIndex(
                                (i) => i._student_id.toString() === ungroup_element.toString(),
                            )
                        ];
                    const data = {};
                    session_types.forEach((session_element) => {
                        data[session_element.toString()] = '-';
                    });
                    data._student_id = temp._student_id;
                    data.name = temp.name;
                    data.academic_no = temp.academic_no;
                    data.gender = temp.gender;
                    data.mark = temp.mark;
                    data.imported_on = temp.imported_on;
                    data.imported_by = temp.imported_by;
                    student.push(data);
                });
                group_student_list[0].course[sub_index].ungrouped = student;
                student = [];
                all_student_id = [];
                session_setting.forEach((ses_element) => {
                    ses_element.groups.forEach((group_element) => {
                        all_student_id = all_student_id.concat(group_element._student_ids);
                    });
                });
                const temps = [];
                all_student_id.forEach((element) => {
                    if (temps.indexOf(element.toString()) === -1) {
                        temps.push(element.toString());
                    }
                });
                all_student_id = temps;

                all_student_id = all_student_id.filter(
                    (item) => !sub_element.setting.ungrouped.includes(item.toString()),
                );
                all_student_id.forEach((master_group_element) => {
                    const temp =
                        doc.data.students[
                            doc.data.students.findIndex(
                                (i) => i._student_id.toString() === master_group_element.toString(),
                            )
                        ];
                    const data = {};
                    let course_session_index = -1;
                    session_setting.forEach((session_element) => {
                        session_element.groups.forEach((group_element) => {
                            if (
                                group_element._student_ids.findIndex(
                                    (i) => i.toString() === master_group_element.toString(),
                                ) !== -1
                            ) {
                                course_session_index = group_element.group_no;
                            }
                        });
                        data[session_element.session_type.toString()] =
                            course_session_index !== -1 ? course_session_index : '-';
                    });
                    data._student_id = temp._student_id;
                    data.name = temp.name;
                    data.academic_no = temp.academic_no;
                    data.gender = temp.gender;
                    data.mark = temp.mark;
                    data.imported_on = temp.imported_on;
                    data.imported_by = temp.imported_by;
                    student.push(data);
                });
                group_student_list[0].course[sub_index].grouped = student;
                delete group_student_list[0].course[sub_index].setting;
            }
        });
        Object.assign(objs, { groups_list: g_list });
        Object.assign(objs, { group_student_list });
        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_DATA'), objs);
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_UNABLE_TO_GET_LIST'),
            req.t('UNABLE_TO_GET_LIST'),
        );
    }
};

exports.rotation_course_grouping_data = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const group_list = [];
        const course_ind = doc.data.courses.findIndex(
            (i) => i._course_id.toString() === req.params.course.toString(),
        );
        const master_group_ind = doc.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.params.group_no.toString(),
        );
        const course_session_setting =
            doc.data.courses[course_ind].setting[master_group_ind].session_setting;
        course_session_setting.forEach((element) => {
            const session = [];
            let obj = {};
            element.groups.forEach((sub_element) => {
                const spli = sub_element.group_name.split('-');
                obj = {
                    group_no: sub_element.group_no,
                    group_name: sub_element.group_name,
                    group: spli[spli.length - 2] + '-' + spli[spli.length - 1],
                    total: element.no_of_student,
                    occupied: sub_element._student_ids.length,
                };
                session.push(obj);
            });
            group_list.push({
                group_name: element.group_name,
                session_type: element.session_type,
                group: session,
            });
        });
        common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_SETTING_PAGE'), group_list);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.sub_course_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc = { status: true, data: [] };
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const master_group_ind = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.body.master_group.toString(),
        );
        const course_session_setting =
            student_group_data.data.courses[course_ind].setting[master_group_ind].session_setting;

        req.body.delivery_group.forEach(async (element, index) => {
            const session =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session.groups.forEach((sub_element) => {
                if (sub_element._student_ids.findIndex((i) => i === req.body._student_id) !== -1) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': {
                            $in: req.body._student_id,
                        },
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
                    { 'k.session_type': element.session_type },
                    { 'l.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (doc.status) {
                    updateStudentGroupFlatCacheData();
                    await updateStudentGroupRedisKey({
                        courseId: req.body._course_id,
                        level: req.body.level,
                        batch: req.body.batch,
                    });
                    common_files.com_response(
                        res,
                        200,
                        true,
                        req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                        doc.data,
                    );
                } else {
                    common_files.com_response(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.rotation_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        let filter;
        objs = {
            $pull: {
                'rotation_group_setting.$[i]._student_ids': req.body._student_ids,
            },
            $push: {
                'rotation_group_setting.$[j]._student_ids': req.body._student_ids,
            },
        };
        filter = {
            arrayFilters: [
                { 'i.group_no': req.body.old_group_no, 'i.gender': req.body.gender },
                { 'j.group_no': req.body.new_group_no, 'j.gender': req.body.gender },
            ],
        };
        // Need to remove student in course groups
        let doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        student_group_data.data.courses.forEach(async (element) => {
            const gro =
                element.setting[
                    element.setting.findIndex(
                        (i) => i._group_no.toString() === req.body.old_group_no.toString(),
                    )
                ];
            if (gro) {
                if (gro.ungrouped.indexOf(req.body._student_ids) !== -1) {
                    objs = {
                        $pull: {
                            'courses.$[i].setting.$[j].ungrouped': req.body._student_ids,
                        },
                        $push: {
                            'courses.$[i].setting.$[k].ungrouped': req.body._student_ids,
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i._course_id': element._course_id },
                            { 'j._group_no': req.body.old_group_no },
                            { 'k._group_no': req.body.new_group_no },
                        ],
                    };
                    doc = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                } else {
                    gro.session_setting.forEach(async (sub_element) => {
                        sub_element.groups.forEach(async (group_element) => {
                            const stud_id =
                                group_element._student_ids[
                                    group_element._student_ids.findIndex(
                                        (i) => i === req.body._student_ids,
                                    )
                                ];
                            if (stud_id) {
                                objs = {
                                    $pull: {
                                        'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                                            req.body._student_ids,
                                    },
                                };
                                filter = {
                                    arrayFilters: [
                                        { 'i._course_id': element._course_id },
                                        { 'j._group_no': gro._group_no },
                                        { 'k.session_type': sub_element.session_type },
                                        { 'l.group_no': group_element.group_no },
                                        { 'm._group_no': req.body.new_group_no },
                                    ],
                                };
                                doc = await base_control.update_condition_array_filter(
                                    student_group,
                                    query,
                                    objs,
                                    filter,
                                );
                            }
                        });
                    });
                    objs = {
                        $push: { 'courses.$[i].setting.$[m].ungrouped': req.body._student_ids },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i._course_id': element._course_id },
                            { 'm._group_no': req.body.new_group_no },
                        ],
                    };
                    doc = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }
            }
        });
        if (doc.status) {
            updateStudentGroupFlatCacheData();
            await updateStudentGroupRedisKey({
                courseId: req.body._course_id,
                level: req.body.level,
                batch: req.body.batch,
            });
            common_files.com_response(
                res,
                200,
                true,
                req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                doc.data,
            );
        } else {
            common_files.com_response(
                res,
                404,
                false,
                req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
            );
        }
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.elective_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc = { status: true, data: [] };
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const master_group_ind = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.body.master_group.toString(),
        );
        const course_session_setting =
            student_group_data.data.courses[course_ind].setting[master_group_ind].session_setting;

        course_session_setting.forEach(async (element, index) => {
            objs = {
                $pull: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': {
                        $in: req.body._student_id,
                    },
                },
                $push: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[m]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
                    { 'k.session_type': element.session_type },
                    { 'l.group_no': req.body.old_group_no },
                    { 'm.group_no': req.body.new_group_no },
                ],
            };
            // ars.push({ objs: objs, filter: filter });
            // doc = { status: true, data: ars };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (course_session_setting.length === index + 1) {
                if (doc.status) {
                    updateStudentGroupFlatCacheData();
                    await updateStudentGroupRedisKey({
                        courseId: req.body._course_id,
                        level: req.body.level,
                        batch: req.body.batch,
                        rotationCount: req.body.master_group,
                    });
                    common_files.com_response(
                        res,
                        200,
                        true,
                        req.t('STUDENTS_ARE_GROUPED_BASED_ON_METHOD'),
                        doc.data,
                    );
                } else {
                    common_files.com_response(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                        req.t('ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY'),
                    );
                }
            }
        });
        // req.body.delivery_group.forEach(async (element, index) => {
        //     let session = course_session_setting[course_session_setting.findIndex(i => i.session_type === element.session_type)];
        //     let group_index = 0;
        //     session.groups.forEach(sub_element => {
        //         if (sub_element._student_ids.findIndex(i => i === req.body._student_id) !== -1) {
        //             group_index = sub_element.group_no;
        //         }
        //     });
        //     if (group_index !== -1) {
        //         objs = {
        //             $pull: { 'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': { $in: req.body._student_id } }
        //         };
        //         filter = {
        //             arrayFilters: [
        //                 { 'i._course_id': req.body._course_id },
        //                 { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
        //                 { 'k.session_type': element.session_type },
        //                 { 'l.group_no': group_index }
        //             ]
        //         };
        //         doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        //     }
        //     objs = {
        //         $push: { 'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': req.body._student_id }
        //     };
        //     filter = {
        //         arrayFilters: [
        //             { 'i._course_id': req.body._course_id },
        //             { 'j._group_no': req.body.master_group, 'j.gender': req.body.gender },
        //             { 'k.session_type': element.session_type },
        //             { 'l.group_no': element.group_no }
        //         ]
        //     };
        //     doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        //     if (req.body.delivery_group.length === (index + 1)) {
        //         if (doc.status) {
        //             common_files.com_response(res, 200, true, "Students are Grouped based on Method", doc.data);
        //         } else {
        //             common_files.com_response(res, 404, false, "Error Unable to Push Student into Group Pls retry", 'ERROR_UNABLE_TO_PUSH_STUDENT_INTO_GROUP_PLS_RETRY');
        //         }
        //     }
        // });
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.get_rotation_course_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const settings = [];
        const strength = 0;
        const obj = {};
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.level === req.params.level && i.term === req.params.batch,
        );
        if (student_group_level === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.params._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        // const ic_get = await base_control.get(
        //     institution_calendar,
        //     { _id: ObjectId(student_group_data.data._institution_calendar_id) },
        //     { calendar_name: 1 },
        // );
        // const p_get = await base_control.get(
        //     program,
        //     { _id: ObjectId(student_group_data.data.master._program_id) },
        //     { name: 1 },
        // );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params._course_id) },
            { courses_name: 1, courses_number: 1 },
        );
        const course_session_order = await base_control.get_list(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params._course_id) },
            { delivery_symbol: 1 },
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {
                /* _program_id: ObjectId(student_group_data.data.master._program_id) */
            },
            { session_type: 1, delivery_type: 1, delivery_symbol: 1 },
        );
        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];
        // Getting program details
        // let group_name = ic_get.data.calendar_name + '-'
        //     + p_get.data.name.substring(0, 1) + 'P' + '-'
        //     + ((req.params.batch) ? 'RT' : 'IT') + '-' + 'Y' +
        //     + student_group_data.data.master.year + '-' + 'L' +
        //     + student_group_data.data.groups[student_group_level].level + '-' + course_get.data.courses_number;
        Object.assign(obj, { program: student_group_data.data.master.program_name });
        Object.assign(obj, { year: student_group_data.data.master.year });
        Object.assign(obj, { level: student_group_data.data.groups[student_group_level].level });
        Object.assign(obj, { course_number: course_get.data.courses_number });
        Object.assign(obj, {
            group_name: student_group_data.data.groups[student_group_level].group_name,
        });
        // Getting session type details
        const delivery = [];
        const theory = [];
        const practical = [];
        const clinical = [];
        symbol.forEach((element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex((i) => i.delivery_symbol === element)
                ];
            delivery.push({
                delivery_type: delivery_data.session_type,
                delivery_name: delivery_data.delivery_type,
                delivery_symbol: delivery_data.delivery_symbol,
            });
        });
        delivery.forEach((element) => {
            if (element.delivery_type === 'Theory')
                theory.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
            if (element.delivery_type === 'Practical')
                practical.push({
                    delivery: element.delivery_name,
                    symbol: element.delivery_symbol,
                });
            if (element.delivery_type === 'Clinical')
                clinical.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
        });
        const session_dev_type = {
            theory,
            practical,
            clinical,
        };
        Object.assign(obj, { session_types: session_dev_type });
        // Getting group list and total count details
        const groups_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting;
        gender_setting = groups_setting.filter(
            (item) => req.params.gender === item.gender.toString(),
        );
        const student_group_no = gender_setting.findIndex(
            (i) => i._group_no.toString() === req.params.group_no.toString(),
        );
        const groups = [];
        const std_ids = [];
        const id = [];
        if (student_group_no !== -1) {
            const count =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_no].ungrouped;
            const strength_count = count.length;
            const student_group_no_session =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_no].session_setting;
            for (let i = 0; i < student_group_no_session.length; i++) {
                const element = student_group_no_session[i].groups;
                groups.push(element);
            }
            groups.forEach((elements) => {
                elements.forEach((sub_element) => {
                    const ids = sub_element._student_ids;
                    std_ids.push(ids);
                });
            });
            std_ids.forEach((element) => {
                element.forEach((ele) => {
                    const std_id = ele;
                    id.push(std_id.toString());
                });
            });
            const res_data = [...new Set(id)];
            Object.assign(obj, { count: strength_count + res_data.length });
            student_group_no_session.forEach((element) => {
                const grp = element.groups;
                if (grp.length !== 0) {
                    const gro = element.groups.map((i) => i.group_name);
                    const objs = {
                        session_symbol: element.session_type,
                        no_of_groups: element.no_of_group,
                        no_of_student: element.no_of_student,
                        group_name: gro,
                    };
                    settings.push(objs);
                    Object.assign(obj, { group: settings });
                } else {
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('NO_GROUPS_CREATED'),
                                req.t('FIRST_CREATE_GROUPS'),
                            ),
                        );
                }
            });
        } else {
            Object.assign(obj, { count: strength });
            Object.assign(obj, { group: settings });
        }
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('SAVED_GROUPS'), obj));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.get_rotation_elective_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const settings = [];
        const strength = 0;
        const obj = {};
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.level === req.params.level && i.term === req.params.batch,
        );
        if (student_group_level === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.params._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        // const ic_get = await base_control.get(
        //     institution_calendar,
        //     { _id: ObjectId(student_group_data.data._institution_calendar_id) },
        //     { calendar_name: 1 },
        // );
        // const p_get = await base_control.get(
        //     program,
        //     { _id: ObjectId(student_group_data.data.master._program_id) },
        //     { name: 1 },
        // );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params._course_id) },
            { courses_name: 1, courses_number: 1, model: 1 },
        );
        const course_session_order = await base_control.get_list(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params._course_id) },
            {},
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {
                /* _program_id: ObjectId(student_group_data.data.master._program_id) */
            },
            {},
        );
        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        // const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];
        // Getting program details
        // let group_name = ic_get.data.calendar_name + '-'
        //     + p_get.data.name.substring(0, 1) + 'P' + '-'
        //     + ((req.params.batch) ? 'RT' : 'IT') + '-' + 'Y' +
        //     + student_group_data.data.master.year + '-' + 'L' +
        //     + student_group_data.data.groups[student_group_level].level + '-' + course_get.data.courses_number;
        Object.assign(obj, { program: student_group_data.data.master.program_name });
        Object.assign(obj, { year: student_group_data.data.master.year });
        Object.assign(obj, { level: student_group_data.data.groups[student_group_level].level });
        Object.assign(obj, { course_number: course_get.data.courses_number });
        Object.assign(obj, {
            group_name: student_group_data.data.groups[student_group_level].group_name,
        });
        // Getting session type details
        const delivery = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        // symbol.forEach((element) => {
        //     const delivery_data =
        //         delivery_type.data[
        //             delivery_type.data.findIndex((i) => i.delivery_symbol === element)
        //         ];
        //     delivery.push({
        //         delivery_type: delivery_data.session_type,
        //         delivery_name: delivery_data.delivery_type,
        //         delivery_symbol: delivery_data.delivery_symbol,
        //     });
        // });
        // delivery.forEach((element) => {
        //     if (element.delivery_type === 'Theory')
        //         theory.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Practical')
        //         practical.push({
        //             delivery: element.delivery_name,
        //             symbol: element.delivery_symbol,
        //         });
        //     if (element.delivery_type === 'Clinical')
        //         clinical.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
        // });
        // const session_dev_type = {
        //     // "elective": course_get.data.model === 'elective' ? [{ delivery: 'elective', symbol: 'e' }] : undefined,
        //     theory,
        //     practical,
        //     clinical,
        // };
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            session_names.push(delivery_data.session_name);
            delivery.push({
                session_type: delivery_data.session_name,
                delivery: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        const session_delivery = [];
        session_names = [...new Set(session_names)];
        session_names.forEach((element) => {
            const del = [];
            delivery.forEach((sub_element) => {
                if (sub_element.session_type === element)
                    del.push({
                        delivery_name: sub_element.delivery,
                        symbol: sub_element.symbol,
                    });
            });
            session_delivery.push({ session_type: element, delivery: del });
        });
        Object.assign(obj, { session_types: session_delivery });
        // Getting group list and total count details
        const groups_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting;
        gender_setting = groups_setting.filter(
            (item) => req.params.gender === item.gender.toString(),
        );
        const student_group_no = gender_setting.findIndex(
            (i) => i._group_no.toString() === req.params.group_no.toString(),
        );
        // const groups = [];
        // const std_ids = [];
        // const id = [];
        if (student_group_no !== -1) {
            // let count = student_group_data.data.groups[student_group_level].courses[student_group_course].setting[student_group_no].ungrouped
            Object.assign(obj, { count: strength });
            student_group_no_session.forEach((element) => {
                const grp = element.groups;
                if (grp.length !== 0) {
                    const gro = element.groups.map((i) => i.group_name);
                    const objs = {
                        session_symbol: element.session_type,
                        no_of_groups: element.no_of_group,
                        no_of_student: element.no_of_student,
                        group_name: gro,
                    };
                    settings.push(objs);
                    Object.assign(obj, { group: settings });
                } else {
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('NO_GROUPS_CREATED'),
                                req.t('FIRST_CREATE_GROUPS'),
                            ),
                        );
                }
            });
        } else {
            Object.assign(obj, { count: strength });
            Object.assign(obj, { group: settings });
        }
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('SAVED_GROUPS'), obj));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_std_delete = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        isDeleted: false,
    };
    const student_group_data = await base_control.get(student_group, query, {});
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_data.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const group_course_row = student_group_data.data.groups[student_group_row].courses.findIndex(
        (i) => i._course_id.toString() === req.body._course_id.toString(),
    );
    if (group_course_row === -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('COURSE_NOT_FOUND'),
                    req.t('COURSE_NOT_FOUND'),
                ),
            );
    const group_course_session =
        student_group_data.data.groups[student_group_row].courses[group_course_row].session_types;
    let doc;
    const student_ids = req.body._student_ids;
    for (element of group_course_session) {
        for (sub_element of student_ids) {
            const objs = {
                $pull: {
                    'groups.$[i].courses.$[j].setting.$[k].ungrouped': {
                        $in: req.body._student_ids,
                    },
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        {
                            $in: req.body._student_ids,
                        },
                },
                $push: {
                    'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_ids,
                },
            };
            const filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    { 'k.gender': req.body.gender, 'k._group_no': req.body.master_group },
                    { 'l.session_type': element.symbol },
                    { 'm._student_ids': sub_element },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
    }
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_DELETE_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                    req.t('ERROR_UNABLE_TO_DELETE_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                ),
            );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
        rotationCount: req.body.master_group,
    });
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
            ),
        );
};

exports.rotation_course_group_change = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_level === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.body._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const student_master_group = student_group_data.data.groups[student_group_level].courses[
            student_group_course
        ].setting.findIndex(
            (i) =>
                i._group_no.toString() === req.body.master_group.toString() &&
                i.gender === req.body.gender,
        );
        if (student_master_group === -1) {
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                    ),
                );
        }
        const course_session_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting[student_master_group].session_setting;
        if (course_session_setting.length === 0) {
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_SESSION_GROUPS_CREATED'),
                        req.t('NO_SESSION_GROUPS_CREATED'),
                    ),
                );
        }
        let excess = 0;
        let capacity = 0;
        let strength = 0;
        const validation = [];
        for (let index = 0; index < req.body.delivery_group.length; index++) {
            const element = req.body.delivery_group[index].session_type;
            const grp_nos = req.body.delivery_group[index].group_no;
            const session_type_ind =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element)
                ];
            if (session_type_ind === undefined)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                        ),
                    );
            capacity = session_type_ind.no_of_student;
            excess = student_group_data.data.groups[student_group_level].course_excess_count
                ? student_group_data.data.groups[student_group_level].course_excess_count
                : 0;
            const new_grp_ind = session_type_ind.groups.findIndex((i) => i.group_no === grp_nos);
            if (new_grp_ind === -1)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('GROUP_NO_NOT_FOUND'),
                            req.t('GROUP_NO_NOT_FOUND'),
                        ),
                    );
            strength = session_type_ind.groups[new_grp_ind]._student_ids.length + 1;
            if (!(strength <= excess + capacity))
                validation.push({
                    session_type: element,
                    message: 'capacity exceeded increase extra allowed',
                });
        }
        if (validation.length !== 0)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                        validation,
                    ),
                );
        req.body.delivery_group.forEach(async (element, index) => {
            const session_types =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session_types.groups.findIndex((sub_element) => {
                if (sub_element._student_ids.findIndex((i) => i === req.body._student_id) !== -1) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                            req.body._student_id,
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._course_id': req.body._course_id },
                        { 'k.gender': req.body.gender, 'k._group_no': req.body.master_group },
                        { 'l.session_type': element.session_type },
                        { 'm.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    { 'k.gender': req.body.gender },
                    { 'l.session_type': element.session_type },
                    { 'm.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (!doc.status)
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('ERROR_UNABLE_TO_EDIT_STUDENT'),
                                doc.data,
                            ),
                        );
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                    rotationCount: req.body.master_group,
                });
                return res
                    .status(200)
                    .send(
                        common_files.response_function(
                            res,
                            200,
                            true,
                            req.t('SUCCESSFULLY_EDITED_STUDENT_IN_ROTATION_COURSE_GROUP'),
                            req.t('SUCCESSFULLY_EDITED_STUDENT_IN_ROTATION_COURSE_GROUP'),
                        ),
                    );
            }
        });
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_add_student_course = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const user_checks = await base_control.get_list(
            user,
            { _id: { $in: [req.body._user_id, req.body._student_id] }, isDeleted: false },
            {},
        );
        if (!user_checks.status && user_checks.data.length !== 2)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const student_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._student_id.toString(),
        );
        const staff_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._user_id.toString(),
        );
        if (student_loc === -1 || staff_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        let objs = {};
        let filter = {};
        const course_status = [];
        const courses = student_group_check.data.groups[student_group_row].courses.map(
            (i) => i._course_id,
        );
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }
        if (
            student_group_check.data.groups[student_group_row].students.findIndex(
                (i) => i._student_id.toString() === req.body._student_id.toString(),
            ) === -1
        ) {
            objs = {
                $push: {
                    'groups.$[i].students': {
                        _student_id: user_checks.data[student_loc]._id,
                        academic_no: user_checks.data[student_loc].user_id,
                        name: user_checks.data[student_loc].name,
                        gender: user_checks.data[student_loc].gender,
                        mark: req.body.mark,
                        imported_on: common_fun.timestampNow(),
                        _imported_by: req.body._user_id,
                        imported_by: user_checks.data[staff_loc].name,
                        course_group_status: course_status,
                    },
                    'groups.$[i].courses.$[j].setting.$[k].ungrouped': req.body._student_id,
                },
                $pull: {
                    'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    {
                        'k._group_no': req.body.group_no,
                        'k.gender': user_checks.data[student_loc].gender,
                    },
                ],
            };
        } else {
            objs = {
                $push: { 'groups.$[i].courses.$[j].setting.$[k].ungrouped': req.body._student_id },
                $pull: { 'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_id },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    {
                        'k._group_no': req.body.group_no,
                        'k.gender': user_checks.data[student_loc].gender,
                    },
                ],
            };
        }

        const doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_ADD_STUDENT'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
            rotationCount: req.body.group_no,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                    req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_get_student_course = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = {
            user_id: req.params.academic_no,
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const user_data = await base_control.get(user, query, project);

        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );

        const query_stud_group = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: req.params._id,
            isDeleted: false,
            isActive: true,
        };
        const student_group_data = await base_control.get_list(student_group, query_stud_group, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );

        const student_arr = [];
        let ungrouped_student_arr = [];
        const groups_ind = student_group_data.data[0].groups.findIndex(
            (element) =>
                element.level.toString() === req.params.level.toString() &&
                element.term === req.params.batch,
        );
        if (groups_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const courses_ind = student_group_data.data[0].groups[groups_ind].courses.findIndex(
            (element) => element._course_id.toString() === req.params._course_id.toString(),
        );
        if (courses_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        const settings = student_group_data.data[0].groups[groups_ind].courses[courses_ind].setting;
        for (let i = 0; i < settings.length; i++) {
            ungrouped_student_arr = ungrouped_student_arr.concat(settings[i].ungrouped);
            for (let j = 0; j < settings[i].session_setting.length; j++) {
                for (let k = 0; k < settings[i].session_setting[j].groups.length; k++) {
                    // getting entire group student ids
                    //student_arr = student_arr.concat(settings[i].session_setting[j].groups[k]._student_ids)
                    student_arr.push({
                        student_ids: settings[i].session_setting[j].groups[k]._student_ids,
                        group_no: settings[i].session_setting[j].groups[k].group_no,
                        group_name: settings[i].session_setting[j].groups[k].group_name,
                    });
                }
            }
        }
        const ungrouped_ind = ungrouped_student_arr.findIndex((ele) =>
            ele.equals(user_data.data._id),
        );
        if (ungrouped_ind !== -1)
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('STUDENT_EXIST_IN_UNGROUPED'),
                        [],
                    ),
                );
        let ind = -1;
        let group_no = '';
        let group_name = '';
        for (let i = 0; i < student_arr.length; i++) {
            ind = student_arr[i].student_ids.findIndex((ele) => ele.equals(user_data.data._id));
            if (ind !== -1) {
                group_no = student_arr[i].group_no;
                group_name = student_arr[i].group_name;
                break;
            }
        }
        const student_details = [
            {
                name: user_data.data.name,
                gender: user_data.data.gender,
                enrolled_program: user_data.data.programs,
                group_no,
                group_name,
                _id: user_data.data._id,
            },
        ];

        if (student_arr.length > 0) {
            if (ind !== -1)
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            true,
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE'),
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_COURSE'),
                        ),
                    );
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                        student_details,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('YOU_CAN_ADD_STUDENT_TO_THIS_COURSE'),
                    student_details,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_course_group_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        let ind_group_setting = [];
        let groups_setting = [];
        let g_type = [];
        let student_groups = [];

        if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
            for (element of student_group_check.data.groups[student_group_row]
                .rotation_group_setting) {
                if (element.gender === req.body.gender) student_groups.push(element);
            }
        } else {
            student_groups =
                student_group_check.data.groups[student_group_row].group_setting[
                    student_group_check.data.groups[student_group_row].group_setting.findIndex(
                        (i) => i.gender === req.body.gender,
                    )
                ];
        }
        const course_pos = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const course_setting =
            student_group_check.data.groups[student_group_row].courses[course_pos].setting;
        groups_setting = course_setting.filter(
            (item) => req.body.gender !== item.gender.toString(),
        );
        req.body.groups.forEach((element) => {
            g_type = [];
            let un_group = [];
            const name =
                student_group_check.data.groups[student_group_row].group_name +
                '-' +
                student_group_check.data.groups[student_group_row].courses[course_pos].course_no +
                '-G' +
                element._group_no;
            if (student_group_check.data.groups[student_group_row].rotation === 'yes')
                for (std of student_groups) un_group = un_group.concat(std._student_ids);
            else
                un_group =
                    student_groups.groups[
                        student_groups.groups.findIndex((i) => i.group_no === element._group_no)
                    ]._student_ids;
            const gs_obj = {
                _group_no: element._group_no,
                gender: element.gender,
                ungrouped: un_group,
            };
            element.delivery_type_group.forEach((sub_element) => {
                ind_group_setting = [];
                for (let i = 1; i <= sub_element.no_of_group; i++) {
                    const igs_obj = {
                        group_no: i,
                        group_name: name + '-' + sub_element.delivery + '-' + i.toString(),
                    };
                    ind_group_setting.push(igs_obj);
                }
                const ses_setting = {
                    group_name: name + '-' + sub_element.delivery,
                    session_type: sub_element.delivery,
                    no_of_group: sub_element.no_of_group,
                    no_of_student: sub_element.no_of_students,
                    groups: ind_group_setting,
                };
                g_type.push(ses_setting);
            });
            Object.assign(gs_obj, { session_setting: g_type });
            groups_setting.push(gs_obj);
        });
        objs = { $set: { 'groups.$[i].courses.$[k].setting': groups_setting } };
        filter = {
            arrayFilters: [
                {
                    'i.level': req.body.level,
                    'i.term': req.body.batch,
                },
                { 'k._course_id': req.body._course_id },
            ],
        };
        // return res.status(200).send({ objs, filter });
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_SET_COURSE_GROUP_SETTING'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        for (const groupElement of req.body.groups) {
            await updateStudentGroupRedisKey({
                courseId: req.body._course_id,
                level: req.body.level,
                batch: req.body.batch,
                rotationCount: groupElement._group_no,
            });
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
