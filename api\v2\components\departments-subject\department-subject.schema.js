const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const subjectSharedWithSchema = new Schema({
    _program_id: Schema.Types.ObjectId,
    programName: {
        type: String,
    },
    _department_id: Schema.Types.ObjectId,
    departmentName: {
        type: String,
    },
});
const subjectSchema = new Schema({
    subjectName: {
        type: String,
    },
    sharedWith: {
        type: [subjectSharedWithSchema],
        default: [],
    },
    isActive: {
        type: Boolean,
        default: true,
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
});

const sharedWithSchema = new Schema({
    _program_id: Schema.Types.ObjectId,
    programName: {
        type: String,
    },
});

module.exports = { subjectSchema, sharedWithSchema };
