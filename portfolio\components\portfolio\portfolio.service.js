const PortfolioModel = require('./portfolio.model');
const FormModel = require('../form/form.model');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const StudentPortfolioModel = require('./student-portfolio.model');
const UserModel = require('../../../lib/models/user');
const StudentResponseModel = require('../student-response/student-response.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const AssignEvaluatorModel = require('../evaluation/assign-evaluator.model');

const { ON_GOING, PUBLISHED, NOT_STARTED, LB } = require('../../common/utils/enums');
const {
    convertToMongoObjectId,
    isIDEquals,
    isNumber,
    deepClone,
} = require('../../common/utils/common.util');
const { calculateRubricPoint } = require('../rubric/rubric.helper');
const { getDocumentsCount } = require('../../base/base.helper');
const {
    NotFoundError,
    BadRequestError,
    UpdateFailedError,
} = require('../../common/utils/api_error_util');
const {
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const { getGrades } = require('../../../lib/utility/digiAssess.helper');
const { standardGrades } = require('../report/report.helper');
const { getAssignedUserSections } = require('../form/form.helper');
const { STUDENT, REGULAR } = require('../../common/utils/constants');

const formProject = {
    title: 1,
    pages: 1,
    type: 1,
    publishedDate: 1,
    createdBy: 1,
    status: 1,
    isTemplate: 1,
};

const getPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    curriculumId,
    rotation,
    rotationCount,
}) => {
    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            term,
            year,
            level,
            ...(curriculumId && { curriculumId }),
            ...(rotation && { rotation }),
            ...(rotationCount && { rotationCount }),
        },
        {
            components: 1,
            courseId: 1,
            programId: 1,
            totalMarks: 1,
            status: 1,
            publishedDate: 1,
            institutionCalendarId: 1,
            name: 1,
            description: 1,
        },
    ).lean();
    if (portfolio) {
        const evaluations = await AssignEvaluatorModel.find(
            { portfolioId: portfolio._id },
            { componentId: 1, childrenId: 1, typeOfEvaluation: 1 },
        ).lean();

        portfolio.components.forEach((component) => {
            component.children.forEach((child) => {
                const evaluation = evaluations.find(
                    (evaluation) =>
                        isIDEquals(evaluation.componentId, component._id) &&
                        isIDEquals(evaluation.childrenId, child._id),
                );

                child.isAssigned = !!evaluation;
            });
        });

        return portfolio;
    }

    const newPortfolio = await PortfolioModel.create({
        courseId,
        programId,
        institutionCalendarId,
        status: NOT_STARTED,
        term,
        year,
        level,
        curriculumId,
        ...(rotation && { rotation }),
        ...(rotationCount && { rotationCount }),
    });

    return { _id: newPortfolio._id, courseId, programId, institutionCalendarId, totalMarks: 0 };
};

const updatePortfolio = async ({ portfolioId, name, description, components, totalMarks }) => {
    const portfolio = await PortfolioModel.findByIdAndUpdate(
        { _id: portfolioId },
        {
            ...(name && { name }),
            ...(description && { description }),
            components,
            totalMarks,
            status: ON_GOING,
        },
        {
            new: true,
            project: {
                components: 1,
                courseId: 1,
                programId: 1,
                academicCalenderId: 1,
                totalMarks: 1,
                name: 1,
                description: 1,
            },
        },
    );

    if (portfolio) {
        const evaluations = await AssignEvaluatorModel.find(
            { portfolioId: portfolio._id },
            { componentId: 1, childrenId: 1, typeOfEvaluation: 1 },
        ).lean();

        portfolio.components.forEach((component) => {
            component.children.forEach((child) => {
                const evaluation = evaluations.find(
                    (evaluation) =>
                        isIDEquals(evaluation.componentId, component._id) &&
                        isIDEquals(evaluation.childrenId, child._id),
                );

                child.isAssigned = !!evaluation;
            });
        });

        return portfolio;
    }

    return portfolio;
};

const publishPortfolio = async ({ portfolioId, level, term, year, rotation, rotationCount }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            courseId: 1,
            programId: 1,
            institutionCalendarId: 1,
            components: 1,
            status: 1,
            totalMarks: 1,
            publishedDate: 1,
            term: 1,
            year: 1,
            level: 1,
            curriculumId: 1,
            rotation: 1,
            rotationCount: 1,
        },
    ).lean();

    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    if (portfolio.status === PUBLISHED) throw new BadRequestError('PORTFOLIO_ALREADY_PUBLISHED');

    if (!portfolio?.components?.length) {
        throw new BadRequestError('PORTFOLIO_HAS_NO_COMPONENTS');
    }
    const isRolesUnAssigned = portfolio.components.some((component) =>
        component.children.some((child) => !child.roles?.length),
    );
    if (isRolesUnAssigned) throw new BadRequestError('ROLES_NOT_ASSIGNED_TO_COMPONENT');

    const grades = await getGrades();
    const grade = grades.find((grade) => grade.isActive);

    const { sgStudentList: students = [] } = await getStudentListFromStudentGroup({
        programId: portfolio.programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId: portfolio.courseId,
        institutionCalendarId: portfolio.institutionCalendarId,
    });

    const insertedStudents = await StudentPortfolioModel.insertMany(
        students.map((student) => ({
            portfolioId: portfolio._id,
            student: {
                _id: student._student_id,
                name: student.name,
                academicNo: student.user_id,
                gender: student.gender,
            },
            components: portfolio.components,
            totalMarks: portfolio.totalMarks,
            status: PUBLISHED,
            publishedDate: portfolio.publishedDate,
            programId: portfolio.programId,
            courseId: portfolio.courseId,
            institutionCalendarId: portfolio.institutionCalendarId,
            grades: grade?.grades || standardGrades,
            year,
            term,
            level,
            ...(rotation && { rotation }),
            ...(rotationCount && { rotationCount }),
        })),
    );

    const result = await PortfolioModel.updateOne(
        { _id: portfolioId },
        {
            $set: {
                status: PUBLISHED,
                publishedDate: new Date(),
                grades: grade?.grades || standardGrades,
            },
        },
    );
    if (!result.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }

    const courseSchedules = await CourseScheduleModel.find(
        {
            _institution_calendar_id: portfolio.institutionCalendarId,
            _program_id: portfolio.programId,
            _course_id: portfolio.courseId,
            year_no: year,
            term,
            level_no: level,
            ...(rotation && { rotation_no: rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
            isDeleted: false,
            isActive: true,
            type: REGULAR,
        },
        {
            scheduleDate: '$schedule_date',
            session: 1,
            start: 1,
            end: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
        },
    ).lean();

    const formIds = [];
    portfolio.components.forEach((component) => {
        component.children.forEach((child) => {
            if (child.formId) {
                formIds.push(convertToMongoObjectId(child.formId));
            }
        });
    });

    const forms = await FormModel.find(
        {
            _id: { $in: formIds },
        },
        { pages: 1, status: 1, marks: 1, title: 1, type: 1, evaluations: 1 },
    ).lean();

    const filterSections = [];

    portfolio.components.forEach((component) => {
        component.children.forEach((child) => {
            const form = forms.find((formEntry) => isIDEquals(formEntry._id, child?.formId));
            if (form && child?.roles?.length) {
                const clonedForm = deepClone(form);
                const role = child.roles.find((role) => role.type === STUDENT);

                clonedForm.pages = getAssignedUserSections({
                    pages: deepClone(clonedForm.pages),
                    viewSections: role?.viewSections || [],
                    modifySections: role?.modifySections || [],
                });

                filterSections.push({
                    ...clonedForm,
                    childrenId: child._id,
                });
            }
        });
    });

    const bulkUpdates = [];
    insertedStudents.forEach((student) => {
        const studentEntry = student.toObject();

        studentEntry.components.forEach((component) => {
            if (component.code === LB) {
                const schedules = courseSchedules.filter((schedule) => {
                    return component.deliveryTypes.some(
                        (deliveryType) =>
                            deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
                    );
                });

                schedules.forEach((schedule) => {
                    component.children?.forEach((child) => {
                        const form = filterSections.find((formEntry) =>
                            isIDEquals(formEntry.childrenId, child._id),
                        );

                        bulkUpdates.push({
                            updateOne: {
                                filter: {
                                    'student._id': convertToMongoObjectId(studentEntry.student._id),
                                    componentId: convertToMongoObjectId(component._id),
                                    childrenId: convertToMongoObjectId(child._id),
                                    scheduleId: convertToMongoObjectId(schedule._id),
                                },
                                update: {
                                    $set: {
                                        status: NOT_STARTED,
                                        pages: form.pages,
                                        student: studentEntry.student,
                                        totalMarks: form.marks,
                                        portfolioId: studentEntry._id,
                                        parentPortfolioId: portfolio._id,
                                        componentId: component._id,
                                        childrenId: child._id,
                                        formId: form._id,
                                        scheduleId: schedule._id,
                                        title: form.title,
                                        type: form.type,
                                        roles: child.roles,
                                    },
                                },
                                upsert: true,
                            },
                        });
                    });
                });
            } else {
                component.children?.forEach((child) => {
                    const form = filterSections.find((formEntry) =>
                        isIDEquals(formEntry.childrenId, child._id),
                    );

                    bulkUpdates.push({
                        updateOne: {
                            filter: {
                                'student._id': convertToMongoObjectId(studentEntry.student._id),
                                componentId: convertToMongoObjectId(component._id),
                                childrenId: convertToMongoObjectId(child._id),
                            },
                            update: {
                                $set: {
                                    status: NOT_STARTED,
                                    pages: form.pages,
                                    student: studentEntry.student,
                                    totalMarks: form.marks,
                                    portfolioId: studentEntry._id,
                                    parentPortfolioId: portfolio._id,
                                    componentId: component._id,
                                    childrenId: child._id,
                                    formId: form._id,
                                    title: form.title,
                                    type: form.type,
                                    roles: child.roles,
                                },
                            },
                            upsert: true,
                        },
                    });
                });
            }
        });
    });

    await StudentResponseModel.bulkWrite(bulkUpdates).catch((err) => {
        throw new BadRequestError('UPDATE_FAILED');
    });

    return students;
};

const getComponentForm = async ({ formId, portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId },
        {
            'components._id': 1,
            'components.children._id': 1,
            'components.children.formId': 1,
            'components.children.templateId': 1,
        },
    ).lean();

    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    // Check if the form is attached to the given component & child
    const component = portfolio.components.find((component) =>
        isIDEquals(component._id, componentId),
    );
    const child = component?.children.find((child) => isIDEquals(child._id, childrenId));
    const isAttachedForm = child && isIDEquals(child?.formId, formId);

    const form = await FormModel.findOne({ _id: formId }, { ...formProject, _id: 0 }).lean();
    if (!form) throw new NotFoundError('FORM_NOT_FOUND');

    if (!isAttachedForm || !form?.isTemplate) {
        return {
            ...form,
            isEditable: isAttachedForm,
            formId: form?.isTemplate ? form._id : child?.templateId,
        };
    }

    // Create and attach new form if isAttachedForm is true and componentForm not found
    const newForm = await FormModel.create({
        ...form,
        isTemplate: false,
    });

    if (newForm?._id) {
        await PortfolioModel.updateOne(
            { _id: portfolioId },
            {
                $set: {
                    'components.$[componentId].children.$[childrenId].formId': newForm._id,
                    'components.$[componentId].children.$[childrenId].templateId': formId,
                },
            },
            {
                arrayFilters: [
                    { 'componentId._id': convertToMongoObjectId(componentId) },
                    { 'childrenId._id': convertToMongoObjectId(childrenId) },
                ],
            },
        );
    }

    return { _id: newForm._id, ...form, isTemplate: false, isEditable: true };
};

const getPortfolioList = async ({
    institutionCalendarId,
    programId,
    courseId,
    limit,
    pageNo,
    skip,
    canPagination,
}) => {
    let portfolioQuery = PortfolioModel.find(
        {
            ...(institutionCalendarId && { institutionCalendarId }),
            ...(programId && { programId }),
            ...(courseId && { courseId }),
            status: { $ne: NOT_STARTED },
        },
        {
            programId: 1,
            courseId: 1,
            components: 1,
            status: 1,
            publishedDate: 1,
            totalMarks: 1,
            institutionCalendarId: 1,
            isReportGenerated: 1,
            createdBy: 1,
        },
    );

    // Only apply skip and limit if pagination is enabled
    if (canPagination) {
        portfolioQuery = portfolioQuery.skip(skip).limit(limit);
    }

    const portfolios = await portfolioQuery.lean();

    const programs = await ProgramModel.find(
        { _id: { $in: portfolios.map(({ programId }) => programId) } },
        { name: 1 },
    ).lean();

    const courses = await CourseModel.find(
        { _id: { $in: portfolios.map(({ courseId }) => courseId) } },
        { course_name: 1, course_code: 1 },
    ).lean();

    const formattedPortfolios = portfolios.map((portfolio) => {
        const program = programs.find((program) => isIDEquals(portfolio.programId, program._id));
        const course = courses.find((course) => isIDEquals(portfolio.courseId, course._id));

        return {
            programName: program?.name,
            courseName: course?.course_name,
            courseCode: course?.course_code,
            ...portfolio,
        };
    });

    // Only return count metadata if pagination is enabled
    if (canPagination) {
        const totalCount = await getDocumentsCount({ Model: PortfolioModel, query });
        return {
            count: {
                totalPages: Math.ceil(totalCount / limit),
                totalCount,
                currentPage: pageNo,
            },
            portfolios: formattedPortfolios,
        };
    }

    return { portfolios: formattedPortfolios };
};

const detachPortfolioFrom = async ({ portfolioId, componentId, childrenId }) => {
    if (portfolioId && componentId && childrenId) {
        const portfolio = await PortfolioModel.findOneAndUpdate(
            { _id: portfolioId },
            {
                $unset: {
                    'components.$[componentId].children.$[childrenId].formId': '',
                    'components.$[componentId].children.$[childrenId].templateId': '',
                },
            },
            {
                arrayFilters: [
                    { 'componentId._id': convertToMongoObjectId(componentId) },
                    { 'childrenId._id': convertToMongoObjectId(childrenId) },
                ],
            },
            {
                project: {
                    'components._id': 1,
                    'components.children._id': 1,
                    'components.children.formId': 1,
                    'components.children.templateId': 1,
                },
            },
        ).lean();

        // Check if the form is attached to the given component & child
        const component = portfolio.components.find((component) =>
            isIDEquals(component._id, componentId),
        );
        const child = component?.children.find((child) => isIDEquals(child._id, childrenId));
        if (child?.formId) {
            await FormModel.deleteOne({ _id: child?.formId, isTemplate: false });
        }
    } else {
        const portfolio = await PortfolioModel.findOne(
            { _id: portfolioId },
            {
                'components._id': 1,
                'components.children._id': 1,
                'components.children.formId': 1,
            },
        );
        const formIds = [];
        portfolio.components.forEach((component) => {
            if (componentId && isIDEquals(component._id, componentId)) {
                component.children.forEach((child) => {
                    if (childrenId) {
                        if (isIDEquals(child._id, childrenId)) {
                            if (child?.formId) {
                                formIds.push(child.formId);
                            }
                        }
                    }
                });
            } else if (!componentId) {
                component.children.forEach((child) => {
                    if (child?.formId) {
                        formIds.push(child.formId);
                    }
                });
            }
        });

        await FormModel.deleteMany({ _id: { $in: formIds }, isTemplate: false });
    }
};

const deletePortfolio = async ({ portfolioId }) => {
    const portfolio = await PortfolioModel.deleteOne({ _id: portfolioId });
    if (!portfolio.deletedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    await StudentPortfolioModel.deleteMany({ portfolioId });

    await StudentResponseModel.deleteMany({ parentPortfolioId: portfolioId });
};

const assignStudentToPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    portfolioId,
    componentId,
    childrenId,
    students,
    startDate,
    endDate,
    isAssigned = true,
}) => {
    const filter = {
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        'student._id': { $in: students.map(({ studentId }) => convertToMongoObjectId(studentId)) },
    };

    const update = isAssigned
        ? {
              $set: {
                  'components.$[componentId].children.$[childrenId].isAssigned': true,
                  ...(startDate && {
                      'components.$[componentId].children.$[childrenId].startDate': startDate,
                  }),
                  ...(endDate && {
                      'components.$[componentId].children.$[childrenId].endDate': endDate,
                  }),
              },
          }
        : {
              $set: { 'components.$[componentId].children.$[childrenId].isAssigned': false },
              $unset: {
                  'components.$[componentId].children.$[childrenId].startDate': '',
                  'components.$[componentId].children.$[childrenId].endDate': '',
              },
          };

    await StudentPortfolioModel.updateMany(filter, update, {
        arrayFilters: [
            { 'componentId._id': convertToMongoObjectId(componentId) },
            { 'childrenId._id': convertToMongoObjectId(childrenId) },
        ],
    });
};

const getStudentsByCourse = async ({
    programId = '',
    year = '',
    level = '',
    rotation = '',
    rotationCount = '',
    term = '',
    courseId = '',
    institutionCalendarId = '',
    portfolioId = '',
    componentId = '',
    childrenId = '',
}) => {
    const studentGroup = await getStudentListFromStudentGroup({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
    });

    const { masterGroup: groups = [], sgStudentList: students = [] } = studentGroup;

    if (!students.length) {
        return {
            students: [],
            count: { total: 0, male: 0, female: 0 },
            groups: groups.map((g) => g.group_name),
            deliveryTypes: groups.map((g) => g.delivery_type),
        };
    }

    const studentIds = students.map((student) => convertToMongoObjectId(student._student_id));
    const activeStudents = await UserModel.find(
        { _id: { $in: studentIds }, isActive: true },
        { _id: 1, email: 1, name: 1, academicNo: '$user_id', gender: 1 },
    ).lean();

    const formattedStudents = activeStudents.map(({ _id, name, academicNo, email, gender }) => ({
        name,
        studentId: _id,
        academicNo,
        email,
        gender: gender === 'male' ? 'M' : 'F',
    }));

    const studentsPortfolio = await StudentPortfolioModel.find(
        {
            programId,
            courseId,
            institutionCalendarId,
        },
        {
            'student._id': 1,
            'components._id': 1,
            'components.children._id': 1,
            'components.children.isAssigned': 1,
            'components.children.startDate': 1,
            'components.children.endDate': 1,
        },
    ).lean();

    const studentResponses = await StudentResponseModel.find(
        {
            'student._id': {
                $in: formattedStudents.map((student) => convertToMongoObjectId(student.studentId)),
            },
            componentId,
            childrenId,
        },
        { 'student._id': 1, status: 1, evaluationStatus: 1, awardedMarks: 1 },
    ).lean();

    formattedStudents.forEach((student) => {
        const response = studentResponses.find((response) =>
            isIDEquals(response?.student?._id, student.studentId),
        );
        const studentPortfolio = studentsPortfolio.find((s) =>
            isIDEquals(s.student._id, student.studentId),
        );
        const matchingComponent = studentPortfolio?.components?.find((c) =>
            isIDEquals(c._id, componentId),
        );
        const matchingChild = matchingComponent?.children?.find((ch) =>
            isIDEquals(ch._id, childrenId),
        );

        Object.assign(student, {
            status: response?.status || NOT_STARTED,
            evaluationStatus: response?.evaluationStatus || NOT_STARTED,
            awardedMarks: response?.awardedMarks || 0,
            isAssigned: matchingChild?.isAssigned,
            startDate: matchingChild?.startDate,
            endDate: matchingChild?.endDate,
            totalMarks: response?.totalMarks,
        });
    });

    const count = formattedStudents.reduce(
        (acc, { gender }) => {
            acc.total++;
            if (gender === 'M') acc.male++;
            if (gender === 'F') acc.female++;
            return acc;
        },
        { total: 0, male: 0, female: 0 },
    );

    const groupData = groups.map(
        ({ group_name, delivery_type, delivery_symbol, session_group, _id }) => ({
            groupName: group_name,
            deliveryType: delivery_type,
            deliverySymbol: delivery_symbol,
            sessionGroups: session_group.map((s) => ({
                _id: s._id,
                studentIds: s._student_ids,
                groupName: s.group_name,
            })),
            _id,
        }),
    );

    return { students: formattedStudents, count, groups: groupData };
};

const updateStudentReview = async ({
    componentId,
    childrenId,
    scheduleId,
    studentId,
    userId,
    text,
}) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1 }).lean();

    await StudentResponseModel.updateOne(
        { 'student._id': studentId, componentId, childrenId, ...(scheduleId && { scheduleId }) },
        { $push: { reviews: { userId, text, createdAt: new Date(), name: user?.name } } },
    );
};

const getPortfolioForAssignEvaluator = async ({ programId, courseId, institutionCalendarId }) => {
    const portfolio = await PortfolioModel.findOne(
        { programId, courseId, institutionCalendarId, status: PUBLISHED },
        { components: 1 },
    ).lean();

    const { components = [] } = portfolio || {};

    if (!portfolio || !components?.length) {
        return [];
    }

    const assignedEvaluators = await AssignEvaluatorModel.find(
        { portfolioId: portfolio._id },
        { evaluators: 1, role: 1, childrenId: 1, componentId: 1 },
    ).lean();

    const formattedComponents = components.map((component) => {
        const children = component.children.map((child) => {
            const evaluators = assignedEvaluators.filter(
                (evaluator) =>
                    isIDEquals(evaluator.childrenId, child._id) &&
                    isIDEquals(evaluator.componentId, component._id),
            );

            const roles = evaluators
                .map((evaluator) => {
                    if (evaluator?.role?._id) {
                        return {
                            _id: evaluator.role._id,
                            name: evaluator.role.name,
                            evaluators: evaluator.evaluators,
                        };
                    }

                    return null;
                })
                .filter(Boolean);

            return {
                ...child,
                roles,
            };
        });

        return {
            _id: component._id,
            name: component.name,
            code: component.code,
            children,
        };
    });

    return { _id: portfolio._id, components: formattedComponents };
};

const updatePortfolioChildField = async ({ portfolioId, componentId, childrenId, update }) => {
    await PortfolioModel.updateOne(
        { _id: portfolioId },
        { $set: update },
        {
            arrayFilters: [
                { 'componentId._id': convertToMongoObjectId(componentId) },
                { 'childrenId._id': convertToMongoObjectId(childrenId) },
            ],
        },
    );
};

const updateFormInPortfolio = async ({ title, type, pages, userId }) => {
    const user = await UserModel.findOne({ _id: userId }, { name: 1, _id: 0 }).lean();

    const newForm = await FormModel.create({
        title,
        type,
        pages,
        createdBy: { id: userId, name: user?.name },
        isTemplate: true,
        status: PUBLISHED,
    });

    return newForm._id;
};

module.exports = {
    getPortfolio,
    updatePortfolio,
    publishPortfolio,
    getComponentForm,
    getPortfolioList,
    detachPortfolioFrom,
    deletePortfolio,
    assignStudentToPortfolio,
    getStudentsByCourse,
    updateStudentReview,
    getPortfolioForAssignEvaluator,
    updatePortfolioChildField,
    updateFormInPortfolio,
};
