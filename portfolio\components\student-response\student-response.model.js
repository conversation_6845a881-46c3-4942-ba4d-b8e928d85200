const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');

const { FORM, STUDENT_RESPONSE } = require('../../common/utils/constants');
const { NOT_STARTED } = require('../../common/utils/enums');
const { rubric } = require('../rubric/rubric.schema');

const schema = new Schema(
    {
        formId: { type: ObjectId, ref: FORM },
        student: {
            _id: {
                type: ObjectId,
                index: true,
            },
            email: {
                type: String,
                required: true,
            },
            name: {
                first: { type: String, trim: true },
                middle: { type: String, trim: true },
                last: { type: String, trim: true },
            },
            academicNo: { type: String, trim: true },
        },
        title: { type: String, trim: true },
        type: {
            code: { type: String, trim: true },
            name: { type: String, trim: true },
            _id: { type: ObjectId },
        },
        pages: [
            {
                elements: [
                    {
                        title: { type: String, trim: true },
                        type: { type: String, trim: true },
                        view: { type: Boolean },
                        edit: { type: Boolean },
                        modify: { type: Boolean },
                        fill: { type: Boolean },
                        elements: [],
                    },
                ],
            },
        ],
        isDeleted: { type: Boolean, default: false },
        status: { type: String, default: NOT_STARTED },
        evaluationStatus: { type: String, default: NOT_STARTED },
        totalMarks: { type: Number },
        awardedMarks: { type: Number },
        portfolioId: { type: ObjectId },
        componentId: { type: ObjectId },
        childrenId: { type: ObjectId },
        parentPortfolioId: { type: ObjectId },
        formTimestamps: {
            firstStartedAt: { type: Date },
            submittedAt: { type: Date },
            lastUpdated: { type: Date },
        },
        evaluators: [
            {
                name: { first: { type: String }, middle: { type: String }, last: { type: String } },
                userId: { type: ObjectId },
                email: { type: String },
                employeeId: { type: String },
                marks: { type: Number },
                rubrics: [rubric],
                awardedMarks: { type: Number },
                globalRubrics: [rubric],
                globalRubricTotalPoints: { type: Number },
                globalRubricAwardedPoints: { type: Number },
            },
        ],
        evaluations: [
            {
                role: { type: String },
                roleId: { type: ObjectId },
                awardedMarks: { type: Number },
                marks: { type: Number },
                rubrics: [rubric],
                globalRubrics: [rubric],
                globalRubricTotalPoints: { type: Number },
                globalRubricAwardedPoints: { type: Number },
            },
        ],
        scheduleId: { type: ObjectId },
        reviews: [
            {
                name: { first: { type: String }, middle: { type: String }, last: { type: String } },
                userId: { type: ObjectId },
                text: { type: String },
                createdAt: { type: Date },
            },
        ],
        approvalStatus: { type: String },
        globalRubricTotalPoints: { type: Number }, // total max points from global rubric
        globalRubricAwardedPoints: { type: Number }, // awarded points from global rubric
        statusHistory: [
            {
                status: { type: String },
                createdAt: { type: Date },
                userId: { type: ObjectId },
            },
        ],
    },
    { timestamps: true },
);

module.exports = model(STUDENT_RESPONSE, schema);
