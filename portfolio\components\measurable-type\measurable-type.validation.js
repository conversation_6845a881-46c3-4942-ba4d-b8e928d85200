const { Joi } = require('../../common/middlewares/validation');

const createMeasurableTypeSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const updateMeasurableTypeSchema = Joi.object({
    query: Joi.object({
        measurableTypeId: Joi.string().hex().length(24).required(),
    }),
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const deleteMeasurableTypeSchema = Joi.object({
    query: Joi.object({
        measurableTypeId: Joi.string().hex().length(24).required(),
    }),
}).unknown(true);

module.exports = {
    createMeasurableTypeSchema,
    updateMeasurableTypeSchema,
    deleteMeasurableTypeSchema,
};
