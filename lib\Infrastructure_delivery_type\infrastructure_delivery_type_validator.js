// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.infrastructure_delivery_type = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
           
            name: Joi.string().min(1).max(100).trim().error(error => {

                return error;
            }),
            primary_delivery_type: Joi.number().min(0).max(10000).error(error => {

                return error;
            }),
            delivery_symbol: Joi.string().alphanum().min(1).max(10).error(error => {

                return error;
            }),
           
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.infrastructure_delivery_type_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}