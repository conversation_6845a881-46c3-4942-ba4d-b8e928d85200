const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const {
    DIGI_COURSE,
    USER,
    DIGI_PROGRAM,
    LATE_CONFIG_MANAGEMENT,
    COURSE_WISE,
    STUDENT_WISE,
    SESSION_WISE,
} = require('../utility/constants');

const lateConfigManagementSchema = new Schemas(
    {
        _institution_d: {
            type: Schemas.Types.ObjectId,
        },
        _institution_calendar_id: {
            type: Schemas.Types.ObjectId,
        },
        programId: {
            type: Schemas.Types.ObjectId,
            ref: DIGI_PROGRAM,
        },
        yearNo: {
            type: String,
        },
        levelNo: {
            type: String,
        },
        term: {
            type: String,
        },
        courseId: {
            type: Schemas.Types.ObjectId,
            ref: DIGI_COURSE,
        },
        sessionId: {
            type: Schemas.Types.ObjectId,
        },
        rotation: Boolean,
        rotationCount: Number,
        studentId: {
            type: Schemas.Types.ObjectId,
            ref: USER,
        },
        typeWiseUpdate: { type: String, enum: [COURSE_WISE, STUDENT_WISE, SESSION_WISE] },
        excludeStatus: { type: Boolean, default: false },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(LATE_CONFIG_MANAGEMENT, lateConfigManagementSchema);
