const messageSchema = require('../../models/chat_message');
const userSchema = require('../../models/user');
const chatGroupSchema = require('../../models/chat_group');
const courseSchema = require('../../models/digi_course');
const courseScheduleSchema = require('../../models/course_schedule');
const institutionCalendarSchema = require('../../models/institution_calendar');
const studentGroupSchema = require('../../models/student_group');
const { convertToMongoObjectId } = require('../../utility/common');
const { logger } = require('../../utility/util_keys');
const {
    DC_STAFF,
    PUBLISHED,
    STUDENT_GROUP_MODE: { FYD },
    GROUP_TYPE: { STUDENT_GROUP, COURSE_GROUP },
} = require('../../utility/constants');
// const { getConcatYearLevelCode } = require('../../utility/digiAssess.helper');

const getUnreadCountForChannels = async ({ availableChannels, userId }) => {
    const channelsIds = [];
    const blockedDates = {};
    for (const channelElement of availableChannels) {
        channelsIds.push(channelElement._id);
        for (const memberElement of channelElement.members) {
            if (memberElement.isBlocked && memberElement.userId.toString() === userId) {
                channelElement.blockedDate = memberElement.blockedDate;
                channelElement.isBlocked = memberElement.isBlocked;
                blockedDates[channelElement._id] = memberElement.blockedDate;
            }
        }
    }
    const messagesForUsersChannels = await messageSchema.aggregate([
        {
            $match: {
                channelId: { $in: channelsIds },
                isDeleted: false,
                deletedFor: { $ne: convertToMongoObjectId(userId) },
            },
        },
        {
            $addFields: {
                blockedDate: {
                    $cond: {
                        if: {
                            $in: [
                                '$channelId',
                                Object.keys(blockedDates).map((id) => convertToMongoObjectId(id)),
                            ],
                        },
                        then: {
                            $let: {
                                vars: {
                                    matchedItem: {
                                        $filter: {
                                            input: Object.entries(blockedDates).map(
                                                ([id, date]) => ({
                                                    id: convertToMongoObjectId(id),
                                                    date,
                                                }),
                                            ),
                                            cond: { $eq: ['$$this.id', '$channelId'] },
                                        },
                                    },
                                },
                                in: { $arrayElemAt: ['$$matchedItem.date', 0] },
                            },
                        },
                        else: null,
                    },
                },
            },
        },
        {
            $match: {
                $or: [
                    { blockedDate: { $eq: null } },
                    { $expr: { $lte: ['$createdAt', '$blockedDate'] } },
                ],
            },
        },
        {
            $sort: { _id: -1 },
        },
        {
            $group: {
                _id: '$channelId',
                unreadCount: {
                    $sum: {
                        $cond: {
                            if: { $in: [convertToMongoObjectId(userId), '$members'] },
                            then: 0,
                            else: 1,
                        },
                    },
                },
                createdAt: { $first: '$createdAt' },
                createdBy: { $first: '$createdBy' },
                msg: { $first: '$msg' },
                fileName: { $first: '$fileName' },
            },
        },
        {
            $project: {
                _id: 1,
                unreadCount: {
                    $cond: [
                        { $lte: ['$unreadCount', 11] }, // Check if the size of messages is less than or equal to 11
                        '$unreadCount', // If true, return the actual size
                        11, // Otherwise, return 10+
                    ],
                },
                createdAt: '$createdAt',
                createdBy: '$createdBy',
                msg: '$msg',
                fileName: '$fileName',
            },
        },
    ]);
    const userData = await userSchema
        .find(
            { _id: availableChannels.map((channelElement) => channelElement.createdBy) },
            { 'name.first': 1, 'name.last': 1 },
        )
        .lean();
    const userMap = {};
    for (const userElement of userData) {
        userMap[userElement._id.toString()] = userElement.name;
    }
    return availableChannels.map((channelElement) => {
        const selectedChannel = messagesForUsersChannels.find(
            (messageElement) => messageElement._id.toString() === channelElement._id.toString(),
        );
        channelElement.unreadCount = selectedChannel?.unreadCount;
        channelElement.lastMsgTime = selectedChannel?.createdAt;
        channelElement.lastMsgSender = userMap[selectedChannel?.createdBy.toString()];
        channelElement.msg = selectedChannel?.msg;
        channelElement.fileName = selectedChannel?.fileName;
        return channelElement;
    });
};

const getUserChannelList = async ({
    institutionCalendarId,
    userId,
    courseId,
    rotationCount,
    term,
    level,
}) => {
    const chatQuery = {
        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        'members.userId': convertToMongoObjectId(userId),
        isDeleted: false,
        ...(courseId && { courseId: convertToMongoObjectId(courseId) }),
        ...(rotationCount && { rotationCount }),
        ...(term && { term }),
        ...(level && { level }),
    };

    const userChannelList = await chatGroupSchema
        .find(chatQuery, {
            channelName: 1,
            channelType: 1,
            rotation: 1,
            rotationCount: 1,
            term: 1,
            course_no: 1,
            course_name: 1,
            level: 1,
            year: 1,
            programId: 1,
            admin: 1,
            members: 1,
        })
        .populate({ path: 'programId', select: { name: 1 } })
        .lean();
    return await getUnreadCountForChannels({ availableChannels: userChannelList, userId });
};

const getScheduledActiveCalendars = async ({ userId, userType, isCourseAdmin }) => {
    const requestingKeys = { userId, userType, isCourseAdmin };
    logger.info({ requestingKeys }, 'courseSession -> userCalendars -> start');
    let institutionCalendarIds = [];
    const currentDate = new Date();
    const institutionCalenderData = await institutionCalendarSchema
        .find(
            {
                isDeleted: false,
                status: PUBLISHED,
                start_date: { $lte: currentDate },
                end_date: { $gte: currentDate },
            },
            { _id: 1 },
        )
        .sort({ _id: -1 })
        .lean();

    const currentInstitutionCalendarIds = institutionCalenderData.map(({ _id }) =>
        convertToMongoObjectId(_id),
    );
    if (userType && userType === DC_STAFF) {
        // Course Coordinator
        if (isCourseAdmin) {
            const courseCoordinatorDatas = await courseSchema.find(
                {
                    isDeleted: false,
                    isActive: true,
                    'coordinators._user_id': convertToMongoObjectId(userId),
                    'coordinators._institution_calendar_id': { $in: currentInstitutionCalendarIds },
                },
                {
                    'coordinators.status': 1,
                    'coordinators._user_id': 1,
                    'coordinators._institution_calendar_id': 1,
                },
            );
            const courseCalendarId = courseCoordinatorDatas
                .map((courseElement) =>
                    courseElement.coordinators
                        .filter(
                            (coordinatorElement) =>
                                coordinatorElement._user_id.toString() === userId.toString() &&
                                coordinatorElement.status,
                        )
                        .map((coordinatorElement) =>
                            coordinatorElement._institution_calendar_id.toString(),
                        )
                        .flat(),
                )
                .flat();
            if (courseCalendarId && courseCalendarId.length)
                institutionCalendarIds = [...new Set(courseCalendarId)];
        } else {
            const scheduleQuery = {
                isDeleted: false,
                'staffs._staff_id': convertToMongoObjectId(userId),
                _institution_calendar_id: { $in: currentInstitutionCalendarIds },
            };
            const courseScheduleData = await courseScheduleSchema
                .distinct('_institution_calendar_id', scheduleQuery)
                .lean();
            if (courseScheduleData) institutionCalendarIds = courseScheduleData;
        }
    } else {
        // Student Group Based Flow
        const studentStudentGroupData = await studentGroupSchema.distinct(
            '_institution_calendar_id',
            {
                'groups.courses.setting.session_setting.groups._student_ids':
                    convertToMongoObjectId(userId),
                _institution_calendar_id: { $in: currentInstitutionCalendarIds },
            },
        );
        if (studentStudentGroupData) institutionCalendarIds = studentStudentGroupData;
    }
    logger.info('courseSession -> userCalendars -> end');
    return {
        userCalendarList: institutionCalendarIds,
    };
};

const findMatchedStudentGroup = ({ course, _group_no, gender }) => {
    return (
        course.student_groups?.find(
            (scheduleGroupElement) =>
                scheduleGroupElement.group_no?.toString() === _group_no.toString() &&
                gender === scheduleGroupElement.gender,
        ) || null
    );
};

const getOverallGroup = ({
    userCalendarList,
    studentGroups,
    schedulesForStaff,
    DC_STAFF,
    type,
    userId,
}) => {
    const overAllGroup = [];
    const queryForChatGroup = {
        courseId: [],
        year: [],
        level: [],
        term: [],
        programId: [],
    };
    for (const institutionCalendarElement of userCalendarList) {
        // institution matched student groups
        const institutionMatchedStudentGroups = studentGroups.filter(
            (studentElement) =>
                studentElement._institution_calendar_id.toString() ===
                institutionCalendarElement.toString(),
        );

        for (const groupElement of institutionMatchedStudentGroups) {
            const isScheduledCourse =
                type === DC_STAFF
                    ? schedulesForStaff?.filter((scheduleElement) =>
                          scheduleElement._student_group_id
                              ? scheduleElement._student_group_id?.toString() ===
                                groupElement._id?.toString()
                              : true,
                      )
                    : true;
            const { groups, _institution_calendar_id, master } = groupElement;
            const filteredScheduleCourses =
                type === DC_STAFF
                    ? isScheduledCourse?.filter(
                          (scheduleElement) =>
                              scheduleElement.year_no === master.year &&
                              scheduleElement._program_id.toString() ===
                                  master._program_id.toString(),
                      )
                    : true;
            if (type === DC_STAFF ? !!filteredScheduleCourses.length : true) {
                for (const mainGroupElement of groups) {
                    const {
                        term,
                        level,
                        courses,
                        rotation,
                        rotation_count,
                        rotation_group_setting,
                        group_mode,
                    } = mainGroupElement;
                    const filteredGroups =
                        type === DC_STAFF
                            ? filteredScheduleCourses?.filter(
                                  (scheduleElement) =>
                                      scheduleElement.term === term &&
                                      scheduleElement.level_no === level,
                              )
                            : true;
                    if (type === DC_STAFF ? !!filteredGroups.length : true) {
                        for (const courseElement of courses) {
                            const { _course_id, course_name, course_no, setting } = courseElement;
                            const isCourseMatched =
                                type === DC_STAFF
                                    ? filteredGroups?.filter(
                                          (scheduleElement) =>
                                              scheduleElement._course_id.toString() ===
                                              _course_id.toString(),
                                      )
                                    : true;
                            if (type === DC_STAFF ? isCourseMatched.length : true) {
                                for (const settingElement of setting) {
                                    const { gender, session_setting, _id, _group_no } =
                                        settingElement;

                                    let students = [];
                                    let channelName = '';
                                    let sessionIndex;
                                    const isMatchedSessionGroup =
                                        type === DC_STAFF &&
                                        isCourseMatched?.find((courseElement) => {
                                            sessionIndex = courseElement.student_groups?.findIndex(
                                                (stGrpElement) =>
                                                    stGrpElement.gender === gender &&
                                                    stGrpElement.group_no === _group_no,
                                            );
                                            return sessionIndex !== -1;
                                        });
                                    let isMatchedStudentGroup;
                                    if (group_mode === FYD.toString()) {
                                        isMatchedStudentGroup =
                                            type === DC_STAFF
                                                ? isCourseMatched.reduce((matchedGroup, course) => {
                                                      return (
                                                          matchedGroup ||
                                                          findMatchedStudentGroup({
                                                              course,
                                                              _group_no,
                                                              gender,
                                                          })
                                                      );
                                                  }, null)
                                                : true;
                                    } else {
                                        const sessionGroupIds = [];
                                        const matchedStudentGroup =
                                            type === DC_STAFF &&
                                            isCourseMatched.reduce((acc, curr) => {
                                                const matchedGenderBasedStudentGroup =
                                                    curr.student_groups?.find(
                                                        (scheduleGroupElement) =>
                                                            scheduleGroupElement.group_id?.toString() ===
                                                                _id.toString() &&
                                                            gender === scheduleGroupElement.gender,
                                                    );
                                                if (matchedGenderBasedStudentGroup) {
                                                    sessionGroupIds.push(
                                                        ...matchedGenderBasedStudentGroup.session_group,
                                                    );
                                                    acc = {
                                                        ...matchedGenderBasedStudentGroup,
                                                        session_group: [...sessionGroupIds],
                                                    };
                                                }

                                                return acc;
                                            }, null);

                                        isMatchedStudentGroup =
                                            type === DC_STAFF
                                                ? rotation === 'yes'
                                                    ? isMatchedSessionGroup &&
                                                      isMatchedSessionGroup.student_groups[
                                                          sessionIndex
                                                      ]
                                                    : matchedStudentGroup
                                                : true;
                                    }

                                    const newChannelGroupData = {
                                        gender,
                                        course_name,
                                        _course_id,
                                        course_no,
                                        rotation,
                                        rotationCount:
                                            rotation == 'yes' ? _group_no : rotation_count,
                                        level,
                                        term,
                                        year: master.year,
                                        programId: master._program_id,
                                        _institution_calendar_id,
                                    };
                                    if (isMatchedStudentGroup) {
                                        for (const sessionSettingElem of session_setting) {
                                            const { groups, session_type } = sessionSettingElem;
                                            for (const nestGroupElement of groups) {
                                                const { _student_ids, _id, group_no } =
                                                    nestGroupElement;
                                                const isScheduledSessionGroup =
                                                    type === DC_STAFF
                                                        ? isMatchedStudentGroup?.session_group?.find(
                                                              (sessionGroupElement) =>
                                                                  sessionGroupElement.session_group_id?.toString() ===
                                                                  _id.toString(),
                                                          )
                                                        : !students.length &&
                                                          _student_ids
                                                              .map((studentElement) =>
                                                                  studentElement.toString(),
                                                              )
                                                              .includes(userId);
                                                if (isScheduledSessionGroup) {
                                                    if (!students.length) {
                                                        students.push(..._student_ids);
                                                        channelName = `${gender[0].toUpperCase()}G-${
                                                            rotation === 'yes' ? 'R' : ''
                                                        }${_group_no ? +_group_no : 1}`;

                                                        // create delivery type sub group
                                                        // const deliveryGroupChannelName = `${gender[0].toUpperCase()}G-${
                                                        //     rotation === 'yes' ? 'R' : ''
                                                        // }${
                                                        //     _group_no ? +_group_no : 1
                                                        // }-${session_type}-G${group_no}`;

                                                        // ! Course Delivery Group Name with Course Code and Name
                                                        // const deliveryGroupChannelName = `${course_no}-${term
                                                        //     .charAt(0)
                                                        //     .toUpperCase()}T-${getConcatYearLevelCode(
                                                        //     {
                                                        //         yearLevelCode: master.year,
                                                        //     },
                                                        // )}-${getConcatYearLevelCode({
                                                        //     yearLevelCode: level,
                                                        // })}-${
                                                        //     rotation === 'yes'
                                                        //         ? 'R'
                                                        //         : gender[0].toUpperCase()
                                                        // }G${
                                                        //     _group_no ? +_group_no : 1
                                                        // }-${session_type}${group_no}`;
                                                        // Course Group Name with Course Code and Name
                                                        const deliveryGroupChannelName = `${
                                                            rotation === 'yes'
                                                                ? 'R'
                                                                : gender[0].toUpperCase()
                                                        }G${
                                                            _group_no ? +_group_no : 1
                                                        }-${session_type}${group_no}`;
                                                        if (
                                                            _student_ids.length &&
                                                            session_type &&
                                                            group_no
                                                        ) {
                                                            overAllGroup.push({
                                                                ...newChannelGroupData,
                                                                _student_ids,
                                                                channelName:
                                                                    deliveryGroupChannelName,
                                                            });
                                                            students = [];
                                                        }
                                                        queryForChatGroup.courseId.push(
                                                            _course_id.toString(),
                                                        );
                                                        queryForChatGroup.programId.push(
                                                            master._program_id.toString(),
                                                        );
                                                        queryForChatGroup.year.push(master.year);
                                                        queryForChatGroup.level.push(level);
                                                        queryForChatGroup.term.push(term);
                                                    }
                                                }
                                            }
                                        }
                                        if (students.length) {
                                            overAllGroup.push({
                                                ...newChannelGroupData,
                                                _student_ids: students,
                                                channelName,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    queryForChatGroup.courseId = [...new Set(queryForChatGroup.courseId)];
    queryForChatGroup.programId = [...new Set(queryForChatGroup.programId)];
    queryForChatGroup.year = [...new Set(queryForChatGroup.year)];
    queryForChatGroup.level = [...new Set(queryForChatGroup.level)];
    queryForChatGroup.term = [...new Set(queryForChatGroup.term)];

    return { overAllGroup, queryForChatGroup };
};

const formatGroupName = ({ channelName = '' }) => {
    const groupNameArr = Array.from(new Set(channelName.split(',')));
    return groupNameArr.sort((a, b) => a.localeCompare(b)).join();
};

const checkAndCreateGroups = ({ overAllGroup, studentGroupsFromDB, schedulesForStaff, userId }) => {
    const bulkOperation = [];
    for (const groupElement of overAllGroup) {
        const isChatChannelPresent = studentGroupsFromDB.find(
            (channelElement) =>
                channelElement.channelType === STUDENT_GROUP &&
                channelElement._institution_calendar_id.toString() ===
                    groupElement._institution_calendar_id.toString() &&
                channelElement.courseId.toString() === groupElement._course_id.toString() &&
                channelElement.channelName === groupElement.channelName &&
                channelElement.year === groupElement.year &&
                channelElement.level === groupElement.level &&
                channelElement.term === groupElement.term &&
                channelElement.gender === groupElement.gender &&
                channelElement.programId.toString() === groupElement.programId.toString() &&
                (channelElement.rotation === 'yes'
                    ? channelElement.rotationCount == groupElement.rotationCount
                    : true),
        );

        const scheduleStaffs = schedulesForStaff
            ?.filter(
                (scheduleElement) =>
                    scheduleElement._course_id?.toString() === groupElement._course_id?.toString(),
            )
            .map((filteredElement) =>
                filteredElement.staffs.map((stObjElement) => stObjElement._staff_id.toString()),
            )
            .flat();
        const admins = [...new Set(scheduleStaffs, userId.toString())];

        if (isChatChannelPresent) {
            const studentListWithCoordinators = groupElement._student_ids.concat(admins);
            const studentId_list = isChatChannelPresent.members.map((membersElement) =>
                membersElement.userId.toString(),
            );
            if (!studentId_list.includes(userId.toString())) {
                const updateGroup = studentListWithCoordinators.filter(
                    (studentElem) => !studentId_list.includes(studentElem.toString()),
                );
                const existingAdmins = isChatChannelPresent?.admin?.map((exAdminElement) =>
                    exAdminElement?.toString(),
                );
                const updateAdmins = admins.filter(
                    (adminElement) => !existingAdmins.includes(adminElement),
                );
                const mapMembersforCollection = updateGroup.map((idElement) => ({
                    userId: idElement,
                }));
                bulkOperation.push({
                    updateOne: {
                        filter: {
                            channelName: groupElement.channelName,
                            courseId: groupElement._course_id,
                            year: groupElement.year,
                            level: groupElement.level,
                            term: groupElement.term,
                            course_name: groupElement.course_name,
                            course_no: groupElement.course_no,
                            programId: groupElement.programId,
                            _institution_calendar_id: groupElement._institution_calendar_id,
                        },
                        update: {
                            $addToSet: {
                                members: { $each: mapMembersforCollection },
                                ...(updateAdmins.length && {
                                    admin: { $each: updateAdmins },
                                }),
                            },
                        },
                    },
                });
            }
        } else {
            let membersList = groupElement._student_ids.map((studentIdElement) => ({
                userId: studentIdElement.toString(),
            }));

            if (admins.length) {
                digiChatChannelcoordinatorIds = admins.map((adminElement) => ({
                    userId: adminElement,
                }));
                membersList = membersList.concat(digiChatChannelcoordinatorIds);
            }
            bulkOperation.push({
                insertOne: {
                    document: {
                        channelType: STUDENT_GROUP,
                        ...groupElement,
                        admin: admins,
                        courseId: groupElement._course_id,
                        members: membersList,
                    },
                },
            });
        }
        const isAllCourseGroupExists = studentGroupsFromDB.find(
            (grpElement) =>
                grpElement.channelType === COURSE_GROUP &&
                grpElement.courseId.toString() === groupElement._course_id.toString() &&
                grpElement._institution_calendar_id.toString() ===
                    groupElement._institution_calendar_id.toString() &&
                (groupElement.rotation === 'yes'
                    ? groupElement.rotationCount == grpElement.rotationCount
                    : true) &&
                grpElement.members.find(
                    (memberElement) => memberElement.userId?.toString() === userId.toString(),
                ),
        );
        if (!isAllCourseGroupExists) {
            const overAllCourseGroupWithoutMem = studentGroupsFromDB.find(
                (grpElement) =>
                    grpElement.channelType === COURSE_GROUP &&
                    (groupElement.rotation === 'yes'
                        ? groupElement.rotationCount == grpElement.rotationCount
                        : true) &&
                    grpElement.courseId.toString() === groupElement._course_id.toString() &&
                    grpElement._institution_calendar_id.toString() ===
                        groupElement._institution_calendar_id.toString(),
            );
            let existingCourseAdmins = [];
            let existingCourseMembers = [];
            if (overAllCourseGroupWithoutMem) {
                //group is already there but the current member is not there

                const existingMembers = overAllCourseGroupWithoutMem?.members?.map((userElement) =>
                    userElement?.userId?.toString(),
                );
                const currentMembers = groupElement._student_ids
                    .map((studentElement) => studentElement.toString())
                    .concat(admins.map((adminElement) => adminElement.toString()));

                const mergedMembers =
                    currentMembers.filter(
                        (currentStudentElement) => !existingMembers.includes(currentStudentElement),
                    ) || [];
                const existingAdmins = overAllCourseGroupWithoutMem.admin.map((adminIdElement) =>
                    adminIdElement.toString(),
                );
                const mergedAdmins =
                    admins.filter((adminElement) => !existingAdmins.includes(adminElement)) || [];
                let channelName;
                if (
                    !overAllCourseGroupWithoutMem.channelName
                        ?.split(',')
                        ?.includes(groupElement.channelName)
                )
                    channelName = `${overAllCourseGroupWithoutMem.channelName},${groupElement.channelName}`;

                bulkOperation.push({
                    updateOne: {
                        filter: {
                            channelType: COURSE_GROUP,
                            courseId: groupElement._course_id,
                            year: groupElement.year,
                            level: groupElement.level,
                            term: groupElement.term,
                            programId: groupElement.programId,
                            course_name: groupElement.course_name,
                            course_no: groupElement.course_no,
                            _institution_calendar_id: groupElement._institution_calendar_id,
                        },
                        update: {
                            channelName:
                                formatGroupName({ channelName }) ||
                                formatGroupName({
                                    channelName: overAllCourseGroupWithoutMem.channelName,
                                }),
                            ...(groupElement.rotationCount && {
                                rotationCount: groupElement.rotationCount,
                            }),
                            gender: groupElement.gender,
                            $addToSet: {
                                members: {
                                    $each: mergedMembers.map((memberElement) => ({
                                        userId: convertToMongoObjectId(memberElement),
                                    })),
                                },
                                admin: {
                                    $each: mergedAdmins.map((adminElement) =>
                                        convertToMongoObjectId(adminElement),
                                    ),
                                },
                            },
                        },
                        upsert: true,
                    },
                });
                existingCourseMembers = existingMembers;
                existingCourseAdmins = existingAdmins;
                overAllCourseGroupWithoutMem.channelType = '';
            } else {
                const isOverallCourseGroupCreated = bulkOperation.find(
                    (grpElement) =>
                        grpElement?.updateOne?.filter?.channelType === COURSE_GROUP &&
                        grpElement?.updateOne?.filter?.courseId?.toString() ===
                            groupElement?._course_id?.toString() &&
                        grpElement?.updateOne?.filter?._institution_calendar_id?.toString() ===
                            groupElement?._institution_calendar_id?.toString(),
                );
                if (isOverallCourseGroupCreated) {
                    const existingMembers =
                        isOverallCourseGroupCreated?.updateOne?.update?.$addToSet?.members?.$each?.map(
                            (userElement) => userElement.userId.toString(),
                        ) || [];

                    const currentMembers = groupElement._student_ids
                        .map((studentElement) => studentElement.toString())
                        .concat(admins.map((adminElement) => adminElement.toString()));

                    const mergedMembers = [
                        ...new Set(
                            currentMembers
                                .concat(existingMembers)
                                ?.filter(
                                    (currentMemElement) =>
                                        !existingCourseMembers.includes(currentMemElement),
                                ),
                        ),
                    ];
                    const existingStaffs =
                        isOverallCourseGroupCreated?.updateOne?.update?.admin?.map(
                            (adminIdElement) => adminIdElement.toString(),
                        ) || [];

                    const mergedAdmins = [
                        ...new Set(
                            admins
                                .concat(existingStaffs)
                                ?.filter(
                                    (staffElement) => !existingCourseAdmins.includes(staffElement),
                                ),
                        ),
                    ];
                    const existingGenders = isOverallCourseGroupCreated?.updateOne?.update?.gender;
                    const gendersWithoutDups = [
                        ...new Set(`${existingGenders},${groupElement.gender}`.split(',')),
                    ].join();

                    if (mergedMembers.length)
                        isOverallCourseGroupCreated.updateOne.update.$addToSet.members = {
                            $each: mergedMembers.map((memberElement) => ({
                                userId: convertToMongoObjectId(memberElement),
                            })),
                        };

                    if (mergedAdmins.length)
                        isOverallCourseGroupCreated.updateOne.update.$addToSet.admin.$each =
                            mergedAdmins.map((adminElement) =>
                                convertToMongoObjectId(adminElement),
                            );
                    isOverallCourseGroupCreated.updateOne.update.gender = gendersWithoutDups;
                    isOverallCourseGroupCreated.updateOne.update.channelName = formatGroupName({
                        channelName: `${isOverallCourseGroupCreated.updateOne.update.channelName},${groupElement.channelName}`,
                    });
                } else {
                    const members = [
                        ...new Set(
                            groupElement._student_ids
                                .concat(admins)
                                .map((userIdElement) => userIdElement.toString()),
                        ),
                    ].map((studentElement) => ({
                        userId: studentElement,
                    }));
                    bulkOperation.push({
                        updateOne: {
                            filter: {
                                channelType: COURSE_GROUP,
                                courseId: groupElement._course_id,
                                year: groupElement.year,
                                level: groupElement.level,
                                term: groupElement.term,
                                course_name: groupElement.course_name,
                                course_no: groupElement.course_no,
                                programId: groupElement.programId,
                                _institution_calendar_id: groupElement._institution_calendar_id,
                                ...(groupElement.rotationCount && {
                                    rotationCount: groupElement.rotationCount,
                                }),
                            },
                            update: {
                                channelName: formatGroupName({
                                    channelName: groupElement.channelName,
                                }),
                                gender: groupElement.gender,
                                $addToSet: {
                                    members: { $each: members },
                                    admin: {
                                        $each: admins.map((adminElement) =>
                                            convertToMongoObjectId(adminElement),
                                        ),
                                    },
                                },
                            },
                            upsert: true,
                        },
                    });
                }
            }
        }
        if (
            isAllCourseGroupExists &&
            !isAllCourseGroupExists.channelName.split(',').includes(groupElement.channelName)
        ) {
            bulkOperation.push({
                updateOne: {
                    filter: {
                        channelType: COURSE_GROUP,
                        courseId: groupElement._course_id,
                        year: groupElement.year,
                        level: groupElement.level,
                        term: groupElement.term,
                        course_name: groupElement.course_name,
                        course_no: groupElement.course_no,
                        programId: groupElement.programId,
                        _institution_calendar_id: groupElement._institution_calendar_id,
                        ...(groupElement.rotationCount && {
                            rotationCount: groupElement.rotationCount,
                        }),
                    },
                    update: {
                        channelName: formatGroupName({
                            channelName: `${isAllCourseGroupExists.channelName},${groupElement.channelName}`,
                        }),
                        gender: `${isAllCourseGroupExists.gender},${groupElement.gender}`,
                        $addToSet: {
                            members: {
                                $each: groupElement._student_ids.map((studentElement) => ({
                                    userId: studentElement,
                                })),
                            },
                            admin: {
                                $each: admins.map((adminElement) =>
                                    convertToMongoObjectId(adminElement),
                                ),
                            },
                        },
                    },
                    upsert: true,
                },
            });
        }
    }

    return bulkOperation;
};

const getChannelDetails = async ({ availableChannels, userId }) => {
    const userIdObj = convertToMongoObjectId(userId);
    const channelsIds = [];
    const blockedDates = {};
    for (const channelElement of availableChannels) {
        channelsIds.push(channelElement._id);
        for (const memberElement of channelElement.members) {
            if (memberElement.isBlocked && memberElement.userId.toString() === userId) {
                channelElement.blockedDate = memberElement.blockedDate;
                channelElement.isBlocked = memberElement.isBlocked;
                blockedDates[channelElement._id] = memberElement.blockedDate;
            }
        }
    }
    const messagesForUsersChannels = await messageSchema.aggregate([
        {
            $match: {
                channelId: { $in: channelsIds },
                isDeleted: false,
                deletedFor: { $nin: [userIdObj] },
            },
        },
        {
            $addFields: {
                blockedDate: {
                    $cond: {
                        if: {
                            $in: [
                                '$channelId',
                                Object.keys(blockedDates).map((id) => convertToMongoObjectId(id)),
                            ],
                        },
                        then: {
                            $let: {
                                vars: {
                                    matchedItem: {
                                        $filter: {
                                            input: Object.entries(blockedDates).map(
                                                ([id, date]) => ({
                                                    id: convertToMongoObjectId(id),
                                                    date,
                                                }),
                                            ),
                                            cond: { $eq: ['$$this.id', '$channelId'] },
                                        },
                                    },
                                },
                                in: { $arrayElemAt: ['$$matchedItem.date', 0] },
                            },
                        },
                        else: null,
                    },
                },
            },
        },
        {
            $match: {
                $or: [
                    { blockedDate: { $eq: null } },
                    { $expr: { $lte: ['$createdAt', '$blockedDate'] } },
                ],
            },
        },
        { $sort: { _id: -1 } },
        {
            $group: {
                _id: '$channelId',
                unreadCount: {
                    $sum: {
                        $cond: {
                            if: { $in: [userIdObj, '$members'] },
                            then: 0,
                            else: 1,
                        },
                    },
                },
                createdAt: { $first: '$createdAt' },
                createdBy: { $first: '$createdBy' },
                msg: { $first: '$msg' },
                fileName: { $first: '$fileName' },
                blockedDate: { $first: '$blockedDate' },
            },
        },
        {
            $project: {
                _id: 1,
                unreadCount: {
                    $cond: [{ $lte: ['$unreadCount', 11] }, '$unreadCount', 11],
                },
                createdAt: 1,
                createdBy: 1,
                msg: 1,
                fileName: {
                    $cond: {
                        if: { $ne: ['$fileName', null] },
                        then: '$fileName',
                        else: '$$REMOVE',
                    },
                },
                blockedDate: 1,
            },
        },
    ]);
    const userData = await userSchema
        .find(
            { _id: messagesForUsersChannels.map((channelElement) => channelElement.createdBy) },
            { 'name.first': 1, 'name.last': 1, user_id: 1 },
        )
        .lean();
    const userMap = userData.reduce((map, user) => {
        map[user._id.toString()] = { name: user.name, academic_no: user.user_id };
        return map;
    }, {});
    const channelMessage = availableChannels
        .map((channelElement) => {
            const selectedChannel = messagesForUsersChannels.find(
                (messageElement) => messageElement._id.toString() === channelElement._id.toString(),
            );
            const isCurrentUser = channelElement.members.every(
                (memberElement) => memberElement.userId.toString() === userId,
            );
            return {
                ...channelElement,
                unreadCount: selectedChannel?.unreadCount ?? 0,
                lastMsgTime: selectedChannel?.createdAt,
                lastMsgSender: userMap[selectedChannel?.createdBy.toString()]?.name,
                academic_no: userMap[selectedChannel?.createdBy.toString()]?.academic_no,
                msg: selectedChannel?.msg,
                fileName: selectedChannel?.fileName,
                currentUser: isCurrentUser,
                ...(isCurrentUser && { userType: channelElement?.members[0]?.userType }),
            };
        })
        .sort((a, b) => {
            if (!a.lastMsgTime) return 1;
            if (!b.lastMsgTime) return -1;
            return new Date(b.lastMsgTime) - new Date(a.lastMsgTime);
        });
    return channelMessage;
};
module.exports = {
    getUserChannelList,
    getScheduledActiveCalendars,
    getOverallGroup,
    checkAndCreateGroups,
    formatGroupName,
    getChannelDetails,
    getUnreadCountForChannels,
};
