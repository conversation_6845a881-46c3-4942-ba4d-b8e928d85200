let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let countrySchema = new Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    code: {
        type: String,
        required: true,
        trim: true,
    },
    currency: {
        type: String,
        required: true,
        trim: true,
    },
    regions: [
        {
            id: {
                type: Number,
                required: true,
            },
            code: {
                type: String,
                required: true,
            },
            name_ar: {
                type: String,
                required: true,
            },
            name: {
                type: String,
                required: true,
            },
            cities: [
                {
                    id: {
                        type: Number,
                        required: true,
                    },
                    name_ar: {
                        type: String,
                        required: true,
                    },
                    name: {
                        type: String,
                        required: true,
                    },
                    districts: [
                        {
                            id: {
                                type: Number,
                                required: true,
                            },
                            name_ar: {
                                type: String,
                                required: true,
                            },
                            name: {
                                type: String,
                                required: true,
                            },
                        },
                    ],
                },
            ],
        },
    ],
    isDeleted: {
        type: Boolean,
        default: false,
    },
    isActive: {
        type: Boolean,
        default: true,
    }
},
    { timestamps: true });

// countrySchema.index({ name: 1, isDeleted: 1 }, { unique: true });
// module.exports = mongoose.model('conty', countrySchema);
module.exports = mongoose.model(constant.COUNTRY_STATE_CITY, countrySchema);