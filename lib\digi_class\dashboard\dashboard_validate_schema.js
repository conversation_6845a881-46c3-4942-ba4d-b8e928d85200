const Joi = require('joi');
const {
    DC_STAFF,
    DC_STUDENT,
    DASHBOARD: { ACTIVITIES, SESSIONS, COURSES, DOCUMENTS, RATINGS },
} = require('../../utility/constants');
const { com_response } = require('../../utility/common');

// get dashboard schema
function getDashboardSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                type: Joi.string()
                    .valid(...[DC_STAFF, DC_STUDENT])
                    .required()
                    .error(() => {
                        return req.t('STUDENT_STAFF_TYPE_REQUIRED');
                    }),
                userId: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
                moduleType: Joi.string()
                    .valid(...[ACTIVITIES, SESSIONS, COURSES, DOCUMENTS, RATINGS])
                    .error(() => {
                        return req.t('MODULE_TYPE_REQUIRED');
                    }),
                courseAdmin: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('IS_COURSE_ADMIN');
                    }),
            },
            headers: Joi.object({
                _institution_id: Joi.string().length(24).required(),
            }).options({ allowUnknown: true }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

// get dashboard altered schema
function getDashboardModuleSchema(req, res, next) {
    const schema = Joi.object()
        .keys({
            query: {
                type: Joi.string()
                    .valid(...[DC_STAFF, DC_STUDENT])
                    .required()
                    .error(() => {
                        return req.t('STUDENT_STAFF_TYPE_REQUIRED');
                    }),
                userId: Joi.string()
                    .length(24)
                    .required()
                    .error(() => {
                        return req.t('USER_ID_REQUIRED');
                    }),
                institutionCalendarId: Joi.string()
                    .length(24)
                    .optional()
                    .error(() => {
                        return req.t('INSTITUTION_CALENDAR_ID');
                    }),
                dateTime: Joi.optional(),
                timeZone: Joi.string().optional(),
                courseAdmin: Joi.boolean()
                    .optional()
                    .error(() => {
                        return req.t('IS_COURSE_ADMIN');
                    }),
            },
            headers: Joi.object({
                _institution_id: Joi.string().length(24),
            }).options({ allowUnknown: true }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
}

module.exports = {
    getDashboardSchema,
    getDashboardModuleSchema,
};
