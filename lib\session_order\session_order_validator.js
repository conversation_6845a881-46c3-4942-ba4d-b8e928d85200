const Joi = require('joi');
const common_files = require('../utility/common');

exports.session_order = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            data: Joi.array().items(
                Joi.object().keys({
                    s_no: Joi.number().min(1).required().error(error => {
                        return error;
                    }),
                    delivery_symbol: Joi.string().min(1).trim().required().error(error => {
                        return error;
                    }),
                    delivery_no: Joi.number().min(1).required().error(error => {
                        return error;
                    }),
                    session_name: Joi.string().min(3).trim().required().error(error => {
                        return error;
                    }),
                    _subject_id: Joi.array().items(Joi.string().alphanum().length(24).required()).error(error => {
                        return error;
                    }),
                    contact_hours: Joi.number().min(1).required().error(error => {
                        return error;
                    }),
                    week: Joi.number().min(1).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.session_order_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            data: Joi.array().items(
                Joi.object().keys({
                    s_no: Joi.number().min(1).error(error => {
                        return error;
                    }),
                    delivery_symbol: Joi.string().min(1).trim().error(error => {
                        return error;
                    }),
                    delivery_no: Joi.number().min(1).error(error => {
                        return error;
                    }),
                    session_name: Joi.string().min(3).trim().error(error => {
                        return error;
                    }),
                    _subject_id: Joi.array().items(Joi.string().alphanum().length(24).required()).error(error => {
                        return error;
                    }),
                    contact_hours: Joi.number().min(1).error(error => {
                        return error;
                    }),
                    week: Joi.number().min(1).error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.session_order_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}