const {
    convertToMongoObjectId,
    convertToAllCase,
    convertToUpperCase,
    getModel,
} = require('../../utility/common');
const institutionSchema = require('../institution/institution.model');
const sessionDeliveryTypeSchema = require('./session-delivery-types.model');
const courseSchema = require('../course/course.model');
const curriculumSchema = require('../curriculum/curriculum.model');
const {
    SESSION_DELIVERY_TYPES,
    CURRICULUM,
    INSTITUTION,
    SESSION_ORDER,
    COURSE_TYPE,
    COURSE,
} = require('../../utility/constants');

const sessionOrderSchema = require('../session-order/session-order.model');
const CourseSchema = require('../course/course.model');
//todo:  Create Session Types
const sessionTypesAdd = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const {
            _program_id,
            programName,
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
            _institution_id,
        } = body;

        const { payload } = headers;

        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionData = await sessionDeliveryModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    isDeleted: false,
                },
                { _id: 1, sessionName: 1, sessionSymbol: 1 },
            )
            .lean();
        let isDuplicateSessionName = false;
        let isDuplicateSessionSymbol = false;
        if (sessionData.length) {
            sessionData.forEach((session) => {
                if (session.sessionName.toLowerCase() === sessionName.toLowerCase()) {
                    isDuplicateSessionName = true;
                } else if (session.sessionSymbol.toLowerCase() === sessionSymbol.toLowerCase()) {
                    isDuplicateSessionSymbol = true;
                }
            });
        }
        if (isDuplicateSessionName) {
            return { statusCode: 410, message: 'SESSION_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateSessionSymbol) {
            return { statusCode: 410, message: 'SESSION_SYMBOL_ALREADY_EXISTS' };
        }

        const createQuery = {
            _institution_id,
            _program_id,
            programName,
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
        };
        const sessionDeliveryCreation = await sessionDeliveryModel.create(createQuery);
        if (!sessionDeliveryCreation) return { statusCode: 500, message: 'DS_CREATE_FAILED' };

        return { statusCode: 201, message: 'DS_CREATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addSessionTypesForIndependentCourse = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const {
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
            _institution_id,
        } = body;

        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionData = await sessionDeliveryModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: { $exists: false },
                    isDeleted: false,
                },
                { _id: 1, sessionName: 1, sessionSymbol: 1 },
            )
            .lean();
        let isDuplicateSessionName = false;
        let isDuplicateSessionSymbol = false;
        if (sessionData.length) {
            sessionData.forEach((session) => {
                if (session.sessionName.toLowerCase() === sessionName.toLowerCase()) {
                    isDuplicateSessionName = true;
                } else if (session.sessionSymbol.toLowerCase() === sessionSymbol.toLowerCase()) {
                    isDuplicateSessionSymbol = true;
                }
            });
        }
        if (isDuplicateSessionName) {
            return { statusCode: 410, message: 'SESSION_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateSessionSymbol) {
            return { statusCode: 410, message: 'SESSION_SYMBOL_ALREADY_EXISTS' };
        }

        const createQuery = {
            _institution_id,
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
        };
        const sessionDeliveryCreation = await sessionDeliveryModel.create(createQuery);
        if (!sessionDeliveryCreation) return { statusCode: 500, message: 'DS_CREATE_FAILED' };

        return { statusCode: 201, message: 'DS_CREATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Create Delivey Types
const deliveryTypesAdd = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { deliveryDuration, deliveryName, deliverySymbol, _program_id, _institution_id } =
            body;
        const { sessionTypeId } = params;
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryModel.findOne(sessionTypeQuery, { _id: 1 }).lean();
        if (!sessionData) return { statusCode: 410, message: 'DS_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    _id: convertToMongoObjectId(sessionTypeId),
                    isDeleted: false,
                },
                { _id: 1, deliveryTypes: 1 },
            )
            .lean();

        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        if (duplicateCheck.deliveryTypes !== null)
            duplicateCheck.deliveryTypes.forEach((delivery) => {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = { _id: sessionData._id, isDeleted: false };
        const deliveryTypeObject = {
            $push: {
                deliveryTypes: {
                    deliveryName,
                    deliverySymbol,
                    deliveryDuration,
                },
            },
        };

        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeObject,
        );
        if (sessionDeliveryUpdation)
            return {
                statusCode: 201,
                message: 'DS_CREATED',
            };
        return { statusCode: 500, message: 'DS_CREATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addIndependentCourseDeliveryType = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { deliveryDuration, deliveryName, deliverySymbol, _institution_id } = body;
        const { id } = params;
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(id),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryModel.findOne(sessionTypeQuery, { _id: 1 }).lean();
        if (!sessionData) return { statusCode: 410, message: 'DS_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: { $exists: false },
                    _id: convertToMongoObjectId(id),
                    isDeleted: false,
                },
                { _id: 1, deliveryTypes: 1 },
            )
            .lean();

        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        if (duplicateCheck.deliveryTypes !== null)
            duplicateCheck.deliveryTypes.forEach((delivery) => {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = { _id: sessionData._id, isDeleted: false };
        const deliveryTypeObject = {
            $push: {
                deliveryTypes: {
                    deliveryName,
                    deliverySymbol,
                    deliveryDuration,
                },
            },
        };

        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeObject,
        );
        if (sessionDeliveryUpdation)
            return {
                statusCode: 201,
                message: 'DS_CREATED',
            };
        return { statusCode: 500, message: 'DS_CREATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Update Session Types

const sessionTypesUpdate = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const {
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
            _program_id,
            _institution_id,
        } = body;
        const { sessionTypeId } = params;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryTypesModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryTypesModel
            .findOne(sessionTypeQuery, { _id: 1 })
            .lean();
        if (!sessionData) return { statusCode: 410, message: 'DS_NOT_FOUND' };
        const checkDuplicate = await sessionDeliveryTypesModel
            .find(
                {
                    _id: { $ne: convertToMongoObjectId(sessionTypeId) },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    isDeleted: false,
                },
                { _id: 1, sessionName: 1, sessionSymbol: 1 },
            )
            .lean();

        let isDuplicateSessionName = false;
        let isDuplicateSessionSymbol = false;
        if (checkDuplicate.length) {
            checkDuplicate.forEach((session) => {
                if (session.sessionName.toLowerCase() === sessionName.toLowerCase()) {
                    isDuplicateSessionName = true;
                } else if (session.sessionSymbol.toLowerCase() === sessionSymbol.toLowerCase()) {
                    isDuplicateSessionSymbol = true;
                }
            });
        }
        if (isDuplicateSessionName) {
            return { statusCode: 410, message: 'SESSION_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateSessionSymbol) {
            return { statusCode: 410, message: 'SESSION_SYMBOL_ALREADY_EXISTS' };
        }

        const objectUpdate = {
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
        };
        const sessionUpdate = { _id: convertToMongoObjectId(sessionTypeId) };
        const sessionDeliveryUpdation = await sessionDeliveryTypesModel.findByIdAndUpdate(
            sessionUpdate,
            objectUpdate,
        );
        if (sessionDeliveryUpdation) {
            const curriculumDocs = await curriculumModel.updateMany(
                {
                    'creditHours.credit.sessionType': sessionDeliveryUpdation.sessionName,
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                },
                { 'creditHours.credit.$[session].sessionType': sessionName },
                { arrayFilters: [{ 'session.sessionType': sessionDeliveryUpdation.sessionName }] },
            );
            const courseDocs = await courseModel.updateMany(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
                },
                {
                    'sessionDeliveryType.$[session].typeName': sessionName,
                    'sessionDeliveryType.$[session].typeSymbol': sessionSymbol,
                },
                {
                    arrayFilters: [
                        { 'session._session_id': convertToMongoObjectId(sessionTypeId) },
                    ],
                },
            );
            return { statusCode: 200, message: 'DS_UPDATED' };
        }
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateIndependentCourseSessionType = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const {
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
            _institution_id,
        } = body;
        const { sessionTypeId } = params;
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryModel.findOne(sessionTypeQuery, { _id: 1 }).lean();
        if (!sessionData) return { statusCode: 400, message: 'DS_NOT_FOUND' };
        const checkDuplicate = await sessionDeliveryModel
            .findOne(
                {
                    _id: { $ne: sessionTypeId },
                    sessionName: { $in: convertToAllCase(sessionName) },
                    sessionSymbol: { $in: convertToAllCase(sessionSymbol) },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                { _id: 1 },
            )
            .lean();
        if (checkDuplicate)
            return { statusCode: 410, message: 'SESSION_NAME_OR_SESSION_SYMBOL_ALREADY_EXISTS' };
        const objectUpdate = {
            sessionName,
            sessionSymbol,
            contactHoursOrPeriodOfWeek,
            sessionOrPeriodDuration,
            creditHoursMode,
        };
        const sessionUpdate = { _id: convertToMongoObjectId(sessionTypeId) };
        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            sessionUpdate,
            objectUpdate,
        );
        if (sessionDeliveryUpdation) return { statusCode: 200, message: 'DS_UPDATED' };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Update Delivery Types
const deliveryTypesUpdate = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { deliveryDuration, deliveryName, deliverySymbol, _program_id, _institution_id } =
            body;
        const { sessionTypeId, deliveryTypeId } = params;
        const sessionTypeChkQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionDelivery = await sessionDeliveryModel
            .findOne(sessionTypeChkQuery, {
                _id: 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'SESSTION_TYPE_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne(
                {
                    _id: convertToMongoObjectId(sessionTypeId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    isDeleted: false,
                },
                { _id: 1, deliveryTypes: 1 },
            )
            .lean();

        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        duplicateCheck.deliveryTypes.forEach((delivery) => {
            if (String(delivery._id) !== String(deliveryTypeId)) {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            }
        });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = {
            _id: convertToMongoObjectId(sessionDelivery._id),
            isDeleted: false,
        };
        const deliveryTypeobject = {
            $set: {
                'deliveryTypes.$[i].deliveryName': deliveryName,
                'deliveryTypes.$[i].deliverySymbol': deliverySymbol,
                'deliveryTypes.$[i].deliveryDuration': deliveryDuration,
            },
        };
        const deliveryTypefilter = {
            arrayFilters: [
                {
                    'i._id': deliveryTypeId,
                },
            ],
        };
        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeobject,
            deliveryTypefilter,
        );
        if (sessionDeliveryUpdation) {
            const sessionOrder = await sessionOrderModel.updateMany(
                { _deliveryType_id: convertToMongoObjectId(deliveryTypeId), isDeleted: false },
                {
                    deliveryType: deliveryName,
                    deliverySymbol,
                },
            );
            const courseDocs = await courseModel.updateMany(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(_program_id),
                    'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
                    'sessionDeliveryType.deliveryType._delivery_id':
                        convertToMongoObjectId(deliveryTypeId),
                },
                {
                    'sessionDeliveryType.$[session].deliveryType.$[delivery].deliveryType':
                        deliveryName,
                    'sessionDeliveryType.$[session].deliveryType.$[delivery].deliverySymbol':
                        deliverySymbol,
                },
                {
                    arrayFilters: [
                        { 'session._session_id': convertToMongoObjectId(sessionTypeId) },
                        { 'delivery._delivery_id': convertToMongoObjectId(deliveryTypeId) },
                    ],
                },
            );
            return { statusCode: 200, message: 'DS_UPDATED' };
        }
        return { statusCode: 500, message: 'DS_UPDATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateIndependentCourseDeliveryType = async ({ body, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { deliveryDuration, deliveryName, deliverySymbol, _institution_id } = body;
        const { sessionTypeId, deliveryTypeId } = params;
        const sessionTypeChkQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionDelivery = await sessionDeliveryModel
            .findOne(sessionTypeChkQuery, {
                _id: 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'SESSTION_TYPE_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne({
                _id: sessionTypeId,
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            })
            .lean();
        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        duplicateCheck.deliveryTypes.forEach((delivery) => {
            if (String(delivery._id) !== String(deliveryTypeId)) {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            }
        });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = {
            _id: convertToMongoObjectId(sessionDelivery._id),
            isDeleted: false,
        };
        const deliveryTypeobject = {
            $set: {
                'deliveryTypes.$[i].deliveryName': deliveryName,
                'deliveryTypes.$[i].deliverySymbol': deliverySymbol,
                'deliveryTypes.$[i].deliveryDuration': deliveryDuration,
            },
        };
        const deliveryTypefilter = {
            arrayFilters: [
                {
                    'i._id': deliveryTypeId,
                },
            ],
        };
        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeobject,
            deliveryTypefilter,
        );
        if (sessionDeliveryUpdation) return { statusCode: 200, message: 'DS_UPDATED' };
        return { statusCode: 500, message: 'DS_UPDATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Delete Session Types
const sessionTypesDelete = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { sessionTypeId } = params;
        const deleteQuery = { _id: convertToMongoObjectId(sessionTypeId) };
        const deleteObj = { isDeleted: true };
        const sessionDeliveryTypesDocs = await sessionDeliveryModel
            .findById({ _id: convertToMongoObjectId(sessionTypeId) })
            .select('_institution_id _program_id sessionName');
        const curriculumDocs = await curriculumModel.find({
            _institution_id: convertToMongoObjectId(sessionDeliveryTypesDocs._institution_id),
            _program_id: convertToMongoObjectId(sessionDeliveryTypesDocs._program_id),
            'creditHours.credit.sessionType': sessionDeliveryTypesDocs.sessionName,
            isDeleted: false,
        });
        if (curriculumDocs.length)
            return {
                statusCode: 404,
                message: 'SESSION_TYPE_ALREADY_MAPPED_WITH_CURRICULUM',
            };
        const courseDocs = await courseModel.find({
            isDeleted: false,
            'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
        });
        if (courseDocs.length)
            return {
                statusCode: 404,
                message: 'SESSION_TYPE_ALREADY_MAPPED_WITH_COURSES',
            };
        const sessionDelivery = await sessionDeliveryModel.updateOne(deleteQuery, deleteObj);
        if (!sessionDelivery) return { statusCode: 500, message: 'DS_DELETE_FAILED' };
        return { statusCode: 200, message: 'DS_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteIndependentSessionTypes = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { sessionTypeId } = params;
        const deleteQuery = { _id: convertToMongoObjectId(sessionTypeId) };
        const deleteObj = { isDeleted: true };
        const courseDocs = await courseModel.find({
            isDeleted: false,
            courseType: COURSE_TYPE.INDEPENDENT,
            'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
        });
        if (courseDocs.length)
            return {
                statusCode: 404,
                message: 'SESSION_TYPE_ALREADY_MAPPED_WITH_COURSES',
            };
        const sessionDelivery = await sessionDeliveryModel.updateOne(deleteQuery, deleteObj);
        if (!sessionDelivery) return { statusCode: 500, message: 'DS_DELETE_FAILED' };
        return { statusCode: 200, message: 'DS_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Delete Delivery Types
const deliveryTypesDelete = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { sessionTypeId, deliveryTypeId } = params;
        const sessionTypeCheckQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionOrder = await sessionOrderModel.find({
            _deliveryType_id: convertToMongoObjectId(deliveryTypeId),
            isDeleted: false,
        });

        if (sessionOrder.length > 0) {
            return { statusCode: 500, message: 'DELIVERY_TYPE_IS_ADDED_IN_SESSION_ORDER' };
        }

        const sessionDelivery = await sessionDeliveryModel
            .findOne(sessionTypeCheckQuery, {
                _id: 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'SESSTION_TYPE_NOT_FOUND' };
        const courseDocs = await courseModel.find({
            isDeleted: false,
            'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
            'sessionDeliveryType.deliveryType._delivery_id': convertToMongoObjectId(deliveryTypeId),
        });
        if (courseDocs.length)
            return {
                statusCode: 404,
                message: 'DELIVERY_TYPE_ALREADY_MAPPED_WITH_COURSES',
            };
        const deliveryTypeQuery = { _id: convertToMongoObjectId(sessionTypeId), isDeleted: false };
        const deliveryTypeObject = {
            $pull: { deliveryTypes: { _id: convertToMongoObjectId(deliveryTypeId) } },
        };
        const deliveryTypeUpdate = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeObject,
        );
        if (deliveryTypeUpdate) return { statusCode: 200, message: 'DS_DELETED' };
        return { statusCode: 500, message: 'DS_DELETE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Get Session Types
const sessionTypesGet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { _program_id } = params;
        const sessionTypeQuery = {
            _program_id: convertToMongoObjectId(_program_id),
            isDeleted: false,
        };
        const sessionDelivery = await sessionDeliveryModel
            .find(sessionTypeQuery, {
                _id: 1,
                programName: 1,
                sessionName: 1,
                sessionSymbol: 1,
                contactHoursOrPeriodOfWeek: 1,
                sessionOrPeriodDuration: 1,
                creditHoursMode: 1,
                'deliveryTypes._id': 1,
                'deliveryTypes.deliveryName': 1,
                'deliveryTypes.deliverySymbol': 1,
                'deliveryTypes.deliveryDuration': 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'DS_NO_DATA_FOUND' };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: sessionDelivery };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getIndependentCourseSessionTypes = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { _institution_id } = params;
        const sessionTypeQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _program_id: { $exists: false },
            isDeleted: false,
        };
        const sessionDelivery = await sessionDeliveryModel
            .find(sessionTypeQuery, {
                _id: 1,
                programName: 1,
                sessionName: 1,
                sessionSymbol: 1,
                contactHoursOrPeriodOfWeek: 1,
                sessionOrPeriodDuration: 1,
                creditHoursMode: 1,
                'deliveryTypes._id': 1,
                'deliveryTypes.deliveryName': 1,
                'deliveryTypes.deliverySymbol': 1,
                'deliveryTypes.deliveryDuration': 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'DS_NO_DATA_FOUND' };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: sessionDelivery };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo:  Get Delivery Types
const deliveryTypesGet = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const sessionDelivery = await sessionDeliveryModel
            .find(
                { isDeleted: false, _program_id: { $exists: true } },
                {
                    _id: 1,
                    programName: 1,
                    sessionName: 1,
                    sessionSymbol: 1,
                    contactHoursOrPeriodOfWeek: 1,
                    sessionOrPeriodDuration: 1,
                    creditHoursMode: 1,
                    'deliveryTypes._id': 1,
                    'deliveryTypes.deliveryName': 1,
                    'deliveryTypes.deliverySymbol': 1,
                    'deliveryTypes.deliveryDuration': 1,
                },
            )
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'DS_NO_DATA_FOUND' };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: sessionDelivery };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sessionCreateIndianSystem = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { sessionName, sessionSymbol, _institution_id, programId } = body;

        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionData = await sessionDeliveryModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                { _id: 1, sessionName: 1, sessionSymbol: 1 },
            )
            .lean();
        let isDuplicateSessionName = false;
        let isDuplicateSessionSymbol = false;
        if (sessionData.length) {
            sessionData.forEach((session) => {
                if (session.sessionName.toLowerCase() === sessionName.toLowerCase()) {
                    isDuplicateSessionName = true;
                } else if (session.sessionSymbol.toLowerCase() === sessionSymbol.toLowerCase()) {
                    isDuplicateSessionSymbol = true;
                }
            });
        }
        if (isDuplicateSessionName) {
            return { statusCode: 410, message: 'SESSION_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateSessionSymbol) {
            return { statusCode: 410, message: 'SESSION_SYMBOL_ALREADY_EXISTS' };
        }

        let createQuery = {
            _institution_id,
            sessionName,
            sessionSymbol,
        };

        if (programId) {
            createQuery = { ...createQuery, _program_id: programId };
        }

        const sessionDeliveryCreation = await sessionDeliveryModel.create(createQuery);
        if (!sessionDeliveryCreation) return { statusCode: 500, message: 'DS_CREATE_FAILED' };

        return { statusCode: 201, message: 'DS_CREATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editSessionIndianSystem = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const { sessionName, sessionSymbol, _institution_id, programId } = body;
        const { sessionTypeId } = params;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryTypesModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryTypesModel
            .findOne(sessionTypeQuery, { _id: 1 })
            .lean();
        if (!sessionData) return { statusCode: 410, message: 'DS_NOT_FOUND' };
        let duplicateCheckQuery = {
            _id: { $ne: convertToMongoObjectId(sessionTypeId) },
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
        };
        if (programId) {
            duplicateCheckQuery = {
                ...duplicateCheckQuery,
                _program_id: convertToMongoObjectId(programId),
            };
        }
        const checkDuplicate = await sessionDeliveryTypesModel
            .find(duplicateCheckQuery, { _id: 1, sessionName: 1, sessionSymbol: 1 })
            .lean();

        let isDuplicateSessionName = false;
        let isDuplicateSessionSymbol = false;
        if (checkDuplicate.length) {
            checkDuplicate.forEach((session) => {
                if (session.sessionName.toLowerCase() === sessionName.toLowerCase()) {
                    isDuplicateSessionName = true;
                } else if (session.sessionSymbol.toLowerCase() === sessionSymbol.toLowerCase()) {
                    isDuplicateSessionSymbol = true;
                }
            });
        }
        if (isDuplicateSessionName) {
            return { statusCode: 410, message: 'SESSION_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateSessionSymbol) {
            return { statusCode: 410, message: 'SESSION_SYMBOL_ALREADY_EXISTS' };
        }

        const objectUpdate = {
            sessionName,
            sessionSymbol,
        };
        const sessionUpdate = { _id: convertToMongoObjectId(sessionTypeId) };
        const sessionDeliveryUpdation = await sessionDeliveryTypesModel.findByIdAndUpdate(
            sessionUpdate,
            objectUpdate,
        );
        if (sessionDeliveryUpdation) {
            let updateCurriculumQuery = {
                'creditHours.credit.sessionType': sessionDeliveryUpdation.sessionName,
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
            };
            if (programId) {
                updateCurriculumQuery = { ...updateCurriculumQuery, _program_id: programId };
            }
            const curriculumDocs = await curriculumModel.updateMany(
                updateCurriculumQuery,
                { 'creditHours.credit.$[session].sessionType': sessionName },
                { arrayFilters: [{ 'session.sessionType': sessionDeliveryUpdation.sessionName }] },
            );
            let updateCourseQuery = {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
            };
            if (programId) {
                updateCourseQuery = { ...updateCourseQuery, _program_id: programId };
            }
            const courseDocs = await courseModel.updateMany(
                updateCourseQuery,
                {
                    'sessionDeliveryType.$[session].typeName': sessionName,
                    'sessionDeliveryType.$[session].typeSymbol': sessionSymbol,
                },
                {
                    arrayFilters: [
                        { 'session._session_id': convertToMongoObjectId(sessionTypeId) },
                    ],
                },
            );
            return { statusCode: 200, message: 'DS_UPDATED' };
        }
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteSessionTypeIndianSystem = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { sessionTypeId } = params;
        const deleteQuery = { _id: convertToMongoObjectId(sessionTypeId) };
        const deleteObj = { isDeleted: true };
        const sessionDeliveryTypesDocs = await sessionDeliveryModel
            .findById({ _id: convertToMongoObjectId(sessionTypeId) })
            .select('_institution_id sessionName');
        const curriculumDocs = await curriculumModel.find({
            _institution_id: convertToMongoObjectId(sessionDeliveryTypesDocs._institution_id),
            'creditHours.credit.sessionType': sessionDeliveryTypesDocs.sessionName,
            isDeleted: false,
        });
        if (curriculumDocs.length)
            return {
                statusCode: 404,
                message: 'SESSION_TYPE_ALREADY_MAPPED_WITH_CURRICULUM',
            };
        const courseDocs = await courseModel.find({
            isDeleted: false,
            'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
        });
        if (courseDocs.length)
            return {
                statusCode: 404,
                message: 'SESSION_TYPE_ALREADY_MAPPED_WITH_COURSES',
            };
        const sessionDelivery = await sessionDeliveryModel.updateOne(deleteQuery, deleteObj);
        if (!sessionDelivery) return { statusCode: 500, message: 'DS_DELETE_FAILED' };
        return { statusCode: 200, message: 'DS_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addDeliveryTypesIndianSystem = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { deliveryName, deliverySymbol, _institution_id } = body;
        const { sessionTypeId } = params;
        const institutionData = await institutionModel.findOne({ _id: _institution_id }).lean();
        if (!institutionData) return { statusCode: 410, message: 'FAILED_TO_GET_INSTITUTE' };
        const sessionTypeQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionData = await sessionDeliveryModel.findOne(sessionTypeQuery, { _id: 1 }).lean();
        if (!sessionData) return { statusCode: 410, message: 'DS_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(sessionTypeId),
                    isDeleted: false,
                },
                { _id: 1, deliveryTypes: 1 },
            )
            .lean();

        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        if (duplicateCheck.deliveryTypes !== null)
            duplicateCheck.deliveryTypes.forEach((delivery) => {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = { _id: sessionData._id, isDeleted: false };
        const deliveryTypeObject = {
            $push: {
                deliveryTypes: {
                    deliveryName,
                    deliverySymbol,
                    deliveryDuration,
                },
            },
        };

        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeObject,
        );
        if (sessionDeliveryUpdation)
            return {
                statusCode: 201,
                message: 'DS_CREATED',
            };
        return { statusCode: 500, message: 'DS_CREATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deliveryTypesUpdateIndianSystem = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { deliveryName, deliverySymbol, _institution_id } = body;
        const { sessionTypeId, deliveryTypeId } = params;
        const sessionTypeChkQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionDelivery = await sessionDeliveryModel
            .findOne(sessionTypeChkQuery, {
                _id: 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'SESSTION_TYPE_NOT_FOUND' };
        const duplicateCheck = await sessionDeliveryModel
            .findOne(
                {
                    _id: convertToMongoObjectId(sessionTypeId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                { _id: 1, deliveryTypes: 1 },
            )
            .lean();

        let isDuplicateDeliveryName = false;
        let isDuplicateDeliverySymbol = false;
        duplicateCheck.deliveryTypes.forEach((delivery) => {
            if (String(delivery._id) !== String(deliveryTypeId)) {
                if (delivery.deliveryName.toLowerCase() === deliveryName.toLowerCase()) {
                    isDuplicateDeliveryName = true;
                } else if (delivery.deliverySymbol.toLowerCase() === deliverySymbol.toLowerCase()) {
                    isDuplicateDeliverySymbol = true;
                }
            }
        });
        if (isDuplicateDeliveryName) {
            return { statusCode: 410, message: 'DELIVERY_NAME_ALREADY_EXISTS' };
        }
        if (isDuplicateDeliverySymbol) {
            return { statusCode: 410, message: 'DELIVERY_SYMBOL_ALREADY_EXISTS' };
        }
        const deliveryTypeQuery = {
            _id: convertToMongoObjectId(sessionDelivery._id),
            isDeleted: false,
        };
        const deliveryTypeobject = {
            $set: {
                'deliveryTypes.$[i].deliveryName': deliveryName,
                'deliveryTypes.$[i].deliverySymbol': deliverySymbol,
            },
        };
        const deliveryTypefilter = {
            arrayFilters: [
                {
                    'i._id': deliveryTypeId,
                },
            ],
        };
        const sessionDeliveryUpdation = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeobject,
            deliveryTypefilter,
        );
        if (sessionDeliveryUpdation) {
            const sessionOrder = await sessionOrderModel.updateMany(
                { _deliveryType_id: convertToMongoObjectId(deliveryTypeId), isDeleted: false },
                {
                    deliveryType: deliveryName,
                    deliverySymbol,
                },
            );
            const courseDocs = await courseModel.updateMany(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
                    'sessionDeliveryType.deliveryType._delivery_id':
                        convertToMongoObjectId(deliveryTypeId),
                },
                {
                    'sessionDeliveryType.$[session].deliveryType.$[delivery].deliveryType':
                        deliveryName,
                    'sessionDeliveryType.$[session].deliveryType.$[delivery].deliverySymbol':
                        deliverySymbol,
                },
                {
                    arrayFilters: [
                        { 'session._session_id': convertToMongoObjectId(sessionTypeId) },
                        { 'delivery._delivery_id': convertToMongoObjectId(deliveryTypeId) },
                    ],
                },
            );
            return { statusCode: 200, message: 'DS_UPDATED' };
        }
        return { statusCode: 500, message: 'DS_UPDATE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deliveryTypesDeleteIndianSystem = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { sessionTypeId, deliveryTypeId } = params;
        const sessionTypeCheckQuery = {
            _id: convertToMongoObjectId(sessionTypeId),
            isDeleted: false,
        };
        const sessionOrder = await sessionOrderModel.find({
            _deliveryType_id: convertToMongoObjectId(deliveryTypeId),
            isDeleted: false,
        });

        if (sessionOrder.length > 0) {
            return { statusCode: 500, message: 'DELIVERY_TYPE_IS_ADDED_IN_SESSION_ORDER' };
        }

        const sessionDelivery = await sessionDeliveryModel
            .findOne(sessionTypeCheckQuery, {
                _id: 1,
            })
            .lean();
        if (!sessionDelivery) return { statusCode: 410, message: 'SESSTION_TYPE_NOT_FOUND' };
        const courseDocs = await courseModel.find({
            isDeleted: false,
            'sessionDeliveryType._session_id': convertToMongoObjectId(sessionTypeId),
            'sessionDeliveryType.deliveryType._delivery_id': convertToMongoObjectId(deliveryTypeId),
        });
        if (courseDocs.length)
            return {
                statusCode: 404,
                message: 'DELIVERY_TYPE_ALREADY_MAPPED_WITH_COURSES',
            };
        const deliveryTypeQuery = { _id: convertToMongoObjectId(sessionTypeId), isDeleted: false };
        const deliveryTypeObject = {
            $pull: { deliveryTypes: { _id: convertToMongoObjectId(deliveryTypeId) } },
        };
        const deliveryTypeUpdate = await sessionDeliveryModel.updateOne(
            deliveryTypeQuery,
            deliveryTypeObject,
        );
        if (deliveryTypeUpdate) return { statusCode: 200, message: 'DS_DELETED' };
        return { statusCode: 500, message: 'DS_DELETE_FAILED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const configureIndianSystemWithCredits = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const { sessions, programId } = body;
        for (const session of sessions) {
            const { sessionId, sessionObj } = session;
            let updateQuery = { _id: sessionId };
            if (programId) {
                updateQuery = { ...updateQuery, _program_id: programId };
            }
            await sessionDeliveryModel.findOneAndUpdate(updateQuery, sessionObj);
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleDeliveryTypeIndianCreditSystem = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliveryTypeSchema,
        );
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { deliveryTypeId, sessionId, _institution_id, isActive } = body;
        const coursesConfigured = await courseModel.find({
            _institution_id,
            sessionDeliveryType: {
                $elemMatch: { _session_id: sessionId, 'deliveryType._delivery_id': deliveryTypeId },
            },
        });
        if (coursesConfigured && coursesConfigured.length) {
            return { statusCode: 406, message: 'ALREADY_CONFIGURED' };
        }
        await sessionDeliveryModel.findOneAndUpdate(
            { _id: sessionId, 'deliveryTypes._id': deliveryTypeId },
            { 'deliveryTypes.$.isActive': isActive },
        );
    } catch (error) {
        if (error instanceof Error) throw error;
        else throw new Error(error);
    }
};

module.exports = {
    sessionTypesAdd,
    deliveryTypesAdd,
    sessionTypesUpdate,
    deliveryTypesUpdate,
    sessionTypesDelete,
    deliveryTypesDelete,
    sessionTypesGet,
    deliveryTypesGet,
    addIndependentCourseDeliveryType,
    updateIndependentCourseSessionType,
    addSessionTypesForIndependentCourse,
    updateIndependentCourseDeliveryType,
    getIndependentCourseSessionTypes,
    deleteIndependentSessionTypes,
    sessionCreateIndianSystem,
    editSessionIndianSystem,
    deleteSessionTypeIndianSystem,
    addDeliveryTypesIndianSystem,
    deliveryTypesUpdateIndianSystem,
    deliveryTypesDeleteIndianSystem,
    configureIndianSystemWithCredits,
    toggleDeliveryTypeIndianCreditSystem,
};
