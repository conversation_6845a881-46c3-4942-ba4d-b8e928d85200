const axios = require('axios');

module.exports = {
    axiosCall: (data) => {
        const config = {
            method: 'post',
            url: process.env.SOCKET_SERVER_URL + '/api/sendSocket',
            headers: {
                'Content-Type': 'application/json',
            },
            data: { data },
            // Set reasonable limits for maxContentLength and maxBodyLength (e.g., 1MB or 10MB)
            maxContentLength: 10 * 1024 * 1024, // 10MB in bytes
            maxBodyLength: 10 * 1024 * 1024,
        };
        axios(config)
            .then(function (res) {
                //console.log('res', res);
            })
            .catch(function (error) {
                //console.log('error', error);
            });
    },
};
