const infrastructure_delivery_type_formate = require('./infrastructure_delivery_type_formate');

const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = require('mongodb').ObjectID;
const constant = require('../utility/constants');
//var infrastructure_delivery_type = require('mongoose').model(constant.INFRASTRUCTURE_DELIVERY_TYPE);
const infrastructure_delivery_type = require('./infrastructure_delivery_type_model');


exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(infrastructure_delivery_type, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "infrastructure delivery type list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ infrastructure_delivery_type_formate.infrastructure_delivery_type(doc.data));
        // common_files.list_all_response(res, 200, true, "infrastructure list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* infrastructure_formate.infrastructure(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
       
    ];
    let doc = await base_control.get_aggregate(infrastructure_delivery_type, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "infrastructure for delivery type  details", /* doc.data */infrastructure_delivery_type_formate.infrastructure_delivery_type_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
   
        let doc = await base_control.insert(infrastructure_delivery_type, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "Infrastructure for delivery type Added successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    
}

exports.update = async (req, res) => {
     
        let object_id = req.params.id;
        let doc = await base_control.update(infrastructure_delivery_type, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "infrastructure for delivery type  update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
     
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(infrastructure_delivery_type, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "infrastructure for delivery type deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(infrastructure_delivery_type, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "infrastructure for delivery type List", infrastructure_delivery_type_formate.infrastructure_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};