const institution = require('../models/institution');
const studentGroups = require('../models/student_group');
const course = require('../models/digi_course');
const user = require('../models/user');
const infrastructure_management = require('../models/infrastructure_management');
const { get, get_list } = require('../base/base_controller');
const { response_function } = require('../utility/common');
const { convertToMongoObjectId } = require('../utility/common');

async function courseGroupListByCourseId(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { programId, instCalId, courseId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Institution Not found'));
        const studentGroupData = await get_list(
            studentGroups,
            {
                _institution_calendar_id: convertToMongoObjectId(instCalId),
                'master._program_id': convertToMongoObjectId(programId),
            },
            {},
        );

        if (!studentGroupData.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Student group Not found'));
        let courseInd = -1;
        let group_data = '';
        const groups = [];
        for (SGData of studentGroupData.data) {
            for (group of SGData.groups) {
                courseInd = group.courses.findIndex(
                    (ele) => ele._course_id.toString() == courseId.toString(),
                );
                if (courseInd != -1) {
                    group_data = group;
                    break;
                }
            }
            if (courseInd != -1) break;
        }

        if (courseInd == -1)
            return res.status(404).send(response_function(res, 404, false, 'Course Not found'));
        const course_settings = group_data.courses[courseInd].setting;
        for (data of course_settings) {
            for (session_setting of data.session_setting) {
                for (group of session_setting.groups) {
                    groups.push(group.group_name);
                }
            }
        }

        if (groups.length == 0)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Student group Not found'));
        return res.status(200).send(response_function(res, 200, true, 'Group data', groups));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function subjectListByCourseId(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { courseId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Institution Not found'));

        //Get Course Details
        const courseDetails = await get(
            course,
            { _id: convertToMongoObjectId(courseId) },
            { id: 1, administration: 1, participating: 1 },
        );
        if (!courseDetails.status)
            return res.status(404).send(response_function(res, 404, false, 'Course Not found'));

        let subject_list = [];
        if (courseDetails.data.participating.length > 0)
            subject_list = courseDetails.data.participating;
        subject_list.push(courseDetails.data.administration);
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Subject List', subject_list));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function staffListByCourseId(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { courseId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Institution Not found'));

        //Get Course Details
        const courseDetails = await get(
            course,
            { _id: convertToMongoObjectId(courseId) },
            { id: 1, administration: 1, participating: 1 },
        );
        if (!courseDetails.status)
            return res.status(404).send(response_function(res, 404, false, 'Course Not found'));

        let subject_list = [];
        if (courseDetails.data.participating.length > 0)
            subject_list = courseDetails.data.participating;
        subject_list.push(courseDetails.data.administration);

        if (subject_list.length == 0)
            return res.status(404).send(response_function(res, 404, false, 'Subject not found'));
        //User Data
        const userData = await get_list(
            user,
            { isDeleted: false },
            { _id: 1, academic_allocation: 1, name: 1 },
        );
        if (!userData.status)
            return res.status(404).send(response_function(res, 404, false, 'User List Not found'));
        const userList = [];
        /* subject_list = [{
            "_id": "60508966439bda4fbcdeae18",
            "_program_id": "5f5de9541bcbe205e91fb353",
            "program_name": "Dentistry",
            "_department_id": "5f5b230f1746212d6e0354f2",
            "department_name": "Preventive dental sciences",
            "_subject_id": "5fbcae29a12605525ebb690a",
            "subject_name": "Orthodontics"
        }] */
        for (const user of userData.data) {
            for (subject of subject_list) {
                const pgm_dept_ind = user.academic_allocation.findIndex(
                    (ele) =>
                        ele._program_id.toString() == subject._program_id.toString() &&
                        ele._department_id.toString() == subject._department_id.toString(),
                );
                if (pgm_dept_ind != -1) {
                    const subj_ind = user.academic_allocation[
                        pgm_dept_ind
                    ]._department_subject_id.findIndex(
                        (ele) => ele.toString() == subject._subject_id.toString(),
                    );
                    if (subj_ind != -1) {
                        userList.push(user);
                    }
                }
            }
        }
        return res.status(200).send(response_function(res, 200, true, 'Staff List', userList));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function infraListByCourseId(req, res) {
    try {
        const {
            headers: { _institution_id },
            params: { courseId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(response_function(res, 404, false, 'Institution Not found'));

        //Get Course Details
        const courseDetails = await get(
            course,
            { _id: convertToMongoObjectId(courseId) },
            { id: 1, administration: 1, participating: 1 },
        );
        if (!courseDetails.status)
            return res.status(404).send(response_function(res, 404, false, 'Course Not found'));

        let subject_list = [];
        if (courseDetails.data.participating.length > 0)
            subject_list = courseDetails.data.participating;
        subject_list.push(courseDetails.data.administration);

        if (subject_list.length == 0)
            return res.status(404).send(response_function(res, 404, false, 'Subject not found'));

        //Infra Data
        const infraList = [];
        const infraData = await get_list(infrastructure_management, { isDeleted: false }, {});
        if (!infraData.status)
            return res.status(404).send(response_function(res, 404, false, 'Infra Not found'));

        /* subject_list = [
            {
                _id: '60508966439bda4fbcdeae18',
                _program_id: '60487bf8ee276a7df06a5736',
                program_name: 'Dentistry',
                _department_id: '60487c2bee276a338c6a573e',
                department_name: 'Preventive dental sciences',
                _subject_id: '60487c2bee276ae8966a573f',
                subject_name: 'Orthodontics',
            },
        ]; */

        for (subject of subject_list) {
            for (const infra of infraData.data) {
                const programInd = infra.program.findIndex(
                    (ele) => ele._program_id.toString() == subject._program_id.toString(),
                );
                const deptInd = infra.department.findIndex(
                    (ele) => ele._department_id.toString() == subject._department_id.toString(),
                );
                const subjInd = infra.subject.findIndex(
                    (ele) => ele._subject_id.toString() == subject._subject_id.toString(),
                );
                if (programInd != -1 && deptInd != -1 && subjInd != -1) infraList.push(infra);
            }
        }
        return res.status(200).send(response_function(res, 200, true, 'Infra List', infraList));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

module.exports = {
    courseGroupListByCourseId,
    subjectListByCourseId,
    staffListByCourseId,
    infraListByCourseId,
};
