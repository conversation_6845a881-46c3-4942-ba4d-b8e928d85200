const roleAssignSchema = require('./role-assign.model');
const roleSchema = require('../role/role.model');
const departmentSubjectSchema = require('../../departments-subject/department-subject.model');
const programSchema = require('../../program-input/program-input.model');
const userSchema = require('../../user-management/user.model');

const {
    USER,
    ROLE,
    ROLE_ASSIGN,
    DEPARTMENT_SUBJECT,
    PROGRAM,
} = require('../../../utility/constants');
const { isIDEquals, convertToMongoObjectId, getModel } = require('../../../utility/common');
const { checkIfInstitutionExists } = require('../role/role.util');

const getByListId = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleAssignModel = getModel(tenantURL, ROLE_ASSIGN, roleAssignSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const userModel = getModel(tenantURL, USER, userSchema);
        const { id } = params;
        const { _institution_id, _parent_id } = headers;
        let roleAssignList = await roleAssignModel
            .findOne(
                {
                    _user_id: convertToMongoObjectId(id),
                },
                {
                    roles: 1,
                    reportTo: 1,
                    userName: 1,
                    isActive: 1,
                    _institution_id: 1,
                    _parent_id: 1,
                    _user_id: 1,
                },
            )
            .findAllAssignRoles({ _institution_id, _parent_id })
            .lean();

        if (!roleAssignList.length) {
            return { statusCode: 410, message: 'THERE_IS_NO_ROLE_ASSIGNED_FOR_THIS_USER' };
        }
        roleAssignList = roleAssignList[0];

        const roles = [];
        const departments = await departmentSubjectModel
            .find(
                {
                    ...(_institution_id && {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    }),
                    ...(_parent_id && { _parent_id: convertToMongoObjectId(_parent_id) }),
                    isDeleted: false,
                    isActive: true,
                },
                { departmentName: 1, _program_id: 1 },
            )
            .lean();

        for (const roleAssignEntry of roleAssignList.roles) {
            const assigned = [];
            const otherDepartments = [];
            const programs = roleAssignEntry.program.map(
                (programEntry) => programEntry._program_id,
            );

            const newDepartments = [];

            programs.forEach((programEntry) => {
                const oldDepartments = departments.filter((departmentEntry) =>
                    isIDEquals(programEntry, departmentEntry.program_id),
                );

                if (oldDepartments.length) {
                    oldDepartments.forEach((oldDepartment) => {
                        newDepartments.push(oldDepartment);
                    });
                }
            });

            if (newDepartments.length) {
                newDepartments.forEach((newDepartment) => {
                    const findProgramIndex = roleAssignEntry.program.findIndex((programEntry) =>
                        isIDEquals(programEntry._program_id, newDepartment.program_id),
                    );

                    if (findProgramIndex >= 0) {
                        otherDepartments.push({
                            _department_id: newDepartment._id,
                            departmentTitle: newDepartment.departmentName,
                            programName: roleAssignEntry.program[findProgramIndex].programName,
                            _program_id: roleAssignEntry.program[findProgramIndex]._id,
                        });
                    }
                });

                roleAssignEntry.department.forEach((departmentEntry) => {
                    const departmentIndex = otherDepartments.findIndex((otherDepartmentIndex) =>
                        isIDEquals(
                            otherDepartmentIndex._department_id,
                            departmentEntry._department_id,
                        ),
                    );
                    if (departmentIndex >= 0) {
                        assigned.push(otherDepartments[departmentIndex]);
                    }
                });
            }

            const roleAssignWithDepartment = {
                isDefault: roleAssignEntry.isDefault,
                isAdmin: roleAssignEntry.isAdmin,
                _id: roleAssignEntry._id,
                _role_id: roleAssignEntry._role_id,
                roleName: roleAssignEntry.roleName,
                program: roleAssignEntry.program,
                department: assigned,
                otherDepartments,
            };
            roles.push(roleAssignWithDepartment);
        }

        let reportTo = {};
        if (roleAssignList.reportTo) {
            //Get Report Role and Data
            const users = await userModel
                .find(
                    { _id: convertToMongoObjectId(roleAssignList.reportTo._user_id) },
                    { name: 1, user_id: 1 },
                )
                .populate({ path: '_role_id', select: { user_name: 1, roles: 1 } })
                .lean();

            let userId = '';
            let role = '';
            let program = [];
            if (users.length) {
                const userData = users[0];
                userId = userData.user_id;
                if (userData._role_id) {
                    role =
                        userData._role_id.roles[
                            userData._role_id.roles.findIndex((roleEntry) => roleEntry.isDefault)
                        ].role_name;
                    program =
                        userData._role_id.roles[
                            userData._role_id.roles.findIndex((roleEntry) => roleEntry.isDefault)
                        ].program;
                }
            }

            reportTo = {
                name: roleAssignList.reportTo.name,
                _user_id: roleAssignList.reportTo._user_id,
                userId,
                role,
                program,
            };
        }

        const roleAssignData = {
            userName: roleAssignList.userName,
            reportTo,
            isActive: roleAssignList.isActive,
            _id: roleAssignList._id,
            _institution_id: roleAssignList._institution_id,
            _parent_id: roleAssignList._parent_id,
            _user_id: roleAssignList._user_id,
            roles,
        };
        if (!roleAssignList) {
            return { statusCode: 400, message: 'ASSIGNED_ROLES_NOT_FOUND', data: {} };
        }

        return { statusCode: 200, message: 'ASSIGNED_ROLES_LIST', data: roleAssignData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createRoleAssign = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleAssignModel = getModel(tenantURL, ROLE_ASSIGN, roleAssignSchema);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const userModel = getModel(tenantURL, USER, userSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { _institution_id, _parent_id } = headers;
        const { _user_id, reportTo, roles: oldRoles } = body;
        if (!(await checkIfInstitutionExists({ _institution_id }))) {
            return { statusCode: 410, message: 'INSTITUTION_NOT_FOUND' };
        }

        const roleAssignList = await roleAssignModel
            .find(
                {
                    _user_id: convertToMongoObjectId(_user_id),
                },
                { _id: 1 },
            )
            .findAllAssignRoles({ _institution_id, _parent_id })
            .lean();

        const userIds = [];
        userIds.push(convertToMongoObjectId(_user_id));
        if (reportTo) {
            userIds.push(convertToMongoObjectId(reportTo));
        }

        const userCheck = await userModel
            .find(
                { _id: { $in: userIds.map((userId) => userId) }, isDeleted: false, isActive: true },
                { name: 1 },
            )
            .lean();

        if (!userCheck.length) {
            return { statusCode: 400, message: 'USER_NOT_FOUND' };
        }

        const programIds = [];
        const departmentIds = [];
        oldRoles.forEach((roleEntry) => {
            if (roleEntry._program_ids.length) {
                roleEntry._program_ids.forEach((programId) => {
                    programIds.push(programId);
                });
            }
            if (roleEntry._department_ids.length) {
                roleEntry._department_ids.forEach((departmentId) => {
                    departmentIds.push(departmentId);
                });
            }
        });

        const programCheck = await programModel
            .find(
                {
                    _id: { $in: programIds.map((programId) => programId) },
                    isDeleted: false,
                    isActive: true,
                },
                { name: 1 },
            )
            .lean();

        if (!programCheck.length) {
            return { statusCode: 400, message: 'PROGRAM_NOT_FOUND' };
        }

        const departmentCheck = await departmentSubjectModel
            .find(
                {
                    _id: {
                        $in: departmentIds.map((departmentId) => departmentId),
                    },
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1, departmentName: 1, _program_id: 1 },
            )
            .lean();

        if (!departmentCheck.length) {
            return { statusCode: 400, message: 'DEPARTMENT_NOT_FOUND' };
        }

        const roles = [];
        oldRoles.forEach((roleEntry) => {
            const programs = [];
            const departments = [];
            if (roleEntry._program_ids.length) {
                roleEntry._program_ids.forEach((programIdEntry) => {
                    const programName =
                        programCheck[
                            programCheck.findIndex((programIndex) =>
                                isIDEquals(programIndex._id, programIdEntry),
                            )
                        ].name;
                    if (programName) {
                        programs.push({
                            _program_id: programIdEntry,
                            programName,
                        });
                    }
                });
            }

            if (roleEntry._department_ids.length) {
                roleEntry._department_ids.forEach((departmentIdEntry) => {
                    const departmentName =
                        departmentCheck[
                            departmentCheck.findIndex((departmentIndex) =>
                                isIDEquals(departmentIndex._id, departmentIdEntry),
                            )
                        ].departmentName;
                    if (departmentName) {
                        departments.push({
                            _department_id: departmentIdEntry,
                            departmentTitle: departmentName,
                        });
                    }
                });
            }

            roles.push({
                _role_id: roleEntry._role_id,
                roleName: roleEntry.roleName,
                program: programs,
                department: departments,
                isDefault: roleEntry.isDefault,
                isAdmin: roleEntry.isAdmin,
            });
        });

        const roleAssignObject = {
            _institution_id,
            ...(_parent_id && { _parent_id }),
            _user_id,
            userName:
                userCheck[userCheck.findIndex((userIndex) => isIDEquals(userIndex._id, _user_id))]
                    .name,
            roles,
            reportTo: {},
        };

        if (reportTo) {
            roleAssignObject.report_to = {
                _user_id: reportTo,
                name: userCheck[
                    userCheck.findIndex((userIndex) => isIDEquals(userIndex._id, reportTo))
                ].name,
            };
        }

        let roleAssignInsert = {};
        if (!roleAssignList.length) {
            roleAssignInsert = await roleAssignModel.create(roleAssignObject);
        } else {
            roleAssignInsert = await roleAssignModel.updateOne(
                { _user_id: convertToMongoObjectId(_user_id) },
                { $set: roleAssignObject },
            );
        }

        // TODO: need to check this cache file
        //  updateUserRoleAssignFlatCacheData(_user_id);

        if (!roleAssignInsert) {
            return {
                statusCode: 500,
                message: 'UNABLE_TO_ASSIGN_ROLE_TO_USER',
                data: roleAssignInsert.data,
            };
        }

        return { statusCode: 200, message: 'ROLE_ASSIGNED_TO_USER', data: roleAssignInsert.data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDepartmentList = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const roleModel = getModel(tenantURL, ROLE, roleSchema);
        const roleAssignModel = getModel(tenantURL, ROLE_ASSIGN, roleAssignSchema);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const departmentSubjectModel = getModel(
            tenantURL,
            DEPARTMENT_SUBJECT,
            departmentSubjectSchema,
        );
        const { _institution_id, _parent_id } = headers;
        const { roleId, programIds } = body;
        const roleCheck = await roleModel
            .find(
                { _id: convertToMongoObjectId(roleId) },
                { _institution_id: 1, name: 1, modules: 1, _college_id: 1 },
            )
            .findAllRoles({ _institution_id, _parent_id })
            .lean();

        if (!roleCheck.length) {
            return { statusCode: 404, message: 'UNABLE_TO_FIND_ROLE' };
        }

        const newProgramCheck = await programModel
            .find(
                {
                    _id: { $in: programIds.map((programId) => programId) },
                    isDeleted: false,
                    isActive: true,
                },
                { name: 1 },
            )
            .lean();

        if (!newProgramCheck.length) {
            return { statusCode: 404, message: 'PROGRAM_NOT_FOUND' };
        }

        const newDepartments = await departmentSubjectModel
            .find(
                {
                    _program_id: { $in: programIds.map((programId) => programId) },
                    isDeleted: false,
                    isActive: true,
                },
                { name: 1, _program_id: 1, departmentName: 1 },
            )
            .lean();

        if (!newDepartments.length) {
            return { statusCode: 404, message: 'DEPARTMENT_NOT_FOUND' };
        }

        const roleAssignWithDepartments = await roleAssignModel
            .find()
            .findAllAssignRoles({ _institution_id, _parent_id })
            .lean();

        const departmentIds = [];
        if (roleAssignWithDepartments.length) {
            roleAssignWithDepartments.forEach((roleAssignEntry) => {
                roleAssignEntry.roles.forEach((roleEntry) => {
                    if (isIDEquals(roleEntry._role_id, roleId)) {
                        roleEntry.department.forEach((departmentEntry) => {
                            if (
                                !departmentIds.find((departmentId) =>
                                    isIDEquals(departmentEntry._department_id, departmentId),
                                )
                            ) {
                                departmentIds.push(departmentEntry._department_id);
                            }
                        });
                    }
                });
            });
        }
        let roleDepartments = [];
        if (newDepartments.length) {
            departmentIds.forEach((departmentId) => {
                const newDepartmentDatas = newDepartments.filter((newDepartment) =>
                    isIDEquals(newDepartment._id, departmentId),
                );
                if (newDepartmentDatas.length) {
                    roleDepartments = roleDepartments.concat(newDepartmentDatas);
                }
            });
        }

        const programDepartments = [];
        roleDepartments.forEach((roleDepartment) => {
            if (roleDepartment._program_id) {
                const programIndex = newProgramCheck.findIndex((programEntry) =>
                    isIDEquals(programEntry._id, roleDepartment._program_id),
                );
                if (programIndex >= 0) {
                    programDepartments.push({
                        _id: roleDepartment._id,
                        departmentName: roleDepartment.departmentName,
                        programName: newProgramCheck[programIndex].name,
                        programId: newProgramCheck[programIndex]._id,
                    });
                }
            }
        });

        return { statusCode: 200, message: 'DEPARTMENT_LIST', data: programDepartments };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getByListId,
    createRoleAssign,
    getDepartmentList,
};
