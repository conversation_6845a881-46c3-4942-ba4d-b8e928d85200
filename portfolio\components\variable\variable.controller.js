const variableService = require('./variable.service');

const createVariable = async ({ body }) => {
    const { programId, courseId, institutionCalendarId, name, type, variable = {} } = body;
    const result = await variableService.createVariable({
        programId,
        courseId,
        institutionCalendarId,
        name,
        type,
        variable,
    });

    return { message: 'VARIABLE_CREATED_SUCCESSFULLY', data: result };
};

const getVariable = async ({
    query: { programId, courseId, institutionCalendarId, type } = {},
}) => {
    const result = await variableService.getVariable({
        programId,
        courseId,
        institutionCalendarId,
        type,
    });

    return { message: 'VARIABLE_RETRIEVED_SUCCESSFULLY', data: result };
};

const updateVariable = async ({
    query: { variableId } = {},
    body: { name, type, variable = {} } = {},
}) => {
    await variableService.updateVariable({ variableId, name, type, variable });

    return { message: 'VARIABLE_UPDATED_SUCCESSFULLY' };
};

const deleteVariable = async ({ query: { variableId } }) => {
    await variableService.deleteVariable({ variableId });

    return { message: 'VARIABLE_DELETED_SUCCESSFULLY' };
};

module.exports = {
    createVariable,
    getVariable,
    updateVariable,
    deleteVariable,
};
