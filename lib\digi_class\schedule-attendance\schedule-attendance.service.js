const ScheduleAttendanceModel = require('../../models/schedule_attendance');
const CourseSchedule = require('../../models/course_schedule');
const userModel = require('../../models/user');
const { convertToMongoObjectId, axiosCall } = require('../../utility/common');
const {
    RUNNING,
    COMPLETED,
    LEAVE,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    PRESENT,
} = require('../../utility/constants');
const push = require('../../utility/notification_push');
const { filterStudentsForCourseRestriction } = require('../../utility/utility.service');
const leavePermissionOnDuty = [ONDUTY, LEAVE, PERMISSION];
const { scheduleUploadDocument } = require('../../utility/file_upload');
const fileUpload = scheduleUploadDocument.fields([{ name: 'file', maxCount: 1 }]);
const { sendResponse } = require('../../utility/common');
const { MulterError } = require('multer');

const makeTitleAndMessage = (courseSchedule) => {
    try {
        let title = 'Make your Attendance';
        if (courseSchedule.course_code) {
            title += '(' + courseSchedule.course_code + ')';
        }
        let message;
        if (courseSchedule.course_name) {
            message = courseSchedule.course_name + ' : ';
        }
        if (courseSchedule.session && courseSchedule.session.session_type) {
            message +=
                courseSchedule.session.session_type +
                ' : ' +
                courseSchedule.session.delivery_symbol +
                courseSchedule.session.delivery_no;
        } else message += courseSchedule.title + ' : ' + courseSchedule.sub_type;
        if (courseSchedule.infra) message += ' : Room - ' + courseSchedule.infra;
        return { title, message };
    } catch (error) {
        throw new Error(error);
    }
};

exports.scheduleAttendanceRetake = async ({
    modeBy,
    _staff_id,
    _id,
    _institution_id,
    isLive,
    faceAuthentication,
}) => {
    try {
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };

        const courseScheduleData = await CourseSchedule.findOne(query)
            .populate('merge_with.schedule_id')
            .lean();
        let scheduleIds = [courseScheduleData._id];
        let mergeSchedules = [];
        const students = courseScheduleData.students;
        if (courseScheduleData.merge_status) {
            mergeSchedules = courseScheduleData.merge_with.map((element) => element.schedule_id);
            scheduleIds = scheduleIds.concat(mergeSchedules);
            for (csData of courseScheduleData.merge_with) {
                for (studentsList of csData.schedule_id.students) {
                    if (
                        students.findIndex(
                            (element) => element._id.toString() === studentsList._id.toString(),
                        ) === -1
                    ) {
                        students.push(studentsList);
                    }
                }
            }
        }
        let studentIds = students
            .filter(
                (studentElement) =>
                    studentElement.status && !leavePermissionOnDuty.includes(studentElement.status),
            )
            .map((element) => element._id);
        const {
            _institution_calendar_id,
            _institution_id: institutionId,
            _program_id,
            _course_id,
            term,
            level_no,
            year_no,
            rotation_count,
        } = courseScheduleData;
        studentIds = await filterStudentsForCourseRestriction({
            _institution_calendar_id,
            _institution_id: institutionId,
            programId: _program_id,
            courseId: _course_id,
            levelNo: level_no,
            yearNo: year_no,
            term,
            rotationCount: rotation_count,
            userIds: studentIds,
        });
        const usersModel = await userModel
            .find(
                { _id: { $in: studentIds } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            )
            .lean();

        if (!usersModel) return { message: 'STUDENTS_NOT_FOUND' };

        const tokens = [];
        const socketIds = [];
        usersModel.forEach((element) => {
            let socketEventIds = element.socketEventId;
            if (socketEventIds) {
                socketEventIds = Object.values(socketEventIds);
                socketIds.push(socketEventIds);
            }
            if ((element.fcm_token || element.web_fcm_token) && element.device_type) {
                tokens.push({
                    device_type: element.device_type,
                    token: element.fcm_token,
                    web_fcm_token: element.web_fcm_token,
                    _user_id: element._id,
                    scheduleId: _id,
                });
            }
        });

        if (tokens.length) {
            const title = 'Make your Attendance';
            const { message } = makeTitleAndMessage(courseScheduleData);
            const data = {
                _id: courseScheduleData._id,
                courseId: courseScheduleData._course_id,
                programId: courseScheduleData._program_id,
                institutionCalendarId: courseScheduleData._institution_calendar_id,
                yearNo: courseScheduleData.year_no,
                term: courseScheduleData.term,
                levelNo: courseScheduleData.level_no,
                mergeStatus: courseScheduleData.merge_status,
                mergeType: courseScheduleData.type,
                clickAction: 'retake_start',
            };
            push.notification_push(tokens, title, message, data);
        }
        // commenting because right now mobile itself calling socket as per mobile team, if they need in future we can enable
        /*  if (students.length) {
            let userIds = students.map((token) => token._id);
            userIds = [...new Set(userIds)];
            const sendSocketData = [];
            const data = JSON.stringify({ notificationCount: 1, sessions: courseScheduleData });
            for (const user of students) {
                const eventId = user._id;
                sendSocketData.push({ eventId, data });
            }
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        } */

        await CourseSchedule.updateOne(
            { _id: { $in: scheduleIds } },
            { $set: { retakeStatus: 1 } },
        );
        const createAttendance = await ScheduleAttendanceModel.create({
            modeBy,
            _staff_id,
            scheduleId: scheduleIds,
            status: RUNNING,
            ...(isLive && {
                isLive,
            }),
            faceAuthentication,
        });
        return { message: 'RETAKE_ATTENDANCE_TRIGGERED', createdAt: createAttendance.createdAt };
    } catch (error) {
        return error.toString();
    }
};

exports.scheduleAttendanceRetakeForAbsentees = async ({
    modeBy,
    _staff_id,
    _id,
    _institution_id,
    isLive,
    faceAuthentication,
}) => {
    try {
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };

        const courseScheduleData = await CourseSchedule.findOne(query)
            .populate('merge_with.schedule_id')
            .lean();
        let scheduleIds = [courseScheduleData._id];
        let mergeSchedules = [];
        const students = courseScheduleData.students.filter(
            (element) =>
                element.primaryStatus !== 'present' &&
                element.primaryStatus !== ONDUTY &&
                element.primaryStatus !== LEAVE &&
                element.primaryStatus !== PERMISSION,
        );
        if (courseScheduleData.merge_status) {
            mergeSchedules = courseScheduleData.merge_with.map((element) => element.schedule_id);
            scheduleIds = scheduleIds.concat(mergeSchedules);
            for (csData of courseScheduleData.merge_with) {
                const absentStudentList = csData.schedule_id.students.filter(
                    (element) =>
                        element.primaryStatus !== 'present' &&
                        element.primaryStatus !== ONDUTY &&
                        element.primaryStatus !== LEAVE &&
                        element.primaryStatus !== PERMISSION,
                );

                for (studentsList of absentStudentList) {
                    if (
                        students.findIndex(
                            (element) => element._id.toString() === studentsList._id.toString(),
                        ) === -1
                    ) {
                        students.push(studentsList);
                    }
                }
            }
        }
        let studentIds = students
            .filter(
                (studentElement) =>
                    studentElement.status && !leavePermissionOnDuty.includes(studentElement.status),
            )
            .map((element) => element._id);
        const {
            _institution_calendar_id,
            _institution_id: institutionId,
            _program_id,
            _course_id,
            term,
            level_no,
            year_no,
            rotation_count,
        } = courseScheduleData;
        studentIds = await filterStudentsForCourseRestriction({
            _institution_calendar_id,
            _institution_id: institutionId,
            programId: _program_id,
            courseId: _course_id,
            levelNo: level_no,
            yearNo: year_no,
            term,
            rotationCount: rotation_count,
            userIds: studentIds,
        });
        const usersModel = await userModel
            .find(
                { _id: { $in: studentIds } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            )
            .lean();

        if (!usersModel) return { message: 'STUDENTS_NOT_FOUND' };

        const tokens = [];
        const socketIds = [];
        usersModel.forEach((element) => {
            let socketEventIds = element.socketEventId;
            if (socketEventIds) {
                socketEventIds = Object.values(socketEventIds);
                socketIds.push(socketEventIds);
            }
            if ((element.fcm_token || element.web_fcm_token) && element.device_type) {
                tokens.push({
                    device_type: element.device_type,
                    token: element.fcm_token,
                    web_fcm_token: element.web_fcm_token,
                    _user_id: element._id,
                    scheduleId: _id,
                });
            }
        });

        if (tokens.length) {
            const title = 'Make your Attendance';
            const { message } = makeTitleAndMessage(courseScheduleData);
            const data = {
                _id: courseScheduleData._id,
                courseId: courseScheduleData._course_id,
                programId: courseScheduleData._program_id,
                institutionCalendarId: courseScheduleData._institution_calendar_id,
                yearNo: courseScheduleData.year_no,
                term: courseScheduleData.term,
                levelNo: courseScheduleData.level_no,
                mergeStatus: courseScheduleData.merge_status,
                mergeType: courseScheduleData.type,
                clickAction: 'retake_start',
            };
            push.notification_push(tokens, title, message, data);
        }
        // commenting because right now mobile itself calling socket as per mobile team, if they need in future we can enable
        /* if (students.length) {
            let userIds = students.map((token) => token._id);
            userIds = [...new Set(userIds)];
            const sendSocketData = [];
            const data = JSON.stringify({ notificationCount: 1, sessions: courseScheduleData });
            for (const user of students) {
                const eventId = user._id;
                sendSocketData.push({ eventId, data });
            }
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        } */

        await CourseSchedule.updateOne(
            { _id: { $in: scheduleIds } },
            { $set: { retakeStatus: 2 } },
        );
        const createAttendance = await ScheduleAttendanceModel.create({
            modeBy,
            _staff_id,
            scheduleId: scheduleIds,
            status: RUNNING,
            ...(isLive && {
                isLive,
            }),
            faceAuthentication,
        });
        return { message: 'RETAKE_ATTENDANCE_TRIGGERED', createdAt: createAttendance.createdAt };
    } catch (error) {
        return error.toString();
    }
};

exports.scheduleAttendanceBuzzer = async ({ modeBy, _staff_id, _id, _institution_id, isLive }) => {
    try {
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };

        const courseScheduleData = await CourseSchedule.findOne(query)
            .populate('merge_with.schedule_id')
            .lean();
        let scheduleIds = [courseScheduleData._id];
        let mergeSchedules = [];
        const students = courseScheduleData.students;
        if (courseScheduleData.merge_status) {
            mergeSchedules = courseScheduleData.merge_with.map((element) => element.schedule_id);
            scheduleIds = scheduleIds.concat(mergeSchedules);

            for (csData of courseScheduleData.merge_with) {
                for (studentsList of csData.schedule_id.students) {
                    if (
                        students.findIndex(
                            (element) => element._id.toString() === studentsList._id.toString(),
                        ) === -1
                    ) {
                        students.push(studentsList);
                    }
                }
            }
        }
        let studentIds = students
            .filter(
                (studentElement) =>
                    studentElement.status && !leavePermissionOnDuty.includes(studentElement.status),
            )
            .map((element) => element._id);
        const {
            _institution_calendar_id,
            _institution_id: institutionId,
            _program_id,
            _course_id,
            term,
            level_no,
            year_no,
            rotation_count,
        } = courseScheduleData;
        studentIds = await filterStudentsForCourseRestriction({
            _institution_calendar_id,
            _institution_id: institutionId,
            programId: _program_id,
            courseId: _course_id,
            levelNo: level_no,
            yearNo: year_no,
            term,
            rotationCount: rotation_count,
            userIds: studentIds,
        });
        const usersModel = await userModel
            .find(
                { _id: { $in: studentIds } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            )
            .lean();

        if (!usersModel) return { message: 'STUDENTS_NOT_FOUND' };

        const tokens = [];
        const socketIds = [];
        usersModel.forEach((element) => {
            let socketEventIds = element.socketEventId;
            if (socketEventIds) {
                socketEventIds = Object.values(socketEventIds);
                socketIds.push(socketEventIds);
            }
            if ((element.fcm_token || element.web_fcm_token) && element.device_type) {
                tokens.push({
                    device_type: element.device_type,
                    token: element.fcm_token,
                    web_fcm_token: element.web_fcm_token,
                    _user_id: element._id,
                    scheduleId: _id,
                });
            }
        });

        if (tokens.length) {
            const title = 'Accept the Buzzer';
            const { message } = makeTitleAndMessage(courseScheduleData);
            const data = {
                _id: courseScheduleData._id,
                courseId: courseScheduleData._course_id,
                programId: courseScheduleData._program_id,
                institutionCalendarId: courseScheduleData._institution_calendar_id,
                yearNo: courseScheduleData.year_no,
                term: courseScheduleData.term,
                levelNo: courseScheduleData.level_no,
                mergeStatus: courseScheduleData.merge_status,
                mergeType: courseScheduleData.type,
                clickAction: 'retake_start',
            };
            push.notification_push(tokens, title, message, data);
        }
        // commenting because right now mobile itself calling socket as per mobile team, if they need in future we can enable
        /*  if (students.length) {
            let userIds = students.map((token) => token._id);
            userIds = [...new Set(userIds)];
            const sendSocketData = [];
            const data = JSON.stringify({ notificationCount: 1, sessions: courseScheduleData });
            for (const user of students) {
                const eventId = user._id;
                sendSocketData.push({ eventId, data });
            }
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        } */

        await CourseSchedule.updateOne(
            { _id: { $in: scheduleIds } },
            { $set: { retakeStatus: 3 } },
        );
        const createAttendance = await ScheduleAttendanceModel.create({
            modeBy,
            _staff_id,
            scheduleId: scheduleIds,
            status: RUNNING,
            ...(isLive && {
                isLive,
            }),
        });
        return { message: 'RETAKE_ATTENDANCE_TRIGGERED', createdAt: createAttendance.createdAt };
    } catch (error) {
        return error.toString();
    }
};

exports.scheduleAttendanceQuiz = async ({
    modeBy,
    _staff_id,
    _id,
    _institution_id,
    scheduleAttendanceId,
    isLive,
}) => {
    try {
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };

        const courseScheduleData = await CourseSchedule.findOne(query)
            .populate('merge_with.schedule_id')
            .lean();
        let scheduleIds = [courseScheduleData._id];
        let mergeSchedules = [];
        const students = courseScheduleData.students;
        if (courseScheduleData.merge_status) {
            mergeSchedules = courseScheduleData.merge_with.map((element) => element.schedule_id);
            scheduleIds = scheduleIds.concat(mergeSchedules);
            for (csData of courseScheduleData.merge_with) {
                for (studentsList of csData.schedule_id.students) {
                    if (
                        students.findIndex(
                            (element) => element._id.toString() === studentsList._id.toString(),
                        ) === -1
                    ) {
                        students.push(studentsList);
                    }
                }
            }
        }
        let studentIds = students
            .filter(
                (studentElement) =>
                    studentElement.status && !leavePermissionOnDuty.includes(studentElement.status),
            )
            .map((element) => element._id);
        const {
            _institution_calendar_id,
            _institution_id: institutionId,
            _program_id,
            _course_id,
            term,
            level_no,
            year_no,
            rotation_count,
        } = courseScheduleData;
        studentIds = await filterStudentsForCourseRestriction({
            _institution_calendar_id,
            _institution_id: institutionId,
            programId: _program_id,
            courseId: _course_id,
            levelNo: level_no,
            yearNo: year_no,
            term,
            rotationCount: rotation_count,
            userIds: studentIds,
        });
        const usersModel = await userModel
            .find(
                { _id: { $in: studentIds } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            )
            .lean();

        if (!usersModel) return { message: 'STUDENTS_NOT_FOUND' };

        const tokens = [];
        const socketIds = [];
        usersModel.forEach((element) => {
            let socketEventIds = element.socketEventId;
            if (socketEventIds) {
                socketEventIds = Object.values(socketEventIds);
                socketIds.push(socketEventIds);
            }
            if ((element.fcm_token || element.web_fcm_token) && element.device_type) {
                tokens.push({
                    device_type: element.device_type,
                    token: element.fcm_token,
                    web_fcm_token: element.web_fcm_token,
                    _user_id: element._id,
                    scheduleId: _id,
                });
            }
        });

        if (tokens.length) {
            const title = 'Accept the Quiz';
            const { message } = makeTitleAndMessage(courseScheduleData);
            const data = {
                _id: courseScheduleData._id,
                courseId: courseScheduleData._course_id,
                programId: courseScheduleData._program_id,
                institutionCalendarId: courseScheduleData._institution_calendar_id,
                yearNo: courseScheduleData.year_no,
                term: courseScheduleData.term,
                levelNo: courseScheduleData.level_no,
                mergeStatus: courseScheduleData.merge_status,
                mergeType: courseScheduleData.type,
                scheduleAttendanceId,
                clickAction: 'retake_start',
            };
            push.notification_push(tokens, title, message, data);
        }
        // commenting because right now mobile itself calling socket as per mobile team, if they need in future we can enable
        /* if (students.length) {
            let userIds = students.map((token) => token._id);
            userIds = [...new Set(userIds)];
            const sendSocketData = [];
            const data = JSON.stringify({ notificationCount: 1, sessions: courseScheduleData });
            for (const user of students) {
                const eventId = user._id;
                sendSocketData.push({ eventId, data });
            }
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        } */

        await CourseSchedule.updateOne(
            { _id: { $in: scheduleIds } },
            { $set: { retakeStatus: 4 } },
        );
        const createAttendance = await ScheduleAttendanceModel.findOneAndUpdate(
            { _id: convertToMongoObjectId(scheduleAttendanceId) },
            {
                $set: {
                    status: RUNNING,
                    ...(isLive && {
                        isLive,
                    }),
                },
            },
        );
        return { message: 'RETAKE_ATTENDANCE_TRIGGERED', createdAt: createAttendance.createdAt };
    } catch (error) {
        return error.toString();
    }
};

exports.sendStudentResponseToStaff = async ({ _staff_id, _student_id, questions }) => {
    try {
        const data = JSON.stringify({ _student_id, questions });
        axiosCall([{ eventId: _staff_id, data }]);
    } catch (error) {
        return error.toString();
    }
};
exports.sendStudentAttendanceToStaff = async ({ socket_port, _student_id }) => {
    try {
        const data = JSON.stringify({ _student_id });
        axiosCall([{ eventId: socket_port, data }]);
    } catch (error) {
        return error.toString();
    }
};

exports.uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err)
            return sendResponse(
                res,
                500,
                false,
                'Something went wrong.Please change file format and upload',
            );
        next();
    });
};
