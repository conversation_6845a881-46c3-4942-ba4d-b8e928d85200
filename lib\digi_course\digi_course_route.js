const express = require('express');
const multer = require('multer');
const route = express.Router();
const digi_course = require('./digi_course_controller');
const digiSessionOrderModules = require('./digi_session_order_module_controller');
const validator = require('./digi_course_validator');
const { uploadSessionDocumentFile } = require('../utility/file_upload');
const sessionDocumentUpload = uploadSessionDocumentFile.fields([{ name: 'file', maxCount: 1 }]);
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
//display session text
route.get(
    '/getSessionText',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    digi_course.getSessionText,
);
route.get(
    '/generateSignedUrl',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    digi_course.getSessionDocumentSignedUrl,
);
route.post(
    '/',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.insert,
);
route.put(
    '/session_flow_update/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.update_session_flow,
);
route.post('/draft', digi_course.insert_draft);
route.post(
    '/import_course_with_assign',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    digi_course.import_course_with_assign,
);
route.post(
    '/data_check_import_course',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.data_check_import_course,
);
route.post(
    '/data_check_import_session_flow',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.data_check_import_session_flow,
);
route.post(
    '/import_session_flow',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    digi_course.import_session_flow,
);
route.post(
    '/data_check_slo',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.data_check_slo,
);
route.post(
    '/import_slo',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    digi_course.import_slo,
);
route.post(
    '/assign_course/:id',
    [userPolicyAuthentication(['program_input:programs:active_programs:config_view'])],
    validator.assign_course,
    digi_course.assign_course,
);
route.put(
    '/assign_course_edit/:id/:assigned_course_details_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.assign_course_edit,
    digi_course.assign_course_edit,
);
route.delete(
    '/assign_course_delete/:id/:assigned_course_details_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.assign_course_delete,
);
route.put('/:id', digi_course.update);

route.get('/program/:program_id/:curriculum_id', digi_course.program_course_list);
route.get(
    '/program_curriculum_year_level_list',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_course.program_curriculum_year_level_list,
);
route.get('/program_level_list/:program_id', validator.program_id, digi_course.program_level_list);
route.get(
    '/program_session_delivery_type_list/:program_id',
    validator.program_id,
    digi_course.program_session_delivery_type_list,
);
route.get(
    '/course_list_recurring_level/:program_id/:curriculum_id/:level_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_course.course_list_recurring_level,
);
route.get(
    '/get_course_assigned_details/:id/:course_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_course.get_course_assigned_details,
);
route.get(
    '/course_assigned_list/:program_id/:curriculum_id/:year_id/:level_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_course.course_assigned_list,
);
route.get(
    '/course_assigned_list_map_view/:program_id/:curriculum_id/:year_id/:level_id',
    digi_course.course_assigned_list_map_view,
);
route.get('/get_course_list_by_curriculum/:curriculum_id', digi_course.curriculum_wise_course_list);
route.get(
    '/program_list_by_course_type/:course_type/:program_id',
    digi_course.program_list_by_course_type,
);
route.get(
    '/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    validator.id,
    digi_course.list_id,
);
route.get('/', [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])], digi_course.list);

route.delete('/:id', validator.id, digi_course.delete_course);

//Session flow

route.get(
    '/list_id_session_flow/:course_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digi_course.list_id_session_flow,
);
route.post(
    '/session_flow_add',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digi_course.insert_session_flow,
);

// Course Session Order Modules
route.get(
    '/sessionOrderModules/:programId/:courseId',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digiSessionOrderModules.list,
);
route.get(
    '/sessionOrderModules/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    digiSessionOrderModules.getById,
);
route.post(
    '/sessionOrderModules',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digiSessionOrderModules.insert,
);
route.put(
    '/sessionOrderModules/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digiSessionOrderModules.update,
);
route.delete(
    '/sessionOrderModules/:id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    digiSessionOrderModules.deleteSessionOrderModule,
);

// Course Versioning
route.post('/courseVersion/:id', digi_course.courseVersion);

//upload session document list
route.post(
    '/uploadSessionDocument',
    (req, res, next) => {
        sessionDocumentUpload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
            }
            if (err) {
                console.log('AWS error', err);
                return res
                    .status(500)
                    .send(
                        response_function(
                            res,
                            500,
                            false,
                            'Please change file format and upload',
                            err.toString(),
                        ),
                    );
            }
            next();
        });
    },
    digi_course.uploadSessionDocument,
);

module.exports = route;
