const lmsStudentSchema = require('./lmsStudent.model');
const lmsSettingSchema = require('../lmsStudentSetting/lmsStudentSetting.model');
const userSchema = require('../models/user');
const programSchema = require('../models/digi_programs');
const courseSchema = require('../models/digi_course');
const roleAssignSchema = require('../models/role_assign');
const roleSchema = require('../models/role');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const globalSessionSchema = require('../global_session_settings/global_session_settings_model');
const courseScheduleSchema = require('../models/course_schedule');
const {
    lmsNewSetting,
    getLateAutoAndManualRange,
    changePresentScheduleBasedLateConfigureForSingleStudent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    getChangedDelayedStatusIntoPending,
} = require('../utility/utility.service');
const {
    convertToMongoObjectId,
    clone,
    response_function,
    convertToUtcTimeFormat,
} = require('../utility/common');
const {
    LMS_STUDENT,
    USER,
    COURSE_WISE,
    INDIVIDUAL,
    CUMULATIVE,
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    DAYS_MODE: { YEAR, MONTH },
    PUBLISHED,
    DC_STUDENT,
    PENDING,
    CANCELLED,
    APPROVED,
    WITHDRAWN,
    REJECTED,
    STUDENT_CRITERIA_MANIPULATION,
    STUDENT_GROUP,
    COURSE_SCHEDULE,
    PROGRAM_CALENDAR,
    LMS,
    STUDENT,
    COURSE,
    COMPLETED,
    ANY,
    ALL,
    BY_USER,
    NOT_INITIATED,
    ROLE_LIST,
    ROLE_BASED,
    USER_BASED,
    NOT_APPLICABLE,
    ALL_USERS,
    ANY_ONE_IN_EACH_ROLE,
    ANY_ONE_USER,
    REJECT,
    FORWARD,
    DELAYED,
    SKIPPED,
    ANY_ONE_IN_ANY_ROLE,
    PENDING_WITH_OTHERS,
    PENDING_WITH_YOU,
    HISTORY,
    COURSE_COORDINATOR,
    MISSED,
    ONGOING,
    ABSENT,
    SCHEDULE_STAFF_DEFAULT_ROLE,
    SCHEDULE_TYPES: { REGULAR },
    LMS_PENDING,
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
} = require('../utility/constants');
const moment = require('moment');
const {
    getCourseAdminParams,
    getCourseIdsWithSessions,
    lmsSettings,
    getSessions,
    getAllCourses,
    logicFlowsForStudentApprovals,
    staffApprovalStatusLogics,
    pagination,
    getLmsReportFromCache,
} = require('./lmsStudent.service');

const CourseSchedule = require('../models/course_schedule');
const ProgramCalendar = require('../models/program_calendar');
const digiSessionDeliveryType = require('../models/digi_session_delivery_types');
const { getUserCourseLists } = require('../digi_class/course_session/course_session_service');
const { logger } = require('../utility/util_keys');
const { getPaginationValues } = require('../../commonService/utility/pagination');
const { getSignedURL } = require('../utility/common_functions');

const getUserCourseList = async ({ userId, _institution_calendar_id }) => {
    try {
        const scheduleQuery = {
            'students._id': convertToMongoObjectId(userId),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleDataAggregate = await CourseSchedule.aggregate([
            { $match: scheduleQuery },
            { $unwind: '$students' },
            {
                $group: {
                    count: { $sum: 1 },
                    _id: '$_course_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    students: {
                        $push: {
                            _id: '$students._id',
                            status: '$students.status',
                            schedule_date: '$schedule_date',
                        },
                    },
                    totalDays: {
                        $sum: {
                            $cond: [
                                { $eq: ['$students._id', convertToMongoObjectId(userId)] },
                                1,
                                0,
                            ],
                        },
                    },
                    totalLeave: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        { $eq: ['$students._id', convertToMongoObjectId(userId)] },
                                        {
                                            $or: [
                                                { $eq: ['$students.status', 'absent'] },
                                                { $eq: ['$students.status', 'leave'] },
                                            ],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalOnDuty: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        { $eq: ['$students._id', convertToMongoObjectId(userId)] },
                                        { $eq: ['$students.status', 'on_duty'] },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalPermission: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        { $eq: ['$students._id', convertToMongoObjectId(userId)] },
                                        { $eq: ['$students.status', 'permission'] },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                },
            },
            {
                $project: {
                    _id: '$_id',
                    schedule_date: '$schedule_date',
                    totalDays: '$totalDays',
                    totalLeave: '$totalLeave',
                    totalPermission: '$totalPermission',
                    totalOnDuty: '$totalOnDuty',
                    course_name: '$course_name',
                    course_code: '$course_code',
                    absentPercentage: {
                        $multiply: [{ $divide: ['$totalLeave', '$totalDays'] }, 100],
                    },
                    students: {
                        $filter: {
                            input: '$students',
                            as: 'item',
                            cond: {
                                $and: [{ $eq: ['$$item._id', convertToMongoObjectId(userId)] }],
                            },
                        },
                    },
                },
            },
        ]);
        return scheduleDataAggregate;
    } catch (error) {
        throw new Error(error);
    }
};

const applyOnDuty = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            classificationType,
            studentId,
            categoryName,
            categoryId,
            typeId,
            typeName,
            dateAndTimeRange,
            noOfHours,
            reason,
            attachments,
            academicId,
            gender,
        } = body;
        const dateNow = moment(new Date());
        const thisYear = dateNow.format('Y');
        const thisMonth = dateNow.format('M');
        const lastYear = moment().subtract(1, 'years').format('Y');
        const lastMonth = moment().subtract(1, 'months').format('MMM YYYY');
        const startDate = new Date(dateAndTimeRange.startDate);
        const endDate = new Date(dateAndTimeRange.endDate);
        const startDay = moment(startDate, 'DD.MM.YYYY');
        const endDay = moment(endDate, 'DD.MM.YYYY');
        const startTime = moment(startDate, ['h:mm A']);
        const endTime = moment(endDate, ['h:mm A']);
        const startMonth = startDay.format('M');
        const endMonth = endDay.format('M');
        const startYear = startDay.format('Y');
        const endYear = endDay.format('Y');
        const studentCourseList = await getUserCourseLists({
            userId: studentId,
            type: DC_STUDENT,
            institutionCalendarId: _institution_calendar_id,
        }); /* .filter(
            (studentCourseElement) =>
                new Date(studentCourseElement.start_date) <= dateNow &&
                new Date(studentCourseElement.end_date) >= dateNow,
        ) */

        //?  For Staging
        const selectedProgram = studentCourseList.reduce((acc, curr) => {
            const levelNo = parseInt(curr.level.split(' ')[1]);
            const previousLevelNo = acc ? parseInt(acc.level.split(' ')[1]) : 0;
            if (levelNo > previousLevelNo) {
                acc = curr;
            }
            return acc;
        }, 0);
        //?  For Production
        //  const selectedProgram = studentCourseList.find((studentCourseElem) => {
        //     const courseStartDate = new Date(studentCourseElem.start_date).getTime();
        //     const courseEndDate = new Date(studentCourseElem.end_date).getTime();
        //     const ODApplyStartDate = new Date(dateAndTimeRange.startDate).getTime();
        //     const ODApplyEndDate = new Date(dateAndTimeRange.endDate).getTime();
        //     return ODApplyStartDate >= courseStartDate && ODApplyEndDate <= courseEndDate;
        // });
        if (!selectedProgram)
            return {
                statusCode: 410,
                message: `Unable to Apply ${classificationType} Because There is No Schedules `,
            };
        const { year = null, level = null, term = null, _program_id = null } = selectedProgram;
        const programId = _program_id;
        const lmsStudentData = await lmsStudentSchema
            .find(
                {
                    _institution_id,
                    _institution_calendar_id,
                    classificationType,
                    studentId,
                    ...(categoryId && { categoryId }),
                    ...(typeId && { typeId }),
                    approvalStatus: { $nin: [CANCELLED, WITHDRAWN] },
                },
                {
                    classificationType: 1,
                    studentId: 1,
                    categoryId: 1,
                    categoryName: 1,
                    typeId: 1,
                    noOfHours: 1,
                    approvalStatus: 1,
                    typeName: 1,
                    dateAndTimeRange: 1,
                    programId: 1,
                    level: 1,
                    year: 1,
                    academicId: 1,
                },
            )
            .lean();
        const lmsSettingData = await lmsSettingSchema
            .findOne({
                _institution_id,
                classificationType,
            })
            .lean();
        const checkLevel = lmsSettingData.levelApprover.find((levelApprovalElement) =>
            levelApprovalElement.programIds.toString().includes(programId.toString()),
        );
        if (checkLevel && checkLevel.genderSegregation) {
            checkLevel.level = checkLevel.level.filter((genderLevel) => {
                if (genderLevel.gender.toLowerCase() === gender.toLowerCase()) return genderLevel;
            });
        }
        if (checkLevel === undefined) {
            return { statusCode: 400, message: `Program does not exists in ${classificationType}` };
        }
        if (checkLevel && !checkLevel.level.length) {
            return { statusCode: 400, message: 'Approver is not yet configured' };
        }

        let studentApply;
        const courses = await getUserCourseList({
            userId: studentId,
            _institution_calendar_id,
        });
        let userIds = [];
        let roleIds = [];
        let approvalStatus;
        lmsSettingData.levelApprover
            .filter((levelApproverElement) =>
                levelApproverElement.programIds.toString().includes(programId.toString()),
            )
            .map((levelElement) =>
                levelElement.level.map((item) => {
                    roleIds = [...roleIds, ...item.roleIds];
                    userIds = [...userIds, ...item.userIds];
                    approvalStatus = `Pending with ${levelElement.level[0].levelName}`;
                }),
            );
        // console.log(roleIds);
        // console.log(year, level, term, _program_id);
        const roleData = await roleSchema
            .find(
                {
                    _id: { $in: roleIds.map((id) => convertToMongoObjectId(id)) },
                    // $or: [
                    //     {
                    //         _id: { $in: roleIds.map((id) => convertToMongoObjectId(id)) },
                    //     },
                    //     {
                    //         name: COURSE_COORDINATOR,
                    //     },
                    // ],
                },
                {
                    name: 1,
                },
            )
            .lean();
        let courseCoOrdinatorRole = roleData.find(
            (roleElement) => roleElement.name === COURSE_COORDINATOR,
        );
        if (courseCoOrdinatorRole) courseCoOrdinatorRole = courseCoOrdinatorRole._id;

        const studentCourseCoordinatorsSet = new Set(
            studentCourseList.map((studentCourseListElement) =>
                JSON.stringify({
                    _id: studentCourseListElement._id,
                    _program_id: studentCourseListElement._program_id,
                    'coordinators._institution_calendar_id':
                        studentCourseListElement._institution_calendar_id,
                    'coordinators.year': studentCourseListElement.year,
                    'coordinators.level_no': studentCourseListElement.level,
                    'coordinators.term': studentCourseListElement.term,
                }),
            ),
        );

        const studentCourseCoordinators = Array.from(studentCourseCoordinatorsSet).map(
            (studentCourseListElement) => JSON.parse(studentCourseListElement),
        );

        const digiCourse = courseCoOrdinatorRole
            ? await courseSchema
                  .find(
                      {
                          isDeleted: false,
                          isActive: true,
                          $or: studentCourseCoordinators,
                      },
                      {
                          _program_id: 1,
                          coordinators: 1,
                      },
                  )
                  .populate({ path: 'coordinators._user_id', select: { name: 1 } })
                  .lean()
            : [];
        const courseCoordinators = [];
        if (digiCourse.length) {
            digiCourse.forEach((courseElement) => {
                courseElement.coordinators.forEach((coordinatorElement) => {
                    if (
                        studentCourseList.find(
                            (studentCourseElement) =>
                                String(studentCourseElement._program_id) ===
                                    String(courseElement._program_id) &&
                                String(coordinatorElement._institution_calendar_id) ===
                                    String(studentCourseElement._institution_calendar_id) &&
                                coordinatorElement.term === studentCourseElement.term &&
                                coordinatorElement.year === studentCourseElement.year &&
                                coordinatorElement.level_no === studentCourseElement.level,
                        )
                    ) {
                        courseCoordinators.push(coordinatorElement._user_id);
                    }
                });
            });
        }
        const programBasedRoleIds = roleIds.filter((roleIdElement) =>
            roleData.find(
                (roleElement) =>
                    String(roleElement._id) === String(roleIdElement) &&
                    roleElement.name !== COURSE_COORDINATOR &&
                    roleElement.name !== SCHEDULE_STAFF_DEFAULT_ROLE,
            ),
        );
        const roleAssignData = await roleAssignSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'roles.program._program_id': convertToMongoObjectId(programId),
                    'roles._role_id': { $in: programBasedRoleIds },
                },
                {
                    _user_id: 1,
                    'roles._role_id': 1,
                    'roles.role_name': 1,
                    'roles.program._program_id': 1,
                },
            )
            .lean();
        let assignedRoles = [];
        for (const roleAssign of roleAssignData) {
            const roleIdsElement = roleAssign.roles
                .map((roleElement) => {
                    if (
                        roleElement.program.find(
                            (programElement) =>
                                programElement._program_id.toString() === programId.toString(),
                        )
                    )
                        return roleElement._role_id.toString();
                })
                .filter((roleIdElement) => roleIdElement != undefined);
            assignedRoles.push(roleIdsElement);
        }
        assignedRoles = assignedRoles.flat();
        if (courseCoordinators.length) {
            assignedRoles.push(courseCoOrdinatorRole.toString());
        }
        if (
            !roleData.find(
                (roleDataElement) => roleDataElement.name === SCHEDULE_STAFF_DEFAULT_ROLE,
            )
        )
            for (const roleId of roleIds) {
                if (!assignedRoles.includes(roleId.toString())) {
                    const role = roleData.find(
                        (roleElement) => roleElement._id.toString() === roleId.toString(),
                    );
                    return {
                        statusCode: 410,
                        message: `${role.name} role has no staff for this program, please contact the admin`,
                    };
                }
            }
        let roleUsers = courseCoordinators.length
            ? courseCoordinators.map((courseCoordinatorElement) => courseCoordinatorElement._id)
            : [];
        const roleIdSet = new Set(roleIds.map((roleIdElement) => roleIdElement.toString()));
        for (const roleAssignElement of roleAssignData) {
            if (
                roleAssignElement.roles.length &&
                roleAssignElement.roles.some(
                    (roleElement) =>
                        roleIdSet.has(roleElement._role_id.toString()) &&
                        roleElement.role_name !== SCHEDULE_STAFF_DEFAULT_ROLE &&
                        roleElement.program.length &&
                        roleElement.program.some(
                            (roleProgram) =>
                                roleProgram._program_id.toString() === programId.toString(),
                        ),
                )
            ) {
                roleUsers.push(String(roleAssignElement._user_id));
            }
        }
        if (
            roleData.find((roleDataElement) => roleDataElement.name === SCHEDULE_STAFF_DEFAULT_ROLE)
        ) {
            // Gather Schedule Staffs for that Students
            const studentCourseDetails = [];
            studentCourseList.forEach((studentCourseElement) => {
                studentCourseDetails.push({
                    _institution_calendar_id: convertToMongoObjectId(
                        studentCourseElement._institution_calendar_id,
                    ),
                    _program_id: convertToMongoObjectId(studentCourseElement._program_id),
                    _course_id: convertToMongoObjectId(studentCourseElement._id),
                    year_no: studentCourseElement.year,
                    level_no: studentCourseElement.level,
                    term: studentCourseElement.term,
                });
            });
            const staffSchedule = await CourseSchedule.find(
                {
                    isDeleted: false,
                    isActive: true,
                    'students._id': convertToMongoObjectId(studentId),
                    type: REGULAR,
                    $or: studentCourseDetails,
                },
                {
                    'staffs._staff_id': 1,
                },
            ).lean();
            const uniqueStaffIds = Array.from(
                new Set(
                    staffSchedule.flatMap((scheduleElement) =>
                        scheduleElement.staffs.map((staffElement) =>
                            String(staffElement._staff_id),
                        ),
                    ),
                ),
            );
            if (!uniqueStaffIds.length)
                return {
                    statusCode: 410,
                    message: `${role.name} role has no staff for this program, please contact the admin`,
                };
            roleUsers = [...roleUsers, ...uniqueStaffIds];
        }
        if (classificationType === LEAVE) {
            const categoryPercentage = lmsSettingData.catagories.find(
                (category) => category._id.toString() === categoryId.toString(),
            ).percentage;
            let date;
            const studentLeaves = { totalLeave: 0, totalDays: 0, leaveDates: 0 };
            for (const course of courses) {
                const leaveDates = course.students.filter((doc) => {
                    if (doc.schedule_date) {
                        date = new Date(doc.schedule_date);
                        return (
                            startDate.getTime() <= date.getTime() &&
                            date.getTime() <= endDate.getTime()
                        );
                    }
                });
                studentLeaves.totalLeave += course.totalLeave;
                studentLeaves.totalDays += course.totalDays;
                studentLeaves.leaveDates += leaveDates.length;
            }
            const leavePercentageChecked =
                ((Number(studentLeaves.totalLeave) + Number(studentLeaves.leaveDates)) /
                    Number(studentLeaves.totalDays)) *
                Number(100);
            if (leavePercentageChecked > categoryPercentage) {
                studentApply = await lmsStudentSchema.create({
                    _institution_id,
                    _institution_calendar_id,
                    classificationType,
                    studentId,
                    categoryName,
                    categoryId,
                    typeId,
                    typeName,
                    dateAndTimeRange,
                    noOfHours,
                    reason,
                    attachments,
                    programId,
                    level,
                    year,
                    term,
                    roleIds,
                    userIds,
                    academicId,
                    approvalStatus,
                    levelApprover: checkLevel,
                    roleUsers,
                });
                if (!studentApply) return { statusCode: 410, message: 'UNABLE_TO_CREATE' };
                return {
                    statusCode: 200,
                    message: `Your ${typeName} has been applied successfully`,
                    data: { id: studentApply._id },
                };
            }
        } else {
            let settingFrequency;
            let totalAvailable;
            if (lmsStudentData.length) {
                for (const studentCategory of lmsStudentData) {
                    studentCategory.studentApplied = 0;
                    studentCategory.existStudentApplied = 0;
                    studentCategory.totalPermissionPerYear = 0;
                    studentCategory.totalPermissionPerMonth = 0;
                    for (const settingCategory of lmsSettingData.catagories) {
                        if (
                            studentCategory.categoryId &&
                            settingCategory._id.toString() === studentCategory.categoryId.toString()
                        ) {
                            for (const settingType of settingCategory.types) {
                                if (
                                    settingType._id.toString() === studentCategory.typeId.toString()
                                ) {
                                    const existStartDate =
                                        studentCategory.dateAndTimeRange.startDate;
                                    const existEndDate = studentCategory.dateAndTimeRange.endDate;
                                    if (lmsSettingData.classificationType === PERMISSION) {
                                        const existStartYear = moment(
                                            existStartDate,
                                            'DD.MM.YYYY',
                                        ).format('Y');
                                        const existStartMonth = moment(
                                            existStartDate,
                                            'DD.MM.YYYY',
                                        ).format('M');
                                        if (Number(startYear) === Number(existStartYear)) {
                                            studentCategory.totalPermissionPerYear += 1;
                                        }
                                        if (Number(startMonth) === Number(existStartMonth)) {
                                            studentCategory.totalPermissionPerMonth += 1;
                                        }
                                        const existStartTime = moment(existStartDate, ['h:mm A']);
                                        const existEndTime = moment(existEndDate, ['h:mm A']);
                                        const duration = moment.duration(
                                            existEndTime.diff(existStartTime),
                                        );
                                        const hours = duration.asHours();
                                        studentCategory.studentApplied = Number(hours).toFixed(1);
                                        studentCategory.totalAvailable = settingType.noHours;
                                        totalAvailable = settingType.noHours;
                                        studentCategory.existStudentApplied =
                                            studentCategory.approvalStatus === APPROVED
                                                ? studentCategory.noOfHours
                                                : 0;
                                        studentCategory.frequency = settingType.frequency;
                                        settingFrequency = settingType.frequency;
                                    } else {
                                        const existStartDay = moment(existStartDate, 'DD.MM.YYYY');
                                        const existEndDay = moment(existEndDate, 'DD.MM.YYYY');
                                        const days = studentCategory.noOfHours;
                                        studentCategory.studentApplied = days;
                                        studentCategory.totalAvailable = settingType.noHours;
                                        totalAvailable = settingType.noHours;
                                        studentCategory.frequency = settingType.frequency;
                                        settingFrequency = settingType.frequency;
                                        if (studentCategory.frequency.frequencyBy === YEAR) {
                                            const existStartYear = existStartDay.format('Y');
                                            const existEndYear = existEndDay.format('Y');
                                            if (Number(existStartYear) === Number(startYear)) {
                                                const existStartDate =
                                                    studentCategory.dateAndTimeRange.startDate;
                                                const existEndDate =
                                                    studentCategory.dateAndTimeRange.endDate;
                                                const existStartDay = moment(
                                                    existStartDate,
                                                    'DD.MM.YYYY',
                                                );
                                                const existEndDay = moment(
                                                    existEndDate,
                                                    'DD.MM.YYYY',
                                                );
                                                existDays = Number(
                                                    existEndDay.diff(existStartDay, 'days'),
                                                );
                                                studentCategory.existStudentApplied =
                                                    studentCategory.approvalStatus === APPROVED
                                                        ? studentCategory.noOfHours
                                                        : 0;
                                            } else {
                                                studentCategory.existStudentApplied = 0;
                                            }
                                        } else if (
                                            studentCategory.frequency.frequencyBy === MONTH
                                        ) {
                                            const existStartMonth = existStartDay.format('M');
                                            const existEndMonth = existEndDay.format('M');
                                            if (Number(existStartMonth) === Number(startMonth)) {
                                                const existStartDate =
                                                    studentCategory.dateAndTimeRange.startDate;
                                                const existEndDate =
                                                    studentCategory.dateAndTimeRange.endDate;
                                                const existStartDay = moment(
                                                    existStartDate,
                                                    'DD.MM.YYYY',
                                                );
                                                const existEndDay = moment(
                                                    existEndDate,
                                                    'DD.MM.YYYY',
                                                );
                                                const existDays = studentCategory.noOfHours;
                                                studentCategory.existStudentApplied =
                                                    studentCategory.approvalStatus === APPROVED
                                                        ? studentCategory.noOfHours
                                                        : 0;
                                            } else {
                                                studentCategory.existStudentApplied = 0;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            } else {
                for (const settingCategory of lmsSettingData.catagories) {
                    if (settingCategory._id.toString() === categoryId.toString()) {
                        for (const settingType of settingCategory.types) {
                            if (settingType._id.toString() === typeId.toString()) {
                                totalAvailable = settingType.noHours;
                                settingFrequency = settingType.frequency;
                            }
                        }
                    }
                }
            }
            const typeWiseStatus = [
                ...lmsStudentData
                    .reduce((r, o) => {
                        const key =
                            o.classificationType +
                            '-' +
                            o.studentId +
                            '-' +
                            o.categoryId +
                            '-' +
                            o.typeId;

                        const item = r.get(key) || {
                            ...o,
                            studentApplied: 0,
                            existStudentApplied: 0,
                            totalPermissionPerYear: 0,
                            totalPermissionPerMonth: 0,
                        };
                        item.studentApplied += Number(o.studentApplied);
                        item.existStudentApplied += o.existStudentApplied;
                        item.totalPermissionPerYear += o.totalPermissionPerYear;
                        item.totalPermissionPerMonth += o.totalPermissionPerMonth;
                        return r.set(key, item);
                    }, new Map())
                    .values(),
            ];
            let studentPermission = 0;
            let frequencyCheck;
            let endFrequencyCheck;
            if (typeWiseStatus.length === 0) {
                if (settingFrequency.frequencyBy === YEAR) {
                    if (Number(startYear) === Number(endYear)) {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                        } else {
                            const studentAskingDays = noOfHours;
                            const studentAskingDaysInHours = noOfHours;
                            const remainingLeave = totalAvailable;
                            studentPermission = remainingLeave - studentAskingDaysInHours;
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck;
                        }
                    } else {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            let endOfMonth = moment(startDay).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endDay).startOf('month').format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                            const endRemainingFrequencyCheck = settingFrequency.setFrequency;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        } else {
                            const studentAskingDays = noOfHours;
                            const remainingLeave = totalAvailable;
                            let endOfMonth = moment(startTime).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endTime)
                                .startOf('month')
                                .format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                            const endRemainingFrequencyCheck = settingFrequency.setFrequency;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        }
                    }
                    if (settingFrequency.frequencyByMonth) {
                        if (Number(startMonth) === Number(endMonth)) {
                            if (classificationType === ONDUTY) {
                                const studentAskingDays = noOfHours;
                                const remainingFrequencyCheck = settingFrequency.frequencyByMonth;
                                frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                            } else {
                                const studentAskingDays = noOfHours;
                                const studentAskingDaysInHours = noOfHours;
                                const remainingLeave = totalAvailable;
                                studentPermission = remainingLeave - studentAskingDaysInHours;
                                const remainingFrequencyCheck = settingFrequency.frequencyByMonth;
                                frequencyCheck = remainingFrequencyCheck;
                            }
                        } else {
                            if (classificationType === ONDUTY) {
                                const studentAskingDays = noOfHours;
                                let endOfMonth = moment(startDay)
                                    .endOf('month')
                                    .format('DD.MM.YYYY');
                                endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                                const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                                const remainingFrequencyCheck = settingFrequency.frequencyByMonth;
                                frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                                let startOfMonth = moment(endDay)
                                    .startOf('month')
                                    .format('DD.MM.YYYY');
                                startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                                const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                                const endRemainingFrequencyCheck =
                                    settingFrequency.frequencyByMonth;
                                endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                            } else {
                                const studentAskingDays = noOfHours;
                                const remainingLeave = totalAvailable;
                                let endOfMonth = moment(startTime)
                                    .endOf('month')
                                    .format('DD.MM.YYYY');
                                endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                                const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                                const remainingFrequencyCheck = settingFrequency.frequencyByMonth;
                                frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                                let startOfMonth = moment(endTime)
                                    .startOf('month')
                                    .format('DD.MM.YYYY');
                                startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                                const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                                const endRemainingFrequencyCheck =
                                    settingFrequency.frequencyByMonth;
                                endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                            }
                        }
                    }
                } else if (settingFrequency.frequencyBy === MONTH) {
                    if (Number(startMonth) === Number(endMonth)) {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                        } else {
                            const studentAskingDays = noOfHours;
                            const studentAskingDaysInHours = noOfHours;
                            const remainingLeave = totalAvailable;
                            studentPermission = remainingLeave - studentAskingDaysInHours;
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck;
                        }
                    } else {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            let endOfMonth = moment(startDay).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endDay).startOf('month').format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                            const endRemainingFrequencyCheck = settingFrequency.setFrequency;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        } else {
                            const studentAskingDays = noOfHours;
                            const remainingLeave = totalAvailable;
                            let endOfMonth = moment(startTime).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                            const remainingFrequencyCheck = settingFrequency.setFrequency;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endTime)
                                .startOf('month')
                                .format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                            const endRemainingFrequencyCheck = settingFrequency.setFrequency;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        }
                    }
                }
            } else if (typeWiseStatus.length && typeWiseStatus[0].frequency.frequencyBy === YEAR) {
                if (Number(startYear) === Number(endYear)) {
                    if (classificationType === ONDUTY) {
                        const studentAskingDays = noOfHours;
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                    } else {
                        const studentAskingDays = noOfHours;
                        const studentAskingDaysInHours = noOfHours;
                        const remainingLeave =
                            typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                        studentPermission = remainingLeave - studentAskingDaysInHours;
                        const remainingFrequencyCheck = typeWiseStatus[0].frequency.setFrequency;
                        frequencyCheck =
                            remainingFrequencyCheck - typeWiseStatus[0].totalPermissionPerYear;
                    }
                } else {
                    if (classificationType === ONDUTY) {
                        const studentAskingDays = noOfHours;
                        let endOfMonth = moment(startDay).endOf('month').format('DD.MM.YYYY');
                        endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                        const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                        let startOfMonth = moment(endDay).startOf('month').format('DD.MM.YYYY');
                        startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                        const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                        const endRemainingFrequencyCheck = typeWiseStatus[0].frequency.setFrequency;
                        endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                    } else {
                        const studentAskingDays = noOfHours;
                        const studentAskingDaysInHours = noOfHours;
                        const remainingLeave =
                            typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                        studentPermission = remainingLeave - studentAskingDaysInHours;
                        let endOfMonth = moment(startTime).endOf('month').format('DD.MM.YYYY');
                        endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                        const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                        let startOfMonth = moment(endTime).startOf('month').format('DD.MM.YYYY');
                        startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                        const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                        const endRemainingFrequencyCheck = typeWiseStatus[0].frequency.setFrequency;
                        endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                    }
                }
                if (typeWiseStatus[0].frequency.frequencyByMonth) {
                    if (Number(startMonth) === Number(endMonth)) {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            const remainingLeave =
                                typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                            const remainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth -
                                typeWiseStatus[0].existStudentApplied;
                            frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                        } else {
                            const studentAskingDays = noOfHours;
                            const studentAskingDaysInHours = noOfHours;
                            const remainingLeave =
                                typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                            studentPermission = remainingLeave - studentAskingDaysInHours;
                            const remainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth -
                                typeWiseStatus[0].existStudentApplied;
                            frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                        }
                    } else {
                        if (classificationType === ONDUTY) {
                            const studentAskingDays = noOfHours;
                            let endOfMonth = moment(startDay).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                            const remainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth -
                                typeWiseStatus[0].existStudentApplied;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endDay).startOf('month').format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                            const endRemainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        } else {
                            const studentAskingDays = noOfHours;
                            const studentAskingDaysInHours = noOfHours;
                            const remainingLeave =
                                typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                            studentPermission = remainingLeave - studentAskingDaysInHours;
                            let endOfMonth = moment(startTime).endOf('month').format('DD.MM.YYYY');
                            endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                            const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                            const remainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth -
                                typeWiseStatus[0].existStudentApplied;
                            frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                            let startOfMonth = moment(endTime)
                                .startOf('month')
                                .format('DD.MM.YYYY');
                            startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                            const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                            const endRemainingFrequencyCheck =
                                typeWiseStatus[0].frequency.frequencyByMonth;
                            endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                        }
                    }
                }
            } else if (typeWiseStatus.length && typeWiseStatus[0].frequency.frequencyBy === MONTH) {
                if (Number(startMonth) === Number(endMonth)) {
                    if (classificationType === ONDUTY) {
                        const studentAskingDays = noOfHours;
                        const remainingLeave =
                            typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                    } else {
                        const studentAskingDays = noOfHours;
                        const studentAskingDaysInHours = noOfHours;
                        const remainingLeave =
                            typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                        studentPermission = remainingLeave - studentAskingDaysInHours;
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - studentAskingDays;
                    }
                } else {
                    if (classificationType === ONDUTY) {
                        const studentAskingDays = noOfHours;
                        let endOfMonth = moment(startDay).endOf('month').format('DD.MM.YYYY');
                        endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                        const startDaysLeft = Number(endOfMonth.diff(startDay, 'days'));
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                        let startOfMonth = moment(endDay).startOf('month').format('DD.MM.YYYY');
                        startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                        const endDaysLeft = Number(endDay.diff(startOfMonth, 'days'));
                        const endRemainingFrequencyCheck = typeWiseStatus[0].frequency.setFrequency;
                        endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                    } else {
                        const studentAskingDays = noOfHours;
                        const studentAskingDaysInHours = noOfHours;
                        const remainingLeave =
                            typeWiseStatus[0].totalAvailable - typeWiseStatus[0].studentApplied;
                        studentPermission = remainingLeave - studentAskingDaysInHours;
                        let endOfMonth = moment(startTime).endOf('month').format('DD.MM.YYYY');
                        endOfMonth = moment(endOfMonth, 'DD.MM.YYYY');
                        const startDaysLeft = Number(endOfMonth.diff(startTime, 'hours'));
                        const remainingFrequencyCheck =
                            typeWiseStatus[0].frequency.setFrequency -
                            typeWiseStatus[0].existStudentApplied;
                        frequencyCheck = remainingFrequencyCheck - startDaysLeft;
                        let startOfMonth = moment(endTime).startOf('month').format('DD.MM.YYYY');
                        startOfMonth = moment(startOfMonth, 'DD.MM.YYYY');
                        const endDaysLeft = Number(endTime.diff(startOfMonth, 'hours'));
                        const endRemainingFrequencyCheck = typeWiseStatus[0].frequency.setFrequency;
                        endFrequencyCheck = endRemainingFrequencyCheck - endDaysLeft;
                    }
                }
            }
            if (studentPermission < 0 || frequencyCheck < 0 || endFrequencyCheck < 0) {
                studentApply = await lmsStudentSchema.create({
                    _institution_id,
                    _institution_calendar_id,
                    classificationType,
                    studentId,
                    categoryName,
                    categoryId,
                    typeId,
                    typeName,
                    dateAndTimeRange,
                    noOfHours,
                    reason,
                    attachments,
                    programId,
                    level,
                    year,
                    term,
                    roleIds,
                    userIds,
                    academicId,
                    approvalStatus,
                    levelApprover: checkLevel,
                    roleUsers,
                });
                return {
                    statusCode: 200,
                    message: `Your ${typeName} has been applied successfully`,
                    data: { id: studentApply._id, isExceed: false },
                };
            }
        }
        studentApply = await lmsStudentSchema.create({
            _institution_id,
            classificationType,
            _institution_calendar_id,
            studentId,
            categoryName,
            categoryId,
            typeId,
            typeName,
            dateAndTimeRange,
            noOfHours,
            reason,
            attachments,
            programId,
            level,
            year,
            term,
            roleIds,
            userIds,
            academicId,
            approvalStatus,
            levelApprover: checkLevel,
            roleUsers,
        });
        if (!studentApply) return { statusCode: 410, message: 'UNABLE_TO_CREATE' };
        return {
            statusCode: 200,
            message: `Your ${typeName} has been applied successfully`,
            data: { id: studentApply._id, isExceed: false },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listData = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { classificationType, studentId } = query;
        let lmsStudentData = await lmsStudentSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    classificationType,
                    studentId: convertToMongoObjectId(studentId),
                    isDeleted: false,
                },
                {
                    categoryId: 1,
                    typeId: 1,
                    categoryName: 1,
                    dateAndTimeRange: 1,
                    noOfHours: 1,
                    approvalStatus: 1,
                    typeName: 1,
                    doNotShowAgain: 1,
                    agree: 1,
                    attachments: 1,
                    approvalFrom: 1,
                    leaveApplicationCriteria: 1,
                    scheduleId: 1,
                    createdAt: 1,
                },
                {
                    sort: { updatedAt: -1 },
                },
            )
            .populate({ path: 'approvalFrom.userId', select: { name: 1 } })
            .populate({
                path: 'scheduleId',
                select: {
                    isDeleted: 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'session.session_topic': 1,
                },
            })
            .lean();
        lmsStudentData = lmsStudentData.filter(
            ({ scheduleId }) => !(scheduleId?.isDeleted ?? false),
        );
        if (!lmsStudentData) return { statusCode: 410, message: 'NO_DATA' };
        const lmsSettingData = await lmsSettingSchema
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType,
            })
            .lean();
        let typeWiseStatus = [];
        let doNotShowAgain = false;
        let agree = false;
        const existsCategoryIds = [];
        const existsTypesIds = [];
        const inActiveTypes = [];
        if (lmsStudentData.length) {
            for (const [index, studentCategory] of lmsStudentData.entries()) {
                if (!doNotShowAgain && studentCategory.doNotShowAgain === true) {
                    doNotShowAgain = true;
                }
                if (!agree && studentCategory.agree === true) {
                    agree = true;
                }
                for (const settingCategory of lmsSettingData.catagories) {
                    if (!settingCategory.isActive) {
                        inActiveTypes.push(settingCategory._id.toString());
                    }
                    if (
                        studentCategory.categoryId &&
                        settingCategory.isActive &&
                        settingCategory._id.toString() === studentCategory.categoryId.toString()
                    ) {
                        for (const settingType of settingCategory.types) {
                            if (!settingType.isActive) {
                                inActiveTypes.push(settingType._id.toString());
                            }
                            if (
                                settingType._id?.toString() ===
                                    studentCategory?.typeId?.toString() &&
                                settingType.isActive
                            ) {
                                if (
                                    settingCategory.isActive &&
                                    !existsCategoryIds.includes(settingCategory._id)
                                )
                                    existsCategoryIds.push(settingCategory._id?.toString());
                                if (!existsTypesIds.includes(settingType._id)) {
                                    existsTypesIds.push(settingType._id?.toString());
                                }
                                studentCategory.studentApplied = studentCategory.noOfHours;
                                studentCategory.totalAvailable = settingType.noHours;
                                studentCategory.frequency = settingType.frequency;
                            }
                        }
                    }
                }
            }
            lmsStudentData = lmsStudentData.filter(
                (element) => element.categoryId && element.categoryId != null,
            );
            typeWiseStatus = [
                ...lmsStudentData
                    .reduce((r, o) => {
                        const key =
                            o.classificationType +
                            '-' +
                            o.studentId +
                            '-' +
                            o.categoryId +
                            '-' +
                            o.typeId;

                        const item = r.get(key) || {
                            categoryId: o.categoryId,
                            typeId: o.typeId,
                            typeName: o.typeName,
                            categoryName: o.categoryName,
                            totalAvailable: o.totalAvailable,
                            availedLeave: 0,
                            studentApplied: 0,
                            isActive: true,
                        };
                        if (o.approvalStatus === APPROVED && o.studentApplied) {
                            item.studentApplied += o.studentApplied;
                        }
                        if (o.agree === true) {
                            agree = true;
                        }
                        if (o.approvalStatus === APPROVED && o.noOfHours) {
                            item.availedLeave += o.noOfHours;
                        }
                        if (
                            inActiveTypes.includes(o.typeId?.toString()) ||
                            inActiveTypes.includes(o.categoryId?.toString())
                        ) {
                            item.isActive = false;
                        }
                        return r.set(key, item);
                    }, new Map())
                    .values(),
            ];
            typeWiseStatus = typeWiseStatus.filter((elem) => elem);
            typeWiseStatus = typeWiseStatus.map((o) => ({
                studentApplied: o.studentApplied,
                availedLeave: o.availedLeave,
                totalAvailable: o.totalAvailable,
                categoryName: o.categoryName,
                categoryId: o.categoryId,
                typeName: o.typeName,
                typeId: o.typeId,
                isActive: o.isActive,
            }));
            for (const settingCategory of lmsSettingData.catagories) {
                if (!existsCategoryIds.includes(settingCategory._id.toString())) {
                    for (const settingType of settingCategory.types) {
                        if (
                            settingCategory.isActive &&
                            !existsTypesIds.includes(settingType._id?.toString()) &&
                            settingType.isActive
                        ) {
                            typeWiseStatus.push({
                                studentApplied: 0,
                                availedLeave: 0,
                                totalAvailable: settingType.noHours,
                                categoryName: settingCategory.categoryName,
                                categoryId: settingCategory._id,
                                typeName: settingType.typeName,
                                typeId: settingType._id,
                                isActive: true,
                            });
                        }
                    }
                } else {
                    for (const settingType of settingCategory.types) {
                        if (
                            settingCategory.isActive &&
                            !existsTypesIds.includes(settingType._id.toString()) &&
                            settingType.isActive
                        ) {
                            typeWiseStatus.push({
                                studentApplied: 0,
                                availedLeave: 0,
                                totalAvailable: settingType.noHours,
                                categoryName: settingCategory.categoryName,
                                categoryId: settingCategory._id,
                                typeName: settingType.typeName,
                                typeId: settingType._id,
                                isActive: true,
                            });
                        }
                    }
                }
            }
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: { lmsStudentData, typeWiseStatus, doNotShowAgain, agree },
            };
        }
        for (const settingCategory of lmsSettingData.catagories) {
            for (const settingType of settingCategory.types) {
                if (settingCategory.isActive && settingType._id && settingType.isActive) {
                    typeWiseStatus.push({
                        studentApplied: 0,
                        totalAvailable: settingType.noHours,
                        categoryName: settingCategory.categoryName,
                        categoryId: settingCategory._id,
                        typeName: settingType.typeName,
                        typeId: settingType._id,
                        isActive: true,
                    });
                }
            }
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { lmsStudentData, typeWiseStatus, doNotShowAgain, agree },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateStatus = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id, categoryId, approvalStatus, typeId, reason, bulkCancelId = [] } = body;
        const filterQuery = bulkCancelId.length
            ? {
                  _id: {
                      $in: bulkCancelId.map((bulkCancelIdElement) =>
                          convertToMongoObjectId(bulkCancelIdElement),
                      ),
                  },
              }
            : { _id: convertToMongoObjectId(id) };
        if (approvalStatus === CANCELLED) {
            const studentSettingData = await lmsStudentSchema
                .updateMany(
                    filterQuery,
                    {
                        $set: {
                            approvalStatus,
                            reason,
                        },
                    },
                    {
                        new: true,
                    },
                )
                .lean();
            if (!studentSettingData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_DATA' };
        } else if (approvalStatus === WITHDRAWN) {
            const studentData = await lmsStudentSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(id),
                    },
                    {
                        studentId: 1,
                        classificationType: 1,
                        dateAndTimeRange: 1,
                    },
                )
                .lean();
            const courseScheduleData = await CourseSchedule.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    'students._id': convertToMongoObjectId(studentData.studentId),
                    scheduleStartDateAndTime:
                        studentData.classificationType === LEAVE &&
                        studentData.dateAndTimeRange.startSession
                            ? {
                                  $gte: new Date(
                                      studentData.dateAndTimeRange.startSession.sessionStart,
                                  ),
                              }
                            : {
                                  $gte: new Date(studentData.dateAndTimeRange.startDate),
                              },
                    scheduleEndDateAndTime:
                        studentData.classificationType === LEAVE &&
                        studentData.dateAndTimeRange.endSession
                            ? {
                                  $lte: new Date(
                                      studentData.dateAndTimeRange.endSession.sessionEnd,
                                  ),
                              }
                            : {
                                  $lte: new Date(studentData.dateAndTimeRange.endDate),
                              },
                },
                {
                    status: 1,
                },
            ).lean();
            const bulkOperations = [];
            const completedScheduleIndex = courseScheduleData.findIndex(
                (scheduleElement) => scheduleElement.status === COMPLETED,
            );
            if (completedScheduleIndex !== -1) {
                return {
                    statusCode: 400,
                    message: `Unable To Withdrawn,Because The Schedule Has Been Completed For The Applied ${studentData.classificationType} Date.`,
                };
            }
            const updatedStudentSettingData = await lmsStudentSchema
                .updateOne(
                    {
                        _id: convertToMongoObjectId(id),
                    },
                    {
                        $set: {
                            approvalStatus,
                            reason,
                        },
                    },
                )
                .lean();
            if (!updatedStudentSettingData)
                return { statusCode: 410, message: 'UNABLE_TO_UPDATE_DATA' };
            for (scheduleElement of courseScheduleData) {
                const update = {
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(scheduleElement._id),
                            'students._id': convertToMongoObjectId(studentData.studentId),
                        },
                        update: {
                            $set: {
                                'students.$[i].status':
                                    scheduleElement.status === ONGOING ? ABSENT : PENDING,
                            },
                        },
                        arrayFilters: [
                            {
                                'i._id': convertToMongoObjectId(studentData.studentId),
                            },
                        ],
                    },
                };
                bulkOperations.push(update);
            }
            if (bulkOperations.length) {
                await CourseSchedule.bulkWrite(bulkOperations);
            }
        }
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttendanceSheets = async ({ query = {}, headers = {} }) => {
    try {
        const { userId, institutionCalendarId, coordinators } = query;
        const { _institution_id } = headers;
        const courses = await getAllCourses(
            userId,
            coordinators,
            institutionCalendarId,
            _institution_id,
        );
        return { statusCode: 200, message: 'DS_DATA_RETRIEVED', data: courses };
    } catch (error) {
        return { statusCode: 400, message: error.message };
    }
};

const previouslyLeaveStatus = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { classificationType, studentId } = query;
        const lmsStudentData = await lmsStudentSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType,
                    studentId: convertToMongoObjectId(studentId),
                },
                {
                    dateAndTimeRange: 1,
                    noOfHours: 1,
                    typeName: 1,
                    approvalStatus: 1,
                    comment: 1,
                },
            )
            .sort({ createdAt: -1 });
        if (!lmsStudentData) return { statusCode: 410, message: 'NO_DATA' };
        const courses = await getUserCourseList({
            userId: studentId,
            _institution_calendar_id,
        });
        return { statusCode: 200, message: 'LIST_DATA', data: { lmsStudentData, courses } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listSchedule = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            startDate,
            endDate,
            _student_id,
            scheduleIds = [],
            leaveApplicationCriteria = DAY,
        } = query;
        const start = new Date(startDate);
        const end = new Date(endDate);
        const studentCourseList = await getUserCourseLists({
            userId: _student_id,
            type: DC_STUDENT,
            institutionCalendarId: _institution_calendar_id,
        });
        //?  For Staging
        const selectedProgram = studentCourseList.reduce((acc, curr) => {
            const levelNo = parseInt(curr.level.split(' ')[1]);
            const previousLevelNo = acc ? parseInt(acc.level.split(' ')[1]) : 0;
            if (levelNo > previousLevelNo) {
                acc = curr;
            }
            return acc;
        }, 0);
        const { year = null, level = null, term = null } = selectedProgram;
        const lmsStudentDateCheck = await lmsStudentSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    studentId: convertToMongoObjectId(_student_id),
                    approvalStatus: { $nin: [CANCELLED, WITHDRAWN, REJECTED] },
                    $or: [
                        {
                            $and: [
                                { 'dateAndTimeRange.startDate': { $lte: start } },
                                { 'dateAndTimeRange.endDate': { $gte: start } },
                            ],
                        },
                        {
                            $and: [
                                { 'dateAndTimeRange.startDate': { $lte: end } },
                                { 'dateAndTimeRange.endDate': { $gte: end } },
                            ],
                        },
                        {
                            $and: [
                                { 'dateAndTimeRange.startDate': { $gte: start } },
                                { 'dateAndTimeRange.endDate': { $lte: end } },
                            ],
                        },
                    ],
                    ...(scheduleIds.length
                        ? {
                              scheduleId: {
                                  $in: scheduleIds.map(({ scheduleIdElement }) =>
                                      convertToMongoObjectId(scheduleIdElement),
                                  ),
                              },
                          }
                        : leaveApplicationCriteria === SESSION
                        ? { leaveApplicationCriteria: SESSION }
                        : {}),
                    isDeleted: false,
                },
                { scheduleId: 1 },
            )
            .lean();
        const existingLeaveAppliedScheduleIds = lmsStudentDateCheck.map(
            ({ scheduleId }) => scheduleId,
        );
        const lmsData = await lmsNewSetting({ _institution_id, leaveApplicationCriteria });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId: studentCourseList.map((courseElement) => courseElement._id),
            programId: lmsStudentDateCheck.programId,
            yearNo: lmsStudentDateCheck.year,
            levelNo: lmsStudentDateCheck.level,
            term: lmsStudentDateCheck.term,
        });
        let listSchedules;
        if (lmsData.leaveCalculation === 'hours') {
            listSchedules = await CourseSchedule.aggregate([
                ...(scheduleIds.length
                    ? [
                          {
                              $match: {
                                  _id: {
                                      $in: scheduleIds.map((scheduleIdElement) =>
                                          convertToMongoObjectId(scheduleIdElement),
                                      ),
                                  },
                                  isActive: true,
                                  isDeleted: false,
                              },
                          },
                      ]
                    : [
                          {
                              $match: {
                                  _institution_id: convertToMongoObjectId(_institution_id),
                                  'students._id': convertToMongoObjectId(_student_id),
                                  type: 'regular',
                                  _institution_calendar_id:
                                      convertToMongoObjectId(_institution_calendar_id),
                                  isActive: true,
                                  isDeleted: false,
                              },
                          },
                      ]),
                {
                    $sort: { schedule_date: 1 },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        localField: '_course_id',
                        foreignField: '_id',
                        as: 'courseVersionDetailsDetails',
                    },
                },
                {
                    $unwind: {
                        path: '$courseVersionDetailsDetails',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        versionNo: { $ifNull: ['$courseVersionDetailsDetails.versionNo', 1] },
                        versioned: { $ifNull: ['$courseVersionDetailsDetails.versioned', false] },
                        versionName: { $ifNull: ['$courseVersionDetailsDetails.versionName', ''] },
                        versionedFrom: {
                            $ifNull: ['$courseVersionDetailsDetails.versionedFrom', null],
                        },
                        versionedCourseIds: {
                            $ifNull: ['$courseVersionDetailsDetails.versionedCourseIds', []],
                        },
                    },
                },
                { $unwind: '$students' },
                {
                    $group: {
                        count: { $sum: 1 },
                        _id: { _course_id: '$_course_id', session_type: '$session.session_type' },
                        _course_id: { $first: '$_course_id' },
                        _program_id: { $first: '$_program_id' },
                        program_name: { $first: '$program_name' },
                        course_name: { $first: '$course_name' },
                        course_code: { $first: '$course_code' },
                        versionNo: { $first: '$versionNo' },
                        versioned: { $first: '$versioned' },
                        versionName: { $first: '$versionName' },
                        versionedFrom: { $first: '$versionedFrom' },
                        versionedCourseIds: { $first: '$versionedCourseIds' },
                        session_type: { $first: '$session.session_type' },
                        schedules: {
                            $push: {
                                status: '$students.status',
                                schedule_date: '$schedule_date',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                scheduleEndDateAndTime: '$scheduleEndDateAndTime',
                                mergeStatus: '$merge_status',
                                mergeWith: '$merge_with',
                                subject_name: '$subjects.subject_name',
                                staff_name: '$staffs.staff_name',
                                staffId: '$staffs._staff_id',
                                session_type: '$session.session_type',
                                session_topic: '$session.session_topic',
                                s_no: '$session.s_no',
                                session_id: '$session._session_id',
                                delivery_no: '$session.delivery_no',
                                delivery_symbol: '$session.delivery_symbol',
                                studentId: '$students._id',
                                scheduleId: '$_id',
                                type: '$type',
                                title: '$title',
                            },
                        },
                        totalDays: {
                            $sum: {
                                $cond: [
                                    {
                                        $eq: ['$students._id', convertToMongoObjectId(_student_id)],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        upcomingLeave: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            {
                                                $gte: [
                                                    '$scheduleStartDateAndTime',
                                                    new Date(startDate),
                                                ],
                                            },
                                            {
                                                $lte: [
                                                    '$scheduleEndDateAndTime',
                                                    new Date(endDate),
                                                ],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalLeave: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            { $eq: ['$students.status', 'on_duty'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalPermission: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            { $eq: ['$students.status', 'permission'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        let: { courseId: '$_id._course_id', sessionType: '$_id.session_type' },
                        pipeline: [
                            { $unwind: '$credit_hours' },
                            { $unwind: '$credit_hours.delivery_type' },
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$_id', '$$courseId'] },
                                            {
                                                $eq: [
                                                    '$credit_hours.delivery_type.delivery_type',
                                                    '$$sessionType',
                                                ],
                                            },
                                        ],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    'credit_hours.delivery_type.duration': 1,
                                },
                            },
                        ],
                        as: 'courseCreditHours',
                    },
                },
                {
                    $addFields: {
                        sessionCreditHours: {
                            $divide: [
                                {
                                    $arrayElemAt: [
                                        '$courseCreditHours.credit_hours.delivery_type.duration',
                                        0,
                                    ],
                                },
                                60,
                            ],
                        },
                    },
                },
                {
                    $addFields: {
                        totalDays: {
                            $multiply: ['$totalDays', '$sessionCreditHours'],
                        },
                        totalLeave: {
                            $multiply: ['$totalLeave', '$sessionCreditHours'],
                        },
                        upcomingLeave: {
                            $multiply: ['$upcomingLeave', '$sessionCreditHours'],
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id._course_id',
                        course_name: '$course_name',
                        sessionCreditHours: '$sessionCreditHours',
                        program_name: '$program_name',
                        course_code: '$course_code',
                        versionNo: '$versionNo',
                        versioned: '$versioned',
                        versionName: '$versionName',
                        versionedFrom: '$versionedFrom',
                        versionedCourseIds: '$versionedCourseIds',
                        totalDays: '$totalDays',
                        totalLeave: '$totalLeave',
                        upcomingLeave: '$upcomingLeave',
                        totalPermission: '$totalPermission',
                        totalOnDuty: '$totalOnDuty',
                        absentPercentage: {
                            $multiply: [{ $divide: ['$totalLeave', '$totalDays'] }, 100],
                        },
                        predictedAbsentPercentage: {
                            $multiply: [
                                {
                                    $divide: [
                                        { $sum: ['$totalLeave', '$upcomingLeave'] },
                                        '$totalDays',
                                    ],
                                },
                                100,
                            ],
                        },
                        schedules: {
                            $filter: {
                                input: '$schedules',
                                as: 'item',
                                cond: {
                                    $and: [
                                        {
                                            $eq: [
                                                '$$item.studentId',
                                                convertToMongoObjectId(_student_id),
                                            ],
                                        },
                                        {
                                            $gte: [
                                                '$$item.scheduleStartDateAndTime',
                                                new Date(startDate),
                                            ],
                                        },
                                        {
                                            $lte: [
                                                '$$item.scheduleEndDateAndTime',
                                                new Date(endDate),
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                    },
                },
                {
                    $group: {
                        _id: '$_id',
                        course_name: { $first: '$course_name' },
                        program_name: { $first: '$program_name' },
                        sessionCreditHours: { $first: '$sessionCreditHours' },
                        course_code: { $first: '$course_code' },
                        totalDays: { $sum: '$totalDays' },
                        totalLeave: { $sum: '$totalLeave' },
                        upcomingLeave: { $sum: '$upcomingLeave' },
                        totalOnDuty: { $sum: '$totalOnDuty' },
                        absentPercentage: { $sum: '$absentPercentage' },
                        predictedAbsentPercentage: { $sum: '$predictedAbsentPercentage' },
                        schedules: { $first: '$schedules' },
                    },
                },
            ]);
        } else {
            listSchedules = await CourseSchedule.aggregate([
                ...(scheduleIds.length
                    ? [
                          {
                              $match: {
                                  _id: {
                                      $in: scheduleIds.map((scheduleIdElement) =>
                                          convertToMongoObjectId(scheduleIdElement),
                                      ),
                                  },
                                  isActive: true,
                                  isDeleted: false,
                              },
                          },
                      ]
                    : [
                          {
                              $match: {
                                  _institution_id: convertToMongoObjectId(_institution_id),
                                  'students._id': convertToMongoObjectId(_student_id),
                                  type: 'regular',
                                  _institution_calendar_id:
                                      convertToMongoObjectId(_institution_calendar_id),
                                  isActive: true,
                                  isDeleted: false,
                              },
                          },
                      ]),
                {
                    $sort: { schedule_date: 1 },
                },
                {
                    $lookup: {
                        from: 'digi_courses',
                        localField: '_course_id',
                        foreignField: '_id',
                        as: 'courseVersionDetailsDetails',
                    },
                },
                {
                    $unwind: {
                        path: '$courseVersionDetailsDetails',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        versionNo: { $ifNull: ['$courseVersionDetailsDetails.versionNo', 1] },
                        versioned: { $ifNull: ['$courseVersionDetailsDetails.versioned', false] },
                        versionName: { $ifNull: ['$courseVersionDetailsDetails.versionName', ''] },
                        versionedFrom: {
                            $ifNull: ['$courseVersionDetailsDetails.versionedFrom', null],
                        },
                        versionedCourseIds: {
                            $ifNull: ['$courseVersionDetailsDetails.versionedCourseIds', []],
                        },
                    },
                },
                { $unwind: '$students' },
                {
                    $match: {
                        'students._id': convertToMongoObjectId(_student_id),
                    },
                },
                {
                    $group: {
                        count: { $sum: 1 },
                        _id: '$_course_id',
                        course_name: { $first: '$course_name' },
                        program_name: { $first: '$program_name' },
                        course_code: { $first: '$course_code' },
                        versionNo: { $first: '$versionNo' },
                        versioned: { $first: '$versioned' },
                        versionName: { $first: '$versionName' },
                        versionedFrom: { $first: '$versionedFrom' },
                        versionedCourseIds: { $first: '$versionedCourseIds' },
                        allSchedules: {
                            $push: {
                                students: '$students',
                                status: '$status',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                _institution_calendar_id: '$_institution_calendar_id',
                                programId: '$_program_id',
                                courseId: '$_course_id',
                                term: '$term',
                                levelNo: '$level_no',
                                yearNo: '$year_no',
                                rotationCount: '$rotation_count',
                                session: '$session',
                            },
                        },
                        schedules: {
                            $push: {
                                status: '$students.status',
                                schedule_date: '$schedule_date',
                                scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                                scheduleEndDateAndTime: '$scheduleEndDateAndTime',
                                mergeStatus: '$merge_status',
                                mergeWith: '$merge_with',
                                subject_name: '$subjects.subject_name',
                                staff_name: '$staffs.staff_name',
                                staffId: '$staffs._staff_id',
                                session_type: '$session.session_type',
                                session_topic: '$session.session_topic',
                                s_no: '$session.s_no',
                                session_id: '$session._session_id',
                                delivery_no: '$session.delivery_no',
                                delivery_symbol: '$session.delivery_symbol',
                                studentId: '$students._id',
                                scheduleId: '$_id',
                                type: '$type',
                                title: '$title',
                            },
                        },
                        totalDays: {
                            $sum: {
                                $cond: [
                                    {
                                        $eq: ['$students._id', convertToMongoObjectId(_student_id)],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalLeave: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            {
                                                $or: [
                                                    { $eq: ['$students.status', 'absent'] },
                                                    { $eq: ['$students.status', 'leave'] },
                                                ],
                                            },
                                            { $eq: ['$status', 'completed'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        upcomingLeave: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            {
                                                $gte: [
                                                    '$scheduleStartDateAndTime',
                                                    new Date(startDate),
                                                ],
                                            },
                                            {
                                                $lte: [
                                                    '$scheduleEndDateAndTime',
                                                    new Date(endDate),
                                                ],
                                            },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalOnDuty: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            { $eq: ['$students.status', 'on_duty'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                        totalPermission: {
                            $sum: {
                                $cond: [
                                    {
                                        $and: [
                                            {
                                                $eq: [
                                                    '$students._id',
                                                    convertToMongoObjectId(_student_id),
                                                ],
                                            },
                                            { $eq: ['$students.status', 'permission'] },
                                        ],
                                    },
                                    1,
                                    0,
                                ],
                            },
                        },
                    },
                },
                {
                    $project: {
                        _id: '$_id',
                        course_name: '$course_name',
                        program_name: '$program_name',
                        course_code: '$course_code',
                        versionNo: '$versionNo',
                        versioned: '$versioned',
                        versionName: '$versionName',
                        versionedFrom: '$versionedFrom',
                        versionedCourseIds: '$versionedCourseIds',
                        totalDays: '$totalDays',
                        totalLeave: '$totalLeave',
                        upcomingLeave: '$upcomingLeave',
                        totalPermission: '$totalPermission',
                        totalOnDuty: '$totalOnDuty',
                        absentPercentage: {
                            $multiply: [{ $divide: ['$totalLeave', '$totalDays'] }, 100],
                        },
                        predictedAbsentPercentage: {
                            $multiply: [
                                {
                                    $divide: [
                                        { $sum: ['$totalLeave', '$upcomingLeave'] },
                                        '$totalDays',
                                    ],
                                },
                                100,
                            ],
                        },
                        allSchedules: '$allSchedules',
                        schedules: {
                            $filter: {
                                input: '$schedules',
                                as: 'item',
                                cond: {
                                    $and: [
                                        {
                                            $eq: [
                                                '$$item.studentId',
                                                convertToMongoObjectId(_student_id),
                                            ],
                                        },
                                        {
                                            $gte: [
                                                '$$item.scheduleStartDateAndTime',
                                                new Date(startDate),
                                            ],
                                        },
                                        {
                                            $lte: [
                                                '$$item.scheduleEndDateAndTime',
                                                new Date(endDate),
                                            ],
                                        },
                                    ],
                                },
                            },
                        },
                    },
                },
            ]);
        }
        if (
            !listSchedules.length &&
            !listSchedules.filter((listScheduledElement) => listScheduledElement.schedules.length)
                .length
        ) {
            return {
                status: 400,
                message: 'NO_DATA',
                data: [],
            };
        }
        changePresentScheduleBasedLateConfigureForSingleStudent({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules: listSchedules,
            lateExcludeManagement,
        });

        listSchedules = listSchedules.filter((scheduleElement) => scheduleElement.schedules.length);
        const mixedDenialStudents = await lmsDenialSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    courseId: {
                        $in: listSchedules.map((schedule) => convertToMongoObjectId(schedule._id)),
                    },
                    ...(year && { yearNo: year }),
                    ...(level && { levelNo: level }),
                    ...(term && { term }),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    studentId: 1,
                    courseId: 1,
                    absencePercentage: 1,
                    rotation: 1,
                    rotationCount: 1,
                    'categoryWisePercentage.categoryName': 1,
                    'categoryWisePercentage.categoryId': 1,
                    'categoryWisePercentage.percentage': 1,
                    denialCondition: 1,
                    typeWiseUpdate: 1,
                    term: 1,
                },
                {
                    sort: { updatedAt: -1 },
                },
            )
            .lean();
        for (const listSchedule of listSchedules) {
            const manipulatedPercentage = clone(lmsData.warningAbsenceData);
            manipulatedPercentage[0].isManipulated = false;
            const isMixedStudent =
                mixedDenialStudents &&
                mixedDenialStudents.length &&
                mixedDenialStudents.find(
                    (denialStudentElement) =>
                        (denialStudentElement.term === term &&
                            denialStudentElement.courseId.toString() ===
                                listSchedule._id.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                        (denialStudentElement.term === term &&
                            denialStudentElement.studentId &&
                            denialStudentElement.courseId.toString() ===
                                listSchedule._id.toString() &&
                            denialStudentElement.studentId.toString() === _student_id.toString()),
                );
            if (
                isMixedStudent &&
                isMixedStudent.absencePercentage &&
                lmsData.warningAbsenceData[0] &&
                lmsData.warningAbsenceData[0].percentage &&
                lmsData.warningAbsenceData[0].percentage < isMixedStudent.absencePercentage
            ) {
                manipulatedPercentage[0].percentage = isMixedStudent.absencePercentage;
                manipulatedPercentage[0].isManipulated = true;
            }
            const warningData =
                manipulatedPercentage[0].labelName === lmsData.denialLabel
                    ? manipulatedPercentage[0].isManipulated
                        ? manipulatedPercentage.find(
                              (manipulatedElement) =>
                                  manipulatedElement.percentage &&
                                  parseFloat(listSchedule.absentPercentage) >
                                      parseFloat(manipulatedElement.percentage),
                          )
                        : lmsData.warningAbsenceData.find(
                              (warningElement) =>
                                  warningElement.percentage &&
                                  parseFloat(listSchedule.absentPercentage) >
                                      parseFloat(warningElement.percentage),
                          )
                    : lmsData.warningAbsenceData.find(
                          (warningElement) =>
                              warningElement.percentage &&
                              parseFloat(listSchedule.absentPercentage) >
                                  parseFloat(warningElement.percentage),
                      );

            listSchedule.warning = warningData ? warningData.labelName : '';
            listSchedule.colorCode = warningData ? warningData.colorCode : '';
            listSchedule.isDenial = false;
            if (manipulatedPercentage[0].labelName === lmsData.denialLabel) {
                listSchedule.isDenial = manipulatedPercentage[0].isManipulated
                    ? manipulatedPercentage[0].percentage <
                      parseFloat(listSchedule.absentPercentage)
                    : lmsData.warningAbsenceData[0].percentage <
                      parseFloat(listSchedule.absentPercentage);
            }
            for (const [index, schedule] of listSchedule.schedules.entries()) {
                schedule.mergedSchedule = [];
                if (schedule.mergeStatus) {
                    schedule.mergedSchedule.push({
                        s_no: schedule.s_no,
                        delivery_symbol: schedule.delivery_symbol,
                        delivery_no: schedule.delivery_no,
                        session_topic: schedule.session_topic,
                        session_type: schedule.session_type,
                    });
                    const scheduleIds = schedule.mergeWith.map((elem) =>
                        elem.schedule_id.toString(),
                    );
                    const listSchedules = listSchedule.schedules.length
                        ? listSchedule.schedules.filter((el, filterIndex) =>
                              scheduleIds.includes(el.scheduleId.toString()),
                          )
                        : [];
                    const removeMergedSchedules = listSchedule.schedules
                        .reduce(function (acc, curr, index) {
                            if (scheduleIds.includes(curr.scheduleId.toString())) {
                                acc.push(index);
                            }
                            return acc;
                        }, [])
                        .reverse();
                    for (const remove of removeMergedSchedules) {
                        listSchedule.schedules.splice(remove, 1);
                    }
                    const merge = listSchedules.map((list) => {
                        return {
                            s_no: list.s_no,
                            delivery_symbol: list.delivery_symbol,
                            delivery_no: list.delivery_no,
                            session_topic: list.session_topic,
                            session_type: list.session_type,
                        };
                    });
                    schedule.mergedSchedule = [...schedule.mergedSchedule, ...merge];
                }
            }
        }
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: { listSchedules, existingLeaveAppliedScheduleIds },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAgree = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { classificationType, studentId, agree } = body;
        const lmsStudentCheck = await lmsStudentSchema.findOne({
            _institution_id: convertToMongoObjectId(_institution_id),
            studentId: convertToMongoObjectId(studentId),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            classificationType,
        });
        let lmsStudentData;
        if (lmsStudentCheck) {
            lmsStudentData = await lmsStudentSchema
                .updateOne(
                    {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        studentId: convertToMongoObjectId(studentId),
                        classificationType,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            agree,
                        },
                    },
                )
                .lean();
        } else {
            lmsStudentData = await lmsStudentSchema.create({
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                studentId: convertToMongoObjectId(studentId),
                classificationType,
                agree,
            });
        }
        if (!lmsStudentData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listApprovalLevelWiseStatus = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { classificationType, id, studentId, _institution_calendar_id } = query;
        const lmsSettings = await lmsSettingSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType: LEAVE,
            },
            {
                warningConfig: 1,
            },
        );
        const studentWarningData = await lmsStudentSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(id),
                },
                {
                    _institution_calendar_id: 1,
                    approvalFrom: 1,
                    categoryName: 1,
                    createdAt: 1,
                    studentId: 1,
                    dateAndTimeRange: 1,
                    levelApprover: 1,
                    approvalStatus: 1,
                    programId: 1,
                    year: 1,
                    term: 1,
                    level: 1,
                    roleUsers: 1,
                    roleIds: 1,
                },
            )
            .populate({
                path: 'approvalFrom.userId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'approvalFrom.roleId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.userIds',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.roleIds',
                select: { _id: 1, name: 1 },
            })
            .lean();

        const dateNow = moment(new Date());
        const studentCourseList = (
            await getUserCourseLists({
                userId: studentId,
                type: DC_STUDENT,
                institutionCalendarId: studentWarningData._institution_calendar_id,
            })
        ).filter(
            (studentCourseElement) =>
                new Date(studentCourseElement.start_date) <= dateNow &&
                new Date(studentCourseElement.end_date) >= dateNow,
        );
        const courseCoordinatorRole = await roleSchema.findOne(
            {
                _id: { $in: studentWarningData.roleIds },
                name: COURSE_COORDINATOR,
            },
            { _id: 1 },
        );
        const courseCoordinators = [];
        if (courseCoordinatorRole) {
            const studentCourseCoordinatorsSet = new Set(
                studentCourseList.map((studentCourseListElement) =>
                    JSON.stringify({
                        _id: studentCourseListElement._id,
                        _program_id: studentCourseListElement._program_id,
                        'coordinators._institution_calendar_id':
                            studentCourseListElement._institution_calendar_id,
                        'coordinators.year': studentCourseListElement.year,
                        'coordinators.level_no': studentCourseListElement.level,
                        'coordinators.term': studentCourseListElement.term,
                    }),
                ),
            );
            const studentCourseCoordinators = Array.from(studentCourseCoordinatorsSet).map(
                (studentCourseListElement) => JSON.parse(studentCourseListElement),
            );
            const digiCourse = await courseSchema
                .find(
                    {
                        isDeleted: false,
                        isActive: true,
                        $or: studentCourseCoordinators,
                    },
                    {
                        _program_id: 1,
                        coordinators: 1,
                    },
                )
                .populate({ path: 'coordinators._user_id', select: { name: 1 } })
                .lean();
            if (digiCourse.length) {
                digiCourse.forEach((courseElement) => {
                    courseElement.coordinators.forEach((coordinatorElement) => {
                        if (
                            studentCourseList.find(
                                (studentCourseElement) =>
                                    String(studentCourseElement._program_id) ===
                                        String(courseElement._program_id) &&
                                    String(coordinatorElement._institution_calendar_id) ===
                                        String(studentCourseElement._institution_calendar_id) &&
                                    coordinatorElement.term === studentCourseElement.term &&
                                    coordinatorElement.year === studentCourseElement.year &&
                                    coordinatorElement.level_no === studentCourseElement.level,
                            )
                        ) {
                            courseCoordinators.push(coordinatorElement._user_id);
                        }
                    });
                });
            }
        }
        const roleAssignDocs = await roleAssignSchema
            .find(
                {
                    $or: [
                        {
                            'roles.program._program_id': studentWarningData.programId,
                        },
                        {
                            'roles.role_name': SCHEDULE_STAFF_DEFAULT_ROLE,
                            _user_id: { $in: studentWarningData.roleUsers },
                        },
                    ],
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _user_id: 1,
                    'roles.role_name': 1,
                    'roles._role_id': 1,
                    'roles.program._program_id': 1,
                },
            )
            .populate({ path: '_user_id', select: { name: 1 } })
            .lean();
        const start = new Date(studentWarningData.dateAndTimeRange.startDate);
        const end = new Date(studentWarningData.dateAndTimeRange.endDate);
        let warningAbsenceData = lmsSettings.warningConfig;
        warningAbsenceData = clone(
            warningAbsenceData.sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            }),
        );
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            courseId: studentCourseList.map((courseElement) => courseElement._id),
            programId: studentWarningData.programId,
            yearNo: studentWarningData.year,
            levelNo: studentWarningData.level,
            term: studentWarningData.term,
        });
        // const absencePercentage = warningAbsenceData[0].percentage;
        let denialCoursesCheck = await CourseSchedule.aggregate([
            {
                $match: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    'students._id': convertToMongoObjectId(studentWarningData.studentId),
                    isActive: true,
                    type: 'regular',
                    isDeleted: false,
                },
            },
            { $unwind: '$students' },
            {
                $match: {
                    'students._id': convertToMongoObjectId(studentWarningData.studentId),
                },
            },
            {
                $group: {
                    count: { $sum: 1 },
                    _id: '$_course_id',
                    course_name: { $first: '$course_name' },
                    course_code: { $first: '$course_code' },
                    rotation: { $first: '$rotation' },
                    rotationCount: { $first: '$rotation_count' },
                    allSchedules: {
                        $push: {
                            students: '$students',
                            status: '$status',
                            scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                            _institution_calendar_id: '$_institution_calendar_id',
                            programId: '$_program_id',
                            courseId: '$_course_id',
                            term: '$term',
                            levelNo: '$level_no',
                            rotationCount: '$rotation_count',
                            session: '$session',
                        },
                    },
                    schedules: {
                        $push: {
                            scheduleId: '$_id',
                            scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                            scheduleEndDateAndTime: '$scheduleEndDateAndTime',
                        },
                    },
                    totalDays: {
                        $sum: 1,
                    },
                    unAppliedLeaves: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: ['$students.status', 'absent'],
                                        },
                                        {
                                            $eq: ['$status', 'completed'],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    appliedLeaves: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: ['$students.status', 'leave'],
                                        },
                                        {
                                            $eq: ['$status', 'completed'],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalLeave: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $or: [
                                                { $eq: ['$students.status', 'absent'] },
                                                { $eq: ['$students.status', 'leave'] },
                                            ],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    upcomingLeave: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $gte: ['$scheduleStartDateAndTime', start],
                                        },
                                        {
                                            $lte: ['$scheduleEndDateAndTime', end],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 1,
                    course_name: '$course_name',
                    course_code: '$course_code',
                    term: '$term',
                    rotationCount: '$rotationCount',
                    unAppliedLeaves: '$unAppliedLeaves',
                    appliedLeaves: '$appliedLeaves',
                    rotation: '$rotation',
                    totalSchedules: '$totalDays',
                    allSchedules: '$allSchedules',
                    schedules: {
                        $filter: {
                            input: '$schedules',
                            as: 'item',
                            cond: {
                                $and: [
                                    {
                                        $gte: ['$$item.scheduleStartDateAndTime', start],
                                    },
                                    {
                                        $lte: ['$$item.scheduleEndDateAndTime', end],
                                    },
                                ],
                            },
                        },
                    },
                    predictedAbsentPercentage: {
                        $multiply: [
                            {
                                $divide: [
                                    { $sum: ['$totalLeave', '$upcomingLeave'] },
                                    '$totalDays',
                                ],
                            },
                            100,
                        ],
                    },
                },
            },
        ]);
        changePresentScheduleBasedLateConfigureForSingleStudent({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules: denialCoursesCheck,
            lateExcludeManagement,
        });
        const mixedDenialStudents = await lmsDenialSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    courseId: denialCoursesCheck.map((schedule) => schedule._id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    studentId: 1,
                    courseId: 1,
                    absencePercentage: 1,
                    denialCondition: 1,
                    rotation: 1,
                    rotationCount: 1,
                    'categoryWisePercentage.categoryName': 1,
                    'categoryWisePercentage.categoryId': 1,
                    'categoryWisePercentage.percentage': 1,
                    typeWiseUpdate: 1,
                },
            )
            .sort({ updatedAt: -1 })
            .lean();
        const lmsStudent = await lmsStudentSchema
            .find(
                {
                    studentId: convertToMongoObjectId(studentWarningData.studentId),
                    approvalStatus: APPROVED,
                },
                {
                    _institution_calendar_id: 1,
                    approvalFrom: 1,
                    categoryName: 1,
                    noOfHours: 1,
                    createdAt: 1,
                    studentId: 1,
                    dateAndTimeRange: 1,
                    levelApprover: 1,
                    approvalStatus: 1,
                    programId: 1,
                    year: 1,
                    term: 1,
                    level: 1,
                },
            )
            .populate({
                path: 'approvalFrom.userId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'approvalFrom.roleId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.userIds',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.roleIds',
                select: { _id: 1, name: 1 },
            })
            .lean();
        let denial;
        if (warningAbsenceData.length && warningAbsenceData[0].isActive) {
            denialCoursesCheck = denialCoursesCheck.filter((course) => {
                if (course.schedules.length) {
                    if (mixedDenialStudents.length) {
                        const isMixedStudent = mixedDenialStudents.find(
                            (denialStudentElement) =>
                                ((course.rotation === 'yes'
                                    ? parseInt(course.rotationCount) ===
                                      denialStudentElement.rotationCount
                                    : true) &&
                                    denialStudentElement.courseId.toString() ===
                                        course._id.toString() &&
                                    denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                                (denialStudentElement.studentId &&
                                    studentWarningData.studentId.toString() ===
                                        denialStudentElement.studentId.toString() &&
                                    denialStudentElement.courseId.toString() ===
                                        course._id.toString()),
                        );
                        if (isMixedStudent && isMixedStudent.denialCondition === CUMULATIVE) {
                            return (
                                Number(course.predictedAbsentPercentage) >=
                                isMixedStudent.absencePercentage
                            );
                        }
                        if (isMixedStudent && isMixedStudent.denialCondition === INDIVIDUAL) {
                            const denialStudentsWithLeave = lmsStudent.reduce((acc, curr) => {
                                if (acc[curr.studentId]) {
                                    if (acc[curr.studentId][curr.categoryName]) {
                                        acc[curr.studentId][curr.categoryName] += curr.noOfHours;
                                    } else {
                                        acc[curr.studentId][curr.categoryName] = curr.noOfHours;
                                    }
                                } else {
                                    acc[curr.studentId] = { [curr.categoryName]: curr.noOfHours };
                                }
                                return acc;
                            }, {});
                            if (course.unAppliedLeaves > 0) {
                                if (warningAbsenceData[0].unappliedLeaveConsideredAs !== '') {
                                    if (
                                        !denialStudentsWithLeave[
                                            studentWarningData.studentId.toString()
                                        ]
                                    ) {
                                        denialStudentsWithLeave[
                                            studentWarningData.studentId.toString()
                                        ] = {};
                                    }
                                    if (
                                        denialStudentsWithLeave[
                                            studentWarningData.studentId.toString()
                                        ][warningAbsenceData[0].unappliedLeaveConsideredAs]
                                    ) {
                                        denialStudentsWithLeave[
                                            studentWarningData.studentId.toString()
                                        ][warningAbsenceData[0].unappliedLeaveConsideredAs] +=
                                            course.unAppliedLeaves;
                                    } else {
                                        denialStudentsWithLeave[
                                            studentWarningData.studentId.toString()
                                        ][warningAbsenceData[0].unappliedLeaveConsideredAs] =
                                            course.unAppliedLeaves;
                                    }
                                }
                            }
                            if (
                                denialStudentsWithLeave[studentWarningData.studentId.toString()] &&
                                warningAbsenceData[0].denialCondition.toLowerCase() === INDIVIDUAL
                            ) {
                                const absentDetails = Object.keys(
                                    denialStudentsWithLeave[
                                        studentWarningData.studentId.toString()
                                    ],
                                ).reduce((acc, curr) => {
                                    const absentDetailObject = {
                                        categoryName: curr,
                                        percentage: (
                                            (denialStudentsWithLeave[
                                                studentWarningData.studentId.toString()
                                            ][curr] /
                                                course.totalSchedules) *
                                            100
                                        ).toPrecision(2),
                                    };
                                    acc.push(absentDetailObject);
                                    return acc;
                                }, []);
                                course.absentDetails = absentDetails;
                            }
                            let isDenialCheck = false;
                            if (course.absentDetails && course.absentDetails.length) {
                                for (let i = 0; i < course.absentDetails.length; i++) {
                                    const categoryName =
                                        course.absentDetails[i].categoryName.toLowerCase();
                                    const percentageDetail = parseFloat(
                                        course.absentDetails[i].percentage,
                                    );
                                    for (
                                        let j = 0;
                                        j < isMixedStudent.categoryWisePercentage.length;
                                        j++
                                    ) {
                                        const categoryDetail =
                                            isMixedStudent.categoryWisePercentage[j];
                                        const categoryNameCategory =
                                            categoryDetail.categoryName.toLowerCase();
                                        const percentageCategory = categoryDetail.percentage;
                                        if (
                                            categoryName === categoryNameCategory &&
                                            percentageDetail > percentageCategory
                                        ) {
                                            isDenialCheck = true;
                                        }
                                    }
                                }
                            }
                            return isDenialCheck;
                        }
                        return (
                            Number(course.predictedAbsentPercentage) >
                            warningAbsenceData[0].percentage
                        );
                    }
                    return (
                        Number(course.predictedAbsentPercentage) > warningAbsenceData[0].percentage
                    );
                }
            });
            denial = {
                denialCourses: denialCoursesCheck.map(({ predictedAbsentPercentage, ...rest }) => ({
                    ...rest,
                })),
                denialCount: denialCoursesCheck.length,
            };
        }
        const levelApprover = await logicFlowsForStudentApprovals({
            studentWarningData,
            roleAssignDocs,
            _user_id,
            denial,
            courseCoordinators,
        });
        return { statusCode: 200, message: 'DATA_UPDATED', data: levelApprover };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const studentListForStaffApprover = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            classificationType,
            roleId,
            userId,
            programId,
            year,
            level,
            searchKey,
            status,
            type,
            page,
            limit,
        } = body;
        let currentPage = 0;
        let currentLimit = 0;
        let historyCount = 0;
        if (type === HISTORY) {
            currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
            currentLimit = page && limit ? Number(limit) : 10;
            const lmsStudentQuery = {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                classificationType,
                approvalStatus: status
                    ? { $in: [status] }
                    : { $in: [APPROVED, CANCELLED, REJECT, WITHDRAWN] },
                roleUsers: convertToMongoObjectId(userId),
            };
            if (roleId && roleId.length) {
                lmsStudentQuery.$or = [
                    {
                        roleIds: {
                            $in: roleId.map((roleIdsElement) =>
                                convertToMongoObjectId(roleIdsElement),
                            ),
                        },
                    },
                    {
                        userIds: convertToMongoObjectId(userId),
                    },
                ];
            } else lmsStudentQuery.userIds = convertToMongoObjectId(userId);
            historyCount = await lmsStudentSchema.find(lmsStudentQuery).countDocuments().lean();
        }
        let studentName;
        if (searchKey) {
            studentName = await userSchema.find(
                {
                    $or: [
                        {
                            'name.first': { $regex: searchKey, $options: 'i' },
                        },
                        {
                            'name.last': { $regex: searchKey, $options: 'i' },
                        },
                        {
                            'name.middle': { $regex: searchKey, $options: 'i' },
                        },
                    ],
                },
                { _id: 1 },
            );
        }
        let findQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            classificationType,
            approvalStatus:
                type === HISTORY && status
                    ? { $in: [status] }
                    : type === HISTORY
                    ? { $in: [APPROVED, CANCELLED, REJECT, WITHDRAWN] }
                    : { $nin: [APPROVED, CANCELLED, REJECT, WITHDRAWN] },
            roleUsers: convertToMongoObjectId(userId),
        };
        if (type === HISTORY) {
            findQuery._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
        }
        if (searchKey || roleId || programId || year || level) {
            findQuery = {
                ...findQuery,
                $and: [
                    ...(searchKey
                        ? [
                              {
                                  $or: [
                                      {
                                          academicId: { $regex: searchKey, $options: 'i' },
                                      },
                                      {
                                          studentId: {
                                              $in: studentName.map((studentElement) =>
                                                  convertToMongoObjectId(studentElement._id),
                                              ),
                                          },
                                      },
                                  ],
                              },
                          ]
                        : []),
                    ...(roleId && roleId.length
                        ? [
                              {
                                  $or: [
                                      {
                                          roleIds: {
                                              $in: roleId.map((roleIdsElement) =>
                                                  convertToMongoObjectId(roleIdsElement),
                                              ),
                                          },
                                      },
                                      {
                                          userIds: convertToMongoObjectId(userId),
                                      },
                                  ],
                              },
                          ]
                        : [{ userIds: convertToMongoObjectId(userId) }]),
                    ...(programId ? [{ programId: convertToMongoObjectId(programId) }] : []),
                    ...(year ? [{ year }] : []),
                    ...(level ? [{ level }] : []),
                ],
            };
        }
        const lmsStudentData = await lmsStudentSchema
            .find(findQuery, {
                _institution_calendar_id: 1,
                roleIds: 1,
                userIds: 1,
                classificationType: 1,
                settingId: 1,
                programId: 1,
                level: 1,
                year: 1,
                term: 1,
                levelApprover: 1,
                categoryName: 1,
                typeName: 1,
                dateAndTimeRange: 1,
                applyingForDays: 1,
                noOfHours: 1,
                reason: 1,
                approvalStatus: 1,
                attachments: 1,
                approvalFrom: 1,
                academicId: 1,
                createdAt: 1,
                updatedAt: 1,
            })
            .sort({ updatedAt: -1 })
            .populate({
                path: 'studentId',
                select: { name: 1, user_id: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: 'approvalFrom.roleId',
                select: { name: 1 },
            })
            .populate({
                path: 'approvalFrom.userId',
                select: { name: 1 },
            })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .skip(currentPage)
            .limit(currentLimit)
            .lean();
        const digiCourse = await courseSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _program_id: {
                        $in: lmsStudentData.map((studentData) =>
                            convertToMongoObjectId(studentData.programId._id),
                        ),
                    },
                    'coordinators._institution_calendar_id': {
                        $in: lmsStudentData.map((studentData) =>
                            convertToMongoObjectId(studentData?._institution_calendar_id?._id),
                        ),
                    },
                    'coordinators.year': {
                        $in: lmsStudentData.map((studentData) => studentData.year),
                    },
                    'coordinators.level_no': {
                        $in: lmsStudentData.map((studentData) => studentData.level),
                    },
                    'coordinators.term': {
                        $in: lmsStudentData.map((studentData) => studentData.term),
                    },
                    'coordinators._user_id': convertToMongoObjectId(userId),
                },
                {
                    _program_id: 1,
                    'coordinators.$': 1,
                },
            )
            .populate({ path: 'coordinators._user_id', select: { name: 1 } })
            .lean();
        const lmsSettingData = await lmsSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType,
                },
                {
                    levelApprover: 1,
                },
            )
            .lean();
        const lmsStudentsProgramIds = lmsStudentData.map((lmsElement) => {
            if (lmsElement.programId) {
                return convertToMongoObjectId(lmsElement.programId._id);
            }
        });

        const roleDetails = await roleSchema
            .find(
                { name: { $in: [COURSE_COORDINATOR, SCHEDULE_STAFF_DEFAULT_ROLE] } },
                { _id: 1, name: 1 },
            )
            .lean();
        const courseCoordinators = roleDetails.find(
            (roleDetailElement) => roleDetailElement.name === COURSE_COORDINATOR,
        );
        const scheduleStaffRole = roleDetails.find(
            (roleDetailElement) => roleDetailElement.name === SCHEDULE_STAFF_DEFAULT_ROLE,
        );
        const courseCoordinatorsId = courseCoordinators._id;
        const roleAssignDocs = await roleAssignSchema
            .find(
                {
                    _user_id: convertToMongoObjectId(userId),
                    'roles._role_id': roleId,
                    // 'roles.program._program_id': { $in: lmsStudentsProgramIds },
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _user_id: 1,
                    'roles._role_id': 1,
                    'roles.program._program_id': 1,
                },
            )
            .populate({ path: '_user_id', select: { name: 1 } })
            .lean();

        let staffApproval = await staffApprovalStatusLogics({
            lmsStudentData,
            roleAssignDocs,
            roleId,
            userId,
            courseCoordinatorsId,
            digiCourse,
            scheduleStaffRole,
        });

        if (staffApproval.bulkWrites.length) {
            await lmsStudentSchema.bulkWrite(staffApproval.bulkWrites);
            const lmsStudentData = await lmsStudentSchema
                .find(findQuery, {
                    roleIds: 1,
                    userIds: 1,
                    classificationType: 1,
                    settingId: 1,
                    programId: 1,
                    level: 1,
                    year: 1,
                    levelApprover: 1,
                    categoryName: 1,
                    typeName: 1,
                    dateAndTimeRange: 1,
                    applyingForDays: 1,
                    noOfHours: 1,
                    reason: 1,
                    approvalStatus: 1,
                    attachments: 1,
                    approvalFrom: 1,
                    academicId: 1,
                    createdAt: 1,
                    updatedAt: 1,
                })
                .sort({ updatedAt: -1 })
                .populate({
                    path: 'studentId',
                    select: { name: 1, user_id: 1 },
                })
                .populate({
                    path: 'programId',
                    select: { name: 1 },
                })
                .populate({
                    path: 'approvalFrom.roleId',
                    select: { name: 1 },
                })
                .populate({
                    path: 'approvalFrom.userId',
                    select: { name: 1 },
                })
                .skip(currentPage)
                .limit(currentLimit)
                .lean();
            staffApproval = await staffApprovalStatusLogics({
                lmsStudentData,
                lmsSettingData,
                roleId,
                userId,
                digiCourse,
            });
        }
        //  filter the current staff login role leaves
        staffApproval.lmsStudentData = staffApproval.lmsStudentData.filter((lmsStudentElement) => {
            return lmsStudentElement.isRole === true;
        });
        const pendingCount = staffApproval.lmsStudentData.filter(
            (staffApprovalElement) => !staffApprovalElement.finishedStatus,
        ).length;
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                lmsStudentData: staffApproval.lmsStudentData,
                pendingCount,
                ...(historyCount && { historyCount }),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const staffApproval = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { id, approvalFrom } = body;
        approvalFrom.date = new Date();
        const lmsStudentDataBaseId = id.map((element) => convertToMongoObjectId(element));
        let lmsStudentData;
        const bulkWrites = [];
        for (const singleId of id) {
            bulkWrites.push({
                updateMany: {
                    filter: { _id: convertToMongoObjectId(singleId) },
                    update: { $push: { approvalFrom } },
                },
            });
        }
        if (bulkWrites.length) {
            lmsStudentData = await lmsStudentSchema.bulkWrite(bulkWrites);
        }
        if (!lmsStudentData) return { statusCode: 410, message: 'UNABLE_TO_ADD' };

        const studentWarningDataFromDb = await lmsStudentSchema
            .find({
                _id: { $in: lmsStudentDataBaseId },
            })
            .populate({
                path: 'approvalFrom.userId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'approvalFrom.roleId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.userIds',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.roleIds',
                select: { _id: 1, name: 1 },
            })
            .lean();
        const lmsStudentsProgramIds = studentWarningDataFromDb.map((lmsElement) => {
            if (lmsElement.programId) {
                return convertToMongoObjectId(lmsElement.programId);
            }
        });

        const roleAssignDocs = await roleAssignSchema
            .find(
                {
                    $or: [
                        {
                            'roles.program._program_id': {
                                $in: lmsStudentsProgramIds,
                            },
                        },
                        {
                            'roles.role_name': SCHEDULE_STAFF_DEFAULT_ROLE,
                            _user_id: {
                                $in: studentWarningDataFromDb.reduce((acc, curr) => {
                                    acc.push(...(curr.roleUsers ?? []));
                                    return acc;
                                }, []),
                            },
                        },
                    ],
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _user_id: 1,
                    'roles.role_name': 1,
                    'roles._role_id': 1,
                    'roles.program._program_id': 1,
                },
            )
            .populate({ path: '_user_id', select: { name: 1 } })
            .lean();
        const digiCourse = await courseSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _program_id: convertToMongoObjectId(studentWarningDataFromDb[0].programId),
                    'coordinators._institution_calendar_id': convertToMongoObjectId(
                        studentWarningDataFromDb[0]._institution_calendar_id,
                    ),
                    'coordinators.year': studentWarningDataFromDb[0].year,
                    'coordinators.level_no': studentWarningDataFromDb[0].level,
                    'coordinators.term': studentWarningDataFromDb[0].term,
                },
                {
                    _program_id: 1,
                    'coordinators.$': 1,
                },
            )
            .populate({ path: 'coordinators._user_id', select: { name: 1 } })
            .lean();
        const courseCoordinators = digiCourse.map((courseElement) => {
            return courseElement.coordinators[0]._user_id;
        });
        for (const singleId of id) {
            let studentWarningData;
            for (const studentWarningElement of studentWarningDataFromDb) {
                if (studentWarningElement._id.toString() === singleId.toString()) {
                    studentWarningData = studentWarningElement;
                    const { approvalStatus } = await logicFlowsForStudentApprovals({
                        studentWarningData,
                        roleAssignDocs,
                        _user_id,
                        courseCoordinators,
                    });
                    if (approvalStatus === APPROVED) {
                        const courseScheduleQuery = {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            isDeleted: false,
                            'students._id': convertToMongoObjectId(studentWarningElement.studentId),
                            scheduleStartDateAndTime:
                                studentWarningElement.classificationType === LEAVE &&
                                studentWarningElement.dateAndTimeRange.startSession
                                    ? {
                                          $gte: new Date(
                                              studentWarningElement.dateAndTimeRange.startSession.sessionStart,
                                          ),
                                      }
                                    : {
                                          $gte: new Date(
                                              studentWarningElement.dateAndTimeRange.startDate,
                                          ),
                                      },
                            scheduleEndDateAndTime:
                                studentWarningElement.classificationType === LEAVE &&
                                studentWarningElement.dateAndTimeRange.endSession
                                    ? {
                                          $lte: new Date(
                                              studentWarningElement.dateAndTimeRange.endSession.sessionEnd,
                                          ),
                                      }
                                    : {
                                          $lte: new Date(
                                              studentWarningElement.dateAndTimeRange.endDate,
                                          ),
                                      },
                        };
                        await CourseSchedule.updateMany(
                            courseScheduleQuery,
                            {
                                $set: {
                                    'students.$[i].status':
                                        studentWarningElement.classificationType,
                                },
                            },
                            {
                                arrayFilters: [
                                    {
                                        'i._id': convertToMongoObjectId(
                                            studentWarningElement.studentId,
                                        ),
                                    },
                                ],
                            },
                        );
                    }
                }
            }
        }
        return { statusCode: 200, message: 'DATA_ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const revokeApproval = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id, roleId, userId } = body;
        let lmsStudentData = await lmsStudentSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(id),
                },
                {
                    classificationType: 1,
                    approvalFrom: 1,
                    programId: 1,
                    studentId: 1,
                    levelApprover: 1,
                    approvalStatus: 1,
                },
            )
            .populate({
                path: 'approvalFrom.userId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'approvalFrom.roleId',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.userIds',
                select: { _id: 1, name: 1 },
            })
            .populate({
                path: 'levelApprover.level.roleIds',
                select: { _id: 1, name: 1 },
            })
            .lean();
        let category;
        let approvalFromCheckForRole;
        const existingApprovalStatus = lmsStudentData.approvalStatus;
        const programId = lmsStudentData.programId.toString();
        const approvalFromCheckForUser =
            lmsStudentData.approvalFrom &&
            lmsStudentData.approvalFrom.length &&
            lmsStudentData.approvalFrom.find((userElement) => {
                return userElement.userId._id.toString() === userId.toString();
            });
        if (approvalFromCheckForUser && approvalFromCheckForUser.roleId) {
            approvalFromCheckForRole =
                lmsStudentData.approvalFrom &&
                lmsStudentData.approvalFrom.find(
                    (roleElement) =>
                        roleId &&
                        roleId.includes(roleElement.roleId && roleElement.roleId._id.toString()),
                );
            category = ROLE_BASED;
        } else {
            category = USER_BASED;
        }
        const approved = lmsStudentData.levelApprover;
        let notAllowed = false;
        if (approved) {
            let findIndex = false;
            let indexValue = 0;
            let levelFindData;
            for (const [index, levelData] of approved.level.entries()) {
                if (category === ROLE_BASED) {
                    levelFindData = levelData.roleIds.find((roleElement) =>
                        roleId.includes(roleElement._id.toString()),
                    );
                } else if (category === USER_BASED) {
                    levelFindData = levelData.userIds.find(
                        (roleElement) => userId.toString() === roleElement._id.toString(),
                    );
                }
                if (findIndex === true) {
                    let levelFindDataForRestrict;
                    if (levelData.categoryBased === ROLE_BASED) {
                        levelFindDataForRestrict = levelData.roleIds.some((roleElement) =>
                            lmsStudentData.approvalFrom
                                .filter((approvalElement) => approvalElement.roleId)
                                .map((ele) => ele.roleId._id.toString())
                                .includes(roleElement._id.toString()),
                        );
                    } else if (levelData.categoryBased === USER_BASED) {
                        levelFindDataForRestrict = levelData.userIds.some((userElement) =>
                            lmsStudentData.approvalFrom
                                .filter((approvalElement) => !approvalElement.roleId)
                                .map((ele) => ele.userId.toString())
                                .includes(userElement._id.toString()),
                        );
                    }
                    if (levelFindDataForRestrict) {
                        notAllowed = true;
                    }
                }
                if (levelFindData) {
                    findIndex = true;
                    indexValue = index;
                }
            }
        }
        if (notAllowed) {
            return { statusCode: 200, message: 'Not allowed to revoke the approvals' };
        }
        if (category === USER_BASED) {
            lmsStudentData = await lmsStudentSchema
                .findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(id),
                    },
                    {
                        $pull: {
                            approvalFrom: {
                                _id: convertToMongoObjectId(approvalFromCheckForUser._id),
                            },
                        },
                    },
                    { new: true },
                )
                .lean();
        } else if (category === ROLE_BASED) {
            lmsStudentData = await lmsStudentSchema
                .findByIdAndUpdate(
                    {
                        _id: convertToMongoObjectId(id),
                    },
                    {
                        $pull: {
                            approvalFrom: {
                                _id: convertToMongoObjectId(approvalFromCheckForRole._id),
                            },
                        },
                    },
                    { new: true },
                )
                .lean();
        }
        if (!lmsStudentData) return { statusCode: 410, message: 'UNABLE_TO_DELETE' };
        const digiCourse = await courseSchema
            .find(
                {
                    isDeleted: false,
                    isActive: true,
                    _program_id: convertToMongoObjectId(lmsStudentData.programId),
                    'coordinators._institution_calendar_id': convertToMongoObjectId(
                        lmsStudentData._institution_calendar_id,
                    ),
                    'coordinators.year': lmsStudentData.year,
                    'coordinators.level_no': lmsStudentData.level,
                    'coordinators.term': lmsStudentData.term,
                },
                {
                    _program_id: 1,
                    'coordinators.$': 1,
                },
            )
            .populate({ path: 'coordinators._user_id', select: { name: 1 } })
            .lean();
        const courseCoordinators = digiCourse.map((courseElement) => {
            return courseElement.coordinators[0]._user_id;
        });
        const roleAssignDocs = await roleAssignSchema
            .find(
                {
                    'roles.program._program_id': convertToMongoObjectId(lmsStudentData.programId),
                },
                {
                    _user_id: 1,
                    'roles._role_id': 1,
                    'roles.program._program_id': 1,
                },
            )
            .populate({ path: '_user_id', select: { name: 1 } })
            .lean();
        await logicFlowsForStudentApprovals({
            studentWarningData: lmsStudentData,
            roleAssignDocs,
            _user_id: userId,
            courseCoordinators,
        });
        if (existingApprovalStatus === APPROVED) {
            const bulkOperations = [];
            const courseScheduleData = await CourseSchedule.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    'students._id': convertToMongoObjectId(lmsStudentData.studentId),
                    scheduleStartDateAndTime:
                        lmsStudentData.classificationType === LEAVE &&
                        lmsStudentData.dateAndTimeRange.startSession
                            ? {
                                  $gte: new Date(
                                      lmsStudentData.dateAndTimeRange.startSession.sessionStart,
                                  ),
                              }
                            : {
                                  $gte: new Date(lmsStudentData.dateAndTimeRange.startDate),
                              },
                    scheduleEndDateAndTime:
                        lmsStudentData.classificationType === LEAVE &&
                        lmsStudentData.dateAndTimeRange.endSession
                            ? {
                                  $lte: new Date(
                                      lmsStudentData.dateAndTimeRange.endSession.sessionEnd,
                                  ),
                              }
                            : {
                                  $lte: new Date(lmsStudentData.dateAndTimeRange.endDate),
                              },
                },
                {
                    status: 1,
                },
            ).lean();
            for (scheduleElement of courseScheduleData) {
                const update = {
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(scheduleElement._id),
                            'students._id': convertToMongoObjectId(lmsStudentData.studentId),
                        },
                        update: {
                            $set: {
                                'students.$[i].status':
                                    scheduleElement.status === COMPLETED
                                        ? ABSENT
                                        : scheduleElement.status === ONGOING
                                        ? ABSENT
                                        : PENDING,
                            },
                        },
                        arrayFilters: [
                            {
                                'i._id': convertToMongoObjectId(lmsStudentData.studentId),
                            },
                        ],
                    },
                };
                bulkOperations.push(update);
            }
            if (bulkOperations.length) {
                await CourseSchedule.bulkWrite(bulkOperations);
            }
        }
        return { statusCode: 200, message: 'DATA_DELETED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editStaffApproval = async ({ body = {} }) => {
    try {
        const { id, approvalFormId, levelId, roleId, userId, categoryBased, status, reason } = body;
        const lmsStudentData = await lmsStudentSchema.updateOne(
            {
                _id: convertToMongoObjectId(id),
            },
            {
                ...(levelId && { 'approvalFrom.$[approvalId].levelId': levelId }),
                ...(roleId && { 'approvalFrom.$[approvalId].roleId': roleId }),
                ...(userId && { 'approvalFrom.$[approvalId].userId': userId }),
                ...(categoryBased && { 'approvalFrom.$[approvalId].categoryBased': categoryBased }),
                ...(status && { 'approvalFrom.$[approvalId].status': status }),
                ...(reason && { 'approvalFrom.$[approvalId].reason': reason }),
            },
            {
                arrayFilters: [
                    {
                        'approvalId._id': convertToMongoObjectId(approvalFormId),
                    },
                ],
            },
        );
        if (!lmsStudentData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_TYPE' };
        return { statusCode: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateTurnAroundTime = async ({ body = {} }) => {
    try {
        const { id, turnAroundTime, updateTurnAroundTime } = body;
        const lmsStudentData = await lmsStudentSchema.findById({
            _id: convertToMongoObjectId(id),
        });
        if (updateTurnAroundTime) {
            lmsStudentData.updatedAt = new Date(updateTurnAroundTime);
        }
        if (turnAroundTime) {
            lmsStudentData.createdAt = new Date(turnAroundTime);
        }
        lmsStudentData.save();
        if (!lmsStudentData) return { statusCode: 410, message: 'UNABLE_TO_UPDATE_TYPE' };
        return { status: 200, message: 'DATA_UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// lms report
const getLeaveManagement = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id } = headers;
        const { programId, from, to, classificationType, state, _institution_calendar_id } = query;
        logger.info('LMS report ->  lmsStudentController - getLeaveManagement -> Start');
        let leaveReport = await getLmsReportFromCache({
            state,
            user_id,
            _institution_calendar_id,
            _institution_id,
            from,
            to,
        });
        if (!leaveReport.length) {
            return {
                status: 400,
                message: 'NO DATA',
            };
        }
        //filter according to the global search
        leaveReport = leaveReport.filter((leaveElement) => {
            return (
                (!classificationType ||
                    classificationType.length === 0 ||
                    classificationType.includes(leaveElement.classificationType)) &&
                (!programId ||
                    programId.length === 0 ||
                    programId.includes(leaveElement.programId._id.toString())) &&
                leaveElement.approvalStatus !== WITHDRAWN
            );
        });
        if (!leaveReport.length) {
            return {
                status: 400,
                message: 'NO DATA',
            };
        }
        //group the data by categoryWise
        const groupedData = {};
        if (leaveReport.length) {
            for (const leaveReportELement of leaveReport) {
                const key = `${leaveReportELement.categoryId}-${leaveReportELement.classificationType}`;
                if (groupedData.hasOwnProperty(key)) {
                    groupedData[key].push(leaveReportELement);
                } else {
                    groupedData[key] = [leaveReportELement];
                }
            }
        }
        const categoryWiseLeaveReport = [];
        const overallLeaveReport = [];
        if (Object.keys(groupedData).length !== 0) {
            // categoryWise percentage
            for (const groupedDataElement in groupedData) {
                if (groupedData.hasOwnProperty(groupedDataElement)) {
                    const categoryObj = {
                        totalLeave: 0,
                        totalApproved: 0,
                        totalRejected: 0,
                        totalPending: 0,
                        totalApprovedPercentage: 0,
                        totalRejectedPercentage: 0,
                        totalPendingPercentage: 0,
                    };
                    for (innerElement of groupedData[groupedDataElement]) {
                        categoryObj.categoryName = innerElement.categoryName.toLowerCase() || '';
                        categoryObj.classificationType = innerElement.classificationType || '';
                        categoryObj.totalLeave = groupedData[groupedDataElement].length || 0;
                        if (innerElement.approvalStatus === 'Approved') {
                            categoryObj.totalApproved += 1;
                        } else if (innerElement.approvalStatus === 'Rejected') {
                            categoryObj.totalRejected += 1;
                        } else if (/^Pending/i.test(innerElement.approvalStatus)) {
                            categoryObj.totalPending += 1;
                        }
                    }
                    categoryObj.totalApprovedPercentage = (
                        (categoryObj.totalApproved / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalRejectedPercentage = (
                        (categoryObj.totalRejected / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalPendingPercentage = (
                        100 -
                        (Number(categoryObj.totalApprovedPercentage) +
                            Number(categoryObj.totalRejectedPercentage))
                    ).toFixed(2);
                    categoryWiseLeaveReport.push(categoryObj);
                }
            }
            // overAll percentages
            const overAllObj = {
                overallTotalLeave: 0,
                overAllApproved: 0,
                overAllRejected: 0,
                overAllPending: 0,
                overAllApprovedPercentage: 0,
                overAllRejectedPercentage: 0,
                overAllPendingPercentage: 0,
            };
            for (const categoryElement of categoryWiseLeaveReport) {
                overAllObj.overallTotalLeave += categoryElement.totalLeave || 0;
                overAllObj.overAllApproved += categoryElement.totalApproved || 0;
                overAllObj.overAllRejected += categoryElement.totalRejected || 0;
                overAllObj.overAllPending += categoryElement.totalPending || 0;
            }
            overAllObj.overAllApprovedPercentage = (
                (overAllObj.overAllApproved / overAllObj.overallTotalLeave) *
                100
            ).toFixed(2);
            overAllObj.overAllRejectedPercentage = (
                (overAllObj.overAllRejected / overAllObj.overallTotalLeave) *
                100
            ).toFixed(2);
            overAllObj.overAllPendingPercentage = (
                100 -
                (Number(overAllObj.overAllApprovedPercentage) +
                    Number(overAllObj.overAllRejectedPercentage))
            ).toFixed(2);
            overallLeaveReport.push(overAllObj);
        }
        return {
            status: 200,
            message: 'LEAVE MANAGEMENT REPORT',
            data: {
                categoryWiseLeaveReport: categoryWiseLeaveReport.length
                    ? categoryWiseLeaveReport.sort((firstElement, secondElement) =>
                          secondElement.categoryName.localeCompare(firstElement.categoryName),
                      )
                    : [],
                overallLeaveReport,
            },
        };
    } catch (error) {
        logger.error(
            { error },
            'LMS report ->  lmsStudentController - getLeaveManagement -> error',
        );
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getApplicationStatus = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id } = headers;
        const {
            from,
            to,
            classificationType,
            programId,
            studentSearch,
            programSearch,
            year,
            level,
            term,
            appClassificationSearch,
            statusSearch,
            type,
            state,
            studentSearchKey,
            programSearchKey,
            _institution_calendar_id,
        } = query;
        logger.info('LMS report ->  lmsStudentController - getApplicationStatus -> Start');
        const pagination = getPaginationValues(query);
        if (type === 'query') {
            let applicationReport = await getLmsReportFromCache({
                state,
                user_id,
                _institution_calendar_id,
                _institution_id,
                from,
                to,
            });
            if (!applicationReport.length) {
                return {
                    status: 400,
                    message: 'NO DATA',
                };
            }
            if (statusSearch && statusSearch.includes(LMS_PENDING)) {
                statusSearch.push(DELAYED);
            }
            applicationReport = applicationReport.filter((applicationElement) => {
                return (
                    (!classificationType ||
                        classificationType.length === 0 ||
                        classificationType.includes(applicationElement.classificationType)) &&
                    (!programId ||
                        programId.length === 0 ||
                        programId.includes(applicationElement.programId._id.toString())) &&
                    (!studentSearch ||
                        studentSearch.length === 0 ||
                        studentSearch.includes(applicationElement.studentId._id.toString())) &&
                    (!programSearch ||
                        programSearch.length === 0 ||
                        programSearch.includes(applicationElement.programId._id.toString())) &&
                    (!year || year.length === 0 || year.includes(applicationElement.year)) &&
                    (!level || level.length === 0 || level.includes(applicationElement.level)) &&
                    (!term || term.length === 0 || term.includes(applicationElement.term)) &&
                    (!appClassificationSearch ||
                        appClassificationSearch.length === 0 ||
                        appClassificationSearch.includes(applicationElement.classificationType)) &&
                    (!statusSearch ||
                        statusSearch.length === 0 ||
                        statusSearch.some((searchElement) => {
                            const regex = new RegExp(searchElement.trim(), 'i');
                            return regex.test(applicationElement.approvalStatus);
                        }))
                );
            });
            return {
                status: 200,
                message: 'LMS Report Application status for body',
                data: {
                    totalCount: applicationReport.length,
                    applicationReport: getChangedDelayedStatusIntoPending({
                        lmsApplications: applicationReport,
                    }).slice(pagination.skip, pagination.skip + pagination.limit),
                },
            };
        }
        if (type === 'headers') {
            let applicationReportForHeaders = await getLmsReportFromCache({
                state,
                user_id,
                _institution_calendar_id,
                _institution_id,
                from,
                to,
            });
            if (!applicationReportForHeaders.length) {
                return {
                    status: 400,
                    message: 'NO DATA',
                };
            }
            applicationReportForHeaders = applicationReportForHeaders.filter(
                (applicationReportElement) => {
                    return (
                        (!classificationType ||
                            classificationType.length === 0 ||
                            classificationType.includes(
                                applicationReportElement.classificationType,
                            )) &&
                        (!programId ||
                            programId.length === 0 ||
                            programId.includes(applicationReportElement.programId._id.toString()))
                    );
                },
            );
            if (!applicationReportForHeaders.length) {
                return {
                    status: 400,
                    message: 'NO DATA',
                };
            }
            const applicationStatusCount = [];
            const programs = [];
            const students = [];
            const categoryObj = {
                totalApproved: 0,
                totalReject: 0,
                totalPending: 0,
                totalWithDrawn: 0,
                terms: [],
            };
            for (const applicationHeaderElement of applicationReportForHeaders) {
                const { approvalStatus, term, programId, year, level, studentId } =
                    applicationHeaderElement;
                if (approvalStatus === 'Approved') {
                    categoryObj.totalApproved += 1;
                } else if (approvalStatus === 'Rejected') {
                    categoryObj.totalReject += 1;
                } else if (approvalStatus === 'Withdrawn') {
                    categoryObj.totalWithDrawn += 1;
                } else if (/^Pending/i.test(approvalStatus) || approvalStatus === DELAYED) {
                    categoryObj.totalPending += 1;
                }
                if (!categoryObj.terms.includes(term)) {
                    categoryObj.terms.push(term);
                }
                if (programs.length) {
                    const programIndex = programs.findIndex(
                        (programIndexElement) =>
                            programIndexElement.programId.toString() === programId._id.toString(),
                    );
                    if (programIndex === -1 && programSearchKey && programSearchKey.length) {
                        if (programSearchKey.includes(programId._id.toString())) {
                            programs.push({
                                programId: programId._id,
                                programName: programId.name,
                                levelYearTerm: [
                                    {
                                        level,
                                        year,
                                        term,
                                    },
                                ],
                            });
                        } else if (studentSearch && studentSearch.length) {
                            if (studentSearch.includes(studentId._id.toString())) {
                                programs.push({
                                    programId: programId._id,
                                    programName: programId.name,
                                    levelYearTerm: [
                                        {
                                            level,
                                            year,
                                            term,
                                        },
                                    ],
                                });
                            }
                        } else {
                            programs.push({
                                programId: programId._id,
                                programName: programId.name,
                                levelYearTerm: [],
                            });
                        }
                    } else if (programIndex === -1 && studentSearch && studentSearch.length) {
                        if (studentSearch.includes(studentId._id.toString())) {
                            programs.push({
                                programId: programId._id,
                                programName: programId.name,
                                levelYearTerm: [
                                    {
                                        level,
                                        year,
                                        term,
                                    },
                                ],
                            });
                        }
                    } else if (programIndex === -1 && !programSearchKey) {
                        programs.push({
                            programId: programId._id,
                            programName: programId.name,
                            levelYearTerm: [
                                {
                                    level,
                                    year,
                                    term,
                                },
                            ],
                        });
                    } else {
                        if (programs[programIndex].levelYearTerm.length) {
                            const yearLevelTermIndex = programs[
                                programIndex
                            ].levelYearTerm.findIndex(
                                (levelYearTermElement) =>
                                    levelYearTermElement.level === level &&
                                    levelYearTermElement.year === year &&
                                    levelYearTermElement.term === term,
                            );
                            if (yearLevelTermIndex === -1) {
                                programs[programIndex].levelYearTerm.push({
                                    level,
                                    year,
                                    term,
                                });
                            }
                        }
                    }
                } else if (programSearchKey && programSearchKey.length) {
                    if (programSearchKey.includes(programId._id.toString())) {
                        programs.push({
                            programId: programId._id,
                            programName: programId.name,
                            levelYearTerm: [
                                {
                                    level,
                                    year,
                                    term,
                                },
                            ],
                        });
                    } else if (studentSearch && studentSearch.length) {
                        if (studentSearch.includes(studentId._id.toString())) {
                            programs.push({
                                programId: programId._id,
                                programName: programId.name,
                                levelYearTerm: [
                                    {
                                        level,
                                        year,
                                        term,
                                    },
                                ],
                            });
                        }
                    } else {
                        programs.push({
                            programId: programId._id,
                            programName: programId.name,
                            levelYearTerm: [],
                        });
                    }
                } else if (studentSearch && studentSearch.length) {
                    if (studentSearch.includes(studentId._id.toString())) {
                        programs.push({
                            programId: programId._id,
                            programName: programId.name,
                            levelYearTerm: [
                                {
                                    level,
                                    year,
                                    term,
                                },
                            ],
                        });
                    }
                } else {
                    programs.push({
                        programId: programId._id,
                        programName: programId.name,
                        levelYearTerm: [
                            {
                                level,
                                year,
                                term,
                            },
                        ],
                    });
                }
            }
            applicationStatusCount.push(categoryObj);

            for (const studentSearchElement of applicationReportForHeaders) {
                const { studentId, programId } = studentSearchElement;
                const regex = new RegExp(studentSearchKey, 'i');
                const isDuplicate = students.some(
                    (student) =>
                        student.studentId.toString() === studentId._id.toString() &&
                        student.academicId.toString() === studentId.user_id.toString(),
                );
                if (studentSearchKey && !programSearchKey) {
                    if (
                        (regex.test(studentId.name.first) ||
                            regex.test(studentId.name.last) ||
                            regex.test(studentId.name.middle) ||
                            regex.test(studentId.user_id.toString())) &&
                        !isDuplicate
                    ) {
                        students.push({
                            studentId: studentId._id,
                            studentName: studentId.name,
                            programId: programId._id,
                            academicId: studentId.user_id,
                        });
                    }
                } else if (programSearchKey && programSearchKey.length && !studentSearchKey) {
                    if (programSearchKey.includes(programId._id.toString()) && !isDuplicate) {
                        students.push({
                            studentId: studentId._id,
                            studentName: studentId.name,
                            programId: programId._id,
                            academicId: studentId.user_id,
                        });
                    }
                } else if (programSearchKey && programSearchKey.length && studentSearchKey) {
                    if (
                        (regex.test(studentId.name.first) ||
                            regex.test(studentId.name.last) ||
                            regex.test(studentId.name.middle) ||
                            regex.test(studentId.user_id.toString())) &&
                        programSearchKey.includes(programId._id.toString()) &&
                        !isDuplicate
                    ) {
                        students.push({
                            studentId: studentId._id,
                            studentName: studentId.name,
                            programId: programId._id,
                            academicId: studentId.user_id,
                        });
                    }
                } else if (!isDuplicate) {
                    students.push({
                        studentId: studentId._id,
                        studentName: studentId.name,
                        programId: programId._id,
                        academicId: studentId.user_id,
                    });
                }
            }
            return {
                status: 400,
                message: 'Application Status For Headers',
                data: {
                    programs,
                    applicationStatusCount,
                    students: students.slice(0, 10),
                    studentIds: students.length
                        ? students
                              .slice(0, 10)
                              .map((studentArrayElement) => studentArrayElement.studentId)
                        : [],
                },
            };
        }
    } catch (error) {
        logger.error(
            { error },
            'LMS report ->  lmsStudentController - getApplicationStatus -> error',
        );
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramReport = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id } = headers;
        const {
            from,
            to,
            classificationType,
            programId,
            sortField,
            sortType,
            programSearch,
            termSearch,
            state,
            _institution_calendar_id,
        } = query;
        logger.info('LMS report ->  lmsStudentController - getProgramReport -> Start');
        const pagination = getPaginationValues(query);
        let programReport = await getLmsReportFromCache({
            state,
            user_id,
            _institution_calendar_id,
            _institution_id,
            from,
            to,
        });
        if (!programReport.length) {
            return {
                status: 400,
                message: 'NO DATA',
            };
        }
        programReport = programReport.filter((programElement) => {
            return (
                (!classificationType ||
                    classificationType.length === 0 ||
                    classificationType.includes(programElement.classificationType)) &&
                (!programId ||
                    programId.length === 0 ||
                    programId.includes(programElement.programId._id.toString())) &&
                (!programSearch ||
                    programSearch.length === 0 ||
                    programSearch.includes(programElement.programId._id.toString())) &&
                (!termSearch ||
                    termSearch.length === 0 ||
                    termSearch.includes(programElement.term)) &&
                programElement.approvalStatus !== WITHDRAWN
            );
        });
        if (!programReport.length) {
            return {
                status: 400,
                message: 'NO DATA',
            };
        }
        const groupedData = {};
        if (programReport.length) {
            for (const programReportELement of programReport) {
                const key = `${programReportELement.programId._id}-${programReportELement.term}`;
                if (groupedData.hasOwnProperty(key)) {
                    groupedData[key].push(programReportELement);
                } else {
                    groupedData[key] = [programReportELement];
                }
            }
        }
        let programWiseLeaveReport = [];
        if (Object.keys(groupedData).length !== 0) {
            for (const groupedDataElement in groupedData) {
                if (groupedData.hasOwnProperty(groupedDataElement)) {
                    const categoryObj = {
                        totalLeave: 0,
                        totalApproved: 0,
                        totalRejected: 0,
                        totalPending: 0,
                        totalApprovedPercentage: 0,
                        totalRejectedPercentage: 0,
                        totalPendingPercentage: 0,
                    };
                    for (innerElement of groupedData[groupedDataElement]) {
                        categoryObj.programName = innerElement.programId.name.toLowerCase() || '';
                        categoryObj.term = innerElement.term || '';
                        categoryObj.programId = innerElement.programId._id || '';
                        categoryObj.totalLeave = groupedData[groupedDataElement].length || 0;
                        if (innerElement.approvalStatus === 'Approved') {
                            categoryObj.totalApproved += 1;
                        } else if (innerElement.approvalStatus === 'Rejected') {
                            categoryObj.totalRejected += 1;
                        } else if (/^Pending/i.test(innerElement.approvalStatus)) {
                            categoryObj.totalPending += 1;
                        }
                    }
                    categoryObj.totalApprovedPercentage = (
                        (categoryObj.totalApproved / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalRejectedPercentage = (
                        (categoryObj.totalRejected / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalPendingPercentage = (
                        100 -
                        (Number(categoryObj.totalApprovedPercentage) +
                            Number(categoryObj.totalRejectedPercentage))
                    ).toFixed(2);
                    programWiseLeaveReport.push(categoryObj);
                }
            }
        }
        programWiseLeaveReport = programWiseLeaveReport.length
            ? programWiseLeaveReport.sort((firstElement, secondElement) => {
                  if (sortType === 'asc') {
                      return firstElement[sortField] - secondElement[sortField];
                  }
                  if (sortType === 'dsc') {
                      return secondElement[sortField] - firstElement[sortField];
                  }
                  return firstElement.programName.localeCompare(secondElement.programName);
              })
            : [];
        return {
            status: 200,
            message: 'programWiseReport',
            data: {
                totalCount: programWiseLeaveReport.length,
                programWiseLeaveReport: programWiseLeaveReport.slice(
                    pagination.skip,
                    pagination.skip + pagination.limit,
                ),
            },
        };
    } catch (error) {
        logger.error({ error }, 'LMS report ->  lmsStudentController - getProgramReport -> error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentApplicationStatus = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            studentId,
            programId,
            year,
            level,
            term,
            startDate,
            endDate,
            _institution_calendar_id,
        } = query;
        logger.info('LMS report ->  lmsStudentController - getStudentApplicationStatus -> Start');
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id,
            programId,
            yearNo: year,
            levelNo: level,
            term,
        });
        let individualStudentStatus = await CourseSchedule.aggregate([
            {
                $match: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'students._id': convertToMongoObjectId(studentId),
                    type: 'regular',
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    isActive: true,
                    isDeleted: false,
                },
            },
            {
                $sort: { schedule_date: 1 },
            },
            {
                $lookup: {
                    from: 'digi_courses',
                    let: { courseId: '$_course_id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ['$_id', '$$courseId'] },
                            },
                        },
                        {
                            $project: {
                                versionNo: 1,
                                versioned: 1,
                                versionName: 1,
                                versionedFrom: 1,
                                versionedCourseIds: 1,
                                _id: 0,
                            },
                        },
                    ],
                    as: 'courseVersionDetailsDetails',
                },
            },
            {
                $unwind: {
                    path: '$courseVersionDetailsDetails',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $addFields: {
                    versionNo: { $ifNull: ['$courseVersionDetailsDetails.versionNo', 1] },
                    versioned: { $ifNull: ['$courseVersionDetailsDetails.versioned', false] },
                    versionName: { $ifNull: ['$courseVersionDetailsDetails.versionName', ''] },
                    versionedFrom: {
                        $ifNull: ['$courseVersionDetailsDetails.versionedFrom', null],
                    },
                    versionedCourseIds: {
                        $ifNull: ['$courseVersionDetailsDetails.versionedCourseIds', []],
                    },
                },
            },
            { $unwind: '$students' },
            {
                $match: {
                    'students._id': convertToMongoObjectId(studentId),
                },
            },
            {
                $group: {
                    count: { $sum: 1 },
                    _id: '$_course_id',
                    course_name: { $first: '$course_name' },
                    program_name: { $first: '$program_name' },
                    course_code: { $first: '$course_code' },
                    versionNo: { $first: '$versionNo' },
                    versioned: { $first: '$versioned' },
                    versionName: { $first: '$versionName' },
                    versionedFrom: { $first: '$versionedFrom' },
                    versionedCourseIds: { $first: '$versionedCourseIds' },
                    allSchedules: {
                        $push: {
                            students: '$students',
                            status: '$status',
                            scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                            _institution_calendar_id: '$_institution_calendar_id',
                            programId: '$_program_id',
                            courseId: '$_course_id',
                            term: '$term',
                            levelNo: '$level_no',
                            rotationCount: '$rotation_count',
                            session: '$session',
                        },
                    },
                    schedules: {
                        $push: {
                            status: '$students.status',
                            schedule_date: '$schedule_date',
                            scheduleStartDateAndTime: '$scheduleStartDateAndTime',
                            scheduleEndDateAndTime: '$scheduleEndDateAndTime',
                            mergeStatus: '$merge_status',
                            mergeWith: '$merge_with',
                            subject_name: '$subjects.subject_name',
                            staff_name: '$staffs.staff_name',
                            staffId: '$staffs._staff_id',
                            session_type: '$session.session_type',
                            session_topic: '$session.session_topic',
                            s_no: '$session.s_no',
                            session_id: '$session._session_id',
                            delivery_no: '$session.delivery_no',
                            delivery_symbol: '$session.delivery_symbol',
                            studentId: '$students._id',
                            scheduleId: '$_id',
                            type: '$type',
                            title: '$title',
                        },
                    },
                    totalDays: {
                        $sum: {
                            $cond: [
                                {
                                    $eq: ['$students._id', convertToMongoObjectId(studentId)],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalLeave: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: [
                                                '$students._id',
                                                convertToMongoObjectId(studentId),
                                            ],
                                        },
                                        {
                                            $or: [
                                                { $eq: ['$students.status', 'absent'] },
                                                { $eq: ['$students.status', 'leave'] },
                                            ],
                                        },
                                        { $eq: ['$status', 'completed'] },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    upcomingLeave: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: [
                                                '$students._id',
                                                convertToMongoObjectId(studentId),
                                            ],
                                        },
                                        {
                                            $or: [
                                                { $eq: ['$status', 'ongoing'] },
                                                { $eq: ['$status', 'pending'] },
                                            ],
                                        },
                                        {
                                            $gte: [
                                                '$scheduleStartDateAndTime',
                                                new Date(startDate),
                                            ],
                                        },
                                        {
                                            $lte: ['$scheduleEndDateAndTime', new Date(endDate)],
                                        },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalOnDuty: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: [
                                                '$students._id',
                                                convertToMongoObjectId(studentId),
                                            ],
                                        },
                                        { $eq: ['$students.status', 'on_duty'] },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                    totalPermission: {
                        $sum: {
                            $cond: [
                                {
                                    $and: [
                                        {
                                            $eq: [
                                                '$students._id',
                                                convertToMongoObjectId(studentId),
                                            ],
                                        },
                                        { $eq: ['$students.status', 'permission'] },
                                    ],
                                },
                                1,
                                0,
                            ],
                        },
                    },
                },
            },
            {
                $project: {
                    _id: '$_id',
                    course_name: '$course_name',
                    program_name: '$program_name',
                    course_code: '$course_code',
                    versionNo: '$versionNo',
                    versioned: '$versioned',
                    versionName: '$versionName',
                    versionedFrom: '$versionedFrom',
                    versionedCourseIds: '$versionedCourseIds',
                    totalDays: '$totalDays',
                    totalLeave: '$totalLeave',
                    upcomingLeave: '$upcomingLeave',
                    totalPermission: '$totalPermission',
                    totalOnDuty: '$totalOnDuty',
                    allSchedules: '$allSchedules',
                    absentPercentage: {
                        $multiply: [{ $divide: ['$totalLeave', '$totalDays'] }, 100],
                    },
                    predictedAbsentPercentage: {
                        $multiply: [
                            {
                                $divide: [
                                    { $sum: ['$totalLeave', '$upcomingLeave'] },
                                    '$totalDays',
                                ],
                            },
                            100,
                        ],
                    },
                    schedules: {
                        $filter: {
                            input: '$schedules',
                            as: 'item',
                            cond: {
                                $and: [
                                    {
                                        $eq: [
                                            '$$item.studentId',
                                            convertToMongoObjectId(studentId),
                                        ],
                                    },
                                    {
                                        $gte: [
                                            '$$item.scheduleStartDateAndTime',
                                            new Date(startDate),
                                        ],
                                    },
                                    {
                                        $lte: ['$$item.scheduleEndDateAndTime', new Date(endDate)],
                                    },
                                ],
                            },
                        },
                    },
                },
            },
        ]);
        if (
            !individualStudentStatus.length &&
            !individualStudentStatus.filter(
                (individualStudentElement) => individualStudentElement.schedules.length,
            ).length
        ) {
            return {
                status: 400,
                message: 'NO_DATA',
            };
        }
        changePresentScheduleBasedLateConfigureForSingleStudent({
            lateDurationRange,
            manualLateRange,
            manualLateData,
            courseSchedules: individualStudentStatus,
            lateExcludeManagement,
        });
        // current warning details for the student
        if (individualStudentStatus && individualStudentStatus.length) {
            individualStudentStatus = individualStudentStatus.filter(
                (individualElement) => individualElement.schedules.length,
            );
            const courseIds = individualStudentStatus.map((courseElement) =>
                convertToMongoObjectId(courseElement._id),
            );
            const lmsData = await lmsNewSetting({ _institution_id });
            const studentCriteriaData = await lmsDenialSchema
                .find({
                    courseId: { $in: courseIds },
                    _institution_calendar_id,
                    levelNo: level,
                    term,
                    isActive: true,
                    isDeleted: false,
                })
                .sort({ updatedAt: -1 })
                .lean();
            for (const [
                individualStudentIndex,
                individualStudentElement,
            ] of individualStudentStatus.entries()) {
                const manipulatedPercentage = clone(lmsData.warningAbsenceData);
                manipulatedPercentage[0].isManipulated = false;
                const isMixedStudent =
                    studentCriteriaData.length &&
                    studentCriteriaData.find(
                        (denialStudentElement) =>
                            (denialStudentElement.term === term &&
                                denialStudentElement.courseId.toString() ===
                                    individualStudentElement._id.toString() &&
                                denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                            (denialStudentElement.term === term &&
                                denialStudentElement.studentId &&
                                denialStudentElement.courseId.toString() ===
                                    individualStudentElement._id.toString() &&
                                denialStudentElement.studentId.toString() === studentId.toString()),
                    );
                if (
                    isMixedStudent &&
                    isMixedStudent.absencePercentage &&
                    lmsData.warningAbsenceData[0] &&
                    lmsData.warningAbsenceData[0].percentage &&
                    lmsData.warningAbsenceData[0].percentage < isMixedStudent.absencePercentage
                ) {
                    manipulatedPercentage[0].percentage = isMixedStudent.absencePercentage;
                    manipulatedPercentage[0].isManipulated = true;
                }
                const warningData =
                    manipulatedPercentage[0].labelName === lmsData.denialLabel &&
                    manipulatedPercentage[0].isManipulated
                        ? manipulatedPercentage.find(
                              (manipulatedElement) =>
                                  manipulatedElement.percentage &&
                                  parseFloat(individualStudentElement.absentPercentage) >
                                      parseFloat(manipulatedElement.percentage),
                          )
                        : lmsData.warningAbsenceData.find(
                              (warningElement) =>
                                  warningElement.percentage &&
                                  parseFloat(individualStudentElement.absentPercentage) >
                                      parseFloat(warningElement.percentage),
                          );
                individualStudentElement.currentWarning = warningData ? warningData.labelName : '';
                individualStudentElement.warningColorCode = warningData
                    ? warningData.colorCode
                    : '';
                individualStudentElement.isDenial =
                    manipulatedPercentage[0].labelName === lmsData.denialLabel &&
                    manipulatedPercentage[0].isManipulated
                        ? manipulatedPercentage[0].percentage <=
                          parseFloat(individualStudentElement.predictedAbsentPercentage)
                        : lmsData.warningAbsenceData[0].percentage <=
                          parseFloat(individualStudentElement.predictedAbsentPercentage);
                delete individualStudentStatus[individualStudentIndex].sortedWarningConfig;
                delete individualStudentStatus[individualStudentIndex].predictedAbsentPercentage;
                delete individualStudentStatus[individualStudentIndex].lmsDenialData;
                for (const [index, schedule] of individualStudentElement.schedules.entries()) {
                    schedule.mergedSchedule = [];
                    if (schedule.mergeStatus) {
                        schedule.mergedSchedule.push({
                            s_no: schedule.s_no,
                            delivery_symbol: schedule.delivery_symbol,
                            delivery_no: schedule.delivery_no,
                            session_topic: schedule.session_topic,
                            session_type: schedule.session_type,
                        });
                        const scheduleIds = schedule.mergeWith.map((elem) =>
                            elem.schedule_id.toString(),
                        );
                        const listSchedules = individualStudentElement.schedules.length
                            ? individualStudentElement.schedules.filter((el, filterIndex) =>
                                  scheduleIds.includes(el.scheduleId.toString()),
                              )
                            : [];
                        const removeMergedSchedules = individualStudentElement.schedules.reduce(
                            function (acc, curr, index) {
                                if (scheduleIds.includes(curr.scheduleId.toString())) {
                                    acc.push(index);
                                }
                                return acc;
                            },
                            [],
                        );
                        for (const remove of removeMergedSchedules) {
                            individualStudentElement.schedules.splice(remove, 1);
                        }
                        const merge = listSchedules.map((list) => {
                            return {
                                s_no: list.s_no,
                                delivery_symbol: list.delivery_symbol,
                                delivery_no: list.delivery_no,
                                session_topic: list.session_topic,
                                session_type: list.session_type,
                            };
                        });
                        schedule.mergedSchedule = [...merge, ...schedule.mergedSchedule];
                    }
                }
            }
        }
        return {
            status: 200,
            message: 'individualStudentStatus',
            data: individualStudentStatus,
        };
    } catch (error) {
        logger.error(
            { error },
            'LMS report ->  lmsStudentController - getStudentApplicationStatus -> error',
        );
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const programYearWiseReport = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id } = headers;
        const { programId, classificationType, from, to, term, state, _institution_calendar_id } =
            query;
        logger.info('LMS report ->  lmsStudentController - programYearWiseReport -> Start');
        let programWiseReport = await getLmsReportFromCache({
            state,
            user_id,
            _institution_calendar_id,
            _institution_id,
            from,
            to,
        });
        if (!programWiseReport.length) return { statusCode: 410, message: 'NO_DATA' };
        programWiseReport = programWiseReport.filter((programWiseElement) => {
            return (
                programId.toString() === programWiseElement.programId._id.toString() &&
                (!classificationType ||
                    classificationType.length === 0 ||
                    classificationType.includes(programWiseElement.classificationType)) &&
                (!term || term.length === 0 || term.includes(programWiseElement.term)) &&
                programWiseElement.approvalStatus !== WITHDRAWN
            );
        });
        if (!programWiseReport.length) {
            return {
                status: 400,
                message: 'NO DATA',
            };
        }
        const programWiseGroupedData = {};
        const programYearWiseLeaveReport = [];
        const overallLeaveReport = [];
        if (programWiseReport.length) {
            for (const programWiseReportELement of programWiseReport) {
                const key = `${programWiseReportELement.year}-${programWiseReportELement.term}`;
                if (programWiseGroupedData.hasOwnProperty(key)) {
                    programWiseGroupedData[key].push(programWiseReportELement);
                } else {
                    programWiseGroupedData[key] = [programWiseReportELement];
                }
            }
        }
        if (Object.keys(programWiseGroupedData).length !== 0) {
            for (const programWiseGroupedDataElement in programWiseGroupedData) {
                if (programWiseGroupedData.hasOwnProperty(programWiseGroupedDataElement)) {
                    const categoryObj = {
                        totalLeave: 0,
                        totalApproved: 0,
                        totalRejected: 0,
                        totalPending: 0,
                        totalApprovedPercentage: 0,
                        totalRejectedPercentage: 0,
                        totalPendingPercentage: 0,
                    };
                    for (innerElement of programWiseGroupedData[programWiseGroupedDataElement]) {
                        categoryObj.term = innerElement.term.toLowerCase() || '';
                        categoryObj.year = innerElement.year.toLowerCase() || '';
                        categoryObj.totalLeave =
                            programWiseGroupedData[programWiseGroupedDataElement].length || 0;
                        if (innerElement.approvalStatus === 'Approved') {
                            categoryObj.totalApproved += 1;
                        } else if (innerElement.approvalStatus === 'Rejected') {
                            categoryObj.totalRejected += 1;
                        } else if (
                            /^Pending/i.test(innerElement.approvalStatus) ||
                            innerElement.approvalStatus === DELAYED
                        ) {
                            categoryObj.totalPending += 1;
                        }
                    }
                    categoryObj.totalApprovedPercentage = (
                        (categoryObj.totalApproved / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalRejectedPercentage = (
                        (categoryObj.totalRejected / categoryObj.totalLeave) *
                        100
                    ).toFixed(2);
                    categoryObj.totalPendingPercentage = (
                        100 -
                        (Number(categoryObj.totalApprovedPercentage) +
                            Number(categoryObj.totalRejectedPercentage))
                    ).toFixed(2);
                    programYearWiseLeaveReport.push(categoryObj);
                }
            }
            const overAllObj = {
                overallTotalLeave: 0,
                overAllApproved: 0,
                overAllRejected: 0,
                overAllPending: 0,
                overAllApprovedPercentage: 0,
                overAllRejectedPercentage: 0,
                overAllPendingPercentage: 0,
            };
            for (const programYearWiseElement of programYearWiseLeaveReport) {
                overAllObj.overallTotalLeave += programYearWiseElement.totalLeave || 0;
                overAllObj.overAllApproved += programYearWiseElement.totalApproved || 0;
                overAllObj.overAllRejected += programYearWiseElement.totalRejected || 0;
                overAllObj.overAllPending += programYearWiseElement.totalPending || 0;
            }
            overAllObj.overAllApprovedPercentage = (
                (overAllObj.overAllApproved / overAllObj.overallTotalLeave) *
                100
            ).toFixed(2);
            overAllObj.overAllRejectedPercentage = (
                (overAllObj.overAllRejected / overAllObj.overallTotalLeave) *
                100
            ).toFixed(2);
            overAllObj.overAllPendingPercentage = (
                100 -
                (Number(overAllObj.overAllApprovedPercentage) +
                    Number(overAllObj.overAllRejectedPercentage))
            ).toFixed(2);
            overallLeaveReport.push(overAllObj);
        }
        return {
            status: 200,
            message: 'LIST_DATA',
            data: {
                programYearWiseLeaveReport: programYearWiseLeaveReport.length
                    ? programYearWiseLeaveReport.sort((firstElement, secondElement) =>
                          firstElement.year.localeCompare(secondElement.year),
                      )
                    : [],
                overallLeaveReport,
            },
        };
    } catch (error) {
        logger.error(
            { error },
            'LMS report ->  lmsStudentController - programYearWiseReport -> error',
        );
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getGlobalSessionSettings = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        logger.info(
            'LMS Student-apply -> lmsStudentController - getGlobalSessionSettings -> start',
        );
        const globalSessions = await globalSessionSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                { basicDetails: 1 },
            )
            .lean();
        return {
            status: 200,
            message: 'LIST_DATA',
            data: {
                globalSessions: globalSessions ? globalSessions.basicDetails : {},
            },
        };
    } catch (error) {
        logger.error(
            { error },
            'LMS Student-apply -> lmsStudentController - getGlobalSessionSettings -> error',
        );
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAttachemntSignedUrl = async ({ params = {} }) => {
    try {
        const { id } = params;
        const lmsData = await lmsStudentSchema
            .findOne({ _id: id }, { 'attachments.url': 1 })
            .lean();
        if (!lmsData || !lmsData?.attachments?.length) {
            return {
                status: 404,
                message: 'No attachment found for given ID',
            };
        }
        lmsData.attachments = await Promise.all(
            lmsData.attachments.map(async (attachmentElement) => {
                attachmentElement.signedUrl = await getSignedURL(attachmentElement.url);
                return attachmentElement;
            }),
        );
        return {
            status: 200,
            message: 'LIST_ATTACHMENT',
            data: {
                attachments: lmsData.attachments,
            },
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getUserAssignedLevelDetails = ({ levelDetails, users }) => {
    const currentLevelDetails = { ...levelDetails };
    return {
        ...currentLevelDetails,
        level: currentLevelDetails.level.map((levelDetailElement) => {
            levelDetailElement.roleUserDetails = levelDetailElement.roleIds.map(
                (userIdElement) => ({
                    roleName: userIdElement.name,
                    roleId: userIdElement._id,
                    userDetails: users.map((userIdElement) => ({
                        staffId: convertToMongoObjectId(userIdElement),
                        approvalStatus: 'pending',
                    })),
                }),
            );
            return levelDetailElement;
        }),
    };
};

const sessionWiseLeaveApply = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const {
            classificationType,
            studentId,
            categoryName,
            categoryId,
            typeId,
            typeName,
            dateAndTimeRange,
            noOfHours,
            reason,
            attachments,
            academicId,
            leaveApplicationCriteria,
            scheduleIds = [],
            programId,
            term,
            year,
            level,
        } = body;
        const lmsSettingData = await lmsSettingSchema
            .findOne(
                {
                    _institution_id,
                    classificationType,
                    isActive: true,
                },
                { levelApprover: 1 },
            )
            .populate({ path: 'levelApprover.level.roleIds', select: { name: 1 } })
            .lean();
        const totalApprovalLevels =
            lmsSettingData?.levelApprover.find(({ programIds = [] }) =>
                String(programIds).includes(String(programId)),
            ) ?? null;
        if (!totalApprovalLevels) {
            return { statusCode: 400, message: `Program does not exists in ${classificationType}` };
        }
        if (!totalApprovalLevels.level.length) {
            return { statusCode: 400, message: 'Approver is not yet configured' };
        }
        const roleIds = totalApprovalLevels.level.flatMap(({ roleIds = [] }) => roleIds);
        const currentDate = new Date();
        // **role users is static only for schedule staff need to handle for dynamic role and dynamic levels
        const getStudentLeaveQueryDetails = {
            _institution_id,
            _institution_calendar_id,
            classificationType,
            studentId,
            categoryName,
            categoryId,
            typeId,
            typeName,
            dateAndTimeRange,
            noOfHours,
            reason,
            attachments,
            academicId,
            leaveApplicationCriteria,
            programId,
            term,
            year,
            level,
            roleIds,
            approvalStatus: totalApprovalLevels.level[0].levelName,
            createdAt: currentDate,
        };
        const bulkInsertData = scheduleIds.map(({ scheduleId, staffId = [] }) => ({
            ...getStudentLeaveQueryDetails,
            scheduleId,
            roleUsers: staffId,
            levelApprover: getUserAssignedLevelDetails({
                levelDetails: totalApprovalLevels,
                users: staffId,
            }),
        }));
        const createdStudentApplicationDetails = await lmsStudentSchema.insertMany(bulkInsertData);
        if (!createdStudentApplicationDetails || !createdStudentApplicationDetails.length) {
            return { statusCode: 410, message: 'UNABLE_TO_CREATE' };
        }
        return {
            statusCode: 200,
            message: `Your ${typeName} has been applied successfully`,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sessionWiseLeaveListForApprover = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { classificationType, userId, roleId, page, limit } = query;
        const currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        const currentLimit = page && limit ? Number(limit) : 10;
        const studentLeaveApplicationFindQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            classificationType,
            approvalStatus: { $nin: [APPROVED, CANCELLED, REJECT, WITHDRAWN] },
            roleIds: convertToMongoObjectId(roleId),
            roleUsers: convertToMongoObjectId(userId),
            isDeleted: false,
        };
        const studentLeaveApplications = await lmsStudentSchema
            .find(studentLeaveApplicationFindQuery, {
                _institution_calendar_id: 1,
                classificationType: 1,
                programId: 1,
                level: 1,
                year: 1,
                term: 1,
                categoryName: 1,
                typeName: 1,
                dateAndTimeRange: 1,
                applyingForDays: 1,
                noOfHours: 1,
                reason: 1,
                approvalStatus: 1,
                attachments: 1,
                createdAt: 1,
                scheduleId: 1,
                categoryId: 1,
                typeId: 1,
                studentId: 1,
            })
            .sort({ updatedAt: -1 })
            .populate({
                path: 'studentId',
                select: { name: 1, user_id: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .populate({
                path: 'scheduleId',
                select: { isDeleted: 1 },
            })
            .lean();
        const totalValidLeaves = studentLeaveApplications.filter(
            ({ scheduleId }) => !(scheduleId?.isDeleted ?? false),
        );
        return {
            statusCode: 200,
            message: 'student application for approval',
            data: {
                studentLeaveApplications: totalValidLeaves.slice(
                    currentPage,
                    currentPage + currentLimit,
                ),
                totalCount: totalValidLeaves.length,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const checkCurrentApplicationLevel = ({
    levelApprover,
    totalApprovedList,
    approvedLevel,
    staffApprovedStatus,
}) => {
    const approvedIndex = levelApprover.level.findIndex(
        ({ levelName }) => levelName === approvedLevel,
    );
    // need to handle skip and approval logics
    return staffApprovedStatus === REJECT
        ? REJECT
        : levelApprover.genderSegregation || approvedIndex === levelApprover.level.length - 1
        ? APPROVED
        : levelApprover.level[approvedIndex + 1]?.levelName ?? APPROVED;
};

const getUpdatedLeaveApplicationStatus = ({
    leaveApplicationWithStatus,
    studentPendingApplications,
    reason,
    approvedStaffId,
    roleId,
    approvedLevel,
    studentId,
}) => {
    const constructedLeaveApplication = {};
    const currentDate = new Date();
    leaveApplicationWithStatus.forEach(({ applicationId, approvalStatus }) => {
        constructedLeaveApplication[String(applicationId)] = {
            status: approvalStatus,
            date: currentDate,
        };
    });
    const leaveApplicationBulkWrite = [];
    const scheduleBulkWrite = [];
    studentPendingApplications.forEach(
        ({ levelApprover, approvalFrom, _id, scheduleId, classificationType }) => {
            const constructedDetail = constructedLeaveApplication[String(_id)];
            const totalApprovedList = [
                ...approvalFrom,
                { ...constructedDetail, reason, userId: approvedStaffId, roleId },
            ];

            // Check if form is approved
            const applicationPendingLevel = checkCurrentApplicationLevel({
                levelApprover,
                totalApprovedList,
                approvedLevel,
                staffApprovedStatus: constructedDetail.status,
            });

            leaveApplicationBulkWrite.push({
                updateMany: {
                    filter: { _id },
                    update: {
                        $set: {
                            approvalFrom: totalApprovedList,
                            approvalStatus: applicationPendingLevel,
                        },
                    },
                },
            });
            if (applicationPendingLevel === APPROVED) {
                scheduleBulkWrite.push({
                    updateMany: {
                        filter: { _id: convertToMongoObjectId(scheduleId) },
                        update: {
                            $set: {
                                'students.$[i].status': classificationType,
                            },
                        },
                        arrayFilters: [
                            {
                                'i._id': convertToMongoObjectId(studentId),
                            },
                        ],
                    },
                });
            }
        },
    );
    return { leaveApplicationBulkWrite, scheduleBulkWrite };
};

const sessionWiseStaffApproval = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            leaveApplicationWithStatus,
            approvedStaffId,
            studentId,
            levelName,
            reason,
            roleId,
        } = body;
        const studentPendingApplications = await lmsStudentSchema
            .find(
                {
                    _id: {
                        $in: leaveApplicationWithStatus.map(({ applicationId }) =>
                            convertToMongoObjectId(applicationId),
                        ),
                    },
                },
                {
                    levelApprover: 1,
                    approvalFrom: 1,
                    scheduleId: 1,
                    classificationType: 1,
                },
            )
            .lean();

        const { leaveApplicationBulkWrite, scheduleBulkWrite } = getUpdatedLeaveApplicationStatus({
            leaveApplicationWithStatus,
            studentPendingApplications,
            reason,
            approvedStaffId,
            roleId,
            approvedLevel: levelName,
            studentId,
        });
        if (leaveApplicationBulkWrite.length) {
            await lmsStudentSchema.bulkWrite(leaveApplicationBulkWrite);
        }
        if (scheduleBulkWrite.length) {
            await courseScheduleSchema.bulkWrite(scheduleBulkWrite);
        }
        return {
            statusCode: 200,
            message: 'Leave Application Updated Successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sessionWiseLeaveHistoryList = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { classificationType, userId, roleId, page, limit } = query;
        const leaveApplicationFindQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            classificationType,
            approvalStatus: { $in: [APPROVED, CANCELLED, REJECT, WITHDRAWN] },
            roleIds: convertToMongoObjectId(roleId),
            roleUsers: convertToMongoObjectId(userId),
            isDeleted: false,
        };
        const currentPage = page && limit ? (Number(page) - 1) * Number(limit) : 0;
        const currentLimit = page && limit ? Number(limit) : 10;
        const studentLeaveApplications = await lmsStudentSchema
            .find(leaveApplicationFindQuery, {
                _institution_calendar_id: 1,
                classificationType: 1,
                programId: 1,
                level: 1,
                year: 1,
                term: 1,
                categoryName: 1,
                typeName: 1,
                dateAndTimeRange: 1,
                applyingForDays: 1,
                noOfHours: 1,
                reason: 1,
                approvalStatus: 1,
                attachments: 1,
                createdAt: 1,
                scheduleId: 1,
                categoryId: 1,
                typeId: 1,
                studentId: 1,
            })
            .sort({ updatedAt: -1 })
            .populate({
                path: 'studentId',
                select: { name: 1, user_id: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .populate({
                path: 'scheduleId',
                select: { isDeleted: 1 },
            })
            .lean();
        const totalValidHistory = studentLeaveApplications.filter(
            ({ scheduleId }) => !(scheduleId?.isDeleted ?? false),
        );
        return {
            statusCode: 200,
            message: 'student application for approval',
            data: {
                studentLeaveApplications: totalValidHistory.slice(
                    currentPage,
                    currentPage + currentLimit,
                ),
                historyCount: totalValidHistory.length,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    applyOnDuty,
    listData,
    updateStatus,
    getAttendanceSheets,
    previouslyLeaveStatus,
    listSchedule,
    updateAgree,
    listApprovalLevelWiseStatus,
    studentListForStaffApprover,
    staffApproval,
    revokeApproval,
    editStaffApproval,
    updateTurnAroundTime,
    getLeaveManagement,
    getApplicationStatus,
    getProgramReport,
    getStudentApplicationStatus,
    programYearWiseReport,
    getGlobalSessionSettings,
    getAttachemntSignedUrl,
    sessionWiseLeaveApply,
    sessionWiseLeaveListForApprover,
    sessionWiseStaffApproval,
    sessionWiseLeaveHistoryList,
};
