const express = require('express');
const route = express.Router();
const modules = require('./module_controller');
const validator = require('./module_validator');
const {
    userPolicyAuthentication,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');

route.get('/:id', validator.role_id, modules.list_id);
route.get('/', modules.list);
// route.get('/', [userPolicyAuthentication([defaultProgramInputPolicy.VIEW])], modules.list);
route.post('/', validator.role, modules.insert);
route.put('/:id', validator.role_id, validator.role, modules.update);
route.put('/page_update/:id', validator.role_id, validator.role, modules.page_update);
route.delete('/:id', validator.role_id, modules.delete);

module.exports = route;
