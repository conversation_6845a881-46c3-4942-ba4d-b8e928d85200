let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let institution = new Schema({
    calendar_name: String,
    calendar_type: {
        type: String,
        enum: [constant.PRIMARY, constant.SECONDARY]
    },
    _primary_calendar_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION_CALENDAR
    },
    batch: {
        type: String,
        required: true
    },
    start_date: {
        type: Date,
        required: true
    },
    end_date: {
        type: Date,
        required: true
    },
    primary_calendar: {
        type: String,
        enum: [constant.GREGORIAN, constant.HIJRI],
        required: true
    },
    status: {
        type: String
    },
    _creater_id: {
        type: Schema.Types.ObjectId,
        ref: constant.STAFF,
        required: true
    },
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.INSTITUTION_CALENDAR, institution);