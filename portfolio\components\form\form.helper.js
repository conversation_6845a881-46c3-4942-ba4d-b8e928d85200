const { PUBLISHED } = require('../../common/utils/enums');
const { isIDEquals } = require('../../common/utils/common.util');

const getAssignedUserSections = ({ pages = [], viewSections = [], modifySections = [] }) => {
    const filteredPages = pages.map((page) => {
        const sections = [];

        page.elements.forEach((element) => {
            const isViewSection = viewSections.some((section) =>
                isIDEquals(section.sectionId, element._id),
            );
            const isModifySection = modifySections.some((section) =>
                isIDEquals(section.sectionId, element._id),
            );

            if (isViewSection) {
                sections.push({
                    ...element,
                    modify: isModifySection,
                });
            }
        });

        return {
            ...page,
            elements: sections,
        };
    });

    return filteredPages;
};

const buildFormQuery = ({ email, formId, isEvaluator }) => {
    const orConditions = isEvaluator
        ? [
              {
                  'pages.elements.users': {
                      $elemMatch: {
                          email,
                          evaluate: true,
                      },
                  },
              },
              {
                  'pages.elements.roles': {
                      $elemMatch: {
                          evaluate: true,
                          users: {
                              $elemMatch: {
                                  email,
                              },
                          },
                      },
                  },
              },
          ]
        : [{ 'pages.elements.users.email': email }, { 'pages.elements.roles.users.email': email }];

    return {
        $or: orConditions,
        status: PUBLISHED,
        isDeleted: false,
        ...(formId && { _id: formId }),
    };
};

module.exports = {
    getAssignedUserSections,
    buildFormQuery,
};
