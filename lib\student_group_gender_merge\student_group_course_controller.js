const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const { BOTH } = require('../utility/constants');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const { INSTITUTION_NAME } = require('../utility/util_keys');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
// const program = require('mongoose').model(constant.PROGRAM);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);
// const course = require('mongoose').model(constant.COURSE);
const course = require('mongoose').model(constant.DIGI_COURSE);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
// const session_type = require('mongoose').model(constant.SESSION_TYPE);
const session_type = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const institution = require('mongoose').model(constant.INSTITUTION);
const {
    removeStudentSchedule,
    addingStudentSchedule,
    updateStudentGroupFlatCacheData,
} = require('./student_group_services');
const {
    updateStudentGroupRedisKey,
    updateUserCourseRedisKey,
} = require('../utility/utility.service');
exports.course_setting_get = async (req, res) => {
    try {
        const query = {
            _id: ObjectId(req.params.id),
            'master.term': req.params.batch,
            isDeleted: false,
        };
        const project = {};
        const doc = await base_control.get(student_group, query, project);

        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );

        const ic_get = await base_control.get(
            institution_calendar,
            { _id: ObjectId(doc.data._institution_calendar_id) },
            { calendar_name: 1 },
        );
        const p_get = await base_control.get(
            program,
            { _id: ObjectId(doc.data.master._program_id) },
            { name: 1 },
        );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params.course) },
            { courses_name: 1, courses_number: 1 },
        );
        const course_session_order = await base_control.get_list(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params.course) },
            {},
        );
        const delivery_type = await base_control.get_list(
            session_type,
            { _program_id: ObjectId(doc.data.master._program_id) },
            {},
        );

        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        // const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];
        const group_name =
            ic_get.data.calendar_name +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P' +
            '-' +
            (req.params.batch ? 'RT' : 'IT') +
            '-' +
            doc.data.master.year +
            'Y' +
            '-' +
            p_get.data.name.substring(0, 1) +
            'P:' +
            doc.data.master.curriculum +
            '-' +
            doc.data.master.level +
            'L-' +
            course_get.data.courses_number;

        const delivery = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            delivery.push({
                session_type: delivery_data.session_name,
                deliver: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        // symbol.forEach((element) => {
        //     const delivery_data =
        //         delivery_type.data[
        //             delivery_type.data.findIndex((i) => i.delivery_symbol === element)
        //         ];
        //     delivery.push({
        //         delivery_type: delivery_data.session_type,
        //         delivery_name: delivery_data.delivery_type,
        //         delivery_symbol: delivery_data.delivery_symbol,
        //     });
        // });
        // delivery.forEach((element) => {
        //     if (element.delivery_type === 'Theory')
        //         theory.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Practical')
        //         practical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Clinical')
        //         clinical.push({ deliver: element.delivery_name, symbol: element.delivery_symbol });
        // });
        // const session_dev_type = {
        //     Theory: theory,
        //     Practical: practical,
        //     Clinical: clinical,
        // };
        // if (course_get.data.model === constant.MODEL.ELECTIVE) {
        //     Object.assign(session_dev_type, { 'elective': '-' });
        // }
        let male_count = 0;
        let female_count = 0;
        const objs = {
            _id: doc.data._id,
            master: doc.data.master,
            group_name,
            session_type: delivery,
            course: course_get.data,
            setting: doc.data.group_setting,
        };
        doc.data.ungrouped.forEach((element) => {
            const std_data =
                doc.data.students[
                    doc.data.students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ];
            if (std_data.gender === constant.GENDER.MALE) {
                male_count++;
            } else {
                female_count++;
            }
        });
        Object.assign(objs, { male_count, female_count });
        Object.assign(objs, { rotation_count: doc.data.rotation_count });
        common_files.com_response(res, 200, true, 'Student Group Setting page', objs);
    } catch (error) {
        common_files.com_response(res, 500, false, 'Catch Error', error.toString());
    }
};

exports.course_group_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            'groups.level': req.body.level,
            'groups.term': req.body.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        let ind_group_setting = [];
        let groups_setting = [];
        let g_type = [];
        let student_groups = [];
        if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
            for (element of student_group_check.data.groups[student_group_row]
                .rotation_group_setting) {
                if (
                    req.query.mode
                        ? req.query.mode === element.gender
                        : element.gender === req.body.gender
                )
                    student_groups.push(element);
            }
        } else {
            student_groups =
                student_group_check.data.groups[student_group_row].group_setting[
                    student_group_check.data.groups[student_group_row].group_setting.findIndex(
                        (i) =>
                            req.query.mode
                                ? req.query.mode === i.gender
                                : i.gender === req.body.gender,
                    )
                ];
        }
        const course_pos = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const course_setting =
            student_group_check.data.groups[student_group_row].courses[course_pos].setting;
        groups_setting = course_setting.filter((item) =>
            req.query.mode
                ? req.query.mode !== item.gender
                : req.body.gender !== item.gender.toString(),
        );
        req.body.groups.forEach((element) => {
            g_type = [];
            let un_group = [];
            if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
                un_group =
                    student_groups[
                        student_groups.findIndex((i) => i.group_no === element._group_no)
                    ]._student_ids;
            } else {
                un_group =
                    student_groups.groups[
                        student_groups.groups.findIndex((i) => i.group_no === element._group_no)
                    ]._student_ids;
            }
            const name =
                student_group_check.data.groups[student_group_row].group_name +
                '-' +
                student_group_check.data.groups[student_group_row].courses[course_pos].course_no +
                '-G' +
                element._group_no;
            const gs_obj = {
                _group_no: element._group_no,
                gender: req.query.mode ? req.query.mode : req.body.gender,
                ungrouped: un_group,
                // ungrouped: student_groups.groups[student_groups.groups.findIndex(i => i.group_no === element._group_no)]._student_ids
            };
            element.delivery_type_group.forEach((sub_element) => {
                ind_group_setting = [];
                for (let i = 1; i <= sub_element.no_of_group; i++) {
                    const igs_obj = {
                        group_no: i,
                        group_name: name + '-' + sub_element.delivery + '-' + i.toString(),
                    };
                    ind_group_setting.push(igs_obj);
                }
                const ses_setting = {
                    group_name: name + '-' + sub_element.delivery,
                    session_type: sub_element.delivery,
                    delivery_type: sub_element.delivery_type,
                    no_of_group: sub_element.no_of_group,
                    no_of_student: sub_element.no_of_students,
                    groups: ind_group_setting,
                };
                g_type.push(ses_setting);
            });
            Object.assign(gs_obj, { session_setting: g_type });
            groups_setting.push(gs_obj);
        });
        objs = { $set: { 'groups.$[i].courses.$[k].setting': groups_setting } };
        filter = {
            arrayFilters: [
                {
                    'i.level': req.body.level,
                    'i.term': req.body.batch,
                },
                { 'k._course_id': req.body._course_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('ERROR_UNABLE_TO_SET_COURSE_GROUP_SETTING'),
                        doc.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                    req.t('STUDENTS_COURSE_GROUP_SETTING_IS_CREATED'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.course_group_list_filter = async (req, res) => {
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const objs = {
            _id: doc.data._id,
            _institution_calendar_id: doc.data._institution_calendar_id,
            group_name: doc.data.group_name,
            master: doc.data.master,
        };
        // let students = [];
        // let ung_count = 0, all_std = 0;
        const group_student_list = [];
        let all_student_id = [];
        const course_datas = [];
        doc.data.courses.forEach((element) => {
            course_datas.push({
                _course_id: element._course_id,
                course_name: element.course_name,
                course_no: element.course_no,
                grouped: [],
                ungrouped: [],
            });
        });
        Object.assign(objs, { course: doc.data.courses });
        const g_list = {};
        doc.data.group_setting.forEach((element) => {
            if (element.gender === req.params.gender) {
                element.groups.forEach((sub_element) => {
                    const gc = 'group' + sub_element.group_no;
                    g_list[gc] = sub_element._student_ids.length;
                    all_student_id = all_student_id.concat(sub_element._student_ids);
                    group_student_list.push({
                        title: gc,
                        group_no: sub_element.group_no,
                        course: course_datas,
                        students_id: sub_element._student_ids,
                    });
                });
            }
        });
        Object.assign(objs, { groups_list: g_list });
        group_student_list.forEach((element, index) => {
            element.course.forEach((sub_element, sub_index) => {
                const c_data =
                    doc.data.courses[
                        doc.data.courses.findIndex(
                            (i) => i._course_id.toString() === sub_element._course_id.toString(),
                        )
                    ];
                const setting_index = c_data.setting.findIndex(
                    (i) => i._group_no.toString() === element.group_no.toString(),
                );
                if (setting_index !== -1) {
                    const session_setting = c_data.setting[setting_index].session_setting;
                    // const session_type = session_setting.map((i) => i.session_type);
                    const student = [];
                    c_data.setting[setting_index].ungrouped.forEach((ungroup_element) => {
                        const temp =
                            doc.data.students[
                                doc.data.students.findIndex(
                                    (i) => i._student_id.toString() === ungroup_element.toString(),
                                )
                            ];
                        if (temp) {
                            const data = {};
                            let course_session_index = -1;
                            session_setting.forEach((session_element) => {
                                session_element.groups.forEach((group_element) => {
                                    if (
                                        group_element._student_ids.findIndex(
                                            (i) => i === temp._student_id,
                                        ) !== -1
                                    ) {
                                        course_session_index = group_element._student_ids.findIndex(
                                            i === temp._student_id,
                                        );
                                    }
                                });
                                data[session_element.session_type.toString()] =
                                    course_session_index !== -1 ? course_session_index : '-';
                            });
                            // session_type.forEach(session_element => {
                            //     data[(session_element.toString())] = '-';
                            // });
                            data._student_id = temp._student_id;
                            data.name = temp.name;
                            data.academic_no = temp.academic_no;
                            data.gender = temp.gender;
                            data.mark = temp.mark;
                            data.imported_on = temp.imported_on;
                            data.imported_by = temp.imported_by;
                            student.push(data);
                        }
                    });
                    group_student_list[index].course[sub_index].ungrouped = student;
                }
            });
        });
        Object.assign(objs, { group_student_list });
        common_files.com_response(res, 200, true, 'Student Group Data', objs);
    } else {
        common_files.com_response(
            res,
            404,
            false,
            'Error Unable to get List',
            'Unable to get List',
        );
    }
};

exports.course_grouped_list_filter = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            'groups.level': req.params.level,
            'groups.term': req.params.batch,
            isDeleted: false,
        };
        // const student_group_check = await base_control.get(student_group, query, {});
        const student_group_check = await student_group
            .findOne(query)
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        if (!student_group_check)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const objs = {
            _id: student_group_check._id,
            _institution_calendar_id: student_group_check._institution_calendar_id,
            group_name: student_group_check.group_name,
            master: student_group_check.master,
            course_excess_count: student_group_check.groups[student_group_row].course_excess_count,
        };

        const group_student_list = [];
        let all_student_id = [];
        const course_datas = [];
        student_group_check.groups[student_group_row].courses.forEach((element) => {
            let settings =
                element.setting[
                    element.setting.findIndex(
                        (i) =>
                            i._group_no.toString() === req.params.group.toString() &&
                            (req.query.mode
                                ? req.query.mode === i.gender
                                : i.gender.toString() === req.params.gender.toString()),
                    )
                ];
            if (student_group_check.groups[student_group_row].rotation === 'yes') {
                if (element.course_type === constant.MODEL.ELECTIVE && req.params.group === 'all') {
                    settings =
                        element.setting[
                            element.setting.findIndex((i) =>
                                req.query.mode
                                    ? req.query.mode === i.gender
                                    : i.gender.toString() === req.params.gender.toString(),
                            )
                        ];
                    course_datas.push({
                        _course_id: element._course_id?._id,
                        versionedCourseIds: element._course_id?.versionedCourseIds || [],
                        course_name: element.course_name,
                        course_no: element.course_no,
                        course_type: element.course_type,
                        versionNo: element._course_id?.versionNo || 1,
                        versioned: element._course_id?.versioned || false,
                        versionName: element._course_id?.versionName || '',
                        versionedFrom: element._course_id?.versionedFrom || null,
                        setting: settings,
                        grouped: [],
                        ungrouped: [],
                        removed: element._removed_student_ids,
                    });
                } else if (
                    element.course_type !== constant.MODEL.ELECTIVE &&
                    req.params.group !== 'all'
                ) {
                    course_datas.push({
                        _course_id: element._course_id?._id,
                        versionedCourseIds: element._course_id?.versionedCourseIds || [],
                        course_name: element.course_name,
                        course_no: element.course_no,
                        course_type: element.course_type,
                        versionNo: element._course_id?.versionNo || 1,
                        versioned: element._course_id?.versioned || false,
                        versionName: element._course_id?.versionName || '',
                        versionedFrom: element._course_id?.versionedFrom || null,
                        setting: settings,
                        grouped: [],
                        ungrouped: [],
                        removed: element._removed_student_ids,
                    });
                }
            } else {
                course_datas.push({
                    _course_id: element._course_id?._id,
                    versionedCourseIds: element._course_id?.versionedCourseIds || [],
                    course_name: element.course_name,
                    course_no: element.course_no,
                    course_type: element.course_type,
                    versionNo: element._course_id?.versionNo || 1,
                    versioned: element._course_id?.versioned || false,
                    versionName: element._course_id?.versionName || '',
                    versionedFrom: element._course_id?.versionedFrom || null,
                    setting: settings,
                    grouped: [],
                    ungrouped: [],
                    removed: element._removed_student_ids,
                });
            }
        });
        const g_list = {};
        const g_count_list = {};
        let total_count = 0;
        let total_capacity = 0;
        let group_students = [];
        if (student_group_check.groups[student_group_row].rotation === 'yes') {
            student_group_check.groups[student_group_row].rotation_group_setting.forEach(
                (element) => {
                    if (
                        req.query.mode
                            ? req.query.mode === element.gender
                            : element.gender.toString() === req.params.gender.toString()
                    ) {
                        const gc = 'group' + element.group_no;
                        g_list[gc] = element._student_ids.length;
                        total_count += element._student_ids.length;
                        total_capacity += element.no_of_student;
                        g_count_list[gc] = element.no_of_student;
                        if (req.params.group === 'all') {
                            group_students = group_students.concat(element._student_ids);
                        } else {
                            if (element.group_no.toString() === req.params.group.toString())
                                group_students = group_students.concat(element._student_ids);
                        }
                    }
                },
            );
            g_list.all = total_count;
            g_count_list.all = total_capacity;
        } else {
            student_group_check.groups[student_group_row].group_setting.forEach((element) => {
                if (
                    req.query.mode
                        ? req.query.mode === element.gender
                        : element.gender === req.params.gender
                ) {
                    element.groups.forEach((sub_element) => {
                        const gc = 'group' + sub_element.group_no;
                        g_list[gc] = sub_element._student_ids.length;
                        g_count_list[gc] = element.no_of_student;
                        if (sub_element.group_no.toString() === req.params.group.toString())
                            group_students = group_students.concat(sub_element._student_ids);
                    });
                }
            });
        }
        group_student_list.push({
            title: 'group' + req.params.group,
            group_no: req.params.group,
            course: course_datas,
        });
        course_datas.forEach((sub_element, sub_index) => {
            const session_setting = sub_element.setting
                ? sub_element.setting.session_setting
                    ? sub_element.setting.session_setting
                    : ''
                : undefined;
            const session_types = session_setting
                ? session_setting.map((i) => i.session_type)
                    ? session_setting.map((i) => i.session_type)
                    : ''
                : '';
            let student = [];
            group_students.forEach((ungroup_element) => {
                if (sub_element.removed.indexOf(ungroup_element.toString()) === -1) {
                    const temp =
                        student_group_check.groups[student_group_row].students[
                            student_group_check.groups[student_group_row].students.findIndex(
                                (i) => i._student_id.toString() === ungroup_element.toString(),
                            )
                        ];
                    const data = {};
                    if (session_types !== '')
                        session_types.forEach((session_element) => {
                            data[session_element.toString()] = '-';
                        });
                    if (temp) {
                        data._student_id = temp._student_id;
                        data.name = temp.name;
                        data.academic_no = temp.academic_no;
                        data.gender = temp.gender;
                        data.mark = temp.mark;
                        data.imported_on = temp.imported_on;
                        data.imported_by = temp.imported_by;
                        student.push(data);
                    }
                }
            });
            group_student_list[0].course[sub_index].ungrouped = student;
            if (sub_element.setting && sub_element.setting.session_setting) {
                // student = [];
                sub_element.setting.ungrouped.forEach((ungroup_element) => {
                    if (group_students.indexOf(ungroup_element.toString()) === -1) {
                        const temp =
                            student_group_check.groups[student_group_row].students[
                                student_group_check.groups[student_group_row].students.findIndex(
                                    (i) => i._student_id.toString() === ungroup_element.toString(),
                                )
                            ];
                        const data = {};
                        session_types.forEach((session_element) => {
                            data[session_element.toString()] = '-';
                        });
                        if (temp) {
                            data._student_id = temp._student_id;
                            data.name = temp.name;
                            data.academic_no = temp.academic_no;
                            data.gender = temp.gender;
                            data.mark = temp.mark;
                            data.imported_on = temp.imported_on;
                            data.imported_by = temp.imported_by;
                            if (
                                student.findIndex(
                                    (i) => i._student_id.toString() === temp._student_id.toString(),
                                ) === -1
                            )
                                student.push(data);
                        }
                    }
                });
                group_student_list[0].course[sub_index].ungrouped = student;
                student = [];
                all_student_id = [];
                session_setting.forEach((ses_element) => {
                    ses_element.groups.forEach((group_element) => {
                        all_student_id = all_student_id.concat(group_element._student_ids);
                    });
                });
                const temps = [];
                all_student_id.forEach((element) => {
                    if (temps.indexOf(element.toString()) === -1) {
                        temps.push(element.toString());
                    }
                });
                all_student_id = temps;
                all_student_id = all_student_id.filter(
                    (item) => !sub_element.setting.ungrouped.includes(item.toString()),
                );
                all_student_id.forEach((master_group_element) => {
                    const temp =
                        student_group_check.groups[student_group_row].students[
                            student_group_check.groups[student_group_row].students.findIndex(
                                (i) => i._student_id.toString() === master_group_element.toString(),
                            )
                        ];
                    if (temp) {
                        const data = {};
                        let course_session_index = -1;
                        session_setting.forEach((session_element) => {
                            session_element.groups.forEach((group_element) => {
                                if (
                                    group_element._student_ids.findIndex(
                                        (i) => i.toString() === master_group_element.toString(),
                                    ) !== -1
                                ) {
                                    course_session_index = group_element.group_no;
                                }
                            });
                            data[session_element.session_type.toString()] =
                                course_session_index !== -1 ? course_session_index : '-';
                        });
                        data._student_id = temp._student_id;
                        data.name = temp.name;
                        data.academic_no = temp.academic_no;
                        data.gender = temp.gender;
                        data.mark = temp.mark;
                        data.imported_on = temp.imported_on;
                        data.imported_by = temp.imported_by;
                        student.push(data);
                    }
                });
                group_student_list[0].course[sub_index].grouped = student;
                const un_group = group_student_list[0].course[sub_index].ungrouped;
                const un_student = [];
                for (student_element of un_group) {
                    if (
                        student.findIndex(
                            (i) =>
                                i._student_id.toString() === student_element._student_id.toString(),
                        ) === -1
                    ) {
                        un_student.push(student_element);
                    }
                }
                group_student_list[0].course[sub_index].ungrouped = un_student;
                delete group_student_list[0].course[sub_index].setting;
            }
        });
        Object.assign(objs, { groups_list: g_list });
        Object.assign(objs, { groups_list_capacity: g_count_list });
        Object.assign(objs, { group_student_list });
        res.status(200).send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('STUDENT_GROUP_COURSE_DATA'),
                objs,
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.course_grouping_data = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            'groups.level': req.params.level,
            'groups.term': req.params.batch,
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_row = student_group_check.data.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );

        const group_list = [];
        const course_ind = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.params.course.toString(),
        );
        const master_group_ind = student_group_check.data.groups[student_group_row].courses[
            course_ind
        ].setting.findIndex((i) => i._group_no.toString() === req.params.group_no.toString());
        const course_session_setting =
            student_group_check.data.groups[student_group_row].courses[course_ind].setting[
                master_group_ind
            ].session_setting;
        course_session_setting.forEach((element) => {
            const session = [];
            let obj = {};
            element.groups.forEach((sub_element) => {
                const split = sub_element.group_name.split('-');
                obj = {
                    group_no: sub_element.group_no,
                    group_name: sub_element.group_name,
                    group: split[split.length - 2] + '-' + split[split.length - 1],
                    total: element.no_of_student,
                    occupied: sub_element._student_ids.length,
                };
                session.push(obj);
            });
            group_list.push({
                group_name: element.group_name,
                session_type: element.session_type,
                group: session,
            });
        });
        common_files.com_response(res, 200, true, 'Student Group Setting page', group_list);
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.sub_course_group_change = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc = { status: true, data: [] };
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const master_group_ind = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) =>
                i.gender === req.body.gender &&
                i._group_no.toString() === req.body.master_group.toString(),
        );
        const course_session_setting =
            student_group_data.data.courses[course_ind].setting[master_group_ind].session_setting;

        req.body.delivery_group.forEach(async (element, index) => {
            const session =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session.groups.forEach((sub_element) => {
                if (sub_element._student_ids.findIndex((i) => i === req.body._student_id) !== -1) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids': {
                            $in: req.body._student_id,
                        },
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i._course_id': req.body._course_id },
                        {
                            'j._group_no': parseInt(req.body.master_group),
                            'j.gender': req.body.gender,
                        },
                        { 'k.session_type': element.session_type },
                        { 'l.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                        req.body._student_id,
                },
            };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j._group_no': parseInt(req.body.master_group), 'j.gender': req.body.gender },
                    { 'k.session_type': element.session_type },
                    { 'l.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (doc.status) {
                    updateStudentGroupFlatCacheData();
                    await updateStudentGroupRedisKey({
                        courseId: req.body._course_id,
                        level: req.body.level,
                        batch: req.body.batch,
                    });
                    common_files.com_response(
                        res,
                        200,
                        true,
                        'Students are Grouped based on Method',
                        doc.data,
                    );
                } else {
                    common_files.com_response(
                        res,
                        404,
                        false,
                        'Error Unable to Push Student into Group Pls retry',
                        'Error Unable to Push Student into Group Pls retry',
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            404,
            false,
            'Error_id_not_match',
            'Check_Parsing_reference_ID',
        );
    }
};

exports.rotation_course_student_add = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc;
    const user_data = await base_control.get(
        user,
        { _id: req.body._student_id, user_type: constant.EVENT_WHOM.STUDENT, isDeleted: false },
        { name: 1, gender: 1, user_id: 1 },
    );
    const user_checks = await base_control.get(
        user,
        { _id: req.body._user_id, isDeleted: false },
        {},
    );
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const rotation_group = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.body.group_no.toString(),
        );
        const session_settings =
            student_group_data.data.courses[course_ind].setting[rotation_group].session_setting;
        let student_ids = [];
        session_settings[0].groups.forEach((sub_element) => {
            student_ids = student_ids.concat(sub_element._student_ids);
        });
        if (student_ids.findIndex((i) => i === req.body._student_id) === -1) {
            doc = {
                status: true,
                data: student_group_data.data.courses[course_ind].setting[rotation_group],
            };

            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j._group_no': parseInt(req.body.group_no), 'j.gender': req.body.gender },
                ],
            };
            console.log(
                student_group_data.data.students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                ),
            );
            if (
                student_group_data.data.students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                ) === -1
            ) {
                const users = {
                    _student_id: user_data.data._id,
                    academic_no: user_data.data.user_id,
                    name: user_data.data.name,
                    gender: user_data.data.gender,
                    mark: req.body.mark,
                    imported_on: common_fun.timestampNow(),
                    _imported_by: req.body._user_id,
                    imported_by: user_checks.data.name,
                };
                objs = {
                    $push: {
                        'courses.$[i].setting.$[j].ungrouped': req.body._student_id,
                        students: users,
                    },
                };
            } else {
                objs = {
                    $push: { 'courses.$[i].setting.$[j].ungrouped': req.body._student_id },
                };
            }
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (doc.status) {
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                });
                common_files.com_response(
                    res,
                    200,
                    true,
                    'Student Group Manual Student Inserted in particular group',
                    doc.data,
                );
            } else {
                common_files.com_response(
                    res,
                    404,
                    false,
                    'Unable to Push Student manually retry it',
                    'Unable to Push Student manually retry it',
                );
            }
        } else {
            common_files.com_response(
                res,
                404,
                false,
                'This student is already present in this list',
                'This student is already present in this list',
            );
        }
    } else {
        common_files.com_response(
            res,
            404,
            false,
            'Error_id_not_match',
            'Check_Parsing_reference_ID',
        );
    }
};

exports.rotation_course_student = async (req, res) => {
    const query = { _id: ObjectId(req.body._id), 'master.term': req.body.batch, isDeleted: false };
    const project = {};
    let doc;
    // let user_data = await base_control.get(user, { _id: req.body._student_id, user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false }, { name: 1, gender: 1, user_id: 1 });
    // let user_checks = await base_control.get(user, { _id: req.body._user_id, 'isDeleted': false }, {});
    const student_group_data = await base_control.get(student_group, query, project);
    if (student_group_data.status) {
        const course_ind = student_group_data.data.courses.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        const rotation_group = student_group_data.data.courses[course_ind].setting.findIndex(
            (i) => i._group_no.toString() === req.body.group_no.toString(),
        );
        const session_settings =
            student_group_data.data.courses[course_ind].setting[rotation_group].session_setting;
        let student_ids = [];
        session_settings[0].groups.forEach((sub_element) => {
            student_ids = student_ids.concat(sub_element._student_ids);
        });
        if (student_ids.findIndex((i) => i.toString() === req.body._student_id.toString()) === -1) {
            // doc = { status: true, data: student_group_data.data.courses[course_ind].setting[rotation_group] };
            filter = {
                arrayFilters: [
                    { 'i._course_id': req.body._course_id },
                    { 'j._group_no': parseInt(req.body.group_no), 'j.gender': req.body.gender },
                ],
            };
            objs = {
                $pull: {
                    'courses.$[i].setting.$[j].ungrouped': req.body._student_id,
                },
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (doc.status) {
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                });
                common_files.com_response(
                    res,
                    200,
                    true,
                    'Student Removed from Rotation group',
                    doc.data,
                );
            } else {
                common_files.com_response(
                    res,
                    404,
                    false,
                    'Unable to Push Student manually retry it',
                    'Unable to Push Student manually retry it',
                );
            }
        } else {
            session_settings.forEach(async (element, index) => {
                let group_index = 0;
                element.groups.forEach((sub_element) => {
                    if (
                        sub_element._student_ids.findIndex(
                            (i) => i.toString() === req.body._student_id.toString(),
                        ) !== -1
                    ) {
                        group_index = sub_element.group_no;
                    }
                });
                if (group_index !== -1) {
                    objs = {
                        $pull: {
                            'courses.$[i].setting.$[j].session_setting.$[k].groups.$[l]._student_ids':
                                {
                                    $in: req.body._student_id,
                                },
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i._course_id': req.body._course_id },
                            {
                                'j._group_no': parseInt(req.body.group_no),
                                'j.gender': req.body.gender,
                            },
                            { 'k.session_type': element.session_type },
                            { 'l.group_no': group_index },
                        ],
                    };
                    doc = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }
                // doc = { status: true, data: objs };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
                if (session_settings.length === index + 1) {
                    if (doc.status) {
                        updateStudentGroupFlatCacheData();
                        await updateStudentGroupRedisKey({
                            courseId: req.body._course_id,
                            level: req.body.level,
                            batch: req.body.batch,
                        });
                        // filter = {
                        //     arrayFilters: [
                        //         { 'j.group_no': req.body.group_no, 'j.gender': req.body.gender }
                        //     ]
                        // };
                        // objs = {
                        //     $pull: {
                        //         'students': { _student_id: (req.body._student_id).toString() },
                        //         'rotation_group_setting.$[j]': { _student_ids: (req.body._student_id).toString() }
                        //     }
                        // };
                        // console.log(await base_control.update_condition_array_filter(student_group, query, objs, filter));
                        common_files.com_response(
                            res,
                            200,
                            true,
                            'Student Removed from Course',
                            doc.data,
                        );
                    } else {
                        common_files.com_response(
                            res,
                            404,
                            false,
                            'Unable to Push Student manually retry it',
                            'Unable to Push Student manually retry it',
                        );
                    }
                }
            });
            // common_files.com_response(res, 404, false, "This student is not present in Group", 'This student is not present in Group');
        }
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.fyd_course_std_delete = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        'groups.level': req.body.level,
        'groups.term': req.body.batch,
        isDeleted: false,
    };
    const student_group_data = await base_control.get(student_group, query, {});
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_data.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const group_course_row = student_group_data.data.groups[student_group_row].courses.findIndex(
        (i) => i._course_id.toString() === req.body._course_id.toString(),
    );
    if (group_course_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('COURSE_NOT_FOUND'),
                    req.t('COURSE_NOT_FOUND'),
                ),
            );
    const group_course_session =
        student_group_data.data.groups[student_group_row].courses[group_course_row].session_types;
    let doc;
    const student_ids = req.body._student_ids;
    for (element of group_course_session) {
        for (sub_element of student_ids) {
            const objs = {
                $pull: {
                    'groups.$[i].courses.$[j].setting.$[k].ungrouped': {
                        $in: req.body._student_ids,
                    },
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        {
                            $in: req.body._student_ids,
                        },
                },
                $push: {
                    'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_ids,
                },
                $set: {
                    'groups.$[i].students.$[s].course_group_status.$[c].status': constant.PENDING,
                },
            };
            const filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': ObjectId(req.body._course_id) },
                    {
                        'k.gender': req.query.mode ? req.query.mode : req.body.gender,
                        'k._group_no': parseInt(req.body.master_group),
                    },
                    { 'l.session_type': element.symbol },
                    { 'm._student_ids': ObjectId(sub_element) },
                    { 's._student_id': ObjectId(sub_element) },
                    { 'c._course_id': ObjectId(req.body._course_id) },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
    }
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                    req.t('ERROR_UNABLE_TO_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                ),
            );

    let studentCourseStatus = false;
    const student_group_dataRe = await base_control.get(student_group, query, {});
    const student_group_rowRe = student_group_dataRe.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_dataRe.data.groups[student_group_rowRe].group_mode === 'rotation') {
        for (rotationElement of student_group_dataRe.data.groups[student_group_rowRe]
            .rotation_group_setting) {
            if (
                !studentCourseStatus &&
                student_ids.filter((ele) =>
                    rotationElement._student_ids.find((ele2) => ele2.toString() === ele.toString()),
                ).length !== 0
            )
                studentCourseStatus = true;
        }
    } else if (student_group_dataRe.data.groups[student_group_rowRe].group_mode === 'foundation') {
        const fydData = student_group_dataRe.data.groups[student_group_rowRe].group_setting.find(
            (ele) =>
                req.query.mode ? req.query.mode === ele.gender : ele.gender === req.body.gender,
        );
        for (studentElement of fydData.groups) {
            if (
                !studentCourseStatus &&
                student_ids.filter((ele) =>
                    studentElement._student_ids.find((ele2) => ele2.toString() === ele.toString()),
                ).length !== 0
            )
                studentCourseStatus = true;
        }
    }
    for (courseElement of student_group_dataRe.data.groups[student_group_rowRe].courses) {
        const groupData = courseElement.setting.filter((ele) =>
            req.query.mode ? req.query.mode === ele.gender : ele.gender === req.body.gender,
        );
        for (settingElement of groupData) {
            if (
                !studentCourseStatus &&
                student_ids.filter((ele) =>
                    settingElement.ungrouped.find((ele2) => ele2.toString() === ele.toString()),
                ).length !== 0
            )
                studentCourseStatus = true;
            for (sessionElement of settingElement.session_setting[0].groups) {
                if (
                    !studentCourseStatus &&
                    student_ids.filter((ele) =>
                        sessionElement._student_ids.find(
                            (ele2) => ele2.toString() === ele.toString(),
                        ),
                    ).length !== 0
                )
                    studentCourseStatus = true;
            }
        }
    }

    // Remove Student data in Level
    const studentCourseData = common_files.clone(
        student_group_dataRe.data.groups[student_group_rowRe].students,
    );
    const bulkWriteObj = [];
    for (studentElement of studentCourseData) {
        if (
            !studentCourseStatus &&
            req.body._student_ids.find(
                (ele) => ele.toString() === studentElement._student_id.toString(),
            ) &&
            (studentElement.course_group_status.length === 1 ||
                studentElement.course_group_status.filter(
                    (ele) => ele.status === constant.PUBLISHED,
                ).length < 2) &&
            studentElement.master_group_status === constant.PENDING
        ) {
            bulkWriteObj.push({
                updateOne: {
                    filter: query,
                    update: {
                        $pull: {
                            'groups.$[i].students': {
                                _student_id: ObjectId(studentElement._student_id),
                            },
                        },
                    },
                    arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
                },
            });
        }
    }
    if (bulkWriteObj.length)
        console.log(
            'Remove Student from Group ',
            await base_control.bulk_write(student_group, bulkWriteObj),
        );
    await removeStudentSchedule(
        student_group_dataRe.data._institution_id,
        student_group_dataRe.data._institution_calendar_id,
        req.body.batch,
        req.body.level,
        req.body._course_id,
        req.body._student_ids,
    );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
            ),
        );
};

async function staffNotification(_course_id, batch, studentName, delivery_tags) {
    //Course Details
    const courseDetails = await base_control.get(
        course,
        {
            _id: ObjectId(_course_id),
            isDeleted: false,
        },
        {
            course_name: 1,
            course_code: 1,
            coordinators: 1,
        },
    );
    const staffData = courseDetails.data.coordinators.find((ele) => ele.term === batch);
    if (staffData) {
        const user_data = await base_control.get(
            user,
            {
                _id: staffData._user_id,
                isDeleted: false,
            },
            { name: 1, user_id: 1, email: 1, mobile: 1 },
        );
        if (user_data.status) {
            const data = user_data.data;
            if (data) {
                const name = common_fun.nameFormatter(data.name);
                const term = batch.charAt(0).toUpperCase() + batch.slice(1);
                const email_message =
                    '<p>Dear ' +
                    name +
                    ',<br>' +
                    common_fun.emailGreetingContent() +
                    '<br>' +
                    'This email is to inform that ' +
                    courseDetails.data.course_name +
                    ' - ' +
                    courseDetails.data.course_code +
                    ' ( ' +
                    term +
                    ' )' +
                    ' course groups has been changed for the Student (' +
                    studentName +
                    '):<br>' +
                    delivery_tags +
                    '<br>For more details, kindly visit the Academic Affairs Office.<br>Thank you,<br>' +
                    'Thank you,<br>' +
                    common_fun.emailRegardsContent() +
                    '</p>';
                const sms_message =
                    'DigiClass - Alert : (' +
                    studentName +
                    ')  group has been  changed for ' +
                    courseDetails.data.course_name +
                    ' - ' +
                    courseDetails.data.course_code +
                    ' ( ' +
                    term +
                    ' )' +
                    ' ,Kindly check the mail.';
                if (data.email !== undefined && data.email !== '')
                    common_fun.send_email(data.email, 'DigiClass Alert', email_message);
                if (data.mobile !== undefined && data.mobile !== '')
                    common_fun.send_sms(data.mobile, sms_message);
            }
        }
    }
}

exports.fyd_course_std_edit = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body._id), isDeleted: false };
        const project = {};
        let doc = { status: true, data: [] };
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_level === -1)
            return res
                .status(400)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.body._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(400)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        400,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const student_master_group = student_group_data.data.groups[student_group_level].courses[
            student_group_course
        ].setting.findIndex(
            (i) =>
                i._group_no.toString() === req.body.master_group.toString() &&
                (req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender),
        );
        if (student_master_group === -1) {
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                    ),
                );
        }
        const course_session_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting[student_master_group].session_setting;
        if (course_session_setting.length === 0) {
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('NO_SESSION_GROUPS_CREATED'),
                        req.t('NO_SESSION_GROUPS_CREATED'),
                    ),
                );
        }
        let excess = 0;
        let capacity = 0;
        let strength = 0;
        const validation = [];
        for (let index = 0; index < req.body.delivery_group.length; index++) {
            const element = req.body.delivery_group[index].session_type;
            const grp_nos = req.body.delivery_group[index].group_no;
            const session_type_ind =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element)
                ];
            if (session_type_ind === undefined)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                        ),
                    );
            capacity = session_type_ind.no_of_student;
            excess = student_group_data.data.groups[student_group_level].course_excess_count
                ? student_group_data.data.groups[student_group_level].course_excess_count
                : 0;
            const new_grp_ind = session_type_ind.groups.findIndex((i) => i.group_no === grp_nos);
            if (new_grp_ind === -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('GROUP_NO_NOT_FOUND'),
                            req.t('GROUP_NO_NOT_FOUND'),
                        ),
                    );
            console.log(
                session_type_ind.groups[new_grp_ind]._student_ids.findIndex(
                    (i) => i.toString() === req.body._student_id.toString(),
                ),
            );
            strength =
                session_type_ind.groups[new_grp_ind]._student_ids.findIndex(
                    (i) => i.toString() === req.body._student_id.toString(),
                ) === -1
                    ? session_type_ind.groups[new_grp_ind]._student_ids.length + 1
                    : session_type_ind.groups[new_grp_ind]._student_ids.length;
            console.log(element, ' ', strength, ' ', capacity);
            if (!(strength <= excess + capacity)) {
                validation.push({
                    session_type: element,
                    message: 'capacity exceeded increase extra allowed',
                });
            }
        }
        if (validation.length !== 0)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                        validation,
                    ),
                );
        req.body.delivery_group.forEach(async (element, index) => {
            const session_types =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session_types.groups.findIndex((sub_element) => {
                if (
                    sub_element._student_ids.findIndex(
                        (i) => i.toString() === req.body._student_id.toString(),
                    ) !== -1
                ) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                            ObjectId(req.body._student_id),
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._course_id': ObjectId(req.body._course_id) },
                        {
                            'k.gender': req.query.mode ? req.query.mode : req.body.gender,
                            'k._group_no': parseInt(req.body.master_group),
                        },
                        { 'l.session_type': element.session_type },
                        { 'm.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        ObjectId(req.body._student_id),
                },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': ObjectId(req.body._course_id) },
                    {
                        'k.gender': req.query.mode ? req.query.mode : req.body.gender,
                        'k._group_no': parseInt(req.body.master_group),
                    },
                    { 'l.session_type': element.session_type },
                    { 'm.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (!doc.status)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('ERROR_UNABLE_TO_EDIT_STUDENT'),
                                req.t('ERROR_UNABLE_TO_EDIT_STUDENT'),
                            ),
                        );

                await removeStudentSchedule(
                    student_group_data.data._institution_id,
                    student_group_data.data._institution_calendar_id,
                    req.body.batch,
                    req.body.level,
                    req.body._course_id,
                    [req.body._student_id],
                );
                await addingStudentSchedule({
                    _institution_id: student_group_data.data._institution_id,
                    institutionCalendarId: student_group_data.data._institution_calendar_id,
                    studentGroup:
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].setting[student_master_group],
                    studentData: student_group_data.data.groups[student_group_level].students,
                    studentIds: [req.body._student_id],
                    deliveryGroups: req.body.delivery_group,
                    courseId: req.body._course_id,
                    status: req.body.status,
                });
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                });
                return res
                    .status(200)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            200,
                            true,
                            req.t('SUCCESSFULLY EDITED STUDENT IN FOUNDATION COURSE GROUP'),
                            req.t('SUCCESSFULLY EDITED STUDENT IN FOUNDATION COURSE GROUP'),
                        ),
                    );
            }
        });

        //Need to send Notification to Student
        const student_loc_status = student_group_data.data.groups[
            student_group_level
        ].students.findIndex((i) => i._student_id.toString() === req.body._student_id.toString());
        const temp =
            student_group_data.data.groups[student_group_level].students[student_loc_status];
        // if (temp.course_group_status !== 'pending') {
        const course_status = temp.course_group_status.findIndex(
            (i) => i._course_id.toString() === req.body._course_id.toString(),
        );
        if (course_status !== -1 && temp.course_group_status[course_status].status !== 'pending') {
            const user_data = await base_control.get(
                user,
                {
                    _id: ObjectId(req.body._student_id),
                    user_type: constant.EVENT_WHOM.STUDENT,
                    isDeleted: false,
                },
                { name: 1, user_id: 1, email: 1, mobile: 1 },
            );
            const email_message =
                '<p>Dear v_name,<br>' +
                common_fun.emailGreetingContent() +
                '<br>' +
                'This email is to inform that your <b>course_code</b> course groups have been changed as follows:<br>' +
                'course_delivery<br>' +
                'For more details, kindly visit the Academic Affairs Office.<br><br>Thank you, <br>' +
                common_fun.emailRegardsContent() +
                '' +
                '</p>';
            const sms_message =
                'Your course group for ' +
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .course_name +
                ' - ' +
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .course_no +
                ' is changed. Please check your registered email for details. For any query, contact course coordinator- ' +
                INSTITUTION_NAME +
                '';

            let delivery_tags = '';
            for (element of req.body.delivery_group) {
                delivery_tags =
                    delivery_tags +
                    'Delivery : ' +
                    element.session_type +
                    ' - ' +
                    ' Group : ' +
                    element.group_no +
                    '<br>';
            }
            const name = user_data.data.name.middle
                ? user_data.data.name.first +
                  ' ' +
                  user_data.data.name.middle +
                  ' ' +
                  user_data.data.name.last
                : user_data.data.name.first + ' ' + user_data.data.name.last;
            const replace_data = {
                v_name: name,
                course_code:
                    student_group_data.data.groups[student_group_level].courses[
                        student_group_course
                    ].course_name +
                    ' - ' +
                    student_group_data.data.groups[student_group_level].courses[
                        student_group_course
                    ].course_no,
                course_delivery: delivery_tags,
            };
            const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
            const mail_message = email_message.replace(re, (matched) => replace_data[matched]);
            if (user_data.data.email !== undefined && user_data.data.email !== '')
                common_fun.send_email(
                    user_data.data.email,
                    'DigiClass Alert - Your Course ' +
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].course_name +
                        ' - ' +
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].course_no +
                        ' Groups Changed',
                    mail_message,
                );
            const message = 'DigiClass - ' + sms_message;
            if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
                common_fun.send_sms(user_data.data.mobile, message);

            // Send Notification Course Coordinator
            staffNotification(req.body._course_id, req.body.batch, name, delivery_tags);
        }
        // }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.get_clone_group = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_FOUND'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        let delivery_symbol = [];
        const objs = {};
        const group = student_group_data.data.groups.findIndex(
            (i) =>
                i.level.toString() === req.params.level.toString() && i.term === req.params.batch,
        );
        if (group === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_data = student_group_data.data.groups[group].courses.findIndex(
            (i) => i._course_id.toString() === req.params._course_id.toString(),
        );
        if (course_data === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const session_types =
            student_group_data.data.groups[group].courses[course_data].session_types;
        const symbol = session_types.map((i) => i.symbol);
        delivery_symbol = symbol.sort();
        let ses_type = [];
        const id = [];
        const level_courses = student_group_data.data.groups[group].courses;
        for (let i = 0; i < level_courses.length; i++) {
            if (level_courses[i].session_types.length === delivery_symbol.length) {
                ses_type = level_courses[i].session_types.map((j) => j.symbol).sort();
                if (
                    ses_type.toString() === delivery_symbol.toString() &&
                    level_courses[i]._course_id.toString() !== req.params._course_id.toString()
                ) {
                    id.push(level_courses[i]._course_id);
                }
            }
        }

        // let ses_type = []; let id = [];
        // for (let index = 0; index < delivery_symbol.length; index++) {
        //     const element = delivery_symbol[index].length;
        //     let level_courses = student_group_data.data.groups[group].courses;
        //     for (let i = 0; i < level_courses.length; i++) {
        //         const session_length = level_courses[i].session_types.length === element;
        //         if (session_length === true) {
        //             ses_type.push(level_courses[i].session_types.map(i => i.symbol))
        //             ses_type.forEach(ele => {
        //                 const element = ele === delivery_symbol.toString();
        //                 if (element === true && (level_courses[i]._course_id).toString() !== (req.params._course_id).toString()) {
        //                     id.push(level_courses[i]._course_id)
        //                 }
        //             })
        //         }
        //     }
        // }
        const final = [...new Set(id)];
        const courses = [];
        if (final.length === 0)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_NO_MATCHING_COURSES'),
                        req.t('ERROR_NO_MATCHING_COURSES'),
                    ),
                );
        final.forEach((ele) => {
            const result = student_group_data.data.groups[group].courses.findIndex(
                (i) => i._course_id.toString() === ele.toString(),
            );
            const res_course = student_group_data.data.groups[group].courses[result];
            courses.push({
                course_id: res_course._course_id,
                course_no: res_course.course_no,
                course_name: res_course.course_name,
            });
        });
        Object.assign(objs, { courses });

        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('GETTING_COURSES_WITH_MATCHING_DELIVERY_TYPES'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.add_student_course = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        'groups.level': req.body.level,
        'groups.term': req.body.batch,
        isDeleted: false,
    };
    const student_group_check = await base_control.get(student_group, query, {});
    if (!student_group_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_check.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const user_checks = await base_control.get_list(
        user,
        { _id: { $in: [req.body._user_id, req.body._student_id] }, isDeleted: false },
        {},
    );
    if (!user_checks.status && user_checks.data.length !== 2)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR STUDENT ACADEMIC NO NOT MATCH'),
                    req.t('ERROR STUDENT ACADEMIC NO NOT MATCH'),
                ),
            );
    const student_loc = user_checks.data.findIndex(
        (i) => i._id.toString() === req.body._student_id.toString(),
    );
    const staff_loc = user_checks.data.findIndex(
        (i) => i._id.toString() === req.body._user_id.toString(),
    );
    if (student_loc === -1 || staff_loc === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR STUDENT ACADEMIC NO NOT MATCH'),
                    req.t('ERROR STUDENT ACADEMIC NO NOT MATCH'),
                ),
            );
    let objs = {};
    const course_status = [];
    const courses = student_group_check.data.groups[student_group_row].courses.map(
        (i) => i._course_id,
    );
    for (cro of courses) {
        course_status.push({ _course_id: cro, status: constant.PENDING });
    }
    if (
        student_group_check.data.groups[student_group_row].students.findIndex(
            (i) => i._student_id.toString() === req.body._student_id.toString(),
        ) === -1
    ) {
        objs = {
            $push: {
                'groups.$[i].students': {
                    _student_id: user_checks.data[student_loc]._id,
                    academic_no: user_checks.data[student_loc].user_id,
                    name: user_checks.data[student_loc].name,
                    gender: user_checks.data[student_loc].gender,
                    mark: req.body.mark,
                    imported_on: common_fun.timestampNow(),
                    _imported_by: req.body._user_id,
                    imported_by: user_checks.data[staff_loc].name,
                    master_group_status: constant.PENDING,
                    course_group_status: course_status,
                },
                'groups.$[i].courses.$[j].setting.$[k].ungrouped': req.body._student_id,
            },
            $pull: {
                'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_id,
            },
        };
    } else {
        objs = {
            $push: { 'groups.$[i].courses.$[j].setting.$[k].ungrouped': req.body._student_id },
            $pull: { 'groups.$[i].courses.$[j]._removed_student_ids': req.body._student_id },
            $set: { 'groups.$[i].students.$[s].course_group_status.$[m].status': 'pending' },
        };
    }
    const filter = {
        arrayFilters: [
            { 'i.term': req.body.batch, 'i.level': req.body.level },
            { 'j._course_id': req.body._course_id },
            {
                'k.gender': req.query.mode ? req.query.mode : user_checks.data[student_loc].gender,
                'k._group_no': parseInt(req.body.group_no),
            },
            { 's._student_id': req.body._student_id },
            { 'm._course_id': req.body._course_id },
        ],
    };
    const doc = await base_control.update_condition_array_filter(
        student_group,
        query,
        objs,
        filter,
    );
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_ADD_STUDENT'),
                    doc.data,
                ),
            );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
            ),
        );
};

exports.fyd_course_clone = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        'groups.level': req.body.level,
        'groups.term': req.body.batch,
        isDeleted: false,
    };
    const student_group_check = await base_control.get(student_group, query, {});
    if (!student_group_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_check.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const course_ids = student_group_check.data.groups[student_group_row].courses.map((i) =>
        i._course_id.toString(),
    );
    const req_course = [...req.body._to_course_ids];
    req_course.push(req.body._from_course_id);
    if (!req_course.every((v) => course_ids.indexOf(v.toString()) >= 0))
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('COURSE_NOT_FOUND DO VERIFY COURSE SELECTION'),
                    req.t('UNABLE_TO_FIND_COURSE DO VERIFY COURSE SELECTION'),
                ),
            );
    let doc = {};
    let objs = {};
    let filter = {};
    const from_course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
        (i) => i._course_id.toString() === req.body._from_course_id.toString(),
    );
    if (from_course_loc === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_COURSE'),
                    req.t('UNABLE_TO_FIND_COURSE'),
                ),
            );
    const from_course_data =
        student_group_check.data.groups[student_group_row].courses[from_course_loc].setting;
    for (element of req.body._to_course_ids) {
        const to_course_loc = student_group_check.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === element.toString(),
        );
        if (to_course_loc !== -1) {
            const to_course_data =
                student_group_check.data.groups[student_group_row].courses[to_course_loc];
            const setting_datas = [];
            for (course_element of from_course_data) {
                const name =
                    student_group_check.data.groups[student_group_row].group_name +
                    '-' +
                    to_course_data.course_no +
                    '-G' +
                    course_element._group_no;
                const session_setting = [];
                if (
                    req.query.mode
                        ? req.query.mode === course_element.gender
                        : course_element.gender === req.body.gender
                ) {
                    //Need to Add Rotation Course clone concept
                    if (student_group_check.data.groups[student_group_row].rotation === 'yes') {
                        const group_students_loc = student_group_check.data.groups[
                            student_group_row
                        ].rotation_group_setting.findIndex(
                            (i) =>
                                (req.query.mode
                                    ? req.query.mode === i.gender
                                    : i.gender === req.body.gender) &&
                                i.group_no === course_element._group_no,
                        );
                        // let group_groups_loc = student_group_check.data.groups[student_group_row].rotation_group_setting[group_students_loc].groups.findIndex(i => i.group_no === course_element._group_no);
                        const group_students =
                            student_group_check.data.groups[student_group_row]
                                .rotation_group_setting[group_students_loc]._student_ids;
                        for (session_element of course_element.session_setting) {
                            const groups = [];
                            for (groups_element of session_element.groups) {
                                let students = groups_element._student_ids;
                                students = students.filter((item) =>
                                    group_students.includes(item.toString()),
                                );
                                groups.push({
                                    _student_ids: students,
                                    group_no: groups_element.group_no,
                                    group_name:
                                        name +
                                        '-' +
                                        session_element.session_type +
                                        '-' +
                                        groups_element.group_no,
                                });
                            }
                            session_setting.push({
                                group_name: name + '-' + session_element.session_type,
                                delivery_type: session_element.delivery_type,
                                session_type: session_element.session_type,
                                no_of_group: session_element.no_of_group,
                                no_of_student: session_element.no_of_student,
                                groups,
                            });
                        }
                        setting_datas.push({
                            ungrouped: [],
                            _group_no: course_element._group_no,
                            gender: course_element.gender,
                            session_setting,
                        });
                    } else {
                        const group_students_loc = student_group_check.data.groups[
                            student_group_row
                        ].group_setting.findIndex((i) =>
                            req.query.mode
                                ? req.query.mode === i.gender
                                : i.gender === req.body.gender,
                        );
                        const group_groups_loc = student_group_check.data.groups[
                            student_group_row
                        ].group_setting[group_students_loc].groups.findIndex(
                            (i) => i.group_no === course_element._group_no,
                        );
                        const group_students =
                            student_group_check.data.groups[student_group_row].group_setting[
                                group_students_loc
                            ].groups[group_groups_loc]._student_ids;
                        for (session_element of course_element.session_setting) {
                            const groups = [];
                            for (groups_element of session_element.groups) {
                                let students = groups_element._student_ids;
                                students = students.filter((item) =>
                                    group_students.includes(item.toString()),
                                );
                                groups.push({
                                    _student_ids: students,
                                    group_no: groups_element.group_no,
                                    group_name:
                                        name +
                                        '-' +
                                        session_element.session_type +
                                        '-' +
                                        groups_element.group_no,
                                });
                            }
                            session_setting.push({
                                group_name: name + '-' + session_element.session_type,
                                delivery_type: session_element.delivery_type,
                                session_type: session_element.session_type,
                                no_of_group: session_element.no_of_group,
                                no_of_student: session_element.no_of_student,
                                groups,
                            });
                        }
                        setting_datas.push({
                            ungrouped: [],
                            _group_no: course_element._group_no,
                            gender: course_element.gender,
                            session_setting,
                        });
                    }
                } else {
                    setting_datas.push(course_element);
                }
            }
            objs = {
                $set: { 'groups.$[i].courses.$[j].setting': setting_datas },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': to_course_data._course_id },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
    }
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_ADD_STUDENT'),
                    doc.data,
                ),
            );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
            ),
        );
};

exports.fyd_course_group_export = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        };
        // const student_group_data = await base_control.get(student_group, query, {});
        const studentGroupData = await student_group
            .findOne(query)
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const student_group_row = studentGroupData.groups.findIndex(
            (i) => i.term === req.params.batch && i.level === req.params.level,
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_loc = studentGroupData.groups[student_group_row].courses.findIndex(
            (i) => i._course_id._id.toString() === req.params.course.toString(),
        );
        let group_loc;
        const course_data = studentGroupData.groups[student_group_row].courses[course_loc];
        if (
            req.params.fyd_group &&
            studentGroupData.groups[student_group_row].courses[course_loc].course_type !==
                'elective'
        ) {
            group_loc = studentGroupData.groups[student_group_row].courses[
                course_loc
            ].setting.findIndex(
                (i) =>
                    i._group_no.toString() === req.params.fyd_group.toString() &&
                    (req.query.mode ? req.query.mode === i.gender : i.gender === req.params.gender),
            );
        } else {
            group_loc = studentGroupData.groups[student_group_row].courses[
                course_loc
            ].setting.findIndex((i) =>
                req.query.mode ? req.query.mode === i.gender : i.gender === req.params.gender,
            );
        }
        const delivery_loc = studentGroupData.groups[student_group_row].courses[course_loc].setting[
            group_loc
        ].session_setting.findIndex(
            (i) => i.session_type.toString() === req.params.delivery.toString(),
        );
        const course_delivery_group = studentGroupData.groups[student_group_row].courses[
            course_loc
        ].setting[group_loc].session_setting[delivery_loc].groups.findIndex(
            (i) => i.group_no.toString() === req.params.d_group_no.toString(),
        );
        const course_delivery =
            studentGroupData.groups[student_group_row].courses[course_loc].setting[group_loc]
                .session_setting[delivery_loc].groups[course_delivery_group];
        const objs = {
            _id: studentGroupData._id,
            _institution_calendar_id: studentGroupData._institution_calendar_id,
            group_name: course_delivery.group_name,
            master: studentGroupData.master,
            course_name: course_data.course_name,
            versionNo: course_data._course_id.versionNo || 1,
            versioned: course_data._course_id.versioned || false,
            versionName: course_data._course_id.versionName || '',
            versionedFrom: course_data._course_id.versionedFrom || null,
            versionedCourseIds: course_data._course_id.versionedCourseIds || [],
            course_no: course_data.course_no,
            course_type: course_data.course_type,
            delivery_type:
                course_data.session_types[
                    course_data.session_types.findIndex((i) => i.symbol === req.params.delivery)
                ].session_type,
        };
        const students = [];
        course_delivery._student_ids.forEach((element) => {
            students.push(
                studentGroupData.groups[student_group_row].students[
                    studentGroupData.groups[student_group_row].students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ],
            );
        });
        Object.assign(objs, { students });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('FOUNDATION_COURSE_DELIVERY_GROUP_STUDENT_DATA'),
                    objs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.std_group_change = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.term === req.body.batch && i.level === req.body.level,
        );
        if (student_group_level === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.body._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(400)
                .send(
                    common_files.response_function(
                        res,
                        400,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        const student_master_group = student_group_data.data.groups[student_group_level].courses[
            student_group_course
        ].setting.findIndex((i) =>
            req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
        );
        if (student_master_group === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                        req.t('NO_SESSION_SETTINGS_ADDED'),
                    ),
                );
        const course_session_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting[student_master_group].session_setting;
        if (course_session_setting.length === 0)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_SESSION_GROUPS_CREATED'),
                        req.t('NO_SESSION_GROUPS_CREATED'),
                    ),
                );
        let excess = 0;
        let capacity = 0;
        let strength = 0;
        const validation = [];
        for (let index = 0; index < req.body.delivery_group.length; index++) {
            const element = req.body.delivery_group[index].session_type;
            const grp_nos = req.body.delivery_group[index].group_no;
            const session_type_ind =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element)
                ];
            if (session_type_ind === undefined)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                            req.t('SESSION_TYPE_NOT_MATCHING'),
                        ),
                    );
            capacity = session_type_ind.no_of_student;
            excess = student_group_data.data.groups[student_group_level].course_excess_count
                ? student_group_data.data.groups[student_group_level].course_excess_count
                : 0;
            const new_grp_ind = session_type_ind.groups.findIndex((i) => i.group_no === grp_nos);
            if (new_grp_ind === -1)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('GROUP_NO_NOT_FOUND'),
                            req.t('GROUP_NO_NOT_FOUND'),
                        ),
                    );
            strength = session_type_ind.groups[new_grp_ind]._student_ids.length + 1;
            if (!(strength <= excess + capacity))
                validation.push({
                    session_type: element,
                    message: 'capacity exceeded increase extra allowed',
                });
        }
        if (validation.length !== 0)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                        validation,
                    ),
                );
        req.body.delivery_group.forEach(async (element, index) => {
            const session_types =
                course_session_setting[
                    course_session_setting.findIndex((i) => i.session_type === element.session_type)
                ];
            let group_index = 0;
            session_types.groups.findIndex((sub_element) => {
                if (
                    sub_element._student_ids.find(
                        (i) => i.toString() === req.body._student_id.toString(),
                    )
                ) {
                    group_index = sub_element.group_no;
                }
            });
            if (group_index !== -1) {
                objs = {
                    $pull: {
                        'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                            ObjectId(req.body._student_id),
                    },
                };
                filter = {
                    arrayFilters: [
                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                        { 'j._course_id': ObjectId(req.body._course_id) },
                        { 'k.gender': req.query.mode ? req.query.mode : req.body.gender },
                        { 'l.session_type': element.session_type },
                        { 'm.group_no': group_index },
                    ],
                };
                doc = await base_control.update_condition_array_filter(
                    student_group,
                    query,
                    objs,
                    filter,
                );
            }
            objs = {
                $push: {
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        ObjectId(req.body._student_id),
                },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': ObjectId(req.body._course_id) },
                    { 'k.gender': req.query.mode ? req.query.mode : req.body.gender },
                    { 'l.session_type': element.session_type },
                    { 'm.group_no': element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
            if (req.body.delivery_group.length === index + 1) {
                if (!doc.status)
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('ERROR_UNABLE_TO_EDIT_STUDENT'),
                                doc.data,
                            ),
                        );

                //Send Notification to User About group change
                //Need to send Notification to Student
                const student_loc_status = student_group_data.data.groups[
                    student_group_level
                ].students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                );
                const temp =
                    student_group_data.data.groups[student_group_level].students[
                        student_loc_status
                    ];
                // if (temp.course_group_status !== 'pending') {
                const course_status = temp.course_group_status.findIndex(
                    (i) => i._course_id.toString() === req.body._course_id.toString(),
                );
                if (
                    course_status !== -1 &&
                    temp.course_group_status[course_status].status !== 'pending'
                ) {
                    const user_data = await base_control.get(
                        user,
                        {
                            _id: ObjectId(req.body._student_id),
                            user_type: constant.EVENT_WHOM.STUDENT,
                            isDeleted: false,
                        },
                        { name: 1, user_id: 1, email: 1, mobile: 1 },
                    );
                    const email_message =
                        '<p>Dear v_name,<br>' +
                        common_fun.emailGreetingContent() +
                        '<br>' +
                        'This email is to inform that your <b>course_code</b> course groups have been changed as follows:<br>' +
                        'course_delivery<br>' +
                        'For more details, kindly visit the Academic Affairs Office.<br><br>Thank you, <br>' +
                        common_fun.emailRegardsContent() +
                        '</p>';
                    const sms_message =
                        'Your course group for ' +
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].course_name +
                        ' - ' +
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].course_no +
                        ' is changed. Please check your registered email for details. For any query, contact course coordinator- ' +
                        INSTITUTION_NAME +
                        '';

                    let delivery_tags = '';
                    for (element of req.body.delivery_group) {
                        delivery_tags =
                            delivery_tags +
                            'Delivery : ' +
                            element.session_type +
                            ' - ' +
                            ' Group : ' +
                            element.group_no +
                            '<br>';
                    }
                    const name = user_data.data.name.middle
                        ? user_data.data.name.first +
                          ' ' +
                          user_data.data.name.middle +
                          ' ' +
                          user_data.data.name.last
                        : user_data.data.name.first + ' ' + user_data.data.name.last;
                    const replace_data = {
                        v_name: name,
                        course_code:
                            student_group_data.data.groups[student_group_level].courses[
                                student_group_course
                            ].course_name +
                            ' - ' +
                            student_group_data.data.groups[student_group_level].courses[
                                student_group_course
                            ].course_no,
                        course_delivery: delivery_tags,
                    };
                    const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                    const mail_message = email_message.replace(
                        re,
                        (matched) => replace_data[matched],
                    );
                    if (user_data.data.email !== undefined && user_data.data.email !== '')
                        common_fun.send_email(
                            user_data.data.email,
                            'DigiClass Alert - Your Course ' +
                                student_group_data.data.groups[student_group_level].courses[
                                    student_group_course
                                ].course_name +
                                ' - ' +
                                student_group_data.data.groups[student_group_level].courses[
                                    student_group_course
                                ].course_no +
                                ' Groups Changed',
                            mail_message,
                        );
                    const message = 'DigiClass - ' + sms_message;
                    if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
                        common_fun.send_sms(user_data.data.mobile, message);
                    staffNotification(req.body._course_id, req.body.batch, name, delivery_tags);
                }
                await removeStudentSchedule(
                    student_group_data.data._institution_id,
                    student_group_data.data._institution_calendar_id,
                    req.body.batch,
                    req.body.level,
                    req.body._course_id,
                    [req.body._student_id],
                );
                await addingStudentSchedule({
                    _institution_id: student_group_data.data._institution_id,
                    institutionCalendarId: student_group_data.data._institution_calendar_id,
                    studentGroup:
                        student_group_data.data.groups[student_group_level].courses[
                            student_group_course
                        ].setting[student_master_group],
                    studentData: student_group_data.data.groups[student_group_level].students,
                    studentIds: [req.body._student_id],
                    deliveryGroups: req.body.delivery_group,
                    courseId: req.body._course_id,
                    status: req.body.status,
                });
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.body._course_id,
                    level: req.body.level,
                    batch: req.body.batch,
                });
                return res
                    .status(200)
                    .send(
                        common_files.response_function(
                            res,
                            200,
                            true,
                            req.t('SUCCESSFULLY_EDITED_STUDENT_IN_GROUP'),
                            req.t('SUCCESSFULLY_EDITED_STUDENT_IN_GROUP'),
                        ),
                    );
            }
        });
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
const getAllCoursesStudents = async (allCourses, courseId) => {
    try {
        let studentsIds = [];
        const courses = allCourses.filter(
            (eleAllCourses) =>
                eleAllCourses._id && eleAllCourses._course_id.toString() !== courseId.toString(),
        );
        if (courses.length > 0) {
            for (eleCourses of courses) {
                if (eleCourses.setting.length > 0) {
                    for (eleSetting of eleCourses.setting) {
                        studentsIds = [...studentsIds, ...eleSetting.ungrouped];
                        if (eleSetting.session_setting.length > 0) {
                            for (eleSessionSetting of eleSetting.session_setting) {
                                if (eleSessionSetting.groups.length > 0) {
                                    for (eleGroups of eleSessionSetting.groups) {
                                        studentsIds = [...studentsIds, ...eleGroups._student_ids];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return studentsIds;
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.individual_std_delete = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        _institution_id: ObjectId(req.headers._institution_id),
        _id: ObjectId(req.body._id),
        isDeleted: false,
    };
    const student_group_data = await base_control.get(student_group, query, {});
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_data.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level === req.body.level,
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const group_course_row = student_group_data.data.groups[student_group_row].courses.findIndex(
        (i) => i._course_id.toString() === req.body._course_id.toString(),
    );
    // let group_course_session = student_group_data.data.groups[student_group_row].courses[group_course_row].session_types;
    let doc = { status: true, data: [] };
    // let student_ids = req.body._student_ids;

    //Get All course students except this course
    const studentIds = await getAllCoursesStudents(
        student_group_data.data.groups[student_group_row].courses,
        req.body._course_id,
    );
    const rem_student_ids = [];
    for (id of req.body._student_ids) {
        const ind = studentIds.findIndex(
            (eleStudentId) => eleStudentId.toString() === id.toString(),
        );
        if (ind == -1) rem_student_ids.push(id);
    }
    //console.log(studentIds);
    //console.log(rem_student_ids);

    let objs = {};
    if (rem_student_ids.length > 0) {
        objs = {
            $pull: {
                'groups.$[i].courses.$[j].setting.$[k].ungrouped': { $in: req.body._student_ids },
                'groups.$[i].students': { _student_id: { $in: rem_student_ids } },
            },
        };
    } else {
        objs = {
            $pull: {
                'groups.$[i].courses.$[j].setting.$[k].ungrouped': { $in: req.body._student_ids },
            },
        };
    }

    let filter = {
        arrayFilters: [
            { 'i.term': req.body.batch, 'i.level': req.body.level },
            { 'j._course_id': req.body._course_id },
            { 'k.gender': req.query.mode ? req.query.mode : req.body.gender },
        ],
    };
    await base_control.update_condition_array_filter(student_group, query, objs, filter);
    const group_course_gender_loc = student_group_data.data.groups[student_group_row].courses[
        group_course_row
    ].setting.findIndex((i) =>
        req.query.mode ? req.query.mode === i.gender : i.gender === req.body.gender,
    );
    for (element of student_group_data.data.groups[student_group_row].courses[group_course_row]
        .setting[group_course_gender_loc].session_setting) {
        for (sub_element of element.groups) {
            objs = {
                $pull: {
                    'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                        {
                            $in: req.body._student_ids,
                        },
                },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': req.body._course_id },
                    { 'k.gender': req.query.mode ? req.query.mode : req.body.gender },
                    { 'l.session_type': element.session_type },
                    { 'm.group_no': sub_element.group_no },
                ],
            };
            doc = await base_control.update_condition_array_filter(
                student_group,
                query,
                objs,
                filter,
            );
        }
    }
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_DELETE_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                    req.t('ERROR_UNABLE_TO_DELETE_REMOVE_STUDENT_FROM_COURSE_GROUP'),
                ),
            );
    await removeStudentSchedule(
        student_group_data.data._institution_id,
        student_group_data.data._institution_calendar_id,
        req.body.batch,
        req.body.level,
        req.body._course_id,
        req.body._student_ids,
    );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    if (req.body._student_ids && req.body._student_ids.length) {
        await updateUserCourseRedisKey({
            studentIds: req.body._student_ids,
        });
    }
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
                req.t('SUCCESSFULLY_REMOVED_STUDENT_FROM_COURSE_GROUP'),
            ),
        );
};

exports.nr_get_clone_group = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _id: ObjectId(req.params._id), isDeleted: false };
    const project = {};
    const student_group_data = await base_control.get(student_group, query, project);
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_FOUND'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const delivery_symbol = [];
    const symbols = [];
    const objs = {};
    const courses = [];
    const final = {};
    const group = student_group_data.data.groups.findIndex(
        (i) => i.level === req.params.level && i.term === req.params.batch,
    );
    const course_loc = student_group_data.data.groups[group].courses.findIndex(
        (i) => i._course_id.toString() === req.params._course_id.toString(),
    );
    const session_type_data =
        student_group_data.data.groups[group].courses[course_loc].session_types;
    const symbol = session_type_data.map((i) => i.symbol);
    delivery_symbol.push(symbol);
    const level_courses = student_group_data.data.groups[group].courses;
    let ob;
    for (let i = 0; i < level_courses.length; i++) {
        const session_types = level_courses[i].session_types;
        const course_symbol = session_types.map((k) => k.symbol);
        symbols.push(course_symbol);
        ob = symbols.findIndex((l) => l === delivery_symbol.toString());
    }
    if (ob !== -1) {
        const result = student_group_data.data.groups[group].courses[ob];
        final.course_no = result.course_no;
        final.course_name = result.course_name;
        final._course_id = result._course_id;
        courses.push(final);
        Object.assign(objs, { courses });
    } else {
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ERROR_NO_MATCHING_COURSES'),
                    req.t('ERROR_NO_MATCHING_COURSES'),
                ),
            );
    }
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('GETTING_COURSES_WITH_MATCHING_DELIVERY_TYPES'),
                objs,
            ),
        );
};

exports.get_course_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) =>
                i.level.toString() === req.params.level.toString() && i.term === req.params.batch,
        );
        if (student_group_level === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.params._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        // const program_check = await base_control.get(
        //     program,
        //     { _id: ObjectId(student_group_data.data.master._program_id), isDeleted: false },
        //     {},
        // );
        // if (!program_check.status)
        //     return res
        //         .status(404)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 404,
        //                 false,
        //                 'Unable to find Student group Program',
        //                 'Unable to find Student group Program',
        //             ),
        //         );
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params._course_id) },
            { courses_name: 1, courses_number: 1, model: 1 },
        );
        const course_session_order = await base_control.get(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params._course_id) },
            { 'session_flow_data._session_id': 1, 'session_flow_data.delivery_symbol': 1 },
        );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        const courseSessionIds = [
            ...new Set(
                course_session_order.data.session_flow_data.map((sessionElement) =>
                    sessionElement._session_id.toString(),
                ),
            ),
        ];
        const delivery_type = await base_control.get_list(
            session_type,
            {
                _id: { $in: courseSessionIds },
                /* _program_id: ObjectId(program_check.data._id) */
            },
            {
                _id: 1,
                session_name: 1,
                session_symbol: 1,
                'delivery_types._id': 1,
                'delivery_types.delivery_name': 1,
                'delivery_types.delivery_symbol': 1,
            },
        );
        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );

        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        const obj = {};
        Object.assign(obj, { program: student_group_data.data.master.program_name });
        Object.assign(obj, { year: student_group_data.data.master.year });
        Object.assign(obj, { level: student_group_data.data.groups[student_group_level].level });
        Object.assign(obj, { course_number: course_get.data.courses_number });
        Object.assign(obj, {
            group_name: student_group_data.data.groups[student_group_level].group_name,
        });
        const delivery = [];
        let session_names = [];
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            session_names.push(delivery_data.session_name);
            delivery.push({
                session_type: delivery_data.session_name,
                delivery: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        const session_delivery = [];
        session_names = [...new Set(session_names)];
        session_names.forEach((element) => {
            const del = [];
            delivery.forEach((sub_element) => {
                if (sub_element.session_type === element)
                    del.push({
                        delivery_name: sub_element.delivery,
                        symbol: sub_element.symbol,
                    });
            });
            session_delivery.push({ session_type: element, delivery: del });
        });
        Object.assign(obj, { session_types: session_delivery });
        let grouped_status = false;

        // For Both Gender
        if (req.query.mode && req.query.mode === BOTH) {
            const bothGenderSetting = [];
            let bothGenderSessionDelivery = [];
            let bothGenderCount = 0;
            const studentGroupBothGender = student_group_data.data.groups[
                student_group_level
            ].courses[student_group_course].setting.find((i) => i.gender === BOTH);
            if (!studentGroupBothGender)
                Object.assign(obj, { both_count: bothGenderCount, both_group: bothGenderSetting });
            else {
                bothGenderCount = studentGroupBothGender.ungrouped.length;
                // const studentGroupSessionBoth = studentGroupBothGender.session_setting;
                // for (let i = 0; i < student_group_session_male.length; i++) {
                //     const element = student_group_session_male[i].groups;
                //     male_groups.push(element);
                // }
                // bothGenderSetting = studentGroupBothGender.session_setting.map(
                //     (groupSessionSettingElement) => groupSessionSettingElement.groups,
                // );
                const studentIds = studentGroupBothGender.session_setting
                    .map((groupSessionSettingElement) => groupSessionSettingElement.groups)
                    .map((studentIdElement) => studentIdElement._student_ids)
                    .flat();
                // // male_groups.forEach((elements) => {
                // //     elements.forEach((sub_element) => {
                // //         const ids = sub_element._student_ids;
                // //         male_std_ids.push(ids);
                // //     });
                // // });
                // male_std_ids.forEach((element) => {
                //     element.forEach((ele) => {
                //         const std_id = ele;
                //         male_id.push(std_id.toString());
                //     });
                // });
                Object.assign(obj, {
                    both_count: bothGenderCount + [...new Set(studentIds)].length,
                });
                for (sessionSettingElement of studentGroupBothGender.session_setting) {
                    if (sessionSettingElement.groups.length) {
                        const gro = sessionSettingElement.groups.map((i) => i.group_name);
                        const delivery_groups = sessionSettingElement.groups.map(
                            (sessionGroupSettingElement) => {
                                return {
                                    group_no: sessionGroupSettingElement.group_no,
                                    occupied: sessionGroupSettingElement._student_ids.length,
                                    availability:
                                        sessionSettingElement.no_of_student -
                                        sessionGroupSettingElement._student_ids.length,
                                };
                            },
                        );
                        grouped_status = !!delivery_groups.find(
                            (deliveryGroupElement) => deliveryGroupElement.occupied !== 0,
                        );
                        // sessionSettingElement.groups.forEach((group_element) => {
                        //     // if (!grouped_status)
                        //     //     grouped_status = group_element._student_ids.length !== 0;
                        //     delivery_groups.push({
                        //         group_no: group_element.group_no,
                        //         occupied: group_element._student_ids.length,
                        //         availability:
                        //             sessionSettingElement.no_of_student -
                        //             group_element._student_ids.length,
                        //     });
                        // });
                        const objs = {
                            delivery_type: sessionSettingElement.delivery_type,
                            session_symbol: sessionSettingElement.session_type,
                            no_of_groups: sessionSettingElement.no_of_group,
                            no_of_student: sessionSettingElement.no_of_student,
                            group_name: gro,
                            availability: delivery_groups,
                        };
                        bothGenderSetting.push(objs);
                        Object.assign(obj, { both_group: bothGenderSetting });
                        bothGenderSessionDelivery.push(
                            sessionSettingElement.delivery_type.toString(),
                        );
                    }
                }
                const delivery_iterated = [];
                bothGenderSessionDelivery = [...new Set(bothGenderSessionDelivery)];
                for (delivery_element of bothGenderSessionDelivery) {
                    const del_data = [];
                    for (settings_element of bothGenderSetting) {
                        if (
                            delivery_element.toString() ===
                            settings_element.delivery_type.toString()
                        ) {
                            del_data.push(settings_element);
                        }
                    }
                    delivery_iterated.push({
                        session_type: delivery_element,
                        delivery: del_data,
                    });
                }
                Object.assign(obj, { both_group_setting: delivery_iterated });
            }
            if (bothGenderSetting.length === 0) {
                if (student_group_data.data.groups[student_group_level].rotation === 'yes') {
                    let rotationBothGenderCount = 0;
                    for (element of student_group_data.data.groups[student_group_level]
                        .rotation_group_setting) {
                        rotationBothGenderCount += element._student_ids.length;
                    }
                    obj.both_count = rotationBothGenderCount;
                }
            }
            Object.assign(obj, { grouped_status });
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(req, 200, true, 'Saved Groups', obj),
                );
        }

        // Old Both Gender Flow
        const male_settings = [];
        const female_settings = [];
        let male_strength = 0;
        let female_strength = 0;
        const student_group_male = student_group_data.data.groups[student_group_level].courses[
            student_group_course
        ].setting.findIndex((i) => i.gender === constant.GENDER.MALE);
        const male_groups = [];
        const male_std_ids = [];
        const male_id = [];
        let male_session_delivery = [];
        if (student_group_male !== -1) {
            const male_count =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_male].ungrouped;
            male_strength = male_count.length;
            const student_group_session_male =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_male].session_setting;
            // console.log(male_count, ' ', male_strength, ' ', student_group_session_male);
            for (let i = 0; i < student_group_session_male.length; i++) {
                const element = student_group_session_male[i].groups;
                male_groups.push(element);
            }
            male_groups.forEach((elements) => {
                elements.forEach((sub_element) => {
                    const ids = sub_element._student_ids;
                    male_std_ids.push(ids);
                });
            });
            male_std_ids.forEach((element) => {
                element.forEach((ele) => {
                    const std_id = ele;
                    male_id.push(std_id.toString());
                });
            });
            const male_res = [...new Set(male_id)];
            Object.assign(obj, { male_count: male_strength + male_res.length });
            for (element of student_group_session_male) {
                // student_group_session_male.forEach(element => {
                const grp = element.groups;
                // console.log(grp.length);
                if (grp.length !== 0) {
                    const gro = element.groups.map((i) => i.group_name);
                    const delivery_groups = [];
                    element.groups.forEach((group_element) => {
                        if (!grouped_status)
                            grouped_status = group_element._student_ids.length !== 0;
                        delivery_groups.push({
                            group_no: group_element.group_no,
                            occupied: group_element._student_ids.length,
                            availability: element.no_of_student - group_element._student_ids.length,
                        });
                    });
                    const objs = {
                        delivery_type: element.delivery_type,
                        session_symbol: element.session_type,
                        no_of_groups: element.no_of_group,
                        no_of_student: element.no_of_student,
                        group_name: gro,
                        availability: delivery_groups,
                    };
                    male_settings.push(objs);
                    Object.assign(obj, { male_group: male_settings });
                    male_session_delivery.push(element.delivery_type);
                    const delivery_iterated = [];
                    male_session_delivery = [...new Set(male_session_delivery)];
                    for (delivery_element of male_session_delivery) {
                        const del_data = [];
                        for (settings_element of male_settings) {
                            if (
                                delivery_element.toString() ===
                                settings_element.delivery_type.toString()
                            ) {
                                del_data.push(settings_element);
                            }
                        }
                        delivery_iterated.push({
                            session_type: delivery_element,
                            delivery: del_data,
                        });
                    }
                    Object.assign(obj, { male_setting: delivery_iterated });
                } /*  else {
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('NO_MALE_GROUPS_CREATED'),
                                req.t('FIRST_CREATE_GROUPS'),
                            ),
                        );
                } */
            }
            // })
        } else {
            Object.assign(obj, { male_count: male_strength });
            Object.assign(obj, { male_group: male_settings });
        }
        // Getting female groups list and count
        const student_group_female = student_group_data.data.groups[student_group_level].courses[
            student_group_course
        ].setting.findIndex((i) => i.gender === constant.GENDER.FEMALE);
        const female_groups = [];
        const female_std_ids = [];
        const female_id = [];
        let female_session_delivery = [];
        if (student_group_female !== -1) {
            const female_count =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_female].ungrouped;
            female_strength = female_count.length;
            const student_group_session_female =
                student_group_data.data.groups[student_group_level].courses[student_group_course]
                    .setting[student_group_female].session_setting;
            for (let i = 0; i < student_group_session_female.length; i++) {
                const element = student_group_session_female[i].groups;
                female_groups.push(element);
            }
            female_groups.forEach((elements) => {
                elements.forEach((sub_element) => {
                    const ids = sub_element._student_ids;
                    female_std_ids.push(ids);
                });
            });
            female_std_ids.forEach((element) => {
                element.forEach((ele) => {
                    const std_id = ele;
                    female_id.push(std_id.toString());
                });
            });
            const female_res = [...new Set(female_id)];
            Object.assign(obj, { female_count: female_strength + female_res.length });
            for (element of student_group_session_female) {
                // student_group_session_female.forEach(element => {
                const grp = element.groups;
                if (grp.length !== 0) {
                    const gro = element.groups.map((i) => i.group_name);
                    const delivery_groups = [];
                    element.groups.forEach((group_element) => {
                        if (!grouped_status)
                            grouped_status = group_element._student_ids.length !== 0;
                        delivery_groups.push({
                            group_no: group_element.group_no,
                            occupied: group_element._student_ids.length,
                            availability: element.no_of_student - group_element._student_ids.length,
                        });
                    });
                    const objs = {
                        delivery_type: element.delivery_type,
                        session_symbol: element.session_type,
                        no_of_groups: element.no_of_group,
                        no_of_student: element.no_of_student,
                        group_name: gro,
                        availability: delivery_groups,
                    };
                    female_settings.push(objs);
                    Object.assign(obj, { female_group: female_settings });
                    female_session_delivery.push(element.delivery_type);
                    const delivery_iterated = [];
                    female_session_delivery = [...new Set(female_session_delivery)];
                    for (delivery_element of female_session_delivery) {
                        const del_data = [];
                        for (settings_element of female_settings) {
                            if (
                                delivery_element.toString() ===
                                settings_element.delivery_type.toString()
                            ) {
                                del_data.push(settings_element);
                            }
                        }
                        delivery_iterated.push({
                            session_type: delivery_element,
                            delivery: del_data,
                        });
                    }
                    Object.assign(obj, { female_setting: delivery_iterated });
                } /* else {
                    return res
                        .status(404)
                        .send(
                            common_files.response_function(
                                res,
                                404,
                                false,
                                req.t('NO_FEMALE_GROUPS_CREATED'),
                                req.t('FIRST_CREATE_GROUPS'),
                            ),
                        );
                } */
            }
            // })
        } else {
            Object.assign(obj, { female_count: female_strength });
            Object.assign(obj, { female_group: female_settings });
        }
        if (female_settings.length === 0 && male_settings.length === 0) {
            if (student_group_data.data.groups[student_group_level].rotation === 'yes') {
                let male_count = 0;
                let female_count = 0;
                for (element of student_group_data.data.groups[student_group_level]
                    .rotation_group_setting) {
                    if (element.gender === 'male') {
                        male_count += element._student_ids.length;
                    } else {
                        female_count += element._student_ids.length;
                    }
                }
                obj.male_count = male_count;
                obj.female_count = female_count;
            }
        }
        Object.assign(obj, { grouped_status });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('SAVED_GROUPS'),
                    obj,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.fyd_get_course_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const project = {};
        const student_group_data = await base_control.get(student_group, query, project);
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        let settings = [];
        const obj = {};
        const student_group_level = student_group_data.data.groups.findIndex(
            (i) => i.level === req.params.level && i.term === req.params.batch,
        );
        if (student_group_level === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const student_group_course = student_group_data.data.groups[
            student_group_level
        ].courses.findIndex((i) => i._course_id.toString() === req.params._course_id.toString());
        if (student_group_course === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_COURSE'),
                        req.t('UNABLE_TO_FIND_COURSE'),
                    ),
                );
        // let ic_get = await base_control.get(institution_calendar, { _id: ObjectId(student_group_data.data._institution_calendar_id) }, { calendar_name: 1 });
        // const program_check = await base_control.get_list(program, { isDeleted: false }, {});
        // const pro_ids = [];
        // let pro_no;
        // program_check.data.forEach((element) => {
        //     if (element._id.toString() === student_group_data.data.master._program_id.toString()) {
        //         pro_no = element.no;
        //     }
        // });
        // program_check.data.forEach((element) => {
        //     if (pro_no === element.no) pro_ids.push(ObjectId(element._id));
        // });
        // let p_get = await base_control.get(program, { _id: ObjectId(student_group_data.data.master._program_id) }, { name: 1 });
        const course_get = await base_control.get(
            course,
            { _id: ObjectId(req.params._course_id) },
            {
                courses_name: 1,
                courses_number: 1,
                model: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );
        const course_session_order = await base_control.get(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params._course_id) },
            { 'session_flow_data._session_id': 1, 'session_flow_data.delivery_symbol': 1 },
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {
                /* _program_id: ObjectId(student_group_data.data.master._program_id) */
            },
            {
                session_name: 1,
                'delivery_types.delivery_name': 1,
                'delivery_types.delivery_symbol': 1,
            },
        );
        if (!delivery_type.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                        req.t('THERE_IS_NO_DELIVERY_TYPES'),
                    ),
                );
        if (!course_session_order.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                        req.t('THERE_IS_NO_SESSION_IN_THIS_COURSE_PLS_ADD_SESSION_FLOW'),
                    ),
                );
        const cso = [];
        course_session_order.data.session_flow_data.forEach((cso_element) => {
            cso.push({
                _session_id: cso_element._session_id,
                delivery_symbol: cso_element.delivery_symbol,
            });
        });
        const symbol = cso.filter(
            (thing, index, self) =>
                index === self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
        );
        // const symbol = [...new Set(course_session_order.data.map((i) => i.delivery_symbol))];
        // Getting program details
        // let group_name = ic_get.data.calendar_name + '-'
        //     + p_get.data.name.substring(0, 1) + 'P' + '-'
        //     + ((req.params.batch) ? 'RT' : 'IT') + '-' + 'Y' +
        //     + student_group_data.data.master.year + '-' + 'L' +
        //     + student_group_data.data.groups[student_group_level].level + '-' + course_get.data.courses_number;
        Object.assign(obj, { program: student_group_data.data.master.program_name });
        Object.assign(obj, { year: student_group_data.data.master.year });
        Object.assign(obj, { level: student_group_data.data.groups[student_group_level].level });
        Object.assign(obj, { course_number: course_get.data.courses_number });
        Object.assign(obj, { versionName: course_get.data.versionName });
        Object.assign(obj, { versioned: course_get.data.versioned });
        Object.assign(obj, { versionedFrom: course_get.data.versionedFrom });
        Object.assign(obj, { versionNo: course_get.data.versionNo });
        Object.assign(obj, { versionedCourseIds: course_get.data.versionedCourseIds });
        Object.assign(obj, {
            group_name: student_group_data.data.groups[student_group_level].group_name,
        });
        // Getting session type details
        // const delivery = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        // symbol.forEach((element) => {
        //     const delivery_data =
        //         delivery_type.data[
        //             delivery_type.data.findIndex((i) => i.delivery_symbol === element)
        //         ];
        //     delivery.push({
        //         delivery_type: delivery_data.session_type,
        //         delivery_name: delivery_data.delivery_type,
        //         delivery_symbol: delivery_data.delivery_symbol,
        //     });
        // });
        // delivery.forEach((element) => {
        //     if (element.delivery_type === 'Theory')
        //         theory.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
        //     if (element.delivery_type === 'Practical')
        //         practical.push({
        //             delivery: element.delivery_name,
        //             symbol: element.delivery_symbol,
        //         });
        //     if (element.delivery_type === 'Clinical')
        //         clinical.push({ delivery: element.delivery_name, symbol: element.delivery_symbol });
        // });
        // const session_dev_type = {
        //     // "elective": course_get.data.model === 'elective' ? [{ delivery: 'elective', symbol: 'e' }] : undefined,
        //     theory,
        //     practical,
        //     clinical,
        // };
        const delivery = [];
        let session_names = [];
        // const theory = [];
        // const practical = [];
        // const clinical = [];
        symbol.forEach((sym_element) => {
            const delivery_data =
                delivery_type.data[
                    delivery_type.data.findIndex(
                        (i) => i._id.toString() === sym_element._session_id.toString(),
                        // i.delivery_symbol ===
                        // sym_element.delivery_symbol,
                    )
                ];
            const delivery_loc = delivery_data.delivery_types.findIndex(
                (i) => i.delivery_symbol === sym_element.delivery_symbol,
            );
            session_names.push(delivery_data.session_name);
            delivery.push({
                session_type: delivery_data.session_name,
                delivery: delivery_data.delivery_types[delivery_loc].delivery_name,
                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
            });
        });
        const session_delivery = [];
        session_names = [...new Set(session_names)];
        session_names.forEach((element) => {
            const del = [];
            delivery.forEach((sub_element) => {
                if (sub_element.session_type === element)
                    del.push({
                        delivery_name: sub_element.delivery,
                        symbol: sub_element.symbol,
                    });
            });
            session_delivery.push({ session_type: element, delivery: del });
        });
        Object.assign(obj, { session_types: session_delivery });
        const group_counts = [];
        if (student_group_data.data.groups[student_group_level].rotation === 'yes') {
            student_group_data.data.groups[student_group_level].rotation_group_setting.forEach(
                (element) => {
                    if (
                        req.query.mode
                            ? req.query.mode === element.gender
                            : element.gender === req.params.gender
                    ) {
                        group_counts.push({
                            group_no: element.group_no,
                            capacity: element._student_ids.length,
                        });
                        // group_students = element._student_ids;
                    }
                },
            );
        } else {
            student_group_data.data.groups[student_group_level].group_setting.forEach((element) => {
                if (
                    req.query.mode
                        ? req.query.mode === element.gender
                        : element.gender === req.params.gender
                ) {
                    element.groups.forEach((sub_element) => {
                        group_counts.push({
                            group_no: sub_element.group_no,
                            capacity: sub_element._student_ids.length,
                        });
                    });
                }
            });
        }
        Object.assign(obj, { count: group_counts });
        const groups_setting =
            student_group_data.data.groups[student_group_level].courses[student_group_course]
                .setting;
        gender_setting = common_files.clone(
            groups_setting.filter((item) =>
                req.query.mode
                    ? req.query.mode === item.gender
                    : req.params.gender.toString() === item.gender.toString(),
            ),
        );
        const groups_list = [];
        const groups_settings = {};
        let session_delivery_type = [];
        let grouped_status = false;
        for (elements of gender_setting) {
            settings = [];
            elements.session_setting.forEach((element) => {
                const grp = element.groups;
                if (grp.length === 0)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('NO_GROUPS_CREATED'),
                                req.t('FIRST_CREATE_GROUPS'),
                            ),
                        );
                const gro = element.groups.map((i) => i.group_name);
                const delivery_groups = [];
                element.groups.forEach((group_element) => {
                    if (!grouped_status) grouped_status = group_element._student_ids.length !== 0;
                    delivery_groups.push({
                        group_no: group_element.group_no,
                        occupied: group_element._student_ids.length,
                        availability: element.no_of_student - group_element._student_ids.length,
                    });
                });
                const deliveryTypeData = delivery.find(
                    (ele) => ele.symbol === element.session_type,
                );
                const delivery_typeData = element.delivery_type
                    ? element.delivery_type
                    : deliveryTypeData
                    ? deliveryTypeData.session_type
                    : '';
                const objs = {
                    session_symbol: element.session_type,
                    delivery_type: delivery_typeData,
                    no_of_groups: element.no_of_group,
                    no_of_student: element.no_of_student,
                    group_name: gro,
                    availability: delivery_groups,
                };
                // Checking is Delivery type present
                if (delivery_typeData.length !== 0) {
                    session_delivery_type.push(delivery_typeData);
                    settings.push(objs);
                }
            });
            const delivery_iterated = [];
            session_delivery_type = [...new Set(session_delivery_type)];
            for (delivery_element of session_delivery_type) {
                const del_data = [];
                for (settings_element of settings) {
                    if (delivery_element.toString() === settings_element.delivery_type.toString()) {
                        del_data.push(settings_element);
                    }
                }
                delivery_iterated.push({ session_type: delivery_element, delivery: del_data });
            }
            groups_list.push({
                group_no: elements._group_no,
                gender: elements.gender,
                delivery: settings,
            });
            groups_settings['group' + elements._group_no] = delivery_iterated;
        }
        Object.assign(obj, { groups: groups_list, groups_settings, grouped_status });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('SAVED_GROUPS'),
                    obj,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
