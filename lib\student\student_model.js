let mongoose = require('mongoose');
let Schemas = mongoose.Schema;
let constant = require('../../utility/constants');

let studentSchemas = new Schemas({
    name: {
        first: {
            type: String,
            required: true
        },
        middle: {
            type: String,
            required: true
        },
        last: {
            type: String,
            required: true
        },
        family: {
            type: String
        }
    },
    reg_id: {
        type: String,
        unique: true,
        required: true
    },
    _program_id: {
        type: Schemas.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    password: {
        type: String,
        required: true
    },
    gender: {
        type: String,
        required: true
    },
    mobile: {
        type: String,
        unique: true,
        required: true
    },
    mailID: {
        type: String,
        unique: true,
        required: true
    },
    nationality: {
        type: Schemas.Types.ObjectId,
        ref: constant.COUNTRY,
        required: true
    },
    nationality_id: {
        type: String,
        required: true
    },
    deviceID: {
        type: String
    },
    face_reg_status: {
        type: Boolean,
        default: false
    },
    device_token: {
        type: String
    },
    os: {
        type: String
    },
    verified_status: {
        type: Boolean,
        default: false,
    },
    otp: String,
    isActive: {
        type: Boolean,
        default: false
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.STUDENT, studentSchemas);