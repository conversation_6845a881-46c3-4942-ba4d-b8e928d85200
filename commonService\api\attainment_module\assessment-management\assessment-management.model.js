const mongoose = require('mongoose');
const { ASSESSMENT_MANAGEMENT } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const assessmentType = new Schema({
    name: String,
    isActive: {
        type: Boolean,
        default: false,
    },
    isDefault: {
        type: Boolean,
        default: false,
    },
});

const assessmentHierarchy = new Schema({
    typeName: { type: String },
    subTypes: [
        {
            typeName: { type: String },
            isDefault: { type: Boolean, default: true },
            isActive: { type: Boolean, default: true },
            assessmentTypes: [assessmentType],
        },
    ],
});

const assessmentManagementSchemas = new Schema(
    {
        types: [assessmentHierarchy],
        _institution_id: {
            type: ObjectId,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSESSMENT_MANAGEMENT, assessmentManagementSchemas);
