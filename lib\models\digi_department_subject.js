let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');
const digi_department_subject = new Schema({
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION
    },
    program_id: {
        type: Schema.Types.ObjectId,
    },
    program_name: {
        type: String
    },
    department_name: {
        type: String
    },
    subject: [{
        subject_name: {
            type: String
        },
        shared_with: [{
            program_id: Schema.Types.ObjectId,
            program_name: {
                type: String
            },
            department_id: Schema.Types.ObjectId,
            department_name: {
                type: String
            }
        }],
        isActive: {
            type: Boolean,
            default: true
        },
        isDeleted: {
            type: Boolean,
            default: false
        },
    }],
    shared_with: [{
        program_id: Schema.Types.ObjectId,
        program_name: {
            type: String
        }
    }],
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
}, {
    timestamps: true
});
module.exports = mongoose.model(constant.DIGI_DEPARTMENT_SUBJECT, digi_department_subject);