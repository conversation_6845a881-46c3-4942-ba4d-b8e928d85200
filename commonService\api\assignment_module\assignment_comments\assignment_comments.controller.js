const assignmentCommentsSchema = require('./assignment_comments.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { getSignedUrl } = require('../assignment-settings/assignment-settings.util');
const deleteKeyQuery = { isDeleted: false };

const updateAssignmentComments = async ({ params = {}, headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId, comments } = body;
        const assignmentComments = await assignmentCommentsSchema.findOne({
            _institution_id,
            assignmentId,
            studentId,
            ...deleteKeyQuery,
        });
        let updateAssignmentComments;
        if (!assignmentComments) {
            updateAssignmentComments = await assignmentCommentsSchema.create({
                _institution_id,
                assignmentId,
                studentId,
                comments,
            });
        } else {
            updateAssignmentComments = await assignmentCommentsSchema.updateOne(
                {
                    _institution_id,
                    assignmentId,
                    studentId,
                    ...deleteKeyQuery,
                },
                {
                    ...(comments && { $addToSet: { comments } }),
                },
            );
        }
        return {
            statusCode: 200,
            message: 'ASSIGNMENT COMMENTS CREATED',
            data: { assignmentCommentsId: updateAssignmentComments._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAssignmentComments = async ({ params = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId } = params;
        const assignmentComments = await assignmentCommentsSchema.findOne(
            {
                _institution_id,
                assignmentId,
                studentId,
                ...deleteKeyQuery,
            },
            { comments: 1 },
        );
        if (!assignmentComments) return { statusCode: 200, message: 'NO ASSIGNMENT COMMENTS' };
        for (const comment of assignmentComments.comments) {
            for (const attachment of comment.attachments) {
                attachment.signedUrl = await getSignedUrl(attachment.url);
            }
        }
        return { statusCode: 200, message: 'ASSIGNMENT COMMENTS', data: assignmentComments };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteAssignmentComments = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId, commentId } = params;
        const deleteAssignment = await assignmentCommentsSchema
            .findOneAndUpdate(
                {
                    _institution_id,
                    assignmentId,
                    studentId,
                    ...deleteKeyQuery,
                },
                {
                    $pull: {
                        'comments._id': convertToMongoObjectId(commentId),
                    },
                },
            )
            .lean();
        if (!deleteAssignment)
            return { statusCode: 410, message: 'ERROR IN DELETING ASSIGNMENT COMMENTS' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT COMMENTS DELETED',
            data: {},
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    updateAssignmentComments,
    listAssignmentComments,
    deleteAssignmentComments,
};
