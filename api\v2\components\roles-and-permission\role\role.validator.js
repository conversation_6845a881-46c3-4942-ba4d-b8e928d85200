const Joi = require('joi');

const roleValidation = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                staff_type: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return req.t('STAFFTYPE_REQUIRED');
                    }),
                position_type: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return req.t('POSITIONFTYPE_REQUIRED');
                    }),
                role: Joi.string()
                    .allow(' ')
                    .min(3)
                    .max(20)
                    .trim()
                    .error((error) => {
                        return req.t('ROLE_REQUIRED');
                    }),
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return req.t('INSTITUTION_ID_REQUIRED');
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const roleIdValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return req.t('ID_REQUIRED');
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

module.exports = {
    roleIdValidation,
    roleValidation,
};
