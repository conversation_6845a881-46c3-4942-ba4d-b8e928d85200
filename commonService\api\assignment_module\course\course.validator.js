const Joi = require('joi');
const { objectIdSchema, stringSchema } = require('../../../utility/validationSchemas');

exports.listCourseValidator = Joi.object({
    staffId: objectIdSchema,
    programId: objectIdSchema,
    roleId: objectIdSchema,
}).unknown(true);

exports.listSubjectsValidator = Joi.object({
    courseId: objectIdSchema,
}).unknown(true);

exports.getCloSloValidator = Joi.object({
    courseId: objectIdSchema,
    sessionIds: Joi.array().items(objectIdSchema),
}).unknown(true);

exports.getCoursesForStaffValidator = Joi.object({
    staffId: objectIdSchema,
    roleId: objectIdSchema.allow(''),
}).unknown(true);

exports.searchTaxonomyValidator = Joi.object({ search: stringSchema }).unknown(true);
