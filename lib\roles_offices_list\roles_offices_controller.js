const constant = require('../utility/constants');
//const roles_offices_model = require("../models/roles_offices_list");
const roles_offices = require('mongoose').model(constant.ROLES_OFFICES_LIST);
const roles_permission = require('mongoose').model(constant.ROLES_PERMISSION);
const role_assign_to_staff_model = require("../role_assign_to_staff/role_assign_to_staff_model");
const role_assign_to_staff = require('mongoose').model(constant.ROLE_ASSIGN_TO_STAFF);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');

exports.create = async(req,res) => {
    let objs = {
        type : req.body.type,
        name : req.body.name
    }
    let doc = await base_control.insert(roles_offices ,objs);
    if(doc.status){
        common_files.com_response(res ,200 ,true, "roles and offices added" ,doc.data)
    } else {
        common_files.com_response(res ,404 ,false ,"Error in creating roles and offices list", "Error in creating roles and offices list")
    }
}


exports.get = async(req,res) => {
    let doc_data = [];
    let doc = await base_control.get_list(roles_offices,{'isDeleted':false})
    if (doc.status){
        doc.data.forEach(element => {
        doc_data.push({_id: element._id,type: element.type,name: element.name});
        });
        common_files.com_response(res, 200, true, "Got all the roles and offices list" ,doc_data)
    } else {
        common_files.com_response(res, 404, false ,"Error in getting roles and offices list", "Error in getting roles and offices list")
    }
}


exports.update = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(roles_offices,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
    let objs = {
        type : req.body.type,
        name : req.body.name
    }
    let doc = await base_control.update(roles_offices,{_id:req.params.id},objs)
    if(doc.status){
        common_files.com_response(res, 200, true, "updated the roles and offices list" ,doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in updating the list", "Error in updating the list")
    }
} else {
       common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}


exports.delete = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(roles_offices,{_id: {$in: req.params.id}});
    if(checks.status){
    let doc = await base_control.delete(roles_offices,{_id:req.params.id})
    if(doc.status){
        common_files.com_response(res, 200, true, "deleted the role and office list" ,doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in deleting the list", "Error in deleting the list")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}


exports.get_id = async(req,res) => {
    let checks = {status:true}
    checks = await base_control.check_id(roles_offices,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
    let doc_data =[];
    let doc = await base_control.get_list(roles_offices,{_id:req.params.id})
    if(doc.status){
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,type:element.type,name:element.name});
        });
        common_files.com_response(res, 200, true, "Got list by id" ,doc_data)
    }else{
        common_files.com_response(res, 404, false, "Error in getting list by id", "Error in getitng list by id")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}


exports.getroles = async(req,res) => {
    let doc_data = [];
    let query = {'type':constant.ROLE_LIST};
    let doc = await base_control.get_list(roles_offices,query)
    if(doc.status) {
        doc.data.forEach(element => {
             doc_data.push({_id:element._id,type:element.type,name:element.name});
        });
    common_files.com_response(res, 200, true, "Got all roles list" ,doc_data)
    } else {
        common_files.com_response(res, 404, false, " Error in getting roles list" ,doc.data)
    }

}


exports.getoffices = async(req,res) => {
    let doc_data =[];
    let query = {'type':constant.OFFICE_LIST};
    let doc = await base_control.get_list(roles_offices,query)
    if(doc.status) {
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,type:element.type,name:element.name});
        });
    common_files.com_response(res, 200, true, "Got all roles list" ,doc_data)
    } else {
        common_files.com_response(res, 404, false, " Error in getting roles list" ,doc.data)
    }

};

exports.getofficelistandroles = async(req,res)=>{
    let doc_data =[];

    //Get Staff Details from role assign to staff collection
    let query_staff = {'_staff_id':req.params.staff_id};
    let doc_staff = await base_control.get_list(role_assign_to_staff,query_staff);
    
    if(doc_staff.status){
        //let query = {'type':constant.OFFICE_LIST,'isDeleted':false};
        let query = {};
        if(doc_staff.data[0].role_name == "Dean")
            query = {'type':constant.OFFICE_LIST,'isDeleted':false};
        else
            query = {'type':constant.OFFICE_LIST,'_id':doc_staff.data[0]._office_id,'isDeleted':false};

        let doc = await base_control.get_list(roles_offices,query);
        let tempData = [];
        if(doc.status) {
            for(let i=0;i<doc.data.length;i++){
                let query2 = {'_office_id':doc.data[i]._id};
                var rolesData = await base_control.get_list(roles_permission,query2)        
                var obj = {};
                obj["_id"] = doc.data[i]["_id"];
                obj["isActive"] = doc.data[i]["isActive"];
                obj["isDeleted"] = doc.data[i]["isDeleted"];
                obj["type"] = doc.data[i]["type"];
                obj["name"] = doc.data[i]["name"];
                obj["createdAt"] = doc.data[i]["createdAt"];
                obj["updatedAt"] = doc.data[i]["updatedAt"];
                obj["__v"] = doc.data[i]["__v"];//console.log(rolesData)

                if(rolesData.status){
                    let tempRolesData = [];
                    for(let j=0;j<rolesData.data.length;j++){//console.log(rolesData.data[j]["_office_id"]);
                        //Get role assigned to staff Data( by using office_id,role_id)
                        let objRoles = {};
                        let query = { _office_id: rolesData.data[j]["_office_id"],_role_id:rolesData.data[j]["_id"], 'isDeleted': false };
                        let docRolesAssignToStaffData = await base_control.get(role_assign_to_staff, query);
                        
                        objRoles["_id"] = rolesData.data[j]["_id"]
                        objRoles["_program_id"] = rolesData.data[j]["_program_id"]
                        objRoles["_permissions"] = rolesData.data[j]["_permissions"]
                        objRoles["isActive"] = rolesData.data[j]["isActive"]
                        objRoles["isDeleted"] = rolesData.data[j]["isDeleted"]
                        objRoles["role_name"] = rolesData.data[j]["role_name"]
                        objRoles["_role_id"] = rolesData.data[j]["_role_id"]
                        objRoles["_assigned_by_id"] = rolesData.data[j]["_assigned_by_id"]
                        objRoles["assigned_by"] = rolesData.data[j]["assigned_by"]
                        objRoles["_office_id"] = rolesData.data[j]["_office_id"]
                        objRoles["office_name"] = rolesData.data[j]["office_name"]
                        objRoles["createdAt"] = rolesData.data[j]["createdAt"]
                        objRoles["updatedAt"] = rolesData.data[j]["updatedAt"]
                        objRoles["__v"] = rolesData.data[j]["__v"]
                        
                        if (docRolesAssignToStaffData.status)
                            objRoles["role_assign_to_staff_data"] = docRolesAssignToStaffData.data
                        else
                            objRoles["role_assign_to_staff_data"] = "";

                        tempRolesData.push(objRoles);
                    }
                    obj["roles_data"] = rolesData.data
                    obj["roles_data"] = tempRolesData
                }
                else{
                    obj["roles_data"] = "";
                }
                tempData.push(obj);
            }
            //console.log(tempRolesData);
            common_files.com_response(res, 200, true, "Got all office and role list" ,tempData)
        } else {
            common_files.com_response(res, 404, false, " Error in getting office and role list" ,doc_staff.data)
        }
    }
    else{
        common_files.com_response(res, 404, false, " Error in getting office and role list" ,doc_staff.data)
    }

    //

    
};