const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const { getUserLog } = require('./userActivityLog.service');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

router.get(
    '/getUserLog',
    [userPolicyAuthentication(['global_search:dashboard:view'])],
    catchAsync(getUserLog),
);

module.exports = router;
