const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_CATEGORY,
    QAPC_FORM_SETTING,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    DIGI_COURSE,
    SAQR_INSTITUTION_TYPE: { UNIVERSITY },
    QAPC_FORM_SETTING_COURSES,
    QAPC_SETTING_TAG,
    INSTITUTION,
    COURSE_TYPE: { STANDARD, SELECTIVE },
    PUBLISHED,
    DRAFT,
} = require('../../utility/constants');

const formSettingCourseSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        categoryId: { type: ObjectId, ref: QAPC_FORM_CATEGORY },
        categoryFormId: { type: ObjectId, ref: QAPC_FORM_SETTING },
        assignedInstitutionId: { type: ObjectId, ref: INSTITUTION },
        institutionName: { type: String },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        programName: { type: String },
        curriculumId: { type: ObjectId, ref: DIGI_CURRICULUM },
        curriculumName: { type: String },
        year: { type: String },
        courseId: { type: ObjectId, ref: DIGI_COURSE },
        courseName: { type: String },
        courseCode: { type: String },
        sharedWithOthers: { type: Boolean },
        sharedFormOthers: { type: Boolean },
        courseType: { type: String, enum: [STANDARD, SELECTIVE] },
        institutionType: { type: String, default: UNIVERSITY },
        isConfigure: { type: Boolean, default: false },
        isEnable: { type: Boolean, default: true },
        tags: [
            {
                _id: { type: ObjectId, ref: QAPC_SETTING_TAG },
                name: { type: String },
                subTag: [{ type: String }],
            },
        ],
        numberOfGroups: [{ type: String }],
        status: { type: String, enum: [PUBLISHED, DRAFT], default: DRAFT },
        actions: {
            studentGroups: { type: Boolean, default: false },
            everyAcademic: { type: Boolean, default: false },
            occurrenceConfiguration: { type: Boolean, default: false },
            academicTerms: { type: Boolean, default: false },
            attemptType: { type: Boolean, default: false },
        },
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_FORM_SETTING_COURSES, formSettingCourseSchema);
