const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    studentCourseList,
    courseSessionList,
    scheduleListBasedDates,
    studentLeaveAbsentSchedule,
    scheduleWithDetails,
    scheduleListDetails,
    scheduleAttendanceChange,
    externalDataSync,
    externalDataGet,
} = require('./hebaAI.controller');
const { getSessionText } = require('../digi_course/digi_course_controller');
const {
    getGlobalSessionDocumentDetails,
} = require('../global_session_settings/global_session_settings_controller');
const { getCourseWarningForCalendar } = require('../lmsWarning/lmsWarning.controller');
const { getAttendanceReport } = require('../session-report/session-report.controller');
const {
    userCourses,
    getStudentList,
    userCalendars,
} = require('../digi_class/course_session/course_session_controller');
const { scheduleList, schedule } = require('../course_schedule/course_schedule_controller');

// HEBA AI
router.get('/studentCourseList', catchAsync(studentCourseList));
router.get('/courseSessionList', catchAsync(courseSessionList));
router.get('/scheduleListBasedDates', catchAsync(scheduleListBasedDates));
router.get('/studentLeaveAbsentSchedule', catchAsync(studentLeaveAbsentSchedule));

// Course Session Order Details
router.get('/getSessionText', getSessionText);
router.get('/getGlobalSessionDocumentDetails', catchAsync(getGlobalSessionDocumentDetails));
router.get(
    '/courseWarningForCalendar',
    (req, res, next) => {
        req.query.institutionCalendarId = '6560732001fe42649f3d21f8';
        next();
    },
    catchAsync(getCourseWarningForCalendar),
);
router.get(
    '/attendanceReport/:sessionDate',
    (req, res, next) => {
        req.headers.user_id = '5efc71334dacffdc70bc834c';
        req.headers.role_id = '601e501c867c4c054bfad27e';
        req.query.institutionCalendarId = '6560732001fe42649f3d21f8';
        req.query.sessionStatus = [
            'completed',
            'notstarted',
            'inprogress',
            'upcoming',
            'missed',
            'cancelled',
            'merged',
        ];
        next();
    },
    catchAsync(getAttendanceReport),
);

// Display Board Service
router.get('/scheduleListDetails', catchAsync(scheduleListDetails));
router.get('/scheduleWithDetails', catchAsync(scheduleWithDetails));
router.put('/scheduleAttendanceChange', catchAsync(scheduleAttendanceChange));
router.get('/userCourses/:userId', userCourses);
router.get('/getStudentList', getStudentList);
router.get('/userCalendars/:userId/:userType', userCalendars);
router.get('/schedule_list/:programId/:instCalId/:courseId/:term/:level_no', scheduleList);
router.post('/schedule', schedule);

// Program Input Sync Data
router.post('/externalDataSync', catchAsync(externalDataSync));
router.get('/externalDataGet', catchAsync(externalDataGet));
module.exports = router;
