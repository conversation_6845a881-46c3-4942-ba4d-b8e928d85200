const { getCourseSessionOrder } = require('../../../service/cache.service');
const courseSchedules = require('../../models/course_schedule');
const activities = require('../../models/activities');
// const Question = require('../../models/question');
const documentManager = require('../../models/document_manager');
const { convertToMongoObjectId, clone, convertToUtcFormat } = require('../../utility/common');
const {
    TIME_GROUP_BOOKING_TYPE,
    PRESENT,
    DC_STAFF,
    ABSENT,
    LEAVE_TYPE: { PERMISSION },
    LEAVE,
    STUDENTS,
} = require('../../utility/constants');
const isDeleteActive = {
    isDeleted: false,
    isActive: true,
};
const { getRemoteInfra } = require('../../utility/data.service');
const { logger } = require('../../utility/util_keys');
const { dateTimeBasedConverter } = require('../../utility/common_functions');

const getCoursesSessionList = async (userId, type, institutionCalendarId) => {
    try {
        // const scheduleQuery =
        //     type === DC_STUDENT
        //         ? {
        //               'students._id': convertToMongoObjectId(userId),
        //               _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //               ...isDeleteActive,
        //           }
        //         : {
        //               'staffs._staff_id': convertToMongoObjectId(userId),
        //               _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //               ...isDeleteActive,
        //           };
        // const scheduleDatas = await courseSchedules.find(scheduleQuery, {
        //     _id: 1,
        //     program_name: 1,
        //     _program_id: 1,
        //     _course_id: 1,
        //     course_name: 1,
        //     course_code: 1,
        //     term: 1,
        //     year_no: 1,
        //     level_no: 1,
        //     mode: 1,
        //     title: 1,
        //     rotation: 1,
        //     rotation_count: 1,
        // });
        // const filteredSchedule = scheduleDatas.reduce(
        //     (unique, item) =>
        //         unique.find(
        //             (ele) =>
        //                 ele._course_id.toString() === item._course_id.toString() &&
        //                 ele.term.toString() === item.term.toString() &&
        //                 ele.level_no.toString() === item.level_no.toString() &&
        //                 ele._program_id.toString() === item._program_id.toString() &&
        //                 ((ele.rotation.toString() === 'yes' &&
        //                     ele.rotation_count.toString() === item.rotation_count.toString()) ||
        //                     ele.rotation.toString() === 'no'),
        //         )
        //             ? unique
        //             : [...unique, item],
        //     [],
        // );
        // const pcCourseData = await getProgramCalendar(institutionCalendarId);
        // return pcCourseData.filter((calendarCourse) =>
        //     filteredSchedule.find(
        //         (scheduleCourse) =>
        //             scheduleCourse._course_id.toString() === calendarCourse._id.toString() &&
        //             scheduleCourse._program_id.toString() ===
        //                 calendarCourse._program_id.toString() &&
        //             scheduleCourse.term.toString() === calendarCourse.term.toString() &&
        //             scheduleCourse.level_no.toString() === calendarCourse.level.toString() &&
        //             ((scheduleCourse.rotation.toString() === 'yes' &&
        //                 scheduleCourse.rotation_count.toString() ===
        //                     calendarCourse.rotation_count.toString()) ||
        //                 scheduleCourse.rotation.toString() === 'no'),
        //     ),
        // );

        let result = await getCourseIdsWithSessions(
            staffId,
            _course_id,
            _institution_calendar_id,
            _program_id,
            year_no,
            level_no,
            rotation,
            rotation_count,
        );
        const { courseSchedule, courses } = result;

        const courseIds = courses.map((courseId) => {
            return courseId._course_id;
        });

        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIds },
            },
            { _id: 1, course_type: 1 },
        );

        let allScheduleIds = courseSchedule.map((schedule) => {
            return schedule.session
                ? schedule.session._session_id.toString()
                : schedule._id.toString();
        });
        allScheduleIds = [...new Set(allScheduleIds)];
        allScheduleIds = allScheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));
        const activities = (
            await getJSON(
                Activity,
                {
                    $or: [
                        { _scheduleId: { $in: allScheduleIds } },
                        { _sessionId: { $in: allScheduleIds } },
                    ],
                    isDeleted: false,
                },
                {},
            )
        ).data;
        const documents = (
            await getJSON(
                Document,
                {
                    'sessionOrScheduleIds._id': { $in: allScheduleIds },
                    isDeleted: false,
                    isActive: true,
                },
                {},
            )
        ).data;

        const feedBackData = await getRatingByCourses(
            _course_id,
            staffId,
            year_no,
            level_no,
            rotation,
            rotation_count,
        );
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id: calendarId,
                _program_id: programId,
                program_name,
                course_name,
                course_code,
                level_no: levelNo,
                year_no: yearNo,
                isActive,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;
            result = getSessions(
                courseSchedule,
                courseId,
                calendarId,
                programId,
                yearNo,
                levelNo,
                staffId,
                rotation,
                rotation_count,
            );
            const {
                courseSessionDetails,
                absentCount,
                leaveCount,
                warningCount,
                presentCount,
                totalSessions,
                completedSessions,
                courseSessions,
                attendedSessions,
            } = result;
            let feedback;
            const feedBacks = feedBackData.find(
                (feedBackDetail) =>
                    feedBackDetail._course_id.toString() === courseId.toString() &&
                    feedBackDetail.level_no === level_no &&
                    feedBackDetail.year_no === year_no &&
                    feedBackDetail.rotation === rotation &&
                    rotation_count &&
                    feedBackDetail.rotation_count === rotation_count,
            );
            if (feedBacks) feedback = feedBacks;
            let course_type;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) course_type = courseTypes.course_type;

            return {
                _id: courseId,
                _program_id: programId,
                courseSessionDetails,
                warningCount,
                presentCount,
                absentCount,
                leaveCount,
                program_name,
                course_name,
                course_code,
                totalSessions,
                completedSessions,
                attendedSessions,
                year: yearNo,
                level: levelNo,
                isActive,
                feedback,
                sessions: courseSessions,
                _institution_calendar_id: calendarId,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
            };
        });
        for (const course of coursesList) {
            for (const session of course.sessions) {
                session.documentCount = 0;
                session.activityCount = 0;
                for (const schedule of session.schedules) {
                    schedule.documentCount = 0;
                    schedule.activityCount = 0;
                    if (schedule.session && schedule.session._session_id) {
                        const docs = documents.filter((doc) =>
                            doc.sessionOrScheduleIds
                                .map((id) => id._id.toString())
                                .includes(schedule.session._session_id.toString()),
                        );
                        schedule.documentCount += docs.length;
                        const filteredActivities = activities.filter(
                            (activity) =>
                                activity._sessionId &&
                                activity._sessionId.toString() ===
                                    schedule.session._session_id.toString(),
                        );
                        schedule.activityCount += filteredActivities.length;
                    } else {
                        const docs = documents.filter((doc) =>
                            doc.sessionOrScheduleIds
                                .map((id) => id._id.toString())
                                .includes(schedule._id.toString()),
                        );
                        schedule.documentCount += docs.length;
                        const filteredActivities = activities.filter(
                            (activity) =>
                                activity._scheduleId &&
                                activity._scheduleId.toString() === schedule._id.toString(),
                        );
                        schedule.activityCount += filteredActivities.length;
                    }
                    session.documentCount += schedule.documentCount;
                    session.activityCount += schedule.activityCount;
                }
            }
            const feedBacks = rotation_count
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          rotation_count &&
                          feedBackDetail.rotationCount === rotation_count,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === _course_id.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) course.feedback = feedBacks;
        }
        return coursesList;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getScheduleBySessionOrScheduleList = async (
    sessionOrScheduleId,
    institutionCalendarId,
    userId,
    type,
) => {
    try {
        const scheduleQuery = {
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            $or: [
                {
                    _id: convertToMongoObjectId(sessionOrScheduleId),
                },
                {
                    'session._session_id': convertToMongoObjectId(sessionOrScheduleId),
                },
            ],
            ...isDeleteActive,
        };
        const scheduleDatas = clone(
            await courseSchedules
                .find(scheduleQuery, {
                    _id: 1,
                    _program_id: 1,
                    _course_id: 1,
                    course_code: 1,
                    topic: 1,
                    mode: 1,
                    title: 1,
                    rotation: 1,
                    rotation_count: 1,
                    'subjects.subject_name': 1,
                    'student_groups.group_name': 1,
                    'student_groups.session_group.group_name': 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    sessionDetails: 1,
                    staffs: 1,
                    infra_name: 1,
                    _infra_id: 1,
                    merge_status: 1,
                    merge_with: 1,
                    session: 1,
                    students: 1,
                    status: 1,
                    classModeType: 1,
                })
                .populate({
                    path: 'merge_with.schedule_id',
                    select: { session: 1 },
                }),
        );
        // IndraData for remote
        const remoteInfra = await getRemoteInfra();
        for (scheduleElement of scheduleDatas) {
            if (scheduleElement.mode === TIME_GROUP_BOOKING_TYPE.REMOTE) {
                scheduleElement.infraDatas = remoteInfra.find(
                    (infraElement) =>
                        scheduleElement._infra_id &&
                        infraElement._id.toString() === scheduleElement._infra_id.toString(),
                );
            }

            if (type === DC_STAFF) {
                const studentElement = scheduleElement.students.filter(
                    (student) =>
                        student.status === PRESENT &&
                        student.feedBack &&
                        student.feedBack.rating &&
                        student.feedBack.rating !== 0,
                );
                scheduleElement.feedBack = {
                    totalFeedback: studentElement.length,
                    avgRating: studentElement
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0),
                };
                scheduleElement.studentCount = {
                    totalCount: scheduleElement.students.length,
                    absent: scheduleElement.students.filter(
                        (student) => student && student.status && student.status === ABSENT,
                    ).length,
                    present: scheduleElement.students.filter(
                        (student) => student.status === PRESENT,
                    ).length,
                    leave: scheduleElement.students.filter((student) => student.status === LEAVE)
                        .length,
                    on_duty: scheduleElement.students.filter(
                        (student) => student.status === 'on_duty',
                    ).length,
                    permission: scheduleElement.students.filter(
                        (student) => student.status === PERMISSION,
                    ).length,
                };
            } else
                scheduleElement.studentDetails = scheduleElement.students.find(
                    (studentElement) => studentElement._id.toString() === userId.toString(),
                );
            delete scheduleElement.students;
        }
        return scheduleDatas;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getSessionWithScheduleBasedCourse = async (
    userId,
    courseId,
    institutionCalendarId,
    programId,
    yearNo,
    levelNo,
    term,
    rotation,
    rotationCount,
    type,
    filterType,
    timeZone,
) => {
    try {
        const courseSessionFlowData = (await getCourseSessionOrder(courseId)).session_flow_data;
        const sessionDelivery = [
            ...new Set(
                courseSessionFlowData.map(
                    (sessionDeliveryElement) => sessionDeliveryElement.delivery_type,
                ),
            ),
        ];
        const sessionDeliveryWithCount = sessionDelivery.map((sessionDeliveryElement) => {
            return {
                delivery: sessionDeliveryElement,
                deliveryCount: courseSessionFlowData.filter(
                    (sessionFlowElement) =>
                        sessionFlowElement.delivery_type === sessionDeliveryElement,
                ).length,
            };
        });
        // return { courseSessionFlowData, sessionDelivery, sessionDeliveryWithCount };
        const scheduleQuery = {
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _course_id: convertToMongoObjectId(courseId),
            $or: [
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
                { 'students._id': convertToMongoObjectId(userId) },
            ],
            term,
            year_no: yearNo,
            level_no: levelNo,
            ...isDeleteActive,
        };
        if (rotation === 'yes') scheduleQuery.rotation_count = rotationCount;
        const scheduleProject = {
            _id: 1,
            // program_name: 1,
            // course_name: 1,
            // _course_id: 1,
            // course_code: 1,
            type: 1,
            sub_type: 1,
            topic: 1,
            mode: 1,
            title: 1,
            rotation: 1,
            rotation_count: 1,
            'subjects.subject_name': 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_name': 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            sessionDetails: 1,
            staffs: 1,
            infra_name: 1,
            _infra_id: 1,
            merge_status: 1,
            merge_with: 1,
            session: 1,
            // 'session._session_id': 1,
            students: 1,
            status: 1,
            year_no: 1,
            level_no: 1,
            schedule_date: 1,
            infra_id: '$_infra_id',
            sessionDetail: 1,
            uuid: 1,
            socket_port: 1,
            term: 1,
            isActive: 1,
        };
        // if (filterType === 'today') {
        //     const timeZonedStartDate = dateTimeBasedConverter(new Date(), timeZone);
        //     const timeZonedBeginDate = timeZonedStartDate.clone().startOf('day');
        //     const timeZonedEndDate = timeZonedStartDate.clone().endOf('day');
        //     scheduleQuery.scheduleStartDateAndTime = {
        //         $gte: new Date(timeZonedBeginDate),
        //         $lte: new Date(timeZonedEndDate),
        //     };
        // } else if (sessionDelivery.includes(filterType)) {
        //     scheduleQuery.session.session_type = filterType;
        // } else if (filterType === 'support_session' || filterType === 'event') {
        // }
        console.time('ScheduleData');
        let scheduleDatas = await courseSchedules.find(scheduleQuery, scheduleProject).populate({
            path: 'merge_with.schedule_id',
            select: { session: 1 },
        });
        console.timeEnd('ScheduleData');
        scheduleDatas = clone(scheduleDatas);
        for (sessionScheduleElement of scheduleDatas) {
            if (type === STUDENTS)
                sessionScheduleElement.studentDetails = sessionScheduleElement.students.find(
                    (studentElement) => studentElement._id.toString() === userId.toString(),
                );
            else
                sessionScheduleElement.staffDetails = sessionScheduleElement.staffs.find(
                    (staffElement) => staffElement._staff_id.toString() === userId.toString(),
                );
            // delete sessionScheduleElement.staffs;
            delete sessionScheduleElement.students;
        }
        // return { courseSessionFlowData, sessionDelivery, sessionDeliveryWithCount, scheduleDatas };
        // const scheduleIds = scheduleDatas.map((scheduleElement) => scheduleElement._id);
        // const scheduleWithSessionIds = [
        //     ...courseSessionFlowData.map((sessionDeliveryElement) => sessionDeliveryElement._id),
        //     ...scheduleIds,
        // ];
        const activityData = await activities.find(
            {
                courseId: convertToMongoObjectId(courseId),
                isDeleted: false,
            },
            { sessionFlowIds: 1, _sessionId: 1 },
        );
        const documentData = await documentManager.find(
            {
                _course_id: convertToMongoObjectId(courseId),
                // 'sessionOrScheduleIds._id': { $in: scheduleWithSessionIds },
                ...isDeleteActive,
            },
            { sessionOrScheduleIds: 1 },
        );
        let sessionMappedData = [];
        // sessionMappedData
        const timeZonedStartDate = dateTimeBasedConverter(
            new Date(),
            // new Date('2021-09-23T00:00:00.000Z'),
            timeZone,
        );
        const timeZonedBeginDate = timeZonedStartDate.clone().startOf('day');
        const timeZonedEndDate = timeZonedStartDate.clone().endOf('day');
        // console.log(timeZonedStartDate);
        // console.log(timeZonedBeginDate);
        // console.log(timeZonedEndDate);
        const todaySchedule = scheduleDatas.filter(
            (scheduleElement) =>
                new Date(scheduleElement.scheduleStartDateAndTime) >=
                    new Date(timeZonedBeginDate) &&
                new Date(scheduleElement.scheduleEndDateAndTime) <= new Date(timeZonedEndDate),
        );
        sessionDeliveryWithCount.push({
            delivery: 'today',
            deliveryCount: todaySchedule.length,
        });
        if (filterType === 'today') {
            sessionMappedData = todaySchedule;
            sessionMappedData.map((sessionOrderElement) => {
                sessionOrderElement.documentCount = documentData.filter((doc) =>
                    doc.sessionOrScheduleIds.find(
                        (id) => id._id.toString() === sessionOrderElement._id.toString(),
                    ),
                ).length;
                sessionOrderElement.activityCount = activityData.filter(
                    (activity) =>
                        (activity.sessionOrScheduleIds &&
                            activity.sessionOrScheduleIds.find(
                                (id) => id._id.toString() === sessionOrderElement._id.toString(),
                            )) ||
                        (activity._sessionId &&
                            activity._sessionId.toString() === sessionOrderElement._id.toString()),
                ).length;
            });
        } else if (filterType === 'support_session' || filterType === 'event') {
            sessionMappedData = scheduleDatas.filter(
                (scheduleElement) => scheduleElement.type === filterType,
            );
            sessionMappedData.map((sessionOrderElement) => {
                sessionOrderElement.documentCount = documentData.filter((doc) =>
                    doc.sessionOrScheduleIds.find(
                        (id) => id._id.toString() === sessionOrderElement._id.toString(),
                    ),
                ).length;
                sessionOrderElement.activityCount = activityData.filter(
                    (activity) =>
                        (activity.sessionOrScheduleIds &&
                            activity.sessionOrScheduleIds.find(
                                (id) => id._id.toString() === sessionOrderElement._id.toString(),
                            )) ||
                        (activity._sessionId &&
                            activity._sessionId.toString() === sessionOrderElement._id.toString()),
                ).length;
            });
        } else if (filterType === 'all') {
            sessionMappedData = courseSessionFlowData.map((sessionOrderElement) => {
                const sessionSchedule = scheduleDatas.filter(
                    (scheduleElement) =>
                        scheduleElement.session &&
                        scheduleElement.session._session_id &&
                        scheduleElement.session._session_id.toString() ===
                            sessionOrderElement._id.toString(),
                );
                return {
                    _session_id: sessionOrderElement._id,
                    s_no: sessionOrderElement.s_no,
                    delivery_symbol: sessionOrderElement.delivery_symbol,
                    delivery_no: sessionOrderElement.delivery_no,
                    session_type: sessionOrderElement.delivery_type,
                    session_topic: sessionOrderElement.delivery_topic,
                    documentCount: documentData.filter((doc) =>
                        doc.sessionOrScheduleIds.find(
                            (id) => id._id.toString() === sessionOrderElement._id.toString(),
                        ),
                    ).length,
                    activityCount: activityData.filter(
                        (activity) =>
                            (activity.sessionOrScheduleIds &&
                                activity.sessionOrScheduleIds.find(
                                    (id) =>
                                        id._id.toString() === sessionOrderElement._id.toString(),
                                )) ||
                            (activity._sessionId &&
                                activity._sessionId.toString() ===
                                    sessionOrderElement._id.toString()),
                    ).length,
                    schedule: sessionSchedule || [],
                };
            });
        } else {
            const deliverySessionFlow = courseSessionFlowData.filter(
                (sessionFlowElement) =>
                    sessionFlowElement.delivery_type.toString() === filterType.toString(),
            );
            sessionMappedData = deliverySessionFlow.map((sessionOrderElement) => {
                const sessionSchedule = scheduleDatas.filter(
                    (scheduleElement) =>
                        scheduleElement.session &&
                        scheduleElement.session._session_id &&
                        scheduleElement.session._session_id.toString() ===
                            sessionOrderElement._id.toString(),
                );
                return {
                    _session_id: sessionOrderElement._id,
                    s_no: sessionOrderElement.s_no,
                    delivery_symbol: sessionOrderElement.delivery_symbol,
                    delivery_no: sessionOrderElement.delivery_no,
                    session_type: sessionOrderElement.delivery_type,
                    session_topic: sessionOrderElement.delivery_topic,
                    documentCount: documentData.filter((doc) =>
                        doc.sessionOrScheduleIds.find(
                            (id) => id._id.toString() === sessionOrderElement._id.toString(),
                        ),
                    ).length,
                    activityCount: activityData.filter(
                        (activity) =>
                            (activity.sessionOrScheduleIds &&
                                activity.sessionOrScheduleIds.find(
                                    (id) =>
                                        id._id.toString() === sessionOrderElement._id.toString(),
                                )) ||
                            (activity._sessionId &&
                                activity._sessionId.toString() ===
                                    sessionOrderElement._id.toString()),
                    ).length,
                    schedule: sessionSchedule || [],
                };
            });
        }
        const supportSessionCount = scheduleDatas.filter(
            (scheduleElement) => scheduleElement.type === 'support_session',
        ).length;
        const eventCount = scheduleDatas.filter(
            (scheduleElement) => scheduleElement.type === 'event',
        ).length;
        if (supportSessionCount !== 0)
            sessionDeliveryWithCount.push({
                delivery: 'support_session',
                deliveryCount: supportSessionCount,
            });
        if (eventCount !== 0)
            sessionDeliveryWithCount.push({
                delivery: 'event',
                deliveryCount: eventCount,
            });
        return {
            schedules: sessionMappedData,
            sessionDeliveryWithCount,
        };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

module.exports = {
    getCoursesSessionList,
    getScheduleBySessionOrScheduleList,
    getSessionWithScheduleBasedCourse,
};
